/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * This file describes the CP data structures
 *
 * Jim Salem, Exa Corporation 
 * Created Sat Jun  4 1994
 *--------------------------------------------------------------------------*/

#ifndef __CP_LATTICE_H
#define __CP_LATTICE_H

#include "common.h"


#if 0
#define COARSEST_SCALE	0
#define FINEST_SCALE	(cp_info.num_scales - 1)

#define DO_SCALES_FINE_TO_COARSE(scale_var) \
  ccDO_FROM_DOWNTO(scale_var, cp_info.num_scales - 1, 0)

#define sim_is_scale_finer(scale1, scale2) ((scale1) > (scale2))
#define sim_is_scale_coarser(scale1, scale2) ((scale1) < (scale2))

#define coarsen_scale(s) ((s) - 1)
#define finen_scale(s)   ((s) + 1)

#define is_voxel_in_mask(voxel, mask) (((mask) >> (voxel)) & 1)

inline auINT32 voxel_mask_to_2d_voxel_mask(auINT32 voxel_mask) { return voxel_mask & 0x55; }
inline asINT32 voxel_loop_increment(asINT32 n_dims) { return 4 - n_dims; }

inline BOOLEAN is_scale_finer  (asINT32 scale1, asINT32 scale2) { return scale1 > scale2; }
inline BOOLEAN is_scale_coarser(asINT32 scale1, asINT32 scale2) { return scale1 < scale2; }

inline BOOLEAN sim_is_scale_same_or_finer  (asINT32 scale1, asINT32 scale2) { return scale1 >= scale2; }
inline BOOLEAN sim_is_scale_same_or_coarser(asINT32 scale1, asINT32 scale2) { return scale1 <= scale2; }

// For SRI, 0 is the finest scale (while in the simulator, 0 is the coarsest scale)
inline BOOLEAN is_sri_scale_finer  (asINT32 scale1, asINT32 scale2) { return scale1 < scale2; }
inline BOOLEAN is_sri_scale_coarser(asINT32 scale1, asINT32 scale2) { return scale1 > scale2; }

/* SIM_SCALE_DIFF returns the difference between 2 scales, a positive result if
 * scale1 is coarser than scale2.
 */
#define sim_scale_diff(scale1, scale2)	((scale2) - (scale1))

#define num_to_voxel_offset(_num, _axis) (((_num) >> (STP_Z_AXIS - (_axis))) & 1)

#endif

#endif /* __CP_LATTICE_H */

