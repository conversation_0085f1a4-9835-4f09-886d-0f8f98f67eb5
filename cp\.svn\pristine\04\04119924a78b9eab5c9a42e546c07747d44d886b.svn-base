/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 *
 * Checkpoint support
 *
 *--------------------------------------------------------------------------*/
#ifndef __CHECKPOINT_H
#define __CHECKPOINT_H

#include "common.h"



typedef class sCKPT_QUEUE_ENTRY *CKPT_QUEUE_ENTRY;
typedef class sCKPT_QUEUE_ENTRY {
public:
  EVENT_ID              id;
  TIMESTEP              timestep;
  TIMESTEP              recur_rate;
  CKPT_QUEUE_ENTRY      next_entry;
  BOOLEAN               initial_condition;
  BOOLEAN               precious;       /* Only meaningful for checkpoints - means
                                        not to give up in the event of output
                                        failure.  Instead, spew error status
                                        and keep trying indefinitely. */
  sCKPT_QUEUE_ENTRY () {
    id = EVENT_ID_INVALID;
    timestep = TIMESTEP_INVALID;
    recur_rate = 0;
    next_entry = NULL;
  }

} *CKPT_QUEUE_ENTRY;

typedef class sCKPT_QUEUE {
public:
  CKPT_QUEUE_ENTRY     head;
  sINT32               entry_count;

  sCKPT_QUEUE() {
    head = NULL;
    entry_count = 0;
  }

  VOID add_entry(CKPT_QUEUE_ENTRY entry);
  BOOLEAN some_non_exit_event_later_than(TIMESTEP ts);
  VOID remove_entry();
  VOID print_queue();
  EVENT_ID remove_recurring_ckpt_event(TIMESTEP removal_timestep, BOOLEAN is_avg_mme_ckpt=FALSE, BOOLEAN force_to_remove=FALSE);
  EVENT_ID remove_recurring_average_mme_ckpt_event_after_time(TIMESTEP removal_timestep);
} *CKPT_QUEUE;

static LGI_STREAM cp_lgi_output_connect(STP_PROC sp);
VOID cp_wait_for_sp_lgi_output_connect();
VOID mpi_sp_lgi_streams_fcn();
LGI_STREAM *return_sp_stream();
BOOLEAN are_streams_closed();
VOID close_lgi_streams();
VOID process_checkpoints();
VOID write_checkpoints();
VOID process_exit_condition(VOID);

#endif /* __CHECKPOINT_H */
