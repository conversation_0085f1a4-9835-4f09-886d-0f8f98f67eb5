/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#include <stdio.h>
#include "common.h"
#include "errbuf.h"
#include "cp_info.h"
#include VMEM_VECTOR_H
#include <vector>

#define	ERROR_FILE_VERSION	1

static STRING err_filename = NULL;
static STRING err_file_pathname = NULL;


/*--------------------------------------------------------------------------*
 * ERROR_MANAGER
 *--------------------------------------------------------------------------*/

typedef enum {
  ERRSYS_UNINITIALIZED,		/* Nothing set up state */
  ERRSYS_READY,			/* Have everything, but no errors seen yet */
  ERRSYS_PROCESSING_INIT_ERRS,	/* File opened, written one or more errors in init state */
  ERRSYS_PROCESSING,		/* File opened, written one or more errors */
} ERRSYS_STATE;

typedef struct sTIMESTEP_ERRBUF {
  TIMESTEP          step;
  VMEM_VECTOR<char> *buf;
  sTIMESTEP_ERRBUF() { 
    step = TIMESTEP_INVALID;
    buf  = NULL;
  }
} *TIMESTEP_ERRBUF;

typedef class sERROR_MANAGER {
private:
  sINT32	m_num_timesteps_with_errors;
  sINT32	m_num_errors;

  sINT32	m_last_written_num_timesteps_with_errors;
  sINT32	m_last_written_num_errors;

  std::vector<sTIMESTEP_ERRBUF> errbufs;

  sINT32        m_num_fpes_current_timestep;
  bool          m_exit_registered;
  bool          m_exit_pending;
  bool   	m_some_error_disable_signal_pending;
  bool  	m_errors_disabled[SP_EER_NUMERRS];
  bool  	m_error_disable_signal_pending[SP_EER_NUMERRS];

  VMEM_VECTOR<char> *find_timestep_errbuf(TIMESTEP timestep);
  VMEM_VECTOR<char> *create_timestep_errbuf(TIMESTEP timestep);
  VOID		free_timestep_errbuf(TIMESTEP timestep);

  VOID dump_header()
  {
    fprintf(m_err_file, "Exa ERR V%05d\n"
            "%-12d # number of timesteps with errors\n"
            "%-12d # number of errors\n",
            ERROR_FILE_VERSION, m_num_timesteps_with_errors, m_num_errors);
  }

  VOID		dump_error_types();
  VOID		dump_error_string(STP_PROC sp, cSTRING error, int timestep, SP_EEP_TYPES errorIndex);
  VOID		maybe_initialize_error_file(TIMESTEP timestep);

public:
  TIMESTEP	m_current_print_timestep;
  BOOLEAN	m_some_errors_current_timestep;

  VOID		flush_timestep_errbuf(TIMESTEP timestep);
  VOID          fpe_exit_register(TIMESTEP timestep);
  VOID          exit_due_to_fpes();
  VOID          increment_fpe_count(SP_EEP_TYPES errorIndex);
  VOID          reset_num_fpes() {
    m_num_fpes_current_timestep = 0;
  }


  BOOLEAN       is_error_disabled(SP_EEP_TYPES ecode) { return m_errors_disabled[ecode]; }

  VOID process_pending_error_enable_signals()
  {
    if (m_some_error_disable_signal_pending) {
      ccDOTIMES(i, SP_EER_NUMERRS) {
        if (m_error_disable_signal_pending[i]) {
          CHARACTER buf[256];
          sprintf(buf, "%s %d", m_errors_disabled[i] ? "disable_warning" : "enable_warning", i);
          if (cp_issue_signal_cmd(buf)) {
            m_error_disable_signal_pending[i] = FALSE;
          } else {
            return;
          }
        }
      }
      m_some_error_disable_signal_pending = FALSE;
    }
  }

  VOID enable_error(SP_EEP_TYPES ecode, BOOLEAN enable_state)
  {
    m_some_error_disable_signal_pending = TRUE;
    m_error_disable_signal_pending[ecode] = TRUE;
    m_errors_disabled[ecode] = ! enable_state;
    process_pending_error_enable_signals();
  }

  VOID		refresh_header();

  ERRSYS_STATE	m_state;

  FILE		*m_err_file;
  WALLCLOCK_TIME_SECS 	m_last_flush_time;	// time of last call to fflush
  bool   	m_errors_written_but_not_flushed;

  sERROR_MANAGER() {
    m_current_print_timestep = 0;
    m_err_file = NULL;
    m_num_timesteps_with_errors = m_num_errors = 0;
    m_num_fpes_current_timestep = 0;
    m_exit_pending = false;
    m_exit_registered = false;
    m_some_errors_current_timestep = FALSE;
    m_state = ERRSYS_UNINITIALIZED;
    m_last_written_num_timesteps_with_errors = -1;
    m_last_written_num_errors = -1;
    m_errors_written_but_not_flushed = FALSE;
    m_last_flush_time = 0;
    m_some_error_disable_signal_pending = FALSE;
    ccDOTIMES(i, SP_EER_NUMERRS)
      m_errors_disabled[i] = m_error_disable_signal_pending[i] = FALSE;
  }

  ~sERROR_MANAGER() {
    if (m_err_file != NULL)
      refresh_header();
  }

  VOID  	queue_error(STP_PROC sp, cSTRING error, asINT32 len);
} *ERROR_MANAGER;

static sERROR_MANAGER errmgr;

/*--------------------------------------------------------------------------*
 * ERROR_MANAGER methods
 *--------------------------------------------------------------------------*/

#define	TIMESTEP_ERRBUF_START_COUNT	4

VOID sERROR_MANAGER::refresh_header() 
{
  if ((m_num_timesteps_with_errors != m_last_written_num_timesteps_with_errors) 
      || (m_num_errors != m_last_written_num_errors)) {
    long currentPos = ftell(m_err_file);
    rewind(m_err_file);

    dump_header();

    fseek(m_err_file, currentPos, SEEK_SET);
    m_last_written_num_timesteps_with_errors = m_num_timesteps_with_errors;
    m_last_written_num_errors                = m_num_errors;
  }
}

VOID sERROR_MANAGER::dump_error_types() 
{
  {
    asINT32 auditLineCount = 0;
    cSTRING audit_trail_string = audit_universal_rep(cp_info.audit_trail);

    if (audit_trail_string) {
      cSTRING ptr = audit_trail_string;

      while (*ptr) auditLineCount += ((*ptr++ == '\n'));
    }

    fprintf(m_err_file, "%-12d # number of audit trail lines\n", auditLineCount);
    fprintf(m_err_file, "%-12d # number of error types\n", spEerCount());
    fprintf(m_err_file, "\n");

    fprintf(m_err_file, "# audit trail\n");
    if (auditLineCount > 0) {
      fputs(audit_trail_string, m_err_file);
    }
    fprintf(m_err_file, "\n");
  }

  fprintf(m_err_file, "# error message table (2 lines per error type)\n");
  ccDOTIMES(i, spEerCount()) {
    SP_EEP_TYPES err_code = spEerCodeN(i);
    CHARACTER classStr[32];
    CHARACTER severityStr[32];

    switch (spEerClass(err_code)) {
      case SP_EEC_VOXEL:	{strcpy(classStr, "voxel"); break;}
      case SP_EEC_SURFEL:	{strcpy(classStr, "surfel"); break;}
      case SP_EEC_POINT:	{strcpy(classStr, "point"); break;}
      default:			{sprintf(classStr, "class %d", spEerClass(err_code)); break;}
    }
    switch (spEerSeverity(err_code)) {
      case SP_EES_INFO:		{strcpy(severityStr, "info"); break;}
      case SP_EES_ERROR:	{strcpy(severityStr, "error"); break;}
      case SP_EES_WARNING:	{strcpy(severityStr, "warning"); break;}
      case SP_EES_FATAL:	{strcpy(severityStr, "fatal"); break;}
      default:			{sprintf(severityStr, "severity %d", spEerSeverity(err_code));
				 break;}
    }
    fprintf(m_err_file, "%d %s %s  %s\n", (int) err_code, classStr, severityStr,
						  spEerDesc(err_code));

    fprintf(m_err_file, " %d", spEerParmCount(err_code));
    ccDOTIMES(j, spEerParmCount(err_code)) {
      CHARACTER typeStr[32];
      switch(spEerParmN(err_code, j)) {
	case SP_EEP_STRING:	{strcpy(typeStr, "string"); break;}
	case SP_EEP_FLOAT:	{strcpy(typeStr, "float"); break;}
        case SP_EEP_UVAL:	{sprintf(typeStr, "uval.%s", spEerParmNUnitClass(err_code, j)); break;}
	case SP_EEP_INT:	{strcpy(typeStr, "integer"); break;}
	default:		{strcpy(typeStr, "invalid_type"); break;}
      }

      fprintf(m_err_file, " \"%s\":%s", spEerParmNName(err_code, j), typeStr);
    }
    fprintf(m_err_file, "\n");
  }
  fprintf(m_err_file, "\n");

  fprintf(m_err_file,
	  "# error table - timestep, error-code, scale, location (x,y,z), error attributes\n");
}

static VOID flag_first_timestep_error(TIMESTEP timestep) 
{
  msg_warn("\"%s\" contains runtime warnings or errors, starting at timestep %d",
           err_filename, timestep);
}

static VOID flag_first_init_error() 
{
  msg_warn("\"%s\" contains initialization warnings or errors", err_filename);
}

VOID sERROR_MANAGER::maybe_initialize_error_file(TIMESTEP timestep) 
{
  switch(m_state) {
    case ERRSYS_UNINITIALIZED: {	/* Nothing set up state */
      msg_internal_error("CP error handler unexpectedly uninitialized");
      break;
    }

    case ERRSYS_READY: {		/* Have everything, but no errors seen yet */
      if ((m_err_file = fopen(err_file_pathname, "w"))) {
	dump_header();
	dump_error_types();
	m_last_flush_time = wallclock_time_secs();
	fflush(m_err_file);
	if (timestep >= 0) {
	  flag_first_timestep_error(timestep);
	  m_state = ERRSYS_PROCESSING;
	} else {
	  flag_first_init_error();
	  m_state = ERRSYS_PROCESSING_INIT_ERRS;
	}
      } else {
        msg_error("Unable to open error file \"%s\": %s.", 
                  err_filename, strerror(errno));
      }
      break;
    }

    case ERRSYS_PROCESSING_INIT_ERRS: {
      if (timestep >= 0) {		/* If encountered first step-related error, flag such */
	flag_first_timestep_error(timestep);
	m_state = ERRSYS_PROCESSING;
      }
      break;
    }

    case ERRSYS_PROCESSING: { break; }	/* Normal operations */

    default: {
      msg_internal_error("CP error handler encountered unexpected state value %d", m_state);
      break;
    }
  }
}


static sINT32 error_counts[SP_EER_NUMERRS] = { 0 };
static cBOOLEAN flagged_init_error[SP_EER_NUMERRS] = { 0 };
static cBOOLEAN flagged_runtime_error[SP_EER_NUMERRS] = { 0 };

// This is used to throttle the error messages sent from the SPs. Each SP
// will only send N errors until is receives an ack message from the CP
// indicating that the N errors have been extracted from the MPI library, and
// thus it is ok to send N more errors. The SPs are actually initialized
// so that they can send 2*N messages, so that we tend not to artificially
// pend the SPs.
static sINT32 *n_errors_extracted_per_sp;

static MPI_Datatype eMPI_cp_ack_sp_errs_type;

VOID sERROR_MANAGER::dump_error_string(STP_PROC sp, cSTRING buffer, int timestep, SP_EEP_TYPES errorIndex) 
{
  asINT32 maxErrors = spEerMaxErrors(errorIndex);

  if (timestep < 0) {
    if (!flagged_init_error[errorIndex]) {
      flagged_init_error[errorIndex] = TRUE;
      msg_warn("Initialization errors of type \"%s\" written to error file.", spEerDesc(errorIndex));
    }
  } else {
    if (!flagged_runtime_error[errorIndex]) {
      flagged_runtime_error[errorIndex] = TRUE;
      msg_warn("Runtime errors of type \"%s\" written to error file starting at timestep %d.", 
               spEerDesc(errorIndex), timestep);
    }
  }

  if ((maxErrors < 0) || (error_counts[errorIndex] < maxErrors)) {
    fprintf(m_err_file, "%s\n", buffer);
    m_errors_written_but_not_flushed = TRUE;
    /* m_errors_disabled[errorIndex] = FALSE; */ // in case user has bumped up maxErrors
    if (!m_some_errors_current_timestep) {
      m_some_errors_current_timestep = TRUE;
      m_num_timesteps_with_errors++;
    }
    m_num_errors++;
    ++ error_counts[errorIndex];
  } else if (!m_errors_disabled[errorIndex] && (error_counts[errorIndex] >= maxErrors)) {
    if (timestep < 0)
      msg_warn("Too many errors (%d) of type \"%s\" (%d).",
               maxErrors, spEerDesc(errorIndex), errorIndex);
    else
      msg_warn("Too many errors (%d) of type \"%s\" (%d) as of timestep %d.",
               maxErrors, spEerDesc(errorIndex), errorIndex, timestep);
        
    /* Queue a disable operation */
    enable_error(errorIndex, FALSE);
  }
}

VMEM_VECTOR<char> *sERROR_MANAGER::find_timestep_errbuf(TIMESTEP timestep) 
{
  // See if we have an errbuf assigned for this timestep
  ccDOTIMES(i, errbufs.size()) {
    if (errbufs[i].step == timestep)
      return errbufs[i].buf;
  }

  return NULL;
}

VMEM_VECTOR<char> *sERROR_MANAGER::create_timestep_errbuf(TIMESTEP timestep) 
{
  asINT32 empty_index = -1;

  // See if we already have an errbuf assigned for this timestep
  ccDOTIMES(i, errbufs.size()) {
    if (errbufs[i].step == timestep)
      return errbufs[i].buf;
    else if (errbufs[i].step == TIMESTEP_INVALID)
      empty_index = i;
  }

  if (empty_index == -1) {
    empty_index = errbufs.size();
    size_t new_size = errbufs.size() == 0 ? TIMESTEP_ERRBUF_START_COUNT : 2 * errbufs.size();
    errbufs.resize(new_size);
  }

  errbufs[empty_index].step = timestep;
  errbufs[empty_index].buf = xnew VMEM_VECTOR<char>;
  errbufs[empty_index].buf->reserve(128 * 1024); // reserve 128K bytes of addr space
  return errbufs[empty_index].buf;
}

VOID sERROR_MANAGER::free_timestep_errbuf(asINT32 timestep) 
{
  ccDOTIMES(i, errbufs.size()) {
    if (errbufs[i].step == timestep) {
      errbufs[i].step = TIMESTEP_INVALID;
      delete errbufs[i].buf;
      errbufs[i].buf = NULL;
      return;
    }
  }
}

static asINT32 n_sync_errors_received = 0;

VOID wait_for_all_initialization_simerrs()
{
  while (n_sync_errors_received < total_sps)
    cp_process_sp_errors(wallclock_time_secs());
}

VOID sERROR_MANAGER::queue_error(STP_PROC sp, cSTRING error, asINT32 len) 
{
  n_errors_extracted_per_sp[sp]++;
  if (n_errors_extracted_per_sp[sp] >= SIM_INFO_INITIAL_ERROR_COUNT) {
    static sCP_ACK_SP_ERRS_INFO_MSG ack_msg = { SIM_INFO_INITIAL_ERROR_COUNT }; // acceptable_message_count
    MPI_Request send_request;
    MPI_Isend(&ack_msg, 1, eMPI_cp_ack_sp_errs_type, sp,
              eMPI_CP_ACK_SP_ERRS_INFO_TAG, eMPI_sp_cp_comm, &send_request);
    // Free the request immediately because we never have to verify that the message was sent,
    // and we never have to free the send buffer or read/write the send buffer.
#if defined(_EXA_HPMPI)
    // For now, never free the request on Platform MPI. See PR 32012 and 40853.
    if (!sim_args.no_mpi_request_free && FALSE)
#else
    if (!sim_args.no_mpi_request_free)
#endif
      MPI_Request_free(&send_request);
    n_errors_extracted_per_sp[sp] = 0;
  }

  int timestep;
  int _errorIndex;
  sscanf(error, "%d %d", &timestep, &_errorIndex);
  SP_EEP_TYPES errorIndex = (SP_EEP_TYPES)_errorIndex;

  if (errorIndex == SP_EER_SYNC) {
    n_sync_errors_received++;
  } else if (timestep <= m_current_print_timestep) {
    maybe_initialize_error_file(timestep);
    dump_error_string(sp, error, timestep, errorIndex);
    increment_fpe_count(errorIndex);
  } else {
    VMEM_VECTOR<char> &buf = *create_timestep_errbuf(timestep);

    buf.push_back((unsigned char) len);
    ccDOTIMES(i, sizeof(sp)) {
      unsigned char sp_byte = (sp >> (8 * i)) & 0xFF;
      buf.push_back(sp_byte);
    }
    buf.insert(buf.end(), error, error + len);
  }
}

VOID sERROR_MANAGER::increment_fpe_count(SP_EEP_TYPES errorIndex) {
  if (errorIndex == SP_EER_FPE_TURB ||
      errorIndex == SP_EER_FPE_SOLVER) {
    m_num_fpes_current_timestep++;
  }
}

VOID sERROR_MANAGER::fpe_exit_register(TIMESTEP timestep) {
  if (m_exit_pending)
    return;
  if (m_num_fpes_current_timestep >= cp_info.n_fpes_allowed_in_timestep) {
    msg_warn("Number of floating point exceptions %d exceeds limit %d at T %d",
              m_num_fpes_current_timestep, cp_info.n_fpes_allowed_in_timestep,
              timestep);
    m_exit_registered = true;
  }
}

VOID sERROR_MANAGER::exit_due_to_fpes() {
  if (m_exit_pending)
    return;
  if (m_exit_registered) {
    cp_jobctl_terminate("Number of FPEs exceeded limit", EXIT_SUCCESS);
    m_exit_pending = true;
  }
}

VOID sERROR_MANAGER::flush_timestep_errbuf(TIMESTEP timestep) 
{
  VMEM_VECTOR<char> *buffer = find_timestep_errbuf(timestep);

  if (buffer != NULL) {
    if (buffer->size() > 0) {
      maybe_initialize_error_file(timestep);
    
      char *buf = &buffer->front();     
      char *buf_end = buf + buffer->size();
      while (buf < buf_end) {
        unsigned char error_len = *buf++;

        STP_PROC sp = 0;
        ccDOTIMES(j, sizeof(sp)) {
          unsigned char u_buf = *buf++;
          sp |= u_buf << (j * 8);
        }

        int timestep;
        int _errorIndex;
        sscanf(buf, "%d %d", &timestep, &_errorIndex);
        SP_EEP_TYPES errorIndex = (SP_EEP_TYPES)_errorIndex;
        dump_error_string(sp, buf, timestep, errorIndex);
        increment_fpe_count(errorIndex);
        buf += error_len;
      }
      // We commented out this call to refresh_header because it is unnecessary
      // at this point and it will flush the m_err_file stream.
      // refresh_header();
    }

    free_timestep_errbuf(timestep);
  }
}

BOOLEAN	cp_process_sp_errors_initialized()
{
  return errmgr.m_state != ERRSYS_UNINITIALIZED;
}

VOID cp_process_sp_errors_init(cSTRING _err_file_pathname) 
{
  // If not yet initialized, do so - but once only
  if (errmgr.m_state == ERRSYS_UNINITIALIZED) {
    eMPI_ack_sp_errs_type_init(&eMPI_cp_ack_sp_errs_type);

    err_file_pathname = strsave(_err_file_pathname);
    err_filename      = filename_no_directory(err_file_pathname);

    n_errors_extracted_per_sp = cnew sINT32[ total_sps ]; // zero

    errmgr.m_state = ERRSYS_READY;
  }
}

VOID cp_process_sp_errors(WALLCLOCK_TIME_SECS time_secs) 
{
  // Process any signals that must be sent to the SPs to enable or disable
  // errors of particular types (i.e. because max was exceeded)
  errmgr.process_pending_error_enable_signals();

  MPI_Status mpi_status;

  // Consume all pending error messages
  while(1) {
    int is_new_msg;
    MPI_Iprobe(MPI_ANY_SOURCE, eMPI_SP_ERROR_TAG, eMPI_sp_cp_comm, &is_new_msg, &mpi_status);

    if (!is_new_msg) {
      break;
    } else {
      int msg_len;

      MPI_Get_count(&mpi_status, MPI_BYTE, &msg_len);

      char buf[8 * 1024]; // more than ample space

      STP_PROC sp = mpi_status.MPI_SOURCE;
      RECV_EXA_SIM_MSG<char> recv_buf(eMPI_SP_ERROR_TAG, msg_len, buf, sp);
      g_exa_sp_cp_comm.recv(recv_buf.mpi_msg);
      buf[msg_len] = '\0';
      msg_len += 1;

      errmgr.queue_error(sp, buf, msg_len);
    }
  }

  // At this point, we have consumed all pending message from the SPs. We know
  // that there can be no more messages for timesteps before cp_info.time, so
  // we flush the messages for all timesteps up to and including cp_info.time 
  // to the .simerr file (in timestep order of course).
  //
  // How do we know that there can be no more messages for timesteps before 
  // cp_info.time? Because cp_info.time is the min timestep reported by all 
  // SPs (via status messages) and MPI ensures the order of messages between 
  // a particular source and dest. MPI's order guarantee is actually a bit
  // subtle, but we believe it covers us here. 
  while (errmgr.m_current_print_timestep < cp_info.time) {
    errmgr.m_some_errors_current_timestep = FALSE;
    errmgr.m_current_print_timestep++;
    errmgr.flush_timestep_errbuf(errmgr.m_current_print_timestep);
    errmgr.fpe_exit_register(errmgr.m_current_print_timestep);
    errmgr.reset_num_fpes();
  }

  const asINT32 N_SECS_BEFORE_FLUSH = 2 * 60;

  if (errmgr.m_errors_written_but_not_flushed 
      && (WALLCLOCK_TIME_DIFF(time_secs, errmgr.m_last_flush_time) > N_SECS_BEFORE_FLUSH)) {
    errmgr.refresh_header();
    fflush(errmgr.m_err_file);
    errmgr.m_last_flush_time = time_secs;
    errmgr.m_errors_written_but_not_flushed = FALSE;
  }

  errmgr.exit_due_to_fpes();
}

VOID cp_change_max_errors(SP_EEP_TYPES errorIndex, asINT32 new_max)
{
  asINT32 old_max = spEerMaxErrors(errorIndex);
  asINT32 current_count = error_counts[errorIndex];

  spSetEerMaxErrors(errorIndex, new_max);

  if (errmgr.m_state != ERRSYS_UNINITIALIZED) {
    if (errmgr.is_error_disabled(errorIndex) && (new_max > current_count))
      errmgr.enable_error(errorIndex, TRUE);
  }
}

