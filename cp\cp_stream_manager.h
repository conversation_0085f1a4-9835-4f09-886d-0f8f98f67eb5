/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
//----------------------------------------------------------------------------
// David Hall, Exa Corporation                  Created Mon, Dec 8, 2008
//----------------------------------------------------------------------------

#ifndef CP_STREAM_MANAGER_H
#define CP_STREAM_MANAGER_H

#include "common.h"
#include "parse_args.h"
#include "cp_lattice.h" //contains cp_info structure
#include "cp_cdi_reader.h"
#include "cp_dgf_reader.h"

#include LOOP_RANGE_H

extern LGI_STREAM* g_sp_streams;

//----------------------------------------------------------------------------
// sCP_STREAM_MANAGER
//----------------------------------------------------------------------------
typedef struct sCP_STREAM_MANAGER
{
  VOID open_sp_data_streams();

  VOID read_dgf_and_cdi_files();
  VOID close_sp_data_streams();
#if SURF_COUP
  VOID exchange_cp_coupling_info();
  VOID init_surface_coupling(SIM_ARGS sim_args, cSTRING cdi_filename) {
    m_cdi_reader.init_surface_coupling(sim_args, cdi_filename);
  }
#endif // SURF_COUP

protected: 
  // methods
  VOID send_cp_to_rp_info();
  VOID recv_patch_decomp();
  VOID send_cp_to_sp_info_record();
  VOID send_synchronization_message_to_all_sps();
  VOID send_case_origin_to_sps();
  VOID send_units_db_to_sps();
  VOID fill_global_info_record();
  VOID read_standard_units_database();
#if SURF_COUP
  sINT32 coarsest_scale_for_cmdl_meas(asINT32 model_index);
  VOID add_surface_coupling_cdi_meas_windows();
  VOID cp_send_coupling_models(BOOLEAN is_checkpoint_restore_p);
  VOID define_surface_coupling_measurements_and_send_windows_to_sps();
  VOID allocate_coupling_buffer_storage();
  VOID build_coupling_buffer_map();
#endif
  VOID cp_send_rotational_dynamics_descs();

  // members
  sCP_CDI_READER m_cdi_reader;
  sCP_DGF_READER m_dgf_reader;
}* CP_STREAM_MANAGER;

template<class RECORD_TYPE>
inline VOID write_header_to_sp_range(RECORD_TYPE &record, Loop::cRANGE range)
{
  for (auto i: range)
    lgi_write_next_head(g_sp_streams[i], record);
}

template<class RECORD_TYPE>
inline VOID write_header_to_all_flow_sps(RECORD_TYPE &record)
{
  ccDOTIMES(i,total_fsps)
    lgi_write_next_head(g_sp_streams[i], record);
}

template<class OBJECT_TYPE>
inline VOID write_to_all_flow_sps(OBJECT_TYPE& object)
{
  ccDOTIMES(i,total_fsps)
    lgi_write(g_sp_streams[i], object);
}

inline VOID write_record_to_all_flow_sps(VOID* record)
{
  ccDOTIMES(i,total_fsps) 
    lgi_write_record(g_sp_streams[i], record);
}

inline VOID write_to_all_flow_sps(VOID* data, asINT32 n_bytes)
{
  ccDOTIMES(i,total_fsps)
    lgi_write(g_sp_streams[i], data, n_bytes);
}

template<class RECORD_TYPE>
inline VOID write_header_to_all_sps(RECORD_TYPE &record)
{
  ccDOTIMES(i,total_sps)
    lgi_write_next_head(g_sp_streams[i], record);
}

template<class OBJECT_TYPE>
inline VOID write_to_all_sps(OBJECT_TYPE& object)
{
  ccDOTIMES(i,total_sps)
    lgi_write(g_sp_streams[i], object);
}

inline VOID write_record_to_all_sps(VOID* record)
{
  ccDOTIMES(i,total_sps) 
    lgi_write_record(g_sp_streams[i], record);
}

inline VOID write_to_all_sps(VOID* data, asINT32 n_bytes)
{
  ccDOTIMES(i,total_sps)
    lgi_write(g_sp_streams[i], data, n_bytes);
}

inline VOID write_to_sp_range(VOID* data, asINT32 n_bytes, Loop::cRANGE sp_range)
{
  for (auto i: sp_range)
    lgi_write(g_sp_streams[i], data, n_bytes);
}

inline VOID write_to_all_sps(std::string &s) {
  write_to_all_sps((VOID*)s.c_str(), sizeof(char) * s.length());
}

#endif //CP_STREAM_MANAGER_H
