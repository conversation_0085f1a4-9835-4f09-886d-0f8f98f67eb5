/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#include "porous_rock.h"
#include "common.h"
#include "cp_stream_manager.h"

#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <sstream>
#include <iterator>

namespace
{

  std::vector<std::string> split_ws(const std::string& str)
  {
    std::istringstream tokenStream(str);
    return std::vector<std::string>(std::istream_iterator<std::string>{ tokenStream },
                                    std::istream_iterator<std::string>());
  }

  template<size_t NUM_COLS>
  std::vector<POROUS_ROCK_VAR> read_porous_rock_table(const std::string& filename)
  {
    std::ifstream file(filename);
    if ( !file.is_open() ) {
      msg_error("Unable to open file %s",filename.c_str());
    }

    const std::istream_iterator<POROUS_ROCK_VAR> end{};
    std::vector<POROUS_ROCK_VAR> vars; vars.reserve(NUM_COLS*1001);
    std::string line;
    std::getline(file, line);
    auto header_variables = split_ws(line);
    if (header_variables.size() != NUM_COLS) {
      msg_error("File %s has incorrect number of columns!", filename.c_str());
    }

    std::getline(file, line); // skip the units line for now

    int line_num = 2;
    while ( std::getline(file, line) ) {
      int count = 0;
      std::istringstream line_stream(line);
      for( std::istream_iterator<POROUS_ROCK_VAR> line_it(line_stream); line_it != end; ++line_it ) {
        POROUS_ROCK_VAR var = *line_it;
        if ( !std::isfinite(var) ) {
          msg_error("Invalid value in file %s, line %d", filename.c_str(), line_num);
        }
        vars.push_back(var);
        count++;
      }
      if ( count != 0 && count != NUM_COLS ) {
        msg_error("Incorrect number of columns in file %s, line %d", filename.c_str(), line_num);
      }
      line_num++;
    }
    return vars;
  }
}

cPOROUS_ROCK::cPOROUS_ROCK(POROUS_ROCK_INDEX index) : m_index(index) {}

cPOROUS_ROCK cPOROUS_ROCK::read_tables(POROUS_ROCK_INDEX index, const std::string& rock_dir)
{
  cPOROUS_ROCK rock(index);

  /*ccDOTIMES(i,3) {
    rock.m_principal_dir[i] = 1.0;
    }*/

  std::string K0_filename = EXA_PATH::Join(rock_dir, "K0.txt");

  rock.m_num_K0_cols = NUM_K0_COLS;
  rock.m_K0_values = read_porous_rock_table<NUM_K0_COLS>(K0_filename);
  rock.m_num_K0_rows = rock.m_K0_values.size() / NUM_K0_COLS;
  rock.m_num_Pc_Kr_cols = 0;
  rock.m_num_Pc_Kr_rows = 0;
  rock.m_Pc_Kr_values = {0};

  if (cp_info.num_fluid_components > 1) {
    std::string Pc_Kr_filename = EXA_PATH::Join(rock_dir, "Pc_Kr.txt");
    rock.m_num_Pc_Kr_cols = NUM_Pc_Kr_COLS;
    rock.m_Pc_Kr_values = read_porous_rock_table<NUM_Pc_Kr_COLS>(Pc_Kr_filename);
    rock.m_num_Pc_Kr_rows = rock.m_Pc_Kr_values.size()/NUM_Pc_Kr_COLS;
  }

  std::string info_filename = EXA_PATH::Join(rock_dir, "info.txt");
  rock.m_num_info_cols = 0;
  rock.m_num_info_rows = 0;
  rock.m_info_values = {0};
  {
    std::ifstream file(info_filename);
    if ( file.is_open() ) {
      rock.m_num_info_cols = NUM_INFO_COLS;
      rock.m_info_values = read_porous_rock_table<NUM_INFO_COLS>(info_filename);
      rock.m_num_info_rows = rock.m_info_values.size() / NUM_INFO_COLS;
      if (rock.m_num_info_rows != NUM_INFO_ROWS ) {
        msg_error("Incorrect number of rows with values in file %s", info_filename.c_str());
      }
    }
  }
  return rock;
}

void cPOROUS_ROCK::write_to_all_sps() const
{
  sLGI_POROUS_ROCK lgi_rock;

  lgi_rock.rock_id = m_index;
  lgi_rock.num_K0_rows = m_num_K0_rows;
  lgi_rock.num_K0_cols = m_num_K0_cols;
  lgi_rock.num_Pc_Kr_rows = m_num_Pc_Kr_rows;
  lgi_rock.num_Pc_Kr_cols = m_num_Pc_Kr_cols;
  lgi_rock.num_info_rows = m_num_info_rows;
  lgi_rock.num_info_cols = m_num_info_cols;

  lgi_rock.tag.id = LGI_POROUS_ROCK_TAG;

  size_t num_K0_values = m_K0_values.size();
  size_t num_Pc_Kr_values = m_Pc_Kr_values.size();
  size_t num_info_values = m_info_values.size();

  lgi_rock.tag.length = lgi_pad_and_encode_record_length(
                          sizeof(lgi_rock) + 
                          sizeof(POROUS_ROCK_VAR)*(num_K0_values + num_Pc_Kr_values + num_info_values)
                        );
  /*ccDOTIMES(i,3) {
    lgi_rock.principal_dir[i] = m_principal_dir[i];
    }*/

  write_header_to_all_sps(lgi_rock);

  if ( num_K0_values > 0 ) {
    ::write_to_all_sps(const_cast<POROUS_ROCK_VAR*>(m_K0_values.data()), sizeof(POROUS_ROCK_VAR)*num_K0_values);
  }

  if ( num_Pc_Kr_values > 0 ) {
    ::write_to_all_sps(const_cast<POROUS_ROCK_VAR*>(m_Pc_Kr_values.data()), sizeof(POROUS_ROCK_VAR)*num_Pc_Kr_values);
  }

  if ( num_info_values > 0 ) {
    ::write_to_all_sps(const_cast<POROUS_ROCK_VAR*>(m_info_values.data()), sizeof(POROUS_ROCK_VAR)*num_info_values);
  }

}

// This should probably go in cp_cdi_reader or cp_dgf_reader, but
// currently neither one of them knows about rock types. It feels
// awkward to choose one or the other, when neither one of them
// "triggers" this function by reading from the CDI or LGI files.
// I'll put it on its own for now. 
void write_porous_rock_tables()
{
  int num_rock_types = 0;
  if ( !cEXA_STAT::IsDir("porous_rock_tables") ) {
    msg_error("Unable to find directory porous_rock_tables");
  }

  std::vector<std::string> rock_type_dirs = EXA_SRCHPATH::FindAll("porous_rock_tables","*", cEXA_STAT::DIR_R);
  num_rock_types = rock_type_dirs.size();
    
  sLGI_POROUS_ROCK_COUNT lgi_porous_rock_count;
  lgi_porous_rock_count.tag.id = LGI_POROUS_ROCK_COUNT_TAG;
  lgi_porous_rock_count.tag.length = lgi_pad_and_encode_record_length(sizeof(lgi_porous_rock_count));
  lgi_porous_rock_count.num_rock_types = num_rock_types;

  //do_debug
  /*ccDOTIMES(i,rock_type_dirs.size()) {
    std::cout<<"rock_types_dir:"<<rock_type_dirs[i]<<std::endl;
    }*/

  write_header_to_all_sps(lgi_porous_rock_count);

  std::vector<int> rt_index;   //rock type index
  ccDOTIMES(i, num_rock_types) {
    std::size_t pos = rock_type_dirs[i].find("/")+1;      // position of "/" in str

    std::string index_s = rock_type_dirs[i].substr (pos);     // get the index number
    int  index = stoi(index_s);

    //do_debug
    //printf("CP: rock_type_index=%d, num_rock_types=%d\n", index, num_rock_types);

    if (index < 0 || index >= num_rock_types)
      msg_error("Rock type index (%d) in directory porous_rock_tables is out of the range [0, %d].", index, num_rock_types-1);

    std::vector<int>::iterator it = std::find(rt_index.begin(), rt_index.end(), index);
    if (it != rt_index.end())
      msg_error("Duplicated rock type index (%d) in directory porous_rock_tables.", index);
    else
      rt_index.push_back(index);

    cPOROUS_ROCK rock = cPOROUS_ROCK::read_tables(index, rock_type_dirs[i]);
    rock.write_to_all_sps();
  }
}
