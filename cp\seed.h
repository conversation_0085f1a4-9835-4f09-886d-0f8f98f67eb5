/* ~~~CO<PERSON>Y<PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#ifndef _SEED_H_
#define _SEED_H_

#include "common.h"

#define N_CELL_INDEX_BITS 13
#define N_CELLS_IN_PAGE (1 << (N_CELL_INDEX_BITS))
#define CELL_INDEX_MASK (0xFFFFFFFF >> (32 - N_CELL_INDEX_BITS))
#define N_PAGES_IN_CACHE 256
const uINT32 MAX_N_SEED_MEAS_VARS = 20;

#define N_NORM_CELL_INDEX_BITS 10
#define N_NORM_CELLS_IN_PAGE (1 << (N_NORM_CELL_INDEX_BITS))
#define NORM_CELL_INDEX_MASK (0xFFFFFFFF >> (32 - N_NORM_CELL_INDEX_BITS))
#define N_NORM_PAGES_IN_CACHE 64

enum SEED_MEAS_VAR_INDEX {
  SEED_MEAS_VAR_PRESSURE_INDEX,
  SEED_MEAS_VAR_XVEL_INDEX,
  SEED_MEAS_VAR_YVEL_INDEX,
  SEED_MEAS_VAR_ZVEL_INDEX
};

typedef struct sBSEED_FACE_CHECK {

  using ENTITY_TYPE = cCDI_GEOMETRY_REF::eGEOMETRY_TYPE;

  sBSEED_FACE_CHECK(): found(FALSE),
                       is_wall(FALSE),
                       entity_type(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) {}
  
  std::string entity_type_name() const {
    return cCDI_GEOMETRY_REF::GetName(entity_type);
  }

  BOOLEAN is_segment_or_part() const {
    return
      entity_type == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part ||
      entity_type == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment;
  }
  
  BOOLEAN found;          // TRUE if face exists
  BOOLEAN is_wall;        // TRUE if face is a wall  
  ENTITY_TYPE entity_type;
} *BSEED_FACE_CHECK;

#define MAX_N_SEED_UDS_VARS  (MAX_N_USER_DEFINED_SCALARS * DGF_N_SEED_UDS_VARS)

typedef struct sSEED_DATA_CACHE_PTE sSEED_DATA_CACHE_PTE, *SEED_DATA_CACHE_PTE;

typedef struct sUSAGE_LIST_ENTRY {

  sUSAGE_LIST_ENTRY *prev;
  sUSAGE_LIST_ENTRY *next;
  sSEED_DATA_CACHE_PTE *pte;
  auINT32 page_index;

} *USAGE_LIST_ENTRY;

typedef struct sUSAGE_LIST {

  sUSAGE_LIST_ENTRY ulist_entries[N_PAGES_IN_CACHE];

  sUSAGE_LIST_ENTRY *mru;
  sUSAGE_LIST_ENTRY *lru;
  auINT32 next_entry_index;

} *USAGE_LIST;

typedef struct sSEED_DATA_CACHE_PTE {

  sINT32 page_index;
  cBOOLEAN resident;
  sINT16 n_points_in_page; // must be big enough to accomodate N_CELLS_IN_PAGE
  sUSAGE_LIST_ENTRY *ulist_entry;

} *SEED_DATA_CACHE_PTE;

typedef class sSEED_DATA_CACHE {
public:
  asINT32 var_page_size;
  sriFLOAT *var_pages;
  asINT32 ref_frame_index_page_size;
  sriLRF_INDEX *ref_frame_index_pages;
  sUSAGE_LIST ulist;
  sSEED_DATA_CACHE_PTE *page_table;

  asINT32 hits;
  asINT32 first_ref_misses;
  asINT32 capacity_misses;

} *SEED_DATA_CACHE;

typedef class sSEED_NORM_DATA_CACHE {
public:
  asINT32 var_page_size;
  sriFLOAT *var_pages;
  sUSAGE_LIST ulist;
  sSEED_DATA_CACHE_PTE *page_table;

  asINT32 hits;
  asINT32 capacity_misses;

} *SEED_NORM_DATA_CACHE;

typedef struct sSEED_LRF_ROTATION_DATA {
  dFLOAT local_to_global_rotation_matrix[3][3];
  sriDOUBLE rotation_angle;
  sriINT n_revolutions;
} *SEED_LRF_ROTATION_DATA;

struct tSEED_VAR_SPEC_BASE {

  static std::vector<std::string> fluid_seed_var_names;
  static std::vector<std::string> boundary_seed_var_names;

};

template< typename SEED_VAR_TYPE >
struct tSEED_VAR_SPEC : tSEED_VAR_SPEC_BASE {
  sINT32 n_vars;
  SEED_VAR_TYPE *seed_var_types;
  //uds
  sINT32 n_uds_vars;
  sINT32 *seed_uds_var_indices;  //[MAX_N_USER_DEFINED_SCALARS][DGF_N_SEED_UDS_VARS];
  
  std::string entity_name;  // we stash the region/face name here during parsing and process later 

  cBOOLEAN is_seed_vars_inclusion_list; // TRUE if seed_include vars, FALSE if seed_exclude_vars

  std::vector<std::string> &seed_var_names() const {

    static_assert(std::is_same<SEED_VAR_TYPE, DGF_SEED_VAR_TYPE>::value || std::is_same<SEED_VAR_TYPE, DGF_BOUNDARY_SEED_VAR_TYPE>::value, 
                  "tSEED_VAR_SPEC doesn't support the requested template argument.");

    if constexpr (std::is_same<SEED_VAR_TYPE, DGF_BOUNDARY_SEED_VAR_TYPE>::value) {
      return boundary_seed_var_names;
    }
    return fluid_seed_var_names;
  };

  VOID print(std::ostream &stream) const {
    stream << "entity_name: " << entity_name << std::endl;
    stream << "n_vars: " << n_vars << std::endl;
    stream << "is_seed_vars_inclusion_list: " << (is_seed_vars_inclusion_list ? "true" : "false") << std::endl;
    stream << "seed_var_types: " << std::endl;
    for (int n = 0; n < n_vars; n++) {
      stream << seed_var_names()[seed_var_types[n]] << ": " << seed_var_types[n] << std::endl;
    }
  }

};

typedef tSEED_VAR_SPEC< DGF_SEED_VAR_TYPE >           sFLUID_SEED_VAR_SPEC,    *FLUID_SEED_VAR_SPEC;
typedef tSEED_VAR_SPEC< DGF_BOUNDARY_SEED_VAR_TYPE >  sBOUNDARY_SEED_VAR_SPEC, *BOUNDARY_SEED_VAR_SPEC;
  
typedef struct sSMART_SEED_PARAMS {
  cBOOLEAN seed_via_dimless_properties;
  cBOOLEAN seed_via_mks;
  cBOOLEAN seed_sim_was_incompressible;
  cBOOLEAN is_seed_data_in_local_csys;
  dFLOAT coord_scale;
  csPOINT origin_shift;
  dFLOAT char_density;
  dFLOAT char_vel;
  dFLOAT char_temp;
  dFLOAT mks_vel_scaling;
  dFLOAT lattice_gas_const;
  dFLOAT char_density_seed;
  dFLOAT char_vel_seed;
  dFLOAT char_temp_seed;
  dFLOAT lattice_gas_const_seed;
  cBOOLEAN m_seed_file_contains_sliding_lrfs;
  std::vector<sSEED_LRF_ROTATION_DATA> seed_lrf_rotation_data;
  std::vector<sriLRF_INDEX> seed_lrf_index_from_ublk_lrf_index;
  BOOLEAN is_seed_rotate_vel;
  cSTRING seed_rotate_vel_csys_name;
  sINT8 seed_rotate_vel_axis;
  dFLOAT seed_rotate_vel_angle;

  VOID setup(sSRI_FILE *seed_file, cGRID *grid, cBOOLEAN seed_from_meas);
  VOID fill_params(cDGF_SMART_SEED_CONTROL &seed_control);

  VOID print(std::ostream &stream) const;

} *SMART_SEED_PARAMS;

inline std::ostream & operator << (std::ostream &out, const sSMART_SEED_PARAMS &smart_seed_params) {
    smart_seed_params.print(out);
    return out;
  }

typedef class cSMART_SEED_CONTROL {

  // The seed var specs are initialized based on what the user provides on the cmd
  // line. If there are fluid regions without a cmd line specification, we create
  // one entry here with all variables included.
  std::vector<sFLUID_SEED_VAR_SPEC>    m_fluid_seed_var_specs;
  std::vector<sBOUNDARY_SEED_VAR_SPEC> m_boundary_seed_var_specs;

  // This is a vector of length cp_info.n_sri_parts with a per-part index into the 
  // seed_var_specs vector. Only parts that are fluid regions contain meaningful
  // entries. Since the type is uINT8, we only support 256 seed var specs.
  uINT8              *m_part_seed_var_spec_indices;
  cBOOLEAN           *m_found_seed_var_spec_physics_desc;

  // Seeding cmdline options saved for processing later
  std::vector<std::string> m_fluid_seed_include_vars;
  std::vector<std::string> m_fluid_seed_exclude_vars;
  std::vector<std::string> m_boundary_seed_include_vars;
  std::vector<std::string> m_boundary_seed_exclude_vars;

  // Member variables
  cSTRING            m_seed_filename;
  SRI_FILE           m_seed_file;
  REALM              m_realm;
  asINT32            m_seed_frame;
  cBOOLEAN           m_do_turb_dimless_seeding;
  asINT32            m_spec_base_index;

  dFLOAT             m_char_vel_ratio;    // used if m_do_turb_dimless_seeding is true
  dFLOAT             m_char_vel_ratio_2;  // used if m_do_turb_dimless_seeding is true
  dFLOAT             m_char_vel_ratio_3;  // used if m_do_turb_dimless_seeding is true

  sINT16             m_n_vars;
  // These 2 arrays contain the union of the variables used by all fluid regions
  SRI_VARIABLE_TYPE  m_sri_var_types[DGF_N_SEED_VARS];
  DGF_SEED_VAR_TYPE  m_seed_var_types[DGF_N_SEED_VARS];

  // UDS
  sINT16             m_n_uds_vars;
  SRI_VARIABLE_TYPE  m_sri_uds_var_types[MAX_N_SEED_UDS_VARS];
  sINT32             m_seed_uds_var_indices[MAX_N_SEED_UDS_VARS];
  
  cGRID              *m_grid;
  sSMART_SEED_PARAMS m_params;
  sriFLOAT           *m_var_table;
  sriLRF_INDEX       *m_ref_frame_index_table;
  BOOLEAN            m_do_extrapolate;

  sINT64             m_n_points;
  sSEED_DATA_CACHE   m_seed_data_cache;

  sINT64             m_n_voxels_seeded;
  sINT64             m_n_voxels_seeded_with_junk;
  sINT64             m_n_voxels_frozen;

  sINT32             m_n_nw_norm_meas_cells;
  sSEED_NORM_DATA_CACHE   m_seed_norm_data_cache;
  std::vector<sriFLOAT> m_nw_surface_normals;    //store surfel normals of nw voxels, only do it for localized velocity forzen (F1) case and special split seeding
  sriINT             *m_nw_norm_meas_cell_indices;  //read from seed file
  BOOLEAN            m_smart_seed_boundaries;
  BOOLEAN            m_smart_seed_contact_angle;

public:

  cSMART_SEED_CONTROL(SRI_FILE file, REALM seed_realm, asINT32 seed_frame) {
    m_realm = seed_realm;
    m_seed_frame = seed_frame;
    m_seed_file = file;
    m_seed_filename = file->name();
    //The setup() method is expected to initialize member variables other than m_realm.
    // Initialize remaining members
    m_part_seed_var_spec_indices = nullptr;
    m_found_seed_var_spec_physics_desc = nullptr;
    m_do_turb_dimless_seeding = FALSE;
    m_spec_base_index = 0;
    m_char_vel_ratio = 0.0;
    m_char_vel_ratio_2 = 0.0;
    m_char_vel_ratio_3 = 0.0;
    m_n_vars = 0;
    m_grid = nullptr;
    m_var_table = nullptr;
    m_ref_frame_index_table = nullptr;
    m_n_points = 0;
    m_n_voxels_seeded = 0;
    m_n_voxels_seeded_with_junk = 0;
    m_n_voxels_frozen = 0;
    m_n_nw_norm_meas_cells = 0;
    m_nw_norm_meas_cell_indices = nullptr;
    m_do_extrapolate = FALSE;
    m_smart_seed_boundaries = FALSE;
    m_smart_seed_contact_angle = FALSE; //smart_seed_contact_angle is a 5g option
  }
  cSMART_SEED_CONTROL() = delete; //Do not allow construction without specifying the realm (e.g. flow or solid conduction).


  enum class eMSEED_OPTIONS {
    SEEDFRAME,
    SEED_MKS,
    NO_EXTRAPOLATE,
    EXTRAPOLATE,
    SMART_SEED_BOUNDARIES,
    SMART_SEED_CONTACT_ANGLE,
    SEED_ROTATE_VEL,
    SEED_EXCLUDE_VARS,
    SEED_INCLUDE_VARS,
    BSEED_EXCLUDE_VARS,
    BSEED_INCLUDE_VARS,
    N_MSEED_OPTIONS
  };
  
  static std::unordered_map<std::string, eMSEED_OPTIONS> mseed_options; 


  // Getters and setters
  sINT16 n_vars() {return m_n_vars; }
  sINT16 n_uds_vars() {return m_n_uds_vars; }
  SRI_FILE seed_file() { return m_seed_file; }
  BOOLEAN is_smart_seed() { return m_seed_file != NULL; }
  BOOLEAN is_seed_data_in_local_csys() { return m_params.is_seed_data_in_local_csys; }
  BOOLEAN seed_sim_was_incompressible() { return m_params.seed_sim_was_incompressible; }
  REALM realm() {return m_realm; }
  BOOLEAN do_extrapolate() {return m_do_extrapolate;}
  BOOLEAN smart_seed_boundaries() {return m_smart_seed_boundaries;}
  BOOLEAN smart_seed_contact_angle() {return m_smart_seed_contact_angle;}
  BOOLEAN seed_via_mks() {return m_params.seed_via_mks;}
  BOOLEAN is_seed_rotate_vel() {return m_params.is_seed_rotate_vel;}

  asINT32 n_fluid_seed_var_specs()  { return m_fluid_seed_var_specs.size(); }
  std::vector<sFLUID_SEED_VAR_SPEC> &fluid_seed_var_specs()  { return m_fluid_seed_var_specs; }
  sFLUID_SEED_VAR_SPEC get_fluid_seed_var_spec(std::size_t spec) { return m_fluid_seed_var_specs[spec]; }

  asINT32 n_boundary_seed_var_specs() { return m_boundary_seed_var_specs.size(); }
  std::vector<sBOUNDARY_SEED_VAR_SPEC> &boundary_seed_var_specs() {return m_boundary_seed_var_specs; }
  sBOUNDARY_SEED_VAR_SPEC get_boundary_seed_var_spec(std::size_t spec) {return m_boundary_seed_var_specs[spec]; }

  BOOLEAN check_seed_file_type();
  VOID setup();
  VOID wrapup();

  uINT8 seed_var_types(std::size_t i) {
    return m_seed_var_types[i];
  }

  uINT32 seed_uds_var_indices(std::size_t i) {
    return m_seed_uds_var_indices[i];
  }

  sSMART_SEED_PARAMS seed_params() {return m_params; }

  VOID add_surface_normals(cDGF_SURFEL_DESC *surfel_desc);

  VOID clear_surface_normals() {
    m_nw_surface_normals.clear();
  }

  VOID delete_norm_meas_cell_indices () {
    if (m_realm == STP_COND_REALM) {
      delete [] m_nw_norm_meas_cell_indices;
    }
  }

  VOID check_boundary_face_bseed_vars(std::vector<sBSEED_FACE_CHECK>& bseed_face_checks, 
                                      std::vector<sBSEED_FACE_CHECK>& bseed_var_face_checks);

  VOID check_seed_var_spec_physics_desc();

  VOID detect_fluid_physics_desc_for_seeded_regions(cSTRING physics_desc_name, const std::vector<asINT32>& rgns);

  VOID parse_cmdline_dgf_seed_var_specs_until(int *argc, char *argv[], 
                                              cSTRING include_cmdline_arg,
                                              cSTRING exclude_cmdline_arg, 
                                              cSTRING argstop);

  VOID parse_cmdline_dgf_bseed_var_specs_until(int *argc, char *argv[], 
                                              cSTRING include_cmdline_arg,
                                              cSTRING exclude_cmdline_arg, 
                                              cSTRING argstop);

  VOID maybe_add_var_seed_specs();

  VOID maybe_write_smart_seed_data(cDGF_UBLK_BASE_DESC *ublk_desc,
                                   auINT32 fluid_like_voxel_mask,
                                   asINT32 ublk_home_sp,
                                   asINT32 simple_part_index,     
                                   sINT32  voxel_part_indices[8],
                                   auINT32 split_voxel_mask);

  template <typename SEED_VAR_TYPE, asINT32 N_SEED_VARS, bool IS_FLUID_SEED>
  VOID parse_seed_var_specs(bool do_smart_seed, int *argc, char *argv[], 
                            cSTRING include_cmdline_arg, cSTRING exclude_cmdline_arg,
                            BOOLEAN is_fluid_region,
                            std::map <std::string, SEED_VAR_TYPE > &seed_var_string_to_seed_var_type,
                            SEED_VAR_TYPE default_seed_vars[], asINT32 n_default_seed_vars,
                            asINT32 n_extra_inclusion_seed_vars, cSTRING purpose);

  template <typename SEED_VAR_TYPE, asINT32 N_SEED_VARS, bool IS_FLUID_SEED>
  static VOID parse_seed_var_spec_vars(std::string seed_vars[],
                                       asINT32 n_seed_vars,
                                       tSEED_VAR_SPEC< SEED_VAR_TYPE > *seed_var_spec,
                                       cSTRING include_cmdline_arg, cSTRING exclude_cmdline_arg,
                                       std::map <std::string, SEED_VAR_TYPE > &seed_var_string_to_seed_var_type,
                                       asINT32 n_extra_inclusion_seed_vars);


  BOOLEAN is_location_in_this_seed_file(cDGF_UBLK_BASE_DESC *ublk_desc, auINT32 fluid_like_voxel_mask);

  VOID maybe_rotate_vel();

  sriINT find_nw_cell_index(sriINT cell_index) {
    sriINT *first = &m_nw_norm_meas_cell_indices[0];
    sriINT *last = &m_nw_norm_meas_cell_indices[m_n_nw_norm_meas_cells];
    sriINT* it = std::lower_bound(first, last, cell_index);
    if ((it != last) && (*it == cell_index))
      return (it - first);
    else
      return -1;
  }

  auINT32 generate_seed_vars(cDGF_UBLK_BASE_DESC *ublk_desc,
                             sFLOAT seed_vars[][N_VOXELS_8],
			     sFLOAT seed_uds_vars[][N_VOXELS_8],
                             auINT32 fluid_like_voxel_mask,
                             auINT32 split_voxel_mask);

  VOID setup_mlrf_initial_position();

  VOID assign_part_seed_var_specs(asINT32 spec_base_index);

  VOID parse_smart_seed_options(int *argc, char *argv[]);
  VOID parse_multi_smart_seed_options(std::vector<std::string> multi_seed_args);
  VOID parse_mme_options(int *argc, char *argv[]);

  VOID print(std::ostream &stream) const;

  template<typename SEED_VAR_TYPE> 
  std::vector<tSEED_VAR_SPEC<SEED_VAR_TYPE>>* var_specs_from_seed_type();

private:
  //VOID setup_parameters();

  VOID scale_seed_vars_via_dimless_properties(sFLOAT seed_vars[][N_VOXELS_8],
                                              asINT32 voxel);

  VOID initialize_seed_data_cache();
  VOID initialize_seed_norm_data_cache();
  sriLRF_INDEX get_all_vars_and_lrf_index(sINT64 meas_cell_index, sriFLOAT meas_vars[DGF_N_SEED_VARS], sriFLOAT meas_uds_vars[MAX_N_SEED_UDS_VARS]);
  VOID get_cell_surface_normal(sINT32 nw_meas_cell_index, sriFLOAT cell_normal[3]);

  VOID get_seed_var_page(sINT64 meas_cell_index, 
                          asINT32 n_points_in_page, 
                          sFLOAT *seed_data_table_page);

  VOID get_seed_ref_frame_index_page(sINT64 meas_cell_index, 
                                     asINT32 n_points_in_page, 
                                     sriLRF_INDEX *seed_ref_frame_index_table_page);
  VOID get_seed_surface_normal_page(sINT64 nw_meas_cell_index, 
				    asINT32 n_points_in_page,
				    sriFLOAT *seed_var_table_page);

  VOID rotate_seed_vars_to_global_csys(sriFLOAT seed_vars[][N_VOXELS_8], asINT32 voxel, sriLRF_INDEX lrf_index);
  VOID rotate_seed_vars_to_local_csys(sriFLOAT seed_vars[][N_VOXELS_8], asINT32 voxel, sriLRF_INDEX lrf_index);

} *SMART_SEED_CONTROL;

inline std::ostream & operator << (std::ostream &out, const cSMART_SEED_CONTROL &smart_seed_control) {
    smart_seed_control.print(out);
    return out;
  }

typedef class cSEED_CONTROL {
  //We might need to support seeding a whole menagerie of realms and flow domains from different seed files.

  std::vector<std::string> m_seed_filenames;
  std::vector<SMART_SEED_CONTROL> m_seed_controllers;
  BOOLEAN m_is_mme_checkpoint;
  BOOLEAN m_has_boundary_seed_controller;

  public:

  SMART_SEED_CONTROL add_seed_controller(cSTRING seed_fname, asINT32 seed_frame=-1, BOOLEAN is_mme_checkpoint=FALSE) {
    if (!seed_fname) {
      return nullptr;
    }
    SRI_FILE sri_file = open_smart_seed_file(seed_fname);
    REALM realm = check_seed_file_type_and_determine_realm(sri_file);
    SMART_SEED_CONTROL seed_controller = xnew cSMART_SEED_CONTROL(sri_file, realm, seed_frame);
    m_seed_controllers.push_back(seed_controller);
    m_seed_filenames.push_back(seed_fname);
    m_is_mme_checkpoint = is_mme_checkpoint;
    return seed_controller;
  }

  SMART_SEED_CONTROL get_seed_controller(std::size_t seed_controller) {
    return m_seed_controllers[seed_controller];
  }

  asINT32 number_of_seed_files(){
    return m_seed_filenames.size();
  }
  
  BOOLEAN has_boundary_seed_controller() {
    return m_has_boundary_seed_controller;
  }

  VOID set_has_boundary_seed_controller(BOOLEAN has_boundary_seed_controller) {
    m_has_boundary_seed_controller = has_boundary_seed_controller;
  }

  BOOLEAN is_sim_seeded() {
    return m_seed_filenames.size() != 0;
  }

  BOOLEAN is_mme_checkpoint() {
    return m_is_mme_checkpoint;
  }

  cSTRING seed_filename(int nth_seed_file) {
    cSTRING nth_seed_filename = NULL;
    if (is_sim_seeded()) {
      nth_seed_filename = m_seed_filenames[nth_seed_file].c_str();
    }
    return nth_seed_filename;
  }

  SRI_FILE open_smart_seed_file(cSTRING seed_filename);

  VOID print(std::ostream &stream) const {
    stream << "cSEED_CONTROL:" << std::endl;
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->print(stream);
    }
  }

  VOID send_header_to_sps();

  VOID setup() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->setup();
    }
    if (m_seed_controllers.size() > 0) {
      send_header_to_sps();
    }
  }

  VOID wrapup() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->wrapup();
    }
  }

  template <typename SEED_VAR_TYPE, asINT32 N_SEED_VARS, bool IS_FLUID_SEED>
  VOID parse_seed_var_specs(bool do_smart_seed, 
                            int *argc, 
                            char *argv[], 
                            cSTRING include_cmdline_arg, 
                            cSTRING exclude_cmdline_arg,
                            BOOLEAN is_volumetric_region,
                            std::map <std::string, SEED_VAR_TYPE > &seed_var_string_to_seed_var_type,
                            SEED_VAR_TYPE default_seed_vars[], 
                            asINT32 n_default_seed_vars,
                            SEED_VAR_TYPE default_solid_seed_vars[], 
                            asINT32 n_default_solid_seed_vars,
                            asINT32 n_extra_inclusion_seed_vars, 
                            cSTRING purpose);

  SRI_FILE seed_file(std::size_t seed_controller) {
    if (m_seed_controllers.size() == 0) {
      return nullptr;
    }
    return m_seed_controllers[seed_controller]->seed_file();
  }

  BOOLEAN is_smart_seed() { 
    if (m_seed_controllers.size() == 0) {
      return FALSE;
    }
    return TRUE; 
  }

  BOOLEAN seed_via_mks() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      if (seed_controller->seed_via_mks()) {
        return TRUE;
      }
    }
    return FALSE;
  }

  BOOLEAN do_extrapolate() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      if (seed_controller->do_extrapolate()) {
        return TRUE;
      }
    }
    return FALSE;
  }

  BOOLEAN smart_seed_boundaries() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      if (seed_controller->smart_seed_boundaries()) {
        return TRUE;
      }
    }
    return FALSE;
  }

  BOOLEAN smart_seed_contact_angle() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      if (seed_controller->smart_seed_contact_angle()) {
        return TRUE;
      }
    }
    return FALSE;
  }

  BOOLEAN is_seed_rotate_vel() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      if (seed_controller->is_seed_rotate_vel()) {
        return TRUE;
      }
    }
    return FALSE;
  }

  VOID check_boundary_face_bseed_vars(std::vector<sBSEED_FACE_CHECK>& bseed_face_checks, 
                                      std::vector<sBSEED_FACE_CHECK>& bseed_var_face_checks)
  {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->check_boundary_face_bseed_vars(bseed_face_checks, bseed_var_face_checks);
    }
  }

  VOID check_seed_var_spec_physics_desc()
  {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->check_seed_var_spec_physics_desc();
    }
  }


  VOID detect_fluid_physics_desc_for_seeded_regions(cSTRING physics_desc_name, const std::vector<asINT32>& rgns)
  {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->detect_fluid_physics_desc_for_seeded_regions(physics_desc_name, rgns);
    }
  }

  VOID assign_part_seed_var_specs() {
    asINT32 spec_base_index = 0;
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->assign_part_seed_var_specs(spec_base_index);
      spec_base_index += seed_controller->n_fluid_seed_var_specs();
    }
  }


  VOID dont_write_smart_seed_data(auINT32 fluid_like_voxel_mask, asINT32 ublk_home_sp);

  VOID maybe_write_smart_seed_data(cDGF_UBLK_BASE_DESC *ublk_desc,
                                   auINT32 fluid_like_voxel_mask,
                                   asINT32 ublk_home_sp,
                                   asINT32 simple_part_index,     
                                   sINT32  voxel_part_indices[8],
                                   auINT32 split_voxel_mask) {

    if (m_seed_controllers.size() == 0) {
      return;
    }

    REALM ublk_realm = (DGF_UBLK_CONDUCTION & ublk_desc->b.ublk_flags) == DGF_UBLK_CONDUCTION ? STP_COND_REALM : STP_FLOW_REALM;

    SMART_SEED_CONTROL active_controller = nullptr;
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      
      // The seed controller realm and ublk realm need to be the same
      if (seed_controller->realm() != ublk_realm) {
        continue;
      }

      // The voxel to be seeded needs to be in the file
      if (!seed_controller->is_location_in_this_seed_file(ublk_desc, fluid_like_voxel_mask)) {
        continue;
      }

      active_controller = seed_controller;
      break;
    }
    if (active_controller) {
      active_controller->maybe_write_smart_seed_data(ublk_desc, fluid_like_voxel_mask, ublk_home_sp, simple_part_index, voxel_part_indices, split_voxel_mask);
    } else {
      dont_write_smart_seed_data(fluid_like_voxel_mask, ublk_home_sp);
    }
  }

  VOID setup_mlrf_initial_position() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->setup_mlrf_initial_position();
    }
  }

  VOID setup_movb_initial_position();

  VOID add_surface_normals(cDGF_SURFEL_DESC *surfel_desc) {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->add_surface_normals(surfel_desc);
    }
  }

  VOID maybe_rotate_vel() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->maybe_rotate_vel();
    }
  };

  VOID maybe_add_var_seed_specs() {
    for (SMART_SEED_CONTROL seed_controller: m_seed_controllers) {
      seed_controller->maybe_add_var_seed_specs();
    }
  };

  VOID clear_surface_normals() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->clear_surface_normals();
    }
  };

  VOID delete_norm_meas_cell_indices() {
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      seed_controller->delete_norm_meas_cell_indices();
    }
  };

  asINT32 n_bseed_var_specs() {
    asINT32 num_bseed_var_specs = 0;
    for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
      if (seed_controller->smart_seed_boundaries()) {
        num_bseed_var_specs = seed_controller->n_boundary_seed_var_specs();
      }
    }
    return num_bseed_var_specs;
  }

  private:

  REALM check_seed_file_type_and_determine_realm(SRI_FILE seed_file) {
    BOOLEAN is_volumetric_data = seed_file->file_type() == SRI_FLUID_TYPE; 
    // With the addition of solid conduction, SRI_FLUID_TYPE has become
    // a misnomer. It now refers to whether a meas file contains
    // volumetric measurements (like only a fnc fluid file previously
    // could) as opposed to surface measurements (like an snc file).
    if(!is_volumetric_data) {
      msg_error("File \"%s\" does not contain volumetric measurements and can't be used for seeding.", seed_file->name());
      return false;
    }

    // To distinguish between solid and fluid volumetric measurements
    // for a file of SRI_FLUID_TYPE, use the is_solid_file file
    // property.
    return seed_file->file_params()->is_solid_file ? STP_COND_REALM : STP_FLOW_REALM;
  }

} *SEED_CONTROL;

inline std::ostream & operator << (std::ostream &out, const cSEED_CONTROL &seed_control) {
    seed_control.print(out);
    return out;
  }

//extern cOLD_SEED_CONTROL g_smart_seed_ctl;
extern cSEED_CONTROL g_seed_ctl;


VOID read_fluid_seed_vars_specs();  //new

#endif
