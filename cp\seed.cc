/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#include "common.h"
#include "seed.h"
#include "cp_info.h"
#include "cp_stream_manager.h"
#include "window_results.h"
#include <cstring>
#include <iostream>

//cOLD_SEED_CONTROL g_smart_seed_ctl(STP_FLOW_REALM);
cSEED_CONTROL g_seed_ctl;

std::vector<std::string> tSEED_VAR_SPEC_BASE::fluid_seed_var_names = {
  "static_pressure", 
  "x_velocity",
  "y_velocity",
  "z_velocity",
  "temp",
  "turb_kinetic_energy",
  "turb_dissipation",
  "density",
  "stress_tensor_mag",
  "water_vapor_mfrac",
  "comp0_density",
  "comp1_density",
  "contact_angle",
  "dynamic_scalar_multiplier",
  "ustar",
  "density_frozen"};

std::vector<std::string> tSEED_VAR_SPEC_BASE::boundary_seed_var_names = {
  "pressure",
  "total_pressure",
  "x_velocity",
  "y_velocity",         
  "z_velocity",         
  "flow_dir_x",
  "flow_dir_y",
  "flow_dir_z",
  "temp",        
  "turb_kinetic_energy",
  "turb_dissipation",   
  "x_mass_flux",        
  "y_mass_flux",        
  "z_mass_flux",        
  "mass_flow"};

//#define DISABLE_CROSS_REALM_SEED_FILES

#define DEN_THRESHOLD 1.0e-6
#define N_FROZEN_VARS   6

// This function takes a cSTRING seed_var_list, and populates a seed_var_spec
template <typename SEED_VAR_TYPE, asINT32 N_SEED_VARS, bool IS_VOLUMETRIC_SEED> // IS_VOLUMETRIC_SEED is FALSE if boundary seeding
VOID cSMART_SEED_CONTROL::parse_seed_var_spec_vars(std::string seed_vars[],
                                                   asINT32 n_seed_vars,
                                                   tSEED_VAR_SPEC< SEED_VAR_TYPE > *seed_var_spec,
                                                   cSTRING include_cmdline_arg, 
                                                   cSTRING exclude_cmdline_arg,
                                                   std::map <std::string, SEED_VAR_TYPE > &seed_var_string_to_seed_var_type,
                                                   asINT32 n_extra_inclusion_seed_vars)
{
  SEED_VAR_TYPE var_indicies[N_SEED_VARS]; 
  sINT32  uds_var_indices[MAX_N_SEED_UDS_VARS];
  asINT32 n_vars = 0;
  asINT32 n_uds_vars = 0;
  ccDOTIMES(i, n_seed_vars) {
    std::string var = seed_vars[i];
    typename std::map<std::string, SEED_VAR_TYPE>::iterator it = seed_var_string_to_seed_var_type.find(var);

    SEED_VAR_TYPE seed_var_type;
    sINT32 seed_uds_var_index;
    BOOLEAN find_variable = FALSE;
    BOOLEAN find_uds_variable = FALSE;
    if (it != seed_var_string_to_seed_var_type.end()) {
      seed_var_type = (*it).second;
      find_variable = TRUE;
    } else if (IS_VOLUMETRIC_SEED)  {
      if (cp_info.is_5g_sim) {
        // Check if it is a 5g component density
        SRI_VARIABLE_TYPE var_type = SRI_VARIABLE_INVALID;
        SRI_VARIABLE_TYPE var_base_type = SRI_VARIABLE_INVALID;
        ccDOTIMES(i, cp_info.num_fluid_components) {
          var_base_type = SRI_VARIABLE_DENSITY;
          var_type = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(var_base_type, i, -1);
          std::string short_name = cp_info.cvid_helper->variable_5g_short_name(var_type, cp_info.s_fluid_components_5g);
          if (short_name == var) {
            if (i==0)
              seed_var_type = (SEED_VAR_TYPE)DGF_SEED_VAR_COMP0_DENSITY; //type cast is only to make compiler happy
            else if (i==1)
              seed_var_type = (SEED_VAR_TYPE)DGF_SEED_VAR_COMP1_DENSITY; //type cast is only to make compiler happy
            find_variable = TRUE;
            break;
          }  
        }
      } else if (cp_info.is_uds_transport) { //keep 5G out  until CASE support for 5G UDS is available;
        SRI_VARIABLE_TYPE uds_var_type = SRI_VARIABLE_INVALID;
        eSRI_VARIABLE_UDS_OFFSET uds_var_offset = SRI_VARIABLE_INVALID_UDS_OFFSET;
        ccDOTIMES(i, cp_info.n_scalars) {
          uds_var_offset = SRI_VARIABLE_UDS_SCALAR_OFFSET;
          uds_var_type = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_uds_var_id(uds_var_offset, i);
          std::string short_name = cp_info.cvid_helper->variable_uds_short_name(uds_var_type, cp_info.s_scalar_materials);
          if (short_name == var) {
            seed_uds_var_index = (int)DGF_SEED_UDS_VAR_SCALAR_VALUE + i * (int)DGF_N_SEED_UDS_VARS;	    	    
            find_uds_variable = TRUE;
            break;
          }
        }	
      }
    }

    if ((!find_variable  && !find_uds_variable) || (find_variable && find_uds_variable))
      msg_error("Invalid variable \"%s\" provided to -%s option", var.c_str(),
                seed_var_spec->is_seed_vars_inclusion_list ? include_cmdline_arg : exclude_cmdline_arg);

    // Do not allow duplicate vars
    BOOLEAN is_duplicate = FALSE;
    if (find_variable) {
      ccDOTIMES(j, n_vars) {
        if (var_indicies[j] == seed_var_type)
          is_duplicate = TRUE;
      }
      if (!is_duplicate)
        var_indicies[n_vars++] = seed_var_type;
    } else if (find_uds_variable) { 
      ccDOTIMES(k, n_uds_vars) {
        if (uds_var_indices[k] == seed_uds_var_index)
          is_duplicate = TRUE;
      }
      if (!is_duplicate)
        uds_var_indices[n_uds_vars++] = seed_uds_var_index;    
    }
  }

  if (seed_var_spec->is_seed_vars_inclusion_list) {
     // For fluid region seed var specs, allocate 2 extra slots for DENSITY and STRESS_TENSOR_MAG necessary.
     if (n_vars > 0) {
       seed_var_spec->seed_var_types = xnew SEED_VAR_TYPE[n_vars + n_extra_inclusion_seed_vars];
       ccDOTIMES(i, n_vars)
 	seed_var_spec->seed_var_types[i] = var_indicies[i];
     } else {
       seed_var_spec->seed_var_types = NULL;
     }
     seed_var_spec->n_vars = n_vars;
 
     //UDS
     if (n_uds_vars > 0) {
       seed_var_spec->seed_uds_var_indices = xnew sINT32[n_uds_vars];
       ccDOTIMES(i, n_uds_vars)
 	seed_var_spec->seed_uds_var_indices[i] = uds_var_indices[i];
     } else {
       seed_var_spec->seed_uds_var_indices = NULL;
     }
     seed_var_spec->n_uds_vars = n_uds_vars;
     
  } else {
    seed_var_spec->n_vars = N_SEED_VARS - n_vars;
    seed_var_spec->seed_var_types = xnew SEED_VAR_TYPE[seed_var_spec->n_vars];
    bool is_var_excluded[N_SEED_VARS] = { false };
    ccDOTIMES(i, n_vars)
      is_var_excluded[var_indicies[i]] = true;

    asINT32 v = 0;
    ccDOTIMES(i, N_SEED_VARS) {
      if (!is_var_excluded[i])
        seed_var_spec->seed_var_types[v++] = (SEED_VAR_TYPE)i;
    }

    // UDS
    if (cp_info.is_uds_transport && !cp_info.is_5g_sim && IS_VOLUMETRIC_SEED) {
      sINT32 total_n_uds_vars = cp_info.n_scalars * (int)DGF_N_SEED_UDS_VARS;
      seed_var_spec->n_uds_vars = total_n_uds_vars - n_uds_vars;
      seed_var_spec->seed_uds_var_indices = xnew sINT32[seed_var_spec->n_uds_vars];
      bool is_uds_var_excluded[ MAX_N_SEED_UDS_VARS] = { false };
      ccDOTIMES(i, n_uds_vars)
	is_uds_var_excluded[uds_var_indices[i]] = true;

      asINT32 v = 0;
      ccDOTIMES(i, total_n_uds_vars) {
	if (!is_uds_var_excluded[i])
	  seed_var_spec->seed_uds_var_indices[v++] = i;
      }     
    } else {
      seed_var_spec->n_uds_vars = 0;
      seed_var_spec->seed_uds_var_indices = NULL;
    }
  }
}

template<typename SEED_VAR_TYPE> 
std::vector<tSEED_VAR_SPEC<SEED_VAR_TYPE>>* cSMART_SEED_CONTROL::var_specs_from_seed_type() {
  static_assert(std::is_same<SEED_VAR_TYPE, DGF_SEED_VAR_TYPE>::value || 
                std::is_same<SEED_VAR_TYPE, DGF_BOUNDARY_SEED_VAR_TYPE>::value, "cSMART_SEED_CONTROL doesn't have a var spec of the required type");
  return nullptr;
}

template<>
std::vector<tSEED_VAR_SPEC<DGF_BOUNDARY_SEED_VAR_TYPE>>* cSMART_SEED_CONTROL::var_specs_from_seed_type() {
  return &m_boundary_seed_var_specs; //&boundary_seed_var_specs();
}

template<>
std::vector<tSEED_VAR_SPEC<DGF_SEED_VAR_TYPE>>* cSMART_SEED_CONTROL::var_specs_from_seed_type() {
  return &m_fluid_seed_var_specs; //&fluid_seed_var_specs();
}

template <typename SEED_VAR_TYPE, asINT32 N_SEED_VARS, bool IS_VOLUMETRIC_SEED>
VOID cSMART_SEED_CONTROL::parse_seed_var_specs(bool do_smart_seed, 
                                               int *argc, 
                                               char *argv[], 
                                               cSTRING include_cmdline_arg, 
                                               cSTRING exclude_cmdline_arg,
                                               BOOLEAN is_volumetric_region,
                                               std::map <std::string, SEED_VAR_TYPE > &seed_var_string_to_seed_var_type,
                                               SEED_VAR_TYPE default_seed_vars[], 
                                               asINT32 n_default_seed_vars,
                                               asINT32 n_extra_inclusion_seed_vars, 
                                               cSTRING purpose)
{

  std::vector< tSEED_VAR_SPEC< SEED_VAR_TYPE > >* seed_var_specs_ptr = var_specs_from_seed_type<SEED_VAR_TYPE>();
  std::vector< tSEED_VAR_SPEC< SEED_VAR_TYPE > > &seed_var_specs = *seed_var_specs_ptr;

  const std::string region_or_face = is_volumetric_region? "Fluid region" : "Face";
  const std::string region_or_face_lower_case = is_volumetric_region? "fluid region" : "face";
  // First add the default seed_var_spec
  tSEED_VAR_SPEC< SEED_VAR_TYPE > default_seed_var_spec;
  default_seed_var_spec.entity_name = "-"; // fluid region or boundary name
  default_seed_var_spec.is_seed_vars_inclusion_list = TRUE;
  default_seed_var_spec.seed_var_types = default_seed_vars;
  default_seed_var_spec.n_vars = n_default_seed_vars;

  //UDS
  if (cp_info.is_uds_transport && !cp_info.is_5g_sim && IS_VOLUMETRIC_SEED) { //for fluid seeding only
    sINT32 n_uds_vars = cp_info.n_scalars * (int)DGF_N_SEED_UDS_VARS;
    default_seed_var_spec.n_uds_vars = n_uds_vars;
    default_seed_var_spec.seed_uds_var_indices = xnew sINT32[n_uds_vars];
    for (int index = 0; index < n_uds_vars; index++)
      default_seed_var_spec.seed_uds_var_indices[index] = index;  //[nth_uds][dgf_seed_uds_var_type]
  } else {
    default_seed_var_spec.n_uds_vars = 0;
    default_seed_var_spec.seed_uds_var_indices = NULL;
  }
  
  seed_var_specs.push_back(default_seed_var_spec); // at index 0 by convention

  std::string all_others_msg;
  // Now add seed_var_specs provided by the user on the command line
  sINT32 count_fluid_exclude = 0;
  sINT32 count_fluid_include = 0;
  sINT32 count_boundary_exclude = 0;
  sINT32 count_boundary_include = 0;
  while (1) {
    tSEED_VAR_SPEC< SEED_VAR_TYPE > new_seed_var_spec;
    cSTRING seed_vars;
    
    if (IS_VOLUMETRIC_SEED) {
      sINT32 n_fluid_exclude = m_fluid_seed_exclude_vars.size();
      sINT32 n_fluid_include = m_fluid_seed_include_vars.size();
      if (count_fluid_exclude < n_fluid_exclude) {
        seed_vars = m_fluid_seed_exclude_vars[count_fluid_exclude].c_str();
        new_seed_var_spec.is_seed_vars_inclusion_list = FALSE;
        count_fluid_exclude++;
      } else {
        if (count_fluid_include < n_fluid_include) {
          seed_vars = m_fluid_seed_include_vars[count_fluid_include].c_str();
          new_seed_var_spec.is_seed_vars_inclusion_list = TRUE;
          count_fluid_include++;
        } else {
          break;
        }
      }
    } else {
      sINT32 n_boundary_exclude = m_boundary_seed_exclude_vars.size();
      sINT32 n_boundary_include = m_boundary_seed_include_vars.size();
      if (count_boundary_exclude < n_boundary_exclude) {
        seed_vars = m_boundary_seed_exclude_vars[count_boundary_exclude].c_str();
        new_seed_var_spec.is_seed_vars_inclusion_list = FALSE;
        count_boundary_exclude++;
      } else {
        if (count_boundary_include < n_boundary_include) {
          seed_vars = m_boundary_seed_include_vars[count_boundary_include].c_str();
          new_seed_var_spec.is_seed_vars_inclusion_list = TRUE;
          count_boundary_include++;
        } else {
          break;
        }
      }
    }
    
    if (seed_vars) {
      const std::string delims(",");
      std::vector<std::string> seed_entity_and_vars = EXA_STR::StripSplit(seed_vars,delims);
      std::string &entity_name = seed_entity_and_vars[0];
      if (entity_name == "-") {
        // user provided a default seed_var_spec
        tSEED_VAR_SPEC< SEED_VAR_TYPE > *seed_var_spec = &seed_var_specs[0];
        seed_var_spec->is_seed_vars_inclusion_list = new_seed_var_spec.is_seed_vars_inclusion_list;
        parse_seed_var_spec_vars< SEED_VAR_TYPE, N_SEED_VARS, IS_VOLUMETRIC_SEED >
          (&seed_entity_and_vars[1], 
           seed_entity_and_vars.size() - 1, 
           seed_var_spec, 
           include_cmdline_arg, 
           exclude_cmdline_arg,
           seed_var_string_to_seed_var_type, 
           n_extra_inclusion_seed_vars);
        
        // Construct msg for all other regions/faces
        asINT32 n_seed_vars = seed_entity_and_vars.size() - 1;
        if (do_smart_seed 
            && (seed_entity_and_vars.size() > 1 || seed_var_spec->is_seed_vars_inclusion_list)) {
          // %s is a placeholder for "other " or nothing
          all_others_msg = std::string("For all %s") + region_or_face_lower_case + "s, ";
          if (seed_var_specs[0].is_seed_vars_inclusion_list) {
            if (n_seed_vars > 0)
              all_others_msg += std::string("only the following variables from the seed file will be used") + purpose + ": ";
            else
              all_others_msg += std::string("no variables from the seed file will be used") + purpose + ".";
          } else
            all_others_msg += std::string("the following variables from the seed file will not be used") + purpose + ": ";
          if (n_seed_vars > 0) {
            for (asINT32 i = 1; i < seed_entity_and_vars.size() - 1; i++)
              all_others_msg += seed_entity_and_vars[i] + ", ";
            all_others_msg += seed_entity_and_vars.back() + ".";
          }
        }
        
      } else if (entity_name != "") {
        auto entity_type = cdi_get_geometry_type_from_entity_name(entity_name);
        BOOLEAN entity_is_a_segment = is_entity_type_a_segment(entity_type);
        BOOLEAN entity_is_a_part = is_entity_type_a_part(entity_type);
        BOOLEAN entity_is_a_part_or_segment = entity_is_a_part || entity_is_a_segment;
        tSEED_VAR_SPEC< SEED_VAR_TYPE > *seed_var_spec = &new_seed_var_spec;
        for (asINT32 i = 1; i < seed_var_specs.size(); i++) {
          if (seed_var_specs[i].entity_name == entity_name) {
            seed_var_spec = &seed_var_specs[i];
            seed_var_spec->is_seed_vars_inclusion_list = new_seed_var_spec.is_seed_vars_inclusion_list;
            msg_warn("%s name \"%s\" appears in multiple -%s and -%s"
                     " specifications. The last entry on the command line has precedence.", 
                     entity_is_a_segment? "Segment" : region_or_face.c_str(),
                     entity_name.c_str(), exclude_cmdline_arg, include_cmdline_arg);
            break;
          }
        }
        seed_var_spec->entity_name = entity_name;
        parse_seed_var_spec_vars< SEED_VAR_TYPE, N_SEED_VARS, IS_VOLUMETRIC_SEED >
          (&seed_entity_and_vars[1], 
           seed_entity_and_vars.size() - 1, 
           seed_var_spec,
           include_cmdline_arg, 
           exclude_cmdline_arg,
           seed_var_string_to_seed_var_type, 
           n_extra_inclusion_seed_vars);
        
        // Print msg for this region/face
        asINT32 n_seed_vars = seed_entity_and_vars.size() - 1;
        if (do_smart_seed 
            && (n_seed_vars > 0 || seed_var_spec->is_seed_vars_inclusion_list)) {
          
          std::string msg_header_region = entity_is_a_segment? "For all ": "For ";
          std::string msg_header_faces = entity_is_a_part_or_segment? "For all inlet and outlet " : "For ";
          std::string msg = is_volumetric_region? msg_header_region : msg_header_faces;
          msg += region_or_face_lower_case;
          std::string include_msg_region = entity_is_a_segment? "s included in segment" : "";
          std::string include_msg_face = entity_is_a_part_or_segment?
            (entity_is_a_segment? "s included in segment" : "s of part") : "";
          msg += is_volumetric_region? include_msg_region : include_msg_face;
          msg += " \"" + entity_name + "\", ";
          if (seed_var_spec->is_seed_vars_inclusion_list) {
            if (n_seed_vars > 0)
              msg += std::string("only the following variables from the seed file will be used") + purpose + ": ";
            else
              msg += std::string("no variables from the seed file will be used") + purpose + ".";
          } else
            msg += std::string("the following variables from the seed file will not be used") + purpose + ": ";
          if (n_seed_vars > 0) {
            for (asINT32 i = 1; i < seed_entity_and_vars.size() - 1; i++)
              msg += seed_entity_and_vars[i] + ", ";
            msg_print("%s%s.", msg.c_str(), seed_entity_and_vars.back().c_str());
          } else
            msg_print("%s", msg.c_str());
        }

        if (seed_var_spec == &new_seed_var_spec) {
          seed_var_specs.push_back(*seed_var_spec);
        }
      }
    }
  }

  if (!all_others_msg.empty()) {
    // Note %s inserted in all_others_msg above
    msg_print(all_others_msg.c_str(), seed_var_specs.size() > 1 ? "other " : "");
    msg_print(""); // a newline
  } else if (n_boundary_seed_var_specs() > 1)
    msg_print(""); // a newline

}

VOID cSMART_SEED_CONTROL::maybe_rotate_vel() {
  if (m_params.is_seed_rotate_vel) {
    uINT32 num_seed_var_specs = n_fluid_seed_var_specs();
    ccDOTIMES(i, num_seed_var_specs) {
      uINT8 num_vel_components_included = 0;
      ccDOTIMES(j, fluid_seed_var_specs()[i].n_vars) {
        DGF_SEED_VAR_TYPE seed_var_type = fluid_seed_var_specs()[i].seed_var_types[j];
        if (seed_var_type == DGF_SEED_VAR_XVEL || seed_var_type == DGF_SEED_VAR_YVEL || seed_var_type == DGF_SEED_VAR_ZVEL) {
          num_vel_components_included += 1;
        }
      }
      if (num_vel_components_included > 0 && num_vel_components_included < 3){
        msg_warn("The --seed_rotate_vel option will be ignored in region \"%s\" because not all 3 velocity components are seeded.", fluid_seed_var_specs()[i].entity_name.c_str());
      }
    }
  }
}

// Add DENSITY and STRESS_TENSOR_MAG to all inclusion-list fluid region seed var specs. These may be
// filtered out in cSMART_SEED_CONTROL::setup().
VOID cSMART_SEED_CONTROL::maybe_add_var_seed_specs() {
  for (asINT32 i=1; i<n_fluid_seed_var_specs(); i++) { // loop over just the non-default ones
    FLUID_SEED_VAR_SPEC seed_var_spec = &fluid_seed_var_specs()[i];
    if (seed_var_spec->is_seed_vars_inclusion_list) {
      asINT32 n_vars = seed_var_spec->n_vars;
      seed_var_spec->seed_var_types[n_vars]     = DGF_SEED_VAR_DENSITY;
      seed_var_spec->seed_var_types[n_vars + 1] = DGF_SEED_VAR_STRESS_TENSOR_MAG;
      seed_var_spec->n_vars = n_vars + 2;

      if (!cp_info.is_5g_sim) {
        seed_var_spec->seed_var_types[n_vars+2] = DGF_SEED_VAR_DYNAMIC_SCALAR_MULTIPLIER; //borrow to flag if the fluid is in fine->coarse seeding region
        seed_var_spec->n_vars = n_vars + 3;
      }
    }
  }
}

static cBOOLEAN is_float(std::string str)
{
  std::istringstream iss(str);
  dFLOAT f;
  iss >> std::noskipws >> f; // noskipws considers leading whitespace invalid
  // Check the entire string was consumed and if either failbit or badbit is set
  return iss.eof() && !iss.fail();
}

static cBOOLEAN is_int(std::string str)
{
  std::istringstream iss(str);
  asINT32 f;
  iss >> std::noskipws >> f; // noskipws considers leading whitespace invalid
  // Check the entire string was consumed and if either failbit or badbit is set
  return iss.eof() && !iss.fail();
}

std::unordered_map<std::string, cSMART_SEED_CONTROL::eMSEED_OPTIONS> cSMART_SEED_CONTROL::mseed_options = 
{
  {"seedframe", eMSEED_OPTIONS::SEEDFRAME},
  {"seed_mks", eMSEED_OPTIONS::SEED_MKS},
  {"no_extrapolate", eMSEED_OPTIONS::NO_EXTRAPOLATE},
  {"extrapolate", eMSEED_OPTIONS::EXTRAPOLATE},
  {"smart_seed_boundaries", eMSEED_OPTIONS::SMART_SEED_BOUNDARIES},
  {"smart_seed_contact_angle", eMSEED_OPTIONS::SMART_SEED_CONTACT_ANGLE},
  {"seed_rotate_vel", eMSEED_OPTIONS::SEED_ROTATE_VEL},
  {"seed_exclude_vars", eMSEED_OPTIONS::SEED_EXCLUDE_VARS},
  {"seed_include_vars", eMSEED_OPTIONS::SEED_INCLUDE_VARS},
  {"bseed_exclude_vars", eMSEED_OPTIONS::BSEED_EXCLUDE_VARS},
  {"bseed_include_vars", eMSEED_OPTIONS::BSEED_INCLUDE_VARS}
};

VOID cSMART_SEED_CONTROL::parse_multi_smart_seed_options(std::vector<std::string> multi_seed_args)
{

  m_params.seed_via_mks = FALSE;
  m_params.is_seed_rotate_vel = FALSE;

  const std::string delims(",");
  for (auto marg : multi_seed_args) {
    std::vector<std::string> sub_args = EXA_STR::StripSplit(marg, delims);
    std::string first_sub_arg = sub_args[0];

    if (mseed_options.count(first_sub_arg) == 0) {
      msg_error("Invalid option specified for -mseed. \"%s\" is not a valid option.", first_sub_arg.c_str());
    }
    eMSEED_OPTIONS option = mseed_options[first_sub_arg];

    switch (option) {
      case eMSEED_OPTIONS::SEEDFRAME:
        if (is_int(sub_args[1])) {
          m_seed_frame = atoi(sub_args[1].c_str());
        } else {
          msg_error("Invalid seedframe passed to the option seedframe. \"%s\" is not an integer.", sub_args[1].c_str());
        }
        break;
      case eMSEED_OPTIONS::SEED_MKS:
        m_params.seed_via_mks = TRUE;
        break;
      case eMSEED_OPTIONS::NO_EXTRAPOLATE:
        m_do_extrapolate = FALSE;
        break;
      case eMSEED_OPTIONS::EXTRAPOLATE:
        m_do_extrapolate = TRUE;
        break;
      case eMSEED_OPTIONS::SMART_SEED_BOUNDARIES:
        if (g_seed_ctl.has_boundary_seed_controller()) {; // A controller is already doing boundary_seeding
          msg_error("Only one smart seed controller is allowed to do boundary seeding.");
        }
        m_smart_seed_boundaries = TRUE;
        g_seed_ctl.set_has_boundary_seed_controller(TRUE);
        break;
      case eMSEED_OPTIONS::SMART_SEED_CONTACT_ANGLE:
        if (cp_info.is_5g_sim) {
          m_smart_seed_contact_angle = TRUE;
        }
        break;
      case eMSEED_OPTIONS::SEED_ROTATE_VEL:
        {
          m_params.is_seed_rotate_vel = TRUE;
          sub_args.erase(sub_args.begin()); // remove seed_rotate_vel arg
          
          // csys
          char *temp = xnew char[sub_args[0].size()+1];
          std::copy(sub_args[0].begin(), sub_args[0].end(), temp);
          temp[sub_args[0].size()] = '\0';
          m_params.seed_rotate_vel_csys_name = temp;

          //axis
          if (sub_args[1] == "x") {
            m_params.seed_rotate_vel_axis = 0;
          } else if (sub_args[1] == "y") {
            m_params.seed_rotate_vel_axis = 1;
          } else if (sub_args[1] == "z") {
            m_params.seed_rotate_vel_axis = 2;
          } else {
            msg_error("Invalid rotation axis passed to the option seed_rotate_vel. Choose x, y or z.");
          }

          // angle
          if (is_float(sub_args[2])) {
            m_params.seed_rotate_vel_angle = atof(sub_args[2].c_str());
            if (m_params.seed_rotate_vel_angle == 0.0) {
              msg_warn("Rotation angle is zero. No rotation will be performed.");
            }
          } else {
            msg_error("Invalid rotation angle passed to the option --seed_rotate_vel. \"%s\" is not a real number.", sub_args[2].c_str());
          }
          break;
        }
      case eMSEED_OPTIONS::SEED_EXCLUDE_VARS:
        {
          sub_args.erase(sub_args.begin()); // remove seed_exclude_vars arg
          std::string input = EXA_STR::Join(sub_args, ","); // This should be a string of the form "region,var1,var2,..."
          m_fluid_seed_exclude_vars.push_back(input);
          break;
        }
      case eMSEED_OPTIONS::SEED_INCLUDE_VARS:
        {
          sub_args.erase(sub_args.begin()); // remove seed_include_vars arg
          std::string input = EXA_STR::Join(sub_args, ","); // This should be a string of the form "region,var1,var2,..."
          m_fluid_seed_include_vars.push_back(input);
          break;
        }
      case eMSEED_OPTIONS::BSEED_EXCLUDE_VARS:
        {
          sub_args.erase(sub_args.begin()); // remove bseed_exclude_vars arg
          std::string input = EXA_STR::Join(sub_args, ","); // This should be a string of the form "region,var1,var2,..."
          m_boundary_seed_exclude_vars.push_back(input);
          break;
        }
      case eMSEED_OPTIONS::BSEED_INCLUDE_VARS:
        {
          sub_args.erase(sub_args.begin()); // remove bseed_include_vars arg
          std::string input = EXA_STR::Join(sub_args, ","); // This should be a string of the form "region,var1,var2,..."
          m_boundary_seed_include_vars.push_back(input);
          break;
        }
      case eMSEED_OPTIONS::N_MSEED_OPTIONS:
        break;
    }

  }

}

VOID cSMART_SEED_CONTROL::parse_smart_seed_options(int *argc, char *argv[])
{

  m_params.seed_via_mks = FALSE;
  while (parse_arg_present_until("-seed_mks", "-smart_seed", argc, argv)) {
    m_params.seed_via_mks = TRUE;
  }

  while (parse_arg_present_until("-no_extrapolate", "-smart_seed", argc, argv)) {
    m_do_extrapolate = FALSE;
  }

  while (parse_arg_present_until("-extrapolate", "-smart_seed", argc, argv)) {
    m_do_extrapolate = TRUE;
  }

  while (parse_arg_present_until("-smart_seed_boundaries", "-smart_seed", argc, argv)) {
    // Call something in g_seed_ctl to check if this is true for any previous
    // controllers. If it is, issue an error that we're ignoring it and
    // setting it to false for this controller.
    if (g_seed_ctl.has_boundary_seed_controller()) {; // A controller is already doing boundary_seeding
      msg_error("Only one smart seed controller is allowed to do boundary seeding.");
    }
    m_smart_seed_boundaries = TRUE;
    g_seed_ctl.set_has_boundary_seed_controller(TRUE);
  }

  while (parse_arg_present_until("-smart_seed_contact_angle", "-smart_seed", argc, argv)) {
    if (cp_info.is_5g_sim) {
      m_smart_seed_contact_angle = TRUE;
    }
  }

  parse_cmdline_dgf_seed_var_specs_until(argc, argv, "-seed_include_vars", "-seed_exclude_vars", "-smart_seed");
  parse_cmdline_dgf_bseed_var_specs_until(argc, argv, "-bseed_include_vars", "-bseed_exclude_vars", "-smart_seed");

  // Rotate the seed velocity by a specified angle about the x,y or z axis of any coordinate system
  cSTRING seed_rotate_vel = NULL;
  m_params.is_seed_rotate_vel = FALSE;
  while (seed_rotate_vel = parse_arg_string_until("-seed_rotate_vel", "-smart_seed", argc, argv)) {

    m_params.is_seed_rotate_vel = TRUE;

    const std::string delims(",");
    std::vector<std::string> csys_axis_angle = EXA_STR::StripSplit(seed_rotate_vel, delims);

    // csys
    char *temp = xnew char[csys_axis_angle[0].size()+1];
    std::copy(csys_axis_angle[0].begin(), csys_axis_angle[0].end(), temp);
    temp[csys_axis_angle[0].size()] = '\0';
    m_params.seed_rotate_vel_csys_name = temp;
 
    // axis
    if (csys_axis_angle[1] == "x")
      m_params.seed_rotate_vel_axis = 0;
    else if (csys_axis_angle[1] == "y")
      m_params.seed_rotate_vel_axis = 1;
    else if (csys_axis_angle[1] == "z")
      m_params.seed_rotate_vel_axis = 2;
    else
      msg_error("Invalid rotation axis passed to the option --seed_rotate_vel. Choose x, y or z.");

    // angle
    if (is_float(csys_axis_angle[2])) {
      m_params.seed_rotate_vel_angle = atof(csys_axis_angle[2].c_str());
      if (m_params.seed_rotate_vel_angle == 0.0)
        msg_warn("Rotation angle is zero. No rotation will be performed.");
    }
    else
      msg_error("Invalid rotation angle passed to the option --seed_rotate_vel. \"%s\" is not a real number.", csys_axis_angle[2].c_str());
  }
}

VOID cSMART_SEED_CONTROL::parse_mme_options(int *argc, char *argv[])
{

  m_params.seed_via_mks = FALSE;
  while (parse_arg_present_p("-seed_mks", argc, argv)) {
    m_params.seed_via_mks = TRUE;
  }

  while (parse_arg_present_p("-no_extrapolate", argc, argv)) {
    m_do_extrapolate = FALSE;
  }

  while (parse_arg_present_p("-extrapolate", argc, argv)) {
    m_do_extrapolate = TRUE;
  }

  // Rotate the seed velocity by a specified angle about the x,y or z axis of any coordinate system
  cSTRING seed_rotate_vel = parse_arg_string("-seed_rotate_vel", argc, argv);
  m_params.is_seed_rotate_vel = FALSE;
  if (seed_rotate_vel != NULL) {
    m_params.is_seed_rotate_vel = TRUE;

    const std::string delims(",");
    std::vector<std::string> csys_axis_angle = EXA_STR::StripSplit(seed_rotate_vel, delims);

    // csys
    char *temp = xnew char[csys_axis_angle[0].size()+1];
    std::copy(csys_axis_angle[0].begin(), csys_axis_angle[0].end(), temp);
    temp[csys_axis_angle[0].size()] = '\0';
    m_params.seed_rotate_vel_csys_name = temp;
 
    // axis
    if (csys_axis_angle[1] == "x")
      m_params.seed_rotate_vel_axis = 0;
    else if (csys_axis_angle[1] == "y")
      m_params.seed_rotate_vel_axis = 1;
    else if (csys_axis_angle[1] == "z")
      m_params.seed_rotate_vel_axis = 2;
    else
      msg_error("Invalid rotation axis passed to the option --seed_rotate_vel. Choose x, y or z.");

    // angle
    if (is_float(csys_axis_angle[2])) {
      m_params.seed_rotate_vel_angle = atof(csys_axis_angle[2].c_str());
      if (m_params.seed_rotate_vel_angle == 0.0)
        msg_warn("Rotation angle is zero. No rotation will be performed.");
    }
    else
      msg_error("Invalid rotation angle passed to the option --seed_rotate_vel. \"%s\" is not a real number.", csys_axis_angle[2].c_str());
  }
}

VOID cSMART_SEED_CONTROL::parse_cmdline_dgf_seed_var_specs_until(int *argc, 
                                                                 char *argv[], 
                                                                 cSTRING include_cmdline_arg,
                                                                 cSTRING exclude_cmdline_arg, 
                                                                 cSTRING argstop)
{
  while (1) {
    cSTRING exclude_vars, include_vars;
    exclude_vars = parse_arg_string_until(exclude_cmdline_arg, argstop, argc, argv);
    if (exclude_vars) {
      std::string input(exclude_vars);
      m_fluid_seed_exclude_vars.push_back(input);
    } else {
      include_vars = parse_arg_string_until(include_cmdline_arg, argstop, argc, argv);
      if (include_vars) {
        std::string input(include_vars);
        m_fluid_seed_include_vars.push_back(input);
      } else
        return;
    }
  }
  return;
}

VOID cSMART_SEED_CONTROL::parse_cmdline_dgf_bseed_var_specs_until(int *argc, 
                                                                 char *argv[], 
                                                                 cSTRING include_cmdline_arg,
                                                                 cSTRING exclude_cmdline_arg, 
                                                                 cSTRING argstop)
{
  while (1) {
    cSTRING exclude_vars, include_vars;
    exclude_vars = parse_arg_string_until(exclude_cmdline_arg, argstop, argc, argv);
    if (exclude_vars) {
      std::string input(exclude_vars);
      m_boundary_seed_exclude_vars.push_back(input);
    } else {
      include_vars = parse_arg_string_until(include_cmdline_arg, argstop, argc, argv);
      if (include_vars) {
        std::string input(include_vars);
        m_boundary_seed_include_vars.push_back(input);
      } else
        return;
    }
  }
  return;
}

SRI_FILE cSEED_CONTROL::open_smart_seed_file(cSTRING seed_filename)
{
  SRI_FILE seed_file = NULL;
  if (seed_filename != NULL) {
    SRI_STATUS status = sri_open_file_for_read(seed_filename, NULL, &seed_file);
    if (status != SRI_SUCCESS)
      msg_error("Unable to open seed file \"%s\": %s.", 
                seed_filename, sri_error_string(status));
  }
  return seed_file;
}

template <typename SEED_VAR_TYPE, asINT32 N_SEED_VARS, bool IS_VOLUMETRIC_SEED>
VOID cSEED_CONTROL::parse_seed_var_specs(bool do_smart_seed, 
                                         int *argc, 
                                         char *argv[], 
                                         cSTRING include_cmdline_arg, 
                                         cSTRING exclude_cmdline_arg,
                                         BOOLEAN is_fluid_region,
                                         std::map <std::string, SEED_VAR_TYPE > &seed_var_string_to_seed_var_type,
                                         SEED_VAR_TYPE default_seed_vars[], 
                                         asINT32 n_default_seed_vars,
                                         SEED_VAR_TYPE default_solid_seed_vars[], 
                                         asINT32 n_default_solid_seed_vars,
                                         asINT32 n_extra_inclusion_seed_vars, 
                                         cSTRING purpose)
{
  for (SMART_SEED_CONTROL seed_controller : m_seed_controllers) {
    if (seed_controller->realm() == STP_FLOW_REALM) {
      seed_controller->parse_seed_var_specs< SEED_VAR_TYPE, N_SEED_VARS, IS_VOLUMETRIC_SEED >
        (do_smart_seed, 
         argc, 
         argv, 
         include_cmdline_arg, 
         exclude_cmdline_arg, 
         is_fluid_region, 
         seed_var_string_to_seed_var_type, 
         default_seed_vars, 
         n_default_seed_vars, 
         n_extra_inclusion_seed_vars, 
         purpose);
    } else { // STP_COND_REALM
      seed_controller->parse_seed_var_specs< SEED_VAR_TYPE, N_SEED_VARS, IS_VOLUMETRIC_SEED >
        (do_smart_seed, 
         argc, 
         argv, 
         include_cmdline_arg, 
         exclude_cmdline_arg, 
         is_fluid_region, 
         seed_var_string_to_seed_var_type, 
         default_solid_seed_vars, 
         n_default_solid_seed_vars, 
         n_extra_inclusion_seed_vars, 
         purpose);
    }
  }
}

VOID cSMART_SEED_CONTROL::check_boundary_face_bseed_vars(std::vector<sBSEED_FACE_CHECK>& bseed_face_checks, std::vector<sBSEED_FACE_CHECK>& bseed_var_face_checks) {
  asINT32 n_bseed_var_specs = bseed_var_face_checks.size();
  for (asINT32 i=1; i<n_bseed_var_specs; i++) { // 0 is the default, where the name is "-"
    std::string entity_prefix = bseed_var_face_checks[i].entity_type_name();
    if (bseed_var_face_checks[i].found == FALSE) {
      msg_warn("%s \"%s\" (included with --%s option) does not exist.",
               entity_prefix.c_str(),
               boundary_seed_var_specs()[i].entity_name.c_str(),
               boundary_seed_var_specs()[i].is_seed_vars_inclusion_list ? "bseed_include_vars" : "bseed_exclude_vars"); 
    }
    else if (bseed_var_face_checks[i].is_wall == TRUE) {
      msg_error("%s \"%s\" (included with --%s option) %s. Boundary seeding does not apply to walls.",
                entity_prefix.c_str(),
                boundary_seed_var_specs()[i].entity_name.c_str(),
                boundary_seed_var_specs()[i].is_seed_vars_inclusion_list ? "bseed_include_vars" : "bseed_exclude_vars",
                bseed_var_face_checks[i].is_segment_or_part()? "contains only walls" : "is a wall");
    }
  }	
}

VOID cSMART_SEED_CONTROL::check_seed_var_spec_physics_desc() {
  for (asINT32 nth_spec = 1; nth_spec < n_fluid_seed_var_specs(); nth_spec++) {
    if (!m_found_seed_var_spec_physics_desc[nth_spec]) {
      msg_warn("No fluid physics descriptor is defined for the region \"%s\". Arguments "
               "passed to the corresponding --%s option will be ignored.",
               fluid_seed_var_specs()[nth_spec].entity_name.c_str(),
               fluid_seed_var_specs()[nth_spec].is_seed_vars_inclusion_list ? "seed_include_vars" : "seed_exclude_vars");
    }
  }
  delete [] m_found_seed_var_spec_physics_desc;
}

VOID cSMART_SEED_CONTROL::add_surface_normals(cDGF_SURFEL_DESC *surfel_desc) {
  ccDOTIMES(i, cp_info.n_dims) {
    m_nw_surface_normals.push_back(surfel_desc->s.normal[i]);
  }
}

VOID cSMART_SEED_CONTROL::detect_fluid_physics_desc_for_seeded_regions(cSTRING physics_desc_name,
                                                                 const std::vector<asINT32>& rgns) {

  uINT32 num_seed_var_specs = n_fluid_seed_var_specs();
  ccDOTIMES(j, num_seed_var_specs) {
    if (!m_found_seed_var_spec_physics_desc[j]) {
      sFLUID_SEED_VAR_SPEC seed_var_spec = fluid_seed_var_specs()[j];
      cSTRING entity_name = seed_var_spec.entity_name.c_str();
      cCDI_GEOMETRY_REF geomRef(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
      if (cdi_get_geometry_by_path(cp_info.partitions(), entity_name, &geomRef)){
        //Valid geometry path could refer a part or segment
        if ((geomRef.GeometrySelectionType() == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) ||
            (geomRef.GeometrySelectionType() == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment)) {
        
          auto part_indices = geomRef.ExpandSelection(cp_info.partitions(), cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
          auto only_fluid_parts = [] (asINT32 part_index) {
            return cp_info.fluid_physics_desc_index_from_part_index[part_index] >= 0;
          };
          std::vector<asINT32> fluid_part_indices;
          std::copy_if(part_indices.begin(), part_indices.end(),
                       std::back_inserter(fluid_part_indices), only_fluid_parts);            
          std::sort(fluid_part_indices.begin(), fluid_part_indices.end());
          //As far as this check is concerned, the seed_var_spec can be associated with a fluid physics descriptor
          //if the parts it represents are a subset of the parts that define the fluid physics descriptor/fluid region.
          //The converse is also true, if the parts that define the fluid physics descriptor are a subset of the parts
          //define by the seed_var_spec, this check passes
          std::vector<asINT32> intersection;
          std::set_intersection(rgns.begin(), rgns.end(),
                                fluid_part_indices.begin(), fluid_part_indices.end(),
                                std::back_inserter(intersection));
          bool seed_spec_parts_can_be_associated_with_one_or_more_fluid_physics_descriptors = !intersection.empty();
          m_found_seed_var_spec_physics_desc[j] = seed_spec_parts_can_be_associated_with_one_or_more_fluid_physics_descriptors;
        }
      }
    }
  }
}

VOID cSEED_CONTROL::dont_write_smart_seed_data(auINT32 fluid_like_voxel_mask, asINT32 ublk_home_sp) {
  cDGF_SMART_SEED_VOXEL_MASKS voxel_masks;
  voxel_masks.needs_seed_data_voxel_mask = fluid_like_voxel_mask;
  voxel_masks.has_seed_data_voxel_mask = 0;
  voxel_masks.write(g_sp_streams[ublk_home_sp]);
}

VOID cSMART_SEED_CONTROL::maybe_write_smart_seed_data(cDGF_UBLK_BASE_DESC *ublk_desc,
                                                      auINT32 fluid_like_voxel_mask,
                                                      asINT32 ublk_home_sp,
                                                      asINT32 simple_part_index,     // -1 if real ublk desc
                                                      sINT32  voxel_part_indices[8], // NULL is simple ublk desc
                                                      auINT32 split_voxel_mask)  //for special split voxel seeding
{
  if (is_smart_seed()) {
    sFLOAT seed_vars[DGF_N_SEED_VARS][N_VOXELS_8];
    sFLOAT seed_uds_vars[MAX_N_SEED_UDS_VARS][N_VOXELS_8];
    cDGF_SMART_SEED_VOXEL_MASKS voxel_masks;
    voxel_masks.needs_seed_data_voxel_mask = fluid_like_voxel_mask;
    voxel_masks.has_seed_data_voxel_mask = generate_seed_vars(ublk_desc, seed_vars, seed_uds_vars, fluid_like_voxel_mask, split_voxel_mask);
    voxel_masks.write(g_sp_streams[ublk_home_sp]);
    ccDOTIMES(voxel, N_VOXELS_8) {
      if ((voxel_masks.has_seed_data_voxel_mask >> voxel) & 1) {
        asINT32 part_index = voxel_part_indices ? voxel_part_indices[voxel] : simple_part_index;
        uINT8 seed_var_spec_index = m_part_seed_var_spec_indices[part_index];
        // display a warning if some components of seed velocity are excluded in an LRF region
        static BOOLEAN is_vector_allocated = FALSE;
        static std::vector<cBOOLEAN> visited_lrf;
        if (!is_vector_allocated) {
          ccDOTIMES(i, cp_info.n_lrfs()) {
            visited_lrf.push_back(FALSE);
          }
          is_vector_allocated = TRUE;
        }
        if (ublk_desc->b.lrf_index != -1) {
          if (!visited_lrf[ublk_desc->b.lrf_index]) {
            if (realm() == STP_FLOW_REALM) {
              // Check for velocity components
              sFLUID_SEED_VAR_SPEC seed_var_spec = fluid_seed_var_specs()[seed_var_spec_index-m_spec_base_index];
              uINT8 num_vel_components_in_seed_vars = 0;
              ccDOTIMES(k, seed_var_spec.n_vars) {
                DGF_SEED_VAR_TYPE seed_var_type = seed_var_spec.seed_var_types[k];
                if (seed_var_type == DGF_SEED_VAR_XVEL || seed_var_type == DGF_SEED_VAR_YVEL || seed_var_type == DGF_SEED_VAR_ZVEL)
                  num_vel_components_in_seed_vars += 1;
              }
              if (num_vel_components_in_seed_vars < 3) {
                msg_warn("Seed velocity components excluded in the LRF region \"%s\" using the --%s option are "
                         "those relative to the global reference frame. They are substituted with corresponding "
                         "components of the initial velocity specified in the CDI file.",
                         cp_info.sri_parts[part_index].name,
                         seed_var_spec.is_seed_vars_inclusion_list ? "seed_include_vars" : "seed_exclude_vars");
                visited_lrf[ublk_desc->b.lrf_index] = TRUE;
              }
            } else {//STP_COND_REALM
              // No velocity components in solid
              visited_lrf[ublk_desc->b.lrf_index] = TRUE;
            }
          }
        }
        // The SPs also contain the table of seed var specs.
        lgi_write(g_sp_streams[ublk_home_sp], seed_var_spec_index);
        ccDOTIMES(var, m_n_vars) {
          cDGF_SMART_SEED_VAR_VALUE subrec;
          subrec.var_value = seed_vars[m_seed_var_types[var]][voxel];
          subrec.write(g_sp_streams[ublk_home_sp]);
        }
	if (cp_info.is_uds_transport && !cp_info.is_5g_sim) {
	  ccDOTIMES(var, m_n_uds_vars) {
	    cDGF_SMART_SEED_VAR_VALUE subrec;
	    subrec.var_value = seed_uds_vars[m_seed_uds_var_indices[var]][voxel];
	    subrec.write(g_sp_streams[ublk_home_sp]);
	  }
	}
      }
    }
  }
}


VOID cSMART_SEED_CONTROL::setup_mlrf_initial_position()
{

  SRI_FILE_PARAMS seed_sri_params = m_seed_file->file_params();
  asINT32 n_lrfs_seed = seed_sri_params->n_lrfs;
  asINT32 n_lrfs = cp_info.n_lrfs();

  m_params.m_seed_file_contains_sliding_lrfs = false;
  if (n_lrfs_seed > 0) {
    m_params.seed_lrf_rotation_data.resize(n_lrfs_seed);
    m_seed_data_cache.ref_frame_index_pages = 
                (sriLRF_INDEX *)cp_vmem_reserve((size_t)sizeof(sriLRF_INDEX) 
                                                * N_PAGES_IN_CACHE * m_seed_data_cache.ref_frame_index_page_size);
  } else {
    m_seed_data_cache.ref_frame_index_pages = NULL;
  }
  ccDOTIMES(i, n_lrfs_seed) {
    sriDOUBLE rotation = 0;
    sriINT n_revolutions = 0;
    m_seed_file->read_ref_frame_angular_rotation(&rotation, i);
    m_seed_file->read_ref_frame_n_revolutions(&n_revolutions, i);
    m_params.seed_lrf_rotation_data[i].rotation_angle = rotation;
    m_params.seed_lrf_rotation_data[i].n_revolutions = n_revolutions;
    if (seed_sri_params->lrfs[i].type == SRI_LRF_MOVING)
      m_params.m_seed_file_contains_sliding_lrfs = true;
  }
  m_seed_file->calculate_lrf_rotation_matrices();
  if (n_lrfs > 0) {
    m_params.seed_lrf_index_from_ublk_lrf_index.resize(n_lrfs, SRI_INVALID_REF_FRAME_INDEX);
    cp_jobctl_output_status("Read reference frame positions from seed file");
    cDGF_SMART_SEED_LRF_INITIAL_ROTATIONS_HEADER header;
    header.tag.id = DGF_SMART_SEED_LRF_INITIAL_ROTATION_TAG;
    header.tag.length = lgi_pad_and_encode_record_length(sizeof(header) + n_lrfs * sizeof(cDGF_SMART_SEED_LRF_INITIAL_ROTATION));
    write_header_to_all_sps(header); 
    ccDOTIMES(j, n_lrfs) {
      SRI_LRF lrf_seed = seed_sri_params->lrfs; // vector of LRF structures
      sriDOUBLE rotation = 0;
      sriINT n_revolutions = 0;
      BOOLEAN is_lrf_name_matched = FALSE;
      ccDOTIMES(i, n_lrfs_seed) {
        if (0 == strcmp(lrf_seed->name, cp_info.sri_lrfs[j].name)) {
          is_lrf_name_matched = TRUE;
          rotation = m_params.seed_lrf_rotation_data[i].rotation_angle;
          n_revolutions = m_params.seed_lrf_rotation_data[i].n_revolutions;

          if (rotation < 0) {
            dFLOAT two_pi = 2.0 * PI;
            asINT32 rotation_over_two_pi = rotation / two_pi - 1;
            rotation -= rotation_over_two_pi * two_pi;
            n_revolutions += rotation_over_two_pi;
            // msg_warn("The rotation angle of sliding mesh reference frame \"%s\" in the seed file is negative.", lrf_seed->name);
          } else if (rotation >= 2.0 * PI) {
            dFLOAT two_pi = 2.0 * PI;
            asINT32 rotation_over_two_pi = rotation / two_pi;
            rotation -= rotation_over_two_pi * two_pi ;
            n_revolutions += rotation_over_two_pi;
            // msg_warn("The rotation angle of sliding mesh reference frame \"%s\" in the seed file is larger than 2*PI.", lrf_seed->name);
          }

          if (lrf_seed->type == SRI_LRF_MOVING && cp_info.sri_lrfs[j].type == SRI_LRF_STATIONARY)
            msg_warn("Rotating reference frame \"%s\" is a sliding mesh region"
                     " in the seed file and a stationary mesh region in this simulation.",
                     lrf_seed->name);
          else if (lrf_seed->type == SRI_LRF_STATIONARY && cp_info.sri_lrfs[j].type == SRI_LRF_MOVING)
            msg_warn("Rotating reference frame \"%s\" is a stationary mesh region"
                     " in the seed file and a sliding mesh region in this simulation.",
                     lrf_seed->name);
          else if (cp_info.sri_lrfs[j].type == SRI_LRF_MOVING && lrf_seed->type == SRI_LRF_MOVING)
            msg_print("Rotating reference frame \"%s\" initialized with a rotation of %g radians"
                      " to match the seed file.",
                      lrf_seed->name, rotation);
          m_params.seed_lrf_index_from_ublk_lrf_index[j] = i;
          break;

        }
        lrf_seed++;
      }
      cDGF_SMART_SEED_LRF_INITIAL_ROTATION subrec;
      subrec.rotation = rotation;
      subrec.n_revolutions = n_revolutions;
      write_to_all_sps(&subrec, sizeof(subrec));
      if (!is_lrf_name_matched)
        msg_warn("Could not find a local rotating reference frame named \"%s\" in the seed file.",
                 cp_info.sri_lrfs[j].name);
    }
  }
}

// send the initial movement information from the seed
// file to the sps for each moving_face_descriptor
VOID cSEED_CONTROL::setup_movb_initial_position()
{

  asINT32 total_movbs_seed = 0;
  for (auto seed_controller : m_seed_controllers) {
    SRI_FILE_PARAMS seed_sri_params = seed_controller->seed_file()->file_params();
    total_movbs_seed += seed_sri_params->n_moving_faces;
  }
  asINT32 n_movbs_cdi = cp_info.n_movbs();

  if (n_movbs_cdi == 0 || total_movbs_seed == 0)
    return;

  cp_jobctl_output_status("Read moving face positions from seed file");

  // Setup and send header to the SPs
  cDGF_SMART_SEED_MOVB_INITIAL_XFORMS_HEADER header;
  header.tag.id = DGF_SMART_SEED_MOVB_INITIAL_XFORM_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(header) + n_movbs_cdi * sizeof(cDGF_SMART_SEED_MOVB_INITIAL_XFORMS));
  header.n_movbs_cdi = n_movbs_cdi;
  write_header_to_all_sps(header); 
  SRI_STATUS status = SRI_SUCCESS;

  // Loop through each movb in the cdi and attempt to match it
  // to a movb in the seed file. Note that a lgi record is sent
  // for each movb in the cdi file, whether it is matched or not.
  ccDOTIMES(j, n_movbs_cdi) {
    BOOLEAN is_movb_cdi_matched = FALSE;
    sSRI_XFORM xform; // invoke default constructor for identity xform
    cDGF_SMART_SEED_MOVB_INITIAL_XFORMS subrec;

    // get cdi information for this face
    sriFACE_INDEX         face_index_cdi       = cp_info.sri_movbs[j].parent_face_index;
    sriMOVING_XFORM_INDEX movb_xform_index_cdi = cp_info.sri_movbs[j].moving_xform_index;
    cSTRING               movb_name_cdi        = cp_info.sri_faces[face_index_cdi].name;     
    dFLOAT                movb_surfel_area_cdi = cp_info.sri_faces[face_index_cdi].surfel_area;

    // Occasionally, users will place a tire inside a solid object in order to
    // "turn it off". This results in a surfel area of 0. We use this flag to
    // eliminate printing messages about these faces which have been "turned
    // off".  This also covers the back faces case which used to be in here.
    // Back faces always have surfel area == 0, so we will never print any
    // messages about them. See PR42807,PR43482. We still need to send the
    // information to the SPs, as they expect to receive one message per face.
    bool has_zero_surfels = movb_surfel_area_cdi == 0;

    for (auto seed_controller : m_seed_controllers) {
      // loop through all faces from the seed file until find a match
      SRI_FILE_PARAMS seed_sri_params = seed_controller->seed_file()->file_params();
      SRI_MOVING_FACE movb_seed = seed_sri_params->moving_faces;
      asINT32 n_movbs_seed = seed_sri_params->n_moving_faces;
      for(int i=0; i < n_movbs_seed; i++, movb_seed++) {

        // get seed information
        sriFACE_INDEX face_index_seed = movb_seed->parent_face_index;
        cSTRING movb_name_seed = seed_sri_params->faces[face_index_seed].name;     

        // if the seed face matches the cp_info face
        if (!strcmp(movb_name_seed, movb_name_cdi)) {
          is_movb_cdi_matched = TRUE;
          sriINT movb_xform_index_seed = movb_seed->moving_xform_index;

          // 5.4a produces meas files where the xform index == -1 on the back side of split open shells.
          if (movb_xform_index_seed >= 0) {
            subrec.xform_index = movb_xform_index_cdi; // set the cdi index
            SRI_STATUS status = seed_controller->seed_file()->read_moving_face_xform(&xform, movb_xform_index_seed, TRUE);
            if (status != SRI_SUCCESS)
              msg_error("Failed to read moving face xform %d from seed file \"%s\": %s", 
                        movb_xform_index_seed, seed_controller->seed_file()->name(), seed_controller->seed_file()->error_string(status));

            // For rotating tires, which undergo rotation-only motion, replace
            // the axis of rotation in the seed file with the axis of rotation in
            // the CDI file and compute the transform

            SRI_MOVING_FACE_DESCRIPTOR movb_xform_seed = &seed_sri_params->moving_face_xforms[movb_xform_index_seed];
            if (movb_xform_seed->motion_type == SRI_ROTATION_MOTION) {
              movb_xform_seed->angle_rotated = xform.xform[3][0];

              ccDOTIMES(axis, 3) {
                movb_xform_seed->axis_origin[axis] = cp_info.sri_movb_xforms[movb_xform_index_cdi].axis_origin[axis];
                movb_xform_seed->axis_direction[axis] = cp_info.sri_movb_xforms[movb_xform_index_cdi].axis_direction[axis];
              }

              movb_xform_seed->compute_xform_from_angle_rotated(&xform);
              if ( !has_zero_surfels ) {
                msg_print("Rotating tire \"%s\" initialized with a rotation %g radians to match the seed file.", movb_name_cdi, movb_xform_seed->angle_rotated);
              }
              xform.xform[3][0] = movb_xform_seed->angle_rotated;

            }
            else {
              msg_internal_error("Invalid motion type in seed file \"%s\" for moving face %d.", movb_name_seed, i);
            }

          }
          break;
        }
      }
    }

    // copy the resulting xform into the record.  This may or may not be
    // changed at this point, but the SPs are expected 1 record for each moving
    // face in the cdi file.
    ccDOTIMES(m, 4) {
      ccDOTIMES(n, 4) {
        subrec.xform[m][n] = xform.xform[m][n];
      }
    }

    write_to_all_sps(&subrec, sizeof(subrec));

    if (!is_movb_cdi_matched && !has_zero_surfels) {
      msg_warn("Could not find a moving face named \"%s\" in the seed file.", movb_name_cdi);
    }
    else {
      cp_info.face_index_to_seed_xform_map[face_index_cdi] = xform;
    }
  }
}

VOID cSMART_SEED_CONTROL::initialize_seed_data_cache()
{
  asINT32 n_vars = m_n_vars;
  if (cp_info.local_vel_freeze)
    n_vars--;

  asINT32 n_uds_vars = m_n_uds_vars;
  asINT32 n_vars_total = n_vars + n_uds_vars;
  
  sINT64 n_points = m_n_points;
  asINT32 last_page_cell_offset = n_points % N_CELLS_IN_PAGE;
  auINT32 n_ptes = (last_page_cell_offset == 0) ? 
    n_points / N_CELLS_IN_PAGE : n_points / N_CELLS_IN_PAGE + 1;

  m_seed_data_cache.var_page_size = N_CELLS_IN_PAGE * n_vars_total;
  m_seed_data_cache.var_pages = (sriFLOAT *)cp_vmem_reserve((size_t)sizeof(sriFLOAT) 
                                                            * N_PAGES_IN_CACHE * m_seed_data_cache.var_page_size);
  m_seed_data_cache.ref_frame_index_page_size = N_CELLS_IN_PAGE;
  m_seed_data_cache.page_table = (sSEED_DATA_CACHE_PTE *)cp_vmem_reserve((size_t)sizeof(sSEED_DATA_CACHE_PTE) * n_ptes);

  ccDOTIMES(pte, n_ptes) {
    m_seed_data_cache.page_table[pte].resident = FALSE;
    if ((pte == n_ptes - 1) && last_page_cell_offset != 0)
      m_seed_data_cache.page_table[pte].n_points_in_page = last_page_cell_offset;
    else
      m_seed_data_cache.page_table[pte].n_points_in_page = N_CELLS_IN_PAGE;
  }

  m_seed_data_cache.ulist.mru = NULL;
  m_seed_data_cache.ulist.lru = NULL;
  m_seed_data_cache.ulist.next_entry_index = 0;

  m_seed_data_cache.hits = 0;
  m_seed_data_cache.first_ref_misses = 0;
  m_seed_data_cache.capacity_misses = 0;
}

VOID cSMART_SEED_CONTROL::initialize_seed_norm_data_cache()
{
  sINT64 n_points = m_n_nw_norm_meas_cells;
  asINT32 last_page_cell_offset = n_points % N_NORM_CELLS_IN_PAGE;
  auINT32 n_ptes = (last_page_cell_offset == 0) ? 
    n_points / N_NORM_CELLS_IN_PAGE : n_points / N_NORM_CELLS_IN_PAGE + 1;

  m_seed_norm_data_cache.var_page_size = N_NORM_CELLS_IN_PAGE * cp_info.n_dims;
  m_seed_norm_data_cache.var_pages = (sriFLOAT *)cp_vmem_reserve((size_t)sizeof(sriFLOAT) 
                                                                 * N_NORM_PAGES_IN_CACHE * m_seed_norm_data_cache.var_page_size);
  m_seed_norm_data_cache.page_table = (sSEED_DATA_CACHE_PTE *)cp_vmem_reserve((size_t)sizeof(sSEED_DATA_CACHE_PTE) * n_ptes);

  ccDOTIMES(pte, n_ptes) {
    m_seed_norm_data_cache.page_table[pte].resident = FALSE;
    if ((pte == n_ptes - 1) && last_page_cell_offset != 0)
      m_seed_norm_data_cache.page_table[pte].n_points_in_page = last_page_cell_offset;
    else
      m_seed_norm_data_cache.page_table[pte].n_points_in_page = N_NORM_CELLS_IN_PAGE;
  }

  m_seed_norm_data_cache.ulist.mru = NULL;
  m_seed_norm_data_cache.ulist.lru = NULL;
  m_seed_norm_data_cache.ulist.next_entry_index = 0;

  m_seed_norm_data_cache.hits = 0;
  m_seed_norm_data_cache.capacity_misses = 0;
}

VOID cSMART_SEED_CONTROL::get_seed_var_page(sINT64 meas_cell_index, 
                                            asINT32 n_points_in_page,
                                            sriFLOAT *seed_var_table_page)
{
  asINT32 n_vars = m_n_vars;
  if (cp_info.local_vel_freeze)
    n_vars--;

  SRI_STATUS status;
  ccDOTIMES (var, n_vars) {
    if (SRI_SUCCESS != (status = m_seed_file->get_variable(m_sri_var_types[var], 
                                                           seed_var_table_page + (var*n_points_in_page), 
                                                           n_points_in_page, 
                                                           meas_cell_index, 
                                                           NULL))) {
      msg_error("Failed to read page of seed data from seed file \"%s\": %s", 
                m_seed_file->name(), m_seed_file->error_string(status));
    }
  }
  if (cp_info.is_uds_transport && !cp_info.is_5g_sim) {
    ccDOTIMES (var, m_n_uds_vars) {
      if (SRI_SUCCESS != (status = m_seed_file->get_variable(m_sri_uds_var_types[var], 
							     seed_var_table_page + ((var+n_vars)*n_points_in_page), 
							     n_points_in_page, 
							     meas_cell_index, 
							     NULL))) {
	msg_error("Failed to read page of seed data from seed file \"%s\": %s", 
		  m_seed_file->name(), m_seed_file->error_string(status));
      }
    }    
  }
}

VOID cSMART_SEED_CONTROL::get_seed_surface_normal_page(sINT64 nw_meas_cell_index, 
                                                       asINT32 n_points_in_page,
                                                       sriFLOAT *seed_var_table_page)
{
  SRI_STATUS status;

  if (SRI_SUCCESS != (status = ((SRI_FLUID_FILE)m_seed_file)->read_nw_surface_normals(seed_var_table_page, 
                                                                                      n_points_in_page, 
                                                                                      nw_meas_cell_index)))
    msg_error("Failed to read page of seed surface normal data from seed file \"%s\": %s", 
              m_seed_file->name(), m_seed_file->error_string(status));

}

VOID cSMART_SEED_CONTROL::get_seed_ref_frame_index_page(sINT64 meas_cell_index, 
                                             asINT32 n_points_in_page, 
                                             sriLRF_INDEX *seed_ref_frame_index_table_page)
{

  SRI_STATUS status;
  if (SRI_SUCCESS != (status = m_seed_file->read_ref_frame_indices( 
                                           seed_ref_frame_index_table_page, 
                                           n_points_in_page, meas_cell_index))) {
    msg_error("Failed to read page of reference frame data from seed file \"%s\": %s", 
              m_seed_file->name(), m_seed_file->error_string(status));
  }
}

/* Though the name implies that it just gets a pointer, this is
 * the function through which the the mechanism of the seed data
 * cache is implemented; if you request a pointer to a meas
 * cell, you actually get a pointer into the cache, which may be
 * updated via a read of the seed file and/or a rearrangement of 
 * the usage list.
 */
sriLRF_INDEX cSMART_SEED_CONTROL::get_all_vars_and_lrf_index(sINT64 meas_cell_index, 
                                                             sriFLOAT meas_vars[DGF_N_SEED_VARS],
  							     sriFLOAT meas_uds_vars[MAX_N_SEED_UDS_VARS])
{
  asINT32 n_vars = m_n_vars;
  if (cp_info.local_vel_freeze)
    n_vars--;

  if (m_var_table != NULL) {
    ccDOTIMES(var,n_vars) {
      meas_vars[var] = *(m_var_table + meas_cell_index + var*m_n_points);
    }
    //UDS
    if (cp_info.is_uds_transport) {
      ccDOTIMES(var1,m_n_uds_vars) {
	meas_uds_vars[var1] = *(m_var_table + meas_cell_index + (var1+n_vars)*m_n_points);
      }
    }    
    if (m_ref_frame_index_table != NULL) {
      return *(m_ref_frame_index_table + meas_cell_index);
    } else {
      return SRI_INVALID_REF_FRAME_INDEX;
    }
  }

  asINT32 page_offset = meas_cell_index & CELL_INDEX_MASK;
  sINT64 sri_page_index = meas_cell_index & ~(CELL_INDEX_MASK);
  asINT32 pte_table_index = meas_cell_index >> N_CELL_INDEX_BITS;
  SEED_DATA_CACHE_PTE pte = &m_seed_data_cache.page_table[pte_table_index];
  USAGE_LIST ulist = &m_seed_data_cache.ulist;
  sriFLOAT *var_pages = m_seed_data_cache.var_pages;
  asINT32 var_page_size = m_seed_data_cache.var_page_size;
  sriLRF_INDEX *ref_frame_index_pages = m_seed_data_cache.ref_frame_index_pages;
  asINT32 ref_frame_index_page_size = m_seed_data_cache.ref_frame_index_page_size;

  if (pte->resident) {

    /* No need to get or replace anything; update the ulist
     * and calculate the data address
     */
    m_seed_data_cache.hits++;
    USAGE_LIST_ENTRY current_entry = pte->ulist_entry;
    if (current_entry->prev != NULL) {                   // Not MRU
      current_entry->prev->next = current_entry->next; 
      if (current_entry->next != NULL)                   // Not LRU
        current_entry->next->prev = current_entry->prev;
      else                                              // LRU
        ulist->lru = current_entry->prev; 
      current_entry->prev = NULL;
      current_entry->next = ulist->mru;
      current_entry->next->prev = current_entry; 
      ulist->mru = current_entry;
    }

  } else {

    /* New page required. Three possibilities; no pages yet allocated
     * (first reference), some but not all pages allocated (no
     * replacement required), all pages allocated (replacement required)
     */

    if (ulist->next_entry_index < N_PAGES_IN_CACHE) { // Not all pages allocated

      m_seed_data_cache.first_ref_misses++;
      USAGE_LIST_ENTRY current_entry = &ulist->ulist_entries[ulist->next_entry_index];
      current_entry->page_index = ulist->next_entry_index++;
      current_entry->prev = NULL;
      current_entry->next = ulist->mru;
      current_entry->pte = pte;
      pte->ulist_entry = current_entry;
      pte->page_index = current_entry->page_index;
      if (ulist->mru != NULL)
        ulist->mru->prev = current_entry; 
      ulist->mru = current_entry;
      if (ulist->lru == NULL) ulist->lru = current_entry; // First reference

      // Get the page of scaled data
      sriFLOAT *seed_data_table_page = (var_pages + pte->page_index * var_page_size);
      get_seed_var_page(sri_page_index, pte->n_points_in_page, seed_data_table_page);
      if (ref_frame_index_pages) {
        sriLRF_INDEX *seed_ref_frame_index_table_page = 
          (ref_frame_index_pages + pte->page_index * ref_frame_index_page_size);
        get_seed_ref_frame_index_page(sri_page_index, pte->n_points_in_page, seed_ref_frame_index_table_page);
      }
      pte->resident = TRUE;

    } else {                                          // replacement required

      m_seed_data_cache.capacity_misses++;
      // Invalidate the old PTE and update the new one

      ulist->lru->pte->resident = FALSE;
      pte->resident = TRUE;
      pte->page_index = ulist->lru->page_index;
      pte->ulist_entry = ulist->lru;

      // Shift the list; the old LRU becomes the new MRU

      pte->ulist_entry->pte = pte; 

      pte->ulist_entry->prev->next = NULL;  // Make the old LRU's
      ulist->lru = pte->ulist_entry->prev;   // prev the new LRU

      pte->ulist_entry->next = ulist->mru;   // Now make the old MRU the
      ulist->mru = pte->ulist_entry;         // next of the new MRU 

      ulist->mru->next->prev = ulist->mru;
      ulist->mru->prev = NULL;

      // Get the page of scaled data
      sriFLOAT *seed_var_table_page = (var_pages + pte->page_index * var_page_size);
      get_seed_var_page(sri_page_index, pte->n_points_in_page, seed_var_table_page);
      if (ref_frame_index_pages) {
        sriLRF_INDEX *seed_ref_frame_index_table_page = 
          (ref_frame_index_pages + pte->page_index * ref_frame_index_page_size);
        get_seed_ref_frame_index_page(sri_page_index, pte->n_points_in_page, seed_ref_frame_index_table_page);
      }
      pte->resident = TRUE;

    }
  }

  ccDOTIMES(var,n_vars) {
    meas_vars[var] = *(var_pages + pte->page_index * var_page_size + 
                       page_offset + pte->n_points_in_page * var);
  }
  if (cp_info.is_uds_transport) {
    ccDOTIMES(var1,m_n_uds_vars) {
      meas_uds_vars[var1] = *(var_pages + pte->page_index * var_page_size + 
			      page_offset + pte->n_points_in_page * (var1+n_vars));
    }
  }
  if (ref_frame_index_pages) {
    return *( ref_frame_index_pages + pte->page_index * ref_frame_index_page_size + 
              page_offset );
  } else {
    return SRI_INVALID_REF_FRAME_INDEX;
  }
}

VOID cSMART_SEED_CONTROL::get_cell_surface_normal(sINT32 nw_meas_cell_index, sriFLOAT cell_normal[3])
{
  asINT32 n_dims = cp_info.n_dims;
  asINT32 page_offset = (nw_meas_cell_index & NORM_CELL_INDEX_MASK) * n_dims;
  sINT64 sri_page_index = nw_meas_cell_index & ~(NORM_CELL_INDEX_MASK);
  asINT32 pte_table_index = nw_meas_cell_index >> N_NORM_CELL_INDEX_BITS;
  SEED_DATA_CACHE_PTE pte = &m_seed_norm_data_cache.page_table[pte_table_index];
  USAGE_LIST ulist = &m_seed_norm_data_cache.ulist;
  sriFLOAT *var_pages = m_seed_norm_data_cache.var_pages;
  asINT32 var_page_size = m_seed_norm_data_cache.var_page_size;

  if (pte->resident) {

    /* No need to get or replace anything; update the ulist
     * and calculate the data address
     */
    m_seed_norm_data_cache.hits++;
    USAGE_LIST_ENTRY current_entry = pte->ulist_entry;
    if (current_entry->prev != NULL) {                   // Not MRU
      current_entry->prev->next = current_entry->next; 
      if (current_entry->next != NULL)                   // Not LRU
        current_entry->next->prev = current_entry->prev;
      else                                              // LRU
        ulist->lru = current_entry->prev; 
      current_entry->prev = NULL;
      current_entry->next = ulist->mru;
      current_entry->next->prev = current_entry; 
      ulist->mru = current_entry;
    }

  } else {

    /* New page required. Three possibilities; no pages yet allocated
     * (first reference), some but not all pages allocated (no
     * replacement required), all pages allocated (replacement required)
     */

    if (ulist->next_entry_index < N_NORM_PAGES_IN_CACHE) { // Not all pages allocated
      USAGE_LIST_ENTRY current_entry = &ulist->ulist_entries[ulist->next_entry_index];
      current_entry->page_index = ulist->next_entry_index++;
      current_entry->prev = NULL;
      current_entry->next = ulist->mru;
      current_entry->pte = pte;
      pte->ulist_entry = current_entry;
      pte->page_index = current_entry->page_index;
      if (ulist->mru != NULL)
        ulist->mru->prev = current_entry; 
      ulist->mru = current_entry;
      if (ulist->lru == NULL) ulist->lru = current_entry; // First reference

      // Get the page of scaled data
      sriFLOAT *seed_data_table_page = (var_pages + pte->page_index * var_page_size);
      get_seed_surface_normal_page(sri_page_index, pte->n_points_in_page, seed_data_table_page);

      pte->resident = TRUE;

    } else {                                          // replacement required

      m_seed_norm_data_cache.capacity_misses++;
      // Invalidate the old PTE and update the new one

      ulist->lru->pte->resident = FALSE;
      pte->resident = TRUE;
      pte->page_index = ulist->lru->page_index;
      pte->ulist_entry = ulist->lru;

      // Shift the list; the old LRU becomes the new MRU

      pte->ulist_entry->pte = pte; 

      pte->ulist_entry->prev->next = NULL;  // Make the old LRU's
      ulist->lru = pte->ulist_entry->prev;   // prev the new LRU

      pte->ulist_entry->next = ulist->mru;   // Now make the old MRU the
      ulist->mru = pte->ulist_entry;         // next of the new MRU 

      ulist->mru->next->prev = ulist->mru;
      ulist->mru->prev = NULL;

      // Get the page of scaled data
      sriFLOAT *seed_var_table_page = (var_pages + pte->page_index * var_page_size);
      get_seed_surface_normal_page(sri_page_index, pte->n_points_in_page, seed_var_table_page);
      pte->resident = TRUE;

    }
  }

  ccDOTIMES(i, n_dims) {
    cell_normal[i] = *(var_pages + pte->page_index * var_page_size + page_offset);
    page_offset++;
  }
}

VOID cSMART_SEED_CONTROL::assign_part_seed_var_specs(asINT32 spec_base_index)
{
  m_spec_base_index = spec_base_index;
  m_part_seed_var_spec_indices = cnew uINT8[cp_info.n_sri_parts];

  ccDOTIMES(i, cp_info.n_sri_parts) {
    m_part_seed_var_spec_indices[i] = spec_base_index;
  }

  if (this->n_fluid_seed_var_specs() <= 1)
    return;

  // By convention, the first seed_var_spec (index 0) is the default spec that includes all seed vars.
  for (asINT32 nth_spec = 1; nth_spec < n_fluid_seed_var_specs(); nth_spec++) {
    cSTRING entity_name = fluid_seed_var_specs()[nth_spec].entity_name.c_str();
    BOOLEAN entity_found = FALSE;    
    cCDI_GEOMETRY_REF geomRef(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
    if (cdi_get_geometry_by_path(cp_info.partitions(), entity_name, &geomRef)){
      //Valid geometry path could refer a part or segment
      if ((geomRef.GeometrySelectionType() == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) ||
          (geomRef.GeometrySelectionType() == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment)) {
        
        auto part_indices = geomRef.ExpandSelection(cp_info.partitions(), cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);

        auto only_fluid_parts = [] (asINT32 part_index) {
          return cp_info.fluid_physics_desc_index_from_part_index[part_index] >= 0;
        };
          
        std::vector<asINT32> fluid_part_indices;
        std::copy_if(part_indices.begin(), part_indices.end(),
                     std::back_inserter(fluid_part_indices), only_fluid_parts);

        if (fluid_part_indices.empty()) {
          msg_warn("Fluid region defined by \"%s\" (included with --%s option) is a valid CDI geometry entity path,"
                   " but has empty effective part selections.",
                   entity_name,
                   fluid_seed_var_specs()[nth_spec].is_seed_vars_inclusion_list
                   ? "seed_include_vars" : "seed_exclude_vars");
          continue;
        }
        
        for (auto i : fluid_part_indices) {
          assert(i < cp_info.n_sri_parts);
          m_part_seed_var_spec_indices[i] = nth_spec + spec_base_index;
        }
        
        entity_found = TRUE;
      }
    }
    
    if (!entity_found) {
      msg_warn("Fluid region \"%s\" (included with --%s option) does not exist.",
               entity_name,
               fluid_seed_var_specs()[nth_spec].is_seed_vars_inclusion_list
               ? "seed_include_vars" : "seed_exclude_vars");      
    }
  } //spec loop
}

auINT32 cSMART_SEED_CONTROL::generate_seed_vars(cDGF_UBLK_BASE_DESC *ublk_desc,
                                                sriFLOAT seed_vars[][N_VOXELS_8],
						sriFLOAT seed_uds_vars[][N_VOXELS_8],
                                                auINT32 fluid_like_voxel_mask,
                                                auINT32 split_voxel_mask)
{
  sINT32 grid_voxel_scale = sim_scale_to_sri_scale(ublk_desc->b.voxel_scale); 
  asINT32 z_max = cp_info.n_dims - 1;
  asINT32 voxel_inc = voxel_loop_increment(cp_info.n_dims);
  sriFLOAT meas_vars[DGF_N_SEED_VARS];
  sriFLOAT meas_uds_vars[MAX_N_SEED_UDS_VARS];
  STP_VOXEL_MASK seeded_mask = 0;
  sFLOAT voxel_size = sFLOAT(1 << grid_voxel_scale);
  STP_COORD *ublk_location = ublk_desc->b.location;
  sriLRF_INDEX ublk_lrf_index = ublk_desc->b.lrf_index;

  const dFLOAT cos_angle_threshold = cos(0.25 * PI);

  sriLRF_INDEX seed_ublk_lrf_index = (ublk_lrf_index == SRI_GLOBAL_REF_FRAME_INDEX ?
                                      SRI_GLOBAL_REF_FRAME_INDEX :
                                      m_params.seed_lrf_index_from_ublk_lrf_index[ublk_lrf_index]);

  BOOLEAN do_special_seeding = cp_info.local_vel_freeze;

  asINT32 n_vars = m_n_vars;
  if (do_special_seeding) {
    if (cp_info.n_dims == 3)
      n_vars = n_vars - N_FROZEN_VARS;  //specially seed density_frozen, x|y|z_velocity_forzen,  ustar_0, and flag of coarsen voxel
    else
      n_vars = n_vars - (N_FROZEN_VARS - 1);  //specially seed density_frozen, x|y_velocity_forzen,  ustar_0, and flag of coarsen voxel
  }

  asINT32 n_uds_vars = m_n_uds_vars;
  
  //5G
  BOOLEAN is_component0_density_seeded = FALSE;
  BOOLEAN is_component1_density_seeded = FALSE;
  if (cp_info.is_5g_sim) {
    ccDOTIMES(var, n_vars) {
      if (m_seed_var_types[var] == DGF_SEED_VAR_COMP0_DENSITY)
	is_component0_density_seeded = TRUE;
      else if (m_seed_var_types[var] == DGF_SEED_VAR_COMP1_DENSITY)
	is_component1_density_seeded = TRUE;
    }
  }

  asINT32 voxel = 0; //voxel index in the ublk
  ccDOTIMES(dx, 2) {
    ccDOTIMES(dy, 2) {
      ccDOTIMES(dz, z_max) {
        if ((fluid_like_voxel_mask >> voxel) & 1) {
          csPOINT voxel_min_point(ublk_location[0]+dx*voxel_size, 
                                  ublk_location[1]+dy*voxel_size, 
                                  ublk_location[2]+dz*voxel_size);
          csPOINT voxel_max_point = voxel_min_point + voxel_size;
          csPOINT grid_min_point = voxel_min_point * m_params.coord_scale + m_params.origin_shift;
          csPOINT grid_max_point = voxel_max_point * m_params.coord_scale + m_params.origin_shift;
          csBOX voxel_grid_box(grid_min_point, grid_max_point);
          sFLOAT accumulated_volume = 0.0;
          sFLOAT total_volume = voxel_grid_box.GetVolume();

          //special seeding
          sFLOAT scale_diff = 0.0;
          BOOLEAN is_fine_to_coarse = FALSE;
          sFLOAT accumulated_volume_frozen = 0.0;

          //split voxel seeding
          BOOLEAN do_split_seed = cp_info.do_split_seed && ((split_voxel_mask >> voxel) & 1);
          BOOLEAN has_cell_included = FALSE;
          asINT64 cell_tracked = -1;
          sFLOAT  cos_angle_tracked = -1.0;  //cos(PI) 
          sFLOAT  common_volume_tracked = 0.0;

          sFLOAT voxel_normal[3] = {0.};  //normal of the closet surfel in the voxel
          if (do_split_seed) {
            DGF_REAL_UBLK_DESC real_ublk_desc = (DGF_REAL_UBLK_DESC)ublk_desc;
            DGF_GENERAL_VOXEL_DESC voxel_desc = &real_ublk_desc->general_voxels[voxel];
            sINT32 closest_surfel_id = voxel_desc->v.closest_surfel_index;

            if (closest_surfel_id == STP_INVALID_SHOB_ID) {
              do_split_seed = FALSE;
            } else {
              sINT32 index = cp_info.n_dims * closest_surfel_id;
              ccDOTIMES(i, cp_info.n_dims){
                voxel_normal[i] = m_nw_surface_normals[index++];
              }
            }
          }

          // initialize the data before accumulating over the meas cells
          ccDOTIMES(var, m_n_vars) {
            seed_vars[m_seed_var_types[var]][voxel] = 0.0;
          }
	  
          if (do_special_seeding) {    
            seed_vars[m_seed_var_types[m_n_vars-1]][voxel] = -1.0;   //flag of fine->coarse seeded voxel
          }

	  //UDS
	  if (cp_info.is_uds_transport) {
	    ccDOTIMES(var, m_n_uds_vars) {
	      seed_uds_vars[m_seed_uds_var_indices[var]][voxel] = 0.0;
	    }
	  }

          sriLRF_INDEX seed_vars_lrf_index = SRI_INVALID_REF_FRAME_INDEX;

          m_grid->TraverseInit(voxel_grid_box);
          sINT8 seed_voxel;
          sFLOAT common_volume;
          for (pCELL seed_ublk = m_grid->Traverse(seed_voxel, common_volume); 
               seed_ublk != NULL; 
               seed_ublk = m_grid->Traverse(seed_voxel, common_volume)) {
            if (common_volume <= DFLOAT_EPSILON)
              continue;

            if (do_special_seeding && !is_fine_to_coarse && total_volume > common_volume) {
              // seeding fine to coarse
              scale_diff = log2(cbrt( total_volume / common_volume ));

              if (scale_diff > 0.9) {
                is_fine_to_coarse = TRUE;
                seed_vars[m_seed_var_types[m_n_vars-1]][voxel] = 1.0;  //flag fine->coarse seeded voxel
              }
            }

            if (!seed_ublk->IsMultiple(seed_voxel)) {
              sINT64 meas_cell_index = seed_ublk->GetDataIndex(seed_voxel);
              sriLRF_INDEX seed_cell_lrf_index = get_all_vars_and_lrf_index(meas_cell_index, meas_vars, meas_uds_vars);

              BOOLEAN is_valid_cell_special = FALSE;
              if (do_special_seeding) {
                if (meas_vars[n_vars] > DEN_THRESHOLD) {//meas_vars[n_vars] is DENSITY_FROZEN which has value only in near wall voxel
                  is_valid_cell_special = TRUE;
                  if (do_split_seed) {
                    BOOLEAN is_single_overlapping_cell = (common_volume >= total_volume);
                    if (!is_single_overlapping_cell) {
                      sriFLOAT cell_normal[3] = {0};
                      sriINT nw_cell_index = find_nw_cell_index(meas_cell_index);

                      if (nw_cell_index >= 0) {
                        get_cell_surface_normal(nw_cell_index, cell_normal);

                        { //valid closest surfel
                          dFLOAT cos_angle = vdot(voxel_normal, cell_normal);
                          if (cos_angle < cos_angle_threshold) {
                            is_valid_cell_special = FALSE;
                            if (cos_angle > cos_angle_tracked) {
                              cos_angle_tracked = cos_angle;
                              cell_tracked = meas_cell_index;
                              common_volume_tracked = common_volume;
                            }
                          }
                        }
                      } else {
                        is_valid_cell_special = FALSE;
                      }
                    }
                  }
                }

                if (is_valid_cell_special)
                  has_cell_included = TRUE;		
              }

              if (seed_cell_lrf_index == seed_vars_lrf_index) {
                ccDOTIMES(var, n_vars)
                  seed_vars[m_seed_var_types[var]][voxel] += common_volume * meas_vars[var];
		if (cp_info.is_uds_transport) {
		  ccDOTIMES(var, n_uds_vars)
		    seed_uds_vars[m_seed_uds_var_indices[var]][voxel] += common_volume * meas_uds_vars[var];
		}
                accumulated_volume += common_volume;

                if (is_valid_cell_special) {
                  for (asINT32 i = n_vars; i < (m_n_vars-1); i++) {
                    seed_vars[m_seed_var_types[i]][voxel] += common_volume * meas_vars[i];
                  }
                  accumulated_volume_frozen += common_volume;
                }
              }
              else if ((seed_cell_lrf_index == seed_ublk_lrf_index)
                       || (seed_vars_lrf_index == SRI_INVALID_REF_FRAME_INDEX)) {
                seed_vars_lrf_index = seed_cell_lrf_index;
                // overwrite seed_vars accumulated so far
                ccDOTIMES(var, n_vars)
                  seed_vars[m_seed_var_types[var]][voxel] = common_volume * meas_vars[var];
		if (cp_info.is_uds_transport) {
		  ccDOTIMES(var, n_uds_vars)
		    seed_uds_vars[m_seed_uds_var_indices[var]][voxel] = common_volume * meas_uds_vars[var];
		}
                accumulated_volume = common_volume; // overwrite volume accumulated so far

                if (is_valid_cell_special) {
                  for (asINT32 i = n_vars; i < (m_n_vars-1); i++) {
                    seed_vars[m_seed_var_types[i]][voxel] = common_volume * meas_vars[i];
                  }
                  accumulated_volume_frozen = common_volume;
                }
              }
            } else {
              asINT32 cell_n_datasets = m_grid->NCellDatasets(seed_ublk, seed_voxel);
              asINT32 n_used_datasets = 0;

              asINT32 n_used_datasets_frozen = 0;

              ccDOTIMES(i, cell_n_datasets) {
                sINT64 meas_cell_index = m_grid->GetCellMultDataIndex(seed_ublk, seed_voxel, i);
                sriFLOAT tmp_meas_vars[DGF_N_SEED_VARS];
		sriFLOAT tmp_meas_uds_vars[MAX_N_SEED_UDS_VARS];
                sriLRF_INDEX seed_cell_lrf_index = get_all_vars_and_lrf_index(meas_cell_index, tmp_meas_vars, tmp_meas_uds_vars);

                BOOLEAN is_valid_cell_special = FALSE;
                if (do_special_seeding) {
                  if (tmp_meas_vars[n_vars] > DEN_THRESHOLD) {//meas_vars[n_vars] is DENSITY_FROZEN which has value only in near wall voxel
                    is_valid_cell_special = TRUE;
                    if (do_split_seed) {
                      sriFLOAT cell_normal[3] = {0};
                      sriINT index = find_nw_cell_index(meas_cell_index);

                      if (index >= 0) {
                        get_cell_surface_normal(index, cell_normal);

                        {
                          dFLOAT cos_angle = vdot(voxel_normal, cell_normal);
                          if (cos_angle < cos_angle_threshold) {
                            is_valid_cell_special = FALSE;
                            if (cos_angle > cos_angle_tracked) {
                              cos_angle_tracked = cos_angle;
                              cell_tracked = meas_cell_index;
                              common_volume_tracked = common_volume;
                            }
                          }
                        }
                      } else {
                        is_valid_cell_special = FALSE;
                      }
                    }
                  }
                  if (is_valid_cell_special)
                    has_cell_included = TRUE;
                }

                if (seed_cell_lrf_index == seed_vars_lrf_index) {
                  if (n_used_datasets == 0) {
                    ccDOTIMES(j, n_vars)
                      meas_vars[j] = tmp_meas_vars[j];

		    if (cp_info.is_uds_transport) {
		      ccDOTIMES(k, n_uds_vars)
			meas_uds_vars[k] = tmp_meas_uds_vars[k];
		    }
                  } else {
                    ccDOTIMES(j, n_vars)
                      meas_vars[j] += tmp_meas_vars[j];

		    if (cp_info.is_uds_transport) {
		      ccDOTIMES(k, n_uds_vars)
			meas_uds_vars[k] += tmp_meas_uds_vars[k];
		    }
                  }
                  n_used_datasets++;

                  if (is_valid_cell_special) {
                    if (n_used_datasets_frozen == 0) {
                      for (asINT32 i = n_vars; i < (m_n_vars-1); i++) 
                        meas_vars[i] = tmp_meas_vars[i];
                    } else {
                      for (asINT32 i = n_vars; i < (m_n_vars-1); i++)
                        meas_vars[i] += tmp_meas_vars[i];
                    }
                    n_used_datasets_frozen++;
                  }
                } else if ((seed_cell_lrf_index == seed_ublk_lrf_index)
                           || (seed_vars_lrf_index == SRI_INVALID_REF_FRAME_INDEX)) {
                  seed_vars_lrf_index = seed_cell_lrf_index;
                  // overwrite seed_vars accumulated so far
                  ccDOTIMES(var, n_vars) {
                    seed_vars[m_seed_var_types[var]][voxel] = 0;
                    meas_vars[var] = tmp_meas_vars[var];
                  }
		  if (cp_info.is_uds_transport) {
		    ccDOTIMES(var, n_uds_vars) {
		      seed_uds_vars[m_seed_uds_var_indices[var]][voxel] = 0;
		      meas_uds_vars[var] = tmp_meas_uds_vars[var];
		    }
		  }
                  accumulated_volume = 0;  // overwrite volume accumulated so far
                  n_used_datasets = 1;

                  if (is_valid_cell_special) {
                    for (asINT32 i = n_vars; i < (m_n_vars-1); i++) {
                      seed_vars[m_seed_var_types[i]][voxel] = 0;
                      meas_vars[i] = tmp_meas_vars[i];
                    }
                    accumulated_volume_frozen = 0;
                    n_used_datasets_frozen = 1;
                  }
                }
              }

              if (n_used_datasets > 0) {
                ccDOTIMES(var, n_vars) {
                  seed_vars[m_seed_var_types[var]][voxel] += (common_volume / n_used_datasets) * meas_vars[var];
                }

                if (cp_info.is_uds_transport) {
                  ccDOTIMES(var, n_uds_vars)
                    seed_uds_vars[m_seed_uds_var_indices[var]][voxel] += (common_volume / n_used_datasets) * meas_uds_vars[var];
                }
                accumulated_volume += common_volume;
              }

              if (do_special_seeding && n_used_datasets_frozen > 0) {
                for (asINT32 i = n_vars; i < (m_n_vars-1); i++) 
                  seed_vars[m_seed_var_types[i]][voxel] += (common_volume / n_used_datasets_frozen) * meas_vars[i];
                accumulated_volume_frozen += common_volume;
              }
            }
          }

          if (do_split_seed && !has_cell_included && cell_tracked >= 0) {
            sriLRF_INDEX seed_cell_lrf_index = get_all_vars_and_lrf_index(cell_tracked, meas_vars, meas_uds_vars);
            for (asINT32 i = n_vars; i < (m_n_vars-1); i++) {
              seed_vars[m_seed_var_types[i]][voxel] = common_volume_tracked * meas_vars[i];
            }
            accumulated_volume_frozen = common_volume_tracked;
          }

          // Velocities in measurement files are always stored with respect to the global reference frame.
          // They may be in the local or global CSYS depending on whether the box for "Average Vectors in
          // Enclosing Body-Fixed Reference Frame" was checked or not under Measurements in PowerCASE.
          // During smart seeding, these are read by CP and rotated appropriately to the local CSYS of 
          // the ublk being seeded. The seed velocities are then sent over to the SPs.
          //
          // Note: Measurement files created using the --mme_checkpoint_at_end option store the velocities 
          //       in the local CSYS.
          //
          // The scenarios with respect to seeding are listed below:
          //
          //  | seed data csys | ublk csys  | action                |
          //  |----------------|------------|-----------------------|
          //  | global         | global     | no rotation           |
          //  | global         | local      | rotate to local csys  |
          //  | local          | global     | rotate to global csys |
          //  | local (L1)     | local (L1) | no rotation           |
          //  | local (L1)     | local (L2) | rotate to global csys |
          //
          if (m_params.m_seed_file_contains_sliding_lrfs ) {

            if (!is_seed_data_in_local_csys())
              seed_vars_lrf_index = SRI_GLOBAL_REF_FRAME_INDEX;

            if ( seed_vars_lrf_index != seed_ublk_lrf_index ) {

              if (seed_vars_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX)
                rotate_seed_vars_to_global_csys(seed_vars, voxel, seed_vars_lrf_index);

              if ((seed_ublk_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX) &&
                  (seed_ublk_lrf_index != SRI_INVALID_REF_FRAME_INDEX))
                rotate_seed_vars_to_local_csys(seed_vars, voxel, seed_ublk_lrf_index);
            }
          }

          BOOLEAN is_meaningful_seed = ((accumulated_volume) > (0.01 * total_volume));
          if (is_meaningful_seed) {
            // Check for NaNs in seed data 
            ccDOTIMES(var, m_n_vars) {
              if (is_dfloat_nan(seed_vars[m_seed_var_types[var]][voxel])) {
                m_n_voxels_seeded_with_junk ++;
                is_meaningful_seed = FALSE;
                break;
              }
            }

	    if (is_meaningful_seed && cp_info.is_uds_transport) {
	      ccDOTIMES(var, m_n_uds_vars) {
		if (is_dfloat_nan(seed_uds_vars[m_seed_uds_var_indices[var]][voxel])) {
		  m_n_voxels_seeded_with_junk ++;
		  is_meaningful_seed = FALSE;
		  break;
		}
	      }
	    }
          }


          if (is_meaningful_seed) {
            m_n_voxels_seeded ++;
            ccDOTIMES(var, n_vars)
              seed_vars[m_seed_var_types[var]][voxel] /= accumulated_volume;

	    if (cp_info.is_uds_transport) {
	      ccDOTIMES(var, n_uds_vars)
		seed_uds_vars[m_seed_uds_var_indices[var]][voxel] /= accumulated_volume;
	    }

	    if (cp_info.is_5g_sim)  {
	      if (is_component0_density_seeded && is_component1_density_seeded) {
          if (seed_vars[DGF_SEED_VAR_COMP0_DENSITY][voxel] == 0 && seed_vars[DGF_SEED_VAR_COMP1_DENSITY][voxel] == 0)
            msg_internal_error("Seeded densities for both components are zero for ublk %d voxel %d", ublk_desc->b.ublk_id, voxel);
	      }
	    } else if (do_special_seeding) {
              sFLOAT one_over_accumulated_volume_frozen = (accumulated_volume_frozen < 1.0e-6) ? 0.0 : 1.0/accumulated_volume_frozen;
              for (asINT32 i = n_vars; i < (m_n_vars-1); i++) {
                seed_vars[m_seed_var_types[i]][voxel] *= one_over_accumulated_volume_frozen;
              } 
            }

            if (do_special_seeding && seed_vars[m_seed_var_types[m_n_vars-1]][voxel] == 1.0) {
              m_n_voxels_frozen++;
            }

            // Since the scale factors for dimless turb seeding are only a fcn of char props,
            // the rescaling can happen on the CP.
            if(m_do_turb_dimless_seeding) {
              seed_vars[DGF_SEED_VAR_TURB_KINETIC_ENERGY][voxel] *= m_char_vel_ratio_2;
              seed_vars[DGF_SEED_VAR_TURB_DISSIPATION][voxel] *= m_params.coord_scale * m_char_vel_ratio_3;
              seed_vars[DGF_SEED_VAR_STRESS_TENSOR_MAG][voxel] *= m_params.coord_scale * m_char_vel_ratio;
            }

#if 0
            if (m_params.seed_via_dimless_properties)
              scale_seed_vars_via_dimless_properties(seed_vars, voxel);
#endif

            seeded_mask |= 1 << voxel; // Turn on voxel's bit in seeded mask
          }
        }
        voxel += voxel_inc;
      } // dz
    } // dy
  } // dx

  return seeded_mask;
}

// Computes parameters used for smart seeding.
VOID sSMART_SEED_PARAMS::setup(sSRI_FILE *seed_file, cGRID *grid, cBOOLEAN seed_from_meas) {

  char_density = cdi_data.char_density;
  char_vel = cdi_data.char_vel;
  char_temp = cdi_data.char_temp;
  lattice_gas_const = cdi_data.lattice_gas_const;
  seed_via_dimless_properties = FALSE;

  //seed_via_mks is set when command line arguments are parsed in the context
  //of a specific seed file. This setup method is never called with
  //seed_from_meas = TRUE unless reading seed data from the cdi file.
  //if(!seed_from_meas) {
  //  seed_via_mks = sim_args.seed_mks;
  //}

  if (seed_from_meas) {
    is_seed_rotate_vel = FALSE;
  }

  seed_sim_was_incompressible = seed_file->is_density_constant();
  char_vel_seed = char_density_seed = char_temp_seed = -1.0;
  is_seed_data_in_local_csys = seed_file->file_params()->is_output_in_local_csys;
  mks_vel_scaling = 1.0;

  dFLOAT seed_lattice_gamma = OLD_LATTICE_GAMMA;
  dFLOAT seed_char_vel = -1.0;
  dFLOAT seed_fixed_temp_lattice = 1.0/3.0;
  dFLOAT seed_mean_temp = -1.0;
  dFLOAT seed_lattice_gas_const = lattice_gas_const_seed = 1.0;
  dFLOAT max_exp_vel_seed = -1.0;
  SRI_CHAR_PARAM_DESC char_param = seed_file->file_params()->params;
  ccDOTIMES (i, seed_file->file_params()->n_params) {
    switch (char_param->param_type) {
    case SRI_CHAR_VELOCITY:
      char_vel_seed = char_param->value;
      seed_char_vel = char_vel_seed;
      break;
    case SRI_CHAR_DENSITY:
      char_density_seed = char_param->value;
      break;
    case SRI_CHAR_TEMP:
      char_temp_seed = char_param->value;
      break;
    case SRI_CHAR_MEAN_TEMP:
      seed_mean_temp = char_param->value;
      break;
    case SRI_LATTICE_GAMMA:
      seed_lattice_gamma = char_param->value;
      break;
    case SRI_LATTICE_GAS_CONSTANT:
      seed_lattice_gas_const = char_param->value;
      lattice_gas_const_seed = char_param->value;
      break;
    case SRI_MAX_EXP_VEL:
      max_exp_vel_seed = char_param->value;
      break;
    default:
      break;
    }
    char_param++;
  }

  dFLOAT cdi_fixed_temp_lattice = (cdi_data.char_temp > 0.0
                                   ? cdi_data.char_temp
                                   : (cdi_data.lattice_gamma == OLD_LATTICE_GAMMA ? 0.42 : 1.0/3.0));

  seed_fixed_temp_lattice = (char_temp_seed > 0.0
                             ? char_temp_seed
                             : (seed_lattice_gamma == OLD_LATTICE_GAMMA ? 0.42 : 1.0/3.0));

  dFLOAT seed_kelvins_per_lattice_temp = 1.0;
  dFLOAT seed_meters_per_cell = 1.0;
  dFLOAT seed_mks_density_scaling = 1.0;
  dFLOAT mks_density_scaling = DENSITY_CONVERSION_FACTOR * cdi_data.kilos_per_particle/
    (cdi_data.meters_per_cell * cdi_data.meters_per_cell * cdi_data.meters_per_cell);


  SRI_LATTICE_TRANSLATION lx = seed_file->file_params()->translations;
  ccDOTIMES (i, seed_file->file_params()->n_translations) {
    if (!strcmp(lx->name, "LatticeTemperature"))
      seed_kelvins_per_lattice_temp = lx->scale;
    else if (!strcmp(lx->name, "LatticeLength"))
      seed_meters_per_cell = lx->scale;
    else if (!strcmp(lx->name, "LatticeVelocity"))
      mks_vel_scaling = lx->scale;
    else if (!strcmp(lx->name, "LatticeDensity"))
      seed_mks_density_scaling = lx->scale;
    lx++;
  }

  if (seed_via_mks) {
    // Check that ratio of char velocity and max expected velocity in MKS units are the same if and only if
    // the maximum expected velocity is in BOTH the seed file and the CDI file.
    BOOLEAN max_exp_vel_in_seed_and_cdi_files = (max_exp_vel_seed > -1.0) && (cdi_data.max_exp_vel > -1.0);
    if (max_exp_vel_in_seed_and_cdi_files)
      if (fabs(char_vel/cdi_data.max_exp_vel - char_vel_seed/max_exp_vel_seed) > 1e-5)
        msg_error("The --seed_mks option requires that the ratio of characteristic velocity and max"
                  " expected velocity in MKS units is the same for the seed file and the new simulation.");

    dFLOAT char_temp_mks_seed = seed_kelvins_per_lattice_temp * char_temp_seed;
    dFLOAT char_temp_mks = cdi_data.kelvins_per_lattice_temp * char_temp;
    BOOLEAN is_active_ht = cp_info.sim_ht_type == SRI_HT_ACTIVE_SCALAR;

    if (fabs(char_temp_mks_seed - char_temp_mks) > 1e-5)
      msg_error("The --seed_mks option requires that the characteristic temperature in MKS units is the same"
                " for the seed file and the new simulation.");

    // Mean temperature is known as "Max expected temperature" in the PowerCASE UI...
    dFLOAT mean_temp_mks = cdi_data.kelvins_per_lattice_temp * cdi_data.mean_temp;
    dFLOAT mean_temp_mks_seed = seed_kelvins_per_lattice_temp * seed_mean_temp;
    if (is_active_ht && fabs(mean_temp_mks - mean_temp_mks_seed) > 1e-5)
      msg_error("The --seed_mks option requires that the max expected temperature in MKS units is the same"
                " for the seed file and the new simulation.");
    if ((fabs(char_vel_seed - char_vel) > 1e-5)
        || (fabs(mks_density_scaling - seed_mks_density_scaling) > 1e-5)
        || (fabs(char_temp_seed - char_temp) > 1e-5)
        || (fabs(lattice_gas_const_seed - lattice_gas_const) > 1e-5)
        || (fabs(char_density_seed - char_density) > 1e-5)
        || (fabs(mks_density_scaling - seed_mks_density_scaling) > 1e-5))
      msg_error("The --seed_mks option requires certain simulation type settings to be the same"
                " for the seed file and the new simulation: Internal/External flow, Turbulence Model/Direct,"
                " Heat Transfer On/Off, Ideal Gas/Liquid, Mach Regime. Also Simulated Mach Number must be set"
                " to Chosen by PowerFLOW.");
  }


  if (!cp_info.is_5g_sim) { //disable seed_via_dimless_properties for 5G
    if (seed_char_vel >= 0 && !seed_via_mks) {
      dFLOAT cdi_mach = cdi_data.char_vel / sqrt(cdi_data.lattice_gamma * cdi_data.lattice_gas_const * cdi_fixed_temp_lattice);
      dFLOAT seed_mach = seed_char_vel / sqrt(seed_lattice_gamma * seed_lattice_gas_const * seed_fixed_temp_lattice);

      dFLOAT cdi_gamma_mach_sqrd = cdi_data.lattice_gamma * cdi_mach * cdi_mach;
      dFLOAT seed_gamma_mach_sqrd = seed_lattice_gamma * seed_mach * seed_mach;

      if ((cdi_gamma_mach_sqrd > (seed_gamma_mach_sqrd + 1.0e-5))
          || (cdi_gamma_mach_sqrd < (seed_gamma_mach_sqrd - 1.0e-5))) {
        seed_via_dimless_properties = TRUE;

        if (cdi_data.lattice_gamma == seed_lattice_gamma) {

          cSTRING dimless_parameter_name = "momentum";
          if (seed_sim_was_incompressible) dimless_parameter_name = "velocity";

          msg_warn("The simulation Mach number for the seed file %s (%g) differs"
                   " from the simulation Mach number for this case (%g)."
                   " To maintain continuity, values of Cp and dimensionless %s from"
                   " the seed file will be used to seed the new simulation.",
                   seed_file->name(), seed_mach, cdi_mach, dimless_parameter_name);
        }
        else {
          msg_warn("The value of gamma*Mach^2 for the seed file %s (Mach number: %g, gamma*Mach^2: %g) differs"
                   " from the value of gamma*Mach^2 for this case (Mach number: %g, gamma*Mach^2: %g)."
                   " To maintain continuity, values of Cp and dimensionless momentum from"
                   " the seed file will be used to seed the new simulation.",
                   seed_file->name(),
                   seed_mach, seed_gamma_mach_sqrd, cdi_mach, cdi_gamma_mach_sqrd);
        }
      }
      else if ((cdi_data.char_density > (char_density_seed + 1.0e-5))
               || (cdi_data.char_density < (char_density_seed - 1.0e-5))
               || (cdi_data.char_vel > (seed_char_vel + 1.0e-5))
               || (cdi_data.char_vel < (seed_char_vel - 1.0e-5))
               || (cdi_data.lattice_gas_const > (seed_lattice_gas_const + 1.0e-5))
               || (cdi_data.lattice_gas_const < (seed_lattice_gas_const - 1.0e-5))
               || (cdi_data.char_temp > (char_temp_seed + 1.0e-5))
               || (cdi_data.char_temp < (char_temp_seed - 1.0e-5))) {
        seed_via_dimless_properties = TRUE;
      }
    }


    dFLOAT cdi_mean_temp = cdi_data.mean_temp;
    if (seed_mean_temp > 0) {
      // seed file from active scalar sim
      if (cdi_mean_temp > 0) {
        // both active scalar
        if ((cdi_fixed_temp_lattice > (seed_fixed_temp_lattice + .001))
            || (cdi_fixed_temp_lattice < (seed_fixed_temp_lattice - .001))) {
          msg_warn("The max expected temperature of the seed file %s (%g K) differs from the"
                   " max expected temperature of this case (%g K)."
                   " To maintain continuity, values of dimensionless temperature from"
                   " the seed file will be used to seed the new simulation.",
                   seed_file->name(),
                   seed_kelvins_per_lattice_temp * seed_mean_temp,
                   cdi_data.kelvins_per_lattice_temp * cdi_mean_temp);
        }
      } else {
        // seed active scalar; current sim not
        msg_warn("The seed file %s was generated by a heat transfer case with coupled"
                 " momentum and temperature equations, while this case %s.",
                 seed_file->name(),
                 !cp_info.is_heat_transfer? "is isothermal" : "does not couple momentum and temperature");
      }
    } else {
      // seed file not from active scalar sim
      if (cdi_mean_temp > 0) {
        // seed not active scalar; current sim is active scalar
        msg_warn("This case is a heat transfer case with coupled"
                 " momentum and temperature equations, while the seed file %s was"
                 " generated by either an isothermal case or a heat transfer case"
                 " that did not couple momentum and temperature.", seed_file->name());
      }
    }
  }

  //Find the coefficients for data conversion.
  if (grid) {
    dFLOAT *lgi_origin = cdi_data.origin;
    dFLOAT lgi_meters_per_cell = cdi_data.meters_per_cell;
    if (cp_info.n_dims != grid->GetNDims()) {
      msg_error("Dimensions of seed file (%d) do not match this simulation (%d).",
                grid->GetNDims(), cp_info.n_dims);
    }
    dFLOAT seed_file_origin[3];
    seed_file->file_params()->lattice_csys()->get_origin(seed_file_origin);

    // get_origin actually returns lattice_origin - csys_origin, where
    // lattice_origin = (0,0,0). To get csys_origin, which is what we really
    // need, reverse the sign

    seed_file_origin[0] *= -1.0; seed_file_origin[1] *= -1.0; seed_file_origin[2] *= -1.0;
    if (seed_meters_per_cell == 0.0) /* lattice units */
      seed_meters_per_cell = 1.0;
    asINT32 seed_n_scales = seed_file->n_scales();
    // If both meters_per_cell are 0, the coordinate scale factor is 1. It's illegal for just
    // one of them to be zero.
    //  if (cdi_n_scales == seed_n_scales)
    coord_scale = 1.0;
    if (seed_meters_per_cell > DFLOAT_EPSILON) {
      if (lgi_meters_per_cell > DFLOAT_EPSILON)
        coord_scale = lgi_meters_per_cell/seed_meters_per_cell;
      else{
        msg_internal_error("CDI file \"%s\" does not define meters_per_cell value.",
                           sim_args.cdi_filename);
      }
    } else if (lgi_meters_per_cell > DFLOAT_EPSILON) {
      msg_internal_error("Seed data file \"%s\" does not define meters_per_cell value.",
                         seed_file->name());
    }

    //the transformation from lgi to to sri coordinates is:
    csPOINT lgi_point = lgi_origin;
    csPOINT seed_point = seed_file_origin;
    origin_shift =  seed_point - lgi_point.scale((sFLOAT)(coord_scale));
  }
}

VOID sSMART_SEED_PARAMS::fill_params(cDGF_SMART_SEED_CONTROL &header)
{
  header.tag.id = DGF_SMART_SEED_CONTROL_TAG;
  asINT32 length = sizeof(header);
  header.tag.length = lgi_pad_and_encode_record_length(length);
  if (seed_via_dimless_properties)
    header.seed_scaling_type = DGF_DIMLESS_SCALING;
  else if (seed_via_mks)
    header.seed_scaling_type = DGF_MKS_SCALING;
  else
    header.seed_scaling_type = DGF_NO_SCALING;
  header.seed_data_mks_vel_scaling = mks_vel_scaling;
  header.seed_sim_was_incompressible = seed_sim_was_incompressible;
  header.seed_data_char_density = char_density_seed;
  header.seed_data_char_vel     = char_vel_seed;
  header.seed_data_char_temp    = char_temp_seed;
  header.seed_data_lattice_gas_const = lattice_gas_const_seed;
  header.n_variables            = 0;
  header.n_fluid_seed_var_specs = 0;
  header.n_boundary_seed_var_specs = 0;
  // Rotate the seed velocity by a specified angle about the x,y or z axis of any coordinate system
  if (is_seed_rotate_vel) {
    // csys
    header.seed_rotate_vel_csys_index = 0;
    cBOOLEAN found_csys = FALSE;
    ccDOTIMES(nth_csys, cp_info.all_csys->num_coord_systems) {
      if (strcmp(cp_info.all_csys->coord_systems[nth_csys].name, seed_rotate_vel_csys_name) == 0) {
        header.seed_rotate_vel_csys_index = nth_csys;
        found_csys = TRUE;
      }
    }
    if (!found_csys) {
      msg_error("Could not find a coordinate system with the name \"%s\". Please provide a valid argument to the option --seed_rotate_vel.", seed_rotate_vel_csys_name);
    }
    // axis
    header.seed_rotate_vel_axis = seed_rotate_vel_axis;
    // angle
    header.seed_rotate_vel_angle = seed_rotate_vel_angle;
  }
  else {
    header.seed_rotate_vel_csys_index = 0;
    header.seed_rotate_vel_axis  = 0;
    header.seed_rotate_vel_angle = 0.0;
  }
}

VOID sSMART_SEED_PARAMS::print(std::ostream &stream) const {
    stream << "seed_via_mks: " << (seed_via_mks ? "true" : "false") << std::endl;
    stream << "seed_sim_was_incompressible: " << (seed_sim_was_incompressible ? "true" : "false") << std::endl;
    stream << "is_seed_data_in_local_csys: " << (is_seed_data_in_local_csys ? "true" : "false") << std::endl;
    stream << "origin_shift: (" << origin_shift.GetX() << ", " << origin_shift.GetY() << ", " << origin_shift.GetZ() << ")" << std::endl;
    stream << "coord_scale: " << coord_scale << std::endl;
    stream << "char_density: " << char_density << std::endl;
    stream << "char_vel: " << char_vel << std::endl;
    stream << "char_temp " << char_temp << std::endl;
    stream << "mks_vel_scaling: " << mks_vel_scaling << std::endl;
    stream << "lattice_gas_const: " << lattice_gas_const << std::endl;
    stream << "char_density_seed: " << char_density_seed << std::endl;
    stream << "char_vel_seed: " << char_vel_seed << std::endl;
    stream << "char_temp_seed: " << char_temp_seed << std::endl;
    stream << "lattice_gas_const_seed: " << lattice_gas_const_seed << std::endl;
}

BOOLEAN cSMART_SEED_CONTROL::check_seed_file_type() {
  BOOLEAN is_volumetric_data = m_seed_file->file_type() == SRI_FLUID_TYPE; 
  // With the addition of solid conduction, SRI_FLUID_TYPE has become
  // a misnomer. It now refers to whether a meas file contains
  // volumetric measurements (like only a fnc fluid file previously
  // could) as opposed to surface measurements (like an snc file).
  if(!is_volumetric_data) {
    msg_error("File \"%s\" does not contain volumetric measurements and can't be used for seeding.", m_seed_file->name());
    return false;
  }
  
#ifdef DISABLE_CROSS_REALM_SEED_FILES
  // To distinguish between solid and fluid volumetric measurements
  // for a file of SRI_FLUID_TYPE, use the is_solid_file file
  // property.
  REALM seed_file_realm = m_seed_file->file_params()->is_solid_file ? STP_COND_REALM : STP_FLOW_REALM;
  if( seed_file_realm != m_realm ) {
    switch(seed_file_realm) {
    case STP_FLOW_REALM:
      msg_error("File \"%s\" contains fluid measurements and can't be used to seed solid conduction regions.", m_seed_file->name());
      break;
    case STP_COND_REALM:
      msg_error("File \"%s\" contains solid conduction measurements and can't be used to seed fluid regions.", m_seed_file->name());
      break;
    default:
      assert( seed_file_realm == STP_FLOW_REALM || seed_file_realm == STP_COND_REALM);
    }
    return false;
  }
#endif
  return true;
}

VOID cSMART_SEED_CONTROL::setup()
{
  // seed filename exists both for MME checkpoint restore and smart seeding

  if (m_seed_filename == nullptr) {
    return;
  }

  cp_jobctl_output_status("Begin seed setup");

  m_n_voxels_seeded = 0;
  m_n_voxels_seeded_with_junk = 0;
  m_n_voxels_frozen = 0;
  m_found_seed_var_spec_physics_desc = cnew cBOOLEAN[n_fluid_seed_var_specs()];

  m_n_points = m_seed_file->n_points();

  m_n_nw_norm_meas_cells = ((SRI_FLUID_FILE)m_seed_file)->n_nw_norm_meas_cells();

  BOOLEAN is_3d = (cp_info.n_dims == 3);

  cp_jobctl_output_status("Build grid for seeding");
  m_grid = new cGRID(m_seed_file);  // file can be NULL

  cp_jobctl_output_status("Compute seed parameters from seed file");

  m_params.setup(m_seed_file, m_grid, FALSE);

  /* Determine the measurement frame for seeding fluid. */
  if (m_seed_frame == -1) {
    m_seed_file->set_meas_frame(m_seed_file->n_frames()-1);
  } else {
    if (m_seed_frame < m_seed_file->n_frames()) {
      m_seed_file->set_meas_frame(m_seed_frame);
    } else {
      msg_warn("Specified seed file frame (%d) exceeds the maximum present in the seed file (%d). Using the last frame.",
               m_seed_frame, m_seed_file->n_frames()-1);
      m_seed_file->set_meas_frame(m_seed_file->n_frames()-1);
    }
  }

  cp_jobctl_output_status("Read data frame from seed file");

  // determine the number of variables to read from the measurement file for seeding
  asINT32 n_vars = 0;
  cBOOLEAN vars_found[DGF_N_SEED_VARS] = { FALSE };
  SRI_VARIABLE_TYPE vars_not_found[DGF_N_SEED_VARS];
  asINT32 n_vars_not_found = 0;

  //UDS
  asINT32 n_uds_vars = 0;
  cBOOLEAN uds_vars_found[MAX_N_SEED_UDS_VARS] = { FALSE };
  SRI_VARIABLE_TYPE uds_vars_not_found[MAX_N_SEED_UDS_VARS];
  asINT32 n_uds_vars_not_found = 0;
  
  SRI_CUSTOM_VAR_ID_HELPER seed_vih = m_seed_file->file_params()->custom_var_id_helper;

  // For an active scalar heat transfer sim, temperature is used both to re-scale
  // velocity through dimless properties and on the SP in the equation of state to
  // determine density from pressure.

  // The default seed_var_spec (m_seed_var_specs[0]) includes all variables. If not used,
  // we should ignore it when filling in is_seed_var_needed below.
  asINT32 first_used_seed_var_spec;
  if (m_part_seed_var_spec_indices) {
    first_used_seed_var_spec = n_fluid_seed_var_specs();
    ccDOTIMES(i, cp_info.n_sri_parts) {
      if (m_part_seed_var_spec_indices[i] - m_spec_base_index < first_used_seed_var_spec)
        first_used_seed_var_spec = m_part_seed_var_spec_indices[i] - m_spec_base_index;
    }
  } else {
    first_used_seed_var_spec = 0;
  }

  cBOOLEAN is_seed_var_needed[DGF_N_SEED_VARS] = { FALSE };
  cBOOLEAN is_seed_uds_var_needed[MAX_N_SEED_UDS_VARS] = { FALSE };
  // There is always at least one seed var spec that is used
  for (asINT32 i = first_used_seed_var_spec; i < n_fluid_seed_var_specs(); i++) { 
    FLUID_SEED_VAR_SPEC seed_var_spec = &m_fluid_seed_var_specs[i];
    ccDOTIMES(v, seed_var_spec->n_vars)
      is_seed_var_needed[seed_var_spec->seed_var_types[v]] = TRUE;

    if (cp_info.is_uds_transport && !cp_info.is_5g_sim) {
      ccDOTIMES(k, seed_var_spec->n_uds_vars)
	is_seed_uds_var_needed[seed_var_spec->seed_uds_var_indices[k]] = TRUE; 
    }
  }

#define is_seed_var_available(name)                                     \
  (m_seed_file->is_variable_derivable(SRI_VARIABLE_ ## name))

#define check_for_var(name)                                             \
  if (is_seed_var_needed[DGF_SEED_VAR_ ## name]) {                      \
    if (is_seed_var_available(name)) {                                  \
      m_sri_var_types[n_vars]    = SRI_VARIABLE_ ## name;               \
      m_seed_var_types[n_vars++] = DGF_SEED_VAR_ ## name;               \
      vars_found[DGF_SEED_VAR_ ## name] = TRUE;                         \
    } else                                                              \
      vars_not_found[n_vars_not_found++] = SRI_VARIABLE_ ## name;       \
  }

#define check_for_var_mix(name1,name2)					\
  if (is_seed_var_needed[DGF_SEED_VAR_ ## name1]) {                      \
    if (is_seed_var_available(name2)) {                                  \
      m_sri_var_types[n_vars]    = SRI_VARIABLE_ ## name2;               \
      m_seed_var_types[n_vars++] = DGF_SEED_VAR_ ## name1;               \
      vars_found[DGF_SEED_VAR_ ## name1] = TRUE;                         \
    } else                                                              \
    vars_not_found[n_vars_not_found++] = SRI_VARIABLE_ ## name2;       \
  }

#define is_seed_var_available_5g_or_uds(id)                                    \
  m_seed_file->is_variable_present_in_file(id)

#define check_for_per_component_var_5g(name,id)		                        \
  if (is_seed_var_needed[DGF_SEED_VAR_ ## name]) {                      \
    if (is_seed_var_available_5g_or_uds(id)) {                                 \
      m_sri_var_types[n_vars]    = id;					\
      m_seed_var_types[n_vars++] = DGF_SEED_VAR_ ## name;		\
      vars_found[DGF_SEED_VAR_ ## name] = TRUE;                         \
    } else                                                              \
      vars_not_found[n_vars_not_found++] = id;				\
  }

#define check_for_global_var_5g(name)			                           \
  if (is_seed_var_needed[DGF_SEED_VAR_ ## name]) {                                         \
    SRI_VARIABLE_TYPE id = (SRI_VARIABLE_TYPE)seed_vih->get_5g_var_id(SRI_VARIABLE_ ## name);\
    if (is_seed_var_available_5g_or_uds(id)) {                                                    \
      m_sri_var_types[n_vars]    = id;					                   \
      m_seed_var_types[n_vars++] = DGF_SEED_VAR_ ## name;		                   \
      vars_found[DGF_SEED_VAR_ ## name] = TRUE;                                            \
    } else                                                                                 \
      vars_not_found[n_vars_not_found++] = id;				                   \
  }

#define check_for_global_var_5g_mix(name, id)				\
  if (is_seed_var_needed[DGF_SEED_VAR_ ## name]) {                                         \
    if (is_seed_var_available_5g_or_uds(id)) {                                                    \
      m_sri_var_types[n_vars]    = id;					                   \
      m_seed_var_types[n_vars++] = DGF_SEED_VAR_ ## name;		                   \
      vars_found[DGF_SEED_VAR_ ## name] = TRUE;                                            \
    } else                                                                                 \
      vars_not_found[n_vars_not_found++] = id;				                   \
  }	

  if (cp_info.is_heat_transfer) {
    if (cp_info.sim_ht_type == SRI_HT_ACTIVE_SCALAR
        && !m_params.seed_sim_was_incompressible
        && (is_seed_var_needed[DGF_SEED_VAR_XVEL] || is_seed_var_needed[DGF_SEED_VAR_XVEL] || is_seed_var_needed[DGF_SEED_VAR_XVEL])) {
      // To maintain continuity, need to re-scale velocity for an active scalar thermal simulation if temperature 
      // is missing from the seed file (because density will based on CDI file temperature or char temp)
      is_seed_var_needed[DGF_SEED_VAR_TEMP] = TRUE;
      check_for_var(TEMP);
      if (!vars_found[DGF_SEED_VAR_TEMP])
        if (!m_params.seed_via_dimless_properties) {
          msg_warn("Seed file does not contain temperature, so the seeded density will be based on the temperature"
                   " initial condition found in the CDI file. As such, the seed file velocity will be rescaled"
                   " to maintain continuity.");
          m_params.seed_via_dimless_properties = TRUE;
        }
    } else {
      check_for_var(TEMP);
    }
  }

  // Finished checking for temperature. This is done whether or not the realm
  // is fluid or solid. Issue warning for solid conduction files if temperature
  // isn't found and then try to find all the other fluid variables. Could put
  // in a big if statement.
  if (n_vars_not_found > 0 && m_seed_file->file_params()->is_solid_file) {
    char msg[4096];
    sprintf(msg, 
            "Seed file \"%s\" does not contain these variables: ",
            m_seed_file->name());
    ccDOTIMES(i, n_vars_not_found) {
      if (vars_not_found[i] < SRI_VARIABLE_FIRST_NON_PREDEFINED) {
        sprintf(msg + strlen(msg), "%s%s", sri_variable_type_to_long_name(vars_not_found[i]),
                i == (n_vars_not_found - 1) ? ". " : ", ");
      }
    }

    sprintf(msg + strlen(msg), 
	    "Seeded values of these variables will be based on the"
	    " initial conditions specified in the CDI file.");
    msg_warn("%s", msg);
  }

  if (m_params.seed_via_dimless_properties
      && !m_params.seed_sim_was_incompressible
      && (is_seed_var_needed[DGF_SEED_VAR_XVEL] || is_seed_var_needed[DGF_SEED_VAR_XVEL] || is_seed_var_needed[DGF_SEED_VAR_XVEL]))
    is_seed_var_needed[DGF_SEED_VAR_PRESSURE] = TRUE; // need pressure to re-scale velocity

  if (cp_info.is_5g_sim) {
    if (seed_vih == NULL)
      msg_error("Unable to find parameters of custom variable ID helper from seed file  \"%s\".",m_seed_file->name());

    SRI_VARIABLE_TYPE id = (SRI_VARIABLE_TYPE)seed_vih->get_5g_var_id(SRI_VARIABLE_DENSITY, 0);
    check_for_per_component_var_5g(COMP0_DENSITY,id);
    if (cp_info.num_fluid_components > 1) {
      id = (SRI_VARIABLE_TYPE)seed_vih->get_5g_var_id(SRI_VARIABLE_DENSITY, 1);
      check_for_per_component_var_5g(COMP1_DENSITY,id);
    }

    if (m_smart_seed_contact_angle) {
      check_for_global_var_5g(CONTACT_ANGLE);

      if (!vars_found[DGF_SEED_VAR_CONTACT_ANGLE])
        msg_error("Unable to find contact angle from seed_file \"%s\".", m_seed_filename);
    } else if (cp_info.is_large_pore_sim && (cp_info.num_fluid_components > 1)) {//large pore multi-phase
      check_for_global_var_5g(CONTACT_ANGLE);
    }

    check_for_global_var_5g(DYNAMIC_SCALAR_MULTIPLIER);
    check_for_global_var_5g(XVEL);
    check_for_global_var_5g(YVEL);
    if (is_3d) {
      check_for_global_var_5g(ZVEL);
    }

    if (cp_info.is_large_pore_sim) {
      id = (SRI_VARIABLE_TYPE)seed_vih->get_5g_var_id(SRI_VARIABLE_POROSITY);
      check_for_global_var_5g_mix(TURB_KINETIC_ENERGY, id);

      id = (SRI_VARIABLE_TYPE)seed_vih->get_5g_var_id(SRI_VARIABLE_PM_MODE_SELECTOR);
      check_for_global_var_5g_mix(TURB_DISSIPATION, id);

      if (cp_info.input_rock_table) {
	      id = (SRI_VARIABLE_TYPE)seed_vih->get_5g_var_id(SRI_VARIABLE_ROCK_TYPE);
	      check_for_global_var_5g_mix(TEMP, id);
      }
    }
  } else {
    check_for_var(PRESSURE);
    check_for_var(XVEL);
    check_for_var(YVEL);
    if (is_3d)
      check_for_var(ZVEL);
    if(cp_info.is_seed_body_force){
      check_for_var(XPRESSURE_GRADIENT);
      check_for_var(YPRESSURE_GRADIENT);
      if (is_3d)
	check_for_var(ZPRESSURE_GRADIENT);
    }
  }

  if (cp_info.is_water_vapor_transport) {
    if (cp_info.is_5g_sim) {
      check_for_global_var_5g(WATER_VAPOR_MFRAC);
    } else {
      is_seed_var_needed[DGF_SEED_VAR_WATER_VAPOR_MFRAC] = TRUE;  //why need this? water_vapor_mfrac is in the default_fluid_seed_vars
      check_for_var(WATER_VAPOR_MFRAC);
    }    
  }

  // We don't actually seed density - we seed pressure. However, if we have to seed via dimless
  // pressure and dimless momentum, and we are using pressure from the seed file, we also need
  // density from the seed file so that we can rescale velocity (via dimless momentum). But we
  // don't use density to re-scale velocity through dimless properties if the seed file is from         
  // an incompressible simulation
  if (m_params.seed_via_dimless_properties
      && !m_params.seed_sim_was_incompressible
      && vars_found[DGF_SEED_VAR_PRESSURE]
      // have we taken velocity from the seed file, which must be rescaled
      && (vars_found[DGF_SEED_VAR_XVEL] || vars_found[DGF_SEED_VAR_YVEL] || vars_found[DGF_SEED_VAR_ZVEL])) {
    is_seed_var_needed[DGF_SEED_VAR_DENSITY] = TRUE;
    check_for_var(DENSITY);
  }

  if (m_params.seed_via_dimless_properties
      && !m_params.seed_sim_was_incompressible
      && (vars_found[DGF_SEED_VAR_XVEL] || vars_found[DGF_SEED_VAR_YVEL] || vars_found[DGF_SEED_VAR_ZVEL])
      && (!vars_found[DGF_SEED_VAR_PRESSURE]
          || !vars_found[DGF_SEED_VAR_DENSITY]))
    msg_warn("Seed file does not contain %s, so dimensionless velocity will be used"
             " from the seed file instead of dimensionless momentum.",
             !vars_found[DGF_SEED_VAR_PRESSURE]
             ? !vars_found[DGF_SEED_VAR_DENSITY] ? "pressure and density" : "pressure"
             : "density");

  if (cp_info.is_turb) {
    check_for_var(TURB_KINETIC_ENERGY);
    check_for_var(TURB_DISSIPATION);

    // If the user chooses to use the seed file velocity (all components) for a region, we should 
    // also use the seed file stress_tensor_mag. Otherwise, we should initialize the region with 
    // a stress_tensor_mag of 0.
    if (vars_found[DGF_SEED_VAR_XVEL] && vars_found[DGF_SEED_VAR_YVEL] 
        && (!is_3d || vars_found[DGF_SEED_VAR_ZVEL]))
      check_for_var(STRESS_TENSOR_MAG);
  }

  if (cp_info.local_vel_freeze) {
    //Make sure density_frozen, xvel_frozen, yvel_frozen, zvel_frozen and ustar_0 are seeded var just before DYNAMIC_SCALAR_MULTIPLIER. They will be seeded specially.
    //Since DGF_N_SEED_VARS has to be <=15 to enable sUBLK_SMART_SEED_DATA in sp to overlap (not exceed) ublk->states[19][8] for 19s, here we borrow 3 5G seed vars to represent 3 frozen vel vars

    check_for_var(DENSITY_FROZEN);
    check_for_var_mix(COMP0_DENSITY,XVEL_FROZEN);
    check_for_var_mix(COMP1_DENSITY,YVEL_FROZEN);
    if (is_3d)
      check_for_var_mix(CONTACT_ANGLE,ZVEL_FROZEN);

    check_for_var(USTAR); 

    //hack
    m_sri_var_types[n_vars]    = SRI_VARIABLE_DYNAMIC_SCALAR_MULTIPLIER;
    m_seed_var_types[n_vars++] = DGF_SEED_VAR_DYNAMIC_SCALAR_MULTIPLIER;

    if (!vars_found[DGF_SEED_VAR_USTAR] || !vars_found[DGF_SEED_VAR_DENSITY_FROZEN] ||
        !vars_found[DGF_SEED_VAR_COMP0_DENSITY] || !vars_found[DGF_SEED_VAR_COMP1_DENSITY] || (is_3d && !vars_found[DGF_SEED_VAR_CONTACT_ANGLE])) {
      msg_error("The seed file \"%s\" is not compatible with selective velocity freeze enabled in this CDI file. An "
                "average fluid checkpoint file (.avg.ckpt.fnc) is required for the seed file or selective velocity freeze "
                "should be disabled.", m_seed_filename);
    }

    if (m_n_nw_norm_meas_cells > 0) {
      m_nw_norm_meas_cell_indices = new sriINT [m_n_nw_norm_meas_cells];

      if (SRI_SUCCESS != ((SRI_FLUID_FILE)m_seed_file)->read_nw_norm_meas_cell_indices(m_nw_norm_meas_cell_indices, m_n_nw_norm_meas_cells, 0))
        msg_error("Fail to read nw_norm_meas_cell_indices from seed file \"%s\".",m_seed_file->name());
    } else {
      msg_warn("Selective velocity freeze needs nw_surface_normals, but no valid nw_surfsel_normals in seed file \"%s\". "
               "Special treatment for split voxel seeding will not be preformed. ", m_seed_file->name());
      cp_info.do_split_seed = FALSE;
    }
  }

   if (cp_info.is_uds_transport && !cp_info.is_5g_sim) {
     if (seed_vih == NULL)
       msg_error("Unable to find parameters of custom variable ID helper from seed file  \"%s\".",m_seed_file->name());
     if (m_seed_file->file_params()->scalars == NULL)
       msg_error("Unable to find info of scalars from seed file  \"%s\".",m_seed_file->name());
 
     for (int nth_uds = 0; nth_uds < cp_info.n_scalars; nth_uds++) {
       sINT32 index = (int)DGF_SEED_UDS_VAR_SCALAR_VALUE + nth_uds * (int)DGF_N_SEED_UDS_VARS;
       if (is_seed_uds_var_needed[index]) {
 	for(asINT32 i=0; i<m_seed_file->file_params()->n_scalars; i++) {
 	  if (strcmp(m_seed_file->file_params()->scalars[i].name, cp_info.s_scalar_materials[nth_uds].name) == 0) {
 	    SRI_VARIABLE_TYPE id = (SRI_VARIABLE_TYPE)seed_vih->get_uds_var_id(SRI_VARIABLE_UDS_SCALAR_OFFSET, i); //based on seed_file 
 	    if (is_seed_var_available_5g_or_uds(id)) {
 	      m_sri_uds_var_types[n_uds_vars]    = id;
 	      m_seed_uds_var_indices[n_uds_vars++] = index;     //based on the current cdi file
 	      uds_vars_found[index] = TRUE;
 	    }
 	    break;
 	  }
 	}
 	if (!uds_vars_found[index]) {
 	  SRI_VARIABLE_TYPE id = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_uds_var_id(SRI_VARIABLE_UDS_SCALAR_OFFSET, nth_uds);  //based on the current cdi file
 	  uds_vars_not_found[n_uds_vars_not_found++] = id;	 //only used for printing
 	}
       }
     }
   }

#undef check_for_var
#undef is_seed_var_available
#undef check_for_per_componentvar_5g
#undef check_for_global_var_5g
#undef is_seed_var_available_5g_or_uds
#undef check_for_var_mix

  if ((n_vars_not_found > 0 || n_uds_vars_not_found) && !m_seed_file->file_params()->is_solid_file) {
    char msg[4096];
    sprintf(msg, 
            "Seed file \"%s\" does not contain these variables: ",
            m_seed_file->name());
    ccDOTIMES(i, n_vars_not_found) {
      if (vars_not_found[i] < SRI_VARIABLE_FIRST_NON_PREDEFINED) {
        sprintf(msg + strlen(msg), "%s%s", sri_variable_type_to_long_name(vars_not_found[i]),
                (i == (n_vars_not_found - 1)  && n_uds_vars_not_found==0) ? ". " : ", ");
      } else if (cp_info.is_5g_sim && seed_vih->is_5g_var_id(vars_not_found[i])) {
        //comp0_density, comp1_density, contact_angle
        sSRI_VARIABLE_DESC desc;
        get_5g_var_desc(vars_not_found[i],
                        seed_vih,
                        &desc);
        sprintf(msg + strlen(msg), "%s%s", desc.long_name,
                (i == (n_vars_not_found - 1) && n_uds_vars_not_found==0) ? ". " : ", ");
      }
    }

    if (cp_info.is_uds_transport) {
      ccDOTIMES(j, n_uds_vars_not_found) {
	if (cp_info.cvid_helper->is_uds_var_id(uds_vars_not_found[j])) {
	  //scalar_value per scalar
	  sSRI_VARIABLE_DESC desc;
	  get_uds_var_desc(uds_vars_not_found[j],
			   cp_info.cvid_helper,
			   &desc,
			   FALSE);
	  sprintf(msg + strlen(msg), "%s%s", desc.long_name,
		  j == (n_uds_vars_not_found - 1) ? ". " : ", ");
	}
      }
    }
    
    if (cp_info.is_large_pore_sim) {
      sprintf(msg + strlen(msg), 
	      "Seeded values of these variables will be based on the"
	      " initial conditions specified in the CDI file or the"
	      " default values set in simulator.");

    } else {
      sprintf(msg + strlen(msg), 
	      "Seeded values of these variables will be based on the"
	      " initial conditions specified in the CDI file.");
    }
    msg_warn("%s", msg);
  }

  // For a heat transfer simulation seeded from an isothermal simulation, the
  // initial conditions for temperature are taken from the CDI file. Similarly
  // for a turb modeling simulation seeded from a DNS simulation, the initial
  // conditions for turbulence properties are taken from the CDI file. A
  // warning message is issued below in such situations.  See PR 26570 for
  // further details.
 
  // Compress unused vars out of seed var specs
  ccDOTIMES(i, n_fluid_seed_var_specs()) {
    FLUID_SEED_VAR_SPEC spec = &m_fluid_seed_var_specs[i];

    bool is_var_present[DGF_N_SEED_VARS] = { false };
    ccDOTIMES(v, spec->n_vars)
      is_var_present[spec->seed_var_types[v]] = true;

    asINT32 v1 = 0;

    if (cp_info.is_5g_sim) {
      ccDOTIMES(v2, spec->n_vars) {
        ccDOTIMES(seed_file_index, n_vars) {
          if (spec->seed_var_types[v2] == m_seed_var_types[seed_file_index]) {
            spec->seed_var_types[v1] = spec->seed_var_types[v2];
            v1++;
            break;
          }
        }
      }
    } else {
      ccDOTIMES(v2, spec->n_vars) {
        ccDOTIMES(seed_file_index, n_vars) {
          if (spec->seed_var_types[v2] == m_seed_var_types[seed_file_index]
              && ((spec->seed_var_types[v2] != DGF_SEED_VAR_DENSITY
                   && spec->seed_var_types[v2] != DGF_SEED_VAR_STRESS_TENSOR_MAG)
                  || (spec->seed_var_types[v2] == DGF_SEED_VAR_STRESS_TENSOR_MAG
                      && is_var_present[DGF_SEED_VAR_XVEL] && is_var_present[DGF_SEED_VAR_YVEL] 
                      && (!is_3d || is_var_present[DGF_SEED_VAR_ZVEL]))
                  || (spec->seed_var_types[v2] == DGF_SEED_VAR_DENSITY
                      && m_params.seed_via_dimless_properties
                      && !m_params.seed_sim_was_incompressible
                      && is_var_present[DGF_SEED_VAR_PRESSURE]
                      && (is_var_present[DGF_SEED_VAR_XVEL] || is_var_present[DGF_SEED_VAR_YVEL] 
                          || is_var_present[DGF_SEED_VAR_ZVEL])))) {
            spec->seed_var_types[v1] = spec->seed_var_types[v2];
            v1++;
            break;
          }
        }
      }
    }
    spec->n_vars = v1;

    v1 = 0;
    if (cp_info.is_uds_transport && !cp_info.is_5g_sim) {
      ccDOTIMES(v2, spec->n_uds_vars) {
        ccDOTIMES(seed_file_index, n_uds_vars) {
          if (spec->seed_uds_var_indices[v2] == m_seed_uds_var_indices[seed_file_index]) {	    
            spec->seed_uds_var_indices[v1] = spec->seed_uds_var_indices[v2];	    
            v1++;
            break;
          }
        }
      }
    }
    spec->n_uds_vars = v1;
  }

  m_n_vars = n_vars;
  m_n_uds_vars = n_uds_vars;

  asINT32 n_vars_used = n_vars; //new
  if (cp_info.local_vel_freeze) {//check the order
    n_vars_used--; //remove DYNAMIC_SCALAR_MULTIPLIER because it is not from seed file

    {
      asINT32 den_frozen_index;
      if (is_3d)
        den_frozen_index = m_n_vars - N_FROZEN_VARS;
      else
        den_frozen_index = m_n_vars -(N_FROZEN_VARS - 1);
      if (m_seed_var_types[den_frozen_index] != DGF_SEED_VAR_DENSITY_FROZEN)
        msg_error("Density_frozen (%d) is not the first specially seeded var(%d)!",DGF_SEED_VAR_DENSITY_FROZEN, m_seed_var_types[den_frozen_index] );
      if (m_seed_var_types[m_n_vars-2] != DGF_SEED_VAR_USTAR)
        msg_error("Ustar (%d) is not the second last seeded var(%d)!",DGF_SEED_VAR_USTAR,m_seed_var_types[m_n_vars-2] );
      if (m_seed_var_types[m_n_vars-1] != DGF_SEED_VAR_DYNAMIC_SCALAR_MULTIPLIER)
        msg_error("Flag of coarsen fluid region (%d) is not the last seeded var(%d)!",DGF_SEED_VAR_DYNAMIC_SCALAR_MULTIPLIER, m_seed_var_types[m_n_vars-1]);
    }
  }

  if (!sim_args.use_seed_cache) {
    SRI_STATUS status;
    // load up all the required data from the measurement file
    m_var_table = new sriFLOAT [ (n_vars_used + n_uds_vars) * m_n_points ];
    ccDOTIMES(var, n_vars_used) {
      if (SRI_SUCCESS != (status = m_seed_file->get_variable(m_sri_var_types[var],
                                                             m_var_table + var * m_seed_file->n_points(),
                                                             m_seed_file->n_points(), 0, NULL))) {
        cSTRING long_name;
        if (m_sri_var_types[var] < SRI_VARIABLE_FIRST_NON_PREDEFINED) {
          long_name = m_seed_file->variable_long_name(m_sri_var_types[var]);
        } else if (cp_info.is_5g_sim && seed_vih->is_5g_var_id(m_sri_var_types[var])) {
          //comp0_density, comp1_density, contact_angle
          sSRI_VARIABLE_DESC desc;
          get_5g_var_desc(m_sri_var_types[var],
                          seed_vih,
                          &desc);
          long_name = desc.long_name;
        }
        msg_error("Cannot read %s from seed file \"%s\": %s",
                  long_name,
                  m_seed_file->name(),
                  m_seed_file->error_string(status));
      }
      if (cp_info.is_uds_transport && !cp_info.is_5g_sim) {
	ccDOTIMES(var, n_uds_vars) {
	  if (SRI_SUCCESS != (status = m_seed_file->get_variable(m_sri_uds_var_types[var],
								 m_var_table + (var + n_vars_used) * m_seed_file->n_points(),
								 m_seed_file->n_points(), 0, NULL))) {
	    cSTRING long_name;
	    //if (seed_vih->is_uds_var_id(m_sri_uds_var_types[var]))
	    {
	      //scalar_value per scalar
	      sSRI_VARIABLE_DESC desc;
	      get_uds_var_desc(m_sri_uds_var_types[var],
			       seed_vih,
			       &desc,
			       FALSE);
	      long_name = desc.long_name;
	      msg_error("Cannot read %s from seed file \"%s\": %s",
			long_name,
			m_seed_file->name(),
			m_seed_file->error_string(status));
	    }
	  }
	}
      }
    }
    
    if (m_seed_file->file_params()->n_lrfs > 0) {
      m_ref_frame_index_table = new sriLRF_INDEX [ m_n_points ];
      if (SRI_SUCCESS != (status = m_seed_file->read_ref_frame_indices( 
                                             m_ref_frame_index_table +  m_seed_file->n_points(), 
                                             m_seed_file->n_points(), 0))) {
        msg_error("Failed to read page of seed data from seed file \"%s\": %s", 
                  m_seed_file->name(), m_seed_file->error_string(status));
      }
    } else {
      m_ref_frame_index_table = NULL;
    }
  } else { // allocate a seed cache to hold N_CELLS_IN_PAGE number of variables 
    m_var_table = NULL;
    m_ref_frame_index_table = NULL;
    initialize_seed_data_cache();
  }

  if (cp_info.do_split_seed)
    initialize_seed_norm_data_cache();

  if (m_params.seed_via_mks) {
    if (m_realm == STP_FLOW_REALM) {
      msg_print("Seeding from file \"%s\" with %ld data points in MKS units.", 
              m_seed_file->name(), m_seed_file->n_points());
    } else { // STP_COND_REALM
      msg_print("Seeding solid domain from file \"%s\" with %ld data points in MKS units.", 
              m_seed_file->name(), m_seed_file->n_points());
    }
  } else {
    if (m_realm == STP_FLOW_REALM) {
      msg_print("Seeding from file \"%s\" with %ld data points.", 
              m_seed_file->name(), m_seed_file->n_points());
    } else { // STP_COND_REALM
      msg_print("Seeding solid domain from file \"%s\" with %ld data points.", 
              m_seed_file->name(), m_seed_file->n_points());
    }
  }

  {
    cJC_PACKAGE seedPackage("Seeding", cJC_PACKAGE::SIM);
    seedPackage.DefineProperty("Seed File", m_seed_file->name());
    seedPackage.DefineProperty("Points", (int) m_seed_file->n_points());
    seedPackage.Emit();
  }

  m_char_vel_ratio   = 1.0;
  m_char_vel_ratio_2 = 1.0;
  m_char_vel_ratio_3 = 1.0;
  if (cp_info.is_turb && (m_params.coord_scale != 1.0 || (m_params.char_vel != m_params.char_vel_seed))) {
    m_do_turb_dimless_seeding = TRUE;
    if (m_params.char_vel != m_params.char_vel_seed) {
      m_char_vel_ratio = m_params.char_vel / m_params.char_vel_seed;
      m_char_vel_ratio_2 = m_char_vel_ratio * m_char_vel_ratio;
      m_char_vel_ratio_3 = m_char_vel_ratio_2 * m_char_vel_ratio;
    }
  } else {
    m_do_turb_dimless_seeding = FALSE;
  }

}

VOID cSEED_CONTROL::send_header_to_sps() 
{
  cDGF_SEED_INFO seed_info;
  seed_info.tag.id = DGF_SEED_INFO_TAG;
  seed_info.tag.length = lgi_pad_and_encode_record_length(sizeof(cDGF_SEED_INFO));
  seed_info.n_seed_controllers = m_seed_controllers.size();
  write_header_to_all_sps(seed_info);

  for (auto seed_controller : m_seed_controllers) {
    cDGF_SMART_SEED_CONTROL header;
    header.tag.id = DGF_SMART_SEED_CONTROL_TAG;
    asINT32 length = sizeof(header) + seed_controller->n_vars() + seed_controller->n_uds_vars();
    ccDOTIMES(i, seed_controller->n_fluid_seed_var_specs()) {
      FLUID_SEED_VAR_SPEC spec = &seed_controller->fluid_seed_var_specs()[i];
      length += spec->n_vars + 1; // one byte for each var + 1 byte for the var count
      length += spec->n_uds_vars + 1; // one byte for each uds_var + 1 byte for the uds_var count
    }
    ccDOTIMES(i, seed_controller->n_boundary_seed_var_specs()) {
      BOUNDARY_SEED_VAR_SPEC spec = &seed_controller->boundary_seed_var_specs()[i];
      length += spec->n_vars + 1; // one byte for each var + 1 byte for the var count
      length += spec->n_uds_vars + 1; // one byte for each uds_var + 1 byte for the uds_var count
    }
    header.tag.length = lgi_pad_and_encode_record_length(length);
    header.n_variables = seed_controller->n_vars();
    header.n_uds_variables = seed_controller->n_uds_vars();
    if (seed_controller->seed_params().seed_via_dimless_properties)
      header.seed_scaling_type = DGF_DIMLESS_SCALING;
    else if (seed_controller->seed_params().seed_via_mks)
      header.seed_scaling_type = DGF_MKS_SCALING;
    else
      header.seed_scaling_type = DGF_NO_SCALING;
    header.seed_data_mks_vel_scaling = seed_controller->seed_params().mks_vel_scaling;
    header.seed_sim_was_incompressible = seed_controller->seed_params().seed_sim_was_incompressible;
    header.seed_data_char_density = seed_controller->seed_params().char_density_seed;
    header.seed_data_char_vel = seed_controller->seed_params().char_vel_seed;
    header.seed_data_char_temp = seed_controller->seed_params().char_temp_seed;
    header.seed_data_lattice_gas_const = seed_controller->seed_params().lattice_gas_const_seed;
    header.n_fluid_seed_var_specs = seed_controller->n_fluid_seed_var_specs();
    header.n_boundary_seed_var_specs = seed_controller->n_boundary_seed_var_specs();
    // Rotate the seed velocity by a specified angle about the x,y or z axis of any coordinate system
    if (seed_controller->seed_params().is_seed_rotate_vel) {
      // csys
      header.seed_rotate_vel_csys_index = 0;
      cBOOLEAN found_csys = FALSE;
      ccDOTIMES(nth_csys, cp_info.all_csys->num_coord_systems) {
        if (strcmp(cp_info.all_csys->coord_systems[nth_csys].name, seed_controller->seed_params().seed_rotate_vel_csys_name) == 0) {
          header.seed_rotate_vel_csys_index = nth_csys;
          found_csys = TRUE;
        }
      }
      if (!found_csys) {
        msg_error("Could not find a coordinate system with the name \"%s\". Please provide a valid argument to the option --seed_rotate_vel.", seed_controller->seed_params().seed_rotate_vel_csys_name);
      }
      // axis
      header.seed_rotate_vel_axis = seed_controller->seed_params().seed_rotate_vel_axis;
      // angle
      header.seed_rotate_vel_angle = seed_controller->seed_params().seed_rotate_vel_angle;
    }
    else {
      header.seed_rotate_vel_csys_index = 0;
      header.seed_rotate_vel_axis  = 0;
      header.seed_rotate_vel_angle = 0.0;
    }

    write_header_to_all_sps(header);

    ccDOTIMES(i, header.n_variables) {
      uINT8 var_type = seed_controller->seed_var_types(i); // create method to get this
      write_to_all_sps(var_type);
    }

    //UDS
    ccDOTIMES(j, seed_controller->n_uds_vars()) {
      uINT8 var_type = seed_controller->seed_uds_var_indices(j);
      write_to_all_sps(var_type);
    }
 

    ccDOTIMES(i, seed_controller->n_fluid_seed_var_specs()) {
      FLUID_SEED_VAR_SPEC spec = &seed_controller->fluid_seed_var_specs()[i];

      uINT8 n_vars = spec->n_vars;
      write_to_all_sps(n_vars);

      uINT8 n_uds_vars = spec->n_uds_vars;
      write_to_all_sps(n_uds_vars);

      ccDOTIMES(v, spec->n_vars) {
        uINT8 var_type = spec->seed_var_types[v];
        write_to_all_sps(var_type);
      }
      ccDOTIMES(k, n_uds_vars) {
        uINT8 var_type = spec->seed_uds_var_indices[k];
        write_to_all_sps(var_type);
      }
    }

    ccDOTIMES(i, seed_controller->n_boundary_seed_var_specs()) {
      BOUNDARY_SEED_VAR_SPEC spec = &seed_controller->boundary_seed_var_specs()[i];

      uINT8 n_vars = spec->n_vars;
      write_to_all_sps(n_vars);
      uINT8 n_uds_vars = spec->n_uds_vars;
      write_to_all_sps(n_uds_vars);

      ccDOTIMES(v, spec->n_vars) {
        uINT8 var_type = spec->seed_var_types[v];
        write_to_all_sps(var_type);
      }
      ccDOTIMES(k, n_uds_vars) {
        uINT8 var_type = spec->seed_uds_var_indices[k];
        write_to_all_sps(var_type);
      }
    }
  }
}

BOOLEAN cSMART_SEED_CONTROL::is_location_in_this_seed_file(cDGF_UBLK_BASE_DESC *ublk_desc, auINT32 fluid_like_voxel_mask)
{

  sINT32 grid_voxel_scale = sim_scale_to_sri_scale(ublk_desc->b.voxel_scale); 
  asINT32 voxel_inc = voxel_loop_increment(cp_info.n_dims);
  sFLOAT voxel_size = sFLOAT(1 << grid_voxel_scale);
  STP_COORD *ublk_location = ublk_desc->b.location;
  asINT32 z_max = cp_info.n_dims - 1;

  asINT32 voxel = 0; //voxel index in the ublk
  ccDOTIMES(dx, 2) {
    ccDOTIMES(dy, 2) {
      ccDOTIMES(dz, z_max) {
        if ((fluid_like_voxel_mask >> voxel) & 1) {
          csPOINT voxel_min_point(ublk_location[0]+dx*voxel_size, 
                                  ublk_location[1]+dy*voxel_size, 
                                  ublk_location[2]+dz*voxel_size);
          csPOINT voxel_max_point = voxel_min_point + voxel_size;
          csPOINT grid_min_point = voxel_min_point * m_params.coord_scale + m_params.origin_shift;
          csPOINT grid_max_point = voxel_max_point * m_params.coord_scale + m_params.origin_shift;
          csBOX voxel_grid_box(grid_min_point, grid_max_point);

          m_grid->TraverseInit(voxel_grid_box);
          sINT8 seed_voxel;
          sFLOAT common_volume;
          if (m_grid->Traverse(seed_voxel, common_volume) != NULL) {
            //while (seed_controller->get_grid()->Traverse(seed_voxel, common_volume) != NULL);
            return TRUE;
          }
        } // fluid_like_voxel_mask
        voxel += voxel_inc;
      } // dz
    } // dy
  } // dx

  return FALSE;
}

VOID cSMART_SEED_CONTROL::wrapup()
{
  if (!this->is_smart_seed())
    return;

  m_seed_file->close_file(TRUE);

  delete m_grid;
  m_grid = NULL;
  if (m_var_table == NULL) {
    vmem_free(m_seed_data_cache.var_pages);
    vmem_free(m_seed_data_cache.page_table);
    m_seed_data_cache.var_pages = NULL;
    m_seed_data_cache.page_table = NULL;
  } else {
    delete [] m_var_table;
    m_var_table = NULL;
  }

  if (cp_info.do_split_seed) {
    vmem_free(m_seed_norm_data_cache.var_pages);
    vmem_free(m_seed_norm_data_cache.page_table);
    m_seed_norm_data_cache.var_pages = NULL;
    m_seed_norm_data_cache.page_table = NULL;
  }

  if (m_seed_file->file_params()->n_lrfs > 0) {
    if (m_ref_frame_index_table == NULL) {
      vmem_free(m_seed_data_cache.ref_frame_index_pages);
      m_seed_data_cache.ref_frame_index_pages = NULL;
    } else {
      delete [] m_ref_frame_index_table;
      m_ref_frame_index_table = NULL;
    }
  }
  if (cp_info.n_lrfs() > 0) 
    m_params.seed_lrf_index_from_ublk_lrf_index.clear();

  msg_print("Seeding completed");

  sINT64 total_voxels;
  if (m_realm == STP_FLOW_REALM) {
    total_voxels = cp_info.control_rec.num_flow_voxels;
  } else { // STP_COND_REALM
    total_voxels = cp_info.control_rec.num_cond_voxels;
  }
  asINT32 percent_seeded = 0.5 + (100.0 * (dFLOAT)m_n_voxels_seeded / total_voxels);
  asINT32 percent_frozen = 0.5 + (100.0 * (dFLOAT)m_n_voxels_frozen / total_voxels);
  if ((percent_seeded == 100) && (m_n_voxels_seeded < total_voxels))
    percent_seeded = 99;
  if ((percent_seeded == 0) && (m_n_voxels_seeded > 0))
    percent_seeded = 1;

  if (m_n_voxels_seeded == total_voxels) {
    if (m_realm == STP_FLOW_REALM) {
      msg_print("%d%% of fluid voxels (%ld of %ld) seeded from fluid seed file.",
                percent_seeded, m_n_voxels_seeded, total_voxels);
    } else { // STP_COND_REALM
      msg_print("%d%% of solid voxels (%ld of %ld) seeded from solid seed file.",
                percent_seeded, m_n_voxels_seeded, total_voxels);
    }
  } else {
    if (m_realm == STP_FLOW_REALM) {
      msg_print("%d%% of fluid voxels (%ld of %ld) seeded from fluid seed file. Seed data for the remaining"
                " fluid voxels will be %s.",
                percent_seeded, m_n_voxels_seeded, total_voxels,
                (m_do_extrapolate 
                 ? "extrapolated from surrounding fluid voxels"
                 : "based on case fluid initial conditions"));
    } else { // STP_COND_REALM
      msg_print("%d%% of solid voxels (%ld of %ld) seeded from solid seed file. Seed data for the remaining"
                " solid voxels will be %s.",
                percent_seeded, m_n_voxels_seeded, total_voxels,
                (m_do_extrapolate 
                 ? "extrapolated from surrounding solid voxels"
                 : "based on case solid initial conditions"));
    }
  }

  if (m_n_voxels_seeded_with_junk > 0) {
    dFLOAT float_percent_junk = 100.0 * (dFLOAT)m_n_voxels_seeded_with_junk / total_voxels;
    asINT32 percent_junk = 0.5 + float_percent_junk;

    // Only print 100% or 0% if exactly 100% or 0%.
    if ((percent_junk == 100) && (m_n_voxels_seeded_with_junk < total_voxels))
      percent_junk = 99;
    if ((percent_junk == 0) && (m_n_voxels_seeded_with_junk > 0))
      percent_junk = 1;

    if (float_percent_junk > 5) {
      msg_error("%d%% of voxels (%ld of %ld) contained invalid seed information derived from seed file.",
                percent_junk,
                m_n_voxels_seeded_with_junk,
                total_voxels);
    } else {
      msg_warn("%ld voxels (of %ld) contained invalid seed information derived from seed file."
               " Seed data for these voxels will be %s.",
               m_n_voxels_seeded_with_junk,
               total_voxels,
               (m_do_extrapolate 
                ? "extrapolated from surrounding voxels"
                : "based on case initial conditions"));
    }
  }
  if (cp_info.local_vel_freeze) {
    msg_print("%d%% of voxels (%ld of %ld) have velocities frozen.",
              percent_frozen, m_n_voxels_frozen, total_voxels);
    if (percent_frozen > 95)
      msg_error("The percentage of frozen voxels exceeds the maximal value 95%%.");
  }
}

VOID cSMART_SEED_CONTROL::rotate_seed_vars_to_local_csys(sriFLOAT seed_vars[][N_VOXELS_8], 
                                                         asINT32 v, sriLRF_INDEX lrf_index) {

  if (m_seed_file->file_params()->lrfs[lrf_index].type == SRI_LRF_STATIONARY) 
    return;

  sriFLOAT  rotated_vars[3];
  sriDOUBLE rotation_matrix[3][3];
  m_seed_file->lrf_global_to_local(rotation_matrix, lrf_index);

  rotated_vars[0] =  rotation_matrix[0][0] * seed_vars[DGF_SEED_VAR_XVEL][v] +
                     rotation_matrix[0][1] * seed_vars[DGF_SEED_VAR_YVEL][v];
  rotated_vars[1] =  rotation_matrix[1][0] * seed_vars[DGF_SEED_VAR_XVEL][v] +
                     rotation_matrix[1][1] * seed_vars[DGF_SEED_VAR_YVEL][v];

  if (cp_info.n_dims == 3) {
    rotated_vars[0] +=  rotation_matrix[0][2] * seed_vars[DGF_SEED_VAR_ZVEL][v];
    rotated_vars[1] +=  rotation_matrix[1][2] * seed_vars[DGF_SEED_VAR_ZVEL][v];
    rotated_vars[2]  =  rotation_matrix[2][0] * seed_vars[DGF_SEED_VAR_XVEL][v] +
                        rotation_matrix[2][1] * seed_vars[DGF_SEED_VAR_YVEL][v] +
                        rotation_matrix[2][2] * seed_vars[DGF_SEED_VAR_ZVEL][v];
    seed_vars[DGF_SEED_VAR_ZVEL][v] = rotated_vars[2];
  }
  seed_vars[DGF_SEED_VAR_XVEL][v] = rotated_vars[0];
  seed_vars[DGF_SEED_VAR_YVEL][v] = rotated_vars[1];
  return;
}

VOID cSMART_SEED_CONTROL::rotate_seed_vars_to_global_csys(sriFLOAT seed_vars[][N_VOXELS_8], 
                                                          asINT32 v, sriLRF_INDEX lrf_index) {

  if (m_seed_file->file_params()->lrfs[lrf_index].type == SRI_LRF_STATIONARY) 
    return;

  sriFLOAT  rotated_vars[3];
  sriDOUBLE rotation_matrix[3][3];
  m_seed_file->lrf_global_to_local(rotation_matrix, lrf_index);

  rotated_vars[0] =  rotation_matrix[0][0] * seed_vars[DGF_SEED_VAR_XVEL][v] +
                     rotation_matrix[1][0] * seed_vars[DGF_SEED_VAR_YVEL][v];
  rotated_vars[1] =  rotation_matrix[0][1] * seed_vars[DGF_SEED_VAR_XVEL][v] +
                     rotation_matrix[1][1] * seed_vars[DGF_SEED_VAR_YVEL][v];

  if (cp_info.n_dims == 3) {
    rotated_vars[0] +=  rotation_matrix[2][0] * seed_vars[DGF_SEED_VAR_ZVEL][v];
    rotated_vars[1] +=  rotation_matrix[2][1] * seed_vars[DGF_SEED_VAR_ZVEL][v];
    rotated_vars[2]  =  rotation_matrix[0][2] * seed_vars[DGF_SEED_VAR_XVEL][v] +
                        rotation_matrix[1][2] * seed_vars[DGF_SEED_VAR_YVEL][v] +
                        rotation_matrix[2][2] * seed_vars[DGF_SEED_VAR_ZVEL][v];
    seed_vars[DGF_SEED_VAR_ZVEL][v] = rotated_vars[2];
  }
  seed_vars[DGF_SEED_VAR_XVEL][v] = rotated_vars[0];
  seed_vars[DGF_SEED_VAR_YVEL][v] = rotated_vars[1];
  return;
}
#undef rotation_matrix 
#undef DEN_THRESHOLD
#undef N_FROZEN_VARS

VOID cSMART_SEED_CONTROL::print(std::ostream &stream) const {
  //cSMART_SEED_CONTROL_BASE::print(stream);

  int var_spec_i = 0;
  for (auto &seed_var_spec : m_fluid_seed_var_specs) {
    stream << "SEED_VAR_SPEC: " << var_spec_i << std::endl;
    seed_var_spec.print(stream);
    var_spec_i++;
  }
  stream << "cSMART_SEED_CONTROL: " << std::endl;
  stream << "m_seed_filename: " << m_seed_filename << std::endl;
  stream << "m_seed_file: " << m_seed_file << std::endl;
  stream << "m_realm: " << (int)m_realm << ": " << (m_realm == STP_FLOW_REALM ? "STP_FLOW_REALM" : "STP_COND_REALM") << std::endl;
  stream << "m_seed_frame: " << m_seed_frame << std::endl;
  stream << "m_n_vars: " << m_n_vars << std::endl;
  stream << "m_sri_var_types: " << std::endl;
  for (int i = 0; i < m_n_vars; i++) {
    stream << m_sri_var_types[i] << std::endl;
  }
  stream << "m_seed_var_types: " << std::endl;
  for (int i = 0; i < m_n_vars; i++) {
    stream << m_seed_var_types[i] << std::endl;
  }
  stream << "m_n_voxels_seeded: " << m_n_voxels_seeded << std::endl;
  stream << "m_n_voxels_seeded_with_junk: " << m_n_voxels_seeded_with_junk << std::endl;
  stream << "m_n_voxels_frozen: " << m_n_voxels_frozen << std::endl;
  stream << "m_n_nw_norm_meas_cells: " << m_n_nw_norm_meas_cells << std::endl;
  stream << m_params;
}


template VOID cSEED_CONTROL::parse_seed_var_specs<DGF_BOUNDARY_SEED_VAR_TYPE, DGF_N_BOUNDARY_SEED_VARS, FALSE>
(bool do_smart_seed, 
 int *argc, 
 char *argv[], 
 cSTRING include_cmdline_arg, 
 cSTRING exclude_cmdline_arg, 
 BOOLEAN is_volumetric_region,
 std::map <std::string, DGF_BOUNDARY_SEED_VAR_TYPE > &seed_var_string_to_seed_var_type, 
 DGF_BOUNDARY_SEED_VAR_TYPE default_seed_vars[],
 asINT32 n_default_seed_vars, 
 DGF_BOUNDARY_SEED_VAR_TYPE default_solid_seed_vars[],
 asINT32 n_default_solid_seed_vars, 
 asINT32 n_extra_inclusion_seed_vars, 
 cSTRING purpose);

template VOID cSEED_CONTROL::parse_seed_var_specs<DGF_SEED_VAR_TYPE, DGF_N_SEED_VARS, TRUE>
(bool do_smart_seed, 
 int *argc, 
 char *argv[], 
 cSTRING include_cmdline_arg, 
 cSTRING exclude_cmdline_arg, 
 BOOLEAN is_volumetric_region,
 std::map <std::string, DGF_SEED_VAR_TYPE > &seed_var_string_to_seed_var_type, 
 DGF_SEED_VAR_TYPE default_seed_vars[],
 asINT32 n_default_seed_vars, 
 DGF_SEED_VAR_TYPE default_solid_seed_vars[],
 asINT32 n_default_solid_seed_vars, 
 asINT32 n_extra_inclusion_seed_vars, 
 cSTRING purpose);
