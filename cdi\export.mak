# ~~~COPYWRITE~~~+ boxcomment("copyright.text", "78")
##############################################################################
### Case Constructor: CAD System Dependent Modules (CSDM) for Pro/E        ###
### Original author: <PERSON>                                        ###
###                                                                        ###
### Unpublished Work Copyright (C) 1994 Exa Corporation.                   ###
### All Rights Reserved.                                                   ###
###                                                                        ###
### This computer program is the property of Exa Corporation and contains  ###
### its confidential trade secrets.  Use, examination, copying, transfer   ###
### and disclosure to others, in whole or in part, are prohibited except   ###
### with the express prior written consent of Exa Corporation.             ###
###                                                                        ###
### RESTRICTED RIGHTS LEGEND                                               ###
###                                                                        ###
### Use, duplication, or disclosure is subject to restrictions stated in   ###
### Contract No. DABT63-93-C-0010 with Exa Corporation.                    ###
##############################################################################
# ~~~COPYWRITE~~~- boxcomment("copyright.text", "78")

# Determine CDI target to use with $(T)
CDI_T=$(shell $(MAKEINCLUDE_DIR)/build_target_for_cpp_lib $(CDI_DIR) $(T))

CDI_H=$(CDI_DIR)/cdi_export.h
CDI_FIX_PARALLEL_DEV_H=$(CDI_DIR)/cdi_fix_parallel_dev.h

CDI_D=-DCDI_H=\"$(CDI_H)\"
CDI_FIX_PARALLEL_DEV_D=-DCDI_FIX_PARALLEL_DEV_H=\"$(CDI_FIX_PARALLEL_DEV_H)\"

CDI_A=$(CDI_DIR)/$(CDI_T)/$(call libname,cdi)


