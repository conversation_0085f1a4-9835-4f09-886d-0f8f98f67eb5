/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Job Control support
 *
 * Jim Salem, Exa Corporation 
 * Created Mon Jun 20 1994
 *--------------------------------------------------------------------------*/

#ifndef __JOBCTL_H
#define __JOBCTL_H

#include "common.h"
#include "jobctl_status.h"


typedef struct sASYNC_EVENT_PKG {
  sASYNC_EVENT_MSG msg;
  cBOOLEAN         user_initiated;
  cBOOLEAN         coarsest_time;
} sASYNC_EVENT_PKG, *ASYNC_EVENT_PKG;


/* Functions */

/* Initializes the job control system on the CP
 * If NULL, jobctl_status_filename defaults to the value of the environment
 * variable EXAJOBCTL_STATUS_FILE or failing that to ".exa_jobctl_status"
 * in the current directory.
 * Writes out the initial status (i.e. "Initializing")
 */
VOID cp_jobctl_initialize(cSTRING jobctl_status_filename, 
			  BOOLEAN write_jobctl_file_p);

VOID cp_handle_signal_cmds(BOOLEAN defer_processing_p);
VOID cp_finish_signal_cmd(BOOLEAN user_initiated, cSTRING cmdptr);

/* Recomputes the new CP status based on the SP status */
VOID force_cp_status_to_time_zero();
VOID cp_recompute_status(VOID);
void cp_jobctl_report_coupling_phase_timer(bool sim_is_done = false);
void update_rp_status();
void update_sp_radiation_wait_time();
VOID initialize_status_tree();

/* Terminates immediately */
VOID cp_jobctl_terminate(cSTRING message, asINT32 exit_code, BOOLEAN print_status = TRUE);

VOID handle_pending_async_event_request(VOID);

VOID queue_checkpoint_request(asINT32 timestep, BOOLEAN full_checkpoint);

BOOLEAN cp_issue_signal_cmd(cSTRING cmd);

asINT32 exit_failure_code(VOID);
BOOLEAN register_async_event_request(EVENT_ID id, dFLOAT arg, TIMESTEP timestep,
                                  BOOLEAN user_initiated, BOOLEAN coarsest_time); 

VOID maybe_terminate_simulation();
#if SEPARATE_LICENSE_PROCESS
VOID cp_wait_on_license_process();
#endif


// Class to manage measuring various statistics about the execution of the thermal solver coupling phases.
class cCOUPLING_PHASES_STATS {

private:

  // Define the set of measurements recorded for a given phase.
  struct sPHASE_STATS {
    TIMESTEP m_num_flowsteps;
    TIMESTEP m_num_condsteps;
    TIMESTEP m_num_basesteps;
    double m_wallclock_duration;
    double m_flow_physical_duration;
    double m_cond_physical_duration;
    
    sPHASE_STATS() :
      m_num_flowsteps(0),
      m_num_condsteps(0),
      m_num_basesteps(0),
      m_wallclock_duration(0),
      m_flow_physical_duration(0),
      m_cond_physical_duration(0)
    {}
    sPHASE_STATS(
                 TIMESTEP flowsteps,
                 TIMESTEP condsteps,
                 TIMESTEP basesteps,
                 double wallclock_duration,
                 double flow_physical_duration,
                 double cond_physical_duration) :
      m_num_flowsteps(flowsteps),
      m_num_condsteps(condsteps),
      m_num_basesteps(basesteps),
      m_wallclock_duration(wallclock_duration),
      m_flow_physical_duration(flow_physical_duration),
      m_cond_physical_duration(cond_physical_duration)
    {}

    // Define how to aggregate the stats from two phases (usually of
    // the same type).
    sPHASE_STATS& operator += (sPHASE_STATS &other) {
      m_num_flowsteps += other.m_num_flowsteps;
      m_num_condsteps += other.m_num_condsteps;
      m_num_basesteps += other.m_num_basesteps;
      m_wallclock_duration += other.m_wallclock_duration;
      m_flow_physical_duration += other.m_flow_physical_duration;
      m_cond_physical_duration += other.m_cond_physical_duration;
      return *this;
    }
  };
  
  struct sPHASE_SIGNATURE {
    eTIME_COUPLING_SCHEME::Enum m_scheme;
    eCOUPLED_SOLVER::Enum m_frozen_solver;
  sPHASE_SIGNATURE(eTIME_COUPLING_SCHEME::Enum scheme, eCOUPLED_SOLVER::Enum frozen_solver) : m_scheme(scheme), m_frozen_solver(frozen_solver) {}
    bool operator< (const sPHASE_SIGNATURE &other) const {
      // Return true if *this comes before other.
      if(m_scheme < other.m_scheme)
        return true;
      if(m_scheme == other.m_scheme)
        if(m_frozen_solver < other.m_frozen_solver)
          return true;
      return false;
    }
    std::string description() const {return eTIME_COUPLING_SCHEME::GetName(m_scheme, m_frozen_solver);}
  };

  // We're not using an unordered map here on purpose because we need
  // to be able to itterate through the keys in a consistent order
  // defined above in the < operator of the signature.
  std::map<sPHASE_SIGNATURE, sPHASE_STATS>  m_aggregated_phase_stats;
  
  WALLCLOCK_TIME m_phase_start_time_usecs;
  int m_next_phase;
  
public:
  
  cCOUPLING_PHASES_STATS() :
    m_aggregated_phase_stats({}),
    m_phase_start_time_usecs(wallclock_time()),
    m_next_phase(1) {}
  
  // Method that checks if any new phases have been completed according to the CP's notion of the current simulation timestep.
  void sample(bool sim_is_done = false);

  //Print the aggregated timing info for each type of coupling phase.
  void print();
  
};


#endif /* __JOBCTL_H */

