/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#ifndef _WRITE_LGI_CP_H_
#define _WRITE_LGI_CP_H_

#include CDI_H
#include LGI_H

#include <string>
#include <cstring> //for memset
#include <vector>
#include "cp_info.h"

//Forward decleration of structs (defined in cdi_readwrite) used as constructor arguments in the below emitter configuration and nozzle record writers.
struct sCDI_EMITTER_CONFIG_BASE;
struct sCDI_NOZZLE_EMITTER_CONFIG;
struct sCDI_RAIN_EMITTER_CONFIG;
struct sCDI_TIRE_EMITTER_CONFIG;
struct sCDI_TIRE_NOZZLE_PROPS;

typedef class sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE : public sLGI_PARTICLE_EMITTER_CONFIGURATION_REC
{
 protected:
  size_t m_num_total_string_bytes;
 public:

  asINT32 m_cdi_major_version;
  asINT32 m_cdi_minor_version;

  sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE(
                                                  sCDI_DATA& cdi_data) {
    memset(this, 0 , sizeof(sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE));
    m_cdi_major_version = cdi_data.major_version;
    m_cdi_minor_version = cdi_data.minor_version;
  }

  sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE(
                                                  sCDI_DATA& cdi_data,
                                                  sCDI_EMITTER_CONFIG_BASE* cdi_emitter_config);

}*LGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER_BASE;

typedef class sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER : sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE 
{
  
 public:
  
 sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER(sCDI_DATA& cdi_data) : 
  sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE(
                                                  cdi_data) {}

  //Declare some constructors that utilize the CDI data structs. 
  sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER(
                                                 sCDI_DATA& cdi_data,
                                                 sCDI_NOZZLE_EMITTER_CONFIG* cdi_nozzle_config);
  sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER(
                                                 sCDI_DATA& cdi_data,
                                                 sCDI_RAIN_EMITTER_CONFIG* cdi_rain_config);
  sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER(
                                                 sCDI_DATA& cdi_data,
                                                 sCDI_TIRE_EMITTER_CONFIG* cdi_tire_config);

  //Declare some methods that can write the data and required strings to the LGI stream
  VOID write_base(sCDI_EMITTER_CONFIG_BASE* config); //write the data for this struct (plus the strings) which are common to all config types
  VOID write(sCDI_NOZZLE_EMITTER_CONFIG* config);   //Write the data for this record plus the strings needed for a nozzle emitter configuration to the LGI stream
  VOID write(sCDI_RAIN_EMITTER_CONFIG* config);   //Write the data for this record plus the strings needed for a rain emitter configuration to the LGI stream.
  VOID write(sCDI_TIRE_EMITTER_CONFIG* config); //Write the data for this record data plus the strings needed for a tire emitter configuration to the LGI stream.

  size_t num_total_bytes() {
    return(m_num_total_string_bytes + sizeof(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC));
  }

}*LGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER;
#if 0
typedef class sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE : public sLGI_PARTICLE_EMITTER_CONFIGURATION_REC
{
 protected:
  size_t m_num_total_string_bytes;
 public:
  sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE()
    {
      memset(this, 0 , sizeof(sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE));
    }
  sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE(sCDI_EMITTER_CONFIG_BASE* cdi_emitter_config);
}*LGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER_BASE;

typedef class sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER : sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE 
{
  
 public:
  
  sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER()
    {
      memset(this, 0 , sizeof(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER));
    }

  //Declare some constructors that utilize the CDI data structs. lgi_record.cc was created to define these.
  sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER(sCDI_NOZZLE_EMITTER_CONFIG* cdi_nozzle_config);
  sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER(sCDI_RAIN_EMITTER_CONFIG* cdi_rain_config);
  sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER(sCDI_TIRE_EMITTER_CONFIG* cdi_tire_config);

  //Declare some methods that can write the data and required strings to the LGI stream
  VOID write_base(sCDI_EMITTER_CONFIG_BASE* config); //write the data for this struct (plus the strings) which are common to all config types
  VOID write(sCDI_NOZZLE_EMITTER_CONFIG* config);   //Write the data for this record plus the strings needed for a nozzle emitter configuration to the LGI stream
  VOID write(sCDI_RAIN_EMITTER_CONFIG* config);   //Write the data for this record plus the strings needed for a rain emitter configuration to the LGI stream.
  VOID write(sCDI_TIRE_EMITTER_CONFIG* config); //Write the data for this record data plus the strings needed for a tire emitter configuration to the LGI stream.

  size_t num_total_bytes() {
    return(m_num_total_string_bytes + sizeof(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC));
  }

}*LGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER;
#endif

typedef class sLGI_NOZZLE_CONFIGURATION_WRITER_BASE : public sLGI_NOZZLE_CONFIGURATION_REC
{
 protected: 
  size_t m_num_total_string_bytes;
 public:

  sLGI_NOZZLE_CONFIGURATION_WRITER_BASE() {
    memset(this, 0 , sizeof(sLGI_NOZZLE_CONFIGURATION_WRITER_BASE));
  }
  
  sLGI_NOZZLE_CONFIGURATION_WRITER_BASE(sCDI_EMITTER_CONFIG_BASE* cdi_config);

}* LGI_NOZZLE_CONFIGURATION_WRITER_BASE;


typedef class sLGI_NOZZLE_CONFIGURATION_REC_WRITER : sLGI_NOZZLE_CONFIGURATION_WRITER_BASE
{

 public:
  
  sLGI_NOZZLE_CONFIGURATION_REC_WRITER()
  {
    memset(this, 0 , sizeof(sLGI_NOZZLE_CONFIGURATION_REC_WRITER));
  }
  //Declare some constructors taking data from various types of CDI emitter configuration data
  sLGI_NOZZLE_CONFIGURATION_REC_WRITER( sCDI_TIRE_NOZZLE_PROPS *cdi_tire_nozzle_props);
  sLGI_NOZZLE_CONFIGURATION_REC_WRITER( sCDI_RAIN_EMITTER_CONFIG *cdi_rain_config);
  sLGI_NOZZLE_CONFIGURATION_REC_WRITER( sCDI_NOZZLE_EMITTER_CONFIG* cdi_nozzle_config);

  //Declare some methods to write this records data to the LGI stream along with the necessecary string characters.
  VOID write(sCDI_TIRE_EMITTER_CONFIG* config, sCDI_TIRE_NOZZLE_PROPS* nozzle_config); //Write nozzle data for a tire emitter configuration;
  VOID write(sCDI_NOZZLE_EMITTER_CONFIG* nozzle_config);  //Write nozzle data for a nozzle emitter configuration (this will write different strings depending on the nozzle type).
  VOID write(sCDI_RAIN_EMITTER_CONFIG* config); //Write nozzle data for a rain emitter configuration.

  size_t num_total_bytes() {
    return(m_num_total_string_bytes + sizeof(sLGI_NOZZLE_CONFIGURATION_REC));
  }

}*LGI_NOZZLE_CONFIGURATION_REC_WRITER;


typedef class sLGI_EMITTER_WRITER_BASE
{
 protected:
  size_t m_num_total_dynamic_bytes; //count for variable length strings and varying number of cylinders and boxes
 public:
  sLGI_EMITTER_WRITER_BASE() {
    m_num_total_dynamic_bytes = 0;
  }
}* LGI_EMITTER_WRITER_BASE;

typedef class sLGI_SURFACE_EMITTER_REC_WRITER : public sLGI_SURFACE_EMITTER_REC , sLGI_EMITTER_WRITER_BASE
{
 public:
  sLGI_SURFACE_EMITTER_REC_WRITER(const sCDI_SURFACE_EMITTER* cdi_emitter);
  VOID write(const sCDI_SURFACE_EMITTER* cdi_emitter);
  size_t num_total_bytes() {
    return(m_num_total_dynamic_bytes + sizeof(sLGI_SURFACE_EMITTER_REC));
  }
}*LGI_SURFACE_EMITTER_REC_WRITER;

typedef class sLGI_VOLUME_EMITTER_REC_WRITER : public sLGI_VOLUME_EMITTER_REC , sLGI_EMITTER_WRITER_BASE
{
 public:
  sLGI_VOLUME_EMITTER_REC_WRITER(const sCDI_VOLUME_EMITTER* cdi_emitter);
  VOID write(const sCDI_VOLUME_EMITTER* cdi_emitter);
  size_t num_total_bytes() {
    return(m_num_total_dynamic_bytes + sizeof(sLGI_VOLUME_EMITTER_REC));
  }
}*LGI_VOLUME_EMITTER_REC_WRITER;


typedef class sLGI_POINT_EMITTER_REC_WRITER : public sLGI_POINT_EMITTER_REC , sLGI_EMITTER_WRITER_BASE
{
 public:
  sLGI_POINT_EMITTER_REC_WRITER(const sCDI_POINT_EMITTER* cdi_emitter);
  VOID write(const sCDI_POINT_EMITTER * cdi_emitter);
   size_t num_total_bytes() {
    return(m_num_total_dynamic_bytes + sizeof(sLGI_POINT_EMITTER_REC));
  } 
}*LGI_POINT_EMITTER_REC_WRITER;

typedef class sLGI_TIRE_EMITTER_REC_WRITER : public sLGI_TIRE_EMITTER_REC , sLGI_EMITTER_WRITER_BASE 
{
 public: 
  sLGI_TIRE_EMITTER_REC_WRITER(const sCDI_TIRE_EMITTER* cdi_emitter);
  VOID write(const sCDI_TIRE_EMITTER* cdi_emitter);
 size_t num_total_bytes() {
    return(m_num_total_dynamic_bytes + sizeof(sLGI_TIRE_EMITTER_REC));
  }
}*LGI_TIRE_EMITTER_REC_WRITER;


typedef class sLGI_RAIN_EMITTER_REC_WRITER : public sLGI_RAIN_EMITTER_REC , sLGI_EMITTER_WRITER_BASE 
{
 public:
  sLGI_RAIN_EMITTER_REC_WRITER(const sCDI_RAIN_EMITTER* cdi_emitter);
  VOID write(const sCDI_RAIN_EMITTER* cdi_emitter);
  size_t num_total_bytes() {
    return(m_num_total_dynamic_bytes + sizeof(sLGI_RAIN_EMITTER_REC));
  }
}*LGI_RAIN_EMITTER_REC_WRITER;



//Classes to write cylinder and box records:

typedef struct sLGI_CYLINDER_REC_WRITER : public sLGI_CYLINDER_REC
 {
  sLGI_CYLINDER_REC_WRITER(){};
  sLGI_CYLINDER_REC_WRITER(const sCDI_CYL_* cdi_cylinder);
  VOID write(const sCDI_CYL_* cdi_cylinder);
  size_t num_total_bytes() {
    return sizeof(sLGI_CYLINDER_REC) + name_length;
  }
}* LGI_CYLINDER_REC_WRITER;

//struct sCDI_BOX_;

typedef struct sLGI_BOX_REC_WRITER : public sLGI_BOX_REC
{
  sLGI_BOX_REC_WRITER(){};
  sLGI_BOX_REC_WRITER(const sCDI_BOX_* cdi_box);
  VOID write(const sCDI_BOX_* cdi_box);
  size_t num_total_bytes() {
    return sizeof(sLGI_BOX_REC) + name_length;
  }

}* LGI_BOX_REC_WRITER;


LGI::eLGI_DISTRIBUTION_TYPE cdi_to_lgi_distribution(CDI_DISTRIBUTION_TYPE cdi_type);
LGI::eLGI_DISTRIBUTION_TYPE cdi_to_lgi_distribution(sCDI_PARM cdi_parm_type);



#endif



