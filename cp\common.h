/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Common definitions for the control process
 *
 * Jim Salem, Exa Corporation 
 * Created Thu May  5 1994
 *--------------------------------------------------------------------------*/

#ifndef __CP_COMMON_H
#define __CP_COMMON_H

#include <stdarg.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <ctype.h>
#include <vector>
#include <queue>
#include <cmath>
#include <atomic>
#if defined(_EXA_MPI)
#include <mpi.h> 
#endif

#include SCALAR_H
#include PLATFORM_H

#include CDI_H
#include LGI_H
#include CP_H

#include FOREST_H
#if SURF_COUP
#include VHASH_H
#endif

#include MSGERR_H
#include DEBUG_H
#include MALLOC_H
#include LOOP_H
#include LOOP_RANGE_H
#include SRI_H
#include XNEW_H
#include VMEM_H
#include EXATIME_H
#include JOBCTL_SERVER_H
#include EXALIC_H

#ifdef	__cplusplus
extern "C" {
#endif
#include AUDIT_H
#include UNITS_H
#include EXPRLANG_H

#ifdef	__cplusplus
}
#endif

#include JOBCTL_SERVER_CPP_H
#include CCUTILS_H
#include "errbuf.h"
#define COARSEST_SCALE	0

// Realms are enumerated as STP_REALM in PHYSTYPES.
// REALM is an int type that can be used as an lvalue.

typedef sINT8 REALM;

BOOLEAN is_cmd_executable_via_system(cSTRING cmd);
VOID trim_trailing_ampersand_from_shell_cmd(STRING cmd);

inline cSTRING time_str() // not thread safe since ctime() uses static storage
{
  time_t now = time(NULL);
  STRING ctime_now = ctime(&now);
  ctime_now[strlen(ctime_now) - 1] = '\0'; // whack newline

  return ctime_now;
}
  
/*--------------------------------------------------------------------------*
 * Type definitions
 *--------------------------------------------------------------------------*/

/*--------------------------------------------------------------------------*
 * Generic CP support routines
 *--------------------------------------------------------------------------*/

const sINT32 sINT32_MAX = 0x7fffffff;

inline STRING strsave(cSTRING s) 
{
  STRING t = xnew char [strlen(s) + 1];
  strcpy(t, s);
  return t;
}

inline void *_cp_vmem_reserve(size_t n_bytes, const char *filename, int line_number)
{
  void *p = vmem_reserve(n_bytes);
  if (p == NULL)
    xnew_throw_bad_alloc(n_bytes, filename, line_number);
  return p;
}

#define cp_vmem_reserve(n_bytes) \
   _cp_vmem_reserve(n_bytes, __FILE__, __LINE__)


/*--------------------------------------------------------------------------*
 * 3D Vectors
 *--------------------------------------------------------------------------*/

#define vdot(a, b)	((a)[0] * (b)[0] + (a)[1] * (b)[1] + (a)[2] * (b)[2])
#define vscale(r, s, v)	{ (r)[0] = (s) * (v)[0]; (r)[1] = (s) * (v)[1]; (r)[2] = (s) * (v)[2]; }
#define vsub(r, a, b)	{ (r)[0] = (a)[0] - (b)[0]; (r)[1] = (a)[1] - (b)[1]; (r)[2] = (a)[2] - (b)[2]; }
#define vadd(r, a, b)	{ (r)[0] = (a)[0] + (b)[0]; (r)[1] = (a)[1] + (b)[1]; (r)[2] = (a)[2] + (b)[2]; }
#define vneg(r, a)	{ (r)[0] = -(a)[0]; (r)[1] = -(a)[1]; (r)[2] = -(a)[2]; }
#define vcopy(r, a)	{ (r)[0] = (a)[0]; (r)[1] = (a)[1]; (r)[2] = (a)[2]; }
#define vneg(r, a)	{ (r)[0] = -(a)[0]; (r)[1] = -(a)[1]; (r)[2] = -(a)[2]; }
#define vzero(r)	{ (r)[0] = 0; (r)[1] = 0; (r)[2] = 0; }
#define vcross(rr, aa, bb)                                \
{                                                         \
  (rr)[0] = (aa)[1] * (bb)[2] - (aa)[2] * (bb)[1];        \
  (rr)[1] = (aa)[2] * (bb)[0] - (aa)[0] * (bb)[2];        \
  (rr)[2] = (aa)[0] * (bb)[1] - (aa)[1] * (bb)[0];        \
}

#define vunitize(a)				\
{						\
  dFLOAT _mag = sqrt(vdot(a,a));		\
  (a)[0] /= _mag;				\
  (a)[1] /= _mag;				\
  (a)[2] /= _mag;				\
}

/*------------------------------------------------------------------------------------------*
 * Params to set up the start time of momentum freeze solver
 *------------------------------------------------------------------------------------------*/
#define MOMENTUM_FREEZE_START_COEFF 80
#define VR_SYNC_COEFF 2
#define MAXIMUM_FREEZE_START_TIME 1000

/*------------------------------------------------------------------------------------------*
 * Params to 5G
 *------------------------------------------------------------------------------------------*/
#define MAX_NUM_COMPONENTS_5G    2
const cSTRING component_string_5g[MAX_NUM_COMPONENTS_5G] = {"0", "1"};

/*------------------------------------------------------------------------------------------*
 * the least common multiple(lcm) and the greatest commom divisor(gcd) of multiple integers
 *------------------------------------------------------------------------------------------*/

inline asINT32 gcd(asINT32 a, asINT32 b) 
{
  /* a is the dividend and b is the divisor */
  if(a == 0 || b==0)
    return 0;
  else 
    {
      asINT32 r = a % b; //remainder
    
      while(r != 0) {
	a = b;
	b = r;
	r = a % b;
      }  
      return b;
    }
}

inline asINT32 gcd(asINT32 a, asINT32 b, asINT32 c) 
{
  
  asINT32 d1 = gcd(a,b);
  asINT32 d2 = gcd(b,c);
  
  return gcd(d1, d2);
}


inline asINT32 gcd(asINT32 a, asINT32 b, asINT32 c, asINT32 d) 
{
  
  asINT32 d1 = gcd(a, b, c);
  asINT32 d2 = gcd(b, c, d);
  
  return gcd(d1, d2);
}

inline asINT32 gcd(asINT32 a, asINT32 b, asINT32 c, asINT32 d, asINT32 e)
{
  
  asINT32 d1 = gcd(a,b);
  asINT32 d2 = gcd(b,c);
  asINT32 d3 = gcd(c,d);
  asINT32 d4 = gcd(d,e);
  
  return gcd(d1, d2, d3, d4);
}

inline asINT32 gcd(asINT32 a, asINT32 b, asINT32 c, asINT32 d, asINT32 e, asINT32 f)
{

  asINT32 d1 = gcd(a,b);
  asINT32 d2 = gcd(b,c);
  asINT32 d3 = gcd(c,d);
  asINT32 d4 = gcd(d,e);
  asINT32 d5 = gcd(d,f);

  return gcd(d1, d2, d3, d4, d5);
}
inline asINT32 lcm(asINT32 a, asINT32 b)
{
  asINT32 n_gcd = gcd(a, b);  //gcd of a and b 
  if(n_gcd == 0)
    return 0;
  else
    return (a * b / n_gcd);

}
  
inline asINT32 lcm(asINT32 a, asINT32 b, asINT32 c) 
{
  return lcm(lcm(a,b), c);
}


inline asINT32 lcm(asINT32 a, asINT32 b, asINT32 c, asINT32 d) 
{
  return lcm(lcm(a, b, c), d);
}
inline asINT32 lcm(asINT32 a, asINT32 b, asINT32 c, asINT32 d, asINT32 e)
{
  return lcm(lcm(a,b,c,d), e);
}

inline asINT32 lcm(asINT32 a, asINT32 b, asINT32 c, asINT32 d, asINT32 e, asINT32 f)
{
  return lcm(lcm(a,b,c,d, e), f);
}

/*--------------------------------------------------------------------------*
 * Simulation arguments
 *--------------------------------------------------------------------------*/

/* This structure defines a set of standard arguments that are
 * used to initialize the simulation engine */
typedef struct sSIM_ARGS {
  sINT32        argc;
  cSTRING       *argv;

  cBOOLEAN      mpi_environment_p;
  /* Whether to report total time on the CP */
  cBOOLEAN	report_total_time_p;
  /* Whether to print the Performance section at the end of the simulation */
  cBOOLEAN  report_performance_p;
  /* Whether to report time per N timesteps on the CP */
  cBOOLEAN report_rp_time;
  volatile asINT32	time_every;	/* Can be updated by async request */
  /* If TRUE, locations records should be skipped when reading LGI files */
  cBOOLEAN 	ignore_locations_p;
  /* If TRUE, measurement records are skipped when reading LGI files */
  cBOOLEAN	ignore_measurements_p;
  /* Whether to clear all states before running the simulation */
  cBOOLEAN	clear_all_p;
  cBOOLEAN      accept_5_0_thermal_feedback_p;
  cBOOLEAN      accept_old_ht_cdi_p;
  cBOOLEAN      accept_old_turb_cdi_p;
  cBOOLEAN      suppress_final_status_p;

  /* Include the starttime in base timestep units when printing the coupling phase table */
  cBOOLEAN report_coupling_phase_basetime;
  
  /* Pathname of the SP binary */
  cSTRING       sp_binary_filename;

  cSTRING       cdi_filename;
  cBOOLEAN      read_old_cdi_p;

  cBOOLEAN      do_extrapolate;
  cBOOLEAN      smart_seed_boundaries;
  cBOOLEAN      use_seed_cache;
  cBOOLEAN      seed_mks;

  /* For momentum-freeze solver */
  cBOOLEAN      freeze_momentum_field;
  cBOOLEAN      suppress_seed_requirement;
  sINT32        momentum_freeze_start_time;
  sINT32        thermal_timestep_ratio;

  /* The sim options string for sim_run */
  SIM_OPTIONS	run_options;

  cSTRING	resume_filename;
  /* 
   * if set, the checkpoint record in the LGI is ignored and checkpoints are
   * taken whenever (time % checkpoint_interval) == 0
   */
  TIMESTEP      checkpoint_interval;
  /* true if checkpoint interval is set */
  cBOOLEAN      checkpoint_override;
  cBOOLEAN	debug_sps_p;
  /* If non-zero, overrides the num_timesteps field of the GLOBAL_INFO record in the LGI file */
  uINT32        num_timesteps;

  sINT32        n_lb_base_steps;   //fhl
  sINT32        n_t_base_steps;    //fhl
  sINT32        n_ke_base_steps;   //fhl
  sINT32        n_uds_base_steps;
  sINT32        n_conduction_base_steps;
  sINT32        n_radiation_base_steps;

  sINT32        acous_start_time;  //fhl

  /* Timers on and off */

  TIMESTEP      timers_on_timestep;
  TIMESTEP      timers_off_timestep;

  /* Disable measurements */

  cBOOLEAN      disable_all_meas_windows;

  /* Number of elements in a SP send buffer */
  uINT32        n_send_elements;

  cSTRING	decomp_file;
  cSTRING	n_processors;
  cSTRING	pmodel_file;
  cSTRING	target_platform;
  cSTRING	decomp_message_log;
  uINT32        umask;
  cBOOLEAN      umask_override;
  cBOOLEAN	unlink_input_after_init;
  cBOOLEAN	fluid_turb_solver_1;
  cBOOLEAN	no_swirl_model;
  sINT32	ablm_cap;
  TIMESTEP	ignore_vel_n_timesteps;
  TIMESTEP	ignore_temp_n_timesteps;
  TIMESTEP	ignore_uds_n_timesteps;
  sINT32	vel_warning_limit;
  sINT32	temp_warning_limit;
  sINT32	fpe_exit_limit;
  cSTRING	max_vel;		// uval: value plus unit
  cSTRING	max_temp;		// uval: value plus unit
  cSTRING	min_temp;		// uval: value plus unit
  cBOOLEAN	fixed_temp_walls;
  cBOOLEAN	local_vel_fan_model;
  cBOOLEAN	mme_chkpnt_at_end;
  cBOOLEAN	full_chkpnt_at_end;
  cBOOLEAN	parallel_io;
  uINT32    parallel_io_max_buffer_size;
  cBOOLEAN	no_final_checkpoint_retry;
  cBOOLEAN	allow_immediate_checkpoints;
  cBOOLEAN	barrier_instead_of_finalize;
  cBOOLEAN	lm_log_consumption;
  cBOOLEAN      allow_missing_meas_files;
  dFLOAT       	dns_nu_over_t_floor;
  cBOOLEAN      no_reserve_addr_space;
  cBOOLEAN      no_mpi_request_free;
  cBOOLEAN      short_mpi_thread_sleep;
  cBOOLEAN      autostop_all_off; // Autostop turned off by exaqsub --autostop_all_off option
  cBOOLEAN 	is_ke_base_step_arg_read;
  cBOOLEAN 	is_cond_base_step_arg_read;
  cBOOLEAN 	is_rad_base_step_arg_read;

  cSTRING	lm_user;
  cSTRING	lm_host;
  cSTRING	lm_alt;
  cBOOLEAN      use_opt_licenses;
  cBOOLEAN      opt_license_failover;

  /* For 5G */
  cBOOLEAN      smart_seed_contact_angle;
  cBOOLEAN      output_surface_contact_angle;
  cBOOLEAN      output_surface_contact_angle_only;
  cBOOLEAN      output_component_velocity;
  cBOOLEAN      output_mass_exchange;
  sINT32        major_flow_direction;

  /* For UDS */
  cBOOLEAN      disable_scalar_diffusivity_bound;
  
  /* For cooling air leakage model */
  cBOOLEAN      disable_calm;

  /* For VLES-RT */
  cBOOLEAN      use_PF6_VLES_model;

  //for conformal body force
  cBOOLEAN      checkpoint_body_force;
  cBOOLEAN      seed_body_force;
  cBOOLEAN      laplace;
  dFLOAT        seed_body_force_scale;

  /* For particle modeling */
  cBOOLEAN      disable_particle_modeling;
  cBOOLEAN      enable_film_trajectory_measurement;

  /* Information about the CP node, used for controlling PowerTHERM binding */

  uINT32        n_cpnode_ranks;
  uINT32        n_cpnode_sockets;
  uINT32        n_cpnode_cores_per_socket;
  uINT32        n_cpnode_threads_per_core;

  cBOOLEAN      gpu;
  cBOOLEAN      gpu_repeatable;
  uINT32        gpu_lrf_max_isends;
  uINT32        gpu_max_buffer_size;

  cBOOLEAN enable_tangential_shell_conduction;
  cBOOLEAN use_implicit_shell_solver;
  cBOOLEAN use_implicit_solid_solver;

} *SIM_ARGS;

extern sSIM_ARGS sim_args;

void die_if_gpu(bool cond, cSTRING msg, bool plural);

int system_cmd(cSTRING cmd);

BOOLEAN uval_to_lattice_value(cSTRING uval_str, 
			      cSTRING unit_class_name,
			      cSTRING printable_unit_class_name,
			      cSTRING lattice_unit_name,
			      cSTRING arg_name,
			      BOOLEAN use_msg_error, // as opposed to msg_warn
			      dFLOAT *result);
#if defined(_EXA_MPI)

template <class T, int N>
int cp_init_mpi_recv(cExaMsg<T, N> &recv_msg,  
                     int rank,
                     int tag, 
                     MPI_Comm mpi_comm, 
                     MPI_Status *eMPI_status)
{
  while (1) {
    int probe_status;
    MPI_Iprobe(rank, tag, mpi_comm, &probe_status, eMPI_status);
    if (probe_status) {
      int status = g_exa_sp_cp_comm.recv(recv_msg);
      return status;
    }
    if (cp_process_sp_errors_initialized())
      cp_process_sp_errors(wallclock_time_secs());
  }
}

template <class T>
int cp_init_mpi_recv(cExaMsg<T> &recv_msg,  
                     int rank,
                    int tag, 
                    MPI_Comm mpi_comm, 
                    MPI_Status *eMPI_status)
{
  while (1) {
    int probe_status;
    MPI_Iprobe(rank, tag, mpi_comm, &probe_status, eMPI_status);
    if (probe_status) {
      int status = g_exa_sp_cp_comm.recv(recv_msg);
      return status;
    }
    if (cp_process_sp_errors_initialized())
      cp_process_sp_errors(wallclock_time_secs());
  }
}

int cp_init_mpi_send(void *buf, 
                     int count, 
                     MPI_Datatype datatype, 
                     int rank, 
                     int tag, 
                     MPI_Comm mpi_comm);
#endif // _EXA_MPI
VOID cp_process_init_time_simerrs_func();
VOID reserve_sri_user_vars(asINT32 n_vars);

#define DEBUG_SURFACE_COUPLING_MEAS_WINDOW 0

#define DEBUG_WEATHER_FILE 0
#define DEBUG_ADAPTIVE_COUPLING 0
#define DEBUG_PT_GRADIENT 1         // Dump temperature gradient information for each coupling

// For t_ptherm support in eqns
#define DEBUG_T_PTHERM_EQNS 0

// For debugging variable PowerTHERM coupling
#define DEBUG_VARIABLE_POWERTHERM_COUPLING 0

// For debugging PowerTHERM coupling table prepared for PowerVIZ
#define DEBUG_POWERTHERM_COUPLING_TABLE 0

#define DEBUG_AVG_MME 0
#define DEBUG_START_EMITTERS 0

// For meas windows started via monitors, it is possible that at ckpt the end of initial transient (EIT)
// of the monitor has not been found yet, and the simulation runs after EIT is found. One strategy to 
// deal with this is to copy all tmp meas files at ckpt timestep. However it is expensive and thus we
// opted for another strategy which does not need to copy meas files.
#define COPY_TMP_MEAS_FILES_AT_CKPT 0

#endif /* __CP_COMMON_H */

