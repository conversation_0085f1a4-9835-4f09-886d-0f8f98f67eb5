/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#include <map>
#include <unordered_map>

#include "common.h"
#include "cp_seed_from_meas.h"
#include "jobctl_status.h"
#include "cp_info.h"

const SRI_VARIABLE_TYPE sSEED_FROM_MEAS_DESC::m_sri_var_type_from_dgf_bseed_var_type[DGF_N_BOUNDARY_SEED_VARS] = {
    SRI_VARIABLE_PRESSURE,
    SRI_VARIABLE_TOTAL_PRESSURE,
    SRI_VARIABLE_XVEL,
    SRI_VARIABLE_YVEL,
    SRI_VARIABLE_ZVEL,
    SRI_VARIABLE_XVEL, // flow direction from velocity
    SRI_VARIABLE_YVEL,
    SRI_VARIABLE_ZVEL,
    SRI_VARIABLE_TEMP,
    SRI_VARIABLE_TURB_KINETIC_ENERGY,
    SRI_VARIABLE_TURB_DISSIPATION,
    SRI_VARIABLE_MASS_FLUX,
    SRI_VARIABLE_MASS_FLUX,
    SRI_VARIABLE_MASS_FLUX,
    SRI_VARIABLE_INVALID
  };

const auINT32 sSEED_FROM_MEAS_DESC::m_vel_mask = 0
                                              | (1 << DGF_BOUNDARY_SEED_VAR_XVEL)
                                              | (1 << DGF_BOUNDARY_SEED_VAR_YVEL)
                                              | (1 << DGF_BOUNDARY_SEED_VAR_ZVEL);

VOID sSEED_FROM_MEAS_DESC::setup()
{
  cp_jobctl_output_status("Begin reading sampled face measurement file");

  die_if_gpu(true, "Sampled Face Measurements", true);

  // open measurement file
  if (m_sampled_face_meas_filename != "") {
    SRI_STATUS status = sri_open_file_for_read(m_sampled_face_meas_filename.c_str(), NULL, &m_sampled_face_meas_file);
    if (status != SRI_SUCCESS)
      msg_error("Unable to open sampled face measurement file \"%s\": %s.",
               m_sampled_face_meas_filename.c_str(), sri_error_string(status));
  }

  // check if m_sampled_face_meas_file is a valid sampled face measurement file
  if ((m_sampled_face_meas_file != NULL) && (m_sampled_face_meas_file->file_type() != SRI_SURFACE_TYPE))
  {
    msg_error("File \"%s\" is not a valid surface measurement file. "
              "Only surface measurement files are supported for velocity profile transfer.",
              m_sampled_face_meas_file->name());
    return;
  }

  m_params.setup(m_sampled_face_meas_file, NULL, TRUE);

  m_n_pts_total      = m_sampled_face_meas_file->n_points();


  sriINT num_points = MIN(m_n_pts_total, MEAS_CELLS_BUFFER_SIZE);

  // read facet ids from the sampled face meas file
  sriPART_INDEX *facet_ids = new sriPART_INDEX[m_n_pts_total];
  if (((SRI_SURFACE_FILE)m_sampled_face_meas_file)->are_facet_ids_present()) {
    SRI_STATUS status = ((SRI_SURFACE_FILE)m_sampled_face_meas_file)->read_facet_ids(facet_ids, m_n_pts_total, 0);
    if (status != SRI_SUCCESS) {
      msg_internal_error("Error while reading facet ids from file \"%s\": %s",
                         m_sampled_face_meas_filename.c_str(),
                         sri_error_string(status));
    }
  } else {
    if(!m_is_octree_map)
      msg_warn("Facet IDs are not present in file \"%s\". Simulator will use the nearest measurement surfel for mapping", m_sampled_face_meas_filename.c_str());
    m_is_octree_map = TRUE;
  }
  if(m_sampled_face_meas_file->file_params()->ptge_rec.cdi_file_id != m_cdi_file_id)
    msg_warn("Seed measurement file  \"%s\" was not generated using the CDI file, used to import the seeded face into PowerCASE. The seeded data on the face may be wrong.",
            m_sampled_face_meas_filename.c_str());

  m_is_mean_vel_added = m_sampled_face_meas_file->file_params()->is_mean_vel_added;

  sBG_POINT3d min_bound(m_sampled_face_meas_file->file_params()->min_bound[0],
                        m_sampled_face_meas_file->file_params()->min_bound[1],
                        m_sampled_face_meas_file->file_params()->min_bound[2]);
  sBG_POINT3d max_bound(m_sampled_face_meas_file->file_params()->max_bound[0],
                        m_sampled_face_meas_file->file_params()->max_bound[1],
                        m_sampled_face_meas_file->file_params()->max_bound[2]);
  sBG_BOX3d range(min_bound, max_bound);
  m_msi_table = new cMSI_TABLE(m_n_pts_total, range);
  m_msi_table->set_centroids((SRI_SURFACE_FILE)m_sampled_face_meas_file, m_n_pts_total);
  set_cdi_to_meas_file_length_scale_factor();
  m_simple_map.init_buffer((SRI_SURFACE_FILE)m_sampled_face_meas_file);
  fill_facet_id_to_meas_cell_map(m_sampled_face_meas_file);
  // create a map from the facet id to the meas cell index
  // In the simulation used to generate the sampled face meas file, the user was supposed to check the option
  // "Use input mesh elements as meas cells". Hence, there is a one-to-one mapping from the facet id to the meas cell.
  // PR54743: Before the 2id branch was merged, the following loop was only hit one time. After the 2id branch, it was hit for
  // each realm, even if there was only 1 active realm, and the warning message below was printed when it shouldn't be. To recover the
  // original behavior, check to see if the map is empty, and only do the following loop if it is empty.
  if (m_meas_cell_index_from_facet_id.empty()) {
    ccDOTIMES(i, m_n_pts_total) {
      if (m_meas_cell_index_from_facet_id.find(facet_ids[i]) == m_meas_cell_index_from_facet_id.end()) {
        m_meas_cell_index_from_facet_id[facet_ids[i]] = i;
      } else if(!m_transient_boundary_seeding) {
        msg_print("Facet ID %d is associated with more than one measurement cell in file \"%s\". "
                  "Boundary conditions via Sampled Face Measurement is currently supported by PowerFLOW "
                  "transient boundary seeded case.",
                  facet_ids[i],
                  m_sampled_face_meas_filename.c_str());
      }
      m_msi_table->insert_msi(i);
    }
  }
  delete[] facet_ids;

  // read surfel areas from the sampled face meas file
  SRI_STATUS status;
  m_surfel_areas = new sriFLOAT[m_n_pts_total];
  status = ((SRI_SURFACE_FILE)m_sampled_face_meas_file)->read_areas(m_surfel_areas, m_n_pts_total, 0);
  if (status != SRI_SUCCESS) {
    msg_internal_error("Error while reading surfel areas from file \"%s\": %s",
                       m_sampled_face_meas_filename.c_str(),
                       sri_error_string(status));
  }
  // length_scale is the number of meters per lattice length in the first simulation (i.e. the simulation that was used 
  // to create the sampled face meas file). This value is obtained from the sampled face meas file.
  dFLOAT length_scale = find_lat_scale(m_sampled_face_meas_file->file_params(), "LatticeLength");
  dFLOAT time_scale = find_lat_scale(m_sampled_face_meas_file->file_params(), "LatticeVelocity");
  dFLOAT area_scale = length_scale*length_scale;

  // m_scale factors is used to rescale the velocities in physics surfels that were created from facets/meas cells 
  // that were intersected by solids in the first simulation. For example, consider a measurement surfel from the 
  // first simulation that had an area 1, but was cut by a solid and only half of it was in the fluid region. Let's 
  // assume a velocity of 3 was stored on this meas cell. In the second simulation, if the inlet seeded from this meas 
  // file includes the whole meas cell, then setting a velocity of 3 on all physics surfels created from this meas 
  // cell/facet will produce a mass flow of 3*1 through the area encompassed by the facet. Instead, to conserve mass, 
  // we halve the velocity when seeding the boundary surfel to maintain the same mass flow. However, in cases where a 
  // facet in the first simulation is intersected by a solid in the second simulation (and therefore its area reduces), 
  // we do not rescale the velocity.
  m_scale_factors = new sriFLOAT[m_n_pts_total];

  ccDOTIMES(i, m_n_pts_total) {
    // The surfel areas in the sampled face meas file are in lattice units. We multiply by area_scale to get them in m^2
    // This is necessary when we calculate the scale factors because we are going to divide the facet/meas surfel area 
    // from the meas file by the total area of the active physics surfels generated from the same facet in the second
    // simulation. To do this, we need to have them in the same units (lattice areas are not comparable if the 
    // resolutions of the two simulations are different)
    m_surfel_areas[i] *= area_scale;
    m_scale_factors[i] = 0;
  }

  SRI_VARIABLE_DESC variables = m_sampled_face_meas_file->file_params()->variables;
  sriSHORT n_variables = m_sampled_face_meas_file->file_params()->n_variables;
  BOOLEAN  is_mass_flux_read_complete = FALSE;

  // set the frame whose measurements will be used for seeding
  sriINT frame_index = -1;
  if(m_meas_frame_num > 0)
    frame_index = m_meas_frame_num;
  else {
    if(m_transient_boundary_seeding) {
      frame_index = m_meas_start_frame_num;
      m_total_meas_frames = m_sampled_face_meas_file->n_frames();
      m_n_total_frames_sent = m_meas_start_frame_num;
    }
    else
      frame_index = m_sampled_face_meas_file->n_frames() - 1;
  }

  if (frame_index >= m_sampled_face_meas_file->n_frames()) {
    msg_error("Invalid Measurement Frame Number. The user specified frame number %d, "
              "but there are only %d frames in the measurement file \"%s\"",
              frame_index, m_sampled_face_meas_file->n_frames(), m_sampled_face_meas_filename.c_str());
  }
  m_sampled_face_meas_file->set_meas_frame(frame_index);
  set_frame_period_and_start_time();
  sriBOOL is_mass_flux_read = FALSE;
  ccDOTIMES(i, DGF_N_BOUNDARY_SEED_VARS) {
    m_meas_start_index[i] = 0;
    // m_masks_union is set up in cp_cdi_reader.cc:read_surface_physics_descriptors
    if (m_masks_union & (1 << i)) {
      sriBOOL is_mass_flux_component = FALSE;
      if (i == DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX ||
          i == DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX ||
          i == DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX) {
        is_mass_flux_component = TRUE;
      }

      // DGN_N_BOUNDARY_SEED_VARS includes three indices for mass flux in x, y and z directions. But the measurement
      // file only stores rho*V dot n (V is the velocity vector and n is the normal of the meas cell). We are looping
      // over values in the range [0, N_BOUNDARY_SEED_VARS), but we need to read the mass flux only once. We do this 
      // for i = DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX and then set is_mass_flux_read to TRUE, and don't read anything from
      // the file for the other mass flux component indices.
      if (!is_mass_flux_component || (i == DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX && !is_mass_flux_read)) {
        if (i == DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX && !is_mass_flux_read)
          is_mass_flux_read = TRUE;

        SRI_VARIABLE_TYPE sri_var_type = m_sri_var_type_from_dgf_bseed_var_type[i];
        if(cp_info.n_dims == 2 && sri_var_type == SRI_VARIABLE_ZVEL )
          continue;

        sriINT            var_index    = m_sampled_face_meas_file->get_variable_index(sri_var_type);

        if (var_index == -1) {
          if (sri_var_type == SRI_VARIABLE_PRESSURE ||  // static pressure is not stored for adiabatic simulations
              sri_var_type == SRI_VARIABLE_TOTAL_PRESSURE) // total pressure is a derived quantity
            var_index = m_sampled_face_meas_file->get_variable_index(SRI_VARIABLE_DENSITY);
          else
            msg_error("SRI variable type %d not present in file \"%s\"", sri_var_type, m_sampled_face_meas_file->name());
        }

        m_is_var_double[i] = variables[var_index].var_double;

        // Initially, we read at most MEAS_CELLS_BUFFER_SIZE variable values from the measurement file
        // In the function fill_seed_from_meas_data, the variables from the measurement file may be read in a random 
        // order. However, get_variable() only returns a contiguous chunk of data from a specified location in the
        // meas file. Since we do not want to read all the variable values from the measurement file at once (to limit 
        // the amount of memory we use), we read chunks of size MEAS_CELLS_BUFFER_SIZE. If the next meas cell index
        // references the chunk we have already read, we pick the value from there. Otherwise, we read another chunk.
        if (m_is_var_double[i]) {
          m_var_values_double[i] = new sriDOUBLE[num_points];
          status = m_sampled_face_meas_file->get_variable(sri_var_type,
                                                          m_var_values_double[i],
                                                          num_points,
                                                          m_meas_start_index[i],
                                                          NULL);
        } else {
          m_var_values[i] = new sriFLOAT[num_points];
          status = m_sampled_face_meas_file->get_variable(sri_var_type,
                                                          m_var_values[i],
                                                          num_points,
                                                          m_meas_start_index[i],
                                                          NULL);
        }

        if (status != SRI_SUCCESS) {
          msg_internal_error("Error while reading %d values of SRI variable type %d starting at index %lu "
                             "from measurement file \"%s\": %s",
                             num_points,
                             sri_var_type,                             
                             m_meas_start_index[i],
                             m_sampled_face_meas_filename.c_str(),
                             sri_error_string(status));
        }
      }
    }
  }

  return;
}

VOID sSEED_FROM_MEAS_DESC::set_frame_period_and_start_time()
{
  sFLOAT vel_scale, time_scale, length_scale;
  vel_scale = find_lat_scale(m_sampled_face_meas_file->file_params(), "LatticeVelocity");
  length_scale = find_lat_scale(m_sampled_face_meas_file->file_params(), "LatticeLength");
  time_scale = find_lat_scale(m_sampled_face_meas_file->file_params(), "LatticeTime");
  sFLOAT time_interp_ratio = time_scale * length_scale / vel_scale;
  time_interp_ratio /= cdi_data.seconds_per_timestep;
  sriINT start_time[2], end_time[2];
  m_sampled_face_meas_file->read_meas_frame_time_interval(start_time, end_time);
  m_frame_period = time_interp_ratio * (start_time[1] - start_time[0]);
  m_frame_start_time = time_interp_ratio * (end_time[0] - start_time[0]) / 2;
  if(m_frame_start_time == 0)
    m_frame_start_time = 1;
  if(cp_info.is_rwnc_seeded) {
    sriINT meas_end_time = time_interp_ratio * m_frame_period * (m_sampled_face_meas_file->n_points() + 1);
    if(meas_end_time < cp_info.end_time)
      msg_warn("Duration of measurement file is less than the duration of current seeded simulation. It will be treated periodic");
  }
}

VOID sSEED_FROM_MEAS_DESC::fill_seed_params(cDGF_SMART_SEED_CONTROL &seed_from_meas_control)
{
  m_params.fill_params(seed_from_meas_control);
  seed_from_meas_control.tag.id = DGF_SMART_SEED_CONTROL_TAG;
  seed_from_meas_control.first_frame_ts = m_frame_start_time;
  seed_from_meas_control.sim_ts_per_frame = m_frame_period;
  seed_from_meas_control.num_frames = m_transient_boundary_seeding ? NUM_FRAMES : 0;
  seed_from_meas_control.n_frame = m_meas_frame_num;
  seed_from_meas_control.n_total_meas_frames = m_total_meas_frames;
  seed_from_meas_control.is_mean_vel_added = m_is_mean_vel_added;
}

VOID sSEED_FROM_MEAS_DESC::fill_seed_from_meas_data(cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data, cDGF_SURFEL_DESC &surfel_desc, asINT32 desc_index, asINT32 frame_index)
{
  seed_from_meas_data.mask = cp_info.seed_from_meas_mask_from_face_index[surfel_desc.s.face_index];
  seed_from_meas_data.start_frame = m_meas_frame_num;
  seed_from_meas_data.num_frames = m_total_meas_frames;

  if (seed_from_meas_data.mask) {
    seed_from_meas_data.descriptor_index = desc_index;

    // use the surfel's facet_id and the offset from the new CDI file to get the facet id in the sampled meas file
    // We make sure not to merge physics surfels across facet boundaries
    sriFACET_ID old_cdi_facet_id = surfel_desc.s.facet_id - m_facet_id_offset;
    
    // use the map created during setup to calculate the meas cell index
    asINT32     meas_cell_index = -1;
    if (m_meas_cell_index_from_facet_id.find(old_cdi_facet_id) != m_meas_cell_index_from_facet_id.end()) {
      meas_cell_index = m_meas_cell_index_from_facet_id[old_cdi_facet_id];
    }
    seed_from_meas_data.meas_cell_index = meas_cell_index;

    if (meas_cell_index != -1) { // if the physics surfel maps to a corresponding measurement surfel

      // sum the areas of all physics surfels that correspond to each meas cell
      // convert physics surfel area to meters^2. m_scale_factors is later normalized by the meas cell/facet area
      if (surfel_desc.s.surfel_flags & STP_PROCESS_ON_EVEN_TIMES) {
        m_scale_factors[meas_cell_index] += surfel_desc.s.area*cdi_data.meters_per_cell*cdi_data.meters_per_cell;
      }

      sriBOOL is_mass_flux_read = FALSE;
      SRI_STATUS status;
      ccDOTIMES(i, DGF_N_BOUNDARY_SEED_VARS) {
        if (seed_from_meas_data.mask & (1 << i)) {
          sriBOOL is_mass_flux_component = FALSE;
          if (i == DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX ||
              i == DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX ||
              i == DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX) {
            is_mass_flux_component = TRUE;
          }

          // read the measurements in chunks of size MEAS_CELLS_BUFFER_SIZE whose range contains meas_cell_index
          sriPOINT num_points = MEAS_CELLS_BUFFER_SIZE;
          
          // The variables from the measurement file may be read in a random order, depending on meas_cell_index.
          // get_variable() returns a contiguous chunk of data from a specified location in the meas file. We read 
          // chunks the variables values from the meas file in chunks of size MEAS_CELLS_BUFFER_SIZE to limit the amount
          // of memory we use while reading the file. If the next meas cell index references the chunk we have already 
          // read, we pick the value from there. Otherwise, we read another chunk starting from meas_cell_index
          sriBOOL  is_update_meas_cells = FALSE;
          if (meas_cell_index < m_meas_start_index[i] || meas_cell_index >= m_meas_start_index[i] + MEAS_CELLS_BUFFER_SIZE) {
            is_update_meas_cells = TRUE;
            m_meas_start_index[i] = (meas_cell_index / MEAS_CELLS_BUFFER_SIZE) * MEAS_CELLS_BUFFER_SIZE;
            if (m_meas_start_index[i] + MEAS_CELLS_BUFFER_SIZE > m_n_pts_total) num_points = m_n_pts_total - m_meas_start_index[i];
          }
          if (is_update_meas_cells) {
            if (!is_mass_flux_component || (i == DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX && !is_mass_flux_read)) {
              SRI_VARIABLE_TYPE sri_var_type = m_sri_var_type_from_dgf_bseed_var_type[i];
              if (m_is_var_double[i]) {
                status = m_sampled_face_meas_file->get_variable(sri_var_type,
                                                                m_var_values_double[i],
                                                                num_points,
                                                                m_meas_start_index[i],
                                                                NULL);
              } else {
                status = m_sampled_face_meas_file->get_variable(sri_var_type,
                                                                m_var_values[i],
                                                                num_points,
                                                                m_meas_start_index[i],
                                                                NULL);
              }
              if (status != SRI_SUCCESS) {
                msg_internal_error("Error while reading %lu values of SRI variable type %d starting at index %lu "
                                   "from measurement file \"%s\": %s",
                                   num_points,
                                   sri_var_type,                           
                                   m_meas_start_index[i],
                                   m_sampled_face_meas_filename.c_str(),
                                   sri_error_string(status));
              }
            }
          }

          if (!is_mass_flux_component) {
            seed_from_meas_data.var_values[i] = (m_is_var_double[i])?
                                         m_var_values_double[i][meas_cell_index - m_meas_start_index[i]]
                                         : m_var_values[i][meas_cell_index - m_meas_start_index[i]];
          } else {
            seed_from_meas_data.var_values[i] = (m_is_var_double[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX])?
                                       m_var_values_double[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX][meas_cell_index - m_meas_start_index[i]]
                                       : m_var_values[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX][meas_cell_index - m_meas_start_index[i]];
            if (!is_mass_flux_read) is_mass_flux_read = TRUE;
          }
        }
      }

      if (is_mass_flux_read) {
        dFLOAT V_dot_n = seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_XVEL]*surfel_desc.s.normal[0]
                       + seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_YVEL]*surfel_desc.s.normal[1]
                       + seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_ZVEL]*surfel_desc.s.normal[2];
        if (fabs(V_dot_n) < 1e-7) {
          seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX] = 0;
          seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX] = 0;
          seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX] = 0;
        } else {
          seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX] *= seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_XVEL]/V_dot_n;
          seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX] *= seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_YVEL]/V_dot_n;
          seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX] *= seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_ZVEL]/V_dot_n;
        }
      }
    } else { // the physics surfel does not have a corresponding measurement surfel
      ccDOTIMES(i, DGF_N_BOUNDARY_SEED_VARS) {
        if (seed_from_meas_data.mask & (1 << i)) {
          switch(i) {
            case DGF_BOUNDARY_SEED_VAR_PRESSURE:
              seed_from_meas_data.var_values[i] = cdi_data.char_density * cdi_data.char_temp * cdi_data.lattice_gas_const;
              break;
            case DGF_BOUNDARY_SEED_VAR_XVEL:
            case DGF_BOUNDARY_SEED_VAR_YVEL:
            case DGF_BOUNDARY_SEED_VAR_ZVEL:
            case DGF_BOUNDARY_SEED_VAR_FLOW_DIR_X:
            case DGF_BOUNDARY_SEED_VAR_FLOW_DIR_Y:
            case DGF_BOUNDARY_SEED_VAR_FLOW_DIR_Z:
              seed_from_meas_data.var_values[i] = 0;
              break;
            case DGF_BOUNDARY_SEED_VAR_TEMP:
              seed_from_meas_data.var_values[i] = cdi_data.char_temp;
              break;
            case DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY:
              seed_from_meas_data.var_values[i] = -1;
              break;
            case DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION:
              seed_from_meas_data.var_values[i] = -1;
              break;
            case DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX:
            case DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX:
            case DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX:
              seed_from_meas_data.var_values[i] = 0;
              break;
          }
        }
      }
    }
  } // if (seed_from_meas_data.mask)

  if ((seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX)) ||
      (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX)) ||
      (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX))) {
    seed_from_meas_data.mask &= ~m_vel_mask;
  }
}

VOID sSEED_FROM_MEAS_DESC::compute_scale_factors()
{
  // always only scale down the velocity to conserve mass
  // if the area of the physics surfels is less than the area of the corresponding meas cell, keep the velocity 
  // unchanged. This may not conserve mass exactly, but it will avoid setting very high velocities in some inlet surfels
  ccDOTIMES(i, m_n_pts_total) {
    m_scale_factors[i] = m_surfel_areas[i]/m_scale_factors[i];
    if (m_scale_factors[i] > 1.0) m_scale_factors[i] = 1.0;
  }
}

VOID sSEED_FROM_MEAS_DESC::write_scale_factors(LGI_STREAM stream)
{
  lgi_write(stream, &m_n_pts_total, sizeof(m_n_pts_total));
  lgi_write(stream, m_scale_factors, m_n_pts_total*sizeof(m_scale_factors[0]));
}

VOID sSEED_FROM_MEAS_DESC::free_memory()
{
  ccDOTIMES(i, DGF_N_BOUNDARY_SEED_VARS) {
    if (!m_var_values[i]) delete [] m_var_values[i];
    if (!m_var_values_double[i]) delete [] m_var_values_double[i];
  }

  if (!m_scale_factors) delete [] m_scale_factors;
  if (!m_surfel_areas) delete [] m_scale_factors;
}

VOID sSEED_FROM_MEAS_DESC::fill_transient_boundary_seeding_data(sFLOAT &surfel_seed_data, asINT32 meas_cell_index, asINT32 frame_index, asINT32 i,
                                                                auINT32 var_mask, cBOOLEAN reset_meas_file_buffer)
{
  if(m_transient_boundary_seeding)
    m_sampled_face_meas_file->set_meas_frame(frame_index);

 if (var_mask) {

    // use the surfel's facet_id and the offset from the new CDI file to get the facet id in the sampled meas file
    // We make sure not to merge physics surfels across facet boundaries

    // use the map created during setup to calculate the meas cell index

    if (meas_cell_index != -1) { // if the physics surfel maps to a corresponding measurement surfel

      // sum the areas of all physics surfels that correspond to each meas cell
      // convert physics surfel area to meters^2. m_scale_factors is later normalized by the meas cell/facet area

      sriBOOL is_mass_flux_read = FALSE;
      SRI_STATUS status;
        if (var_mask & (1 << i)) {
          sriBOOL is_mass_flux_component = FALSE;
          if (i == DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX ||
              i == DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX ||
              i == DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX) {
            is_mass_flux_component = TRUE;
          }

          // read the measurements in chunks of size MEAS_CELLS_BUFFER_SIZE whose range contains meas_cell_index
          sriPOINT num_points = MEAS_CELLS_BUFFER_SIZE;

          // The variables from the measurement file may be read in a random order, depending on meas_cell_index.
          // get_variable() returns a contiguous chunk of data from a specified location in the meas file. We read
          // chunks the variables values from the meas file in chunks of size MEAS_CELLS_BUFFER_SIZE to limit the amount
          // of memory we use while reading the file. If the next meas cell index references the chunk we have already
          // read, we pick the value from there. Otherwise, we read another chunk starting from meas_cell_index
          sriBOOL  is_update_meas_cells = FALSE;
          if (meas_cell_index < m_meas_start_index[i] || meas_cell_index >= m_meas_start_index[i] + MEAS_CELLS_BUFFER_SIZE || reset_meas_file_buffer) {
            is_update_meas_cells = TRUE;
            m_meas_start_index[i] = (meas_cell_index / MEAS_CELLS_BUFFER_SIZE) * MEAS_CELLS_BUFFER_SIZE;
            if (m_meas_start_index[i] + MEAS_CELLS_BUFFER_SIZE > m_n_pts_total) num_points = m_n_pts_total - m_meas_start_index[i];
          }
          if (is_update_meas_cells) {
            if (!is_mass_flux_component || (i == DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX && !is_mass_flux_read)) {
              SRI_VARIABLE_TYPE sri_var_type = m_sri_var_type_from_dgf_bseed_var_type[i];
              if (m_is_var_double[i]) {
                status = m_sampled_face_meas_file->get_variable(sri_var_type,
                                                                m_var_values_double[i],
                                                                num_points,
                                                                m_meas_start_index[i],
                                                                NULL);
              } else {
                status = m_sampled_face_meas_file->get_variable(sri_var_type,
                                                                m_var_values[i],
                                                                num_points,
                                                                m_meas_start_index[i],
                                                                NULL);
              }
              if (status != SRI_SUCCESS) {
                msg_internal_error("Error while reading %lu values of SRI variable type %d starting at index %lu "
                                   "from measurement file \"%s\": %s",
                                   num_points,
                                   sri_var_type,                                   
                                   m_meas_start_index[i],
                                   m_sampled_face_meas_filename.c_str(),
                                   sri_error_string(status));
              }
            }
          }

          if (!is_mass_flux_component) {
            surfel_seed_data = (m_is_var_double[i])?
                                         m_var_values_double[i][meas_cell_index - m_meas_start_index[i]]
                                         : m_var_values[i][meas_cell_index - m_meas_start_index[i]];
          } else {
            surfel_seed_data = (m_is_var_double[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX])?
                                       m_var_values_double[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX][meas_cell_index - m_meas_start_index[i]]
                                       : m_var_values[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX][meas_cell_index - m_meas_start_index[i]];
            if (!is_mass_flux_read) is_mass_flux_read = TRUE;
          }
        }

//      if (is_mass_flux_read) {
//        dFLOAT V_dot_n = surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_XVEL]*surfel_desc.s.normal[0]
//                       + surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_YVEL]*surfel_desc.s.normal[1]
//                       + surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_ZVEL]*surfel_desc.s.normal[2];
//        if (fabs(V_dot_n) < 1e-7) {
//          surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX] = 0;
//          surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX] = 0;
//          surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX] = 0;
//        } else {
//          surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX] *= surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_XVEL]/V_dot_n;
//          surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX] *= surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_YVEL]/V_dot_n;
//          surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX] *= surfel_seed_data.var_values[DGF_BOUNDARY_SEED_VAR_ZVEL]/V_dot_n;
//        }
        //      }
    } else { // the physics surfel does not have a corresponding measurement surfel
      if (var_mask & (1 << i)) {
        switch(i) {
        case DGF_BOUNDARY_SEED_VAR_PRESSURE:
          surfel_seed_data = cdi_data.char_density * cdi_data.char_temp * cdi_data.lattice_gas_const;
          break;
        case DGF_BOUNDARY_SEED_VAR_XVEL:
        case DGF_BOUNDARY_SEED_VAR_YVEL:
        case DGF_BOUNDARY_SEED_VAR_ZVEL:
        case DGF_BOUNDARY_SEED_VAR_FLOW_DIR_X:
        case DGF_BOUNDARY_SEED_VAR_FLOW_DIR_Y:
        case DGF_BOUNDARY_SEED_VAR_FLOW_DIR_Z:
          surfel_seed_data = 0;
          break;
        case DGF_BOUNDARY_SEED_VAR_TEMP:
          surfel_seed_data = cdi_data.char_temp;
          break;
        case DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY:
          surfel_seed_data = -1;
          break;
        case DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION:
          surfel_seed_data = -1;
          break;
        case DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX:
        case DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX:
        case DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX:
          surfel_seed_data = 0;
          break;
        }
      }
    }
 } // if (var_mask)

  if ((var_mask & (1 << DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX)) ||
      (var_mask & (1 << DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX)) ||
      (var_mask & (1 << DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX))) {
    var_mask &= ~m_vel_mask;
  }
}

VOID sSEED_FROM_MEAS_DESC::fill_facet_id_to_meas_cell_map(SRI_FILE seed_file)
{
  if(m_is_octree_map)
    return;
  sriPART_INDEX *facet_ids = new sriPART_INDEX[m_n_pts_total];
  if (((SRI_SURFACE_FILE)m_sampled_face_meas_file)->are_facet_ids_present()) {
    SRI_STATUS status = ((SRI_SURFACE_FILE)m_sampled_face_meas_file)->read_facet_ids(facet_ids, m_n_pts_total, 0);
    if (status != SRI_SUCCESS) {
      msg_internal_error("Error while reading facet ids from file \"%s\": %s",
          m_sampled_face_meas_filename.c_str(),
          sri_error_string(status));
    }
  } else {
    msg_error("Facet IDs are not present in file \"%s\".", seed_file->name());
  }
  ccDOTIMES(i, m_n_pts_total) {
    auto it = m_facet_id_to_meas_cells_map.find(facet_ids[i]);
    if(it == m_facet_id_to_meas_cells_map.end())
      m_facet_id_to_meas_cells_map[facet_ids[i]] = std::vector<auINT32>();
    m_facet_id_to_meas_cells_map[facet_ids[i]].push_back(i);
  }
}

auINT32 sSEED_FROM_MEAS_DESC::nearest_meas_cell_to_surfel(std::vector<auINT32> &meas_cells, sFLOAT centroid[3])
{
  auINT32 mci = -1;
  if(m_is_octree_map)
    return -1;
  else
    mci = m_simple_map.nearest_meas_cell((SRI_SURFACE_FILE)m_sampled_face_meas_file, meas_cells, centroid);
  return mci;
}

auINT32 cSIMPLE_MAP::nearest_meas_cell(SRI_SURFACE_FILE face_meas_file, std::vector<auINT32> &meas_cell_indices,
                                        sFLOAT surfel_centroid[3])
{
  sFLOAT dist = vdot(surfel_centroid, surfel_centroid);
  sFLOAT diff[3];
  auINT32 mci = -1;
  auINT32 nth_buffer, index = -1;
  for(auto const &it : meas_cell_indices) {
    ccDOTIMES(i, N_SIMPLE_MAP_BUFFERS) {
      if(it <= (m_min_mci_in_buffer[i] + buffer_size) && it >= m_min_mci_in_buffer[i]) {
        index = it - m_min_mci_in_buffer[i];
        nth_buffer = i;
        break;

      } else if(i == 3) {
        reset_buffer(face_meas_file, it);
        index = 0;
        nth_buffer = m_buffer_index;
        increment_buffer_index();
      }
    }
    vsub(diff, m_buffer.meas_cell_from_buffer(nth_buffer, index), surfel_centroid);
    if(vdot(diff, diff) < dist)
      mci = it;
  }
  return mci;
}
VOID cSIMPLE_MAP::reset_buffer(SRI_SURFACE_FILE face_meas_file, auINT32 mci) {

  face_meas_file->get_centroids(m_buffer.meas_cell_from_buffer(m_buffer_index), buffer_size, mci);
  m_min_mci_in_buffer[m_buffer_index] = mci;

}

VOID cSIMPLE_MAP::increment_buffer_index() {
  m_buffer_index++;
  if(m_buffer_index > 3)
    m_buffer_index = 0;
}

VOID cSIMPLE_MAP::init_buffer(SRI_SURFACE_FILE face_meas_file) {
  sriFLOAT *centroid;
  buffer_size = (SIMPLE_MAP_BUFFER_SIZE > (face_meas_file->n_points())) ?
                face_meas_file->n_points() : SIMPLE_MAP_BUFFER_SIZE;
  dFLOAT length_scale = find_lat_scale(face_meas_file->file_params(), "LatticeLength");
  ccDOTIMES(i, N_SIMPLE_MAP_BUFFERS) {
    centroid = m_buffer.meas_cell_from_buffer(i);
    face_meas_file->get_centroids(centroid, buffer_size, i*buffer_size);
    m_min_mci_in_buffer[i] = i*buffer_size;
  }
}


template<int N, int BUFFER_SIZE>
sriFLOAT *cSEED_CACHE<N, BUFFER_SIZE>::meas_cell_from_buffer(asINT32 n, auINT32 mci)
{
  return m_meas_cell_buffer[n][mci];
}

VOID sSEED_FROM_MEAS_DESC::set_cdi_to_meas_file_length_scale_factor()
{
  m_cdi_to_meas_file_length_scale_factor = cdi_data.meters_per_cell / find_lat_scale(m_sampled_face_meas_file->file_params(), "LatticeLength");
}
