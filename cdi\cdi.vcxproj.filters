﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;cc</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx</Extensions>
    </Filter>
    <Filter Include="Dummy CPP File">
      <UniqueIdentifier>
      </UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="cdi_accessers.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cdi_get.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cdi_interface.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cdi_physics.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cdi_readwrite.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="dump_cdi.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="dump_cdi_lrf.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="test_cdi.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="undump_cdi.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ignore_me.cpp">
      <Filter>Dummy CPP File</Filter>
    </ClCompile>
    <ClCompile Include="cdi_io.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cdi_tempDepParms.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lexer.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="test_lexer.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pri_support.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="cdi_accessers.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cdi_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cdi_export.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cdi_get.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cdi_interface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cdi_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cdi_io.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cdi_physics.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cdi_readwrite.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lexer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pri_support.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cdi_tempDepParms.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>