/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

/*--------------------------------------------------------------------------*
 *
 * Autostop monitor support
 *--------------------------------------------------------------------------*/

#include "common.h"
#include "monitor.h"
#include "cp_info.h"
#include "jobctl.h"

const WALLCLOCK_TIME_SECS SECS_BETWEEN_FLUSHING_SIGNAL_FILE = 1;

// For a flow monitor, if wheelbase is used to calculate dimless vehicle moments,
// we need to scale the dimless value converted using char_length by a factor 
// (char_length/wheelbase).
template <class FLOAT_TYPE>
VOID sMONITOR::maybe_scale_vehicle_moments(FLOAT_TYPE& moment_value)
{
  if (m_type == eCDI_MNTR_TYPE::Flow) {
    FLOW_MONITOR flow_monitor = (FLOW_MONITOR)this;
    if (flow_monitor->m_use_wheelbase_for_dimless_vehicle_moments &&
       (sri_is_variable_vehicle_moment(flow_monitor->m_surface_var_type) ||
        sri_is_variable_vehicle_moment(flow_monitor->m_fluid_var_type))) {
      moment_value *= cdi_data.char_length/cp_info.sri_vehicle_defn->wheelbase; 
    }
  } 
}

// The vehicle moment units scale factors will be scaled by (wheelbase/char_length).  
template <class FLOAT_TYPE>
VOID sMONITOR::maybe_scale_vehicle_moments_units(FLOAT_TYPE& scale_factor)
{
  if (m_type == eCDI_MNTR_TYPE::Flow) {
    FLOW_MONITOR flow_monitor = (FLOW_MONITOR)this;
    if (flow_monitor->m_use_wheelbase_for_dimless_vehicle_moments &&
       (sri_is_variable_vehicle_moment(flow_monitor->m_surface_var_type) ||
        sri_is_variable_vehicle_moment(flow_monitor->m_fluid_var_type))) {
      scale_factor *= cp_info.sri_vehicle_defn->wheelbase/cdi_data.char_length; 
    }
  } 
}

// The desired accuracy value read from CDI file is in lattice units. Should convert it to
// monitor units (dimless for forces and moments, mks_degC for other variables).
VOID sMONITOR::maybe_update_desired_accuracy()
{
  if (m_msap.m_accuracyType != eMSA_ACCURACY_TYPE::MSA_ACCURACY_FRACTION_MEAN) { // desired accuracy uses value
    units_convert(cp_info.units_db, 
                  m_msap.m_desiredAccuracy,
                  m_use_var_diff_lattice_unit? m_var_diff_lattice_unit : m_var_lattice_unit,
                  m_use_var_diff_monitor_unit? m_var_diff_monitor_unit : m_var_monitor_unit,
                  &m_msap.m_desiredAccuracy);

    maybe_scale_vehicle_moments(m_msap.m_desiredAccuracy);
  }
  if (m_msap.m_subWindowRangeLimitViaFractionMidpoint == false) { // subwindow range via value or percentage of accuracy
    units_convert(cp_info.units_db, 
                  m_msap.m_stabilizationSubWindowRangeLimit,
                  m_use_var_diff_lattice_unit? m_var_diff_lattice_unit : m_var_lattice_unit,
                  m_use_var_diff_monitor_unit? m_var_diff_monitor_unit : m_var_monitor_unit,
                  &m_msap.m_stabilizationSubWindowRangeLimit);

    maybe_scale_vehicle_moments(m_msap.m_stabilizationSubWindowRangeLimit);
  }
  m_msa.ChangeParameters(m_msap);
}

// For classic autostop, variance gradient limit, creep limit and running average gradient limit  should be 
// converted from lattice units to monitor units.
VOID sMONITOR::maybe_update_classic_autostop_parameters()
{
  if (m_msap.m_useClassicAlgorithm) {
    // Unit for variance gradient limit is m_var_monitor_unit^2
    units_convert(cp_info.units_db,
                  m_msap.m_classicVarianceGradLimit,
                  m_use_var_diff_lattice_unit? m_var_diff_lattice_unit : m_var_lattice_unit,
                  m_use_var_diff_monitor_unit? m_var_diff_monitor_unit : m_var_monitor_unit,
                  &m_msap.m_classicVarianceGradLimit);
    units_convert(cp_info.units_db,
                  m_msap.m_classicVarianceGradLimit,
                  m_use_var_diff_lattice_unit? m_var_diff_lattice_unit : m_var_lattice_unit,
                  m_use_var_diff_monitor_unit? m_var_diff_monitor_unit : m_var_monitor_unit,
                  &m_msap.m_classicVarianceGradLimit);
  
    units_convert(cp_info.units_db,
                  m_msap.m_classicCreepLimit,
                  m_use_var_diff_lattice_unit? m_var_diff_lattice_unit : m_var_lattice_unit,
                  m_use_var_diff_monitor_unit? m_var_diff_monitor_unit : m_var_monitor_unit,
                  &m_msap.m_classicCreepLimit);
  
    units_convert(cp_info.units_db,
                  m_msap.m_classicRunningAvgGradLimit,
                  m_use_var_diff_lattice_unit? m_var_diff_lattice_unit : m_var_lattice_unit,
                  m_use_var_diff_monitor_unit? m_var_diff_monitor_unit : m_var_monitor_unit,
                  &m_msap.m_classicRunningAvgGradLimit);
    m_msa.ChangeParameters(m_msap);
  }
}

sFLOW_MONITOR::sFLOW_MONITOR(cSTRING name,
                  bool analyze_signal, 
                  bool analyze_convergence, 
                  bool end_sim_via_init_transient,
                  bool end_sim_if_converged,
                  SRI_VARIABLE_TYPE flow_var_type,
                  eCDI_MNTR_FLOW_VAR_SRC::Enum var_source, 
                  bool use_wheelbase_for_dimless_vehicle_moments,
                  asINT32 cdi_meas_window_index, 
                  cMONITOR_SIGNAL_ANALYZER_PARAMS p
                  ) :
             sMONITOR(name, analyze_signal, analyze_convergence, 
                      end_sim_via_init_transient, end_sim_if_converged, p), 
             m_var_source(var_source),
             m_use_wheelbase_for_dimless_vehicle_moments(use_wheelbase_for_dimless_vehicle_moments),
             m_cdi_meas_window_index(cdi_meas_window_index)
{ 
  m_type = eCDI_MNTR_TYPE::Flow;
  m_n_cp_meas_windows_written = 0;
  m_porous_data = 0.0;
  m_surface_data = 0.0;
  
  m_csys_index = 0;

  m_surface_var_type = SRI_VARIABLE_INVALID;
  m_fluid_var_type = SRI_VARIABLE_INVALID;

  if (flow_var_type < 0)
    msg_internal_error("monitor variable is INVALID!");
  
  switch(m_var_source)
  {
  case eCDI_MNTR_FLOW_VAR_SRC::SurfacePlusFanAndPorous:
    // If the source is SURFACE+FAN_AND_POROUS, flow_var_type is the porous variable,
    // and one needs to use the extrinsic var table to find the surface variable.
    m_fluid_var_type = flow_var_type;
    m_surface_var_type = sri_extrinsic_variable_porous_to_surface(m_fluid_var_type);
    break;
  case eCDI_MNTR_FLOW_VAR_SRC::Fluid:
  case eCDI_MNTR_FLOW_VAR_SRC::FanAndPorous:
    m_fluid_var_type = flow_var_type;
    break;
  case eCDI_MNTR_FLOW_VAR_SRC::Surface:
    m_surface_var_type = flow_var_type;
    break;
  default:
    msg_internal_error("Variable is missing for monitor \"%s\"", m_name);
    break;
  }

  if (var_is_force_or_moment(m_fluid_var_type, m_surface_var_type))
    m_is_var_force_or_moment = true;
 
  m_var_unit_class_name = sri_monitor_variable_unit_class_name(flow_var_type, (cp_info.n_dims == 2));
  
  // Find the lattice unit for the variable unit class
  // Flow monitor raw signals are always in lattice unit
  units_get_default_for_class(cp_info.units_db, "lattice", m_var_unit_class_name, &m_var_unit);
  m_var_lattice_unit = m_var_unit;
  if (strcmp(m_var_unit_class_name, "StaticPressure") == 0) {
    units_get_default_for_class(cp_info.units_db, "lattice", "DynamicPressure", &m_var_diff_lattice_unit);
    m_use_var_diff_lattice_unit = true; // Use DynamicPressure for variable difference like desired accuracy by value.
  }
  
#if USE_LATTICE_UNITS_FOR_FLOW_MONITORS
  cSTRING set_name = "lattice";
#else
  // Monitors will use mks_degC units for all variables except forces and moments
  cSTRING set_name = "mks_degC";
  if (var_is_force_or_moment(m_fluid_var_type, m_surface_var_type)) {
    set_name = "dimless";
  }
#endif
  units_get_default_for_class(cp_info.units_db, set_name, m_var_unit_class_name, &m_var_monitor_unit);

  if (strcmp(m_var_unit_class_name, "Temperature") == 0) {
    units_get_default_for_class(cp_info.units_db, "mks", "Temperature", &m_var_diff_monitor_unit);
    m_use_var_diff_monitor_unit = true; // Use temperature unit degK for diff type variables (like desired accuracy by value).
    // MSA should use degK internally, so need to set the offset -273.15 for msa.
    m_msap.m_signalOffset = -273.15;
    m_msa.ChangeParameters(m_msap);
  }

  maybe_update_desired_accuracy();
  maybe_update_classic_autostop_parameters();
}

sSOLID_MONITOR::sSOLID_MONITOR(cSTRING name,
                  bool analyze_signal, 
                  bool analyze_convergence, 
                  bool end_sim_via_init_transient,
                  bool end_sim_if_converged,
                  SRI_VARIABLE_TYPE solid_var_type,
                  eCDI_MNTR_FLOW_VAR_SRC::Enum var_source, 
                  asINT32 cdi_meas_window_index, 
                  cMONITOR_SIGNAL_ANALYZER_PARAMS p
                  ) :
             sMONITOR(name, analyze_signal, analyze_convergence, 
                      end_sim_via_init_transient, end_sim_if_converged, p), 
             m_var_source(var_source),
             m_cdi_meas_window_index(cdi_meas_window_index)
{ 
  m_type = eCDI_MNTR_TYPE::Solid;
  m_n_cp_meas_windows_written = 0;
  m_solid_data = 0.0;
  m_shell_data = 0.0;
  
  m_csys_index = 0;

  m_shell_var_type = SRI_VARIABLE_INVALID;
  m_solid_var_type = SRI_VARIABLE_INVALID;

  if (solid_var_type < 0)
    msg_internal_error("monitor variable is INVALID!");
  
  switch(m_var_source)
  {
  case eCDI_MNTR_FLOW_VAR_SRC::ShellPlusSolid:
    m_solid_var_type = solid_var_type;
    m_shell_var_type = (solid_var_type == SRI_VARIABLE_HEAT_GENERATION)? SRI_VARIABLE_HEAT_FLUX: solid_var_type;
    break;
  case eCDI_MNTR_FLOW_VAR_SRC::Solid:
    m_solid_var_type = solid_var_type;
    break;
  case eCDI_MNTR_FLOW_VAR_SRC::Shell:
    m_shell_var_type = solid_var_type;
    break;
  default:
    msg_internal_error("Variable is missing for monitor \"%s\"", m_name);
    break;
  }

  m_var_unit_class_name = sri_monitor_variable_unit_class_name(solid_var_type, (cp_info.n_dims == 2));
  
  // Find the lattice unit for the variable unit class
  // Solid monitor raw signals are always in lattice unit
  units_get_default_for_class(cp_info.units_db, "lattice", m_var_unit_class_name, &m_var_unit);
  m_var_lattice_unit = m_var_unit;
  
#if USE_LATTICE_UNITS_FOR_FLOW_MONITORS
  cSTRING set_name = "lattice";
#else
  // Monitors will use mks_degC units for all variables except forces and moments
  cSTRING set_name = "mks_degC";
#endif
  units_get_default_for_class(cp_info.units_db, set_name, m_var_unit_class_name, &m_var_monitor_unit);

  if (strcmp(m_var_unit_class_name, "Temperature") == 0) {
    units_get_default_for_class(cp_info.units_db, "mks", "Temperature", &m_var_diff_monitor_unit);
    m_use_var_diff_monitor_unit = true; // Use temperature unit degK for diff type variables (like desired accuracy by value).
    // MSA should use degK internally, so need to set the offset -273.15 for msa.
    m_msap.m_signalOffset = -273.15;
    m_msa.ChangeParameters(m_msap);
  }

  maybe_update_desired_accuracy();
  maybe_update_classic_autostop_parameters();
}

sPOWERTHERM_MONITOR::sPOWERTHERM_MONITOR(cSTRING name,
                        bool analyze_signal,
                        bool analyze_convergence,
                        bool end_sim_via_init_transient,
                        bool end_sim_if_converged,
                        asINT32 pt_model_index, 
                        cSTRING pt_part, 
                        eCDI_MNTR_PT_SIDE::Enum surface_sides, 
                        eCDI_MNTR_PT_VAR::Enum pt_var_type, 
                        cMONITOR_SIGNAL_ANALYZER_PARAMS p
                        ):
                        sMONITOR(name, analyze_signal, analyze_convergence,
                                 end_sim_via_init_transient, end_sim_if_converged, p), 
                        m_powertherm_model_index(pt_model_index), 
                        m_powertherm_part(pt_part), 
                        m_surface_sides(surface_sides), 
                        m_powertherm_var_type(pt_var_type)
{
  m_type = eCDI_MNTR_TYPE::PowerTHERM;
  switch (m_powertherm_var_type) {
    case eCDI_MNTR_PT_VAR::MeanTemp:
    case eCDI_MNTR_PT_VAR::MinTemp:
    case eCDI_MNTR_PT_VAR::MaxTemp:
      m_var_unit_class_name = "Temperature";
      break;
    case eCDI_MNTR_PT_VAR::MeanHTC:
    case eCDI_MNTR_PT_VAR::MinHTC:
    case eCDI_MNTR_PT_VAR::MaxHTC:
      m_var_unit_class_name = "HeatTransferCoeff";
      break;
    default:
      break;
  }
  // PowerTHERM monitor raw signals are always in mks units (temperature in degK)
  units_get_default_for_class(cp_info.units_db, "mks", m_var_unit_class_name, &m_var_unit);
  units_get_default_for_class(cp_info.units_db, "lattice", m_var_unit_class_name, &m_var_lattice_unit);
  units_get_default_for_class(cp_info.units_db, "mks_degC", m_var_unit_class_name, &m_var_monitor_unit);

  if (strcmp(m_var_unit_class_name, "Temperature") == 0) {
    units_get_default_for_class(cp_info.units_db, "mks", "Temperature", &m_var_diff_monitor_unit);
    m_use_var_diff_monitor_unit = true; // Use temperature unit degK for diff type variables (like desired accuracy by value).
    // MSA should use degK internally, so need to set the offset -273.15 for msa.
    m_msap.m_signalOffset = -273.15;
    m_msa.ChangeParameters(m_msap);
  }

  maybe_update_desired_accuracy();
  maybe_update_classic_autostop_parameters();
}

sHX_MONITOR::sHX_MONITOR(cSTRING name,
                bool analyze_signal,
                bool analyze_convergence,
                bool end_sim_via_init_transient,
                bool end_sim_if_converged,
                eCDI_MNTR_HX_VAR::Enum hx_var_type, 
                asINT32 hx_index, 
                cMONITOR_SIGNAL_ANALYZER_PARAMS p
                ) :
                sMONITOR(name, analyze_signal, analyze_convergence, 
                         end_sim_via_init_transient, end_sim_if_converged, p), 
                m_hx_var_type(hx_var_type), 
                m_hx_index(hx_index)
{
  m_type = eCDI_MNTR_TYPE::HeatExchanger;

  switch(m_hx_var_type) {
  case eCDI_MNTR_HX_VAR::CoolantEntryTemp:
  case eCDI_MNTR_HX_VAR::CoolantExitTemp:
  case eCDI_MNTR_HX_VAR::InletAirMeanTemp:
  case eCDI_MNTR_HX_VAR::OutletAirMeanTemp:
  case eCDI_MNTR_HX_VAR::EntryAirMeanTemp:
    m_var_unit_class_name = "Temperature";
    break;
  case eCDI_MNTR_HX_VAR::HeatRejected:
    m_var_unit_class_name = "Power";
    break;
  case eCDI_MNTR_HX_VAR::AirMassFlowRate:
  case eCDI_MNTR_HX_VAR::CoolantMassFlowRate:
    m_var_unit_class_name = "MassFlow";
    break;
  default:
    break;
  }
  // Heat exchanger monitor raw signals are always in mks_degC units
  units_get_default_for_class(cp_info.units_db, "mks_degC", m_var_unit_class_name, &m_var_unit);
  units_get_default_for_class(cp_info.units_db, "lattice", m_var_unit_class_name, &m_var_lattice_unit);
  m_var_monitor_unit = m_var_unit;

  if (strcmp(m_var_unit_class_name, "Temperature") == 0) {
    units_get_default_for_class(cp_info.units_db, "mks", "Temperature", &m_var_diff_monitor_unit);
    m_use_var_diff_monitor_unit = true; // Use temperature unit degK for diff type variables (like desired accuracy by value).
    // MSA should use degK internally, so need to set the offset -273.15 for msa.
    m_msap.m_signalOffset = -273.15;
    m_msa.ChangeParameters(m_msap);
  }

  if (m_hx_var_type == eCDI_MNTR_HX_VAR::HeatRejected) {
    if (units_parse_unit(cp_info.units_db, "kwatt", &m_var_unit) != UNITS_STATUS_OK)
      msg_internal_error("Power units kwatt is not recognized.");
    m_var_monitor_unit = m_var_unit;
  }

  maybe_update_desired_accuracy();
  maybe_update_classic_autostop_parameters();
}

// Always rewrite the signal output files when restoring from ckpt
VOID sMONITOR::append_and_analyze_signal(sFLOAT signal, TIMESTEP signal_timestep, 
                             TIMESTEP next_signal_timestep,
                             BOOLEAN write_monitor_data_file_p,
                             BOOLEAN convert_signal_unit_p,
                             BOOLEAN restoring_from_ckpt_p)  // If resuming from ckpt, need to check if we want to set the 
                                                             // monitor to not ending the simulation based on whether the 
                                                             // previous run is ended by monitors. See PR39927
{
 // Convert to monitor unit if necessary. Do not convert the unit if it 
  // is reading from a monitor ckpt file.
  if (convert_signal_unit_p) {
    dFLOAT new_signal_value;
    units_convert(cp_info.units_db, signal, m_var_unit, m_var_monitor_unit, &new_signal_value);
    signal = new_signal_value;

    maybe_scale_vehicle_moments(signal);
  }

  // If the monitor has not started (controlled by other monitors), append the signal to the monitor and 
  // do not push to msa until later when the monitor is started.
  if ( m_type != eCDI_MNTR_TYPE::PowerTHERM &&
       m_cdi_meas_win->start_time_via_monitors_p && 
       !m_cdi_meas_win->meas_started_p) {
    m_signal.push_back(signal);
    m_timesteps.push_back(signal_timestep);
    m_next_timesteps.push_back(next_signal_timestep);
#if DEBUG_MONITOR
    msg_print("Monitor %s is not started yet, push signal %e at timestep %d to monitor instead of msa.",
              m_name, signal, signal_timestep);
#endif
    return;
  } else {
    m_msa.SignalAppend(signal); 
#if DEBUG_MONITOR
    msg_print("Append signal %e to monitor %s at timestep %d", signal, m_name, signal_timestep);
#endif
    m_timesteps.push_back(signal_timestep);
    m_next_timesteps.push_back(next_signal_timestep);
  }

  // Write the header only when the first signal comes in. For monitors controlled by other monitors, 
  // write the header only when the monitor is just started.
  if ( m_type != eCDI_MNTR_TYPE::PowerTHERM &&
       m_cdi_meas_win->start_time_via_monitors_p) {
    // It is guarantted that m_cdi_meas_win->meas_started_p = true if the code reaches here.
    if (!m_header_written_p) {
      if (!m_msap.m_useClassicAlgorithm) {
        if (m_analyze_signal) {
          write_signal_file_header(TRUE);
          // Create symbol link from <monitor_name>.dat to <monitor_name>.it.dat
          CHARACTER base_name[PLATFORM_MAXPATHLEN];
          platform_get_file_base_name(m_signal_it_filename, base_name);
          symlink(base_name, m_signal_filename);
        } else {
          write_signal_file_header(FALSE);
        }
        m_header_written_p = true;
      } 
    }
  } else { 
    if (m_timesteps.size() == 1) {
      if (!m_msap.m_useClassicAlgorithm) {
        if (m_analyze_signal) {
          write_signal_file_header(TRUE);
          // Create symbol link from <monitor_name>.dat to <monitor_name>.it.dat
          CHARACTER base_name[PLATFORM_MAXPATHLEN];
          platform_get_file_base_name(m_signal_it_filename, base_name);
          symlink(base_name, m_signal_filename);
        } else {
          write_signal_file_header(FALSE);
        }
        m_header_written_p = true;
      }
    }
  }

  if (!m_msap.m_useClassicAlgorithm) { // Never write .it.dat file for classic autostop monitor
    // Write signal to .it.dat file
    if (write_monitor_data_file_p) {
      if (!m_end_init_transient_reached && m_analyze_signal) {// End of initial transient not detected yet, write to .it.dat file
        write_monitor_signal_to_file(TRUE, FALSE, FALSE, m_timesteps.size() - 1);
        WALLCLOCK_TIME_SECS time_secs = wallclock_time_secs();
        if (WALLCLOCK_TIME_DIFF(time_secs, m_last_flushing_time) >= SECS_BETWEEN_FLUSHING_SIGNAL_FILE) {
          fflush(m_signal_it_fout);
          m_last_flushing_time = time_secs;
        }
      } 
    }
  }

  // Update monitor status immediately
  // If using classic autostop algorithm, only update the status when writing the monitor data file.
  if (!m_msap.m_useClassicAlgorithm) {
    update_status(write_monitor_data_file_p, restoring_from_ckpt_p);

    // Write signal to .dat file
    if (write_monitor_data_file_p) {  
      asINT32 msa_time = m_timesteps.size() - 1;
      if (!m_analyze_signal)
        write_monitor_signal_to_file(FALSE, FALSE, FALSE, msa_time); 
      else if (m_end_init_transient_reached) { // End of initial transient detected, write to .dat file
        // If moving avg, write the signal first, then rewind and update the last moving avg
        if (m_is_moving_avg) {
          write_monitor_signal_to_file(FALSE, FALSE, FALSE, msa_time);
          asINT32 new_running_avg_size = m_msa.RunningAvgSize();
          if (new_running_avg_size > m_running_avg_size) {
            if (new_running_avg_size > (m_running_avg_size+1))
              msg_internal_error("New running average size cannot be bigger than (m_running_avg_size+1)!");
            asINT32 new_running_avg_index = new_running_avg_size - 1;
            write_monitor_signal_to_file(FALSE, FALSE, TRUE, new_running_avg_index);
            m_running_avg_size = new_running_avg_size;
          }
        } else { // cumulative running avg
          write_monitor_signal_to_file(FALSE, FALSE, TRUE, msa_time);
        }
      }

      WALLCLOCK_TIME_SECS time_secs = wallclock_time_secs();
      if (WALLCLOCK_TIME_DIFF(time_secs, m_last_flushing_time) >= SECS_BETWEEN_FLUSHING_SIGNAL_FILE) {
        fflush(m_signal_fout);
        m_last_flushing_time = time_secs;
      }
    }
  }
   
  // If using classic algorithm, write a separate signal data file for every (m_msap.m_classic_signal_processing_interval * monitor_period) timesteps.
  if (m_msap.m_useClassicAlgorithm && (m_timesteps.size() % m_classic_signal_processing_interval == 0)) {   
    m_msa.ComputeInitialTransient();
    if (m_msa.EndInitialTransient() >= 0)
      m_endInitialTransientTime = m_timesteps[m_msa.EndInitialTransient()];  

    bool end_sim_by_monitors = false;
    if (m_analyze_convergence) {
      m_msa.ComputeSignalConvergence();
      if (m_convergenceTime < 0 && m_msa.SignalConvergenceTime() >= 0) {  
        m_convergenceTime = m_timesteps[m_msa.SignalConvergenceTime()];
        // When the function is called when restoring from ckpt file, if the monitor is used to end the simulation and the previous run was ended by monitors, 
        // donot use it to stop the simulation after resuming and remove it from the cp_info.monitors_to_end_sim list so that the simulation will continue.
        if (restoring_from_ckpt_p && m_converged_and_ended_sim_before_ckpt) {
          m_end_sim_if_converged = FALSE;
          std::vector<MONITOR> &monitors_list = cp_info.monitors_to_end_sim;
#if DEBUG_MONITORS
          msg_print("List of monitors to end sim");
          ccDOTIMES(i, cp_info.monitors_to_end_sim.size()) {
            msg_print("%s", cp_info.monitors_to_end_sim[i]->m_name);
          }
#endif
          msg_warn("Monitor \"%s\" is converged before ckpt timestep and it stopped a previous simulation. "
                   "It will not be used to end the resumed simulation.", m_name);

          monitors_list.erase(std::remove(monitors_list.begin(), monitors_list.end(), this), 
                              monitors_list.end());  
        } else {
          end_sim_by_monitors = true;
        }
      } else if (m_convergenceTime >= 0 && m_msa.SignalConvergenceTime() < 0) {  //converge previously and diverge now
        m_convergenceTime = -1;
      } else { // still converged
        if (m_msa.SignalConvergenceTime() >= 0)
          m_convergenceTime = m_timesteps[m_msa.SignalConvergenceTime()];
      }
    }

    // Update the .monitor file 
    FILE *monitor_file = fopen(cp_info.monitor_log_filename.c_str(), "r+");
    if (!monitor_file)
      msg_warn("Unable to write initial_transient_end %.16g and convergence time %.16g to monitor log file \"%s\"\n", 
               get_frame_time_from_signal_time(m_endInitialTransientTime), 
               get_frame_time_from_signal_time(m_convergenceTime), 
               cp_info.monitor_log_filename.c_str());
    else {
      fseek(monitor_file, m_monitor_log_file_pos, SEEK_SET);
      fprintf(monitor_file, "\"%-.*s\"    %16.15g    %16.15g\n", 
              cp_info.longest_monitor_name_size, 
              m_name, 
              get_frame_time_from_signal_time(m_endInitialTransientTime), 
              get_frame_time_from_signal_time(m_convergenceTime));
      fflush(monitor_file);
    }

    // Write the signal data file
    sprintf(m_signal_filename, "%s/%s.%d.dat", monitors_dir_name, m_name, 
            (asINT32)get_frame_time_from_signal_time(signal_timestep));
    write_signal_file_header(FALSE, FALSE);

    ccDOTIMES(i, m_timesteps.size()) {
      // Always cumulative running avg
      if (m_analyze_convergence && m_endInitialTransientTime >= 0)
        fprintf(m_signal_fout, "%-16.15g    %12.9g    %12.9g    %1d    %06.2f\n", 
                get_frame_time_from_signal_time(m_timesteps[i]), 
                m_msa.Signal(i), 
                m_msa.RunningAvg(i), 
                m_msa.IsSignalConverged(i), 
                m_msa.FractionComplete(i)*100);
      else
        fprintf(m_signal_fout, "%-16.15g    %12.9g\n", 
                get_frame_time_from_signal_time(m_timesteps[i]), 
                m_msa.Signal(i));
    }
    fclose(m_signal_fout);

    if (end_sim_by_monitors)
      maybe_end_sim_by_monitors(m_timesteps.back());
  }
}

// Update monitor initial transient end time and convergence time
VOID sMONITOR::update_status(BOOLEAN write_monitor_data_file_p, BOOLEAN restoring_from_ckpt_p)
{
  if (!m_analyze_signal)
    return;

  m_msa.ComputeInitialTransient();

  if (cp_info.monitor_log_filename.empty())
    msg_internal_error("Monitor log file name is not specified!");
  FILE *monitor_file = fopen(cp_info.monitor_log_filename.c_str(), "r+");

  bool end_init_transient_reached = false;
  if (!m_end_init_transient_reached) {
    if (m_msap.m_isUserSpecifiedEndInitialTransient) { 
      if ((m_timesteps.size()-1) >= m_msap.m_userSpecifiedEndInitialTransient)
        end_init_transient_reached = true;
    } else { // End of initial transient detected automatically
      if (m_endInitialTransientTime < 0 && m_msa.EndInitialTransient() >= 0)
        end_init_transient_reached = true;
    }
  }

  // Check if the monitor detects the end of initial transient
  if (!m_end_init_transient_reached && end_init_transient_reached) {

    m_endInitialTransientTime = m_timesteps[m_msa.EndInitialTransient()];
    if (m_endInitialTransientDetectionTime < 0) // Only updated once when EIT is detected
      m_endInitialTransientDetectionTime = m_timesteps.back();
    if (getenv("EXA_SHOW_MONITOR_DIT"))
        msg_print("Monitor %s EIT: %d DIT: %d", m_name, m_endInitialTransientTime, m_timesteps.back());
#if DEBUG_MONITOR
    msg_print("Monitor %s EIT: %d EITDT: %d cp_info.time: %d", m_name, 
              m_endInitialTransientTime, m_timesteps.back(), cp_info.time);
#endif
   
    // It is possible that the convergence is found between EIT and EITDT, in that case we need to check convergence immediately
    // at EITDT
    if (m_analyze_convergence)
      m_msa.ComputeSignalConvergence(); 
    
    // Check if we should use moving average or cummulative running average
    m_is_moving_avg = !m_msa.IsRunningAvgCumulative();

    if (m_is_moving_avg) {
      msg_warn("The signal associated with monitor \"%s\" is monotonic. The end of its initial transient has "
               "been identified as timestep %.16g because a moving average of the signal first satisfies the "
               "specified convergence criteria over a stabilization window starting at %.16g. The convergence "
               "analysis after the initial transient will use a moving average (not a cumulative running average).",
               m_name, get_frame_time_from_signal_time(m_endInitialTransientTime), 
               get_frame_time_from_signal_time(m_endInitialTransientTime));
    }

    if (write_monitor_data_file_p) {
      // Create the .dat.tmp file and rename it to .dat file after rewritten
      write_signal_file_header(FALSE, m_is_moving_avg);
      // Write previous signals
      if (!m_signal_tmp_fout) {
  	    msg_error("Unable to open monitor signal tmp output file \"%s\" for appending, please check permissions in the run directory", m_signal_tmp_filename);
      }

      m_running_avg_size = m_msa.RunningAvgSize();
      ccDOTIMES(i, m_timesteps.size()-1) {
        //sriFLOAT signal = m_msa.Signal(i);
        if (i < m_running_avg_size && i >= m_msa.EndInitialTransient()) {
          write_monitor_signal_to_file(FALSE, TRUE, TRUE, i);
        } else {
          write_monitor_signal_to_file(FALSE, TRUE, FALSE, i);
        }
      }
      fclose(m_signal_tmp_fout);

      platform_rename_file(m_signal_tmp_filename, m_signal_filename);
      m_signal_fout = fopen(m_signal_filename, "r+");
      if (!m_signal_fout) {
  	    msg_error("Unable to open monitor signal output file \"%s\" for writing, please check permissions in the run directory", m_signal_filename);
      }
      fseek(m_signal_fout, 0, SEEK_END);

      // Write the end of initial transient in the .it.dat file
      if (!m_signal_it_fout) {
  	    msg_error("Unable to open monitor signal output file \"%s\" for writing, please check permissions in the run directory", m_signal_it_filename);
      }
      fseek(m_signal_it_fout, m_end_init_transient_it_pos, SEEK_SET);
      fprintf(m_signal_it_fout, "\n%-30s    %-16.15g\n", "end_init_transient", get_frame_time_from_signal_time(m_endInitialTransientTime));
      fseek(m_signal_it_fout, m_end_init_transient_detection_it_pos, SEEK_SET);
      fprintf(m_signal_it_fout, "\n%-30s    %d\n", "end_init_transient_detection", m_endInitialTransientDetectionTime);

      fclose(m_signal_it_fout);
    }
    // Update the monitor status in the monitor log file
    if (!monitor_file)
      msg_warn("Unable to write initial_transient_end,%.16g,\"%s\" to monitor log file \"%s\"\n",
               get_frame_time_from_signal_time(m_endInitialTransientTime), m_name, cp_info.monitor_log_filename.c_str());
    else {
      fseek(monitor_file, m_monitor_log_file_pos, SEEK_SET);
      fprintf(monitor_file, "\"%-.*s\"    %16.15g    %16.15g\n", cp_info.longest_monitor_name_size, m_name, 
              get_frame_time_from_signal_time(m_endInitialTransientTime), 
              get_frame_time_from_signal_time(m_convergenceTime));
    }

    maybe_start_meas_windows_via_monitor();
    maybe_start_emitters();
    maybe_start_wipers();
    maybe_find_sim_stoptime_via_monitor();

    m_end_init_transient_reached = end_init_transient_reached;
  }

  // Only check convergence if the end of initial transient is reached (either detected by the simulator, or the simulation has
  // passed the user-specified end of initial transient time).
  if (m_analyze_convergence && m_end_init_transient_reached)
    m_msa.ComputeSignalConvergence();

  // Check if the monitor has just converged
  if (m_convergenceTime < 0 && m_msa.SignalConvergenceTime() >= 0) {
    m_convergenceTime = m_timesteps[m_msa.SignalConvergenceTime()];

    // When the function is called when restoring from ckpt file, if the monitor is used to end the simulation and it is converged, donot use it to stop
    // the simulation after resuming and remove it from the cp_info.monitors_to_end_sim list so that the simulation will continue if the previous run is
    // ended by converged monitors.
    if (restoring_from_ckpt_p && m_converged_and_ended_sim_before_ckpt) {
      m_end_sim_if_converged = FALSE;
      std::vector<MONITOR> &monitors_list = cp_info.monitors_to_end_sim;
#if DEBUG_MONITORS
      msg_print("List of monitors to end sim");
      ccDOTIMES(i, monitors_list.size()) {
        msg_print("%s", monitors_list[i]->m_name);
      }
#endif
      msg_warn("Monitor \"%s\" is converged before ckpt timestep and it stopped a previous simulation. "
               "It will not be used to end the resumed simulation.", m_name);
      monitors_list.erase(std::remove(monitors_list.begin(), monitors_list.end(), this), 
                          monitors_list.end());  
    } else {
      if (m_end_sim_if_converged)
        maybe_end_sim_by_monitors(m_timesteps.back());
    }
    if (!monitor_file)
      msg_warn("Unable to write signal_convergence,%.16g,\"%s\" to monitor log file \"%s\"\n",
                get_frame_time_from_signal_time(m_timesteps.back()), m_name, cp_info.monitor_log_filename.c_str());
    else {
      fseek(monitor_file, m_monitor_log_file_pos, SEEK_SET);
      fprintf(monitor_file, "\"%-.*s\"    %16.15g    %16.15g\n", cp_info.longest_monitor_name_size, m_name, 
              get_frame_time_from_signal_time(m_endInitialTransientTime), 
              get_frame_time_from_signal_time(m_convergenceTime));
    }
  } else if (m_convergenceTime >=0 && m_msa.SignalConvergenceTime() < 0) {
  // Check if the monitor has changed from converged to not converged
    m_convergenceTime = -1;

    if (!monitor_file)
      msg_warn("Unable to write signal_no_convergence,%.16g,\"%s\" to monitor log file \"%s\"\n",
                get_frame_time_from_signal_time(m_timesteps.back()), m_name, cp_info.monitor_log_filename.c_str());
    else {
      fseek(monitor_file, m_monitor_log_file_pos, SEEK_SET);
      fprintf(monitor_file, "\"%-.*s\"    %16.15g    %16.15g\n", cp_info.longest_monitor_name_size, m_name, 
              get_frame_time_from_signal_time(m_endInitialTransientTime), 
              get_frame_time_from_signal_time(m_convergenceTime));
    }
  } else if (m_convergenceTime >= 0) { // monitor is still converged
    // If the current timestep is the last timestep where we checked if all autostop monitors are converged, we should check again here
    if (m_end_sim_if_converged && !cp_info.request_to_exit_by_monitor_convergence 
        && m_timesteps.back() == cp_info.last_timestep_to_check_autostop_monitors_converged) {
      maybe_end_sim_by_monitors(m_timesteps.back());
    }
  }

  fclose(monitor_file);
}

VOID sMONITOR::maybe_start_emitters()
{
  if (emitters_to_start.empty())
    return;
  ccDOTIMES(i, emitters_to_start.size()) 
  {
    EMITTER emitter = emitters_to_start[i];
    emitter->remove_control_monitor(this);
  }
}
VOID sMONITOR::maybe_start_wipers()
{
  if (wipers_to_start.empty())
    return;
  ccDOTIMES(i, wipers_to_start.size()) 
  {
    WIPER wiper = wipers_to_start[i];
    wiper->remove_control_monitor(this);
  }
}

// Only called when the end of init transient is found for the current monitor
VOID sMONITOR::maybe_start_meas_windows_via_monitor()
{
  if (cdi_meas_windows_to_start.empty())
    return;
  ccDOTIMES(i, cdi_meas_windows_to_start.size())
  {
    CDI_MEAS_WINDOW cdi_win = cdi_meas_windows_to_start[i];
    cdi_win->n_master_monitors_to_check--;
#if DEBUG_MONITOR
    msg_print("cdi_win %s n_master_monitors_to_check = %d", cdi_win->name, cdi_win->n_master_monitors_to_check);
#endif    
    if (cdi_win->n_master_monitors_to_check < 0)
      msg_internal_error("cdi_win->n_master_monitors_to_check can not be negative!");
    if (cdi_win->n_master_monitors_to_check == 0)  { // should start the meas now
      // Find the maximal end of initial transient time among all monitors
      TIMESTEP end_init_transient_time = 0;
      ccDOTIMES(j, cdi_win->master_monitors.size()) {
        MONITOR monitor = cp_info.monitors[cdi_win->master_monitors[j]];
        end_init_transient_time = MAX(end_init_transient_time, 
                                      get_frame_time_from_signal_time(monitor->m_endInitialTransientTime));
      }
#if DEBUG_MONITOR
      msg_print("start cdi meas window %s at timestep %d", cdi_win->name, end_init_transient_time);
#endif
      cdi_win->start_meas_windows(m_timesteps.back(), end_init_transient_time);
    }
  }
}

// Only called when the end of init transient is just detected.
VOID sMONITOR::maybe_find_sim_stoptime_via_monitor()
{
  if (!m_end_sim_via_init_transient)
    return;

  static sFLOAT greatest_end_init_transient_time = -1;
  sFLOAT frame_time = get_frame_time_from_signal_time(m_endInitialTransientTime);
  if (frame_time > greatest_end_init_transient_time)
    greatest_end_init_transient_time = frame_time;
  cp_info.n_monitors_to_check_sim_end_init_transient--;

  // All the monitors with m_end_sim_via_init_transient=TRUE have passed
  // the end of initial transient phase, find the sim end time now
  if (cp_info.sim_duration_via == eCDI_SIM_DURATION_VIA::AfterInitialTransient && 
      cp_info.n_monitors_to_check_sim_end_init_transient == 0) {
    TIMESTEP sim_end_time = MIN(greatest_end_init_transient_time + cp_info.duration_after_init_transient, cp_info.end_time);

#if DEBUG_MONITOR  
    msg_print("cp_info.n_monitors_to_check_sim_end_init_transient = %d", cp_info.n_monitors_to_check_sim_end_init_transient);
    msg_print("All init transient monitors ended initial transient at timestep %.16g. Stop the simulation at timestep %d",
              greatest_end_init_transient_time, sim_end_time);
#endif
    if (sim_end_time != cp_info.end_time) {
      // If this is resuming from ckpt and the previous simulation was stopped because of monitor+duration, do not stop now
      // if user speicifes some timestep in exaqsub (i.e. sim_args.num_timesteps > 0).
      //msg_print("sim_end_time %d cp_info.end_time %d cp_info.time %d is_ckpt_resume %d restart_time %d, sim_args.num_timesteps %d", 
      //          sim_end_time, cp_info.end_time, cp_info.time, cp_info.is_full_checkpoint_restore, cp_info.restart_time,
      //          sim_args.num_timesteps);
      if (cp_info.is_full_checkpoint_restore && (sim_end_time <= cp_info.restart_time) && (sim_args.num_timesteps > sim_end_time)) {
        return;
      }

      if (cp_info.monitors_to_find_sim_end_init_transient.size() == 1) {
        asINT32 monitor_index = cp_info.monitors_to_find_sim_end_init_transient[0];
        msg_print("Request to stop the simulation at timestep %d, which is the "
                  "sum of a user-specified duration (%d timesteps) and the " 
                  "end of initial transient of monitor \"%s\".", sim_end_time, cp_info.duration_after_init_transient, 
                  cp_info.monitors[monitor_index]->m_name);
      } else { // Multiple monitors
        msg_print_nnl("Request to stop the simulation at timestep %d, which is the "
                      "sum of a user-specified duration (%d timesteps) and the maximum " 
                      "end of initial transient of these monitors:", sim_end_time, cp_info.duration_after_init_transient);
        ccDOTIMES(i, cp_info.monitors_to_find_sim_end_init_transient.size()) {
          asINT32 monitor_index = cp_info.monitors_to_find_sim_end_init_transient[i];
          msg_print_nnl("    %s", cp_info.monitors[monitor_index]->m_name);
        }
        msg_print_nnl("\n");
      }
      if (sim_end_time < cp_info.end_time) {
        cp_info.request_to_exit_by_monitor = TRUE;
        cp_info.exit_time_requested_by_monitor = sim_end_time;
      } else { // If the current end time (either from exaqsub or from exasignal) is earlier, do not request to stop. PR41559
        msg_print_nnl("However the current end time is earlier, and the simulation will end at timestep %d.", cp_info.end_time);
      }
    }
  } 
}

static VOID parse_known_unit(cSTRING unit_str, UNITS_UNIT *unit)
{
  UNITS_STATUS status;
  if ((status = units_parse_unit(cp_info.units_db, unit_str, unit)) != UNITS_STATUS_OK)
    msg_internal_error("Unable to parse unit \"%s\": error code: %d", unit_str, status);
}

static VOID compute_units_conversion_coefficients(UNITS_UNIT unit1, UNITS_UNIT unit2,
						  dFLOAT *u_slope, dFLOAT *u_offset)
{
  UNITS_STATUS status;
  if ((status = units_conversion_coefficients(cp_info.units_db, unit1, unit2, u_slope, u_offset))
      != UNITS_STATUS_OK) {
    cSTRING name1 = units_unit_name(unit1);
    cSTRING name2 = units_unit_name(unit2);
    msg_internal_error("Bad unit when writing monitor signal file: \"%s\": Not convertible to \"%s\": error code %d", name1, name2, status);
  }
}  


VOID sMONITOR::write_signal_file_header(BOOLEAN is_it_file, BOOLEAN is_moving_avg)
{
  cSTRING signal_filename;
  if (is_it_file) {
    signal_filename = m_signal_it_filename;
  } else {
    // If not analyzing the monitor signal, write monitor.dat file directly
    // If using classic autostop, write monitor.dat directly
    if (m_analyze_signal && !m_msap.m_useClassicAlgorithm)
      signal_filename = m_signal_tmp_filename;
    else
      signal_filename = m_signal_filename;
  }

  FILE* fout = fopen(signal_filename,"w");
  if (!fout) {
	  msg_error("Unable to create monitor signal output file \"%s\", please check permissions in the run directory", signal_filename);
  }

  if (is_it_file) {
    m_signal_it_fout = fout;
  } else {
    if (m_analyze_signal && !m_msap.m_useClassicAlgorithm)
      m_signal_tmp_fout = fout;
    else  
      m_signal_fout = fout;
  }

  fprintf(fout, "%-16s    %d\n\n", "EXAMF",  1);
  fprintf(fout, "%-16s    %s\n", "monitor_name", m_name);
  
  switch (m_type) {
  case eCDI_MNTR_TYPE::Flow:
    {
      FLOW_MONITOR flow_monitor = (FLOW_MONITOR)this;
      fprintf(fout, "%-16s    %s\n", "monitor_type", "flow");
      
      switch(flow_monitor->m_var_source)
      {
      case eCDI_MNTR_FLOW_VAR_SRC::SurfacePlusFanAndPorous:
        fprintf(fout, "%-16s    %s\n", "variable", sri_extrinsic_variable_type_to_common_name(flow_monitor->m_fluid_var_type));
        break;
      case eCDI_MNTR_FLOW_VAR_SRC::Fluid:
      case eCDI_MNTR_FLOW_VAR_SRC::FanAndPorous:
        fprintf(fout, "%-16s    %s\n", "variable", sri_variable_type_to_long_name(flow_monitor->m_fluid_var_type));
        break;
      case eCDI_MNTR_FLOW_VAR_SRC::Surface:
        fprintf(fout, "%-16s    %s\n", "variable", sri_variable_type_to_long_name(flow_monitor->m_surface_var_type));
        break;
      default:
        msg_internal_error("Variable is missing for monitor \"%s\"", m_name);
        break;
      }
      
      fprintf(fout, "%-16s    %s\n\n", "meas_window", cp_info.cdi_meas_windows[flow_monitor->m_cdi_meas_window_index].name);
     
      break;
    }
  case eCDI_MNTR_TYPE::Solid:
    {
      SOLID_MONITOR solid_monitor = (SOLID_MONITOR)this;
      fprintf(fout, "%-16s    %s\n", "monitor_type", "solid");
      
      switch(solid_monitor->m_var_source)
      {
      case eCDI_MNTR_FLOW_VAR_SRC::ShellPlusSolid:
        fprintf(fout, "%-16s    %s\n", "variable", sri_extrinsic_variable_type_to_common_name(solid_monitor->m_solid_var_type));
        break;
      case eCDI_MNTR_FLOW_VAR_SRC::Solid:
        fprintf(fout, "%-16s    %s\n", "variable", sri_variable_type_to_long_name(solid_monitor->m_solid_var_type));
        break;
      case eCDI_MNTR_FLOW_VAR_SRC::Shell:
        fprintf(fout, "%-16s    %s\n", "variable", sri_variable_type_to_long_name(solid_monitor->m_shell_var_type));
        break;
      default:
        msg_internal_error("Variable is missing for monitor \"%s\"", m_name);
        break;
      }
      
      fprintf(fout, "%-16s    %s\n\n", "meas_window", cp_info.cdi_meas_windows[solid_monitor->m_cdi_meas_window_index].name);
     
      break;
    }
  case eCDI_MNTR_TYPE::HeatExchanger:
    {
      HX_MONITOR hx_monitor = (HX_MONITOR)this;
      fprintf(fout, "%-16s    %s\n", "monitor_type", "heat_exchanger");
      fprintf(fout, "%-16s    %s\n", "variable", eCDI_MNTR_HX_VAR::GetName(hx_monitor->m_hx_var_type).c_str());
      fprintf(fout, "%-16s    %d\n\n", "heat_exchanger", hx_monitor->m_hx_index);
      break;
    }
  case eCDI_MNTR_TYPE::PowerTHERM:
    {
      POWERTHERM_MONITOR pt_monitor = (POWERTHERM_MONITOR)this;
      fprintf(fout, "%-16s    %s\n", "monitor_type", "powertherm");
      fprintf(fout, "%-16s    %s\n", "variable", eCDI_MNTR_PT_VAR::GetName(pt_monitor->m_powertherm_var_type).c_str());
      fprintf(fout, "%-16s    %d\n", "coupled_model", pt_monitor->m_powertherm_model_index);
      fprintf(fout, "%-16s    %s\n\n", "part", pt_monitor->m_powertherm_part.c_str());
      break;
    }
  default:
    msg_warn("Invalid monitor type %d", m_type);
    break;
  }

  if (is_it_file || !m_analyze_convergence) {
    fprintf(fout, "%-16s    %d\n", "num_datasets", 2);
  } else {
    // MeanError is not applicable for classic autostop. It is also not available if 
    // msa is not computing confidence interval
    if (m_msap.m_useClassicAlgorithm || !m_msap.m_computeConfidenceInterval)
      fprintf(fout, "%-16s    %d\n", "num_datasets", 5);
    else
      fprintf(fout, "%-16s    %d\n", "num_datasets", 6);
  }

  fprintf(fout, "%-16s    %d\n\n", "time_dataset", 0);
 
  if (!is_it_file && m_analyze_convergence)
    fprintf(fout, "%-30s    %d\n\n", "is_signal_converged_dataset", 3);
  
  fprintf(fout, "%-16s    %-16s\n", "unit_class", "Time");
  fprintf(fout, "%-16s    %-16s    %-16s\n", "#unit", "scale_factor", "offset");
  
  UNITS_UNIT base_u, user_u;
  dFLOAT scale_factor, offset;
  asINT32 n_units;
  units_class_n_units(cp_info.units_db, "Time", &n_units);
  units_get_default_for_class(cp_info.units_db, "mks_degC", "Time", &base_u);
  cSTRING base_unit_name = units_unit_name(base_u);
  fprintf(fout, "%-16s    %-16.9f    %-16.9f\n", base_unit_name, 1.0, 0.0);

  ccDOTIMES(i, n_units) { 
    units_class_nth_unit(cp_info.units_db, "Time", i, &user_u);
    // Exclude base_u
    cSTRING user_unit_name = units_unit_name(user_u);
    if (strcmp(user_unit_name, base_unit_name) == 0)
      continue;
    
    compute_units_conversion_coefficients(user_u, base_u, &scale_factor, &offset);
    fprintf(fout, "%-16s    %-16.9f    %-16.9f\n", user_unit_name, scale_factor, offset);
  }

  fprintf(fout, "end_unit_class\n\n");
  
  fprintf(fout, "%-30s    %s\n", "unit_class", m_var_unit_class_name);
  fprintf(fout, "%-30s    %-16s    %-16s\n", "#unit", "scale_factor", "offset");
  units_class_n_units(cp_info.units_db, m_var_unit_class_name, &n_units);
  
  base_unit_name = units_unit_name(m_var_monitor_unit);
  fprintf(fout, "%-30s    %-16.9f    %-16.9f\n", base_unit_name, 1.0, 0.0);

  ccDOTIMES(i, n_units) {
    units_class_nth_unit(cp_info.units_db, m_var_unit_class_name, i, &user_u);
    cSTRING user_unit_name = units_unit_name(user_u);
    if (strcmp(user_unit_name, base_unit_name) == 0)
      continue;

    compute_units_conversion_coefficients(user_u, m_var_monitor_unit, &scale_factor, &offset);
    
    // If wheelbase is used to calculate dimless vehicle moments (roll, yaw, pitch), we need 
    // to scale the scale_factor by (wheelbase/char_length).
    maybe_scale_vehicle_moments_units(scale_factor);

    // Ignore erg/sec for Power units (PR43584)
    if (strcmp(user_unit_name, "erg/sec") == 0)
      continue;

    fprintf(fout, "%-30s    %-16.9f    %-16.9f\n", user_unit_name, scale_factor, offset);
  }
  fprintf(fout, "end_unit_class\n\n");
  
  // If the unit class is StaticPressure, should write DynamicsPressure units into the file
  if (strcmp(m_var_unit_class_name, "StaticPressure") == 0) {
    cSTRING unit_class_name = "DynamicPressure";
    fprintf(fout, "%-30s    %s\n", "unit_class", unit_class_name);
    fprintf(fout, "%-30s    %-16s    %-16s\n", "#unit", "scale_factor", "offset");
    units_class_n_units(cp_info.units_db, unit_class_name, &n_units);
    
    base_unit_name = units_unit_name(m_var_monitor_unit); // m_var_monitor_unit is Pa
    fprintf(fout, "%-30s    %-16.9f    %-16.9f\n", base_unit_name, 1.0, 0.0);
  
    ccDOTIMES(i, n_units) {
      units_class_nth_unit(cp_info.units_db, unit_class_name, i, &user_u);
      cSTRING user_unit_name = units_unit_name(user_u);
      if (strcmp(user_unit_name, base_unit_name) == 0)
        continue;
  
      compute_units_conversion_coefficients(user_u, m_var_monitor_unit, &scale_factor, &offset);
      
      fprintf(fout, "%-30s    %-16.9f    %-16.9f\n", user_unit_name, scale_factor, offset);
    }
    fprintf(fout, "end_unit_class\n\n");
  }
 
  // Write the rounded timing parameters for the meas window. For variable PowerTHERM coupling,
  // write the timing parameters of all coupling phases.
  fprintf(fout, "%-16s\n", "phases");
  fprintf(fout, "%-16s    %s\n", "#start", "period");

  // TODO: print all phases of heat exchanger once variable coupling is supported
  switch (m_type) {
  case eCDI_MNTR_TYPE::Flow:
  case eCDI_MNTR_TYPE::HeatExchanger:
    fprintf(fout, "%-16.15g    %-16d\n", get_frame_time_from_signal_time(m_timesteps[0]), m_cdi_meas_win->fluid_time_desc.period);
    break;
  case eCDI_MNTR_TYPE::PowerTHERM:
    ccDOTIMES(phase, m_cmdl->m_coupling_phase_descs.size()) {
      fprintf(fout, "%-16d    %-16d\n", m_cmdl->m_coupling_phase_descs[phase].start,
                                        m_cmdl->m_coupling_phase_descs[phase].period);
    }
    break;
  default:
    break;
  }
  
  fprintf(fout, "%-16s\n\n", "end_phases");

  fprintf(fout, "#dataset     %-16s    %-30s    %-30s\n", "unit class", "unit", "name");
  fprintf(fout, "dataset      %-16s    %-30s    %-30s\n", "Time", "timestep", "Time");
  fprintf(fout, "dataset      %-16s    %-30s    %-30s\n", m_var_unit_class_name,
          units_unit_name(m_var_monitor_unit), "Signal");
  
  if (!is_it_file && m_analyze_convergence) {
    if (is_moving_avg)
      fprintf(fout, "dataset      %-16s    %-30s    %-30s\n", m_var_unit_class_name,
            units_unit_name(m_var_monitor_unit), "Moving average");
    else
      fprintf(fout, "dataset      %-16s    %-30s    %-30s\n", m_var_unit_class_name,
            units_unit_name(m_var_monitor_unit), "Cumulative running average");
    fprintf(fout, "dataset      %-16s    %-30s    %-30s\n", "Bool", "-", "Is signal converged");
    fprintf(fout, "dataset      %-16s    %-30s    %-30s\n", "Dimensionless", "dimensionless", "Percent complete");
    
    if (!m_msap.m_useClassicAlgorithm && m_msap.m_computeConfidenceInterval) {
      if (strcmp(m_var_unit_class_name, "StaticPressure") == 0)
        fprintf(fout, "dataset      %-16s    %-30s    %-30s\n", "DynamicPressure",
                units_unit_name(m_var_monitor_unit), "Error estimate");
      else if (strcmp(m_var_unit_class_name, "Temperature") == 0)
        fprintf(fout, "dataset      %-16s    %-30s    %-30s\n", m_var_unit_class_name,
                units_unit_name(m_var_diff_monitor_unit), "Error estimate");
      else
        fprintf(fout, "dataset      %-16s    %-30s    %-30s\n", m_var_unit_class_name,
                units_unit_name(m_var_monitor_unit), "Error estimate");
    } 
  }

  if (is_it_file)
    m_end_init_transient_it_pos = ftell(fout);
  fprintf(fout, "\n%-30s    %-16.15g", "end_init_transient", get_frame_time_from_signal_time(m_endInitialTransientTime));
  if (is_it_file)
    m_end_init_transient_detection_it_pos = ftell(fout);
  fprintf(fout, "\n%-30s    %d\n\n", "end_init_transient_detection", m_endInitialTransientDetectionTime);

  fprintf(fout, "data\n\n");
  if (is_it_file || !m_analyze_convergence) {
    fprintf(fout, "%-16s    %-16s\n", "#Time", "Signal");
  } else {
   
    // Classic autostop monitor does not have "Error estimate" dataset
    if (m_msap.m_useClassicAlgorithm || !m_msap.m_computeConfidenceInterval) {
    if (is_moving_avg)
      fprintf(fout, "%-16s    %-16s    %-30s    %-24s    %-24s\n", "#Time", "Signal", "Moving average", "Is signal converged", "Percent complete");
    else  
      fprintf(fout, "%-16s    %-16s    %-30s    %-24s    %-24s\n", "#Time", "Signal", "Cumulative running average", "Is signal converged", "Percent complete");
    } else {
    if (is_moving_avg)
      fprintf(fout, "%-16s    %-16s    %-30s    %-24s    %-24s    %-24s\n", "#Time", "Signal", "Moving average", "Is signal converged", "Percent complete", "Error estimate");
    else  
      fprintf(fout, "%-16s    %-16s    %-30s    %-24s    %-24s    %-24s\n", "#Time", "Signal", "Cumulative running average", "Is signal converged", "Percent complete", "Error estimate");
    }
  }
}

TIMESTEP sMONITOR::find_next_signal_timestep(TIMESTEP signal_timestep)
{
  TIMESTEP next_signal_timestep;
  asINT32 index = 0;
  switch(m_type)
  {
  case eCDI_MNTR_TYPE::Flow:
  case eCDI_MNTR_TYPE::HeatExchanger:
    next_signal_timestep = signal_timestep + m_period;
    break;
  case eCDI_MNTR_TYPE::PowerTHERM:
    // Find the phase in which the last signal is located and use the corresponding time_desc
    ccDO_FROM_TO(i, 1, m_cmdl->n_coupling_phases-1)
    {
      // Signal timestep is the coupling model read in time
      if (signal_timestep < (m_cmdl->m_coupling_phase_descs[i].start + m_cmdl->m_coupling_phase_descs[i].delay))
        break;  
      index++;
    }
    
    next_signal_timestep = signal_timestep + m_cmdl->m_coupling_phase_descs[index].period;
    
    // Check if next_signal_timestep is crossing the phase boundary
    if (index < (m_cmdl->n_coupling_phases - 1)) {
      if (next_signal_timestep > m_cmdl->m_coupling_phase_descs[index+1].start)
        next_signal_timestep = m_cmdl->m_coupling_phase_descs[index+1].start + m_cmdl->m_coupling_phase_descs[index+1].delay;
    }
    break;
  default:  // Should never happen
    break;
  }
  return next_signal_timestep;
}


// Read signals up to last_timestep from the monitor data file
VOID sMONITOR::read_monitor_signals_from_file(TIMESTEP last_timestep)
{
  std::vector<MONITOR_SIGNAL> signals;
  // Check if the monitor data file is present
  FILE* signal_file;
  char* line = NULL;
  size_t len = 0;
  TIMESTEP timestep;
  sFLOAT signal;
  MONITOR_SIGNAL monitor_signal;

  if (m_msap.m_useClassicAlgorithm) { // For classic autostop monitor, the signals are stored in
                                      // file monitors/monitor_name.dat.ckpt for mme ckpt.

    CHARACTER signal_filename[PLATFORM_MAXPATHLEN];
    sprintf(signal_filename, "%s/%s.dat.ckpt", monitors_dir_name, m_name);
    if ((signal_file = fopen(signal_filename, "r"))) {
      if(getline(&line, &len, signal_file) != -1) {
        sscanf(line, "%d", &m_converged_and_ended_sim_before_ckpt);         
      }
      while(getline(&line, &len, signal_file) != -1) {
        sscanf(line, "%d    %g", &timestep, &signal);         
#if DEBUG_MONITOR
        msg_print("Read signal: %d    %f", timestep, signal);
#endif
        if (timestep > last_timestep)
          msg_internal_error("Signal timestep %d in file %s exceeds the ckpt restart time!", timestep, signal_filename);
 
        monitor_signal.signal = signal;
        monitor_signal.signal_timestep = timestep;
        monitor_signal.next_signal_timestep = -1;
        if (signals.size() > 0)
          signals.back().next_signal_timestep = timestep; // Update the next signal time of the previous signal 
        signals.push_back(monitor_signal);
      }
      // Find the next signal time for the last signal
      if (signals.size() > 0)
        signals.back().next_signal_timestep = find_next_signal_timestep(signals.back().signal_timestep);
      fclose(signal_file);  
    }
  } else { // For non-classic autostop monitors, obtain the signals from the monitor data files.
    if ((signal_file = fopen(m_signal_filename, "r"))) {
      BOOLEAN find_signal = FALSE;
      while (getline(&line, &len, signal_file) != -1) {
        if (strstr(line, "#Time") != NULL) {
          find_signal = TRUE;
          break;
        }
      }
      if (!find_signal)
        msg_warn("No signals found in the monitor file!");
      while (getline(&line, &len, signal_file) != -1) {
        sFLOAT frame_time;
        sscanf(line, "%f    %f", &frame_time, &signal);
        timestep = get_signal_time_from_frame_time(frame_time);
#if DEBUG_MONITOR
        msg_print("Read signal: %d    %f", timestep, signal);
#endif
        if (timestep > last_timestep)
          break;
        monitor_signal.signal = signal;
        monitor_signal.signal_timestep = timestep;
        monitor_signal.next_signal_timestep = -1;
        if (signals.size() > 0)
          signals.back().next_signal_timestep = timestep; // Update the next signal time of the previous signal 
        signals.push_back(monitor_signal);
      }
      // Find the next signal time for the last signal
      if (signals.size() > 0)
        signals.back().next_signal_timestep = find_next_signal_timestep(signals.back().signal_timestep);
      fclose(signal_file);  
    }
  }
  
  // Append signals to monitors
  ccDOTIMES(i, signals.size()) {
#if DEBUG_MONITOR
    msg_print("Append signal: %f %d %d", signals[i].signal, signals[i].signal_timestep, signals[i].next_signal_timestep);
#endif
    append_and_analyze_signal(signals[i].signal, 
                  signals[i].signal_timestep,
                  signals[i].next_signal_timestep,
                  TRUE,    // Write to monitor data files
                  FALSE,  // Do not convert the units when reading signals from file
                  TRUE);  // Restoring from mme ckpt file
  }
}

VOID sMONITOR::write_monitor_signal_to_file(BOOLEAN is_it_file, 
                                    BOOLEAN is_tmp_file,
                                    BOOLEAN is_running_avg_available,
                                    asINT32 signal_index)
{
  FILE *signal_file;
  if (is_it_file) {
    signal_file = m_signal_it_fout;
  } else if (is_tmp_file) {
    signal_file = m_signal_tmp_fout;
  } else {
    signal_file = m_signal_fout;
  }
  
  if (!signal_file)
    msg_internal_error("Unable to open signal file of monitor \"%s\" for appending.", m_name);

  if (signal_index >= m_timesteps.size())
    msg_internal_error("The %d-th signal is not available yet!", signal_index);

  sFLOAT signal = m_msa.Signal(signal_index);
  TIMESTEP signal_time = m_timesteps[signal_index];

  // If not analyzing signal convergence, the extra datasets are not applicable
  if (is_it_file || !m_analyze_convergence) {
    fprintf(signal_file, "%-16.15g    %12.9g\n", get_frame_time_from_signal_time(signal_time), signal);
  } else { // .tmp.dat or .dat file
    BOOLEAN is_new_signal = TRUE;
    if (signal_index < m_signal_file_pos.size()) {// Signal already written, rewind and rewrite
      fseek(signal_file, m_signal_file_pos[signal_index], SEEK_SET);
      is_new_signal = FALSE;
    }
    if (is_new_signal && !is_it_file) {
      m_signal_file_pos.push_back(ftell(signal_file));
    }

    // MeanError is not applicable if msa is not computing confidence interval
    if (!m_msap.m_computeConfidenceInterval) {
      if (is_running_avg_available)
        fprintf(signal_file, "%-16.15g    %12.9g    %12.9g    %1d    %06.2f\n", get_frame_time_from_signal_time(signal_time), signal, m_msa.RunningAvg(signal_index), m_msa.IsSignalConverged(signal_index), m_msa.FractionComplete(signal_index)*100);
      else
        fprintf(signal_file, "%-16.15g    %12.9g    %12s    %1s    %6s\n", get_frame_time_from_signal_time(signal_time), signal, "?", "?", "?"); 
    } else {

      if (is_running_avg_available)
        fprintf(signal_file, "%-16.15g    %12.9g    %12.9g    %1d    %06.2f    %12.9g\n", get_frame_time_from_signal_time(signal_time), signal, m_msa.RunningAvg(signal_index), m_msa.IsSignalConverged(signal_index), m_msa.FractionComplete(signal_index)*100, m_msa.MeanError(signal_index, signal_index));
      else
        fprintf(signal_file, "%-16.15g    %12.9g    %12s    %1s    %6s    %12s\n", get_frame_time_from_signal_time(signal_time), signal, "?", "?", "?", "?"); 
 
    }
  }
}


// If one or more monitors are selected to end the simulation after converged,
// we should check if all of these monitors have converged.
VOID maybe_end_sim_by_monitors(TIMESTEP current_timestep)
{
  if (cp_info.monitors_to_end_sim.size() <= 0)  
    return;

  BOOLEAN all_monitors_converged = TRUE;
  ccDOTIMES(i, cp_info.monitors.size())
  {
    MONITOR monitor = cp_info.monitors[i];
    if (monitor->m_end_sim_if_converged) {
      // Check if the most recent monitor status is converged
      asINT32 last_signal = monitor->m_timesteps.size();
      if (last_signal == 0) // No signal yet
        return;
      TIMESTEP last_signal_timestep = monitor->m_timesteps[last_signal - 1];
      
      TIMESTEP next_signal_timestep;
      // For classic autostop, should use the next status update time
      if (monitor->m_msap.m_useClassicAlgorithm)
        next_signal_timestep = monitor->m_timesteps[last_signal - 1] + 
          monitor->m_period * monitor->m_classic_signal_processing_interval;
      else
        next_signal_timestep = monitor->m_next_timesteps[last_signal - 1];
      
      // Some autostop monitors have not updated their status yet at this timestep.
      // This function maybe_end_sim_by_monitors() will be called later when those monitors receive the signal.
      if (next_signal_timestep == current_timestep) {
        cp_info.last_timestep_to_check_autostop_monitors_converged = current_timestep;
        return;
      }
      
      // At least one monitor is not converged
      if (monitor->m_convergenceTime < 0) {
        all_monitors_converged = FALSE;
        break;
      }
    }
  }
  if (all_monitors_converged) {
#if DEBUG_MONITOR
    msg_print("All monitors which are used to stop the simulation converged. Stop the simulation at timestep %d", cp_info.time);
#endif

    // If the current run is ended by monitors, all the subsequent resumed runs from ckpt will not used
    // these monitors to end the simulation.
    ccDOTIMES(i, cp_info.monitors_to_end_sim.size()) {
      cp_info.monitors_to_end_sim[i]->m_converged_and_ended_sim_before_ckpt = TRUE;
    }

    if (cp_info.monitors_to_end_sim.size() == 1) {
      msg_print("Request to stop the simulation at timestep %d because the signal associated "
                "with monitor \"%s\" is converged.", current_timestep,  
                cp_info.monitors_to_end_sim[0]->m_name);
    } else { // Multiple monitors
      msg_print_nnl("Request to stop the simulation at timestep %d because the signals associated "
                    "with the following monitors are all converged:", current_timestep);
      ccDOTIMES(i, cp_info.monitors_to_end_sim.size()) {
        msg_print_nnl("    %s", cp_info.monitors_to_end_sim[i]->m_name);
      }
      msg_print_nnl("\n");
    }
    cp_info.request_to_exit_by_monitor = TRUE;
    cp_info.request_to_exit_by_monitor_convergence = TRUE;
  }
}
