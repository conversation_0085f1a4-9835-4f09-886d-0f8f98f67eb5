/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#if SURF_COUP
#include "common.h"
#include "coupling_model.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "jobctl.h"
#include <string>
#include <algorithm>

#include "monitor.h"

VOID dump_tpi_messages(cTHIRD_PARTY_INTERFACE *tpi, cSTRING model_name, 
    BOOLEAN unique_messages_only_p)
{
  asINT32 n = tpi->NumMessages();
  ccDOTIMES(i, n) {
    cTPI_MESSAGE const *tpi_msg = tpi->GetMessage(i);
    cSTRING text = tpi_msg->GetText();
    BOOLEAN output_msg_p = TRUE;
    if (unique_messages_only_p) {
      ccDOTIMES(j,i) {
        if (!strcmp(text,tpi->GetMessage(j)->GetText())) {
          output_msg_p = FALSE;
          break;
        }
      }
    }

    if (!output_msg_p)
      continue;

    switch (tpi_msg->GetType()) {
    case TPI_WARNING_MESSAGE:
      msg_warn("%s model \"%s\": %s", tpi->GetAppName(), model_name, text);
      break;
    case TPI_ERROR_MESSAGE:
      msg_error("%s model \"%s\": %s", tpi->GetAppName(), model_name, text);
      break;
    default:
      msg_print("%s model \"%s\": %s", tpi->GetAppName(), model_name, text);
      break;
    }
  }
  tpi->ClearMessages();
}

#define ucmd(cmd)						  \
{								  \
  if ((units_status = (cmd)) != UNITS_STATUS_OK) {              \
    msg_error("UNITS failure code: %d\n",units_status);         \
    return;                                                     \
  }                                                             \
}

BOOLEAN is_file_dir_direct_descendant_of_rundir(cSTRING file_path)
{
  CHARACTER resolved_pathname[PLATFORM_MAXPATHLEN];
  CHARACTER run_dir[PLATFORM_MAXPATHLEN];
  memset(resolved_pathname,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
  memset(run_dir,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));

  if (!file_path) { 
    return FALSE;
  }

  // translate the supplied model/results filename path into an absolute
  // pathname
  if (!platform_resolve_pathname(file_path,resolved_pathname) ||
      !platform_get_cwd(run_dir,PLATFORM_MAXPATHLEN-1)) {    
    return FALSE;
  }

  STRING sr = run_dir, sp = resolved_pathname;
  // walk over the two absolute paths and compare
  asINT32 i, n = strlen(run_dir);
  for (i = 0; i < n; i++) {
    if (*sr++ != *sp++) {
      return FALSE;
    }
  }

  // if rundir is prefix of path and path is the direct sub-dir in rundir, return TRUE
  if (*sp == PLATFORM_PATHSEP[0])
    sp++;
  else
    return FALSE;

  // the remaining string looks like "dir_1/dir_2/.../test.tdf"
  if ((sp = strchr(sp, PLATFORM_PATHSEP[0]))) {  
    sp++;
    if (strchr(sp, PLATFORM_PATHSEP[0]))
      return FALSE;
    else {
      return TRUE;  // path is directly sub-dir of rundir
    }
  } else
    return FALSE; // path == rundir
}

eMODEL_LOCATION locate_coupling_file(STRING coupling_filename, 
                                     cSTRING abs_coupling_filename, 
                                     cSTRING model_subdir, // "powertherm", "amesim", ...
                                     cSTRING search_path,
                                     STRING fullname) // result of this fcn
{
  // order of searching for the model file is 
  // a) search path specified on the exaqsub command line
  // b) relative path specified in the CDI file 
  // c) absolute path specified in the CDI file 
  // d) run directory 
  // e) powertherm subdirectory 
  cSTRING model_dir;
  CHARACTER base_name[PLATFORM_MAXPATHLEN];
  CHARACTER run_dir[PLATFORM_MAXPATHLEN];

  if (!platform_get_cwd(run_dir,PLATFORM_MAXPATHLEN-1)) {
    return MODEL_LOCATION_INVALID;
  }

  // a) first check the search path if provided
  if (search_path) {
    // get the basename of the coupling file
    if (NULL == platform_get_file_base_name(coupling_filename,base_name)) {
      return MODEL_LOCATION_INVALID;
    }
    asINT32 basename_len = strlen(base_name);
    while (1) {
      asINT32 count = strcspn(search_path, PLATFORM_SRCH_PATHSEP);
      if ((count > 0) && ((count + basename_len) < FILENAME_MAX)) {
        strncpy(fullname, search_path, count);
        strncpy(fullname + count, PLATFORM_PATHSEP, 1);
        strcpy(fullname + count + 1, base_name);

        if (platform_file_present(fullname)) {
          return MODEL_LOCATION_SEARCH_PATH;
        }
      }
      if (search_path[count] == '\0')
        break;
      search_path += count + 1;
    }
  }

  // b) Check supplied relative path in CDI next
  // relative path could also be an absolute path 
  if (platform_file_present(coupling_filename)) {
    strcpy(fullname,coupling_filename);
    return MODEL_LOCATION_CDI_RELATIVE;
  }

  // c) Check supplied absolute path in CDI next
  if (abs_coupling_filename && platform_file_present(abs_coupling_filename)) {
    strcpy(fullname,abs_coupling_filename);
    return MODEL_LOCATION_CDI_ABSOLUTE;
  }

  // d) Check the run directory next
  if (NULL != platform_get_file_base_name(coupling_filename,base_name)) {
    if (platform_file_present(base_name)) {
      strcpy(fullname,base_name);
      return MODEL_LOCATION_RUNDIR;
    }
  } else {
    return MODEL_LOCATION_INVALID;
  }

  // e) Check the powertherm/amesim sub-directory next
  if (NULL != platform_get_file_base_name(coupling_filename,base_name)) {
    sprintf(fullname,"%s/%s",model_subdir,base_name);
    if (platform_file_present(fullname)) {
      return MODEL_LOCATION_MODEL_SUBDIR;
    }
  } else {
    return MODEL_LOCATION_INVALID;
  }

  return MODEL_LOCATION_INVALID;
}

static VOID make_coupling_model_dir(cSTRING model_dir_name)
{
  // make a powertherm directory if it does not exist 
  struct stat buf;
  if (platform_file_present(model_dir_name)) {
    if (-1 == stat(model_dir_name,&buf)) {
      msg_error("Error while creating \"%s\" subdirectory: %s", model_dir_name, strerror(errno));
    }
    if (!S_ISDIR(buf.st_mode)) {
      msg_error("Cannot create directory \"%s\" for surface coupling files. A file by that name already exists.",model_dir_name);
    }
  } else {
    if (!platform_createdir(model_dir_name)) {
      msg_error("Unable to create \"%s\" subdirectory for surface coupling files.", model_dir_name);
    }
  } 
}

VOID compose_coupling_file_current_name(cSTRING from_file,      // location of original file
          cSTRING model_dir_name, // NULL if not stored in model dir
          STRING  tpi_file)       // result: location of _cur file
{
  CHARACTER base_file_name[PLATFORM_MAXPATHLEN];
  CHARACTER current_fname[PLATFORM_MAXPATHLEN];
  memset(current_fname,'\0',PLATFORM_MAXPATHLEN*sizeof(char));
  memset(base_file_name,'\0',PLATFORM_MAXPATHLEN*sizeof(char));

  // find last occurrence of .
  char *cptr = (char *) strrchr(from_file,'.'); 
  if (!platform_get_file_base_name(from_file,base_file_name)) {
    msg_internal_error("Cannot obtain basename of surface coupling file %s",from_file);
  }
  // model_dir_name only defined if the file is not in a subdir of the run directory
  if (model_dir_name) {
    strcpy(current_fname,model_dir_name);
    strcat(current_fname,"/");
  } 
  if (cptr) {
    if (model_dir_name) {
      strncat(current_fname, base_file_name, strlen(base_file_name)-strlen(cptr));
    } else {
      strncpy(current_fname, from_file, strlen(from_file)-strlen(cptr));
    }
    strcat(current_fname, "_cur");
    strcat(current_fname, cptr);
  } else {
    if (model_dir_name) {
      strcat(current_fname, base_file_name);
    } else {
      strcpy(current_fname, from_file);
    }
    strcat(current_fname, "_cur");
  }

  strcpy(tpi_file, current_fname);
}

STRING replace_coupling_file_current_suffix(cSTRING current_filename, cSTRING suffix, 
               char result_filename[PLATFORM_MAXPATHLEN])
{
  // find occurrence of _cur, and replace with number 001 etc 
  cSTRING uptr = rindex(current_filename, '_'); 
  if (!uptr) {
    msg_internal_error("Surface coupling model file \"%s\" lacks \"_cur\" substring.",
                       current_filename);
  } 
  cSTRING cur_ptr = strstr(uptr,"_cur"); 
  if (!cur_ptr) {
    msg_internal_error("Surface coupling model file \"%s\" lacks \"_cur\" substring.",
                       current_filename);
  } 

  cur_ptr++;
  strncpy(result_filename, current_filename, cur_ptr - current_filename);

  // overwrite the _cur substring with suffix
  strcpy(result_filename + (cur_ptr - current_filename), suffix);
  strcat(result_filename + (cur_ptr - current_filename), current_filename + (cur_ptr - current_filename) + 3);

  return result_filename;
}

static VOID remove_coupling_file_current_suffix(cSTRING current_filename, 
            char result_filename[PLATFORM_MAXPATHLEN])
{
  // find occurrence of _cur, and delete it 
  strcpy(result_filename, current_filename);
  STRING uptr = rindex(result_filename, '_'); 
  if (!uptr) {
    msg_internal_error("Surface coupling model file \"%s\" lacks \"_cur\" substring.",
                       current_filename);
  } 
  STRING cur_ptr = strstr(uptr,"_cur"); 
  if (!cur_ptr) {
    msg_internal_error("Surface coupling model file \"%s\" lacks \"_cur\" substring.",
                       current_filename);
  } 
  // delete the _cur substring
  STRING s = cur_ptr + 4;
  while (*s != '\0') {
    *cur_ptr++ = *s++;
  }
  *cur_ptr = '\0';
}

static BOOLEAN restore_ckpt_coupling_file(cSTRING current_filename,
            BOOLEAN is_full_checkpoint)
{
  CHARACTER ckpt_1_filename[PLATFORM_MAXPATHLEN];
  CHARACTER cmd[PLATFORM_MAXPATHLEN];

  replace_coupling_file_current_suffix(current_filename, 
               is_full_checkpoint ? "full_ckpt" : "mme_ckpt",
               ckpt_1_filename);

  if (!platform_file_present(ckpt_1_filename))
    return FALSE;

  if (!platform_copy_file(ckpt_1_filename, current_filename)) {
    msg_warn("Cannot copy file \"%s\" to \"%s\" at checkpoint restore", ckpt_1_filename, current_filename);
  } else {
    msg_print("Model file \"%s\" copied to \"%s\" at checkpoint restore.", ckpt_1_filename, current_filename);
  }

  return TRUE;
}


static VOID copy_current_results_to_ckpt_2(cSTRING results_filename, cSTRING ckpt_filename, TIMESTEP ckpt_timestep)
{
  if (platform_copy_file(results_filename, ckpt_filename))
    msg_print("Copied \"%s\" to \"%s\" for checkpoint at timestep %d", 
        results_filename, ckpt_filename, ckpt_timestep);
  else
    msg_warn("Cannot copy \"%s\" to \"%s\" for checkpoint at timestep %d", 
       results_filename, ckpt_filename, ckpt_timestep);
}

VOID ckpt_surface_coupling_models(TIMESTEP ckpt_timestep, BOOLEAN is_full_checkpoint) 
{
  // If we need to *copy* <model>_cur.tdf to <model>_full_ckpt.tdf and there is a 
  // job still running for this model, we need to wait for the job to complete. 
  // 
  // This is only an issue if the read_time of a coupling model is the same
  // as ckpt_timestep, and only if cp_info.ptherm_only_keep_orig_model is true,
  // because if cp_info.ptherm_only_keep_orig_model is false, we make symbolic
  // links.
  //
  // Fortunately, the sequencing in the SPs ensures this behavior. The SPs service 
  // events  (including reading surface coupling results) before they write checkpoints.
  // For the last timestep, however, the SPs don't wait for surface coupling results 
  // to be transmitted from the CP. Hence we need to explicitly wait.
  //
  BOOLEAN only_keep_orig_model = cp_info.ptherm_only_keep_orig_model;
  if (only_keep_orig_model && ckpt_timestep == cp_info.end_time)
    maybe_wait_for_surface_coupling_jobs();

  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
  ccDOTIMES(i,cp_info.n_surface_couplings) {
    // If this is a one-way coupling (PF->app), skip it
    if (!coupling_model_desc->couple_during_sim_p)
      continue;

    // Don't write the ckpt_1 file if results from a run of the coupled model are not yet in use.
    // Remember that the CP may read results from a coupled model long before the results begin to
    // be used in simulation on the SPs. However, if we are not writing a checkpoint file, we need
    // to ensure that any old checkpoint files are deleted.
    BOOLEAN latest_run_in_use_before_ckpt = coupling_model_desc->last_run_read_time <= ckpt_timestep;
    BOOLEAN create_ckpt_1 = (coupling_model_desc->n_runs_launched > 1
                             || (coupling_model_desc->n_runs_launched == 1
                             && latest_run_in_use_before_ckpt));
    asINT32 ckpt_1_backup_file_num = (latest_run_in_use_before_ckpt
                                      ? coupling_model_desc->n_runs_launched - 1
                                      : coupling_model_desc->n_runs_launched - 2);

    char suffix[10];
    char src_filename[PLATFORM_MAXPATHLEN];
    char ckpt_filename[PLATFORM_MAXPATHLEN];
    char base_filename[PLATFORM_MAXPATHLEN];
    cSTRING results_filename = coupling_model_desc->results_filename;

    replace_coupling_file_current_suffix(results_filename, 
            is_full_checkpoint ? "full_ckpt" : "mme_ckpt", 
            ckpt_filename);
    remove(ckpt_filename);
    platform_get_file_base_name(results_filename, base_filename);
    if (create_ckpt_1) {
      if (!only_keep_orig_model) {
        sprintf(suffix, "%03d", ckpt_1_backup_file_num);
        replace_coupling_file_current_suffix(base_filename, suffix, src_filename);
        if (symlink(src_filename, ckpt_filename) == -1)	
          msg_warn("Could not create a symbolic link from \"%s\" to \"%s\" for checkpoint at timestep %d: %s", 
                   src_filename, ckpt_filename, ckpt_timestep, strerror(errno));
        else
          msg_print("Created a symbolic link from \"%s\" to \"%s\" for checkpoint at timestep %d", 
                    src_filename, ckpt_filename, ckpt_timestep);
      } else {
        cSTRING src = (latest_run_in_use_before_ckpt
                       ? results_filename
                       : replace_coupling_file_current_suffix(results_filename, 
                                "bak", 
                                src_filename));
        if (platform_copy_file(src, ckpt_filename))
          msg_print("Copied \"%s\" to \"%s\" for checkpoint at timestep %d", 
                    src, ckpt_filename, ckpt_timestep);
        else
          msg_warn("Cannot copy \"%s\" to \"%s\" for checkpoint at timestep %d", 
                   src, ckpt_filename, ckpt_timestep);
      }
    }

    // If the checkpoint occurs between the launch of the application and PowerFLOW 
    // beginning to use the generated results, create the ckpt_2 file. This condition 
    // is true only if:
    //     
    //     ckpt_time < next_read_of_app_results < next_launch_of_app
    //
    CP_MEAS_WINDOW window = coupling_model_desc->window;
    BOOLEAN create_ckpt_2 = ((window->m_output_timestep/cp_info.n_user_base_steps) > coupling_model_desc->next_periodic_read
           && coupling_model_desc->next_periodic_read > ckpt_timestep);
    replace_coupling_file_current_suffix(results_filename, 
           is_full_checkpoint ? "full_ckpt_2" : "mme_ckpt_2", 
           ckpt_filename);
    remove(ckpt_filename);
    if (create_ckpt_2) {
      if (!only_keep_orig_model) {
        sprintf(suffix, "%03d", ckpt_1_backup_file_num + 1);
        replace_coupling_file_current_suffix(base_filename, suffix, src_filename);
        if (symlink(src_filename, ckpt_filename) == -1)	
          msg_warn("Could not create a symbolic link from \"%s\" to \"%s\" for checkpoint at timestep %d: %s", 
                   src_filename, ckpt_filename, ckpt_timestep, strerror(errno));
        else
          msg_print("Created a symbolic link from \"%s\" to \"%s\" for checkpoint at timestep %d", 
                    src_filename, ckpt_filename, ckpt_timestep);
      } else {
        cTHIRD_PARTY_INTERFACE *coupling_interface = 
          cp_info.surface_couplings[coupling_model_desc->coupling_model_index].tpi;
        eTPI_JOB_STATUS job_status = coupling_interface->GetJobStatus();
        if (job_status != TPI_JOB_NOT_SUBMITTED) {
          // Job still to be processed by maybe_read_coupling_models or maybe_wait_for_surface_coupling_jobs.
          // Assign those routines responsibilty for copying the current results file to the ckpt_2 file 
          // when the job completes.
          if (is_full_checkpoint) {
            coupling_model_desc->copy_current_results_to_full_ckpt_2_on_job_completion = TRUE;
            coupling_model_desc->full_ckpt_2_timestep = ckpt_timestep;
          } else {
            coupling_model_desc->copy_current_results_to_mme_ckpt_2_on_job_completion = TRUE;
            coupling_model_desc->mme_ckpt_2_timestep = ckpt_timestep;
          }
        } else {
          copy_current_results_to_ckpt_2(results_filename, ckpt_filename, ckpt_timestep);
        }
      }
    }

    coupling_model_desc++;
  }
}

#if 0
VOID restore_from_ckpt_surface_coupling_models(BOOLEAN is_full_checkpoint_restore_p) 
{
  eTPI_DB_ORGANIZATION organization;
  BOOLEAN model_has_units_p;
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
  ccDOTIMES(i,cp_info.n_surface_couplings) {
    CDI_CMDL cmdl = cp_info.surface_couplings[coupling_model_desc->coupling_model_index].get_cmdl();
    if (IS_TAI_APP(cmdl->model_type)) {
      if (cp_info.ptherm_model_changed_p) {
        // do not copy over the checkpoint file
        continue;
      }
    } else {
      msg_warn("Unknown model type %s encountered while making backup copy of coupling model file",cmdl->model_type);
      return;
    }
    copy_coupling_ckpt_file(coupling_model_desc->results_filename, -1, is_full_checkpoint_restore_p, TRUE);
    // ignore when the model and result filenames are the same (eg. PowerTHERM/RadTherm)
    if (IS_TAI_APP(cmdl->model_type)) {
      copy_coupling_ckpt_file(coupling_model_desc->viewfactor_filename, -1, is_full_checkpoint_restore_p, TRUE);
    }
    cTHIRD_PARTY_INTERFACE *coupling_interface = 
      cp_info.surface_couplings[coupling_model_desc->coupling_model_index].tpi;
    coupling_interface->GetDatabaseInfo(organization,model_has_units_p);
    if (TPI_ONE_FILE != organization) {
      copy_coupling_ckpt_file(coupling_model_desc->model_filename, -1, is_full_checkpoint_restore_p, TRUE);
    }
    // remove the last numbered link 
    remove_last_numbered_link(coupling_model_desc);
    coupling_model_desc++;
  }
}
#endif

static VOID copy_coupling_file_to_current_name(cSTRING from_file,   // location of original file
                                               cSTRING to_file,     // location of _cur file
                                               cSTRING app_type,
                                               BOOLEAN is_ckpt_restore_p,
                                               BOOLEAN is_full_ckpt_restore_p,
                                               BOOLEAN is_fail_on_copy_p)
{
  // If model has changed (cp_info.ptherm_model_changed_p), copy over the original 
  // model file again even if it is a checkpoint restore simulation.
  //
  // Also a checkpoint file will only exist if PowerFLOW began to use results from a 
  // run of the coupled application prior to the checkpoint. If not, we want to copy
  // over the original model again because the coupled application may have run
  // part way altering the contents of the _cur file. This of course means that the
  // coupled application has to be launched immediately on checkpoint restore.

  // We would like to make copying the checkpoint files conditional on:
  //
  //     coupling_model_desc->n_coupling_models_read > 0
  //
  // but the coupling_model_desc structs have not yet been constructed at 
  // point in time where this fcn is called. Thus we rely on the absence
  // of a checkpoint file to indicate that it was not created. Obviously this
  // does not allow us to ascertain that the file is missing erroneously.
  if (is_ckpt_restore_p && !cp_info.ptherm_model_changed_p
      && restore_ckpt_coupling_file(to_file, is_full_ckpt_restore_p)) {

  } else {
    if (!platform_file_present(from_file) && !is_fail_on_copy_p) {
      msg_warn("%s model file \"%s\" not present, it will be regenerated.",app_type,from_file);
      return;
    }
    if (!platform_copy_file(from_file, to_file)) {
      msg_error("Cannot copy %s model file \"%s\" to \"%s\". Please check permissions.",
                app_type, from_file, to_file);
    } else {
      msg_print("%s model file \"%s\" copied to \"%s\".", app_type, from_file, to_file);
    }
  }
}

// If adaptive or curve stepping is used in the tdf file, and the calculation type is set to
// Transient, issue warning message and use constant step size when calling PowerTHERM.
VOID maybe_warn_disable_adaptive_stepsize(cSTRING tpi_file, asINT32 model_index)
{
  if (!platform_file_present(tpi_file))
    return;
  else {
    //COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs + model_index;
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
    cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    eTPI_STEPSIZE_TYPE stepsize_type;
    if (TPI_SUCCESS != coupling_interface->StepsizeType(stepsize_type)) {
      msg_warn("Failed to get stepsize type from the model file \"%s\"", tpi_file);
      return;
    }
    if (stepsize_type == TPI_UNKNOWN_STEPSIZE_TYPE)
      msg_internal_error("Unknown stepsize type in the model file \"%s\".", tpi_file);
    if (stepsize_type != TPI_CONSTANT && cmdl->m_calculation_type == eCDI_POWERTHERM_CALCULATION_TYPE::Transient) {
      if (cmdl->m_coupling_phase_descs.size() > 1)
        msg_warn("%s time stepping is enabled for PowerTHERM model \"%s\", but will "
                 "be overridden by the fixed PowerTHERM step size value(s) specified on "
                 "the PowerTHERM Model dialog in PowerCASE. To run with adaptive time "
                 "stepping, you must set calculation type to \"Infer From Model\" on "
                 "that dialog. However, note that \"Infer From Model\" cannot properly "
                 "support multiple coupling phases with different periods.", 
                 (stepsize_type == TPI_ADAPTIVE)? "Adaptive" : "Curve",
                 cmdl->model_filename);
      else
        msg_warn("%s time stepping is enabled for PowerTHERM model \"%s\", but will "
                 "be overridden by the fixed PowerTHERM step size value(s) specified on "
                 "the PowerTHERM Model dialog in PowerCASE. To run with adaptive time "
                 "stepping, you must set calculation type to \"Infer From Model\" on "
                 "that dialog.", 
                 (stepsize_type == TPI_ADAPTIVE)? "Adaptive" : "Curve",
                 cmdl->model_filename);
    }
  }
}

VOID compose_slf_filename(cSTRING coupling_file, STRING slf_file)
{
  // find last occurrence of .
  char *cptr = (char *) strrchr(coupling_file,'.');
  if (cptr) {
    strncpy(slf_file, coupling_file, cptr - coupling_file);
    strcpy(slf_file + (cptr - coupling_file), ".slf");
  } else {
    strcpy(slf_file, coupling_file);
    strcat(slf_file, ".slf");
  }
}

VOID compose_batteryconfig_filename(cSTRING coupling_file, STRING batteryconfig_file)
{
  // find last occurrence of .
  char *cptr = (char *) strrchr(coupling_file,'.'); 
  if (cptr) {
    strncpy(batteryconfig_file, coupling_file, cptr - coupling_file);
    strcpy(batteryconfig_file + (cptr - coupling_file), ".tdf.battConfig.txt");
  } else {
    strcpy(batteryconfig_file, coupling_file);
    strcat(batteryconfig_file, ".tdf.battConfig.txt");
  }
}

VOID compose_viewfactor_filename(cSTRING coupling_file, STRING viewfactor_file)
{
  // find last occurrence of .
  char *cptr = (char *) strrchr(coupling_file,'.'); 
  if (cptr) {
    strncpy(viewfactor_file, coupling_file, cptr - coupling_file);
    strcpy(viewfactor_file + (cptr - coupling_file), ".vfs");
  } else {
    strcpy(viewfactor_file, coupling_file);
    strcat(viewfactor_file, ".vfs");
  }
}

VOID compose_tlf_filename(cSTRING coupling_file, STRING tlf_file)
{
  // find last occurrence of .
  char *cptr = (char *) strrchr(coupling_file,'.');
  if (cptr) {
    strncpy(tlf_file, coupling_file, cptr - coupling_file);
    strcpy(tlf_file + (cptr - coupling_file), ".tlf");
  } else {
    strcpy(tlf_file, coupling_file);
    strcat(tlf_file, ".tlf");
  }
}

static VOID remove_coupling_file_numbered_backups(cSTRING current_filename)
{
  CHARACTER filename[PLATFORM_MAXPATHLEN];
  strcpy(filename, current_filename);

  // find occurrence of _cur, and replace with number 001 etc 
  char *cptr = (char *) strrchr(filename, '.');
  if (cptr == NULL)
    msg_internal_error("Coupling model file lacks \"_cur\" extension");
  cptr -= 4;
  if (cptr < filename || (strncmp(cptr, "_cur", 4) != 0))
    msg_internal_error("Coupling model file lacks \"_cur\" extension");
  *cptr = '\0';

  /* The following code does not work since platform_remove_file does not support wilecard filenames.
   * Instead we use system_cmd to call rm directly to remove the files. This only works for Linux. 
   * No error msg will be checked and the simulation will proceed if it failed to delete the files.  
  strcat(filename, "_[0-9][0-9][0-9]*.*");

  platform_remove_file(filename); // ignore failure

  *cptr = '\0';
  strcat(filename, "_bak.*");
  platform_remove_file(filename);
  */
  char fullcmd[16 * 1024];
  // This command will also remove files with the extension .eqc.transinit.csv
  sprintf(fullcmd, "rm -f %s_[0-9][0-9][0-9]*.*", filename);
  system_cmd(fullcmd);
  sprintf(fullcmd, "rm -f %s_bak.*", filename);
  system_cmd(fullcmd);

}

VOID copy_initial_powertherm_coupling_files(STRING coupling_file, 
                                            STRING abs_coupling_file, 
                                            cSTRING app_type, 
                                            BOOLEAN is_ckpt_restore_p,
                                            BOOLEAN is_full_ckpt_restore_p,
                                            STRING tpi_file, // result of this fcn
                                            STRING tdf_dir,  // result of this fcn
                                            STRING tdf_file) // result of this fcn
{
  cSTRING powertherm_subdir = "powertherm";
  char base_name[PLATFORM_MAXPATHLEN];

  if (cp_info.ptherm_predecessor_run) {
    char predecessor_coupling_file[PLATFORM_MAXPATHLEN];
    platform_get_file_base_name(coupling_file, base_name);
    sprintf(predecessor_coupling_file, "%s/%s/%s", cp_info.ptherm_predecessor_run, powertherm_subdir, base_name);
    compose_coupling_file_current_name(predecessor_coupling_file, NULL, tdf_file);
    if (!platform_file_present(tdf_file))
      msg_error("Cannot find %s model file \"%s\".", 
                app_type, tdf_file);

    // For the prior PowerTHERM run, the current directory was the PowerFLOW run directory, not the "powertherm"
    // subdirectory of the run directory. Hence all paths inside the associated TDF file are relative to that
    // run directory.
    strcpy(tdf_dir, cp_info.ptherm_predecessor_run);
  } else {
    eMODEL_LOCATION model_location = 
      locate_coupling_file(coupling_file, abs_coupling_file, powertherm_subdir, cp_info.ptherm_model_path, tdf_file);

    if (MODEL_LOCATION_INVALID == model_location) {
      msg_error("Cannot find %s model file \"%s\".", app_type, coupling_file);
    } 

    platform_get_file_dir_name(tdf_file, tdf_dir);
  }


  cSTRING model_dir_name = NULL;
  if (cp_info.ptherm_predecessor_run || !is_file_dir_direct_descendant_of_rundir(tdf_file)) {
    model_dir_name = powertherm_subdir;
    if (!is_ckpt_restore_p) {
      make_coupling_model_dir(model_dir_name);
    } else {
      struct stat buf;
      asINT32 rval = stat(model_dir_name,&buf);
      // check for the existence of the model dir, abort otherwise
      if (!platform_file_present(model_dir_name) || -1 == rval) {
        msg_error("Cannot find \"%s\" directory upon checkpoint resume. Maybe it has been deleted or the run is not resumed from the appropriate run directory.",model_dir_name);
      } else if (!S_ISDIR(buf.st_mode)) {
        msg_error("Expected to find directory \"%s\" upon checkpoint resume. Instead a file with the same name exists in the run directory. Please resume the run from the correct directory.", model_dir_name);
      }
    }
  }

  if (cp_info.ptherm_predecessor_run) {
    platform_get_file_base_name(tdf_file, base_name);
    sprintf(tpi_file, "%s/%s", powertherm_subdir, base_name);
  } else {
    compose_coupling_file_current_name(tdf_file, model_dir_name, tpi_file);
  }

  if (!is_ckpt_restore_p)
    remove_coupling_file_numbered_backups(tpi_file);

  copy_coupling_file_to_current_name(tdf_file, tpi_file, app_type, is_ckpt_restore_p, is_full_ckpt_restore_p, TRUE);

  // Find viewfactor and slf file (if present) alongside model file
  if (!is_ckpt_restore_p) {
    CHARACTER viewfactor_file[PLATFORM_MAXPATHLEN];
    CHARACTER slf_file[PLATFORM_MAXPATHLEN];
    CHARACTER tlf_file[PLATFORM_MAXPATHLEN];
    compose_viewfactor_filename(tdf_file, viewfactor_file);
    compose_slf_filename(tdf_file, slf_file);
    compose_tlf_filename(tdf_file, tlf_file);

    if (!platform_file_present(viewfactor_file)) {
      msg_warn("Cannot find viewfactor file \"%s\". It will be regenerated by %s.",
               viewfactor_file, app_type);
    } else {
      CHARACTER tpi_viewfactor_file[PLATFORM_MAXPATHLEN];
      if (cp_info.ptherm_predecessor_run) {
        platform_get_file_base_name(viewfactor_file, base_name);
        sprintf(tpi_viewfactor_file, "%s/%s", powertherm_subdir, base_name);
      } else {
        compose_coupling_file_current_name(viewfactor_file, model_dir_name, tpi_viewfactor_file);
      }
      copy_coupling_file_to_current_name(viewfactor_file, tpi_viewfactor_file, app_type, is_ckpt_restore_p, is_full_ckpt_restore_p, FALSE);
    }

    if (platform_file_present(slf_file)) {
      CHARACTER tpi_slf_file[PLATFORM_MAXPATHLEN];
      if (cp_info.ptherm_predecessor_run) {
        platform_get_file_base_name(slf_file, base_name);
        sprintf(tpi_slf_file, "%s/%s", powertherm_subdir, base_name);
      } else {
        compose_coupling_file_current_name(slf_file, model_dir_name, tpi_slf_file);
      }
      copy_coupling_file_to_current_name(slf_file, tpi_slf_file, app_type, is_ckpt_restore_p, is_full_ckpt_restore_p, FALSE);
    }

    if (platform_file_present(tlf_file)) {
      CHARACTER tpi_tlf_file[PLATFORM_MAXPATHLEN];
      if (cp_info.ptherm_predecessor_run) {
        platform_get_file_base_name(tlf_file, base_name);
        sprintf(tpi_tlf_file, "%s/%s", powertherm_subdir, base_name);
      } else {
        compose_coupling_file_current_name(tlf_file, model_dir_name, tpi_tlf_file);
      }
      copy_coupling_file_to_current_name(tlf_file, tpi_tlf_file, app_type, is_ckpt_restore_p, is_full_ckpt_restore_p, FALSE);
    }
  }
}

VOID maybe_copy_human_comfort_files(cSTRING tpi_file, cSTRING orig_model_file_dir, asINT32 model_index, BOOLEAN is_full_ckpt_restore_p)
{
  if (!platform_file_present(tpi_file) || is_full_ckpt_restore_p)
    return;
  else {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
    cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;
    coupling_interface->CopyHumanComfortFiles(orig_model_file_dir, tpi_file);
  }
}


VOID maybe_copy_battery_config_file(cSTRING tdf_file, cSTRING tpi_file, cSTRING orig_model_file_dir, asINT32 model_index, BOOLEAN is_ckpt_restore_p, BOOLEAN is_full_ckpt_restore_p)
{
  if (!platform_file_present(tpi_file) || is_full_ckpt_restore_p)
    return;
  else {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
    cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;

    CHARACTER batteryconfig_file[PLATFORM_MAXPATHLEN];
    compose_batteryconfig_filename(tdf_file, batteryconfig_file);
    cSTRING powertherm_subdir = "powertherm";
    char base_name[PLATFORM_MAXPATHLEN];

    if (coupling_interface->isBatteryUsed()) {
      if (platform_file_present(batteryconfig_file)) {
        CHARACTER tpi_batteryconfig_file[PLATFORM_MAXPATHLEN];
        if (cp_info.ptherm_predecessor_run) {
          platform_get_file_base_name(batteryconfig_file, base_name);
          sprintf(tpi_batteryconfig_file, "%s/%s", powertherm_subdir, base_name);
        } else {
          strcpy(tpi_batteryconfig_file, tpi_file);  // tpi_file is the filename with "_cur"
          strcat(tpi_batteryconfig_file, ".battConfig.txt"); 
        }
        copy_coupling_file_to_current_name(batteryconfig_file, tpi_batteryconfig_file, surface_coupling->cmdl.model_type, is_ckpt_restore_p, is_full_ckpt_restore_p, FALSE);
      } else {
        msg_warn("The PowerTHERM model uses battery model, but the battery config file \"%s\" does not exist in the rundir.",
                 batteryconfig_file);
      }
    }
  }
}

VOID check_distributed_solver(cSTRING tpi_file, asINT32 model_index)
{
  if (!platform_file_present(tpi_file) || cp_info.ptherm_processes <= 1)
    return;
  else {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
    cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;
    bool is_distributed_solver_supported = coupling_interface->isDistributedSolverSupported();
    if (!is_distributed_solver_supported) {
      if (cp_info.ptherm_threads == 0)
        cp_info.ptherm_threads = cp_info.ptherm_processes;
      else
        cp_info.ptherm_threads *= cp_info.ptherm_processes;
      cp_info.ptherm_processes = 0; // If using 1, the number of processes will be specified as 1 when launching PowerTHERM, and
                                    // the hybrid solver will be used in PowerTHERM 12.4.
      msg_print("The model \"%s\" contains features which are not supported by the distributed solver in PowerTHERM, %s solver will be used with %d %s.",
                tpi_file, 
                cp_info.ptherm_threads > 1? "shared memory" : "serial",
                MAX(cp_info.ptherm_threads, 1), 
                cp_info.ptherm_threads > 1? "threads" : "thread");
    }
  }
}

VOID check_coupling_dependent_files(cTHIRD_PARTY_INTERFACE *coupling_interface,
                                    STRING coupling_file, STRING app_type)
{
  if (!coupling_interface)
    return;

  cSTRING_ARRAY dependency_paths;
  sINT32 num_dependencies;
  coupling_interface->GetFileDependencies(dependency_paths,num_dependencies);
  if (!dependency_paths || num_dependencies <= 0)
    return; // no dependencies for this coupling application type
  ccDOTIMES (i, num_dependencies) {
    cSTRING dep_file = dependency_paths[i];
    if (!platform_file_present(dep_file)) {
      msg_error("For %s model \"%s\", the %s file \"%s\" does not exist.", 
                app_type,coupling_file,
                IS_TAI_APP(app_type)? "seed" : "dependent", 
                (dep_file)? dep_file: "");
    } else {
      msg_print("For %s model \"%s\", using %s file \"%s\".",
                app_type,coupling_file,
                IS_TAI_APP(app_type)? "seed" : "dependent", 
                dep_file);
    }
  } // for num_dependencies
}

static VOID insert_coupling_model_queue_entry(COUPLING_MODEL_QUEUE_ENTRY entry)
{
  COUPLING_MODEL_QUEUE_ENTRY queue = cp_info.coupling_model_queue;
  if ((queue == NULL)
      || (entry->read_time < queue->read_time)) {
    cp_info.coupling_model_queue = entry;
    entry->next = queue;
  } else {
    COUPLING_MODEL_QUEUE_ENTRY before = queue;
    COUPLING_MODEL_QUEUE_ENTRY after = before->next;
    while (after != NULL) {
      if (after->read_time > entry->read_time)
        break;
      before = after;
      after = before->next;
    }
    before->next = entry;
    entry->next = after;
  }
}

static VOID remove_future_lines_from_surface_coupling_time_file(COUPLING_MODEL_DESC coupling_model_desc)
{
  cSTRING time_filename = coupling_model_desc->coupling_time_filename;
  FILE *time_file = fopen(time_filename, "r+");
  if (!time_file) {
    SURFACE_COUPLING surface_coupling = 
      cp_info.surface_couplings + coupling_model_desc->coupling_model_index;
    msg_warn("Unable to open %s coupling time file \"%s\": %s", surface_coupling->tpi->GetAppName(), time_filename, strerror(errno));
    return;
  }

  const int MAX_LINE_LENGTH = 2048;  // Maximal length of a line in PowerTHERM results file
  char line[MAX_LINE_LENGTH];

  TIMESTEP restart_time = cp_info.restart_time;
  long truncate_file_offset = 0;
  
  // Advance file pointer to where we should truncate the file
  while (fgets(line, MAX_LINE_LENGTH, time_file)) {
    // Skip lines starting with "#"
    if (line[0] == '#') {
      truncate_file_offset = ftellf(time_file);
      continue;
    }
    
    sFLOAT pt_start, pt_end, pt_step, pt_pf_ratio;
    int phase_index, pf_start, pf_end;
    char file_name[512];
    asINT32 n_matched = sscanf(line, "%s %f %f %f %f %d %d %d", file_name, &pt_start, &pt_end, &pt_step, &pt_pf_ratio,
                               &phase_index, &pf_start, &pf_end);
    if (n_matched < 8) {
      // We encountered on a non-comment line that did not have the valid data entry
      truncate_file_offset = ftellf(time_file);
      continue;
    }
    if (pf_start > restart_time) {
      msg_print("Creating a backup copy of \"%s\" in \"%s.bak\" before deleting lines written after"
                " the time the checkpoint file was written (%d timesteps).",
                time_filename, time_filename, cp_info.restart_time);
      char cmd[2 * PLATFORM_MAXPATHLEN + 30];
      sprintf(cmd, "cp \"%s\" \"%s.bak\"", time_filename, time_filename);
      system(cmd);
      ftruncate(fileno(time_file), truncate_file_offset);
      break;
    }
    truncate_file_offset = ftellf(time_file);
  }
  fclose(time_file);
}

static VOID remove_future_lines_from_surface_coupling_summary_file(COUPLING_MODEL_DESC coupling_model_desc)
{
  cSTRING summary_filename = coupling_model_desc->summary_filename;
  FILE *summary_file = fopen(summary_filename, "r+");
  if (!summary_file) {
    SURFACE_COUPLING surface_coupling = 
      cp_info.surface_couplings + coupling_model_desc->coupling_model_index;
    msg_warn("Unable to open %s summary file \"%s\": %s", surface_coupling->tpi->GetAppName(), summary_filename, strerror(errno));
    return;
  }

  const int MAX_LINE_LENGTH = 2048;  // Maximal length of a line in PowerTHERM results file
  char line[MAX_LINE_LENGTH];

  TIMESTEP restart_time = cp_info.restart_time;
  long truncate_file_offset = 0;
  
  // Advance file pointer to where we should truncate the file
  while (fgets(line, MAX_LINE_LENGTH, summary_file)) {
    // Skip lines starting with "#"
    if (line[0] == '#')
      continue;
    
    int timestep, iteration;
    dFLOAT min, max, avg;
    char var_name[512], face_type[128];
    asINT32 n_matched = sscanf(line, "%d %d %lg %lg %lg %s %s", &iteration, &timestep, &min, &max, &avg, var_name, face_type);

    if (n_matched < 2)
      // We encountered on a non-comment line that did not have the iteration and timestep entries
      continue;
    if (timestep > restart_time
        || (timestep == restart_time
            && n_matched == 7
            && strcmp(face_type, "PowerFLOW") == 0)) {
      msg_print("Creating a backup copy of \"%s\" in \"%s.bak\" before deleting lines written after"
                " the time the checkpoint file was written (%d timesteps).",
                summary_filename, summary_filename, cp_info.restart_time);
      char cmd[2 * PLATFORM_MAXPATHLEN + 30];
      sprintf(cmd, "cp \"%s\" \"%s.bak\"", summary_filename, summary_filename);
      system(cmd);
      ftruncate(fileno(summary_file), truncate_file_offset);
      break;
    }
    truncate_file_offset = ftellf(summary_file);
  }

  fclose(summary_file);
}

static VOID read_and_fill_coupling_data_array( COUPLING_MODEL_DESC coupling_model_desc, 
                                               CP_COUPLING_MODEL_COMM_DESC comm_desc, 
                                               TIMESTEP read_timestep,
                                               BOOLEAN is_periodic_read,
                                               sPT_TIME_INFO& pt_time_info)
{
  if (!coupling_model_desc)
    return;
  sINT32 model_index = coupling_model_desc->coupling_model_index;
  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+model_index;

  // load the latest results into memory
  if (read_timestep != 0) {
    surface_coupling->tpi->RefreshResults();
  }

  sINT32 n_vars = surface_coupling->n_import_vars();
  CDI_CPLW cplws = surface_coupling->get_cplws();
  cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;
  CDI_CMDL cmdl = surface_coupling->get_cmdl();
  cTPI_FRAME_DESC frameDesc;
  cSTRING results_filename = coupling_model_desc->results_filename;
  asINT32 num_coup_frames = coupling_interface->NumFrames();
  if (num_coup_frames == 0) {
    msg_error("No result frames present in the surface_coupling results file %s",results_filename);
  }
  coupling_interface->GetFrameDesc(frameDesc,num_coup_frames-1);

  // import the data
  ccDOTIMES (var, n_vars) {
    BOOLEAN hasVar = frameDesc.isVariableTypeAvailable(
            eTPI_VARIABLE_TYPE((surface_coupling->import_vars())[var]));
    if (!hasVar) {
      msg_error("Result frame number %d of file \"%s\" does not"
                " include variable %s.", 
                num_coup_frames-1,results_filename,
                coupling_interface->GetVarName(eTPI_VARIABLE_TYPE((surface_coupling->import_vars())[var])));
      return;
    }

    eTPI_STATUS status = coupling_interface->ImportSurfaceResult(
                       surface_coupling->foreign_resulting_source_data[var], 
                       eTPI_VARIABLE_TYPE((surface_coupling->import_vars())[var]), -1);
    if (coupling_interface->NumMessages() > 0)
      dump_tpi_messages(coupling_interface, cmdl->model_filename);
    if (status == TPI_FAILURE)
      // dump_tpi_messages above should call msg_error so this should never execute
      msg_error("Failed to extract results from \"%s\".",
                cmdl->model_filename);

    // compute average of above value to use as unmatched value during fill
    sINT32 n_import_source_facet_faces = 
      surface_coupling->foreign_resulting_source_surface->NumFacetFaces();
    dFLOAT unmapped_value = 0.0; // dFLOAT to accomodate large summation
    sFLOAT *source_data = 
      (surface_coupling->foreign_resulting_source_data)[var].GetScalarArray();
    ccDOTIMES(i,n_import_source_facet_faces) {
      unmapped_value += source_data[i];
    }
    if (n_import_source_facet_faces == 0) {
      msg_internal_error("Number of source facet faces from foreign "
                         " model is 0 in results file %s",results_filename);
    }
    unmapped_value /= n_import_source_facet_faces;
    asINT32 buf_index = 0;
    ccDOTIMES (pf_bc, surface_coupling->cmdl.num_pf_bcs) {
      // skip invalid native targets for which source eligibility failed
      if (-1 == surface_coupling->native_target_indices[pf_bc]) {
        continue;
      }
      asINT32 npoints = coupling_model_desc->nsurfels_per_target[pf_bc];
      if (npoints == 0)
        continue; // skip this zero element PowerFLOW face
      sFLOAT *values_array = 
        (surface_coupling->native_target_data[pf_bc])[var].GetScalarArray();
      surface_coupling->foreign_to_native_map->MapSourceToTarget(
                           source_data,
                           values_array,
                           surface_coupling->native_target_indices[pf_bc],
                           unmapped_value,
                           TRUE,
                           TRUE);
      if (read_timestep == 0 || is_periodic_read) {
        sFLOAT min_var_value = SFLOAT_MAX, max_var_value = -SFLOAT_MAX;
        dFLOAT av_var_value = 0.0;
        ccDOTIMES (i, npoints) {
          min_var_value = MIN(min_var_value,values_array[i]);
          max_var_value = MAX(max_var_value,values_array[i]);
          av_var_value += values_array[i];
        }
        av_var_value /= npoints;
        // print statistics here before converting to lattice units
        std::string varname = coupling_interface->GetVarName(eTPI_VARIABLE_TYPE(surface_coupling->import_vars()[var]));
        std::replace(varname.begin(), varname.end(), ' ', '_');
        fprintf(coupling_model_desc->summary_fp," %3d %6d %11g %11g %11g  %s(%s)\t%s\t%s\n",
                coupling_model_desc->n_coupling_models_read, read_timestep, 
                min_var_value, max_var_value, av_var_value,
                varname.c_str(),
                surface_coupling->import_var_units()[var],
                "PowerFLOW",
                cp_info.sri_faces[cplws[pf_bc].face_index].name
                );
        fflush(coupling_model_desc->summary_fp);
      }

      // must convert the resulting values to lattice units before updating the
      // physics surfels
      sFLOAT u_slope = surface_coupling->import_var_unit_conversion_slopes[var];
      sFLOAT u_offset = surface_coupling->import_var_unit_conversion_offsets[var];
      ccDOTIMES(i,npoints) {
        values_array[i] = u_offset + values_array[i]*u_slope;
      }

      asINT32 proc, buf_index;
      // layout of buffer is (surf1_var1, surf2_var1,.., surf1_var2,
      // surf2_var2..., surf1_var3, surf2_var3)
      // Must ensure the SPs receive and assign coupling data in the same order
      ccDOTIMES (target_index, coupling_model_desc->nsurfels_per_target[pf_bc]) {
        buf_index = coupling_model_desc->buf_indices_per_target[pf_bc][target_index];
        proc = coupling_model_desc->proc_indices_per_target[pf_bc][target_index];
        asINT32 max_i = (var)*coupling_model_desc->n_surfels_per_sp[proc];
        comm_desc[proc].coupling_data_buf[buf_index+max_i] = values_array[target_index];
      }
    } // for each PF target face
  } // for each imported variable
  
  // Update the PowerTHERM monitors that this coupling model contributes to
  double data;

  CDI_SCBC scbcs = surface_coupling->get_scbcs();
  dFLOAT max_T_gradient = 0.0;  // max gradient among all coupled PT faces

 
  // The following code should only be called for adaptive coupling, or when t_ptherm is used for equations.
  if (cp_info.adaptive_coupling_p || cp_info.is_ptherm_time_input_to_eqns) {
    UNITS_UNIT lattice_unit;
    UNITS_UNIT mks_unit; 
    units_get_default_for_class(cp_info.units_db, "lattice", "Time", &lattice_unit);
    units_get_default_for_class(cp_info.units_db, "mks", "Time", &mks_unit);

    TIMESTEP pt_launch_timestep = read_timestep;
    asINT32 pt_phase_index = coupling_model_desc->m_coupling_phase_descs.size()-1;
    ccDO_FROM_TO(phase_index, 1, coupling_model_desc->m_coupling_phase_descs.size()-1) {
      TIMESTEP next_phase_start_time = coupling_model_desc->m_coupling_phase_descs[phase_index].start - coupling_model_desc->m_coupling_phase_descs[phase_index].delay;
      if (pt_launch_timestep < next_phase_start_time) {
        pt_phase_index = phase_index - 1;
        break;
      }
    }

    if (cp_info.adaptive_coupling_p && cmdl->m_coupling_phase_descs[pt_phase_index].therm_time_ratio < 0) // ratio = -1.0 for steady state or infer from model
    {
      msg_internal_error("Adaptive coupling can be used only for transient simulation.");
    }

    pt_launch_timestep -= coupling_model_desc->m_coupling_phase_descs[pt_phase_index].delay;
    // Find the index within the current phase
    asINT32 index_in_phase = (pt_launch_timestep - cmdl->m_coupling_phase_descs[pt_phase_index].start)/cmdl->m_coupling_phase_descs[pt_phase_index].period;

    sFLOAT ratio_factor = 1.0;
    // Note that index_in_phase could be < 0 for the initial reading which is before the first 
    // PowerTHERM launch. We ignore this for the gradient calculation.
    // Calculate the temp gradient either adaptive coupling is on or when ptherm_dump_gradient option is used.
    if (index_in_phase >= 0 
        && (cp_info.ptherm_dump_gradient_p || cmdl->m_coupling_phase_descs[pt_phase_index].adaptive_p)
       ) {
      BOOLEAN includeFront, includeBack;
      ccDOTIMES(coupling_model_bc, cmdl->num_coupling_model_bcs) {

        // If the part has an assigned temperature (fixed or curve), do NOT consider it for the gradient calculation.
        if (coupling_interface->isAssignedTemperaturePart(scbcs[coupling_model_bc].target_name))
          continue;

        switch(scbcs[coupling_model_bc].target_side_to_match) {
        case CDI_SURFACE_FRONT:
          includeFront = TRUE;
          includeBack = FALSE;
          break;
        case CDI_SURFACE_BACK:
          includeFront = FALSE;
          includeBack = TRUE;
          break;
        case CDI_SURFACE_FRONT_AND_BACK:
          includeFront = TRUE;
          includeBack = TRUE;
          break;
        }
        //msg_print("Check PT face %s temperature FRONT %d BACK %d", scbcs[coupling_model_bc].target_name, includeFront, includeBack);
        sFLOAT min_temp, max_temp, mean_temp;
        eTPI_STATUS status;
        if (includeFront && includeBack) {
          status = coupling_interface->GetPartResult(scbcs[coupling_model_bc].target_name, 1, 1, num_coup_frames-1, min_temp, max_temp, mean_temp);
        } else if (includeFront) {   
          status = coupling_interface->GetPartResult(scbcs[coupling_model_bc].target_name, 1, 0, num_coup_frames-1, min_temp, max_temp, mean_temp);
        } else if (includeBack) {
          status = coupling_interface->GetPartResult(scbcs[coupling_model_bc].target_name, 0, 1, num_coup_frames-1, min_temp, max_temp, mean_temp);
        } else { 
          msg_internal_error("PowerTherm face %s does not have any side coupled!", scbcs[coupling_model_bc].target_name);
        }

        sFLOAT T_gradient = 0.0;
        dFLOAT delta_t = 0.0; 
        units_convert(cp_info.units_db, cmdl->m_coupling_phase_descs[pt_phase_index].exact_period, 
                      lattice_unit, mks_unit, &delta_t);

        delta_t *= cmdl->m_coupling_phase_descs[pt_phase_index].therm_time_ratio * coupling_model_desc->m_current_ratio_scale_factor;

        // XDU TODO: find the appropriate scale factor or ask the user to enter in the input file
        const sFLOAT gradient_scale_factor = 1.0;

        sFLOAT current_temp = cp_info.m_adaptive_params.use_max_temp ? max_temp : mean_temp;

        // Only calculate the gradient if this is the second or higher coupling
        if (index_in_phase >= 1) {
          T_gradient = gradient_scale_factor * fabs(current_temp - surface_coupling->foreign_target_temperature[coupling_model_bc])/delta_t;
          max_T_gradient = MAX(T_gradient, max_T_gradient);
        }
#if DEBUG_ADAPTIVE_COUPLING
        msg_print("---------------------------------------------------------");
        msg_print("Phase %d index in phase %d", pt_phase_index, index_in_phase);
        msg_print("Part %s temp %f old_temp %f PF exact period %f ratio %f scale_factor %f delta_t %f T_gradient %f", 
                  scbcs[coupling_model_bc].target_name,
                  current_temp,
                  surface_coupling->foreign_target_temperature[coupling_model_bc],
                  cmdl->m_coupling_phase_descs[pt_phase_index].exact_period,
                  cmdl->m_coupling_phase_descs[pt_phase_index].therm_time_ratio,
                  cmdl->m_coupling_phase_descs[pt_phase_index].therm_time_ratio * coupling_model_desc->m_current_ratio_scale_factor,
                  delta_t,
                  T_gradient
                 );
        msg_print("---------------------------------------------------------");
#endif
        surface_coupling->foreign_target_temperature[coupling_model_bc] = current_temp;
      }

      if (index_in_phase == 0) {
        coupling_model_desc->m_previous_gradient = 
          (cp_info.m_adaptive_params.gradient_low + cp_info.m_adaptive_params.gradient_high) / 2.0;     // Make the initial gradient within the range so that the initial ratio scale factor is 1.0
        coupling_model_desc->m_current_ratio_scale_factor = 1.0;
      }

      if (cp_info.ptherm_dump_gradient_p) {
        fprintf(coupling_model_desc->gradient_fp," %3d    %6d   %11g    %f    %d\n",
                coupling_model_desc->n_coupling_models_read, read_timestep, 
                max_T_gradient,
                cmdl->m_coupling_phase_descs[pt_phase_index].therm_time_ratio * coupling_model_desc->m_current_ratio_scale_factor,
                pt_phase_index
               );
        fflush(coupling_model_desc->gradient_fp);
      }

      // The ratio can only change starting from the 2nd coupling within phase after the gradient is available 
      BOOLEAN is_previous_gradient_in_range = cp_info.m_adaptive_params.is_gradient_in_range(coupling_model_desc->m_previous_gradient);
      BOOLEAN is_current_gradient_in_range = cp_info.m_adaptive_params.is_gradient_in_range(max_T_gradient);
      // If both the previous and current gradient is within (gradient_low, gradient_high), then the ratio factor should be 1 even if the change of gradient is above threshold
      BOOLEAN both_gradient_in_range = is_previous_gradient_in_range && is_current_gradient_in_range;

      if (index_in_phase == 1)
        both_gradient_in_range = FALSE;

      if (index_in_phase >= 1 && cmdl->m_coupling_phase_descs[pt_phase_index].adaptive_p && !both_gradient_in_range) {
        ratio_factor = cp_info.m_adaptive_params.get_pt_pf_ratio_scale_factor(cmdl->m_coupling_phase_descs[pt_phase_index].therm_time_ratio, max_T_gradient);

        // Only change the ratio if the gradient changes a significant amount (by percentage_threshold)
        // For the first phase, always change the ratio if it is out of the range (gradient_low, gradient_high)
        dFLOAT gradient_change = max_T_gradient - coupling_model_desc->m_previous_gradient;
        BOOLEAN change_ratio_p = (index_in_phase == 1)?  !is_current_gradient_in_range
                                                         :
                                                         fabs(gradient_change/coupling_model_desc->m_previous_gradient) > cp_info.m_adaptive_params.gradient_percentage_threshold;

        if (change_ratio_p) {
          coupling_model_desc->m_previous_gradient = max_T_gradient;
#if DEBUG_ADAPTIVE_COUPLING
          msg_print("---------------------------------------------------------");
          msg_print("T %d coupling phase %d index %d gradient %f change ratio from %f to %f starting from timestep %d", cp_info.time, pt_phase_index, index_in_phase, 
                    max_T_gradient,
                    cmdl->m_coupling_phase_descs[pt_phase_index].therm_time_ratio,
                    cmdl->m_coupling_phase_descs[pt_phase_index].therm_time_ratio * ratio_factor,
                    pt_launch_timestep + coupling_model_desc->m_coupling_phase_descs[pt_phase_index].period
                   );
          msg_print("---------------------------------------------------------");
#endif
          // The ratio should change starting from the next launch time
          coupling_model_desc->m_coupling_phase_ratios[pt_phase_index].push_back(sPT_PF_RATIO_RECORD(pt_launch_timestep + coupling_model_desc->m_coupling_phase_descs[pt_phase_index].period, ratio_factor));
          coupling_model_desc->m_current_ratio_scale_factor = ratio_factor;
        }
      }
    }

    // Send pt_time_info to SPs for evaluating equations in terms of thermal time 
    if (cp_info.time <= 0) {
      pt_time_info.pf_start_time = cmdl->m_coupling_phase_descs[pt_phase_index].exact_start;
      pt_time_info.pt_start_time = cp_info.m_adaptive_params.first_pt_start_time;
    } else {
      pt_time_info.pf_start_time = pt_launch_timestep + coupling_model_desc->m_coupling_phase_descs[pt_phase_index].period; 
      pt_time_info.pt_start_time = cp_info.next_powertherm_start_time;
    }

    cBOOLEAN need_to_convert_ratio_p = TRUE;  // Whether we need to convert the ratio from dimless to units sec/timestep

    eCDI_POWERTHERM_CALCULATION_TYPE::Enum calc_type = static_cast<eCDI_POWERTHERM_CALCULATION_TYPE::Enum>(cmdl->m_calculation_type);
    if (calc_type == eCDI_POWERTHERM_CALCULATION_TYPE::SteadyState) {
      pt_time_info.pt_pf_time_ratio = 1.0;
      if (cp_info.time <= 0)
        pt_time_info.pt_pf_time_ratio = -1.0; // Before first coupling, set ratio to -1 so that SPs can extrapolate backward to figure out the PT time
                                              // Note: on SP this ratio is for the next coupling, so we need to use this negative number as a convenction
                                              //       to tell SP that the ratio is -1 before first coupling and 1 for the first coupoling

    } else if (calc_type == eCDI_POWERTHERM_CALCULATION_TYPE::InferFromModel) {
      dFLOAT pt_duration;
      coupling_interface->GetTransientSimDuration(pt_duration);
      if (pt_duration == 0) { // steady state
        pt_time_info.pt_pf_time_ratio = 1.0;
        if (cp_info.time <= 0)
          pt_time_info.pt_pf_time_ratio = -1.0;

        dFLOAT pt_start_time;
        units_convert(cp_info.units_db, (double)pt_time_info.pf_start_time, 
                  lattice_unit, mks_unit, &pt_start_time);
        pt_time_info.pt_start_time = pt_start_time;
      } else { // transient like
        dFLOAT pt_start_time, pt_end_time;
        coupling_interface->GetInferFromModelTimes(cp_info.time < coupling_model_desc->m_coupling_phase_descs[0].start,
                                                   pt_start_time,
                                                   pt_end_time);
#if DEBUG_T_PTHERM_EQNS
        msg_print("INFER_FROM_MODEL: cp_info.time %d pt_start_time %f pt_end_time %f",
                  cp_info.time,
                  pt_start_time,
                  pt_end_time);
#endif
        pt_time_info.pt_pf_time_ratio = (pt_end_time - pt_start_time) / coupling_model_desc->m_coupling_phase_descs[pt_phase_index].period;
        need_to_convert_ratio_p = FALSE;
      }
    } else { // Transient
      // Check if this is the last period within this phase. If so use the next phase ratio
      if (pt_phase_index < (coupling_model_desc->m_coupling_phase_descs.size() - 1) 
          &&pt_time_info.pf_start_time == cmdl->m_coupling_phase_descs[pt_phase_index + 1].start) {
        pt_time_info.pt_pf_time_ratio = cmdl->m_coupling_phase_descs[pt_phase_index + 1].therm_time_ratio; // The value is the thermal time in seconds for one timestep
      } else {
        pt_time_info.pt_pf_time_ratio = cmdl->m_coupling_phase_descs[pt_phase_index].therm_time_ratio * ratio_factor; // The value is the thermal time in seconds for one timestep
      }
    }

    if (need_to_convert_ratio_p) {
      double new_ratio; // convert timestep directly to PowerTHERM physical time in seconds
      units_convert(cp_info.units_db, (double)pt_time_info.pt_pf_time_ratio, 
                    lattice_unit, mks_unit, &new_ratio);
      pt_time_info.pt_pf_time_ratio = new_ratio;
    }
  }

  // Skip the signal if t=0 or this is a non-periodic read time after checkpoint resume
  if (read_timestep != 0 && is_periodic_read) {
    ccDOTIMES(i, cp_info.pt_monitors.size()) {
      POWERTHERM_MONITOR pt_monitor = cp_info.pt_monitors[i];
      
      std::string part_name = pt_monitor->m_powertherm_part;
      if (pt_monitor->m_powertherm_model_index == model_index) {
        // Only check the temperature variables here. 
        // HTC variables are handled in launch_surface_coupling_app() when writing the output file 
        if (is_powertherm_var_type_surface_temp(pt_monitor->m_powertherm_var_type)) {
          // Obtain temperature of PowerTHERM part.
          // If the part is surface type, the sides (front, back or both) should be provided.
          // Otherwise the sides are ignored.
          sFLOAT min_temp, max_temp, mean_temp;
          eTPI_STATUS status;
          switch(pt_monitor->m_surface_sides) {
          case eCDI_MNTR_PT_SIDE::FrontAndBack:
            status = coupling_interface->GetPartResult(part_name, 1, 1, num_coup_frames-1, min_temp, max_temp, mean_temp);
            break;
          case eCDI_MNTR_PT_SIDE::Front:      
            status = coupling_interface->GetPartResult(part_name, 1, 0, num_coup_frames-1, min_temp, max_temp, mean_temp);
            break;
          case eCDI_MNTR_PT_SIDE::Back:
            status = coupling_interface->GetPartResult(part_name, 0, 1, num_coup_frames-1, min_temp, max_temp, mean_temp);
            break;
          case eCDI_MNTR_PT_SIDE::NotApplicable:
            status = coupling_interface->GetPartResult(part_name, 0, 0, num_coup_frames-1, min_temp, max_temp, mean_temp);
            break;
          default:  // Should never happen
            msg_internal_error("Powertherm monitor surface sides not recognized!");
            break;
          }
          if (status == TPI_FAILURE) {
            if (coupling_interface->NumMessages() > 0)
              dump_tpi_messages(coupling_interface, cmdl->model_filename);
            // dump_tpi_messages above should call msg_error so this should never execute
            msg_error("Failed to get results for part %s in frame %d.", part_name.c_str(), num_coup_frames-1);
          }
          switch(pt_monitor->m_powertherm_var_type) {
          case eCDI_MNTR_PT_VAR::MeanTemp:
            data = mean_temp;
            break;
          case eCDI_MNTR_PT_VAR::MaxTemp:
            data = max_temp;
            break;
          case eCDI_MNTR_PT_VAR::MinTemp:
            data = min_temp;
            break;
          default:  // Should never happen
            msg_internal_error("Powertherm monitor var type is not supported!");
            break;
          }

          TIMESTEP signal_timestep = read_timestep; // Signal timestep should be (read_timestep - delay)
          TIMESTEP next_read_timestep = read_timestep + coupling_model_desc->period;
          TIMESTEP next_signal_timestep = next_read_timestep;
          asINT32 phase = 0;
          asINT32 n_phases = coupling_model_desc->m_coupling_phase_descs.size();
          
          // Find the phase of current signal
          asINT32 signal_phase_index = 0;
          ccDO_FROM_TO(phase_index, 1, n_phases-1) {
            TIMESTEP next_phase_start_time = coupling_model_desc->m_coupling_phase_descs[phase_index].start - coupling_model_desc->m_coupling_phase_descs[phase_index].delay;
            if (signal_timestep < next_phase_start_time) {
              signal_phase_index = phase_index;
              break;
            }
          }
          signal_timestep -= coupling_model_desc->m_coupling_phase_descs[signal_phase_index].delay;

          // Find if the next periodic read is crossing the phase boundary
          ccDO_FROM_TO(phase_index, 1, n_phases-1) {
            TIMESTEP next_phase_start_time = coupling_model_desc->m_coupling_phase_descs[phase_index].start - coupling_model_desc->m_coupling_phase_descs[phase_index].delay;
            if (read_timestep < next_phase_start_time && next_read_timestep >= next_phase_start_time)
            {
              next_read_timestep = coupling_model_desc->m_coupling_phase_descs[phase_index].start - coupling_model_desc->m_coupling_phase_descs[phase_index].delay;
              break;
            }
          }

          // Find the phase of next signal and set next signal time to be (next_read_time - delay)
          asINT32 next_signal_phase_index = 0;
          ccDO_FROM_TO(phase_index, 1, n_phases-1) {
            TIMESTEP next_phase_start_time = coupling_model_desc->m_coupling_phase_descs[phase_index].start - coupling_model_desc->m_coupling_phase_descs[phase_index].delay;
            if (next_read_timestep < next_phase_start_time) {
              next_signal_phase_index = phase_index;
              break;
            }
          }
          next_signal_timestep = next_read_timestep - coupling_model_desc->m_coupling_phase_descs[next_signal_phase_index].delay;
          
          pt_monitor->append_and_analyze_signal(data, signal_timestep, next_signal_timestep);
        }
      }
    }
  }
}

static VOID backup_coupling_model_files(COUPLING_MODEL_DESC coupling_model_desc,
          TIMESTEP read_time)
{
  backup_log_file(coupling_model_desc);
  backup_current_coupling_link(coupling_model_desc);

  coupling_model_desc->n_coupling_models_read++;
  coupling_model_desc->last_results_read_time = read_time;

  if (coupling_model_desc->copy_current_results_to_full_ckpt_2_on_job_completion) {
    coupling_model_desc->copy_current_results_to_full_ckpt_2_on_job_completion = FALSE;
    char ckpt_filename[PLATFORM_MAXPATHLEN];
    replace_coupling_file_current_suffix(coupling_model_desc->results_filename, 
                                         "full_ckpt_2", 
                                         ckpt_filename);
    copy_current_results_to_ckpt_2(coupling_model_desc->results_filename, ckpt_filename, 
                                   coupling_model_desc->full_ckpt_2_timestep);
  }
  if (coupling_model_desc->copy_current_results_to_mme_ckpt_2_on_job_completion) {
    coupling_model_desc->copy_current_results_to_mme_ckpt_2_on_job_completion = FALSE;
    char ckpt_filename[PLATFORM_MAXPATHLEN];
    replace_coupling_file_current_suffix(coupling_model_desc->results_filename, 
                                         "mme_ckpt_2", 
                                         ckpt_filename);
    copy_current_results_to_ckpt_2(coupling_model_desc->results_filename, ckpt_filename, 
                                   coupling_model_desc->mme_ckpt_2_timestep);
  }
}

static VOID maybe_read_coupling_model_internal(COUPLING_MODEL_QUEUE_ENTRY entry)
{
  COUPLING_MODEL_DESC coupling_model_desc = entry->coupling_model_desc;
  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+coupling_model_desc->coupling_model_index;
  const WALLCLOCK_TIME_SECS SECS_BETWEEN_COUPLING_MODEL_DATA_CHECKS = 1;
  const int SECONDS_BETWEEN_LICENSE_CHECKS = 10;
  WALLCLOCK_TIME_SECS time_secs;

  if ((WALLCLOCK_TIME_DIFF(time_secs = wallclock_time_secs(), 
      coupling_model_desc->last_check_time) > SECS_BETWEEN_COUPLING_MODEL_DATA_CHECKS)) {
    cTHIRD_PARTY_INTERFACE *coupling_interface = 
      cp_info.surface_couplings[coupling_model_desc->coupling_model_index].tpi;
    coupling_model_desc->last_check_time = time_secs;
    cSTRING model_type = cp_info.surface_couplings[coupling_model_desc->coupling_model_index].cmdl.model_type;

    eTPI_JOB_STATUS job_status = coupling_interface->GetJobStatus();

    if (coupling_interface->NumMessages() > 0) {
      CDI_CMDL cmdl = surface_coupling->get_cmdl();
      dump_tpi_messages(coupling_interface, cmdl->model_filename);
    }

    if (TPI_JOB_IN_PROGRESS == job_status) {
      if ((cp_info.time >= entry->read_time) && !entry->waiting_msg_issued) {
        entry->waiting_msg_issued = TRUE;
        msg_print("Waiting for coupling model results file \"%s\" to be updated at timestep %d", 
                  coupling_model_desc->results_filename, entry->read_time);        
        char status_string[512];
        sprintf(status_string,"Timestep %d (Waiting for coupling model results file \"%s\" to be updated)", 
                entry->read_time,coupling_model_desc->results_filename);

        cp_jobctl_output_status(status_string);
      }
    } else if (coupling_model_desc->init_pf_bc_coupling_p 
               || (coupling_model_desc->read_results_from_ckpt_2_file && entry->read_time < cp_info.end_time)
               || (TPI_JOB_COMPLETED_SUCCESS == job_status && entry->read_time < cp_info.end_time)) {
      entry->waiting_msg_issued = FALSE;
      entry->license_wait_msg_issued = FALSE;
      if (TPI_JOB_COMPLETED_SUCCESS == job_status) {
        coupling_interface->ResetJobStatus();
        backup_coupling_model_files(coupling_model_desc, entry->read_time);
      } else if (coupling_model_desc->read_results_from_ckpt_2_file) {
        coupling_model_desc->n_coupling_models_read++;
        coupling_model_desc->n_runs_launched++;
        coupling_model_desc->last_results_read_time = entry->read_time;
      }

      char interface_status[512];
      sprintf(interface_status, "%s (Reading results from \"%s\")", 
              cp_jobctl_status_string(),
              coupling_model_desc->results_filename);
      cp_jobctl_output_status(interface_status);

      sPT_TIME_INFO pt_time_info;
      CP_COUPLING_MODEL_COMM_DESC comm_descs = cp_info.coupling_model_comm_descs;
      read_and_fill_coupling_data_array(coupling_model_desc, comm_descs, entry->read_time, entry->is_periodic_read,
                                        pt_time_info);
      entry->is_periodic_read = TRUE;
      if (entry->read_time == cp_info.time) {
        msg_print("Read data from coupling model results file \"%s\" at timestep %d",
                  coupling_model_desc->results_filename, entry->read_time);
      } else if (cp_info.time < 0) { // initialization
        msg_print("Read data from coupling model results file \"%s\" at timestep 0",
                  coupling_model_desc->results_filename);
      } else {
        if (entry->read_time > cp_info.time)
          msg_print("Read data from coupling model results file \"%s\" at timestep %d"
                    " to be used beginning at timestep %d",
                    coupling_model_desc->results_filename, cp_info.time, entry->read_time);
        else
          msg_internal_error("Wrong: entry->read_time %d < cp_info.time %d", entry->read_time, cp_info.time);
      }

      // At this point, we know we have a good coupling_model.

      // Use synchronous sends (MPI_Issend) below to prevent the CP from racing ahead
      // reading coupling_models that get buffered internal to MPI. 
      // Send the coupling_model data to the SPs
      ccDOTIMES(proc, total_fsps) {
        asINT32 data_size = coupling_model_desc->n_surfels_per_sp[proc]*surface_coupling->n_import_vars();
        if (cp_info.is_ptherm_time_input_to_eqns) {
          MPI_Datatype mpi_type;
          MPI_Request  send_request;
          sPT_TIME_INFO::mpi_type_init(&mpi_type);
#if DEBUG_T_PTHERM_EQNS
          if (proc == 0) {
            msg_print("Send pf_start_time %d pt_start_time %f pt_pf_time_ratio %f to SPs", 
                    pt_time_info.pf_start_time,
                    pt_time_info.pt_start_time, 
                    pt_time_info.pt_pf_time_ratio);
          }
#endif

          MPI_Issend(&pt_time_info, 
                     1,
                     mpi_type, 
                     proc, 
                     eMPI_POWERTHERM_TIME_INFO_TAG,
                     eMPI_sp_cp_comm, 
                     &send_request);
          // We can free the request immediately because we know by construction when the SPs are guaranteed
          // to have received the message, and we won't send again until the next read time.
          if (!sim_args.no_mpi_request_free)
            MPI_Request_free(&send_request);
        }

        if (data_size > 0) {
          comm_descs[proc].coupling_data_sent_p = FALSE;
          int tag = make_mpi_tag<eMPI_MSG::COUPLING>(coupling_model_desc->coupling_model_index);
          MPI_Issend(comm_descs[proc].coupling_data_buf, 
                     data_size, 
                     eMPI_sFLOAT, 
                     proc, 
                     tag,
                     eMPI_sp_cp_comm, 
                     &comm_descs[proc].mpi_request);
  
        } else {
          comm_descs[proc].coupling_data_sent_p = TRUE;
        }
      }

      entry->result_available_p = TRUE;
      entry->next_sp_to_test = 0;

      // Restore status message to a standard timestep report
      cp_jobctl_output_current_status();

    } else if (TPI_JOB_COMPLETED_FAILURE == job_status) {
      CDI_CMDL cmdl = surface_coupling->get_cmdl();
      if (coupling_interface->NumMessages() > 0) {
        dump_tpi_messages(coupling_interface, cmdl->model_filename);
      }
      msg_error("%s job for model \"%s\" failed: %s. Check the log file for details.",
                cmdl->model_type, coupling_model_desc->model_filename,
                coupling_interface->GetApplicationExitMessage());
    } else if (TPI_JOB_COMPLETED_TERMINATED == job_status) {
      CDI_CMDL cmdl = surface_coupling->get_cmdl();
      if (coupling_interface->NumMessages() > 0) {
        dump_tpi_messages(coupling_interface, cmdl->model_filename);
      }
      msg_error("%s job for model \"%s\" terminated: %s. Check the log file for details.",
                cmdl->model_type, coupling_model_desc->model_filename,
                coupling_interface->GetApplicationExitMessage());
    } else if (TPI_JOB_COMPLETED_NOLICENSE == job_status) {
      CDI_CMDL cmdl = surface_coupling->get_cmdl();
      if (!entry->license_wait_msg_issued) {
        msg_warn("Cannot obtain a license for the %s job for model \"%s\". Trying again...",
                 cmdl->model_type, cmdl->model_filename);
        entry->license_wait_msg_issued = TRUE;
        platform_sleep_seconds(SECONDS_BETWEEN_LICENSE_CHECKS);
      }
      if ( TPI_JOB_SUBMIT_SUCCESS != coupling_interface->RunJob()) {
        if (coupling_interface->NumMessages() > 0)
          dump_tpi_messages(coupling_interface, cmdl->model_filename);
        msg_error("Failed to launch %s job (model \"%s\"). Check the log file for details.", cmdl->model_type,cmdl->model_filename);
      }
    }
  }
}

static VOID maybe_copy_ckpt_2_file_to_current(COUPLING_MODEL_DESC coupling_model_desc)
{
  // On checkpoint restore, if the checkpoint occurred between the launch of the
  // application and PowerFLOW beginning to use the generated results, we now
  // want to copy the ckpt_2 file to <model>_cur.tdf. This condition is true only if:
  //     
  //     ckpt_time < next_read_of_app_results < next_launch_of_app
  //
  CP_MEAS_WINDOW window = coupling_model_desc->window;

  // @@@ It is likely incorrect to bail out of this function if window is NULL because
  // according to the code in maybe_launch_surface_coupling_jobs, the PowerTHERM launch 
  // time in this situation is determined by the CDI meas window associated with this
  // coupling model.

  if (window // window is NULL if PowerFLOW data is not used as BC in PowerTHERM (pointless, but possible)
      && (window->m_output_timestep/cp_info.n_user_base_steps) > coupling_model_desc->next_periodic_read
      && coupling_model_desc->next_periodic_read > cp_info.restart_time
      && !cp_info.ptherm_model_changed_p) {
    coupling_model_desc->read_results_from_ckpt_2_file = TRUE;
    CHARACTER src_filename[PLATFORM_MAXPATHLEN];
    replace_coupling_file_current_suffix(coupling_model_desc->model_filename, 
           cp_info.is_full_checkpoint_restore ? "full_ckpt_2" : "mme_ckpt_2", 
           src_filename);
    if (!platform_copy_file(src_filename, coupling_model_desc->model_filename)) {
      msg_warn("Cannot copy file \"%s\" to \"%s\".", src_filename, coupling_model_desc->model_filename);
    } else {
      msg_print("Model file \"%s\" copied to \"%s\".", src_filename, coupling_model_desc->model_filename);
    }
  }
}

VOID maybe_read_coupling_models()
{	
  COUPLING_MODEL_QUEUE_ENTRY entry = cp_info.coupling_model_queue;

  if (NULL == entry || NULL == entry->coupling_model_desc)
    return;

  if (!entry->result_available_p) {
    maybe_read_coupling_model_internal(entry);
  } else {
    // It is possible that SPs already received the data, but it is not the data import time yet. 
    // Should wait here before updating the next periodic read time.
    if (cp_info.time < entry->read_time)
      return;

    CP_COUPLING_MODEL_COMM_DESC comm_descs = cp_info.coupling_model_comm_descs;

    ccDO_FROM_BELOW(sp,entry->next_sp_to_test,total_fsps) {
      if (comm_descs[sp].coupling_data_sent_p) { 
        entry->next_sp_to_test = sp + 1;
        continue;
      }
      asINT32 n_chunks_sent = 0;
      int flag = 0;
      MPI_Status status;
      MPI_Test(&comm_descs[sp].mpi_request, &flag, &status);
      if (flag) {
        n_chunks_sent++;
      } else {
        break;
      }
      if (n_chunks_sent == 1) {
        comm_descs[sp].coupling_data_sent_p = TRUE;
        entry->next_sp_to_test = sp+1;
      } else {
        break;
      }
    }

    if (entry->next_sp_to_test == total_fsps) {
      // Done transferring this coupling_model to all SPs
      // invalidate the results flag, so the next time around it waits if necessary
      entry->result_available_p = FALSE;
      cp_info.coupling_model_queue = entry->next;

      COUPLING_MODEL_DESC coupling_model_desc = entry->coupling_model_desc;
      if (coupling_model_desc->init_pf_bc_coupling_p)
      {
        coupling_model_desc->init_pf_bc_coupling_p = FALSE;
        // Initial BC read completed, reset read times to original periodic values
        TIMESTEP next_read_time = entry->coupling_model_desc->next_periodic_read;
        if (next_read_time <= entry->coupling_model_desc->end_time)
        {
          maybe_copy_ckpt_2_file_to_current(coupling_model_desc);
          entry->read_time = next_read_time;
          insert_coupling_model_queue_entry(entry);
        }
      }
      else if (entry->read_time == entry->coupling_model_desc->next_periodic_read)
      {
        coupling_model_desc->read_results_from_ckpt_2_file = FALSE;
        // This was a periodic read - reinsert in queue

        TIMESTEP next_read_time = entry->read_time + entry->coupling_model_desc->period;

        // For supporting variable PowerTherm coupling periods
        // check if we need to change to a new phase
        asINT32 n_phases = coupling_model_desc->m_coupling_phase_descs.size();
        if (n_phases > 1)
        {
          ccDO_FROM_TO(phase_index, 1, n_phases-1)
          {
            // next_phase_start is the measurement output time (= coupling time - delay)
            TIMESTEP next_phase_start_time = coupling_model_desc->m_coupling_phase_descs[phase_index].start
              - coupling_model_desc->m_coupling_phase_descs[phase_index].delay;
            if (entry->read_time < next_phase_start_time && next_read_time >= next_phase_start_time)
            {
              // update coupling times
              entry->coupling_model_desc->period = coupling_model_desc->m_coupling_phase_descs[phase_index].period;
              next_read_time = coupling_model_desc->m_coupling_phase_descs[phase_index].start;
              break;
            }
          }
        }

        if (next_read_time <= entry->coupling_model_desc->end_time)
        {
          entry->read_time = next_read_time;
          entry->coupling_model_desc->next_periodic_read = next_read_time;
#if DEBUG_VARIABLE_POWERTHERM_COUPLING
          msg_print("CP: at timestep %d read_coupling event %d is inserted into queue", cp_info.time, entry->read_time);
#endif
          insert_coupling_model_queue_entry(entry);
        }

      }
    } // n_sps_done == total_sps
  }
}

VOID initialize_coupling_model_queue()
{
  // Insert coupling_model descs with a known read time in the queue
  COUPLING_MODEL_DESC coupling_model_descs = cp_info.coupling_model_descs;
  asINT32 n_coupling_models = cp_info.n_surface_couplings;
  COUPLING_MODEL_QUEUE_ENTRY entries = cnew sCOUPLING_MODEL_QUEUE_ENTRY[ n_coupling_models];

  COUPLING_MODEL_DESC coupling_model_desc = coupling_model_descs;
  COUPLING_MODEL_QUEUE_ENTRY entry = entries;

  for (asINT32 i = 0; i < n_coupling_models; i++, coupling_model_desc++, entry++) {
    if (coupling_model_desc->couple_during_sim_p
        && (coupling_model_desc->init_pf_bc_coupling_p
            || ((coupling_model_desc->next_periodic_read > cp_info.restart_time)
                && (coupling_model_desc->next_periodic_read <= coupling_model_desc->end_time)))) {

      if (!coupling_model_desc->init_pf_bc_coupling_p)
        maybe_copy_ckpt_2_file_to_current(coupling_model_desc);

      entry->coupling_model_desc = coupling_model_desc;
      if (coupling_model_desc->init_pf_bc_coupling_p) {
        entry->read_time = cp_info.restart_time;
        entry->is_periodic_read = (cp_info.restart_time > 0
                                   && cp_info.restart_time == (coupling_model_desc->next_periodic_read - coupling_model_desc->period));
      } else {
        entry->read_time = coupling_model_desc->next_periodic_read;
        entry->is_periodic_read = TRUE;
      }

      insert_coupling_model_queue_entry(entry);
    }
  }
}

VOID allocate_shared_export_var_array() 
{
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
  asINT32 max_export_array_size = -1;
  ccDOTIMES(i,cp_info.n_surface_couplings) {
    sINT32 model_index = coupling_model_desc->coupling_model_index;
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+model_index;
    sINT32 n_export_vars = surface_coupling->n_export_vars(); 
    if (!surface_coupling->native_resulting_source_surface ||
        0 == surface_coupling->native_resulting_source_surface->NumFacetFaces()) {
      continue;
    }
    asINT32 nfaces = surface_coupling->native_resulting_source_surface->NumFacetFaces();
    max_export_array_size = MAX(max_export_array_size, nfaces*n_export_vars);
    coupling_model_desc++;
  }
  if (max_export_array_size > 0) {
    cp_info.coupling_export_var_array = cnew sFLOAT[max_export_array_size];
  }

}

VOID close_coupling_model_summary_files() 
{
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
  // could be called due to an error condition before the model is built
  // properly
  if (cp_info.n_surface_couplings <= 0 || !coupling_model_desc)
    return;

  ccDOTIMES(i,cp_info.n_surface_couplings) {
    if (coupling_model_desc->summary_fp) {
      fclose(coupling_model_desc->summary_fp);
      coupling_model_desc->summary_fp = NULL;
    }
    if (coupling_model_desc->gradient_fp) {
      fclose(coupling_model_desc->gradient_fp);
      coupling_model_desc->gradient_fp = NULL;
    }

    if (coupling_model_desc->coupling_time_fp) {
      fclose(coupling_model_desc->coupling_time_fp);
      coupling_model_desc->coupling_time_fp = NULL;
    }
    coupling_model_desc++;
  }
}

VOID open_coupling_model_summary_files(BOOLEAN is_checkpoint_restore_p) 
{
  CHARACTER sum_name[PLATFORM_MAXPATHLEN];
  CHARACTER time_name[PLATFORM_MAXPATHLEN];
  CHARACTER gradient_name[PLATFORM_MAXPATHLEN];
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
  ccDOTIMES(i,cp_info.n_surface_couplings) {
    SURFACE_COUPLING surface_coupling = 
      cp_info.surface_couplings + coupling_model_desc->coupling_model_index;
    cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    memset(sum_name,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
    // look for the _cur
    cSTRING cptr = strrchr(coupling_model_desc->model_filename,'_');
    if (cptr) {
      strncpy(sum_name,coupling_model_desc->model_filename,
              strlen(coupling_model_desc->model_filename)-strlen(cptr));
    } else {
      strcpy(sum_name,coupling_model_desc->model_filename);
    }
    strcpy(time_name, sum_name);
    strcpy(gradient_name, sum_name);

    strcat(sum_name,".out");
    coupling_model_desc->summary_filename = xnew char[ strlen(coupling_model_desc->model_filename) + 1 + 4];
    strcpy(coupling_model_desc->summary_filename, sum_name);
    if (is_checkpoint_restore_p) {
      remove_future_lines_from_surface_coupling_summary_file(coupling_model_desc);
      coupling_model_desc->summary_fp = fopen(coupling_model_desc->summary_filename,"a");
    } else {
      coupling_model_desc->summary_fp = fopen(coupling_model_desc->summary_filename,"w");
    }
    if (!coupling_model_desc->summary_fp) {
      msg_error("Cannot open %s summary output file %s", surface_coupling->get_cmdl()->model_type,
                coupling_model_desc->summary_filename); 
    }
    if (!is_checkpoint_restore_p) {
      fprintf(coupling_model_desc->summary_fp,"# Iter TimeStep Minimum     Maximum     Average    VarName(units)       FaceType        Name\n");
      fflush(coupling_model_desc->summary_fp);
    }
    if (cp_info.ptherm_dump_gradient_p) {
      strcat(gradient_name,".gradient_out");
      coupling_model_desc->gradient_filename = xnew char[ strlen(coupling_model_desc->model_filename) + 1 + 13];
      strcpy(coupling_model_desc->gradient_filename, gradient_name);
      coupling_model_desc->gradient_fp = fopen(coupling_model_desc->gradient_filename,"w");
      if (!coupling_model_desc->gradient_fp) {
        msg_error("Cannot open %s gradient output file %s", surface_coupling->get_cmdl()->model_type,
                  coupling_model_desc->gradient_filename); 
      }
      if (!is_checkpoint_restore_p) {
        fprintf(coupling_model_desc->gradient_fp,"#Iter  TimeStep   Gradient(K/second)    Ratio   Phase\n");
        fflush(coupling_model_desc->gradient_fp);
      }
    }

    strcat(time_name,".tdx");
    coupling_model_desc->coupling_time_filename = xnew char[ strlen(coupling_model_desc->model_filename) + 1 + 4];
    strcpy(coupling_model_desc->coupling_time_filename, time_name);
    if (is_checkpoint_restore_p) {
      remove_future_lines_from_surface_coupling_time_file(coupling_model_desc);
      coupling_model_desc->coupling_time_fp = fopen(coupling_model_desc->coupling_time_filename,"a");
    } else {
      coupling_model_desc->coupling_time_fp = fopen(coupling_model_desc->coupling_time_filename,"w");
    }
    if (!coupling_model_desc->coupling_time_fp) {
      msg_error("Cannot open %s coupling time output file %s", cmdl->model_type,
                coupling_model_desc->coupling_time_filename); 
    }
    if (!is_checkpoint_restore_p) {
      fprintf(coupling_model_desc->coupling_time_fp, "file_version 1.1\n");
      fprintf(coupling_model_desc->coupling_time_fp, "meters_per_cell %f\n", cdi_data.meters_per_cell);
      // The transformation matrix between PF and PT models
      fprintf(coupling_model_desc->coupling_time_fp, "powertherm_xform\n");
      fprintf(coupling_model_desc->coupling_time_fp, "%17.13g %17.13g %17.13g %17.13g\n",
             cmdl->l_to_g_xform[0][0],
             cmdl->l_to_g_xform[0][1],
             cmdl->l_to_g_xform[0][2],
             cmdl->l_to_g_xform[0][3]);
      fprintf(coupling_model_desc->coupling_time_fp, "%17.13g %17.13g %17.13g %17.13g\n",
             cmdl->l_to_g_xform[1][0],
             cmdl->l_to_g_xform[1][1],
             cmdl->l_to_g_xform[1][2],
             cmdl->l_to_g_xform[1][3]);
      fprintf(coupling_model_desc->coupling_time_fp, "%17.13g %17.13g %17.13g %17.13g\n",
             cmdl->l_to_g_xform[2][0],
             cmdl->l_to_g_xform[2][1],
             cmdl->l_to_g_xform[2][2],
             cmdl->l_to_g_xform[2][3]);
      fprintf(coupling_model_desc->coupling_time_fp, "%17.13g %17.13g %17.13g %17.13g\n\n",
             cmdl->l_to_g_xform[3][0],
             cmdl->l_to_g_xform[3][1],
             cmdl->l_to_g_xform[3][2],
             cmdl->l_to_g_xform[3][3]);
      eCDI_POWERTHERM_CALCULATION_TYPE::Enum calc_type = static_cast<eCDI_POWERTHERM_CALCULATION_TYPE::Enum>(cmdl->m_calculation_type);
      std::string calc_type_name = GetCDIEnumValueDescription(calc_type);
      fprintf(coupling_model_desc->coupling_time_fp,"calculation_type %s\n\n", calc_type_name.c_str());
      fprintf(coupling_model_desc->coupling_time_fp,"coupling_table\n");
      fprintf(coupling_model_desc->coupling_time_fp,"#file_name   PT_start(s)   PT_end(s)   PT_step(s)    PT-PF_ratio   phase_index   PF_start(ts)  PF_end(ts)\n");
      fflush(coupling_model_desc->coupling_time_fp);
    }
    coupling_model_desc++;
  }
}

VOID backup_init_log_file(COUPLING_MODEL_DESC coupling_model_desc) 
{
  CHARACTER init_log_filename[PLATFORM_MAXPATHLEN];

  if (!coupling_model_desc)
    return;

  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_desc->coupling_model_index;
  cSTRING log_file_name = surface_coupling->tpi->GetLogFilePath();
  if (!log_file_name) {
    msg_warn("No log file defined for coupling model %d",coupling_model_desc->coupling_model_index);
    return;
  }

  memset(init_log_filename,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
  // look for the _cur
  cSTRING cptr = strrchr(log_file_name,'_');
  if (cptr) {
    strncpy(init_log_filename,log_file_name, strlen(log_file_name)-strlen(cptr));
  } else {
    msg_internal_error("Unexpected log filename \"%s\" encountered for coupling model %d", log_file_name, coupling_model_desc->coupling_model_index);
  }
  strcat(init_log_filename,"_init.log");

  remove(init_log_filename); 
  if (!platform_copy_file(log_file_name, init_log_filename)) {
    msg_warn("Cannot copy file \"%s\" to \"%s\"", log_file_name,init_log_filename);
  }
} 

VOID backup_log_file(COUPLING_MODEL_DESC coupling_model_desc) 
{
  CHARACTER backup_filename[PLATFORM_MAXPATHLEN];
  CHARACTER model_number[PLATFORM_MAXPATHLEN];
  CHARACTER csv_backup_filename[PLATFORM_MAXPATHLEN];
  CHARACTER csv_file_name[PLATFORM_MAXPATHLEN];

  if (!coupling_model_desc)
    return;

  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_desc->coupling_model_index;
  cSTRING log_file_name = surface_coupling->tpi->GetLogFilePath();
  if (!log_file_name) {
    msg_warn("No log file defined for coupling model %d",coupling_model_desc->coupling_model_index);
    return;
  }

  // Derive the battery CSV file name by replacing "_cur.log" with "_cur.tdf.eqc.transinit.csv"
  strcpy(csv_file_name, log_file_name);
  char* cur_log_pos = strstr(csv_file_name, "_cur.log");
  if (cur_log_pos) {
    strcpy(cur_log_pos, "_cur.tdf.eqc.transinit.csv");
  } else {
    msg_warn("Unexpected log file format: \"%s\"", log_file_name);
    return;
  }


  // find occurrence of _cur, and replace with number 001 etc 
  sprintf(model_number,"%03d", coupling_model_desc->n_coupling_models_read);
  replace_coupling_file_current_suffix(log_file_name, model_number, backup_filename);

  remove(backup_filename); 
  if (!platform_copy_file(log_file_name, backup_filename)) {
    msg_warn("Cannot copy file \"%s\" to \"%s\"", 
             log_file_name,backup_filename);
  }

  // Backup the CSV file if present
  if (platform_file_present(csv_file_name)) {
    replace_coupling_file_current_suffix(csv_file_name, model_number, csv_backup_filename);
    remove(csv_backup_filename);
    if (!platform_copy_file(csv_file_name, csv_backup_filename)) {
      msg_warn("Cannot copy file \"%s\" to \"%s\"", csv_file_name, csv_backup_filename);
    }
  }

} 

VOID make_current_coupling_links(COUPLING_MODEL_DESC coupling_model_desc) 
{
  CHARACTER link_file_name[PLATFORM_MAXPATHLEN];
  CHARACTER base_name[PLATFORM_MAXPATHLEN];
  CHARACTER model_number[10];
  eTPI_DB_ORGANIZATION organization;
  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_desc->coupling_model_index;
  CDI_CMDL cmdl = surface_coupling->get_cmdl();
  BOOLEAN model_has_units_p;

  sprintf(model_number,"%03d", coupling_model_desc->n_coupling_models_read);

  // find last occurrence of _cur, and replace with number 001 etc 
  replace_coupling_file_current_suffix(coupling_model_desc->model_filename, model_number, link_file_name);

  remove(link_file_name);
  platform_get_file_base_name(coupling_model_desc->model_filename, base_name);
  if (-1 == symlink(base_name, link_file_name)) {
    msg_warn("Could not create a symbolic link from \"%s\" to \"%s\": %s", 
       link_file_name, coupling_model_desc->model_filename, strerror(errno));
  }

  // make a link to the current log file 
  if (surface_coupling->tpi->GetLogFilePath()) {
    replace_coupling_file_current_suffix(surface_coupling->tpi->GetLogFilePath(), model_number, link_file_name);

    remove(link_file_name);
    platform_get_file_base_name(surface_coupling->tpi->GetLogFilePath(), base_name);
    if (-1 == symlink(base_name, link_file_name)) {
      msg_warn("Could not create a symbolic link from \"%s\" to \"%s\": %s", 
         link_file_name, surface_coupling->tpi->GetLogFilePath(), strerror(errno));
    }
  }

  if (IS_TAI_APP(cmdl->model_type)) {
    // link to viewfactor file
    replace_coupling_file_current_suffix(coupling_model_desc->viewfactor_filename, model_number, link_file_name);

    remove(link_file_name);
    platform_get_file_base_name(coupling_model_desc->viewfactor_filename, base_name);
    if (-1 == symlink(base_name, link_file_name)) {
      msg_warn("Could not create a symbolic link from \"%s\" to \"%s\": %s",
         link_file_name, coupling_model_desc->viewfactor_filename, strerror(errno));
    }
    // link to slf file
    if (platform_file_present(coupling_model_desc->slf_filename)) {
      replace_coupling_file_current_suffix(coupling_model_desc->slf_filename, model_number, link_file_name);

      remove(link_file_name);
      platform_get_file_base_name(coupling_model_desc->slf_filename, base_name);
      if (-1 == symlink(base_name, link_file_name)) {
        msg_warn("Could not create a symbolic link from \"%s\" to \"%s\": %s",
                 link_file_name, coupling_model_desc->slf_filename, strerror(errno));
      }
    }
    // link to tlf file
    if (platform_file_present(coupling_model_desc->tlf_filename)) {
      replace_coupling_file_current_suffix(coupling_model_desc->tlf_filename, model_number, link_file_name);

      remove(link_file_name);
      platform_get_file_base_name(coupling_model_desc->tlf_filename, base_name);
      if (-1 == symlink(base_name, link_file_name)) {
        msg_warn("Could not create a symbolic link from \"%s\" to \"%s\": %s",
                 link_file_name, coupling_model_desc->tlf_filename, strerror(errno));
      }
    }

  }

  // Make a "bak" copy of the results file if ptherm_only_keep_orig_model is set and there is
  // a delay between launching the coupling job and using the results in PowerFLOW. The "bak"
  // copy is necessary if a checkpoint occurs between the launch of the application and PowerFLOW 
  // beginning to use the generated results.
  if (cp_info.ptherm_only_keep_orig_model) {
    CDI_CMDL cmdl = cp_info.surface_couplings[coupling_model_desc->coupling_model_index].get_cmdl();

    // TODO: add a variable current_coupling_phase to cp_info(?) to tell cp which coupling phase it is in
    // then use the delay for that phase to determine whether to create "bak" copies
    // Here we always creat the bak copy for simplicity
    // if (cmdl->delay > 0) 
    if (1) {
      char bak_filename[PLATFORM_MAXPATHLEN];
      replace_coupling_file_current_suffix(coupling_model_desc->results_filename, 
                                           "bak", 
                                           bak_filename);
      if (!platform_copy_file(coupling_model_desc->results_filename, bak_filename))
        msg_warn("Cannot copy \"%s\" to \"%s\" prior to launch of %s", 
                 coupling_model_desc->model_filename, bak_filename, cmdl->model_type);
      else
        msg_print("Copying \"%s\" to \"%s\" prior to launch of %s", 
                  coupling_model_desc->results_filename, bak_filename, cmdl->model_type);
    }
  }

  cTHIRD_PARTY_INTERFACE *coupling_interface = 
    cp_info.surface_couplings[coupling_model_desc->coupling_model_index].tpi;
  coupling_interface->GetDatabaseInfo(organization, model_has_units_p);
  if (TPI_ONE_FILE == organization)
    return;

  // make links to the results file
  replace_coupling_file_current_suffix(coupling_model_desc->results_filename, model_number, link_file_name);

  remove(link_file_name);
  platform_get_file_base_name(coupling_model_desc->results_filename, base_name);
  if (-1 == symlink(base_name, link_file_name)) {
    msg_warn("Could not create a symbolic link from \"%s\" to \"%s\": %s",
             link_file_name, coupling_model_desc->results_filename, strerror(errno));
  }
}

VOID backup_current_coupling_link(COUPLING_MODEL_DESC coupling_model_desc)
{
  CHARACTER link_file_name[PLATFORM_MAXPATHLEN];
  CHARACTER copy_file_name[PLATFORM_MAXPATHLEN];
  CHARACTER model_number[PLATFORM_MAXPATHLEN];
  eTPI_DB_ORGANIZATION organization;
  BOOLEAN model_has_units_p;
#define BACKUP_COPY_RETRY_SECONDS 60

  if (!coupling_model_desc)
    return;

  if (!coupling_model_desc->model_filename || 
      !coupling_model_desc->results_filename ||
      !coupling_model_desc->viewfactor_filename)
    return;

  memset(link_file_name,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
  memset(copy_file_name,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
  memset(model_number,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
  BOOLEAN keep_original_only_p = FALSE;

  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_desc->coupling_model_index;
  CDI_CMDL cmdl = surface_coupling->get_cmdl();
  if (IS_TAI_APP(cmdl->model_type)) {
    keep_original_only_p = cp_info.ptherm_only_keep_orig_model;
  } else {
    msg_internal_error("Unknown model type %s encountered while making backup copies of the coupling model files",
                       cmdl->model_type);
    return;
  }

  sprintf(model_number, "%03d", coupling_model_desc->n_coupling_models_read);

  // find occurrence of _cur, and replace with number 001 etc 
  replace_coupling_file_current_suffix(coupling_model_desc->model_filename, model_number, link_file_name);

  if (keep_original_only_p) {
    if (remove(link_file_name) != 0)
      msg_warn("Cannot remove \"%s\": %s", link_file_name, strerror(errno));
  } else {
    strcpy(copy_file_name, link_file_name);
    strcat(copy_file_name, ".copy");
    BOOLEAN print_warning = TRUE;
    while (!platform_copy_file(coupling_model_desc->model_filename, copy_file_name)) {
      if (print_warning) {
        msg_warn("Could not copy \"%s\" to \"%s\". Retrying until successful...",
                 coupling_model_desc->model_filename, copy_file_name);
        print_warning = FALSE;
      }
      platform_sleep_seconds(BACKUP_COPY_RETRY_SECONDS);
    }
    if (-1 == rename(copy_file_name, link_file_name)) {
      msg_warn("Could not rename \"%s\" to \"%s\": %s",
               copy_file_name, link_file_name, strerror((errno)));
    }
  }

  if (IS_TAI_APP(cmdl->model_type)) {
    // link to viewfactor file
    replace_coupling_file_current_suffix(coupling_model_desc->viewfactor_filename, model_number, link_file_name);

    if (remove(link_file_name) != 0) {
      msg_warn("Could not remove \"%s\": %s", link_file_name, strerror(errno));
    }
    // link to slf file
    if (platform_file_present(coupling_model_desc->slf_filename)) {
      replace_coupling_file_current_suffix(coupling_model_desc->slf_filename, model_number, link_file_name);

      if (remove(link_file_name) != 0) {
        msg_warn("Could not remove \"%s\": %s", link_file_name, strerror(errno));
      }
    }
    // link to tlf file
    if (platform_file_present(coupling_model_desc->tlf_filename)) {
      replace_coupling_file_current_suffix(coupling_model_desc->tlf_filename, model_number, link_file_name);

      if (remove(link_file_name) != 0) {
        msg_warn("Could not remove \"%s\": %s", link_file_name, strerror(errno));
      }
    }
  } 

  cTHIRD_PARTY_INTERFACE *coupling_interface = 
    cp_info.surface_couplings[coupling_model_desc->coupling_model_index].tpi;
  coupling_interface->GetDatabaseInfo(organization,model_has_units_p);
  if (TPI_ONE_FILE == organization)
    return;

  // make links to the results file also
  replace_coupling_file_current_suffix(coupling_model_desc->results_filename, model_number, link_file_name);

  if (keep_original_only_p) {
    if (remove(link_file_name) != 0) {
      msg_warn("Could not remove \"%s\": %s", link_file_name, strerror(errno));
    }
  } else {
    strcpy(copy_file_name, link_file_name);
    strcat(copy_file_name, ".copy");
    BOOLEAN print_warning = TRUE;
    while (!platform_copy_file(coupling_model_desc->results_filename, copy_file_name)) { 
      if (print_warning) {
        msg_warn("Could not copy \"%s\" to \"%s\". Retrying until successful...",
                 coupling_model_desc->results_filename, copy_file_name);
        print_warning = FALSE;
      }
      platform_sleep_seconds(BACKUP_COPY_RETRY_SECONDS);
    }
    if (-1 == rename(copy_file_name, link_file_name)) {
      msg_warn("Could not rename \"%s\" to \"%s\": %s", 
               copy_file_name, link_file_name, strerror(errno));
    }
  }
#undef BACKUP_COPY_RETRY_SECONDS 
}


#if 0
VOID maybe_launch_coupling_app_on_ckpt_restore(cBOOLEAN is_checkpoint_restore_p)
{
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
  CP_MEAS_WINDOW window;
  const int SECONDS_BETWEEN_LICENSE_CHECKS = 10;
  if (!is_checkpoint_restore_p)
    return;

  ccDOTIMES(i,cp_info.n_surface_couplings) {
    sINT32 model_index = coupling_model_desc->coupling_model_index;
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+model_index;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;
    SIM_ABSTRACT_SURFACE source_surf = surface_coupling->native_resulting_source_surface;
    if (!source_surf || source_surf->NumFacetFaces() == 0) {
      if (surface_coupling->output_timestep > coupling_model_desc->next_periodic_read
          && coupling_model_desc->next_periodic_read > cp_info.restart_time) {
        run_surface_coupling_job(NULL,model_index,surface_coupling->output_timestep);
      }
    } else  {
      if (NULL == (window = get_coupling_window(model_index))) {
        msg_internal_error("No window associated with coupling model index %d",model_index);
      }
      // Only run the coupled application if the checkpoint occurred between the
      // the launch of the application and PowerFLOW beginning to use the generated
      // results. This only occurs if:
      //
      //      ckpt_time < next_read_of_app_results < next_launch_of_app
      //
      TIMESTEP window_output_timestep = window->m_output_timestep/cp_info.n_user_base_steps;
      if ((window_output_timestep > coupling_model_desc->next_periodic_read
           && coupling_model_desc->next_periodic_read > cp_info.restart_time) {
          run_surface_coupling_job(window,model_index,window_output_timestep);
          }
    }
    coupling_model_desc++;
  } // finished checking all coupling models
}
#endif

VOID maybe_wait_for_surface_coupling_jobs()
{
  const WALLCLOCK_TIME_SECS SECS_BETWEEN_COUPLING_MODEL_DATA_CHECKS = 1;
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
  ccDOTIMES(i,cp_info.n_surface_couplings) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_desc->coupling_model_index;
    cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    BOOLEAN print_message = TRUE;
    while (coupling_interface->GetJobStatus() == TPI_JOB_IN_PROGRESS) {
      if (print_message) {
        msg_print("Waiting for %s job to complete (model \"%s\")",
                  cmdl->model_type, coupling_model_desc->model_filename);
        platform_sleep_seconds(SECS_BETWEEN_COUPLING_MODEL_DATA_CHECKS);
        print_message = FALSE;
      }
    }
    if (coupling_interface->NumMessages() > 0) {
      dump_tpi_messages(coupling_interface, cmdl->model_filename);
    }
    eTPI_JOB_STATUS job_status = coupling_interface->GetJobStatus();
    if (job_status == TPI_JOB_COMPLETED_SUCCESS) {
      msg_print("%s job completed (model \"%s\")", 
                cmdl->model_type, coupling_model_desc->model_filename);
      backup_coupling_model_files(coupling_model_desc, coupling_model_desc->next_periodic_read);
    } else if (TPI_JOB_NOT_SUBMITTED != job_status) {
      msg_print("%s job (model \"%s\") exited abnormally: %s. Check the log file for details.",
                cmdl->model_type, coupling_model_desc->model_filename,
                coupling_interface->GetApplicationExitMessage());
    }
    coupling_interface->ShutdownClient();
    coupling_interface->ResetJobStatus();
    coupling_model_desc++;
  }
}

VOID maybe_terminate_surface_coupling_jobs()
{
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
  // maybe called by an error condition before the model has been built 
  if (cp_info.n_surface_couplings <= 0 || !coupling_model_desc)
    return;

  ccDOTIMES(i,cp_info.n_surface_couplings) {
    cTHIRD_PARTY_INTERFACE *coupling_interface = 
      cp_info.surface_couplings[i].tpi;
    if (!coupling_interface)
      continue;
    if (coupling_interface->GetJobStatus() == TPI_JOB_IN_PROGRESS) {
      // if terminate fails, issue a warning and continue
      if (coupling_interface->TerminateJob() != TPI_JOB_TERMINATE_SUCCESS) {
        msg_warn("Unable to terminate external application for model \"%s\"",
                 coupling_model_desc->model_filename);
      }
    }
    coupling_model_desc++;
  }
  close_coupling_model_summary_files();
}

BOOLEAN skip_first_coupling(COUPLING_MODEL_DESC coupling_model_desc, 
          asINT32 model_index, TIMESTEP current_time, TIMESTEP restart_time)
{
  if (restart_time <= 0 )
    return FALSE;

  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+model_index;
  CDI_CMDL cmdl = surface_coupling->get_cmdl();

  if (IS_TAI_APP(cmdl->model_type)) {
    if (cp_info.ptherm_ckpt_skip_first_coupling_p && (current_time > restart_time) && 
        !coupling_model_desc->skipped_first_coupling_p) {
      coupling_model_desc->skipped_first_coupling_p = TRUE;
      return TRUE;
    }
  } else {
    msg_warn("Unknown model type %s encountered while making backup copy of coupling model file",cmdl->model_type);
    return FALSE;
  }
  return FALSE;
}

BOOLEAN skip_ckpt_read_p(COUPLING_MODEL_DESC coupling_model_desc)
{
  sINT32 model_index = coupling_model_desc->coupling_model_index;
  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+model_index;
  CDI_CMDL cmdl = surface_coupling->get_cmdl();
  if (IS_TAI_APP(cmdl->model_type)) {
    if (cp_info.ptherm_model_changed_p) {
      return TRUE;
    }
  } else {
    msg_warn("Unknown model type %s encountered while making backup copy of coupling model file",cmdl->model_type);
    return FALSE;
  }
  return FALSE;
}

#endif // SURF_COUP
