﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_with_symbols_64|Win32">
      <Configuration>Release_with_symbols_64</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_with_symbols_64|x64">
      <Configuration>Release_with_symbols_64</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_with_symbols|Win32">
      <Configuration>Release_with_symbols</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_with_symbols|x64">
      <Configuration>Release_with_symbols</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <RootNamespace>cdi</RootNamespace>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{25279A3C-1EDD-4822-B1C2-A6982DC0E439}</ProjectGuid>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols_64|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v120</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v120</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v120</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols_64|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v120</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols_64|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols_64|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>12.0.21005.1</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)</OutDir>
    <IntDir>x86_vs2008_mdd\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)</OutDir>
    <IntDir>x86_vs2008_md\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols|Win32'">
    <OutDir>$(SolutionDir)</OutDir>
    <IntDir>x86_vs2008_md\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols_64|x64'">
    <OutDir>$(SolutionDir)</OutDir>
    <IntDir>amd64_vs2008_md\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>__AMD64_VS2008_MD__;__MSVC__;WINNT;NOMINMAX;EXA_CURRENT_YEAR=2015;CDI_VERSION=413-rstairs-03;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <AdditionalIncludeDirectories>..\amd64_vs2008_md;..;.;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ProgramDataBaseFileName>cdi.pdb</ProgramDataBaseFileName>
      <AdditionalOptions>/EHsc /nologo /MP8 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <MinimalRebuild>true</MinimalRebuild>
      <WarningLevel>Level2</WarningLevel>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PrecompiledHeader />
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OutputFile />
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <TargetMachine>MachineX86</TargetMachine>
      <ProgramDatabaseFile />
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
    </Link>
    <Lib>
      <OutputFile>$(IntDir)cdi.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>__AMD64_VS2008_MD__;__MSVC__;WINNT;NDEBUG;NOMINMAX;QT_NO_DEBUG;EXA_CURRENT_YEAR=2015;CDI_VERSION=413-rstairs-03;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <AdditionalIncludeDirectories>..\amd64_vs2008_md;..;.;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat />
      <ProgramDataBaseFileName>cdi.pdb</ProgramDataBaseFileName>
      <AdditionalOptions>/EHsc /nologo /MP8 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Full</Optimization>
      <MinimalRebuild>false</MinimalRebuild>
      <WarningLevel>Level2</WarningLevel>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PrecompiledHeader />
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OutputFile />
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <TargetMachine>MachineX86</TargetMachine>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
    </Link>
    <Lib>
      <OutputFile>$(IntDir)cdi.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>__AMD64_VS2008_MD__;__MSVC__;WINNT;NDEBUG;NOMINMAX;QT_NO_DEBUG;EXA_CURRENT_YEAR=2015;CDI_VERSION=413-rstairs-03;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <AdditionalIncludeDirectories>..\amd64_vs2008_md;..;.;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ProgramDataBaseFileName>cdi.pdb</ProgramDataBaseFileName>
      <AdditionalOptions>/EHsc /nologo /MP8 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <MinimalRebuild>true</MinimalRebuild>
      <WarningLevel>Level2</WarningLevel>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PrecompiledHeader />
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OutputFile />
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <TargetMachine>MachineX86</TargetMachine>
      <ProgramDatabaseFile />
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
    </Link>
    <Lib>
      <OutputFile>$(IntDir)cdi.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols_64|x64'">
    <ClCompile>
      <PreprocessorDefinitions>__AMD64_VS2008_MD__;__MSVC__;WINNT;NDEBUG;NOMINMAX;QT_NO_DEBUG;EXA_CURRENT_YEAR=2015;CDI_VERSION=413-rstairs-03;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <AdditionalIncludeDirectories>..\amd64_vs2008_md;..;.;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ProgramDataBaseFileName>cdi.pdb</ProgramDataBaseFileName>
      <AdditionalOptions>/EHsc /nologo /MP8 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <MinimalRebuild>true</MinimalRebuild>
      <WarningLevel>Level2</WarningLevel>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PrecompiledHeader />
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OutputFile />
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <TargetMachine>MachineX64</TargetMachine>
      <ProgramDatabaseFile />
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
    </Link>
    <Lib>
      <OutputFile>$(IntDir)cdi.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="cdi_accessers.cc" />
    <ClCompile Include="cdi_get.cc" />
    <ClCompile Include="cdi_interface.cc" />
    <ClCompile Include="cdi_io.cc" />
    <ClCompile Include="cdi_physics.cc" />
    <ClCompile Include="cdi_readwrite.cc" />
    <ClCompile Include="cdi_tempDepParms.cc" />
    <ClCompile Include="dump_cdi.cc" />
    <ClCompile Include="dump_cdi_lrf.cc" />
    <ClCompile Include="lexer.cc" />
    <ClCompile Include="pri_support.cc" />
    <ClCompile Include="test_cdi.cc" />
    <ClCompile Include="test_lexer.cc" />
    <ClCompile Include="undump_cdi.cc" />
    <ClCompile Include="ignore_me.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols_64|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release_with_symbols|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="cdi_accessers.h" />
    <ClInclude Include="cdi_common.h" />
    <ClInclude Include="cdi_export.h" />
    <ClInclude Include="cdi_get.h" />
    <ClInclude Include="cdi_interface.h" />
    <ClInclude Include="cdi_internal.h" />
    <ClInclude Include="cdi_io.h" />
    <ClInclude Include="cdi_physics.h" />
    <ClInclude Include="cdi_readwrite.h" />
    <ClInclude Include="cdi_tempDepParms.h" />
    <ClInclude Include="lexer.h" />
    <ClInclude Include="pri_support.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>