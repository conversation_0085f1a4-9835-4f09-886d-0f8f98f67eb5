/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#ifndef __COUPLING_MODEL_H
#define __COUPLING_MODEL_H

#if SURF_COUP

#include "common.h"
#include "surface_coupling.h"
#include TPI_H

struct sCDI_MEAS_WINDOW; // forward reference
struct sCP_MEAS_WINDOW;  // forward reference

typedef struct sCOUPLING_PHASE_DESC {
  asINT32 start;
  asINT32 period;
  asINT32 delay;
} *COUPLING_PHASE_DESC;

typedef struct sPT_PF_RATIO_RECORD {
  TIMESTEP timestep;
  sFLOAT  ratio_scale_factor;
  sPT_PF_RATIO_RECORD () {};
  sPT_PF_RATIO_RECORD (TIMESTEP t, sFLOAT factor): timestep(t), ratio_scale_factor(factor) {}
} *PT_PF_RATIO_RECORD;

typedef std::vector<sPT_PF_RATIO_RECORD> sPT_PF_RATIO_RECORDS;

typedef struct sSURFEL_INDEX_MAP {
  STP_SURFEL_ID surfel_id;
  STP_SURFEL_ID surfel_index;
} *SURFEL_INDEX_MAP;

typedef struct sCOUPLING_MODEL_DESC {
  sINT32 coupling_model_index;          // index into the list of coupling models 
  // the following two names are cached for ease of access
  STRING model_filename;                // model filename 
  STRING results_filename;              // results filename 
  STRING viewfactor_filename;           // viewfactor filename 
  STRING slf_filename;                  // Solar lamp array filename 
  STRING tlf_filename;                  // Thermal link filename 
  STRING summary_filename;              // summary of coupling iterations 
  FILE *summary_fp;                     // filename pointer to summary
  STRING gradient_filename;             // PowerTHERM temperature gradient filename
  FILE *gradient_fp;
  STRING coupling_time_filename;     // file containing the coupling time information (for PowerVIZ)
  FILE *coupling_time_fp;
  sINT32 n_coupling_models_read;	// how many time have we read results from a run launched by the CP
  sINT32 n_runs_launched;
  sINT32 n_runs_before_ckpt_restore;
  cBOOLEAN couple_during_sim_p;         // read coupling data during simulation
  cBOOLEAN skipped_first_coupling_p;    // skip the first coupling iteration after resuming from checkpoint 
  cBOOLEAN init_pf_bc_coupling_p;       // initialize powerflow bc from coupling model data 
  cBOOLEAN read_results_from_ckpt_2_file;
  cBOOLEAN copy_current_results_to_full_ckpt_2_on_job_completion;
  cBOOLEAN copy_current_results_to_mme_ckpt_2_on_job_completion;
  TIMESTEP full_ckpt_2_timestep;
  TIMESTEP mme_ckpt_2_timestep;
  // need to duplicate these since these could be different from the values
  // stored in the cp_info.cmdls and also the CDI meas window parameters (for
  // e.g. staggered coupling )
  TIMESTEP first_interval;
  TIMESTEP period;        		// Period after the first interval
  TIMESTEP end_time;
  TIMESTEP next_periodic_read;		// Initialized to first_interval, and incremented by period
  TIMESTEP last_results_read_time;	// Set whenever n_coupling_models_read incremented
  TIMESTEP last_run_read_time;		// Set whenever n_runs_launched incremented
  WALLCLOCK_TIME_SECS last_check_time;
  sCDI_MEAS_WINDOW *cdi_meas_window;	// associated pseudo-CDI meas window
  sCP_MEAS_WINDOW *window;
  // store list of surfel ids on each sp for this coupling model
  asINT32 *n_surfels_per_sp;
  STP_SURFEL_ID **surfel_ids_per_sp;
  sSURFEL_INDEX_MAP **surfel_index_map_per_sp; 
  asINT32 *nsurfels_per_target;         // number of surfels per PF target face
  asINT32 **buf_indices_per_target;     // indices into the coupling data buffer
  asINT32 **proc_indices_per_target;    // sp indices for each target surfel
  cBOOLEAN **is_front_per_target;       // front/back surfel flag
  // for multiple coupling phases
  std::vector<sCOUPLING_PHASE_DESC> m_coupling_phase_descs; // interval is not needed for coupling
  std::vector<sPT_PF_RATIO_RECORDS> m_coupling_phase_ratios;  // for each phase there is a set of history ratio scale factor records
  sFLOAT          m_previous_gradient;                    // T gradient value from the previous coupling where the ratio is changed
  sFLOAT          m_current_ratio_scale_factor;
} *COUPLING_MODEL_DESC;

typedef struct sCOUPLING_MODEL_QUEUE_ENTRY {
  COUPLING_MODEL_DESC 		coupling_model_desc;
  cBOOLEAN 			waiting_msg_issued;
  cBOOLEAN 			license_wait_msg_issued;
  cBOOLEAN                      is_periodic_read;
  // Store a read time here distinct from the coupling_model desc read time to allow for
  // async requests via exasignal.
  TIMESTEP 			read_time;
  // indicates whether data is available to read from the external application
  cBOOLEAN 			result_available_p;          
  STP_PROC			next_sp_to_test;
  sCOUPLING_MODEL_QUEUE_ENTRY	*next;

} *COUPLING_MODEL_QUEUE_ENTRY;  

typedef struct sCP_COUPLING_MODEL_COMM_DESC {
  cBOOLEAN coupling_data_sent_p;

  sFLOAT *coupling_data_buf;

  // MPI info
  // These send requests are used to track the progress of the coupling model
  // data sent to an SP
  MPI_Request    mpi_request;
} *CP_COUPLING_MODEL_COMM_DESC;

VOID dump_tpi_messages(cTHIRD_PARTY_INTERFACE *tpi, cSTRING msg_prefix,
    BOOLEAN unique_messages_only_p = TRUE);
enum eMODEL_LOCATION {
  MODEL_LOCATION_INVALID = -1,
  MODEL_LOCATION_SEARCH_PATH = 0,
  MODEL_LOCATION_CDI_RELATIVE = 1,
  MODEL_LOCATION_CDI_ABSOLUTE = 2,
  MODEL_LOCATION_RUNDIR = 3,
  MODEL_LOCATION_MODEL_SUBDIR = 4
};

BOOLEAN is_descendant_of_rundir(cSTRING file_path);
eMODEL_LOCATION locate_coupling_file(STRING coupling_filename, 
                                     cSTRING abs_coupling_filename, 
                                     cSTRING model_subdir, // "powertherm", "amesim", ...
                                     cSTRING search_path,
                                     STRING fullname); // result of this fcn
VOID compose_viewfactor_filename(cSTRING coupling_file, STRING viewfactor_file);
VOID compose_slf_filename(cSTRING coupling_file, STRING slf_file);
VOID compose_tlf_filename(cSTRING coupling_file, STRING tlf_file);
VOID compose_batteryconfig_filename(cSTRING coupling_file, STRING batteryconfig_file);
VOID copy_initial_powertherm_coupling_files(STRING coupling_file, STRING abs_coupling_file,
					    cSTRING app_type, BOOLEAN is_ckpt_restore_p, 
					    BOOLEAN is_full_ckpt_restore_p, STRING tpi_file, STRING tdf_dir, STRING tdf_file);

VOID check_distributed_solver(cSTRING tpi_file, asINT32 model_index);
VOID maybe_copy_human_comfort_files(cSTRING tpi_file, cSTRING orig_model_file_dir, asINT32 model_index, BOOLEAN is_full_ckpt_restore_p);
VOID maybe_copy_battery_config_file(cSTRING tdf_file, cSTRING tpi_file, cSTRING orig_model_file_dir, asINT32 model_index, BOOLEAN is_ckpt_restore_p, BOOLEAN is_full_ckpt_restore_p);
VOID maybe_warn_disable_adaptive_stepsize(cSTRING tpi_file, asINT32 model_index);
VOID make_current_coupling_links(COUPLING_MODEL_DESC coupling_model_desc);
VOID backup_current_coupling_link(COUPLING_MODEL_DESC coupling_model_desc);

VOID unit_map(eTPI_VARIABLE_TYPE var_type, cSTRING var_expr, 
              cBOOLEAN to_lattice_p, dFLOAT *u_slope, dFLOAT *u_offset);
VOID maybe_read_coupling_models();
VOID maybe_terminate_surface_coupling_jobs();
VOID initialize_coupling_model_queue();
VOID allocate_shared_export_var_array();
VOID maybe_wait_for_surface_coupling_jobs();
VOID open_coupling_model_summary_files(BOOLEAN is_checkpoint_restore_p);
VOID close_coupling_model_summary_files();
VOID backup_init_log_file(COUPLING_MODEL_DESC coupling_model_desc);
VOID backup_log_file(COUPLING_MODEL_DESC coupling_model_desc);
VOID ckpt_surface_coupling_models(TIMESTEP ckpt_timestep, BOOLEAN is_full_checkpoint_p); 
BOOLEAN skip_first_coupling(COUPLING_MODEL_DESC coupling_model_desc, 
                            asINT32 model_index, TIMESTEP current_time, TIMESTEP restart_time);
BOOLEAN skip_ckpt_read_p(COUPLING_MODEL_DESC coupling_model_desc);
VOID check_coupling_dependent_files(cTHIRD_PARTY_INTERFACE *coupling_interface,
				    STRING coupling_file, STRING app_type);

STRING replace_coupling_file_current_suffix(cSTRING current_filename, cSTRING suffix, 
               char result_filename[PLATFORM_MAXPATHLEN]);

#define IS_TAI_APP(mType)\
      (!strcmp(mType,"PowerTHERM") || !strcmp(mType,"RadTherm"))

#define IS_NOT_TAI_APP(mType)\
      (strcmp(mType,"PowerTHERM") && strcmp(mType,"RadTherm"))

#endif // SURF_COUP
#endif /* __COUPLING_MODEL_H */
