/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 *
 * Checkpoint support
 *
 *--------------------------------------------------------------------------*/

#include "checkpoint_control.h"
#include "common.h"
#include "full_ckpt.h"
#include "mme_ckpt.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "jobctl.h"
#include "exa_sim.h"

/* These return TRUE depending on the current SP checkpoint state */
static BOOLEAN async_checkpoint_ready_p(VOID)
{
  static asINT32 next_sp_to_probe = 0;

  ccDO_FROM_BELOW(i, next_sp_to_probe, total_sps) {
    MPI_Status eMPI_status;
    int new_status;
    MPI_Iprobe(i, eMPI_ASYNC_CKPT_READY_TAG, eMPI_sp_cp_comm, &new_status, &eMPI_status);
    if(new_status) {
      MPI_Recv(nullptr, 0, eMPI_BOOLEAN, i,
               eMPI_ASYNC_CKPT_READY_TAG, eMPI_sp_cp_comm, &eMPI_status);
    } else {
      next_sp_to_probe = i;
      return FALSE;
    }
  }
  next_sp_to_probe = 0; // reset
  return TRUE;
}


#if 0
/* Updates the next_ckpt_time slot in all SPs */
/*  Assumes cp_info.cp_status holds the current timestep */
static VOID checkpoint_update_sps(VOID)
{
  cp_info.last_next_ckpt_time = cp_info.next_ckpt_time;
  cp_info.next_ckpt_time = compute_next_time(cp_info.next_ckpt_time, cp_info.next_ckpt_time,
                                             cp_info.ckpt_time_descs_count, cp_info.ckpt_time_descs,
                                             NULL, NULL);

  ccDOTIMES(i, total_sps) {
    MPI_Send(&cp_info.next_ckpt_time, 1, eMPI_TIMESTEP,
             i, eMPI_NEXT_CKPT_TIME_TAG, eMPI_sp_cp_comm);
  }
}
#endif

/* Actually, writes the checkpoint data */
static BOOLEAN do_checkpoint(BOOLEAN full_ckpt_p, TIMESTEP ckpt_timestep, BOOLEAN precious, BOOLEAN is_avg_mme)
{

  CHARACTER status[256];

  start_ckpt_timer();
  /* Temporarily change status */
  if (cp_info.time == ckpt_timestep) {
    sprintf(status, "%s (%s checkpoint in progress)",
            cp_jobctl_status_string(),
            full_ckpt_p ? "full" : "mme");
  } else {
    // This variant of the message deals w/problems in the ilk of PR 3352,
    // because it is remotely possible that the SPs will get all their checkpoint
    // data sent upstream AND advance their timestep before we get here - not
    // probable, but better this than something that's confusing.  Also note,
    // in processing checkpoint events we know that "cp_info.time" will never
    // be less than ckpt_timestep, so we don't need to worry about that
    sprintf(status, "%s (%s checkpoint in progress with timestep %d data)",
            cp_jobctl_status_string(),
            full_ckpt_p ? "full" : "mme",
            ckpt_timestep);
  }
  cp_jobctl_output_status(status);

  char ckpt_filename[FILENAME_MAXLEN];             /* Checkpoint file */
  char tmp_filename[FILENAME_MAXLEN];              /* Temporary ckpt file */
  char cond_ckpt_filename[1024];             /* MME Checkpoint file for conduction */
  char cond_tmp_filename[1024];              /* Temporary ckpt file for conduction */
  cSTRING tmp_ext;
  cSTRING ckpt_ext;
  cSTRING tmp_ext_cond;
  cSTRING ckpt_ext_cond;

  if (full_ckpt_p) {
    tmp_ext =  CHECKPOINT_TMPFILE_EXT ;
    ckpt_ext = CHECKPOINT_FILE_EXT;
  } else {
    // checkpoint files are fluid files
    tmp_ext = "ctmp.fnc";
    ckpt_ext = "ckpt.fnc";
    tmp_ext_cond = "ctmp.vnc";
    ckpt_ext_cond = "ckpt.vnc";
  }

  if (is_avg_mme) {
    sprintf(tmp_filename, "%s", cp_info.avg_mme_ckpt_tmp_filename.c_str());
    sprintf(cond_tmp_filename, "%s", cp_info.avg_mme_ckpt_tmp_filename_cond.c_str());
    if (cp_info.avg_mme_ckpt_after_eit_p){
      sprintf(ckpt_filename, "%s", cp_info.avg_mme_ckpt_filename.c_str());
      sprintf(cond_ckpt_filename, "%s", cp_info.avg_mme_ckpt_filename_cond.c_str());
    }else{
      sprintf(ckpt_filename, "%s", cp_info.avg_mme_ckpt_preit_filename.c_str());
      sprintf(cond_ckpt_filename, "%s", cp_info.avg_mme_ckpt_preit_filename_cond.c_str());
    }
  } else if (full_ckpt_p){
    cSTRING root = cp_info.checkpoint_filename ? cp_info.checkpoint_filename : cp_info.root_filename;
    sprintf(tmp_filename, "%s.%s", root, tmp_ext);
    sprintf(ckpt_filename, "%s.%s", root, ckpt_ext);
  } else { 
    cSTRING root = cp_info.checkpoint_filename ? cp_info.checkpoint_filename : cp_info.root_filename;
    sprintf(tmp_filename, "%s.%s", root, tmp_ext);
    sprintf(ckpt_filename, "%s.%s", root, ckpt_ext);
    sprintf(cond_tmp_filename, "%s.%s", root, tmp_ext_cond);
    sprintf(cond_ckpt_filename, "%s.%s", root, ckpt_ext_cond);
  }
  /* Checkpoint */

  BOOLEAN success;

  sprintf(status, "Timestep %d", ckpt_timestep);

  if(full_ckpt_p) {
    success = write_full_ckpt(tmp_filename, ckpt_timestep, precious, status);
  }
  else {
    BOOLEAN success_flow = TRUE;
    BOOLEAN success_cond = TRUE;
    sriPOINT     n_voxels_written[STP_N_REALMS] = {0};
    sriPOINT     n_nw_voxels_written[STP_N_REALMS] = {0};
    if (cp_info.is_flow)
      success_flow = write_mme_ckpt(tmp_filename, ckpt_timestep, precious, status, is_avg_mme, STP_FLOW_REALM, n_voxels_written, n_nw_voxels_written);
    if (cp_info.is_conduction)
      success_cond = write_mme_ckpt(cond_tmp_filename, ckpt_timestep, precious, status, is_avg_mme, STP_COND_REALM, n_voxels_written, n_nw_voxels_written);
    
    success = success_flow && success_cond; 
    //below is moved outside of write_mme_ckpt_voxel_data
    sriPOINT n_voxels_written_both_realms = n_voxels_written[STP_FLOW_REALM] + n_voxels_written[STP_COND_REALM];

    if (n_voxels_written[STP_FLOW_REALM] != cp_info.total_fluid_like_voxels_in_each_realm[STP_FLOW_REALM])
      msg_warn("Inconsistency in number of voxels written to Fluid checkpoint file %ld with total number of fluid like voxels in flow realm %ld",
               n_voxels_written[STP_FLOW_REALM], cp_info.total_fluid_like_voxels_in_each_realm[STP_FLOW_REALM]);
    if (n_voxels_written[STP_COND_REALM] != cp_info.total_fluid_like_voxels_in_each_realm[STP_COND_REALM])
      msg_warn("Inconsistency in number of voxels written to Solid checkpoint file %ld with total number of fluid like voxels in cond realm %ld",
              n_voxels_written[STP_COND_REALM], cp_info.total_fluid_like_voxels_in_each_realm[STP_COND_REALM]);


    // One difference between nextgen and mainline is that for DNS or no_heat_transfer case, we do not set closest surfels for nearblks in nextgen, but we do in mainine.
    // As a result, in nextgen nw voxels are not ckpted in those cases and the sanity check should not be done. 
    // The logic here should be consistent with the logic in sUBLK::finalize_distance_info() in simeng/ublk.cc.
    if ((cp_info.is_turb || cp_info.is_heat_transfer
#ifdef BUILD_5G_LATTICE
         || cp_info.is_5g
#endif
        )) {
      sriPOINT n_nw_voxels_written_both_realms = n_nw_voxels_written[STP_FLOW_REALM] + n_nw_voxels_written[STP_COND_REALM];
      if (n_nw_voxels_written[STP_FLOW_REALM] != cp_info.total_nw_fluid_like_voxels_in_each_realm[STP_FLOW_REALM]) {
        msg_internal_error("Inconsistency in number of near-surface voxels written to Fluid checkpoint file %lu with total number of near-surface fluid like voxels in flow realm %ld",
                          n_nw_voxels_written[STP_FLOW_REALM], cp_info.total_nw_fluid_like_voxels_in_each_realm[STP_FLOW_REALM]);
      }
      if (n_nw_voxels_written[STP_COND_REALM] != cp_info.total_nw_fluid_like_voxels_in_each_realm[STP_COND_REALM]) {
        msg_internal_error("Inconsistency in number of near-surface voxels written to Solid checkpoint file %lu with total number of near-surface fluid like voxels in cond realm %ld",
                          n_nw_voxels_written[STP_COND_REALM], cp_info.total_nw_fluid_like_voxels_in_each_realm[STP_COND_REALM]);
      }
    }
  
  }
  if (success) {
    // If the regular avg mme ckpt file is written successfully, delete the previous .pre_IT.avg.ckpt.fnc file
    if (is_avg_mme && cp_info.avg_mme_ckpt_after_eit_p){
      platform_remove_file(cp_info.avg_mme_ckpt_preit_filename.c_str());
      platform_remove_file(cp_info.avg_mme_ckpt_preit_filename_cond.c_str());
    }
    // If a pre_IT avg mme ckpt file is written successfuly, delete existed regular avg mme file to avoid confusion
    if (is_avg_mme && !cp_info.avg_mme_ckpt_after_eit_p){
      platform_remove_file(cp_info.avg_mme_ckpt_filename.c_str());
      platform_remove_file(cp_info.avg_mme_ckpt_filename_cond.c_str());
    }
    /* If successful, rename tmpfile to real file */
    if (full_ckpt_p){
      if (rename(tmp_filename, ckpt_filename)){
        msg_warn("Error while attempting to rename a new checkpoint file to \"%s\".\n"
                 "The new checkpoint will be kept in the file \"%s\".\n"
                 "System error message was: %s\n",
                 ckpt_filename, tmp_filename, strerror(errno));
        success = FALSE;
      }
    } else{ //MME CKPT could write two files
      if(cp_info.is_flow){
        if (rename(tmp_filename, ckpt_filename)){
          msg_warn("Error while attempting to rename a new checkpoint file to \"%s\".\n"
                   "The new checkpoint will be kept in the file \"%s\".\n"
                   "System error message was: %s\n",
                   ckpt_filename, tmp_filename, strerror(errno));
          success = FALSE;
        }
      }
      if(cp_info.is_conduction){
        if (rename(cond_tmp_filename, cond_ckpt_filename)){
          msg_warn("Error while attempting to rename a new checkpoint file to \"%s\".\n"
                   "The new checkpoint will be kept in the file \"%s\".\n"
                   "System error message was: %s\n",
                   cond_ckpt_filename, cond_tmp_filename, strerror(errno));
          success = FALSE;
        }
      }
    }
  }

#if SURF_COUP
  ckpt_surface_coupling_models(ckpt_timestep, full_ckpt_p) ;
#endif

#if COPY_TMP_MEAS_FILES_AT_CKPT
  // Copy tmp meas files to ckpt files
  ckpt_tmp_meas_windows();
#endif

  /* Restore status message to a standard timestep report */
  cp_jobctl_output_current_status();
  stop_ckpt_timer();

  return success;
}

/* Outputs data for the currently ready checkpoint.  Assumes */
/* cp_info.cp_status is set the correct time */
static cSTRING timestep_string(asINT32 step) {
  static CHARACTER timestep_buf[128];

  if (step == TIMESTEP_LAST) {
    return("last timestep");
  } else {
    sprintf(timestep_buf, "timestep %d", step);
    return(timestep_buf);
  }
}

#if     0
VOID process_exit_condition(VOID)
{
  CKPT_QUEUE_ENTRY queue_head = cp_info.async_ckpt_queue->head;

  if ((queue_head != NULL) && (queue_head->id == EVENT_ID_EXIT)) {
    /* Now that we've hit the exit event, we're going to reprocess the
     * queue a little bit to deal with end-of-time events, surplus
     * events (events that are not going to occur now), etc.
     */
    TIMESTEP exit_timestep = queue_head->timestep;
    CKPT_QUEUE old_queue = cp_info.async_ckpt_queue;
    CKPT_QUEUE new_queue = xnew sCKPT_QUEUE;
    cp_info.async_ckpt_queue = new_queue;
    cp_info.exit_on_schedule = queue_head->initial_condition;
                                /* Flag whether exiting based on an
                                initial condition or due to an
                                exit specification after the fact. */

    old_queue->remove_entry();  /* Throw away the exit event */

    /* Rip through the old entries.  Anything that is associated with the current
     * timestep by virtue of being on THE current timestep, or being associated with
     * the end-of-time timestep, is added back to the queue with the timestep set to
     * be the current time step.
     */
    while (old_queue->entry_count > 0) {
      if ((old_queue->head->timestep == exit_timestep) || (old_queue->head->timestep == TIMESTEP_LAST)) {
        CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
        *new_queue_entry = *old_queue->head;
        new_queue_entry->timestep = exit_timestep;
        new_queue_entry->recur_rate = 0;
        new_queue_entry->next_entry = NULL;

        new_queue->add_entry(new_queue_entry);
      };
      old_queue->remove_entry();
    }
  }
}

#else

static void cp_signal_sps_unlock_timestep_last_events()
{
  ccDOTIMES(sp, total_sps) {
    MPI_Send(NULL, 0, MPI_INT, sp, eMPI_UNLOCK_TIMESTEP_LAST_EVENTS_TAG, eMPI_sp_cp_comm);
  }
}

VOID process_exit_condition(VOID)
{
  static BOOLEAN exitProcessed = FALSE;

  if (!exitProcessed) {         /* This can happen but once */
    TIMESTEP exit_timestep = cp_info.end_time;
    exitProcessed = TRUE;
    cp_signal_sps_unlock_timestep_last_events();

    /* Now that we've been told that it's exit time - or we've hit
     * the exit event (and we've got some events left to worry about),
     * we're now going to reprocess the queue.  What we do here is
     * to leave any unprocessed pre-exit events as is.  The exit
     * event itself is wiped out.  Events marked as associated with
     * exit time are added to the reprocessed queue marked with
     * exit time as their time step.
     */
    CKPT_QUEUE old_queue = cp_info.async_ckpt_queue;
    CKPT_QUEUE new_queue = xnew sCKPT_QUEUE;
    cp_info.async_ckpt_queue = new_queue;

    /* While we aren't loking at the actual exit event... */
    while ((old_queue->head != NULL) && (old_queue->head->id != EVENT_ID_EXIT) && (old_queue->head->id != EVENT_ID_HALT)) {
      /* If this entry isn't obsolescent... */
      if (old_queue->head->timestep <= exit_timestep) {
        CKPT_QUEUE_ENTRY old_entry = old_queue->head;
        CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
        *new_queue_entry = *old_queue->head;
        new_queue_entry->recur_rate = 0;
        new_queue->add_entry(new_queue_entry);

        /* If we're looking at a recurring event, and there could be further
        occurences before the now frozen exit time arrives, then add actual
        events */
        while (old_entry->recur_rate &&
               ((new_queue_entry->timestep + old_entry->recur_rate) < exit_timestep)) {
          CKPT_QUEUE_ENTRY newer_queue_entry = xnew sCKPT_QUEUE_ENTRY;
          *newer_queue_entry = *new_queue_entry;
          newer_queue_entry->timestep = new_queue_entry->timestep + old_entry->recur_rate;
          new_queue->add_entry(newer_queue_entry);
          new_queue_entry = newer_queue_entry;
        }
      }
      old_queue->remove_entry();/* Pull the pre-exit event off the old queue */
    }

    /*
     * At this point, we MUST be looking at the exit event, so we can now flag
     * whether the exit was set after the fact
     */
    cp_info.exit_on_schedule = old_queue->head->initial_condition;
                                /* Flag whether exiting based on an
                                initial condition or due to an
                                exit specification after the fact. */
    EVENT_ID exit_event_id = old_queue->head->id; // ID is either EXIT or HALT
    cp_info.exit_event_id = exit_event_id;

    /*
     * We've arrived here because we've either hit the exit/halt
     * event, or exit has been flagged to us by some state
     * transition of the SPs.  So, if necessary, update our
     * internal notion of the timestep
     */
    if (cp_info.time < old_queue->head->timestep) {
      cp_info.time = old_queue->head->timestep;
      if (cp_info.cp_status.status != SIM_STATUS_DONE)
        cp_info.cp_status.status = cp_info.time;
    }

    old_queue->remove_entry();  /* Discard the exit event */

    /*
     * Rip through the remaining entries.  Anything marked for the `exit' event
     * needs to be reformulated for the now known time.  There shouldn't be anything
     * else, since nothing should sort past exit
     */
    // At this point, the cp_info.time variable would have been updated and no
    // longer out-of-sync with the SPs (see comment in PR23726), so it should
    // be safe to compare this with the queue time-step.
    while (old_queue->head != NULL) {
      if (((old_queue->head->timestep == TIMESTEP_LAST) && (exit_event_id != EVENT_ID_HALT))
	  || (old_queue->head->timestep == cp_info.time)) {
        CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
        *new_queue_entry = *old_queue->head;
        new_queue_entry->timestep = exit_timestep;
        new_queue_entry->recur_rate = 0;

        new_queue->add_entry(new_queue_entry);
      };
      old_queue->remove_entry();
    }
  }
}
#endif


typedef struct sCP_LGI_OUTPUT_MEMBUF {
  cBOOLEAN              eof_pending_p;
  sINT16                current_buffer;
  VOID                  *cp_buffers[2];
  sINT32                buffer_size;
  STP_PROC                  sp;
  MPI_Request           mpi_request;
  sINT16                current_buffer_mpi_thread;
  sCP_LGI_OUTPUT_MEMBUF() { mpi_request = MPI_REQUEST_NULL; }
} *CP_LGI_OUTPUT_MEMBUF;

class sCKPT_CONTROL {
  CP_LGI_OUTPUT_MEMBUF membuf;
  volatile int n_open_sp_output_lgi_streams;
  LGI_STREAM *sp_streams;
//  Following variables are used by process_checkpoint and checkpoint_output functions
  volatile static BOOLEAN  ckpt_file_written;
  volatile BOOLEAN checkpoint_success;
  volatile CP_LGI_OUTPUT_MEMBUF sp_membuf;
  volatile int sp_actual_size;
  volatile int probing;
  volatile BOOLEAN m_writing_full_ckpt = FALSE;
public:
  VOID init() {
    membuf = NULL;
    sp_streams = NULL;
    checkpoint_success = FALSE;
    sp_membuf = NULL;
    sp_actual_size = -1;
    n_open_sp_output_lgi_streams = 0;
    probing = 0;
    m_writing_full_ckpt = FALSE;
  }

  VOID set_writing_full_ckpt(bool value) { m_writing_full_ckpt = value;}
  BOOLEAN writing_full_ckpt()const{ return m_writing_full_ckpt; }
  CP_LGI_OUTPUT_MEMBUF get_membuf() {
    return membuf;
  }

  CP_LGI_OUTPUT_MEMBUF get_sp_membuf() {
    return sp_membuf;
  }

  int get_n_open_sp_output_lgi_streams() {
    return n_open_sp_output_lgi_streams;
  }

  VOID decrement_n_open_sp_output_lgi_streams() {
    n_open_sp_output_lgi_streams--;
  }

  VOID set_n_open_sp_output_lgi_streams(int n) {
    n_open_sp_output_lgi_streams = n;
  }

  LGI_STREAM *get_streams() {
    return sp_streams;
  }

  VOID set_stream(LGI_STREAM *streams) {
    sp_streams = streams;
  }

  VOID set_sp_actual_size(int size) {
    sp_actual_size = size;
  }

  int get_sp_actual_size() {
    return sp_actual_size;
  }

  VOID set_sp_membuf(VOID *membuf) {
    sp_membuf = (CP_LGI_OUTPUT_MEMBUF) membuf;
  }

  BOOLEAN get_ckpt_file_written() {
    return ckpt_file_written;
  }
  
  VOID done_probing() {
    probing = 1;
  }

  int return_probing() {
    return probing;
  }

  VOID recv_buffer(STP_PROC sp)
  {
    asINT32 buffer_size;
    cSTRING user_specified_buffer_size = getenv("EXA_CKPT_BUFSIZE");
    if(user_specified_buffer_size != NULL) {
      buffer_size = atoi(user_specified_buffer_size);
    } else {
      buffer_size = CKPT_BUFFER_SIZE;
    }

    /* Expand to multiple of 8 bytes */

    buffer_size = (((buffer_size - 1)/8) + 1) * 8;
    membuf = (CP_LGI_OUTPUT_MEMBUF)
               exa_malloc(sizeof(sCP_LGI_OUTPUT_MEMBUF) + 2 * buffer_size,
                   "cp_lgi_output_connect", "CP LGI membuf structure");
    /* Initialize membuf */
    membuf->sp = sp;
    membuf->buffer_size = buffer_size;
    membuf->cp_buffers[0] = (char *)membuf + sizeof(sCP_LGI_OUTPUT_MEMBUF);
    membuf->cp_buffers[1] =
        (char *)membuf->cp_buffers[0] + buffer_size;
    membuf->current_buffer = 0;
    membuf->current_buffer_mpi_thread = 0;
    membuf->eof_pending_p = FALSE;
    MPI_Irecv(membuf->cp_buffers[membuf->current_buffer_mpi_thread], membuf->buffer_size, MPI_BYTE, membuf->sp,
        eMPI_LGI_SP_SEND_TAG, MPI_COMM_WORLD, &membuf->mpi_request);
  }

  VOID close_streams() {
    ccDOTIMES(j, total_sps)
           lgi_close_stream(sp_streams[j]);
    while(!return_probing())
      cp_sleep();
  }
// This function is called by the IO thread for writing the checkpoint file.
// It waits for the streams read by process_checkpoint and writes them to checkpoint file
  VOID checkpoint_output()
  {
    /*  Asynchronous checkpoint */
    //Elaborate on this?
    if (get_n_open_sp_output_lgi_streams() == total_sps || (sim_args.parallel_io && writing_full_ckpt())) 
    {
      CKPT_QUEUE_ENTRY queue_head = cp_info.async_ckpt_queue->head;
      TIMESTEP ckpt_timestep = queue_head->timestep;
      BOOLEAN precious_ckpt = queue_head->precious;
      EVENT_ID id = queue_head->id;
      switch (id) {
        case EVENT_ID_FULL_CKPT:
          checkpoint_success = do_checkpoint(TRUE, ckpt_timestep, precious_ckpt, FALSE);
          break;

        case EVENT_ID_MME_CKPT:
          checkpoint_success = do_checkpoint(FALSE, ckpt_timestep, precious_ckpt, FALSE);
          break;

        case EVENT_ID_AVG_MME_CKPT:
          checkpoint_success = do_checkpoint(FALSE, ckpt_timestep, precious_ckpt, TRUE);
          break;

        default:
          msg_internal_error("Encountered unexpected event id in CP, id = %d", id);
          break;
      }
      if (id != EVENT_ID_INVALID) {
        msg_print("%s %s %s checkpoint %s at %s",
                  (precious_ckpt ? "Required"
                   : ((queue_head->initial_condition > 0)
                      ? "Periodic" : "User signal requested")),
                  (id == EVENT_ID_AVG_MME_CKPT ? "average" : ""),
                  ((id == EVENT_ID_FULL_CKPT) ? "full" : "fluid"),
                  (checkpoint_success
                   ? "completed successfully" : "failed"),
                  timestep_string(ckpt_timestep));
      }
      //    Ckpt file has been written, ok for the main thread to proceed
#ifndef _EXA_HPMPI
      if(writing_full_ckpt() && sim_args.parallel_io)
      {
        set_n_open_sp_output_lgi_streams(0);
        set_writing_full_ckpt(false);
      }
#endif
      ckpt_file_written = TRUE;
    }
  }
// This function is called by the main thread.
// It checks if it time to write a ckpt, connect to SP streams and receive them to write to ckpt file
  VOID process_checkpoint(VOID)
  {

    CKPT_QUEUE_ENTRY queue_head = cp_info.async_ckpt_queue->head;

    // For avg mme ckpt, it is possible that the stop avg mme is scheduled before this ckpt timestep, and 
    // SPs are not sending any ckpt data for this timestep, and we should remove this ckpt event immediately.
    if (queue_head != NULL && queue_head->id == EVENT_ID_AVG_MME_CKPT) {
      TIMESTEP ckpt_timestep = queue_head->timestep;
      if (ckpt_timestep > cp_info.time_to_stop_avg_mme) {
        cp_info.async_ckpt_queue->remove_recurring_ckpt_event(ckpt_timestep, TRUE, TRUE);
        cp_info.average_mme_stopped = TRUE;
  #if DEBUG_AVG_MME
        msg_print("At time %d remove avg fluid ckpt event for time %d", cp_info.time, ckpt_timestep);
  #endif
        return;      
      }
    }

    if(queue_head != NULL) {
      if(async_checkpoint_ready_p()) {
        EVENT_ID id = queue_head->id;
        init();
        /* If an event arrives which tells us that the SPs are
         * actually further on than we previously thought, then
         * adjust our internal notion of time
         */
        if (cp_info.time < queue_head->timestep) {
          cp_info.time = queue_head->timestep;
        }
        if((id == EVENT_ID_EXIT) || (id == EVENT_ID_HALT)) {
          process_exit_condition();
          /* Perform exit-oriented queue reprocessing */
          queue_head = cp_info.async_ckpt_queue->head;

          // The ckpt event could be removed after it is stopped, thus it is possible that there is no
          // ckpt event after the exit event is processed.

          // Now that the queue is representative of the work remaining to do, we can
          // just recurse to get the next checkpoint event, and allow the normal sequence
          // of calls back to this function to catch whatever else is remaining to do.
          if (queue_head != NULL)
            id = queue_head->id;
        }

        if(id == EVENT_ID_AVG_MME_CKPT) {
	  TIMESTEP ckpt_timestep = queue_head->timestep;
          if (ckpt_timestep >= cp_info.time_to_stop_avg_mme) {
#if DEBUG_AVG_MME
            msg_print("At time %d remove avg fluid ckpt event for time %d after writing ckpt", cp_info.time, ckpt_timestep);
#endif
            cp_info.async_ckpt_queue->remove_recurring_ckpt_event(ckpt_timestep, TRUE);
            cp_info.average_mme_stopped = TRUE;
          }
        }

        if(id != EVENT_ID_MME_CKPT && id != EVENT_ID_FULL_CKPT && id != EVENT_ID_AVG_MME_CKPT)
          msg_internal_error("Encountered unexpected end-of-time event id in CP, id = %d", id);

        if (sim_args.gpu && id == EVENT_ID_FULL_CKPT) {
          static bool full_ckpt_gpu_warned = false;
          if (!full_ckpt_gpu_warned) {
            msg_warn("Full checkpoints are not supported on GPU");
            full_ckpt_gpu_warned = true;
          }
          ckpt_file_written = TRUE;
        } else {
#ifndef _EXA_HPMPI
          if(id == EVENT_ID_FULL_CKPT && sim_args.parallel_io)
          {
            // previous code is not needed anymore lgi streams are not used
            // still need to flag a full ckpt is ongoing
            set_writing_full_ckpt(true);
          }
          else
#endif
            // This function receives the data from each SP
            // and shuttles it to the IO thread, which does
            // the actual writing
            cp_wait_for_sp_lgi_output_connect();
        }
        while (!ckpt_file_written)   // Only at this point we are sure that the IO thread finishes writing ckpt file
          sleep_lgi_wait();
        ckpt_file_written = FALSE;

        if (queue_head) {  // Queue head could be deleted already if it is an average mme ckpt event
          if (queue_head->recur_rate > 0) {
            /* If a recurring checkpoint requested... */
            /* Establish entry in CP queue indicating the checkpoint we're setting up */
            CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
            *new_queue_entry = *queue_head;
            new_queue_entry->id = id;
            new_queue_entry->timestep += queue_head->recur_rate;
            cp_info.async_ckpt_queue->remove_entry();
            cp_info.async_ckpt_queue->add_entry(new_queue_entry);
#if DEBUG_AVG_MME
            if (id == EVENT_ID_AVG_MME_CKPT)
              msg_print("Insert new ckpt event for timestep %d", new_queue_entry->timestep);
#endif
          } else {
            cp_info.async_ckpt_queue->remove_entry();
          }
        }
      }
    }
  }
};

sCKPT_CONTROL ckpt_control;


VOID mpi_sp_lgi_streams_fcn()
{
  while(1) {
    CP_LGI_OUTPUT_MEMBUF membuf;
    MPI_Status status;
    do {
      int flag;
      // Encourage the MPI progression engine to advance
      MPI_Iprobe(MPI_ANY_SOURCE, eMPI_DUMMY_IPROBE_TAG, MPI_COMM_WORLD, &flag, &status);
      if (ckpt_control.get_n_open_sp_output_lgi_streams() == 0) {
        ckpt_control.done_probing();
        return;
      }
      membuf = ckpt_control.get_sp_membuf();
    } while (membuf == NULL);
    MPI_Wait(&membuf->mpi_request, &status);
    int actual_size;
    MPI_Get_count(&status, MPI_BYTE, &actual_size);
    ckpt_control.set_sp_actual_size(actual_size);
    ckpt_control.set_sp_membuf(NULL);

    if (actual_size != 0) {
      // Post next recv
      membuf->current_buffer_mpi_thread = membuf->current_buffer_mpi_thread ^ 1;
      MPI_Irecv(membuf->cp_buffers[membuf->current_buffer_mpi_thread], membuf->buffer_size, MPI_BYTE, membuf->sp,
          eMPI_LGI_SP_SEND_TAG, MPI_COMM_WORLD, &membuf->mpi_request);
    }
  }
}

volatile BOOLEAN sCKPT_CONTROL::ckpt_file_written = FALSE;

static VOID *lgi_read_next_fcn(VOID *membuf_info, asINT32 *length_ptr)
// This is called when we need a new buffer of data.
{
  CP_LGI_OUTPUT_MEMBUF membuf = (CP_LGI_OUTPUT_MEMBUF) membuf_info;
  if (membuf->eof_pending_p) {
    // Return end of file
    *length_ptr = 0;
    return NULL;
  }
  ckpt_control.set_sp_membuf(membuf);
  while (ckpt_control.get_sp_membuf() != NULL) {
    sleep_lgi_wait();
  }
  int actual_size = ckpt_control.get_sp_actual_size();
  if (actual_size == 0) {
    membuf->eof_pending_p = TRUE;
    // Return end of file
    *length_ptr = 0;
    return NULL;
  }

  VOID *buffer = membuf->cp_buffers[membuf->current_buffer];
  membuf->current_buffer = membuf->current_buffer ^ 1;

  // Set the length and return value
  *length_ptr = actual_size;

  return buffer;
}


static VOID lgi_read_close_fcn(VOID *membuf_info, asINT32 length_left)
{
  CP_LGI_OUTPUT_MEMBUF membuf = (CP_LGI_OUTPUT_MEMBUF) membuf_info;

  /* Read any remaining data */
  while (!membuf->eof_pending_p) {
    asINT32 dummy_length;
    lgi_read_next_fcn(membuf_info, &dummy_length);
  }

  ckpt_control.decrement_n_open_sp_output_lgi_streams();
  exa_free(membuf);
}


static LGI_STREAM cp_lgi_output_connect(STP_PROC sp)
{
  ckpt_control.recv_buffer(sp);
  return lgi_open_input_stream(LGI_MEMBUF_TYPE, lgi_read_next_fcn,
      lgi_read_close_fcn, ckpt_control.get_membuf());
}

/*-----------------------------------------------*
 * LGI connection
 *-----------------------------------------------*/

VOID cp_wait_for_sp_lgi_output_connect()
{
  LGI_STREAM *streams =
      (LGI_STREAM *)exa_malloc(total_sps * sizeof(LGI_STREAM),
          "cp_wait_for_sp_lgi_output_connect",
          "array of stream pointers");
  ccDOTIMES(sp, total_sps) {
    streams[sp] = cp_lgi_output_connect(sp);
  }
  ckpt_control.set_stream(streams);
  ckpt_control.set_n_open_sp_output_lgi_streams(total_sps);
  mpi_sp_lgi_streams_fcn();
}

LGI_STREAM *return_sp_stream()
{
  return ckpt_control.get_streams();
}

VOID close_lgi_streams()
{
  ckpt_control.close_streams();
}

VOID process_checkpoints()
{
  ckpt_control.process_checkpoint();
}

VOID write_checkpoints()
{
  ckpt_control.checkpoint_output();
}
// Remove recurrsive avg mme ckpt events scheduled after removal_timestep
EVENT_ID sCKPT_QUEUE::remove_recurring_average_mme_ckpt_event_after_time(TIMESTEP removal_timestep)
{
  CKPT_QUEUE_ENTRY current_entry = head;
  CKPT_QUEUE_ENTRY previous_entry = NULL;

  while(current_entry != NULL) {
    EVENT_ID current_entry_id = current_entry->id;
    if (current_entry_id == EVENT_ID_AVG_MME_CKPT && current_entry->recur_rate > 0) {
      if (current_entry->timestep <= removal_timestep) {
        // Find the only avg mme ckpt event and the condition is not met, just stop.
        break;
      } else {
        if (previous_entry) {
          previous_entry->next_entry = current_entry->next_entry;
        } else {
          head = current_entry->next_entry;
        }
        delete current_entry;
        entry_count--;
      }
      return current_entry_id;
    } else {
      previous_entry = current_entry;
      current_entry = current_entry->next_entry;
    }
  }
  return EVENT_ID_INVALID;
}


EVENT_ID sCKPT_QUEUE::remove_recurring_ckpt_event(TIMESTEP removal_timestep, BOOLEAN is_avg_mme_ckpt, BOOLEAN force_to_remove)
{
  CKPT_QUEUE_ENTRY current_entry = head;
  CKPT_QUEUE_ENTRY previous_entry = NULL;

  while(current_entry != NULL) {
    EVENT_ID current_entry_id = current_entry->id;
    BOOLEAN is_ckpt_event_id = (is_avg_mme_ckpt) ?
                               current_entry_id == EVENT_ID_AVG_MME_CKPT :
                               current_entry_id == EVENT_ID_MME_CKPT || current_entry_id == EVENT_ID_FULL_CKPT;
    if (is_ckpt_event_id && current_entry->recur_rate > 0) {
      if (current_entry->timestep <= removal_timestep && !force_to_remove) {
        // SPs have already sent checkpoint data for this checkpoint, so don't remove event
        current_entry->recur_rate = 0;
      } else {
        if (previous_entry) {
          previous_entry->next_entry = current_entry->next_entry;
        } else {
          head = current_entry->next_entry;
        }
        delete current_entry;
        entry_count--;
      }
      return current_entry_id;
    } else {
      previous_entry = current_entry;
      current_entry = current_entry->next_entry;
    }
  }
  return EVENT_ID_INVALID;
}

VOID sCKPT_QUEUE::add_entry(CKPT_QUEUE_ENTRY entry)
{
  CKPT_QUEUE_ENTRY current_entry = head;
  CKPT_QUEUE_ENTRY previous_entry = NULL;

  entry->next_entry = NULL;     /* Make sure we aren't getting any more than this entry */

  if (entry->timestep <= cp_info.restart_time) {
    if (entry->recur_rate > 0) {
      while (sim_args.allow_immediate_checkpoints
             ? entry->timestep < cp_info.restart_time : entry->timestep <= cp_info.restart_time) {
        entry->timestep += entry->recur_rate;
                                /* Increment timestep for event until at or
                                above the initial step for the case */
      }
    } else {
      if (!(sim_args.allow_immediate_checkpoints
          ? entry->timestep < cp_info.restart_time : entry->timestep <= cp_info.restart_time)) {
        return;                 /* Don't enter an event before the beginning of time */
      }
    }
  }

  /* If adding an Exit event, first make sure that there are no
  other exit events in the queue */
  switch(entry->id) {
  case EVENT_ID_FULL_CKPT:
  case EVENT_ID_MME_CKPT:
  case EVENT_ID_AVG_MME_CKPT: {
    break;
  };

  case EVENT_ID_EXIT: 
  case EVENT_ID_HALT: {
    while(current_entry != NULL) {
      if ((current_entry->id == EVENT_ID_EXIT) || (current_entry->id == EVENT_ID_HALT)) {
        if (previous_entry) {
          previous_entry->next_entry = current_entry->next_entry;
          delete current_entry;
          current_entry = previous_entry->next_entry;
        } else {
          head = current_entry->next_entry;
          delete current_entry;
          current_entry = head;
        }
        entry_count--;
        break;
      } else {
        previous_entry = current_entry;
        current_entry = current_entry->next_entry;
      }
    }

    current_entry = head;
    previous_entry = NULL;
    break;
  }

  default: {
    msg_internal_error("Attempt to add unrecognized id to event queue! - id = %d", entry->id);
    break;
  }
  }

  /* Find the location to add the entry */
  while(current_entry != NULL) {
    /* If current entry is beyond where we want the new entry, insert here... */
    if ((current_entry->timestep > entry->timestep) ||
        ((current_entry->timestep == entry->timestep) && (current_entry->id > entry->id))) {
      entry->next_entry = current_entry;
      if(previous_entry == NULL) {
        head = entry;
      } else {
        previous_entry->next_entry = entry;
      }
      entry_count++;
      return;
    } else if ((current_entry->timestep == entry->timestep) &&
        (current_entry->id == entry->id)) {
      /* Intelligently suppress redundant requests (we
                                always keep the recurring event, if any) */
      switch(((current_entry->recur_rate > 0) << 1) | (entry->recur_rate > 0)) {
      case 0:   {               /* Neither is recurring, just dump the new one,
                                but preserve the precious state if either has one */
        current_entry->precious = (current_entry->precious || entry->precious);
        delete entry;
        break;
      }
      case 2:                   /* Existing entry recurring, just dump the new one */
      case 3: {         /* Both entries recurring, just dump the new one */
        current_entry->initial_condition = current_entry->initial_condition || entry->initial_condition;
        delete entry;
        break;
      }

      case 1:   {               /* New entry recurring, just make the existing one recur */
        current_entry->recur_rate = entry->recur_rate;
        current_entry->initial_condition = current_entry->initial_condition || entry->initial_condition;
        delete entry;
      }
      }
      return;                   /* No new elements, don't increment count */
    }
    previous_entry = current_entry;
    current_entry = current_entry->next_entry;
  }

  /* Insert at end of list */

  if(previous_entry == NULL) {
    head = entry;
  } else {
    previous_entry->next_entry = entry;
  }
  entry_count++;

  return;
}

BOOLEAN sCKPT_QUEUE::some_non_exit_event_later_than(TIMESTEP ts)
{
  CKPT_QUEUE_ENTRY entry = head;

  while(entry != NULL) {
    if (entry->timestep > ts
        && entry->timestep != TIMESTEP_LAST
        && entry->recur_rate <= 0 // ignore periodic checkpoints
        && entry->id != EVENT_ID_EXIT
	&& entry->id != EVENT_ID_HALT) {
      return TRUE;
    }
    entry = entry->next_entry;
  }
  return FALSE;
}

VOID sCKPT_QUEUE::remove_entry() {

  CKPT_QUEUE_ENTRY previous_head = head;
  head = previous_head->next_entry;
  delete previous_head;
  entry_count--;
}

VOID sCKPT_QUEUE::print_queue()
{

  CKPT_QUEUE_ENTRY current_entry = head;
  sINT32 entrys_encountered = 0;

  msg_print("Checkpoint queue has %d entries", entry_count);
  while(current_entry != NULL) {
    entrys_encountered++;
    msg_print("   Entry %d, id = %d, timestep = %d, next_entry = 0x%p",
              entrys_encountered, current_entry->id,
              current_entry->timestep, (void*)current_entry->next_entry);
    current_entry = current_entry->next_entry;
  }
}

