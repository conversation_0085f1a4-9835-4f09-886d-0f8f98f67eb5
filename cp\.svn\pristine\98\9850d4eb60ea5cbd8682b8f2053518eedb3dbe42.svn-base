#ifndef CP_POROUS_ROCK_H
#define CP_POROUS_ROCK_H

#include <vector>
#include <array>
#include "common.h"

using POROUS_ROCK_VAR = sFLOAT;
using POROUS_ROCK_INDEX = sINT32;
void write_porous_rock_tables();

class cPOROUS_ROCK
{
  static const size_t NUM_K0_COLS = 4;
  static const size_t NUM_Pc_Kr_COLS = 19;
  static const size_t NUM_INFO_COLS = 6;
  static const size_t NUM_INFO_ROWS = 1;

  POROUS_ROCK_INDEX m_index;

  uINT32 m_num_K0_rows;
  uINT32 m_num_K0_cols;
  std::vector<POROUS_ROCK_VAR> m_K0_values;

  uINT32 m_num_Pc_Kr_rows;
  uINT32 m_num_Pc_Kr_cols;

  std::vector<POROUS_ROCK_VAR> m_Pc_Kr_values;

  uINT32 m_num_info_rows;
  uINT32 m_num_info_cols;
  std::vector<POROUS_ROCK_VAR> m_info_values;

  //std::array<dFLOAT,3> m_principal_dir;

  explicit cPOROUS_ROCK(POROUS_ROCK_INDEX index);

public:
  static cPOROUS_ROCK read_tables(POROUS_ROCK_INDEX index, const std::string& dir);
  void write_to_all_sps() const;
};
#endif
