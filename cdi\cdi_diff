#!/bin/csh

# usage: cdi_diff file1 file2

if !($?R) then
  echo "${0}: registry is undefined"
  exit 1
endif

if ($#argv < 2) then
  echo "usage: ${0} [diff_args] file1 file2"
  exit 1
endif

set pid=$$

set args=()
while ($#argv > 2)
  set args=($args ${1})
  shift
end

set file1=$1
set file2=$2

set file1_t=$file1:t
set file2_t=$file2:t

set file1_txt=/tmp/${file1_t}.txt.${pid}a
set file2_txt=/tmp/${file2_t}.txt.${pid}b

dump_canonical_cdi $file1 > $file1_txt
dump_canonical_cdi $file2 > $file2_txt

# echo "diff $args $file1_txt $file2_txt"
diff $args $file1_txt $file2_txt

'rm' -f $file1_txt $file2_txt
