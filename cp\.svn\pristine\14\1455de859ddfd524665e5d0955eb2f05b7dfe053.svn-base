/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Job Control support
 *
 * Jim Salem, Exa Corporation 
 * Created Mon Jun 20 1994
 *--------------------------------------------------------------------------*/

#ifndef __JOBCTL_STATUS_H
#define __JOBCTL_STATUS_H

#include "common.h"


#define JOBCTL_FILE_ENVVAR "EXAJOBCTL_STATUS_FILE"

/* Yields true if reporting memory status */
BOOLEAN cp_jobctl_memory_status_requested();

/* Status reporting functions */

/* Returns a statues message based on cp_info.cp_status */
cSTRING cp_jobctl_status_string(VOID);

int cp_jobctl_timestep_status_string(SIM_STATUS status, SIM_STATUS flow, SIM_STATUS cond, char status_string[]);

/* Writes a status message to the jobctl file */
VOID cp_jobctl_output_status(cSTRING status_string);
VOID cp_jobctl_output_memory_status(cSTRING status_string);

/* Outputs the current status */
VOID cp_jobctl_output_current_status(VOID);

#endif /* __JOBCTL_STATUS_H */

