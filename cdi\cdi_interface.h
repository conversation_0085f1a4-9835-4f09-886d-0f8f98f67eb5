/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
/**
 * This file represents a baby step towards cleaning up CDI.  There are several
 * differences between a chunk in this header file and a chunk in
 * cdi_readwrite.h:
 * 
 * 1) chunks don't have vestigal cccc members
 * 2) read/write functions return a bool status
 * 3) read/write functions tend to have verbose function names
 *    (e.g. cdi_write_condenser, not cdi_write_cdsr)
 * 4) write functions don't push/pop themselves; use WITH_CDI_CHUNK
 * 5) callers own chunk objects; (e.g. cdi_empty_face, not cdi_destroy_face)
 */


#ifndef _CDI_INTERFACE_H
#define _CDI_INTERFACE_H

#include <list>
#include <string>
#include <unordered_set>
#include <vector>

class cGEOM_COMMON_ENTITY;
class cGEOM_COMMON_ENTITY_LIST;
enum eGEOM_COMMON_ITEM_TYPE : char;


/*                     *
 * material properties *
 *                     */
#define	CDI_CHUNK_TYPE_MATL		CIO_BUILDCCCC('m', 'a', 't', 'l')
#define	CDI_CHUNK_TYPE_LIGH		CIO_BUILDCCCC('l', 'i', 'g', 'h')

typedef struct sCDI_SHIN
{
#define	CDI_CHUNK_TYPE_SHIN		CIO_BUILDCCCC('s', 'h', 'i', 'n')
  double shininess;
} *CDI_SHIN;

typedef struct sCDI_RGBA
{
#define	CDI_CHUNK_TYPE_RGBA		CIO_BUILDCCCC('r', 'g', 'b', 'a')
  double red;
  double green;
  double blue;
  double alpha;
} *CDI_RGBA;

typedef struct sCDI_TXTR
{
#define	CDI_CHUNK_TYPE_TXTR		CIO_BUILDCCCC('t', 'x', 't', 'r')
  char *name;		          /* the name of the texture map */
  cdiINT32 xtxtr;                  /* texture map dimension 1 */
  cdiINT32 ytxtr;                  /* texture map dimension 2 */
  CDI_RGBA map;                   /* texture bitmap [size = (xtxtr * ytxtr * sizeof(sCDI_RGBA))] */
} *CDI_TXTR;


struct sCDI_FACT
{
  cdiINT32 front_face;		/* front face index */
  cdiINT32 back_face;		/* back face index */
  cdiINT32 n_vertices;           /* number of facet vertices */
  cdiINT32 *vertices;		/* the array of vertex indices */

sCDI_FACT() : front_face(0), back_face(0), n_vertices(0), vertices(NULL) {}
};
typedef sCDI_FACT *CDI_FACT;

typedef struct sCDI_FACT_HEADER
{
  cdiINT32 n_facets;		/* number of facets */
  cdiINT32 n_vertex_refs;        /* number of vertex references */
} *CDI_FACT_HEADER;

enum CDI_DISPLAY_MODE { 
  CDI_DISPLAY_NONE,
  CDI_DISPLAY_HIDDEN,
  CDI_DISPLAY_WIREFRAME,
  CDI_DISPLAY_OUTLINE,
  CDI_DISPLAY_FACETS,
  CDI_DISPLAY_SOLID,
  CDI_DISPLAY_SAME_AS_REFERENCE,
};

#define  CDI_SEGMENT_SAME_AS_PARENT -1

/* This struct used for reading and writing faces */
typedef struct sCDI_PSEG {
  cdiINT32 segment_index;        /* segment index */
  cdiINT32 effective_segment_index;
} *CDI_PSEG;

#define CDI_TRANSPARENCY_SAME_AS_PARENT -1.0 /* Magic value for  Same as Parent transparency */
#define CDI_TRANSPARENCY_SAME_AS_REFERENCE -2.0 /* Magic value for Same as Reference transparency */

typedef struct sCDI_DPRP
{
  char* color;
  char* realisticLook;
  CDI_DISPLAY_MODE display_mode;
  double transparency;
} *CDI_DPRP;

class cCDI_SEGMENT_REF;
// chuck to store segment/model view information
struct sCDI_PSMV
{
#define CDI_CHUNK_TYPE_PSMV CIO_BUILDCCCC('p', 's', 'm', 'v')
  std::vector<cdiINT32> effective_segments;              /* this is needed to support old CDIs */
  std::vector<cCDI_SEGMENT_REF> partition_segment_refs;  /* partition, segment reference list */
  cdiINT32 n_model_views;
  CDI_DPRP mv_display_props;
  sCDI_PSMV() : n_model_views(0), mv_display_props(NULL) {}
};
typedef sCDI_PSMV *CDI_PSMV;

// face properties chunk
#define CDI_CHUNK_TYPE_FACP             CIO_BUILDCCCC('f', 'a', 'c', 'p')
// chunk for model view display properties in psmv
#define CDI_CHUNK_TYPE_MVDP             CIO_BUILDCCCC('m', 'v', 'd', 'p')

struct sCDI_FACE
{
  cdiINT32 index;                  /* the index that facets (front &/or back) use to refer to this face */
  cdiINT32 prop;                   /* the flow surface property index (may be CDI_PHYS_TYPE_NONE) */
  cdiINT32 flow_physics_comp;      /* the flow surface property index for the complementary face */
  cdiINT32 thermal_physics;        /* the thermal surface property index (may be CDI_PHYS_TYPE_NONE) */
  cdiINT32 thermal_physics_comp;   /* the thermal surface property index for the complementary face */
  char *name;                      /* the name of the face */
  cdiINT32 material_index;         /* the index of an associated material property (-1 == none)*/
  cdiINT32 n_front_facets;         /* number of uses of face by facet fronts */
  cdiINT32 n_back_facets;          /* number of uses of face by facet backs */
  cdiINT32 n_layers;               /* number of layers for shell conduction */
  sCDI_PSMV psmv;

  sCDI_FACE() : prop(-1), flow_physics_comp(-1), thermal_physics(-1), thermal_physics_comp(-1), material_index(-1), n_layers(-1) {}

};
typedef sCDI_FACE *CDI_FACE;

typedef struct sCDI_RGNN
{
#define	CDI_CHUNK_TYPE_RGNN		CIO_BUILDCCCC('r', 'g', 'n', 'n')
  cdiINT32 n_vertices;
  cdiINT32 n_faces;
  cdiINT32 n_facets;
  cdiINT32 n_vertex_refs;
} *CDI_RGNN;

struct sCDI_RGND
{
#define	CDI_CHUNK_TYPE_RGND		CIO_BUILDCCCC('r', 'g', 'n', 'd')
  std::string name;
  cdiINT32 is_offset;              /* zero if not an offset, nonzero if offset */ 
  cdiINT32 tire_index;             /* -1 if not parametric, else a tire index */
  double bbox[3][2];               /* the coordinates of the bbox corners */
  sCDI_RGNN rgnn;
  CDI_RGNS sub_regions;
  sCDI_PSMV psmv;
  sCDI_RGND() : is_offset(0), tire_index(-1), sub_regions(NULL) {}
};
typedef sCDI_RGND *CDI_RGND;

enum CDI_PARTITION_TYPE { 
  CDI_PARTITION_PART,
  CDI_PARTITION_FACE
};

typedef struct sCDI_PRTT
{
#define CDI_CHUNK_TYPE_PRTT             CIO_BUILDCCCC('p', 'r', 't', 't') /* partition type */
  CDI_PARTITION_TYPE partition_type;     /* Partition type */
} *CDI_PRTT;

#define CDI_CHUNK_TYPE_PSGS             CIO_BUILDCCCC('p', 's', 'g', 's') /* list of partition segment information for regions*/
#define CDI_CHUNK_TYPE_PSEG             CIO_BUILDCCCC('p', 's', 'e', 'g') /* partition segment information for regions */

typedef struct sCDI_SGID
{
#define CDI_CHUNK_TYPE_SGID             CIO_BUILDCCCC('s', 'g', 'i', 'd') /* segment index */
  cdiINT32 segment_index;        /* segment index */
} *CDI_SGID;

// chunk to store all model view (MDLV) chunks
#define CDI_CHUNK_TYPE_MVDF CIO_BUILDCCCC('m', 'v', 'd', 'f')

// chunk to store information about a single model view
typedef struct sCDI_MDLV
{
#define CDI_CHUNK_TYPE_MDLV CIO_BUILDCCCC('m', 'd', 'l', 'v')
  char* name;
  cdiINT32 partition_index;
  cdiINT32 color_ref_index;       // -1 = none
  cdiINT32 disp_mode_ref_index;   // -1 = none
  cdiINT32 n_partition_segments;
  CDI_DPRP segment_display_props;
  cdiINT32 n_partial_parts;
  CDI_DPRP partial_display_props;
} *CDI_MDLV;

#define	CDI_CHUNK_TYPE_RGDF		CIO_BUILDCCCC('r', 'g', 'd', 'f')

typedef struct sCDI_RGCT
{
#define	CDI_CHUNK_TYPE_RGCT		CIO_BUILDCCCC('r', 'g', 'c', 't')
  cdiINT32 n_regions;	 /* Number of regions in the case */
} *CDI_RGCT;

typedef struct sCDI_RGDP
{
#define	CDI_CHUNK_TYPE_RGDP		CIO_BUILDCCCC('r', 'g', 'd', 'p')
  char *color;                    /* the name for the color of the region */
  cdiINT32 material_index;        /* the index of an associated material property (-1 == none)*/
  CDI_DISPLAY_MODE display_mode;  /* display mode */
} *CDI_RGDP;

/* Enumerations used for surface coupling...*/
enum eCDI_MODEL_ITEM_TYPE {
  CDI_MODEL_ITEM,
  CDI_COMPONENT_ITEM,
  CDI_SURFACE_ITEM,
  CDI_SET_ITEM
};

enum eCDI_SURFACE_SIDE {
  CDI_SURFACE_FRONT,
  CDI_SURFACE_BACK,
  CDI_SURFACE_FRONT_AND_BACK
};

#define CDI_CPLW_PREFER_INITIAL_CONDITIONS_IN_MODEL  (1 << 0)


/* Surface Coupling chunk -- houses SCCT, CMDL, and SCBC subchunks */
#define	CDI_CHUNK_TYPE_SCPL     CIO_BUILDCCCC('s', 'c', 'p', 'l')

/* Suface Coupling Count chunk -- Provides a count of CMDL, SCBC & CPLW
   chunks present in this file. */
typedef struct sCDI_SCCT
{
#define	CDI_CHUNK_TYPE_SCCT     CIO_BUILDCCCC('s', 'c', 'c', 't')
  cdiINT32 n_models;  /* Number of surface coupling models referenced by this case */
  cdiINT32 n_bcs;     /* Number of boundary conditions applied to the SC models */
  cdiINT32 n_walls;   /* Number of boundary conditions applied to the PF geometry */
} *CDI_SCCT;


typedef sINT32 CDI_TIMESTEP;

typedef struct sCDI_COUPLING_PHASE_DESC {
  /* This structure is used to specify a particular measurement interval */
  CDI_TIMESTEP start            = 0;
  CDI_TIMESTEP period           = 0;
  CDI_TIMESTEP interval         = 0;
  CDI_TIMESTEP delay            = 0;
  double       therm_time_ratio = 1;
  double       stepsize         = 0;
  double       exact_start      = 0;
  double       exact_period     = 0;
  double       exact_interval   = 0;
  cdiBOOLEAN   adaptive_p       = 0;
} *CDI_COUPLING_PHASE_DESC;

/* Coupling Model chunk -- Description of a third-party model used for
   surface coupling (e.g. a RadTherm model). Additionally provides
   information about how the coupling will be performed. */
typedef struct sCDI_CMDL
{
#define	CDI_CHUNK_TYPE_CMDL     CIO_BUILDCCCC('c', 'm', 'd', 'l')
  std::string model_name;
  char *model_type;
  cdiINT32 coupling_type; /* Look up meaning in tpi_common.h */
  char *model_filename;
  char *absolute_model_filename;
  char *results_filename;
  char *absolute_results_filename;
  char *model_length_unit;
  cdiINT32 n_export_variables;
  cdiINT32 *export_variables; /* Look up meaning in tpi_common.h */
  char **export_variable_units;
  cdiINT32 n_import_variables;
  cdiINT32 *import_variables; /* Look up meaning in tpi_common.h */
  char **import_variable_units;
  double l_to_g_xform[4][4];
  cdiINT32 num_coupling_model_bcs;
  cdiINT32 num_pf_bcs;
  cdiINT32 *pf_faces_with_bcs;

  // Number of entries in the coupling phase table.
  // For old CDI (ver <= 4.2) with surface coupling, it is 1
  cdiINT32 n_coupling_phases;
  // Coupling timesteps specified by the user. Could contain multiple phases with different periods, averaging intervals, etc.
  // The start_time is implicitly the end time of the previous phase. Table is in user's timesteps and always sorted in ascending order of start_time.
  std::vector<sCDI_COUPLING_PHASE_DESC> m_coupling_phase_descs;
  // Users specify num_iterations OR end_time in PowerCASE. Defaults values are infinity.
  cdiBOOLEAN use_end_time_for_coupling; // 1: use end_time to stop coupling; 0: use num_iterations to stop coupling 
  cdiINT32 num_iterations;
  cdiINT32 end_time;

  // For old CDI (ver<=5.3), calculation_type is InferFromModel.
  cdiINT32 m_calculation_type;
  double m_powertherm_start_time;
  cdiBOOLEAN m_use_default_powertherm_start_time;   // 1: Let simulator calculate the powertherm start time; 0: use the time above

  // For adaptive coupling
  double adaptive_up_coeff;
  double gradient_low;
  double adaptive_down_coeff; // Not used by PowerCASE
  double gradient_high;       // Not used by PowerCASE
  double ratio_max;
  double gradient_percentage_threshold; // Not used by PowerCASE
  cdiBOOLEAN fix_pt_time_p;
  double total_pt_duration;       // Positive if user specifies the total PowerTHERM duration
                                  // Negative if the PowerTHERM end time is inferred from PowerFLOW time
} *CDI_CMDL;

/* Surface Coupling Boundary Condition chunk -- Describes a boundary
   condition that will be applied to the coupling model (using data
   from the PowerFLOW simulation). */
typedef struct sCDI_SCBC
{
#define	CDI_CHUNK_TYPE_SCBC     CIO_BUILDCCCC('s', 'c', 'b', 'c')
  cdiINT32 model_index;
  cdiINT32 target_type;
  char *target_name;
  double max_match_angle;
  double max_match_distance;
  cdiINT32 target_side_to_match;
  cdiINT32 n_source_constraints;
  cdiINT32 *source_constraint_face_indices;
} *CDI_SCBC;

/* Coupled Wall chunk -- Describes a wall whose boundary condition will be
   based on data coming from the coupling model. */
typedef struct sCDI_CPLW
{
#define	CDI_CHUNK_TYPE_CPLW     CIO_BUILDCCCC('c', 'p', 'l', 'w')
  cdiINT32 face_index;
  cdiINT32 model_index;
  double max_match_angle;
  double max_match_distance;
  cdiINT32 n_source_constraints;
  cdiINT32 *source_constraint_types;
  cdiINT32 *source_constraint_sides;
  char **source_constraint_names;
  cdiINT32 flags;
} *CDI_CPLW;

typedef struct sCDI_CDSR
{           /* condenser */
#define  CDI_CHUNK_TYPE_CDSR     CIO_BUILDCCCC('c', 'd', 's', 'r')
  char *name;                       /* condenser name */
  char *part_name;                  /* name of the condenser's part, same as above for part-based condensers */
  cdiINT32 type;                    /* the fluid type (R134a, etc.) */
  cdiINT32 tool;                    /* 1D tool (powercool, etc.) */
  cdiINT32 flags;                   /* Currently only CDI_HXCH_HAS_PM_INTERFACE */
  cdiINT32 table_csys_index;        /* csys chunk index used for table */
  cdiINT32 medium_csys_index;       /* csys chunk index used for porous medium */
  cdiINT32 inlet_face_index;        /* inlet measurement face chunk index */
  cdiINT32 outlet_face_index;       /* outlet measurement face chunk index */
  cdiINT32 coolant_entry_face_index;/* coolant entry face chunk index */
  cdiINT32 top_exchanger_face_index;/* top exchanger face chunk index */
  cdiINT32 inlet_facet_offset;      /* inlet measurement facet index offset */
  cdiINT32 outlet_facet_offset;     /* outlet measurement facet index offset */
  cdiINT32 inlet_meas_index;        /* inlet measurement chunk index */
  cdiINT32 outlet_meas_index;       /* outlet measurement chunk index */
  cdiINT32 heat_gen_meas_index;     /* heat generation measurement chunk index */
  cdiINT32 table_index;             /* data table chunk index */
  cdiINT32 adiabatic_index;         /* adiabatic medium layer chunk index (into FLUD list) */
  cdiINT32 medium_index;            /* porous medium chunk index (into FLUD list) */
  cdiINT32 n_passes;                /* number of passes */
  cdiINT32 *n_tubes;                /* one n_tubes value for each pass */
  double x_len;                     /* length of x-oriented depth */
  double y_len;                     /* length of y-oriented depth */
  double z_len;                     /* length of z-oriented depth */
  double mass_flow_rate;
  double heat_rejection;
  double entry_temp;
  double entry_pressure;            /* refrigerant pressure at inlet */
  double pressure_drop;
  double experiment_exit_temp;      /* experiment outlet temperature */
  double min_air_flow;              /* minimum input air mass flow rate */
  double max_air_flow;              /* maximum input air mass flow rate */
  double kc_coeff;                  /* sandwich formula Kc coefficient */
  double alpha_coeff;               /* sandwich formula alpha coefficient */
  double d_coeff;                   /* sandwich formula D coefficient */
  char *data_string;                /* condenser data string */
  sCDI_UDST udstValues;             /* user-defined scalar transport values */
} *CDI_CDSR;


/* Fluid component chunk -- describes the characteristics of a fluid component
   in a multi-phase simulation.*/
typedef struct sCDI_FCMP
{
#define	CDI_CHUNK_TYPE_FCMP     CIO_BUILDCCCC('f', 'c', 'm', 'p')
  char *name;                       /* condenser name */
  cdiINT32 equation_of_state;
  double viscosity;
  double molecular_weight;
} *CDI_FCMP;

/* 'Components' definition -- contains any number of FCMP chunks */
#define	CDI_CHUNK_TYPE_CMPS		CIO_BUILDCCCC('c', 'm', 'p', 's')

/* 'Super-region' definition -- contains a list (RGNS) of its constituent regions */
#define	CDI_CHUNK_TYPE_SPRG		CIO_BUILDCCCC('s', 'p', 'r', 'g')


//virtual structs
#define CDI_CHUNK_TYPE_VSRS             CIO_BUILDCCCC('v', 's', 'r', 's')
//virtual struct
#define CDI_CHUNK_TYPE_VSTR             CIO_BUILDCCCC('v', 's', 't', 'r')

typedef struct sCDI_MMBR 
{
  #define CDI_CHUNK_TYPE_MMBR             CIO_BUILDCCCC('m', 'm', 'b', 'r')
  std::string name;
  double value;
  std::string unit;
  std::string unitclass;
  
} *CDI_MMBR;


// realistic looks container chunk
#define CDI_CHUNK_TYPE_RLKS             CIO_BUILDCCCC('r', 'l', 'k', 's')

// chunk to store information about a single realistic look
typedef struct sCDI_LOOK
{
#define CDI_CHUNK_TYPE_LOOK CIO_BUILDCCCC('l', 'o', 'o', 'k')
  std::string name;
  std::string type;
  std::string origin;
  std::vector<unsigned char> serializedLook;
} *CDI_LOOK;



cdiBOOLEAN cdi_begin_write_chunk(CDI_INFO cdi_info, CIO_CCCC cccc);
cdiBOOLEAN cdi_end_write_chunk(CDI_INFO cdi_info);

#define WITH_CDI_CHUNK(cdi_info, cccc)				\
  cdiINT32 ___(i) = 0;                      			\
  cdi_push(cdi_info, cccc);					\
  for(___(i) = 0; ___(i) < 1; ___(i)++, cdi_pop(cdi_info))

/*
  Example:

  WITH_CDI_CHUNK((cdi_info, CDI_CHUNK_TYPE_FACT) {
    write_the_fact_stuff;;;;;
  }
*/

cdiBOOLEAN cdi_read_face(CDI_INFO cdi_info, CDI_FACE face);
cdiBOOLEAN cdi_write_face(CDI_INFO cdi_info, CDI_FACE face);
void cdi_empty_face(CDI_FACE face);

cdiBOOLEAN cdi_read_rgnd(CDI_INFO cdi_info, CDI_RGND rgnd);
void cdi_empty_rgnd(CDI_RGND rgnd);

cdiBOOLEAN cdi_read_model_view(CDI_INFO cdi_info, CDI_MDLV mdlv);
cdiBOOLEAN cdi_write_model_view(CDI_INFO cdi_info, CDI_MDLV mdlv);
void cdi_empty_model_view(CDI_MDLV mdlv);

cdiBOOLEAN cdi_read_psmv(CDI_INFO cdi_info, CDI_PSMV psmv);
cdiBOOLEAN cdi_write_psmv(CDI_INFO cdi_info, CDI_PSMV psmv);
void cdi_empty_psmv(CDI_PSMV psmv);

cdiBOOLEAN cdi_read_facet(CDI_INFO cdi_info, CDI_FACT facet);
cdiBOOLEAN cdi_write_facets(CDI_INFO cdi_info, CDI_FACT facets, cdiINT32 n_facets);
cdiBOOLEAN cdi_read_facet_header(CDI_INFO cdi_info, CDI_FACT_HEADER header);
cdiBOOLEAN cdi_write_facet_header(CDI_INFO cdi_info, CDI_FACT_HEADER header);

cdiBOOLEAN cdi_read_vertex_coords(CDI_INFO cdi_info, double *coords, cdiINT32 n_vertex);
cdiBOOLEAN cdi_write_vertex_coords(CDI_INFO cdi_info, double *coords, cdiINT32 n_vertex);

cdiBOOLEAN cdi_read_case_region_count(CDI_INFO cdi_info, CDI_RGCT rgct);
cdiBOOLEAN cdi_write_case_region_count(CDI_INFO cdi_info, CDI_RGCT rgct);

cdiBOOLEAN cdi_read_region_numeration(CDI_INFO cdi_info, CDI_RGNN rgnn);
cdiBOOLEAN cdi_write_region_numeration(CDI_INFO cdi_info, CDI_RGNN rgnn);

cdiBOOLEAN cdi_read_super_region(CDI_INFO cdi_info, CDI_RGNN rgnn);
cdiBOOLEAN cdi_write_super_region(CDI_INFO cdi_info, CDI_RGNN rgnn);

cdiBOOLEAN cdi_read_region_display_properties(CDI_INFO cdi_info, CDI_RGDP rgdp);
cdiBOOLEAN cdi_write_region_display_properties(CDI_INFO cdi_info, CDI_RGDP rgdp);

cdiBOOLEAN cdi_read_partition_type(CDI_INFO cdi_info, CDI_PRTT prtt);
cdiBOOLEAN cdi_write_partition_type(CDI_INFO cdi_info, CDI_PRTT prtt);

cdiBOOLEAN cdi_read_segment_index(CDI_INFO cdi_info, CDI_SGID sgid);
cdiBOOLEAN cdi_write_segment_index(CDI_INFO cdi_info, CDI_SGID sgid);

cdiBOOLEAN cdi_read_shininess(CDI_INFO cdi_info, CDI_SHIN shininess);
cdiBOOLEAN cdi_write_shininess(CDI_INFO cdi_info, CDI_SHIN shininess);

cdiBOOLEAN cdi_read_rgba(CDI_INFO cdi_info, CDI_RGBA rgba, cdiINT32 n_rgba);
cdiBOOLEAN cdi_write_rgba(CDI_INFO cdi_info, CDI_RGBA rgba, cdiINT32 n_rgba);

cdiBOOLEAN cdi_read_texture_map(CDI_INFO cdi_info, CDI_TXTR txtr);
cdiBOOLEAN cdi_write_texture_map(CDI_INFO cdi_info, CDI_TXTR txtr);

cdiBOOLEAN cdi_read_scct(CDI_INFO cdi_info, CDI_SCCT scct);
cdiBOOLEAN cdi_write_scct(CDI_INFO cdi_info, CDI_SCCT scct);

cdiBOOLEAN cdi_read_cmdl(CDI_INFO cdi_info, CDI_CMDL cmdl);
cdiBOOLEAN cdi_write_cmdl(CDI_INFO cdi_info, CDI_CMDL cmdl);
void cdi_empty_cmdl(CDI_CMDL cmdl);

cdiBOOLEAN cdi_read_scbc(CDI_INFO cdi_info, CDI_SCBC scbc);
cdiBOOLEAN cdi_write_scbc(CDI_INFO cdi_info, CDI_SCBC scbc);
void cdi_empty_scbc(CDI_SCBC scbc);

cdiBOOLEAN cdi_read_cplw(CDI_INFO cdi_info, CDI_CPLW cplw);
cdiBOOLEAN cdi_write_cplw(CDI_INFO cdi_info, CDI_CPLW cplw);
void cdi_empty_cplw(CDI_CPLW cplw);

cdiBOOLEAN cdi_read_fcmp(CDI_INFO cdi_info, CDI_FCMP fcmp);
cdiBOOLEAN cdi_write_fcmp(CDI_INFO cdi_info, CDI_FCMP fcmp);
void cdi_empty_fcmp(CDI_FCMP fcmp);

cdiBOOLEAN cdi_read_condenser(CDI_INFO cdi_info, CDI_CDSR cdsr);
cdiBOOLEAN cdi_write_condenser(CDI_INFO cdi_info, CDI_CDSR cdsr);
void cdi_empty_condenser(CDI_CDSR cdsr);

cdiBOOLEAN cdi_read_mmbr(CDI_INFO cdi_info, CDI_MMBR);
cdiBOOLEAN cdi_write_mmbr(CDI_INFO cdi_info,CDI_MMBR);
//no empty needed

cdiBOOLEAN cdi_read_realistic_look(CDI_INFO cdi_info, CDI_LOOK);
cdiBOOLEAN cdi_write_realistic_look(CDI_INFO cdi_info, CDI_LOOK);
//no empty needed

void cdi_report_read_error(const char *chunk_name);
void cdi_report_write_error(const char *chunk_name);

//--- Begin geometry retrieval section ---

// CDI_ENTITY_FILTER_FUNCTIONs are used to identify faces and regions of interest.  For example,
// in PowerACOUSTICS we are only interested in faces that are included in a measurement and have
// solid physics.  The caller supplies a filter function that determines what's interesting.
//
// Note: filter functions should err on the side of "interesting".
//
// measured:    whether the entity was included in any measurement
// filterClass: a loose categorization based on the needs of ACOUSTICS and INSIGHT
//              and potentially derived from multiple sources
enum CDI_ENTITY_FILTER_CLASS {
  CDI_TREAT_AS_SOLID,
  CDI_TREAT_AS_FLUID,
  CDI_TREAT_AS_UNKNOWN
};
typedef bool (*CDI_ENTITY_FILTER_FUNCTION)(bool measured, CDI_ENTITY_FILTER_CLASS filterClass);

// Default filter function.  Always returns true.
bool cdi_match_any(bool measured, CDI_ENTITY_FILTER_CLASS filterClass);

// Filter function that returns true for entities that are categorized as solid.  These are
// interesting for the Solid Faces widget in the PowerINSIGHT Run Monitor editor.
bool cdi_is_solid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass);

// Filter function that returns true for entities that not known to be fluid.  This filter is
// deliberately looser than cdi_is_solid.
bool cdi_is_not_fluid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass);

// Filter function that returns true for solid entities that can show up in a measurement
// file, with pressure data. These are the interesting entities for PowerACOUSTICS.
bool cdi_is_measured_solid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass);

// Filter function that returns true for solid entities or measured entities.  This is designed
// to capture measured sampled surfaces that are not solid, but will not exclude solid entities
// that are not measured.
bool cdi_is_measured_or_solid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass);

// Filter function for entities that are measured and not known to be fluid.
bool cdi_is_measured_not_fluid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass);

// Filter function that returns true for entities categorized as fluid.
// These are interesting in the Run Monitor editor.
bool cdi_is_fluid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass);

// cdi_find_geometry extracts the geometry hierarchy from a CDI File, in a form
// suitable for populating a cQ_GEOM_SELECTION_DLG.  The caller must provide a
// freshly-constructed cCDI_PARTITIONS object (as it likely wants to store that
// object for subsequent calls to cdi_find_geometry_by_path).
//
// Usage examples:
// n = cdi_find_geometry(cdiInfo, cdiPartitions, entities)                        => populates entities without filtering
// n = cdi_find_geometry(cdiInfo, cdiPartitions, entities, cdi_is_measured_solid) => populates entities and marks faces that satisfy
//                                                                                   cdi_is_measured_solid as interesting
//
bool cdi_find_geometry(CDI_INFO cdiInfo,
                       cCDI_PARTITIONS& cdiPartitions,
                       cGEOM_COMMON_ENTITY_LIST& entities,
                       CDI_ENTITY_FILTER_FUNCTION faceFilterFunc = cdi_match_any);

// For use on prepared cCDI_PARTITIONS and cCDI_GEOM_SELECTION_TREE objects, and sets of IDs
// for parts and faces actually measured, such as might be loaded from a measurement file.
bool cdi_find_geometry(cCDI_PARTITIONS& cdiPartitions,
                       const std::unordered_set<cdiINT32>& regionsMeasured,
                       const std::unordered_set<cdiINT32>& facesMeasured,
                       cCDI_GEOM_SELECTION_TREE& selectionTree,
                       cGEOM_COMMON_ENTITY_LIST& entities,
                       CDI_ENTITY_FILTER_FUNCTION regionFilterFunc = cdi_match_any,
                       CDI_ENTITY_FILTER_FUNCTION faceFilterFunc = cdi_match_any);

// cdi_find_geometry_by_path is what PowerACOUSTICS and PowerINSIGHT (Run Monitor) use to
// check to see if stored face names or full paths are valid and unambiguous according
// to the cCDI_PARTITIONS object.
//
// If there is exactly one match for the input path (which can also be a face or part
// name) then the function will return true. If the input path is just a name, then
// the candidates are further qualified by typeExpected. An invalid path/name
// (zero matches), or an ambiguous one (more than one match) will both result
// in cdi_find_geometry_by_path returning false.
//
// For a true return, if you pass nullptr for both pEntity and pFaceIndices, the function
// will simply return whether the match was valid and unambiguous.  If you pass pEntity,
// then *pEntity will be filled with the cGEOM_COMMON_ENTITY for the matching path.  If you
// pass pFaceIndices, then *pFaceIndices wlil be filled in with the constituent face indices
// for the match.
bool cdi_find_geometry_by_path(const std::string& path,                         // can also be a face or part name
                               eGEOM_COMMON_ITEM_TYPE typeExpected,             // used ONLY if path is a name
                               const cCDI_PARTITIONS& cdiPartitions,
                               std::vector<cdiINT32>* pFaceIndices = nullptr,   // optional out parameter
                               cGEOM_COMMON_ENTITY* pEntity = nullptr);         // optional out parameter

// Expand a vector of selection paths, which may have a prefixed ! for an explicit deselection,
// to a vector (indexed by face index) of bool, where the bool means "effectively included after
// evaluating all selections and deselections".
//
// The paths may be simple face names, as taken from a legacy measurement file, but since we
// did not support deselections with legacy face names, it should not be necessary to call this
// function.
//
// If you pass a callback function, it will be called with each path from the original vector
// of selections that is either missing or ambiguous. Your callback should normally remember
// the failing paths for logging in an app-dependent way, and return true. If it returns
// false, cdi_expand_selections_to_face_indices will abort and return an empty vector.
std::vector<bool> cdi_expand_selections_to_face_indices(const std::vector<std::string>& selections,
                                                        const cCDI_PARTITIONS& cdiPartitions,
                                                        bool (*errorCallback)(const std::string& path) = nullptr);

//--- End geometry retrieval section ---

#endif /* !_CDI_INTERFACE_H */
