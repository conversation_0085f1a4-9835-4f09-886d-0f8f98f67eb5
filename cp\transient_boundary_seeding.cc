/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Transient boundary seeding class
 *
 * Mohit Jain, Exa Corporation, Dassault Systemes Simulia Corp.
 * Created Thu Jun 20, 2019
 *--------------------------------------------------------------------------*/

#include "transient_boundary_seeding.h"
#include "cp_info.h"

VOID sTRANSIENT_BOUNDARY_SEEDING::allocate_send_requests()
{
  m_tbs_send_requests = new MPI_Request[total_sps];
  ccDOTIMES(sp, total_sps) {
    m_tbs_send_requests[sp] = MPI_REQUEST_NULL;
  }
}

VOID sTRANSIENT_BOUNDARY_SEEDING::insert_value_in_n_var_mask_map(auINT32 var_mask)
{
  if(m_n_var_mask_map.find(var_mask) == m_n_var_mask_map.end())
    m_n_var_mask_map[var_mask] = 0;
}

inline auINT32 sTRANSIENT_BOUNDARY_SEEDING::tbs_data_index(auINT32 var_index, STP_PROC home_sp, auINT32 var_mask)
{
  auINT32 index = var_index + m_tbs_info[home_sp].n_total_vars * m_n_frames_sent + m_n_var_mask_map[var_mask];
  return index;
}

VOID sTRANSIENT_BOUNDARY_SEEDING::increment_tbs_info(auINT32 var_mask, STP_PROC home_sp)
{
  m_tbs_info[home_sp].n_tbs_surfels++;
  ccDOTIMES(var, DGF_N_BOUNDARY_SEED_VARS) {
    if((var_mask & 1 << var) && (var != DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX || var != DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX))
      m_tbs_info[home_sp].n_total_vars++;
  }
}

VOID sTRANSIENT_BOUNDARY_SEEDING::read_transient_boundary_seeding_data(auINT32 var)
{
  ccDOTIMES(sci, m_n_pts_total) {
    auto it = m_transient_boundary_seeding_map.find(sci);
    if(it == m_transient_boundary_seeding_map.end())
      continue;
    else {
      std::vector<sTBS_SURFEL_INFO> &tbs_surfel_info = it->second;
      for(auto const& s : tbs_surfel_info) {
        STP_PROC home_sp =  s.sp;
        auINT32 var_mask = s.var_mask;
        if(!(var_mask & (1 << var)))
          continue;
        auINT32 index = tbs_data_index(s.var_index, home_sp, var_mask);
        fill_transient_boundary_seeding_data(m_send_buffer[home_sp][index], sci, m_n_total_frames_sent, var, var_mask, m_reset_meas_file_buffer);
        m_reset_meas_file_buffer = FALSE;
      }
    }
  }
}

VOID sTRANSIENT_BOUNDARY_SEEDING::increment_variable_mask_map(auINT32 var)
{
  for(auto &x : m_n_var_mask_map) {
    if(x.first & (1 << var))
      x.second++;
  }
}

VOID sTRANSIENT_BOUNDARY_SEEDING::reset_variable_mask_map()
{
  for(auto &x : m_n_var_mask_map) {
    x.second = 0;
  }
}

VOID sTRANSIENT_BOUNDARY_SEEDING::set_n_frames_per_iter() {
  ccDOTIMES(n, cp_info.n_seed_from_meas_descs)
    m_n_frames_per_iter = MIN(m_n_frames_per_iter, m_total_meas_frames);
}

VOID sTRANSIENT_BOUNDARY_SEEDING::set_buffer_size()
{
  ccDOTIMES(sp, total_sps)
    m_send_buffer_size += m_tbs_info[sp].n_total_vars;
  m_send_buffer_size *= m_n_frames_per_iter;
}

VOID sTRANSIENT_BOUNDARY_SEEDING::allocate_send_buffer()
{
  set_buffer_size();
  m_send_buffer = new sFLOAT*[total_sps];
//  allocating total buffer size needed for send data. index > 0 have pointer to data for corresponding SPs.
  m_send_buffer[0] = new sFLOAT[m_send_buffer_size];
  for(int i = 1; i < total_sps; i++) {
    m_send_buffer[i] = m_send_buffer[i - 1] + m_tbs_info[i-1].n_total_vars * m_n_frames_per_iter;
  }
}

VOID sTRANSIENT_BOUNDARY_SEEDING::transient_seed_surfel_comm(asINT32 imeas)
{
  int flag = 0;
  ccDOTIMES(n, cp_info.n_seed_from_meas_descs) {
    if(m_n_total_frames_sent >= m_total_meas_frames)
      m_n_total_frames_sent = m_meas_start_frame_num;
  }
  MPI_Status status;
  m_n_frames_sent = 0;
  ccDOTIMES(sp, total_sps) {
    MPI_Test(&m_tbs_send_requests[sp], &flag, &status);
    if(!flag)
      return;
  }
  for(int i = m_n_frames_sent; i < m_n_frames_per_iter; i++) {
    ccDOTIMES(var, DGF_N_BOUNDARY_SEED_VARS) {
      m_reset_meas_file_buffer = TRUE;
      if(!(m_masks_union & (1 << var)))
        continue;
      read_transient_boundary_seeding_data(var);
      increment_variable_mask_map(var);
    }
    m_n_frames_sent++;
    m_n_total_frames_sent++;
    if(m_n_total_frames_sent >= m_total_meas_frames)
      m_n_total_frames_sent = m_meas_start_frame_num;
    reset_variable_mask_map();
  }
  ccDOTIMES(sp, total_sps) {
      int tag = make_mpi_tag<eMPI_MSG::TBS>(imeas);
      if(m_tbs_info[sp].n_total_vars > 0)
        MPI_Issend(m_send_buffer[sp],m_tbs_info[sp].n_total_vars * m_n_frames_per_iter, MPI_FLOAT, sp,
            tag, eMPI_sp_cp_comm, &m_tbs_send_requests[sp]);
  }
}

VOID sTRANSIENT_BOUNDARY_SEEDING::set_meas_cell_index_from_map(cDGF_SURFEL_DESC &surfel_desc, asINT32 &meas_cell_index)
{
  meas_cell_index = -1;
  sriFACET_ID old_cdi_facet_id = surfel_desc.s.facet_id - m_facet_id_offset;
  if (m_meas_cell_index_from_facet_id.find(old_cdi_facet_id) != m_meas_cell_index_from_facet_id.end()) {
    meas_cell_index = m_meas_cell_index_from_facet_id[old_cdi_facet_id];
  }
}

VOID sTRANSIENT_BOUNDARY_SEEDING::fill_transient_boundary_seeding_map(cDGF_SURFEL_DESC &surfel_desc, STP_PROC home_sp, cDGF_TBS &tbs)
{
  asINT32 meas_cell_index = -1;
  sFLOAT translated_surfel_centroid[3], scaled_surfel_centroid[3];
  vsub(translated_surfel_centroid, surfel_desc.s.centroid, m_surfel_translation_coord);
  vscale(scaled_surfel_centroid, m_cdi_to_meas_file_length_scale_factor, translated_surfel_centroid);
  if(!m_is_octree_map) {
    sriFACET_ID old_cdi_facet_id = surfel_desc.s.facet_id - m_facet_id_offset;
    auto const &it = m_facet_id_to_meas_cells_map.find(old_cdi_facet_id);
    if(it != m_facet_id_to_meas_cells_map.end()) {
      meas_cell_index = nearest_meas_cell_to_surfel(it->second, scaled_surfel_centroid);
    }
  } else {
  dFLOAT max_distance = 2 * scale_to_cube_size(surfel_desc.s.surfel_scale) * m_cdi_to_meas_file_length_scale_factor;
  meas_cell_index = m_msi_table->find_nearest_mci(scaled_surfel_centroid, max_distance);
#ifdef DEBUG_OCTREE
  if(msi != meas_cell_index && meas_cell_index != -1 && msi != -1) {
    sFLOAT diff_facet[3], diff_octree[3];
    sFLOAT facet_cent[3], octree_cent[3];
    ccDOTIMES(i, 3) {
      facet_cent[i] = m_msi_table->get_centroid(meas_cell_index)[i];
      octree_cent[i] = m_msi_table->get_centroid(msi)[i];
    }
    vsub(diff_facet, surfel_centroid, facet_cent);
    vsub(diff_octree, surfel_centroid, octree_cent);
    sFLOAT dist_facet, dist_octree;
    dist_facet = vdot(diff_facet, diff_facet);
    dist_octree = vdot(diff_octree, diff_octree);
    if(dist_octree > dist_facet)
    msg_print("Index from facet %d octree %d octree dist %f facet dis %f\n facet centroid %f %f %f octree centroid %f %f %f surfel centroid %f %f %f",
        meas_cell_index, msi, dist_octree, dist_facet, m_msi_table->get_centroid(meas_cell_index)[0],
        m_msi_table->get_centroid(meas_cell_index)[1], m_msi_table->get_centroid(meas_cell_index)[2], m_msi_table->get_centroid(msi)[0],
        m_msi_table->get_centroid(msi)[1],  m_msi_table->get_centroid(msi)[2], surfel_centroid[0], surfel_centroid[1], surfel_centroid[2]);
  }
#endif
  }
  if(meas_cell_index == -1)
    return;
  tbs.transient_boundary_seeding = TRUE;
  tbs.var_mask = cp_info.seed_from_meas_mask_from_face_index[surfel_desc.s.face_index];
  auto it2 = m_transient_boundary_seeding_map.find(meas_cell_index);
  if(it2 == m_transient_boundary_seeding_map.end()) {
    m_transient_boundary_seeding_map[meas_cell_index] = std::vector<sTBS_SURFEL_INFO>();
  }
  sTBS_SURFEL_INFO surfel_info(home_sp, m_tbs_info[home_sp].n_total_vars, cp_info.seed_from_meas_mask_from_face_index[surfel_desc.s.face_index], 1.0F);
  m_transient_boundary_seeding_map[meas_cell_index].push_back(surfel_info);
}

VOID sTRANSIENT_BOUNDARY_SEEDING::tbs_finalize() {
  ccDOTIMES(sp, total_sps) {
    if(m_tbs_send_requests[sp] != MPI_REQUEST_NULL)
      MPI_Cancel(&m_tbs_send_requests[sp]);
  }
}

VOID init_transient_boundary_seeding_comm() {
  ccDOTIMES(i, cp_info.n_seed_from_meas_descs) {
    if(!cp_info.seed_from_meas_descs[i]->m_transient_boundary_seeding)
      continue;
    TRANSIENT_BOUNDARY_SEEDING tbs = static_cast<TRANSIENT_BOUNDARY_SEEDING>(cp_info.seed_from_meas_descs[i]);
    tbs->allocate_send_requests();
    tbs->set_n_frames_per_iter();
    tbs->allocate_send_buffer();
  }
}

VOID send_transient_seed_data() {
  ccDOTIMES(i, cp_info.n_seed_from_meas_descs) {
    if(!cp_info.seed_from_meas_descs[i]->m_transient_boundary_seeding)
      continue;
    static_cast<TRANSIENT_BOUNDARY_SEEDING>(cp_info.seed_from_meas_descs[i])->transient_seed_surfel_comm(i);
  }
}

VOID complete_tbs_send_req() {
  ccDOTIMES(i, cp_info.n_seed_from_meas_descs) {
    if(!cp_info.seed_from_meas_descs[i]->m_transient_boundary_seeding)
      continue;
    ccDOTIMES(sp, total_sps) {
      int flag = 0;
      MPI_Status status;
      while(!flag)
        MPI_Test(&static_cast<TRANSIENT_BOUNDARY_SEEDING>(cp_info.seed_from_meas_descs[i])->m_tbs_send_requests[sp], &flag, &status);
    }
  }
}
VOID finalize_tbs_requests() {
  ccDOTIMES(i, cp_info.n_seed_from_meas_descs) {
    if(!cp_info.seed_from_meas_descs[i]->m_transient_boundary_seeding)
      continue;
    static_cast<TRANSIENT_BOUNDARY_SEEDING>(cp_info.seed_from_meas_descs[i])->tbs_finalize();
  }
}
