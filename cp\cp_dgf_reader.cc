/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 


//----------------------------------------------------------------------------
// David Hall                                             Dec 10, 2008
//----------------------------------------------------------------------------

#include TIRE_H

#include "cp_dgf_reader.h"
#include "common.h"
#include "cp_stream_manager.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "jobctl.h"
#include "seed.h"
#include "window.h"
#include <iostream>
#include <sys/socket.h>
#include "ublk_octree.h"
#include "cp_seed_from_meas.h"
#include "trajectory_results.h"
#include <unordered_map>
#include "coupling_model.h"

#ifndef IGNORE_MSSG_TYPES
#define IGNORE_MSSG_TYPES message_type::SUCCESS, message_type::INFO, message_type::WARNING
// #define IGNORE_MSSG_TYPES message_type::SUCCESS
#endif
// #define DEBUGGING_COLORINCHIS

// ----------------------------------------------------------------------------
// Colored ouput messages for Linux
// ----------------------------------------------------------------------------
enum class message_type
{
  NONE,
  OK,
  SUCCESS,
  ERROR,
  WARNING,
  INFO,
  END
};

template <message_type MT> struct message_color;

#define ADD_MSSG_COLOR(MSSG_TYPE, TERM_COLOR)             \
template <> struct message_color<message_type::MSSG_TYPE> \
{                                                         \
  static constexpr const char * term_color = TERM_COLOR;  \
};

#ifdef DEBUGGING_COLORINCHIS
ADD_MSSG_COLOR(NONE   , "\e[0m")
ADD_MSSG_COLOR(END    , "\e[0m\n")
ADD_MSSG_COLOR(OK     , "\e[38;5;076m[   OK    ] ")
ADD_MSSG_COLOR(SUCCESS, "\e[38;5;076m[ SUCCESS ] ")
ADD_MSSG_COLOR(ERROR  , "\e[38;5;160m[  ERROR  ] ")
ADD_MSSG_COLOR(WARNING, "\e[38;5;208m[ WARNING ] ")
ADD_MSSG_COLOR(INFO   , "\e[38;5;081m[   INFO  ] ")
#undef DEBUGGING_COLORINCHIS
#else
ADD_MSSG_COLOR(NONE   , "")
ADD_MSSG_COLOR(END    , "\n")
ADD_MSSG_COLOR(OK     , "[   OK    ] ")
ADD_MSSG_COLOR(SUCCESS, "[ SUCCESS ] ")
ADD_MSSG_COLOR(ERROR  , "[  ERROR  ] ")
ADD_MSSG_COLOR(WARNING, "[ WARNING ] ")
ADD_MSSG_COLOR(INFO   , "[   INFO  ] ")
#endif
#undef ADD_MSSG_COLOR

#define MSSG_INDENT "\n            "

template <message_type... MSSG_TYPES>
struct PrintMssgDeactivator
{
  public:

    template <message_type MSSG_TYPE>
    static constexpr bool isDeactivated()
    {
      return isDeactivatedRecursive<MSSG_TYPE>();
    }

    template <message_type MSSG_TYPE>
    static constexpr bool isActivated()
    {
      return !isDeactivated<MSSG_TYPE>();
    }

  protected:

    template <message_type MSSG_TYPE, int MSSG_IDX = 0>
    static constexpr bool isDeactivatedRecursive()
    {
      if constexpr (MSSG_IDX == std::tuple_size<decltype(hiden_val_)>())
        return false;
      else if constexpr (MSSG_TYPE == std::get<MSSG_IDX>(hiden_val_))
        return true;
      else
        return isDeactivatedRecursive<MSSG_TYPE, MSSG_IDX + 1>();
    }

    static constexpr std::tuple hiden_val_ = std::tuple(MSSG_TYPES...);
};
using mssg_activator = PrintMssgDeactivator<IGNORE_MSSG_TYPES>;

template <message_type MT, typename... Ts>
static inline void print_mssg(const char * mssg, const Ts&... args)
{
  if constexpr (mssg_activator::isActivated<MT>())
  {
    #pragma clang diagnostic push
    #pragma clang diagnostic ignored "-Wformat"
    printf(message_color<MT>::term_color);
    printf(mssg, args...);
    printf(message_color<message_type::END>::term_color);
    #pragma clang diagnostic pop
  }
}

template <message_type MT = message_type::ERROR, typename... Ts>
static inline void print_assert(bool condition, const char * mssg, const Ts&... args)
{
#ifndef NDEBUG
  if (!condition)
  {
    print_mssg<MT, Ts...>(mssg, args...);
    assert(condition);
  }
#endif
}

template <sCP_DGF_READER::reader_t RT, message_type MT = message_type::INFO, typename... Ts>
static inline void print_mssg_in_debug(const char * mssg, const Ts&... args)
{
  if constexpr (RT == sCP_DGF_READER::DEBUG_MODE || MT == message_type::ERROR)
    print_mssg<MT, Ts...>(mssg, args...);
}

template <message_type MT = message_type::INFO, typename... Ts>
static inline void print_mssg_in_debug(sCP_DGF_READER::reader_t rt, const char * mssg, const Ts&... args)
{
  if constexpr (MT == message_type::ERROR)
    print_mssg<MT, Ts...>(mssg, args...);
  else if (rt == sCP_DGF_READER::DEBUG_MODE)
    print_mssg<MT, Ts...>(mssg, args...);
}


/** Macro to create a function associated to another of the HDF5 that returns hid_t.
 * If the returned hid_t is invalid, then print a message and exit. The generated
 * functions takes the form FUN_NAME_e
 */
#define HDF5_EXIT_ON_FAIL_OPEN(FUN_NAME)                           \
template <typename T0, typename... Ts>                             \
inline hid_t FUN_NAME ## _e(T0 arg0, Ts... args)                   \
{                                                                  \
  if constexpr (std::is_same<T0, const char *>::value)             \
  {                                                                \
    hid_t type_id = FUN_NAME(args...);                             \
    if (type_id == H5I_INVALID_HID)                                \
    {                                                              \
      std::cerr << message_color<message_type::ERROR>::term_color  \
        << arg0 << message_color<message_type::END  >::term_color; \
      MPI_Abort(eMPI_sp_cp_comm, 1);                                \
    }                                                              \
    return type_id;                                                \
  }                                                                \
  else                                                             \
  {                                                                \
    hid_t type_id = FUN_NAME(arg0, args...);                       \
    if (type_id == H5I_INVALID_HID)                                \
      MPI_Abort(eMPI_sp_cp_comm, 1);                                \
    return type_id;                                                \
  }                                                                \
}

HDF5_EXIT_ON_FAIL_OPEN(H5Fopen)
HDF5_EXIT_ON_FAIL_OPEN(H5Gopen2)
HDF5_EXIT_ON_FAIL_OPEN(H5Dopen2)
HDF5_EXIT_ON_FAIL_OPEN(H5Dget_space)


// Function for testing
#define MEAS_WIN_TEST_LINE(VAR_TYPE, VAR, LINE)                   \
{                                                                 \
  VAR_TYPE VAR ## 2;                                              \
  lgi_read(m_ckpt_istream, VAR ## 2);                             \
  print_assert(VAR == VAR ## 2,                                   \
               "Different value of variable " # VAR ": %i VS %i"  \
               MSSG_INDENT "%s:%u",                               \
               VAR, VAR ## 2, __FILE__, LINE);                    \
  if (VAR == VAR ## 2) print_mssg<message_type::SUCCESS>(         \
    "Success when reading variable " # VAR ". Value: %u", VAR);   \
}

#define MEAS_WIN_TEST_LINE_N(VAR_TYPE, VAR, N, LINE)              \
{                                                                 \
  VAR_TYPE * VAR ## 2 = new VAR_TYPE[N];                          \
  lgi_read(m_ckpt_istream, VAR ## 2, N * sizeof(VAR_TYPE));       \
  int nSuccess = 0, nErrors = 0;                                  \
  for (int nn = 0; nn < N; ++nn)                                  \
    if (VAR[nn] == VAR ## 2 [nn])                                 \
      nSuccess++;                                                 \
    else                                                          \
      nErrors++;                                                  \
  print_assert(nErrors == 0,                                      \
               "Detected %i of %i errors when reading " # VAR     \
               MSSG_INDENT "%s:%u",                               \
               nErrors, N, __FILE__, LINE);                       \
  if (nErrors == 0) print_mssg<message_type::SUCCESS>(            \
    "Success when reading array " # VAR ". %u elements read", N); \
  delete [] VAR ## 2;                                             \
}


#define READ_NEXT_N_LINE(VAR_TYPE, VAR, N, LINE)                        \
{                                                                       \
  VAR_TYPE VAR ## 2;                                                    \
  lgi_read(m_ckpt_istream, VAR ## 2);                                   \
  if (VAR != VAR ## 2)                                                  \
  {                                                                     \
    int nn = 0;                                                         \
    do                                                                  \
    {                                                                   \
      shob_sp_meas_win.read(& VAR);                                     \
      if (false) print_mssg<message_type::INFO>("% 3u: Variable " # VAR \
        ", value LGI: %u, value HDF5: % 10u", nn++, VAR ## 2, VAR);     \
    } while (VAR != VAR ## 2);                                          \
    print_assert(false, "%s:%u", __FILE__, LINE);                       \
  }                                                                     \
}

#define READ_DUMP_DATA_FROM_LGI(TYPE) \
{                                     \
  TYPE dump_var;                      \
  lgi_read(m_ckpt_istream, dump_var); \
}

#define READ_DUMP_DATA_FROM_LGI_N(TYPE, N) \
for (int nn = 0; nn < N; ++nn)             \
READ_DUMP_DATA_FROM_LGI(TYPE)

#define UPPER_DIV(N, DIV) ((N + DIV - 1) / DIV)
#define UPPER_DIV_T(N, TYPE) UPPER_DIV(N, sizeof(TYPE))

#define MEAS_WIN_TEST(VAR_TYPE, VAR) MEAS_WIN_TEST_LINE(VAR_TYPE, VAR, __LINE__)
#define MEAS_WIN_TEST_N(VAR_TYPE, VAR, N) MEAS_WIN_TEST_LINE_N(VAR_TYPE, VAR, N, __LINE__)
#define READ_NEXT_N(VAR_TYPE, VAR, N) READ_NEXT_N_LINE(VAR_TYPE, VAR, N, __LINE__)

sSUB_OCTREE g_ublk_octree;
VMEM_VECTOR<sCP_DYNBLK> g_cp_dynblks;
constexpr size_t N_VOXELS_2D = N_VOXELS_8 / 2;

static BOOLEAN mci_has_two_meas(asINT32 meas_surfel_index) {
  return (meas_surfel_index & 0x1);
}

// This is to support bsurfels, which are unique to the FLOW realm
static VOID maybe_add_ublk_to_octree(cDGF_UBLK_BASE &ublk_base)
{
  sCP_DYNBLK cp_dynblk;
  BOOLEAN  is_bsurfel_interacting  = (ublk_base.ublk_flags & DGF_UBLK_BSURFEL_INTERACTING);
  if (is_bsurfel_interacting) {
    if (cp_dynblk.add_dynblk(ublk_base)) { 
      g_cp_dynblks.push_back(cp_dynblk);
      g_ublk_octree.register_ublk(&g_cp_dynblks.back());
    }
  }
}

static VOID verify_next_tag_id(LGI_TAG_ID expected_tag_id, LGI_STREAM istream)
{
  LGI_TAG next_tag = lgi_peek_tag(istream);

  if (next_tag.id != expected_tag_id) {
    msg_internal_error("Next LGI tag is %s, not %s as expected.",
                       lgi_tag_namestring(next_tag.id),
                       lgi_tag_namestring(expected_tag_id));
  }
}

static VOID read_ckpt_lgi_header_verify_completeness(LGI_STREAM lgi_stream,
                                                     cSTRING filename)
{
  BOOLEAN complete_p;
  auINT32 lgi_version = lgi_read_header_and_completeness(lgi_stream, FALSE, &complete_p);

  asINT32 lgi_errno = lgi_stream_errno(lgi_stream);

  if (lgi_errno == LGI_ERRNO_ENDERR) {
    /* Explicitly look for the endian-ness error */
    cSTRING endian = SCALAR_BIG_ENDIAN ? "big" : "little";
    cSTRING other =  SCALAR_BIG_ENDIAN ? "little" : "big";

    msg_error("The checkpoint file \"%s\" was created on a machine with a different byte ordering."
              " This is a %s endian machine. The checkpoint file was created on a %s endian machine."
              " and can only be used to resume your simulation on a %s endian machine.",
              filename,
              endian, other, other);
  } else if (lgi_errno == LGI_ERRNO_CORHDR) {
    msg_error("The checkpoint file \"%s\" has been corrupted, or possibly it is not a"
              " checkpoint file.",
              filename);
  } else {
    if (!complete_p)
      /** Actually, this is pretty unlikely, as any error in writing the checkpoint
      *** will prevent the ctmp file from being renamed to ckpt.
      */
      msg_error("The checkpoint file \"%s\" is not complete.\n"
                "The likely cause is that the simulator terminated prematurely "
                "while writing the checkpoint file.\n"
                "Please check the simulator output file from your last run, and try again.",
                filename);
  }

  if (lgi_version != LGI_CHECKPOINT_VERSION)
    msg_error("The checkpoint file \"%s\" is version %d."
              " This software expects checkpoint version %d.",
              filename, lgi_version, LGI_CHECKPOINT_VERSION);
}

std::vector<STP_PROC> g_ublk_ghost_proc_vec;
std::vector<sINT64> g_ublk_ghost_vec_index;


// ----------------------------------------------------------------------------
// Buffer to read HDF5
// ----------------------------------------------------------------------------
struct hdf5_shob
{
  static constexpr int MAX_DEPTH_GROUP = 2;
  hid_t groups_[MAX_DEPTH_GROUP] = {H5I_INVALID_HID, H5I_INVALID_HID};
  hid_t dataset_ = H5I_INVALID_HID;
  hid_t dataspace_ = H5I_INVALID_HID;
  hsize_t offset_ = 0;
  hsize_t size_ = 0;

  static constexpr size_t buffer_capacity_ = 1ul << 24; ///< 16 Mb of data of buffer by default
  uint8_t * buffer_;
  size_t buffer_offset_;
  size_t buffer_size_;

  sCP_DGF_READER::reader_t reader_mode_;

  std::string names_[MAX_DEPTH_GROUP] = {"", ""};

  // No copy allowed
  hdf5_shob(hdf5_shob&) = delete;
  hdf5_shob(const hdf5_shob&) = delete;

  hdf5_shob() : groups_   {H5I_INVALID_HID, H5I_INVALID_HID},
                dataset_  (H5I_INVALID_HID),
                dataspace_(H5I_INVALID_HID),
                offset_(0),
                size_  (0),
                buffer_(new uint8_t[buffer_capacity_]),
                buffer_offset_(0),
                buffer_size_(0),
                reader_mode_(sCP_DGF_READER::UNKNOWN),
                names_{"", ""}
  {}

  template <typename... Ts>
  hdf5_shob(sCP_DGF_READER::reader_t rt, hid_t file_id, const std::string& group0_name, const Ts&... other_group_names) : hdf5_shob()
  {
    this->open<Ts...>(rt, file_id, group0_name, other_group_names...);
  }

  template <typename... Ts>
  hdf5_shob(hid_t file_id, const std::string& group0_name, const Ts&... other_group_names) : hdf5_shob()
  {
    this->open<Ts...>(sCP_DGF_READER::UNKNOWN, file_id, group0_name, other_group_names...);
  }

  ~hdf5_shob()
  {
    this->close();
    delete [] buffer_;
  }
  
  template <typename... Ts>
  void open(sCP_DGF_READER::reader_t rt, hid_t file_id, const std::string& group0_name, const Ts&... other_group_names)
  {
    reader_mode_ = rt;
    // Assert if the file ID is not valid
    print_assert(file_id != H5I_INVALID_HID, "Invalid HDF5 file");
    // Open groups recursively
    open_groups<std::string, Ts...>(rt, file_id, names_, groups_, group0_name, other_group_names...);
    // Open data set
    print_assert(dataset_ == H5I_INVALID_HID, "HDF5 data set already initialized.");
    dataset_ = H5Dopen2_e(groups_[last_group_idx()], "Data", H5P_DEFAULT);
    // Open data space
    print_assert(dataspace_== H5I_INVALID_HID, "HDF5 data space already initialized.");
    dataspace_ = H5Dget_space_e(dataset_);
    // Get data size from HDF5 file
    H5Sget_simple_extent_dims(dataspace_, &size_, NULL);
    // Initialize memory offset to 0
    offset_ = 0;
  }

  void close()
  {
    // Close HDF5 objects
    if (dataspace_ != H5I_INVALID_HID) H5Sclose(dataspace_);
    if (dataset_   != H5I_INVALID_HID) H5Dclose(dataset_  );
    for (int nn = MAX_DEPTH_GROUP - 1; nn >= 0; --nn)
    {
      if (groups_[nn] != H5I_INVALID_HID)
        H5Gclose(groups_[nn]);
      groups_[nn] = H5I_INVALID_HID;
    }

    // Restart values
    dataset_   = H5I_INVALID_HID;
    dataspace_ = H5I_INVALID_HID;
    offset_    = 0;                ///< Memory offset in HDF5 data
    size_      = 0;                ///< Memory size of the HDF5 data

    // Restart buffer
    buffer_offset_ = 0;
    buffer_size_   = 0;

    // Info message and deleting names of groups
    for (int nn = MAX_DEPTH_GROUP - 1; nn >= 0; --nn)
    {
      if (!names_[nn].empty())
        print_mssg_in_debug<message_type::INFO>(reader_mode_, "Closing group %s", names_[nn].c_str());
      names_[nn] = "";
      reader_mode_ = sCP_DGF_READER::UNKNOWN;
    }
  }

  hsize_t load_data(void * const rawDataPtr, hsize_t maxBytesOfData)
  {
    // Reduce amount of bytes to be read if it is larger than the amount stored in the HDF5 file
    hsize_t bytesOfData = offset_ + maxBytesOfData < size_ ?
                          maxBytesOfData : size_ - offset_;
    // Select contiguous data to be copied
    herr_t error = H5Sselect_hyperslab(dataspace_, H5S_SELECT_SET,
                                      &offset_, NULL, &bytesOfData, NULL);
    print_assert(error >= 0, "Error while slecting HDF5 data to be read. Group %s", names_[0].c_str());

    // Creting memory space for destiny
    hid_t memSpace = H5Screate_simple(1, &bytesOfData, NULL);
    // Getting data from HDF5 file to the buffer
    error = H5Dread(dataset_, H5T_NATIVE_UCHAR, memSpace,
                    dataspace_, H5P_DEFAULT, rawDataPtr);
    print_assert(error >= 0, "Error while reading HDF5 data");

    // Move memory offset
    offset_ += bytesOfData;
    // Return amount of read bytes
    return bytesOfData;
  }

  /**
   * Reads data from the file into the stream
   * @param out Stream where to save the data read from the file
   * @param data_length Number of bytes to be copied into the stream
   */
  template <typename T>
  void read(T out, size_t data_length)
  {
    // When the remaining amount of data is larger than the buffer size...
    if (buffer_capacity_ < data_length)
    {
      // Amount of data to copy from buffer, the data that have been loaded from HDF5 file
      // previously
      size_t length_to_copy_from_buffer = buffer_size_ - buffer_offset_;

      // Copy already loaded data into the ouput from the buffer
      if constexpr (std::is_pointer<T>::value)
        if (length_to_copy_from_buffer > 0)
          memcpy(out, buffer_ + buffer_offset_, length_to_copy_from_buffer);
      else
        print_assert<message_type::ERROR>(false,
          "Import large chunks into LGI streams is not ready yet.");

      // Read remaining data directly from hdf5
      load_data(out + buffer_size_, data_length - length_to_copy_from_buffer);

      // Restart buffer parameters
      buffer_offset_ = 0;
      buffer_size_ = 0;

      // Nothing more to do
      return;
    }

    // When the amount of required data is lower than the amount of remaining data stored
    //  in the buffer the additional data is read from file
    if (data_length > buffer_size_ - buffer_offset_)
      this->load_raw_data_from_file();

    // If the amount of required data is still larger than the available data the simulation stops
    print_assert<message_type::ERROR>(data_length <= buffer_size_ - buffer_offset_,
      "Available amount of data: %lu bytes"
      MSSG_INDENT "Required data: %lu bytes"
      MSSG_INDENT "%s:%d",
      buffer_size_ - buffer_offset_, data_length, __FILE__, __LINE__);

    // Reading data from buffer to lgi stream
    if constexpr (std::is_base_of<LGI_STREAM, T>::value)
      lgi_write(out, buffer_ + buffer_offset_, data_length);
    // Reading data from buffer to raw pointer
    else if constexpr (std::is_pointer<T>::value)
      memcpy(out, buffer_ + buffer_offset_, data_length);
    else
      static_assert(std::is_base_of<LGI_STREAM, T>::value | std::is_pointer<T>::value);

    // Update pointer to unused data in the buffer
    buffer_offset_ += data_length;
  }

  template <typename T>
  void read(T * const out)
  {
    this->read(out, sizeof(T));
  }

  /**
   * Skip a specific amount of data
   * @param data_length Number of bytes to be skipped
   */
  void skip(size_t data_length)
  {
    buffer_offset_ += data_length;
  }

   /**
   * Reads data from file to buffer, keeping the unused tail of data located at the end of
   * the buffer and moving it to the beginning. The raw data is load from this point in advance
   * until filling the buffer or reaching the end of the file.
   */
  void load_raw_data_from_file()
  {
    // Moving tail of unused data to the beginning of the buffer
    {
      // It is obtained the amount of raw memory of unused data
      size_t unused_data_size = buffer_size_ - buffer_offset_;
      // This amount of data is moved from the end of the buffer to the beginning
      memcpy(buffer_, buffer_ + buffer_offset_, unused_data_size);
      // The new size of the buffer is the extension of the unused data
      buffer_size_ = unused_data_size;
      // The offset is reset to point to the beginning of the buffer
      buffer_offset_ = 0ul;
    }

    // Read data from file
    {
      // It is obtained the free unused space located at the end of the buffer
      hsize_t data_size_to_read = buffer_capacity_ - buffer_size_;
      // Reading data from the file
      // const size_t read_data_size = fread(buffer_ + buffer_offset_, 1, data_size_to_read, fid);
      data_size_to_read = load_data(buffer_ + buffer_size_, data_size_to_read);
      // The size of the buffer is enlarge with the amount of read data
      buffer_size_ += data_size_to_read;
    }
  }

  protected:

  int last_group_idx() const
  {
    for (int nn = MAX_DEPTH_GROUP - 1; nn >= 0; --nn)
      if (groups_[nn] != H5I_INVALID_HID)
        return nn;
    return -1;
  }

  template <typename... Ts> struct arg_counter;

  template <typename T0, typename... Ts>
  static typename std::enable_if<std::is_convertible<T0, std::string>::value, void>::type
    open_groups(sCP_DGF_READER::reader_t rm,
                hid_t parent,
                std::string* const names,
                hid_t * const groups,
                const T0& group0_name,
                const Ts&... other_group_names)
  {
    if constexpr (std::is_same<T0, std::string>::value)
    {
      static_assert(arg_counter<T0, Ts...>::value <= MAX_DEPTH_GROUP);
      *names = group0_name;
      print_assert(*groups == H5I_INVALID_HID, "HDF5 group %s already initialized.", names[0].c_str());
      print_mssg_in_debug<message_type::INFO>(rm, "Opening group %s", names[0].c_str());
      std::stringstream errorMssg;
      errorMssg << "Error while trying to open group \"" << group0_name << "\"";
      *groups = H5Gopen2_e(errorMssg.str().c_str(), parent, group0_name.c_str(), H5P_DEFAULT);
      print_assert(*groups != H5I_INVALID_HID, "HDF5 data set already initialized.");
      if constexpr (arg_counter<Ts...>::value != 0)
        open_groups<Ts...>(rm, *groups, names + 1, groups + 1, other_group_names...);
    }
    else
    {
      open_groups<std::string, Ts...>(rm, parent, names, groups, std::string(group0_name), other_group_names...);
    }
  }
};

template <> struct hdf5_shob::arg_counter<> {static constexpr int value = 0;};
template <typename T0, typename... Ts> struct hdf5_shob::arg_counter<T0, Ts...> {static constexpr int value = 1 + arg_counter<Ts...>::value;};

struct hdf5_reader
{
  ~hdf5_reader()
  {
    this->close();
  }

  void open_file(const std::string& file_name, sCP_DGF_READER::reader_t rt)
  {
    reader_mode_ = rt;
    // Open file
    print_mssg_in_debug<message_type::INFO>(rt, "Opening HDF5 file %s", file_name.c_str());
    print_assert(file_ == H5I_INVALID_HID, "HDF5 file already opened");
    file_ = H5Fopen_e("[JGA3] Error when trying to open hdf5 file",
                      file_name.c_str(), H5F_ACC_RDONLY, H5P_DEFAULT);
  }

  hdf5_shob open_shob(const std::string& shob_name, sCP_DGF_READER::reader_t rt)
  {
    return {rt, file_, shob_name};
  }

  hdf5_shob open_shob(const std::string& file_name, const std::string& shob_name, sCP_DGF_READER::reader_t rt)
  {
    if (file_ == H5I_INVALID_HID)
      this->open_file(file_name, rt);
    return open_shob(shob_name, rt);
  }

  void add_shob_ref_to_collection(const std::string & shob_name, hdf5_shob * const shob)
  {
    shob_collection_.insert(std::pair{shob_name, shob});
  }

  void rm_shob_ref_of_collection(const std::string & shob_name)
  {
    shob_collection_.erase(shob_name);
  }

  hdf5_shob& operator[](const std::string & shob_name)
  {
    return *(shob_collection_.at(shob_name));
  }

  hdf5_shob& operator[](const char * shob_name)
  {
    return this->operator[](std::string(shob_name));
  }

  void close()
  {
    if (file_ != H5I_INVALID_HID)
    {
      H5Fclose(file_);
      file_ = H5I_INVALID_HID;
      print_mssg_in_debug<message_type::INFO>(reader_mode_, "Closing HDF5 file");
    }
  }

  hid_t file_ = H5I_INVALID_HID;
  std::map<std::string, hdf5_shob * const> shob_collection_;
  sCP_DGF_READER::reader_t reader_mode_ = sCP_DGF_READER::UNKNOWN;
} g_hdf5_reader;


template <sCP_DGF_READER::reader_t RT, bool IS_HDF5_REF = false> struct LocalReader;

template <bool IS_HDF5_REF>
struct LocalReader<sCP_DGF_READER::LEGACY, IS_HDF5_REF>
{
  LocalReader(LocalReader&) = delete;
  LocalReader(LGI_STREAM& s) : stream(s) {}
  template <typename T> void read(T& __restrict in, const char*, const char*, int) {lgi_read(stream, in);}
  template <typename T> void read(T* __restrict const in, size_t size, const char*, const char*, int) {lgi_read(stream, in, size);}
  template <typename T> void read_hdf5(T& __restrict) {}
  template <typename T> void read_hdf5(T* __restrict const, size_t) {}
  template <typename T> void read_lgi(T& __restrict in) {lgi_read(stream, in);}
  template <typename T> void read_lgi(T* __restrict const in, size_t size) {lgi_read(stream, in, size);}
  template <typename T> static void compare(T, T, const char*, const char*, int) {}
  template <typename T> static void print_info(T) {}
  STRING read_string(size_t n_chars, const char*, const char*, int)
  {
    STRING s = xnew char[n_chars];
    lgi_read(stream, s, n_chars);
    return s;
  }

  LGI_STREAM& stream;
};

template <>
struct LocalReader<sCP_DGF_READER::HDF5_SERIAL, true>
{
  LocalReader(LocalReader&) = delete;

  LocalReader(hdf5_shob * const extern_shob) : shob_ptr(extern_shob) {}

  template <typename T> void read(T& __restrict in, const char*, const char*, int){shob_ptr->read(&in);}
  template <typename T> void read(T * __restrict const in, size_t size, const char*, const char*, int) {shob_ptr->read(in, size);}
  template <typename T> void read_hdf5(T& __restrict in) {shob_ptr->read(&in);}
  template <typename T> void read_hdf5(T* __restrict const in, size_t size) {shob_ptr->read(in, size);}
  template <typename T> void read_lgi(T& __restrict) {}
  template <typename T> void read_lgi(T* __restrict const, size_t) {}
  template <typename T> static void compare(T, T, const char*, const char*, int) {}
  template <typename T> static void print_info(T) {}
  STRING read_string(size_t n_chars, const char*, const char*, int)
  {
    STRING s = xnew char[n_chars];
    shob_ptr->read(s, n_chars);
    return s;
  }

  hdf5_shob * const shob_ptr;
};

template <>
struct LocalReader<sCP_DGF_READER::HDF5_SERIAL, false> : public LocalReader<sCP_DGF_READER::HDF5_SERIAL, true>
{
  LocalReader(LocalReader&) = delete;

  LocalReader(const char * const hdf5_file_name,
              const char * const hdf5_group_name) :
              shob(g_hdf5_reader.open_shob(hdf5_file_name, hdf5_group_name, sCP_DGF_READER::HDF5_SERIAL)),
              LocalReader<sCP_DGF_READER::HDF5_SERIAL, true>(&shob) {}
  
  ~LocalReader() {shob.close();}

  hdf5_shob shob;
};

template <>
struct LocalReader<sCP_DGF_READER::DEBUG_MODE, true>
{
  LocalReader(LocalReader&) = delete;

  LocalReader(LGI_STREAM& __restrict s, hdf5_shob * const extern_shob) :
    stream(s), shob_ptr(extern_shob) {}
  
  template <typename T>
  void read(T* __restrict const in, size_t size, const char * const v, const char * const f, int l)
  {
    // Read from HDF5 file
    shob_ptr->read(in, size);

    // Read from legacy
    uint8_t * __restrict const in_raw = (uint8_t*) in;
    uint8_t * __restrict const in_legacy = new uint8_t[size];

    // Read data from legacy
    lgi_read(stream, in_legacy, size);

    // Check if both data legacy and hdf5 are the same
    this->compare(in_raw, in_legacy, size, v, f, l);

    // Free memory
    delete [] in_legacy;
  }

  template <typename T>
  void read(T& __restrict in, const char * const v, const char * const f, int l)
  {
    if constexpr (std::is_convertible<T, int>::value)
    {
      // Read from HDF5 file
      shob_ptr->read(&in);
      // Read from legacy
      T in_legacy;
      lgi_read(stream, in_legacy);
      // Convert to printable output
      this->compare<T>(in, in_legacy, v, f, l);
    }
    else
    {
      read<T>(&in, sizeof(T), v, f, l);
    }
  }

  /**
   * Compares raw data and prints an error message if they are not equal. An error message is
   * shown if variables are different. Assert if variables are different.
   * @tparam IS_ASSERT_ACTIVE. True to abort simulation if values are not equal. Default: true.
   * @param[in] in0 Pointer to the  first array to be compared
   * @param[in] in1 Pointer to the second array to be compared
   * @param[in] size Number of bytes to compare
   * @param[in] v Name of the variable
   * @param[in] f Name of the file where this function is call. Use __FILE__.
   * @param[in] l Line of the file where this function is call. Use __LINE__.
   */
  template <bool IS_ASSERT_ACTIVE = true>
  static void compare(uint8_t * __restrict const in0,
                      uint8_t * __restrict const in1,
                      size_t size,
                      const char * const v,
                      const char * const f,
                      int l)
  {
    uint8_t cum_or = 0;
    for (size_t nn = 0; nn < size; ++nn)
      cum_or |= in0[nn] ^ in1[nn];
    
    // Print message
    if (cum_or != 0)
      print_mssg<message_type::ERROR>("Error when reading array %s.\n%s:%i", v, f, l);

    // Assert if error
    if constexpr (IS_ASSERT_ACTIVE)
      assert(cum_or == 0);
  }

  /**
   * Compares variables and prints an error message if they are not equal. An error message is
   * shown if variables are different, success message otherwise (only if variable is convertible
   * to integer values). Assert if variables are different.
   * @tparam T Types of variables to be compared
   * @tparam IS_ASSERT_ACTIVE. True to abort simulation if values are not equal. Default: true.
   * @param[in] in0 Fist variable to be compared
   * @param[in] in1 Second variable to be compared
   * @param[in] v Name of the variable
   * @param[in] f Name of the file where this function is call. Use __FILE__.
   * @param[in] l Line of the file where this function is call. Use __LINE__.
   */
  template <typename T, bool IS_ASSERT_ACTIVE = true>
  static void compare(T in0, T in1, const char * const v, const char * const f, int l)
  {
    if constexpr (std::is_convertible<T, int>::value)
    {
      using print_val_t = typename std::conditional<std::is_integral<T>::value && sizeof(T) == 1, int, T>::type;
      // Print message
      if (in0 == in1)
      {
        std::stringstream in2str;
        in2str << (print_val_t) in0;
        print_mssg<message_type::SUCCESS>("Read variable %s: %s", v, in2str.str().c_str());
      }
      else
      {
        std::stringstream in2str0, in2str1;
        in2str0 << (print_val_t) in0;
        in2str1 << (print_val_t) in1;

        if constexpr (std::is_floating_point<T>::value)
        {
          // Numerical error shown as warning (threshold around 1e6 times the minimum non-denorm value)
          constexpr T warning_threshold = std::numeric_limits<T>::min() * (1 << 20);
          if (std::fabs(in0 - in1) < warning_threshold)
          {
            print_mssg<message_type::WARNING>("Different values when reading variable %s: %s vs %s.\n%s:%i",
              v, in2str0.str().c_str(), in2str1.str().c_str(), f, l);
            return;
          }
        }

        print_mssg<message_type::ERROR>("Error when reading variable %s: %s vs %s.\n%s:%i",
          v, in2str0.str().c_str(), in2str1.str().c_str(), f, l);

        // Assert if error
        if constexpr (IS_ASSERT_ACTIVE)
          assert(false);
      }
    }
    else
    {
      compare<IS_ASSERT_ACTIVE>((uint8_t*) &in0, (uint8_t*) &in1, sizeof(T), v, f, l);
    }
  }

  template <typename T>
  static void compare_no_assert(T in0, T in1, const char * const v, const char * const f, int l)
  {
    compare<T, false>(in0, in1, v, f, l);
  }

  template <typename T>
  static void print_info(T in)
  {
    using print_val_t = typename std::conditional<std::is_integral<T>::value && sizeof(T) == 1, int, T>::type;
    std::stringstream in2str;
    in2str << (print_val_t) in;
    print_mssg<message_type::INFO>("%s", in2str.str().c_str());
  }

  template <typename T> void read_hdf5(T& __restrict in) {shob_ptr->read(&in);}
  template <typename T> void read_hdf5(T* __restrict const in, size_t size) {shob_ptr->read(in, size);}
  template <typename T> void read_lgi(T& __restrict in) {lgi_read(stream, in);}
  template <typename T> void read_lgi(T* __restrict const in, size_t size) {lgi_read(stream, in, size);}
  STRING read_string(size_t n_chars, const char* v, const char* f, int l)
  {
    STRING s = xnew char[n_chars];
    read(s, n_chars, v, f, l);
    return s;
  }
  
  LGI_STREAM& stream;
  hdf5_shob * const shob_ptr;
};

template <>
struct LocalReader<sCP_DGF_READER::DEBUG_MODE, false> : public LocalReader<sCP_DGF_READER::DEBUG_MODE, true>
{
  LocalReader(LocalReader&) = delete;

  LocalReader(LGI_STREAM& __restrict s,
              const char * const hdf5_file_name,
              const char * const hdf5_group_name) :
              shob(g_hdf5_reader.open_shob(hdf5_file_name, hdf5_group_name, sCP_DGF_READER::DEBUG_MODE)),
              LocalReader<sCP_DGF_READER::DEBUG_MODE, true>(s, &shob) {}

  ~LocalReader() {shob.close();}

  hdf5_shob shob;
};

template <sCP_DGF_READER::reader_t RT>
LocalReader<RT, false> initLocalReader(LGI_STREAM& __restrict lgi_stream,
                                       const char * const hdf5_file_name,
                                       const char * const hdf5_group_name)
{
  // Returning parameters for constructor
  if constexpr (RT == sCP_DGF_READER::LEGACY)
    return {lgi_stream};
  else if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL)
    return {hdf5_file_name, hdf5_group_name};
  else if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
  {
    // Modify extension to read from .h5 file
    std::string complete_file_name(hdf5_file_name);
    {
      const size_t found_pos = complete_file_name.rfind(".ckpt");
      print_assert(found_pos != std::string::npos,
        "Different file extensions than ckpt not allowed for debugging");
      complete_file_name.replace(found_pos, 5, ".h5");
    }
    return {lgi_stream, complete_file_name.c_str(), hdf5_group_name};
  }
  else
    assert(false);
}

template <sCP_DGF_READER::reader_t RT>
LocalReader<RT, true> initLocalReader(LGI_STREAM& __restrict lgi_stream,
                                      const char * const shob_name)
{
  if constexpr (RT == sCP_DGF_READER::LEGACY)
    return {lgi_stream};
  else if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL)
    return {&g_hdf5_reader[shob_name]};
  else if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
    return {lgi_stream, &g_hdf5_reader[shob_name]};
  else
    assert(false);
}

#define READ_GENERIC_VAR_FROM_FILE(     VARNAME      ) reader. read(VARNAME,       #VARNAME, __FILE__, __LINE__);
#define READ_GENERIC_ARRAY_FROM_FILE(   VARNAME, SIZE) reader. read(VARNAME, SIZE, #VARNAME, __FILE__, __LINE__);
#define READPTR_GENERIC_VAR_FROM_FILE(  VARNAME      ) reader->read(VARNAME,       #VARNAME, __FILE__, __LINE__);
#define READPTR_GENERIC_ARRAY_FROM_FILE(VARNAME, SIZE) reader->read(VARNAME, SIZE, #VARNAME, __FILE__, __LINE__);
#define READ_GENERIC_STRING_FROM_FILE(VARNAME, SIZE) reader.read_string(SIZE, #VARNAME, __FILE__, __LINE__);
#define READ_DUMMY_VAR_HDF5(TYPE) {TYPE dummy_var; reader.read_hdf5(dummy_var);}
#define READ_DUMMY_VAR_LEGACY(TYPE) {TYPE dummy_var; reader.read_lgi(dummy_var);}
#define TYPE_OR_REF_IF_DEBUG(TYPE) typename std::conditional<RT == sCP_DGF_READER::DEBUG_MODE, TYPE, TYPE&>::type

// Struct where temporally store the data coming from "SPs Light data"
struct LightData
{
  friend struct LightDataCollection;

  LightData(hdf5_shob& shob) : 
    size(data_size(shob)),
    raw_data(new uint8_t[size])
  {
    // Read all the data at once
    shob.read(raw_data, size);
    // Compute pointers to each piece of data
    size_t cum_offset = 0;
    rand_state_          = append_record(cum_offset);
    eqn_rand_state_      = append_record(cum_offset);
    turb_synth_          = append_record(cum_offset);
    grf_state_           = append_record(cum_offset);
    lrf_state_           = append_record(cum_offset);
    movb_state_          = append_record(cum_offset);
    thermal_accel_state_ = append_record(cum_offset);
    fan_descs_           = append_record(cum_offset);
    particle_sim_        = append_record(cum_offset);
  }

  LightData(LightData&) = delete;   // Deleted copy operator
  LightData(LightData&&) = default; // Move operator default
  ~LightData() {delete [] raw_data;}

  #define GET_LIGHT_DATA_REF(VAR_NAME)                                              \
  uint8_t* VAR_NAME ##  raw() const {return VAR_NAME;}                              \
  uint8_t* VAR_NAME ## data() const {return VAR_NAME + sizeof(size_t);}             \
  size_t   VAR_NAME ## size() const {return *((size_t*)VAR_NAME) - sizeof(size_t);} \
  void     VAR_NAME ## read(LGI_STREAM stream) const                                \
    { std::cout << #VAR_NAME << " SIZE: " << VAR_NAME ## size() << std::endl; \
      lgi_write(stream, VAR_NAME ## data(), VAR_NAME ## size());}
  
  GET_LIGHT_DATA_REF(rand_state_         )
  GET_LIGHT_DATA_REF(eqn_rand_state_     )
  GET_LIGHT_DATA_REF(turb_synth_         )
  GET_LIGHT_DATA_REF(grf_state_          )
  GET_LIGHT_DATA_REF(lrf_state_          )
  GET_LIGHT_DATA_REF(movb_state_         )
  GET_LIGHT_DATA_REF(thermal_accel_state_)
  GET_LIGHT_DATA_REF(fan_descs_          )
  GET_LIGHT_DATA_REF(particle_sim_       )
  #undef GET_LIGHT_DATA_REF
  
protected:

  static size_t data_size(hdf5_shob& shob)
  {
    // Read size of all the light data
    size_t record_size;
    shob.read(&record_size);
    return record_size - sizeof(size_t);
  }

  uint8_t* append_record(size_t& offset)
  {
    uint8_t * out = raw_data + offset;
    size_t record_size = *((size_t*) (raw_data + offset));
    offset += record_size;
    return out;
  }

  const size_t size;
  uint8_t * const raw_data;

  uint8_t * rand_state_;
  uint8_t * eqn_rand_state_;
  uint8_t * turb_synth_;
  uint8_t * grf_state_;
  uint8_t * lrf_state_;
  uint8_t * movb_state_;
  uint8_t * thermal_accel_state_;
  uint8_t * fan_descs_;
  uint8_t * particle_sim_;
};

struct LightDataCollection
{
  void initialize(sCP_DGF_READER::reader_t rm, const std::string& root_file_name, asINT32 total_sps_in_ckpt)
  {
    // Empty vector
    reset();
    // Create access to HDF5 file
    hdf5_shob light_data_shob(g_hdf5_reader.open_shob(root_file_name, "SPs Light data", rm));
    // For each SP in the saved file, read raw data
    for (asINT32 sp = 0; sp < total_sps_in_ckpt; ++sp)
      collection_.emplace_back(new LightData(light_data_shob));
  }

  void reset()
  {
    for (const LightData* const sp_light_data : collection_)
      delete sp_light_data;
    collection_.clear();
  }

  size_t total_sps() const {return collection_.size();}

  #define GET_LIGHT_DATA_REF(VAR_NAME)                                                      \
  uint8_t* VAR_NAME ##  raw(asINT32 sp) const {return collection_.at(sp)->VAR_NAME ##  raw();} \
  uint8_t* VAR_NAME ## data(asINT32 sp) const {return collection_.at(sp)->VAR_NAME ## data();} \
  size_t   VAR_NAME ## size(asINT32 sp) const {return collection_.at(sp)->VAR_NAME ## size();} \
  size_t   VAR_NAME ## cum_size() const                                                     \
  {                                                                                         \
    size_t cum_size = 0;                                                                    \
    for (const LightData* const sp_light_data : collection_)                                \
      cum_size += sp_light_data->VAR_NAME ## size();                                        \
    return cum_size;                                                                        \
  }
  
  GET_LIGHT_DATA_REF(rand_state_         )
  GET_LIGHT_DATA_REF(eqn_rand_state_     )
  GET_LIGHT_DATA_REF(turb_synth_         )
  GET_LIGHT_DATA_REF(grf_state_          )
  GET_LIGHT_DATA_REF(lrf_state_          )
  GET_LIGHT_DATA_REF(movb_state_         )
  GET_LIGHT_DATA_REF(thermal_accel_state_)
  GET_LIGHT_DATA_REF(fan_descs_          )
  GET_LIGHT_DATA_REF(particle_sim_       )
  #undef GET_LIGHT_DATA_REF


private:
  std::vector<LightData*> collection_;
} g_light_data_collection;

//----------------------------------------------------------------------------
// open_file
//----------------------------------------------------------------------------


typedef struct {
  asINT32       fd;
  FILE          *fp;
  VOID          *input_buffer;
  asINT32       input_buffer_size;
} sDECOMP_MEMBUF_INPUT_INFO, *DECOMP_MEMBUF_INPUT_INFO;

pid_t decomposer_child_pid = (pid_t) -1;

static DECOMP_MEMBUF_INPUT_INFO open_fcn(SIM_ARGS sim_args)
{
  DECOMP_MEMBUF_INPUT_INFO membuf_info = xnew sDECOMP_MEMBUF_INPUT_INFO;

  {
    asINT32 not_timed_out = 30;
    int sv[2];

    if (socketpair(AF_UNIX, SOCK_STREAM, 0, sv) != 0) {
      msg_error("Unable to create socket pair for preprocessor communication, errno = %d",
                errno);
      exit(exit_failure_code());
    }

    do {
      decomposer_child_pid = fork();
    } while ((decomposer_child_pid == ((pid_t) -1)) && (errno == EAGAIN) && not_timed_out--);

    if (decomposer_child_pid == (pid_t) -1) {
      msg_warn("Unable to fork a process to create input preprocessor, errno = %d",
               errno);
      exit(exit_failure_code());
    };

#define MAX_DECOMP_ARG_LENGTH 1024
#define MAX_CP_DECOMP_ARGS 20

    if (decomposer_child_pid == (pid_t) 0) {
      /* If we're the child... */
      CHARACTER decomp_args[MAX_CP_DECOMP_ARGS + 1][MAX_DECOMP_ARG_LENGTH];
      asINT32 decomp_argc = 0;
      CHARACTER *decomp_argv[MAX_CP_DECOMP_ARGS + 1];

      sprintf(decomp_args[decomp_argc++], "%s", sim_args->decomp_file);
      sprintf(decomp_args[decomp_argc++], "-n_processors");
      sprintf(decomp_args[decomp_argc++], "%s", sim_args->n_processors);
      //      sprintf(decomp_args[decomp_argc++], "-pmodel_file");
      //      sprintf(decomp_args[decomp_argc++], "%s", sim_args->pmodel_file);
      if (sim_args->target_platform) {
        sprintf(decomp_args[decomp_argc++], "-target_platform");
        sprintf(decomp_args[decomp_argc++], "%s", sim_args->target_platform);
      }

      if(cp_info.is_sim_double_precision) {
        sprintf(decomp_args[decomp_argc++], "-double_precision");
      } else {
        sprintf(decomp_args[decomp_argc++], "-single_precision");
      }

      if(sim_args->freeze_momentum_field) {
        sprintf(decomp_args[decomp_argc++], "-freeze_momentum_field");
      }

      if(sim_args->disable_particle_modeling) {
        sprintf(decomp_args[decomp_argc++], "-disable_particle_modeling");
      }

      sprintf(decomp_args[decomp_argc++], "-cdi_file");
      sprintf(decomp_args[decomp_argc++], "%s", sim_args->cdi_filename);
      sprintf(decomp_args[decomp_argc++], "-output_via_fd");
      sprintf(decomp_args[decomp_argc++], "%d", sv[0]);
      sprintf(decomp_args[decomp_argc++], "%s", cp_info.root_filename);

      ccDOTIMES(i, decomp_argc) {
        decomp_argv[i] = &decomp_args[i][0];
      }
      decomp_argv[decomp_argc] = (char *) NULL;

      // Issue an error if we walked off the end of the decomp_args array.
      if (decomp_argc >= MAX_CP_DECOMP_ARGS)
        msg_internal_error("Too many arguments passed to decomposer");

      if (sim_args->decomp_message_log) {
        // Rename if existing, then reopen log file on stdout handle
        cEXA_FILE *efp = cEXA_FILE::OpenLogSavingExisting(sim_args->decomp_message_log, stdout);

        if (efp) {
          dup2(1, 2);   /* Merge stdout and stderr */
        }
      }

      execvp(sim_args->decomp_file, decomp_argv);

      /* DEATH IF HERE */
      msg_internal_error("Failed to exec decomposer \"%s\".\n"
                         "System error message was: %s",
                         sim_args->decomp_file,
                         strerror(errno));
      exit(exit_failure_code());
    }

    membuf_info->fp = fdopen(sv[1], "r");
    /* Open a stream upon the fd */
  }

  membuf_info->input_buffer_size = (1024 * 64);
  membuf_info->input_buffer = xnew char[membuf_info->input_buffer_size];

  return(membuf_info);
}

/*
 *  next_fcn should return the next block of data by returning a pointer to
 *  a buffer of data and setting the contents of length_ptr to the buffer's length.
 *  If the returned pointer is NULL or if the length is 0, EOF is assumed.
 */
static VOID *next_fcn(VOID *membuf_info, asINT32 *length_ptr) {
  DECOMP_MEMBUF_INPUT_INFO info = (DECOMP_MEMBUF_INPUT_INFO) membuf_info;
  uINT32 packet_len = 0;

  while (packet_len == 0) {
    fread(&packet_len, sizeof(uINT32), 1, info->fp);

    if (packet_len > 0) {
      *length_ptr = (asINT32) fread(info->input_buffer, 1, packet_len, info->fp);
    /* Read contents of the buffer */
    } else {
      *length_ptr = 0;
    }
  }

  return(info->input_buffer);   /* Let LGI start at the beginning of the available buffer */
}


/*
 *  close_fcn is called when the stream is being closed.  The length
 *  argument will be equal to the number of bytes left unread from the last
 *  buffer requested via next_fcn.
 */
static VOID close_fcn(VOID *membuf_info, asINT32 length) {
  DECOMP_MEMBUF_INPUT_INFO info = (DECOMP_MEMBUF_INPUT_INFO) membuf_info;

  fclose(info->fp);             /* Close things out */
  delete[] (char*) info->input_buffer;  /* Release the buffer */
  delete info;                  /* Release the info struct itself */
}

VOID sCP_DGF_READER::open_inline_decomposer_stream()
{
  DECOMP_MEMBUF_INPUT_INFO membuf_info = open_fcn(&sim_args);
  m_decomp_istream = lgi_open_input_stream(LGI_MEMBUF_TYPE,
                                           next_fcn,
                                           close_fcn,
                                           membuf_info);
}

VOID sCP_DGF_READER::open_file_streams(bool is_legacy)
{
  m_main_istream  = lgi_open_input_stream(LGI_FILE_TYPE, cp_info.lgi_filename);
  m_ublk_table_istream = lgi_open_input_stream(LGI_FILE_TYPE, cp_info.lgi_filename);
  // Later m_decomp_istream may be connected to the inline decomposer
  m_decomp_istream = lgi_open_input_stream(LGI_FILE_TYPE, cp_info.lgi_filename);

  if (m_main_istream == NULL || m_ublk_table_istream == NULL || m_decomp_istream == NULL) {
    msg_error("Could not open LGI file \"%s\"", cp_info.lgi_filename);
  }

  read_file_header();

  if (cp_info.is_full_checkpoint_restore && is_legacy) {
    m_ckpt_istream = lgi_open_input_stream(LGI_FILE_TYPE, sim_args.resume_filename);
    if (m_ckpt_istream == NULL)
      msg_error("Could not open checkpoint file \"%s\"", sim_args.resume_filename);

    read_ckpt_lgi_header_verify_completeness(m_ckpt_istream, sim_args.resume_filename);
  }

}

//----------------------------------------------------------------------------
// close_file
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::close_file_streams(bool is_legacy)
{
  lgi_close_stream(m_main_istream);
  lgi_close_stream(m_ublk_table_istream);
  lgi_close_stream(m_decomp_istream);

  if (cp_info.is_full_checkpoint_restore && is_legacy)
    lgi_close_stream(m_ckpt_istream);

  // Jettison file asap to save space for measurements
  if (sim_args.unlink_input_after_init) unlink(cp_info.lgi_filename);

  // Close HDF5 file
  g_hdf5_reader.close();
  
  if (total_rps > 0) {
    m_rad_file.close_file();
  }
}

//----------------------------------------------------------------------------
// seek_lgi_tag
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::seek_lgi_tag(LGI_TAG_ID target_tag_id)
{
  LGI_TAG tag;
  while(1) {
    tag = lgi_peek_tag(m_main_istream);
    if (tag.id == target_tag_id || tag.id == LGI_EOF_TAG)
      break;
    else
      lgi_skip_record(m_main_istream);
  }

  if (tag.id != target_tag_id) {
    msg_error("Could not find LGI tag %s", lgi_tag_namestring(target_tag_id));
  }
}

VOID sCP_DGF_READER::seek_ckpt_tag(LGI_TAG_ID target_tag_id)
{
  LGI_TAG tag;
  while(1) {
    tag = lgi_peek_tag(m_ckpt_istream);
    if (tag.id == target_tag_id || tag.id == LGI_EOF_TAG)
      break;
    else
      lgi_skip_record(m_ckpt_istream);
  }

  if (tag.id != target_tag_id) {
    msg_error("Could not find LGI tag %s", lgi_tag_namestring(target_tag_id));
  }
}

//----------------------------------------------------------------------------
// jump_to_file_position
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::jump_to_file_position(sLGI_POS& position)
{
  lgi_setpos(m_main_istream, &position);
}

VOID sCP_DGF_READER::jump_to_ckpt_file_position(sLGI_POS& position)
{
  lgi_setpos(m_ckpt_istream, &position);
}

//----------------------------------------------------------------------------
// read_file_header
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::read_file_header()
{
  BOOLEAN header_is_complete     = FALSE;
  BOOLEAN allow_wrong_endianness = TRUE;
  m_version_number = lgi_read_header_and_completeness(m_main_istream, allow_wrong_endianness, &header_is_complete);
  report_header_errors(header_is_complete);

  ccDOTIMES(i, total_sps) {
    lgi_write_header(g_sp_streams[i], LGI_DGF_VERSION);
  }

  lgi_getpos(m_main_istream, &m_beginning_of_file);
}

//----------------------------------------------------------------------------
// report_header_errors
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::report_header_errors(BOOLEAN header_is_complete)
{
  asINT32 lgi_errno = lgi_stream_errno(m_main_istream);

  // Report mismatched LGI version number
  if (m_version_number > LGI_DGF_VERSION)
    msg_error("The version of the LGI file being read (%u) "
              "is newer than the version supported by this software (%u)",
              (unsigned) m_version_number, (unsigned) LGI_DGF_VERSION);

  if (m_version_number < LGI_DGF_VERSION)
    msg_error("The version of the LGI file being read (%u) "
              "is older than the version supported by this software (%u)",
              (unsigned) m_version_number, (unsigned) LGI_DGF_VERSION);

  // Report incorrect bit-order
  if (lgi_errno == LGI_ERRNO_ENDERR)
    {
      cSTRING endian = SCALAR_BIG_ENDIAN ? "big" : "little";
      cSTRING other =  SCALAR_BIG_ENDIAN ? "little" : "big";

      msg_error("The LGI file \"%s\" was created on a machine with a different byte ordering."
                " This is a %s endian machine. The LGI file was created on a %s endian machine."
                " To simulate on this machine, you must discretize the case on a %s endian"
                " machine. Alternatively, you can simulate on %s endian machine.",
                cp_info.lgi_filename, endian, other, endian, other);
    }

  // Report missing header
  if (lgi_errno == LGI_ERRNO_NOHDR)
    msg_error("The LGI file \"%s\" has no header."
              "The file may have been damaged, or is not an LGI file.",
              cp_info.lgi_filename);

  // Report corrupt header
  if (lgi_errno == LGI_ERRNO_CORHDR)
    msg_error("The LGI file \"%s\" has a corrupt header."
              "The file may have been damaged, or is not an LGI file.",
              cp_info.lgi_filename);

  //  Treat all other errors as garbled .lgi file
  if (!header_is_complete)
    msg_error("The LGI file %s is not complete."
              " The likely cause is that ExaDISC or ExaDECOMP terminated prematurely,"
              " possibly because you ran out of disk space."
              " Please check the discretizer and decomposer output files, and try again.",
              cp_info.lgi_filename);
}

static sSTP_LATTICE_TYPE_TABLE_ENTRY lattice_type_table[] = STP_LATTICE_TYPE_TABLE();

static cSTRING find_lattice_type_public_name(STP_LATTICE_TYPE lat_type)
{
  ccDOTIMES(i, STP_N_LATTICE_TYPES) {
    if (lattice_type_table[i].type == lat_type)
      return lattice_type_table[i].public_name;
  }
  return "Unknown";
}

template <sCP_DGF_READER::reader_t RT>
asINT32 sCP_DGF_READER::read_ckpt_info_record()
{
  // Creating the reader
  LocalReader<RT, false> reader(initLocalReader<RT>(m_ckpt_istream,
                                                    sim_args.resume_filename,
                                                    "Checkpoint info"));
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.ckpt_info_pos);
    verify_next_tag_id(LGI_CHECKPOINT_INFO_TAG, m_ckpt_istream);
  }
  // Record size (only in HDF5 file)
  {
    size_t record_size;
    reader.read_hdf5(record_size);
  }

  LGI_CHECKPOINT_INFO_REC record;
  if constexpr (RT == sCP_DGF_READER::LEGACY)
    record.read(m_ckpt_istream);
  else
  {
    reader.read_hdf5(record);    
    if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
    {
      // Load legacy to compare with
      LGI_CHECKPOINT_INFO_REC record2;
      record2.read(m_ckpt_istream);

      #define COMPARE_MEMBER(VAR) LocalReader<RT, false>::compare\
        (record.VAR, record2.VAR, "record." #VAR, __FILE__, __LINE__)
      COMPARE_MEMBER(time);
      COMPARE_MEMBER(n_sps);
      COMPARE_MEMBER(version);
      COMPARE_MEMBER(lattice_type);
      COMPARE_MEMBER(is_big_endian);
      COMPARE_MEMBER(is_double_precision);
      COMPARE_MEMBER(n_lb_base_steps);
      COMPARE_MEMBER(n_t_base_steps);
      COMPARE_MEMBER(n_ke_base_steps);
      COMPARE_MEMBER(n_scalar_base_steps);
      COMPARE_MEMBER(n_particle_base_steps);
      COMPARE_MEMBER(acous_start_time);
      COMPARE_MEMBER(freeze_momentum_field);
      COMPARE_MEMBER(thermal_timestep_ratio);
      COMPARE_MEMBER(momentum_freeze_start_time);
      COMPARE_MEMBER(avg_mme_ckpt);
      COMPARE_MEMBER(local_vel_freeze);
      COMPARE_MEMBER(calibration_iterations);
      COMPARE_MEMBER(is_large_pore);
      #undef COMPARE_MEMBER
    }
  }

  cp_info.time = record.time;   //in user's timestep
  cp_info.restart_base_time = cp_info.time * cp_info.n_user_base_steps;

  cp_info.restart_time = record.time;
  cp_info.restart_time_flow = cp_info.convert_to_ts_flow(record.time);
  cp_info.restart_time_cond = cp_info.convert_to_ts_cond(record.time);

  //params for momentum_freeze solver
  cp_info.freeze_momentum_field      = record.freeze_momentum_field;
  cp_info.thermal_timestep_ratio     = record.thermal_timestep_ratio;
  cp_info.momentum_freeze_start_time = record.momentum_freeze_start_time;
  cp_info.full_ckpt_with_avg_mme     = record.avg_mme_ckpt;
  cp_info.full_ckpt_with_frozen_vars = (record.avg_mme_ckpt || record.local_vel_freeze);
  
  //params for large pore solver
  cp_info.is_large_pore_sim      = record.is_large_pore;
  
  if(record.calibration_iterations > 0) {
    TIMESTEP coarsest_timestep = 1 << (cp_info.num_scales - 1);
    if(cp_info.end_time % coarsest_timestep != 0) {
      cp_info.end_time = coarsest_timestep * (cp_info.end_time / coarsest_timestep + 1);
      msg_warn("End time will be adjusted to the nearest multiple of coarsest timestep %d", cp_info.end_time);
    }
    cp_info.end_time *= record.calibration_iterations;
  }

  //params for local vel freeze solver
  if (cp_info.local_vel_freeze != record.local_vel_freeze) {
    if (cp_info.local_vel_freeze)
      msg_error("Checkpoint file \"%s\" was generated from a simulation without option -local_vel_freeze. It cannot be used to resume a simulation with option -local_vel_freeze.", sim_args.resume_filename);
    else
      msg_error("Checkpoint file \"%s\" was generated from a simulation with option -local_vel_freeze. It cannot be used to resume a simulation without option -local_vel_freeze.", sim_args.resume_filename);
  }

  if (cp_info.restart_time >= cp_info.end_time)
    msg_error("The number of timesteps specified for the resumed simulation is %d, but the checkpoint file was written at timestep %d.", cp_info.end_time, cp_info.restart_time);

  msg_print("Restoring from full checkpoint at timestep %d", record.time);

  cSTRING resume_filename = sim_args.resume_filename;

  if (record.is_big_endian != SCALAR_BIG_ENDIAN)
    msg_error("Checkpoint file \"%s\" was generated from a simulation running on"
              " a %s endian machine. It cannot be used to resume a simulation"
              " on a %s endian machine.",
              resume_filename,
              record.is_big_endian ? "big" : "little",
              SCALAR_BIG_ENDIAN ? "big" : "little");

  if (record.lattice_type != cp_info.lattice_type)
    msg_error("Checkpoint file \"%s\" was generated from a simulation using the %s lattice."
              " It cannot be used to resume a simulation using the %s lattice.",
              resume_filename,
              find_lattice_type_public_name((STP_LATTICE_TYPE)record.lattice_type),
              find_lattice_type_public_name(cp_info.lattice_type));

  if(record.n_lb_base_steps != cp_info.n_lb_base_steps)
    msg_error("Checkpoint file \"%s\" was generated from a n_lb_base_steps=%d simulation."
              " It cannot be used to resume a n_lb_base_steps=%d simulation.",
              resume_filename, record.n_lb_base_steps, cp_info.n_lb_base_steps);

  if(record.n_t_base_steps != cp_info.n_t_base_steps)
    msg_error("Checkpoint file \"%s\" was generated from a n_t_base_steps=%d simulation."
              " It cannot be used to resume a n_t_base_steps=%d simulation.",
              resume_filename, record.n_t_base_steps, cp_info.n_t_base_steps);

  if(record.n_ke_base_steps != cp_info.n_ke_base_steps)
    msg_error("Checkpoint file \"%s\" was generated from a n_ke_base_steps=%d simulation."
              " It cannot be used to resume a n_ke_base_steps=%d simulation.",
              resume_filename, record.n_ke_base_steps, cp_info.n_ke_base_steps);

  if(record.n_uds_base_steps != cp_info.n_uds_base_steps)
    msg_error("Checkpoint file \"%s\" was generated from a n_uds_base_steps=%d simulation."
              " It cannot be used to resume a n_uds_base_steps=%d simulation.",
              resume_filename, record.n_uds_base_steps, cp_info.n_uds_base_steps);

  if(record.n_conduction_base_steps != cp_info.n_conduction_base_steps)
    msg_error("Checkpoint file \"%s\" was generated from a n_conduction_base_steps=%d simulation."
              " It cannot be used to resume a n_conduction_base_steps=%d simulation.",
              resume_filename, record.n_conduction_base_steps, cp_info.n_conduction_base_steps);

  if(record.n_radiation_base_steps != cp_info.n_radiation_base_steps)
    msg_error("Checkpoint file \"%s\" was generated from a n_radiation_base_steps=%d simulation."
              " It cannot be used to resume a n_radiation_base_steps=%d simulation.",
              resume_filename, record.n_radiation_base_steps, cp_info.n_radiation_base_steps);

  if(record.n_particle_base_steps != cp_info.n_particle_base_steps)
    msg_error("Checkpoint file \"%s\" was generated from a n_particle_base_steps=%d simulation."
              " It cannot be used to resume a n_particle_base_steps=%d simulation.",
              resume_filename, record.n_particle_base_steps, cp_info.n_particle_base_steps);

  BOOLEAN not_check_lighthill_switch = (record.acous_start_time < record.time &&
                                        cp_info.acous_start_time < record.time);

  if(!not_check_lighthill_switch) {
    if(record.acous_start_time != cp_info.acous_start_time)
      msg_error("Checkpoint file \"%s\" was generated from a acous_start_time=%d simulation."
                " It cannot be used to resume a acous_start_time=%d simulation.",
                resume_filename, record.acous_start_time, cp_info.acous_start_time);

  }

  if (record.n_sps != total_sps)
    msg_print("Checkpoint file \"%s\" generated from a %d processor simulation."
              " Resuming on %d processor%s.",
              resume_filename, record.n_sps, total_sps, total_sps > 1 ? "s" : "");

  return record.n_sps;
}
template asINT32 sCP_DGF_READER::read_ckpt_info_record<sCP_DGF_READER::LEGACY>();
template asINT32 sCP_DGF_READER::read_ckpt_info_record<sCP_DGF_READER::HDF5_SERIAL>();
template asINT32 sCP_DGF_READER::read_ckpt_info_record<sCP_DGF_READER::DEBUG_MODE>();

//----------------------------------------------------------------------------
// read_audit_trail_record
//----------------------------------------------------------------------------
template <sCP_DGF_READER::reader_t RT>
static inline VOID read_audit_trail_internal(LGI_STREAM stream)
{
  // Creating the reader
  LocalReader<RT, false> reader(initLocalReader<RT>(stream, sim_args.resume_filename,
                                                    "Audit trail"));
  // Reading the tag from legacy file
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
    verify_next_tag_id(LGI_AUDIT_TRAIL_TAG, stream);
  
  // Depending on the case, the length comes from the HDF5 or from the legacy
  auto get_record_length = [&reader, &stream]()
  {
    if constexpr (RT == sCP_DGF_READER::LEGACY)
    {
      LGI_AUDIT_TRAIL_REC record;
      record.read(stream);
      return record.length;
    }
    else
    {
      size_t record_size;
      reader.read_hdf5(record_size);
      return record_size - sizeof(record_size);
    }
  };

  // Computing the length
  const auto length = get_record_length();
  // Allocate memory space where copy the read data
  STRING audit_ur = xnew char[length + 1];
  // Reading data...
  if constexpr (RT == sCP_DGF_READER::LEGACY)
    reader.read_lgi(audit_ur, length);
  else
    reader.read_hdf5(audit_ur, length);
  // Mark of end of string
  audit_ur[length] = '\0';
  // Print data (only on debug mode)
  LocalReader<RT, false>::print_info(audit_ur);

  // Only take the first audit trail we see (we'll see one from the
  // decomposition stream and a second from the undecomposed LGI).  If
  // present, we only want to look at the decomposition stream version.
  if (cp_info.audit_trail == NULL) {
    cp_info.audit_trail = audit_make(audit_ur);
  }
  delete [] audit_ur;
}

VOID sCP_DGF_READER::read_audit_trail_record()
{
  lgi_setpos(m_main_istream, &m_table_of_contents.audit_trail_pos);
  read_audit_trail_internal<sCP_DGF_READER::LEGACY>(m_main_istream);
}

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_ckpt_audit_trail_record()
{
  if constexpr (RT == sCP_DGF_READER::LEGACY)
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.audit_trail_pos);
  read_audit_trail_internal<RT>(m_ckpt_istream);
}
template VOID sCP_DGF_READER::read_ckpt_audit_trail_record<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_ckpt_audit_trail_record<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_ckpt_audit_trail_record<sCP_DGF_READER::DEBUG_MODE>();

VOID sCP_DGF_READER::read_decomp_audit_trail_record()
{
  if (lgi_stream_type(m_decomp_istream) == LGI_FILE_TYPE)
    lgi_setpos(m_decomp_istream, &m_table_of_contents.audit_trail_pos);
  read_audit_trail_internal<sCP_DGF_READER::LEGACY>(m_decomp_istream);
}

VOID sCP_DGF_READER::copy_record_to_sp(LGI_STREAM istream, LGI_TAG_ID tag_id, STP_PROC sp)
{
  LGI_TAG tag;
  lgi_read_next_head(istream, tag);
  if (tag.id != tag_id)
    msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                       lgi_tag_namestring(tag_id), tag_id,
                       lgi_tag_namestring(tag.id), tag.id);

  lgi_write_next_head(g_sp_streams[sp], tag);

  copy_record_body_to_sp(istream, tag, sp);
}

VOID sCP_DGF_READER::copy_record_body_to_sp(LGI_STREAM istream, LGI_TAG &tag, STP_PROC sp)
{
  const asINT32 COPY_BUFSIZE = 16 * 1024;
  char buf[COPY_BUFSIZE];
  FILE_OFFSET n_bytes_left = (FILE_OFFSET)tag.length * LGI_LEN_MULTIPLE;

  n_bytes_left -= sizeof(LGI_TAG);

  while (n_bytes_left > 0) {
    size_t n = MIN(n_bytes_left, sizeof(buf));
    if (lgi_read(istream, buf, n) != n)
      goto error;

    if (!lgi_write(g_sp_streams[sp], buf, n))
      goto error;
    n_bytes_left -= n;
  }

  return;

 error:
  msg_internal_error ("Failed while reading LGI record %s.", lgi_tag_namestring(tag.id));
}

template <sCP_DGF_READER::reader_t RT, bool IS_REF>
inline VOID copy_record_body_to_sp(LocalReader<RT, IS_REF>& reader, LGI_TAG &tag, STP_PROC sp)
{
  const asINT32 COPY_BUFSIZE = 16 * 1024;
  char buf[COPY_BUFSIZE];
  FILE_OFFSET n_bytes_left = (FILE_OFFSET)tag.length * LGI_LEN_MULTIPLE;

  n_bytes_left -= sizeof(LGI_TAG);

  while (n_bytes_left > 0) {
    size_t n = MIN(n_bytes_left, sizeof(buf));
    READ_GENERIC_ARRAY_FROM_FILE(buf, n);
    if (!lgi_write(g_sp_streams[sp], buf, n))
      msg_internal_error ("Failed while reading LGI record %s.", lgi_tag_namestring(tag.id));
    n_bytes_left -= n;
  }
}

VOID sCP_DGF_READER::copy_record_to_sp_range(LGI_STREAM istream, LGI_TAG_ID tag_id, Loop::cRANGE sp_range)
{
  LGI_TAG tag;
  lgi_read_next_head(istream, tag);
  if (tag.id != tag_id)
    msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                       lgi_tag_namestring(tag_id), tag_id,
                       lgi_tag_namestring(tag.id), tag.id);

  write_header_to_sp_range(tag, sp_range);

  copy_record_body_to_sp_range(istream, tag, sp_range);
}

VOID sCP_DGF_READER::copy_record_body_to_sp_range(LGI_STREAM istream, LGI_TAG &tag, Loop::cRANGE sp_range)
{
  const asINT32 COPY_BUFSIZE = 16 * 1024;
  char buf[COPY_BUFSIZE];
  FILE_OFFSET n_bytes_left = (FILE_OFFSET)tag.length * LGI_LEN_MULTIPLE;

  n_bytes_left -= sizeof(LGI_TAG);

  while (n_bytes_left > 0) {
    size_t n = MIN(n_bytes_left, sizeof(buf));
    lgi_read(istream, buf, n);

    write_to_sp_range(buf, n, sp_range);
    n_bytes_left -= n;
  }
}

VOID sCP_DGF_READER::copy_record_to_all_sps(LGI_STREAM istream, LGI_TAG_ID tag_id)
{
  LGI_TAG tag;
  lgi_read_next_head(istream, tag);
  if (tag.id != tag_id)
    msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                       lgi_tag_namestring(tag_id), tag_id,
                       lgi_tag_namestring(tag.id), tag.id);

  write_header_to_all_sps(tag);

  copy_record_body_to_all_sps(istream, tag);
}

VOID sCP_DGF_READER::copy_record_body_to_all_sps(LGI_STREAM istream, LGI_TAG &tag)
{
  const asINT32 COPY_BUFSIZE = 16 * 1024;
  char buf[COPY_BUFSIZE];
  FILE_OFFSET n_bytes_left = (FILE_OFFSET)tag.length * LGI_LEN_MULTIPLE;

  n_bytes_left -= sizeof(LGI_TAG);

  while (n_bytes_left > 0) {
    size_t n = MIN(n_bytes_left, sizeof(buf));
    lgi_read(istream, buf, n);

    write_to_all_sps(buf, n);
    n_bytes_left -= n;
  }
}

template <sCP_DGF_READER::reader_t RT, bool IS_REF>
VOID copy_record_body_to_all_sps(LocalReader<RT, IS_REF>& reader, LGI_TAG &tag)
{
  const asINT32 COPY_BUFSIZE = 16 * 1024;
  char buf[COPY_BUFSIZE];
  FILE_OFFSET n_bytes_left = (FILE_OFFSET)tag.length * LGI_LEN_MULTIPLE;

  n_bytes_left -= sizeof(LGI_TAG);

  while (n_bytes_left > 0) {
    size_t n = MIN(n_bytes_left, sizeof(buf));
    READ_GENERIC_ARRAY_FROM_FILE(buf, n);
    write_to_all_sps(buf, n);
    n_bytes_left -= n;
  }
}

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_global_nirf_ckpt_data()
{
  if (cp_info.is_global_ref_frame) {
    if constexpr (RT == sCP_DGF_READER::LEGACY)
    {
      jump_to_ckpt_file_position(m_ckpt_table_of_contents.nirf_pos);
      copy_record_to_all_sps(m_ckpt_istream, LGI_GLOBAL_NIRF_STATE_TAG);
    }
    else
    {
      constexpr asINT32 master_sp = 0;
      uint8_t * const hdf5_raw_data = g_light_data_collection.grf_state_data(master_sp);
      size_t hdf5_raw_size = g_light_data_collection.grf_state_size(master_sp);

      LGI_NIRF_STATE record;
      lgi_write_init_tag(&record, LGI_GLOBAL_NIRF_STATE_TAG, hdf5_raw_size + sizeof(LGI_NIRF_STATE));
      record.n_bytes_nirf_state = hdf5_raw_size;
      write_header_to_all_sps(record);

      write_to_all_sps(hdf5_raw_data, hdf5_raw_size);

      // Comparing for debugging
      if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
      {
        // Read data from lgi
        jump_to_ckpt_file_position(m_ckpt_table_of_contents.nirf_pos);

        // Read and compare headers
        {
          LGI_NIRF_STATE record_lgi;
          lgi_read(m_ckpt_istream, record_lgi);
          #define COMPARE_RECORD(VAR) LocalReader<RT>::compare(record.VAR , record_lgi.VAR, "GRF record." #VAR, __FILE__, __LINE__)
          COMPARE_RECORD(tag.id            );
          COMPARE_RECORD(tag.length        );
          COMPARE_RECORD(n_bytes_nirf_state);
          #undef COMPARE_RECORD
        }

        // Read and compare data: Copied from "sim.h"
        {
          struct sGRF
          {
            cBOOLEAN        is_defined;
            cBOOLEAN        is_time_varying;
            cBOOLEAN        is_rotation;
            sINT8           axis_change_mark;
            sINT8           ref_pt_vel_dir_change_mark;
            dFLOAT          ref_point                                   [3];
            dFLOAT          domega                      [STP_MAX_SCALES][3];
            dFLOAT          linear_accel                                [3];
            dFLOAT          angular_vel                                 [3];
            dFLOAT          delta_u_of_scale            [STP_MAX_SCALES][3];
            dFLOAT          mean_delta_u_of_scale       [STP_MAX_SCALES][3];
            dFLOAT          delta_u_of_scale_prior      [STP_MAX_SCALES][3];
            dFLOAT          mean_delta_u_of_scale_prior [STP_MAX_SCALES][3];
            dFLOAT          linear_accel0                               [3];
            dFLOAT          linear_accel_m1                             [3];
            dFLOAT          ref_pt_vel                                  [3];
            dFLOAT          ref_pt_vel1                                 [3];
            dFLOAT          angular_vel1                                [3];
            dFLOAT          linear_accel1                               [3];
            dFLOAT          last_axis                                   [3];
            dFLOAT          last_pt_on_axis                             [3];
            dFLOAT          last_ref_vel_dir                            [3];
            dFLOAT          angle_rotated;
            dFLOAT          quaternion                                  [4];
            dFLOAT          quaternion_inverse                          [4];
            sBG_TRANSFORM3d ground_to_global_xform;
            dFLOAT          ground_to_global_translation                [3];
          };
          
          sGRF& grf = *(sGRF*) (hdf5_raw_data);
          sGRF grf_lgi;
          lgi_read(m_ckpt_istream, grf_lgi);

          #define COMPARE_VAR(VAR) LocalReader<RT>::compare(grf.VAR, grf_lgi.VAR, "grf." #VAR, __FILE__, __LINE__);
          #define COMPARE_TRIVAR(VAR) COMPARE_VAR(VAR[0]) COMPARE_VAR(VAR[1]) COMPARE_VAR(VAR[2])
          #define COMPARE_QUATVAR(VAR) COMPARE_TRIVAR(VAR) COMPARE_VAR(VAR[3])
          #define COMPARE_STP_SCALES(VAR)                                                                         \
            for (int nn = 0; nn < STP_MAX_SCALES; ++nn)                                                           \
            {                                                                                                     \
              std::stringstream ss;                                                                               \
              ss << "grf." << #VAR << "[" << nn << "]";                                                           \
              LocalReader<RT>::compare(grf.VAR[nn][0], grf_lgi.VAR[nn][0], ss.str().c_str(), __FILE__, __LINE__); \
              LocalReader<RT>::compare(grf.VAR[nn][1], grf_lgi.VAR[nn][1], ss.str().c_str(), __FILE__, __LINE__); \
              LocalReader<RT>::compare(grf.VAR[nn][2], grf_lgi.VAR[nn][2], ss.str().c_str(), __FILE__, __LINE__); \
            }

          COMPARE_VAR(is_defined)
          COMPARE_VAR(is_time_varying)
          COMPARE_VAR(is_rotation)
          COMPARE_VAR(axis_change_mark)
          COMPARE_VAR(ref_pt_vel_dir_change_mark)
          COMPARE_TRIVAR(ref_point)
          COMPARE_STP_SCALES(domega)
          COMPARE_TRIVAR(linear_accel)
          COMPARE_TRIVAR(angular_vel)
          COMPARE_STP_SCALES(delta_u_of_scale)
          COMPARE_STP_SCALES(mean_delta_u_of_scale)
          COMPARE_STP_SCALES(delta_u_of_scale_prior)
          COMPARE_STP_SCALES(mean_delta_u_of_scale_prior)
          COMPARE_TRIVAR(linear_accel0)
          COMPARE_TRIVAR(linear_accel_m1)
          COMPARE_TRIVAR(ref_pt_vel)
          COMPARE_TRIVAR(ref_pt_vel1)
          COMPARE_TRIVAR(angular_vel1)
          COMPARE_TRIVAR(linear_accel1)
          COMPARE_TRIVAR(last_axis)
          COMPARE_TRIVAR(last_pt_on_axis)
          COMPARE_TRIVAR(last_ref_vel_dir)
          COMPARE_VAR(angle_rotated)
          COMPARE_QUATVAR(quaternion)
          COMPARE_QUATVAR(quaternion_inverse)
          COMPARE_VAR(ground_to_global_xform); // Warning: cheking byte per byte
          COMPARE_TRIVAR(ground_to_global_translation)

          #undef COMPARE_VAR
          #undef COMPARE_TRIVAR
          #undef COMPARE_QUADVAR
          #undef COMPARE_STP_SCALES
        }
      }
    }
  }
  else
    print_mssg_in_debug<RT, message_type::INFO>("There is no data for grf");
}
template VOID sCP_DGF_READER::read_global_nirf_ckpt_data<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_global_nirf_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_global_nirf_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_lrf_ckpt_data()
{
  if (cp_info.n_lrfs()) {
    if constexpr (RT == sCP_DGF_READER::LEGACY)
    {
      jump_to_ckpt_file_position(m_ckpt_table_of_contents.lrf_pos);
      LGI_TAG tag = lgi_peek_tag(m_ckpt_istream);
      if (tag.id != LGI_LRF_STATE_TAG)
        return;
      copy_record_to_all_sps(m_ckpt_istream, LGI_LRF_STATE_TAG);
    }
    else
    {
      constexpr asINT32 master_sp = 0;
      uint8_t * const hdf5_raw_data = g_light_data_collection.lrf_state_data(master_sp);
      size_t hdf5_raw_size = g_light_data_collection.lrf_state_size(master_sp);

      LGI_NIRF_STATE record;
      lgi_write_init_tag(&record, LGI_LRF_STATE_TAG, hdf5_raw_size + sizeof(LGI_NIRF_STATE));
      record.n_bytes_nirf_state = hdf5_raw_size;
      write_header_to_all_sps(record);

      write_to_all_sps(hdf5_raw_data, hdf5_raw_size);

      // Comparing for debugging
      if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
      {
        // Read and compare headers
        {
          jump_to_ckpt_file_position(m_ckpt_table_of_contents.lrf_pos);
          LGI_NIRF_STATE record_lgi;
          record_lgi.tag = lgi_peek_tag(m_ckpt_istream);
          lgi_read(m_ckpt_istream, record_lgi.n_bytes_nirf_state);
          #define COMPARE_RECORD(VAR) LocalReader<RT>::compare(record.VAR , record_lgi.VAR, "LRF record." #VAR, __FILE__, __LINE__)
          COMPARE_RECORD(tag.id            );
          COMPARE_RECORD(tag.length        );
          COMPARE_RECORD(n_bytes_nirf_state);
          #undef COMPARE_RECORD
        }

        // Compare raw data: there is no enought data to be able to understand by CP what is saved 
        uint8_t * const lgi_raw_data = new uint8_t[hdf5_raw_size];
        lgi_read(m_ckpt_istream, lgi_raw_data, hdf5_raw_size);
        uint8_t cum_or = 0;
        for (size_t nn = 0; nn < hdf5_raw_size; ++nn)
          cum_or |= lgi_raw_data[nn] ^ hdf5_raw_data[nn];
        print_assert(cum_or != 0, "Detected discrepancies in LRF data");
        delete [] lgi_raw_data;
      }
    }
  }
  else
    print_mssg_in_debug<RT, message_type::INFO>("There is no data for lrf");
}
template VOID sCP_DGF_READER::read_lrf_ckpt_data<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_lrf_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_lrf_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();

VOID sCP_DGF_READER::read_rotdyn_ckpt_data()
{
  if (cp_info.n_rotational_dynamics_descs > 0) {
    LGI_TAG tag = lgi_peek_tag(m_ckpt_istream);
    if (tag.id != LGI_ROTDYN_STATE_TAG)
      return;

    copy_record_to_all_sps(m_ckpt_istream, LGI_ROTDYN_STATE_TAG);
  }
}

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_cp_rotdyn_ckpt_data()
{
  // Creating the reader
  LocalReader<RT, false> reader(initLocalReader<RT>(m_ckpt_istream, sim_args.resume_filename,
                                                     "CP Rot dyn"));
  // Jump to file position when opening legacy files and reading the tag
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.rotdyn_pos);
    LGI_TAG tag;
    lgi_read_next_head(m_ckpt_istream, tag);
    if (tag.id != DGF_CKPT_ROTDYN_DESC_TAG)
      msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                         lgi_tag_namestring(DGF_CKPT_ROTDYN_DESC_TAG), DGF_CKPT_ROTDYN_DESC_TAG,
                         lgi_tag_namestring(tag.id), tag.id);
  }
  // Record size (only in HDF5 file)
  {
    size_t record_size;
    reader.read_hdf5(record_size);
  }

  sINT32 n_rotdyn_descs;
  READ_GENERIC_VAR_FROM_FILE(n_rotdyn_descs);
  if (n_rotdyn_descs != cp_info.n_rotational_dynamics_descs)
    msg_internal_error("Number of rotational dynamics descriptors in checkpoint file (%d) does not match number in CDI file (%d).",
                       n_rotdyn_descs, cp_info.n_rotational_dynamics_descs);

  ccDOTIMES(i, n_rotdyn_descs) {
    CP_ROTATIONAL_DYNAMICS_DESC rotdyn_desc = cp_info.rotational_dynamics_descs + i;
    DGF_CKPT_ROTDYN_DESC ckpt_rotdyn_desc;
    READ_GENERIC_VAR_FROM_FILE(ckpt_rotdyn_desc);
    rotdyn_desc->m_moment_of_inertia = ckpt_rotdyn_desc.moment_of_inertia;
    rotdyn_desc->m_angular_acceleration = ckpt_rotdyn_desc.angular_acceleration;
    rotdyn_desc->m_angular_acceleration_m1 = ckpt_rotdyn_desc.angular_acceleration_m1;
    TIMESTEP period = rotdyn_desc->m_window->cdi_meas_window->fluid_time_desc.period;
    rotdyn_desc->m_tau = ckpt_rotdyn_desc.tau;
    rotdyn_desc->m_tau_m1 = ckpt_rotdyn_desc.tau_m1;
    rotdyn_desc->m_tau_abs = ckpt_rotdyn_desc.tau_abs;
    rotdyn_desc->m_tau_abs_m1 = ckpt_rotdyn_desc.tau_abs_m1;
    rotdyn_desc->m_char_MI = ckpt_rotdyn_desc.char_MI;
    rotdyn_desc->m_omega_max = ckpt_rotdyn_desc.omega_max;
    rotdyn_desc->m_max_radius_lrf = ckpt_rotdyn_desc.max_radius_lrf;
    rotdyn_desc->m_lrf_height = ckpt_rotdyn_desc.lrf_height;
    rotdyn_desc->m_count = ckpt_rotdyn_desc.count;
    rotdyn_desc->m_omega = ckpt_rotdyn_desc.omega;
    rotdyn_desc->m_omega_next = ckpt_rotdyn_desc.omega_next;
    rotdyn_desc->m_cur_alpha_weight = ckpt_rotdyn_desc.cur_alpha_weight;
    rotdyn_desc->m_mi_decrease_factor = ckpt_rotdyn_desc.mi_decrease_factor;
    rotdyn_desc->m_initial_mi_coeff = ckpt_rotdyn_desc.initial_mi_coeff;
    rotdyn_desc->m_min_mi_ratio = ckpt_rotdyn_desc.min_mi_ratio;
  }
}
template VOID sCP_DGF_READER::read_cp_rotdyn_ckpt_data<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_cp_rotdyn_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_cp_rotdyn_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_tbs_desc_ckpt_data()
{
  // Creating the reader
  LocalReader<RT, false> reader(initLocalReader<RT>(m_ckpt_istream, sim_args.resume_filename,
                                                    "Transient seed descs"));
  // Jump to file position when opening legacy files and reading the tag
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    LGI_TAG tag;
    lgi_read_next_head(m_ckpt_istream, tag);
    if(tag.id != DGF_CKPT_TBS_DESC_TAG)
      msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                         lgi_tag_namestring(DGF_CKPT_TBS_DESC_TAG), DGF_CKPT_TBS_DESC_TAG,
                         lgi_tag_namestring(tag.id), tag.id);
  }
  // Record size (only in HDF5 file)
  {
    size_t record_size;
    reader.read_hdf5(record_size);
  }
  asINT32 n_tbs_descs;
  READ_GENERIC_VAR_FROM_FILE(n_tbs_descs);
  if(n_tbs_descs != cp_info.n_transient_seed_from_meas_descs)
    msg_internal_error("Number of transient boundary seeding descriptors in checkpoint file (%d) does not match number in CDI file (%d).",
                       n_tbs_descs, cp_info.n_transient_seed_from_meas_descs);
  ccDOTIMES(i, cp_info.n_seed_from_meas_descs) {
    if(!cp_info.seed_from_meas_descs[i]->m_transient_boundary_seeding)
      continue;
    TRANSIENT_BOUNDARY_SEEDING tbs = static_cast<TRANSIENT_BOUNDARY_SEEDING>(cp_info.seed_from_meas_descs[i]);
    DGF_CKPT_TBS_DESC tbs_desc;
    READ_GENERIC_VAR_FROM_FILE(tbs_desc);
    tbs->m_n_frames_sent = tbs_desc.n_frames_sent;
    tbs->m_n_total_frames_sent = tbs_desc.n_total_frames_sent;
  }
}
template VOID sCP_DGF_READER::read_tbs_desc_ckpt_data<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_tbs_desc_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_tbs_desc_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_turb_synth_info_ckpt_data()
{
  // Nothing to do if there is no data
  if constexpr (RT == sCP_DGF_READER::LEGACY ||RT == sCP_DGF_READER::DEBUG_MODE)
    if (m_ckpt_table_of_contents.turb_synth_pos <= 0)
      return;

  // Read data from legacy and send raw data to SPs
  if constexpr (RT == sCP_DGF_READER::LEGACY)
  {
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.turb_synth_pos);
    LGI_TAG tag = lgi_peek_tag(m_ckpt_istream);
    if (tag.id != LGI_TURB_SYNTH_INFO_STATE_TAG) {
      msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                         lgi_tag_namestring(LGI_TURB_SYNTH_INFO_STATE_TAG), LGI_TURB_SYNTH_INFO_STATE_TAG,
                         lgi_tag_namestring(tag.id), tag.id);
      return;
    }
    copy_record_to_all_sps(m_ckpt_istream, LGI_TURB_SYNTH_INFO_STATE_TAG);
  }
  // Read data from HDF5
  else
  {
    // Only one of the SPs saved the data. Searching for the owner of the data.
    size_t hdf5_size = 0;
    STP_PROC sp_master = PROC_INVALID;
    for (STP_PROC sp = 0; sp < g_light_data_collection.total_sps(); ++sp)
      if (g_light_data_collection.turb_synth_size(sp) > 0)
      {
        hdf5_size = g_light_data_collection.turb_synth_size(sp);
        sp_master = sp;
        break;
      }
    if( hdf5_size == 0) 
      return; // Nothing to do if there is no data
    else // The SP that saves the data have had to be found
      print_assert(sp_master != PROC_INVALID, "Owner of turbulent data not found");

    // Write header to all SPs
    {
      LGI_TAG hdf5_tag;
      lgi_write_init_tag (&hdf5_tag, LGI_TURB_SYNTH_INFO_STATE_TAG, hdf5_size);
      write_header_to_all_sps(hdf5_tag);

      // Compare header between Legacy and HDF5
      if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
      {
        jump_to_ckpt_file_position(m_ckpt_table_of_contents.turb_synth_pos);
        LGI_TAG lgi_tag = lgi_peek_tag(m_ckpt_istream);

        LocalReader<RT, false>::compare(lgi_tag.length, hdf5_tag.length,
          "\"Turb Synth tag length\"", __FILE__, __LINE__);
      }
    }

    // Access to the raw data
    uint8_t * const hdf5_data = g_light_data_collection.turb_synth_data(sp_master);

    // Write data to all SPs
    {
      size_t mem_offset = 0;
      while (mem_offset < hdf5_size)
      {
        constexpr asINT32 COPY_BUFSIZE = 16 * 1024;
        size_t chunk_size = hdf5_size - mem_offset;
        chunk_size = chunk_size < COPY_BUFSIZE ? chunk_size : COPY_BUFSIZE;
        write_to_all_sps(hdf5_data + mem_offset, hdf5_size);
      }
    }

    // Compare variable per variable
    if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
    {
      #define COMPARE_RAW_DATA(TYPE, VAR)                    \
        TYPE VAR = *(TYPE*)(hdf5_data + mem_offset);         \
        mem_offset += sizeof(TYPE);                          \
        {                                                    \
          TYPE VAR ## _lgi;                                  \
          lgi_read(m_ckpt_istream, VAR ## _lgi);             \
          LocalReader<RT, false>::compare(VAR ## _lgi, VAR,  \
            "\"Turb Synth " #VAR "\"", __FILE__, __LINE__);  \
        }
      
      size_t mem_offset = 0;
      COMPARE_RAW_DATA(uINT32, nInlets)
      for (uINT32 inlet_idx = 0; inlet_idx < nInlets; ++inlet_idx)
      {
        COMPARE_RAW_DATA(sFLOAT, freqMinOrig);
        COMPARE_RAW_DATA(sFLOAT, freqMaxOrig);
        COMPARE_RAW_DATA(sFLOAT, freqMin);
        COMPARE_RAW_DATA(sFLOAT, freqMax);
        COMPARE_RAW_DATA(uINT32, nSpatialModes);
        for (int nn = 0; nn < 3 * nSpatialModes; ++nn)
        {
          COMPARE_RAW_DATA(dFLOAT, unitNormalComp);
        }
        COMPARE_RAW_DATA(BOOLEAN, isValidLeft);
        COMPARE_RAW_DATA(BOOLEAN, isValidRight);
        COMPARE_RAW_DATA(dFLOAT, W);
        COMPARE_RAW_DATA(dFLOAT, turbIntensX);
        COMPARE_RAW_DATA(dFLOAT, turbIntensY);
        COMPARE_RAW_DATA(dFLOAT, turbIntensZ);
        COMPARE_RAW_DATA(dFLOAT, turbLenX);
        COMPARE_RAW_DATA(dFLOAT, turbLenY);
        COMPARE_RAW_DATA(dFLOAT, turbLenZ);
      }
      #undef COMPARE_RAW_DATA
    }
  }
  cp_info.num_turb_tables = 1;
}
template VOID sCP_DGF_READER::read_turb_synth_info_ckpt_data<sCP_DGF_READER::LEGACY     >();
template VOID sCP_DGF_READER::read_turb_synth_info_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_turb_synth_info_ckpt_data<sCP_DGF_READER::DEBUG_MODE >();

VOID sCP_DGF_READER::read_radiation_tm_ckpt_data()
{
  using namespace Loop;

  if (m_ckpt_table_of_contents.radiation_tm_pos <= 0)
    return;
  jump_to_ckpt_file_position(m_ckpt_table_of_contents.radiation_tm_pos);
  LGI_TAG tag = lgi_peek_tag(m_ckpt_istream);
  if (tag.id != LGI_CKPT_RADIATION_TM_TAG) {
    msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                       lgi_tag_namestring(LGI_CKPT_RADIATION_TM_TAG), LGI_CKPT_RADIATION_TM_TAG,
                       lgi_tag_namestring(tag.id), tag.id);

    return;
  }

  STP_PROC max_flow_sp = 0;
  STP_PROC max_cond_sp = 0;

  if (cp_info.is_flow) {
    max_flow_sp = total_sps;
  }

  if (cp_info.is_conduction) {
    max_flow_sp /= 2;
    max_cond_sp = total_sps;
  }

  // For a flow & conduction case, there are two RADIATION_TM datasets 
  // (one for conduction sps, one for flow sps). 

  if (cp_info.is_flow) {
    copy_record_to_sp_range(m_ckpt_istream, LGI_CKPT_RADIATION_TM_TAG, range(max_flow_sp));
  }

  if (cp_info.is_conduction) {
    copy_record_to_sp_range(m_ckpt_istream, LGI_CKPT_RADIATION_TM_TAG, range(max_flow_sp, max_cond_sp));
  }
}

static sSRI_XFORM to_sri_xform(const sBG_TRANSFORM3d& bg, sriDOUBLE angle_rotated) 
{

  sSRI_XFORM xform;

  for(int i=0; i<4; i++) {
    for(int j=0; j<4; j++) {
      xform.xform[i][j] = bg.M(i,j);
    }
  }
  xform.set_angle_rotated(angle_rotated);

  return xform;
}

static sBG_TRANSFORM3d to_bg_transform(const sSRI_XFORM& xform) {
  return sBG_TRANSFORM3d(xform.xform[0][0], xform.xform[0][1], xform.xform[0][2], xform.xform[0][3],
                         xform.xform[1][0], xform.xform[1][1], xform.xform[1][2], xform.xform[1][3],
                         xform.xform[2][0], xform.xform[2][1], xform.xform[2][2], xform.xform[2][3]);
}


template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_movb_ckpt_data()
{
  if (cp_info.n_movb_xforms() > 0) {
    if constexpr (RT == sCP_DGF_READER::LEGACY)
    {
      jump_to_ckpt_file_position(m_ckpt_table_of_contents.movb_pos);
      LGI_MOVB_STATE record;
      record.read(m_ckpt_istream);
#if 0
      lgi_read_next_head(m_ckpt_istream, tag);
      if (tag.id != LGI_MOVB_STATE_TAG)
        msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                           lgi_tag_namestring(LGI_MOVB_STATE_TAG), LGI_MOVB_STATE_TAG,
                           lgi_tag_namestring(tag.id), tag.id);

      write_header_to_all_sps(tag);
#endif
      write_header_to_all_sps(record);

      ccDOTIMES(i, cp_info.n_movb_xforms()) {
        dFLOAT initial_angle_rotated;
        dFLOAT angle_rotated;
        sBG_TRANSFORM3d motion_xform; 
        lgi_read(m_ckpt_istream, initial_angle_rotated);
        lgi_read(m_ckpt_istream, angle_rotated);
        lgi_read(m_ckpt_istream, motion_xform);
        cp_info.ckpt_movb_xforms.push_back( to_sri_xform(motion_xform, angle_rotated) );
        write_to_all_sps(initial_angle_rotated);
        write_to_all_sps(angle_rotated);
        write_to_all_sps(motion_xform);
      }
    }
    else
    {
      constexpr asINT32 master_sp = 0;
      uint8_t * const hdf5_raw_data = g_light_data_collection.movb_state_data(master_sp);
      size_t hdf5_raw_size = g_light_data_collection.movb_state_size(master_sp);

      LGI_MOVB_STATE record;
      lgi_write_init_tag(&record, LGI_MOVB_STATE_TAG, hdf5_raw_size + sizeof(LGI_MOVB_STATE));
      record.n_bytes_movb_state = hdf5_raw_size;
      write_header_to_all_sps(record);

      if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
      {
        jump_to_ckpt_file_position(m_ckpt_table_of_contents.movb_pos);
        // Read and compare headers
        {
          LGI_MOVB_STATE record_lgi;
          lgi_read_next_head(m_ckpt_istream, record_lgi);
          #define COMPARE_RECORD(VAR) LocalReader<RT>::compare(record.VAR , record_lgi.VAR, "MOVB record." #VAR, __FILE__, __LINE__)
          COMPARE_RECORD(tag.id            );
          COMPARE_RECORD(tag.length        );
          COMPARE_RECORD(n_bytes_movb_state);
          #undef COMPARE_RECORD
        }
      }

      size_t offset = 0;

      // Lambda function to read data depending on the mode of the reader
      auto read_and_compare = [&](auto& VAR, const char * v, const char * f, int l)
      {
        using in_t = typename std::remove_reference<decltype(VAR)>::type;
        VAR = *((in_t*) (hdf5_raw_data + offset));
        offset += sizeof(in_t);
        if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
        {
          in_t var_lgi;
          lgi_read(m_ckpt_istream, var_lgi);
          LocalReader<RT>::template compare<in_t>(VAR, var_lgi, v, f, l);
        }
      };
      
      ccDOTIMES(i, cp_info.n_movb_xforms()) {
        dFLOAT initial_angle_rotated;
        dFLOAT angle_rotated;
        sBG_TRANSFORM3d motion_xform;
        #define READ_AND_COMPARE(VAR) read_and_compare(VAR, "movb." #VAR, __FILE__, __LINE__)
        READ_AND_COMPARE(initial_angle_rotated);
        READ_AND_COMPARE(angle_rotated);
        READ_AND_COMPARE(motion_xform);
        #undef READ_AND_COMPARE
        cp_info.ckpt_movb_xforms.push_back( to_sri_xform(motion_xform, angle_rotated) );
        write_to_all_sps(initial_angle_rotated);
        write_to_all_sps(angle_rotated);
        write_to_all_sps(motion_xform);
      }
    }
  }
  else
    print_mssg_in_debug<RT, message_type::INFO>("There is no data for movb");
}
template VOID sCP_DGF_READER::read_movb_ckpt_data<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_movb_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_movb_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_thermal_accel_ckpt_data()
{
  if (cp_info.is_heat_transfer) {
    if constexpr (RT == sCP_DGF_READER::LEGACY)
    {
      jump_to_ckpt_file_position(m_ckpt_table_of_contents.thermal_accel_pos);
      copy_record_to_all_sps(m_ckpt_istream, LGI_THERMAL_ACCEL_STATE_TAG);
    }
    else
    {
      constexpr asINT32 master_sp = 0;
      uint8_t * const hdf5_raw_data = g_light_data_collection.thermal_accel_state_data(master_sp);
      size_t hdf5_raw_size = g_light_data_collection.thermal_accel_state_size(master_sp);

      LGI_THERMAL_ACCEL_STATE record;
      lgi_write_init_tag(&record, LGI_THERMAL_ACCEL_STATE_TAG, hdf5_raw_size + sizeof(LGI_THERMAL_ACCEL_STATE));
      record.n_bytes_thermal_accel_state = hdf5_raw_size;
      write_header_to_all_sps(record);
      write_to_all_sps(hdf5_raw_data, hdf5_raw_size);

      if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
      {
        jump_to_ckpt_file_position(m_ckpt_table_of_contents.thermal_accel_pos);

        // Read and compare headers
        {
          LGI_THERMAL_ACCEL_STATE record_lgi;
          lgi_read_next_head(m_ckpt_istream, record_lgi);
          #define COMPARE_RECORD(VAR) LocalReader<RT>::compare(record.VAR , record_lgi.VAR, "THERMAL ACCEL record." #VAR, __FILE__, __LINE__)
          COMPARE_RECORD(tag.id                     );
          COMPARE_RECORD(tag.length                 );
          COMPARE_RECORD(n_bytes_thermal_accel_state);
          #undef COMPARE_RECORD
        }

        // This struct has been copied from "sim.h" to have same content than the saved one
        struct sTHERMAL_ACCEL_INFO {
          TIMESTEP start;
          TIMESTEP period;
          TIMESTEP repeat;
          TIMESTEP interval;
          cBOOLEAN is_hacked;
          TIMESTEP hacked_start;
          TIMESTEP hacked_period;
          TIMESTEP hacked_stop;
          cBOOLEAN acc_on;
          cBOOLEAN do_T_solver_switch;
          TIMESTEP T_solver_switch_time_for_surfel_recv_group;
          TIMESTEP T_solver_switch_time_for_ublk_recv_group;
        };

        sTHERMAL_ACCEL_INFO& thermal_accel_hdf5 = *(sTHERMAL_ACCEL_INFO*)(hdf5_raw_data);
        sTHERMAL_ACCEL_INFO thermal_accel_lgi;
        lgi_read(m_ckpt_istream, thermal_accel_lgi);

        #define COMPARE_THERMAL_ACCEL(VAR) LocalReader<RT>::compare(thermal_accel_hdf5.VAR, thermal_accel_lgi.VAR, "thermal_accel." #VAR, __FILE__, __LINE__);
        COMPARE_THERMAL_ACCEL(start)
        COMPARE_THERMAL_ACCEL(period)
        COMPARE_THERMAL_ACCEL(repeat)
        COMPARE_THERMAL_ACCEL(interval)
        COMPARE_THERMAL_ACCEL(is_hacked)
        COMPARE_THERMAL_ACCEL(hacked_start)
        COMPARE_THERMAL_ACCEL(hacked_period)
        COMPARE_THERMAL_ACCEL(hacked_stop)
        COMPARE_THERMAL_ACCEL(acc_on)
        COMPARE_THERMAL_ACCEL(do_T_solver_switch)
        COMPARE_THERMAL_ACCEL(T_solver_switch_time_for_surfel_recv_group)
        COMPARE_THERMAL_ACCEL(T_solver_switch_time_for_ublk_recv_group)
        #undef COMPARE_THERMAL_ACCEL
      }
    }
  }
  else
    print_mssg_in_debug<RT, message_type::INFO>("There is no data for thermal_accel");
}
template VOID sCP_DGF_READER::read_thermal_accel_ckpt_data<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_thermal_accel_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_thermal_accel_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_fan_ckpt_data(asINT32 total_ckpt_sps)
{
  // ----------------------------------------------------------------------------------------------
  // EARLY RETURN
  // ----------------------------------------------------------------------------------------------
  // Criteria to end the function. There are two in debug mode and only one in legacy or serial_hdf5
  bool lgi_criterion = false;
  TYPE_OR_REF_IF_DEBUG(bool) hdf5_criterion = lgi_criterion;
  // Compute criteria for early return
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    LGI_TAG tag = lgi_peek_tag(m_ckpt_istream);
    lgi_criterion = tag.id != LGI_CKPT_FAN_TAG;
  }
  if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    hdf5_criterion = true;
    for (int sp = 0; sp < total_ckpt_sps; ++sp)
      hdf5_criterion &= g_light_data_collection.fan_descs_size(sp) == 0;
  }
  // Check, when debugging, if both criteria are equal
  if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
    LocalReader<RT>::compare(lgi_criterion, hdf5_criterion, "FAN early return criterion", __FILE__, __LINE__);
  // Early return if there is no data related with fan
  if (lgi_criterion)
  {
    print_mssg_in_debug<RT, message_type::INFO>("There is no data for fan_descs");
    return;
  }
  // ----------------------------------------------------------------------------------------------

  size_t offset_hdf5;
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.fan_pos);
  
  auto read_record_head = [this, &offset_hdf5](asINT32 sp, LGI_CKPT_FAN_REC& record)
  {
    TYPE_OR_REF_IF_DEBUG(LGI_CKPT_FAN_REC) record_copy = record;

    if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
    {
      lgi_read_next_head(m_ckpt_istream, record_copy);
    }
    if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
    {
      offset_hdf5 = 0;
      size_t hdf5_raw_size = g_light_data_collection.fan_descs_size(sp);
      lgi_write_init_tag(&record, LGI_CKPT_FAN_TAG, hdf5_raw_size + sizeof(LGI_CKPT_FAN_REC));
      record.n_global_fan_descs = hdf5_raw_size / sizeof(LGI_CKPT_FAN_SUBREC);
    }
    if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
    {
      #define COMPARE_FAN_RECORD(VAR) LocalReader<RT>::compare(record.VAR, record_copy.VAR, "FAN record." #VAR, __FILE__, __LINE__);
      COMPARE_FAN_RECORD(tag.id            )
      COMPARE_FAN_RECORD(tag.length        )
      COMPARE_FAN_RECORD(n_global_fan_descs)
      #undef COMPARE_FAN_RECORD
    }
  };
  
  auto read_record_data = [this, &offset_hdf5](asINT32 sp, LGI_CKPT_FAN_SUBREC& subrec)
  {
    TYPE_OR_REF_IF_DEBUG(LGI_CKPT_FAN_SUBREC) subrec_copy = subrec;

    // Read from legacy
    if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
    {
      lgi_read(m_ckpt_istream, subrec_copy);
    }
    if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
    {
      uint8_t * const hdf5_raw_data = g_light_data_collection.fan_descs_data(sp);
      auto read_val = [hdf5_raw_data, &offset_hdf5]()
      {
        dFLOAT out =  *(dFLOAT*)(hdf5_raw_data + offset_hdf5);
        offset_hdf5 += sizeof(dFLOAT);
        return out;
      };
      subrec.u_axial_accum   = read_val();
      subrec.n_finest_voxels = read_val();
      subrec.u_tilda_accum   = read_val();
      subrec.factor_accum    = read_val();
    }
    if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
    {
      #define COMPARE_FAN(VAR) LocalReader<RT>::compare(subrec.VAR, subrec_copy.VAR, "fan." #VAR, __FILE__, __LINE__);
      COMPARE_FAN(u_axial_accum  )
      COMPARE_FAN(n_finest_voxels)
      COMPARE_FAN(u_tilda_accum  )
      COMPARE_FAN(factor_accum   )
      #undef COMPARE_FAN
    }
  };

  LGI_CKPT_FAN_REC record;

  if (total_sps >= total_ckpt_sps) {
    // If there are the same or fewer SPs in the ckpt file, write zero-filled LGI_CKPT_FAN_SUBRECs to
    // the extra SPs.
    ccDOTIMES(sp, total_ckpt_sps) {
      read_record_head(sp, record);
      record.write(g_sp_streams[sp], FALSE);
      ccDOTIMES(i, record.n_global_fan_descs) {
        LGI_CKPT_FAN_SUBREC subrec;
        read_record_data(sp, subrec);
        subrec.write(g_sp_streams[sp]);
      }
    }
    if (total_sps > total_ckpt_sps) {
      LGI_CKPT_FAN_SUBREC subrec = { 0 };
      ccDO_FROM_BELOW(sp, total_ckpt_sps, total_sps) {
        record.write(g_sp_streams[sp], FALSE);
        ccDOTIMES(i, record.n_global_fan_descs)
          subrec.write(g_sp_streams[sp]);
      }
    }
  } else {
    // If there are more SPs in the ckpt file, add the extra LGI_CKPT_FAN_SUBRECs into the last SP's subrec
    ccDOTIMES(sp, total_sps - 1) {
      read_record_head(sp, record);
      record.write(g_sp_streams[sp], FALSE);
      ccDOTIMES(i, record.n_global_fan_descs) {
        LGI_CKPT_FAN_SUBREC subrec;
        read_record_data(sp, subrec);
        subrec.write(g_sp_streams[sp]);
      }
    }
    LGI_CKPT_FAN_SUBREC *total_subrecs = xnew LGI_CKPT_FAN_SUBREC [record.n_global_fan_descs];
    ccDO_FROM_BELOW(sp, total_sps - 1, total_ckpt_sps) {
      read_record_head(sp, record);
      ccDOTIMES(i, record.n_global_fan_descs) {
        LGI_CKPT_FAN_SUBREC subrec;
        read_record_data(sp, subrec);
        total_subrecs[i].u_axial_accum   += subrec.u_axial_accum;
        total_subrecs[i].n_finest_voxels += subrec.n_finest_voxels;
      }
    }
    record.write(g_sp_streams[total_sps - 1], FALSE);
    lgi_write(g_sp_streams[total_sps - 1], total_subrecs, record.n_global_fan_descs * sizeof(LGI_CKPT_FAN_SUBREC));
    delete [] total_subrecs;
  }
}
template VOID sCP_DGF_READER::read_fan_ckpt_data<sCP_DGF_READER::LEGACY>     (asINT32);
template VOID sCP_DGF_READER::read_fan_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>(asINT32);
template VOID sCP_DGF_READER::read_fan_ckpt_data<sCP_DGF_READER::DEBUG_MODE> (asINT32);


//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_random_particle_property_state(asINT32 total_sps_in_ckpt) { //(reads from the file and writes to the lgi stream)

  LGI_TAG tag_lgi;
  bool return_criterion_lgi = true;
  TYPE_OR_REF_IF_DEBUG(bool) return_criterion_hdf5 = return_criterion_lgi;
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    tag_lgi = lgi_peek_tag(m_ckpt_istream);
    return_criterion_lgi = tag_lgi.id != LGI_CKPT_PARTICLE_RANDOM_PROPERTY_TAG;
  }
  if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    return_criterion_hdf5 = true;
    for (int sp = 0; sp < total_sps_in_ckpt; sp++)
    {
      uint8_t * __restrict const hdf5_raw_data = g_light_data_collection.particle_sim_data(sp);
      asINT32 n_emitters = *(asINT32*) hdf5_raw_data;
      size_t emitter_size = n_emitters * sizeof(LGI_CKPT_PARTICLE_EMITTER_REC) + sizeof(size_t);
      return_criterion_hdf5 &= emitter_size == g_light_data_collection.particle_sim_size(sp);
    }
  }
  if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
  {
    LocalReader<RT, false>::compare(return_criterion_lgi, return_criterion_hdf5,
      "\"Random particle state criterion\"", __FILE__, __LINE__);
  }
  if (return_criterion_hdf5)
    return;
  
  auto send_data_to_sp = [this](asINT32 sp)
  {
    if constexpr (RT == sCP_DGF_READER::LEGACY)
    {
      copy_record_to_sp(m_ckpt_istream, LGI_CKPT_PARTICLE_RANDOM_PROPERTY_TAG, sp);
    }
    else
    {
      uint8_t * __restrict const hdf5_raw_data = g_light_data_collection.particle_sim_data(sp);
      asINT32 n_emitters = *(asINT32*) hdf5_raw_data;
      size_t hdf5_raw_offset = n_emitters * sizeof(asINT32) + sizeof(asINT32); // Different size in LGI

      auto read_hdf5_raw_data = [hdf5_raw_data, &hdf5_raw_offset](auto& data)
      {
        using in_t = typename std::remove_reference<decltype(data)>::type;
        data = *(in_t*)(hdf5_raw_data + hdf5_raw_offset);
        hdf5_raw_offset += sizeof(in_t);
      };

      size_t record_length;
      read_hdf5_raw_data(record_length);
      
      // Create header from available data in HDF5
      LGI_CKPT_PARTICLE_RANDOM_PROPERTY_HEADER header_hdf5;
      {
        lgi_write_init_tag(&header_hdf5, LGI_CKPT_PARTICLE_RANDOM_PROPERTY_TAG,
          record_length + sizeof(LGI_CKPT_PARTICLE_RANDOM_PROPERTY_HEADER) - sizeof(size_t) - sizeof(asINT32));
        asINT32 n_random_number_generators;
        read_hdf5_raw_data(n_random_number_generators);
        header_hdf5.n_random_number_generators = n_random_number_generators; // Conversion from asINT32 to asINT64
      }

      // Comparing headers
      if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
      {
        LGI_CKPT_PARTICLE_RANDOM_PROPERTY_HEADER header_lgi;
        lgi_read_next_head(m_ckpt_istream, header_lgi);
        #define PART_RAND_HEADER_COMP(VAR) LocalReader<RT>::compare(header_lgi.VAR, header_hdf5.VAR,\
          "\"Random particle state header." #VAR "\"", __FILE__, __LINE__);
        PART_RAND_HEADER_COMP(tag.id)
        PART_RAND_HEADER_COMP(tag.length)
        PART_RAND_HEADER_COMP(n_random_number_generators)
        #undef PART_RAND_HEADER_COMP
      }

      // Send header to SPs
      lgi_write_next_head(g_sp_streams[sp], header_hdf5);

      // Send body to SPs
      {
        // Default buffer size while working with LGI buffers
        constexpr asINT32 COPY_BUFSIZE = 16 * 1024;

        // Lambda function to allocate a buffer for LGI, only when running in debug mode
        auto allocate_buffer_lgi = []() -> uint8_t*
        {
          if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
            return new uint8_t[COPY_BUFSIZE];
          else
            return nullptr;
        };
        uint8_t * __restrict const data_lgi = allocate_buffer_lgi();

        // Total size to know remaining amount of data
        const size_t hdf5_raw_size = g_light_data_collection.particle_sim_size(sp);
        
        // Sending data to SPs in chunks of size COPY_BUFSIZE (default value when working with other functions)
        while (hdf5_raw_offset < hdf5_raw_size)
        {
          const asINT32 remaining_size = hdf5_raw_size - hdf5_raw_offset;
          const asINT32 chunkSize = COPY_BUFSIZE < remaining_size ? COPY_BUFSIZE : remaining_size;
          if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
          {
            print_mssg<message_type::INFO>("Reading %lu bytes of raw data", chunkSize);
            lgi_read(m_ckpt_istream, data_lgi, chunkSize);
            LocalReader<RT>::compare(data_lgi, hdf5_raw_data + hdf5_raw_offset, chunkSize,
              "Raw rng state data for particles", __FILE__, __LINE__);
          }
          lgi_write(g_sp_streams[sp], hdf5_raw_data + hdf5_raw_offset, chunkSize);
          hdf5_raw_offset += COPY_BUFSIZE;
        }

        // Free memory, only in debug mode
        if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
          delete [] data_lgi;
      }
    }
  };

 if (total_sps >= total_sps_in_ckpt) {
   ccDOTIMES(sp, total_sps_in_ckpt) { //Write the existing emitter checkpoint records to the SPs that exist.
     send_data_to_sp(sp);
   }
 } else {
   ccDOTIMES(sp, total_sps) { //Write the existing emitter checkpoint records to the first SPs.
     send_data_to_sp(sp);
   }
   
   // Remainig data only read from legacy
   if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
     ccDO_FROM_BELOW(sp, total_sps, total_sps_in_ckpt) { //Consume the extra records from the lgi stream.
       LGI_CKPT_PARTICLE_RANDOM_PROPERTY_HEADER header;
       lgi_read_next_head(m_ckpt_istream, header);
       FILE_OFFSET n_bytes = (FILE_OFFSET)tag_lgi.length * LGI_LEN_MULTIPLE - sizeof(LGI_TAG);
       char* buffer = xnew char[n_bytes];
       lgi_read(m_ckpt_istream, buffer, n_bytes);
     }
 }
}
template VOID sCP_DGF_READER::read_random_particle_property_state<sCP_DGF_READER::LEGACY>     (asINT32);
template VOID sCP_DGF_READER::read_random_particle_property_state<sCP_DGF_READER::HDF5_SERIAL>(asINT32);
template VOID sCP_DGF_READER::read_random_particle_property_state<sCP_DGF_READER::DEBUG_MODE> (asINT32);

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_particle_emitter_ckpt(asINT32 total_sps_in_ckpt) {
  // Creating the reader
  LocalReader<RT, false> reader(initLocalReader<RT>(m_ckpt_istream, sim_args.resume_filename,
                                                    "CP Emitter states"));
  // Write cp emitter state for emitters started via monitors
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    LGI_TAG tag;
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.emitter_pos);
    lgi_read_next_head(m_ckpt_istream, tag);
    if (tag.id != LGI_CKPT_PARTICLE_EMITTERS_STATE_TAG)
      msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                         lgi_tag_namestring(LGI_CKPT_PARTICLE_EMITTERS_STATE_TAG), LGI_CKPT_PARTICLE_EMITTERS_STATE_TAG,
                         lgi_tag_namestring(tag.id), tag.id);
  }
  // Record size (only in HDF5 file)
  {
    size_t record_size;
    reader.read_hdf5(record_size);
  }

  sINT32 n_emitters;
  READ_GENERIC_VAR_FROM_FILE(n_emitters);
  LGI_CKPT_PARTICLE_EMITTERS_STATE_HEADER emitters_state_header;
  lgi_write_init_tag(&emitters_state_header, LGI_CKPT_PARTICLE_EMITTERS_STATE_TAG,
    sizeof(emitters_state_header) + n_emitters * sizeof(LGI_CKPT_PARTICLE_EMITTERS_STATE_REC));
  emitters_state_header.n_emitters = n_emitters;
  write_header_to_all_sps(emitters_state_header);

  if (n_emitters != cp_particle_sim.emitters.size())
    msg_internal_error("Number of emitters started after initial transient in checkpoint file (%d) does not match number in CDI file (%zu).",
                       n_emitters, cp_particle_sim.emitters.size());

  ccDOTIMES(i, n_emitters) {
    LGI_CKPT_PARTICLE_EMITTERS_STATE_REC ckpt_emitter_desc;
    READ_GENERIC_VAR_FROM_FILE(ckpt_emitter_desc);

    assert(ckpt_emitter_desc.sim_emitter_id == cp_particle_sim.emitters[i]->id());
    // Update the emitters accordingly
    if (ckpt_emitter_desc.is_requested_to_start) {
#if DEBUG_START_EMITTERS
      msg_print("Request to start emitter %d to start at max(ckpt scheduled start time %d, cp time %d)",
                cp_particle_sim.emitters[i]->id(),
                ckpt_emitter_desc.requested_start_time,
                cp_info.time);
#endif
      cp_particle_sim.emitters[i]->request_to_start(MAX(ckpt_emitter_desc.requested_start_time, cp_info.time));
    } else if (ckpt_emitter_desc.is_scheduled_to_start) {
 #if DEBUG_START_EMITTERS
      msg_print("Set emitter %d start time to %d",
                cp_particle_sim.emitters[i]->id(),
                ckpt_emitter_desc.start_time);
#endif
      cp_particle_sim.emitters[i]->set_start_time(ckpt_emitter_desc.start_time);
    }

    ccDOTIMES(sp, total_sps)
      ckpt_emitter_desc.write(g_sp_streams[sp]);    
  }

  // Stop criterion for legacy data. Finish if there is no more available data.
  bool return_criterion_lgi = false;
  TYPE_OR_REF_IF_DEBUG(bool) return_criterion_hdf5 = return_criterion_lgi;

  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    LGI_TAG tag = lgi_peek_tag(m_ckpt_istream);
    return_criterion_lgi = tag.id != LGI_CKPT_PARTICLE_EMITTERS_TAG;
  }
  if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    return_criterion_hdf5 = g_light_data_collection.particle_sim_cum_size() == 0;
  }
  if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
    LocalReader<RT, false>::compare(return_criterion_lgi, return_criterion_hdf5,
      "\"Return criterion for Particle Emitters\"", __FILE__, __LINE__);
  
  // Lambda function to get the reference to the raw data for the particle sim
  auto get_particle_sim_data_ref = [](asINT32 sp) -> uint8_t*
  {
    if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
      return g_light_data_collection.particle_sim_data(sp);
    else
      return nullptr;
  };

  //if there aren't less sp's than what the checkpoint was created with:
  if (total_sps >= total_sps_in_ckpt) {
    LGI_CKPT_PARTICLE_EMITTERS_HEADER header_lgi;
    TYPE_OR_REF_IF_DEBUG(LGI_CKPT_PARTICLE_EMITTERS_HEADER) header_hdf5 = header_lgi;
    
    ccDOTIMES(sp, total_sps_in_ckpt) { //write the existing emitter checkpoint records to the first SPs

      // Some variables and lambda functions to work with HDF5 data
      uint8_t * __restrict const hdf5_raw_data = get_particle_sim_data_ref(sp);
      size_t hdf5_raw_offset = 0;
      auto read_hdf5_raw_data = [hdf5_raw_data, &hdf5_raw_offset](auto& data)
      {
        using in_t = typename std::remove_reference<decltype(data)>::type;
        data = *(in_t*)(hdf5_raw_data + hdf5_raw_offset);
        hdf5_raw_offset += sizeof(in_t);
      };
      
      // Read header (tag + n_emitters) and compare
      if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
        lgi_read_next_head(m_ckpt_istream, header_lgi);
      if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
      {
        read_hdf5_raw_data(header_hdf5.n_emitters);
        size_t record_length=sizeof(LGI_CKPT_PARTICLE_EMITTERS_HEADER) + header_hdf5.n_emitters * sizeof(LGI_CKPT_PARTICLE_EMITTER_REC);
        lgi_write_init_tag(&header_hdf5, LGI_CKPT_PARTICLE_EMITTERS_TAG, record_length);
      }
      if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
      {
        #define PARTICLE_EMITTER_HEADER_COMP(VAR) \
          LocalReader<RT>::compare(header_lgi.VAR, header_hdf5.VAR, "Particle emitter " #VAR, __FILE__, __LINE__);
        PARTICLE_EMITTER_HEADER_COMP(tag.id)
        PARTICLE_EMITTER_HEADER_COMP(tag.length)
        PARTICLE_EMITTER_HEADER_COMP(n_emitters)
        #undef PARTICLE_EMITTER_HEADER_COMP
      }
      
      // Send data of the header to SP
      header_hdf5.write(g_sp_streams[sp], FALSE);
      // For each emitter...
      ccDOTIMES(i, header_hdf5.n_emitters) {
        LGI_CKPT_PARTICLE_EMITTER_REC record_lgi;
        TYPE_OR_REF_IF_DEBUG(LGI_CKPT_PARTICLE_EMITTER_REC) record_hdf5 = record_lgi;
        if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
        {
          record_lgi.read(m_ckpt_istream);
        }
        if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
        {
          asINT32 parcel_id_counter=0;
          read_hdf5_raw_data(parcel_id_counter);
          record_hdf5.parcel_id_counter=parcel_id_counter;
        }
        if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
        {
          // Check if both records are equal
          LocalReader<RT, false>::compare(record_lgi.parcel_id_counter,
            record_hdf5.parcel_id_counter, "parcel_id_counter", __FILE__, __LINE__);
        }
        record_hdf5.write(g_sp_streams[sp]);
      }
    }

    //if there are any remaining SPs, dont bother sending emitter checkpoint data and just use their default initial state.
#if 0
    //for the remaming SPs, use the last emitter ckpt data included in the checkpoint for all the remaming SPs.
    ccDO_FROM_BELOW(sp,total_sps_in_ckpt, total_sps) {  //write the last header to all remaining sps
      header.write(g_sp_streams[sp], FALSE);     //reuse the last header from the above loop
      LGI_CKPT_PARTICLE_EMITTER_REC record;
      ccDOTIMES(i, header.n_emitters) {   //reuse the same emitter ckpt data from above for all remaining SPs
        record.write(g_sp_streams[sp]);
      }
    }
#endif
  } else { //otherwise there are less SPs than what is in the checkpoint file, simply discard the extras
    ccDOTIMES(sp, total_sps) { //write the emitter checkpoint records to existing SPs
      uint8_t * __restrict const hdf5_raw_data = get_particle_sim_data_ref(sp);
      size_t hdf5_raw_offset = 0;
      auto read_hdf5_raw_data = [hdf5_raw_data, &hdf5_raw_offset](auto& data)
      {
        using in_t = typename std::remove_reference<decltype(data)>::type;
        data = *(in_t*)(hdf5_raw_data + hdf5_raw_offset);
        hdf5_raw_offset += sizeof(in_t);
      };

      LGI_CKPT_PARTICLE_EMITTERS_HEADER header_lgi;
      TYPE_OR_REF_IF_DEBUG(LGI_CKPT_PARTICLE_EMITTERS_HEADER) header_hdf5 = header_lgi;
      // Read header (tag + n_emitters) and compare
      if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
        lgi_read_next_head(m_ckpt_istream, header_lgi);
      if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
      {
        asINT32 n_emitters;
        read_hdf5_raw_data(n_emitters);
        size_t header_size = sizeof(LGI_CKPT_PARTICLE_EMITTERS_HEADER)
                           + n_emitters * sizeof(LGI_CKPT_PARTICLE_EMITTER_REC);
        lgi_write_init_tag(&header_hdf5, LGI_CKPT_PARTICLE_EMITTERS_TAG, header_size);
        header_hdf5.n_emitters = n_emitters;
      }
      if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
      {
        #define PARTICLE_EMITTER_HEADER_COMP(VAR) \
          LocalReader<RT>::compare(header_lgi.VAR, header_hdf5.VAR, "Particle emitter " #VAR, __FILE__, __LINE__);
        PARTICLE_EMITTER_HEADER_COMP(tag.id)
        PARTICLE_EMITTER_HEADER_COMP(tag.length)
        PARTICLE_EMITTER_HEADER_COMP(n_emitters)
        #undef PARTICLE_EMITTER_HEADER_COMP
      }
      //header_hdf5.write(g_sp_streams[sp], FALSE);
      lgi_write_next_head(g_sp_streams[sp], header_hdf5);
      ccDOTIMES(i, header_hdf5.n_emitters) {
        LGI_CKPT_PARTICLE_EMITTER_REC record_lgi;
        TYPE_OR_REF_IF_DEBUG(LGI_CKPT_PARTICLE_EMITTER_REC) record_hdf5 = record_lgi;
        if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
        {
          record_lgi.read(m_ckpt_istream);
        }
        if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
        {
          // Saved data has 32bits whereas the struct has 64bits
          asINT32 parcel_id_counter;
          read_hdf5_raw_data(parcel_id_counter);
          record_hdf5.parcel_id_counter = parcel_id_counter;
        }
        if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
        {
          // Check if both records are equal
          LocalReader<RT, false>::compare(record_lgi.parcel_id_counter,
            record_hdf5.parcel_id_counter, "parcel_id_counter", __FILE__, __LINE__);
        }
        record_hdf5.write(g_sp_streams[sp]);
      }
    }
    if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
    { 
      ccDO_FROM_BELOW(sp, total_sps, total_sps_in_ckpt) { //discard the extras from the lgi stream
        LGI_CKPT_PARTICLE_EMITTERS_HEADER header;
        lgi_read_next_head(m_ckpt_istream, header);
        ccDOTIMES(i, header.n_emitters) {
          LGI_CKPT_PARTICLE_EMITTER_REC record;
          record.read(m_ckpt_istream);
        }
      }
    }
  }
}
template VOID sCP_DGF_READER::read_particle_emitter_ckpt<sCP_DGF_READER::LEGACY     >(asINT32 total_sps_in_ckpt);
template VOID sCP_DGF_READER::read_particle_emitter_ckpt<sCP_DGF_READER::HDF5_SERIAL>(asINT32 total_sps_in_ckpt);
template VOID sCP_DGF_READER::read_particle_emitter_ckpt<sCP_DGF_READER::DEBUG_MODE >(asINT32 total_sps_in_ckpt);

// ToDo: Move this functionality to sCP_TRAJECTORY_ID_MAP::read_ckpt when possible
template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_trajectory_id_map_ckpt(asINT32 total_ckpt_sps)
{
  if constexpr (RT == sCP_DGF_READER::LEGACY)
    g_trajectory_id_map.read_ckpt(m_ckpt_istream, total_ckpt_sps);
  else
  {
    // This record only exits if trajectory windows are saved
    if(!has_trajectory_window() || sim_args.disable_particle_modeling)
      return;

    // Creating the reader
    LocalReader<RT, false> reader(initLocalReader<RT>(m_ckpt_istream,
                                                      sim_args.resume_filename,
                                                     "Trajectory windows"));
    // Get record size
    size_t record_size;
    reader.read_hdf5(record_size);

    // Create stream where temporally store the data
    {
      // The size of the data to be read is given by the header of the data in the HDF5,
      // substracting the size of the header itself
      const size_t raw_data_size = record_size - sizeof(size_t);
      // Reserve espace to temporally store the data read from HDF5 file
      sCKPT_BUFFER ckpt_buff(raw_data_size);
      // Reading data from HDF5
      reader.read_hdf5(ckpt_buff.m_buffer, raw_data_size);
      g_trajectory_id_map.read_ckpt(ckpt_buff, total_ckpt_sps);
      // Free memory
      ckpt_buff.deallocate();
    }

    // Compare data if we are in debug mode
    if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
    {
      // Create a new variable to compare with, read from legacy
      sCP_TRAJECTORY_ID_MAP g_trajectory_id_map2;
      g_trajectory_id_map2.initialize();
      g_trajectory_id_map2.read_ckpt(m_ckpt_istream, total_ckpt_sps);

      // Comparing accessible variables (there is additional private data)
      if (total_ckpt_sps == total_sps)
      {
        LocalReader<RT, false>::compare(
          g_trajectory_id_map.base_global_id_size(),
          g_trajectory_id_map2.base_global_id_size(),
          "trajectory_id_map.m_base_global_ids.size()",
          __FILE__, __LINE__);
        for( size_t id=0; id<g_trajectory_id_map.base_global_id_size(); id++)
        {
          LocalReader<RT, false>::compare(
          g_trajectory_id_map.base_global_id_at(id),
          g_trajectory_id_map2.base_global_id_at(id),
          "trajectory_id_map.base_global_id_at",
          __FILE__, __LINE__);
        }
      }
      LocalReader<RT, false>::compare(
        g_trajectory_id_map.next_available_global_index_size(),
        g_trajectory_id_map2.next_available_global_index_size(),
        "trajectory_id_map.next_available_global_index_size",
        __FILE__, __LINE__);  
      for( size_t id=0; id<g_trajectory_id_map.next_available_global_index_size(); id++)
      {
        LocalReader<RT, false>::compare(
        g_trajectory_id_map.next_available_global_index_at(id),
        g_trajectory_id_map2.next_available_global_index_at(id),
        "trajectory_id_map.next_available_global_index_at",
        __FILE__, __LINE__);
      }
    }
  }
}
template VOID sCP_DGF_READER::read_trajectory_id_map_ckpt<sCP_DGF_READER::LEGACY>     (asINT32);
template VOID sCP_DGF_READER::read_trajectory_id_map_ckpt<sCP_DGF_READER::HDF5_SERIAL>(asINT32);
template VOID sCP_DGF_READER::read_trajectory_id_map_ckpt<sCP_DGF_READER::DEBUG_MODE> (asINT32);

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_ckpt_random_seed_record(STP_PROC total_sps_in_ckpt)
{
  // Execute this in the first access to light data to read it from HDF5 file
  // ToDo: to be moved somewhere else
  if constexpr (RT == sCP_DGF_READER::HDF5_SERIAL || RT == sCP_DGF_READER::DEBUG_MODE)
    g_light_data_collection.initialize(RT, sim_args.resume_filename, total_sps_in_ckpt);

  // ToCheck: Jump located here instead of internal to the loop to read data from all SPs
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.random_seed_pos);
  
  if (total_sps_in_ckpt == total_sps)  {
    ccDOTIMES(sp, total_sps)
    {
      if constexpr (RT == sCP_DGF_READER::LEGACY)
      {
        LGI_TAG tag;
        copy_record_to_sp(m_ckpt_istream, LGI_CKPT_RANDOM_SEED_TAG, sp);
      }
      else
      {
        // Get size of current data. Only saved the rand state
        size_t hdf5_rand_state_length = g_light_data_collection.rand_state_size(sp);

        // This record is the equivalent to the legacy one. It has to be created because it does not exist on HDF5
        LGI_RANDOM_SEED_REC hdf5_record;
        asINT32 hdf5_record_length = sizeof(LGI_RANDOM_SEED_REC) + hdf5_rand_state_length;
        lgi_write_init_tag (&hdf5_record, LGI_CKPT_RANDOM_SEED_TAG, hdf5_record_length);
        hdf5_record.n_bytes_rand_state = hdf5_rand_state_length;

        if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
        {
          // Read legacy record
          LGI_RANDOM_SEED_REC lgi_record;
          lgi_read_next_head(m_ckpt_istream, lgi_record);
          if (lgi_record.tag.id != LGI_CKPT_RANDOM_SEED_TAG)
            msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                               lgi_tag_namestring(LGI_CKPT_RANDOM_SEED_TAG), LGI_CKPT_RANDOM_SEED_TAG,
                               lgi_tag_namestring(lgi_record.tag.id), lgi_record.tag.id);

          // Compare data of the records
          #define COMPARE_LGI_HDF5(VAR_NAME) LocalReader<RT, false>::compare( \
            hdf5_record.VAR_NAME, lgi_record.VAR_NAME, "\"rand state " #VAR_NAME "\"", __FILE__, __LINE__);
          COMPARE_LGI_HDF5(tag.id)
          COMPARE_LGI_HDF5(tag.length)
          COMPARE_LGI_HDF5(n_bytes_rand_state)
          #undef COMPARE_LGI_HDF5
        
          // COMPARE raw data between legacy and hdf5 files
          uint8_t * const lgi_data = new uint8_t[hdf5_rand_state_length];
          uint8_t * const hdf5_data = g_light_data_collection.rand_state_data(sp);
          lgi_read(m_ckpt_istream, lgi_data, hdf5_rand_state_length);
          uint8_t cum_xor = 0;
          for (int nn = 0; nn < hdf5_rand_state_length; ++nn)
          {
            // Check byte per byte
            // std::stringstream ss;
            // ss << "\"raw rand state data\". " << nn << "th byte";
            // LocalReader<sCP_DGF_READER::DEBUG_MODE, false>::compare_no_assert(
            //   lgi_data[nn], hdf5_data[nn], ss.str().c_str(), __FILE__, __LINE__);
            cum_xor |= lgi_data[nn] ^ hdf5_data[nn];
          }
          LocalReader<RT, false>::compare(cum_xor, (uint8_t) 0, "\"raw rand state data\"", __FILE__, __LINE__);
          delete [] lgi_data;
        }

        // Copy data to SPs
        lgi_write_next_head(g_sp_streams[sp], hdf5_record);
        lgi_write(g_sp_streams[sp], g_light_data_collection.rand_state_data(sp), hdf5_rand_state_length);
      }
    }
    // ToCheck: Jump located here instead of internal to the loop to read data from all SPs
    if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
      jump_to_ckpt_file_position(m_ckpt_table_of_contents.eqn_random_seed_pos);
    ccDOTIMES(sp, total_sps)
    {
      if constexpr (RT == sCP_DGF_READER::LEGACY)
      {
        LGI_TAG tag;
        copy_record_to_sp(m_ckpt_istream, LGI_CKPT_EQN_RANDOM_SEED_TAG, sp);
      }
      else
      {
        // Get size of current data. Only saved the rand state
        size_t hdf5_eqn_rand_state_length = g_light_data_collection.eqn_rand_state_size(sp);

        // This record is the equivalent to the legacy one. It has to be created because it does not exist on HDF5
        LGI_RANDOM_SEED_REC hdf5_record;
        asINT32 hdf5_record_length = sizeof(LGI_RANDOM_SEED_REC) + hdf5_eqn_rand_state_length;
        lgi_write_init_tag (&hdf5_record, LGI_CKPT_EQN_RANDOM_SEED_TAG, hdf5_record_length);
        hdf5_record.n_bytes_rand_state = hdf5_eqn_rand_state_length >> 1;

        if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
        {
          // Read legacy record
          LGI_RANDOM_SEED_REC lgi_record;
          lgi_read_next_head(m_ckpt_istream, lgi_record);
          if (lgi_record.tag.id != LGI_CKPT_EQN_RANDOM_SEED_TAG)
            msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                               lgi_tag_namestring(LGI_CKPT_EQN_RANDOM_SEED_TAG), LGI_CKPT_EQN_RANDOM_SEED_TAG,
                               lgi_tag_namestring(lgi_record.tag.id), lgi_record.tag.id);

          // Compare data of the records
          #define COMPARE_LGI_HDF5(VAR_NAME) LocalReader<RT, false>::compare( \
            hdf5_record.VAR_NAME, lgi_record.VAR_NAME, "\"rand-t state " #VAR_NAME "\"", __FILE__, __LINE__);
          COMPARE_LGI_HDF5(tag.id)
          COMPARE_LGI_HDF5(tag.length)
          COMPARE_LGI_HDF5(n_bytes_rand_state)
          #undef COMPARE_LGI_HDF5
        
          // COMPARE raw data between legacy and hdf5 files
          uint8_t * const lgi_data = new uint8_t[hdf5_eqn_rand_state_length];
          uint8_t * const hdf5_data = g_light_data_collection.eqn_rand_state_data(sp);
          lgi_read(m_ckpt_istream, lgi_data, hdf5_eqn_rand_state_length);
          uint8_t cum_xor = 0;
          for (int nn = 0; nn < hdf5_eqn_rand_state_length; ++nn)
          {
            // Check byte per byte
            // std::stringstream ss;
            // ss << "SP: " << sp << ". \"raw rand-t state data\". " << nn << "th byte";
            // LocalReader<sCP_DGF_READER::DEBUG_MODE, false>::compare_no_assert(
            //   lgi_data[nn], hdf5_data[nn], ss.str().c_str(), __FILE__, __LINE__);
            cum_xor |= lgi_data[nn] ^ hdf5_data[nn];
          }
          LocalReader<RT, false>::compare(cum_xor, (uint8_t) 0, "\"raw rand state data\"", __FILE__, __LINE__);
          delete [] lgi_data;
        }

        // Copy data to SPs
        lgi_write_next_head(g_sp_streams[sp], hdf5_record);
        lgi_write(g_sp_streams[sp], g_light_data_collection.eqn_rand_state_data(sp), hdf5_eqn_rand_state_length);
      }
    }
  } else {
    if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
    {
      LGI_TAG tag = lgi_peek_tag(m_ckpt_istream);
      while (tag.id == LGI_CKPT_RANDOM_SEED_TAG
          || tag.id == LGI_CKPT_EQN_RANDOM_SEED_TAG) {
        lgi_skip_record(m_ckpt_istream);
        tag = lgi_peek_tag(m_ckpt_istream);
      }
    }
  }
}
template VOID sCP_DGF_READER::read_ckpt_random_seed_record<sCP_DGF_READER::LEGACY>     (STP_PROC);
template VOID sCP_DGF_READER::read_ckpt_random_seed_record<sCP_DGF_READER::HDF5_SERIAL>(STP_PROC);
template VOID sCP_DGF_READER::read_ckpt_random_seed_record<sCP_DGF_READER::DEBUG_MODE> (STP_PROC);

VOID sCP_DGF_READER::read_ckpt_ublk_dyn_data_header()
{
  LGI_TAG tag;
  lgi_read_next_head(m_ckpt_istream, tag);
  if (tag.id != LGI_SHOB_STATE_TAG)
    msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                       lgi_tag_namestring(LGI_SHOB_STATE_TAG), LGI_SHOB_STATE_TAG,
                       lgi_tag_namestring(tag.id), tag.id);
}

VOID sCP_DGF_READER::read_ckpt_surfel_dyn_data_header()
{
  LGI_TAG tag;
  lgi_read_next_head(m_ckpt_istream, tag);
  if (tag.id != LGI_SHOB_STATE_TAG)
    msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                       lgi_tag_namestring(LGI_SHOB_STATE_TAG), LGI_SHOB_STATE_TAG,
                       lgi_tag_namestring(tag.id), tag.id);
}

static inline STRING read_string(LGI_STREAM stream, asINT32 n_chars)
{
  STRING s = xnew char[n_chars];
  lgi_read(stream, s, n_chars);
  return s;
}

//----------------------------------------------------------------------------
// read_parentage_record
//----------------------------------------------------------------------------
template <sCP_DGF_READER::reader_t RT>
static VOID read_parentage_record_internal(LGI_STREAM stream)
{
  // Creating the reader
  LocalReader<RT, false> reader(initLocalReader<RT>(stream, sim_args.resume_filename,
                                                    "File parentage"));
  // Read headers and check tag of the data from LEGACY
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    // If we haven't yet read the parentage record, then we'll save this one.
    // We'll see a different parentage record from a separate decomposition stream
    // than we will from the undecomposed LGI, and we only want to consider
    // the first (though we want to read past the parentage record in the standard
    // LGI file in order for everything else to continue working right).
    verify_next_tag_id(LGI_PARENTAGE_TAG, stream);
  }
  // Record size (only in HDF5 file)
  {
    size_t record_size;
    reader.read_hdf5(record_size);
  }

  // Pointer to the struct where to save the read data
  const bool is_no_parentage_record_read = cp_info.ptge.disc_platform_n_chars == 0;
  LGI_PARENTAGE_REC * const ptge_ptr = is_no_parentage_record_read ?
                                       &cp_info.ptge : new LGI_PARENTAGE_REC;
  
  // Depending on the rading mode, this lambda function returns the reference to ptge_ptr if
  // this mode is not DEBUG. Otherwise creates an additional variable to compare with.
  auto get_reference_to_ptge = [ptge_ptr]() -> LGI_PARENTAGE_REC*
  {
    if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
      return new LGI_PARENTAGE_REC;
    else
      return ptge_ptr;
  };
  LGI_PARENTAGE_REC * const ptge_redundant_ptr = get_reference_to_ptge();

  // Read from legacy
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
    ptge_redundant_ptr->read(stream);
  // Read from HDF5 (LEGACY do nothing)
  reader.read_hdf5(*ptge_ptr);
  // Compare in DEBUG_MODE. There are only two variables in this mode.
  if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
  {
    #define COMPARE_MEMBER(VAR) LocalReader<RT, false>::compare\
      (ptge_ptr->VAR, ptge_redundant_ptr->VAR, "cp_info.ptge." #VAR, __FILE__, __LINE__)
    COMPARE_MEMBER(tag.id                     );
    COMPARE_MEMBER(tag.length                 );
    COMPARE_MEMBER(case_file_id               );
    COMPARE_MEMBER(case_geometry_id           );
    COMPARE_MEMBER(cdi_file_id                );
    COMPARE_MEMBER(lgi_file_id                );
    // if (lgi_stream_encrypted_version(stream))
    COMPARE_MEMBER(encryption_version_id      );
    COMPARE_MEMBER(disc_n_threads             );
    COMPARE_MEMBER(disc_platform_n_chars      );
    COMPARE_MEMBER(decomp_lgi_file_id         );
    COMPARE_MEMBER(decomp_target_n_chars      );
    COMPARE_MEMBER(decomp_n_procs             );
    COMPARE_MEMBER(powercase_version_n_chars  );
    COMPARE_MEMBER(disc_version_n_chars       );
    COMPARE_MEMBER(decomp_version_n_chars     );
    COMPARE_MEMBER(loadfactors_version_n_chars);
    COMPARE_MEMBER(solver_version             );
    COMPARE_MEMBER(dummy_unused               );
    // if (lgi_stream_has_solver_version(stream)) {
    //   COMPARE_MEMBER(solver_version);
    //   COMPARE_MEMBER(dummy_unused);
    // }
    #undef COMPARE_MEMBER
  }
  // Read additional strings
  if (is_no_parentage_record_read)
  {
    #define READ_STRING(VAR) cp_info.VAR = reader.read_string(cp_info.ptge.VAR ## _n_chars, "cp_info.ptge." #VAR, __FILE__, __LINE__)
    READ_STRING(disc_platform      );
    READ_STRING(decomp_target      );
    READ_STRING(powercase_version  );
    READ_STRING(disc_version       );
    READ_STRING(decomp_version     );
    READ_STRING(loadfactors_version);
    #undef READ_STRING
  }
  else
  {
    // Undecomposed_ptge
    {STRING out = reader.read_string(cp_info.ptge.   disc_platform_n_chars, "cp_info.ptge.disc_platform",       __FILE__, __LINE__); delete [] out;}
    {STRING out = reader.read_string(ptge_ptr->  powercase_version_n_chars, "cp_info.ptge.powercase_version",   __FILE__, __LINE__); delete [] out;}
    {STRING out = reader.read_string(ptge_ptr->       disc_version_n_chars, "cp_info.ptge.disc_version",        __FILE__, __LINE__); delete [] out;}
    {STRING out = reader.read_string(ptge_ptr->     decomp_version_n_chars, "cp_info.ptge.decomp_version",      __FILE__, __LINE__); delete [] out;}
    {STRING out = reader.read_string(ptge_ptr->loadfactors_version_n_chars, "cp_info.ptge.loadfactors_version", __FILE__, __LINE__); delete [] out;}
    // Free allocated memory
    delete ptge_ptr;
  }

  // Free redundant memory
  if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
    delete ptge_redundant_ptr;
}

VOID sCP_DGF_READER::read_parentage_record()
{
  lgi_setpos(m_main_istream, &m_table_of_contents.file_parentage_pos);
  read_parentage_record_internal<sCP_DGF_READER::LEGACY>(m_main_istream);
  lgi_set_geom_encryption(m_main_istream,
                          cp_info.ptge.encryption_version_id,
                          cp_info.ptge.lgi_file_id);

}

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_ckpt_parentage_record()
{
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.file_parentage_pos);
  read_parentage_record_internal<RT>(m_ckpt_istream);
}
template VOID sCP_DGF_READER::read_ckpt_parentage_record<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_ckpt_parentage_record<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_ckpt_parentage_record<sCP_DGF_READER::DEBUG_MODE>();

VOID sCP_DGF_READER::read_decomp_parentage_record()
{
  if (lgi_stream_type(m_decomp_istream) == LGI_FILE_TYPE) {
    lgi_setpos(m_decomp_istream, &m_table_of_contents.file_parentage_pos);
  }

  read_parentage_record_internal<sCP_DGF_READER::LEGACY>(m_decomp_istream);
}

//----------------------------------------------------------------------------
// read_lattice_type_record
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::read_lattice_type_record()
{
  seek_lgi_tag(LGI_LATTICE_TYPE_TAG);
  LGI_LATTICE_TYPE_REC record;
  record.read(m_main_istream);
  cp_info.lattice_type = (STP_LATTICE_TYPE)record.type;

  switch (cp_info.lattice_type) {
  case STP_LATTICE_D34:
    msg_error("The LGI file \"%s\" is compatible with 3.* releases of PowerFLOW", 
              platform_get_file_base_name(cp_info.lgi_filename, NULL));
    break;
  case STP_LATTICE_D25:
    cp_info.lattice_type = STP_LATTICE_D19;
    cp_info.n_lattice_vectors = N_LATTICE_VECTORS_D19;
    cp_info.n_states = N_STATES_D19;
    break;
  case STP_LATTICE_5G:
    cp_info.lattice_type = STP_LATTICE_5G;
    cp_info.n_lattice_vectors = N_LATTICE_VECTORS_D19;
    cp_info.n_states = N_STATES_D19;
    break;
  case STP_LATTICE_D19:
    msg_error("The LGI file \"%s\" is compatible with 4.* releases of PowerFLOW", 
              platform_get_file_base_name(cp_info.lgi_filename, NULL));
    break;
  case STP_LATTICE_D39:
    cp_info.lattice_type = STP_LATTICE_D39;
    cp_info.n_lattice_vectors = N_LATTICE_VECTORS_D39;
    cp_info.n_states = N_STATES_D39;
    break;
  default:
    msg_internal_error("Unrecognized lattice type.");
  }

  // This record is not written to the SPs because there are separate SP
  // executables for each lattice type.
}

//----------------------------------------------------------------------------
// read_global_part_names_record
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::read_global_part_names_record()
{
  seek_lgi_tag(DGF_GLOBAL_PART_NAMES_TAG);

  DGF_GLOBAL_PART_NAMES_REC record;
  record.read(m_main_istream);

  write_header_to_all_sps(record);

  asINT32 n_parts = cp_info.control_rec.num_cdi_regions;
  if (n_parts > record.num_regions) {
    msg_internal_error("Number of CDI parts is greater than the number of parts in the part names record.");
  }
  cp_info.n_sri_parts = n_parts;
  cp_info.sri_parts   = xnew sSRI_PART[n_parts];
  cp_info.fluid_physics_desc_index_from_part_index = xnew sINT32[record.num_regions];

  // Read each part sub-record
  ccDOTIMES(i, record.num_regions) {
    DGF_GLOBAL_PART_NAMES_SUBREC subrec;
    subrec.read(m_main_istream);
    write_to_all_sps(subrec);
    // For measurement files, ignore extra parts generated by the discretizer not in CDI
    if (i < n_parts) {
      cp_info.sri_parts[i].cdi_id = subrec.part_cdi_id;
    }
    cp_info.fluid_physics_desc_index_from_part_index[i] = subrec.cdi_physics_index;
  }

  // Read the part names, which are packed into one giant string
  char *part_names = xnew char[record.total_name_length];
  lgi_read(m_main_istream, part_names, record.total_name_length);

  ccDOTIMES(i, n_parts) {
    cp_info.sri_parts[i].name = part_names;
    part_names += strlen(part_names) + 1;
  }
}

//----------------------------------------------------------------------------
// read_global_face_names_record
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::read_global_face_names_record()
{
  // Skip if this tag isn't present
  LGI_TAG tag = lgi_peek_tag(m_main_istream);
  //if (tag.id != DGF_GLOBAL_FACE_NAMES_TAG) return;

  verify_next_tag_id(DGF_GLOBAL_FACE_NAMES_TAG, m_main_istream);
  DGF_GLOBAL_FACE_NAMES_REC record;
  record.read(m_main_istream);

  write_header_to_all_sps(record);

  asINT32 n_sri_faces = cp_info.control_rec.num_cdi_faces;
  if (n_sri_faces > record.num_faces) {
    msg_internal_error("Number of CDI faces is greater than the number of faces in the face names record.");
  }
  cp_info.n_sri_faces = n_sri_faces;
  SRI_FACE sri_faces = xnew sSRI_FACE[n_sri_faces];
  cp_info.sri_faces = sri_faces;
  cp_info.face_to_parent_part_index_map = xnew asINT32[n_sri_faces]; 
  cp_info.face_index_is_front_only.resize(n_sri_faces);
  cp_info.front_flow_surface_phys_desc_index_from_face_index.resize(n_sri_faces);
  cp_info.front_thermal_surface_phys_desc_index_from_face_index.resize(n_sri_faces);
  cp_info.back_flow_surface_phys_desc_index_from_face_index.resize(n_sri_faces);
  cp_info.back_thermal_surface_phys_desc_index_from_face_index.resize(n_sri_faces);
  ccDOTIMES(i, record.num_faces) {
    DGF_GLOBAL_FACE_NAMES_SUBREC subrec;
    subrec.read(m_main_istream);
    write_to_all_sps(subrec);

    // For measurement files, ignore extra faces generated by the discretizer not in CDI
    if (i < n_sri_faces) {
      sri_faces[i].cdi_id           = subrec.face_cdi_id;
      sri_faces[i].parent_index     = subrec.face_parent_index;
      sri_faces[i].is_parent_a_face = subrec.face_parent_type == LGI_FACE_PARENT_FACE;
      sri_faces[i].surfel_area      = subrec.surfel_area;
      sri_faces[i].n_layers         = subrec.n_layers;
      if(subrec.n_layers == DGF_ZERO_LAYERS_INSULATOR)
        sri_faces[i].n_layers = 0;
      // Sri face considered front if face is FrontOnly or FrontAndBack (i.e. open shell with faces not split)
      sri_faces[i].is_front         = subrec.is_front_only || (subrec.face_cdi_id == subrec.opposite_face_cdi_id);
      sri_faces[i].opposite_face    = subrec.opposite_face_cdi_id;
      cp_info.face_index_is_front_only[i] = subrec.is_front_only;
      cp_info.front_flow_surface_phys_desc_index_from_face_index[i]    = subrec.cdi_front_flow_physics_index;
      cp_info.front_thermal_surface_phys_desc_index_from_face_index[i] = subrec.cdi_front_thermal_physics_index;
      cp_info.back_flow_surface_phys_desc_index_from_face_index[i]    = subrec.cdi_back_flow_physics_index;
      cp_info.back_thermal_surface_phys_desc_index_from_face_index[i] = subrec.cdi_back_thermal_physics_index;
    }
  }

  // Read the face names, which are packed into one giant string
  STRING face_names = xnew char[record.total_name_length];
  lgi_read(m_main_istream, face_names, record.total_name_length);

  ccDOTIMES(i, n_sri_faces) {
    cp_info.sri_faces[i].name = face_names;
    face_names += strlen(face_names) + 1;
  }
}

VOID sCP_DGF_READER::read_dgf_part_protection_subrec(){

  // Skip if this tag isn't present
  LGI_TAG tag = lgi_peek_tag(m_main_istream);
  verify_next_tag_id(DGF_GLOBAL_PART_PROTECTION_TAG, m_main_istream);

  cDGF_PART_PROTECTION pprot;
  pprot.read(m_main_istream);

  //Use LGI information to verify CDI encryption
  cp_info.encryption_struct.verify_encryption_version_from_lgi((int) cp_info.ptge.encryption_version_id);
  cp_info.encryption_struct.verify_part_protection_ids_from_lgi(pprot.part_protection_ids);
}

//----------------------------------------------------------------------------
// read_decomp_control_record
//----------------------------------------------------------------------------

VOID sCP_DGF_READER::read_decomp_control_record()
{
  LGI_STREAM stream = m_decomp_istream;
  if (lgi_stream_type(stream) == LGI_FILE_TYPE)
    lgi_setpos(stream, &m_table_of_contents.decomp_control_pos);
  verify_next_tag_id(DGF_DECOMPOSITION_CONTROL_TAG, stream);  
  m_decomp_control_rec.read(stream);
  cp_info.parallel_io_one_write_buffer_size_mb = m_decomp_control_rec.parallel_io_one_write_buffer_size_mb;
}

//----------------------------------------------------------------------------
// read_control_record
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::read_control_record()
{
  jump_to_file_position(m_table_of_contents.control_pos);
  cp_info.control_rec.read(m_main_istream);
  cp_info.control_rec.tag.length = 0;
  if (cp_info.control_rec.num_shob_types != STP_N_SHOB_TYPES)
    msg_internal_error("Unexpected number of shob types");

  cp_info.num_scales = cp_info.control_rec.num_scales;
  cp_info.coarsest_coupled_scale = cp_info.control_rec.coarsest_coupled_scale;

  if (cp_info.num_scales > STP_MAX_SCALES) {
    msg_internal_error("LGI file exceeds simulator limit on the number of scales %d.",
                       STP_MAX_SCALES);
  }

  ccDOTIMES(axis, 3) {
    cp_info.simvol_size[axis] = cp_info.control_rec.simv_size[axis];
  }
}

static STP_PROC *source_sp_counts;

// Pick up where read_control_record left off.
VOID sCP_DGF_READER::send_control_record()
{
  write_header_to_all_sps(cp_info.control_rec);

  source_sp_counts = cnew STP_PROC[total_sps * cp_info.num_scales];

  // reserve space for storing the per-surfel and per-ublk processor ids
  cp_info.num_ublks[STP_FLOW_REALM]    = cp_info.control_rec.num_flow_ublks;
  cp_info.num_ublks[STP_COND_REALM]    = cp_info.control_rec.num_cond_ublks;
  cp_info.num_surfels[STP_FLOW_REALM]  = cp_info.control_rec.num_flow_surfels;
  cp_info.num_surfels[STP_COND_REALM]  = cp_info.control_rec.num_cond_surfels;
  cp_info.num_bsurfels  = cp_info.control_rec.num_bsurfels;

  g_cp_dynblks.reserve(cp_info.num_ublks[STP_FLOW_REALM]);

  if (total_sps > 1) {
    DO_ACTIVE_REALMS(realm) {
      cp_info.ublk_procs[realm].reserve(cp_info.num_ublks[realm]);
      cp_info.surfel_procs[realm].reserve(cp_info.num_surfels[realm]);
    }
  }


  // If there are multiple SPs, skip the discretizer's shob counts and read
  // the contents from the decomposer record
  LGI_STREAM control_stream = (total_sps > 1) ? m_decomp_istream : m_main_istream;

  // Read and forward cDGF_SHOB_COUNTS record

  DO_REALMS(realm) {
    ccDOTIMES (sp, total_sps) {
      ccDOTIMES(scale, cp_info.num_scales) {
        cDGF_SHOB_COUNTS shob_counts;
        shob_counts.read(control_stream);
        shob_counts.write(g_sp_streams[sp]);
      }
    }
  }

  return;
}

//----------------------------------------------------------------------------
// read_table_of_contents
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::read_table_of_contents_record(bool is_legacy)
{
  seek_lgi_tag(DGF_TOC_TAG);

  //verify_next_tag_id(DGF_TOC_TAG);
  m_table_of_contents.read(m_main_istream);

  /* This initial setting is only appropriate for LGI files
   * that contain both the primary LGI information and the decomposition
   * information.  Streamed decomposer output should start in the right
   * place - a setpos applied on a stream would fail */
  if (lgi_stream_type(m_decomp_istream) == LGI_FILE_TYPE) {
    lgi_setpos(m_decomp_istream, &m_table_of_contents.audit_trail_pos);
  }

  if (cp_info.is_full_checkpoint_restore && is_legacy) {
    seek_ckpt_tag(DGF_CKPT_TOC_TAG);
    m_ckpt_table_of_contents.read(m_ckpt_istream);
  }
    
}

//----------------------------------------------------------------------------
// create_surfel_mesh
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::create_surfel_mesh()
{
  if (cp_info.native_mesh != NULL)
    msg_internal_error("Multiple attempts to create the native mesh");

  cp_info.native_model = new cMIO_MODELf();

  if (!cp_info.native_model)
    msg_internal_error("Failure to create surfel mesh");

  cp_info.native_model->SetWIP(&cp_info.native_model_wip);
  cp_info.native_mesh = cp_info.native_model->CreateMesh();

  if (!cp_info.native_mesh)
    msg_internal_error("Unable to create surfel mesh");
}

//----------------------------------------------------------------------------
// read_vertices
//----------------------------------------------------------------------------
VOID sCP_DGF_READER::read_vertex_coordinates()
{
  jump_to_file_position(m_table_of_contents.vertex_pos);

  verify_next_tag_id(DGF_VERTICES_TAG, m_main_istream);
  cDGF_VERTICES record;
  record.read(m_main_istream);



  //Vertex information might be encrypted. Turning this global variable
  //on in LGI uses the cipher, if any, to decrypt vertices before they
  //are read
  lgi_encrypt_stream(m_main_istream,true);

  asINT32 n_dims = cp_info.control_rec.num_dimensions;
  if (cp_info.n_surface_couplings > 0) {
#if SURF_COUP
    cp_info.vertex_array.reserve(record.num_surfel_vertices);
    // Read in surfel vertices and add to mesh object
    create_surfel_mesh();
    asINT32 n_dims = cp_info.control_rec.num_dimensions;
    cMIO_MODELf::cMESH *surfel_brep = cp_info.native_mesh;
    ccDOTIMES(vertex, record.num_surfel_vertices) {
      if (n_dims == 3) {
        cDGF_VERTEX_3D_POINT p;
        p.read(m_main_istream);
        sVPoint vp = {p.vtx_coord[0], p.vtx_coord[1], p.vtx_coord[2]};
        cp_info.vertex_array.push_back(vp);
        surfel_brep->AddVertex(p.vtx_coord[0], p.vtx_coord[1], p.vtx_coord[2]);
      }
      else {//num dimensions = 2
        cDGF_VERTEX_2D_POINT p;
        p.read(m_main_istream);
        sVPoint vp = {p.vtx_coord[0], p.vtx_coord[1], 0.0F};
        cp_info.vertex_array.push_back(vp);
        surfel_brep->AddVertex(p.vtx_coord[0], p.vtx_coord[1], 0.0F);
      }
    }
    BuildNativeModel();
#endif
  }
  else {
    cp_info.vertex_array.reserve(record.num_surfel_vertices);
    ccDOTIMES(vertex, record.num_surfel_vertices) {
      if (n_dims == 3) {
        cDGF_VERTEX_3D_POINT p;
        p.read(m_main_istream);
        sVPoint vp = {p.vtx_coord[0], p.vtx_coord[1], p.vtx_coord[2]};
        cp_info.vertex_array.push_back(vp);
      }
      else {//num dimensions = 2
        cDGF_VERTEX_2D_POINT p;
        p.read(m_main_istream);
        sVPoint vp = {p.vtx_coord[0], p.vtx_coord[1], 0.0F};
        cp_info.vertex_array.push_back(vp);
      }
    }
  }

  if (cp_info.is_particle_solver || cp_info.is_shell_conduction_solver) {
    record.tag.length = lgi_pad_and_encode_record_length(sizeof(record) + record.num_fpoly_vertices*sizeof(cDGF_VERTEX_2D_POINT));
    write_header_to_all_sps(record);
    cp_info.n_original_surfel_vertices = record.num_surfel_vertices; // before adding back vertices
    ccDOTIMES(vertex, record.num_fpoly_vertices) {
      cDGF_VERTEX_2D_POINT p;
      p.read(m_main_istream);
      write_to_all_sps(p);
    }
  }
  lgi_encrypt_stream(m_main_istream, false);
}

// The master_sp for a meas window is the SP that calculates meas window related ref
// frame info and sends it to the CP along with each frame of data.
VOID sCP_DGF_READER::send_meas_window_master_sps_and_output_times()
{
  LGI_MEAS_WINDOW_MASTER_SP_HEADER record;
  record.tag.id = LGI_MEAS_WINDOW_MASTER_SP_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(sizeof(record)
                                                       + cp_info.n_meas_windows() * sizeof(LGI_MEAS_WINDOW_MASTER_SP));
  write_header_to_all_sps(record);

  // Fairly distribute master responsibilities to all SPs. Note that the workload for a
  // probe on its master SP is light (perhaps zero), so we distribute these independently.
  // We only want to assign master responsibilities for a meas window to an SP that actually
  // contributes to that meas window since SPs prefer to disavow all knowledge of meas
  // windows to which they do not contribute.
  asINT32 probe_master_sp = 0;
  asINT32 non_probe_master_sp = 0;
  ccDOTIMES(i, cp_info.n_meas_windows()) {
    CP_MEAS_WINDOW window = cp_info.meas_windows[i];
    LGI_MEAS_WINDOW_MASTER_SP subrec;
    if (window) {

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
      if(window->m_is_particle_trajectory_window) {
        subrec.master_sp = -1; //no checkpointing for trajectory windows, master_sp set to -1.
        window->m_master_sp = subrec.master_sp; 
      } else {
//#endif
        asINT32 &master_sp = window->is_probe ? probe_master_sp : non_probe_master_sp;
        
        if (window->m_sp_n_meas_cells[master_sp] > 0 || window->n_moving_meas_cells > 0) {
          subrec.master_sp = master_sp++;
          if (master_sp >= total_sps)
            master_sp = 0;
        } else if (!window->is_average_mme) {
          // Find the first non empty meas cell and the first SP for that cell. Only for
          // dev windows can meas cells be empty.
          asINT32 first_sp_housing_a_meas_cell = -1;
          for (sriPOINT i = 0; i < window->n_meas_cells; i++) {
            asINT32 sp = window->first_sp_housing_meas_cell(i);
            if (sp >= 0) {
              first_sp_housing_a_meas_cell = sp;
              break;
            }
            
          }
          subrec.master_sp = first_sp_housing_a_meas_cell;
        } else { // avg mme window
          subrec.master_sp = -1;
        }
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
      }
//#endif
      window->m_master_sp = subrec.master_sp;

      subrec.time_desc           = window->meas_window_type == LGI_VOLUME_WINDOW 
                                || window->meas_window_type == LGI_SHELL_WINDOW 
                                || window->meas_window_type == LGI_SAMPLING_SHELL_WINDOW 
                                 ? window->cdi_meas_window->cond_time_desc : window->cdi_meas_window->fluid_time_desc;
      subrec.initial_clear_time  = window->m_clear_timestep;
      subrec.initial_output_time = window->m_output_timestep;
      subrec.next_clear_time     = window->m_next_update_time.clear_time;
      subrec.next_output_time    = window->m_next_update_time.output_time;
    }
    else {
      subrec.master_sp = -1;
    }

    if (window && window->coupling_window_p)
    {
      asINT32 model_index = window->coupling_model_index;
      SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
      CDI_CMDL cmdl = surface_coupling->get_cmdl();
      subrec.n_meas_phases = cmdl->n_coupling_phases;
      subrec.end_time = cmdl->end_time;
    } else {
      subrec.n_meas_phases = 0;
      // Use end_time as duration if the avg mme window is started via monitors
      if (window && window->is_average_mme)
        subrec.end_time = window->cdi_meas_window->duration;
      else
        subrec.end_time = cp_info.end_time;
    }
    write_to_all_sps(subrec);

    asINT32 n_user_base_steps = cp_info.n_user_base_steps;
    if (window && window->coupling_window_p)
    {
      SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + window->coupling_model_index;
      CDI_CMDL cmdl = surface_coupling->get_cmdl();

      cDGF_MEAS_PHASE dgf_meas_phase = { 0 };
      dgf_meas_phase.tag.id = DGF_MEAS_PHASE_TAG;
      asINT32 n_coupling_phase = cmdl->m_coupling_phase_descs.size();
      ccDO_FROM_TO(phase_index, 0, n_coupling_phase-1) {
        CDI_COUPLING_PHASE_DESC coupling_phase_desc = &cmdl->m_coupling_phase_descs[phase_index];  //in base steps

        dgf_meas_phase.next_output_time = coupling_phase_desc->start;  // in user's steps
        dgf_meas_phase.period = coupling_phase_desc->period;           // in user's steps
        dgf_meas_phase.interval = coupling_phase_desc->interval;       // in user's steps

        // If there are at least two phases, then the number of meas intervals is (num_couplings + 1) for the 1st phase. Otherwise it is num_couplings
        // If it is not the last phase, num_meas = num_couplings
        // If it is the last phase, num_meas = (num_couplings - 1)
        if (phase_index == 0) {
          if ( n_coupling_phase == 1 )
            // (end_time - delay) is the last PT launch time
            dgf_meas_phase.repeat = (cmdl->end_time - coupling_phase_desc->delay - (coupling_phase_desc->start)) / coupling_phase_desc->period;
          else
            dgf_meas_phase.repeat = (cmdl->m_coupling_phase_descs[1].start - coupling_phase_desc->start)/coupling_phase_desc->period + 1;
        } else {
          if (phase_index == (n_coupling_phase - 1))
            // (end_time - delay) is the last PT launch time
            dgf_meas_phase.repeat = (cmdl->end_time - coupling_phase_desc->delay - coupling_phase_desc->start)/coupling_phase_desc->period;
          else
            dgf_meas_phase.repeat = (cmdl->m_coupling_phase_descs[phase_index+1].start - coupling_phase_desc->start)/coupling_phase_desc->period;
        }
        // Send meas phase to all the SPs
        ccDOTIMES(sp, total_fsps) {
          dgf_meas_phase.write(g_sp_streams[sp], FALSE);
        }
      }
    }
  }
}

VOID sCP_DGF_READER::send_meas_vars_to_sps() {

  DO_CP_MEAS_WINDOWS(meas_window) {
    if (meas_window->coupling_window_p || meas_window->is_average_mme)
      continue;
    BOOLEAN is_x_force_output = FALSE;
    BOOLEAN is_y_force_output = FALSE;
    BOOLEAN is_z_force_output = (cp_info.n_dims == 2);
    asINT32 n_vars = meas_window->n_variables;
    ccDOTIMES(j, n_vars) {
      if (meas_window->var_types[j] == SRI_VARIABLE_XFORCE) 
        is_x_force_output = TRUE;
      else if (meas_window->var_types[j] == SRI_VARIABLE_YFORCE)
        is_y_force_output = TRUE;
      else if (meas_window->var_types[j] == SRI_VARIABLE_ZFORCE)
        is_z_force_output = TRUE;
    }
    BOOLEAN all_forces_are_output = is_x_force_output &&
                                    is_y_force_output &&
                                    is_z_force_output;
    if (all_forces_are_output) {
      BOOLEAN all_meas_cells_in_global_ref_frame = TRUE;
      if ((cp_info.n_lrfs() > 0) &&
          (meas_window->meas_window_type == LGI_SURFACE_WINDOW || meas_window->meas_window_type == LGI_SHELL_WINDOW) &&
          !meas_window->is_composite &&
          !meas_window->is_development &&
          !meas_window->is_output_in_local_csys) {
        ccDOTIMES(mci, ((CP_SURFACE_MEAS_WINDOW)meas_window)->m_n_surfels) {
          sriLRF_INDEX ref_frame_index = meas_window->m_ref_frame_indices.at(mci);
          BOOLEAN is_meas_cell_in_sliding_rf = (ref_frame_index >= 0) &&
                                               (cp_info.sri_lrfs[ref_frame_index].type == SRI_LRF_MOVING);
          if (is_meas_cell_in_sliding_rf) {
            all_meas_cells_in_global_ref_frame = FALSE;
      	    break;
      	  }
        }
      }
      if (!all_meas_cells_in_global_ref_frame) {
        SRI_VARIABLE_TYPE *var_types = xnew SRI_VARIABLE_TYPE[n_vars+1];
        sINT16 *population_var_indices = xnew sINT16[n_vars+1];
        ccDOTIMES(j, n_vars) {
          var_types[j] = meas_window->var_types[j];
          population_var_indices[j] = meas_window->population_var_indices[j];
        }
        var_types[n_vars] = SRI_VARIABLE_TFORCE_MAG;
        population_var_indices[n_vars] = -1;
        n_vars++;
        delete [] meas_window->var_types;
        delete [] meas_window->population_var_indices;
        meas_window->var_types = var_types;
        meas_window->population_var_indices = population_var_indices;
        meas_window->n_variables = n_vars;
      }
    }

    write_meas_window_vars_to_sps(meas_window);
  }
}

#define ACCUMULATE_MEAS_CELL_REF_FRAME_DATA()                                 \
  if (window->m_ref_frame_indices.at(mci) == SRI_OVERLAP_REF_FRAME_INDEX) {      \
    meas_cells_span_ref_frames[sp] |= 1 << n_meas_cells_accumulated[sp];      \
  }                                                                           \
  n_meas_cells_accumulated[sp]++;                                             \
  if (n_meas_cells_accumulated[sp] == N_MEAS_CELLS_PER_SP) {                  \
    lgi_write(g_sp_streams[sp],meas_cells_span_ref_frames+sp,sizeof(uINT64)); \
    meas_cells_span_ref_frames[sp] = 0;                                       \
    n_meas_cells_accumulated[sp] = 0;                                         \
  }

VOID  sCP_DGF_READER::send_meas_cell_reference_frame_conflicts()
{
  LGI_MEAS_CELL_REFERENCE_FRAME_CONFLICTS conflicts;
  conflicts.tag.id = LGI_MEAS_CELL_REFERENCE_FRAME_CONFLICTS_TAG;
  // The size of the record not known a priori
  conflicts.tag.length = 0;
  write_header_to_all_sps(conflicts);

  if (cp_info.n_lrfs() <= 0) return;

  const sINT8 N_MEAS_CELLS_PER_SP = 64;
  uINT64 *meas_cells_span_ref_frames = cnew uINT64 [total_sps];
  uINT8 *n_meas_cells_accumulated    = cnew uINT8  [total_sps];
  DO_CP_MEAS_WINDOWS(window) {
    if (!window->is_composite) continue;
    if (!window->is_output_in_local_csys) continue;
    ccDOTIMES(mci,window->n_stationary_meas_cells) {
      sCP_MEAS_WINDOW::uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &window->m_sp_meas_cell_refs[mci];

      if (ref_or_list->is_list()) {
        sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF_LIST_ELT *list = ref_or_list->list();
        sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF          ref   = list->m_ref;
        STP_PROC                                    sp    = ref.sp();

        ACCUMULATE_MEAS_CELL_REF_FRAME_DATA();
        list = list->m_next; // jump to second ref

        while (list != NULL) {
          sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = list->m_ref;
          STP_PROC                           sp  = ref.sp();
          ACCUMULATE_MEAS_CELL_REF_FRAME_DATA();
          list = list->m_next;
        }
      } else {
        sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = ref_or_list->ref();
        STP_PROC                           sp  = ref.sp();
        ACCUMULATE_MEAS_CELL_REF_FRAME_DATA();
      }
      // This should be changed after data has been sent to all the sps
      if (window->m_ref_frame_indices.at(mci) == SRI_OVERLAP_REF_FRAME_INDEX)
        window->m_ref_frame_indices.at(mci) = SRI_GLOBAL_REF_FRAME_INDEX;
    }

    ccDOTIMES (sp, total_sps) {
      // send only if number of cells > 0
      if (n_meas_cells_accumulated[sp] > 0 ) {
        lgi_write(g_sp_streams[sp],meas_cells_span_ref_frames+sp,sizeof(uINT64));
        meas_cells_span_ref_frames[sp] = 0;
        n_meas_cells_accumulated[sp] = 0;
      }
    }
  }
  delete [] meas_cells_span_ref_frames;
  delete [] n_meas_cells_accumulated;
}

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_cdi_meas_window_ckpt_data()
{
  // Open HDF5 file and access to data
  LocalReader<RT, false> reader(initLocalReader<RT>(m_ckpt_istream, sim_args.resume_filename, "CDI Measurement windows"));

  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.cdi_meas_window_pos);
    LGI_TAG tag;
    lgi_read_next_head(m_ckpt_istream, tag);
    if (tag.id != DGF_CKPT_CDI_MEAS_WINDOW_TAG)
      msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                         lgi_tag_namestring(DGF_CKPT_CDI_MEAS_WINDOW_TAG), DGF_CKPT_CDI_MEAS_WINDOW_TAG,
                         lgi_tag_namestring(tag.id), tag.id);
  }
  
  // Record size (only in HDF5 file)
  {
    size_t record_size;
    reader.read_hdf5(record_size);
  }

  // Read number of measurement windows
  sINT32 n_cdi_meas_windows;
  READ_GENERIC_VAR_FROM_FILE(n_cdi_meas_windows);

  // Error when there is a discrepancy between CDI and HDF5 files
  if (n_cdi_meas_windows != cp_info.n_non_surf_coupling_cdi_meas_windows)
    msg_internal_error("Number of measurement windows in checkpoint file (%d) "
                       "does not match number in CDI file (%d).",
                       n_cdi_meas_windows, cp_info.n_non_surf_coupling_cdi_meas_windows);

  ccDOTIMES(i, n_cdi_meas_windows) {
    CDI_MEAS_WINDOW cdi_win = cp_info.cdi_meas_windows + i;
    DGF_CKPT_CDI_MEAS_WINDOW ckpt_cdi_win;
    if constexpr (RT == sCP_DGF_READER::DEBUG_MODE)
    {
      // When comparing, there is unused memory space in DGF_CKPT_CDI_MEAS_WINDOW that is saved,
      // but it can be different each time tha data is written, so a direct comparison on the
      // memory loaded can show a false error. Consequently member per member comparison is done
      DGF_CKPT_CDI_MEAS_WINDOW ckpt_cdi_win1;
      reader.read_hdf5(ckpt_cdi_win);
      reader.read_lgi(ckpt_cdi_win1);
      #define COMPARE_MEMBER(VAR) LocalReader<RT, false>::compare\
        (ckpt_cdi_win.VAR, ckpt_cdi_win1.VAR, "ckpt_cdi_win." #VAR, __FILE__, __LINE__)
      COMPARE_MEMBER(overridden_by_meas_timing);
      COMPARE_MEMBER(disabled_by_meas_include_exclude);
      COMPARE_MEMBER(min_start_time);
      COMPARE_MEMBER(start_time);
      COMPARE_MEMBER(end_time);
      COMPARE_MEMBER(average_interval);
      COMPARE_MEMBER(period);
      COMPARE_MEMBER(is_meas_started);
      if (cdi_win->is_average_mme)
      {
        COMPARE_MEMBER(is_requested_to_stop);
        COMPARE_MEMBER(requested_stop_time);
        COMPARE_MEMBER(is_scheduled_to_stop);
        COMPARE_MEMBER(scheduled_stop_time);
        COMPARE_MEMBER(is_meas_stopped);
      }
      #undef COMPARE_MEMBER
    }
    else
      READ_GENERIC_VAR_FROM_FILE(ckpt_cdi_win);
    
    //READ_GENERIC_VAR_FROM_FILE(ckpt_cdi_win);
    cdi_win->meas_started_p            = ckpt_cdi_win.is_meas_started;
    if (cdi_win->is_average_mme) {
      if (ckpt_cdi_win.is_requested_to_stop) {
        cp_info.request_to_stop_avg_mme = TRUE;
        cp_info.requested_time_to_stop_avg_mme = ckpt_cdi_win.requested_stop_time;
      } else if (ckpt_cdi_win.is_scheduled_to_stop) {
        cp_info.request_to_stop_avg_mme = FALSE;
        cp_info.time_to_stop_avg_mme = ckpt_cdi_win.scheduled_stop_time;
      }
      cp_info.average_mme_stopped      = ckpt_cdi_win.is_meas_stopped;
     
      // Avg mme window may be rescheduled before the ckpt
      cdi_win->start_time = ckpt_cdi_win.start_time;
      cdi_win->end_time   = ckpt_cdi_win.end_time;

#if DEBUG_AVG_MME
      msg_print("CKPT: is_requested_to_stop %d requested_time_to_stop_avg_mme %d is_scheduled_to_stop %d time_to_stop_avg_mme %d average_mme_stopped %d",
                ckpt_cdi_win.is_requested_to_stop, cp_info.requested_time_to_stop_avg_mme, ckpt_cdi_win.is_scheduled_to_stop, cp_info.time_to_stop_avg_mme,
                cp_info.average_mme_stopped);
      msg_print("CKPT: start %f end %f period %f", ckpt_cdi_win.start_time, ckpt_cdi_win.end_time, ckpt_cdi_win.period);
#endif
    }

    if (cdi_win->start_time_via_monitors_p && cp_info.is_full_checkpoint_restore) {
      if (cdi_win->meas_started_p) {
        // avg mme is already started by monitors before ckpt. When resuming the avg mme 
        // meas window is actually started by time
        cdi_win->start_time_via_monitors_p = FALSE;
        cp_info.avg_mme_ckpt_after_eit_p = TRUE;
     
        // Possibly stop the simulation at greatest meas window end time
        if (cp_info.sim_duration_via == eCDI_SIM_DURATION_VIA::GreatestMeasurementEndTime) {
          cp_info.greatest_meas_window_end_time = MAX(cp_info.greatest_meas_window_end_time,
                                                      cdi_win->end_time);
          if (--cp_info.n_meas_windows_to_check_end_time == 0) {
            msg_print("Find greatest meas window end time %d", cp_info.greatest_meas_window_end_time);
            if ((TIMESTEP)cp_info.end_time == TIMESTEP_MAX) { // If current end time is set by user (either from exaqsub or from exasignal) 
                                          // do not request to stop. See PR41559.
              cp_info.request_to_exit_by_greatest_meas_window_end_time = TRUE;
            }
          }
        }
#if DEBUG_AVG_MME
        msg_print("RESUME FROM CKPT: avg mme window is already started in the previous run.");
#endif
      }
    }

    if (ckpt_cdi_win.overridden_by_meas_timing
        // If the user provided a new --meas_include or --meas_exclude, we ignore the content of the ckpt file
        || (ckpt_cdi_win.disabled_by_meas_include_exclude && cp_info.cmdline_include_exclude_meas_windows == NULL)) {
      cdi_win->overridden_by_meas_timing = ckpt_cdi_win.overridden_by_meas_timing;
      cdi_win->disabled_by_meas_include_exclude = ckpt_cdi_win.disabled_by_meas_include_exclude;
      cdi_win->start_time                = ckpt_cdi_win.start_time;
      cdi_win->end_time                  = ckpt_cdi_win.end_time;
      cdi_win->average_interval          = ckpt_cdi_win.average_interval;
      cdi_win->period                    = ckpt_cdi_win.period;
      cdi_win->min_start_time            = ckpt_cdi_win.min_start_time;
    }
    else if (ckpt_cdi_win.disabled_by_meas_include_exclude
             && !cdi_win->disabled_by_meas_include_exclude) {
      // start_time cannot be earlier than checkpoint restore time
      cdi_win->start_time                = cp_info.restart_time;
      cdi_win->min_start_time            = cp_info.restart_time;
    }
  }
}

template VOID sCP_DGF_READER::read_cdi_meas_window_ckpt_data<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_cdi_meas_window_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_cdi_meas_window_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();

template <sCP_DGF_READER::reader_t RT> struct MeasWinLocalReader;

template <>
struct MeasWinLocalReader<sCP_DGF_READER::LEGACY>
{
  MeasWinLocalReader(MeasWinLocalReader&) = delete;
  MeasWinLocalReader(LGI_STREAM& s) : stream(s) {}
  void open_window(int) {}
  void close_window() {}
  template <typename T> void read_cp(T& __restrict in, const char*, const char*, int) {lgi_read(stream, in);}
  template <typename T> void read_sp(T& __restrict in, const char*, const char*, int) {lgi_read(stream, in);}
  template <typename T> void read_cp(T* __restrict const in, size_t size, const char*, const char*, int) {lgi_read(stream, in, size);}
  template <typename T> void read_sp(T* __restrict const in, size_t size, const char*, const char*, int) {lgi_read(stream, in, size);}
  LGI_STREAM& stream;
};

template <>
struct MeasWinLocalReader<sCP_DGF_READER::HDF5_SERIAL>
{
  MeasWinLocalReader(MeasWinLocalReader&) = delete;
  MeasWinLocalReader(const char * const hdf5_file_name) :
    reader_cp(g_hdf5_reader.open_shob(hdf5_file_name, "CP Measurement windows", sCP_DGF_READER::HDF5_SERIAL)) {}
  ~MeasWinLocalReader()
  {
    reader_cp.close();
    reader_sp.close();
  }
  void open_window(int idx)
  {
    std::stringstream ss;
    ss << "Win_" << idx;
    reader_sp.open(sCP_DGF_READER::HDF5_SERIAL, g_hdf5_reader.file_, "SPs Measurement windows", ss.str());
  }
  void close_window()
  {
    reader_sp.close();
  }
  template <typename T> void read_cp(T& __restrict in, const char*, const char*, int){reader_cp.read(&in);}
  template <typename T> void read_sp(T& __restrict in, const char*, const char*, int){reader_sp.read(&in);}
  template <typename T> void read_cp(T * __restrict const in, size_t size, const char*, const char*, int) {reader_cp.read(in, size);}
  template <typename T> void read_sp(T * __restrict const in, size_t size, const char*, const char*, int) {reader_sp.read(in, size);}
  hdf5_shob reader_cp;
  hdf5_shob reader_sp;
};


template <>
struct MeasWinLocalReader<sCP_DGF_READER::DEBUG_MODE>
{
  MeasWinLocalReader(MeasWinLocalReader&) = delete;
  MeasWinLocalReader(LGI_STREAM& __restrict s, const char * const hdf5_file_name) :
    stream(s),
    reader_cp(g_hdf5_reader.open_shob("CP Measurement windows", sCP_DGF_READER::DEBUG_MODE)) {}
  ~MeasWinLocalReader()
  {
    reader_cp.close();
    reader_sp.close();
  }
  void open_window(int idx)
  {
    std::stringstream ss;
    ss << "Win_" << idx;
    reader_sp.open(sCP_DGF_READER::DEBUG_MODE, g_hdf5_reader.file_, "SPs Measurement windows", ss.str().c_str());
  }
  void close_window()
  {
    reader_sp.close();
  }
  template <typename T>
  void read_cp(T& __restrict in, const char * const v, const char * const f, int l)
  {
    reader_cp.read(&in);
    compare(in, v, f, l);
  }
  template <typename T>
  void read_sp(T& __restrict in, const char * const v, const char * const f, int l)
  {
    reader_sp.read(&in);
    compare(in, v, f, l);
  }
  template <typename T>
  void read_cp(T * __restrict const in, size_t size, const char * const v, const char * const f, int l)
  {
    reader_cp.read(in, size);
    compare(in, size, v, f, l);
  }
  template <typename T>
  void read_sp(T * __restrict const in, size_t size, const char * const v, const char * const f, int l)
  {
    reader_sp.read(in, size);
    compare(in, size, v, f, l);
  }
  template <typename T>
  void compare(T& __restrict in, const char * const v, const char * const f, int l)
  {
    T in_legacy;
    lgi_read(stream, in_legacy);
    using print_val_t = typename std::conditional<std::is_integral<T>::value
      && sizeof(T) == 1, int, T>::type;
    if (in == in_legacy)
    {
      std::stringstream in2str;
      in2str << (print_val_t) in;
      print_mssg<message_type::SUCCESS>("Read variable %s: %s", v, in2str.str().c_str());
    }
    else
    {
      std::stringstream in2str0, in2str1;
      in2str0 << (print_val_t) in;
      in2str1 << (print_val_t) in_legacy;
      print_mssg<message_type::ERROR>("Error when reading variable %s: %s vs %s.\n%s:%i",
        v, in2str0.str().c_str(), in2str1.str().c_str(), f, l);
    }
    assert(in == in_legacy);
  }
  template <typename T>
  void compare(T * __restrict const in, size_t size, const char * const v, const char * const f, int l)
  {
    uint8_t * __restrict const in_raw = (uint8_t*) in;
    uint8_t * __restrict const in_legacy = new uint8_t[size];
    lgi_read(stream, in_legacy, size);
    uint8_t cum_or = 0;
    for (size_t nn = 0; nn < size; ++nn)
      cum_or |= in_raw[nn] ^ in_legacy[nn];
    if (cum_or != 0)
      print_mssg<message_type::ERROR>("Error when reading array %s.\n %s:%i", v, f, l);
    assert(cum_or == 0);
  }
  LGI_STREAM& stream;
  hdf5_shob reader_cp;
  hdf5_shob reader_sp;
};

#define READ_MEAS_WIN_VAR_CP(VARNAME)         reader.read_cp(VARNAME,       #VARNAME, __FILE__, __LINE__);
#define READ_MEAS_WIN_VAR_SP(VARNAME)         reader.read_sp(VARNAME,       #VARNAME, __FILE__, __LINE__);
#define READ_MEAS_WIN_ARRAY_CP(VARNAME, SIZE) reader.read_cp(VARNAME, SIZE, #VARNAME, __FILE__, __LINE__);
#define READ_MEAS_WIN_ARRAY_SP(VARNAME, SIZE) reader.read_sp(VARNAME, SIZE, #VARNAME, __FILE__, __LINE__);

// read_meas_window_ckpt_data assumes that meas cell data is found in the checkpoint file in global
// meas cell order. The logic in this routine must parallel that found in write_meas_windows,
// which writes meas windows to a full checkpoint file.
template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_meas_window_ckpt_data(asINT32 total_ckpt_sps,
                                                std::unordered_map<STP_MEAS_WINDOW_INDEX,std::vector<STP_PROC> >& moving_meas_cell_ckpt_sp_map)
{
  LGI_TAG tag;
  if constexpr (RT == LEGACY || RT == DEBUG_MODE)
  {
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.meas_window_pos);
    lgi_read_next_head(m_ckpt_istream, tag);
    if (tag.id != DGF_CKPT_MEAS_WINDOW_TAG)
      msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                         lgi_tag_namestring(DGF_CKPT_MEAS_WINDOW_TAG), DGF_CKPT_MEAS_WINDOW_TAG,
                         lgi_tag_namestring(tag.id), tag.id);
  }
  if constexpr (RT == HDF5_SERIAL || RT == DEBUG_MODE)
  {
    tag.id = DGF_CKPT_MEAS_WINDOW_TAG;
  }
  tag.length = 0; // variable length
  write_header_to_all_sps(tag);

  // Lambda function for initialization of the reader
  auto initReader = [this]() -> MeasWinLocalReader<RT>
  {
    if constexpr (RT == LEGACY)
      return {m_ckpt_istream};
    else if constexpr (RT == HDF5_SERIAL)
      return {sim_args.resume_filename};
    else if constexpr (RT == DEBUG_MODE)
      return {m_ckpt_istream, sim_args.resume_filename};
    else
      assert(false);
  };

  // Reader
  MeasWinLocalReader<RT> reader(initReader());
  if constexpr (RT == HDF5_SERIAL || RT == DEBUG_MODE)
  {
    size_t len;
    reader.reader_cp.read(&len);
  }

  STP_MEAS_WINDOW_INDEX n_ckpt_meas_windows;
  READ_MEAS_WIN_VAR_CP(n_ckpt_meas_windows);
  if (n_ckpt_meas_windows != cp_info.n_meas_windows())
    msg_error("Inconsistency in number of measurement windows between LGI and checkpoint files (%d vs %d).",
              cp_info.n_meas_windows(), n_ckpt_meas_windows);

  const int N_VARS_BUFFER = 32;
  std::vector<SP_MEAS_CELL_VAR> sp_meas_cell_buffer(N_VARS_BUFFER);      // space for 32 variables should be ample
  std::vector<SP_MEAS_CELL_VAR> tmp_sp_meas_cell_buffer(N_VARS_BUFFER);
  std::vector<SP_MEAS_CELL_VAR> zero_sp_meas_cell_buffer(N_VARS_BUFFER);

  SP_MEAS_CELL_VAR *sp_meas_cell = &sp_meas_cell_buffer[0];
  SP_MEAS_CELL_VAR *tmp_sp_meas_cell = &tmp_sp_meas_cell_buffer[0];
  SP_MEAS_CELL_VAR *zero_sp_meas_cell = &zero_sp_meas_cell_buffer[0];

  // Note that this loop skips empty meas windows, which means that the first pieces of data sent
  // to the SPs in the loop (ckpt_clear_timestep) will not be sent for empty meas windows.
  DO_CP_MEAS_WINDOWS(window) {
    if (window->is_average_mme ) {
      continue;
    }
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    // Trajectory windows do not need anything to be checkpointed.
    if(!window->m_is_particle_trajectory_window) {
//#endif
      TIMESTEP ckpt_clear_timestep;
      TIMESTEP prev_nsets_written;
      TIMESTEP base_frame;
      TIMESTEP n_empty_cyclic_buffer_slots;
      TIMESTEP first_set_to_write_in_file;
      READ_MEAS_WIN_VAR_CP(ckpt_clear_timestep);
      READ_MEAS_WIN_VAR_CP(prev_nsets_written);
      READ_MEAS_WIN_VAR_CP(base_frame);
      READ_MEAS_WIN_VAR_CP(n_empty_cyclic_buffer_slots);
      READ_MEAS_WIN_VAR_CP(first_set_to_write_in_file);
      write_to_all_sps(ckpt_clear_timestep);
      // Note that prev_nsets_written may reflect some missing meas frames if the meas window end time
      // originally preceded the checkpoint time, but on checkpoint restore, it has been extended beyond
      // the checkpoint time.
      window->base_frame = base_frame;
      window->prev_nsets_written = prev_nsets_written;
      window->initial_prev_nsets_written = prev_nsets_written;
      window->n_empty_cyclic_buffer_slots = n_empty_cyclic_buffer_slots;
      window->first_set_to_write_in_file = first_set_to_write_in_file;
#if DEBUG_MONITOR_CKPT    
      msg_print("read window->m_n_empty_cyclic_buffer_slots %d from ckpt file", n_empty_cyclic_buffer_slots);
      msg_print("read window->first_set_to_write_in_file %d from ckpt file", first_set_to_write_in_file);
#endif
      TIMESTEP restart_solver_time = cp_info.restart_base_time;
      if(cp_info.is_conduction && cp_info.is_flow) {
        if(window->is_cond_window())
          restart_solver_time = cp_info.convert_to_ts_cond(cp_info.restart_base_time);
        else
          restart_solver_time = cp_info.convert_to_ts_flow(cp_info.restart_base_time);
      }
      if (ckpt_clear_timestep != window->m_clear_timestep
          && window->m_clear_timestep >= 0) {
        if (window->m_clear_timestep < restart_solver_time) {
          cSTP_SCALE coarsest_shob_scale = ((window->meas_window_type == LGI_VOLUME_WINDOW) 
                                         || (window->meas_window_type == LGI_SHELL_WINDOW) 
                                         || (window->meas_window_type == LGI_SAMPLING_SHELL_WINDOW)) ?
                                           window->cdi_meas_window->coarsest_cond_shob_scale : window->cdi_meas_window->coarsest_fluid_shob_scale;
          BOOLEAN is_equilibrium_ckpt = vr_equilibrium_timestep_p(cp_info.time, coarsest_shob_scale);
          if (ckpt_clear_timestep < 0) {
            if (!is_equilibrium_ckpt)
              msg_warn("The original end time of measurement window \"%s\" was before the checkpoint restart"
                       " timestep. Now it is after the restart timestep. As a result, because you are restarting"
                       " from a timestep that is not synchronous with respect to all scales included in this"
                       " measurement window, the data in the first frame after the checkpoint restart timestep"
                       " will be invalid for those scales for which the restart time is asynchronous.",
                       window->output_filename);
            window->m_clear_timestep = restart_solver_time;
          } else {
            window->m_clear_timestep = ckpt_clear_timestep;
          }
        }
      }

      // No need to checkpoint this window if the data will simply be cleared after the checkpoint-restore,
      // or if we have passed the window's end time.
      if (ckpt_clear_timestep >= 0 && ckpt_clear_timestep < restart_solver_time) {

        // Opening access to SP data in HDF5 files
        reader.open_window(window->index);

        sINT8 some_meas_cell_spans_procs;
        sINT16 n_sp_variables; // the SPs may collect extra variables (e.g. for std-dev measurements)
        sINT16 n_ckpt_variables;
        READ_MEAS_WIN_VAR_CP(some_meas_cell_spans_procs);
        READ_MEAS_WIN_VAR_CP(n_sp_variables);
        READ_MEAS_WIN_VAR_CP(n_ckpt_variables);
        if (n_ckpt_variables != window->n_variables)
          msg_error("Inconsistency in variable list for measurement window \"%s\" between CDI and checkpoint files.",
                    window->output_filename);

        if (n_sp_variables > N_VARS_BUFFER) {
          sp_meas_cell_buffer.resize(n_sp_variables);
          tmp_sp_meas_cell_buffer.resize(n_sp_variables);
          zero_sp_meas_cell_buffer.resize(n_sp_variables);
          sp_meas_cell = &sp_meas_cell_buffer[0];
          tmp_sp_meas_cell = &tmp_sp_meas_cell_buffer[0];
          zero_sp_meas_cell = &zero_sp_meas_cell_buffer[0];
        }
        asINT32 sp_meas_cell_size = n_sp_variables * sizeof(SP_MEAS_CELL_VAR);

        sINT16 header_len;
        READ_MEAS_WIN_VAR_CP(header_len);

        const asINT32 BUFLEN = 1024;
        char buf[BUFLEN];
        while (header_len > 0) {
          asINT32 n = MIN(header_len, BUFLEN);
          READ_MEAS_WIN_ARRAY_SP(buf, n);
          lgi_write(g_sp_streams[window->m_master_sp], buf, n);
          header_len -= n;
        }

        STP_MEAS_CELL_INDEX n_ckpt_meas_cells;
        READ_MEAS_WIN_VAR_CP(n_ckpt_meas_cells);

        if (n_ckpt_meas_cells != window->n_meas_cells)
        {
          msg_error("Inconsistency in cell count for measurement window \"%s\" between LGI and checkpoint files.",
                    window->output_filename);
        }

        // Lambda function to read cell
        auto read_cell_from_file = [sp_meas_cell_size, &reader, this](SP_MEAS_CELL_VAR * __restrict const meas_cell_data)
        {
          if constexpr (RT == LEGACY)
            lgi_read(m_ckpt_istream, meas_cell_data, sp_meas_cell_size);
          else if constexpr (RT == HDF5_SERIAL)
          {
            STP_PROC src_sp;
            reader.reader_cp.read(&src_sp);
            if (src_sp >= 0)
              reader.reader_sp.read(meas_cell_data, sp_meas_cell_size);
          }
          else
          {
            STP_PROC src_sp;
            reader.reader_cp.read(&src_sp);
            // Negative src_sp must match previous values in meas_cell_data after reading
            if (src_sp >= 0)
              READ_MEAS_WIN_ARRAY_SP(meas_cell_data, sp_meas_cell_size)
            else
              lgi_read(m_ckpt_istream, meas_cell_data, sp_meas_cell_size);
          }
        };

        if (!some_meas_cell_spans_procs) {
          // number of SP meas cells per meas cell in ckpt file is assumed to be 1
          for (STP_MEAS_CELL_INDEX i = 0; i < window->n_stationary_meas_cells; i++) {
            sCP_MEAS_WINDOW::uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &window->m_sp_meas_cell_refs[i];
            read_cell_from_file(sp_meas_cell);
            sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref;
            if (ref_or_list->is_list()) {
              // When checkpointed, the meas cell did not span procs. Now it does.
              // Presumably the decomposition has changed. We send the checkpointed
              // meas cell data to the first SP and zeros to other SPs.
              sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF_LIST_ELT *list = ref_or_list->list();
              sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF          ref   = list->m_ref;
              STP_PROC                                    sp    = ref.sp();
              if (sp >= 0)
                lgi_write(g_sp_streams[sp], sp_meas_cell, sp_meas_cell_size);
              list = list->m_next; // jump to second ref
              while (list != NULL) {
                sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = list->m_ref;
                STP_PROC                           sp  = ref.sp();
                if (sp >= 0)
                  lgi_write(g_sp_streams[sp], zero_sp_meas_cell, sp_meas_cell_size);
                list = list->m_next;
              }
            } else {
              sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = ref_or_list->ref();
              STP_PROC                           sp  = ref.sp();
              if (sp >= 0)
                lgi_write(g_sp_streams[sp], sp_meas_cell, sp_meas_cell_size);
            }
          }
        } else {
          // number of SP meas cells per meas cell explicitly recorded in ckpt file
          for (STP_MEAS_CELL_INDEX i = 0; i < window->n_stationary_meas_cells; i++) {
            sCP_MEAS_WINDOW::uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &window->m_sp_meas_cell_refs[i];
            if (!ref_or_list->is_list()) {
              sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = ref_or_list->ref();
              STP_PROC                           sp  = ref.sp();
              // add up all the SP cells in the file to send to a single SP
              STP_PROC n_sp_cells_in_file;
              READ_MEAS_WIN_VAR_CP(n_sp_cells_in_file);
              read_cell_from_file(sp_meas_cell);
              ccDOTIMES(i, n_sp_cells_in_file - 1) {
                read_cell_from_file(tmp_sp_meas_cell);
                ccDOTIMES(j, window->n_variables)
                  sp_meas_cell[j] += tmp_sp_meas_cell[j];
              }
              if (sp >= 0)
                lgi_write(g_sp_streams[sp], sp_meas_cell, sp_meas_cell_size);
            }
            else {
              sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF_LIST_ELT *list = ref_or_list->list();
              STP_PROC n_sp_cells_in_file;
              READ_MEAS_WIN_VAR_CP(n_sp_cells_in_file);

              // If the file contains more SP cells than this simulation, the last SP
              // will be sent a sum of multiple SP meas cells from the file.
              while (list != NULL) {
                if (list->m_next == NULL && n_sp_cells_in_file > 1)
                  break;
                sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = list->m_ref;
                STP_PROC                           sp  = ref.sp();
                if (n_sp_cells_in_file > 0) {
                  read_cell_from_file(sp_meas_cell);
                  lgi_write(g_sp_streams[sp], sp_meas_cell, sp_meas_cell_size);
                  --n_sp_cells_in_file;
                } else {
                  lgi_write(g_sp_streams[sp], zero_sp_meas_cell, sp_meas_cell_size);
                }
                list = list->m_next;
              }

              if (n_sp_cells_in_file > 1) {
                // list has 1 entry; n_sp_cells_in_file is greater than 1
                read_cell_from_file(sp_meas_cell);
                ccDOTIMES(n, n_sp_cells_in_file - 1) {
                  read_cell_from_file(tmp_sp_meas_cell);
                  ccDOTIMES(j, window->n_variables)
                    sp_meas_cell[j] += tmp_sp_meas_cell[j];
                }
                sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = list->m_ref;
                STP_PROC                           sp  = ref.sp();
                if (sp >= 0)
                  lgi_write(g_sp_streams[sp], sp_meas_cell, sp_meas_cell_size);
              }
            }
          }
        }

        // Now read the moving meas cell data if any
        if (window->n_moving_meas_cells > 0) {
          BOOLEAN is_restored_on_same_sps = (total_ckpt_sps == total_sps);
          sp_meas_cell_buffer.resize(n_sp_variables+1); // the extra +1 is for the meas_index/ref_count
          sp_meas_cell = &sp_meas_cell_buffer[0];
          asINT32 sp_meas_cell_size = (n_sp_variables+1) * sizeof(SP_MEAS_CELL_VAR);
          if (n_sp_variables > window->n_variables) // std deviation vars
            sp_meas_cell_size = (n_sp_variables) * sizeof(SP_MEAS_CELL_VAR);
          STP_MEAS_CELL_INDEX n_meas_cells_per_sp;
          STP_PROC ckpt_sp;

          ccDOTIMES(sp, total_ckpt_sps) {
            // Reading source SP
            READ_MEAS_WIN_VAR_SP(ckpt_sp);
            if (ckpt_sp != sp)
              msg_error("Mismatch between ckpt SP %d and send SP %d during full checkpoint restore "
                        "of moving meas data for window \"%s\"", ckpt_sp, sp, window->output_filename);

            // Rading number of cells saved by curremt SP
            READ_MEAS_WIN_VAR_SP(n_meas_cells_per_sp);

            if ( is_restored_on_same_sps ) {
              ccDOTIMES(n, n_meas_cells_per_sp) {
                READ_MEAS_WIN_ARRAY_SP(sp_meas_cell, sp_meas_cell_size);
                lgi_write(g_sp_streams[sp], sp_meas_cell, sp_meas_cell_size);
              }
            }
            else {
              std::vector<STP_PROC>& ckpt_sp_map = moving_meas_cell_ckpt_sp_map.at( window->index );

              ccDOTIMES(n, n_meas_cells_per_sp) {
                READ_MEAS_WIN_ARRAY_SP(sp_meas_cell, sp_meas_cell_size);
                STP_MEAS_CELL_INDEX gid = reinterpret_cast<int64_t&>(*sp_meas_cell); // sp_meas_cell is 64 bits
                STP_PROC meas_cell_new_sp = ckpt_sp_map.at( gid - (window->n_stationary_meas_cells) );
                if (meas_cell_new_sp == STP_PROC_INVALID) {
                  msg_error("Checkpointed Moving Measurement cell %d for window \"%s\" has no home SP!",gid,window->output_filename);
                  meas_cell_new_sp = 0;
                }
                lgi_write(g_sp_streams[meas_cell_new_sp], sp_meas_cell, sp_meas_cell_size);
              }
            }
          }

          int64_t sentinal = -1;
          ccDOTIMES(sp, total_sps) {
            lgi_write(g_sp_streams[sp], &sentinal, sizeof(sentinal));
          }
        }

        // Closing window
        reader.close_window();
      }
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    } else {
      //When resuming from a full checkpoint, a CP trajectory window needs to know the index of the checkpoint table 
      //entry in the PMR file to use (which is created in a PMR file when checkpoints are taken) to rewind the pmr 
      //file.  This index is stored in the checkpoint file by write_meas_files() in full_ckpt.cc and is retrieved here.
      sINT32 checkpoint_table_index;
      READ_MEAS_WIN_VAR_CP(checkpoint_table_index);

      CP_TRAJECTORY_WINDOW trajectory_window = (CP_TRAJECTORY_WINDOW)window;
      trajectory_window->set_checkpoint_index(checkpoint_table_index); 
      //The fix for PR41929 requires that the number of parent and child table entries in an existing PMR file be 
      //known before any hitpoint or startpoint records are processed. These counts are read upon opening the pmr file
      //for resuming so force the file open now to ensure these counts are provided to each trajectory window.
      trajectory_window->pmr_file();
    }
//#endif
  }
}


template VOID sCP_DGF_READER::read_meas_window_ckpt_data<sCP_DGF_READER::LEGACY>
  (asINT32, std::unordered_map<STP_MEAS_WINDOW_INDEX,std::vector<STP_PROC> >&);

template VOID sCP_DGF_READER::read_meas_window_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>
  (asINT32, std::unordered_map<STP_MEAS_WINDOW_INDEX,std::vector<STP_PROC> >&);

template VOID sCP_DGF_READER::read_meas_window_ckpt_data<sCP_DGF_READER::DEBUG_MODE>
  (asINT32, std::unordered_map<STP_MEAS_WINDOW_INDEX,std::vector<STP_PROC> >&);

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_coupling_model_pt_pf_ratio_history()
{
  // Creating the reader
  LocalReader<RT, false> reader(initLocalReader<RT>(m_ckpt_istream, sim_args.resume_filename,
                                                    "Coupling model pt pf ratio history"));
  // Jump to file position when opening legacy files and reading the tag
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.pt_pf_ratio_pos);
    LGI_TAG tag;
    lgi_read_next_head(m_ckpt_istream, tag);
    if (tag.id != DGF_PT_PF_RATIO_TAG)
      msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                         lgi_tag_namestring(DGF_PT_PF_RATIO_TAG), DGF_PT_PF_RATIO_TAG,
                         lgi_tag_namestring(tag.id), tag.id);
  }
  // Record size (only in HDF5 file)
  {
    size_t record_size;
    reader.read_hdf5(record_size);
  }

  sINT16 n_models;
  READ_GENERIC_VAR_FROM_FILE(n_models);
  assert(n_models == cp_info.n_surface_couplings);
  ccDOTIMES(model_index, n_models) {
    asINT32 n_phases;
    READ_GENERIC_VAR_FROM_FILE(n_phases);
    COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs + model_index;
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
    cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    assert(n_phases == cmdl->m_coupling_phase_descs.size());
    ccDOTIMES(phase_index, n_phases) {
      asINT32 n_pt_pf_records;
      READ_GENERIC_VAR_FROM_FILE(n_pt_pf_records);
      ccDOTIMES(record_index, n_pt_pf_records) {
        //asINT32 timestep;
        //sFLOAT ratio_scale_factor;
        sPT_PF_RATIO_RECORD ratio_record;
        READ_GENERIC_VAR_FROM_FILE(ratio_record.timestep);
        READ_GENERIC_VAR_FROM_FILE(ratio_record.ratio_scale_factor);
#if DEBUG_ADAPTIVE_COUPLING
        msg_print("Read in record %d %f for phase %d from full ckpt file", ratio_record.timestep, ratio_record.ratio_scale_factor, phase_index);
#endif
        coupling_model_desc->m_coupling_phase_ratios[phase_index].push_back(ratio_record);
      }
    }
  }
}

template VOID sCP_DGF_READER::read_coupling_model_pt_pf_ratio_history<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_coupling_model_pt_pf_ratio_history<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_coupling_model_pt_pf_ratio_history<sCP_DGF_READER::DEBUG_MODE>();

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_monitors_ckpt_data()
{
  // Creating the reader
  LocalReader<RT, false> reader(initLocalReader<RT>(m_ckpt_istream, sim_args.resume_filename, "Monitors"));
  // Jump to file position when opening legacy files and reading the tag
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE)
  {
    jump_to_ckpt_file_position(m_ckpt_table_of_contents.monitors_pos);
    LGI_TAG tag;
    lgi_read_next_head(m_ckpt_istream, tag);
    if (tag.id != DGF_MONITORS_TAG)
      msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                         lgi_tag_namestring(DGF_MONITORS_TAG), DGF_MONITORS_TAG,
                         lgi_tag_namestring(tag.id), tag.id);
  }
  // Record size (only in HDF5 file)
  {
    size_t record_size;
    reader.read_hdf5(record_size);
  }

  sINT16 n_monitors;
  READ_GENERIC_VAR_FROM_FILE(n_monitors);
#if DEBUG_MONITOR
  msg_print("Read %d monitors from ckpt file", n_monitors);
  assert(n_monitors == cp_info.monitors.size());
#endif
  ccDOTIMES(i, n_monitors) {
    sINT8 converged_and_ended_previous_run_before_ckpt;
    READ_GENERIC_VAR_FROM_FILE(converged_and_ended_previous_run_before_ckpt);
    cp_info.monitors[i]->m_converged_and_ended_sim_before_ckpt = converged_and_ended_previous_run_before_ckpt;

    cBOOLEAN disabled_autostop_by_on_off;
    READ_GENERIC_VAR_FROM_FILE(disabled_autostop_by_on_off);
    cBOOLEAN enabled_autostop_by_on_off;
    READ_GENERIC_VAR_FROM_FILE(enabled_autostop_by_on_off);

    // If the user provides a new --autostop_all_off, --autostop_off or --autostop_on option, we ignore 
    // the disabled_autostop_by_on_off flag.
    if (!(sim_args.autostop_all_off || cp_info.cmdline_autostop_off_monitors || cp_info.cmdline_autostop_on_monitors)) {
      if (disabled_autostop_by_on_off) {
        cp_info.monitors[i]->m_disabled_autostop_by_on_off = TRUE;

        if (cp_info.monitors[i]->m_end_sim_if_converged == true) {
          cp_info.monitors[i]->m_end_sim_if_converged = false;
        }
      }
      if (enabled_autostop_by_on_off) {
        cp_info.monitors[i]->m_enabled_autostop_by_on_off = TRUE;
        if (cp_info.monitors[i]->m_end_sim_if_converged == false) {
          cp_info.monitors[i]->m_end_sim_if_converged = true;
        }
      }
      // Update the monitors_to_end_sim list
      cp_info.monitors_to_end_sim.clear();
      ccDOTIMES(i, cp_info.monitors.size()) {
        if (cp_info.monitors[i]->m_end_sim_if_converged)
          cp_info.monitors_to_end_sim.push_back(cp_info.monitors[i]);
      }
    }
    TIMESTEP n_signals;
    READ_GENERIC_VAR_FROM_FILE(n_signals);
#if DEBUG_MONITOR
    msg_print("Read %d signals for monitor %d", n_signals, i);
#endif
    ccDOTIMES(j, n_signals) {
      sFLOAT signal;
      READ_GENERIC_VAR_FROM_FILE(signal);
      TIMESTEP timestep;
      READ_GENERIC_VAR_FROM_FILE(timestep);
      TIMESTEP next_timestep = cp_info.monitors[i]->find_next_signal_timestep(timestep);
#if DEBUG_MONITOR      
      msg_print("Read signal %f for timestep %d next timestep %d", signal, timestep, next_timestep);
#endif      
      // Always rewrite monitor log file (case_name.monitor) and monitor data file (monitor_name.dat 
      // or monitor_name.it.dat) when restarting from ckpt because they may have been updated after 
      // the ckpt time.
      // Do not convert the signal unit as the data in the monitor ckpt file has already been converted.

      // If previously the simulation was stopped by this monitor, then do not let it stop the simulation after resuming from the checkpoint.
      cp_info.monitors[i]->append_and_analyze_signal(signal, timestep, next_timestep, TRUE, FALSE, TRUE);
    }
  }
}

template VOID sCP_DGF_READER::read_monitors_ckpt_data<sCP_DGF_READER::LEGACY>();
template VOID sCP_DGF_READER::read_monitors_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
template VOID sCP_DGF_READER::read_monitors_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();

VOID write_meas_window_vars_to_sps(CP_MEAS_WINDOW meas_window, BOOLEAN flow_sps_only)
{
  asINT32 n_vars = meas_window->n_variables;
  LGI_MEAS_WINDOW_VARS record;

  record.tag.id = LGI_MEAS_WINDOW_VARS_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(n_vars * sizeof(LGI_MEAS_WINDOW_VARS_SUBREC) + sizeof(record));

  record.n_variables = n_vars;

  record.min_pressure       = meas_window->cdi_meas_window->min_pressure;
  record.max_pressure       = meas_window->cdi_meas_window->max_pressure;
  record.reference_point[0] = meas_window->cdi_meas_window->reference_point[0];
  record.reference_point[1] = meas_window->cdi_meas_window->reference_point[1];
  record.reference_point[2] = meas_window->cdi_meas_window->reference_point[2];

  record.is_probe           = meas_window->is_probe;
  record.solver_mask        = meas_window->cdi_meas_window->solver_mask;
  if(meas_window->meas_window_type == LGI_VOLUME_WINDOW || meas_window->meas_window_type == LGI_SHELL_WINDOW || meas_window->meas_window_type == LGI_SAMPLING_SHELL_WINDOW)
    record.solver_mask = meas_window->cdi_meas_window->solver_mask & CONDUCTION_PDE_ACTIVE;
  else
    record.solver_mask = meas_window->cdi_meas_window->solver_mask & ~CONDUCTION_PDE_ACTIVE;

  record.is_output_in_local_csys = meas_window->is_output_in_local_csys;
  record.is_meas_vars_output_dp  = meas_window->is_meas_vars_output_dp;
  record.calc_htc_for_adb_walls  = meas_window->calc_htc_for_adb_walls;

  // only used in lighthill switch
  record.acous_switch_scale = meas_window->cdi_meas_window->lcm_flow_tsteps / cp_info.n_lb_base_steps;

  if (flow_sps_only) {
    write_header_to_all_flow_sps(record);
  }
  else {
    write_header_to_all_sps(record);
  }
  ccDOTIMES (j, n_vars) {
    LGI_MEAS_WINDOW_VARS_SUBREC subrec;
    subrec.var_type = meas_window->var_types[j];
    if (flow_sps_only) {
      write_to_all_flow_sps(subrec);
    } else {
      write_to_all_sps(subrec);
    }
  }
}


VOID sCP_DGF_READER::read_meas_windows_header()
{
  sCP_MEAS_WINDOW::validate_sp_meas_cell_index_pair_layout();

  asINT32 n_meas_windows = cp_info.control_rec.num_meas_windows;
  asINT32 n_dims         = cp_info.n_dims;

  jump_to_file_position(m_table_of_contents.meas_window_pos);
  cp_info.meas_cell_index_per_window.resize(n_meas_windows);
  cp_info.meas_surfel_index_per_window.resize(n_meas_windows);
  cp_info.meas_windows.reserve(n_meas_windows);
  // Its final value will be set in add_meas_window when the window name matches
  // the one specified by the user.
  ccDOTIMES(i, n_meas_windows) {
    cDGF_MEAS_WINDOW dgf_window;
    dgf_window.read(m_main_istream);

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    //Intercept DGF meas window records for trajectory windows and change the meas type to LGI_TRAJECTORY_WINDOW.
    //The SPs can't distinguish trajectory windows otherwise since they don't have access to the corresponding 
    //CDI meas window record.  This should be removed before upstream support for trajectory windows is finalized.
    BOOLEAN is_trajectory = FALSE;
    if(cp_info.cdi_meas_windows[dgf_window.cdi_meas_window_index].m_is_particle_trajectory_window) {
      dgf_window.meas_type = LGI_TRAJECTORY_WINDOW;
      is_trajectory = TRUE;
    }

    if(cp_info.cdi_meas_windows[dgf_window.cdi_meas_window_index].m_per_material_particle_measurements)
      dgf_window.meas_window_flags |= DGF_MEAS_WINDOW_PER_MATERIAL;
    
    if(cp_info.cdi_meas_windows[dgf_window.cdi_meas_window_index].m_per_emitter_particle_measurements)
      dgf_window.meas_window_flags |= DGF_MEAS_WINDOW_PER_EMITTER;

//#endif    

    write_header_to_all_sps(dgf_window);

    CP_MEAS_WINDOW meas_window = cp_info.meas_windows.add_meas_window(&dgf_window);
    BOOLEAN is_composite = dgf_window.meas_window_flags & DGF_MEAS_WINDOW_IS_COMPOSITE;
    BOOLEAN is_development = dgf_window.meas_window_flags & DGF_MEAS_WINDOW_IS_DEVELOPMENT;

    // meas_window could be NULL if there is no meas cell for this measurement
    if (meas_window) {
      CDI_MEAS_WINDOW cdi_meas_window = meas_window->cdi_meas_window;

      // Update the CP meas window indices for the CDI meas window
      switch(meas_window->meas_window_type) {
      case LGI_AVERAGE_MME_WINDOW:
        if (!meas_window->cdi_meas_window->is_average_mme)
          msg_internal_error("LGI meas window is average mme type while the cdi meas window is not.");
        break;
      case LGI_FLUID_WINDOW:
        cdi_meas_window->fluid_meas_window_index = i;
        break;
      case LGI_POROUS_WINDOW:
        cdi_meas_window->porous_meas_window_index = i;
        break;
      case LGI_SURFACE_WINDOW:
        cdi_meas_window->surface_meas_window_index = i;
        if(!is_development && !is_composite) {
          static_cast<CP_SURFACE_MEAS_WINDOW>(meas_window)->m_mate_window_read = FALSE;
          static_cast<CP_SURFACE_MEAS_WINDOW>(meas_window)->mate_window = NULL;
        }
        break;
      case LGI_SHELL_WINDOW:
        cdi_meas_window->shell_meas_window_index = i;
        if(!is_development && !is_composite) {
          static_cast<CP_SURFACE_MEAS_WINDOW>(meas_window)->m_mate_window_read = FALSE;
          static_cast<CP_SURFACE_MEAS_WINDOW>(meas_window)->mate_window = NULL;
        }
        break;
      case LGI_SAMPLING_SHELL_WINDOW:
        cdi_meas_window->shell_meas_window_index = i;
        cdi_meas_window->solid_meas_window_index = i;
        break;
      case LGI_VOLUME_WINDOW:
        cdi_meas_window->solid_meas_window_index = i;
        break;
      case LGI_SAMPLING_SURFACE_WINDOW:    // sampling surface window is considered as a surface window by CDI, see PR40675
        //cdi_meas_window->surface_meas_window_index = cdi_meas_window->shell_meas_window_index = i;
        //cdi_meas_window->fluid_meas_window_index = cdi_meas_window->solid_meas_window_index = i;     
        cdi_meas_window->surface_meas_window_index = i;
        cdi_meas_window->fluid_meas_window_index = i;     
        // In order for old CDI file (in which sampling surface measurement 
        // window is marked as fluid measurement type in monitors) to work, 
        // we need to set the fluid meas window index here too.
        break;
      default:  // Should never happen
        break;
      }
      if(cdi_meas_window->shell_meas_window_index >= 0 && cdi_meas_window->surface_meas_window_index >= 0 
        && meas_window->meas_window_type != LGI_SAMPLING_SURFACE_WINDOW && meas_window->meas_window_type != LGI_SAMPLING_SHELL_WINDOW) {
	cdi_meas_window->m_mated_flow_conduction_surface_files = TRUE;
	if(meas_window->meas_window_type == LGI_SURFACE_WINDOW) {
	  static_cast<CP_SURFACE_MEAS_WINDOW>(meas_window)->mate_window
				   = static_cast<CP_SURFACE_MEAS_WINDOW>(cp_info.meas_windows[cdi_meas_window->shell_meas_window_index]);
	  static_cast<CP_SURFACE_MEAS_WINDOW>(cp_info.meas_windows[cdi_meas_window->shell_meas_window_index])->mate_window
									= static_cast<CP_SURFACE_MEAS_WINDOW>(meas_window);
	}
	else if(meas_window->meas_window_type == LGI_SHELL_WINDOW) {
	  static_cast<CP_SURFACE_MEAS_WINDOW>(meas_window)->mate_window
				    = static_cast<CP_SURFACE_MEAS_WINDOW>(cp_info.meas_windows[cdi_meas_window->surface_meas_window_index]);
	  static_cast<CP_SURFACE_MEAS_WINDOW>(cp_info.meas_windows[cdi_meas_window->surface_meas_window_index])->mate_window
									= static_cast<CP_SURFACE_MEAS_WINDOW>(meas_window);
	}
      }
      else
    	  cdi_meas_window->m_mated_flow_conduction_surface_files = FALSE;
    }

    if (meas_window && meas_window->is_average_mme) {
      // seek until the next meas window
      if (i < n_meas_windows - 1)
        seek_lgi_tag(DGF_MEAS_WINDOW_TAG);
      continue;
    }

    // For standard windows,
    // dgf_window.meas_type == LGI_SAMPLING_SURFACE_WINDOW || LGI_SURFACE_WINDOW is same as
    // meas_window->sri_file_type == SRI_SURFACE_TYPE


//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    //Trajectory windows need some additional information appended to the LGI stream that contains the trajectory window 
    //specific user options that the SP's will need. These values are actually from the CDI meas window data which 
    //the SPs will not have access to. The SP should expect this subrec or not based on wether this window is a trajectory window.
    if(is_trajectory) {

      LGI_TRAJECTORY_WINDOW_OPTIONS_HEADER header;
      header.tag.id = LGI_TRAJECTORY_WINDOW_OPTIONS_HEADER_TAG;
      header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_TRAJECTORY_WINDOW_OPTIONS_HEADER) + 
                                                           sizeof(LGI_TRAJECTORY_WINDOW_OPTIONS_REC));
      write_header_to_all_sps(header);

      LGI_TRAJECTORY_WINDOW_OPTIONS_REC options;
      sCDI_MEAS_WINDOW &cdi_window = cp_info.cdi_meas_windows[dgf_window.cdi_meas_window_index];
      options.start_time = cdi_window.start_time;
      options.end_time = cdi_window.end_time;
      options.record_trajectories = cdi_window.m_record_trajectories;
      options.record_velocities = cdi_window.m_record_velocities;
      options.use_dynamic_decimation = cdi_window.m_use_dynamic_decimation;
      options.decimation_tolerance = cdi_window.m_decimation_tolerance;
      options.static_decimation_rate = cdi_window.m_static_decimation_rate;
      options.hitpoint_options = cdi_window.m_hitpoint_options;
      options.record_only_adhering_hitpoints = cdi_window.m_record_only_adhering_hitpoints;
      options.record_normal_impulse = cdi_window.m_record_normal_impulse;
      options.fraction_eligible_for_measurement = cdi_window.m_fraction_eligible_for_measurement;
      write_to_all_sps(options);
    }
//#endif

    // Development window parameters must be be read from LGI in windows_header
    if (is_development && (dgf_window.meas_type == LGI_SURFACE_WINDOW || dgf_window.meas_type == LGI_SHELL_WINDOW)) {
      //development surface window
      STP_MEAS_CELL_INDEX meas_cell_index = 0;
      cDGF_DEV_WINDOW_LINE_SEGMENTS dgf_segment;

      if (dgf_window.num_meas_cells > 0) {
        dgf_segment.read(m_main_istream);
        write_header_to_all_sps(dgf_segment);

        sCP_SURFACE_DEV_MEAS_WINDOW *surface_window = 
                      static_cast<CP_SURFACE_DEV_MEAS_WINDOW> (meas_window);
        surface_window->fill_segment_data(&dgf_segment, cp_info.n_dims);

        cDGF_MEAS_WIN_SUBRECS meas_win_subrecs;
        meas_win_subrecs.read(m_main_istream);
        write_header_to_all_sps(meas_win_subrecs);

        ccDOTIMES(j, dgf_window.num_subrecs) {
          cDGF_SURFACE_DEV_WINDOW_FACE_EXTENT  extent;
          extent.read(m_main_istream);
          write_to_all_sps(extent);
          uINT8 axis = extent.dev_axis;
          surface_window->m_face_first_meas_cell[axis][extent.face_index] = meas_cell_index;
          surface_window->m_face_first_segment  [axis][extent.face_index] = extent.first_segment_index;
          surface_window->m_face_n_segments     [axis][extent.face_index] = extent.num_face_segments;
          meas_cell_index += extent.num_face_segments;
        }
      }
    }

    if (is_development && (dgf_window.meas_type == LGI_POROUS_WINDOW ||
                           dgf_window.meas_type == LGI_FLUID_WINDOW)) {
      //development fluid window
      STP_MEAS_CELL_INDEX meas_cell_index = 0;

      cDGF_DEV_WINDOW_LINE_SEGMENTS dgf_segment;
      if (dgf_window.num_meas_cells > 0) {
        dgf_segment.read(m_main_istream);

        write_header_to_all_sps(dgf_segment);

        sCP_FLUID_DEV_MEAS_WINDOW *fluid_window = 
                      static_cast<CP_FLUID_DEV_MEAS_WINDOW> (meas_window);
        fluid_window->fill_segment_data(&dgf_segment, cp_info.n_dims);

        cDGF_MEAS_WIN_SUBRECS meas_win_subrecs;
        meas_win_subrecs.read(m_main_istream);
        write_header_to_all_sps(meas_win_subrecs);

        ccDOTIMES(j, dgf_window.num_subrecs) {
          cDGF_FLUID_DEV_WINDOW_PART_EXTENT  extent;
          extent.read(m_main_istream);
          write_to_all_sps(extent);
          uINT8 axis = extent.dev_axis;
          fluid_window->m_part_first_meas_cell[axis][extent.part_index] = meas_cell_index;
          fluid_window->m_part_first_segment  [axis][extent.part_index] = extent.first_segment_index;
          fluid_window->m_part_n_segments     [axis][extent.part_index] = extent.num_part_segments;
          meas_cell_index += extent.num_part_segments;
        }
      }
    }

    if (dgf_window.num_meas_cells > 0) {
      if (!(is_composite || is_development) &&
          ((dgf_window.meas_type == LGI_SAMPLING_SURFACE_WINDOW) || (dgf_window.meas_type == LGI_SURFACE_WINDOW) || (dgf_window.meas_type == LGI_SHELL_WINDOW) || (dgf_window.meas_type == LGI_SAMPLING_SHELL_WINDOW))) {
        CP_SURFACE_MEAS_WINDOW surface_window = static_cast<CP_SURFACE_MEAS_WINDOW> (meas_window);
        surface_window->allocate_prelim_data_for_sri();
      }
      // moved sending of meas vartypes later, since we need to append
      // tangential force to surface measurements under certain circumstances.
    } // num_meas_cells > 0

    // seek until the next meas window
    if (i < n_meas_windows - 1)
      seek_lgi_tag(DGF_MEAS_WINDOW_TAG);

  } // n_meas_windows

  cp_info.write_sri_results_queue = new sWINDOW_QUEUE_MT(cp_info.meas_windows.n_meas_windows(), WRITE_SRI_RESULTS_QUEUE);
  cp_info.post_recvs_queue = new sWINDOW_QUEUE_MT(cp_info.meas_windows.n_meas_windows(), POST_RECVS_QUEUE);

  // The coarsest shob scale associated with each meas window is read from
  // the LGI file above and deposited in the associated CDI meas
  // windows. The window time parameters cannot be aligned/rounded until
  // the coarsest shob scales are known.
  align_meas_window_time_parameters();
}

VOID sCP_DGF_READER::read_meas_windows_data()
{
  asINT32 n_meas_windows = cp_info.control_rec.num_meas_windows;
  asINT32 n_dims         = cp_info.n_dims;

  jump_to_file_position(m_table_of_contents.meas_window_pos);

  asINT32 n_global_vertices = cp_info.vertex_array.size();
  std::vector< bool > vertex_marks;
  vertex_marks.reserve( 2 * n_global_vertices ); // allow space for front and back vertices

  VMEM_VECTOR< DGF_VERTEX_INDEX > back_vertex_indices;
  back_vertex_indices.resize(n_global_vertices, (DGF_VERTEX_INDEX) -1); // initialize all to -1

  // We accumulate the vertex refs into a temporary vector and then
  // copy them into the meas window once we know how many we have.
  VMEM_VECTOR < DGF_VERTEX_INDEX > vertex_refs;
  vertex_refs.reserve(1024 * 1024);

  ccDOTIMES(i, n_meas_windows) {
    cDGF_MEAS_WINDOW dgf_window;
    dgf_window.read(m_main_istream);
    if (dgf_window.meas_type == LGI_AVERAGE_MME_WINDOW)
      continue;

    CP_MEAS_WINDOW meas_window = cp_info.meas_windows[i];


    if (dgf_window.num_meas_cells > 0) {
      meas_window->allocate_window_data_for_sri();
      asINT32 n_meas = (meas_window->is_development) ? dgf_window.num_meas_cells : dgf_window.num_measurements;
      cp_info.meas_cell_index_per_window[i].resize(n_meas, 0);
      cp_info.meas_surfel_index_per_window[i].resize(n_meas, 0);
      switch (dgf_window.meas_type) {
      case LGI_SAMPLING_SURFACE_WINDOW:
      case LGI_SURFACE_WINDOW:
      case LGI_SAMPLING_SHELL_WINDOW:
      case LGI_SHELL_WINDOW:
       
        if (meas_window->is_composite) {
          // composite surface window
          CP_COMPOSITE_SURFACE_MEAS_WINDOW surface_window =
            static_cast<CP_COMPOSITE_SURFACE_MEAS_WINDOW> (meas_window);
          asINT32 p = 0; // index in window's projected area array
          cDGF_MEAS_WIN_SUBRECS meas_win_subrecs; // present only to simplify inc disc
          meas_win_subrecs.read(m_main_istream);
          asINT32 num_points = 0;
          if (n_dims == 2) {
            ccDOTIMES(j, dgf_window.num_meas_cells) {
              cDGF_MEAS_COMPOSITE_SURFEL meas_surfel;
              meas_surfel.read(m_main_istream);
              cp_info.meas_cell_index_per_window.at(i).at(j) = j;
              if(surface_window->n_surfels[meas_surfel.face_index] == 0) {
                surface_window->n_surfels_with_two_meas[meas_surfel.face_index] = 0;
                surface_window->first_surfel[meas_surfel.face_index] = j;
                surface_window->first_point[meas_surfel.face_index] = num_points;
              }
              surface_window->m_faces[j]             = meas_surfel.face_index;
              surface_window->m_face_areas[j]        = meas_surfel.area;
              surface_window->m_projected_areas[p++] = meas_surfel.projected_area[0];
              surface_window->m_projected_areas[p++] = meas_surfel.projected_area[1];
              surface_window->n_surfels[meas_surfel.face_index]++;
              if((dgf_window.meas_type == LGI_SHELL_WINDOW || dgf_window.meas_type == LGI_SAMPLING_SHELL_WINDOW) && (cp_info.sri_faces[meas_surfel.face_index].n_layers >0))
                num_points += cp_info.sri_faces[meas_surfel.face_index].n_layers + 1;
              else
                num_points++;
            }
          } else {
            ccDOTIMES(j, dgf_window.num_meas_cells) {
              cDGF_MEAS_COMPOSITE_SURFEL meas_surfel;

              meas_surfel.read(m_main_istream);
              cp_info.meas_cell_index_per_window.at(i).at(j) = j;
              if(surface_window->n_surfels[meas_surfel.face_index] == 0) {
                surface_window->n_surfels_with_two_meas[meas_surfel.face_index] = 0;
                surface_window->first_surfel[meas_surfel.face_index] = j;
                surface_window->first_point[meas_surfel.face_index] = num_points;
              }
              surface_window->m_faces[j]             = meas_surfel.face_index;
              surface_window->m_face_areas[j]        = meas_surfel.area;
              surface_window->m_projected_areas[p++] = meas_surfel.projected_area[0];
              surface_window->m_projected_areas[p++] = meas_surfel.projected_area[1];
              surface_window->m_projected_areas[p++] = meas_surfel.projected_area[2];
              surface_window->n_surfels[meas_surfel.face_index]++;
              if((dgf_window.meas_type == LGI_SHELL_WINDOW || dgf_window.meas_type == LGI_SAMPLING_SHELL_WINDOW) && (cp_info.sri_faces[meas_surfel.face_index].n_layers >0))
                num_points += cp_info.sri_faces[meas_surfel.face_index].n_layers + 1;
              else
                num_points++;
            }
          }
        } else if (meas_window->is_development) {          //if development, read data from stream and drop on the floor
          cDGF_DEV_WINDOW_LINE_SEGMENTS dgf_segment;       //in order to get to correct location in LGI stream
          dgf_segment.read(m_main_istream);
          cDGF_MEAS_WIN_SUBRECS meas_win_subrecs;
          meas_win_subrecs.read(m_main_istream);
          ccDOTIMES(j, dgf_window.num_subrecs) {
            cDGF_SURFACE_DEV_WINDOW_FACE_EXTENT  extent;
            extent.read(m_main_istream);
          }
        } else  {
          cDGF_MEAS_WIN_SUBRECS meas_win_subrecs; // present only to simplify inc disc
          meas_win_subrecs.read(m_main_istream);
          sCP_SURFACE_MEAS_WINDOW *surface_window = static_cast<CP_SURFACE_MEAS_WINDOW> (meas_window);
          DGF_VERTEX_INDEX first_vertex_ref = 0;
          surface_window->m_n_surfels = dgf_window.num_meas_cells;
          sCP_SURFACE_MEAS_WINDOW *mate_window = NULL;
          if(surface_window->cdi_meas_window->mated_flow_conduction_surface_files())
        	  mate_window = surface_window->mate_window;
          surface_window->n_mated_surfels_per_face.resize(cp_info.n_sri_faces, 0);
          SRI_SURFEL_BLOCK_MAP this_current_block = NULL;
          std::vector<sSRI_SURFEL_BLOCK_MAP>::iterator mate_current_block;
          if(surface_window->m_mate_window_read)
            mate_current_block = mate_window->surfel_block_map.end();
          BOOLEAN prior_meas_surfel_has_a_mate = FALSE;
          // the following declarations are needed for curved porous media
          // inlet meas windows
          BOOLEAN is_curved_hx_inlet =  surface_window->cdi_meas_window->is_curved_hx_inlet;
          std::map <DGF_VERTEX_INDEX, DGF_VERTEX_INDEX> hx_global_to_local_vertex_map;
          std::vector <DGF_VERTEX_INDEX> hx_global_vertices;
          DGF_VERTEX_INDEX n_hx_vertices = 0;
          
          if (is_curved_hx_inlet) {
            cDGF_QUAD_MESH_HEADER quad_mesh_header;
            quad_mesh_header.tag.length = 0; // set to zero since record length is not known apriori
            quad_mesh_header.tag.id = DGF_QUAD_MESH_HEADER_TAG;
            quad_mesh_header.num_facets = dgf_window.num_meas_cells;
            quad_mesh_header.facet_offset = surface_window->cdi_meas_window->inlet_facet_offset; 
            quad_mesh_header.window_index = meas_window->index;

            ccDOTIMES(sp, total_sps) {
              quad_mesh_header.write(g_sp_streams[sp], FALSE);
            }

            hx_global_vertices.reserve(2*dgf_window.num_meas_cells);
          }
                

          vertex_marks.assign( 2 * n_global_vertices, false);
          vertex_refs.resize(0);
          asINT32 mci = 0;
          asINT32 num_points = 0;
          asINT32 n_surfels_with_two_meas = 0;
          asINT32 prev_meas_surfel_face_index = 0;
          asINT32 stationary_meas_cells = dgf_window.num_meas_cells - dgf_window.num_moving_meas_cells;
          asINT32 n_mated_surfels = 0;
          asINT32 n_mated_surfels_in_mate_window = 0;
          asINT32 n_surfels_found_mate_in_block = 0;
          if(surface_window->m_mate_window_read)
            n_mated_surfels_in_mate_window = mate_window->surfel_block_map[0].n_surfels;
          ccDOTIMES(j, dgf_window.num_meas_cells) {
            cDGF_MEAS_SURFEL meas_surfel;
            meas_surfel.read(m_main_istream);
            if((meas_surfel.face_index < prev_meas_surfel_face_index) && (j < stationary_meas_cells) && !meas_window->is_development)
              msg_internal_error("Measurement surfels in measurement window \"%s\" are not ordered by face ids", cp_info.cdi_meas_windows[dgf_window.cdi_meas_window_index].name);
            if(surface_window->n_surfels[meas_surfel.face_index] == 0) {
              surface_window->n_surfels_with_two_meas[meas_surfel.face_index] = 0;
              surface_window->first_surfel[meas_surfel.face_index] = j;
              surface_window->first_point[meas_surfel.face_index] = num_points;
            }
            surface_window->n_surfels[meas_surfel.face_index]++;
            if((dgf_window.meas_type == LGI_SHELL_WINDOW || dgf_window.meas_type == LGI_SAMPLING_SHELL_WINDOW) && (cp_info.sri_faces[meas_surfel.face_index].n_layers >0))
              num_points += cp_info.sri_faces[meas_surfel.face_index].n_layers + 1;
            else
              num_points += meas_surfel.n_flow_measurements();
            cp_info.meas_cell_index_per_window.at(i).at(j) = j;
            cp_info.meas_cell_index_per_window.at(i).at(j) <<= 1;
            if((meas_surfel.n_flow_measurements() == 2) && (dgf_window.meas_type != LGI_SHELL_WINDOW) && (dgf_window.meas_type != LGI_SAMPLING_SHELL_WINDOW)) {
              //              setting the bit to set this meas surfel has two measurements
             cp_info.meas_cell_index_per_window[i][j] |= 0x1;
             surface_window->n_surfels_with_two_meas[meas_surfel.face_index]++;
           }
            BOOLEAN is_back_meas_surfel = FALSE;
            DGF_VERTEX_COUNT num_vertices = meas_surfel.num_vertices();
            is_back_meas_surfel = meas_surfel.is_surfel_on_back();
            if (num_vertices < 0) {
              num_vertices = -num_vertices;
              is_back_meas_surfel = TRUE;
            }
            BOOLEAN has_a_mate = meas_surfel.is_mated();
            if(has_a_mate) {
              n_mated_surfels++;
              surface_window->n_mated_surfels_per_face[meas_surfel.face_index]++;
              if(prior_meas_surfel_has_a_mate && (meas_surfel.face_index == prev_meas_surfel_face_index) &&
                  surface_window->m_mate_window_read && (n_mated_surfels_in_mate_window >= n_mated_surfels)) {
                this_current_block->n_surfels++;
              } else if(prior_meas_surfel_has_a_mate && (meas_surfel.face_index == prev_meas_surfel_face_index) && !surface_window->m_mate_window_read) {
                this_current_block->n_surfels++;
              } else {
        	if(surface_window->m_mate_window_read) {
        	  if(mate_current_block != mate_window->surfel_block_map.end()) {
        	    if(n_mated_surfels_in_mate_window < n_mated_surfels) {
        	      //        	      if(mate_current_block->n_surfels != this_current_block->n_surfels)
        	      //        	      msg_internal_error("Number of mated measurement surfels (%d) in flow surface meas file on face index %d does not match with"
        	      //        	          " mated measurement surfels (%d) in conduction surface measurement file ",mate_current_block->n_surfels, prev_meas_surfel_face_index, this_current_block->n_surfels);
        	      mate_current_block++;
        	      n_surfels_found_mate_in_block = 0;
        	      n_mated_surfels_in_mate_window += mate_current_block->n_surfels;
        	      mate_current_block->start_surfel_other_file = j;
        	    } else
        	      n_surfels_found_mate_in_block += this_current_block->n_surfels;
        	  } else {
        	    mate_current_block = mate_window->surfel_block_map.begin();
        	  }
        	}
        	sSRI_SURFEL_BLOCK_MAP b;
        	b.start_surfel_this_file = j;
        	if(mate_current_block != mate_window->surfel_block_map.end()) {
        	  b.start_surfel_other_file = mate_current_block->start_surfel_this_file + n_surfels_found_mate_in_block;
        	} else
        	  b.start_surfel_other_file = 0;
        	surface_window->surfel_block_map.push_back(b);
        	this_current_block = &surface_window->surfel_block_map.back();
              }
              prior_meas_surfel_has_a_mate = TRUE;
            }
             else
              prior_meas_surfel_has_a_mate = FALSE;
            if (is_curved_hx_inlet && num_vertices != 4) {
              msg_internal_error("Expected quad meas surfel for curved heat exchanger");  
            }

            surface_window->m_faces[j]             = meas_surfel.face_index;
            if (surface_window->m_facet_ids) {
              // facet IDs are only written to sampling surface meas files (in support of PowerCOOL)
              surface_window->m_facet_ids[j]         = meas_surfel.facet_id;
            }
            if (surface_window->m_inv_phys_norm)
              surface_window->m_inv_phys_norm[j] = meas_surfel.opposes_physics_surfel();

            surface_window->m_first_vertex_refs[j] = first_vertex_ref;
            first_vertex_ref                        += num_vertices;

            ccDOTIMES(k, num_vertices) {
              cDGF_MEAS_SURFEL_VERTEX_INDEX vi;
              vi.read(m_main_istream);
              DGF_VERTEX_INDEX vertex_index = vi.vertex_index;

              if (is_back_meas_surfel) {
                if (back_vertex_indices[vertex_index] == (DGF_VERTEX_INDEX)-1) { // cast added to suppress compiler warning.
                  sVPoint point = cp_info.vertex_array[vertex_index];
                  cp_info.vertex_array.push_back(point);
                  back_vertex_indices[vertex_index] = cp_info.vertex_array.size() - 1;
                }
                vertex_index = back_vertex_indices[vertex_index];
              }

              vertex_refs.push_back(vertex_index);

              if (!vertex_marks[ vertex_index ]) {
                vertex_marks[ vertex_index ] = true;
                surface_window->m_n_vertices++;

                sVPoint *vertex = &cp_info.vertex_array[vertex_index];
                ccDOTIMES(axis, n_dims) {
                  if (vertex->pcoord[axis] < meas_window->min_bound[axis])
                    meas_window->min_bound[axis] = floorf(vertex->pcoord[axis]);
                  if (vertex->pcoord[axis] > meas_window->max_bound[axis])
                    meas_window->max_bound[axis] = ceilf(vertex->pcoord[axis]);
                }
                if (is_curved_hx_inlet) {
                  hx_global_to_local_vertex_map[vertex_index] = n_hx_vertices++;
                  hx_global_vertices.push_back(vertex_index);
                }

              } // !vertex_marks

              if (is_curved_hx_inlet) {
                cDGF_MEAS_SURFEL_VERTEX_INDEX meas_surfel_vertex_index;
                meas_surfel_vertex_index.vertex_index = hx_global_to_local_vertex_map[vertex_index];
                ccDOTIMES(sp, total_sps) {
                  meas_surfel_vertex_index.write(g_sp_streams[sp]);
                }
              }

            } // num_vertices
            prev_meas_surfel_face_index = meas_surfel.face_index;
          } // num_meas_cells
          if(surface_window->m_mate_window_read) {
            for(auto i = 0; i <  cp_info.n_sri_faces; i++) {
              if(surface_window->n_mated_surfels_per_face[i] != surface_window->mate_window->n_mated_surfels_per_face[i])
                msg_internal_error("Number of mated measurement surfels on face %d of conduction (%d) and flow(%d) measurement file  \"%s\" does not match.", i, surface_window->n_mated_surfels_per_face[i], surface_window->mate_window->n_mated_surfels_per_face[i], cp_info.cdi_meas_windows[dgf_window.cdi_meas_window_index].name);
            }
          }
          if(num_points != dgf_window.num_measurements)
            msg_internal_error("Total number of measurements %d recorded on measurements surfels does not match with the reported number in LGI file %d", num_points, dgf_window.num_measurements);

          if(mate_window)
            mate_window->m_mate_window_read = TRUE;

          if (is_curved_hx_inlet) {
            cDGF_NUM_VERTICES num_vertices;
            num_vertices.n = hx_global_vertices.size();

            ccDOTIMES(sp, total_sps)
              num_vertices.write(g_sp_streams[sp]);

            ccDOTIMES(i, hx_global_vertices.size()) {
              cDGF_VERTEX_3D_SPOINT ref_point;
              sVPoint *vertex = &cp_info.vertex_array[hx_global_vertices[i]];
              ref_point.vtx_coord[0] = vertex->pcoord[0];
              ref_point.vtx_coord[1] = vertex->pcoord[1];
              ref_point.vtx_coord[2] = vertex->pcoord[2];
              ccDOTIMES(sp,total_sps) {
                ref_point.write(g_sp_streams[sp]);
              }
            }
          } // is_curved_hx_inlet

          // Copy vertex refs into meas window
          surface_window->m_n_vertex_refs = vertex_refs.size();
          surface_window->m_vertex_refs = xnew sriINT [ vertex_refs.size() ];
          ccDOTIMES(k, vertex_refs.size())
            surface_window->m_vertex_refs[k] = vertex_refs[k];
        }
        break;
      case LGI_POROUS_WINDOW:
      case LGI_FLUID_WINDOW:
      case LGI_VOLUME_WINDOW:
        if (meas_window->is_composite) {
          cDGF_MEAS_WIN_SUBRECS meas_win_subrecs;
          meas_win_subrecs.read(m_main_istream);
          sCP_COMPOSITE_FLUID_MEAS_WINDOW *fluid_window = static_cast<CP_COMPOSITE_FLUID_MEAS_WINDOW> (meas_window);
          ccDOTIMES(j, dgf_window.num_meas_cells) {
            cDGF_MEAS_COMPOSITE_CELL meas_cell;
            meas_cell.read(m_main_istream);
            fluid_window->m_part_indices[j] = meas_cell.part_index;
            fluid_window->m_part_volumes[j] = meas_cell.volume;
          }
        } else if (meas_window->is_development) {          //if development, read data from stream and drop on the floor
          cDGF_DEV_WINDOW_LINE_SEGMENTS dgf_segment;       //in order to get to correct location in LGI stream
          dgf_segment.read(m_main_istream);
          cDGF_MEAS_WIN_SUBRECS meas_win_subrecs;
           meas_win_subrecs.read(m_main_istream);
          ccDOTIMES(j, dgf_window.num_subrecs) {
            cDGF_FLUID_DEV_WINDOW_PART_EXTENT  extent;
            extent.read(m_main_istream);
          }
        }
        break;
      } // switch meas_window_type
    } // num_meas_cells > 0

  } // num_meas_windows

  // trim the vertex array when done adding any meas surfel back face vertices
  cp_info.vertex_array.trim();

}


// Read surfel descriptor just for getting surface normals, which is needed for local vel freeze

VOID sCP_DGF_READER::read_surfel_normals(REALM realm)
{
  if(realm == STP_FLOW_REALM) {
    jump_to_file_position(m_table_of_contents.flow_surfel_table_pos);
  } else {
    jump_to_file_position(m_table_of_contents.cond_surfel_table_pos);
  }

  cDGF_SURFEL_TABLE table_header;
  table_header.read(m_main_istream);
  // Declare surfel_desc outside the loop so that it is not repeatedly deleted and recreated
  cDGF_SURFEL_DESC surfel_desc;
  ccDOTIMES64(surfel_id, table_header.num_surfels) {
    surfel_desc.read(m_main_istream);
    g_seed_ctl.add_surface_normals(&surfel_desc);
  } // for num_surfels
}

//----------------------------------------------------------------------------
// read_surfel_descriptors
//----------------------------------------------------------------------------

template <typename SURFEL_PROC_GHOST>
static VOID process_dgf_surfel_desc(REALM realm, cDGF_SURFEL_DESC *surfel_desc, STP_PROC surfel_home_sp,
                                    std::vector<SURFEL_PROC_GHOST> &surfel_proc_ghosts)
{
  asINT32 n_dims            = cp_info.n_dims;
  asINT32 surfel_scale      = surfel_desc->s.surfel_scale;
  asINT32 sri_surfel_scale  = sim_scale_to_sri_scale(surfel_scale);
  sriLRF_INDEX ref_frame_index   = surfel_desc->s.lrf_index;

  if (cp_info.do_split_seed) {
    g_seed_ctl.add_surface_normals(surfel_desc);
  }
				  
  if (ref_frame_index >= 0) {
    ccDOTIMES(i, cp_info.n_rotational_dynamics_descs) {
      if (ref_frame_index == cp_info.rotational_dynamics_descs[i].m_lrf_index && !(surfel_desc->s.surfel_flags & DGF_SURFEL_REFERENCE_FRAME) ) {
        dFLOAT r[3], par_axis[3], perp_axis[3];
        dFLOAT *axis_origin = cp_info.rotational_dynamics_descs[i].m_axis_origin;
        vsub(r, surfel_desc->s.centroid, axis_origin);
        dFLOAT *a = cp_info.rotational_dynamics_descs[i].m_axis_direction;
        vscale(par_axis, vdot(r, a), a);
        vsub(perp_axis, r, par_axis);
        dFLOAT radius = sqrt(vdot(perp_axis, perp_axis));
        if (radius > cp_info.rotational_dynamics_descs[i].m_max_radius_lrf) {
          cp_info.rotational_dynamics_descs[i].m_max_radius_lrf = radius;
        }
      }
    }
  }

  std::vector < cDGF_SURFEL_MEAS_SURFEL_REFERENCE > &meas_surfel_refs = surfel_desc->meas_surfel_refs;
  std::vector<cDGF_SURFEL_MEAS_SURFEL_REFERENCE> layer_meas_surfel_refs;
  asINT32 last_meas_window_index = -1;

  // loop over all measurement cell references
  DO_STD_VECTOR(cDGF_SURFEL_MEAS_SURFEL_REFERENCE, meas_surfel_ref, meas_surfel_refs) {
    if (meas_surfel_ref.meas_window_index < last_meas_window_index)
      msg_internal_error("LGI surfel desc (ID %d) contains meas surfel references that are not"
                         " sorted by meas window index.",
                         surfel_desc->s.surfel_id);
    last_meas_window_index = meas_surfel_ref.meas_window_index;
    cBOOLEAN is_front = ((surfel_desc->s.surfel_flags & DGF_SURFEL_IS_BACK) == 0);
    cBOOLEAN is_volume_conduction = ((surfel_desc->s.surfel_flags & DGF_SURFEL_VOLUME_CONDUCTION) != 0);
    CP_MEAS_WINDOW window = cp_info.meas_windows[meas_surfel_ref.meas_window_index];
    STP_MEAS_CELL_INDEX meas_surfel_index = 0;
    cBOOLEAN has_two_meas = FALSE;
    asINT32 face_index = surfel_desc->s.face_index;
    asINT32 n_layers = cp_info.sri_faces[face_index].n_layers;
    if(window->n_surfels[face_index] == 0)
      face_index = cp_info.sri_faces[face_index].opposite_face;
    if(window->is_development)
      meas_surfel_index = meas_surfel_ref.meas_surfel_index;
    else {
      meas_surfel_index = cp_info.meas_cell_index_per_window.at(meas_surfel_ref.meas_window_index).at(meas_surfel_ref.meas_surfel_index);
      if(!window->is_composite) {
        has_two_meas = mci_has_two_meas(meas_surfel_index);
        meas_surfel_index >>= 1;
      }
      meas_surfel_index = window->first_point[face_index] + (meas_surfel_index - window->first_surfel[face_index]);
    }
    if(!window->is_development)
      cp_info.meas_surfel_index_per_window.at(meas_surfel_ref.meas_window_index).at(meas_surfel_index) = meas_surfel_ref.meas_surfel_index;
    if(has_two_meas && (surfel_desc->s.surfel_flags & DGF_SURFEL_IS_BACK) && !window->is_composite && !window->is_development) {
      meas_surfel_index += window->n_surfels[face_index];
      cp_info.meas_surfel_index_per_window.at(meas_surfel_ref.meas_window_index).at(meas_surfel_index) = meas_surfel_ref.meas_surfel_index;
    }
    if (window->is_development) {
      CP_SURFACE_DEV_MEAS_WINDOW win =
        static_cast<CP_SURFACE_DEV_MEAS_WINDOW> (window);
      STP_MEAS_CELL_INDEX segment_plus_axis = meas_surfel_index;
      asINT32 iface = surfel_desc->s.face_index;

      // extract segment and axis
      STP_MEAS_CELL_INDEX segment;
      uINT8 axis;
      lgi_meas_cell_index_dev_segment(segment_plus_axis, segment, axis);

      cBOOLEAN is_surfel_in_sliding_rf = (ref_frame_index >= 0
                                          && cp_info.sri_lrfs[ref_frame_index].type == SRI_LRF_MOVING);

      if (is_surfel_in_sliding_rf) {
        meas_surfel_index = win->m_face_first_meas_cell[axis][iface];
      }  else {
        meas_surfel_index = win->m_face_first_meas_cell[axis][iface] + (segment - win->m_face_first_segment[axis][iface]);
      }

      // replace segment with real meas_surfel_index
      meas_surfel_ref.meas_surfel_index = lgi_meas_dev_segment_cell_index(meas_surfel_index, axis);

      if (meas_surfel_index < 0)
        msg_internal_error("LGI surfel desc (ID %d) contains negative meas surfel index (%d) for window \"%s\"",
                           surfel_desc->s.surfel_id, meas_surfel_index, window->cdi_meas_window->name);

      if (is_surfel_in_sliding_rf) {

        STP_MEAS_CELL_INDEX face_n_segments   = win->m_face_n_segments[axis][iface];
        STP_MEAS_CELL_INDEX face_last_meas_cell = win->m_face_first_meas_cell[axis][iface] + face_n_segments;

        if (face_last_meas_cell > window->n_meas_cells)
          msg_internal_error("LGI surfel desc (ID %d) meas surfel index (%d) for window \"%s\" is out of range [0,%lu)",
                             surfel_desc->s.surfel_id, face_last_meas_cell,
                             window->cdi_meas_window->name, window->n_meas_cells);

        ccDOTIMES(j,face_n_segments) {
          STP_MEAS_CELL_INDEX imeas_surfel = meas_surfel_index + j;

          if (!window->is_meas_cell_on_sp(imeas_surfel, surfel_home_sp)) {
            window->m_sp_n_meas_cells[surfel_home_sp] ++;
            window->add_sp_meas_cell_ref(imeas_surfel, surfel_home_sp);
          }
        }
      } else {
        if (meas_surfel_index < 0)
          msg_internal_error("LGI surfel desc (ID %d) contains negative meas surfel index (%d) for window \"%s\"",
                             surfel_desc->s.surfel_id, meas_surfel_index, window->cdi_meas_window->name);

        if ( meas_surfel_index >= window->n_meas_cells)
          msg_internal_error("LGI surfel desc (ID %d) meas surfel index (%d) for window \"%s\" is out of range [0,%lu)",
                             surfel_desc->s.surfel_id, meas_surfel_index,
                             window->cdi_meas_window->name, window->n_meas_cells);

        if (!window->is_meas_cell_on_sp(meas_surfel_index, surfel_home_sp)) {
          window->m_sp_n_meas_cells[surfel_home_sp] ++;
          window->add_sp_meas_cell_ref(meas_surfel_index, surfel_home_sp);
        }
      }
    } else {
      cDGF_SURFEL_MEAS_SURFEL_REFERENCE meas_surfel_layer_ref;
      if(n_layers > 0 && (window->meas_window_type == LGI_SHELL_WINDOW || window->meas_window_type == LGI_SAMPLING_SHELL_WINDOW)) {
        ccDOTIMES(layer, n_layers+1) {
          asINT32 next_layer_meas_surfel_index = meas_surfel_index + layer*window->n_surfels[face_index];
          cp_info.meas_surfel_index_per_window.at(meas_surfel_ref.meas_window_index).at(next_layer_meas_surfel_index)
                                                                                = meas_surfel_ref.meas_surfel_index;
          meas_surfel_layer_ref.meas_surfel_index = next_layer_meas_surfel_index;
          meas_surfel_layer_ref.meas_window_index = meas_surfel_ref.meas_window_index;
          layer_meas_surfel_refs.push_back(meas_surfel_layer_ref);
        }
      }
      if (!window->is_meas_cell_on_sp(meas_surfel_index, surfel_home_sp)) {
         if ( meas_surfel_index >= window->n_meas_cells)
        msg_internal_error("LGI surfel desc (ID %d) meas surfel index (%d) for window \"%s\" is out of range [0,%lu)",
                           surfel_desc->s.surfel_id, meas_surfel_index,
                           window->cdi_meas_window->name, window->n_meas_cells);

        window->m_sp_n_meas_cells[surfel_home_sp] ++;
        window->add_sp_meas_cell_ref(meas_surfel_index, surfel_home_sp);
        if (n_layers > 0 && (window->meas_window_type == LGI_SHELL_WINDOW || window->meas_window_type == LGI_SAMPLING_SHELL_WINDOW)) {
          ccDOTIMES(layer, n_layers) {
            asINT32 next_layer_meas_surfel_index = meas_surfel_index + (layer+1)*window->n_surfels[face_index];
            window->m_sp_n_meas_cells[surfel_home_sp] ++;
            window->add_sp_meas_cell_ref(next_layer_meas_surfel_index, surfel_home_sp);
          }
        }
      }
      // for particle modeling, sampling surfels are ghosted, hence measurement cells associated
      // with these ghost sampling surfels should be created.
      if (cp_info.is_particle_solver && (window->meas_window_type == LGI_SAMPLING_SURFACE_WINDOW)) {
        ccDOTIMES(sp, surfel_proc_ghosts.size()) {
          SURFEL_PROC_GHOST ghost_sp = surfel_proc_ghosts[sp];
          if (!window->is_meas_cell_on_sp(meas_surfel_index, ghost_sp.ghost_sp)) {
            if ( meas_surfel_index >= window->n_meas_cells)
              msg_internal_error("LGI surfel desc (ID %d) meas surfel index (%d) for window \"%s\" is out of range [0,%lu)",
                                 surfel_desc->s.surfel_id, meas_surfel_index,
                                 window->cdi_meas_window->name, window->n_meas_cells);

            window->m_sp_n_meas_cells[ghost_sp.ghost_sp] ++;
            window->add_sp_meas_cell_ref(meas_surfel_index, ghost_sp.ghost_sp);
          }
        }
      }

      if (window->is_composite) {
        if (window->m_ref_frame_indices.size() != 0) { // will be NULL if there are no LRFs
          cBOOLEAN is_meas_cell_in_sliding_rf =
            (window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) < 0 ) ?
            FALSE :
            (cp_info.sri_lrfs[window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index)].type == SRI_LRF_MOVING);

          cBOOLEAN is_surfel_in_sliding_rf =
            (ref_frame_index < 0 ) ?
            FALSE :
            (cp_info.sri_lrfs[ref_frame_index].type == SRI_LRF_MOVING);

          if (window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) == SRI_INVALID_REF_FRAME_INDEX)  {
            window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) = ref_frame_index;
          } else if ((window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) != ref_frame_index) &&
                     (window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) != SRI_OVERLAP_REF_FRAME_INDEX) &&
                     (is_meas_cell_in_sliding_rf || is_surfel_in_sliding_rf) &&
                     (window->is_output_in_local_csys)) {
            if ((ref_frame_index != SRI_GLOBAL_REF_FRAME_INDEX) &&
                (window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) != SRI_GLOBAL_REF_FRAME_INDEX )) {
              msg_warn("Face \"%s\" spans local reference frames (\"%s\" and \"%s\"). Thus for"
                       " composite window \"%s\", vectors (force, velocity, etc) for this face"
                       " will be averaged in the global reference frame coordinate system.",
                       cp_info.sri_faces[surfel_desc->s.face_index].name,
                       cp_info.sri_lrfs[window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index)].name,
                       cp_info.sri_lrfs[ref_frame_index].name, window->cdi_meas_window->name);
            } else  {
              char *lrf_name = (ref_frame_index == SRI_GLOBAL_REF_FRAME_INDEX) ?
                cp_info.sri_lrfs[window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index)].name :
                cp_info.sri_lrfs[ref_frame_index].name;
              msg_warn("Face \"%s\" spans local reference frame \"%s\" and global reference frame. Thus for"
                       " composite window \"%s\", vectors (force, velocity, etc) for this face will be"
                       " averaged in the global reference frame coordinate system.",
                       cp_info.sri_faces[surfel_desc->s.face_index].name,lrf_name, window->cdi_meas_window->name);
            }
            window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) = SRI_OVERLAP_REF_FRAME_INDEX;
          }

          if (!window->is_meas_cell_on_sp(meas_surfel_index, surfel_home_sp)) {
            window->m_sp_n_meas_cells[surfel_home_sp] ++;
            window->add_sp_meas_cell_ref(meas_surfel_index, surfel_home_sp);
          }
        }
      }
    }
    if (window->is_composite || window->is_development) {
      if (cp_info.n_surface_couplings > 0) {
#if SURF_COUP
        DO_STD_VECTOR(cDGF_SURFEL_VERTEX_INDEX, vi, surfel_desc->vertices) {
          cMIO_MODELf::cMESH::sBG_POINT3 vertex = cp_info.native_mesh->GetVertexPoint( vi.vertex_index );
          ccDOTIMES(axis, n_dims) {
            if (vertex[axis] < window->min_bound[axis])
              window->min_bound[axis] = floorf(vertex[axis]);
            if (vertex[axis] > window->max_bound[axis])
              window->max_bound[axis] = ceilf(vertex[axis]);
          }
        }
#endif
      }
      else {
        DO_STD_VECTOR(cDGF_SURFEL_VERTEX_INDEX, vi, surfel_desc->vertices) {
          sVPoint *vertex = &cp_info.vertex_array[ vi.vertex_index ];
          ccDOTIMES(axis, n_dims) {
            if (vertex->pcoord[axis] < window->min_bound[axis])
              window->min_bound[axis] = floorf(vertex->pcoord[axis]);
              if (vertex->pcoord[axis] > window->max_bound[axis])
                window->max_bound[axis] = ceilf(vertex->pcoord[axis]);
          }
        }
      }
    } else {
      CP_SURFACE_MEAS_WINDOW win = static_cast<CP_SURFACE_MEAS_WINDOW > (window);
      win->m_scales[meas_surfel_ref.meas_surfel_index] &= ~0x80;
      if (is_sri_scale_finer(sri_surfel_scale, win->m_scales[meas_surfel_ref.meas_surfel_index])) {
        win->m_scales[meas_surfel_ref.meas_surfel_index] = sri_surfel_scale;
      }
     
        if (window->m_ref_frame_indices.size() != 0) { // will be NULL if there are no LRFs
          if (window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) == SRI_INVALID_REF_FRAME_INDEX)
            window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) = ref_frame_index;
          else if (window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) != ref_frame_index)
            msg_internal_error("LGI surfel desc (ID %d, ref frame %d) meas surfel index (%d) for window \"%s\""
                               " refers to meas surfel already associated with ref frame %d",
                               surfel_desc->s.surfel_id, ref_frame_index, meas_surfel_index, window->cdi_meas_window->name,
                               window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index));
        }
        // Meas surfel normals should always point towards the front (the user’s notion of the front).
        // Careful: A conduction surfel on a closed shell that interacts with conduction voxels is tagged as “front”,
        // but it’s normal points into the closed shell.

        STP_EVEN_ODD even_odd = surfel_desc->s.surfel_flags & DGF_SURFEL_EVEN_ODD_MASK;
        if (even_odd & STP_PROCESS_ON_EVEN_TIMES && (!has_two_meas || is_front))
          win->m_d_surfel_areas[meas_surfel_ref.meas_surfel_index] += surfel_desc->s.area;
        BOOLEAN invert_phys_surfel = win->m_inv_phys_norm[meas_surfel_ref.meas_surfel_index];
        STP_MEAS_COORD_INDEX meas_coord_index = ((STP_MEAS_COORD_INDEX) meas_surfel_ref.meas_surfel_index) * n_dims;
        dFLOAT nx = is_front ? surfel_desc->s.normal[0] : -surfel_desc->s.normal[0];
        dFLOAT ny = is_front ? surfel_desc->s.normal[1] : -surfel_desc->s.normal[1];
        dFLOAT nz = 0.0;
        if (n_dims == 3)
          nz = is_front ? surfel_desc->s.normal[2] : -surfel_desc->s.normal[2];
        if (invert_phys_surfel) {
          win->m_surfel_normals[meas_coord_index + 0] = -nx;
          win->m_surfel_normals[meas_coord_index + 1] = -ny;
          if (n_dims == 3)
            win->m_surfel_normals[meas_coord_index + 2] = -nz;
        }
        else {
          win->m_surfel_normals[meas_coord_index + 0] = nx;
          win->m_surfel_normals[meas_coord_index + 1] = ny;
          if (n_dims == 3)
            win->m_surfel_normals[meas_coord_index + 2] = nz;
        }
    }
    if(!window->is_development)
      meas_surfel_ref.meas_surfel_index = meas_surfel_index;

  }
//  Only update the meas surfel refs if there are layers attached to the surfel's face
  if(layer_meas_surfel_refs.size() > surfel_desc->meas_surfel_refs.size()) {
    surfel_desc->meas_surfel_refs.clear();
    surfel_desc->meas_surfel_refs.insert(surfel_desc->meas_surfel_refs.end(), layer_meas_surfel_refs.begin(), layer_meas_surfel_refs.end());
    surfel_desc->s.num_meas_surfel_refs = surfel_desc->meas_surfel_refs.size();
  }
}

#if SURF_COUP
VOID sCP_DGF_READER::get_surface_coupling_info_from_surfel(cDGF_SURFEL_DESC& surfel_desc, 
                                                           std::map<DGF_SURFEL_INDEX, sSURFEL_TO_FACET_INFO> &surfel_to_facet_map)
{
  // NOTE: the following depends on read_global_face_names_record having been called already
  asINT32 cdi_face_index = get_cdi_face_index(surfel_desc.s.face_index);

  // face index in the LGI file not found in the global face name record in the
  // LGI file (PR11559), so do not add this face to the surface coupling
  // native mesh
  COMPONENT face_component;
  if (cdi_face_index < 0) {
    face_component = NULL;
  } else {
    face_component = cp_info.native_model->GetComponentById(cdi_face_index);
  }

  // do not include mirror, conduction (only flow surfels are coupled to external solvers), or odd surfels in the BREP mesh
  STP_EVEN_ODD even_odd = surfel_desc.s.surfel_flags & DGF_SURFEL_EVEN_ODD_MASK;
  if ((surfel_desc.s.surfel_flags & (DGF_SURFEL_IS_MIRROR | DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION)) ||
      !(even_odd & STP_PROCESS_ON_EVEN_TIMES)) {
    return;
  }

  // face_component will be NULL if this is not a selected face. However, does not return yet in case is part of an open
  // shell and needs to load the other surfel of the front/back pair
  BOOLEAN skip_surfel = (NULL == face_component);

  // For surfels derived from shells, we skip the inverted surfel but enable the back in the MIO component if both
  // front and back surfels belong to the same face. 
  // We need to defer loading the surfel until we process the second one, moment when we have all the information 
  // needed to determine how to load them. Therefore, we use a map to link front and back temporarily. Once both are 
  // processed, we erase the entry to the map so its size will remain small, specially since the discretizer stores 
  // closely front and back (inverted) surfels in the LGI
  // NOTE: To be stored and retrieved properly, needs to be a pure boolean. Bitset flag check yields 4 when is true, 
  //       but that value yields erroneusly false when calling IsFacetFrontAndBack() method. Hence the !=0 comparison.
  BOOLEAN include_back = ((surfel_desc.s.surfel_flags & DGF_SURFEL_DERIVED_FROM_OPEN_SHELL) != 0); 
  STP_SURFEL_ID inverted_clone_index = -1;
  if (include_back) { //it is a shell, front & back surfels exist, 
    if (surfel_desc.s.opposite_index < 0) {
      msg_internal_error("Surfel ID %d derived from shell with no opposite index specified.",surfel_desc.s.surfel_id);
    }
    if (surfel_desc.s.surfel_id < surfel_desc.s.opposite_index) { 
      //1st of front/back pair being processed
      if (!skip_surfel) {
        //stores info needed to load it when the second one is processed
        sSURFEL_TO_FACET_INFO first_surfel_info(face_component, surfel_desc);
        surfel_to_facet_map[surfel_desc.s.surfel_id] = first_surfel_info;
      }
      return;
    } else { 
      //2nd one being processed, retrieves info from the map and erases it to keep its size small
      auto it = surfel_to_facet_map.find(surfel_desc.s.opposite_index);
      if (it == surfel_to_facet_map.end()) {
        //first processed face of the front/back pair not part of coupling
        if (skip_surfel) { //this face not involved in coupling neither, nothing to be done
          return;
        } else { //updates flag to load this surfel as a regular one
          include_back = false;
        }
      } else {
        if (skip_surfel) {
          //Only previous face is part of coupling, loads it and returns since back is not part of coupling
          add_facet_and_coupling_info(it->second, false);
          surfel_to_facet_map.erase(it);
          return;
        } else if (face_component != it->second.m_face_component) { 
          //front and back assigned to different faces, treats both as if they were front
          add_facet_and_coupling_info(it->second, false); //loads the previous here before clearing the map
          include_back = false; //updates flag to load this surfel as a regular one 
        } else if (surfel_desc.s.surfel_flags & DGF_SURFEL_OPEN_SHELL_INVERTED) { //is_back
          //needs to load the front data stored in the map, but first store the proper inverted clone 
          it->second.m_coupling_info.inverted_clone_id = static_cast<STP_SURFEL_ID>(surfel_desc.s.clone_index);
          add_facet_and_coupling_info(it->second, true);
          surfel_to_facet_map.erase(it);
          return;
        } else {
          inverted_clone_index = it->second.m_coupling_info.clone_id;
        }
        surfel_to_facet_map.erase(it);
      }
    }
  } else if (skip_surfel) {//closed shell, nothing left to be done
    return;
  }

  //We could load the info into a sSURFEL_TO_FACET_INFO and call add_facet_and_coupling_info(..). However, most  
  //of the surfels are not shells usually so done directly here instead to avoid extra overhead of copying data.
  MIO_COMPONENT_INDEX face_component_index = face_component->GetIndex();
  cp_info.native_mesh->FacetBegin(BREP_INVALID_IFACET);

  DO_STD_VECTOR(cDGF_SURFEL_VERTEX_INDEX, vi, surfel_desc.vertices) {
    cp_info.native_mesh->FacetAddVertex(vi.vertex_index);
  }

  iBREP_FACET facet_index = cp_info.native_mesh->FacetEnd();
  if (facet_index == BREP_INVALID_IFACET)
    msg_internal_error("Failed to add surfel ID %d to the surface coupling mesh.",
                       surfel_desc.s.surfel_id);

  cp_info.native_mesh->AppendToElementMap(surfel_desc.s.surfel_id);
  FACET facet = cp_info.native_mesh->GetFacet(facet_index);
  facet->SetComponentIndex(face_component_index);
  face_component->AddFacet(facet_index, include_back);

  // can look up the proc for the specific surfel id from cp_info.surfel_procs
  asINT32 sri_surfel_scale  = sim_scale_to_sri_scale(surfel_desc.s.surfel_scale);
  sSURFEL_COUPLING_INFO surfel_coupling_info = {surfel_desc.s.surfel_id,
                                                static_cast<STP_SURFEL_ID>(surfel_desc.s.clone_index),
                                                static_cast<STP_SURFEL_ID>(surfel_desc.s.opposite_index),
                                                inverted_clone_index,
                                                static_cast<sFLOAT>(surfel_desc.s.area),
                                                static_cast<STP_SCALE>(sri_surfel_scale)
#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
                                                ,surfel_desc.s.lrf_index,
                                                surfel_desc.s.face_index
#endif
  };
  // NOTE: the following is ordered by increasing facet_index and in general facet_index != surfel id
  m_facet_index_surfel_coupling_info.push_back(surfel_coupling_info);
}

VOID sCP_DGF_READER::
add_facet_and_coupling_info(sSURFEL_TO_FACET_INFO &surfel_info, BOOLEAN include_back) {
  //Adds facet to the mesh
  MIO_COMPONENT_INDEX face_component_index = surfel_info.m_face_component->GetIndex();
  cp_info.native_mesh->FacetBegin(BREP_INVALID_IFACET);

  DO_STD_VECTOR(cDGF_SURFEL_VERTEX_INDEX, vi, surfel_info.m_vertices) {
    cp_info.native_mesh->FacetAddVertex(vi.vertex_index);
  }

  iBREP_FACET facet_index = cp_info.native_mesh->FacetEnd();
  if (facet_index == BREP_INVALID_IFACET)
    msg_internal_error("Failed to add surfel ID %d to the surface coupling mesh.",
                       surfel_info.m_coupling_info.id);

  //Adds facet to the map
  cp_info.native_mesh->AppendToElementMap(surfel_info.m_coupling_info.id);
  FACET facet = cp_info.native_mesh->GetFacet(facet_index);
  facet->SetComponentIndex(face_component_index);
  surfel_info.m_face_component->AddFacet(facet_index, include_back);

  //Adds coupling info
  //NOTE: the following is ordered by increasing facet_index and in general facet_index != surfel id
  m_facet_index_surfel_coupling_info.push_back(surfel_info.m_coupling_info);
}
#endif // SURF_COUP

// fill mask and boundary seeding data for velocity profile transfer
// from sampled face measurement file to corresponding seeded faces

static VOID fill_seed_from_meas_data(cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data, cDGF_SURFEL_DESC &surfel_desc, asINT32 frame_index = -1)
{
  asINT32 seed_from_meas_desc_index = (surfel_desc.s.face_index < cp_info.n_sri_faces)?
                          cp_info.seed_from_meas_desc_index_from_face_index[surfel_desc.s.face_index] : -1;
  if (seed_from_meas_desc_index == -1) {
    seed_from_meas_data.mask = 0;
  } else {
    if(cp_info.is_face_tbs[surfel_desc.s.face_index] && cp_info.seed_from_meas_descs[seed_from_meas_desc_index]->m_transient_boundary_seeding)
      return;
    cp_info.seed_from_meas_descs[seed_from_meas_desc_index]->fill_seed_from_meas_data(seed_from_meas_data,
                                                             surfel_desc,
                                                             seed_from_meas_desc_index, frame_index);
  }
}

static VOID fill_transient_boundary_seeding_map(cDGF_SURFEL_DESC &surfel_desc, cDGF_TBS &tbs, STP_PROC home_sp)
{
  asINT32 seed_from_meas_desc_index = (surfel_desc.s.face_index < cp_info.n_sri_faces)?
                          cp_info.seed_from_meas_desc_index_from_face_index[surfel_desc.s.face_index] : -1;
  if (seed_from_meas_desc_index == -1) {
    return;
  } else {
    if(cp_info.is_face_tbs[surfel_desc.s.face_index] &&
        cp_info.seed_from_meas_descs[seed_from_meas_desc_index]->m_transient_boundary_seeding) {
      cp_info.m_n_surfels_on_transient_seeded_faces ++;
      tbs.meas_index = seed_from_meas_desc_index;
      static_cast<TRANSIENT_BOUNDARY_SEEDING>(cp_info.seed_from_meas_descs
          [seed_from_meas_desc_index])->fill_transient_boundary_seeding_map(surfel_desc, home_sp, tbs);
    }
  }
}

template <sCP_DGF_READER::reader_t RT>
VOID sCP_DGF_READER::read_surfel_descriptors(REALM realm)
{
  jump_to_file_position( realm == STP_FLOW_REALM ? m_table_of_contents.flow_surfel_table_pos : m_table_of_contents.cond_surfel_table_pos );
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE ) {
    if (cp_info.is_full_checkpoint_restore && realm == STP_FLOW_REALM) {
      jump_to_ckpt_file_position(m_ckpt_table_of_contents.surfel_shob_state_pos);
      read_ckpt_surfel_dyn_data_header();
    }
  }

  sINT64 n_surfels_per_scale[STP_MAX_SCALES] = { 0 };
  cDGF_SURFEL_TABLE table_header;
  cDGF_PROC_ID proc;
  cDGF_PROC_ID radiation_patch_proc;
  table_header.read(m_main_istream);

  ccDOTIMES(i, cp_info.n_seed_from_meas_descs) {
    cp_info.seed_from_meas_descs[i]->setup();
    if(!cp_info.seed_from_meas_descs[i]->m_transient_boundary_seeding)
      continue;
    TRANSIENT_BOUNDARY_SEEDING tbs_desc = static_cast<TRANSIENT_BOUNDARY_SEEDING>(cp_info.seed_from_meas_descs[i]);
    tbs_desc->m_tbs_info.resize(total_sps);
  }

  cDGF_SEED_FROM_MEAS_INFO seed_from_meas_info;
  seed_from_meas_info.n_seed_from_meas_descs = cp_info.n_seed_from_meas_descs;
  
  table_header.tag.length = 0; // set to a non-positive number since record length is not known apriori

  
  ccDOTIMES(sp, total_sps) {
    table_header.write(g_sp_streams[sp], FALSE);
    seed_from_meas_info.write(g_sp_streams[sp]);
    ccDOTIMES(imeas, cp_info.n_seed_from_meas_descs) {
      cDGF_SMART_SEED_CONTROL seed_from_meas_control;
      cp_info.seed_from_meas_descs[imeas]->fill_seed_params(seed_from_meas_control);
      lgi_write(g_sp_streams[sp], seed_from_meas_control);
    }
  }

  if (total_sps > 1) {

    if(realm == STP_FLOW_REALM) {
      verify_next_tag_id(DGF_FLOW_SURFEL_DECOMPOSITION_TAG, m_decomp_istream);
    } else {
      verify_next_tag_id(DGF_COND_SURFEL_DECOMPOSITION_TAG, m_decomp_istream);
    }

    cDGF_SURFEL_PROC_TABLE surfel_proc_table;
    surfel_proc_table.read(m_decomp_istream);

    if (table_header.num_surfels != surfel_proc_table.num_surfels) {
      msg_internal_error("Mismatch in decomposition record. Expected %u surfels, but found %u surfels",
                         table_header.num_surfels, surfel_proc_table.num_surfels);
    }
  }

#if SURF_COUP
  if (realm == STP_FLOW_REALM && cp_info.n_surface_couplings > 0) {
    m_facet_index_surfel_coupling_info.reserve(table_header.num_surfels);
  }
  std::map<DGF_SURFEL_INDEX, sSURFEL_TO_FACET_INFO> surfel_to_facet_map; 
#endif // SURF_COUP

  char status[256];
  dFLOAT one_over_num_surfels = 1.0 / table_header.num_surfels;
  asINT32 percent_surfels_processed = 0;
  cp_jobctl_output_status("Initializing surfels");

  STP_PROC home_sp = 0;
#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  std::vector<char> ckpt_buf; //changed this to eliminate need for a specific LGI_MAX_SHOB_CKPT_LEN
#else
  char ckpt_buf[LGI_MAX_SHOB_CKPT_LEN];
#endif
  std::vector<cDGF_SURFEL_PROC_GHOST> surfel_proc_ghosts;
  // Declare surfel_desc outside the loop so that it is not repeatedly deleted and recreated
  cDGF_SURFEL_DESC surfel_desc;
  cDGF_SURFEL_PROC surfel_proc;
  cDGF_SEED_FROM_MEAS_DATA seed_from_meas_data;

  m_contact_info_map.clear();

  // Open HDF5 file in case of reading data from the checkpoint
  const bool read_surfels_from_hdf5 = cp_info.is_full_checkpoint_restore &&
    table_header.num_surfels > 0;

  LocalReader<RT, false> * const reader = read_surfels_from_hdf5 ?
    new LocalReader<RT, false>(initLocalReader<RT>(m_ckpt_istream, sim_args.resume_filename, "Surfels")) :
    nullptr;

  ccDOTIMES64(surfel_id, table_header.num_surfels) {

    cDGF_TBS tbs;
    tbs.transient_boundary_seeding = FALSE;
    surfel_proc_ghosts.clear();
    surfel_desc.read(m_main_istream);
    asINT32 seed_from_meas_desc_index = -1;
    if (cp_info.n_seed_from_meas_descs > 0 ) {
      seed_from_meas_desc_index = (surfel_desc.s.face_index < cp_info.n_sri_faces)?
          cp_info.seed_from_meas_desc_index_from_face_index[surfel_desc.s.face_index] : -1;
      fill_seed_from_meas_data(seed_from_meas_data, surfel_desc);
    }
       STP_EVEN_ODD even_odd = surfel_desc.s.surfel_flags & DGF_SURFEL_EVEN_ODD_MASK;
    if ((even_odd & STP_PROCESS_ON_EVEN_TIMES) && ((surfel_desc.s.surfel_flags & DGF_SURFEL_SAMPLING) == 0))
      n_surfels_per_scale[surfel_desc.s.surfel_scale] ++;

    if (total_sps > 1) {
      surfel_proc.read(m_decomp_istream);
      home_sp = surfel_proc.home_sp;

      cp_info.surfel_procs[realm].push_back(home_sp);

      if (home_sp >= total_sps) {
        msg_internal_error("Decomposition record for surfel ID %u references processor %d, "
                           " but the simulation has only %d processors",
                           (STP_SURFEL_ID)surfel_id, home_sp, total_sps);
      }
      ccDOTIMES(ghost, surfel_proc.num_ghost_sps) {
        cDGF_SURFEL_PROC_GHOST surfel_proc_ghost;
        surfel_proc_ghost.read(m_decomp_istream);
        surfel_proc_ghosts.push_back(surfel_proc_ghost);
      }
    }
    if (cp_info.n_seed_from_meas_descs > 0 ) {
      if(cp_info.tbs_info.is_tbs) {
        fill_transient_boundary_seeding_map(surfel_desc, tbs, home_sp);
      }
    }
    if(tbs.transient_boundary_seeding) {
      TRANSIENT_BOUNDARY_SEEDING tbs_desc = static_cast<TRANSIENT_BOUNDARY_SEEDING>(cp_info.seed_from_meas_descs[seed_from_meas_desc_index]);
      auINT32 var_mask = cp_info.seed_from_meas_mask_from_face_index[surfel_desc.s.face_index];
      tbs_desc->insert_value_in_n_var_mask_map(var_mask);
      tbs_desc->increment_tbs_info(var_mask, home_sp);
    }
    // 1. Send home_sp to the home SP and all ghost SPs
    //    - Each SP uses this to decide whether or not they own the surfel
    // 2. Send a ghost count and all the ghost SP indices to the home SP
    // 3. Send surfel_desc to the home SP and all ghost SPs
    // 4. Send the surfel ckpt data to the home SP (on full ckpt restore)
    // this routine may alter meas cell indices in surfel_desc

    process_dgf_surfel_desc(realm, &surfel_desc, home_sp, surfel_proc_ghosts);

    //stores the proc_id if it is surfel involved in conctact across gaps
    if ((surfel_desc.s.surfel_flags & (DGF_CONTACT_SURFEL_PRIMARY | DGF_CONTACT_SURFEL_SECONDARY))
        && surfel_desc.s.opposite_index < 0 && realm == STP_COND_REALM) {
      sSURFEL_CONTACT_INFO surfel_contact_info(home_sp, surfel_desc.s.surfel_scale, surfel_desc.s.surfel_flags);
      m_contact_info_map[surfel_desc.s.surfel_id] = surfel_contact_info;
    }

    // Checking consistency of realm and surfel flags. Warning for wrong flags/realm
    // Workaround for conduction surfels with no conduction bits set - surfel wight sets = 0
    // Avoids segfault in the SP when s2s data is initialized.
    if (realm == STP_FLOW_REALM) {
      //flow surfels are also tagged as DGF_CONTACT_SURFEL_PRIMARY / DGF_CONTACT_SURFEL_SECONDARY to enforce the use of
      //adiabatic BC rather than using the face boundary condition
      if ( (surfel_desc.s.surfel_flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION)) ) {
        msg_warn("CP: Surfel %d in Flow table has conduction bits set", surfel_desc.s.surfel_id);
        surfel_desc.s.num_interacting_ublks = 0;
        surfel_desc.s.num_surfel_weight_sets = 0;
      }
    } else {
      if (!(surfel_desc.s.surfel_flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION)) ) {
        msg_warn("CP: Surfel %d in Cond table has no conduction bits set", surfel_desc.s.surfel_id);
        surfel_desc.s.num_surfel_weight_sets = 0;
        surfel_desc.s.num_interacting_ublks = 0;
      }
    }


#if SURF_COUP
    if (realm == STP_FLOW_REALM && cp_info.n_surface_couplings > 0) {
      get_surface_coupling_info_from_surfel(surfel_desc, surfel_to_facet_map);
    }
#endif
    //msg_print("CP: realm %d id %d sp %d, ghosts %d",realm,surfel_id,home_sp, surfel_proc_ghosts.size());

    cDGF_SURFEL_PROC proc_msg;
    proc_msg.home_sp = home_sp;
    proc_msg.num_ghost_sps = surfel_proc_ghosts.size();
    proc_msg.write(g_sp_streams[home_sp]);

    tbs.write(g_sp_streams[home_sp]);
    ccDOTIMES(ghost, surfel_proc_ghosts.size()) {
      //proc.proc_id = surfel_proc_ghosts[ghost].ghost_sp;
      //proc.write(g_sp_streams[home_sp]);
      surfel_proc_ghosts[ghost].write(g_sp_streams[home_sp]);
    }

    //Counting degrees of freedom for the implicit shell conduction solver
    surfel_desc.s.implicit_shell_state_index = -1; 
    if (surfel_desc.s.surfel_flags & DGF_SURFEL_SHELL_CONDUCTION) {
      int even_odd_mask = surfel_desc.s.surfel_flags & DGF_SURFEL_EVEN_ODD_MASK;
      bool is_even = even_odd_mask == STP_PROCESS_ON_EVEN_TIMES;
      if (!is_even) { // only use odd surfels for implicit shell solver
        surfel_desc.s.implicit_shell_state_index = cp_info.implicit_shell_solver_state_index_offset[home_sp];
        asINT32 n_layers = cp_info.sri_faces[surfel_desc.s.face_index].n_layers;
        cp_info.implicit_shell_solver_state_index_offset[home_sp] += n_layers;
        cp_info.num_implicit_shell_solver_states += n_layers;
      }
    }

    surfel_desc.write(g_sp_streams[home_sp]);

    if (cp_info.n_seed_from_meas_descs > 0 ) {
      seed_from_meas_data.write(g_sp_streams[home_sp]);
    }

    if (cp_info.is_radiation) {
      if (surfel_desc.s.radiation_patch_id >= 0) {
        radiation_patch_proc.proc_id = 0;
        if (total_rps > 1) {
          radiation_patch_proc.proc_id = cp_info.patch_decomp.patch_rp(surfel_desc.s.radiation_patch_id);
        }
        radiation_patch_proc.write(g_sp_streams[home_sp]);

        radiation_patch_proc.proc_id = -1;
        if (surfel_desc.s.backside_radiation_patch_id >= 0) {
          if (total_rps > 1) {
            radiation_patch_proc.proc_id = cp_info.patch_decomp.patch_rp(surfel_desc.s.backside_radiation_patch_id);
            if (radiation_patch_proc.proc_id == RadIO::INVALID_PATCH_RP) {
              msg_error("Unable to get patch rp");
            }
          }
        }
        // Always send both, even if the second one is invalid
        radiation_patch_proc.write(g_sp_streams[home_sp]);
      }

    }

    ccDOTIMES(ghost, surfel_proc_ghosts.size()) {
      // Use the num_ghost_sps slot to send the ghost_flags to the ghost SPs
      proc_msg.home_sp = home_sp;
      proc_msg.num_ghost_sps = surfel_proc_ghosts[ghost].ghost_flags;
      //msg_print("CP: realm %d id %d ghost_sp %d",realm,surfel_id,surfel_proc_ghosts[ghost].ghost_sp);
      proc_msg.write(g_sp_streams[surfel_proc_ghosts[ghost].ghost_sp]);
      surfel_desc.write(g_sp_streams[surfel_proc_ghosts[ghost].ghost_sp]);
      if (cp_info.n_seed_from_meas_descs > 0 ) {
        seed_from_meas_data.write(g_sp_streams[surfel_proc_ghosts[ghost].ghost_sp]);
      }
    }

    if (cp_info.is_full_checkpoint_restore) {
      cDGF_CKPT_SHOB_HEADER shob_header;
      READPTR_GENERIC_VAR_FROM_FILE(shob_header);
      shob_header.write(g_sp_streams[home_sp]);

#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
      if (ckpt_buf.size() < shob_header.len) {
        ckpt_buf.resize(2 * shob_header.len);
      }
      READPTR_GENERIC_ARRAY_FROM_FILE(ckpt_buf.data(), shob_header.len);
      lgi_write(g_sp_streams[home_sp], &ckpt_buf[0], shob_header.len);
#else
      READPTR_GENERIC_ARRAY_FROM_FILE(ckpt_buf, shob_header.len);
      lgi_write(g_sp_streams[home_sp], ckpt_buf, shob_header.len);
#endif
    }

    asINT32 percent_surfels = (surfel_id+1) * (100.0 * one_over_num_surfels);
    if (percent_surfels > percent_surfels_processed) {
      percent_surfels_processed = percent_surfels;

      sprintf(status, "Initializing surfels (%d%% complete [%u of %u])",
              percent_surfels_processed, (STP_SURFEL_ID)surfel_id+1, table_header.num_surfels);
      cp_jobctl_output_status(status);
      maybe_terminate_simulation();
    }

  } // for num_surfels

  // Close HDF5 file and its objects. Print message of completeness.
  if (reader)
  {
    delete reader;
    sprintf(status, "Initializing surfels (100%% complete [%d of %d])",
            table_header.num_surfels, table_header.num_surfels);
  }
  cp_jobctl_output_status(status);

  DO_SCALES_FINE_TO_COARSE(scale) {
    cp_info.total_regular_surfels += n_surfels_per_scale[scale];
    cp_info.total_regular_fesus   += (dFLOAT)n_surfels_per_scale[scale] / (dFLOAT)scale_to_delta_t(scale);
  }

  ccDOTIMES(i, cp_info.n_seed_from_meas_descs) {
    cp_info.seed_from_meas_descs[i]->compute_scale_factors();
    ccDOTIMES(sp, total_sps) {
      cp_info.seed_from_meas_descs[i]->write_scale_factors(g_sp_streams[sp]);
    }
    cp_info.seed_from_meas_descs[i]->free_memory();
  }
}

template VOID sCP_DGF_READER::read_surfel_descriptors<sCP_DGF_READER::LEGACY>(REALM realm);
template VOID sCP_DGF_READER::read_surfel_descriptors<sCP_DGF_READER::HDF5_SERIAL>(REALM realm);
template VOID sCP_DGF_READER::read_surfel_descriptors<sCP_DGF_READER::DEBUG_MODE>(REALM realm);

VOID sCP_DGF_READER::read_gap_contact_table_descriptor()
{
  //Reads complete contact table from stream
  jump_to_file_position(m_table_of_contents.gap_contact_table_pos);
  cDGF_GAP_CONTACT_TABLE_DESC contact_table_desc;
  contact_table_desc.read(m_main_istream);

  //It is important to ensure consistency between surfel attributes and contact info to avoid failures when being
  //processed. To do so, two checks need to be done
  // 1. All surfels within the table need to be marked as contact_surfels (contact->surfel check)
  //    - This is done here as we parse the table, moment when we traverse all weight items.
  //    - As the surfels table is read, done before the contact, we add an entry to m_contact_info_map for every
  //      primary/secondary surfel. Thus, as we process the contact table here, all surfels with an associated weight
  //      must have an entry in the table to satisfy this check.
  // 2. All contact_surfels marked as primary need to have an entry in the contact table (surfel->contact check)
  //    - This check is done later, when loading the weights into the contact_surfel groups triggered when the 
  //      contact_surfel fsets are initialized. At that point, all contact_surfels are traversed and expect to 
  //      retrieve info from the global map, so it is a logical place to check if it exists to satisfy this check.
  
  //Loop through the map, accumulating areas to determine the total contact area associated with each surfel.
  //Additionally, this is the first time it loops through the surfels involved in contact, which should have been 
  //loaded & initialized in the contact_info_map when parsing the surfels table before. Thus, it does a sanity 
  //check here to ensure all are present in the table
  uINT32 j_begin = 0;
  ccDOTIMES(i, contact_table_desc.t.num_contact_area_sets) {
    STP_SURFEL_ID primary_surfel_id = contact_table_desc.contact_area_sets[i].primary_surfel_id;
    auto it_primary = m_contact_info_map.find(primary_surfel_id);
    if (it_primary==m_contact_info_map.end()) {
      msg_internal_error("Surfel %d present in contact table, but not identified from surfel attributes", primary_surfel_id);
    }
    sSURFEL_CONTACT_INFO *primary_info = &(it_primary->second);
    primary_info->primary_num_items = (uINT32)contact_table_desc.contact_area_sets[i].num_contact_area_items;
    uINT32 j_end = j_begin + primary_info->primary_num_items;
    for (uINT32 j=j_begin; j<j_end; j++) {
      STP_SURFEL_ID secondary_surfel_id = contact_table_desc.contact_area_items[j].secondary_surfel_id;
      auto it_secondary = m_contact_info_map.find(secondary_surfel_id);
      if (it_secondary==m_contact_info_map.end()) {
        msg_internal_error("Surfel %d present in contact table, but not identified from surfel attributes", secondary_surfel_id);
      }
      sSURFEL_CONTACT_INFO *secondary_info = &(it_secondary->second);
      sFLOAT area = (sFLOAT)contact_table_desc.contact_area_items[j].contact_area;
      // msg_print("table %d %d %f", primary_surfel_id, secondary_surfel_id, area);
#ifdef ENABLE_CONSISTENCY_CHECKS
      if (area == 0.0) { 
        //Zero area contact not expected from the area mapping determined in discretizer, issue a warning and remove it
        msg_warn("Found entry with zero contact area between %d %d surfels", primary_surfel_id, secondary_surfel_id);
        primary_info->primary_num_items--;
        continue;
      }
#endif
      //If contact is in a VR region, discretizer includes all even/odd combinations in the contact table. However, when
      //a even_and_odd surfel is in contact with an even_or_odd surfel, the even_and_odd surfel will have a duplicated
      //entry in the contact table that should be skipped in the accumulation to compute the total area.
      if ((primary_info->even_odd != STP_PROCESS_ON_ALL_TIMES) || (secondary_info->even_odd & STP_PROCESS_ON_EVEN_TIMES)) {
        primary_info->total_contact_area += area;
      }
      if ((secondary_info->even_odd != STP_PROCESS_ON_ALL_TIMES) || (primary_info->even_odd & STP_PROCESS_ON_EVEN_TIMES)) {
        secondary_info->total_contact_area += area;
      }
      //Store the reciprocal contact (secondary->primary) in the surfel_info data if:
      //- primary & secondary are in different SPs 
      //- secondary will fall in a group processed before the primary, with groups processed from finer (highest value)
      //  to coarser (smallest value) scales, and within each scale groups ordered from lowest to highest dest_sp
      //since contact will be computed independently in each SP or by the group containing the secondary prior to the
      //one containing the primary one within the SP.
      bool in_same_sps = (primary_info->proc_id == secondary_info->proc_id);
      if (!in_same_sps || is_reversed_contact_order(primary_info, secondary_info)) {
        cDGF_GAP_CONTACT_AREA_ITEM reciprocal_item;
        reciprocal_item.secondary_surfel_id = primary_surfel_id;
        reciprocal_item.contact_area = contact_table_desc.contact_area_items[j].contact_area;
        secondary_info->contact_area_items.push_back(reciprocal_item);
        //If both in the same SP (scales are different), this primary-secondary interaction will be handled by the
        //secondary one, so it is removed from the primary counter
        if (in_same_sps) {
          primary_info->primary_num_items--;
        }
      }
    }
    primary_info->primary_set_idx = i;
    primary_info->primary_first_item_idx = j_begin;
    j_begin = j_end;
  }

  //Now that we have identify the secondary that need to act as primary_secondary, we can count the number of 
  //primary and primary_secondary that should be commed to each SP
  std::vector<uINT32> num_contact_sets(total_sps);
  std::vector<uINT32> num_contact_items(total_sps);
  if (total_sps==1 && cp_info.num_scales==1) { //no primary_secondary, and all primaries are sent to the single SP
    num_contact_sets[0]=contact_table_desc.t.num_contact_area_sets;
    num_contact_items[0]=contact_table_desc.t.num_contact_area_items;
  } else {
    for (auto &it: m_contact_info_map) {
      sSURFEL_CONTACT_INFO& info = it.second;
      if ((info.primary_set_idx >= 0 && info.primary_num_items > 0) || !info.contact_area_items.empty()) {
        num_contact_sets[info.proc_id]++;
        num_contact_items[info.proc_id] += info.primary_num_items + info.contact_area_items.size();
      }
    }
  }
  
  //Time to get all the SPs ready to get contact info from their stream
  cDGF_GAP_CONTACT_TABLE table_header(contact_table_desc.t);
  table_header.tag.length = 0; // set to a non-positive number since record length is not known apriori
  ccDOTIMES(sp, total_sps) {
    table_header.num_contact_area_sets = num_contact_sets[sp];
    table_header.num_contact_area_items = num_contact_items[sp];
    table_header.write(g_sp_streams[sp], FALSE);
  }
  //Now that all sp's are expecting contact info, loop through the map and send each one to the corresponding sp
  STP_SURFEL_ID secondary_surfel_id;
  STP_SURFEL_WEIGHT contact_area, weight;
  for (auto &it: m_contact_info_map) {
    sSURFEL_CONTACT_INFO& info = it.second;
    STP_PROC home_sp = info.proc_id;
    //Streams the contact area set as header of all the normalized weight items that will follow after
    cDGF_GAP_CONTACT_AREA_SET contact_area_set;
    contact_area_set.primary_surfel_id = it.first;
    if (info.primary_set_idx >= 0) { //primary contact surfel
      if (info.primary_num_items <= 0) { //all its interactions processed by secondaries, nothing left to send
        continue;
      }
      //primary surfel, retrieve them from contact table (keep in mind that not all weights from
      //contact table might be send, if interacts with secondaries in a finer scale or earlier dest_sp)
      contact_area_set.num_contact_area_items = info.primary_num_items;
      contact_area_set.write(g_sp_streams[home_sp]);
      uINT32 n_set_items = contact_table_desc.contact_area_sets[info.primary_set_idx].num_contact_area_items;
      sINT32 i_end = info.primary_first_item_idx + n_set_items;
      for (sINT32 i = info.primary_first_item_idx; i<i_end; i++) {
        secondary_surfel_id = contact_table_desc.contact_area_items[i].secondary_surfel_id;
        sSURFEL_CONTACT_INFO *secondary_info = &m_contact_info_map[secondary_surfel_id];
        //check order to discard weights that point to a secondary that will be processed before within same SP
        if ((info.proc_id == secondary_info->proc_id) && is_reversed_contact_order(&info, secondary_info)) {
          continue;
        }
        contact_area = contact_table_desc.contact_area_items[i].contact_area;
#ifdef ENABLE_CONSISTENCY_CHECKS
        if (contact_area == 0.0) { 
          continue; 
        }
#endif
        // msg_print("sps_table %d %d",contact_area_set.primary_surfel_id, secondary_surfel_id);
        lgi_write(g_sp_streams[home_sp], secondary_surfel_id);
        //normalized weight
        weight = contact_area / info.total_contact_area;
        lgi_write(g_sp_streams[home_sp], weight);
        //normalized opposite weight
        weight = contact_area / m_contact_info_map[secondary_surfel_id].total_contact_area;
        lgi_write(g_sp_streams[home_sp], weight);
      }
    } else { //secondary contact surfel, only can have reciprocal weights (if any)
      if (info.contact_area_items.empty()) {
        continue;
      }
      contact_area_set.num_contact_area_items = info.contact_area_items.size();
      contact_area_set.write(g_sp_streams[home_sp]);
      //Streams reciprocal weight items as above, but with negative weight value so SPs are aware that they are reversed
      ccDOTIMES(i, info.contact_area_items.size()) {
        secondary_surfel_id = info.contact_area_items[i].secondary_surfel_id;
        // msg_print("sps_flipped %d %d",contact_area_set.primary_surfel_id, secondary_surfel_id);
        lgi_write(g_sp_streams[home_sp], secondary_surfel_id);
        contact_area = -info.contact_area_items[i].contact_area;
        weight = contact_area / info.total_contact_area;
        lgi_write(g_sp_streams[home_sp], weight);
        weight = contact_area / m_contact_info_map[secondary_surfel_id].total_contact_area;
        lgi_write(g_sp_streams[home_sp], weight);
      }
    }
  }

  //Final step, clear the descriptor, surfels counter per sp, and map since we don't need them any more
  contact_table_desc.clear();
  std::unordered_map<STP_SURFEL_ID,sSURFEL_CONTACT_INFO>().swap(m_contact_info_map); //m_contact_info_map.clear();
}

VOID sCP_DGF_READER::read_averaged_contact_ckpt_data() {
  if (m_ckpt_table_of_contents.averaged_contact_pos <= 0)
    return;
  jump_to_ckpt_file_position(m_ckpt_table_of_contents.averaged_contact_pos);
  LGI_TAG tag = lgi_peek_tag(m_ckpt_istream);
  if (tag.id != LGI_CKPT_AVERAGED_CONTACTS_TAG) {
    //DFG-TODO: We can recompute the values during seeding, so might be worth to just peek the tag and throw a warning
    //if it is not the expected one. Ask
    msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)",
                       lgi_tag_namestring(LGI_CKPT_AVERAGED_CONTACTS_TAG), LGI_CKPT_AVERAGED_CONTACTS_TAG,
                       lgi_tag_namestring(tag.id), tag.id);

    return;
  }
  //Checks that all averaged contacts exist
  cLGI_CKPT_AVERAGED_CONTACT_HEADER header;
  lgi_read_next_head(m_ckpt_istream, header);
#ifdef ENABLE_CONSISTENCY_CHECKS
  if (header.num_averaged_contacts != cp_info.num_averaged_contacts) {
    msg_internal_error("Expected %d averaged contacts from ckpt but found %d", 
                        cp_info.num_averaged_contacts, header.num_averaged_contacts);
  }
#endif
  write_header_to_all_sps(header);
  //Gets each averaged contact record and sends it to all SPs
  cLGI_CKPT_AVERAGED_CONTACT_ITEM item;
  ccDOTIMES(i, cp_info.num_averaged_contacts) {
    item.read(m_ckpt_istream);
    write_to_all_sps(item);
  }
}

static VOID assign_part_indices_for_meas_cells_of_current_meas_cube(CP_FLUID_MEAS_WINDOW window)
{
  if (window->meas_window_type != LGI_POROUS_WINDOW) {
    DO_MEAS_CELL_PART_CONTRIBUTIONS(iterator, window) {
      STP_MEAS_CELL_INDEX meas_cell_index = iterator->first;
      sCP_FLUID_MEAS_WINDOW::sPART_INDEX_TO_VOLUME_MAP &part_to_volume_map = iterator->second;
      sFLOAT largest_volume = -1;
      asINT32 largest_part_index = -1;
      DO_MEAS_CELL_PART_TO_VOLUME_MAP(part_index, volume, part_to_volume_map) {
        if (volume > largest_volume) {
          largest_volume = volume;
          largest_part_index = part_index;
        }
      }
      window->m_part_indices[meas_cell_index] = largest_part_index;
    }
  }
  else {
    // For a .pnc file, the part with the largest overlap of the meas cell wins, except that porous media 
    // and fan parts have precedence over basic fluid parts (see PR 30500).
    DO_MEAS_CELL_PART_CONTRIBUTIONS(iterator, window) {
      STP_MEAS_CELL_INDEX meas_cell_index = iterator->first;
      sCP_FLUID_MEAS_WINDOW::sPART_INDEX_TO_VOLUME_MAP &part_to_volume_map = iterator->second;
      sFLOAT largest_volume = -1;
      asINT32 largest_part_index = -1;
      BOOLEAN is_largest_part_basic_fluid = -1; // -1 (not yet determined), FALSE (0), or TRUE (1)
      DO_MEAS_CELL_PART_TO_VOLUME_MAP(part_index, volume, part_to_volume_map) {
        // This is coded so that we don't look up the physics type of the current largest part until we encounter
        // a second part. In particular, this optimizes the common case where a meas cube has only 1 part.
        if (largest_part_index < 0) {
          largest_volume = volume;
          largest_part_index = part_index;
        } else {
          // first determine is_largest_part_basic_fluid if previously deferred
          if (is_largest_part_basic_fluid < 0) {
            asINT32 largest_fluid_phys_desc = cp_info.fluid_physics_desc_index_from_part_index[largest_part_index];
            asINT32 largest_cdi_phys_type   = cp_info.cdi_phys_type_from_fluid_physics_desc_index[largest_fluid_phys_desc];
            switch (largest_cdi_phys_type) {
            case POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
            case FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
            case TABLE_FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
              is_largest_part_basic_fluid = FALSE;
              break;
            default:
              is_largest_part_basic_fluid = TRUE;
              break;
            }
          }
          
          if (is_largest_part_basic_fluid
              && volume > largest_volume) {
            largest_volume = volume;
            largest_part_index = part_index;
            is_largest_part_basic_fluid = -1; // defer determination of this
          } else {
            asINT32 fluid_phys_desc = cp_info.fluid_physics_desc_index_from_part_index[part_index];
            asINT32 cdi_phys_type   = cp_info.cdi_phys_type_from_fluid_physics_desc_index[fluid_phys_desc];
            BOOLEAN is_part_basic_fluid;

            switch (cdi_phys_type) {
            case POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
            case FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
            case TABLE_FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
              is_part_basic_fluid = FALSE;
              break;
            default:
              is_part_basic_fluid = TRUE;
              break;
            }

            if ((!is_part_basic_fluid
                 && (is_largest_part_basic_fluid || volume > largest_volume))
                || (is_largest_part_basic_fluid && volume > largest_volume)) {
              largest_volume = volume;
              largest_part_index = part_index;
              is_largest_part_basic_fluid = is_part_basic_fluid;
            }
          }
        } 
      }
      window->m_part_indices[meas_cell_index] = largest_part_index;
    }
  }

  window->clear_meas_cell_part_contributions_map();
}

static VOID create_nw_centroids_for_meas_cells_of_current_meas_cube(CP_FLUID_MEAS_WINDOW window)
{
  STP_MEAS_CELL_INDEX meas_cell_index;
  if (window->m_current_meas_cube_first_meas_cell_is_partial
      && (meas_cell_index = window->m_current_meas_cube_first_meas_cell_index) >= 0) {
    dFLOAT one_over_meas_cell_volume = 1.0 / window->m_d_volumes[meas_cell_index];
    window->m_nw_meas_cell_indices.push_back(meas_cell_index);
    ccDOTIMES(i, cp_info.n_dims)
      window->m_nw_centroids.push_back(one_over_meas_cell_volume * window->m_current_meas_cube_first_meas_cell_centroid[i]);
  }

  DO_STD_MAP(STP_MEAS_CELL_INDEX, sCP_FLUID_MEAS_WINDOW::sMEAS_CELL_CENTROID,
             iterator, window->m_meas_cell_centroids_map) {
    STP_MEAS_CELL_INDEX meas_cell_index             = iterator->first;
    sCP_FLUID_MEAS_WINDOW::sMEAS_CELL_CENTROID &mcc = iterator->second;
    if (mcc.m_is_partial_cell) {
      dFLOAT one_over_meas_cell_volume = 1.0 / window->m_d_volumes[meas_cell_index];
      window->m_nw_meas_cell_indices.push_back(meas_cell_index);
      ccDOTIMES(i, cp_info.n_dims)
        window->m_nw_centroids.push_back(one_over_meas_cell_volume * mcc.m_centroid[i]);
    }
  }

  window->clear_meas_cell_centroids_map();
}

// This routine may alter meas cells refs associated with force dev windows. Thus it
// must be called before sending the meas cell refs to the SPs. Note that for a
// simple ublk desc, there is a single part_index and all voxel pfluids are 1.0.

static VOID process_ublk_desc_meas_cell_refs(cDGF_UBLK_BASE_DESC *ublk_desc,
                                             std::vector < cDGF_UBLK_MEAS_CELL_REFERENCE > &meas_cell_refs,
                                             asINT32 ublk_home_sp,
                                             asINT32 simple_part_index,     // -1 if real ublk desc
                                             dFLOAT  voxel_pfluids[8],      // NULL if simple ublk desc
                                             sINT32  voxel_part_indices[8], // NULL if simple ublk desc
                                             asINT32 cube_offset) // cube offset of ublk; octree ordered
{
  asINT32 voxel_scale         = ublk_desc->b.voxel_scale;
  STP_COORD *ublk_location    = ublk_desc->b.location;
  asINT32 ublk_scale          = coarsen_scale(voxel_scale);
  asINT32 n_dims              = cp_info.n_dims;
  sriLRF_INDEX ref_frame_index= ublk_desc->b.lrf_index;
  BOOLEAN is_simple_ublk_desc = simple_part_index >= 0;

  asINT32 last_meas_window_index = -1;

  // find and set dsm window related information
  if (cp_info.dsm_reader) {
    BOOLEAN dsm_window_found = FALSE;
    DO_STD_VECTOR(cDGF_UBLK_MEAS_CELL_REFERENCE, meas_cell_ref, meas_cell_refs) {
      if (meas_cell_ref.meas_window_index == cp_info.dsm_reader->get_meas_window_index()) {
        dsm_window_found = TRUE;
        CP_MEAS_WINDOW window = cp_info.meas_windows[meas_cell_ref.meas_window_index];
        BOOLEAN is_per_voxel = window->is_per_voxel_for_scale(voxel_scale);
        if (is_per_voxel) {
          cp_info.dsm_reader->add_voxel_based_mci(ublk_desc->b.ublk_id, meas_cell_ref.voxel_mask,
              meas_cell_ref.meas_cell_index, cube_offset);
        } else {
          cp_info.dsm_reader->add_ublk_based_mci(ublk_desc->b.ublk_id, meas_cell_ref.meas_cell_index, cube_offset);
        }
      }
    }

    if(!dsm_window_found) {
      cp_info.dsm_reader->add_null_ublk_entry();
    }
  }

  DO_STD_VECTOR(cDGF_UBLK_MEAS_CELL_REFERENCE, meas_cell_ref, meas_cell_refs) {
    if (meas_cell_ref.meas_window_index < last_meas_window_index)
      msg_internal_error("LGI ublk desc (ID %d) contains meas cell references that are not"
                         " sorted by meas window index.",
                         ublk_desc->b.ublk_id);
    last_meas_window_index = meas_cell_ref.meas_window_index;

    CP_MEAS_WINDOW window = cp_info.meas_windows[meas_cell_ref.meas_window_index];
    STP_MEAS_CELL_INDEX meas_surfel_index = meas_cell_ref.meas_cell_index;
    asINT32 meas_cell_scale = window->meas_cell_scale;
    BOOLEAN is_per_voxel = window->is_per_voxel_for_scale(voxel_scale);
    BOOLEAN is_composite = window->is_composite;
    BOOLEAN is_development = window->is_development;


// #ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    BOOLEAN is_trajectory = window->m_is_particle_trajectory_window;
//#endif
    BOOLEAN is_voxel_or_ublk_meas_cell = sim_is_scale_same_or_finer(meas_cell_scale, ublk_scale);
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    if(!is_trajectory) {
//#endif
    if (is_development) {
      CP_FLUID_DEV_MEAS_WINDOW win = (CP_FLUID_DEV_MEAS_WINDOW)window;
      asINT32 ipart;

      // get the region that our measurement cell contributes to
      // either we are dealing with a simple ublk, in which each voxel belongs to the same fluid region OR
      // we are dealing with a portion of a microblock in a particular fluid region
      if (is_simple_ublk_desc) {
        ipart = simple_part_index;
      }
      else {
        auINT32 meas_cell_voxel_mask = meas_cell_ref.voxel_mask;
        DO_VOXELS_IN_MASK(voxel, meas_cell_voxel_mask) {
          ipart = voxel_part_indices[voxel];
          break;
        }
      }

      cBOOLEAN is_ublk_in_sliding_rf = (ref_frame_index < 0
                                        ? FALSE
                                        : cp_info.sri_lrfs[ref_frame_index].type == SRI_LRF_MOVING);

      STP_MEAS_CELL_INDEX meas_cell_index;
      uINT8 axis;
      BOOLEAN is_first_ublk_in_cube = cube_offset == 0;

      if (is_first_ublk_in_cube) {
        STP_MEAS_CELL_INDEX segment_plus_axis = meas_cell_ref.meas_cell_index;
        sINT32 segment;
        lgi_meas_cell_index_dev_segment(segment_plus_axis, segment, axis);

        if (is_ublk_in_sliding_rf) {
          meas_cell_index = win->m_part_first_meas_cell[axis][ipart];
        } else {
          meas_cell_index = win->m_part_first_meas_cell[axis][ipart]
            + (segment - win->m_part_first_segment[axis][ipart]);
        }
        // encode actual meas_cell_index and axis in meas_cell_ref.meas_cell_index for SPs
        STP_MEAS_CELL_INDEX meas_cell_index_plus_axis = lgi_meas_dev_segment_cell_index(meas_cell_index, axis);
        meas_cell_ref.meas_cell_index = meas_cell_index_plus_axis;

        if (meas_cell_index < 0)
          msg_internal_error("LGI ublk desc (ID %d) contains negative meas cell index (%d) for window \"%s\"",
                             ublk_desc->b.ublk_id, meas_cell_index, window->cdi_meas_window->name); 

      } else {
        // extract actual_meas_cell_index and axis encoded in meas_cell_ref.meas_cell_index in the prior call
        // to this routine
        STP_MEAS_CELL_INDEX meas_cell_index_plus_axis = meas_cell_ref.meas_cell_index;
        lgi_meas_cell_index_dev_segment(meas_cell_index_plus_axis, meas_cell_index, axis);
      }

      if (is_ublk_in_sliding_rf) {
        STP_MEAS_CELL_INDEX part_n_segments = win->m_part_n_segments[axis][ipart];
        STP_MEAS_CELL_INDEX part_last_meas_cell = win->m_part_first_meas_cell[axis][ipart] + part_n_segments;

        if (part_last_meas_cell > window->n_meas_cells)
          msg_internal_error("LGI ublk desc (ID %d) meas cell index (%d) for window \"%s\" is out of range [0,%lu)",
                             ublk_desc->b.ublk_id,part_last_meas_cell,
                             window->cdi_meas_window->name, window->n_meas_cells);

        // iterate over the meas cell indices of the part associated with voxels in meas_cell_ref.voxel_mask @@@
        ccDOTIMES(j, part_n_segments) {
          STP_MEAS_CELL_INDEX imeas_cell = meas_cell_index + j;
          if (!window->is_meas_cell_on_sp(imeas_cell, ublk_home_sp)) {
            window->m_sp_n_meas_cells[ublk_home_sp] ++;
            window->add_sp_meas_cell_ref(imeas_cell, ublk_home_sp);
          }
        }
      } else {
        if (meas_cell_index >= window->n_meas_cells) {
          msg_internal_error("LGI ublk desc (ID %d) meas cell index (%d) for window \"%s\" is out of range [0,%lu)",
                             ublk_desc->b.ublk_id, meas_cell_index,
                             window->cdi_meas_window->name, window->n_meas_cells);
        }

        if (!window->is_meas_cell_on_sp(meas_cell_index, ublk_home_sp)) {
          window->m_sp_n_meas_cells[ublk_home_sp] ++;
          window->add_sp_meas_cell_ref(meas_cell_index, ublk_home_sp);
        }
      }

      auINT32 meas_cell_voxel_mask = meas_cell_ref.voxel_mask;
      asINT32 voxel_size = scale_to_cube_size(voxel_scale);
      DO_VOXELS_IN_MASK(voxel, meas_cell_voxel_mask) {
        ccDOTIMES(axis, n_dims) {
          asINT32 cell_coord = ublk_location[axis] + voxel_size * num_to_voxel_offset(voxel, axis);
          if (cell_coord < window->min_bound[axis])
            window->min_bound[axis] = cell_coord;
          if (cell_coord + voxel_size > window->max_bound[axis])
            window->max_bound[axis] = cell_coord + voxel_size;
        }
      }
      if (cp_info.is_5g_sim) {
        // Store measurement cell volumes 
        dFLOAT voxel_volume = scale_to_cube_volume(voxel_scale);
        if (is_simple_ublk_desc) {
          DO_VOXELS_IN_MASK(voxel, meas_cell_voxel_mask) {
            win->m_volumes[meas_cell_index] += voxel_volume;
          }
        } else {
          DO_VOXELS_IN_MASK(voxel, meas_cell_voxel_mask) {
            dFLOAT voxel_fluid = voxel_pfluids[voxel] * voxel_volume;
            win->m_volumes[meas_cell_index] += voxel_fluid;
          }
        }
      }
    }
    else {
      // none of the rest of this fcn applies to dev windows

      STP_MEAS_CELL_INDEX meas_cell_index;
      auINT32 meas_cell_voxel_mask = meas_cell_ref.voxel_mask;
      if (is_composite) {
        meas_cell_index = meas_cell_ref.meas_cell_index;
      } else {
        if (is_per_voxel) {
          asINT32 n_bits_in_meas_voxel_mask = bitcount8(meas_cell_voxel_mask);
          meas_cell_index = meas_cell_ref.meas_cell_index + n_bits_in_meas_voxel_mask * cube_offset;
        } else {
          asINT32 log2_ublks_per_meas_cell;
          if (is_voxel_or_ublk_meas_cell)
            log2_ublks_per_meas_cell = 0;
          else
            log2_ublks_per_meas_cell = n_dims * sim_scale_diff(meas_cell_scale, ublk_scale);
          meas_cell_index = meas_cell_ref.meas_cell_index + (cube_offset >> log2_ublks_per_meas_cell);
        }
      }

      if (meas_cell_index < 0)
        msg_internal_error("LGI ublk desc (ID %d) contains negative meas cell index (%d) for window \"%s\"",
                           ublk_desc->b.ublk_id, meas_cell_index, window->cdi_meas_window->name);

      if (n_dims == 2 && (meas_cell_voxel_mask & 0xAA)) {
        msg_internal_error("LGI ublk desc (ID %d) contains an invalid 2D meas cell voxel mask (0x%02x) for"
                           " window \"%s\"",
                           ublk_desc->b.ublk_id, meas_cell_voxel_mask, window->cdi_meas_window->name);
      }
      asINT32 n_cells_for_ublk;
      if (is_per_voxel) {
        n_cells_for_ublk = bitcount8(meas_cell_voxel_mask);
      } else {
        n_cells_for_ublk = 1;
      }

      if (!window->is_meas_cell_on_sp(meas_cell_index, ublk_home_sp)) {
        window->m_sp_n_meas_cells[ublk_home_sp] += n_cells_for_ublk;

        if (meas_cell_index + n_cells_for_ublk > window->n_meas_cells)
          msg_internal_error("LGI ublk desc (ID %d) meas cell index (%d) for window \"%s\" is out of range [0,%lu)",
                             ublk_desc->b.ublk_id, meas_cell_index + n_cells_for_ublk - 1,
                             window->cdi_meas_window->name, window->n_meas_cells);

        asINT32 first_sp_housing_cell = window->first_sp_housing_meas_cell(meas_cell_index);
        if (first_sp_housing_cell >= 0) {
          if (!is_composite && is_voxel_or_ublk_meas_cell) {
            // A voxel or ublk size meas cell will never span processor boundaries
            msg_internal_error("LGI ublk desc (ID %d, home SP %d) contains meas cell index (%d) for window \"%s\""
                               " that also exists on SP %d, but this meas cell is too small to"
                               " span processor boundaries.",
                               ublk_desc->b.ublk_id, ublk_home_sp, meas_cell_index, window->cdi_meas_window->name,
                               first_sp_housing_cell);
          }

          // is_per_voxel is FALSE and n_cells_for_ublk is 1 if we reach this point
          window->add_sp_meas_cell_ref(meas_cell_index, ublk_home_sp);

        } else {
          // This is the first ublk that references this meas cell
          if (is_per_voxel) {
            // For per-voxel measurements, the meas cube is exactly the size of the voxel.
            asINT32 voxel_size = scale_to_cube_size(voxel_scale);
            asINT32 cell = 0;
            DO_VOXELS_IN_MASK(voxel, meas_cell_voxel_mask) {
              window->add_sp_meas_cell_ref(meas_cell_index + cell, ublk_home_sp);

              if (!(is_composite || is_development)) {
                CP_FLUID_MEAS_WINDOW win = static_cast<CP_FLUID_MEAS_WINDOW>(window);
                // This is only technically correct if meas cells are split by thin walls. If not,
                // multiple split voxels can contribute to a single voxel-sized meas cell. This
                // code will assign the part index of the first encountered split voxel to such a
                // meas cell. The correct behavior is to assign the dominant part index. Since this
                // is unlikely to matter to anyone, we will live with the current behavior.
                win->m_part_indices[meas_cell_index + cell] = (voxel_part_indices
                                                               ? voxel_part_indices[voxel]
                                                               : simple_part_index);
                STP_MEAS_COORD_INDEX coord_index =
                  (((STP_MEAS_COORD_INDEX)meas_cell_index) + cell) * n_dims;
                ccDOTIMES(axis, n_dims) {
                  asINT32 cell_coord = ublk_location[axis] + voxel_size * num_to_voxel_offset(voxel, axis);
                  win->m_coords[coord_index + axis] = cell_coord;

                  // Adjust min and max bounds of meas window
                  if (cell_coord < window->min_bound[axis])
                    window->min_bound[axis] = cell_coord;
                  if (cell_coord + voxel_size > window->max_bound[axis])
                    window->max_bound[axis] = cell_coord + voxel_size;
                }
              }
              cell++;
            }
            // This conditional is only needed as long as meas cells are not split by thin walls.
            // It is required to support the last call to win->add_meas_cell_centroid_contribution
            // below for per-voxel meas windows. Once that call is deleted (as described in a comment
            // next to the call), this conditional can also be deleted.
            if (!is_composite && (ublk_desc->b.ublk_flags & DGF_UBLK_IS_SPLIT)) {
              // This code takes a bit of liberty with win->m_current_meas_cube_location, storing
              // the current ublk coordinates instead of the meas cube (i.e. voxel) coordinates.
              CP_FLUID_MEAS_WINDOW win = static_cast<CP_FLUID_MEAS_WINDOW>(window);
              if (ublk_location[0] != win->m_current_meas_cube_location[0]
                  || ublk_location[1] != win->m_current_meas_cube_location[1]
                  || ublk_location[2] != win->m_current_meas_cube_location[2]) {
                ccDOTIMES(i, 3)
                  win->m_current_meas_cube_location[i] = ublk_location[i];
                // final processing of last ublk cube, not last meas cube
                create_nw_centroids_for_meas_cells_of_current_meas_cube(win);
              }
            }
          } else { // !is_per_voxel
            window->add_sp_meas_cell_ref(meas_cell_index, ublk_home_sp);

            STP_COORD cell_size  = scale_to_cube_size(meas_cell_scale);
            STP_COORD ublk_size  = 2*scale_to_cube_size(voxel_scale);
            cell_size = MAX(cell_size, ublk_size);
            STP_COORD cell_coord_mask = ~(cell_size - 1);

            if (! is_composite) {
              CP_FLUID_MEAS_WINDOW win = static_cast<CP_FLUID_MEAS_WINDOW>(window);
              BOOLEAN is_new_meas_cube = FALSE; // is this the first cell in a new meas cube
              STP_MEAS_COORD_INDEX coord_index = ((STP_MEAS_COORD_INDEX) meas_cell_index) * n_dims;
              ccDOTIMES(axis, n_dims) {
                STP_COORD cell_coord = ublk_location[axis] & cell_coord_mask;
                win->m_coords[coord_index + axis] = cell_coord;

                // Adjust min and max bounds of meas window
                if (cell_coord < window->min_bound[axis])
                  window->min_bound[axis] = cell_coord;
                if (cell_coord + cell_size > window->max_bound[axis])
                  // This may extend beyond the simvol bounds - finish_init_of_meas_windows cleans this up
                  window->max_bound[axis] = cell_coord + cell_size;

                if (cell_coord != win->m_current_meas_cube_location[axis]) {
                  is_new_meas_cube = TRUE;
                  // Record the coordinates of the new meas cube
                  win->m_current_meas_cube_location[axis] = cell_coord;
                }
              }

              if (is_new_meas_cube) {
                assign_part_indices_for_meas_cells_of_current_meas_cube(win); // final processing of last cube
                create_nw_centroids_for_meas_cells_of_current_meas_cube(win); // final processing of last cube
              }
            }
          }
        }
      }

      if (is_composite) {
        // Adjust min and max bounds of meas window
        asINT32 voxel_size = scale_to_cube_size(voxel_scale);
        asINT32 part_index_for_warning = -1;
        DO_VOXELS_IN_MASK(voxel, meas_cell_voxel_mask) {
          ccDOTIMES(axis, n_dims) {
            asINT32 cell_coord = ublk_location[axis] + voxel_size * num_to_voxel_offset(voxel, axis);
            if (cell_coord < window->min_bound[axis])
              window->min_bound[axis] = cell_coord;
            if (cell_coord + voxel_size > window->max_bound[axis])
              window->max_bound[axis] = cell_coord + voxel_size;
          }
          // All voxels will have the same part index
          part_index_for_warning = (voxel_part_indices
                                    ? voxel_part_indices[voxel]
                                    : simple_part_index);
        }
        CP_COMPOSITE_FLUID_MEAS_WINDOW win = 
          static_cast<CP_COMPOSITE_FLUID_MEAS_WINDOW>(window);
        ccDOTIMES(i, n_cells_for_ublk) {
          STP_MEAS_CELL_INDEX mci = meas_cell_index + i;
          if (window->m_ref_frame_indices.size() != 0) { // will be NULL if there are no LRFs
            cBOOLEAN is_meas_cell_in_sliding_rf =
              (window->m_ref_frame_indices.at(mci) < 0 ) ?
              FALSE :
              cp_info.sri_lrfs[window->m_ref_frame_indices.at(mci)].type == SRI_LRF_MOVING;

            cBOOLEAN is_ublk_in_sliding_rf =
              (ref_frame_index < 0 ) ?
              FALSE :
              (cp_info.sri_lrfs[ref_frame_index].type == SRI_LRF_MOVING);

            if (window->m_ref_frame_indices.at(mci) == SRI_INVALID_REF_FRAME_INDEX) {
              window->m_ref_frame_indices.at(mci) = ref_frame_index;
            } else if ((window->m_ref_frame_indices.at(mci) != ref_frame_index) &&
                       (window->m_ref_frame_indices.at(mci) != SRI_OVERLAP_REF_FRAME_INDEX) &&
                       (is_meas_cell_in_sliding_rf || is_ublk_in_sliding_rf) &&
                       (win->is_output_in_local_csys)) {

              if ((ref_frame_index != SRI_GLOBAL_REF_FRAME_INDEX) &&
                  (window->m_ref_frame_indices.at(mci) != SRI_GLOBAL_REF_FRAME_INDEX )) {
                msg_warn("Part \"%s\" spans local reference frames (\"%s\" and \"%s\")."
                         " Thus for composite window \"%s\", vectors (force, velocity, etc) for"
                         " this part will be averaged in the global reference frame coordinate system.",
                         cp_info.sri_parts[part_index_for_warning].name,
                         cp_info.sri_lrfs[window->m_ref_frame_indices.at(mci)].name,
                         cp_info.sri_lrfs[ref_frame_index].name, win->cdi_meas_window->name);
              } else  {
                char *lrf_name = (ref_frame_index == SRI_GLOBAL_REF_FRAME_INDEX) ?
                  cp_info.sri_lrfs[window->m_ref_frame_indices.at(mci)].name :
                  cp_info.sri_lrfs[ref_frame_index].name;
                msg_warn("Part \"%s\" spans local reference frame \"%s\" and the global reference frame."
                         " Thus for composite window \"%s\", vectors (force, velocity, etc) for this part"
                         " will be averaged in the global reference frame coordinate system.",
                         cp_info.sri_parts[part_index_for_warning].name,lrf_name, win->cdi_meas_window->name);
              }
              window->m_ref_frame_indices.at(mci) = SRI_OVERLAP_REF_FRAME_INDEX;
            }
          }
        }
      } else {
        CP_FLUID_MEAS_WINDOW win = static_cast<CP_FLUID_MEAS_WINDOW>(window);

        asINT32 sri_voxel_scale = sim_scale_to_sri_scale(voxel_scale);
        ccDOTIMES(i, n_cells_for_ublk) {
          STP_MEAS_CELL_INDEX mci = meas_cell_index + i;
          if (is_sri_scale_finer(sri_voxel_scale, win->m_scales[mci]))
            win->m_scales[mci] = sri_voxel_scale;

          if (window->m_ref_frame_indices.size() != 0) { // will be NULL if there are no LRFs
            if (window->m_ref_frame_indices.at(mci) == SRI_INVALID_REF_FRAME_INDEX)
              window->m_ref_frame_indices.at(mci) = ref_frame_index;
#if 0 // This is not a legit error check until we split meas cells at thin walls
            else if (is_per_voxel)
              msg_internal_error("LGI ublk desc (ID %d) per voxel meas cell index (%d) for window \"%s\""
                                 " refers to a meas cell already associated with another voxel",
                                 ublk_desc->b.id, mci, window->cdi_meas_window->name);
#endif
            else if (window->m_ref_frame_indices.at(mci) != ref_frame_index)
              msg_internal_error("LGI ublk desc (ID %d, ref frame %d) meas cell index (%d) for window \"%s\""
                                 " refers to meas cell already associated with ref frame %d",
                                 ublk_desc->b.ublk_id, ref_frame_index, mci, window->cdi_meas_window->name,
                                 window->m_ref_frame_indices.at(mci));
          }
        }

        if (!is_per_voxel) {
          asINT32 voxel_size = scale_to_cube_size(voxel_scale);
          dFLOAT  half_voxel_size = 0.5 * voxel_size;
          if (is_simple_ublk_desc) {
            sINT64 ublk_volume = scale_to_cube_volume(ublk_scale);
            win->m_d_volumes[meas_cell_index] += ublk_volume;
            if (!is_voxel_or_ublk_meas_cell) { // also note that simple ublks are never split ublks
              win->add_meas_cell_part_contribution(meas_cell_index, simple_part_index, ublk_volume);
              dFLOAT centroid[3];
              ccDOTIMES(i, 3)
                centroid[i] = ublk_volume * (ublk_location[i] + voxel_size);
              win->add_meas_cell_centroid_contribution(meas_cell_index, centroid, FALSE);
            } else {
              // This is the only ublk that references this meas cell
              win->m_part_indices[meas_cell_index] = simple_part_index;
            }
          } else {
            BOOLEAN simple_part_treatment; // optimize the common case
            if (is_voxel_or_ublk_meas_cell && !(ublk_desc->b.ublk_flags & DGF_UBLK_IS_SPLIT)) {
              // This is the only ublk that references this meas cell
              simple_part_treatment = TRUE;
              asINT32 common_part_index = -1;
              DO_VOXELS_IN_MASK(voxel, meas_cell_voxel_mask) {
                if (common_part_index < 0) {
                  common_part_index = voxel_part_indices[voxel];
                } else if (voxel_part_indices[voxel] != common_part_index) {
                  simple_part_treatment = FALSE;
                  break;
                }
              }
              if (simple_part_treatment)
                win->m_part_indices[meas_cell_index] = common_part_index;
            } else {
              simple_part_treatment = FALSE;
            }

            dFLOAT total_fluid = 0;
            dFLOAT voxel_volume = scale_to_cube_volume(voxel_scale);
            cDGF_REAL_UBLK_DESC *real_ublk_desc = (cDGF_REAL_UBLK_DESC *)ublk_desc;
            dFLOAT centroid[3] = { 0 };
            BOOLEAN is_partial_cell = FALSE;

            DO_VOXELS_IN_MASK(voxel, meas_cell_voxel_mask) {
              dFLOAT voxel_fluid = voxel_pfluids[voxel] * voxel_volume;
              total_fluid += voxel_fluid;
              if (!simple_part_treatment)
                win->add_meas_cell_part_contribution(meas_cell_index, voxel_part_indices[voxel], voxel_fluid);

              STP_LOCATION voxel_location;
              ccDOTIMES(i, 3)
                voxel_location[i] = ublk_location[i] + (voxel_size & (0 - num_to_voxel_offset(voxel, i)));

              if (voxel_pfluids[voxel] < (1.0 - 1e-6))
                is_partial_cell = TRUE;

              if (real_ublk_desc->voxel_flags[voxel].voxel_flags & DGF_VOXEL_GENERAL) {
                DGF_GENERAL_VOXEL_DESC voxel_desc = &real_ublk_desc->general_voxels[voxel];
                // In the LGI file, voxel centroids are normalized from 0 to 1
                ccDOTIMES(i, 3)
                  centroid[i] += voxel_fluid * (voxel_location[i] + voxel_size * voxel_desc->v.centroid[i]);
              } else {
                ccDOTIMES(i, 3)
                  centroid[i] += voxel_fluid * (voxel_location[i] + half_voxel_size);
              }
            }
            if (!is_voxel_or_ublk_meas_cell || (ublk_desc->b.ublk_flags & DGF_UBLK_IS_SPLIT)) {
              win->add_meas_cell_centroid_contribution(meas_cell_index, centroid, is_partial_cell);
            } else if (is_partial_cell) {
              dFLOAT one_over_meas_cell_volume = 1.0 / total_fluid;
              win->m_nw_meas_cell_indices.push_back(meas_cell_index);
              ccDOTIMES(i, cp_info.n_dims)
                win->m_nw_centroids.push_back(one_over_meas_cell_volume * centroid[i]);
            }
            win->m_d_volumes[meas_cell_index] += total_fluid;
          }
        } else { // is_per_voxel
          if (is_simple_ublk_desc) {
            // No need to consider near wall centroids here because the presence of a simple ublk desc
            // implies all voxels are 100% fluid - hence, near wall centroids are unneeded.
            ccDOTIMES(m, n_cells_for_ublk) {
              win->m_d_volumes[meas_cell_index + m] += scale_to_cube_volume(voxel_scale);
            }
          } else {
            asINT32            m               = 0;
            asINT32            voxel_size      = scale_to_cube_size(voxel_scale);
            dFLOAT             half_voxel_size = 0.5 * voxel_size;
            dFLOAT             voxel_volume    = scale_to_cube_volume(voxel_scale);
            cDGF_REAL_UBLK_DESC *real_ublk_desc  = (cDGF_REAL_UBLK_DESC *)ublk_desc;

            DO_VOXELS_IN_MASK(voxel, meas_cell_voxel_mask) {
              dFLOAT voxel_fluid = voxel_pfluids[voxel] * voxel_volume;
              win->m_d_volumes[meas_cell_index + m] += voxel_fluid;

              // see PR23559 - AO voxels may appear before the real voxels
              // associated with a valid part index
              if ((win->m_part_indices[meas_cell_index + m] < 0) &&
                  (voxel_part_indices[voxel] >= 0)) {
                win->m_part_indices[meas_cell_index + m] = voxel_part_indices[voxel];
              }

              if (voxel_pfluids[voxel] < (1.0 - 1e-6)) {
                STP_LOCATION voxel_location;
                ccDOTIMES(i, 3)
                  voxel_location[i] = ublk_location[i] + (voxel_size & (0 - num_to_voxel_offset(voxel, i)));

                dFLOAT centroid[3];
                if (real_ublk_desc->voxel_flags[voxel].voxel_flags & DGF_VOXEL_GENERAL) {
                  DGF_GENERAL_VOXEL_DESC voxel_desc = &real_ublk_desc->general_voxels[voxel];
                  // In the LGI file, voxel centroids are normalized from 0 to 1
                  ccDOTIMES(i, 3)
                    centroid[i] = voxel_fluid * (voxel_location[i] + voxel_size * voxel_desc->v.centroid[i]);
                  // Once meas cells are split by thin walls, we can directly add an entry to
                  // win->m_nw_meas_cell_indices here instead of calling add_meas_cell_centroid_contribution.
                  if (ublk_desc->b.ublk_flags & DGF_UBLK_IS_SPLIT) {
                    win->add_meas_cell_centroid_contribution(meas_cell_index + m, centroid, TRUE);
                  } else {
                    dFLOAT one_over_meas_cell_volume = 1.0 / voxel_fluid;
                    win->m_nw_meas_cell_indices.push_back(meas_cell_index + m);
                    ccDOTIMES(i, cp_info.n_dims)
                      win->m_nw_centroids.push_back(one_over_meas_cell_volume * centroid[i]);
                  }
                }
              }
              m++;
            }
          }
        }
      }
    }
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    }
//#endif
  }
}

static inline asINT32 octree_index_to_3d_coord(auINT32 i)
{
  auINT32 result = 0;
  auINT32 mask   = 1;
  auINT32 shift  = 2;
  while (i > 0) {
    result |= i & mask;
    mask = mask << 1;   // move mask to next bit position
    i = i >> shift;     // move next bit into place to be masked
  }
  return result;
}

static inline asINT32 octree_index_to_2d_coord(asINT32 i)
{
  auINT32 result = 0;
  auINT32 mask   = 1;
  auINT32 shift  = 1;
  while (i > 0) {
    result |= i & mask;
    mask = mask << 1;   // move mask to next bit position
    i = i >> shift;     // move next bit into place to be masked
  }
  return result;
}


template <typename UBLK_BASE>
static VOID compute_offset_location(UBLK_BASE& b,
                                    STP_COORD orig_ublk_location[3],
                                    asINT32 cube_offset,
                                    asINT32 ublk_size)
{
  if (cp_info.n_dims == 3) {
    b.location[0] = orig_ublk_location[0] +
      ublk_size * octree_index_to_3d_coord(cube_offset >> 2);
    b.location[1] = orig_ublk_location[1] +
      ublk_size * octree_index_to_3d_coord(cube_offset >> 1);
    b.location[2] = orig_ublk_location[2] +
      ublk_size * octree_index_to_3d_coord(cube_offset);

  } else {
    b.location[2] = orig_ublk_location[2];
    b.location[0] = orig_ublk_location[0] +
      ublk_size * octree_index_to_2d_coord(cube_offset >> 1);
    b.location[1] = orig_ublk_location[1] +
      ublk_size * octree_index_to_2d_coord(cube_offset);

  }

}

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
static std::vector<char> ublk_ckpt_buf;
static VOID free_ublk_ckpt_buf() {
  //only way to free storage of a static std::vector is rather odd
  std::vector<char> dummy;
  dummy.swap(ublk_ckpt_buf);
}
//#endif
template <sCP_DGF_READER::reader_t RT, typename UBLK_PROC_GHOST>
VOID sCP_DGF_READER::forward_ublk_full_ckpt_data(asINT32 home_sp, BOOLEAN is_vr_fine,
                                                 std::vector<UBLK_PROC_GHOST>& ublk_proc_ghosts)
{
#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  if (cp_info.is_full_checkpoint_restore)
  {
    // Reading the header from HDF5 or legacy file.
    LocalReader<RT, true> reader(initLocalReader<RT>(m_ckpt_istream, "Ublocks"));
    cDGF_CKPT_SHOB_HEADER shob_header;
    READ_GENERIC_VAR_FROM_FILE(shob_header.shob_id);
    READ_GENERIC_VAR_FROM_FILE(shob_header.len);
    shob_header.write(g_sp_streams[home_sp]);
    if (ublk_ckpt_buf.size() < shob_header.len)
      ublk_ckpt_buf.resize(2 * shob_header.len);

    // Reading the raw ublock data and save it into the buffer.
    READ_GENERIC_ARRAY_FROM_FILE(ublk_ckpt_buf.data(), shob_header.len);

    // Copy raw data from the buffer of the cp to the buffer of the SPs.
    lgi_write(g_sp_streams[home_sp], &ublk_ckpt_buf[0], shob_header.len);

    // Send checkpointed data for vr fine ublks to ghost SPs, since ghost VR
    // fine ublk go through explode and advection
    if (is_vr_fine) {
      ccDOTIMES(ghost, ublk_proc_ghosts.size()) {
        STP_PROC ghost_sp = ublk_proc_ghosts[ghost].ghost_sp;
        shob_header.write(g_sp_streams[ghost_sp]);
        lgi_write(g_sp_streams[ghost_sp], &ublk_ckpt_buf[0], shob_header.len);
      }
    }
  }
#else
  char ckpt_buf[LGI_MAX_SHOB_CKPT_LEN];
  if (cp_info.is_full_checkpoint_restore) {
    cDGF_CKPT_SHOB_HEADER shob_header;
    shob_header.read(m_ckpt_istream);
    shob_header.write(g_sp_streams[home_sp]);

    lgi_read(m_ckpt_istream, ckpt_buf, shob_header.len);
    lgi_write(g_sp_streams[home_sp], ckpt_buf, shob_header.len);
  }
#endif
}

template <sCP_DGF_READER::reader_t RT, typename UBLK_PROC_GHOST>
asINT32 sCP_DGF_READER::process_dgf_simple_ublk_desc(REALM realm, cDGF_SIMPLE_UBLK_DESC *ublk_desc, STP_PROC ublk_home_sp,
                                                     std::vector<UBLK_PROC_GHOST>& ublk_proc_ghosts)
{
  asINT32 part_index   = ublk_desc->u.part_index;
  asINT32 cube_factor  = ublk_desc->b.cube_factor;

  auINT32   flags                 = ublk_desc->b.ublk_flags;
  auINT32   vr_mask               = flags & DGF_UBLK_VR_INTERFACE_MASK;
  BOOLEAN   is_vr_fine            = (vr_mask == DGF_UBLK_VR_FINE);

  const auINT32 fluid_like_voxel_mask = (cp_info.n_dims == 2) ? 0x55 : 0xFF;

  if (cube_factor == 0) {
    process_ublk_desc_meas_cell_refs(ublk_desc, ublk_desc->meas_cell_refs, ublk_home_sp,
                                     part_index, NULL, NULL, 0);
    //msg_print("Simple: loc %d %d %d",ublk_desc->b.location[0],ublk_desc->b.location[1],ublk_desc->b.location[2]);
    ublk_desc->write(g_sp_streams[ublk_home_sp]);

    ccDOTIMES(ghost, ublk_proc_ghosts.size()) {
      STP_PROC ghost_sp = ublk_proc_ghosts[ghost].ghost_sp;
      ublk_desc->write(g_sp_streams[ghost_sp]);
    }

    forward_ublk_full_ckpt_data<RT>(ublk_home_sp, is_vr_fine, ublk_proc_ghosts);

    if (!is_vr_fine) {
      g_seed_ctl.maybe_write_smart_seed_data(ublk_desc, fluid_like_voxel_mask, ublk_home_sp, part_index, NULL, 0);
      return (cp_info.n_dims == 3)? N_VOXELS_8 : N_VOXELS_2D;
    } else
      return 0;
  } else if (total_sps == 1) {
    asINT32 ublk_size = scale_to_cube_size(coarsen_scale(ublk_desc->b.voxel_scale));
    STP_UBLK_ID n_ublks   = 1 << (cp_info.n_dims * cube_factor);
    STP_COORD orig_ublk_location[3];
    ccDOTIMES(i, 3)
      orig_ublk_location[i] = ublk_desc->b.location[i];

    // iterate over the ublks in the cube in octree order
    ccDOTIMES64(cube_offset, n_ublks) {
      compute_offset_location(ublk_desc->b, orig_ublk_location, cube_offset, ublk_size);
      process_ublk_desc_meas_cell_refs(ublk_desc, ublk_desc->meas_cell_refs, ublk_home_sp,
                                       part_index, NULL, NULL, cube_offset);

      // for development windows, all processing done in  process_ublk_desc_meas_cell_refs
      // is identical for all calls within the cube_offset loop

      if (cube_offset == 0)
        ublk_desc->write(g_sp_streams[ublk_home_sp]);

      forward_ublk_full_ckpt_data<RT>(ublk_home_sp, is_vr_fine, ublk_proc_ghosts);
      if (!is_vr_fine) {
        g_seed_ctl.maybe_write_smart_seed_data(ublk_desc, fluid_like_voxel_mask, ublk_home_sp, part_index, NULL, 0);
      }
    }
    return is_vr_fine ? 0 : ((cp_info.n_dims == 3) ? N_VOXELS_8  : N_VOXELS_2D) * n_ublks;
  } else {
    msg_internal_error("Compact ublk should never reach here.");
  }

  return 0;
}

#if 0
static dFLOAT extract_general_voxel_pfluid(cDGF_GENERAL_VOXEL_DESC *voxel_desc)
{
  dFLOAT pfluid = 0;
  ccDOTIMES(r, voxel_desc->regions.size()) {
    cDGF_VOXEL_REGION &part       = voxel_desc->regions[r];
    asINT32            part_index = part.region_id;

    pfluid += region.fraction; // fraction of full cube
  }

  return pfluid;
}
#endif

// Returns a part index and fills in total_pfluid. When a voxel is partially in
// multiple porous media regions, the dominant among those porous media regions wins, with
// the porous media fraction of the voxel set to the sum of all porous media fractions from
// all the porous media regions. The same logic applies to a voxel covered by multiple fan
// regions or multiple basic fluid regions.
//
// TODO: The function below is mostly identical to its counterpart in SIMENG.
// Should find a common component to put this shared code into.
static asINT32 find_voxel_dominant_fluid_region(DGF_GENERAL_VOXEL_DESC voxel_desc,
                                                dFLOAT &total_pfluid)
{
  dFLOAT total_basic_fluid = 0;
  dFLOAT total_porous_media = 0;
  dFLOAT total_fan = 0;

  total_pfluid = 0;

  asINT32 thermal_porous_media_fluid_region = -1;
  asINT32 adiabatic_porous_media_fluid_region = -1;
  asINT32 fan_fluid_region;
  asINT32 basic_fluid_fluid_region;

  dFLOAT adiabatic_porous_media_largest_volume = 0.0;
  dFLOAT thermal_porous_media_largest_volume = 0.0;
  dFLOAT fan_largest_volume = 0.0;
  dFLOAT basic_fluid_largest_volume = 0.0;
  BOOLEAN some_solid_region = FALSE;

  ccDOTIMES(r, voxel_desc->regions.size()) {
    cDGF_VOXEL_REGION  &region         = voxel_desc->regions[r];
    asINT32            fluid_region    = region.part_index;
    STP_GEOM_VARIABLE  volume          = region.fraction; // fraction of full cube
    asINT32            fluid_phys_desc = cp_info.fluid_physics_desc_index_from_part_index[fluid_region];

    if (fluid_phys_desc < 0) { // less than 0 if a solid region
      some_solid_region = TRUE;
      continue;
    }

    asINT32            cdi_phys_type   = cp_info.cdi_phys_type_from_fluid_physics_desc_index[fluid_phys_desc];

    switch (cdi_phys_type) {
    case THERMAL_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
      if (volume > thermal_porous_media_largest_volume) {
        thermal_porous_media_fluid_region = fluid_region;
        thermal_porous_media_largest_volume = volume;
      }
      total_porous_media += volume;
      total_pfluid += volume;
      break;
    case ADIABATIC_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
      if (volume > adiabatic_porous_media_largest_volume) {
        adiabatic_porous_media_fluid_region = fluid_region;
        adiabatic_porous_media_largest_volume = volume;
      }
      total_porous_media += volume;
      total_pfluid += volume;
      break;
    case FAN_CDI_FLUID_PHYSICS_TYPE_CASE :
    case TABLE_FAN_CDI_FLUID_PHYSICS_TYPE_CASE :
      if ((total_fan <= 0) || (volume > fan_largest_volume)) {
        fan_fluid_region = fluid_region;
        fan_largest_volume = volume;
      }
      total_fan += volume;
      total_pfluid += volume;
      break;

    default: // basic fluid
      if ((total_basic_fluid <= 0) || (volume > basic_fluid_largest_volume)) {
        basic_fluid_fluid_region = fluid_region;
        basic_fluid_largest_volume = volume;
      }
      total_basic_fluid += volume;
      total_pfluid += volume;
      break;
    }
  }

  if (total_pfluid > 1.0
      || (!some_solid_region && (total_pfluid > (1.0 - 1e-6))))
    total_pfluid = 1.0;

  if (total_porous_media > 0.0) {
    if (total_porous_media > total_fan)
      return (thermal_porous_media_fluid_region >= 0)? thermal_porous_media_fluid_region:
                                                       adiabatic_porous_media_fluid_region;
    else
      return fan_fluid_region;
  } else if (total_fan > 0.0) {
    return fan_fluid_region;
  } else if (total_basic_fluid > 0.0) {
    return basic_fluid_fluid_region;
  } else {
    return -1;
  }
}

static VOID extract_voxel_dominant_fluid_regions_and_pfluids(cDGF_REAL_UBLK_DESC *ublk_desc,
                                                             dFLOAT voxel_pfluids[8],
                                                             sINT32 voxel_part_indices[8],
                                                             auINT32 &fluid_like_voxel_mask,
                                                             auINT32 &nw_voxel_mask,
                                                             auINT32 &split_voxel_mask)
{
  fluid_like_voxel_mask = 0;
  nw_voxel_mask = 0;
  split_voxel_mask = 0;

  ccDOTIMES(voxel, N_VOXELS_8) {
    cDGF_VOXEL_TYPE_FLAGS *vflags = &ublk_desc->voxel_flags[voxel];

    if (vflags->voxel_flags & DGF_VOXEL_SIMPLE) {
      cDGF_SIMPLE_VOXEL_DESC *simple_voxel_desc = &ublk_desc->simple_voxels[voxel];
      voxel_pfluids[voxel] = 1.0;
      voxel_part_indices[voxel] = simple_voxel_desc->v.part_index;
      fluid_like_voxel_mask |= 1 << voxel;
    } else if (vflags->voxel_flags & DGF_VOXEL_GENERAL) {
      cDGF_GENERAL_VOXEL_DESC *general_voxel_desc = &ublk_desc->general_voxels[voxel];
      voxel_part_indices[voxel] = find_voxel_dominant_fluid_region(general_voxel_desc, voxel_pfluids[voxel]);
      if (voxel_pfluids[voxel] > 0) {
        fluid_like_voxel_mask |= 1 << voxel;
        if (vflags->voxel_flags & DGF_VOXEL_SPLIT)
          split_voxel_mask |= 1 << voxel;
        if (general_voxel_desc->v.closest_surfel_index != STP_INVALID_SHOB_ID)
          nw_voxel_mask |= 1 << voxel;
      }
    } else {
      voxel_pfluids[voxel] = 0;
      voxel_part_indices[voxel] = -1;
    }
  }
}

template <sCP_DGF_READER::reader_t RT, typename UBLK_PROC_GHOST>
asINT32 sCP_DGF_READER::process_dgf_real_ublk_desc(REALM realm, cDGF_REAL_UBLK_DESC *ublk_desc, STP_PROC ublk_home_sp,
                                                   std::vector<UBLK_PROC_GHOST>& ublk_proc_ghosts,
                                                   asINT32 &n_nw_fluid_like_voxels)
{
  dFLOAT voxel_pfluids[8];
  sINT32 voxel_part_indices[8];

  auINT32   flags        = ublk_desc->b.ublk_flags;
  auINT32   vr_mask      = flags & DGF_UBLK_VR_INTERFACE_MASK;
  BOOLEAN   is_vr_fine   = (vr_mask == DGF_UBLK_VR_FINE);

  auINT32 fluid_like_voxel_mask = 0;
  auINT32 split_voxel_mask = 0;
  auINT32 nw_voxel_mask = 0;

  if (!is_vr_fine) {
    extract_voxel_dominant_fluid_regions_and_pfluids(ublk_desc, voxel_pfluids, voxel_part_indices,
                                                     fluid_like_voxel_mask, nw_voxel_mask, split_voxel_mask);
    process_ublk_desc_meas_cell_refs(ublk_desc, ublk_desc->meas_cell_refs, ublk_home_sp, -1,
                                     voxel_pfluids, voxel_part_indices, 0);
  }
  //msg_print("Real: loc %d %d %d",ublk_desc->b.location[0],ublk_desc->b.location[1],ublk_desc->b.location[2]);

  ublk_desc->write(g_sp_streams[ublk_home_sp]);

  ccDOTIMES(ghost, ublk_proc_ghosts.size()) {
    STP_PROC ghost_sp = ublk_proc_ghosts[ghost].ghost_sp;
    ublk_desc->write(g_sp_streams[ghost_sp]);
  }

  forward_ublk_full_ckpt_data<RT>(ublk_home_sp, is_vr_fine, ublk_proc_ghosts);
  
  n_nw_fluid_like_voxels = bitcount8(nw_voxel_mask);

  if (!is_vr_fine) {
    g_seed_ctl.maybe_write_smart_seed_data(ublk_desc, fluid_like_voxel_mask, ublk_home_sp, -1, voxel_part_indices, split_voxel_mask);
    return bitcount8(fluid_like_voxel_mask);
  } else {
    return 0;
  }
}

//----------------------------------------------------------------------------
// read_ublk_descriptors
//----------------------------------------------------------------------------

STP_PROC update_ghost_info(cDGF_GHOST_INFO *ghost_info, cDGF_FLOW_UBLK_PROC_GHOST *ublk_proc_ghost) {
  STP_PROC ghost_sp = ublk_proc_ghost->ghost_sp;
  ghost_info->proc_id = ghost_sp;
  ghost_info->nmi = ublk_proc_ghost->ghost_sp_nmi;
  return ghost_sp;
}

STP_PROC update_ghost_info(cDGF_GHOST_INFO *ghost_info, cDGF_COND_UBLK_PROC_GHOST *ublk_proc_ghost) {
  STP_PROC ghost_sp = ublk_proc_ghost->ghost_sp;
  ghost_info->proc_id = ghost_sp;
  return ghost_sp;
}

template <sCP_DGF_READER::reader_t RT, typename UBLK_PROC, typename UBLK_PROC_GHOST>
VOID sCP_DGF_READER::read_ublk_descriptors(REALM realm)
{
  static STP_PROC last_coarse_home_sp = -1;
  msg_print("Reading ublk descriptors for realm %d",realm);
  if constexpr (RT == sCP_DGF_READER::LEGACY || RT == sCP_DGF_READER::DEBUG_MODE )
  {
    if (cp_info.is_full_checkpoint_restore && realm == STP_FLOW_REALM) {
      jump_to_ckpt_file_position(m_ckpt_table_of_contents.ublk_shob_state_pos);
      read_ckpt_ublk_dyn_data_header();
    }
  }

  // Declare descriptors here so that they are not deleted until we exit this fcn

  cDGF_SIMPLE_UBLK_DESC simple_desc;
  cDGF_REAL_UBLK_DESC   real_desc;
  cDGF_MIRROR_UBLK_DESC mirror_desc;

  //One use of the ublk octree was to find home_ublks for the bsurfels.
  //this responsibility has now been moved to the SPs, so I am commenting this code here
  //I do not know if we want to use the octree for something else, or if we want to come back later and use it
  //so leaving this here for now.
  //bool build_octree = cp_info.is_full_checkpoint_restore || cp_info.are_movbs_present() || cp_info.has_deforming_tires();
  
  bool build_octree = false;

  if ( build_octree ) {
    g_ublk_octree.init();
  }

  LGI_TAG_ID base_table_tag;
  LGI_TAG_ID table_tag;
  LGI_TAG_ID ublk_decomp_tag;
  if(realm == STP_FLOW_REALM) {
    base_table_tag = DGF_FLOW_UBLK_BASE_TABLE_TAG;
    table_tag = DGF_FLOW_UBLK_TABLE_TAG;
    ublk_decomp_tag = DGF_FLOW_UBLK_DECOMPOSITION_TAG;
    jump_to_file_position(m_table_of_contents.flow_ublk_base_table_pos);
    lgi_setpos(m_ublk_table_istream, &m_table_of_contents.flow_ublk_table_pos);
  } else {
    base_table_tag = DGF_COND_UBLK_BASE_TABLE_TAG;
    table_tag = DGF_COND_UBLK_TABLE_TAG;
    ublk_decomp_tag = DGF_COND_UBLK_DECOMPOSITION_TAG;
    jump_to_file_position(m_table_of_contents.cond_ublk_base_table_pos);
    lgi_setpos(m_ublk_table_istream, &m_table_of_contents.cond_ublk_table_pos);
  }

  cDGF_UBLK_BASE_TABLE base_table;
  base_table.read(m_main_istream);

  cDGF_UBLK_TABLE ublk_table;
  ublk_table.read(m_ublk_table_istream);

  sINT64 n_fluid_like_voxels_per_scale[STP_MAX_SCALES] = { 0 };
  sINT64 num_voxels = (realm == STP_FLOW_REALM) ? cp_info.control_rec.num_flow_voxels : cp_info.control_rec.num_cond_voxels;
  dFLOAT one_over_num_voxels = 1.0 / num_voxels;

  asINT32 percent_voxels_processed = 0;
  sINT64 num_voxels_processed = 0;
  char status[100];
  cp_jobctl_output_status("Initializing voxels");

  if (total_sps > 1) {
    //lgi_setpos(m_decomp_istream, &m_table_of_contents.ublk_decomp_pos);
    verify_next_tag_id(ublk_decomp_tag, m_decomp_istream);
    cDGF_UBLK_PROC_TABLE ublk_proc_table;
    ublk_proc_table.read(m_decomp_istream);
  }

  ublk_table.tag.length = 0; // set to a non-positive number since record length is not known apriori
  ccDOTIMES(sp, total_sps) {
    ublk_table.write(g_sp_streams[sp], FALSE);
  }

  std::vector<UBLK_PROC_GHOST> ublk_proc_ghosts;
  std::vector<bool> sent_descriptor_to_sp(total_sps);

  cDGF_GHOST_INFO ghost_info;

  STP_PROC home_sp = 0;

  // Open HDF5 file in case of reading data from the full checkpoint
  LocalReader<RT, false> * const ublocks_reader_ptr =
    cp_info.is_full_checkpoint_restore ?
    new LocalReader<RT, false>(initLocalReader<RT>(m_ckpt_istream, sim_args.resume_filename, "Ublocks")) :
    nullptr;

  // To be able to use the shob for ublocks without passing it by argument to several
  // functions its reference is stored into a global variable
  if constexpr (RT == DEBUG_MODE || RT == HDF5_SERIAL)
    if (cp_info.is_full_checkpoint_restore)
      g_hdf5_reader.add_shob_ref_to_collection("Ublocks", &ublocks_reader_ptr->shob);

  for (STP_UBLK_ID iblk = 0; iblk < base_table.num_ublks; iblk++) {
    //Read the base record from the ublk_base+table
    cDGF_UBLK_BASE ublk_base;
    asINT32 scale;
    asINT32 n_fluid_like_voxels = 0;
    asINT32 n_nw_fluid_like_voxels = 0;
    UBLK_PROC ublk_proc;
    ublk_proc_ghosts.clear();

    ublk_base.read (m_main_istream);
    lgi_set_record_offset(m_ublk_table_istream, ublk_base.ublk_offset);
    auINT32 vr_mask  = ublk_base.ublk_flags & DGF_UBLK_VR_INTERFACE_MASK;
    BOOLEAN is_vr_fine = (vr_mask == DGF_UBLK_VR_FINE);
    BOOLEAN is_non_advect_only_vr_fine = is_vr_fine && ublk_base.empty_voxel_mask != 0xff;
    BOOLEAN is_vr_coarse = (vr_mask == DGF_UBLK_VR_COARSE_NO_FINE ||
                            vr_mask == DGF_UBLK_VR_COARSE);
    

    if ((ublk_base.cube_factor > 0) &&
        (ublk_base.ublk_type == DGF_UBLK_REAL))  {
      msg_internal_error("For a REAL ublk %d, cube factor = %d cannot be > 0",
                         ublk_base.ublk_id, ublk_base.cube_factor);
    }
    if (total_sps > 1) {
      if (ublk_base.cube_factor != 0) {
        simple_desc.b = ublk_base;
        cDGF_CUBE_OFFSET c_offset;
        simple_desc.read (m_ublk_table_istream);
        scale = simple_desc.b.voxel_scale;
        asINT32 ublk_size = scale_to_cube_size(coarsen_scale(scale));
        STP_UBLK_ID n_cube_ublks = 1 << (cp_info.n_dims * ublk_base.cube_factor);
        asINT32 part_index   = simple_desc.u.part_index;
        STP_COORD orig_ublk_location[3];
        ccDOTIMES(loc, 3) {
          orig_ublk_location[loc] = simple_desc.b.location[loc];
        }

        std::fill(sent_descriptor_to_sp.begin(), sent_descriptor_to_sp.end(), false);

        ccDOTIMES64(cube_offset, n_cube_ublks) {
          simple_desc.b.ublk_id = ublk_base.ublk_id + cube_offset;
          ublk_proc.read(m_decomp_istream);
          home_sp = ublk_proc.home_sp;
          cp_info.ublk_procs[realm].push_back(home_sp);

          if (is_vr_coarse) {
            last_coarse_home_sp = home_sp;
          }

          if (is_non_advect_only_vr_fine && (home_sp != last_coarse_home_sp)) {
            msg_internal_error("VR fine ublk (ID %ld) is not assigned to the same SP as its coarse parent",
                               ublk_base.ublk_id + cube_offset);
          }

          ublk_proc_ghosts.clear();
          if (cp_info.are_movbs_present()) {
            if (ublk_proc.num_ghost_sps > 0) {
              g_ublk_ghost_vec_index.push_back(g_ublk_ghost_proc_vec.size());
              g_ublk_ghost_proc_vec.push_back(ublk_proc.num_ghost_sps);
            }
            else
              g_ublk_ghost_vec_index.push_back(-1);
          }

          ccDOTIMES(ghost, ublk_proc.num_ghost_sps) {
            UBLK_PROC_GHOST ublk_proc_ghost;
            ublk_proc_ghost.read(m_decomp_istream);
            ublk_proc_ghosts.push_back(ublk_proc_ghost);
            if (cp_info.are_movbs_present()) {
              g_ublk_ghost_proc_vec.push_back(ublk_proc_ghost.ghost_sp); 
            }
          }

          // set and restore the location of each ublk in the compacted ublk
          // record for sending later to other home_sps and ghost sps
          compute_offset_location(simple_desc.b, orig_ublk_location, cube_offset, ublk_size);
          process_ublk_desc_meas_cell_refs(&simple_desc, simple_desc.meas_cell_refs, home_sp,
                                           part_index, NULL, NULL, cube_offset);

          //set implicit_solid_state_index for current ublk descriptor
          ublk_base.implicit_solid_state_index = -1; 
          simple_desc.b.implicit_solid_state_index = -1; 
          if (ublk_base.ublk_flags & DGF_UBLK_CONDUCTION) {
            ublk_base.implicit_solid_state_index = cp_info.implicit_solid_solver_state_index_offset[home_sp];
            asINT32 n_voxels_this_ublk = is_vr_fine ? 0 :
                                  ((cp_info.n_dims == 3) ? N_VOXELS_8  : N_VOXELS_2D); 
            simple_desc.b.implicit_solid_state_index = ublk_base.implicit_solid_state_index;
            //msg_print("total_sp > 1 cube offset %d ublk id: %d, home_sp %d state index %d %d n_voxels %d", cube_offset, simple_desc.b.ublk_id, home_sp, simple_desc.b.implicit_solid_state_index, ublk_base.implicit_solid_state_index, n_voxels_this_ublk);
            cp_info.implicit_solid_solver_state_index_offset[home_sp] += n_voxels_this_ublk; 
            //msg_print("home_sp %d offset %d", home_sp, cp_info.implicit_solid_solver_state_index_offset[home_sp]);
          }
          
          if (!sent_descriptor_to_sp[home_sp]) {
            ublk_base.write(g_sp_streams[home_sp]);
            simple_desc.write(g_sp_streams[home_sp]);
            sent_descriptor_to_sp[home_sp] = true;
          }

          // write a cube_offset + home_sp pair for each ublk to the home sp
          c_offset.cube_offset = cube_offset;
          // Also write a base implicit_solid_state_index + home_sp pair for each ublk to the home sp
          // because if a cube span across sp boundary, the base index is different
          c_offset.implicit_solid_state_index = ublk_base.implicit_solid_state_index;  
          c_offset.write(g_sp_streams[home_sp]);
          //proc.proc_id = home_sp;
          ublk_proc.write(g_sp_streams[home_sp]);

          // Note that n_ghosts is being sent as a proc_id

          //proc.proc_id = ublk_proc_ghosts.size();
          //proc.write(g_sp_streams[home_sp]);

          //  Same NMI is sent to the home SP and the ghost SP

          // Restore the home SP identifier for sending to the ghost SP's
          //proc.proc_id = home_sp;
          ccDOTIMES(ghost, ublk_proc_ghosts.size()) {
            STP_PROC ghost_sp = update_ghost_info(&ghost_info,&ublk_proc_ghosts[ghost]); 
            ghost_info.write(g_sp_streams[home_sp]);
            if (!sent_descriptor_to_sp[ghost_sp]) {
              ublk_base.write(g_sp_streams[ghost_sp]);
              simple_desc.write(g_sp_streams[ghost_sp]);
              sent_descriptor_to_sp[ghost_sp] = true;
            }

            // write a cube_offset + home_sp pair for each ublk to the ghost sp
            c_offset.cube_offset = cube_offset;
            c_offset.implicit_solid_state_index = ublk_base.implicit_solid_state_index;  
            c_offset.write(g_sp_streams[ghost_sp]);
            ublk_proc.write(g_sp_streams[ghost_sp]);
            ghost_info.proc_id = home_sp;
            ghost_info.write(g_sp_streams[ghost_sp]);
          }

          forward_ublk_full_ckpt_data<RT>(home_sp, is_vr_fine, ublk_proc_ghosts);

          if (!is_vr_fine) {
            const auINT32 fluid_like_voxel_mask = (cp_info.n_dims == 2) ? 0x55 : 0xFF;
            g_seed_ctl.maybe_write_smart_seed_data(&simple_desc, fluid_like_voxel_mask, home_sp, part_index, NULL, 0);
          }

          ccDOTIMES(loc,3) {
            simple_desc.b.location[loc] = orig_ublk_location[loc];
          }
        } // for all cube offsets

        // send a sentinel cube_offset of -1 to all SPs to whom the descriptor
        // has been sent to end processing of this descriptor

        ccDOTIMES(sp, total_sps) {
          if (sent_descriptor_to_sp[sp]) {
            c_offset.cube_offset = -1;
            c_offset.write(g_sp_streams[sp]);
          }
        }
      } // cube_factor != 0
      else {
        ublk_proc.read(m_decomp_istream);
        home_sp = ublk_proc.home_sp;
        cp_info.ublk_procs[realm].push_back(home_sp);
        if (home_sp >= total_sps) {
          msg_internal_error("Decomposition record for ublk ID %d references processor %d, "
                             " but the simulation has only %d processors",
                             ublk_base.ublk_id, home_sp, total_sps);
        }

        if (cp_info.are_movbs_present()) {
          if (ublk_proc.num_ghost_sps > 0) {
            g_ublk_ghost_vec_index.push_back(g_ublk_ghost_proc_vec.size());
            g_ublk_ghost_proc_vec.push_back(ublk_proc.num_ghost_sps);
          }
          else
            g_ublk_ghost_vec_index.push_back(-1);
        }

        ccDOTIMES(ghost, ublk_proc.num_ghost_sps) {
          UBLK_PROC_GHOST ublk_proc_ghost;
          ublk_proc_ghost.read(m_decomp_istream);
          ublk_proc_ghosts.push_back(ublk_proc_ghost);
          if (cp_info.are_movbs_present()) {
            g_ublk_ghost_proc_vec.push_back(ublk_proc_ghost.ghost_sp);
          }
        }

        // bsurfels only interact with real ublks which have cube factor = 0
        if (build_octree) 
          maybe_add_ublk_to_octree(ublk_base); 

        if (is_vr_coarse) {
          last_coarse_home_sp = home_sp;
        }

        if (is_non_advect_only_vr_fine && (home_sp != last_coarse_home_sp)) {
          msg_internal_error("VR fine ublk (ID %d) is not assigned to the same SP as its coarse parent",
                             ublk_base.ublk_id);
        }

        //set implicit_solid_state_index for current ublk descriptor
        ublk_base.implicit_solid_state_index = -1; 
        if (ublk_base.ublk_flags & DGF_UBLK_CONDUCTION) {
          ublk_base.implicit_solid_state_index = cp_info.implicit_solid_solver_state_index_offset[home_sp];
          //msg_print("total_sp > 1 cf = 0 ublk id: %d, home_sp %d state index %d", ublk_base.ublk_id, home_sp, ublk_base.implicit_solid_state_index );
        }
        
        ublk_base.write(g_sp_streams[home_sp]);

        // Home SP identifier
        //proc.proc_id = home_sp;
        ublk_proc.write(g_sp_streams[home_sp]);
        // Number of ghosts
        //proc.proc_id = ublk_proc_ghosts.size();
        //proc.write(g_sp_streams[home_sp]);

        // Same NMI is sent to the home SP and the ghost SP

        // Restore the home SP identifier for sending to the ghost SP's
        //proc.proc_id = home_sp;

        ccDOTIMES(ghost, ublk_proc_ghosts.size()) {

          STP_PROC ghost_sp = update_ghost_info(&ghost_info,&ublk_proc_ghosts[ghost]);

          // send the home SP identifier and the ublk base to each ghost SP

          ublk_base.write(g_sp_streams[ghost_sp]);
          ublk_proc.write(g_sp_streams[ghost_sp]);
          
          // Send the ghost info for this ghost tp the home SP

          ghost_info.write(g_sp_streams[home_sp]);
          // Now send it to the ghost SP. Since it knows its ID,
          // send it the home proc ID instead. Redundant.
          ghost_info.proc_id = home_sp;
          ghost_info.write(g_sp_streams[ghost_sp]);
        }
      } // cube_factor == 0
    } else {// total_sps > 1
      //set implicit_solid_state_index for current ublk descriptor
      ublk_base.implicit_solid_state_index = -1; 
      if (ublk_base.ublk_flags & DGF_UBLK_CONDUCTION) {
        ublk_base.implicit_solid_state_index = cp_info.implicit_solid_solver_state_index_offset[home_sp];
        //msg_print("total_sp = 1 ublk id: %d, home_sp %d state index %d", ublk_base.ublk_id, home_sp, ublk_base.implicit_solid_state_index );
      }
      ublk_base.write(g_sp_streams[home_sp]);
      if (build_octree) { // unravel ublk compaction for populating ublk octree for bsurfel home ublk search
        // bsurfels only interact with real ublks which have cube factor = 0
        if (ublk_base.cube_factor == 0) {
          maybe_add_ublk_to_octree(ublk_base); 
        }
      }
    }
    

    //Read the remaining records from the ublk_base+table
    switch(ublk_base.ublk_type)
      {
      case DGF_UBLK_SIMPLE: {
        if (total_sps > 1 && ublk_base.cube_factor != 0) {
          n_fluid_like_voxels = is_vr_fine ? 0 :
                                ((cp_info.n_dims == 3) ? N_VOXELS_8  : N_VOXELS_2D) *
                                (1 << (cp_info.n_dims * ublk_base.cube_factor));
        }
        else {
          simple_desc.b = ublk_base;
          simple_desc.read (m_ublk_table_istream);
          scale = simple_desc.b.voxel_scale;
          n_fluid_like_voxels = process_dgf_simple_ublk_desc<RT>(realm, &simple_desc, home_sp, ublk_proc_ghosts);
        }
        break;
      }

      case DGF_UBLK_REAL: {
        real_desc.b = ublk_base;
        real_desc.read (m_ublk_table_istream);
        scale = real_desc.b.voxel_scale;
        n_fluid_like_voxels = process_dgf_real_ublk_desc<RT>(realm, &real_desc, home_sp, ublk_proc_ghosts, n_nw_fluid_like_voxels);
        break;
      }

      case DGF_UBLK_MIRROR: {
        die_if_gpu(true, "Symmetry Planes", true);
        mirror_desc.b = ublk_base;
        mirror_desc.read (m_ublk_table_istream);
        mirror_desc.write(g_sp_streams[home_sp]);
        scale = mirror_desc.b.voxel_scale;
        n_fluid_like_voxels = 0;
        ccDOTIMES(ghost, ublk_proc_ghosts.size()) {
          STP_PROC ghost_sp = ublk_proc_ghosts[ghost].ghost_sp;
         mirror_desc.write(g_sp_streams[ghost_sp]);
        }
        forward_ublk_full_ckpt_data<RT>(home_sp, is_vr_fine, ublk_proc_ghosts);
        break;
      }

      default:
        msg_internal_error("Unknown ublk type %d encountered while reading ublk table.", (int)ublk_base.ublk_type);
        break;
      }

    num_voxels_processed += n_fluid_like_voxels;
    cp_info.total_fluid_like_voxels += n_fluid_like_voxels;
    cp_info.total_fluid_like_voxels_in_each_realm[realm] += n_fluid_like_voxels;
    cp_info.total_nw_fluid_like_voxels += n_nw_fluid_like_voxels;
    cp_info.total_nw_fluid_like_voxels_in_each_realm[realm] += n_nw_fluid_like_voxels;
    if (ublk_base.lrf_index >= 0)
      cp_info.lrf_n_fluid_like_voxels[ublk_base.lrf_index] += n_fluid_like_voxels;
    n_fluid_like_voxels_per_scale[scale] += n_fluid_like_voxels;
    
    //Counting degrees of freedom for the implicit solid conduction solver except for cube_factor !=0
    if (ublk_base.ublk_flags & DGF_UBLK_CONDUCTION) {
      if (ublk_base.ublk_type == DGF_UBLK_SIMPLE && total_sps > 1 && ublk_base.cube_factor != 0){
        cp_info.num_implicit_solid_solver_states += n_fluid_like_voxels;
        //implicit_solid_solver_state_index_offset is calculated before
      }
      else {
        cp_info.implicit_solid_solver_state_index_offset[home_sp] += n_fluid_like_voxels;
        cp_info.num_implicit_solid_solver_states += n_fluid_like_voxels;
        //std::cout << "<CP> ublk id: " << ublk_base.ublk_id << 
        //             ", home_sp: " << home_sp <<
        //             ", n_voxels: " << n_fluid_like_voxels <<
        //             ", base state index: " << ublk_base.implicit_solid_state_index << 
        //             ", state offset: " << cp_info.implicit_solid_solver_state_index_offset[home_sp] << 
        //             ", num_states: " << cp_info.num_implicit_solid_solver_states << std::endl; 
                     //", SP / total_sps: " << home_sp  << " / " << total_sps << std::endl;
      }
    }

    asINT32 percent_voxels = num_voxels_processed * (100.0 * one_over_num_voxels);
    if (percent_voxels > percent_voxels_processed) {
      percent_voxels_processed = percent_voxels;

      sprintf(status, "Initializing voxels (%d%% complete [%ld of %ld])",
              percent_voxels_processed, num_voxels_processed, num_voxels);
      cp_jobctl_output_status(status);
      maybe_terminate_simulation();
    }
  } // for num_ublks

  if (ublocks_reader_ptr)
  {
    // Remove reference to ublocks from global variable
    g_hdf5_reader.rm_shob_ref_of_collection("Ublocks");
    // Delete shob of ublocks if it exists
    delete ublocks_reader_ptr;
  }

  if (cp_info.do_split_seed) {
    g_seed_ctl.clear_surface_normals();
    g_seed_ctl.delete_norm_meas_cell_indices();
  }

  if(realm == STP_FLOW_REALM) {
    g_ublk_octree.build_octree();
  }

  // Send a sentinel ublk with a type of -1
  cDGF_UBLK_BASE ublk_base_sentinel;
  ublk_base_sentinel.ublk_type = DGF_UBLK_INVALID;
  ccDOTIMES(sp, total_sps) {
    ublk_base_sentinel.write(g_sp_streams[sp]);
  }

  sprintf(status, "Initializing voxels (100%% complete [%ld of %ld])",
          cp_info.total_fluid_like_voxels, cp_info.total_fluid_like_voxels);
  cp_jobctl_output_status(status);

  DO_SCALES_FINE_TO_COARSE(scale)
    cp_info.total_fluid_like_fevos += (dFLOAT)n_fluid_like_voxels_per_scale[scale] / (dFLOAT)scale_to_delta_t(scale);

  DO_CP_MEAS_WINDOWS(window) {
    if (window->sri_file_type == SRI_FLUID_TYPE) {
      CP_FLUID_MEAS_WINDOW win = static_cast<CP_FLUID_MEAS_WINDOW>(window);
      assign_part_indices_for_meas_cells_of_current_meas_cube(win); // process the last meas cube
      create_nw_centroids_for_meas_cells_of_current_meas_cube(win); // process the last meas cube
    }
  }
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  free_ublk_ckpt_buf();
//#endif
}

template VOID sCP_DGF_READER::read_ublk_descriptors<sCP_DGF_READER::LEGACY, cDGF_FLOW_UBLK_PROC, cDGF_FLOW_UBLK_PROC_GHOST>(REALM realm);
template VOID sCP_DGF_READER::read_ublk_descriptors<sCP_DGF_READER::LEGACY, cDGF_COND_UBLK_PROC, cDGF_COND_UBLK_PROC_GHOST>(REALM realm);

template VOID sCP_DGF_READER::read_ublk_descriptors<sCP_DGF_READER::HDF5_SERIAL, cDGF_FLOW_UBLK_PROC, cDGF_FLOW_UBLK_PROC_GHOST>(REALM realm);
template VOID sCP_DGF_READER::read_ublk_descriptors<sCP_DGF_READER::HDF5_SERIAL, cDGF_COND_UBLK_PROC, cDGF_COND_UBLK_PROC_GHOST>(REALM realm);

template VOID sCP_DGF_READER::read_ublk_descriptors<sCP_DGF_READER::DEBUG_MODE, cDGF_FLOW_UBLK_PROC, cDGF_FLOW_UBLK_PROC_GHOST>(REALM realm);
template VOID sCP_DGF_READER::read_ublk_descriptors<sCP_DGF_READER::DEBUG_MODE, cDGF_COND_UBLK_PROC, cDGF_COND_UBLK_PROC_GHOST>(REALM realm);

static VOID compute_lseg_area_centroid_normal(dFLOAT v[2][3], 
                                             BOOLEAN reverse_normal, 
                                             //outputs from this function
                                             dFLOAT &area, 
                                             dFLOAT centroid[3], 
                                             dFLOAT normal[3])
{

  // In 2D, surfel area is length
  area = sqrt((v[0][0]-v[1][0]) * (v[0][0]-v[1][0])  +  (v[0][1]-v[1][1]) * (v[0][1]-v[1][1])); 

  if (area <= 0.0F)
    msg_error("Area of facet for immersed boundary is 0");

  normal[0] = (v[0][0]-v[1][0])/area; normal[1] = (v[1][1] - v[0][1])/area; normal[2] = 0.0F;
  if (reverse_normal) {
    dFLOAT tmp = normal[0];
    normal[0] = -normal[1];
    normal[1] = -tmp;
  }

  DOTIMES(i,3,centroid[i]=(v[0][i]+v[1][i])/2.0);
}

static std::tuple<sBG_POINT3d,sBG_VECTOR3d,dFLOAT> 
compute_tri_area_centroid_normal(const sBG_TRIANGLE3d& tri)
{

  return std::make_tuple( tri.Center(), tri.Normal(), tri.Area() );

}

static VOID process_dgf_bsurfel_desc(cDGF_BSURFEL_DESC *bsurfel_desc, STP_PROC bsurfel_home_sp, 
                                     const sBG_VECTOR3d& normal, dFLOAT area, asINT32 moving_face_index,
                                     bool is_restored_on_same_sps,
                                     std::unordered_map<STP_MEAS_WINDOW_INDEX,std::vector<STP_PROC> >& moving_meas_cell_ckpt_sp_map)
{
  asINT32 n_dims               = cp_info.n_dims;
  asINT32 bsurfel_scale        = bsurfel_desc->bs.bsurfel_scale;
  asINT32 sri_bsurfel_scale    = sim_scale_to_sri_scale(bsurfel_scale);
  sriLRF_INDEX ref_frame_index = bsurfel_desc->bs.lrf_index;

  std::vector < cDGF_BSURFEL_MEAS_SURFEL_REFERENCE > &meas_surfel_refs = bsurfel_desc->meas_bsurfel_refs;

  asINT32 last_meas_window_index = -1;


  // loop over all measurement cell references
  DO_STD_VECTOR(cDGF_BSURFEL_MEAS_SURFEL_REFERENCE, meas_surfel_ref, meas_surfel_refs) {
    if (meas_surfel_ref.meas_window_index < last_meas_window_index)
      msg_internal_error("LGI bsurfel desc (ID %d) contains meas bsurfel references that are not"
                         " sorted by meas window index.",
                         bsurfel_desc->bs.bsurfel_id);
    last_meas_window_index = meas_surfel_ref.meas_window_index;

    CP_MEAS_WINDOW window = cp_info.meas_windows[meas_surfel_ref.meas_window_index];
    STP_MEAS_CELL_INDEX meas_surfel_index = cp_info.meas_cell_index_per_window[meas_surfel_ref.meas_window_index][meas_surfel_ref.meas_surfel_index];
    cBOOLEAN has_two_meas = FALSE;

    if(window->is_development || window->is_composite)
      meas_surfel_index = meas_surfel_ref.meas_surfel_index;
    else {
      meas_surfel_index = cp_info.meas_cell_index_per_window.at(meas_surfel_ref.meas_window_index).at(meas_surfel_ref.meas_surfel_index);
      has_two_meas = mci_has_two_meas(meas_surfel_index);
      meas_surfel_index >>= 1;
    }
    if(!window->is_composite && !window->is_development)
      cp_info.meas_surfel_index_per_window.at(meas_surfel_ref.meas_window_index).at(meas_surfel_index) = meas_surfel_ref.meas_surfel_index;
    if(has_two_meas && !window->is_composite && !window->is_development) {
      meas_surfel_index = cp_info.meas_cell_index_per_window[meas_surfel_ref.meas_window_index][meas_surfel_ref.meas_surfel_index];
      cp_info.meas_surfel_index_per_window.at(meas_surfel_ref.meas_window_index).at(meas_surfel_index) = meas_surfel_ref.meas_surfel_index;
    }
    if (window->is_development) {
      CP_SURFACE_DEV_MEAS_WINDOW win =
        static_cast<CP_SURFACE_DEV_MEAS_WINDOW> (window);
      STP_MEAS_CELL_INDEX segment_plus_axis = meas_surfel_index;
      asINT32 iface = bsurfel_desc->bs.face_index;
      if (!win->m_face_is_movb[iface])
        win->m_face_is_movb[iface] = TRUE;

      // extract segment and axis, though only axis will be used
      STP_MEAS_CELL_INDEX segment;
      uINT8 axis;
      lgi_meas_cell_index_dev_segment(segment_plus_axis, segment, axis);
      meas_surfel_index = win->m_face_first_meas_cell[axis][iface];

      // replace segment with real meas_surfel_index
      meas_surfel_ref.meas_surfel_index = lgi_meas_dev_segment_cell_index(meas_surfel_index, axis);

      if (meas_surfel_index < 0)
        msg_internal_error("LGI bsurfel desc (ID %d) contains negative meas bsurfel index (%d) for window \"%s\"",
                           bsurfel_desc->bs.bsurfel_id, meas_surfel_index, window->cdi_meas_window->name);

      STP_MEAS_CELL_INDEX face_n_segments   = win->m_face_n_segments[axis][iface];
      STP_MEAS_CELL_INDEX face_last_meas_cell = win->m_face_first_meas_cell[axis][iface] + face_n_segments;

      if (face_last_meas_cell > window->n_meas_cells)
        msg_internal_error("LGI bsurfel desc (ID %d) meas surfel index (%d) for window \"%s\" is out of range [0,%lu)",
                           bsurfel_desc->bs.bsurfel_id, face_last_meas_cell,
                           window->cdi_meas_window->name, window->n_meas_cells);

      // since references are created for all segments of the face on all SPs,
      // verifying the first segment on the last sp is sufficient.
      // Previously all the segments and sp were verified which is NP*NP
      // operation.
      if (!window->is_meas_cell_on_sp(meas_surfel_index, total_sps-1)) {
        ccDOTIMES(j,face_n_segments) {
          STP_MEAS_CELL_INDEX imeas_surfel = meas_surfel_index + j;

          // Bsurfels can potentially transit through all the SPs so add a
          // reference to the bsurfel dev meas cell on each sp
          ccDOTIMES(sp, total_sps) {
            window->m_sp_n_meas_cells[sp] ++;
            window->add_sp_meas_cell_ref(imeas_surfel, sp);
          }
        }
      }
    }
    else {
      if ( meas_surfel_index >= window->n_meas_cells)
        msg_internal_error("LGI bsurfel desc (ID %d) meas bsurfel index (%d) for window \"%s\" is out of range [0,%lu)",
                           bsurfel_desc->bs.bsurfel_id, meas_surfel_index,
                           window->cdi_meas_window->name, window->n_meas_cells);

      if (window->is_composite) {
        if (window->m_ref_frame_indices.size() != 0) { // will be NULL if there are no LRFs
          cBOOLEAN is_meas_cell_in_sliding_rf = (window->m_ref_frame_indices.at(meas_surfel_index) < 0 ) ?  FALSE :
            (cp_info.sri_lrfs[window->m_ref_frame_indices.at(meas_surfel_index)].type == SRI_LRF_MOVING);

          cBOOLEAN is_bsurfel_in_sliding_rf = (ref_frame_index < 0 ) ?  FALSE :
            (cp_info.sri_lrfs[ref_frame_index].type == SRI_LRF_MOVING);

          if (window->m_ref_frame_indices.at(meas_surfel_index) == SRI_INVALID_REF_FRAME_INDEX)  {
            window->m_ref_frame_indices.at(meas_surfel_index) = ref_frame_index;
          } else if ((window->m_ref_frame_indices.at(meas_surfel_index) != ref_frame_index) &&
                     (window->m_ref_frame_indices.at(meas_surfel_index) != SRI_OVERLAP_REF_FRAME_INDEX) &&
                     (is_meas_cell_in_sliding_rf || is_bsurfel_in_sliding_rf) &&
                     (window->is_output_in_local_csys)) {
            if ((ref_frame_index != SRI_GLOBAL_REF_FRAME_INDEX) &&
                (window->m_ref_frame_indices.at(meas_surfel_index) != SRI_GLOBAL_REF_FRAME_INDEX )) {
              msg_warn("Face \"%s\" spans local reference frames (\"%s\" and \"%s\"). Thus for"
                       " composite window \"%s\", vectors (force, velocity, etc) for this face"
                       " will be averaged in the global reference frame coordinate system.",
                       cp_info.sri_faces[bsurfel_desc->bs.face_index].name,
                       cp_info.sri_lrfs[window->m_ref_frame_indices.at(meas_surfel_index)].name,
                       cp_info.sri_lrfs[ref_frame_index].name, window->cdi_meas_window->name);
            } else  {
              char *lrf_name = (ref_frame_index == SRI_GLOBAL_REF_FRAME_INDEX) ?
                cp_info.sri_lrfs[window->m_ref_frame_indices.at(meas_surfel_index)].name :
                cp_info.sri_lrfs[ref_frame_index].name;
              msg_warn("Face \"%s\" spans local reference frame \"%s\" and global reference frame. Thus for"
                       " composite window \"%s\", vectors (force, velocity, etc) for this face will be"
                       " averaged in the global reference frame coordinate system.",
                       cp_info.sri_faces[bsurfel_desc->bs.face_index].name,lrf_name, window->cdi_meas_window->name);
            }
            window->m_ref_frame_indices.at(meas_surfel_index) = SRI_OVERLAP_REF_FRAME_INDEX;
          }
        }
      }
    }

    if (window->is_composite || window->is_development) {
      ccDOTIMES(vv, 3) {
        cDGF_BSURFEL_VERTEX_INDEX vi = bsurfel_desc->vertices[vv];
        sVPoint *vertex = &cp_info.vertex_array[vi.vertex_index];
        ccDOTIMES(axis, n_dims) {
          if (vertex->pcoord[axis] < window->min_bound[axis])
            window->min_bound[axis] = floorf(vertex->pcoord[axis]);
          if (vertex->pcoord[axis] > window->max_bound[axis])
            window->max_bound[axis] = ceilf(vertex->pcoord[axis]);
        }
      }
      if (window->is_composite) {
        CP_COMPOSITE_SURFACE_MEAS_WINDOW win = static_cast<CP_COMPOSITE_SURFACE_MEAS_WINDOW > (window);
        win->m_moving_face_n_surfels[moving_face_index]++;
      }
    } else {
      CP_SURFACE_MEAS_WINDOW win = static_cast<CP_SURFACE_MEAS_WINDOW > (window);
      if (is_sri_scale_finer(sri_bsurfel_scale, win->m_scales[meas_surfel_ref.meas_surfel_index]))
        win->m_scales[meas_surfel_ref.meas_surfel_index] = sri_bsurfel_scale;
      if (window->m_ref_frame_indices.size() != 0) { // will be NULL if there are no LRFs
        if (window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) == SRI_INVALID_REF_FRAME_INDEX)
          window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) = ref_frame_index;
        else if (window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index) != ref_frame_index)
          msg_internal_error("LGI bsurfel desc (ID %d, ref frame %d) meas surfel index (%d) for window \"%s\""
                             " refers to meas bsurfel already associated with ref frame %d",
                             bsurfel_desc->bs.bsurfel_id, ref_frame_index, meas_surfel_index, window->cdi_meas_window->name,
                             window->m_ref_frame_indices.at(meas_surfel_ref.meas_surfel_index));
      }

      win->m_d_surfel_areas[meas_surfel_ref.meas_surfel_index] += area;
      if(!has_two_meas ) {
        STP_MEAS_COORD_INDEX meas_coord_index = ((STP_MEAS_COORD_INDEX) meas_surfel_ref.meas_surfel_index) * n_dims;
        win->m_surfel_normals[meas_coord_index + 0] = normal[0];
        win->m_surfel_normals[meas_coord_index + 1] = normal[1];
        if (n_dims == 3)
          win->m_surfel_normals[meas_coord_index + 2] = normal[2];
      }
      if(!window->is_development && !window->is_composite)
	meas_surfel_ref.meas_surfel_index = meas_surfel_index;
      win->m_moving_face_n_surfels[moving_face_index]++;
    }

      if (cp_info.is_full_checkpoint_restore && !is_restored_on_same_sps && !window->is_development) {
        auto& moving_meas_cell_ckpt_sp = moving_meas_cell_ckpt_sp_map[window->index];
        if ( moving_meas_cell_ckpt_sp.size() == 0 ) {
          moving_meas_cell_ckpt_sp.resize(window->n_moving_meas_cells,STP_PROC_INVALID);
        }
        moving_meas_cell_ckpt_sp.at(meas_surfel_index - (window->n_stationary_meas_cells)) = bsurfel_home_sp;
      }
  }
}

static asINT32 find_moving_face(sINT16 face_index, 
                                sriLRF_INDEX ref_frame_index)
                                
{
  bool found = false;
  ccDOTIMES(i, cp_info.n_movbs()) {
    if (cp_info.sri_movbs[i].parent_face_index == face_index) {
      found = true;
      // TODO: When support is added for a moving face to straddle lrfs, this
      // moving face instance should be copied over into a new one with a
      // separate ref frame index
      cp_info.sri_movbs[i].ref_frame_index = ref_frame_index;
      return i;
      break;
    }
  }

  if (!found) {
    msg_internal_error("Unable to locate moving face index %d", face_index);
  }

  // Will not come here, but prevent compiler warning
  return -1;

}


static std::tuple<sBG_POINT3d,sBG_VECTOR3d> 
rotate_bsurfel_centroid_and_normal(const sSRI_XFORM& xform, const sBG_POINT3d& centroid, const sBG_VECTOR3d& normal) 
{
  auto bg_xform = to_bg_transform(xform);
  return std::make_pair( bg_xform.Transform(centroid), bg_xform.Transform(normal) );
}

static sBG_POINT3d to_bg_point(const STP_DGEOM_VARIABLE p[3]) {
  sBG_POINT3d bg;
  ccDOTIMES(i,3) {
    bg[i] = p[i];
  }
  return bg;
}

static sBG_VECTOR3d to_bg_vector(const STP_DGEOM_VARIABLE p[3]) {
  sBG_VECTOR3d bg;
  ccDOTIMES(i,3) {
    bg[i] = p[i];
  }
  return bg;
}

static void to_array(const sBG_POINT3d& bg, STP_DGEOM_VARIABLE p[3]) {
  ccDOTIMES(i,3) {
    p[i] = bg[i];
  }
}

static void to_array(const sBG_VECTOR3d& bg, STP_DGEOM_VARIABLE p[3]) {
  ccDOTIMES(i,3) {
    p[i] = bg[i];
  }
}

static std::tuple<sBG_POINT3d, sBG_VECTOR3d, dFLOAT> 
compute_deforming_bsurfel_centroid_normal_area(const Tire::cDEFORMING_TIRE& dtire, 
                                             Dierckx::cSURFACE::sEVAL_DATA& ed, 
                                             sriDOUBLE angle_rotated, 
                                             std::unordered_map<DGF_VERTEX_INDEX, cDGF_DEFORMING_TIRE_VERTEX>& vertex_cache, 
                                             const cDGF_BSURFEL_VERTEX_INDEX vidx[3])
{
          sBG_POINT3d v[3];

          ccDOTIMES(vv, 3) {
            cDGF_BSURFEL_VERTEX_INDEX vi = vidx[vv];
            cDGF_DEFORMING_TIRE_VERTEX& vertex = vertex_cache[vi.vertex_index];
            sBG_POINT3d xyz;
            if ( !vertex.is_seeded ) {
              v[vv] = dtire.rotate(angle_rotated, to_bg_point(vertex.uvw), ed);
              to_array(v[vv],vertex.xyz);
              vertex.is_seeded = TRUE;
            }
            else {
              v[vv] = to_bg_point(vertex.xyz);
            }
          }

          return compute_tri_area_centroid_normal(sBG_TRIANGLE3d(v[0],v[1],v[2]));
}

VOID sCP_DGF_READER::read_bsurfel_descriptors(bool is_restored_on_same_sps, 
                                              std::unordered_map<STP_MEAS_WINDOW_INDEX,std::vector<STP_PROC> >& moving_meas_cell_ckpt_sp_map)
{
  if (m_table_of_contents.bsurfel_table_pos < 0 ) {
    return;
  }
  jump_to_file_position(m_table_of_contents.bsurfel_table_pos);

  cDGF_BSURFEL_TABLE table_header;
  table_header.read(m_main_istream);
  asINT32 n_dims = cp_info.n_dims;

  if (table_header.num_bsurfels == 0)
    return;

  table_header.tag.length = 0; // set to a non-positive number since record length is not known apriori
  ccDOTIMES(sp, total_sps) {
    table_header.write(g_sp_streams[sp], FALSE);
  }

  char status[256];
  dFLOAT one_over_num_bsurfels = 1.0 / table_header.num_bsurfels;
  asINT32 percent_bsurfels_processed = 0;
  cp_jobctl_output_status("Initializing bsurfels");
  BOOLEAN seed_bsurfels = (!cp_info.face_index_to_seed_xform_map.empty());

  STP_PROC home_sp = 0;
#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  std::vector<char> ckpt_buf; //Changed this to eliminate need for a specific LGI_MAX_SHOB_CKPT_LEN 
                              //which could be exceeded if checkpointed particles are included with the record.
#else
  char ckpt_buf[LGI_MAX_SHOB_CKPT_LEN];
#endif
  cDGF_BSURFEL_DESC bsurfel_desc;
  cDGF_BSURFEL_GEOM bsurfel_geom;
  cDGF_CKPT_SHOB_HEADER shob_header;

  std::unordered_map<DGF_VERTEX_INDEX,cDGF_DEFORMING_TIRE_VERTEX> lgi_deforming_vertices;
  std::vector< std::unordered_set<DGF_VERTEX_INDEX> > bsurfel_vertices_to_sps(total_sps);
  std::vector<Tire::cSURFACE_POINT_FINDER> dtire_finders;
  Dierckx::cSURFACE::sEVAL_DATA ed;
  dtire_finders.reserve(cp_info.n_dtires);

  ccDOTIMES(t,cp_info.n_dtires) {
    dtire_finders.push_back( cp_info.sri_dtires[t].dtire->get_finder() );
  }

  ccDOTIMES(bsurfel_id, table_header.num_bsurfels) {

    bsurfel_desc.read(m_main_istream);
    sBG_POINT3d vtmp[3];

    // Add the bsurfel face index to the set of moving faces
    asINT32 bsurfel_face_index = bsurfel_desc.bs.face_index;
    asINT32 phys_desc_index = cp_info.part_to_movb_phys_desc_index_map[cp_info.face_to_parent_part_index_map[bsurfel_face_index]];
    sriINT moving_xform_index = phys_desc_index;
    asINT32 moving_face_index = find_moving_face(bsurfel_face_index, bsurfel_desc.bs.lrf_index);
    asINT32 deforming_tire_index = cp_info.sri_movbs[moving_face_index].deforming_tire_index;
    bool is_deforming = cp_info.is_deforming_tire_index(deforming_tire_index);

    ccDOTIMES(vv, n_dims) {
      cDGF_BSURFEL_VERTEX_INDEX vi = bsurfel_desc.vertices[vv];
      sVPoint *vertex = &cp_info.vertex_array[vi.vertex_index];
      vtmp[vv] = to_bg_point(vertex->pcoord);

      if ( is_deforming ) {
        sSRI_DEFORMING_TIRE & sri_dtire = cp_info.sri_dtires[deforming_tire_index];
        if (!lgi_deforming_vertices.count(vi.vertex_index) ) {
          sBG_POINT3d uvw = sri_dtire.dtire->to_uvw(vtmp[vv], dtire_finders[deforming_tire_index]);

          cDGF_DEFORMING_TIRE_VERTEX dgf_vertex;
          dgf_vertex.deforming_tire_index = deforming_tire_index;
          dgf_vertex.vertex_index = vi.vertex_index;
          dgf_vertex.is_seeded = FALSE;
          to_array(uvw, dgf_vertex.uvw);
          // overwrite the XYZ value in the vertex array with the computed UVW value
          // This will make sure these vertices are output to measurement files
          to_array(uvw, cp_info.vertex_array[vi.vertex_index].pcoord); 

          // the XYZ -> UVW -> XYZ conversion is NOT technically invertible, 
          // so we have to store the "new" position and use it to compute the 
          // bsurfel geometry
          auto new_xyz = sri_dtire.dtire->to_xyz(uvw, ed); 
          auto d = new_xyz - vtmp[vv];
          dgf_vertex.location_is_accurate = Tire::norm(d) < 1.0;

          vtmp[vv] = new_xyz;
          to_array(vtmp[vv],dgf_vertex.xyz); 
          lgi_deforming_vertices.insert( std::make_pair(vi.vertex_index, dgf_vertex) );
        }
        else { // this vertex has already been processed, and vtmp is already in uvw form
          vtmp[vv] = sri_dtire.dtire->to_xyz(vtmp[vv], ed); 
        }
      }
    }

    sBG_TRIANGLE3d tri( vtmp[0], vtmp[1], vtmp[2] );
    sBG_POINT3d centroid;
    sBG_VECTOR3d normal;
    dFLOAT area;

    std::tie(centroid, normal, area) = compute_tri_area_centroid_normal(tri);

    to_array(centroid, bsurfel_geom.centroid);
    to_array(normal, bsurfel_geom.normal);
    bsurfel_geom.area = area;

    sBG_POINT3d new_centroid = centroid;
    sBG_VECTOR3d new_normal = normal;

    bool seed_this_face = seed_bsurfels && cp_info.face_index_to_seed_xform_map.count(bsurfel_face_index);
    bool needs_rotation = seed_this_face || cp_info.is_full_checkpoint_restore;
    
    //We do not want to use the octree to change the bsurfel home_ublk anymore.
    //The SPs take care of putting the bsurfel in the right home_ublk and voxel.

    if ( needs_rotation ) {
      const sSRI_XFORM * xform{nullptr};
      if (seed_this_face) {
           xform = &cp_info.face_index_to_seed_xform_map[bsurfel_face_index];
      }
      else if (cp_info.is_full_checkpoint_restore) {
            xform = &cp_info.ckpt_movb_xforms[moving_xform_index];
      }
      else {
        msg_internal_error("Unknown bsurfel_rotation");
      }

      if (is_deforming) {
        sSRI_DEFORMING_TIRE & sri_dtire = cp_info.sri_dtires[deforming_tire_index];
        std::tie(new_centroid, new_normal, area) = compute_deforming_bsurfel_centroid_normal_area( 
                                                    *(sri_dtire.dtire),
                                                     ed,
                                                     xform->get_angle_rotated(),
                                                     lgi_deforming_vertices,
                                                     bsurfel_desc.vertices);
      }
      else {
        std::tie(new_centroid, new_normal) = rotate_bsurfel_centroid_and_normal(
                                               *xform,
                                               new_centroid, 
                                               new_normal);
      }
    }

    if (total_sps > 1) {
      home_sp = cp_info.ublk_procs[STP_FLOW_REALM][bsurfel_desc.bs.ublk_id];
      if (home_sp >= total_sps) {
        msg_internal_error("Home ublk for bsurfel ID %d references processor %d, "
                           " but the simulation has only %d processors",
                           bsurfel_id, home_sp, total_sps);
      }
    }

    process_dgf_bsurfel_desc(&bsurfel_desc, home_sp, normal, area, moving_face_index, is_restored_on_same_sps, moving_meas_cell_ckpt_sp_map);

    // send bsurfel descriptor and the original geometry
    bsurfel_desc.write(g_sp_streams[home_sp]);
    bsurfel_geom.phys_desc_index = phys_desc_index;
    bsurfel_geom.write(g_sp_streams[home_sp]);

    if ( is_deforming ) {
      ccDOTIMES(axis,3) {
        bsurfel_vertices_to_sps[home_sp].insert(bsurfel_desc.vertices[axis].vertex_index);
      }
    }

    // now we send the seeded geometry, the new centroid, normal, area
    if (g_seed_ctl.is_smart_seed() || cp_info.is_full_checkpoint_restore) {
      // Write the transformed centroid and normal corresponding to the seeded or restored position
      to_array(new_centroid, bsurfel_geom.centroid);
      to_array(new_normal, bsurfel_geom.normal);
      bsurfel_geom.area = area; // For deforming moving faces, the bsurfel area could have changed
      bsurfel_geom.write(g_sp_streams[home_sp]);
    }

    asINT32 percent_bsurfels = (bsurfel_id+1) * (100.0 * one_over_num_bsurfels);
    if (percent_bsurfels > percent_bsurfels_processed) {
      percent_bsurfels_processed = percent_bsurfels;

      sprintf(status, "Initializing bsurfels (%d%% complete [%d of %d])",
              percent_bsurfels_processed, bsurfel_id+1, table_header.num_bsurfels);
      cp_jobctl_output_status(status);
      maybe_terminate_simulation();
    }

  } // for num_bsurfels

  // send sentinel bsurfel to all SPs
  bsurfel_desc.bs.bsurfel_id = STP_INVALID_BSURFEL_ID;
  ccDOTIMES(sp,total_sps) {
    bsurfel_desc.write(g_sp_streams[sp]);
  }

  if ( cp_info.has_deforming_tires() ) {
    cDGF_DEFORMING_TIRE_VERTEX invalid;
    invalid.vertex_index = STP_INVALID_BSURFEL_ID;
    ccDOTIMES(sp,total_sps) {
      for(const auto& vv : bsurfel_vertices_to_sps[sp]) {
        cDGF_DEFORMING_TIRE_VERTEX& lgi_v = lgi_deforming_vertices.at(vv);
        lgi_v.write(g_sp_streams[sp]);
      }
      invalid.write(g_sp_streams[sp]);
    }
  }

  // If a simulation has development windows and immersed boundaries, we need
  // to ensure that meas cells for every development window are allocated on
  // every SP.  As the immersed boundary moves around the simulation volume,
  // any face can be present in an SP at any time, and contribute to any of the
  // development window meas cells.  So we need to make sure that development
  // window meas cells for all the moving boundary faces are allocated on every
  // SP.
  DO_CP_MEAS_WINDOWS(window) {
    if (window->is_development && (window->meas_window_type == LGI_SURFACE_WINDOW ||window->meas_window_type == LGI_SHELL_WINDOW) ) {
      CP_SURFACE_DEV_MEAS_WINDOW win = static_cast<CP_SURFACE_DEV_MEAS_WINDOW> (window);
      
      cDGF_MOVB_FACES movb_faces;
      movb_faces.reserve(cp_info.n_sri_faces);
      ccDOTIMES(face, cp_info.n_sri_faces) {
        if (win->m_face_is_movb[face])
          movb_faces.push_back(face);
      }
      ccDOTIMES(sp, total_sps) {
        movb_faces.write(g_sp_streams[sp]);
      }
    }
  }

  sprintf(status, "Initializing bsurfels (100%% complete [%d of %d])",
          table_header.num_bsurfels, table_header.num_bsurfels);
  cp_jobctl_output_status(status);

  cp_info.total_bsurfels = table_header.num_bsurfels;

  die_if_gpu(cp_info.total_bsurfels > 0, "Immersed Boundaries", true);

  g_ublk_ghost_proc_vec.resize(0);
  g_ublk_ghost_vec_index.resize(0);
  g_cp_dynblks.resize(0);
  g_ublk_octree.clear();

}

VOID sCP_DGF_READER::read_lrf_containment()
{
  if (m_table_of_contents.lrf_containment_pos < 0 ) {
    return;
  }

  jump_to_file_position(m_table_of_contents.lrf_containment_pos);

  cp_jobctl_output_status("Read MLRF containment");

  cDGF_LRF_CONTAINMENT lrf_containment;
  lrf_containment.read(m_main_istream); 
  ccDOTIMES(sp, total_sps) {
    lrf_containment.write(g_sp_streams[sp], FALSE);
  }


  ccDOTIMES(containment_set_index, lrf_containment.num_lrfs) {
    cDGF_LRF_CONTAINMENT_INDEX lrf_containment_index;
    lrf_containment_index.read(m_main_istream);
    asINT32 index = lrf_containment_index.lrf_index;
    if (index >= 0) {
      SRI_LRF sri_lrf = &cp_info.sri_lrfs[index];
      sri_lrf->parent_index = lrf_containment_index.containing_lrf_index;
    }

    ccDOTIMES(sp, total_sps) {
      lrf_containment_index.write(g_sp_streams[sp]);
    }
  }
}

VOID sCP_DGF_READER::read_mlrf_ring_sets(asINT32 n_ring_sets, STP_REALM realm)
{

  sLGI_POS mlrf_rings_pos = (realm == STP_FLOW_REALM) ? m_table_of_contents.flow_mlrf_rings_pos :
    m_table_of_contents.cond_mlrf_rings_pos;

  if (mlrf_rings_pos < 0 || n_ring_sets <= 0) {
    return;
  }

  jump_to_file_position(mlrf_rings_pos);

  char status[256];
  sprintf(status,"Read sliding mesh ring definitions for %s realm",realm == STP_FLOW_REALM ? "flow" : "conduction");
  cp_jobctl_output_status(status);

  // store the number of surfels per ring on each SP and ring segment
  // definitions
  std::vector<auINT32> n_ring_surfels_on_proc(total_sps);
  std::vector<LGI_MLRF_RING_SEGMENT> ring_segments;

  ccDOTIMES(ring_set_index, n_ring_sets) {

    cDGF_MLRF_RING_SET ring_set;
    ring_set.read(m_main_istream);

    // set to a non-positive number since record length on a per-sp basis is
    // not known apriori
    ring_set.tag.length = 0;
    ccDOTIMES(sp, total_sps) {
      ring_set.write(g_sp_streams[sp], FALSE);
    }

    cDGF_MLRF_RING ring;
    ccDOTIMES (rindex, ring_set.n_rings) {
      ring.read(m_main_istream);
      ccDOTIMES(sp, total_sps) {
        ring.write(g_sp_streams[sp]);
      }

      asINT32 n_ring_surfels = ring.n_surfels_in_ring; // surfel pairs
      cDGF_MLRF_RING_SURFEL ring_surfel;

      ring_segments.clear();

      std::fill(n_ring_surfels_on_proc.begin(), n_ring_surfels_on_proc.end(), 0);

      LGI_MLRF_RING_SEGMENT ring_segment;
      STP_PROC last_home_sp;

      // send all exterior ring surfels and compute ring segments
      ccDOTIMES(surfel_index, n_ring_surfels) {
        ring_surfel.read(m_main_istream);
        // write ring surfel ids and indexes only to their home SPs
        STP_PROC home_sp = (total_sps == 1)? 0 : cp_info.surfel_procs[realm][ring_surfel.surfel_id];
        n_ring_surfels_on_proc[home_sp]++;

        if (surfel_index == 0) {
          last_home_sp = home_sp;
          ring_segment.proc_id = home_sp;
          ring_segment.n_surfels = 0;
        }

        if (last_home_sp != home_sp) {
          // reset the ring segment
          ring_segments.push_back(ring_segment);
          ring_segment.n_surfels = 1;
          ring_segment.proc_id = home_sp;
          last_home_sp = home_sp;
        }
        else {
          ring_segment.n_surfels++;
        }

        ring_surfel.write(g_sp_streams[home_sp]);
        cDGF_RING_SURFEL_INDEX ring_index = {surfel_index};
        ring_index.write(g_sp_streams[home_sp]);
      }

      ring_segments.push_back(ring_segment);

      // now write all the interior surfel ids and indexes
      ccDOTIMES(surfel_index, n_ring_surfels) {
        ring_surfel.read(m_main_istream);
        STP_PROC home_sp = (total_sps == 1)? 0 : cp_info.surfel_procs[realm][ring_surfel.surfel_id];
        ring_surfel.write(g_sp_streams[home_sp]);
        cDGF_RING_SURFEL_INDEX ring_index = {surfel_index};
        ring_index.write(g_sp_streams[home_sp]);
      }
      // write end-marker for the surfels with an invalid id to all SPs
      ring_surfel.surfel_id = (STP_SURFEL_ID) -1; //avoid compiler warning
      ccDOTIMES(sp, total_sps)
        ring_surfel.write(g_sp_streams[sp]);

      // send the ring segment definitions only to processors which have at
      // least one surfel pair from the ring
      ccDOTIMES(sp, total_sps) {

        cDGF_RING_NUM_SEGMENTS ring_segment_size = {static_cast<asINT32>(n_ring_surfels_on_proc[sp] > 0? ring_segments.size() : 0)};
        if (n_ring_surfels_on_proc[sp] > 0) {
          ring_segment_size.write(g_sp_streams[sp]);
          ccDOTIMES(nr, ring_segments.size()) {
            ring_segments[nr].write(g_sp_streams[sp]);
          }
        }
      }
    } // for all rings in this ring set
  } // for all ring sets

}

VOID sCP_DGF_READER::read_neighbor_mask_table()
{
  copy_record_to_all_sps(m_decomp_istream, DGF_NEIGHBOR_MASK_TABLE_TAG);
}

RadIO::sPATCH_DECOMP sCP_DGF_READER::read_patch_decomposition()
{
  if (m_rad_file.open_file_for_read(cp_info.rad_filename) != RadIO::eERROR_CODE::SUCCESS) {
    msg_error("Could not open RAD file \"%s\"", cp_info.rad_filename.c_str());
  }

  RadIO::sPATCH_DECOMP patch_decomp;
  patch_decomp.allocate_decomp(0, m_rad_file.file_params().num_patches());

  auto err = m_rad_file.read_patch_decomp(patch_decomp);
  if (err != RadIO::eERROR_CODE::SUCCESS) {
    msg_error("Unable to read patch decomposition from '%s'",m_rad_file.filename().c_str());
  }
  return patch_decomp;
}

RadIO::sPATCH_DECOMP sCP_DGF_READER::allocate_patch_decomposition()
{
  if (m_rad_file.open_file_for_read(cp_info.rad_filename) != RadIO::eERROR_CODE::SUCCESS) {
    msg_error("Could not open RAD file \"%s\"", cp_info.rad_filename.c_str());
  }

  RadIO::sPATCH_DECOMP patch_decomp;
  if(!patch_decomp.allocate_decomp(0, m_rad_file.file_params().num_patches())){
    msg_error("Could not allocate decomp");  }

  return patch_decomp;
}
