/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * CP Argument list parsing
 *
 * Jim Salem, Exa Corporation 
 * Created Sat Jun  4 1994
 *--------------------------------------------------------------------------*/

#ifndef __PARSE_ARGS_H
#define __PARSE_ARGS_H

#include "common.h"

/*--------------------------------------------------------------------------*
 * Standard arguments
 *--------------------------------------------------------------------------*/
/* The parse_std_args function parses the CP argument list into the following
 * structure.  This structure is also sent to the SP for it to initialize
 * itself. */
#define STD_ARGS_MAX_STRLEN 255

typedef struct _std_args {
  /* TRUE if exa_enable_malloc_debug should be called */
  BOOLEAN		debug_malloc_p;
  EXA_MALLOC_DEBUG_MODE	debug_malloc_mode;

  /* If TRUE, the random number generator is set to zero upon */
  /* initialization */
  BOOLEAN		disable_random_p;
} sSTD_ARGS, *STD_ARGS;

/* This parses an argument list for the "standard" arguments and fills in 
 * a static STD_ARGS structure.  It returns an arglist with the standard
 * arguments removed.
 */
STD_ARGS parse_std_args(int *argc, char *argv[]);

/* This string contains a description of the "standard" options, suitable
 * for printing in a "Usage:..." message */
extern cSTRING std_args_description_string;

/*--------------------------------------------------------------------------*
 * Parsing utilities
 *--------------------------------------------------------------------------*/

/* This returns TRUE if the argument is present in the argument list.
 * If it is present, it removes the argument from the argument list by
 * modifing argc and argv.
 */
BOOLEAN parse_arg_present_p(cSTRING argname, int *argc, char *argv[]);

/* Same as parse_arg_present_p, but stops checking if a second argument is
 * found 
 */
BOOLEAN parse_arg_present_until(cSTRING argname, cSTRING argstop, int *argc, char *argv[]);

/* This returns TRUE if the argument is present in the argument list.
 */

BOOLEAN parse_arg_present(cSTRING argname, int *argc, char *argv[]);

/* This looks for an argument of the form: <argname> <argvalue>.  If the
 * argument is present, <argvalue> is returned, otherwise NULL is returned.
 * If it is present, it removes both argname and argvalue from the argument
 * list by modifing argc and argv.
 */
cSTRING parse_arg_string(cSTRING argname, int *argc, char *argv[]);

/* Same as parse_arg_string, but takes a stopping argument.
 */
cSTRING parse_arg_string_until(cSTRING argname, cSTRING argstop, int *argc, char *argv[]);

/* This looks for an argument of the form: <argname> <argvalue>.  If the
 * argument is present, <argvalue> is returned, otherwise 0xFFFFFFFF is 
 * returned. If it is present, this function removes both argname and  
 * argvalue from the argument list by modifing argc and argv.
 */
uINT32 parse_arg_uINT32(cSTRING argname, int *argc, char *argv[]);

/* This looks for an argument of the form: <argname> <argvalue>.  If the
 * argument is present, <argvalue> is returned, otherwise 0.0 is 
 * returned. If it is present, this function removes both argname and  
 * argvalue from the argument list by modifing argc and argv.
 */
BOOLEAN parse_arg_sFLOAT(cSTRING argname, sFLOAT &result, int *argc, char *argv[]);
BOOLEAN parse_arg_sINT32(cSTRING argname, sINT32 &result, int *argc, char *argv[]);

/* This removes the numbered argument (arg 0 is the first one) from the
 * argument list and returns it. 
 * If there was no such argument, NULL is returned.
 */
cSTRING parse_arg_string_and_optnl_asINT32(cSTRING argname, 
					   int *argc, 
					   char *argv[],
					   asINT32 *p_n);
cSTRING parse_arg_remove(asINT32 arg_number, int *argc, char *argv[]);

//Convert an integer to ordina strign (e.g. "1st", "2nd", "3rd",....)
std::string convert_to_ordinal(std::size_t i);

#endif /* __PARSE_ARGS_H */
