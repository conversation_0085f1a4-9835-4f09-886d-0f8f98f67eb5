/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#ifndef _CDI_READWRITE_H
#define _CDI_READWRITE_H

#include <algorithm>
#include <assert.h>
#include <stdio.h>
#include <string>
#include <vector>
#include <set>
#include <unordered_map>
#include <map>

#include "cdi_io.h"
#include "cdi_physics.h"

/* IMPORTANT:
 *
 * This file is now exported outside Exa for developers in need of reading
 * CDI files. Thus do not #include any Exa components other than CIO, SRI, or PRI.
 */

#include CIO_H
#include SRI_H
#include PRI_H
#include SIMUTILS_H

/* version 0.2: mstp added for measurement options -sjr 6/9/94 */
/* version 0.3-0.8: ??? (unrecorded changes) */
/* version 1.0: added face name, color, and index at the face chunk -kosmas 10/5/99 */
/* version 2.0: k-epsilon support. old CDI files no longer supported */
/* version 2.1: new input variables in equations: nx, ny, and nz */
/* version 2.2  New mappings to support non-air fluids and constant-temperature
 *              simulations. */
/* version 2.3  Support for vtables -peter 2/27/02 */
/* version 2.4  Support for coordinate systems -valle 3/8/02 */
/* version 2.5  Reverted to Release 3.3 physics */
/* version 2.6  Updated heat transfer model for Release 3.4p1 -peter 7/17/02 */
/* version 2.7  Support for fluid probes, sym planes, & case.num_res_levels -valle 3/3/03 */
/* version 2.8  3.4p3 features: gravity, buoyancy, non-inertial frames, BC response time,
 *              surface roughness, wall model char length */

/* version 3.0  Overhaul geometry format + add material properties chunks -peter 8/05 */
/* version 3.1  Addition of flags to GTBL chunk & num_frames to MPRM chunk */
/* version 3.2  Addition of experiment_area to HXCH chunk */
/* version 3.3  Addition of inlet_facet_offset and outlet_facet_offset to HXCH chunk */
/* version 3.4  Addition of surface coupling chunks (SCPL, SCCT, CMDL, CPLW, SCBC)
                and absolute filename to GTBL chunk */
/* version 3.5  Add abs path backup to CMDL chunk's filename */
/* version 3.6  Addition of custom measurement options -mterry 2007-02-06 */
/* version 3.7  Extend face offset with region index & both front and back offset distances,
 *              add generalized precedence values to scale & physics descriptors -peter 3/12/07 */
/* version 3.8  New SRI measurement support */
/* version 3.9  Four bitmasks per measurement option, not two. */
/* version 3.10 New LatticeTime definition, in terms of LatticeTimeInc */
/* version 3.11 Global NIRF ref point no longer erroneously offset by default csys origin if
 *              defined relative to csys other than default csys */
/* version 3.12 Add coolant physical properties to hxch chunk */
/* version 3.13 Add partition and segment chunks */
/* version 3.14 Add boolean is_closed to pnts chunk for vrevs */
/* version 3.15 Changed HXCH chunk to support multi-pass heat exchangers */
/* version 3.16 Std dev meas variables in meas window custom options instead of sqrd variables, variable structs support added */
/* version 3.17 Output vector quantities in global/local reference frame for
   both standard and composite measurement windows */
/* version 3.18 Augment offset distance record with front/back/both facet selection */
/* version 3.19 particle tracking changes */
/* version 3.20 Non-rectangular heat-exchanger additions to heat-exchanger chunks */
/* version 3.21 particle tracking variables to measurements */
/* version 3.22 added period_sync_group_index to mprm chunks */
/* version 3.23 added frac_trajectory_recording to pemp chunks (pemp chunks have since been removed - v4.6) */
/* version 3.24 added support for SRI_VARIABLES beyond 127 (e.g. swirl) */

/* NOTE -- Versions 3.24 & 3.25 *were* used in intermediate versions of the MV branch. Knowledge */
/* of those intermediate versions has been wiped from CDI to free up those versions for other */
/* purposes (5.0b extensions which need to precede the model view changes). All MV-specific */
/* are now triggered by CDI version 4.0. Any CDI files versioned 3.24 and 3.25 from those intermediate */
/* builds of PowerCASE can no longer be read, but we believe there are few if any in existence. */

/* version 4.0 incremented major version because non trivial MV changes warranted doing so */
/* version 4.1  added support for SRI_VARIABLES beyond 127 (e.g. swirl) */
/* version 4.2  added 'enable reflection' to SRMI structure/chunk */
/* version 4.2+ Added support for Momentum Freeze Option, two new members to ghdr chunk,
 *              realistic looks -- new rlks & look chunks + new info at the
                end of mdlv, psmv, & face chunks without bumping CDI version */
/* version 4.3  Changed CMDL chunk to support multiple coupling phases with variable periods */
/* version 4.3+ See notes for 4.6. */
/* version 4.4  support for new BODF chunk spec */
/* version 4.5  support for multi-phase physics changes */
/* version 4.6  Bogus "non-version" that was supposed to be the new Emitter Configs. and Emitters,
                but all that stuff actually was written in 4.3. */
/* version 4.7  Particle Modeling: New "Particles per Parcel" parameter in Emitter objects.*/
/* version 4.8  Add new entries to the lrfs chunk to support rotational dynamics */
/* version 4.9  support for new MOVB chunk for rotating tires (and future wipers) */
/* version 4.10 Add new liquid material type to the first block of GHDR chunk */
/* version 4.11 Add Filter Geometry information to sCDI_MNTR (Autostop Flow Monitors) and Total Simulation time options to GHDR */
/* version 4.12 Add start_via and end_via info to sCDI_MPRM (Measurement Parameters)*/
/* version 4.13 Added Classic Autostop Support */
/* version 4.14 Tweaked contents of the VHCL chunk */
/* version 4.15 Add new use_wheelbase chunk for MNTR chunk, add new initial_delay chunk for
 *              the WIPR chunk */
/* version 4.16 Always write/read a Surface Emitter's sCDI_EMITTER_NOZZLE_ORIENTATION to/from CDI,
                i.e. stop making it conditional on user_specified_nozzle_orientation == UserSpecified.
                This is because we still need some of the information in the nozzle orientation when the
                nozzle is elliptical, even if nozzle_orientation is by Surface normals.
                And when we are reading, there's no way to know a priori whether the nozzle is elliptical
                until we actually read the sCDI_EMITTER_NOZZLE_ORIENTATION.
                NOTE: For clients, there is no change in data structures or interface, so we can bump the
                CDI version in PowerCASE immediately; clients only need to add (post-read) logic to handle
                this possible combination of nozzle type and emitter. */
/* version 4.17 Add classic autostop algorithm flag */
/* version 4.18 Add classic_autostop_analysis_scheme and running_average_gradient_limit_via to sCDI_MNTR */
/* version 4.19 Change tire particle emitter emission rate to be per-tire-width.  Add new fields to 
                particle modeling NOZZLE_EMITTER_CONFIGURATION and sCDI_TIRE_NOZZLE_PROPS:
                Inner Half Angle Limit, Outer..., Major Outer..., Minor Outer...*/
/* version 4.20 Added running_average_gradient_interval_via & signal_processing_period_via to 
                sCDI_MNTR for use with classic autostop */
/* version 4.21 Added nozzle visibility settings (cone, arrow look and size) to rain emitter */
/* version 4.22 Added preferred coordinate system for points in sCDI_POINT_EMITTER */
/* version 5.0  Added new char properties for real & sim mach num. Added region & face filtering partition
                to monitor struct/chunk (sCDI_MNTR/mntr) */
/* version 5.1  Added emitter emission points visualization settings to Surface, Volume, Tire and Rain emitters.*/
/* version 5.2  Added PowerCASE coupled model user-specified name to sCDI_CMDL.*/
/* version 5.3  Moved 'frac_trajectory_recording' parameter from sCDI_PARTICLE_EMITTER_BASE to sCDI_PRTJ.
                This parameter will be assigned a default value of 0.001 (per PowerCASE) when older CDI files,
                which do not have the paremeter in the sCDI_PRTJ struct, are read.
                NOTE: To ensure forward compatibility for 5.4a with 5.4b, we add the new parameter at the end
                of the sCDI_PRTJ so that older executables simply skip reading it from file, and we write a dummy
                value to the 'pemt' chunk where sCDI_PARTICLE_EMITTER_BASE used to write its member data.*/
/* version 5.4  Supprt for Transient PowerTHERM Simulations and Multiple Coupling Phases:
 *              Added related cdi information for sCDI_CMDL and sCDI_COUPLING_PHASE_DESC chunks.*/
/* version 5.5  Always read / write visibility properties for the emitters per PR 40717. */
/* version 5.6  Added unrounded ("exact") start, period, & interval to each coupling phase in a CMDL */
/* version 5.7  Added part_name to HX chunks to help the simulator figure out which part is relevant for segment-based HXs */
/* version 6.0  Added geometry encryption version field in PTGE and new RGPN chunk*/
/* version 6.1  Added the global option for average mme meas window and local velocity freeze.*/
/* version 6.2  Migrated PowerTHERM/PowerFLOW Time Ratio from sCDI_CMDL to sCDI_COUPLING_PHASE_DESC.*/
/* version 6.3  Added the monitor control for emitters.*/
/* version 7.0  Add generalized encryption for CVDP chunk.*/
/* version 7.1  Adding support for seed_mks option for sampled face measurement.*/
//
//=== 2019_R2 Release ===
//
// With increased use of development branches, we need to coordinate CDI version increments.
// If you need to increment the CDI version in your branch, discuss with other branches and
// determine in what ORDER the various branches will be introduced to the mainline.
// Then decide on a new version for your branch, leaving some EMPTY VERSION SLOTS, in case another
// branch is later found to be needed on the mainline before yours.
// For example, if the current mainline version is 7.0, and no other branches are anticipated to be
// added before yours, then choose a next version like 7.5 or 7.10, depending on how long development
// will continue on your development branch.  If 7.10 is already taken by another branch, and you must
// release your branch to the mainline before that branch, then take a version like 7.5.
// When you take a new version on your branch, it will help others if you also edit the CDI component
// on the mainline by adding a comment for the new version with (PLACEHOLDER) beside it, but *not*
// actually changing CDI_MAJOR_VERSION, CDI_MINOR_VERSION in this file until you release your branch
// to the mainline.

/* version 7.10  (PLACEHOLDER) For first use of new CLBR (Upstream Turbulent Inflow Calibration) chunk. */
/* version 7.11  Adding support for moving reference frame in the rain emitter configuration.*/
/* version 7.12  Added geometry encryption for HXCH chunk*/
/* version 7.13  Adding support for region translation in transient boundary seeding.*/
/* version 7.20  Add deformed tire geometry. */
/* version 7.21  Added CDI file id to bsrg chunk */
/* version 7.22  Add start time for wipers */
/* version 7.23  Added adaptive PT coupling parameters in scpl chunk */
/* version 7.24  Added an sample "accr" chunk for ice accretion */
/* version 7.25  Change usage of CDI_LEGACY_ENERGY_SOLVER (formerly CDI_ENABLE_LB_ENTROPY_SOLVER) identifier by simulator.  Starting with F45, it is sometimes (High Subsonic, I believe) ignored.*/
/* version 7.26  PR_42469 - To support the new flag 'measured_fraction' in CDI_SCRN */
/*               PR 44870 - Use PT absolute start time when weather file is being used for a coupled case. */
/* version 7.27  Added temperature-dependent specific heat ratio option to 'ghdr' chunk */
/* version 7.28  Added cooling air leakage option to 'ghdr' chunk */
/* version 7.29  Added scale field to GAPD chunk */
/* version 7.30  Add "phase averaged" to AMW_ struct */
/* version 7.31  Support for PR_48032*/
/* version 7.32  Support for k-e Super Cycle added for 2021r4 */
/* version 7.33  Support for Hydrogen EoS */

/* version 8.1   Support for Mesh Curvature VR */
/* version 8.2   Revised purpose of a "PARM" chunk in the "SRFM" chunk to serve PR-48949, Disable Film Solver (no code changes other than version change)*/

/* version 8.0   incremented major version because of support for nested partitions/segments */
/* version 8.1   Added partial parts model view properties to mvdf chunk */
/* version 8.2   Obsolete: mesr, phrg, and gscl using old segment references */
/* version 8.3   Obsolete: emitters, screens, and wipers using old segment references */
/* version 8.4   Obsolete: SRPH using old segment references */
/* version 8.5   Add new geometry references and selection tree (mesr, phrg, gscl, srph, srpt, emitters, screens, wipers */
/* version 8.6   Add a reference partition to the geometry references */
/* version 8.7   Add geometry selection tree to monitors */
/* version 8.8   Write both geometry references and region list in gscl and phrg chunks */
/* version 8.9   Add Screen Invalid, Min/Max Cp options to Averaged Measurement */
/* version 8.10  Add bool flag to Averaged Measurement chunk (not the struct itself) to indicate whether
                 Min/Max Cp options are present (a fix for problems with v8.9) */
/* version 8.11  Add scale factor to ice accretion struct and support read and write for double data type*/
/* version 8.12  Support for Curvature VR feature*/
/* version 8.13  Monitor Enhancement: Allow Accuracy as Combined Max Value OR Max Percentage */
/* version 8.14  Support for User-Defined Scalar Transport */

/* version 9.0   New native heat conduction solver which includes */
/*               Add thermal contact by face, solid shell configurations, and solid/shell measurements */
/*               Add number of shell layers to the FACE chunk */
/* version 9.1   Added decoupling time parameter to GLOB and MPRM */
/* version 9.2   Added surface property index to thermal contacts CDI_STCR */
/* version 9.3   Added max separation and max angle parameters for thermal contact (CDI_STCP) */
/* version 9.4   Merged thermal solver & partition changes */
/* version 9.5   Split SRPT surfaces into flow and thermal, i.e. new THPT */
/* version 9.6   Fix thermal conductivity units */
/* version 9.7   Add close gaps option to thermal contact (STCP) */
/*               Radiation: global parameters, surface conditions (RDSC, RDSL), */
/*                          compute radiation to solid physics */
/* version 9.8   Add material anisotropy enumerations and cylindrical axes, min/max design temperatures */
/* version 9.9   Synchronization with mainline */
/* version 9.10  Synchronization with mainline */
/* version 9.11  Radiation updates: add measurement vars, add patch size to axisymmetric */
/*               Added user defined dependent variables with columns (CDI_UDDC), sets with one X column */
/*                 and n Y columns (CDI_UDDS), and lists of those sets (CDI_UDDL) */
/* version 9.12  Add solid contact precedence list (CDI_SCPR, CDI_SCPL) to contact parameters (CDI_STCP) */
/* version 9.14  Use geometry references for contact (CDI_STCR) */
/* version 9.15  Synchronization with mainline */
/* version 9.16 Synchronization with mainline */
/* version 9.17  Add solid material assignments using different partition (CDI_SCMA) */
/* version 9.18  Allow heat input for conducting solids to be in “power” units */
/* version 9.19  Add surface physics for complementary faces (CDI_FACE) */
/* version 9.20  Add parameters for air/vacuum shell layers, also add radiation_cond and num_radiation_faces to cprp */
/* Current version of newly written CDI files */
#define CDI_MAJOR_VERSION 9
#define CDI_MINOR_VERSION 20

/* These are macros to assist with version checking */
#define CDI_VERSION_NEWER_THAN(major, minor, target_major, target_minor) \
  (((major) > (target_major)) || \
   (((major) == (target_major)) && \
    ((minor) > (target_minor))))

#define CDI_VERSION_NEWER(major, minor) \
  CDI_VERSION_NEWER_THAN((major), (minor), CDI_MAJOR_VERSION, CDI_MINOR_VERSION)

#define CDI_VERSION_AT_LEAST(major, minor, target_major, target_minor) \
  (((major) > (target_major)) || \
   (((major) == (target_major)) && \
    ((minor) >= (target_minor))))

#define CDI_INFO_VERSION_AT_LEAST(info, target_major, target_minor) \
  CDI_VERSION_AT_LEAST((info)->major_version, (info)->minor_version,\
                       (target_major), (target_minor))

/* predefined characteristic property names */
#define CDI_CASE_CHAR_LENGTH_NAME "$CASE_CHAR_LENGTH"
#define CDI_CASE_CHAR_SPEED_NAME "$CASE_CHAR_SPEED"
#define CDI_CASE_CHAR_DENSITY_NAME "$CASE_CHAR_DENSITY"
#define CDI_CASE_CHAR_PRESSURE_NAME "$CASE_CHAR_PRESSURE"
#define CDI_CASE_CHAR_TEMPERATURE_NAME "$CASE_CHAR_TEMPERATURE"
#define CDI_CASE_MEAN_TEMPERATURE_NAME "$CASE_MEAN_TEMPERATURE"
#define CDI_CASE_CHAR_AREA_NAME "$CASE_CHAR_AREA"
#define CDI_CASE_MAX_EXP_VEL_NAME "$CASE_MAX_EXP_VEL"
#define CDI_CASE_REYNOLDS_NUMBER_NAME "$CASE_REYNOLDS_NUMBER"
#define CDI_CASE_FLUID_PRANDTL_NUMBER_NAME "$CASE_FLUID_PRANDTL_NUMBER"
#define CDI_CASE_TURB_PRANDTL_NUMBER_NAME "$CASE_TURB_PRANDTL_NUMBER"
#define CDI_CASE_GAS_CONSTANT_NAME "$CASE_GAS_CONSTANT"
#define CDI_CASE_SPECIFIC_HEAT_RATIO_NAME "$CASE_SPECIFIC_HEAT_RATIO"
#define CDI_CASE_SPECIFIC_HEAT_NAME "$CASE_SPECIFIC_HEAT"
#define CDI_CASE_LATTICE_SPECIFIC_HEAT_RATIO_NAME "$CASE_LATTICE_SPECIFIC_HEAT_RATIO"
#define CDI_CASE_CHAR_CHANNEL_WIDTH_NAME "$CASE_CHAR_CHANNEL_WIDTH"
#define CDI_CASE_AIR_KINEMATIC_VISCOSITY "$CASE_AIR_KINEMATIC_VISCOSITY"
#define CDI_CASE_WATER_KINEMATIC_VISCOSITY "$CASE_WATER_KINEMATIC_VISCOSITY"
#define CDI_CASE_LATTICE_GAS_CONSTANT_NAME "$CASE_LATTICE_GAS_CONSTANT"
#define CDI_CASE_REAL_MACH_NUM "$CASE_REAL_MACH_NUM"
#define CDI_CASE_SIMULATED_MACH_NUM "$CASE_SIMULATED_MACH_NUM"
#define CDI_CASE_SPECIFIC_HEAT_SOLID_NAME "$CASE_SPECIFIC_HEAT_SOLID"//MMD_Eutectic
#define CDI_CASE_MELTING_TEMP_NAME "$CASE_MELTING_TEMP"
#define CDI_CASE_BOILING_TEMP_NAME "$CASE_BOILING_TEMP"
#define CDI_CASE_LATENT_HEAT_OF_FUSION_NAME "$CASE_LATENT_HEAT_OF_FUSION"
#define CDI_CASE_THERMAL_CONDUCTIVITY_SOLID_NAME "$CASE_THERMAL_CONDUCTIVITY_SOLID"
#define CDI_CASE_THERMAL_CONDUCTIVITY_LIQUID_NAME "$CASE_THERMAL_CONDUCTIVITY_LIQUID"
#define CDI_CASE_DENSITY_SOLID_NAME "$CASE_DENSITY_SOLID"
//kEps_SuperCycle
#define CDI_CASE_ACTIVATE_SUPER_CYCLE_TURBULENCE_SOLVER "$CASE_SUPER_CYCLE_TURBULENCE_SOLVER"
#define CDI_CASE_SUPER_CYCLING_FACTOR "$CASE_SUPER_CYCLING_FACTOR"
//Hydrogen EoS
#define CDI_CASE_H2EOS_BETA_NAME "$CASE_H2EOS_BETA"
//Radiation parameters
#define CDI_CASE_RADIATION_UPDATE_RATIO "$CASE_RADIATION_UPDATE_RATIO"
#define CDI_CASE_RADIATION_BACKGROUND_TEMP "$CASE_RADIATION_BACKGROUND_TEMP"
#define CDI_CASE_RADIATION_VIEWFACTOR_RAYS "$CASE_RADIATION_VIEWFACTOR_RAYS"
#define CDI_CASE_NUM_RADIATION_FACES "$CASE_NUM_RADIATION_FACES"

// measurement options
#define CDI_MEAS_OPT_STANDARD_SURFACE   (1 << 0)
#define CDI_MEAS_OPT_STANDARD_FLUID     (1 << 1)
#define CDI_MEAS_OPT_DOUBLE_PRECISION   (1 << 3)   // Record double precision variables
#define CDI_MEAS_OPT_HEAT_FLUX          (1 << 2)
#define CDI_MEAS_OPT_MEAS_PER_VOXEL     (1 << 4)
#define CDI_MEAS_OPT_TURB               (1 << 5)
#define CDI_MEAS_OPT_STANDARD_POROUS    (1 << 6)   // For body force model
#define CDI_MEAS_OPT_HTC_CHAR_TEMP      (1 << 7)   // Heat transfer coefficients, char temp
#define CDI_MEAS_OPT_COMPRESS           (1 << 8)   // Enable compression of measurement file, override obsolete bit
#define CDI_MEAS_OPT_DIV_U              (1 << 9)   // Divergence of velocity
#define CDI_MEAS_OPT_MEAS_SURFEL_PER_VOXEL (1 << 10)  // Equivalent of MEAS_PER_VOXEL for surface measurements
#define CDI_MEAS_OPT_AVERAGE_MME        (1 << 11)  // Average MME window, override obsolete bit
#define CDI_MEAS_OPT_NO_SURFEL_MERGER   (1 << 12)  // Don't merge physics surfels (e.g. realistic wind calibration)
#define CDI_MEAS_OPT_RWNC_MEAS          (1 << 13)  // Meas file will be used in the realistic wind noise cancellation run)
#define CDI_MEAS_OPT_STANDARD_SHELL     (1 << 14)
#define CDI_MEAS_OPT_ROUND_PERIOD_DOWN  (1 << 15)  // Round period down, rather than to closest
#define CDI_MEAS_OPT_COMPOSITE_GEOMETRY (1 << 16)  // Composite meas cell geometry
#define CDI_MEAS_OPT_FILE_PER_FRAME     (1 << 17)  // Header + file per frame output format
#define CDI_MEAS_OPT_DONT_SHIFT_START_TIME (1 << 18)  // Write 1st frame at start time (not ST + AI/2)
#define CDI_MEAS_OPT_EXCLUDE_COARSE_LEVELS (1 << 19) // Exclude grid levels that are too coarse for the specified period
#define CDI_MEAS_OPT_CUSTOM_FLUID       (1 << 20) // Use custom fluid meas options (below)
#define CDI_MEAS_OPT_CUSTOM_SURFACE     (1 << 21) // Use custom surface meas options (below)
#define CDI_MEAS_OPT_CUSTOM_POROUS      (1 << 22) // Use custom porous meas options (below)
#define CDI_MEAS_OPT_HTC_NEAR_WALL_TEMP (1 << 23) // Heat transfer coefficients, near wall temp
#define CDI_MEAS_OPT_LOCAL_CSYS         (1 << 24) // Quantities in local coordinate system
#define CDI_MEAS_OPT_STANDARD_FLUID_PARTICLE (1 << 25) // Use standard fluid particle meas options
#define CDI_MEAS_OPT_STANDARD_SURFACE_PARTICLE (1 << 26) // Use standard surface particle meas options
#define CDI_MEAS_OPT_CUSTOM_FLUID_PARTICLE (1 << 27) // Use custom fluid particle meas options
#define CDI_MEAS_OPT_CUSTOM_SURFACE_PARTICLE (1 << 28) // Use custom surface particle meas options
#define CDI_MEAS_OPT_PARTICLE_VARS_PER_MATERIAL (1 << 29) // Separate Particle Variables per particle material
#define CDI_MEAS_OPT_PARTICLE_VARS_PER_EMITTER_PART (1 << 30) // Separate Particle Variables per emitter part
#define CDI_MEAS_OPT_CALC_HTC_FOR_ADB_WALLS  (1 << 31)

// obsolete measurement options (used by CDI to interpret old files)
#define CDI_MEAS_OPT_COMPOSITE_FORCE_OBSOLETE   (1 << 8)   // Replaced by meas options
#define CDI_MEAS_OPT_FLUID_PRESSURE_OBSOLETE    (1 << 10)  // Replaced by meas options
#define CDI_MEAS_OPT_FLUID_VEL_MAG_OBSOLETE     (1 << 11)  // Replaced by meas options
#define CDI_MEAS_OPT_SURFACE_PRESSURE_OBSOLETE  (1 << 12)  // Replaced by meas options
#define CDI_MEAS_OPT_SURFACE_VEL_MAG_OBSOLETE   (1 << 13)  // Replaced by meas options
#define CDI_MEAS_OPT_COMPOSITE_MOMENTS_OBSOLETE (1 << 14)  // Replaced by meas options

/* physics types */
#define CDI_PHYS_TYPE_NONE                                  (-1)
#define CDI_PHYS_TYPE_FV_FLUID                                1 /* fixed viscosity fluid; DEPRECATED */
#define CDI_PHYS_TYPE_NON_SLIP                                2
#define CDI_PHYS_TYPE_SLIP                                    3
#define CDI_PHYS_TYPE_VSSP_SLIP                               4
#define CDI_PHYS_TYPE_SOURCE                                  5
#define CDI_PHYS_TYPE_VV_FLUID                                6 /* variable viscosity fluid */
#define CDI_PHYS_TYPE_SOLID                                   7
#define CDI_PHYS_TYPE_SLIP95                                  8
#define CDI_PHYS_TYPE_TRUE_NOSLIP                             9
#define CDI_PHYS_TYPE_STATIC_PRESSURE_FIXED_DIR              10
#define CDI_PHYS_TYPE_STATIC_PRESSURE_FREE_DIR               11
#define CDI_PHYS_TYPE_STAG_PRESSURE_FIXED_DIR                12
#define CDI_PHYS_TYPE_STAG_PRESSURE_FREE_DIR                 13
#define CDI_PHYS_TYPE_LINEAR_SLIP                            14
#define CDI_PHYS_TYPE_LINEAR_NOSLIP                          15
#define CDI_PHYS_TYPE_ANGULAR_SLIP                           16
#define CDI_PHYS_TYPE_ANGULAR_NOSLIP                         17
#define CDI_PHYS_TYPE_MASS_FLUX                              18
#define CDI_PHYS_TYPE_LES_VV_FLUID                           19
#define CDI_PHYS_TYPE_FIXED_VEL                              20
#define CDI_PHYS_TYPE_NEW_VV_FLUID                           21 /* VVFLUID in pvt form */
#define CDI_PHYS_TYPE_NEW_SOURCE                             22 /* SOURCE in pvt form */
#define CDI_PHYS_TYPE_NEW_LES_VV_FLUID                       23 /* LES_VV_FLUID in pvt form */
#define CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR          24 /* New units for pressure */
#define CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR           25
#define CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR            26
#define CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR             27
#define CDI_PHYS_TYPE_NEW_MASS_FLUX                          28
#define CDI_PHYS_TYPE_SOURCE_SURFEL                          29
#define CDI_PHYS_TYPE_PASS_THRU                              30
#define CDI_PHYS_TYPE_POROUS_VV_FLUID                        31

/* physics types for thermal stuff */
#if 0 /* These types are not currently in use */
#define CDI_PHYS_TYPE_NEW_VV_FLUID_THERMAL                   32
#define CDI_PHYS_TYPE_NEW_LES_VV_FLUID_THERMAL               33
#endif
#define CDI_PHYS_TYPE_POROUS_VV_FLUID_THERMAL                34

#define CDI_PHYS_TYPE_SLIP95_FIXED_TEMP                      35
#define CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_TEMP                 36
#define CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_TEMP                 37
#define CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_TEMP               38
#define CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_TEMP                39
#define CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_TEMP              40

#define CDI_PHYS_TYPE_SLIP95_FIXED_HEAT_FLUX                 41
#define CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_HEAT_FLUX            42
#define CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_HEAT_FLUX            43
#define CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_HEAT_FLUX          44
#define CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_HEAT_FLUX           45
#define CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_HEAT_FLUX         46

/* TFLOAT types no longer used. T is instead written as -1 */
#define CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR_TFLOAT   47
#define CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR_TFLOAT    48
#define CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR_TFLOAT     49
#define CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR_TFLOAT      50
#define CDI_PHYS_TYPE_NEW_MASS_FLUX_TFLOAT                   51
#define CDI_PHYS_TYPE_SOURCE_SURFEL_TFLOAT                   52
#define CDI_PHYS_TYPE_FIXED_VEL_TFLOAT                       53

/* physics types for fan model */
#define CDI_PHYS_TYPE_INLINE_FAN                             54

#define CDI_PHYS_TYPE_POROUS_VV_FLUID_FIXED_TEMP             55

#define CDI_PHYS_TYPE_LES_SOURCE                             56
#define CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR          57
#define CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR           58
#define CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR            59
#define CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR             60
#define CDI_PHYS_TYPE_LES_MASS_FLUX                          61
#define CDI_PHYS_TYPE_LES_FIXED_VEL                          62
#define CDI_PHYS_TYPE_LES_SOURCE_SURFEL                      63

/* TFLOAT types no longer used. T is instead written as -1 */
#define CDI_PHYS_TYPE_LES_SOURCE_TFLOAT                      64
#define CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR_TFLOAT   65
#define CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR_TFLOAT    66
#define CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR_TFLOAT     67
#define CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR_TFLOAT      68
#define CDI_PHYS_TYPE_LES_MASS_FLUX_TFLOAT                   69
#define CDI_PHYS_TYPE_LES_FIXED_VEL_TFLOAT                   70
#define CDI_PHYS_TYPE_LES_SOURCE_SURFEL_TFLOAT               71

#define CDI_PHYS_TYPE_POROUS_LES_VV_FLUID                    72
#define CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_FIXED_TEMP         73
#define CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_THERMAL            74
#define CDI_PHYS_TYPE_LES_INLINE_FAN                         75

#define CDI_PHYS_TYPE_NON_INERTIAL_FRAME                     76
#define CDI_PHYS_TYPE_GRAVITY_BUOYANCY                       77
#define CDI_PHYS_TYPE_LOCAL_REF_FRAME                        78

#define CDI_PHYS_TYPE_INLINE_TABLE_FAN                       79
#define CDI_PHYS_TYPE_LES_INLINE_TABLE_FAN                   80

#define CDI_PHYS_TYPE_HEAT_EXCHANGER                         81
#define CDI_PHYS_TYPE_LES_HEAT_EXCHANGER                     82

#define CDI_PHYS_TYPE_SLIP95_THERMAL_RESIST                  83
#define CDI_PHYS_TYPE_TRUE_NOSLIP_THERMAL_RESIST             84
#define CDI_PHYS_TYPE_LINEAR_SLIP_THERMAL_RESIST             85
#define CDI_PHYS_TYPE_LINEAR_NOSLIP_THERMAL_RESIST           86
#define CDI_PHYS_TYPE_ANGULAR_SLIP_THERMAL_RESIST            87
#define CDI_PHYS_TYPE_ANGULAR_NOSLIP_THERMAL_RESIST          88

/* For Acoustic Porous Medium */
#define CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID                  89
#ifdef APM_WITH_HEAT_TRANSFER
#define CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID_FIXED_TEMP       90
#define CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID_THERMAL          91
#endif

#define CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID              92
#ifdef APM_WITH_HEAT_TRANSFER
#define CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID_FIXED_TEMP   93
#define CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID_THERMAL      94
#endif

// A dummy type for discretizer identification
// of a fluid interface "region"
#define CDI_PHYS_TYPE_APM_FLUID_INTERFACE                    95

// For curved heat exchanger porous medium

#define CDI_PHYS_TYPE_CURVED_HX_POROUS_VV_FLUID_THERMAL      96
#define CDI_PHYS_TYPE_CURVED_HX_POROUS_LES_VV_FLUID_THERMAL  97

#define CDI_PHYS_TYPE_VEL_SLIP                               98
/* For Acoustic Porous Medium boundary conditions */
#define CDI_PHYS_TYPE_APM_SLIP95                             99
#define CDI_PHYS_TYPE_APM_TRUE_NOSLIP                       100
#define CDI_PHYS_TYPE_APM_LINEAR_SLIP                       101
#define CDI_PHYS_TYPE_APM_LINEAR_NOSLIP                     102
#define CDI_PHYS_TYPE_APM_ANGULAR_SLIP                      103
#define CDI_PHYS_TYPE_APM_ANGULAR_NOSLIP                    104

#define CDI_PHYS_TYPE_BODY_FORCE                            105

// For rotating tire
#define CDI_PHYS_TYPE_ROTATING_TIRE                         106

// Mass flow BC
#define CDI_PHYS_TYPE_MASS_FLOW                             107
#define CDI_PHYS_TYPE_LES_MASS_FLOW                         108
#define CDI_PHYS_TYPE_MASS_FLOW_TFLOAT                      109
#define CDI_PHYS_TYPE_LES_MASS_FLOW_TFLOAT                  110

// A dummy type for discretizer identification
// of a heat-exchanger interface "region"
#define CDI_PHYS_TYPE_PM_INTERFACE                          111

// Solid Conduction
#define CDI_PHYS_TYPE_SOLID_CONDUCTOR                       112
#define CDI_PHYS_TYPE_SOLID_INSULATOR                       113
#define CDI_PHYS_TYPE_ADIABATIC                             115
#define CDI_PHYS_TYPE_FIXED_TEMP                            116
#define CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP                117
#define CDI_PHYS_TYPE_FIXED_HEAT_FLUX                       118
#define CDI_PHYS_TYPE_CONTACT_RESISTANCE                    119

// A dummy type for discretizer identification
// of a conductor solid interface "region"
#define CDI_PHYS_TYPE_CONDUCTOR_INTERFACE                   120
#define CDI_PHYS_TYPE_SLIP95_COUPLED                        121
#define CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED                   122
#define CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED                   123
#define CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED                 124
#define CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED                  125
#define CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED                126
#define CDI_PHYS_TYPE_COUPLED_THERMAL                       127

// CONDUCTING SHELL LAYER
#define CDI_PHYS_TYPE_CONDUCTION_LAYER                      128

// Thermal resistance boundary
#define CDI_PHYS_TYPE_THERMAL_RESIST                        129

// Hollow Solid
#define CDI_PHYS_TYPE_SOLID_HOLLOW                          130

// Prescribed Heat Flow
#define CDI_PHYS_TYPE_FIXED_HEAT_FLOW                       131

/* 5G physics types - All new non-5G physics types should be defined above
   (using IDs < 200) */
#define CDI_PHYS_TYPE_5G_FLUID                              200
#define CDI_PHYS_TYPE_5G_PRESS_VEL_BC                       201
#define CDI_PHYS_TYPE_5G_MASS_FLUX_BC                       202
#define CDI_PHYS_TYPE_5G_PRESS_BC                           203
#define CDI_PHYS_TYPE_5G_DNS_WALL                           204
#define CDI_PHYS_TYPE_5G_DNS_FRICTIONLESS_WALL              205
#define CDI_PHYS_TYPE_5G_DNS_LINEAR_WALL                    206
#define CDI_PHYS_TYPE_5G_DNS_ANGULAR_WALL                   207
#define CDI_PHYS_TYPE_5G_PRESS_FIXED_DIR_BC                 208
// FPSS surface
#define CDI_PHYS_TYPE_5G_FLUID_PHASE_SOURCE_SINK            209

/* UDS physics types, starting from 300 */
#define CDI_UDS_PHYS_TYPE_NONE                              (-1)
#define CDI_UDS_PHYS_FLUID                                  300
#define CDI_UDS_PHYS_SPECIAL_FLUID                          301
#define CDI_UDS_PHYS_WALL                                   302
#define CDI_UDS_PHYS_INLET_OUTLET                           303

/* Derived predicates */

#define CDI_PHYS_TYPE_IS_FAN(cdi_phys_type) \
  ((cdi_phys_type == CDI_PHYS_TYPE_LES_INLINE_FAN) \
   || (cdi_phys_type == CDI_PHYS_TYPE_INLINE_FAN) \
   || (cdi_phys_type == CDI_PHYS_TYPE_LES_INLINE_TABLE_FAN) \
   || (cdi_phys_type == CDI_PHYS_TYPE_INLINE_TABLE_FAN))

// The next 12 symbols are intended for use in switch statements like this:
//
//   switch (classify_cdi_fluid_physics_type(cdi_phys_type)) {
//     case POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
//       ...
//       break;
//     case FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
//       ...
//       break;
//     case TABLE_FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
//       ...
//       break;
//     default: // These are all the basic fluid types
//       ...
//       break;
//   }

#define FAN_CDI_FLUID_PHYSICS_TYPE_CASE                 \
       CDI_PHYS_TYPE_INLINE_FAN:                        \
  case CDI_PHYS_TYPE_LES_INLINE_FAN

#define TABLE_FAN_CDI_FLUID_PHYSICS_TYPE_CASE           \
       CDI_PHYS_TYPE_INLINE_TABLE_FAN:                  \
  case CDI_PHYS_TYPE_LES_INLINE_TABLE_FAN

#define POROUS_CDI_FLUID_PHYSICS_TYPE_CASE                 \
       CDI_PHYS_TYPE_POROUS_VV_FLUID:                      \
  case CDI_PHYS_TYPE_POROUS_VV_FLUID_THERMAL:              \
  case CDI_PHYS_TYPE_POROUS_VV_FLUID_FIXED_TEMP:           \
  case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID:                  \
  case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_THERMAL:          \
  case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_FIXED_TEMP:       \
  case CDI_PHYS_TYPE_CURVED_HX_POROUS_VV_FLUID_THERMAL:    \
  case CDI_PHYS_TYPE_CURVED_HX_POROUS_LES_VV_FLUID_THERMAL

#define VARIABLE_HEAT_RATE_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE  \
       CDI_PHYS_TYPE_POROUS_VV_FLUID_THERMAL:                  \
  case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_THERMAL

#define CURVED_HX_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE            \
       CDI_PHYS_TYPE_CURVED_HX_POROUS_VV_FLUID_THERMAL:         \
  case CDI_PHYS_TYPE_CURVED_HX_POROUS_LES_VV_FLUID_THERMAL

#define ADIABATIC_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE  \
       CDI_PHYS_TYPE_POROUS_VV_FLUID:                 \
  case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID

#define FIXED_TEMP_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE   \
       CDI_PHYS_TYPE_POROUS_VV_FLUID_FIXED_TEMP:        \
  case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_FIXED_TEMP

#define THERMAL_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE              \
       CDI_PHYS_TYPE_POROUS_VV_FLUID_THERMAL:                   \
  case CDI_PHYS_TYPE_POROUS_VV_FLUID_FIXED_TEMP:                \
  case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_THERMAL:               \
  case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_FIXED_TEMP:            \
  case CDI_PHYS_TYPE_CURVED_HX_POROUS_VV_FLUID_THERMAL:         \
  case CDI_PHYS_TYPE_CURVED_HX_POROUS_LES_VV_FLUID_THERMAL

#define SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE      \
       CDI_PHYS_TYPE_SLIP95:                    \
  case CDI_PHYS_TYPE_SLIP95_FIXED_TEMP:         \
  case CDI_PHYS_TYPE_SLIP95_FIXED_HEAT_FLUX:    \
  case CDI_PHYS_TYPE_SLIP95_THERMAL_RESIST:     \
  case CDI_PHYS_TYPE_SLIP95_COUPLED

#define NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE         \
       CDI_PHYS_TYPE_TRUE_NOSLIP:                    \
  case CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_TEMP:         \
  case CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_HEAT_FLUX:    \
  case CDI_PHYS_TYPE_TRUE_NOSLIP_THERMAL_RESIST:     \
  case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:            \
  case CDI_PHYS_TYPE_5G_DNS_WALL

#define ANGULAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE    \
       CDI_PHYS_TYPE_ANGULAR_SLIP:                    \
  case CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_TEMP:         \
  case CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_HEAT_FLUX:    \
  case CDI_PHYS_TYPE_ANGULAR_SLIP_THERMAL_RESIST:     \
  case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED

#define ANGULAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE    \
       CDI_PHYS_TYPE_ANGULAR_NOSLIP:                    \
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_TEMP:         \
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_HEAT_FLUX:    \
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_THERMAL_RESIST:     \
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED:            \
  case CDI_PHYS_TYPE_5G_DNS_ANGULAR_WALL

#define LINEAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE    \
       CDI_PHYS_TYPE_LINEAR_SLIP:                    \
  case CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_TEMP:         \
  case CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_HEAT_FLUX:    \
  case CDI_PHYS_TYPE_LINEAR_SLIP_THERMAL_RESIST:     \
  case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED

#define LINEAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE    \
       CDI_PHYS_TYPE_LINEAR_NOSLIP:                    \
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_TEMP:         \
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_HEAT_FLUX:    \
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_THERMAL_RESIST:     \
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:            \
  case CDI_PHYS_TYPE_5G_DNS_LINEAR_WALL

#define CONDUCTION_CDI_SURFACE_PHYSICS_TYPE_CASE          \
       CDI_PHYS_TYPE_ADIABATIC:                           \
  case CDI_PHYS_TYPE_FIXED_TEMP:      \
  case CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP:  \
  case CDI_PHYS_TYPE_FIXED_HEAT_FLUX:  \
  case CDI_PHYS_TYPE_FIXED_HEAT_FLOW:  \
  case CDI_PHYS_TYPE_COUPLED_THERMAL

inline cdiBOOLEAN cdi_phys_type_is_wall(cdiINT32 cdi_phys_type)
{
  switch(cdi_phys_type) {
  case SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case ANGULAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case ANGULAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case LINEAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case LINEAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case CONDUCTION_CDI_SURFACE_PHYSICS_TYPE_CASE:
    return TRUE;
  default:
    return FALSE;
  }
}

#ifdef APM_WITH_HEAT_TRANSFER
#define ACOUSTIC_POROUS_FLUID_PHYSICS_TYPE_CASE            \
       CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID:                \
  case CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID_FIXED_TEMP:     \
  case CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID_THERMAL:        \
  case CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID:            \
  case CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID_FIXED_TEMP: \
  case CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID_THERMAL
#else
#define ACOUSTIC_POROUS_FLUID_PHYSICS_TYPE_CASE            \
       CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID:                \
  case CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID
#endif

#define ADIABATIC_ACOUSTIC_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE  \
       CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID:                    \
  case CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID

#ifdef APM_WITH_HEAT_TRANSFER
#define FIXED_TEMP_ACOUSTIC_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE \
       CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID_FIXED_TEMP:         \
  case CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID_FIXED_TEMP

#define VARIABLE_HEAT_RATE_ACOUSTIC_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE  \
       CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID_THERMAL:                     \
  case CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID_THERMAL
#endif

#define APM_FLUID_INTERFACE_CDI_PHYSICS_TYPE_CASE \
        CDI_PHYS_TYPE_APM_SLIP95:                 \
  case  CDI_PHYS_TYPE_APM_TRUE_NOSLIP:            \
  case  CDI_PHYS_TYPE_APM_LINEAR_SLIP:            \
  case  CDI_PHYS_TYPE_APM_LINEAR_NOSLIP:          \
  case  CDI_PHYS_TYPE_APM_ANGULAR_SLIP:           \
  case  CDI_PHYS_TYPE_APM_ANGULAR_NOSLIP
#define CONDUCTION_SOLID_PHYSICS_TYPE_CASE            \
        CDI_PHYS_TYPE_SOLID_CONDUCTOR:    \
  case  CDI_PHYS_TYPE_SOLID_INSULATOR

// For usage in simulator to facilitate UDS data input
inline cdiBOOLEAN cdi_phys_type_is_wall_for_sim(cdiINT32 cdi_phys_type)
{
  //not include APM_FLUID_INTERFACE_CDI_PHYSICS_TYPE_CASE
  switch(cdi_phys_type) {
  case SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case ANGULAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case ANGULAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case LINEAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case LINEAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
  case CDI_PHYS_TYPE_VEL_SLIP:
    return TRUE;
  default:  
    return FALSE;
  }
}

#define STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE_CASE   \
       CDI_PHYS_TYPE_STATIC_PRESSURE_FIXED_DIR:      \
  case CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR:  \
  case CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR_TFLOAT: \
  case CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR:        \
  case CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR_TFLOAT:	\
  case CDI_PHYS_TYPE_5G_PRESS_FIXED_DIR_BC


#define STAG_PRESSURE_FREE_DIR_SURFEL_TYPE_CASE   \
       CDI_PHYS_TYPE_STAG_PRESSURE_FREE_DIR:          \
  case CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR:      \
  case CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR_TFLOAT:  \
  case CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR:         \
  case CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR_TFLOAT

#define STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE_CASE  \
       CDI_PHYS_TYPE_STAG_PRESSURE_FIXED_DIR:        \
  case CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR:        \
  case CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR_TFLOAT: \
  case CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR:        \
  case CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR_TFLOAT


#define STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE_CASE       \
       CDI_PHYS_TYPE_STATIC_PRESSURE_FREE_DIR:          \
  case CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR:      \
  case CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR_TFLOAT:\
  case CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR:       \
  case CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR_TFLOAT:\
  case CDI_PHYS_TYPE_5G_PRESS_BC


#define MASS_FLUX_SURFEL_TYPE_CASE                        \
       CDI_PHYS_TYPE_MASS_FLUX:                           \
  case CDI_PHYS_TYPE_NEW_MASS_FLUX:                       \
  case CDI_PHYS_TYPE_NEW_MASS_FLUX_TFLOAT:                \
  case CDI_PHYS_TYPE_LES_MASS_FLUX:                       \
  case CDI_PHYS_TYPE_LES_MASS_FLUX_TFLOAT:                \
  case CDI_PHYS_TYPE_5G_MASS_FLUX_BC


#define MASS_FLOW_SURFEL_TYPE_CASE                        \
       CDI_PHYS_TYPE_MASS_FLOW:                           \
  case CDI_PHYS_TYPE_LES_MASS_FLOW

#define VEL_SURFEL_TYPE_TYPE_CASE                         \
       CDI_PHYS_TYPE_FIXED_VEL:                           \
  case CDI_PHYS_TYPE_FIXED_VEL_TFLOAT:                    \
  case CDI_PHYS_TYPE_LES_FIXED_VEL:                       \
  case CDI_PHYS_TYPE_LES_FIXED_VEL_TFLOAT

#define SOURCE_SURFEL_TYPE_CASE                           \
       CDI_PHYS_TYPE_SOURCE_SURFEL:                       \
  case CDI_PHYS_TYPE_SOURCE_SURFEL_TFLOAT:                \
  case CDI_PHYS_TYPE_LES_SOURCE_SURFEL:                   \
  case CDI_PHYS_TYPE_LES_SOURCE_SURFEL_TFLOAT:            \
  case CDI_PHYS_TYPE_5G_PRESS_VEL_BC

#define PASS_THRU_SURFEL_TYPE_CASE                        \
       CDI_PHYS_TYPE_PASS_THRU


inline cdiBOOLEAN cdi_phys_type_is_inlet_or_outlet_for_sim(cdiINT32 cdi_phys_type)
{
  switch(cdi_phys_type) {
  case STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE_CASE:
  case STAG_PRESSURE_FREE_DIR_SURFEL_TYPE_CASE:
  case STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE_CASE:
  case STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE_CASE:
  case MASS_FLUX_SURFEL_TYPE_CASE:
  case MASS_FLOW_SURFEL_TYPE_CASE:
  case VEL_SURFEL_TYPE_TYPE_CASE:
  case SOURCE_SURFEL_TYPE_CASE:
  case PASS_THRU_SURFEL_TYPE_CASE:
    return TRUE;
  default:
    return FALSE;
  }
}


inline cdiBOOLEAN cdi_phys_type_is_special_fluid_for_sim(cdiINT32 cdi_phys_type)
{
  //not include ACOUSTIC_POROUS_FLUID_PHYSICS_TYPE_CASE
  switch(cdi_phys_type) {
  case FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
  case TABLE_FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
  case POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
    return TRUE;
  default:
    return FALSE;
  }
}

inline cdiBOOLEAN cdi_phys_type_is_basic_fluid_for_sim(cdiINT32 cdi_phys_type)
{
  //not include Acoustic Porous Media
  switch(cdi_phys_type) {
  case CDI_PHYS_TYPE_FV_FLUID:
  case CDI_PHYS_TYPE_VV_FLUID:
  case CDI_PHYS_TYPE_LES_VV_FLUID:
  case CDI_PHYS_TYPE_NEW_VV_FLUID:
  case CDI_PHYS_TYPE_NEW_LES_VV_FLUID:
  case CDI_PHYS_TYPE_5G_FLUID:
    return TRUE;
  default:
    return FALSE;
  }
}

inline CDI_UDS_PHYS_TYPE_DESCRIPTOR find_uds_physics(cdiINT32 cdi_phys_type)
{
  if (cdi_phys_type_is_basic_fluid_for_sim(cdi_phys_type))
    return cdi_lookup_uds_physics(CDI_UDS_PHYS_FLUID);
  else if (cdi_phys_type_is_special_fluid_for_sim(cdi_phys_type))
    return cdi_lookup_uds_physics(CDI_UDS_PHYS_SPECIAL_FLUID);
  else if (cdi_phys_type_is_wall_for_sim(cdi_phys_type))
    return cdi_lookup_uds_physics(CDI_UDS_PHYS_WALL);
  else if (cdi_phys_type_is_inlet_or_outlet_for_sim(cdi_phys_type))
    return cdi_lookup_uds_physics(CDI_UDS_PHYS_INLET_OUTLET);
  else
    return NULL;  //APM surface and fluid
}

// END: For usage in simulator to facilitate UDS data input 


/* variable types */
#define CDI_VAR_ID_NU_ON_T    10
#define CDI_VAR_ID_WALL_DQ    11
#define CDI_VAR_ID_WALL_T     12
#define CDI_VAR_ID_SLIP_FAC   13
#define CDI_VAR_ID_VSLIP_FAC  14
#define CDI_VAR_ID_MASS       15
#define CDI_VAR_ID_X_MOM      16
#define CDI_VAR_ID_Y_MOM      17
#define CDI_VAR_ID_Z_MOM      18
#define CDI_VAR_ID_ENERGY     19
#define CDI_VAR_ID_PRESSURE   20
#define CDI_VAR_ID_UNIT_VEC_X 21
#define CDI_VAR_ID_UNIT_VEC_Y 22
#define CDI_VAR_ID_UNIT_VEC_Z 23
#define CDI_VAR_ID_MASS_FLUX  24
#define CDI_VAR_ID_VEL_X      25
#define CDI_VAR_ID_VEL_Y      26
#define CDI_VAR_ID_VEL_Z      27
#define CDI_VAR_ID_ANG_VEL    28
#define CDI_VAR_ID_POINT_X    29
#define CDI_VAR_ID_POINT_Y    30
#define CDI_VAR_ID_POINT_Z    31
#define CDI_VAR_ID_TEMP       32
#define CDI_VAR_ID_HYDRAULIC_DIAMETER 33
#define CDI_VAR_ID_MASS_FLUX_X        34
#define CDI_VAR_ID_MASS_FLUX_Y        35
#define CDI_VAR_ID_MASS_FLUX_Z        36
#define CDI_VAR_ID_NEW_PRESSURE       37
#define CDI_VAR_ID_NEW_MASS_FLUX_X    38
#define CDI_VAR_ID_NEW_MASS_FLUX_Y    39
#define CDI_VAR_ID_NEW_MASS_FLUX_Z    40

/* Porous media specification */
#define CDI_VAR_ID_RES0_R   41 /* Velocity independent resistance coefficients in primary */
#define CDI_VAR_ID_RES0_G   42 /* coordinate system. */
#define CDI_VAR_ID_RES0_B   43
#define CDI_VAR_ID_RES1_R   44 /* Velocity dependent resistance coefficients in primary */
#define CDI_VAR_ID_RES1_G   45 /* coordinate system. */
#define CDI_VAR_ID_RES1_B   46
#define CDI_VAR_ID_RAXIS_X  47 /* Vector definitions of the x and y axis (called R and G */
#define CDI_VAR_ID_RAXIS_Y  48 /* respectively) of the primary coordinate system in terms  */
#define CDI_VAR_ID_RAXIS_Z  49 /* of the base coordinate system. */
#define CDI_VAR_ID_GAXIS_X  50
#define CDI_VAR_ID_GAXIS_Y  51
#define CDI_VAR_ID_GAXIS_Z  52

/* Variable types for thermal stuff */
#define CDI_VAR_ID_TURB_PRANDTL         53
#define CDI_VAR_ID_POROUS_HEAT_COEF     54
#define CDI_VAR_ID_POROUS_REF_TEMP      55
#define CDI_VAR_ID_WALL_HEAT_FLUX       56

/* Variable types for fan model */
#define CDI_VAR_ID_R_FORCE_COEFF0       57  /* 0th-order velocity coeff for axial external force */
#define CDI_VAR_ID_R_FORCE_COEFF1       58  /* 1st-order velocity coeff for axial external force */
#define CDI_VAR_ID_R_FORCE_COEFF2       59  /* 2nd-order velocity coeff for axial external force */
#define CDI_VAR_ID_R_FORCE_COEFF3       60  /* 3rd-order velocity coeff for axial external force */
#define CDI_VAR_ID_U_THETA_COEFF0       61  /* 0th-order velocity coeff for tangential velocity  */
#define CDI_VAR_ID_U_THETA_COEFF1       62  /* 1st-order velocity coeff for tangential velocity  */
#define CDI_VAR_ID_U_THETA_COEFF2       63  /* 2nd-order velocity coeff for tangential velocity  */
#define CDI_VAR_ID_U_THETA_COEFF3       64  /* 3rd-order velocity coeff for tangential velocity  */

#define CDI_VAR_ID_POROUS_Q0            65  /* Constant volumetric heat input */

#define CDI_VAR_ID_TURB_VIA             66

#define CDI_TURB_VIA_INTENSITY 0
#define CDI_TURB_VIA_KE        1
#define CDI_TURB_EXTRAPOLATED  2

#define CDI_VAR_ID_TURB_INTENSITY       67
#define CDI_VAR_ID_TURB_LENGTH_SCALE    68
#define CDI_VAR_ID_TURB_KE              69
#define CDI_VAR_ID_TURB_DISSIPATION     70
#define CDI_VAR_ID_COORD_SYSTEM         71

#define CDI_VAR_ID_SURFACE_ROUGHNESS    72
#define CDI_VAR_ID_SURFACE_CHAR_LEN     73

#define CDI_VAR_ID_RESPONSE_TIME        74

#define CDI_VAR_ID_ACCEL_X              75
#define CDI_VAR_ID_ACCEL_Y              76
#define CDI_VAR_ID_ACCEL_Z              77
#define CDI_VAR_ID_ANG_VEL_X            78
#define CDI_VAR_ID_ANG_VEL_Y            79
#define CDI_VAR_ID_ANG_VEL_Z            80
#define CDI_VAR_ID_REF_TEMP             81
#define CDI_VAR_ID_THERMAL_EXP_COEFF    82 /* Units of inverse temperature */
#define CDI_VAR_ID_REF_FRAME            83

#if 0
// These variables were defined with IDs that are already in use for other variables.
// Fortunately, these variables are not used, so we can simply delete them.
#define CDI_VAR_ID_PARA_DIVISIONS       84
#define CDI_VAR_ID_PERP_DIVISIONS       85
#define CDI_VAR_ID_FRONT_OFFSET         86
#define CDI_VAR_ID_BACK_OFFSET          87
#define CDI_VAR_ID_PERCENT_FLOW         88
#define CDI_VAR_ID_MASS_FLOW_RATE       89
#define CDI_VAR_ID_SPECIFIC_HEAT        90
#define CDI_VAR_ID_HEAT_REJECTION       91
#define CDI_VAR_ID_ENTRY_TEMP           92
#define CDI_VAR_ID_EXPERIMENT_REF_TEMP  93
#endif

/* Local reference frames are numbered starting at 2 */
#define CDI_REF_FRAME_GLOBAL     0 /* global body-fixed */
#define CDI_REF_FRAME_GROUND     1 /* ground-fixed */

#define cdi_is_ref_frame_global(f)    ((f)  < 2)
#define cdi_is_ref_frame_local(f)     ((f) >= 2)
#define cdi_local_ref_frame_index(f)  ((f)  + 2)

#define CDI_VAR_ID_VEL_X_T0             84
#define CDI_VAR_ID_VEL_Y_T0             85
#define CDI_VAR_ID_VEL_Z_T0             86

#define CDI_VAR_ID_DENSITY_T0           87

#define CDI_VAR_ID_FAN_LENGTH           88
#define CDI_VAR_ID_FAN_RADIUS           89
#define CDI_VAR_ID_FAN_HUB_RADIUS       90

#define CDI_VAR_ID_TANG_VEL_MODEL       91
#define CDI_TANG_VEL_MODEL_USER_DEFINED 0
#define CDI_TANG_VEL_MODEL_H_AND_O      1

/* No longer used, but reserve IDs to prevent old CDI files from mistakenly
 * being consumed without error
 */
#if 0
#define CDI_VAR_ID_TANG_VEL_FACTOR      92
#define CDI_VAR_ID_MAX_TANG_VEL         93
#endif

#define CDI_VAR_ID_USE_REFL_DAMPING     94
#define CDI_VAR_ID_DIST_TO_REFL_SURFACE 95

/* analytical LRF surface definition */
#define CDI_VAR_ID_CYL_BEGIN_X          99
#define CDI_VAR_ID_CYL_BEGIN_Y         100
#define CDI_VAR_ID_CYL_BEGIN_Z         101
#define CDI_VAR_ID_CYL_END_X           102
#define CDI_VAR_ID_CYL_END_Y           103
#define CDI_VAR_ID_CYL_END_Z           104
#define CDI_VAR_ID_BEGIN_RADIUS        105
#define CDI_VAR_ID_END_RADIUS          106
#define CDI_VAR_ID_CYL_NUM_SIDES       107

#define CDI_VAR_ID_TANG_VEL_COEFF0       108
#define CDI_VAR_ID_TANG_VEL_THRUST_COEFF 109

/* Temperature dependent resistance */
#define CDI_VAR_ID_RESISTANCE_REF_TEMP  110

/* Dynamic heating rate (in porous media) */
#define CDI_VAR_ID_USE_DYN_HEAT_RATE    111
#define CDI_VAR_ID_DYN_HEAT_RATE_COEFF0 112
#define CDI_VAR_ID_DYN_HEAT_RATE_COEFF1 113
#define CDI_VAR_ID_DYN_HEAT_RATE_COEFF2 114
#define CDI_VAR_ID_CURVED_POROUS_MEDIUM 115

/* LRF direction vector define a half-plane orthogonal to the axis of rotation */
#define CDI_VAR_ID_PLANE_VEC_X          116
#define CDI_VAR_ID_PLANE_VEC_Y          117
#define CDI_VAR_ID_PLANE_VEC_Z          118

#define CDI_VAR_ID_SUCTION_VEL          119

enum CDI_LRF_TYPE {
  CDI_STATIC_LRF = 1,
  CDI_SLIDING_LRF = 2
};

/* MRF type (value 1 = static, 2 = sliding-mesh) */
#define CDI_VAR_ID_MRF_TYPE             120

/* Thermal resistance boundary condition vars */
#define CDI_VAR_ID_WALL_THICKNESS       121
#define CDI_VAR_ID_WALL_CONDUCTIVITY    122
#define CDI_VAR_ID_EXT_HEAT_XFER_COEFF  123

/* Acoustic Porous Medium */
#define CDI_VAR_ID_APM_DENSITY          124
#define CDI_VAR_ID_APM_POROSITY         125
#define CDI_VAR_ID_APM_TORTUOSITY       132
#define CDI_VAR_ID_APM_THICKNESS        126
#define CDI_VAR_ID_FLUID_DESC_INDEX     127

//surface material
#define CDI_VAR_ID_SURFACE_MATERIAL     128

// curved hx variables

#define CDI_VAR_ID_CURVED_HX_TABLE_INDEX 129
#define CDI_VAR_ID_CURVED_HX_MEAS_WINDOW_INDEX 130
/* slip velocity type (value 1 = Rigid Body Motion, 2 = Arbitrary Motion) */
#define CDI_VAR_ID_VEL_SLIP_TYPE         131

// Boundary Layer Type
#define CDI_VAR_ID_BOUNDARY_LAYER_TYPE   133

// Far Field Boundary Condition
#define CDI_VAR_ID_FAR_FIELD_BC          134

// Rotating Tire
#define CDI_VAR_ID_DEFORMING_TIRE        135
#define CDI_VAR_ID_AVERAGE_MEA_ACROSS    136

// Torque measured velocity in LRF
#define CDI_VAR_ID_VELOCITY_VIA_TORQUE   137
#define CDI_VAR_ID_RESISTIVE_TORQUE      138
#define CDI_VAR_ID_EXTERNAL_TORQUE       139
#define CDI_VAR_ID_START_TIME            140 
#define CDI_VAR_ID_END_TIME              141
#define CDI_VAR_ID_TIME_ACCURATE         142
#define CDI_VAR_ID_MOMENT_OF_INERTIA     143
#define CDI_VAR_ID_MEAS_WINDOW_INDEX     144
#define CDI_VAR_ID_INITIAL_ANG_VEL       145

// Water Vapor Transport
#define CDI_VAR_ID_CONDENSABLE_SURFACE      146
#define CDI_VAR_ID_FILM_THICKNESS           147
#define CDI_VAR_ID_WATER_VAPOR_SOURCE_TERM  148
#define CDI_VAR_ID_WATER_MASS_FRACTION      149
#define CDI_VAR_ID_RELATIVE_HUMIDITY        150

// Mass flow
#define CDI_VAR_ID_MASS_FLOW_RATE           151 
//
// Synthetic turbulent velocity generation parameters
#define CDI_VAR_ID_TURB_INTENSITY_X         152
#define CDI_VAR_ID_TURB_INTENSITY_Y         153
#define CDI_VAR_ID_TURB_INTENSITY_Z         154
#define CDI_VAR_ID_TURB_LEN_SCALE_X         155
#define CDI_VAR_ID_TURB_LEN_SCALE_Y         156
#define CDI_VAR_ID_TURB_LEN_SCALE_Z         157
#define CDI_VAR_ID_TURB_FREQ_MIN            158
#define CDI_VAR_ID_TURB_FREQ_MAX            159
#define CDI_VAR_ID_TURB_DELTA_T             160
#define CDI_VAR_ID_TABULAR_DATA_ID          161

// Advanced Fluid-APM Interface Modeling
#define CDI_VAR_ID_APM_USE_ADV_FLUID        162
#define CDI_VAR_ID_APM_VISCOUS_RES          163
#define CDI_VAR_ID_APM_INERTIAL_RES         164
#define CDI_VAR_ID_APM_REACTANCE_COEFF      165
#define CDI_VAR_ID_APM_SURFACE_POROSITY     166

// Inlet pressure 
#define CDI_VAR_ID_IS_TOTAL_TEMP            167
#define CDI_VAR_ID_TOTAL_TEMP_OBSOLETE      168
#define CDI_VAR_ID_IS_SUPERSONIC_INLET      169
#define CDI_VAR_ID_SS_INLET_PRESSURE        170

#define CDI_VAR_ID_INLET_AREA               171

// Boundary Seeding
#define CDI_VAR_ID_MEAS_FRAME_NUM           172

#define CDI_VAR_ID_REVERSE_MASS_FLOW        173

#define CDI_VAR_ID_ROTATION_VIA_REF_FRAME   174

/* "Reference Frame May Contain Transonic Flow" for Sliding Mesh Rotating
   Ref Frames. */
#define CDI_VAR_ID_CONTAIN_TRANSONIC_FLOW   175

// Q Patch (Wall Acoustic Absorption)
#define CDI_VAR_ID_SURFACE_ACOUSTIC_ABSORB  176

#define CDI_VAR_ID_VEL_N                    177
#define CDI_VAR_ID_IS_VEL_N                 178
// Transient boundary seeding
#define CDI_VAR_ID_MEAS_START_FRAME_NUM     179
#define CDI_VAR_ID_IMPORT_FLUCTUATIONS      180
#define CDI_VAR_ID_PM_LEAKAGE_RESOLUTION    181   // (-1 => not applicable), (0, 1, 2 => Low, Medium, High)
// Solid conduction
#define CDI_VAR_ID_SOLID_MATERIAL           182
#define CDI_VAR_ID_SHELL_CONFIGURATION      183
#define CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT   184  //  Imposed heat in Power Density
#define CDI_VAR_ID_CONTACT_RESISTANCE       185
// Solid thermal contact fluxes
#define CDI_VAR_ID_HEAT_FLUX_1              186
#define CDI_VAR_ID_HEAT_FLUX_2              187
// Conducting shells
#define CDI_VAR_ID_SHELL_LAYER_THICKNESS    188
// Radiation
#define CDI_VAR_ID_SOLID_COMPUTE_RADIATION  189
#define CDI_VAR_ID_RADIATION_SURFACE_COND   190
// Cylindrical Anisotropy
#define CDI_VAR_ID_ANISOTROPY_USE_PART_AXIS 191
#define CDI_VAR_ID_MIN_DESIGN_TEMP          192
#define CDI_VAR_ID_MAX_DESIGN_TEMP          193

#define CDI_VAR_ID_RADIATION_TEMP           194

// Insert PF v2025 Conduction stuff here

#define CDI_VAR_ID_VISCOUS_DAMPING_FACTOR   195
// Allow heat input for conducting solids to be in power units
#define CDI_VAR_ID_SPECIFY_HEAT_VIA_POWER_DENSITY 196    // value 1 = Imposed heat in Power Density (Watts/m^3), 2 = Imposed Heat in Power
#define CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT_IN_POWER 197   // "Imposed Heat" in Power units
#define CDI_VAR_ID_CONDUCTOR_WALL_HEAT_FLOW_IN 198       //  "Heat Flow In" in Power units
#define CDI_VAR_ID_CONDUCTOR_WALL_HEAT_FLOW_OUT 199      // "Heat Flow Out" in Power units
#define CDI_VAR_ID_SPECIAL_LAYER 200                     // value = -1 [None], 0[Vacuum], 1[Air]
#define CDI_VAR_ID_LAYER_CONTACT_AREA 201                // Percentage of contact area for special layers of Air/Vacuum

/* VAR IDs 1000-1999 are reserved for friction factors. Currently we
   support just two, but anticipate generalizing this in the future to support
   arbitrary numbers of components (presumably nowhere close to 1000). */
#define CDI_VAR_ID_COMP0_FRICTION_FACTOR 1000
#define CDI_VAR_ID_COMP1_FRICTION_FACTOR 1001

/* VAR IDs 2000-2999 are reserved for wetting densities. Currently we support
   just two, but anticipate generalizing this in the future to support arbitrary
   numbers of components (presumably nowhere close to 1000). */
#define CDI_VAR_ID_COMP0_WETTING_DENS 2000
#define CDI_VAR_ID_COMP1_WETTING_DENS 2001

/* VAR IDs 3000-3999 are reserved for densities. Currently we support just two,
   but anticipate generalizing this in the future to support arbitrary numbers
   of components (presumably nowhere close to 1000). */
#define CDI_VAR_ID_COMP0_DENSITY 3000
#define CDI_VAR_ID_COMP1_DENSITY 3001

/* VAR IDs 4000-4999 are reserved for mass fluxes. Currently we support just two,
   but anticipate generalizing this in the future to support arbitrary numbers
   of components (presumably nowhere close to 1000). */
#define CDI_VAR_ID_COMP0_MASS_FLUX 4000
#define CDI_VAR_ID_COMP1_MASS_FLUX 4001

/* VAR IDs 5000-5999 are reserved for nu/T values. Currently we support just
   two, but anticipate generalizing this in the future to support arbitrary
   numbers of components (presumably nowhere close to 1000). */
#define CDI_VAR_ID_COMP0_NU_ON_T 5000   
#define CDI_VAR_ID_COMP1_NU_ON_T 5001

/* VAR IDs 6000-6999 are reserved for volumetric fraction values. Currently we
   support just two, but anticipate generalizing this in the future to support
   arbitrary numbers of components (presumably nowhere close to 1000). */
#define CDI_VAR_ID_COMP0_VOL_FRAC 6000
#define CDI_VAR_ID_COMP1_VOL_FRAC 6001

/* VAR IDs 7000-7999 are reserved for other 5G parameters. Previously the 1st
   two were defined as 124 & 125 in the old 5G version, but they were shifted by
   7000 when merged into the mainline because var IDs 124 & 125 were already in
   used.*/
#define CDI_VAR_ID_5G_OFFSET   7000

// State of matter (0=gas, 1=liquid, maybe more in the future?) 
#define CDI_VAR_ID_STATE_OF_MATTER  7124

// Var ID that can be included in some BC chunks to help keep track of whether a
// BC is an inlet or an outlet. Downstream tools mostly don't care, but
// exasumcdi should be able to differentiate between inlets and outlets when the
// same physics type could be used for either purpose. Currently only used for
// 5g. Inlet=0, Outlet=1
#define CDI_VAR_ID_BC_TYPE   7125

enum CDI_COMP_RATIO_TYPE {
  CDI_COMP_RATIO_VIA_DENS,          // "Component Densities"
  CDI_COMP_RATIO_VIA_DENS_AND_FRAC, // "Total Density & Component Fractions"
  CDI_COMP_RATIO_VIA_PRESS_AND_FRAC // "Total Pressure & Component Fractions"
};

#define CDI_VAR_ID_COMP_RATIO_TYPE 7126

#define CDI_MENT_FLUID     1
#define CDI_MENT_BOUNDARY  2

#define CDI_NUM_FLOW_PARAMS 5 /* mass, mom[3], energy */

#define CDI_MESR_FILENAME_EXT ".msr"

#define CDI_INFINITE_END_TIME 0x7FFFFFFF

/* VAR IDs 8000-8999 are reserved for User-Defined Scalar Transport.*/
#define CDI_VAR_ID_FIRST_USER_DEFINED_VAR_ID 8000


//========================================================================================
//
// CODING UTILITIES
//
//========================================================================================

void cdi_syntax_error_descend(const char *chunk_name);
void cdi_syntax_error_ascend(const char *chunk_name, cdiINT32 index);

inline CIO_ERRCODE cdi_cio_descend_with_error(CIO_INFO cio, const char *chunk_name)
{
  CIO_ERRCODE errcode = cio_descend(cio);
  if (errcode != CIO_ERR_SUCCESS)
    cdi_syntax_error_descend(chunk_name);
  return errcode;
}

inline CIO_ERRCODE cdi_cio_ascend_with_error(CIO_INFO cio, const char *chunk_name, cdiINT32 index_var)
{
  CIO_ERRCODE errcode = cio_ascend(cio);
  if (errcode != CIO_ERR_SUCCESS)
    cdi_syntax_error_ascend(chunk_name, index_var);
  return errcode;
}

/***
*** Uniquifying names (by source line number)
***
*** The macro ___cdi can be used to generate unique names for loop macros.  It
*** appends the current source line number to the argument variable, thus
*** providing functionality similar in spirit to Common Lisp's gensym.
*/

#define _cpp_cdi_concat(x,y) x ## y

#define _cpp_cdi_concat2(x,y) _cpp_cdi_concat(x,y)

#define ___cdi(name) _cpp_cdi_concat2(name,__LINE__)

#define ccCDI_DO_INNER_CHUNKS(index_var, chunk_name, cdi_info)                                  \
  CIO_INFO ___cdi(_cio) = (cdi_info)->cio_info;                                                 \
  const char *___cdi(_chunk_name) = (chunk_name);                                               \
  cdiINT32 ___cdi(_count) = cio_get_count(___cdi(_cio));                                        \
  asINT32 index_var;                                                                            \
for (index_var = 0; index_var < ___cdi(_count);                                               \
  index_var++, cdi_cio_ascend_with_error(___cdi(_cio), ___cdi(_chunk_name), index_var))    \
if (cdi_cio_descend_with_error(___cdi(_cio), ___cdi(_chunk_name)) == CIO_ERR_SUCCESS)

#define CDI_WITH_INNER_CHUNK(cdi_info)                                                                                  \
  CIO_INFO ___cdi(_cio) = (cdi_info)->cio_info;                                                                         \
  cdiINT32 ___cdi(_i);                                                                                                  \
for (___cdi(_i) = 0; ___cdi(_i) < 1; ___cdi(_i)++, cdi_cio_ascend_with_error(___cdi(_cio), "unknown", ___cdi(_i)))    \
if (cdi_cio_descend_with_error(___cdi(_cio), "unknown") == CIO_ERR_SUCCESS)

#define CDI_DO_INNER_CHUNKS(index_var, chunk_name, cdi_info, body)\
{\
  CDI_INFO _cdi_info = (cdi_info); \
  const char *_chunk_name = (chunk_name); \
  cdiINT32 _count = cio_get_count(_cdi_info->cio_info); \
  DOTIMES(index_var, _count, { \
if (cio_descend(_cdi_info->cio_info) == CIO_ERR_SUCCESS) {\
  body; \
  if (cio_ascend(_cdi_info->cio_info) != CIO_ERR_SUCCESS) {\
    cdi_syntax_error_ascend(_chunk_name, index_var); \
  }\
}\
else\
  cdi_syntax_error_descend(_chunk_name); \
}); \
}

#define ccCDI_DO_INNER_CHUNK(index_var, chunk_name, cdi_info)\
  CIO_INFO ___cdi(_cio) = (cdi_info)->cio_info; \
  const char *___cdi(_chunk_name) = (chunk_name); \
for (asINT32 zz = 0; zz < 1; \
  zz++, cdi_cio_ascend_with_error(___cdi(_cio), ___cdi(_chunk_name), index_var++))\
if (cdi_cio_descend_with_error(___cdi(_cio), ___cdi(_chunk_name)) == CIO_ERR_SUCCESS)

void cdi_push(CDI_INFO cdi_info,    /* the info structure */
              CIO_CCCC chunk_type); /* Type of cdi chunk */
void cdi_pop(CDI_INFO cdi_info);    /* the info structure */
//========================================================================================


//========================================================================================
// Chunks for lower-level data types

// For i/o from your class or struct which contains a boolean data member, use
//   cdi_inner_chunk_read_bool()
//   cdi_write_bool()
//   print_annotated_bool()
//   undump_bool_with_id()
//
// print_bool() and undump_bool() are used only in the recursive dump_chunk() and
// undump_chunk() contexts, 
#define CDI_CHUNK_TYPE_BOOL CIO_BUILDCCCC('b', 'o', 'o', 'l')

// For i/o from your class or struct which contains an integer data member, use
//   cdi_inner_chunk_read_int_()
//   cdi_write_int_()
//   print_annotated_int_()
//   undump_bool_with_id()
//
// print_int_() and undump_int_() are used only in the recursive dump_chunk() and
// undump_chunk() contexts.
#define CDI_CHUNK_TYPE_INT_ CIO_BUILDCCCC('i', 'n', 't', '_')

// For i/o from your class or struct which contains an enum data member, use
//   cdi_inner_chunk_read_enum<>()
//   cdi_write_enum()
//   print_annotated_enum()
//   undump_enum_with_id()

// print_enum() and undump_enum() are used only in the recursive dump_chunk() and
// undump_chunk() contexts.

// NOTE: The formatted print_annotated_enum() function can print the 'name' of the
//       enum value as part of the formatting, i.e.
//             enum { 3 } ( my_enum_setting: MyEnumValue )
//
//       To accomplish this, overload the static method
//       std::string GetCDIEnumValueDescription() to take your enum as an argument
//       and return a string description of your enum value.
//       See eCDI_MNTR_TYPE for an example.
//
//       For a usage example:
//       eCDI_MNTR_TYPE::Enum monitorType = eCDI_MNTR_TYPE::Flow;
//       print_annotated_enum(monitorType, "monitor_type", depth);
//       results in:
//       enum { 0 } ( monitor type: Flow )
//
//       If you do not implement GetCDIEnumValueDescription() for your enum, then
//       print_annotated_enum() will call the version that takes an 'int' argument, which
//       returns an empty string, resulting in the following output:
//       enum { 0 } ( monitor type )
//
//       The undump_enum() method can parse either of these formats.

#define CDI_CHUNK_TYPE_ENUM CIO_BUILDCCCC('e', 'n', 'u', 'm')

// Use cdi_read_dbls(), cdi_write_dbls(), print_dbls(), undump_dbls(), and
// cdi_inner_chunk_read_dbls()
//typedef struct sCDI_DBLS
//{
//#define CDI_CHUNK_TYPE_DBLS CIO_BUILDCCCC('d', 'b', 'l', 's')
//  CIO_CCCC cccc;
//  cdiINT32 size; /* the number of vertices */
//  double *values; /* the 3-space coordinates of the vertices*/
//} *CDI_DBLS;

#define CDI_CHUNK_TYPE_DBLS CIO_BUILDCCCC('d', 'b', 'l', 's')

// For i/o from your class or struct which contains a std::string data member, use
//   cdi_inner_chunk_read_strg()
//   cdi_write_strg()
//   print_annotated_strg()
//   undump_strg_with_id()
//
// print_strg() and undump_strg() are used only in the recursive dump_chunk() and
// undump_chunk() contexts.
#define CDI_CHUNK_TYPE_STRG CIO_BUILDCCCC('s', 't', 'r', 'g')

#define CDI_CHUNK_TYPE_DBLE CIO_BUILDCCCC('d', 'b', 'l', 'e')


//========================================================================================
#define CDI_MAX_N_USER_DEFINED_SCALARS 10
#define CDI_WALL_SCALAR_VALUE_BC    0
#define CDI_WALL_SCALAR_FLUX_BC     1

enum CDI_VAR_ID_UDS_OFFSET {
  CDI_VAR_ID_UDS_INVALID = -1,
  // Fluid variables
  CDI_VAR_ID_UDS_FLUID_INITIAL_CONDITION = 0,
  CDI_VAR_ID_UDS_FLUID_SOURCE_TERM,
  // BC variables
  CDI_VAR_ID_UDS_BC_INLET_CONTENT_VIA,
  CDI_VAR_ID_UDS_BC_INLET_WALL_VALUE,
  CDI_VAR_ID_UDS_BC_INLET_NO_SCALAR_DIFFUSION,
  CDI_VAR_ID_UDS_BC_WALL_SCALAR_BOUNDARY_TYPE,
  CDI_VAR_ID_UDS_BC_WALL_SCALAR_FLUX,
  CDI_VAR_ID_UDS_BC_OUTLET_REVERSE_FLOW_COMPOSITION,
  CDI_VAR_ID_UDS_BC_INLET_MEAN_VALUE,
  CDI_VAR_ID_UDS_FLUID_DIFFUSIVITY,  //for Porous Media or Fan
  CDI_VAR_ID_UDS_MAX_VARS
};
class cCDI_USER_DEFINED_VARS {
public:
  cCDI_USER_DEFINED_VARS(int numUserDefinedScalars) :m_numUserDefinedScalars(numUserDefinedScalars) {}
  int GetFirstOffset() const { return m_startOffset; }
  int GetNumberOfUserDefinedScalars() const { return m_numUserDefinedScalars; }
  int GetNumberOfVariables() const { return m_numVars; }
  int GetScalarValueIndex(int indexOfScalar, int indexOfScalarValue) const;
  bool GetScalarTypeAndIndex(int scalarValueIndex, int* scalarType, CDI_VAR_ID_UDS_OFFSET* scalarValueType);

private:
  int m_startOffset = CDI_VAR_ID_FIRST_USER_DEFINED_VAR_ID;
  int m_numUserDefinedScalars = 0;
  int m_numVars = CDI_VAR_ID_UDS_MAX_VARS;
  int m_indexOffset = 0;

};

//========================================================================================


// Store a set of sri variable types. The expected use of this class is to
// identify the variables that should be recorded by a measurement
// window. Presently, the class cannot store "user" variables, but it could be
// extended to do so.
class cSRI_MEAS_VARS
{
  typedef sriUINT MASK_WORD_TYPE;
  static const int NUM_BITS_PER_MASK_WORD = sizeof(MASK_WORD_TYPE) * 8;

  // Do not change this without understanding the ramifications.  This
  // variable is used when reading, writing, dumping, and undumping the chunk.
  // Most likely if this changes, the CDI version needs to be bumped and
  // cdi_meas_vars_num_bitmasks needs to be modified.
  static const unsigned int NUM_MASK_WORDS = 4;

 public:
  cSRI_MEAS_VARS();

  sriINT var(sriINT nth_var) const;  //accessor to private member m_vars[nth_var]
  void add_var(sriINT var);
  void remove_var(sriINT var);
  sriBOOL has_var(sriINT var) const;
  void replace_var_if_present(sriINT old_var,
                              sriINT new_var);

  sriINT num_vars() const;
  sriINT *get_vars(sriINT &num_vars) const;
  void sort_vars();

  // m_bitmasks was used in v. 3.23 and prior to store SRI_VARIABLES.
  // As of v. 3.24, SRI_VARIABLES are stored in m_vars.
  // m_bitmasksObsolete is retained to read older CDI files; the bitmask
  // is then converted to the vector m_vars via convert_bit_mask_to_vars
  MASK_WORD_TYPE m_bitmasksObsolete[NUM_MASK_WORDS];
  void convert_bit_mask_to_vars(); 
  void convert_vars_to_bit_mask();
 private:
  std::vector<sriINT> m_vars;
  
};

/* unit types */
#define CDI_MAX_UNIT_TYPES 22
/* @@@ Replaced POWER_UNIT with AREA_UNIT since adding AREA_UNIT to the
 * end hosed the case constructor.
*/
/* New unit types must be added to the end of this list to maintain compatibility with
 * old case files and old CDI files.
 */
enum CDI_UNIT_TYPES
{
  length_unit,mass_unit,time_unit,temperature_unit,
  force_unit,energy_unit,torque_unit,area_unit,velocity_unit,
  dyn_visc_unit,kin_visc_unit,therm_diff_unit,density_unit,
  acceleration_unit,heat_flux_unit,pressure_unit,mass_flux_unit,
  dimensionless,lattice,
  angular_velocity_unit,
  inverse_length_unit,
  inverse_time_unit
};

#define CDI_CHUNK_TYPE_PARM CIO_BUILDCCCC('p', 'a', 'r', 'm')
struct sCDI_PARM
{

  CIO_CCCC cccc;
  bool defaulted;   //if defaulted, this is a copy of a default setting
  bool isEqVariable;//designates whether this or a function will provide the value
  bool isDataCurve; //designates whether this value is from a data curve
  std::string variableName;//this function name is used if fromFunction is true
  double value;
  std::string preferredUnit;

  sCDI_PARM()
    : defaulted(false)
    , isEqVariable(false)
    , isDataCurve(false)
    , value(0.0)
    , preferredUnit("-")
  { cccc = CDI_CHUNK_TYPE_PARM; }

  //These are conversion operators that allows a sCDI_PARM object to be easily passed 
  //to various PRI functions which expect PRI::COMPOUND_FLOAT or PRI::COMPOUND_DOUBLE argument types.
  operator const PRI::cCOMPOUND_FLOAT() const {
    PRI::cCOMPOUND_FLOAT pri_parm((float)value, preferredUnit.c_str(), variableName.c_str());
    return pri_parm;
  }

  operator const PRI::cCOMPOUND_DOUBLE() const {
    PRI::cCOMPOUND_DOUBLE pri_parm(value, preferredUnit.c_str(), variableName.c_str());
    return pri_parm;
  }
};
typedef sCDI_PARM* CDI_PARM;

void cdi_read_parm(CDI_INFO cdi_info, CDI_PARM);

inline void
cdi_inner_chunk_read_parm(cdiINT32 &index, const char* chunkName,
                          CDI_INFO info, CDI_PARM parm)
{
  if (cdi_cio_descend_with_error(info->cio_info, "parm") == 0)
  {
    cdi_read_parm(info, parm);
  }
  cdi_cio_ascend_with_error(info->cio_info, chunkName, ++index);
}


template <typename T>
inline void cdi_inner_chunk_read_enum(cdiINT32 &index, const char* chunkName,
                                      CDI_INFO info, T* enumValue)
{
  if (cdi_cio_descend_with_error(info->cio_info, "enum") == 0)
  {
    *enumValue = (T)cdi_read_one_asINT32(info);
  }
  cdi_cio_ascend_with_error(info->cio_info, chunkName, ++index);
}

inline void cdi_inner_chunk_read_int_(cdiINT32 &index, const char* chunkName,
                                      CDI_INFO info, sINT32* value)
{
  if (cdi_cio_descend_with_error(info->cio_info, "int_") == 0)
  {
    *value = cdi_read_one_asINT32(info);
  }
  cdi_cio_ascend_with_error(info->cio_info, chunkName, ++index);
}

inline void cdi_inner_chunk_read_bool(cdiINT32 &index, const char* chunkName,
                                      CDI_INFO info, bool* value)
{
  if (cdi_cio_descend_with_error(info->cio_info, "bool") == 0)
  {
    sINT32 intValue = cdi_read_one_asINT32(info);
    *value = (intValue != 0);
  }
  cdi_cio_ascend_with_error(info->cio_info, chunkName, ++index);
}

void cdi_read_dbls(CDI_INFO, idFLOAT*, cdiINT32&);

// cdi_inner_chunk_read_dbls is used to read a known sturcture 
// there is an assumption that values is static or pre-allocated
// no extra memory for values is allocated in this function
inline void cdi_inner_chunk_read_dbls(cdiINT32 &index, const char* chunkName,
                                      CDI_INFO info, idFLOAT* values)
{
  if (cdi_cio_descend_with_error(info->cio_info, "dbls") == 0)
  {
    cdiINT32 count = 0;
    cdi_read_dbls(info, values, count);
  }
  cdi_cio_ascend_with_error(info->cio_info, chunkName, ++index);
}

inline void cdi_inner_chunk_read_dble(cdiINT32 &index, const char* chunkName,
                                      CDI_INFO info, idFLOAT* value)
{
   if (cdi_cio_descend_with_error(info->cio_info, "dble") == 0)
   {
      cdi_read_idFLOAT(info, value, 1);
   }
   cdi_cio_ascend_with_error(info->cio_info, chunkName, ++index);
}

inline void cdi_inner_chunk_read_strg(cdiINT32 &index
                                    , const char* chunkName
                                    , CDI_INFO info
                                    , std::string* value)
{
  if (cdi_cio_descend_with_error(info->cio_info, "strg") == 0)
  {
    cdi_read_stdstring(info, *value);
  }
  cdi_cio_ascend_with_error(info->cio_info, chunkName, ++index);
}



typedef struct sCDI_RGNS
{ /* regions list */
#define CDI_CHUNK_TYPE_RGNS CIO_BUILDCCCC('r', 'g', 'n', 's')
  CIO_CCCC cccc;
  cdiINT32 n_region; /* the number of regions */
  cdiINT32 *region; /* the array of regions */
} *CDI_RGNS;

struct sCDI_VECOF_RGNS // STL-friendly version of above
{
// NOTE: There are now overloads of the cdi_write_rgns(), cdi_read.., etc. functions
//       which take a std::vector<cdiINT32> as an argument, so you can simply use a
//       std::vector for your chunk data, and ignore this struct; it really just exists
//       so as to not break code that was written for some particle modeling data.
  std::vector<cdiINT32> regions;  // Region indices
};

#define CDI_CHUNK_TYPE_XCDI CIO_BUILDCCCC('x', 'c', 'd', 'i')

typedef struct sCDI_PTGE
{ /* file parentage */
#define CDI_CHUNK_TYPE_PTGE CIO_BUILDCCCC('p', 't', 'g', 'e')
  CIO_CCCC cccc;
  cdiINT64 case_file_id;
  cdiINT64 case_geometry_id;
  cdiINT64 cdi_file_id;
  cdiINT32 powercase_version_n_chars;
  char *powercase_version;
  cdiINT64 encryption_version_id;
} *CDI_PTGE;

typedef struct sCDI_BBOX
{
#define CDI_CHUNK_TYPE_BBOX CIO_BUILDCCCC('b', 'b', 'o', 'x')
  CIO_CCCC cccc;
  double coord[3][2]; /* the coordinates of the bbox corners */
} *CDI_BBOX;

typedef struct sCDI_AUDT {
#define CDI_CHUNK_TYPE_AUDT CIO_BUILDCCCC('a', 'u', 'd', 't')
  CIO_CCCC cccc;
  cdiINT32 n_audit_ur_char; /* Number of characters in the universal rep */
  char *audit_ur; /* Universal rep of audit trail contents */
} *CDI_AUDT;

typedef struct sCDI_UNDB {
#define CDI_CHUNK_TYPE_UNDB CIO_BUILDCCCC('u', 'n', 'd', 'b')
  CIO_CCCC cccc;
  cdiINT32 units_db_str_length; /* Number of characters in the units database string */
  char *units_db_str; /* Units database string contents */
} *CDI_UNDB;

typedef sSRI_USER_DEFINED_UNIT  sCDI_USER_DEFINED_UNIT, *CDI_USER_DEFINED_UNIT;

typedef sSRI_UNIT_CLASS_ENTRY   sCDI_UNIT_CLASS_ENTRY, *CDI_UNIT_CLASS_ENTRY;

typedef cSU_CHANGEABLE_UNIT     sCDI_CHANGEABLE_UNIT, *CDI_CHANGEABLE_UNIT;

typedef struct sCDI_CONDENSED_UNITS_DB {
  std::vector<sCDI_CHANGEABLE_UNIT>   changeable_units; // dimless and lattice units
  std::vector<sCDI_USER_DEFINED_UNIT> user_defined_units;
  std::vector<sCDI_UNIT_CLASS_ENTRY>  unit_class_additions;
  std::vector<sCDI_UNIT_CLASS_ENTRY>  unit_class_deletions;
  VOID update_units_db(UNITS_DB db);
} *CDI_CONDENSED_UNITS_DB;

typedef class cCDI_GLOB_BUNDLE {
 public:
  double char_length_lattice;
  double char_area_lattice;
  double char_temp_lattice;
  double char_vel_lattice;
  double char_density_lattice;
  double char_specific_heat_mks;
  double char_static_pressure_lattice;

  double fluid_prandtl_num;
  double reynolds_number;

  double gas_constant_mks;
  double gas_constant_lattice;
  double gamma_mks;
  double gamma_lattice;

  double mach_num_real;
  double mach_num_sim;

  //MMD_Eutectic
  double specific_heat_solid;
  double melting_temp;
  double boiling_temp;
  double latent_heat_of_fusion;
  double thermal_conductivity_solid;
  double thermal_conductivity_liquid;
  double density_solid;

  bool are_real_and_lattice_gamma_equal();
  bool are_real_and_sim_mach_number_equal();
  bool read_glob_chunk_into_bundle(CDI_INFO cdi_info, bool is_5g_sim, CDI_CONDENSED_UNITS_DB cdb);
  bool define_dimless_units(CDI_CONDENSED_UNITS_DB cdb);

  //kEps_SuperCycle
  bool bsuper_cycle_turbulence_solver;
  cdiINT32 super_cycling_factor;

  //H2 EoS
  double h2EoSBeta_mks;

  // Radiation
  double radiation_background_temp;
  cdiINT32 radiation_viewfactor_rays;
  double radiation_update_ratio;
  double num_radiation_faces;
} *CDI_GLOB_BUNDLE;

typedef struct sCDI_EQNS {
#define CDI_CHUNK_TYPE_EQNS CIO_BUILDCCCC('e', 'q', 'n', 's')
  CIO_CCCC cccc;
  cdiINT32 equations_length; /* Number of characters in the equations */
  char *equations; /* Equations contents */
  cdiBOOLEAN pthermTimeAvailable; // True if t_ptherm is available for equation.
} *CDI_EQNS;

typedef struct sCDI_GTBL {
#define CDI_CHUNK_TYPE_GTBL CIO_BUILDCCCC('g', 't', 'b', 'l')
  CIO_CCCC cccc;
  char *name;
  char *filename;
  char *absolute_filename;
  cdiINT32 flags;
  cdiINT32 coord_sys;
  cdiINT32 read_during_sim;
  cdiINT32 read_after_meas;
  cdiINT32 meas_window_index;
  cdiINT32 first_meas_frame_to_read_after;
  cdiINT32 num_meas_frames_between_reads;
  char *command_to_run_before_read;
  cdiINT32 first_interval;
  cdiINT32 subsequent_intervals;
  cdiINT32 end_time;
  cdiINT32 table_length; /* Not needed to write CDI */
  char *table_string;
} *CDI_GTBL;

/* Definition of CDI_GTBL flags */
#define CDI_GTBL_COUPLE_TO_POWERCOOL     1
#define CDI_GTBL_COUPLE_TO_AMESIM        2

typedef struct scCDI_TABL {
#define CDI_CHUNK_TYPE_TABL CIO_BUILDCCCC('t', 'a', 'b', 'l')
  CIO_CCCC cccc;
  cdiINT32 num_tables;
  CDI_GTBL tables;
} *cCDI_TABL;

typedef struct sCDI_CSYS {
  char *name;
  double g_to_l_xform[4][4];   /* 2D section plane transform */
  double l_to_g_xform[4][4];   /* 2D section plane transform */
} *CDI_CSYS;

typedef struct scCDI_CSYS {
#define CDI_CHUNK_TYPE_CSYS CIO_BUILDCCCC('c', 's', 'y', 's')
  CIO_CCCC cccc;
  cdiINT32 num_coord_systems;
  CDI_CSYS coord_systems;
} *cCDI_CSYS;

typedef struct sCDI_CMNT
{
#define CDI_CHUNK_TYPE_CMNT CIO_BUILDCCCC('c', 'm', 'n', 't')
  CIO_CCCC cccc;
  cdiINT32 n_title_char; /* number of title characters */
  char *title; /* the title */
  cdiINT32 n_value_char; /* number of value characters */
  char *value; /* the value */
} *CDI_CMNT;

// Contains PowerBy information needed by downstream applications.
#define CDI_CHUNK_TYPE_PWBY CIO_BUILDCCCC('p', 'w', 'b', 'y')
struct sCDI_PWBY
{
  CIO_CCCC GetChunkType() const
  { return CDI_CHUNK_TYPE_PWBY; }

  bool m_isAPowerByFile = false;
  std::string m_itemName;
  std::string m_itemId;
  
  void WriteToCDI(sCDI_INFO* pCDI_info) const;
  void ReadFromCDI(sCDI_INFO* pCDI_info);
};

#define CDI_GEOM_NONE (-1) /* used for referencing non-existing geometry, */
                             /* like missing conjugate edges */
                             /* in single sided surfaces */

typedef struct sCDI_CASE
{
#define CDI_CHUNK_TYPE_CASE CIO_BUILDCCCC('c', 'a', 's', 'e')
  CIO_CCCC cccc;
} *CDI_CASE;

typedef struct sCDI_GEOM
{
#define CDI_CHUNK_TYPE_GEOM CIO_BUILDCCCC('g', 'e', 'o', 'm')
  CIO_CCCC cccc;
} *CDI_GEOM;


typedef struct sCDI_RGPN
{
#define CDI_CHUNK_TYPE_RGPN CIO_BUILDCCCC('r', 'g', 'p', 'n')
  CIO_CCCC cccc;
  cdiINT32 n_regions;
  std::vector<cdiUINT32> regionIds;
  std::vector<cdiUINT32> protectionIds;
} *CDI_RGPN;

typedef struct sCDI_VRTX
{
#define CDI_CHUNK_TYPE_VRTX CIO_BUILDCCCC('v', 'r', 't', 'x')
  CIO_CCCC cccc;
  cdiINT32 n_vertex; /* the number of vertices */
  double *coord; /* the 3-space coordinates of the vertices*/
} *CDI_VRTX;

typedef struct sCDI_FACT_OLD
{
  cdiINT32 face; /* the parent face */
  cdiINT32 edgehalf[3]; /* the array of edgehalves */
  double normal[3]; /* the facet normal */
} *CDI_FACT_OLD;

#define CDI_CHUNK_TYPE_FACT CIO_BUILDCCCC('f', 'a', 'c', 't')

typedef struct scCDI_FACT
{
  CIO_CCCC cccc;
  cdiINT32 n_facet; /* the number of facets */
  CDI_FACT_OLD facet; /* the array of facets */
} *cCDI_FACT;

typedef struct sCDI_FTAB
{
#define CDI_CHUNK_TYPE_FTAB CIO_BUILDCCCC('f', 't', 'a', 'b')
  CIO_CCCC cccc;
} *CDI_FTAB;

typedef struct sCDI_EDGE
{
  cdiINT32 conj_facet; /* the conjugate facet */
  cdiINT32 conj_edgehalf; /* the conjugate edgehalf */
  cdiINT32 head_vert; /* the vertex at the head of the edgehalf */
  cdiINT32 tail_vert; /* the vertex at the tail of the edgehalf */
} *CDI_EDGE;

typedef struct scCDI_EDGE
{
#define CDI_CHUNK_TYPE_EDGE CIO_BUILDCCCC('e', 'd', 'g', 'e')
  CIO_CCCC cccc;
  cdiINT32 n_edgehalf; /* the number of edgehalves */
  CDI_EDGE edgehalf; /* the array of edgehalves */
} *cCDI_EDGE;

#define CDI_CHUNK_TYPE_FACE CIO_BUILDCCCC('f', 'a', 'c', 'e')
typedef struct sCDI_FACE_OLD
{
  CIO_CCCC cccc;
  char *name; /* the name of the face */
  cdiINT32 index; /* the index that facets use to refer to this face */
  char *color; /* the name for the color of the face */
  cdiINT32 n_facet; /* the number of facets */
  cdiINT32 *facet; /* the array of facet indices */
  cdiINT32 prop; /* the surface property index (may be CDI_PHYS_TYPE_NONE) */
} *CDI_FACE_OLD;

typedef struct sCDI_NULL
{
#define CDI_CHUNK_TYPE_NULL CIO_BUILDCCCC('n', 'u', 'l', 'l')
  CIO_CCCC cccc;
  cdiINT32 n_null_char; /* number of null characters */
  char *null_char; /* the null characters */
} *CDI_NULL;


typedef struct sCDI_NAME
{
#define CDI_CHUNK_TYPE_NAME CIO_BUILDCCCC('n', 'a', 'm', 'e')
  CIO_CCCC cccc;
  cdiINT32 n_char; /* number of name characters */
  char *name; /* the name */
} *CDI_NAME;

typedef struct sCDI_OFFS
{
/* #define CDI_OFFS_CCCC "offs" */
#define CDI_CHUNK_TYPE_OFFS CIO_BUILDCCCC('o', 'f', 'f', 's')
  CIO_CCCC cccc;
  cdiINT32 offset; /* zero if not an offset, nonzero if offset */
} *CDI_OFFS;

typedef struct sCDI_PRGN
{
/* #define CDI_PRGN_CCCC "prgn" */
#define CDI_CHUNK_TYPE_PRGN CIO_BUILDCCCC('p', 'r', 'g', 'n')
  CIO_CCCC cccc;
  cdiINT32 tire_index;
} *CDI_PRGN;

typedef struct sCDI_SHLL
{
#define CDI_CHUNK_TYPE_SHLL CIO_BUILDCCCC('s', 'h', 'l', 'l')
  CIO_CCCC cccc;
} *CDI_SHLL;

const cdiINT32 CDI_MEAS_SPATIAL_AVERAGING_WARNING_THRESHOLD = 128;

namespace eCDI_MEAS_START_TIME_VIA {
enum Enum {
    StartTime,
    AfterInitialTransient,
    RelativeToEmitter
};

static std::string GetName(Enum ordinal) {
    switch (ordinal) {
    case StartTime: return "Start Time";
    case AfterInitialTransient: return "After Initial Transient";
    case RelativeToEmitter: return "Relative to Emitter";
    }
    return "";
}
}

inline std::string GetCDIEnumValueDescription(eCDI_MEAS_START_TIME_VIA::Enum ordinal)
{
  return eCDI_MEAS_START_TIME_VIA::GetName(ordinal);
}

namespace eCDI_MEAS_END_TIME_VIA {
enum Enum {
    EndTime,
    Duration
};

static std::string GetName(Enum ordinal) {
    switch (ordinal) {
    case EndTime: return "End Time";
    case Duration: return "Duration";
    }
    return "";
}
}

inline std::string GetCDIEnumValueDescription(eCDI_MEAS_END_TIME_VIA::Enum ordinal)
{
  return eCDI_MEAS_END_TIME_VIA::GetName(ordinal);
}

namespace eCDI_COUPLED_SOLVER {
  enum Enum {
    FlowSolver,
    ConductionSolver
  };

  static std::string GetName(Enum ordinal) {
    switch (ordinal) {
    case FlowSolver: return "Flow Solver";
    case ConductionSolver: return "Conduction Solver";
    }
    return "";
  }
}

inline std::string GetCDIEnumValueDescription(eCDI_COUPLED_SOLVER::Enum ordinal)
{
  return eCDI_COUPLED_SOLVER::GetName(ordinal);
}

typedef struct sCDI_MPRM
{ /* measurement parameters */
#define CDI_CHUNK_TYPE_MPRM CIO_BUILDCCCC('m', 'p', 'r', 'm')
  CIO_CCCC cccc;
  eCDI_MEAS_START_TIME_VIA::Enum start_via;
  std::vector<cdiINT32> monitors;
  cdiINT32 start_time;
  eCDI_MEAS_END_TIME_VIA::Enum end_via;
  cdiINT32 duration;
  cdiINT32 end_time;
  cdiINT32 num_frames;
  cdiINT32 period;
  cdiINT32 period_sync_group_index;
  cdiINT32 average_interval;
  cdiINT32 spacing;
  eCDI_COUPLED_SOLVER::Enum time_rel_solver;
} *CDI_MPRM;

typedef struct sCDI_MPSG
{ /* measurement period sync group */
#define CDI_CHUNK_TYPE_MPSG CIO_BUILDCCCC('m', 'p', 's', 'g')
  CIO_CCCC cccc;
  std::string name;
} *CDI_MPSG;

/* development windows */
#define CDI_CHUNK_TYPE_MDEV CIO_BUILDCCCC('m','d','e','v')
typedef struct sCDI_MDEV
{
  CIO_CCCC cccc;
  double   seg_size; // segment size for development line (in lattice units)
  cdiINT32 icsys;    // local coord system representing triad of development lines
  cdiINT32 ilrf;     // reference frame that development line is defined relative to
} *CDI_MDEV;

enum CDI_MEASTYPE {
  CDI_MEASTYPE_REGION,
  CDI_MEASTYPE_FACE,
  CDI_MEASTYPE_COMPOSITE_REGION,
  CDI_MEASTYPE_COMPOSITE_FACE,
  CDI_MEASTYPE_SAMPLED_FACE,
  CDI_MEASTYPE_PROBE,
  CDI_MEASTYPE_COMPOSITE_SAMPLED_FACE,
  CDI_MEASTYPE_DEVELOPMENT_REGION,
  CDI_MEASTYPE_DEVELOPMENT_FACE,
  CDI_MEASTYPE_PARTICLE_TRAJECTORY,
  CDI_NUM_MEASTYPES
};

enum CDI_MEAS_WINDOW_TYPE {
  CDI_FLUID_WINDOW,
  CDI_SOLID_CONDUCTOR_WINDOW,
  CDI_POROUS_WINDOW,
  CDI_SURFACE_WINDOW
};

class cCDI_BASE_VAR_OPTIONS
{
  /*
   * This is an abstract base class for a list of options. It can be used
   * instead of bit flags, for example in measurements and simulation options.
   * The options are written to the CDI as strings and are strongly typed
   * with sCDI_VAR_OPTION structure.
   * Derived classes should add public static constexpr sCDI_VAR_OPTION
   * variables to define the options and then implement
   * GetAllOptions(), which is used when reading a CDI and undumping a text
   */
protected:
  struct sCDI_VAR_OPTION
  {
    const char* name;

    friend bool operator== (const sCDI_VAR_OPTION& opt1, const sCDI_VAR_OPTION& opt2) {
      return strcmp(opt1.name, opt2.name) == 0;
    }

    friend bool operator< (const sCDI_VAR_OPTION& opt1, const sCDI_VAR_OPTION& opt2) {
      return strcmp(opt1.name, opt2.name) < 0;
    }
  };
public:
  virtual std::set<sCDI_VAR_OPTION> GetAllOptions() const = 0;

  void SetOption(const sCDI_VAR_OPTION& opt);
  void UnsetOption(const sCDI_VAR_OPTION& opt) { m_options.erase(opt); }
  bool GetOption(const sCDI_VAR_OPTION& opt) const { return m_options.count(opt) == 1; }

  bool ReadFromCDI(CDI_INFO cdi_info);
  bool WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(CDI_INFO cdi_info, int depth) const;
  void Undump(CDI_INFO cdi_info);
private:
  bool GetVarOptionByName(const char* varName, sCDI_VAR_OPTION& opt) const;
  std::set<sCDI_VAR_OPTION> m_options;
};

class cCDI_MEAS_STD_VAR_OPTIONS : public cCDI_BASE_VAR_OPTIONS
{
  /*
   * These are the extended standard variables for measurements.
   * Add any new options as a static constexpr sCDI_VAR_OPTION and
   * insert it into the GetAllOptions set.
  */
public:
  static constexpr sCDI_VAR_OPTION STD_RADIATION_VARS{ "STD_RADIATION_VARS" };

  std::set<sCDI_VAR_OPTION> GetAllOptions() const override {
    return { STD_RADIATION_VARS };
  }
};

#define CDI_CHUNK_TYPE_MSTP CIO_BUILDCCCC('m', 's', 't', 'p')
struct sCDI_MSTP
{
  sCDI_MSTP() {
    cccc = CDI_CHUNK_TYPE_MSTP; 
    meas_type = (CDI_MEASTYPE)0;
    standard_mask = (cdiINT32)0;
  }

  CIO_CCCC cccc;

  CDI_MEASTYPE meas_type; // Measurement type

  cdiINT32 standard_mask; /* measurement options */
  // standard_mask is out of bits so use a new variable options class
  cCDI_MEAS_STD_VAR_OPTIONS extended_std_var_options;

  cSRI_MEAS_VARS custom_fluid_options;
  cSRI_MEAS_VARS custom_fluid_particle_options;
  cSRI_MEAS_VARS custom_surface_options;
  cSRI_MEAS_VARS custom_surface_particle_options;
  cSRI_MEAS_VARS custom_porous_options;
  cSRI_MEAS_VARS custom_solid_volume_options;
  cSRI_MEAS_VARS custom_shell_options;
};
typedef sCDI_MSTP* CDI_MSTP;

// Version required to support PowerCASE as a client
cSRI_MEAS_VARS cdi_mstp_vars(
  cdiINT32 cdi_major_version,
  cdiINT32 cdi_minor_version,
  const sCDI_MSTP* mstp,
  CDI_MEAS_WINDOW_TYPE meas_window_type,
  cdiINT32 stp_lattice_type,
  SRI_HT_TYPE ht_type,
  cdiINT32 boolean_is_ht_off_in_powercase,
  cdiINT32 boolean_is_3d,
  cdiINT32 boolean_is_std_meas_mme,
  cdiINT32 boolean_is_density_constant,
  cdiINT32 is_water_vapor_transport,
  const sSRI_CUSTOM_VAR_ID_HELPER& vih,
  cdiINT32 boolean_remove_var = 1,
  cdiINT32 boolean_is_turb = 0,
  cdiINT32 is_Eutectic_Fluid = 0);

// Version used by CDI File consumers.
inline cSRI_MEAS_VARS cdi_mstp_vars(
  CDI_INFO cdi_info,
  const sCDI_MSTP* mstp,
  CDI_MEAS_WINDOW_TYPE meas_window_type,
  cdiINT32 stp_lattice_type,
  SRI_HT_TYPE ht_type,
  cdiINT32 boolean_is_ht_off_in_powercase,
  cdiINT32 boolean_is_3d,
  cdiINT32 boolean_is_std_meas_mme,
  cdiINT32 boolean_is_density_constant,
  cdiINT32 is_water_vapor_transport,
  const sSRI_CUSTOM_VAR_ID_HELPER& vih,
  cdiINT32 boolean_remove_var = 1,
  cdiINT32 boolean_is_turb = 0,
  cdiINT32 is_Eutectic_Fluid = 0)
{
  return cdi_mstp_vars(
    cdi_info->major_version,
    cdi_info->minor_version,
    mstp,
    meas_window_type,
    stp_lattice_type,
    ht_type,
    boolean_is_ht_off_in_powercase,
    boolean_is_3d,
    boolean_is_std_meas_mme,
    boolean_is_density_constant,
    is_water_vapor_transport,
    vih,
    boolean_remove_var,
    boolean_is_turb,
    is_Eutectic_Fluid);
}

int cdi_meas_vars_num_bitmasks(CDI_INFO info);

//
// methods to determine basic meas type from option mask
//
inline cdiBOOLEAN cdi_meas_opt_fluid_meas(cdiINT32 option_mask)
{
  return ( 0 != (option_mask &
                 (CDI_MEAS_OPT_STANDARD_FLUID |
                  CDI_MEAS_OPT_CUSTOM_FLUID |
                  CDI_MEAS_OPT_STANDARD_FLUID_PARTICLE |
                  CDI_MEAS_OPT_CUSTOM_FLUID_PARTICLE)) );
}

inline cdiBOOLEAN cdi_meas_opt_surface_meas(cdiINT32 option_mask)
{
  return ( 0 != (option_mask &
                 (CDI_MEAS_OPT_STANDARD_SURFACE |
                  CDI_MEAS_OPT_CUSTOM_SURFACE |
                  CDI_MEAS_OPT_STANDARD_SURFACE_PARTICLE |
                  CDI_MEAS_OPT_CUSTOM_SURFACE_PARTICLE)) );
}

inline cdiBOOLEAN cdi_meas_opt_shell_meas(cdiINT32 option_mask)
{
  return ( 0 != (option_mask &
                 (CDI_MEAS_OPT_STANDARD_SHELL )) );
}
inline cdiBOOLEAN cdi_meas_opt_porous_meas(cdiINT32 option_mask) // includes "fan" as well as "porous"
{
  return ( 0 != (option_mask &
                 (CDI_MEAS_OPT_STANDARD_POROUS | CDI_MEAS_OPT_CUSTOM_POROUS)));
}

inline cdiBOOLEAN cdi_meas_type_composite_meas(CDI_MEASTYPE meas_type)
{
  return (meas_type == CDI_MEASTYPE_COMPOSITE_REGION ||
          meas_type == CDI_MEASTYPE_COMPOSITE_FACE ||
          meas_type == CDI_MEASTYPE_COMPOSITE_SAMPLED_FACE);
}

inline cdiBOOLEAN cdi_meas_type_sampled_meas(CDI_MEASTYPE meas_type)
{
  return (meas_type == CDI_MEASTYPE_SAMPLED_FACE ||
          meas_type == CDI_MEASTYPE_COMPOSITE_SAMPLED_FACE);
}

inline cdiBOOLEAN cdi_meas_type_development_meas(CDI_MEASTYPE meas_type)
{
  return (meas_type == CDI_MEASTYPE_DEVELOPMENT_REGION ||
          meas_type == CDI_MEASTYPE_DEVELOPMENT_FACE);
}

inline cdiBOOLEAN cdi_meas_opt_average_mme_meas(cdiINT32 option_mask)
{
   return ( 0 != (option_mask & (CDI_MEAS_OPT_AVERAGE_MME)));
}

inline cdiBOOLEAN cdi_meas_opt_no_surfel_merger_meas(cdiINT32 option_mask)
{
  return (0 != (option_mask & (CDI_MEAS_OPT_NO_SURFEL_MERGER)));
}

inline cdiBOOLEAN cdi_meas_opt_rwnc_meas(cdiINT32 option_mask)
{
  return (0 != (option_mask & (CDI_MEAS_OPT_RWNC_MEAS)));
}
enum CDI_MEAS_FACE_GROUPING_TYPE {
  CDI_MEAS_FACE_GROUP_SINGLE,
  CDI_MEAS_FACE_GROUP_MULTIPLE
};

/* MFAC: face measurement control */
typedef struct sCDI_MFAC {
#define CDI_CHUNK_TYPE_MFAC CIO_BUILDCCCC('m', 'f', 'a', 'c')
  CIO_CCCC cccc;
  CDI_MEAS_FACE_GROUPING_TYPE grouping;
  cdiINT32 merge_to_input;     /* 1 => merge to input facet, 0 => standard merger */
} *CDI_MFAC;

/* Composite force meas window filter bounds */
#define CDI_CHUNK_TYPE_MFLT CIO_BUILDCCCC('m', 'f', 'l', 't')
typedef struct sCDI_MFLT
{
  CIO_CCCC cccc;
  double min_pressure;
  double max_pressure;
} *CDI_MFLT;

sCDI_MFLT* cdi_read_mflt(CDI_INFO cdi_info);

inline void cdi_inner_chunk_read_mflt(cdiINT32 &index
                                    , const char* chunkName
                                    , CDI_INFO info
                                    , sCDI_MFLT** value)
{
  if (cdi_cio_descend_with_error(info->cio_info, "mflt") == 0)
  {
    *value = cdi_read_mflt(info);
  }
  cdi_cio_ascend_with_error(info->cio_info, chunkName, ++index);
}



/* Composite moments meas window reference point */
#define CDI_CHUNK_TYPE_MREF CIO_BUILDCCCC('m', 'r', 'e', 'f')
typedef struct sCDI_MREF
{
  CIO_CCCC cccc;
  double reference_point[3];
} *CDI_MREF;

typedef struct sCDI_PTYP
{ /* physics type */
#define CDI_CHUNK_TYPE_PTYP CIO_BUILDCCCC('p', 't', 'y', 'p')
  CIO_CCCC cccc;
  cdiINT32 type; /* the physics type (CDI_PHYS_TYPE) */
  cdiINT32 n_integer; /* number of integer values */
  cdiINT32 n_continuous; /* number of continuous dyn parms */
  cdiINT32 n_initial; /* number of initial dyn parms */
} *CDI_PTYP;

typedef struct sCDI_CVDP
{ /* continuous dynamical parameters */
#define CDI_CHUNK_TYPE_CVDP CIO_BUILDCCCC('c', 'v', 'd', 'p')
  CIO_CCCC cccc;
  cdiINT32 type; /*the variable type,(CDI_VAR_ID)*/
  double value; /* the continuous dynamic value */
} *CDI_CVDP;

typedef struct sCDI_EQDP
{ /* continuous dynamical parameters defined by equation */
#define CDI_CHUNK_TYPE_EQDP CIO_BUILDCCCC('e', 'q', 'd', 'p')
  CIO_CCCC cccc;
  cdiINT32 type; /*the variable type,(CDI_VAR_ID)*/
  cdiINT32 var_name_length;
  char* var_name;
} *CDI_EQDP;

/* User-Defined Scalar Transport ( no struct, re-using CVDP) */
//#define CDI_CHUNK_TYPE_UDST CIO_BUILDCCCC('u', 'd', 's', 't')

struct sCDI_UDST
{
#define CDI_CHUNK_TYPE_UDST CIO_BUILDCCCC('u','d','s','t')
  CIO_CCCC   cccc;
  cdiINT32   n_cvdp;
  cdiINT32   n_eqdp;
  CDI_CVDP* cvdp;
  CDI_EQDP* eqdp;

  BOOLEAN HasVariable(cdiINT32 varType) const;
  BOOLEAN IsSetToEqVar(cdiINT32 varType) const;
  dFLOAT GetConstValue(cdiINT32 varType) const;
  cSTRING GetEqVarName(cdiINT32 varType) const;
};
typedef sCDI_UDST* CDI_UDST;

#define  CDI_CHUNK_TYPE_COUP CIO_BUILDCCCC('c', 'o', 'u', 'p')

typedef struct sCDI_HXCH_BASE
{
  CIO_CCCC cccc;
  char *name;                       /* heat exchanger name */
  char *part_name;                  /* name of the HX's part, same as above for part-based HXs */
  cdiINT32 type;                    /* the exchanger type (radiator, etc.) */
  cdiINT32 tool;                    /* 1D tool (powercool, etc.) */
  cdiINT32 flags;                   /* tells which parts of this struct to use */
  cdiINT32 table_csys_index;        /* csys chunk index used for table */
  cdiINT32 medium_csys_index;       /* csys chunk index used for porous medium */
  cdiINT32 inlet_face_index;        /* inlet measurement face chunk index */
  cdiINT32 outlet_face_index;       /* outlet measurement face chunk index */
  cdiINT32 coolant_entry_face_index;/* coolant entry face chunk index */
  cdiINT32 top_exchanger_face_index;/* top exchanger face chunk index */
  cdiINT32 inlet_facet_offset; /* inlet measurement facet index offset */
  cdiINT32 outlet_facet_offset;/* outlet measurement facet index offset */
  cdiINT32 inlet_meas_index;   /* inlet measurement chunk index */
  cdiINT32 outlet_meas_index;  /* outlet measurement chunk index */
  cdiINT32 heat_gen_meas_index;/* heat generation measurement chunk index */
  cdiINT32 table_index;        /* data table chunk index */
  cdiINT32 adiabatic_index;    /* adiabatic medium layer chunk index (into FLUD list) */
  cdiINT32 medium_index;       /* porous medium chunk index (into FLUD list) */

  /* upstream_index and n_stages should really not be in the base class (for instance,
   * they are not relevant to an AMESim heat exchanger), but leaving them in the base
   * class simplified read and write methods because of the historical order in which
   * fields of this structure were written.
   */
  cdiINT32 upstream_index;     /* optional upstream exchanger */
  cdiINT32 n_stages;           /* number of coolant passes */
  cdiINT32 n_y_divisions;      /* number of coolant-direction divisions */
  cdiINT32 *n_pass_divisions;  /* one # divisions value for each pass */
  double x_len;                /* length of x-oriented depth */
  double y_len;                /* length of y-oriented depth */
  double z_len;                /* length of z-oriented depth */
} *CDI_HXCH_BASE;

typedef struct sCDI_HXCH : public sCDI_HXCH_BASE
{           /* heat exchanger */
#define  CDI_CHUNK_TYPE_HXCH CIO_BUILDCCCC('h', 'x', 'c', 'h')
  double experiment_area;               /* Area of air inlet face used in experiment */
  double percent_flow;                  /* percent upstream flow */
  double mass_flow_rate;
  double specific_heat;                 /* user specified coolant specific heat */
  double heat_rejection;                /* either heat rejection or */
  double entry_temp;                    /* entry temperature is specified */
  double experiment_ref_temp;           /* experiment reference temperature */
  double min_air_flow;                  /* minimum input air mass flow rate */
  double max_air_flow;                  /* maximum input air mass flow rate */
  double kc_coeff;                      /* sandwich formula Kc coefficient */
  double kh_coeff;                      /* sandwich formula Kh coefficient */
  double alpha_coeff;                   /* sandwich formula alpha coefficient */
  double beta_coeff;                    /* sandwich formula beta coefficient */
  double d_coeff;                       /* sandwich formula D coefficient */
  char *data_string;                    /* exchanger data string */
  double exp_coolant_sp_heat;           /* experimental coolant specific heat */
  double exp_coolant_viscosity;         /* experimental coolant dynamic viscosity */
  double exp_coolant_thermal_conductivity;  /* experimental coolant thermal conductivity */
  double user_coolant_viscosity;         /* user specified coolant dynamic viscosity */
  double user_coolant_thermal_conductivity;  /* user specified coolant thermal conductivity */
  double experimental_height;           /* hx height in the experiment */
  double experimental_width;            /* hx width in the experiment */
  double experimental_depth;            /* hx depth in the experiment */
  sCDI_UDST udstValues;                 /* user-defined scalar transport values */
} *CDI_HXCH;

typedef struct sCDI_AMHX : public sCDI_HXCH_BASE
{           /* AMESim heat exchanger */
#define  CDI_CHUNK_TYPE_AMHX CIO_BUILDCCCC('a', 'm', 'h', 'x')
  char *model_filename;                 /* model name of the group of hxs and cdsrs */
  char *absolute_model_filename;        /* model name of the group of hxs and cdsrs */
  char *amesim_hx_name;                 /* name of heat exchanger in AMESim */
  sCDI_UDST udstValues;                 /* user-defined scalar transport values */
} *CDI_AMHX;

/* Definition of CDI_HXCH flags */
#define CDI_HXCH_USE_HEAT_REJECTION       (1 << 0) /* vs. using entry_temp */
#define CDI_HXCH_USE_COEFFICIENTS         (1 << 1) /* vs. using data_string */
#define CDI_HXCH_USE_SUTHERLAND           (1 << 2) /* Sutherland Correction */
/* In old (pre-release) CDI files air mass flow in HXCH was written in mks.
   We've wasted the following flag in the HXCH chunk for all new CDI file to
   indicate that air mass flow is now written in lattice units. */
#define CDI_HXCH_LATTICE_AIR_MASS_FLOW    (1 << 3)
#define CDI_HXCH_IS_CURVED                (1 << 4)
#define CDI_HXCH_HAS_HEAT_GEN_MEAS        (1 << 5)
/* Indicate HX is to have open-shell interface surfaces at inlet/outlet */
#define CDI_HXCH_HAS_PM_INTERFACE         (1 << 6) 

typedef struct sCDI_IVDP
{ /* integer dynamical parameters */
#define CDI_CHUNK_TYPE_IVDP CIO_BUILDCCCC('i', 'v', 'd', 'p')
  CIO_CCCC cccc;
  cdiINT32 type; /*the variable type,(CDI_VAR_ID)*/
  cdiINT32 value; /* the integer value */
} *CDI_IVDP;

typedef struct sCDI_BSDP
{ /* boundary seeding indicator dynamical parameters */
#define CDI_CHUNK_TYPE_BSDP CIO_BUILDCCCC('b', 's', 'd', 'p')
  CIO_CCCC cccc;
  cdiINT32 type; /*the variable type,(CDI_VAR_ID)*/
  double value; /* the default value if no meas surfel is found*/
} *CDI_BSDP;

typedef struct sCDI_PNTS
{ /* continuous dynamical parameters */
#define CDI_CHUNK_TYPE_PNTS CIO_BUILDCCCC('p', 'n', 't', 's')
  CIO_CCCC cccc;
  cdiINT32 num_points;
  cdiINT32 is_closed;
  cdiINT32 dim;
  double *points; /* point array, point[i] starts at point+i*3 */
} *CDI_PNTS;

struct sCDI_VECOF_PNTS   // STL-friendly version of above
{                        // Writes exactly the same data chunk to CDI
  CIO_CCCC GetChunkType() const
  { return CDI_CHUNK_TYPE_PNTS; }

  void WriteToCDI(CDI_INFO cdi_info) const;
  void ReadFromCDI(CDI_INFO cdi_info);

  // You can infer num_points from point.size() / dim;
  cdiINT32 is_closed;
  cdiINT32 dim;
  std::vector<double> points; /* point vector, point[i] starts at point+i*3 */

  void ConvertToPRI(asINT32 point_index, PRI::cPOINT &pri_point) const {
    pri_point.SetPosition(PRI::VECTOR3F(points[point_index * 3],
                                   points[point_index * 3 + 1],
                                   points[point_index * 3 + 2]));
  }

};

typedef struct sCDI_PLRF
{ /* intermediate property lattice */
#define CDI_CHUNK_TYPE_PLRF CIO_BUILDCCCC('p', 'l', 'r', 'f')
  CIO_CCCC cccc;
  /* LATER */
} *CDI_PLRF;

typedef struct sCDI_IVAL
{ /* initial conditions */
#define CDI_CHUNK_TYPE_IVAL CIO_BUILDCCCC('i', 'v', 'a', 'l')
  CIO_CCCC cccc;
  CDI_CVDP cdyn_vals; /* the array of continuous dynamic values */
  CDI_PLRF ipls; /* the array of intermed property lattices */
} *CDI_IVAL;

typedef struct sCDI_DPRM
{ /* dynamic parameters */
#define CDI_CHUNK_TYPE_DPRM CIO_BUILDCCCC('d', 'p', 'r', 'm')
  CIO_CCCC cccc;
  CDI_CVDP cdyn_vals; /* the array of continuous dynamic values */
  CDI_BSDP bsdps;     /* the array of boundary seeding indicator dynamical values */
  CDI_PLRF ipls;      /* the array of intermed property lattices */
} *CDI_DPRM;


/* non-inertial reference frame (no struct provided since sCDI_DPRM
                                 should be sufficient to store a fram) */
#define CDI_CHUNK_TYPE_FRAM CIO_BUILDCCCC('f', 'r', 'a', 'm')

/* non-inertial reference frames (no need for struct, only used to mark
                                  beginning of nirfs) */
#define CDI_CHUNK_TYPE_NIRF CIO_BUILDCCCC('n', 'i', 'r', 'f')

/* gravity/buoyancy force (no struct provided since sCDI_DPRM should
                           be sufficient to store a grvf) */
#define CDI_CHUNK_TYPE_GRVF CIO_BUILDCCCC('g', 'r', 'v', 'f')

/* gravity/buoyancy forces (no need for struct, only used to mark beginning of forces) */
#define CDI_CHUNK_TYPE_GRAV CIO_BUILDCCCC('g', 'r', 'a', 'v')

struct sCDI_BFPR
{ /* body force parameters */
#define CDI_CHUNK_TYPE_BFPR CIO_BUILDCCCC('b', 'f', 'p', 'r')
  CIO_CCCC   cccc;
  cdiINT32   n_cvdp;
  cdiINT32   n_eqdp;
  CDI_CVDP   *cvdp;
  CDI_EQDP   *eqdp;

  BOOLEAN HasVariable(cdiINT32 varType) const;
  BOOLEAN IsSetToEqVar(cdiINT32 varType) const;
  dFLOAT GetConstValue(cdiINT32 varType) const;
  cSTRING GetEqVarName(cdiINT32 varType) const;
};
typedef sCDI_BFPR* CDI_BFPR;

typedef struct sCDI_BFDF
{ /* body force definition */
#define CDI_CHUNK_TYPE_BFDF CIO_BUILDCCCC('b', 'f', 'd', 'f')
  CIO_CCCC   cccc;
  CDI_RGNS   rgns;
  CDI_BFPR   bfpr;
} *CDI_BFDF;

struct sCDI_BODF
{ /* body force chunk */
#define CDI_CHUNK_TYPE_BODF CIO_BUILDCCCC('b', 'o', 'd', 'f')
  CIO_CCCC   cccc;
  cdiINT32   n_bfdf;
  CDI_BFDF   bfdf;

  // Gets the BFPR chunk info associated with the specified region. NULL if the
  // region doesn't have any body forces specified.
  sCDI_BFPR* GetBfpr(cdiINT32 regionIndex) const;
};
typedef sCDI_BODF* CDI_BODF;

typedef struct sCDI_PRDF
{ /* default precedence values */
#define CDI_CHUNK_TYPE_PRDF CIO_BUILDCCCC('p', 'r', 'd', 'f')
  CIO_CCCC cccc;
  cdiINT32 default_scale_precedence;
  cdiINT32 default_physics_precedence;
} *CDI_PRDF;

typedef struct sCDI_PREC
{ /* precedence value */
#define CDI_CHUNK_TYPE_PREC CIO_BUILDCCCC('p', 'r', 'e', 'c')
  CIO_CCCC cccc;
  cdiINT32 precedence;
} *CDI_PREC;

typedef struct sCDI_PHYS
{ /* physics */
#define CDI_CHUNK_TYPE_PHYS CIO_BUILDCCCC('p', 'h', 'y', 's')
  CIO_CCCC cccc;
  sCDI_PTYP physics_type; /* physics type */
  CDI_IVDP integer; /* array of integer parameters */
  CDI_DPRM continuous; /* array of dynamic parameters */
  CDI_IVAL initial; /* array of initial conditions */
  sCDI_NAME name; /* the physics propset name */
} *CDI_PHYS;

typedef struct sCDI_PHYSICS_VARIABLE {
  char    *name;         // name from user eqns (NULL if not defined via eqns)
  double  value;         // only relevant if name is NULL
} *CDI_PHYSICS_VARIABLE;

typedef struct sCDI_PHYSICS_DESCRIPTOR {
  char                 *name;
  sCDI_PHYS_TYPE_DESCRIPTOR *phys_type_desc;
  cdiINT16             n_parameters;            // copied from phys_type_desc (n_continuous_dp)
  cdiINT16             n_initial_conditions;    // copied from phys_type_desc
  CDI_PHYSICS_VARIABLE parameters; // vector of parameters
  CDI_PHYSICS_VARIABLE initial_conditions; // vector of initial conditions
} *CDI_PHYSICS_DESCRIPTOR;

typedef struct sCDI_LRF : public sCDI_PHYSICS_DESCRIPTOR {

  double cyl_begin[3];
  double cyl_end[3];

  double half_plane_dir[3];
  CDI_LRF_TYPE lrf_type;
  sCDI_PNTS pnts;
  cBOOLEAN has_transonic_flow;
  cBOOLEAN  is_velocity_via_torque;
} *CDI_LRF;

/* FACD: face-dist record */
typedef struct sCDI_FACD {
  cdiINT32 n_face_name_char;
  char*  face_name;            /* face name */
  double front_offset;         /* front offset distance */
  double back_offset;          /* back offset distance */
  cdiINT32 region_index;       /* region index (of the offset region - not the region owning the face*/
  cdiINT32 facet_side;         /* offset from 0: both front+back of facets, 1: only front of facets, 2: only back of facets */
} *CDI_FACD;

/* FDLT: list of face and distance records*/
typedef struct sCDI_FDLT {
#define CDI_CHUNK_TYPE_FDLT CIO_BUILDCCCC('f', 'd', 'l', 't')
    CIO_CCCC cccc;
    cdiINT32 n_facd;             /* the number of records */
    CDI_FACD facd;              /* the array of rfd records */
} *CDI_FDLT;

/* FLST: list of faces */
typedef struct sCDI_FLST {
#define CDI_CHUNK_TYPE_FLST CIO_BUILDCCCC('f', 'l', 's', 't')
  CIO_CCCC cccc;
  cdiINT32  n_face;            /* the number of faces */
  cdiINT32  *face;             /* the array of faces (ids) */
} *CDI_FLST;

class cCDI_SEGMENT_REF;
class cCDI_PARTIAL_PART_REF;
class cCDI_PARTITIONS;
class cCDI_PARTITION;
class cCDI_SEGMENT;

class cCDI_GEOMETRY_REF
{
public:
  enum eGEOMETRY_TYPE { Segment, Part, PartialPart, Face };

  static std::string GetName(eGEOMETRY_TYPE entity_type) {
    switch (entity_type) {
      case eGEOMETRY_TYPE::Face: return "Face";
      case eGEOMETRY_TYPE::Part: return "Part";
      case eGEOMETRY_TYPE::PartialPart: return "PartialPart";
      case eGEOMETRY_TYPE::Segment: return "Segment";
      default: return "";
    }
  }
  
  cCDI_GEOMETRY_REF(eGEOMETRY_TYPE geomSelectionType)
    : m_geometrySelectionType(geomSelectionType), m_partitionIndex(0) {}
  std::vector<cdiINT32> face_list;
  std::vector<cdiINT32> rgn_list;
  std::vector<cCDI_PARTIAL_PART_REF> partial_part_list;
  std::vector<cCDI_SEGMENT_REF> segment_list;
  std::set<cdiINT32> prohibited_selections;  // indices of geometry type that could not be selected

  eGEOMETRY_TYPE GeometrySelectionType() const { return m_geometrySelectionType; }
  void GeometrySelectionType(eGEOMETRY_TYPE geomType) { m_geometrySelectionType = geomType; }

  cdiINT32 PartitionIndex() const { return m_partitionIndex; }
  void PartitionIndex(cdiINT32 partitionIndex) { m_partitionIndex = partitionIndex; }

  bool IsEmpty() const { return (face_list.empty() && rgn_list.empty() && partial_part_list.empty() && segment_list.empty()); }

  std::vector<cdiINT32> ExpandSelection(const cCDI_PARTITIONS& partitions, bool recursive = true) const;
  std::vector<cdiINT32> ExpandSelection(const cCDI_PARTITIONS& partitions, eGEOMETRY_TYPE geomSelectionType, bool recursive = true) const;

#define CDI_CHUNK_TYPE_GMRF             CIO_BUILDCCCC('g', 'm', 'r', 'f')
  static CIO_CCCC GetGeometryRefChunkType() { return CDI_CHUNK_TYPE_GMRF; }
  CIO_CCCC GetChunkType() const { return GetGeometryRefChunkType(); }

  bool ReadFromCDI(CDI_INFO cdi_info);
  bool WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(CDI_INFO cdi_info, int depth) const;
  void Undump(CDI_INFO cdi_info);

private:
  eGEOMETRY_TYPE m_geometrySelectionType;
  cdiINT32 m_partitionIndex; // Needed if the user wants full names of regions/faces relative to a partition
};

class cCDI_GEOM_SELECTION_TREE
{
public:
  cCDI_GEOM_SELECTION_TREE(cdiINT32 partitionIndex, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geomSelectionType);

  cCDI_GEOMETRY_REF::eGEOMETRY_TYPE GeometrySelectionType() const { return m_selections.GeometrySelectionType(); }

  std::vector<cdiINT32> ExpandSelection(const cCDI_PARTITIONS& partitions) const;
  std::vector<cdiINT32> ExpandSelection(const cCDI_PARTITIONS& partitions, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geomSelectionType) const;

  cdiINT32 PartitionIndex() const { return m_partitionIndex; }
  void PartitionIndex(cdiINT32 partitionIndex) { m_partitionIndex = partitionIndex; }

#define CDI_CHUNK_TYPE_GEOS             CIO_BUILDCCCC('g', 'e', 'o', 's')
  static CIO_CCCC GetGeometrySelectionChunkType() { return CDI_CHUNK_TYPE_GEOS; }
  CIO_CCCC GetChunkType() const { return GetGeometrySelectionChunkType(); }

  bool ReadFromCDI(CDI_INFO cdi_info);
  bool WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(CDI_INFO cdi_info, int depth) const;
  void Undump(CDI_INFO cdi_info);
  // Read/Write string support for measurement files
  void PrintToStream(std::ostream &output, int depth) const;
  bool ReadFromStream(std::istream &input);

  // Geometry selections and exclusion
  cCDI_GEOMETRY_REF m_selections;
  cCDI_GEOMETRY_REF m_exclusions;

private:
  bool IsSelected(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geometryType, cdiINT32 geomIndex) const;
  bool IsExcluded(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geometryType, cdiINT32 geomIndex) const;
  void ProcessPartAndLowerLevelSelectionsDirectly(const cCDI_PARTITION& selectionPartition,
                                                  cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geomSelectionType,
                                                  std::vector<cdiINT32> &selections) const;
  void ExpandSelectionSegment(const cCDI_SEGMENT& segment,
                              bool isParentSelected,
                              cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geomSelectionType,
                              std::vector<cdiINT32> &selections,
                              std::map<cdiINT32, cdiINT32> &partialPartFaceSumMap) const;
  cdiINT32 m_partitionIndex;
  cdiINT32 m_printMajor;
  cdiINT32 m_printMinor;
};

// Some helper functions for geometry selections
bool cdi_is_valid_geometry_chunk(CDI_INFO cdi_info);
bool cdi_read_geometry_selection(CDI_INFO cdi_info, cCDI_GEOMETRY_REF &gmrf);
bool cdi_read_geometry_selection(CDI_INFO cdi_info, cCDI_GEOM_SELECTION_TREE &geos);
std::vector<cdiINT32> cdi_get_effective_selections(CDI_INFO cdi_info, const cCDI_PARTITIONS& partitions);
cCDI_GEOMETRY_REF::eGEOMETRY_TYPE cdi_get_geometry_type_from_entity_name(const std::string& entity_name);

// Angle type for mesh curvature VR
namespace eCDI_ANGLE_TYPE  {
enum Enum{
    Curvature,
    SharpAngle
};

static std::string GetName(Enum ordinal) {
    switch (ordinal) {
      case Curvature: return "Curvature";
      case SharpAngle: return "Sharp Angle";
    }
    return "";
}
}

inline std::string GetCDIEnumValueDescription(eCDI_ANGLE_TYPE::Enum ordinal)
{
  return eCDI_ANGLE_TYPE::GetName(ordinal);
}

// Voxel Size Via for mesh curvature VR
namespace eCDI_VOXEL_SIZE_VIA  {
enum Enum{
  VRLevel,
  Resolution
};

static std::string GetName(Enum ordinal) {
  switch (ordinal) {
  case VRLevel: return "VR Level";
  case Resolution: return "Resolution";
  }
  return "";
}
}

inline std::string GetCDIEnumValueDescription(eCDI_VOXEL_SIZE_VIA::Enum ordinal)
{
  return eCDI_VOXEL_SIZE_VIA::GetName(ordinal);
}


/* FARG: face angle-range record */
typedef struct sCDI_FARG {
#define CDI_CHUNK_TYPE_FARG CIO_BUILDCCCC('f', 'a', 'r', 'g')
  eCDI_ANGLE_TYPE::Enum type;   /* 0: curvature, 1: sharp-angle */
  sCDI_PARM min_param;          /* Param = Min Curvature for curvature, Param = Min Angle for Sharp Angle */
  sCDI_PARM max_param;          /* Param = Max Curvature for curvature, Param = Max Angle for Sharp Angle */
  eCDI_VOXEL_SIZE_VIA::Enum voxel_size_via; /* 0: VR level, 1: Resolution for curvature */
  double resolution;        /* > 0.0: for curvature type */
  cdiINT32 scale;           /* Scale value for the selected VR level */
  cdiINT32 thickness;       /* vr thickness in local voxels (>= 2) */
} *CDI_FARG;

/* GFAR: table of angle ranges and mesh curvature VR control parameters */
typedef struct sCDI_GFAR {
#define CDI_CHUNK_TYPE_GFAR CIO_BUILDCCCC('g', 'f', 'a', 'r')
  CIO_CCCC cccc;
  cdiINT32 n_farg;            /* the number of angle-range records */
  std::vector<sCDI_FARG> farg;  /* the vector of angle-range records */
} *CDI_GFAR;

/* GMCV: mesh curvature VR control */
typedef struct sCDI_GMCV
{ /* grid mesh curvature VR control */
#define CDI_CHUNK_TYPE_GMCV CIO_BUILDCCCC('g', 'm', 'c', 'v')
  CIO_CCCC cccc;
  CDI_FLST face_list; /* the face list (where control is applied)*/
  sCDI_GFAR angle_ranges; /* angle range table */
  cCDI_GEOMETRY_REF geom_ref{ cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face }; /*  the face references (where control is applied)*/
  cCDI_GEOM_SELECTION_TREE geom_selection            /* the face references (where control is applied)*/
    { 0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face };
} *CDI_GMCV;

/* GAPD: gap VR parameter data */
typedef struct sCDI_GAPD {
#define CDI_CHUNK_TYPE_GAPD CIO_BUILDCCCC('g', 'a', 'p', 'd')
  CIO_CCCC cccc;
  cdiINT32 n_voxels;  /* requested number of voxels across any gap found */
  sCDI_PARM gap_size;    /* gap search size */
  cdiINT32 scale;        /* Scale value for the selected VR level */
} *CDI_GAPD;

/* GGAP: gap VR control */
typedef struct sCDI_GGAP
{ /* grid gap VR control */
#define CDI_CHUNK_TYPE_GGAP CIO_BUILDCCCC('g', 'g', 'a', 'p')
  CIO_CCCC cccc;
  sCDI_NAME name;     /* the gap VR control name */
  cCDI_GEOM_SELECTION_TREE geom_selection            /* the face references (where control is applied)*/
    { 0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face };
  sCDI_GAPD gapd;     
} *CDI_GGAP;

typedef struct sCDI_PHRG
{ /* physics regions */
#define CDI_CHUNK_TYPE_PHRG CIO_BUILDCCCC('p', 'h', 'r', 'g')
  CIO_CCCC cccc;
  sCDI_RGNS rgn_list; /* the region list (where prop applied)*/
  sCDI_PHYS physics; /* the physics which comprise the prop */
  sCDI_PREC priority;           /* the physics region precedence */
  cCDI_GEOMETRY_REF geom_ref{ cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part }; /* the geometry list (where prop applied)*/
} *CDI_PHRG;

typedef struct sCDI_GSCC
{ /* grid scale value*/
#define CDI_CHUNK_TYPE_GSCC CIO_BUILDCCCC('g', 's', 'c', 'c')
  CIO_CCCC cccc;
  cdiINT32 value; /* the numerical grid scale value */
} *CDI_GSCC;

typedef struct sCDI_GSCL
{ /* grid scale */
#define CDI_CHUNK_TYPE_GSCL CIO_BUILDCCCC('g', 's', 'c', 'l')
  CIO_CCCC cccc;
  sCDI_NAME name; /* the gridscale propset name */
  sCDI_RGNS rgn_list; /* the region list (where grid applied)*/
  sCDI_GSCC scale; /* the grid scale value chunk */
  sCDI_PREC priority;           /* the grid region precedence */
  cCDI_GEOMETRY_REF geom_ref{ cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part }; /* the geometry list (where grid applied)*/
} *CDI_GSCL;

typedef struct sCDI_GFDS
{ /* grid face offset scale */
#define CDI_CHUNK_TYPE_GFDS CIO_BUILDCCCC('g', 'f', 'd', 's')
  CIO_CCCC cccc;
  sCDI_NAME name; /* the grid face offset propset name */
  sCDI_FDLT fd_list; /* the face-distance list (where grid applied)*/
  sCDI_GSCC scale; /* the grid scale value chunk */
  sCDI_PREC priority;           /* the grid face-offset precedence */
} *CDI_GFDS;

/* PRBE: probe definition chunk */
typedef struct sCDI_PRBE {
#define CDI_CHUNK_TYPE_PRBE CIO_BUILDCCCC('p', 'r', 'b', 'e')
    CIO_CCCC cccc;
    double  x;                 /* x position */
    double  y;                 /* y position */
    double  z;                 /* z position: 0 in 2d */
    double  diam;              /* diameter */
} *CDI_PRBE;

/* PRTJ: particle tracking definition chunk */
enum CDI_PRTJ_HIT_PTS_TYPE {
  CDI_HIT_POINTS_NONE,           // hit_points ==  None
  CDI_HIT_POINTS_ALL_HITS,       // hit_points ==  All Hits
  CDI_HIT_POINTS_ONLY_ELIGIBLE   // hit_points ==  Only Hits of Particles Eligible for Trajectory Recording
};

typedef struct sCDI_PRTJ {
#define CDI_CHUNK_TYPE_PRTJ CIO_BUILDCCCC('p', 'r', 't', 'j')
  CIO_CCCC cccc;
  sCDI_PARM trajectories;      // 0 or 1. Particle trajectories
  sCDI_PARM recordVel;         // 0 or 1. Record velocities
  sCDI_PARM dynTrajDecimation; // 0 or 1. Dynamic Trajectory Decimation
  sCDI_PARM decimErrortol;     // Decimation Error Tolerance
  sCDI_PARM hitPoints;         // Int selecting hit_points type enum
  sCDI_PARM adherParticles;    // 0 or 1. Adhering Particles
  sCDI_PARM recordNormImpulse; // 0 or 1. Record Normal Impulse
  sCDI_PARM frac_trajectory_recording;  // Fraction of Particles Eligible for Trajectory Recording
                                        // Added at end of 'prtj' chunk for v5.3 so that 5.4a exe's will
                                        // have forward compatibility with 5.4b-produced files.
} *CDI_PRTJ;

/* MENT: measurement entity chunk */
typedef struct sCDI_MENT {
#define CDI_CHUNK_TYPE_MENT CIO_BUILDCCCC('m', 'e', 'n', 't')
    CIO_CCCC cccc;
    cdiINT32  options;           /* OR of CDI_MENT_... flags */
} *CDI_MENT;

typedef struct sCDI_MESR
{ /* measurement window */
#define CDI_CHUNK_TYPE_MESR CIO_BUILDCCCC('m', 'e', 's', 'r')
  CIO_CCCC cccc;
  CDI_NAME file_name;       // the measurement window file name
  CDI_RGNS rgn_list;        // the region list (actually only one used)
  CDI_PRBE probe;           // the probe
  sCDI_PRTJ* prtclTrajec;     // Particle Trajectory Meas
  CDI_FLST face_list;       // the face list (actually only one used)
  cCDI_GEOM_SELECTION_TREE* geom_selection; // the geometries selected in the measurement

  CDI_MPRM params;          // the measurement parameters
  CDI_MSTP options;         // measurement options
  CDI_MFLT force_filter;    // filter for composite force meas windows
  CDI_MREF rp;              // reference poishnt for composite moment meas windows
  CDI_MFAC face_meas;       // face control
  CDI_MDEV dev_win;         // development window

} *CDI_MESR;

typedef struct sCDI_FLUD
{ /* fluid partition*/
#define CDI_CHUNK_TYPE_FLUD CIO_BUILDCCCC('f', 'l', 'u', 'd')
  CIO_CCCC cccc;
  CDI_PHRG phys_regions; /* the array of physics regions */
} *CDI_FLUD;

typedef struct sCDI_SOLD
{ /* solid partition*/
#define CDI_CHUNK_TYPE_SOLD CIO_BUILDCCCC('s', 'o', 'l', 'd')
  CIO_CCCC cccc;
  CDI_PHRG phys_regions; /* the array of physics regions */
} *CDI_SOLD;

typedef struct sCDI_HCSD
{ /* heat conducting solid */
#define CDI_CHUNK_TYPE_HCSD CIO_BUILDCCCC('h', 'c', 's', 'd')
  CIO_CCCC cccc;
  CDI_PHRG phys_regions; /* the array of physics regions */
} *CDI_HCSD;

typedef struct sCDI_IHCS
{ /* inverted heat conducting solid partition */
#define CDI_CHUNK_TYPE_IHCS CIO_BUILDCCCC('i', 'h', 'c', 's')
  CIO_CCCC cccc;
  CDI_PHRG phys_regions; /* the array of physics regions */
} *CDI_IHCS;

typedef struct sCDI_HCSL
{ /* Heat Conducting Layer*/
#define CDI_CHUNK_TYPE_HCSL CIO_BUILDCCCC('h', 'c', 's', 'l')
  CIO_CCCC cccc;
  CDI_PHYS layer_props; /* the layer properties */
} *CDI_HCSL;

typedef struct sCDI_HCSH
{ /* Heat Conducting Shell*/
#define CDI_CHUNK_TYPE_HCSH CIO_BUILDCCCC('h', 'c', 's', 'h')
  CIO_CCCC cccc;
  std::string surf_name;        /* the name of surface (or partition) shell is applied to */
  cdiINT32 n_layers;          /* the number of layers */
  CDI_HCSL hcsl;              /* the array of shell layers */
} *CDI_HCSH;

/* set of heat conducting solid shells */
#define CDI_CHUNK_TYPE_HCSS CIO_BUILDCCCC('h', 'c', 's', 's')

namespace eCDI_SOLID_MATERIAL_TYPE {
  enum Enum {
    Isotropic,
    AnisotropicVolumetric,
    AnisotropicShell
  };

  static std::string GetName(Enum ordinal) {
    switch (ordinal) {
    case Isotropic: return "Isotropic";
    case AnisotropicVolumetric: return "Anisotropic Volumetric";
    case AnisotropicShell: return "Anisotropic Shell";
    }
    return "";
  }
}

inline std::string GetCDIEnumValueDescription(eCDI_SOLID_MATERIAL_TYPE::Enum ordinal)
{
  return eCDI_SOLID_MATERIAL_TYPE::GetName(ordinal);
}

namespace eCDI_SOLID_ANISOTROPY_TYPE {
  enum Enum {
    None,
    Cartesian,
    Cylindrical
  };

  static std::string GetName(Enum ordinal) {
    switch (ordinal) {
    case None: return "None";
    case Cartesian: return "Cartesian";
    case Cylindrical: return "Cylindrical";
    }
    return "";
  }
}

inline std::string GetCDIEnumValueDescription(eCDI_SOLID_ANISOTROPY_TYPE::Enum ordinal)
{
  return eCDI_SOLID_ANISOTROPY_TYPE::GetName(ordinal);
}

typedef struct sCDI_SCMT
{ /* solid conducting material */
#define CDI_CHUNK_TYPE_SCMT CIO_BUILDCCCC('s', 'c', 'm', 't')
  CIO_CCCC cccc;
  std::string name;
  eCDI_SOLID_MATERIAL_TYPE::Enum material_type;     // Material type
  sCDI_PARM density;                                // density
  sCDI_PARM specific_heat;                          // specific heat
  eCDI_SOLID_ANISOTROPY_TYPE::Enum anisotropy_type; // anisotropy type
  sCDI_PARM therm_conductivity[3];                  // components of thermal conductivity
} *CDI_SCMT;

/* set of solid conducting materials */
#define CDI_CHUNK_TYPE_SCMS CIO_BUILDCCCC('s', 'c', 'm', 's')

typedef struct sCDI_SCMA
{ /* solid conductor material assignments */
#define CDI_CHUNK_TYPE_SCMA CIO_BUILDCCCC('s', 'c', 'm', 'a')
  CIO_CCCC cccc;
  std::vector<cdiINT32> regions;                                         /* the region list (where material assigned)*/
  cCDI_GEOMETRY_REF geom_ref{ cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part }; /* the geometry list (where material assigned)*/
  cdiINT32 material;                                                     /* index into the scms list */
} *CDI_SCMA;

/* set of solid conducting material assignments */
#define CDI_CHUNK_TYPE_SCAS CIO_BUILDCCCC('s', 'c', 'a', 's')

class cCDI_SOLID_MATERIAL_ASSIGNMENTS
{
public:
  typedef std::unordered_map<cdiINT32, cdiINT32> SOLID_MATERIAL_ASSIGNMENT_MAP_TYPE;

  cCDI_SOLID_MATERIAL_ASSIGNMENTS() {}

  cCDI_SOLID_MATERIAL_ASSIGNMENTS(std::vector<CDI_SCMA> materialAssigns);

  ~cCDI_SOLID_MATERIAL_ASSIGNMENTS();

  // Clients can call ReadFromCDI when the "SCAS" chunk is reached
  void ReadFromCDI(CDI_INFO cdi_info);

  cdiINT32 GetMaterialIndexForPart(cdiINT32 partIndex) const;
  void SetMaterialIndexForPart(cdiINT32 partIndex, cdiINT32 materialIndex);

  void AppendMaterialAssignment(CDI_SCMA scma);

private:
  void UpdateMapForMaterialAssignment(CDI_SCMA scma);

  std::vector<CDI_SCMA> mMaterialAssignments;
  std::unordered_map<cdiINT32, cdiINT32> mPartMaterialMap;
};

namespace eCDI_THERMAL_CONTACT_MESH
{
  enum Enum
  {
    SameAsParent = -1,
    Part1,
    Part2,
    MinArea,
    MaxArea,
    UserPrecedence
  };
  static std::string GetName(Enum ordinal)
  {
    switch (ordinal) {
    case SameAsParent: return "Same as Parent";
    case Part1: return "Part 1";
    case Part2: return "Part 2";
    case MinArea: return "Min Area";
    case MaxArea: return "Max Area";
    case UserPrecedence: return "User Precedence";
    }
    return "";
  }
}
inline std::string GetCDIEnumValueDescription(eCDI_THERMAL_CONTACT_MESH::Enum ordinal)
{
  return eCDI_THERMAL_CONTACT_MESH::GetName(ordinal);
}

namespace eCDI_THERMAL_CONTACT_BOOL
{
  enum Enum
  {
    SameAsParent,
    Yes,
    No
  };
  static std::string GetName(Enum ordinal)
  {
    switch (ordinal) {
    case SameAsParent: return "Same as Parent";
    case Yes: return "Yes";
    case No: return "No";
    }
    return "";
  }
}
inline std::string GetCDIEnumValueDescription(eCDI_THERMAL_CONTACT_BOOL::Enum ordinal)
{
  return eCDI_THERMAL_CONTACT_BOOL::GetName(ordinal);
}

namespace eCDI_THERMAL_CONTACT_EXTENT
{
  enum Enum
  {
    SameAsParent,
    IntersectionOnly,
    IntersectionAndGaps,
    EntireFace
  };
  static std::string GetName(Enum ordinal)
  {
    switch (ordinal) {
    case SameAsParent: return "Same as Parent";
    case IntersectionOnly: return "Intersection Only";
    case IntersectionAndGaps: return "Intersection and Small Gaps";
    case EntireFace: return "Entire Geometry";
    }
    return "";
  }
}
inline std::string GetCDIEnumValueDescription(eCDI_THERMAL_CONTACT_EXTENT::Enum ordinal)
{
  return eCDI_THERMAL_CONTACT_EXTENT::GetName(ordinal);
}

namespace eCDI_THERMAL_CONTACT_CRITERIA_TYPE
{
  enum Enum
  {
    Material,
    ContactArea,
    Volume,
    Part
  };
  static std::string GetName(Enum ordinal)
  {
    switch (ordinal) {
    case Material: return "Material";
    case ContactArea: return "Contact Area";
    case Volume: return "Volume";
    case Part: return "Part";
    }
    return "";
  }
}
inline std::string GetCDIEnumValueDescription(eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Enum ordinal)
{
  return eCDI_THERMAL_CONTACT_CRITERIA_TYPE::GetName(ordinal);
}

namespace eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION
{
  enum Enum
  {
    Min,
    Max,
    Part1,
    Part2,
    Custom
  };
  static std::string GetName(Enum ordinal)
  {
    switch (ordinal) {
    case Min: return "Min";
    case Max: return "Max";
    case Part1: return "Geometry 1";
    case Part2: return "Geometry 2";
    case Custom: return "Custom";
    }
    return "";
  }
}
inline std::string GetCDIEnumValueDescription(eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Enum ordinal)
{
  return eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::GetName(ordinal);
}

namespace eCDI_THERMAL_CONTACT_PRECEDENCE_MATERIAL_PROP
{
  enum Enum
  {
    SpecificHeat,
    Density,
    ThermalConductivity,
    ThermalDiffusivity
  };
  static std::string GetName(Enum ordinal)
  {
    switch (ordinal) {
    case SpecificHeat: return "Specific Heat";
    case Density: return "Density";
    case ThermalConductivity: return "Thermal Conductivity";
    case ThermalDiffusivity: return "Thermal Diffusivity";
    }
    return "";
  }
}
inline std::string GetCDIEnumValueDescription(eCDI_THERMAL_CONTACT_PRECEDENCE_MATERIAL_PROP::Enum ordinal)
{
  return eCDI_THERMAL_CONTACT_PRECEDENCE_MATERIAL_PROP::GetName(ordinal);
}

typedef struct sCDI_SCPR
{ /* solid thermal contact mesh precedence */
#define CDI_CHUNK_TYPE_SCPR CIO_BUILDCCCC('s', 'c', 'p', 'r')
  CIO_CCCC cccc;

  eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Enum criteria;
  eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Enum mesh_keep_option;
  eCDI_THERMAL_CONTACT_PRECEDENCE_MATERIAL_PROP::Enum mesh_keep_property;
  cdiINT32 material_precedence_list_index = -1;

} *CDI_SCPR;

sCDI_SCPR cdi_convert_old_mesh_enum_to_scpr(eCDI_THERMAL_CONTACT_MESH::Enum oldMeshToKeep);
eCDI_THERMAL_CONTACT_MESH::Enum cdi_convert_scpr_to_old_mesh_enum(const sCDI_SCPR &precedence);

/* list of solid thermal contact mesh precedence */
#define CDI_CHUNK_TYPE_SCPL CIO_BUILDCCCC('s', 'c', 'p', 'l')

/* Custom material precedence list */
typedef struct sCDI_MTPR
{
#define CDI_CHUNK_TYPE_MTPR CIO_BUILDCCCC('m', 't', 'p', 'r')
  CIO_CCCC cccc;

  std::string name;
  std::vector<cdiINT32> materials;  // Material indices
} *CDI_MTPR;

/* list of custom solid material precedences */
#define CDI_CHUNK_TYPE_MTPL CIO_BUILDCCCC('m', 't', 'p', 'l')

class cCDI_CUSTOM_MATERIAL_PRECEDENCES
{
public:
  cCDI_CUSTOM_MATERIAL_PRECEDENCES() {}

  cCDI_CUSTOM_MATERIAL_PRECEDENCES(std::vector<CDI_SCMT> materials) : mMaterials(materials) {}

  ~cCDI_CUSTOM_MATERIAL_PRECEDENCES();

  // Clients can call ReadFromCDI when the "MTPL" chunk is reached
  void ReadFromCDI(CDI_INFO cdi_info);
  void AddMaterialList(std::vector<CDI_SCMT> materialList) { mMaterials = materialList; }
  void AppendCustomPrecedenceList(CDI_MTPR precedenceList) { mPrecedences.push_back(precedenceList); }

  // This method determines if material1 is preferred over material in a given precedence list (listIndex)
  // The first member of the pair is if the materials are in the precedence list
  // The second member of the pair is if material1 is preferred over material2
  std::pair<bool, bool> IsMaterialPreferred(CDI_SCMT material1, CDI_SCMT material2, cdiINT32 listIndex) const;

  CDI_SCMT GetMaterialProperty(size_t index) {
    return ((index >= 0) && (index < mMaterials.size())) ? mMaterials[index] : nullptr;
  }

private:
  std::vector<CDI_SCMT> mMaterials;
  std::vector<CDI_MTPR> mPrecedences;
};

typedef struct sCDI_STCP
{ /* solid thermal contact parameters */
#define CDI_CHUNK_TYPE_STCP CIO_BUILDCCCC('s', 't', 'c', 'p')
  CIO_CCCC cccc;
  
  eCDI_THERMAL_CONTACT_BOOL::Enum enabled;
  eCDI_THERMAL_CONTACT_EXTENT::Enum contact_extent;
  eCDI_THERMAL_CONTACT_BOOL::Enum heat_uniform;
  eCDI_THERMAL_CONTACT_BOOL::Enum join_parts;
  eCDI_THERMAL_CONTACT_MESH::Enum mesh_to_keep;
  sCDI_PARM max_separation;
  sCDI_PARM max_angle;
  bool close_gaps = false;
  std::vector<sCDI_SCPR> mesh_precedence_list;
} *CDI_STCP;

typedef struct sCDI_STCR
{ /* solid thermal contact regions */
#define CDI_CHUNK_TYPE_STCR CIO_BUILDCCCC('s', 't', 'c', 'r')
  CIO_CCCC cccc;
  std::string region_name;    /* the contact region name */
  cdiINT32 parent_index;      /* index of the parent contact region */
  cCDI_GEOMETRY_REF geometry_1{ cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part };
  cCDI_GEOMETRY_REF geometry_2{ cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part };
  sCDI_STCP contact_parameters;
  cdiINT32 surf_props_index;  /* index to surface physics properties */
} *CDI_STCR;

/* set of solid thermal contact regions */
#define CDI_CHUNK_TYPE_STCT CIO_BUILDCCCC('s', 't', 'c', 't')

struct sCDI_CONTACT_PRECEDENCE_PARAMETERS
{
  cdiINT32 geometry_index;
  double contact_area;
  double volume;
  CDI_SCMT material;
};

class cCDI_THERMAL_CONTACTS
{
public:
  typedef std::unordered_map<cdiINT32, std::unordered_map<cdiINT32, std::pair<cdiINT32, bool>>> CONTACT_PAIR_MAP_TYPE;
  
  cCDI_THERMAL_CONTACTS() {}

  cCDI_THERMAL_CONTACTS(std::vector<CDI_STCR> contacts) : mThermalContacts(contacts) {}

  ~cCDI_THERMAL_CONTACTS();

  // Clients can call ReadFromCDI when the "STCT" chunk is reached
  void ReadFromCDI(CDI_INFO cdi_info);
  // Clients need to call BuildContactTable after reading and before calling other methods
  void BuildContactTable(cCDI_PARTITIONS& partitions);

  cdiINT32 GetContactIndexForPartPair(cdiINT32 partIndex1, cdiINT32 partIndex2) const;
  cdiINT32 GetContactIndexForPartPair(cdiINT32 partIndex1, cdiINT32 partIndex2, bool& isPart1Primary) const;

  cdiINT32 GetContactIndexForFacePair(cdiINT32 faceIndex1, cdiINT32 faceIndex2) const;
  cdiINT32 GetContactIndexForFacePair(cdiINT32 faceIndex1, cdiINT32 faceIndex2, bool& isFace1Primary) const;
  
  void SetContactIndexForPartPair(cdiINT32 partIndex1, cdiINT32 partIndex2, cdiINT32 contactIndex, bool isPart1Primary);

  void SetContactIndexForEntireFace(std::vector<cdiINT32> &faceIndices1, std::vector<cdiINT32> &faceIndices2, cdiINT32 contactIndex);

  void AppendContactRegion(CDI_STCR stcr);

  CDI_STCR GetContactRegion(cdiINT32 contactIndex);

  // This method is for the "Entire Geometry" contacts. It returns the face indices assigned
  // to geometry 1 and geometry 2 for a given contact. It will return false if the contact
  // index is out of range or not an Entire Geometry contact
  bool GetContactEntireFaceGeometryIndices(cdiINT32 contactIndex, std::vector<cdiINT32> &faceIndices1, std::vector<cdiINT32> &faceIndices2);

  sCDI_STCP GetContactParameters(cdiINT32 contactIndex);

  // These methods provide shortcuts to check if a contact is of a certain type without the 
  // need to retrieve the complete set of parameters
  bool IsEntireFace(cdiINT32 contactIndex);
  bool IsHeatUniform(cdiINT32 contactIndex);

  size_t GetNumContactRegions()
  {
    return mThermalContacts.size();
  }

  std::vector<CDI_STCR>& GetContactRegions()
  {
    return mThermalContacts;
  }

  CONTACT_PAIR_MAP_TYPE& GetPartPairContacts()
  {
    return mContactPartPair.map;
  }

  std::unordered_map<cdiINT32, std::pair<std::vector<cdiINT32>, std::vector<cdiINT32>>>& GetEntireFaceContacts()
  {
    return mContactEntireFaceGeom;
  }

  bool ValidContactIndex(cdiINT32 contactIndex) const
  {
    return ((contactIndex >= 0) && ((size_t)contactIndex < mThermalContacts.size()));
  }

  void SetCustomMaterialPrecedences(cCDI_CUSTOM_MATERIAL_PRECEDENCES* customPrecedences) { mCustomMaterialPrecedences = customPrecedences; }

  // These methods return the preferred geometry mesh to keep based on the contact table entry
  // The first bool is if the pair is in the contact table. This should really only be relevant for face pairs
  //   because at the moment all part pairs are considered to inherit the root contact
  // The second bool indicates if geometry 1 is preferred over geometry 2
  std::pair<bool, bool> GetMeshPrecedenceForPartPair(const sCDI_CONTACT_PRECEDENCE_PARAMETERS& part1, const sCDI_CONTACT_PRECEDENCE_PARAMETERS& part2);
  std::pair<bool, bool> GetMeshPrecedenceForFacePair(const sCDI_CONTACT_PRECEDENCE_PARAMETERS& face1, const sCDI_CONTACT_PRECEDENCE_PARAMETERS& face2);

  bool GetMeshPrecedenceForPair(const sCDI_CONTACT_PRECEDENCE_PARAMETERS& part1,
                                const sCDI_CONTACT_PRECEDENCE_PARAMETERS& part2,
                                const sCDI_STCP& contactParameters,
                                bool isPart1Primary);

private:
  struct sCONTACT_INDEX_MAP {
    CONTACT_PAIR_MAP_TYPE map;
    void Add(cdiINT32 i1, cdiINT32 i2, cdiINT32 iContact, bool is1Primary);
    bool Exist(cdiINT32 i1, cdiINT32 i2) const;
    bool GetContactIndex(cdiINT32 i1, cdiINT32 i2, cdiINT32& iContact) const;
   private:
    bool Sort(cdiINT32 &i1, cdiINT32 &i2) const;
  };

  void GetContactRegionParents(cdiINT32 contactIndex, std::vector<asINT32> &parentContactIndices);

  void SetContactIndexForFacePair(cdiINT32 faceIndex1, cdiINT32 faceIndex2, cdiINT32 contactIndex, bool isFace1Primary);
  
  std::vector<CDI_STCR> mThermalContacts;
  sCONTACT_INDEX_MAP mContactPartPair;
  sCONTACT_INDEX_MAP mContactFacePair;
  std::unordered_map<cdiINT32, std::pair<std::vector<cdiINT32>, std::vector<cdiINT32>>> mContactEntireFaceGeom;
  cCDI_CUSTOM_MATERIAL_PRECEDENCES* mCustomMaterialPrecedences;
};

// Part axes used to define material anisotropy
#define CDI_CHUNK_TYPE_PAXL CIO_BUILDCCCC('p', 'a', 'x', 'l')

typedef struct sCDI_PAXS
{ /* part axis defining material anisotropy */
#define CDI_CHUNK_TYPE_PAXS CIO_BUILDCCCC('p', 'a', 'x', 's')
  CIO_CCCC cccc;
  cdiINT32 part_index;
  double axis_dir[3];
} *CDI_PAXS;

typedef struct sCDI_MOVB
{ /* moving|immersed boundary partition */
#define CDI_CHUNK_TYPE_MOVB CIO_BUILDCCCC('m', 'o', 'v', 'b')
  CIO_CCCC cccc;
  CDI_PHRG phys_regions; /* the array of physics regions */
} *CDI_MOVB;

// Fluid phase source/sink (used for rock physics / 5g)
#define CDI_CHUNK_TYPE_FPSS CIO_BUILDCCCC('f', 'p', 's', 's')

typedef struct sCDI_ISLD
{ /* inverted solid partition */
#define CDI_CHUNK_TYPE_ISLD CIO_BUILDCCCC('i', 's', 'l', 'd')
  CIO_CCCC cccc;
  CDI_PHRG phys_regions; /* the array of physics regions */
} *CDI_ISLD;

typedef struct sCDI_PCFG
{ /* physics configuration (partition)*/
#define CDI_CHUNK_TYPE_PCFG CIO_BUILDCCCC('p', 'c', 'f', 'g')
  CIO_CCCC cccc;
  union
    {
      sCDI_SOLD solid; /* a solid, or */
      sCDI_FLUD fluid; /* a fluid, or */
      sCDI_ISLD inv_solid; /* an inverted solid */
    } flud_or_sold;
} *CDI_PCFG;

typedef struct sCDI_GRDF
{ /* grid default parameters */
#define CDI_CHUNK_TYPE_GRDF CIO_BUILDCCCC('g', 'r', 'd', 'f')
  CIO_CCCC cccc;
  cdiINT32 resolution_precedence;  /* containment tie-breaker: 0 - use finer (default); 1 - use coarser scale */
  cdiINT32 separation_factor;      /* integer multiplier of coarse-microblocks (>= 1) */
} *CDI_GRDF;

typedef struct sCDI_GSEP
{ /* grid scale separation factor */
#define CDI_CHUNK_TYPE_GSEP CIO_BUILDCCCC('g', 's', 'e', 'p')
  CIO_CCCC cccc;
  cdiINT32 scale; /* grid scale value */
  cdiINT32 separation_factor;   /* miniumum separation thickness between between (scale - 1) and (scale + 1) in units of local microblocks (>=1) */
} *CDI_GSEP;

typedef struct sCDI_GRID
{ /* grid partition*/
#define CDI_CHUNK_TYPE_GRID CIO_BUILDCCCC('g', 'r', 'i', 'd')
  CIO_CCCC cccc;
  CDI_GSCL grid_scales;       /* array of region grid scales */
  CDI_GFDS grid_face_dists;   /* array of faces and offset distances */
  CDI_GSEP grid_separations;  /* array of scale separation factors */
  sCDI_GRDF grid_defaults;    /* grid default parameters */
} *CDI_GRID;

typedef struct sCDI_MEAS
{ /* measurement partition*/
#define CDI_CHUNK_TYPE_MEAS CIO_BUILDCCCC('m', 'e', 'a', 's')
  CIO_CCCC cccc;
  CDI_MESR meas; /* the array of measurement windows */
} *CDI_MEAS;

typedef struct sCDI_SRPH
{ /* Surface Physics */
#define CDI_CHUNK_TYPE_SRPH CIO_BUILDCCCC('s', 'r', 'p', 'h')
  CIO_CCCC cccc;
  cCDI_GEOMETRY_REF face_geom{ cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face }; /* the references to the face geometry */
  CDI_PHYS surf_props; /* the array of surface properties */
} *CDI_SRPH;

typedef struct sCDI_SRPT
{ /* Surface Property Table*/
#define CDI_CHUNK_TYPE_SRPT CIO_BUILDCCCC('s', 'r', 'p', 't')
  CIO_CCCC cccc;
  CDI_PHYS surf_props; /* the array of surface properties */
} *CDI_SRPT;

typedef struct sCDI_THPH
{ /* Thermal Physics */
#define CDI_CHUNK_TYPE_THPH CIO_BUILDCCCC('t', 'h', 'p', 'h')
  CIO_CCCC cccc;
  cCDI_GEOMETRY_REF face_geom{ cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face }; /* the references to the face geometry */
  CDI_PHYS therm_props;
} *CDI_THPH;

// The table/container for the thermal physics
#define CDI_CHUNK_TYPE_THPT CIO_BUILDCCCC('t', 'h', 'p', 't')

typedef struct sCDI_SIMV
{ /* simulation volume*/
#define CDI_CHUNK_TYPE_SIMV CIO_BUILDCCCC('s', 'i', 'm', 'v')
  CIO_CCCC cccc;
  double max[3]; /* maximum coordinates of simulation volume */
  cdiINT32 flags; /* general purpose flag */
  double xform[4][4]; /* 2D section plane transform */
} *CDI_SIMV;

/* Definition of CDI_SIMV flags */
#define CDI_IS_2D                                  (1 << 0)
#define CDI_IS_INTERNAL_FLOW                       (1 << 1)
#define CDI_IS_TURB_MODEL                          (1 << 2)
#define CDI_IS_HEAT_TRANSFER                       (1 << 3)
#define CDI_EXPR_RELATIVE_TO_USER_CSYS             (1 << 4)
#define CDI_IS_LIQUID                              (1 << 5)
#define CDI_USER_SPECIFIED_DNS_NU_OVER_T           (1 << 6)
#define CDI_19_STATE                               (1 << 7)
#define CDI_TRUE_INCOMPRESSIBLE                    (1 << 8)
#define CDI_IS_MULTI_PHASE                         (1 << 9)
#define CDI_39_STATE                               (1 << 10)
#define CDI_5_0_THERMAL_FEEDBACK                   (1 << 11)
#define CDI_DOUBLE_PRECISION_SOLVER                (1 << 12)
#define CDI_5_0_SOLVER                             (1 << 13)
#define CDI_HIGH_SUBSONIC_MACH_REGIME              (1 << 14)
#define CDI_BOUNDARY_LAYER_TRANSITION              (1 << 15)
#define CDI_USES_5_X_ONLY_FEATURE                  (1 << 16)
#define CDI_33_STATE                               (1 << 17)
// The following flag signifies that the case was set up with particle
// modeling enabled. However, there may be no particle modeling objects
// set up in the case. When deciding whether to perform any potentially
// expensive steps in support of particle modeling, CDI readers should
// not decide based on the presence of this flag (the presence of one 
// or more emitters is usually a better indicator).
#define CDI_PARTICLE_MODELING_ENABLED              (1 << 18)
#define CDI_WATER_VAPOR_TRANSPORT                  (1 << 19)
#define CDI_BC_MEAS_IMPORT                         (1 << 20)
#define CDI_6_0_SOLVER                             (1 << 21)
#define CDI_USES_6_X_ONLY_FEATURE                  (1 << 22)
#define CDI_TRANSONIC_FLOW_ONLY_IN_RRF             (1 << 23)
#define CDI_LEGACY_ENERGY_SOLVER                   (1 << 24)
#define CDI_EUTECTIC                               (1 << 25)         //MMD_Eutectic
#define CDI_HYDROGEN_EOS                           (1 << 26)
#define CDI_HIGH_SUBSONIC_EXTENDED_MACH_REGIME     (1 << 27)
#define CDI_ENABLE_CONDUCTION_SOLVER               (1 << 28)
#define CDI_ENABLE_FLOW_SOLVER                     (1 << 29)
#define CDI_ENABLE_RADIATION                       (1 << 30)
#define CDI_USER_DEFINED_SCALAR_TRANSPORT            (1 << 31)

typedef struct sCDI_THMA
{ /* thermal acceleration */
#define CDI_CHUNK_TYPE_THMA CIO_BUILDCCCC('t', 'h', 'm', 'a')
  CIO_CCCC cccc;
  cdiINT32 start_time;
  cdiINT32 end_time;
  cdiINT32 period;
  cdiINT32 interval;
} *CDI_THMA;

typedef struct sCDI_SYMP
{ /* symmetry plane(s) */
#define CDI_CHUNK_TYPE_SYMP CIO_BUILDCCCC('s', 'y', 'm', 'p')
  CIO_CCCC cccc;
  cdiINT32 axes;                 /* bit encoding of directions with symmetry */
  cdiINT32 min[3]; /* min coordinates of symmetry plane(s) */
  cdiINT32 max[3]; /* max coordinates of symmetry plane(s) */
} *CDI_SYMP;

#define CDI_X_SYMMETRY 1
#define CDI_Y_SYMMETRY 2
#define CDI_Z_SYMMETRY 4

typedef struct sCDI_UNIT
{ /* lattice units */
#define CDI_CHUNK_TYPE_UNIT CIO_BUILDCCCC('u', 'n', 'i', 't')
  CIO_CCCC cccc;
  double kilos_per_particle; /* mass */
  double meters_per_cell; /* length */
  double seconds_per_timestep; /* time */
  double kelvins_per_lattice_temp; /* temperature */
  cdiINT32 n_user_units; /* number of user unit chunks */
} *CDI_UNIT;


namespace eCDI_SIM_DURATION_VIA
{
enum Enum
{
    Invalid = -1,
    SpecifiedBelow,
    AfterInitialTransient,
    GreatestMeasurementEndTime
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case SpecifiedBelow: return "Specified Below";
    case AfterInitialTransient: return "Duration After Initial Transient";
    case GreatestMeasurementEndTime: return "Greatest Measurement Window End Time";
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_SIM_DURATION_VIA::Enum ordinal)
{
  return eCDI_SIM_DURATION_VIA::GetName(ordinal);
}


namespace eCDI_COOLING_AIR_LEAKAGE_OPTION {
  enum Enum { Off = -1, Low, Medium, High };

  static std::string GetName(Enum ordinal) {
    switch (ordinal) {
    case Off: return "Off";
    case Low: return "Low";
    case Medium: return "Medium";
    case High: return "High";
    }
    return "";
  }
}
inline std::string GetCDIEnumValueDescription(eCDI_COOLING_AIR_LEAKAGE_OPTION::Enum ordinal)
{
  return eCDI_COOLING_AIR_LEAKAGE_OPTION::GetName(ordinal);
}



// Global header chunk. We've added a couple of new blocks of parameters to this
// chunk without bumping the CDI version. So we keep track of whether the
// parameters are included in the file or not so that we can preserve the
// contents of the file during a dump/undump.
struct sCDI_GHDR 
{ 
#define CDI_CHUNK_TYPE_GHDR CIO_BUILDCCCC('g', 'h', 'd', 'r')
  CIO_CCCC cccc;
   
  // First block of parameters is always read/written
  eCDI_SIM_DURATION_VIA::Enum sim_duration_via;
  std::vector<cdiINT32> monitors;
  cdiINT32 n_timesteps_after_init_trans;
  cdiINT32 n_timesteps;
  cdiINT32 n_processors; 
  cdiINT32 liquidMaterialType;
  cdiINT32 has_average_mme_window;
  cdiINT32 local_vel_freeze;

  cdiINT32 m_temperatureDependentGamma;
  eCDI_COOLING_AIR_LEAKAGE_OPTION::Enum m_coolingAirOpt;
  eCDI_COUPLED_SOLVER::Enum timesteps_rel_solver;

  // Second block of params -- added for defrost capability
  bool freeze_momentum_field;
  double physical_time_scaling;
  bool include_second_block; // used for dump/undump only

  // Third block of params -- used to compute custom variable IDs
  cdiINT32 numParticleEmitters;
  cdiINT32 numParticleMaterials;
  cdiINT32 numParticleModelingVars;
  cdiINT32 numUDScalars;
  cdiINT32 numUDSVars;
  cdiINT32 num5gFluidComponents;
  cdiINT32 num5gVars;
  bool include_third_block; // used for dump/undump only

  // C++-11 would allow us to put this default initialization with the members
  // listed above, which would be much cleaner, but all users of this API are
  // not building with C++-11 yet (e.g. b/c they still build with an old VS
  // version).
  sCDI_GHDR()
    : cccc(CDI_CHUNK_TYPE_GHDR),
      sim_duration_via(eCDI_SIM_DURATION_VIA::SpecifiedBelow),
      n_timesteps_after_init_trans(0),
      n_timesteps(0),
      n_processors(1),
      liquidMaterialType(0),
      has_average_mme_window(0),
      local_vel_freeze(0),
      m_temperatureDependentGamma(0),
      m_coolingAirOpt(eCDI_COOLING_AIR_LEAKAGE_OPTION::Off),
      timesteps_rel_solver(eCDI_COUPLED_SOLVER::FlowSolver),
      freeze_momentum_field(false),
      physical_time_scaling(1.0),
      include_second_block(true),
      numParticleEmitters(0),
      numParticleMaterials(0),
      numParticleModelingVars(0),
      numUDScalars(0),
      numUDSVars(0),
      num5gFluidComponents(0),
      num5gVars(0),
      include_third_block(true)
  {}
};
typedef sCDI_GHDR* CDI_GHDR;

typedef struct sCDI_UUNT
{ /* user units */
#define CDI_CHUNK_TYPE_UUNT CIO_BUILDCCCC('u', 'u', 'n', 't')
  CIO_CCCC cccc;
  CDI_UNIT_TYPES unit_type; /* unit identifier */
  cdiINT32 mass_exponent; /* exponent of mass */
  cdiINT32 length_exponent; /* exponent of length */
  cdiINT32 time_exponent; /* exponent of time */
  cdiINT32 temp_exponent; /* exponent of temperature */
  double scale; /* multiplier from user to SI (MKS) units */
  double offset; /* offset from user to SI (MKS) temp units */
  cdiINT32 n_char; /* characters in unit name */
  char *unit_name; /* the unit name (e.g. "meters/second") */
} *CDI_UUNT;

typedef struct sCDI_CPRP
{ /* characteristic property */
#define CDI_CHUNK_TYPE_CPRP CIO_BUILDCCCC('c', 'p', 'r', 'p')
  CIO_CCCC cccc;
  cdiINT32 mass_exponent; /* exponent of mass */
  cdiINT32 length_exponent; /* exponent of length */
  cdiINT32 time_exponent; /* exponent of time */
  cdiINT32 temp_exponent; /* exponent of temperature */
  cdiINT32 n_char; /* number of characters in char prop name */
  char *char_prop_name; /* the name (e.g. "velocity") */
  double value; /* the actual value */
} *CDI_CPRP;

typedef struct sCDI_CPNT
{ /* checkpoint*/
#define CDI_CHUNK_TYPE_CPNT CIO_BUILDCCCC('c', 'p', 'n', 't')
  CIO_CCCC cccc;
  cdiINT32 start; /* starting timestep for checkpointing */
  cdiINT32 end; /* ending timestep */
  cdiINT32 freq; /* checkpoint frequency */
} *CDI_CPNT;

struct sCDI_KESC //kEps_SuperCycle
{ 
#define CDI_CHUNK_TYPE_KESC CIO_BUILDCCCC('k', 'e', 's', 'c')
  CIO_CCCC cccc = CDI_CHUNK_TYPE_KESC;
  bool bKesc_activated = false; /* support for k-e super cycling solver */
  cdiINT32 super_cycling_factor = 2; 
};

typedef struct sCDI_GLOB
{ /* global chunk*/
#define CDI_CHUNK_TYPE_GLOB CIO_BUILDCCCC('g', 'l', 'o', 'b')
  CIO_CCCC cccc;
  CDI_GHDR global_header; /* the header */
  CDI_UNIT lattice_units; /* lattice units */
  CDI_UUNT user_units; /* array of user units */
  CDI_CPNT checkpoint; /* the checkpoint */
  sCDI_KESC kesc;      /*k-e super cycle*/
  cdiINT32 n_char_props; /* number of characteristic properties */
  CDI_CPRP char_props; /* array of characteristic properties */
} *CDI_GLOB;

/* set of coupling solver phases */
#define CDI_CHUNK_TYPE_CSPS CIO_BUILDCCCC('c', 's', 'p', 's')

namespace eCDI_TIME_COUPLING_SCHEME
{
  enum Enum
  {
    SameRate,
    DifferentRate,
    Stagger,
    FreezeOne
  };
  static std::string GetName(Enum ordinal)
  {
    switch (ordinal) {
    case SameRate: return "Advance Solvers at Same Rate";
    case DifferentRate: return "Advance Solvers at Different Rates";
    case Stagger: return "Stagger Solvers";
    case FreezeOne: return "Freeze One Solver";
    }
    return "";
  }
}
inline std::string GetCDIEnumValueDescription(eCDI_TIME_COUPLING_SCHEME::Enum ordinal)
{
  return eCDI_TIME_COUPLING_SCHEME::GetName(ordinal);
}

struct sCDI_CSPH
{ /* coupling solver phase */
#define CDI_CHUNK_TYPE_CSPH CIO_BUILDCCCC('c', 's', 'p', 'h')
  CIO_CCCC cccc;
  eCDI_COUPLED_SOLVER::Enum time_rel_solver;
  cdiINT32 start;
  eCDI_TIME_COUPLING_SCHEME::Enum time_coupling;
  double therm_time_ratio;
  cdiINT32 flow_duration;
  cdiINT32 flow_avg_interval;
  cdiINT32 conduction_duration;
  cdiINT32 conduction_avg_interval;
  eCDI_COUPLED_SOLVER::Enum frozen_solver;
  double radiation_update_ratio;

  sCDI_CSPH()
    : cccc(CDI_CHUNK_TYPE_CSPH),
    time_rel_solver(eCDI_COUPLED_SOLVER::FlowSolver),
    start(-1),
    time_coupling(eCDI_TIME_COUPLING_SCHEME::SameRate),
    therm_time_ratio(1.0),
    flow_duration(-1),
    flow_avg_interval(-1),
    conduction_duration(-1),
    conduction_avg_interval(-1),
    frozen_solver(eCDI_COUPLED_SOLVER::FlowSolver),
    radiation_update_ratio(1.0)
  {}

};
typedef sCDI_CSPH* CDI_CSPH;

typedef struct sCDI_REGN
{
#define CDI_CHUNK_TYPE_REGN CIO_BUILDCCCC('r', 'e', 'g', 'n')
  CIO_CCCC cccc;
} *CDI_REGN;

typedef struct sCDI_SHLF
{
/* #define CDI_SHLF_CCCC "shlf" */
#define CDI_CHUNK_TYPE_SHLF CIO_BUILDCCCC('s', 'h', 'l', 'f')
  CIO_CCCC cccc;
} *CDI_SHLF;

typedef struct sCDI_BODY
{
#define CDI_CHUNK_TYPE_BODY CIO_BUILDCCCC('b', 'o', 'd', 'y')
  CIO_CCCC cccc;
} *CDI_BODY;

typedef struct sCDI_BSRG
{
#define CDI_CHUNK_TYPE_BSRG CIO_BUILDCCCC('b', 's', 'r', 'g')
  CIO_CCCC cccc;
  char* mea_name;                /* name of associated measurement */
  cdiINT64 cdi_file_id;          /* File id of imported CDI file */
  cdiINT32 region_index;         /* region index */
  cdiINT32 facet_offset;         /* facet index offset */
  cdiINT32 spatial_mapping;      /* spatial mapping from physics surfel to meas surfel */
  cBOOLEAN transient_boundary_seeding;
  cBOOLEAN is_merged_to_input;
  cBOOLEAN seed_via_mks;             /* mks seeding from sampled face measurements */
  double coord_offset[3];        /* Offset of region specified in powercase */
} *CDI_BSRG;

typedef struct sCDI_BSRS
{ /* Boundary Seeding Region Table*/
#define CDI_CHUNK_TYPE_BSRS CIO_BUILDCCCC('b', 's', 'r', 's')
  CIO_CCCC cccc;
  CDI_BSRG bseed_regions; /* the array of boundary seeding regions */
} *CDI_BSRS;

typedef struct sCDI_VPTS
{
#define CDI_CHUNK_TYPE_VPTS CIO_BUILDCCCC('v', 'p', 't', 's')
  CIO_CCCC cccc;
  CDI_PHYS viewpoints; /* the array of viewpoints */
} *CDI_VPTS;

typedef struct sCDI_VPNT
{
#define CDI_CHUNK_TYPE_VPNT CIO_BUILDCCCC('v', 'p', 'n', 't')
  CIO_CCCC cccc;
  char* name;                  /* name */
  cdiINT32 csys_index;         /* csys index */
  char* camera_pos_unit;       /* camera position unit */
  double camera_pos[3];        /* [xyz] camera position */
  double view_dir[3];          /* [xyz] view direction */
  double up_dir[3];            /* [xyz] up direction */
  // 0 -> orthographic, 1 -> perspective
  cdiINT32 projection_type;    /* projection type */
  char* orthographic_fov_unit; /* orthographic field of view unit */
  double orthographic_fov;     /* orthographic field of view */
  double perspective_fov;      /* perspective field of view */
  char* spin_center_unit;      /* spin center unit */
  double spin_center[3];       /* [xyz] spin center */
} sCDI_VPNT,*CDI_VPNT;

/* a collection of data tables */
#define CDI_CHUNK_TYPE_TBLS CIO_BUILDCCCC('t', 'b', 'l', 's')

/* a cylindrical local reference frame region */
#define CDI_CHUNK_TYPE_CYLR CIO_BUILDCCCC('c', 'y', 'l', 'r')

/* set of cylindrical local reference frame regions/faces */
#define CDI_CHUNK_TYPE_CYLS CIO_BUILDCCCC('c', 'y', 'l', 's')

/* set of volume of revolution local reference frame */
#define CDI_CHUNK_TYPE_VREV CIO_BUILDCCCC('v', 'r', 'e', 'v')

/* "stationary" local reference frame region */
#define CDI_CHUNK_TYPE_SLRF CIO_BUILDCCCC('s', 'l', 'r', 'f')

/* "moving" local reference frame region */
#define CDI_CHUNK_TYPE_MLRF CIO_BUILDCCCC('m', 'l', 'r', 'f')

/* local reference frame regions chunk */
#define CDI_CHUNK_TYPE_LRFS CIO_BUILDCCCC('l', 'r', 'f', 's')

// Obsolete chunk formerly used in MESR chunks (kept to allow reading
// of old CDI files).
#define CDI_CHUNK_TYPE_MSTT CIO_BUILDCCCC('m', 's', 't', 't')

//
// incremental discretization control
//
enum CDI_INCRDEF_TYPE {
  increment_undefined,
  increment_bbox,
  increment_rgns,
  increment_flst
};

typedef struct sCDI_INCR
{ /* increment */
#define CDI_CHUNK_TYPE_INCR CIO_BUILDCCCC('i', 'n', 'c', 'r')
  CIO_CCCC cccc;

  sCDI_NAME inc_name;   /* the increment name */

  CDI_INCRDEF_TYPE incrdef_type;   /* how increment geometry defined */

  union {
    sCDI_BBOX bbox;
    sCDI_RGNS rgn_list;
    sCDI_FLST face_list;
  } incrdef;

} *CDI_INCR;

#define CDI_CHUNK_TYPE_INCM CIO_BUILDCCCC('i', 'n', 'c', 'm')

#define CDI_CHUNK_TYPE_TRAC CIO_BUILDCCCC('t', 'r', 'a', 'c')
#define CDI_CHUNK_TYPE_PRMS CIO_BUILDCCCC('p', 'r', 'm', 's')
#define CDI_CHUNK_TYPE_ECGS CIO_BUILDCCCC('e', 'c', 'g', 's')
#define CDI_CHUNK_TYPE_PEMC CIO_BUILDCCCC('p', 'e', 'm', 'c')
#define CDI_CHUNK_TYPE_SRMS CIO_BUILDCCCC('s', 'r', 'm', 's')
#define CDI_CHUNK_TYPE_SRFM CIO_BUILDCCCC('s', 'r', 'f', 'm')
#define CDI_CHUNK_TYPE_SCRS CIO_BUILDCCCC('s', 'c', 'r', 's')
#define CDI_CHUNK_TYPE_WIPS CIO_BUILDCCCC('w', 'i', 'p', 's')
#define CDI_CHUNK_TYPE_TIGR CIO_BUILDCCCC('t', 'i', 'g', 'r')

enum CDI_DISTRIBUTION_TYPE
{
  DISTRIBUTION_UNIFORM,
  DISTRIBUTION_GAUSSIAN,
  DISTRIBUTION_GAMMA,
  DISTRIBUTION_NONE,
  DISTRIBUTION_ROSIN_RAMMLER,
  DISTRIBUTION_LOG_NORMAL,
  DISTRIBUTION_LINEAR,
  DISTRIBUTION_1MX_POW_N,      // (1-x)^n
  DISTRIBUTION_HALF_COSINE,     // 1/2*(1+cos(PI*x^n))
  DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION //PR_48032
};

struct sCDI_DISTRIBUTION_TYPE_UTILS
{
  static std::string GetName(CDI_DISTRIBUTION_TYPE ordinal) {
    switch (ordinal) {
    case DISTRIBUTION_UNIFORM: return "Uniform";
    case DISTRIBUTION_GAUSSIAN: return "Gaussian";
    case DISTRIBUTION_GAMMA: return "Gamma";
    case DISTRIBUTION_NONE: return "None";
    case DISTRIBUTION_ROSIN_RAMMLER: return "Rosin-Rammler";
	case DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION: return "Rosin-Rammler Volume Fraction";//PR_48032
    case DISTRIBUTION_LOG_NORMAL: return "Log-Normal";
    case DISTRIBUTION_LINEAR: return "Linear";
    case DISTRIBUTION_1MX_POW_N: return "(1-x)^n";
    case DISTRIBUTION_HALF_COSINE: return "1/2*(1+cos(PI*x^n))";
    }
    return "";
  }
};
inline std::string GetCDIEnumValueDescription(CDI_DISTRIBUTION_TYPE ordinal)
{
  return sCDI_DISTRIBUTION_TYPE_UTILS::GetName(ordinal);
}



typedef struct sCDI_BOX_
{
#define CDI_CHUNK_TYPE_BOX_ CIO_BUILDCCCC('b', 'o', 'x', '_')
  CIO_CCCC  cccc;
  sCDI_VECOF_RGNS region;   // This contains a vector<> of indices, but there will always be only 1 element in the vector.
  sCDI_CVDP csys;           // csys
  sCDI_BBOX bbox;           // box relative to csys (but in lattice units)
  
  void ConvertToPRI(PRI::cBOX_VIA_CORNERS &pri_box) const;

} *CDI_BOX_;

#define CDI_CHUNK_TYPE_BOXS CIO_BUILDCCCC('b', 'o', 'x', 's')

struct sCDI_PGLB //particle_globals
{
#define CDI_CHUNK_TYPE_PGLB CIO_BUILDCCCC('p', 'g', 'l', 'b')
  CIO_CCCC cccc;
  sCDI_BOX_ dispersion_box;                 // description of a box_ geom
  sCDI_PARM gravity_csys;                   // reference to gravity csys
  sCDI_PARM gravity[3];                     // X,Y,Z components of gravity acceleration
  sCDI_PARM coupled_momentum_solver;        // Coupled Particle/Momentum Solver
  sCDI_PARM default_emitter_start;          // Default time at which to start emitting
  sCDI_PARM default_emitter_end;            // Default time at which to stop emitting
  sCDI_PARM default_max_particle_age;       // Particles older are dropped (-1 == unused)
  sCDI_PARM default_max_num_reflections;    // Too many bounces gets dropped
  sCDI_PARM default_min_particle_velocity;  // Slower than this gets dropped
  
  void ConvertToPRI(PRI::cPARAMETERS &pri_parameters) const;
};
typedef sCDI_PGLB* CDI_PGLB;


struct sCDI_ACCR //Parameters needed for ice accretion cases
{
#define CDI_CHUNK_TYPE_ACCR CIO_BUILDCCCC('a', 'c', 'c', 'r')
  CIO_CCCC cccc; 
  sCDI_PARM m_total_accretion_duration;             // extrusion_factor 
  sCDI_PARM m_liquid_water_content;                 // LWC
  cdiINT32 m_material_index = -1;                   // Index of particle material (should be liquid)
  sCDI_PARM m_accretion_material_density;           // density of accreted material
  cdiINT32 m_total_num_of_accretion_layers = -1;
  sCDI_PARM m_collection_duration_per_layer; 
  sCDI_PARM m_accretion_acceleration_factor;
  cdiINT32 m_meas_window_index = -1;                // Index of accretion surface measurement window
  double m_accretion_scale_factor = 0.0;               // scale factor 
  
  std::vector<cdiINT32> m_surface_ids;              // surface id  

  void cdi_read(CDI_INFO info);
  void cdi_write(CDI_INFO info) const;
  //Keep the following imlementations in dump_cdi.cc and undump_cdi.cc up to date.
  //void print_accr(CDI_INFO cdi_info, int depth);
  //void undump_accr(CDI_INFO info);

};
typedef sCDI_ACCR* CDI_ACCR;




//========================================================================================
class cCDI_FOUR_PARAMETER_DISTRIBUTION
{
protected: // ................................ You can't use me directly.       
  cCDI_FOUR_PARAMETER_DISTRIBUTION()
  {
    m_distribution.value = -1.0;
    m_param1.value = 0.0;
    m_param2.value = 0.0;
	m_param3.value = 0.0;
	m_param4.value = -1.0;
  }
  virtual ~cCDI_FOUR_PARAMETER_DISTRIBUTION() { }

protected:
  void SetDistribution(CDI_DISTRIBUTION_TYPE distributionType, const sCDI_PARM& param1, const sCDI_PARM& param2)
  {
    m_distribution.value = distributionType;
    m_param1 = param1;
    m_param2 = param2;
  }

  void SetDistribution(CDI_DISTRIBUTION_TYPE distributionType, const sCDI_PARM& param1, const sCDI_PARM& param2, const sCDI_PARM& param3, const sCDI_PARM& param4)
  {
    m_distribution.value = distributionType;
    m_param1 = param1;
    m_param2 = param2;
	m_param3 = param3;//PR_48032
	m_param4 = param4;
  }


public:

  void GetParameterValues(sCDI_PARM* pParam1, sCDI_PARM* pParam2) const
  {
    assert(pParam1 && pParam2);
    *pParam1 = m_param1;
    *pParam2 = m_param2;
  }

  void GetParameterValues(sCDI_PARM* pParam1, sCDI_PARM* pParam2, sCDI_PARM* minDia, sCDI_PARM* maxDia) const
  {
    assert(pParam1 && pParam2 && minDia && maxDia);
    *pParam1 = m_param1;
    *pParam2 = m_param2;
    *minDia = m_param3;//PR_48032
    *maxDia = m_param4;
  }

  sCDI_PARM GetDistributionType() const
  {
    return m_distribution;
  }

  void WriteToCDI(CDI_INFO info) const;
  void ReadFromCDI(CDI_INFO info, const char* chunkName, cdiINT32* pIndex);

private:
  sCDI_PARM m_distribution;         // CDI_DISTRIBUTION_TYPE
  sCDI_PARM m_param1;               // First parameter, typically mean value, characteristic value
  sCDI_PARM m_param2;               // Second parameter, typically range, stddev, shape, etc.
  sCDI_PARM m_param3;               // third parameter, minimum diameter. //PR_48032
  sCDI_PARM m_param4;               // fourth parameter, maximum diameter.
};

class cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION : public cCDI_FOUR_PARAMETER_DISTRIBUTION
{
public:
  // sCDI_PARM GetDistributionType() const;   (in base class)
  void GetNoneDistribution(sCDI_PARM* pMean) const;
  void GetUniformDistribution(sCDI_PARM* pMean, sCDI_PARM* pRange) const;
  void GetGaussianDistribution(sCDI_PARM* pMean, sCDI_PARM* pStdDev) const;
  void GetGammaDistribution(sCDI_PARM* pMean, sCDI_PARM* pShapeParam) const;
  void GetGaussianDistribution(sCDI_PARM* pMean, sCDI_PARM* pStdDev, sCDI_PARM* mindia, sCDI_PARM* maxDia) const;//PR_48032
  void GetGammaDistribution(sCDI_PARM* pMean, sCDI_PARM* pShapeParam, sCDI_PARM* mindia, sCDI_PARM* maxDia) const;//PR_48032

  void SetNoneDistribution(const sCDI_PARM& mean);
  void SetUniformDistribution(const sCDI_PARM& mean, const sCDI_PARM& range);
  void SetGaussianDistribution(const sCDI_PARM& mean, const sCDI_PARM& stdDev);
  void SetGammaDistribution(const sCDI_PARM& mean, const sCDI_PARM& shapeParam);
  void SetGaussianDistribution(const sCDI_PARM& mean, const sCDI_PARM& stdDev, const sCDI_PARM& minDia, const sCDI_PARM& maxDia); //PR_48032
  void SetGammaDistribution(const sCDI_PARM& mean, const sCDI_PARM& shapeParam, const sCDI_PARM& minDia, const sCDI_PARM& maxDia); //PR_48032
};
class cCDI_PARTICLE_ANGLE_DISTRIBUTION : public cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION
{
public:
  // Pointless wrappers for Gamma getter/setter, but NOTE THE DIFFERENT MEANINGS OF THE ARGUMENTS!
  void GetGammaDistribution(sCDI_PARM* pMean, sCDI_PARM* pStdDev) const
  {
    cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::GetGammaDistribution(pMean, pStdDev);
  }
  void SetGammaDistribution(const sCDI_PARM& mean, const sCDI_PARM& stdDev)
  {
    cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::SetGammaDistribution(mean, stdDev);
  }
};
typedef cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION cCDI_PARTICLE_DENSITY_DISTRIBUTION;
typedef cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION cCDI_PARTICLE_VELOCITY_DISTRIBUTION;


//========================================================================================
enum CDI_PARTICLE_MATERIAL_TYPE {
  PARTICLE_MATERIAL_LIQUID = 0,
  PARTICLE_MATERIAL_SOLID,
  NUM_PARTICLE_MATERIAL_TYPES
};

struct sCDI_PRMT //particle material
{
#define CDI_CHUNK_TYPE_PRMT CIO_BUILDCCCC('p', 'r', 'm', 't')
  CIO_CCCC cccc;
  std::string name;
  sCDI_PARM type;                     // int selecting particle material type enum
  sCDI_PARM viscosity;                // Viscosity
  sCDI_PARM surface_tension;          // Surface Tension
  cCDI_PARTICLE_DENSITY_DISTRIBUTION density_info;
  sCDI_PARM breakup_model;            // 0 == enabled, 1 == disabled

  void ConvertToPRI(PRI::cPARTICLE_MATERIAL &pri_material) const;

};
typedef sCDI_PRMT *CDI_PRMT;

// wind profile data table
typedef struct sCDI_WPDT
{
#define CDI_CHUNK_TYPE_WPDT CIO_BUILDCCCC('w', 'p', 'd', 't')
  CIO_CCCC cccc;
  cdiINT32 index; /* the index of wind profile data table (unique) */
  char* filename; /* the original name of the input wind profile data file (may not be unique) */
  cdiINT32 n_timesteps; /* the number of timesteps */
  double *velocity; /* the velocities in 3-coordinate directions */
} *CDI_WPDT;

// a collection of wind profile data tables
typedef struct sCDI_WPTS {
#define CDI_CHUNK_TYPE_WPTS CIO_BUILDCCCC('w', 'p', 't', 's')
  CIO_CCCC cccc;
  CDI_WPDT wpd_tables;
} *CDI_WPTS;


enum CDI_EMISSION_RATE_TYPE
{
  EMISSION_RATE_NONE = -1,
  EMISSION_RATE_MASSFLOW,
  EMISSION_RATE_PARTICLE,
  EMISSION_RATE_VOLUMETRIC,
  EMISSION_RATE_MASSFLOW_PER_UNIT_VOLUME,  // Used in old Volume Emitter Model pset (preserved for backwards compatibility)
  EMISSION_RATE_DEPTH,
  EMISSION_RATE_LWC
};

struct sCDI_EMISSION_RATE_TYPE_UTILS
{
  static std::string GetName(CDI_EMISSION_RATE_TYPE ordinal) {
    switch (ordinal) {
    case EMISSION_RATE_NONE: return "None";
    case EMISSION_RATE_MASSFLOW: return "Mass Flow Rate";
    case EMISSION_RATE_PARTICLE: return "Particle Rate";
    case EMISSION_RATE_VOLUMETRIC: return "Volumetric Flow Rate";
    case EMISSION_RATE_MASSFLOW_PER_UNIT_VOLUME: return "Mass Flow Rate Per Unit Volume";
    case EMISSION_RATE_DEPTH: return "Depth Rate";
    case EMISSION_RATE_LWC: return "Liquid Water Content";
    }
    return "";
  }
};
inline std::string GetCDIEnumValueDescription(CDI_EMISSION_RATE_TYPE ordinal)
{
  return sCDI_EMISSION_RATE_TYPE_UTILS::GetName(ordinal);
}

//========================================================================================
// Emitter Configurations
//========================================================================================
#define CDI_CHUNK_TYPE_ECFG CIO_BUILDCCCC('e', 'c', 'f', 'g')

struct sCDI_EMITTER_CONFIG_BASE;
struct sCDI_NOZZLE_EMITTER_CONFIG;
struct sCDI_RAIN_EMITTER_CONFIG;
struct sCDI_TIRE_EMITTER_CONFIG;

typedef class cCDI_EMITTER_CONFIGURATIONS
{
public:
  cCDI_EMITTER_CONFIGURATIONS()
  { }
  virtual ~cCDI_EMITTER_CONFIGURATIONS();
private:
  cCDI_EMITTER_CONFIGURATIONS(const cCDI_EMITTER_CONFIGURATIONS& other);  // Prohibited

public:
  enum eEMITTER_CONFIGURATION_TYPE
  {
    UNKNOWN = -1,
    NOZZLE,
    RAIN,
    TIRE
  };
  static std::string GetTypeName(eEMITTER_CONFIGURATION_TYPE ordinal) {
    switch (ordinal) {
    case UNKNOWN: return "Unknown";
    case NOZZLE: return "Nozzle";
    case RAIN: return "Rain";
    case TIRE: return "Tire";
    }
    return "";
  }

public:
  void AddConfiguration(sCDI_EMITTER_CONFIG_BASE* pConfig)
  { m_emitterConfigs.push_back(pConfig); }

  const std::vector<const sCDI_EMITTER_CONFIG_BASE*>& GetAllEmittersConfigs() const
  { return m_emitterConfigs; }

  std::vector<const sCDI_NOZZLE_EMITTER_CONFIG*> GetNozzleEmitterConfigs() const;
  std::vector<const sCDI_RAIN_EMITTER_CONFIG*> GetRainEmitterConfigs() const;
  std::vector<const sCDI_TIRE_EMITTER_CONFIG*> GetTireEmitterConfigs() const;

  void WriteToCDI(CDI_INFO cdi_info) const;
  void ReadFromCDI(CDI_INFO cdi_info);
  void ConvertToPRI(sCDI_INFO &cdi_info,
                    PRI::cPARAMETERS &pri_parameters,
                    std::vector<std::size_t> &cdi_to_pri_emitter_config_map) const; //Writes all emitter configurations to a pri object


private:
  std::vector<const sCDI_EMITTER_CONFIG_BASE*> m_emitterConfigs;
}* CDI_EMITTER_CONFIGURATIONS;

inline std::string GetCDIEnumValueDescription(cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE ordinal)
{
  return cCDI_EMITTER_CONFIGURATIONS::GetTypeName(ordinal);
}



struct sCDI_EMITTER_CONFIG_BASE
{
  virtual ~sCDI_EMITTER_CONFIG_BASE() {}

  CIO_CCCC GetChunkType() const
  { return CDI_CHUNK_TYPE_ECFG; }

  static cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE GetEmitterConfigType()
  { return cCDI_EMITTER_CONFIGURATIONS::UNKNOWN; }

  virtual cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE GetType() const
  { return GetEmitterConfigType(); }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);

  std::string name;
  sCDI_PARM material_index;                // Index of defined particle material
};


// Particle Diameter can be specified by some additional types of distribution
class cCDI_PARTICLE_DIAMETER_DISTRIBUTION : public cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION
{
public:
  void GetRosinRammlerDistribution(sCDI_PARM* pCharacteristicValue, sCDI_PARM* pExponent, sCDI_PARM* minDia, sCDI_PARM* maxDia) const;
  void GetLogNormalDistribution(sCDI_PARM* pMean, sCDI_PARM* pStdDev) const;
  void GetLogNormalDistribution(sCDI_PARM* pMean, sCDI_PARM* pStdDev, sCDI_PARM* minDia, sCDI_PARM* maxDia) const;
  void GetRosinRammlerVolumeFractionDistribution(sCDI_PARM* pCharacteristicValue, sCDI_PARM* pExponent, sCDI_PARM* minDia = nullptr, sCDI_PARM* maxDia = nullptr) const; //PR-48032

  void SetRosinRammlerDistribution(const sCDI_PARM& characteristicValue, const sCDI_PARM& exponent, const sCDI_PARM& minDia, const sCDI_PARM& maxDia);
  void SetRosinRammlerDistribution(const sCDI_PARM& characteristicValue, const sCDI_PARM& exponent);
  void SetLogNormalDistribution(const sCDI_PARM& mean, const sCDI_PARM& stdDev, const sCDI_PARM& minDia, const sCDI_PARM& maxDia);
  void SetLogNormalDistribution(const sCDI_PARM& mean, const sCDI_PARM& stdDev);
  void SetRosinRammlerVolumeFractionDistribution(const sCDI_PARM& characteristicValue, const sCDI_PARM& exponent, const sCDI_PARM& minDia, const sCDI_PARM& maxDia);//PR-48032
};


//========================================================================================
struct sCDI_NOZZLE_EMITTER_CONFIG : public sCDI_EMITTER_CONFIG_BASE
{
  typedef sCDI_EMITTER_CONFIG_BASE SUPER_EMITTER_CONFIG;

  typedef enum
  {
    NOZZLETYPE_NONE = -1,
    NOZZLETYPE_FULL_CONE,
    NOZZLETYPE_HOLLOW_CONE,
    NOZZLETYPE_ELLIPTICAL_CONE
  } CDI_EMITTER_NOZZLE_TYPE;
  static std::string GetTypeName(CDI_EMITTER_NOZZLE_TYPE ordinal) {
    switch (ordinal) {
    case NOZZLETYPE_NONE: return "None";
    case NOZZLETYPE_FULL_CONE: return "Full Cone";
    case NOZZLETYPE_HOLLOW_CONE: return "Hollow Cone";
    case NOZZLETYPE_ELLIPTICAL_CONE: return "Elliptic Cone (Flat Fan)";
    }
    return "";
  }

  static cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE GetEmitterConfigType()
  { return cCDI_EMITTER_CONFIGURATIONS::NOZZLE; }

  virtual cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE GetType() const
  { return GetEmitterConfigType(); }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  void ConvertToPRI(sCDI_INFO &cdi_info, 
                    PRI::cNOZZLE_EMITTER_CONFIGURATION &pri_emitter_config) const;

  sCDI_PARM nozzle_type;                   // CDI_EMITTER_NOZZLE_TYPE
  sCDI_PARM angle_distribution;            // CDI_DISTRIBUTION_TYPE
  sCDI_PARM cone_half_angle;
  sCDI_PARM mean_angle;
  sCDI_PARM angle_range;
  sCDI_PARM inner_half_angle_limit;     // Hollow cone w/ Gaussian dist.
  sCDI_PARM outer_half_angle_limit;     // Full/Hollow cone w/ Gaussian dist.
  sCDI_PARM major_half_angle;
  sCDI_PARM major_outer_half_angle_limit;    // Elliptical w/ Gaussian dist.
  sCDI_PARM minor_half_angle;
  sCDI_PARM minor_outer_half_angle_limit;    // Elliptical w/ Gaussian dist.
  cCDI_PARTICLE_VELOCITY_DISTRIBUTION velocity_info;
  cCDI_PARTICLE_DIAMETER_DISTRIBUTION particle_diam_info;
  sCDI_PARM emission_rate_type;              // CDI_EMISSION_RATE_TYPE
  sCDI_PARM emission_rate;
};

inline std::string GetCDIEnumValueDescription(sCDI_NOZZLE_EMITTER_CONFIG::CDI_EMITTER_NOZZLE_TYPE ordinal)
{
  return sCDI_NOZZLE_EMITTER_CONFIG::GetTypeName(ordinal);
}


struct sCDI_RAIN_EMITTER_CONFIG : public sCDI_EMITTER_CONFIG_BASE
{
  typedef sCDI_EMITTER_CONFIG_BASE SUPER_EMITTER_CONFIG;

  static cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE GetEmitterConfigType()
  { return cCDI_EMITTER_CONFIGURATIONS::RAIN; }

  virtual cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE GetType() const
  { return GetEmitterConfigType(); }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  void ConvertToPRI(PRI::cRAIN_EMITTER_CONFIGURATION& pri_emitter_config) const;

  cCDI_PARTICLE_DIAMETER_DISTRIBUTION particle_diam_info;
  sCDI_PARM emission_rate_type;               // CDI_EMISSION_RATE_TYPE
  sCDI_PARM emission_rate;
  sCDI_PARM wind_x_velocity;                 // Wind X Velocity for the rain emitter config
  sCDI_PARM wind_y_velocity;
  sCDI_PARM wind_z_velocity;
  std::string refFrameName; 
  cdiINT32 csysIndex;
  cdiINT32 refFrameIndex;
};

#define CDI_CHUNK_TYPE_TANP CIO_BUILDCCCC('t', 'a', 'n', 'p')
struct sCDI_TIRE_NOZZLE_PROPS
{
  CIO_CCCC GetChunkType() const
  { return CDI_CHUNK_TYPE_TANP; }

  void WriteToCDI(CDI_INFO cdi_info) const;
  void ReadFromCDI(CDI_INFO cdi_info);

  sCDI_PARM tire_arc_position;
  cCDI_PARTICLE_ANGLE_DISTRIBUTION emission_offset_angle_info;
  sCDI_PARM outer_half_angle_limit;
  sCDI_PARM transverse_stretch_factor;
  cCDI_PARTICLE_VELOCITY_DISTRIBUTION velocity_info;
  cCDI_PARTICLE_DIAMETER_DISTRIBUTION particle_diam_info;

  CDI_DISTRIBUTION_TYPE ConvertToPRI(PRI::cNOZZLE_PROPERTIES& pri_nozzle_property, 
                                     PRI::cANGLE_NOZZLE_PROP_MAP &pri_nozzle_angle_map_element) const;

};

struct sCDI_TIRE_EMITTER_CONFIG : public sCDI_EMITTER_CONFIG_BASE
{
  typedef sCDI_EMITTER_CONFIG_BASE SUPER_EMITTER_CONFIG;

  static cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE GetEmitterConfigType()
  { return cCDI_EMITTER_CONFIGURATIONS::TIRE; }

  virtual cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE GetType() const
  { return GetEmitterConfigType(); }

  void AddTireNozzle(sCDI_TIRE_NOZZLE_PROPS& tireNozzle)
  { m_tireNozzles.push_back(tireNozzle); }
  const std::vector<sCDI_TIRE_NOZZLE_PROPS>& GetTireNozzles() const
  { return m_tireNozzles; }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  void ReadFromCDI(CDI_INFO cdi_info, asINT32 itemCount, cdiINT32* pIndex);
  void ConvertToPRI(PRI::cTIRE_EMITTER_CONFIGURATION& pri_emitter_config,
                    std::vector<PRI::cNOZZLE_PROPERTIES> &pri_arc_station_properties,
                    std::vector<PRI::cANGLE_NOZZLE_PROP_MAP> &pri_nozzle_station_map) const;

  sCDI_PARM GetSpatialEmissionDistributionType() const
  {
    return spatial_emission_distribution;
  }

  sCDI_PARM GetSpatialEmissionParameter() const
  {
    return spatial_emission_distribution_parameter;
  }

  sCDI_PARM GetSpatialEmissionDistributionParameter() const;
  sCDI_PARM GetSpatialEmissionGaussianStandardDeviation() const;
  sCDI_PARM GetSpatialEmission1MXExponent() const;
  sCDI_PARM GetSpatialEmissionHalfCosineExponent() const;

  // These set the appropriate distribution type.
  void SetSpatialEmissionLinearDistribution();
  void SetSpatialEmissionGaussianDistribution(const sCDI_PARM& stdDev);
  void SetSpatialEmission1MXDistribution(const sCDI_PARM& exponent);
  void SetSpatialEmissionHalfCosineDistribution(const sCDI_PARM& exponent);

  sCDI_PARM emission_rate_type;              // CDI_EMISSION_RATE_TYPE
  sCDI_PARM emission_rate;
  sCDI_PARM emission_rate_ratio;

private:
  sCDI_PARM spatial_emission_distribution;             // CDI_DISTRIBUTION_TYPE
  sCDI_PARM spatial_emission_distribution_parameter;   // Standard Deviation or Exponent,
                                                       // depending on distribution type.

private:
  std::vector<sCDI_TIRE_NOZZLE_PROPS> m_tireNozzles;
};


// Helper method, used by cCDI_EMITTER_CONFIGURATIONS methods.
// I couldn't make it a method of that class, due to inter-class dependencies
// The template member method would want a _full_ definition of sCDI_EMITTER_CONFIG_BASE
// to precede the cCDI_EMITTER_CONFIGURATIONS class definition.  But sCDI_EMITTER_CONFIG_BASE
// needs to be after cCDI_EMITTER_CONFIGURATIONS, since it uses an enum eEMITTER_CONFIGURATION_TYPE
// defined in cCDI_EMITTER_CONFIGURATIONS.  (And you can't predeclare an enum  :-( ).
template <typename T> std::vector<const T*> GetEmitterConfigsOfType(const cCDI_EMITTER_CONFIGURATIONS& emitterConfigurations)
{
  std::vector<const T*> emitterConfigsOfType;
  const std::vector<const sCDI_EMITTER_CONFIG_BASE*>& allConfigs = emitterConfigurations.GetAllEmittersConfigs();
  for (size_t ice = 0; ice < allConfigs.size(); ice++)
  if (allConfigs[ice]->GetType() == T::GetEmitterConfigType())
    emitterConfigsOfType.push_back(dynamic_cast<const T*>(allConfigs[ice]));
  return emitterConfigsOfType;
}


//========================================================================================
// Particle Emitters
//========================================================================================
struct sCDI_PARTICLE_EMITTER_BASE;
struct sCDI_SURFACE_EMITTER;
struct sCDI_VOLUME_EMITTER;
struct sCDI_POINT_EMITTER;
struct sCDI_TIRE_EMITTER;
struct sCDI_RAIN_EMITTER;

class cCDI_PARTICLE_EMITTERS
  // Holds all the Emitters found in a CDI file.
  // Contains a separate std::vector<> for each emitter type.
{
public:
  cCDI_PARTICLE_EMITTERS()
  { }
  virtual ~cCDI_PARTICLE_EMITTERS();
private:
  cCDI_PARTICLE_EMITTERS(const cCDI_PARTICLE_EMITTERS& other);  // Prohibited

public:
  enum eCDI_EMITTER_TYPE
  {
    EMITTER_UNKNOWN = -1,
    EMITTER_SURFACE,
    EMITTER_VOLUME,
    EMITTER_POINT,
    EMITTER_TIRE,
    EMITTER_RAIN
  };
  static std::string GetTypeName(eCDI_EMITTER_TYPE ordinal) {
    switch (ordinal) {
    case EMITTER_UNKNOWN: return "Unknown";
    case EMITTER_SURFACE: return "Surface";
    case EMITTER_VOLUME: return "Volume";
    case EMITTER_POINT: return "Point";
    case EMITTER_RAIN: return "Rain";
    case EMITTER_TIRE: return "Tire";
    }
    return "";
  }

public:
  void AddEmitter(const sCDI_PARTICLE_EMITTER_BASE* pEmitter)
  { m_emitters.push_back(pEmitter); }

  std::vector<const sCDI_SURFACE_EMITTER*> GetSurfaceEmitters() const;
  std::vector<const sCDI_VOLUME_EMITTER*> GetVolumeEmitters() const;
  std::vector<const sCDI_POINT_EMITTER*> GetPointEmitters() const;
  std::vector<const sCDI_TIRE_EMITTER*> GetTireEmitters() const;
  std::vector<const sCDI_RAIN_EMITTER*> GetRainEmitters() const;

  const std::vector<const sCDI_PARTICLE_EMITTER_BASE*>& GetAllEmitters() const
  { return m_emitters; }
 
  void UpdateEmitterTime(const std::vector<asINT32> &real_emitter_start_time, const std::vector<asINT32> &real_emitter_end_time); 
  void WriteToCDI(CDI_INFO cdi_info) const;
  void ReadFromCDI(CDI_INFO cdi_info);

  //Writes all emitters into a PRI file
  void ConvertToPRI(PRI::cPARAMETERS &pri_parameters, 
                    const std::vector<std::string> &region_names,
                    const std::vector<std::string> &sri_face_names, 
                    const std::vector<std::size_t> &cdi_to_pri_emitter_configuration_map,
                    const std::vector<asINT32> &real_emitter_start_time,
                    const std::vector<asINT32> &real_emitter_end_time
                    );


private:
  std::vector<const sCDI_PARTICLE_EMITTER_BASE*> m_emitters;
};

inline std::string GetCDIEnumValueDescription(cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE ordinal)
{
  return cCDI_PARTICLE_EMITTERS::GetTypeName(ordinal);
}


struct sCDI_PARTICLE_EMITTER_BASE
{
#define CDI_CHUNK_TYPE_PEMT CIO_BUILDCCCC('p', 'e', 'm', 't')

  virtual ~sCDI_PARTICLE_EMITTER_BASE()
  { }

  CIO_CCCC GetChunkType() const
  { return CDI_CHUNK_TYPE_PEMT; }

  static cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetEmitterType()
  { return cCDI_PARTICLE_EMITTERS::EMITTER_UNKNOWN; }

  virtual cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetType() const
  { return GetEmitterType(); }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  virtual void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);

  std::string name;                     // duh
  sCDI_PARM emitter_configuration;      // Index of emitter configuration (within a configuration *type*)
  sCDI_PARM particles_per_parcel;       // Number of particles per parcel
  sCDI_PARM subject_to_dispersion_box;  // should particles outside the dispersion box be discarded?
  sCDI_PARM subject_to_gravity;         // include global gravity force on the particles?
  eCDI_MEAS_START_TIME_VIA::Enum start_via;
  std::vector<cdiINT32> monitors;
  sCDI_PARM start;                      // Time at which to start emitting
  eCDI_MEAS_END_TIME_VIA::Enum end_via;
  sCDI_PARM end;
  sCDI_PARM duration;
  sCDI_PARM max_age;                    // Particles older are dropped (-1 == unused)
  sCDI_PARM max_num_reflections;        // Too many bounces gets dropped
  sCDI_PARM min_particle_velocity;      // Slower than this gets dropped
  sCDI_PARM visible;                    // Not needed for particle stuff directly, but keep for
                                        // downstream clients (PRI) and round-trip purposes.
};

//========================================================================================
struct sCDI_EMITTER_NOZZLE_ORIENTATION
{
  sCDI_EMITTER_NOZZLE_ORIENTATION()
  {
    mean_spray_direction.resize(3);
  }

  void WriteToCDI(CDI_INFO info) const;
  void ReadFromCDI(CDI_INFO info, const char* chunkName, cdiINT32* pIndex);

  sCDI_PARM spray_direction_csys;                  // (index of) Coordinate system used for direction specification
  std::vector<sCDI_PARM> mean_spray_direction;     // X,Y,Z components of mean emission direction
  sCDI_PARM elliptical_nozzle;                     // 1, if the emitter's nozzle configuration is elliptical, else 0
  std::vector<sCDI_PARM> major_ellipse_direction;  // X,Y,Z components of elliptical nozzle major axis direction - size() == 3, if nozzle is elliptical
                                                   //                                                            - otherwise, empty
};


//========================================================================================
struct sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS
{
  sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS();

  void WriteToCDI(CDI_INFO info) const;
  void ReadFromCDI(CDI_INFO info, const char* chunkName, cdiINT32* pIndex);

  sCDI_PARM nozzle_show;
  std::string nozzle_cone_look;
  std::string nozzle_body_color;
  std::string nozzle_arrow_look;
  sCDI_PARM nozzle_size;
};


//========================================================================================
struct sCDI_EMITTER_EMISSION_BOUNDARY_VISIBILITY_SETTINGS
{
  sCDI_EMITTER_EMISSION_BOUNDARY_VISIBILITY_SETTINGS();

  void WriteToCDI(CDI_INFO info) const;
  void ReadFromCDI(CDI_INFO info, const char* chunkName, cdiINT32* pIndex);

  sCDI_PARM emission_boundary_show;
  std::string emission_boundary_look;
  sCDI_PARM emission_boundary_size;
};


//========================================================================================
struct sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS
{
  sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS();

  void WriteToCDI(CDI_INFO info) const;
  void ReadFromCDI(CDI_INFO info, const char* chunkName, cdiINT32* pIndex);

  bool emission_point_show;
  std::string emission_point_color;
  sCDI_PARM emission_point_size;
};


//========================================================================================
struct sCDI_SURFACE_EMITTER : public sCDI_PARTICLE_EMITTER_BASE
{
  typedef sCDI_PARTICLE_EMITTER_BASE SUPER_EMITTER;

  static cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetEmitterType()
  { return cCDI_PARTICLE_EMITTERS::EMITTER_SURFACE; }

  virtual cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetType() const
  { return GetEmitterType(); }

  const sCDI_NOZZLE_EMITTER_CONFIG& GetConfiguration(const cCDI_EMITTER_CONFIGURATIONS& configurations) const
  {
    assert(size_t(sCDI_PARTICLE_EMITTER_BASE::emitter_configuration.value) < configurations.GetAllEmittersConfigs().size());
    return *(dynamic_cast<const sCDI_NOZZLE_EMITTER_CONFIG*>(
      configurations.GetAllEmittersConfigs()[size_t(sCDI_PARTICLE_EMITTER_BASE::emitter_configuration.value)]));
  }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  virtual void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  virtual void ConvertToPRI(PRI::cSURFACE_EMITTER& pri_emitter) const; //Write data from this struct to a PRI::cSURFACE_EMITTER class
 

  cCDI_GEOM_SELECTION_TREE geom_selection
    { 0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face};         // Geometry (face) selection
  sCDI_PARM fixed_release_points;                          // 0: emit from random locations on the surface, 1: emit from facet centers
  sCDI_PARM user_specified_nozzle_orientation;             // 0: emission direction determined by surface normals, 1: use nozzle_orientation
  sCDI_EMITTER_NOZZLE_ORIENTATION nozzle_orientation;      // use if user_specified_nozzle_orientation == 1, OR use parts of this, if nozzle is elliptical
  sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS nozzle_visibility_settings;   // Settings for PowerVIZ
  sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS emission_point_visibility_settings;   // Settings for PowerVIZ
};


//========================================================================================
// Chunk to nest multiple CYL_ chunks for particle emitter regions
#define CDI_CHUNK_TYPE_CYLZ CIO_BUILDCCCC('c', 'y', 'l', 'z')
struct sCDI_CYL_
{
#define CDI_CHUNK_TYPE_CYL_ CIO_BUILDCCCC('c', 'y', 'l', '_')

  sCDI_CYL_()
  {
    start_point.resize(3);
    end_point.resize(3);
  }

  CIO_CCCC GetChunkType() const
  { return CDI_CHUNK_TYPE_CYL_; }

  void WriteToCDI(CDI_INFO cdi_info) const;
  void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  void ConvertToPRI(PRI::cCYLINDER_VIA_ENDPOINTS &pri_cylinder) const;

  std::string name;
  sCDI_VECOF_RGNS region;               // This contains a vector<> of indices, but there will always be only 1 element in the vector.
  std::vector<sCDI_PARM> start_point;   // X,Y,Z coordinates of center of face at one end of cylinder
  std::vector<sCDI_PARM> end_point;     // X,Y,Z coordinates of center of face at opposite end of cylinder
  sCDI_PARM start_radius;
  sCDI_PARM end_radius;
  sCDI_PARM num_sides;
};


//========================================================================================
struct sCDI_VOLUME_EMITTER : public sCDI_PARTICLE_EMITTER_BASE
{
  typedef sCDI_PARTICLE_EMITTER_BASE SUPER_EMITTER;

  static cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetEmitterType()
  { return cCDI_PARTICLE_EMITTERS::EMITTER_VOLUME; }

  virtual cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetType() const
  { return GetEmitterType(); }

  const sCDI_NOZZLE_EMITTER_CONFIG& GetConfiguration(const cCDI_EMITTER_CONFIGURATIONS& configurations) const
  {
    assert(size_t(sCDI_PARTICLE_EMITTER_BASE::emitter_configuration.value) < configurations.GetAllEmittersConfigs().size());
    return *(dynamic_cast<const sCDI_NOZZLE_EMITTER_CONFIG*>(
      configurations.GetAllEmittersConfigs()[size_t(sCDI_PARTICLE_EMITTER_BASE::emitter_configuration.value)]));
  }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  virtual void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  virtual void ConvertToPRI(PRI::cVOLUME_EMITTER& pri_emitter) const; //Write data from this struct to a PRI::cVOLUME_EMITTER class
 
 

  cCDI_GEOM_SELECTION_TREE geom_selection
    { 0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part };    // Geometry (part) selection
  sCDI_EMITTER_NOZZLE_ORIENTATION nozzle_orientation;  // Mean emission direction, and elliptical nozzle orientation if applicable.
  sCDI_PARM fixed_release_points;                      // 0: emit from random locations in the volume, 1: emit from locations at fixed spacing
  std::vector<sCDI_PARM> release_spacing;              // X,Y,Z components of release point spacing - size() == 3, if fixed_release_points == 1
                                                       //                                           - otherwise, empty
  std::vector<sCDI_CYL_> m_cylGeometries;              // All cylinder-type geometries that are part of the emissions volume
                                                       // Prints out extra box corner info. for downstream CDI user convenience.
  std::vector<sCDI_BOX_> m_boxGeometries;              // All box-type geometries that are part of the emissions volume
                                                       // Prints out extra box corner info. for downstream CDI user convenience.
  sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS nozzle_visibility_settings;                         // Settings for PowerVIZ
  sCDI_EMITTER_EMISSION_BOUNDARY_VISIBILITY_SETTINGS emission_boundary_visibility_settings;   // More settings for PowerVIZ
  sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS emission_point_visibility_settings;   // Settings for PowerVIZ
};

//========================================================================================
struct sCDI_POINT_EMITTER : public sCDI_PARTICLE_EMITTER_BASE
{
  typedef sCDI_PARTICLE_EMITTER_BASE SUPER_EMITTER;

  static cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetEmitterType()
  { return cCDI_PARTICLE_EMITTERS::EMITTER_POINT; }

  virtual cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetType() const
  { return GetEmitterType(); }

  const sCDI_NOZZLE_EMITTER_CONFIG& GetConfiguration(const cCDI_EMITTER_CONFIGURATIONS& configurations) const
  {
    assert(size_t(sCDI_PARTICLE_EMITTER_BASE::emitter_configuration.value) < configurations.GetAllEmittersConfigs().size());
    return *(dynamic_cast<const sCDI_NOZZLE_EMITTER_CONFIG*>(
      configurations.GetAllEmittersConfigs()[size_t(sCDI_PARTICLE_EMITTER_BASE::emitter_configuration.value)]));
  }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  virtual void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  virtual void ConvertToPRI(PRI::cPOINT_EMITTER& pri_emitter) const;//Write data from this struct to a PRI::cPOINT_EMITTER class
 

  sCDI_VECOF_PNTS points;                                               // Emission points
  cdiINT32 preferred_csys_index;                                        // The PowerCASE coordinate system used to define _most_ of the points
  sCDI_EMITTER_NOZZLE_ORIENTATION nozzle_orientation;                   // Mean emission direction, and elliptical nozzle orientation if applicable.
  sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS nozzle_visibility_settings;   // Settings for PowerVIZ
};

//========================================================================================
struct sCDI_TIRE_EMITTER : public sCDI_PARTICLE_EMITTER_BASE
{
  typedef sCDI_PARTICLE_EMITTER_BASE SUPER_EMITTER;

  sCDI_TIRE_EMITTER()
  {
    zero_angle_direction.resize(3);
    approx_rotation_axis_dir.resize(3);
    tire_tread_look = "d:#c0c0c0 s:#303030 e:#000000 g:128.000000 t:0.000000";
  }

  static cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetEmitterType()
  { return cCDI_PARTICLE_EMITTERS::EMITTER_TIRE; }

  virtual cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetType() const
  { return GetEmitterType(); }

  const sCDI_TIRE_EMITTER_CONFIG& GetConfiguration(const cCDI_EMITTER_CONFIGURATIONS& configurations) const
  {
    assert(size_t(sCDI_PARTICLE_EMITTER_BASE::emitter_configuration.value) < configurations.GetAllEmittersConfigs().size());
    return *(dynamic_cast<const sCDI_TIRE_EMITTER_CONFIG*>(
      configurations.GetAllEmittersConfigs()[size_t(sCDI_PARTICLE_EMITTER_BASE::emitter_configuration.value)]));
  }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  virtual void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  virtual void ConvertToPRI(PRI::cTIRE_EMITTER& pri_emitter) const;

  cCDI_GEOM_SELECTION_TREE geom_selection
    { 0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part }; // Geometry (part) selection
  sCDI_PARM zero_angle_direction_csys;              // (index of) coordinate system used to specify zero angle direction
  // The following two parameters are specified as discrete user choices ("X", "-X", "Y", "-Y", "Z", "-Z") in the CASE UI.
  // They will be stored here in vector form: (1,0,0), (-1,0,0), (0,1,0), (0,-1,0), (0,0,1), (0,0,-1)
  std::vector<sCDI_PARM> zero_angle_direction;      // direction of "zero angle" along tire arc for tire nozzle locations.  size() always == 3.
  std::vector<sCDI_PARM> approx_rotation_axis_dir;  // approximate direction of axis of tire rotation (used for tire nozzle angle interpretation).  size() always == 3.
  std::vector<sCDI_CYL_> m_cylGeometries;           // All cylinder-type geometries that are part of the emissions volume
                                                    // Prints out extra box corner info. for downstream CDI user convenience.
  sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS nozzle_visibility_settings;                         // Settings for PowerVIZ
  sCDI_EMITTER_EMISSION_BOUNDARY_VISIBILITY_SETTINGS emission_boundary_visibility_settings;   // More settings for PowerVIZ
  sCDI_PARM tire_tread_show;
  std::string tire_tread_look;
  sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS emission_point_visibility_settings;   // Settings for PowerVIZ
};

//========================================================================================
struct sCDI_RAIN_EMITTER : public sCDI_PARTICLE_EMITTER_BASE
{
  typedef sCDI_PARTICLE_EMITTER_BASE SUPER_EMITTER;

  sCDI_RAIN_EMITTER()
  { subject_to_gravity.value = 1; }    // The Rain Emitter has no UI for this in CASE, but for convenience of downstream applications,
                                       // just include it and always set it to "true".

  static cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetEmitterType()
  { return cCDI_PARTICLE_EMITTERS::EMITTER_RAIN; }

  virtual cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE GetType() const
  { return GetEmitterType(); }

  const sCDI_RAIN_EMITTER_CONFIG& GetConfiguration(const cCDI_EMITTER_CONFIGURATIONS& configurations) const
  {
    assert(size_t(sCDI_PARTICLE_EMITTER_BASE::emitter_configuration.value) < configurations.GetAllEmittersConfigs().size());
    return *(dynamic_cast<const sCDI_RAIN_EMITTER_CONFIG*>(
      configurations.GetAllEmittersConfigs()[size_t(sCDI_PARTICLE_EMITTER_BASE::emitter_configuration.value)]));
  }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  virtual void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  virtual void ConvertToPRI(PRI::cRAIN_EMITTER& pri_emitter) const;

  cCDI_GEOM_SELECTION_TREE geom_selection
    { 0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part }; // Geometry (part) selection
  std::vector<sCDI_CYL_> m_cylGeometries;     // All cylinder-type geometries that are part of the emissions volume
                                              // Prints out extra box corner info. for downstream CDI user convenience.
  std::vector<sCDI_BOX_> m_boxGeometries;     // All box-type geometries that are part of the emissions volume
                                              // Prints out extra box corner info. for downstream CDI user convenience.
  sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS nozzle_visibility_settings;                         // Settings for PowerVIZ
  sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS emission_point_visibility_settings;   // Settings for PowerVIZ
};


// Helper method, used by cCDI_PARTICLE_EMITTERS methods.
// Similar to one created for cCDI_EMITTER_CONFIGURATIONS.
template <typename T> std::vector<const T*> GetEmittersOfType(const cCDI_PARTICLE_EMITTERS& emitters)
{
  std::vector<const T*> emittersOfType;
  const std::vector<const sCDI_PARTICLE_EMITTER_BASE*>& allEmitters = emitters.GetAllEmitters();
  for (size_t ie = 0; ie < allEmitters.size(); ie++)
  if (allEmitters[ie]->GetType() == T::GetEmitterType())
    emittersOfType.push_back(dynamic_cast<const T*>(allEmitters[ie]));
  return emittersOfType;
}


//========================================================================================
// Surface Material Interaction
//========================================================================================

typedef struct sCDI_SRMI
{
#define CDI_CHUNK_TYPE_SRMI CIO_BUILDCCCC('s', 'r', 'm', 'i')
  CIO_CCCC cccc;
  sCDI_PARM particle_material;
  sCDI_PARM splash_model;                // 0 == enabled, 1 == disabled
  sCDI_PARM enable_reflection;           // 0 == enabled, 1 == disabled
  sCDI_PARM reflect_min_momentum;        // Lower momentum won't reflect
  sCDI_PARM reflect_min_normal_vel;      // Lower norm. velocity won't reflect
  sCDI_PARM reflect_min_angle;           // Smaller angle won't reflect
  sCDI_PARM normal_rest_coeff;           // Bounciness along normal
  sCDI_PARM tang_restitution_coeff;      // Bounciness along tangent
  sCDI_PARM scatter_angle_distribution;  // Int selecting distribution enum
  sCDI_PARM scatter_angle_range;         // Range or stdev based on distribution


  void ConvertToPRI(asINT32 index, std::string name, PRI::cSURFACE_MATERIAL_INTERACTIONS &pri_surface_interaction, 
                    PRI::cPARTICLE_SURFACE_INTERACTION &pri_particle_surface_interaction) const;

} *CDI_SRMI;

typedef struct sCDI_SCRN
{
#define CDI_CHUNK_TYPE_SCRN CIO_BUILDCCCC('s', 'c', 'r', 'n')
  CIO_CCCC cccc;
  std::string name;
  cCDI_GEOM_SELECTION_TREE geom_selection
    { 0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face };         // Geometry (face) selection
  sCDI_PARM opening_size;                // Size of screen openings
  sCDI_PARM pass_thru_fraction;          // Fraction smaller than size which pass through
  sCDI_PARM surface_material;            // Index of surface material of this screen
  sCDI_PARM measured_fraction;          // Fraction of particles to be made measurable in trajectory windows (PR42469)
  void ConvertToPRI(PRI::cPARAMETERS &pri_parameters,
                    const std::vector<std::string> &face_names,
                    const cCDI_PARTITIONS& partitions);
  //depreciated due to PR52104:
  void ConvertToPRI(PRI::cPARAMETERS &pri_parameters,
                    const std::vector<std::string> &face_names);

} *CDI_SCRN;

// Virtual Wiper
namespace eCDI_WIPER_TYPE {
enum Enum{
    Pivot,
    Pantograph
};

static std::string GetName(Enum ordinal) {
    switch (ordinal) {
      case Pivot: return "Pivot";
      case Pantograph: return "Pantograph";
    }
    return "";
}
}

inline std::string GetCDIEnumValueDescription(eCDI_WIPER_TYPE::Enum ordinal)
{
  return eCDI_WIPER_TYPE::GetName(ordinal);
}

typedef struct sCDI_WIPR
{
#define CDI_CHUNK_TYPE_WIPR CIO_BUILDCCCC('w', 'i', 'p', 'r')
  std::string name;
  cCDI_GEOM_SELECTION_TREE wiper_blade_geoms
    { 0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face };         // Wiper blade Geometry (face) selection
  cCDI_GEOM_SELECTION_TREE wiped_surface_geoms
    { 0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face };         // Wiped surface Geometry (face) selection
  double wiper_axis_origin[3];         // wiper axis
  double wiper_axis_dir[3];
  double inner_endpoint[3];            // endpoints to establish the wiper plane
  double outer_endpoint[3];
  eCDI_WIPER_TYPE::Enum wiper_type;    // wiper type
  sCDI_PARM import_angle;              // angles
  sCDI_PARM start_angle;
  sCDI_PARM end_angle;
  sCDI_PARM stroke_duration;           // time
  sCDI_PARM delay;
  eCDI_MEAS_START_TIME_VIA::Enum start_via;
  std::vector<cdiINT32> monitors;
  cdiINT32 emitter_index;
  sCDI_PARM emitter_delay;
  sCDI_PARM initial_delay;
  sCDI_PARM num_strokes;
} *CDI_WIPR;

typedef struct sCDI_AFTD
{ /* water film thickness acceleration */
#define CDI_CHUNK_TYPE_AFTD CIO_BUILDCCCC('a', 'f', 't', 'd')
  CIO_CCCC cccc;
  double acceleration_factor;
} *CDI_AFTD;

// Deforming Tires..............................................................................
// A deforming tire consists of two parametric surfaces: a wrap and a carcass.
// The knots are the same between them, but the coefficients are not. 
// If it is a grooved tire, then we don't need the wrap surface. 
typedef struct sCDI_TIRE
{
#define CDI_CHUNK_TYPE_TIRE CIO_BUILDCCCC('t', 'i', 'r', 'e')
  CIO_CCCC cccc;
  cdiINT32 csys_index;
  cdiBOOLEAN grooved_tire;
  auINT32 n_knots[2];
  std::vector<idFLOAT> u_knots;
  std::vector<idFLOAT> v_knots;
  std::vector<idFLOAT> coeff_carcass;
  std::vector<idFLOAT> coeff_wrap;
} *CDI_TIRE;

/* function prototypes */
cdiBOOLEAN cdi_fread_double(double *dptr, cdiINT32 count, FILE *fp);
cdiBOOLEAN cdi_fread_int32(cdiINT32 *iptr, cdiINT32 count, FILE *fp);
cdiBOOLEAN cdi_fread_chars(char *buf, cdiINT32 len, FILE *fp);


// Function to handle the enum value description for the general case. For enums
// where we want to provide value descriptions in the CDI dump, this function
// should be overloaded.
inline std::string GetCDIEnumValueDescription(int value) { return ""; }


//========================================================================================
// AUTOSTOP
//========================================================================================

// Vehicle Definition ....................................................................
namespace eCDI_MOMENTS_VIA
{
enum Enum {
    SAE_J1594,
    RightHandRule
};

static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case SAE_J1594: return "SAE J1594";
    case RightHandRule: return "Right-Hand Rule";
    }
    return "";
}

}

inline std::string GetCDIEnumValueDescription(eCDI_MOMENTS_VIA::Enum ordinal) 
{
  return eCDI_MOMENTS_VIA::GetName(ordinal);
}

inline std::string GetCDIEnumValueDescription(SRI_DIRECTION ordinal)
{
  switch (ordinal) {
  case SRI_DIRECTION_PLUS_X: return "+X";
  case SRI_DIRECTION_MINUS_X: return "-X";
  case SRI_DIRECTION_PLUS_Y: return "+Y";
  case SRI_DIRECTION_MINUS_Y: return "-Y";
  case SRI_DIRECTION_PLUS_Z: return "+Z";
  case SRI_DIRECTION_MINUS_Z: return "-Z";
  }
  return "";
}

typedef struct sCDI_VHCL
{
#define CDI_CHUNK_TYPE_VHCL CIO_BUILDCCCC('v', 'h', 'c', 'l')
   cdiINT32 csys_index;
   eCDI_MOMENTS_VIA::Enum moments_via;
   SRI_DIRECTION centerline_direction;
   SRI_DIRECTION up_direction;
   SRI_DIRECTION side_force_direction;
   bool is_front_axle_applicable;
   cdiINT32 front_axle_preferred_csys_index;
   double front_axle_origin[3]; 
   double front_axle_dir[3]; 
   bool is_rear_axle_applicable;
   cdiINT32 rear_axle_preferred_csys_index;
   double rear_axle_origin[3]; 
   double rear_axle_dir[3]; 
   double moment_center[3];
   cdiINT32 floor_part_index;
   double floor_point[3];
   sCDI_PARM wheelbase;
} *CDI_VHCL;

// Averaged Measurements..............................................................................
namespace eCDI_MEAS_AVG_TIME_VIA {
enum Enum {
    SpecifiedTime,
    SpecifiedFrame,
    AfterInitialTransient
};

static std::string GetName(Enum ordinal) {
    switch (ordinal) {
    case SpecifiedTime: return "Specified Time";
    case SpecifiedFrame: return "Specified Frame";
    case AfterInitialTransient: return "After Initial Transient";
    }
    return "";
}
}

inline std::string GetCDIEnumValueDescription(eCDI_MEAS_AVG_TIME_VIA::Enum ordinal) 
{
  return eCDI_MEAS_AVG_TIME_VIA::GetName(ordinal);
}


typedef struct sCDI_AMW_ 
{
#define CDI_CHUNK_TYPE_AMW_ CIO_BUILDCCCC('a', 'm', 'w', '_')
  std::string name;
  cdiINT32 meas_window_to_average;
  bool average_fnc;                              // Averaging fluid?
  bool average_pnc;                              // Averaging fan + porous?
  bool average_snc;                              // Averaging surface?
  eCDI_MEAS_AVG_TIME_VIA::Enum start_via;        // Via Time, Frame, or After Init Transient
  sCDI_PARM start;                               // Specified timestep or frame number
  std::vector<cdiINT32> monitors;                // Indices of monitors for init transient
  eCDI_MEAS_AVG_TIME_VIA::Enum end_via;          // Via Time or Frame
  sCDI_PARM end;                                 // Specified timestep or frame number
  eCDI_MEAS_AVG_TIME_VIA::Enum avg_interval_via; // Via Time or Frame
  sCDI_PARM avg_interval;                        // Value of -1 indicates End-Start
  bool m_phaseAveraged = false;
  CDI_MFLT force_filter = nullptr;               // filter for averaged meas windows
} *CDI_AMW_;

// Chunk which will serve as a container of AMW_ chunks
#define CDI_CHUNK_TYPE_AMWS CIO_BUILDCCCC('a', 'm', 'w', 's')


// Monitors ..............................................................................
namespace eCDI_MNTR_TYPE
{
enum Enum
{
    Invalid = -1,
    Flow,
    HeatExchanger,
    PowerTHERM,
    Solid
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case Flow: return "Flow";
    case HeatExchanger: return "Heat Exchanger";
    case PowerTHERM: return "PowerTHERM";
    case Solid: return "Solid";
    }
    return "";
}
}

inline std::string GetCDIEnumValueDescription(eCDI_MNTR_TYPE::Enum ordinal)
{
  return eCDI_MNTR_TYPE::GetName(ordinal);
}


namespace eCDI_MNTR_FLOW_VAR_SRC
{
enum Enum
{
    Invalid = -1,
    Fluid,
    FanAndPorous,
    Surface,
    SurfacePlusFanAndPorous,
    Solid,
    Shell,
    ShellPlusSolid,
    NumFlowVarSources   // Keep this at the end of the list
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case Fluid: return "Fluid Measurements";
    case FanAndPorous: return "Fan and Porous Media Measurements";
    case Surface: return "Surface Measurements";
    case SurfacePlusFanAndPorous: return "Surface + Fan and Porous Media Measurements";
    case Solid: return "Volume Measurements";
    case Shell: return "Shell Measurements";
    case ShellPlusSolid: return "Shell + Volume Measurements";
    default: break;
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_FLOW_VAR_SRC::Enum ordinal)
{
  return eCDI_MNTR_FLOW_VAR_SRC::GetName(ordinal);
}


namespace eCDI_MNTR_GEOM_FILTER_SENSE
{
enum Enum
{
    Invalid = -1,
    Include,
    Exclude
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case Include: return "Include";
    case Exclude: return "Exclude";
    default: return "";
    }
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_GEOM_FILTER_SENSE::Enum ordinal)
{
  return eCDI_MNTR_GEOM_FILTER_SENSE::GetName(ordinal);
}


namespace eCDI_MNTR_HX_VAR
{
enum Enum
{
    Invalid = -1,
    CoolantEntryTemp,
    HeatRejected,
    CoolantExitTemp,
    CoolantMassFlowRate,
    InletAirMeanTemp,
    OutletAirMeanTemp,
    EntryAirMeanTemp,
    AirMassFlowRate,
    NumHXVars   // Keep this at the end of the list
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case CoolantEntryTemp: return "Coolant Entry Temperature";
    case HeatRejected: return "Heat Rejected";
    case CoolantExitTemp: return "Coolant/Refrigerant Exit Temperature";
    case CoolantMassFlowRate: return "Coolant/Refrigerant Mass Flow Rate";
    case InletAirMeanTemp: return "Inlet Air Mean Temperature";
    case OutletAirMeanTemp: return "Outlet Air Mean Temperature";
    case EntryAirMeanTemp: return "Entry Air Mean Temperature";
    case AirMassFlowRate: return "Air Mass Flow Rate";
    default: return "";
    }
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_HX_VAR::Enum ordinal)
{
  return eCDI_MNTR_HX_VAR::GetName(ordinal);
}


namespace eCDI_MNTR_PT_SIDE
{
enum Enum
{
    Invalid = -1,
    Front,
    Back,
    FrontAndBack,
    NotApplicable
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal)
    {
    case Invalid: return "Invalid";
    case Front: return "Front";
    case Back: return "Back";
    case FrontAndBack: return "Front and Back";
    case NotApplicable: return "Not Applicable";
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_PT_SIDE::Enum ordinal)
{
  return eCDI_MNTR_PT_SIDE::GetName(ordinal);
}


namespace eCDI_MNTR_PT_VAR
{
enum Enum
{
    Invalid = -1,
    MeanHTC,
    MaxHTC,
    MinHTC,
    MeanTemp,
    MaxTemp,
    MinTemp
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case MeanHTC: return "Mean HTC";
    case MaxHTC: return "Max HTC";
    case MinHTC: return "Min HTC";
    case MeanTemp: return "Mean Temperature";
    case MaxTemp: return "Max Temperature";
    case MinTemp: return "Min Temperature";
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_PT_VAR::Enum ordinal)
{
  return eCDI_MNTR_PT_VAR::GetName(ordinal);
}


namespace eCDI_MNTR_SIGNAL_ANALYSIS
{
enum Enum
{
    Invalid = -1,
    RecordSignalOnly,
    DetectInitialTransientOnly,
    DetectInitialTransientAndSignalConvergence
};

static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case RecordSignalOnly: return "Record Signal Only";
    case DetectInitialTransientOnly: return "Detect Initial Transient Only";
    case DetectInitialTransientAndSignalConvergence: return "Detect Initial Transient and Signal Convergence";
    default: return "";
    }
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_SIGNAL_ANALYSIS::Enum ordinal)
{
  return eCDI_MNTR_SIGNAL_ANALYSIS::GetName(ordinal);
}


namespace eCDI_MNTR_ANALYSIS_SCHEME
{
enum Enum
{
    // NOTE: Do not remove items from this enum -- only mark them as retired. Also new items should
    // always be added to the *end (just before NumAnalysisSchemes). Otherwise, we'll lose the
    // ability to properly recognize enum values in existing CDI files. Finally, any time we change
    // a label, we should add a line to GetOrdinal() to handle the old label for backwards
    // compatibility purposes.
    Invalid = -1,
    Custom,
    VehicleConfidenceInterval,
    StdAeroDrag,
    StdAeroLift,
    HeavyTruckDrag, // RETIRED
    HeavyTruckLift, // RETIRED
    LowFreqDrag,
    LowFreqLift,
    UltraLowFreqDrag,
    UltraLowFreqLift,
    HXTempFine,
    HXMassFlowFine,
    HXHeatRejectionFine,
    HXTempCoarse,
    HXMassFlowCoarse,
    HXHeatRejectionCoarse,
    PTPartTemp,
    PTPartHTC,
    Formula1Drag,
    Formula1Lift,
    AeroDragBP,
    NumAnalysisSchemes   // Keep this at the end of the list
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case Custom: return "Custom";
    case VehicleConfidenceInterval: return "Vehicle Confidence Interval";
    case StdAeroDrag: return "Automotive Steady Drag";
    case StdAeroLift: return "Automotive Steady Lift";
    case HeavyTruckDrag: return "Custom"; // We report retired schemes as Custom
    case HeavyTruckLift: return "Custom"; // We report retired schemes as Custom
    case LowFreqDrag: return "Auto/Truck Low-Frequency Drag";
    case LowFreqLift: return "Auto/Truck Low-Frequency Lift";
    case UltraLowFreqDrag: return "Auto/Truck Ultra-Low-Frequency Drag";
    case UltraLowFreqLift: return "Auto/Truck Ultra-Low-Frequency Lift";
    case HXTempFine: return "Heat Exchanger Temperature - Fine";
    case HXMassFlowFine: return "Heat Exchanger Mass Flow - Fine";
    case HXHeatRejectionFine: return "Heat Exchanger Heat Rejection - Fine";
    case HXTempCoarse: return "Heat Exchanger Temperature - Coarse";
    case HXMassFlowCoarse: return "Heat Exchanger Mass Flow - Coarse";
    case HXHeatRejectionCoarse: return "Heat Exchanger Heat Rejection - Coarse";
    case PTPartTemp: return "PowerTHERM Part Temperature";
    case PTPartHTC: return "PowerTHERM Part HTC";
    case Formula1Drag: return "Formula 1 Drag";
    case Formula1Lift: return "Formula 1 Lift";
    case AeroDragBP: return "Aero Drag (Best Practice)";
    default: return "";
    }
}

Enum GetOrdinal(const std::string& name);

}

inline std::string GetCDIEnumValueDescription(eCDI_MNTR_ANALYSIS_SCHEME::Enum ordinal)
{
  return eCDI_MNTR_ANALYSIS_SCHEME::GetName(ordinal);
}


namespace eCDI_MNTR_SGNL_CONV_CRIT
{
enum Enum
{
    Invalid = -1,
    ConfidenceInterval,
    StabilizationWindow,
    ConfidenceIntervalAndStabilizationWindow,
    NumConvergenceCriteria
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case ConfidenceInterval: return "Confidence Interval";
    case StabilizationWindow: return "Stabilization Window";
    case ConfidenceIntervalAndStabilizationWindow: return "Confidence Interval and Stabilization Window";
    default: return "";
    }
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_SGNL_CONV_CRIT::Enum ordinal)
{
  return eCDI_MNTR_SGNL_CONV_CRIT::GetName(ordinal);
}


namespace eCDI_MNTR_FLOW_PASS_VIA
{
enum Enum 
{
    Invalid = -1,
    Time,
    NumCouplingPeriods
};
static std::string GetName(Enum ordinal) 
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case Time: return "Time";
    case NumCouplingPeriods: return "# Coupling Periods";
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_FLOW_PASS_VIA::Enum ordinal)
{
  return eCDI_MNTR_FLOW_PASS_VIA::GetName(ordinal);
}


namespace eCDI_MNTR_TIME_VIA
{
enum Enum 
{
    Invalid = -1,
    Time,
    NumMonitorFlowPasses
};
static std::string GetName(Enum ordinal) 
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case Time: return "Time";
    case NumMonitorFlowPasses: return "# Monitor Flow Passes";
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_TIME_VIA::Enum ordinal)
{
  return eCDI_MNTR_TIME_VIA::GetName(ordinal);
}
  

namespace eCDI_MNTR_IT_VIA
{
enum Enum
{
    Invalid = -1,
    Automatic,
    UserSpecified
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case Automatic: return "Automatic";
    case UserSpecified: return "User Specified";
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_IT_VIA::Enum ordinal)
{
  return eCDI_MNTR_IT_VIA::GetName(ordinal);
}


namespace eCDI_MNTR_ACCY_VIA
{
enum Enum
{
    Invalid = -1,
    PercentageOfMean,
    Value,
    LesserOfValueAndPercentageOfMean,
    GreaterOfValueAndPercentageOfMean
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case PercentageOfMean: return "Percentage of Mean";
    case Value: return "Value";
    case LesserOfValueAndPercentageOfMean: return "Lesser of Value and Percentage of Mean";
    case GreaterOfValueAndPercentageOfMean: return "Greater of Value and Percentage of Mean";
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_ACCY_VIA::Enum ordinal)
{
  return eCDI_MNTR_ACCY_VIA::GetName(ordinal);
}

namespace eCDI_MNTR_CONFIDENCE_LEVEL_VIA
{
enum Enum
{
    Invalid = -1,
    OneStdDev,  // 68.3%
    TwoStdDevs, // 95.4%
    Custom,     // custom
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case OneStdDev: return "1 std dev - 68.3%";
    case TwoStdDevs: return "2 std devs - 95.4%";
    case Custom: return "Custom";
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_MNTR_CONFIDENCE_LEVEL_VIA::Enum ordinal)
{
  return eCDI_MNTR_CONFIDENCE_LEVEL_VIA::GetName(ordinal);
}

namespace eCDI_SUBWINDOW_RANGE_VIA
{
enum Enum
{
    Invalid = -1,
    PercentageOfMean,
    Value,
    PercentageOfAccuracy,
    NumSubwindowRangeOpts
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case PercentageOfMean: return "Percentage of Mean";
    case Value: return "Value";
    case PercentageOfAccuracy: return "Percentage of Accuracy";
    default:
      break;
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_SUBWINDOW_RANGE_VIA::Enum ordinal)
{
  return eCDI_SUBWINDOW_RANGE_VIA::GetName(ordinal);
}

namespace eCDI_RUN_AVG_GRAD_LIMIT_VIA
{
enum Enum
{
    Invalid = -1,
    Percentage,
    Value
};
static std::string GetName(Enum ordinal)
{
    switch (ordinal) {
    case Invalid: return "Invalid";
    case Percentage: return "Percentage";
    case Value: return "Value";
    default:
      break;
    }
    return "";
}
}
inline std::string GetCDIEnumValueDescription(eCDI_RUN_AVG_GRAD_LIMIT_VIA::Enum ordinal)
{
  return eCDI_RUN_AVG_GRAD_LIMIT_VIA::GetName(ordinal);
}

#define CDI_CHUNK_TYPE_MNTR CIO_BUILDCCCC('m', 'n', 't', 'r')
struct sCDI_MNTR
{
  sCDI_MNTR()
  : monitor_type(eCDI_MNTR_TYPE::Invalid)
  , meas_window_index(-1)
  , variable_source(eCDI_MNTR_FLOW_VAR_SRC::Invalid)
  , flow_variable(SRI_VARIABLE_INVALID)
  , variable_is_vector_component(false)
  , reference_csys(-1)
  , variable_is_moment_component(false)
  , use_wheelbase(false)
  , region_filtering(eCDI_MNTR_GEOM_FILTER_SENSE::Invalid)
  , region_filtering_tree(0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part)
  , face_filtering(eCDI_MNTR_GEOM_FILTER_SENSE::Invalid)
  , face_filtering_tree(0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face)
  , heat_exchanger_index(-1)
  , heat_exchanger_variable(eCDI_MNTR_HX_VAR::Invalid)
  , powertherm_model_index(-1)
  , powertherm_part_side(eCDI_MNTR_PT_SIDE::Invalid)
  , coupled(false)
  , powertherm_variable(eCDI_MNTR_PT_VAR::Invalid)
  , signal_analysis(eCDI_MNTR_SIGNAL_ANALYSIS::Invalid)
  , automatically_stop(false)
  , analysis_scheme(eCDI_MNTR_ANALYSIS_SCHEME::Invalid)
  , signal_conv_criteria(eCDI_MNTR_SGNL_CONV_CRIT::Invalid)
  , monitor_flow_pass_via(eCDI_MNTR_FLOW_PASS_VIA::Invalid)
  , coupling_phase(-1)
  , initial_transient_determined_via(eCDI_MNTR_IT_VIA::Invalid)
  , minimum_initial_transient_via(eCDI_MNTR_TIME_VIA::Invalid)
  , end_of_initial_transient_via(eCDI_MNTR_TIME_VIA::Invalid)
  , initial_transient_variance_window_via(eCDI_MNTR_TIME_VIA::Invalid)
  , desired_accuracy_via(eCDI_MNTR_ACCY_VIA::Invalid)
  , confidence_level_via(eCDI_MNTR_CONFIDENCE_LEVEL_VIA::Invalid)
  , custom_confidence_level(-1.0)
  , minimum_averaging_time_via(eCDI_MNTR_TIME_VIA::Invalid)
  , stabilization_window_via(eCDI_MNTR_TIME_VIA::Invalid)
  , enable_subwindows(false)
  , subwindow_via(eCDI_MNTR_TIME_VIA::Invalid)
  , subwindow_range_limit_via(eCDI_SUBWINDOW_RANGE_VIA::Invalid)
  , classic_autostop_algorithm(false)
  , running_average_gradient_limit_via(eCDI_RUN_AVG_GRAD_LIMIT_VIA::Invalid)
  , running_average_gradient_interval_via(eCDI_MNTR_TIME_VIA::Invalid)
  , signal_processing_period_via(eCDI_MNTR_TIME_VIA::Invalid)
  {
    reference_point[0] = 0.; reference_point[1] = 0; reference_point[2] = 0.;
  }

  static CIO_CCCC GetMonitorChunkType()
  { return CDI_CHUNK_TYPE_MNTR; }

  CIO_CCCC GetChunkType() const
  { return GetMonitorChunkType(); }

  void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  void WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(CDI_INFO cdi_info, int depth) const;
  void Undump(CDI_INFO cdi_info);


  // Member data .........................................................................
  eCDI_MNTR_TYPE::Enum monitor_type;
  std::string name;
  std::string preferred_time_unit;  // Obsolete -- no longer written by CASE...

  // Only applicable for Flow Monitors
  cdiINT32 meas_window_index;
  eCDI_MNTR_FLOW_VAR_SRC::Enum variable_source;
  // Contains porous variable, if variable_source is SurfacePlusFanAndPorous
  SRI_VARIABLE_TYPE flow_variable;
  bool variable_is_vector_component;
  cdiINT32 reference_csys;
  bool variable_is_moment_component;
  double reference_point[3];
  bool use_wheelbase;

  // Region filtering -- used to identify the fluid or fan & porous media regions (depending on
  // variable_source) to be included/excluded in the monitor
  eCDI_MNTR_GEOM_FILTER_SENSE::Enum region_filtering;
  cCDI_GEOM_SELECTION_TREE region_filtering_tree;

  // Face filtering -- used to identify the solid faces to be included/excluded in the monitor
  eCDI_MNTR_GEOM_FILTER_SENSE::Enum face_filtering;
  cCDI_GEOM_SELECTION_TREE face_filtering_tree;

  // Only applicable for Heat Exchanger Monitors
  cdiINT32 heat_exchanger_index;
  eCDI_MNTR_HX_VAR::Enum heat_exchanger_variable;

  // Only applicable for Coupled PowerTHERM Model Monitors
  cdiINT32 powertherm_model_index;
  std::string coupled_powertherm_part;
  eCDI_MNTR_PT_SIDE::Enum powertherm_part_side;
  bool coupled;
  eCDI_MNTR_PT_VAR::Enum powertherm_variable;

  // Signal Analysis Info.
  eCDI_MNTR_SIGNAL_ANALYSIS::Enum signal_analysis;
  bool automatically_stop;
  eCDI_MNTR_ANALYSIS_SCHEME::Enum analysis_scheme;
  std::string classic_autostop_analysis_scheme;
  eCDI_MNTR_SGNL_CONV_CRIT::Enum signal_conv_criteria;

  // Monitor Flow Pass
  eCDI_MNTR_FLOW_PASS_VIA::Enum monitor_flow_pass_via; // time or #coupling periods
  cdiINT32 coupling_phase;
  sCDI_PARM monitor_flow_pass;

  // Initial Transient Determined Via
  eCDI_MNTR_IT_VIA::Enum initial_transient_determined_via; // automatic or user specified

   // Minimum Initial Transient
  eCDI_MNTR_TIME_VIA::Enum minimum_initial_transient_via; // time or #monitor flow passes
  sCDI_PARM minimum_initial_transient;

  // End of Initial Transient
  eCDI_MNTR_TIME_VIA::Enum end_of_initial_transient_via; // time or #monitor flow passes
  sCDI_PARM end_of_initial_transient;

  // Initial Transient Mean/Variance Window 
  eCDI_MNTR_TIME_VIA::Enum initial_transient_variance_window_via; // time or #monitor flow passes
  sCDI_PARM initial_transient_variance_window;

  // Desired Accuracy
  eCDI_MNTR_ACCY_VIA::Enum desired_accuracy_via; // value or % of mean or Lesser of Value and Percentage of Mean or Greater of Value and Percentage of Mean
  sCDI_PARM desired_accuracy;  // value
  sCDI_PARM desired_accuracy_percentage;

  // Confidence Level
  eCDI_MNTR_CONFIDENCE_LEVEL_VIA::Enum confidence_level_via; // 95.4%, 68.3%, or custom
  double custom_confidence_level;

  // Minimum Averaging Time
  eCDI_MNTR_TIME_VIA::Enum minimum_averaging_time_via; // time or #monitor flow passes
  sCDI_PARM minimum_averaging_time;

  // Stabilization Window 
  eCDI_MNTR_TIME_VIA::Enum stabilization_window_via; // time or #monitor flow passes
  sCDI_PARM stabilization_window;

  // Enable Stabilization Subwindows
  bool enable_subwindows;

  // Subwindow
  eCDI_MNTR_TIME_VIA::Enum subwindow_via; // time or #monitor flow passes
  sCDI_PARM subwindow;

  // Subwindow Range Limit
  eCDI_SUBWINDOW_RANGE_VIA::Enum subwindow_range_limit_via; // value, % of mean, or % of accuracy
  sCDI_PARM subwindow_range_limit;

  // Classic Autostop Support
  bool classic_autostop_algorithm;

  // Classic Autostop Support: Initial Transient Fields
  sCDI_PARM variance_gradient_limit;
  sCDI_PARM creep_limit;

  // Classic Autostop Support: Signal Convergence Fields
  eCDI_RUN_AVG_GRAD_LIMIT_VIA::Enum running_average_gradient_limit_via;
  sCDI_PARM running_average_gradient_limit;

  eCDI_MNTR_TIME_VIA::Enum running_average_gradient_interval_via; // time or #monitor flow passes
  sCDI_PARM running_average_gradient_interval;

  // Classic Autostop Support: Miscellaneous
  eCDI_MNTR_TIME_VIA::Enum signal_processing_period_via; // time or #monitor flow passes
  sCDI_PARM signal_processing_period;
};


#define CDI_CHUNK_TYPE_MNTS CIO_BUILDCCCC('m', 'n', 't', 's')
class cCDI_MONITORS
{
public:
  void ReadFromCDI(CDI_INFO cdi_info);
  void WriteToCDI(CDI_INFO cdi_info) const;

  std::vector<sCDI_MNTR> m_monitors;
};

typedef struct sCDI_CLBR
{
//  Realistic wind calibration run parameters
#define CDI_CHUNK_TYPE_CLBR CIO_BUILDCCCC('c','l','b','r')
  CIO_CCCC cccc;
  cdiINT32 face_index;
  cdiINT32 meas_window_index;
  cdiINT32 calibration_iterations;
  cBOOLEAN cancel_pressure_fluctuations;
  cBOOLEAN subtract_mean_velocity;
  cBOOLEAN reset_initial_condition;
} *CDI_CLBR;


#define CDI_CHUNK_TYPE_SCLR CIO_BUILDCCCC('s', 'c', 'l', 'r')
struct sCDI_SCLR
{
  sCDI_SCLR()
    :
    uds_name(""),
    unit_type(""),
    allow_negative_values(true)
  {}
  static CIO_CCCC GetScalarChunkType() { return CDI_CHUNK_TYPE_SCLR; }

  CIO_CCCC GetChunkType() const { return GetScalarChunkType(); }
  void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  void WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(CDI_INFO cdi_info, int depth);
  void Undump(CDI_INFO cdi_info);

  // Member Data
  // convert double to s-parm.
  std::string uds_name;
  std::string unit_type;
  std::string unit_class_expression;
  sCDI_PARM diffusion_coefficient;
  sCDI_PARM scalar_turb_schmidt_number;
  sCDI_PARM scalar_source_term;
  bool allow_negative_values;
  sCDI_PARM minimum_value;
  sCDI_PARM maximum_value;
  sCDI_PARM default_initial_condition;
  sCDI_PARM molecular_weight;
};

/*typedef struct sCDI_SCLR
{
  //  Realistic wind calibration run parameters
#define CDI_CHUNK_TYPE_CLBR CIO_BUILDCCCC('s','c','l','r')
  CIO_CCCC cccc;
  CDI_UNIT_TYPES unit_type;
  char* unit_class_expression;
  double diffusion_coefficient;
  double scalar_turb_schmidt_number;
  double scalar_source_term;
  cBOOLEAN allow_negative_values;
  double maximum_value;
  double default_initial_condition;
  cBOOLEAN constant_density;
  double molecular_weight;
} *CDI_SCLR;
*/

#define CDI_CHUNK_TYPE_SCLS CIO_BUILDCCCC('s', 'c', 'l', 's')
class cCDI_SCALARS
{
public:
  void ReadFromCDI(CDI_INFO cdi_info);
  void WriteToCDI(CDI_INFO cdi_info) const;

  std::vector<sCDI_SCLR> m_scalars;
};

// Provide an interface for dump / undump between enum and int for cmdl chunks.
namespace eCDI_POWERTHERM_CALCULATION_TYPE {
enum Enum {
    SteadyState,
    Transient,
    InferFromModel
};

static std::string GetName(Enum ordinal) {
    switch (ordinal) {
      case SteadyState:    return "Steady State";
      case Transient:      return "Transient";
      case InferFromModel: return "Infer from Model";
    }
    return "";
}
}

inline std::string
GetCDIEnumValueDescription(eCDI_POWERTHERM_CALCULATION_TYPE::Enum ordinal)
{
  return eCDI_POWERTHERM_CALCULATION_TYPE::GetName(ordinal);
}

//mmd_Eutectic
namespace eCDI_GLOB_LIQUID_MATERIAL_CRIT
{
	enum Enum
	{
		Invalid = -1,
		SpecifiedBelow,
		Water,
		EthyleneGlycol30,
		EthyleneGlycol50,
		FC77,
		NumliquidMatCriteria
	};
	static std::string GetName(Enum ordinal)
	{
		switch (ordinal) {
		case Invalid: return "Invalid";
		case SpecifiedBelow: return "Specified Below";
		case Water: return "Water";
		case EthyleneGlycol30: return "Ethylene Glycol 30%";
		case EthyleneGlycol50 : return "Ethylene Glycol 50%";
		case FC77: return "FC-77";
		default: return "";
		}
	}
}
inline std::string GetCDIEnumValueDescription(eCDI_GLOB_LIQUID_MATERIAL_CRIT::Enum ordinal)
{
	return eCDI_GLOB_LIQUID_MATERIAL_CRIT::GetName(ordinal);
}

// Radiation

/* list of radiation surface conditions */
#define CDI_CHUNK_TYPE_RDSL CIO_BUILDCCCC('r', 'd', 's', 'l')

typedef struct sCDI_RDSC
{ /* radiation surface condition */
#define CDI_CHUNK_TYPE_RDSC CIO_BUILDCCCC('r', 'd', 's', 'c')
  CIO_CCCC cccc;
  std::string name;
  sCDI_PARM emissivity;
} *CDI_RDSC;

/* Radiation patches*/
class cCDI_RADIATION_PATCH_BASE;
class cCDI_STD_RADIATION_PATCH;
class cCDI_AXISYM_RADIATION_PATCH;

/* set of radiation patches */
#define CDI_CHUNK_TYPE_RDPS CIO_BUILDCCCC('r', 'd', 'p', 's')
class cCDI_RADIATION_PATCHES
{
public:
  cCDI_RADIATION_PATCHES() { }
  virtual ~cCDI_RADIATION_PATCHES();
private:
  cCDI_RADIATION_PATCHES(const cCDI_RADIATION_PATCHES& other);  // Prohibited

public:
  enum eCDI_RADIATION_PATCH_TYPE
  {
    PATCH_STANDARD,
    PATCH_AXISYMMETRIC
  };
  static std::string GetTypeName(eCDI_RADIATION_PATCH_TYPE ordinal) {
    switch (ordinal) {
    case PATCH_STANDARD: return "Standard";
    case PATCH_AXISYMMETRIC: return "Axisymmetric";
    }
    return "";
  }

public:
  void AddPatch(cCDI_RADIATION_PATCH_BASE* pPatch) { m_patches.push_back(pPatch); }
  // Methods for getting the patch of a face
  // Clients need to call BuildPatchTable after reading in the patch. This method
  // creates a map of face indices to the patch accounting for segments/inheritance
  void BuildPatchTable(cCDI_PARTITIONS& partitions);
  cCDI_RADIATION_PATCH_BASE* GetRadiationPatchForFace(cdiINT32 faceIndex) const;
  

  const std::vector<cCDI_RADIATION_PATCH_BASE*>& GetAllPatches() const { return m_patches; }

  void WriteToCDI(CDI_INFO cdi_info) const;
  void ReadFromCDI(CDI_INFO cdi_info);
  void Dump(CDI_INFO cdi_info, int depth) const;
  void Undump(CDI_INFO cdi_info);

private:
  std::vector<cCDI_RADIATION_PATCH_BASE*> m_patches;
  std::unordered_map<cdiINT32, cdiINT32> m_patchFaceMap;
};

inline std::string GetCDIEnumValueDescription(cCDI_RADIATION_PATCHES::eCDI_RADIATION_PATCH_TYPE ordinal)
{
  return cCDI_RADIATION_PATCHES::GetTypeName(ordinal);
}

class cCDI_RADIATION_PATCH_BASE
{
public:
  cCDI_RADIATION_PATCH_BASE() {}
  virtual ~cCDI_RADIATION_PATCH_BASE() { }

  virtual CIO_CCCC GetPatchChunkType() const = 0;
  virtual cCDI_RADIATION_PATCHES::eCDI_RADIATION_PATCH_TYPE GetPatchType() const = 0;

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  virtual void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex);
  //virtual void Dump(CDI_INFO cdi_info, int depth) const = 0;

  cCDI_GEOMETRY_REF face_geom{ cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face };
  sCDI_PARM angle_tolerance;
  sCDI_PARM patch_size;
};

class cCDI_STD_RADIATION_PATCH : public cCDI_RADIATION_PATCH_BASE
{
public:
  cCDI_STD_RADIATION_PATCH() : cCDI_RADIATION_PATCH_BASE() {}
#define CDI_CHUNK_TYPE_RPST CIO_BUILDCCCC('r', 'p', 's', 't')
  static CIO_CCCC GetChunkType() { return CDI_CHUNK_TYPE_RPST; }
  CIO_CCCC GetPatchChunkType() const override { return GetChunkType(); }

  cCDI_RADIATION_PATCHES::eCDI_RADIATION_PATCH_TYPE GetPatchType() const override
  {
    return cCDI_RADIATION_PATCHES::PATCH_STANDARD;
  }

  void WriteToCDI(CDI_INFO cdi_info) const override;
  void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex) override;
  //void Dump(CDI_INFO cdi_info, int depth) const override;

};

class cCDI_AXISYM_RADIATION_PATCH : public cCDI_RADIATION_PATCH_BASE
{
public:
  cCDI_AXISYM_RADIATION_PATCH() : cCDI_RADIATION_PATCH_BASE() {}
#define CDI_CHUNK_TYPE_RPAX CIO_BUILDCCCC('r', 'p', 'a', 'x')
  static CIO_CCCC GetChunkType() { return CDI_CHUNK_TYPE_RPAX; }
  CIO_CCCC GetPatchChunkType() const override { return GetChunkType(); }

  cCDI_RADIATION_PATCHES::eCDI_RADIATION_PATCH_TYPE GetPatchType() const override
  {
    return cCDI_RADIATION_PATCHES::PATCH_AXISYMMETRIC;
  }

  void WriteToCDI(CDI_INFO cdi_info) const override;
  void ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex) override;
  //void Dump(CDI_INFO cdi_info, int depth) const override;

  cdiINT32 axis_preferred_csys_index;
  double axis_origin[3];
  double axis_dir[3];
};

// User Defined Dependent Variables

typedef enum {
  CURVE_STATUS_OK,
  CURVE_NOT_FOUND,
  COLUMN_NOT_FOUND
} eCDI_UDD_STATUS;

typedef class cCDI_UDDC
{ /* user dependent data columns */
#define CDI_CHUNK_TYPE_UDDC CIO_BUILDCCCC('u', 'd', 'd', 'c')
public:
  CIO_CCCC GetChunkType() const
    { return CDI_CHUNK_TYPE_UDDC; }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  virtual void ReadFromCDI(CDI_INFO cdi_info);

  void Dump(CDI_INFO cdi_info, int depth) const;
  void Undump(CDI_INFO cdi_info);

  void SetName(std::string name) { m_name = name; }
  void SetUnitsClass(std::string unitsClass) { m_unitsClass = unitsClass; }
  void SetUnits(std::string units) { m_units = units; }

  std::string GetName() { return m_name; }
  std::string GetUnitsClass() { return m_unitsClass; }
  std::string GetUnits() { return m_units; }

  void AddData(double datum) { m_data.push_back(datum); }
  void SetData(const std::vector<double>* data);

  std::vector<double> GetData() const { return m_data; }

protected:
  std::string m_name;
  std::string m_unitsClass;
  std::string m_units;
  std::vector<double> m_data;
} *CDI_UDDC;

typedef class cCDI_UDDS
{ /* user dependent data sets */
#define CDI_CHUNK_TYPE_UDDS CIO_BUILDCCCC('u', 'd', 'd', 's')
public:
  CIO_CCCC GetChunkType() const
    { return CDI_CHUNK_TYPE_UDDS; }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  virtual void ReadFromCDI(CDI_INFO cdi_info);

  void Dump(CDI_INFO cdi_info, int depth) const;
  void Undump(CDI_INFO cdi_info);

  void SetName(std::string name) { m_name = name; }
  void SetXData(cCDI_UDDC xData) { m_xData = xData; }
  void AddYData(cCDI_UDDC yData) { m_yData.push_back(yData); }
  void SetYData(std::vector<cCDI_UDDC>* yData);

  std::string GetName() { return m_name; }
  eCDI_UDD_STATUS GetXY(std::string name, cCDI_UDDC *xCol, cCDI_UDDC *yCol);
  eCDI_UDD_STATUS GetColumn(std::string name, cCDI_UDDC *col);

  eCDI_UDD_STATUS GetXCol(cCDI_UDDC* xCol);
  cCDI_UDDC GetXCol() { return m_xData;}
  eCDI_UDD_STATUS GetYCols(std::vector<cCDI_UDDC>* yCols);
  std::vector<cCDI_UDDC> GetAllYCols() {return m_yData;}


  eCDI_UDD_STATUS GetYValueAt(std::string yCurveName, double xVal, double &yVal);

  // Helper functions that can be used elsewhere to cut down on code redundancy
  static double GetYValueAt(double xVal, const cCDI_UDDC *xCol, const cCDI_UDDC *yCol);
  static double GetYValueAt(double xVal, const std::vector<double> &xCol, const std::vector<double> &yCol);

protected:
  std::string m_name;
  cCDI_UDDC m_xData;
  std::vector<cCDI_UDDC> m_yData;
} *CDI_UDDS;

typedef class cCDI_UDDL
{ /* user dependent data vector */
#define CDI_CHUNK_TYPE_UDDL CIO_BUILDCCCC('u', 'd', 'd', 'l')
public:
  CIO_CCCC GetChunkType() const
    { return CDI_CHUNK_TYPE_UDDL; }

  virtual void WriteToCDI(CDI_INFO cdi_info) const;
  virtual void ReadFromCDI(CDI_INFO cdi_info);

  void Dump(CDI_INFO cdi_info, int depth) const;
  void Undump(CDI_INFO cdi_info);
  std::vector<cCDI_UDDS> GetDataCurves() { return m_userDataCurves;}
  void AddCurve(cCDI_UDDS curve) { m_userDataCurves.push_back(curve); }

  eCDI_UDD_STATUS GetXY(std::string name, cCDI_UDDC* xCol, cCDI_UDDC* yCol);
  eCDI_UDD_STATUS GetAllXY(std::string name, cCDI_UDDC* xCol, std::vector<cCDI_UDDC> *yCols);
  eCDI_UDD_STATUS GetYValueAt(std::string yCurveName, double xVal, double& yVal);


protected:
  std::vector<cCDI_UDDS> m_userDataCurves;
} *CDI_UDDL;

//========================================================================================
void cdi_write_ptge(CDI_INFO cdi_info, CDI_PTGE);
void cdi_write_bbox(CDI_INFO cdi_info,CDI_BBOX);
void cdi_write_cmnt(CDI_INFO cdi_info,CDI_CMNT);
void cdi_write_audt(CDI_INFO cdi_info,CDI_AUDT);
void cdi_write_undb(CDI_INFO cdi_info,CDI_UNDB);
void cdi_write_eqns(CDI_INFO cdi_info,CDI_EQNS);
void cdi_write_gtbl(CDI_INFO cdi_info,CDI_GTBL);
void cdi_write_tabl(CDI_INFO cdi_info,cCDI_TABL);
void cdi_write_csys(CDI_INFO cdi_info,cCDI_CSYS);
void cdi_write_rgpn(CDI_INFO cdi_info,CDI_RGPN);
void cdi_write_vrtx(CDI_INFO cdi_info,CDI_VRTX);
void cdi_write_fact(CDI_INFO cdi_info,cCDI_FACT);
void cdi_write_edge(CDI_INFO cdi_info,cCDI_EDGE);
void cdi_write_face_old(CDI_INFO cdi_info,CDI_FACE_OLD);
void cdi_write_null(CDI_INFO cdi_info,CDI_NULL);
void cdi_write_name(CDI_INFO cdi_info,CDI_NAME);
void cdi_write_offs(CDI_INFO cdi_info,CDI_OFFS);
void cdi_write_prgn(CDI_INFO cdi_info,CDI_PRGN);
void cdi_write_shll(CDI_INFO cdi_info);
void cdi_write_body(CDI_INFO cdi_info);
void cdi_write_ptyp(CDI_INFO cdi_info,CDI_PTYP);
void cdi_write_hxch(CDI_INFO cdi_info,CDI_HXCH);
void cdi_write_amhx(CDI_INFO cdi_info,CDI_AMHX);
void cdi_write_bsrs(CDI_INFO cdi_info,CDI_BSRS);
void cdi_write_bsrg(CDI_INFO cdi_info,CDI_BSRG);
void cdi_write_vpts(CDI_INFO cdi_info,CDI_VPTS);
void cdi_write_vpnt(CDI_INFO cdi_info,CDI_VPNT);
void cdi_write_ivdp(CDI_INFO cdi_info,CDI_IVDP);
void cdi_write_cvdp(CDI_INFO cdi_info,CDI_CVDP);
void cdi_write_bsdp(CDI_INFO cdi_info,CDI_BSDP);
void cdi_write_pnts(CDI_INFO cdi_info,CDI_PNTS);
void cdi_write_eqdp(CDI_INFO cdi_info,CDI_EQDP);
void cdi_write_plrf(CDI_INFO cdi_info,CDI_PLRF);
void cdi_write_ival(CDI_INFO cdi_info,CDI_IVAL);
void cdi_write_dprm(CDI_INFO cdi_info,CDI_DPRM);
void cdi_write_phys(CDI_INFO cdi_info,CDI_PHYS);
void cdi_write_rgns(CDI_INFO cdi_info,CDI_RGNS);
void cdi_write_gscc(CDI_INFO cdi_info,CDI_GSCC);
void cdi_write_gsep(CDI_INFO cdi_info,CDI_GSEP);
void cdi_write_gscl(CDI_INFO cdi_info,CDI_GSCL);
void cdi_write_gfds(CDI_INFO cdi_info,CDI_GFDS);
void cdi_write_grdf(CDI_INFO cdi_info,CDI_GRDF);
void cdi_write_prdf(CDI_INFO cdi_info,CDI_PRDF);
void cdi_write_prec(CDI_INFO cdi_info,CDI_PREC);
void cdi_write_mesr(CDI_INFO cdi_info,CDI_MESR);
void cdi_write_mpsg(CDI_INFO cdi_info,CDI_MPSG);
void cdi_write_flud(CDI_INFO cdi_info,CDI_FLUD);
void cdi_write_movb(CDI_INFO cdi_info,CDI_MOVB);
void cdi_write_sold(CDI_INFO cdi_info,CDI_SOLD);
void cdi_write_hcsd(CDI_INFO cdi_info,CDI_HCSD);
void cdi_write_ihcs(CDI_INFO cdi_info,CDI_IHCS);
void cdi_write_scmt(CDI_INFO cdi_info,CDI_SCMT);
void cdi_write_scma(CDI_INFO cdi_info, CDI_SCMA);
void cdi_write_paxs(CDI_INFO cdi_info,CDI_PAXS);
void cdi_write_rdsc(CDI_INFO cdi_info,CDI_RDSC);
void cdi_write_isld(CDI_INFO cdi_info,CDI_ISLD);
void cdi_write_hcsh(CDI_INFO cdi_info,CDI_HCSH);
void cdi_write_hcsl(CDI_INFO cdi_info,CDI_HCSL);
void cdi_write_stcr(CDI_INFO cdi_info,CDI_STCR);
void cdi_write_stcp(CDI_INFO cdi_info,CDI_STCP);
void cdi_write_scpr(CDI_INFO cdi_info,CDI_SCPR);
void cdi_write_mtpr(CDI_INFO cdi_info,CDI_MTPR);
void cdi_write_pcfg(CDI_INFO cdi_info,CDI_PCFG);
void cdi_write_meas(CDI_INFO cdi_info,CDI_MEAS);
void cdi_write_srpt(CDI_INFO cdi_info,CDI_SRPT);
void cdi_write_simv(CDI_INFO cdi_info,CDI_SIMV);
void cdi_write_thma(CDI_INFO cdi_info,CDI_THMA);
void cdi_write_symp(CDI_INFO cdi_info,CDI_SYMP);
void cdi_write_ghdr(CDI_INFO cdi_info,CDI_GHDR);
void cdi_write_unit(CDI_INFO cdi_info,CDI_UNIT);
void cdi_write_uunt(CDI_INFO cdi_info,CDI_UUNT);
void cdi_write_cpnt(CDI_INFO cdi_info,CDI_CPNT);
void cdi_write_kesc(CDI_INFO cdi_info, const sCDI_KESC&);//kEps_SuperCycle
void cdi_write_cprp(CDI_INFO cdi_info,CDI_CPRP);
void cdi_write_prbe(CDI_INFO cdi_info,CDI_PRBE);
void cdi_write_prtj(CDI_INFO cdi_info,sCDI_PRTJ*);
void cdi_write_ment(CDI_INFO cdi_info,CDI_MENT);
void cdi_write_flst(CDI_INFO cdi_info, const sCDI_FLST*);
void cdi_write_fdlt(CDI_INFO cdi_info,CDI_FDLT);
void cdi_write_facd(CDI_INFO cdi_info,CDI_FACD);
void cdi_write_mdev(CDI_INFO cdi_info,CDI_MDEV);
void cdi_write_parm(CDI_INFO cdi_info,const sCDI_PARM*);
void cdi_write_wipr(CDI_INFO cdi_info,CDI_WIPR);
void cdi_write_enum(CDI_INFO cdi_info, sINT32);
void cdi_write_int_(CDI_INFO cdi_info, sINT32);
void cdi_write_bool(CDI_INFO cdi_info, bool);
void cdi_write_dbls(CDI_INFO cdi_info, idFLOAT*, cdiINT32);
void cdi_write_dble(CDI_INFO cdi_info, const idFLOAT);
void cdi_write_strg(CDI_INFO cdi_info, const std::string&);
void cdi_write_pglb(CDI_INFO cdi_info, CDI_PGLB);
void cdi_write_prmt(CDI_INFO cdi_info,CDI_PRMT);
void cdi_write_srmi(CDI_INFO cdi_info,CDI_SRMI);
void cdi_write_scrn(CDI_INFO cdi_info,CDI_SCRN);
void cdi_write_box_(CDI_INFO cdi_info,CDI_BOX_);
void cdi_write_vhcl(CDI_INFO cdi_info,CDI_VHCL);
void cdi_write_amw_(CDI_INFO cdi_info,CDI_AMW_);
void cdi_write_aftd(CDI_INFO cdi_info,CDI_AFTD);
void cdi_write_wpdt(CDI_INFO cdi_info,CDI_WPDT);
void cdi_write_tire(CDI_INFO cdi_info,CDI_TIRE);
void cdi_write_clbr(CDI_INFO cdi_info, CDI_CLBR);
void cdi_write_farg(CDI_INFO cdi_info,sCDI_FARG);
void cdi_write_gfar(CDI_INFO cdi_info,CDI_GFAR);
void cdi_write_gapd(CDI_INFO cdi_info,CDI_GAPD);
void cdi_write_csph(CDI_INFO cdi_info,CDI_CSPH);

// STL versions for RGNS and FLST
void cdi_write_rgns(CDI_INFO cdi_info, std::vector<cdiINT32>& rgnVec);
void cdi_write_flst(CDI_INFO info, const std::vector<cdiINT32>& values);

CDI_PTGE cdi_read_ptge(CDI_INFO cdi_info);
CDI_BBOX cdi_read_bbox(CDI_INFO cdi_info);
CDI_CMNT cdi_read_cmnt(CDI_INFO cdi_info);
CDI_AUDT cdi_read_audt(CDI_INFO cdi_info);
CDI_UNDB cdi_read_undb(CDI_INFO cdi_info);
cdiINT32 cdi_undb_read_length(CDI_INFO cdi_info);
cdiINT32 cdi_undb_read_chars(CDI_INFO cdi_info, void *buf, cdiINT32 n_chars);
// This can be called to read a UNDB chunk
CDI_CONDENSED_UNITS_DB cdi_read_condensed_units_db(CDI_INFO cdi_info);
VOID cdi_update_units_db(UNITS_DB db, CDI_CONDENSED_UNITS_DB cdb);

CDI_EQNS cdi_read_eqns(CDI_INFO cdi_info);
cdiINT32 cdi_eqns_read_length(CDI_INFO cdi_info);
cdiINT32 cdi_eqns_read_chars(CDI_INFO cdi_info, void *buf, cdiINT32 n_chars);

CDI_GTBL cdi_read_gtbl(CDI_INFO cdi_info);
cCDI_TABL cdi_read_tabl(CDI_INFO cdi_info);
cCDI_CSYS cdi_read_csys(CDI_INFO cdi_info);
CDI_LRF  cdi_read_lrf(CDI_INFO cdi_info, bool warn_about_unexpected_params = false);
CDI_RGPN cdi_read_rgpn(CDI_INFO cdi_info);
CDI_VRTX cdi_read_vrtx(CDI_INFO cdi_info);
cCDI_FACT cdi_read_fact(CDI_INFO cdi_info);
cCDI_EDGE cdi_read_edge(CDI_INFO cdi_info);
CDI_FACE_OLD cdi_read_face_old(CDI_INFO cdi_info);
CDI_NULL cdi_read_null(CDI_INFO cdi_info);
CDI_NAME cdi_read_name(CDI_INFO cdi_info);
CDI_OFFS cdi_read_offs(CDI_INFO cdi_info);
CDI_PRGN cdi_read_prgn(CDI_INFO cdi_info);
CDI_SHLL cdi_read_shll(CDI_INFO cdi_info);
CDI_PTYP cdi_read_ptyp(CDI_INFO cdi_info);
CDI_HXCH cdi_read_hxch(CDI_INFO cdi_info);
CDI_AMHX cdi_read_amhx(CDI_INFO cdi_info);
CDI_BSRS cdi_read_bsrs(CDI_INFO cdi_info);
CDI_BSRG cdi_read_bsrg(CDI_INFO cdi_info);
CDI_VPNT cdi_read_vpts(CDI_INFO cdi_info);
CDI_VPNT cdi_read_vpnt(CDI_INFO cdi_info);
CDI_IVDP cdi_read_ivdp(CDI_INFO cdi_info);
CDI_CVDP cdi_read_cvdp(CDI_INFO cdi_info);
CDI_BSDP cdi_read_bsdp(CDI_INFO cdi_info);
CDI_PNTS cdi_read_pnts(CDI_INFO cdi_info);
CDI_EQDP cdi_read_eqdp(CDI_INFO cdi_info);
CDI_PLRF cdi_read_plrf(CDI_INFO cdi_info);
CDI_IVAL cdi_read_ival(CDI_INFO cdi_info);
CDI_DPRM cdi_read_dprm(CDI_INFO cdi_info);
CDI_PHYS cdi_read_phys(CDI_INFO cdi_info);
CDI_RGNS cdi_read_rgns(CDI_INFO cdi_info);
CDI_GSCC cdi_read_gscc(CDI_INFO cdi_info);
CDI_GSEP cdi_read_gsep(CDI_INFO cdi_info);
CDI_GSCL cdi_read_gscl(CDI_INFO cdi_info);
CDI_GFDS cdi_read_gfds(CDI_INFO cdi_info);
CDI_GRDF cdi_read_grdf(CDI_INFO cdi_info);
CDI_PRDF cdi_read_prdf(CDI_INFO cdi_info);
CDI_PREC cdi_read_prec(CDI_INFO cdi_info);
void cdi_correct_mesr (CDI_MESR mesr,
                       cdiINT32 major_verion,
                       cdiINT32 minor_version,
                       cdiBOOLEAN is_fluidProbe,
                       cdiBOOLEAN is_2d,
                       cdiBOOLEAN is_heat_xfer);
CDI_MESR cdi_get_mesr(CDI_INFO cdi_info,
                      cdiBOOLEAN is_2d,
                      cdiBOOLEAN is_heat_xfer,
                      cdiBOOLEAN *p_is_fluidProbe = NULL);
CDI_MESR cdi_read_mesr(CDI_INFO cdi_info,
                       cdiBOOLEAN is_2d,
                       cdiBOOLEAN is_heat_xfer);
CDI_FLUD cdi_read_flud(CDI_INFO cdi_info);
CDI_MOVB cdi_read_movb(CDI_INFO cdi_info);
CDI_SOLD cdi_read_sold(CDI_INFO cdi_info);
CDI_HCSD cdi_read_hcsd(CDI_INFO cdi_info);
CDI_IHCS cdi_read_ihcs(CDI_INFO cdi_info);
CDI_ISLD cdi_read_isld(CDI_INFO cdi_info);
CDI_PCFG cdi_read_pcfg(CDI_INFO cdi_info);
CDI_MEAS cdi_read_meas(CDI_INFO cdi_info);
CDI_SRPT cdi_read_srpt(CDI_INFO cdi_info);
CDI_SIMV cdi_read_simv(CDI_INFO cdi_info);
CDI_THMA cdi_read_thma(CDI_INFO cdi_info);
CDI_SYMP cdi_read_symp(CDI_INFO cdi_info);
CDI_GHDR cdi_read_ghdr(CDI_INFO cdi_info);
CDI_UNIT cdi_read_unit(CDI_INFO cdi_info);
CDI_UUNT cdi_read_uunt(CDI_INFO cdi_info);
CDI_CPNT cdi_read_cpnt(CDI_INFO cdi_info);
sCDI_KESC cdi_read_kesc(CDI_INFO cdi_info);//kEps_SuperCycle
CDI_CPRP cdi_read_cprp(CDI_INFO cdi_info);
CDI_PRBE cdi_read_prbe(CDI_INFO cdi_info);
sCDI_PRTJ* cdi_read_prtj(CDI_INFO cdi_info);
CDI_MENT cdi_read_ment(CDI_INFO cdi_info);
CDI_FLST cdi_read_flst(CDI_INFO cdi_info);
CDI_FDLT cdi_read_fdlt(CDI_INFO cdi_info);
CDI_MDEV cdi_read_mdev(CDI_INFO cdi_info);
CDI_BFPR cdi_read_bfpr(CDI_INFO cdi_info);
sCDI_UDST cdi_read_udst(CDI_INFO cdi_info);
CDI_BODF cdi_read_bodf(CDI_INFO cdi_info);
CDI_WPDT cdi_read_wpdt(CDI_INFO cdi_info);
void cdi_read_facd(CDI_INFO cdi_info, CDI_FACD);
void cdi_read_pglb(CDI_INFO cdi_info, CDI_PGLB);
void cdi_read_wipr(CDI_INFO cdi_info, CDI_WIPR);
void cdi_read_prmt(CDI_INFO cdi_info, CDI_PRMT);
void cdi_read_srmi(CDI_INFO cdi_info, CDI_SRMI);
void cdi_read_scrn(CDI_INFO cdi_info, CDI_SCRN);
void cdi_read_box_(CDI_INFO cdi_info, sCDI_BOX_ &);
void cdi_read_vhcl(CDI_INFO cdi_info, CDI_VHCL);
void cdi_read_amw_(CDI_INFO cdi_info, CDI_AMW_);
void cdi_read_mpsg(CDI_INFO cdi_info, CDI_MPSG);
void cdi_read_scmt(CDI_INFO cdi_info, CDI_SCMT);
void cdi_read_scma(CDI_INFO cdi_info, CDI_SCMA);
void cdi_read_paxs(CDI_INFO cdi_info, CDI_PAXS);
void cdi_read_rdsc(CDI_INFO cdi_info, CDI_RDSC);
CDI_AFTD cdi_read_aftd(CDI_INFO cdi_info);
CDI_TIRE cdi_read_tire(CDI_INFO cdi_info);
CDI_CLBR cdi_read_clbr(CDI_INFO cdi_info);
sCDI_FARG cdi_read_farg(CDI_INFO cdi_info);
sCDI_GFAR cdi_read_gfar(CDI_INFO cdi_info);
CDI_GMCV cdi_read_gmcv(CDI_INFO cdi_info);
void cdi_read_hcsh(CDI_INFO cdi_info, CDI_HCSH);
void cdi_read_hcsl(CDI_INFO cdi_info, CDI_HCSL);
void cdi_read_stcr(CDI_INFO cdi_info, CDI_STCR);
void cdi_read_stcp(CDI_INFO cdi_info, CDI_STCP);
void cdi_read_scpr(CDI_INFO cdi_info, CDI_SCPR);
void cdi_read_mtpr(CDI_INFO cdi_info, CDI_MTPR);
sCDI_GAPD cdi_read_gapd(CDI_INFO cdi_info);
sCDI_GGAP cdi_read_ggap(CDI_INFO cdi_info);
CDI_CSPH cdi_read_csph(CDI_INFO cdi_info);
cCDI_GEOMETRY_REF* cdi_read_gmrf(CDI_INFO cdi_info);
cCDI_GEOM_SELECTION_TREE* cdi_read_geos(CDI_INFO cdi_info);

//STL versions for RGNS and FLST
void cdi_inner_chunk_read_rgns(cdiINT32 &index, const char* chunkName, CDI_INFO cdi_info, std::vector<cdiINT32>& rgnVec);
void cdi_inner_chunk_read_flst(cdiINT32 &index, const char* chunkName, CDI_INFO info, std::vector<cdiINT32> &values);

// This is a copy of the dierckx_surface_coeff_array_size() function, defined
// in the dierckx component. We copy it here to prevent forcing all downstream
// users of CDI to link in the dierckx libraries.
int cdi_tire_surface_coeff_array_size(int ndim, int n_u_knots, int n_v_knots);

void cdi_destroy_ptge(CDI_PTGE);
void cdi_destroy_bbox(CDI_BBOX);
void cdi_destroy_cmnt(CDI_CMNT);
void cdi_destroy_audt(CDI_AUDT);
void cdi_destroy_undb(CDI_UNDB);
void cdi_destroy_eqns(CDI_EQNS);
void cdi_destroy_gtbl(CDI_GTBL);
void cdi_destroy_tabl(cCDI_TABL);
void cdi_destroy_rgpn(CDI_RGPN);
void cdi_destroy_vrtx(CDI_VRTX);
void cdi_destroy_fact(cCDI_FACT);
void cdi_destroy_edge(cCDI_EDGE);
void cdi_destroy_face_old(CDI_FACE_OLD);
void cdi_destroy_null(CDI_NULL);
void cdi_destroy_name(CDI_NAME);
void cdi_destroy_offs(CDI_OFFS);
void cdi_destroy_prgn(CDI_PRGN);
void cdi_destroy_shll(CDI_SHLL);
void cdi_destroy_ptyp(CDI_PTYP);
void cdi_destroy_hxch(CDI_HXCH);
void cdi_destroy_amhx(CDI_AMHX);
void cdi_destroy_bsrs(CDI_BSRS);
void cdi_destroy_bsrg(CDI_BSRG);
void cdi_destroy_vpts(CDI_VPTS);
void cdi_destroy_vpnt(CDI_VPNT);
void cdi_destroy_ivdp(CDI_IVDP);
void cdi_destroy_cvdp(CDI_CVDP);
void cdi_destroy_bsdp(CDI_BSDP);
void cdi_destroy_pnts(CDI_PNTS);
void cdi_destroy_eqdp(CDI_EQDP);
void cdi_destroy_plrf(CDI_PLRF);
void cdi_destroy_ival(CDI_IVAL);
void cdi_destroy_dprm(CDI_DPRM);
void cdi_destroy_phys(CDI_PHYS);
void cdi_destroy_rgns(CDI_RGNS);
void cdi_destroy_gscc(CDI_GSCC);
void cdi_destroy_gsep(CDI_GSEP);
void cdi_destroy_gscl(CDI_GSCL);
void cdi_destroy_gfds(CDI_GFDS);
void cdi_destroy_grdf(CDI_GRDF);
void cdi_destroy_prdf(CDI_PRDF);
void cdi_destroy_prec(CDI_PREC);
void cdi_destroy_mesr(CDI_MESR);
void cdi_destroy_flud(CDI_FLUD);
void cdi_destroy_movb(CDI_MOVB);
void cdi_destroy_sold(CDI_SOLD);
void cdi_destroy_hcsd(CDI_HCSD);
void cdi_destroy_ihcs(CDI_IHCS);
void cdi_destroy_scmt(CDI_SCMT);
void cdi_destroy_scma(CDI_SCMA);
void cdi_destroy_paxs(CDI_PAXS);
void cdi_destroy_rdsc(CDI_RDSC);
void cdi_destroy_isld(CDI_ISLD);
void cdi_destroy_pcfg(CDI_PCFG);
void cdi_destroy_meas(CDI_MEAS);
void cdi_destroy_srpt(CDI_SRPT);
void cdi_destroy_simv(CDI_SIMV);
void cdi_destroy_thma(CDI_THMA);
void cdi_destroy_symp(CDI_SYMP);
void cdi_destroy_ghdr(CDI_GHDR);
void cdi_destroy_unit(CDI_UNIT);
void cdi_destroy_uunt(CDI_UUNT);
void cdi_destroy_cpnt(CDI_CPNT);
void cdi_destroy_cprp(CDI_CPRP);
void cdi_destroy_prbe(CDI_PRBE);
void cdi_destroy_ment(CDI_MENT);
void cdi_destroy_flst(CDI_FLST);
void cdi_destroy_fdlt(CDI_FDLT);
void cdi_clear_fdlt(CDI_FDLT);
void cdi_destroy_facd(CDI_FACD);
void cdi_destroy_mdev(CDI_MDEV);
void cdi_clear_facd(CDI_FACD);
void cdi_destroy_csys(cCDI_CSYS);
void cdi_destroy_lrf(CDI_LRF);
void cdi_destroy_bodf(CDI_BODF);
void cdi_destroy_wpdt(CDI_WPDT);
void cdi_destroy_bfpr(CDI_BFPR);
void cdi_destroy_dbls(idFLOAT*);
void cdi_destroy_vhcl(CDI_VHCL);
void cdi_destroy_wipr(CDI_WIPR);
void cdi_destroy_tire(CDI_TIRE);
void cdi_destroy_clbr(CDI_CLBR);
void cdi_destroy_hcsh(CDI_HCSH);
void cdi_clear_hcsh(CDI_HCSH);
void cdi_destroy_hcsl(CDI_HCSL);
void cdi_destroy_stcr(CDI_STCR);
void cdi_destroy_stcp(CDI_STCP);
void cdi_destroy_scpr(CDI_SCPR);
void cdi_destroy_mtpr(CDI_MTPR);
void cdi_destroy_csph(CDI_CSPH);
void cdi_destroy_gmrf(cCDI_GEOMETRY_REF*);
void cdi_destroy_geos(cCDI_GEOM_SELECTION_TREE*);
void cdi_destroy_udst(CDI_UDST);

sCDI_CPNT cdi_round_cpnt (cdiINT32 coarsest_timestep,
                          sCDI_CPNT unrounded);

#endif /* !_CDI_READWRITE_H */
