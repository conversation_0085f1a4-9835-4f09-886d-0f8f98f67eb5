/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#ifndef _CDI_PHYSICS_H
#define _CDI_PHYSICS_H

#define CDI_MAX_INTEGER_DP 8
#define CDI_MAX_CONTINUOUS_DP 36
#define CDI_MAX_INITIAL_CONDITIONS 16

#define CDI_MIN_8BIT_PRESSURE   840.0
#define CDI_MAX_8BIT_PRESSURE  1680.0

#define CDI_MIN_PRESSURE  (CDI_MIN_8BIT_PRESSURE/(54.0*255.0))
#define CDI_MAX_PRESSURE  (CDI_MAX_8BIT_PRESSURE/(54.0*255.0))

/* New definitions for passive scalar temp solver */
#define CDI_MIN_TEMPERATURE   0.0
#define CDI_MAX_TEMPERATURE   DBL_MAX
#define CDI_MAX_TEMPERATURE_HS 0.5

/* @@@ Should be altered for 19 state lattice */
#define CDI_CHAR_LB_TEMPERATURE   0.42

#define CDI_MAX_VELOCITY    0.35
#define CDI_MIN_VELOCITY    -CDI_MAX_VELOCITY

#define CDI_MAX_MASS_FLUX   (CDI_MAX_PRESSURE*CDI_MAX_VELOCITY/CDI_CHAR_LB_TEMPERATURE)
#define CDI_MIN_MASS_FLUX   -CDI_MAX_MASS_FLUX

#define CDI_MAX_MASS_FLOW   DBL_MAX
#define CDI_MIN_MASS_FLOW   -CDI_MAX_MASS_FLOW

#define CDI_MIN_NU_OVER_T   0.006
#define CDI_MAX_NU_OVER_T   0.5

// New definitions for pr29377
#define CDI_MIN_ACOUSTIC_POROSITY   0
#define CDI_MAX_ACOUSTIC_POROSITY   1

// New definitions for pr30514
#define CDI_MIN_TORTUOSITY   1.0
#define CDI_MAX_TORTUOSITY   4.0

/* Heat flux limits are disabled for now
#define CDI_MIN_WALL_HEAT_FLUX   ( - (200.0/3000.0))
#define CDI_MAX_WALL_HEAT_FLUX   (200.0/3000.0)*/
#define CDI_MIN_WALL_HEAT_FLUX -DBL_MAX
#define CDI_MAX_WALL_HEAT_FLUX DBL_MAX

enum CDI_PHYSICS_CLASS
{
  CDI_PHYSICS_IS_VOLUME,
  CDI_PHYSICS_IS_ENDOSURFACE,
  CDI_PHYSICS_IS_EXOSURFACE,
  CDI_PHYSICS_IS_TRUE_SURFACE,
  CDI_PHYSICS_IS_SHELL_CONDUCTION,
  CDI_PHYSICS_IS_UNDEFINED
};

enum CDI_BOUNDARY_LAYER_TRANSITION_TYPE
{
  CDI_INVALID_BOUNDARY_LAYER_TYPE = -1,
  CDI_BOUNDARY_LAYER_AUTOMATIC_LTT,
  CDI_BOUNDARY_LAYER_FULLY_TURBULENT,
  CDI_BOUNDARY_LAYER_LAMINAR_FIXED
};

typedef class sCDI_PHYS_TYPE_DESCRIPTOR
{
public:

    struct sDP_VAR
    {
        cdiINT32    id;
        const char *name;
        const char *unit_class;
    };

    cdiINT32 continuous_dp_index (cdiINT32 id) const
    {
        return lookup_id(continuous_dp_var, CDI_MAX_CONTINUOUS_DP, id);
    }

    cdiINT32 initial_condition_index (cdiINT32 id) const
    {
        return lookup_id(initial_condition_var, CDI_MAX_INITIAL_CONDITIONS, id);
    }

    cdiINT32 cdi_physics_type;
    const char *name;
    CDI_PHYSICS_CLASS physics_class;
    cdiBOOLEAN requires_normal_p;
    cdiINT32 n_continuous_dp;
    cdiINT32 n_initial_conditions;

    const sDP_VAR *continuous_dp_var[CDI_MAX_CONTINUOUS_DP];
    const sDP_VAR *initial_condition_var[CDI_MAX_INITIAL_CONDITIONS];

private:

    static cdiINT32 lookup_id (const sDP_VAR *const var_array[], cdiINT32 n_vars, cdiINT32 id)
    {
        for (cdiINT32 i = 0; i < n_vars && var_array[i]; ++i) {
            if (var_array[i]->id == id)
                return i;
        }
        return -1;
    }
} *CDI_PHYS_TYPE_DESCRIPTOR;

CDI_PHYS_TYPE_DESCRIPTOR cdi_lookup_physics(cdiINT32 cdi_physics_type);
CDI_PHYSICS_CLASS cdi_physics_class (cdiINT32 cdi_physics_type);
cdiBOOLEAN cdi_physics_requires_normal_p (cdiINT32 cdi_physics_type);

//---------------------------------------
//---- sCDI_UDS_PHYS_TYPE_DESCRIPTOR ----
//---------------------------------------
typedef class sCDI_UDS_PHYS_TYPE_DESCRIPTOR
{
public:

    struct sDP_VAR
    {
      cdiINT32    id;
      const char *name;
      const char *unit_class;
    };

    cdiINT32 parameter_index (cdiINT32 id) const
    {
        return lookup_id(parameter_var, CDI_MAX_CONTINUOUS_DP, id);
    }

    cdiINT32 initial_condition_index (cdiINT32 id) const
    {
        return lookup_id(initial_condition_var, CDI_MAX_INITIAL_CONDITIONS, id);
    }

    cdiINT32 uds_physics_type;
    const char *name;    
    cdiINT32 n_parameters;
    cdiINT32 n_initial_conditions;

    const sDP_VAR *parameter_var[CDI_MAX_CONTINUOUS_DP];
    const sDP_VAR *initial_condition_var[CDI_MAX_INITIAL_CONDITIONS];

private:

    static cdiINT32 lookup_id (const sDP_VAR *const var_array[], cdiINT32 n_vars, cdiINT32 id)
    {
        for (cdiINT32 i = 0; i < n_vars && var_array[i]; ++i) {
            if (var_array[i]->id == id)
                return i;
        }
        return -1;
    }
} *CDI_UDS_PHYS_TYPE_DESCRIPTOR;


CDI_UDS_PHYS_TYPE_DESCRIPTOR cdi_lookup_uds_physics(cdiINT32 uds_physics_type) ;
  

//-------------------------------
// The following class computes the equilibrium drift velocity (terminal velocity) of a particle 
// of a certain size and density falling through air of a arbitrary density.  It exists here 
// so the same calculation can be used in PowerVIZ's rain emitters.

typedef class sPARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE
{

 private:

  double min_reynolds_number;
  double max_reynolds_number;
  double min_K;
  double max_K;
  double K_tolerance;
  double reynolds_number_tolerance;

  std::vector<double> reynolds_number_table;
  std::vector<double> k_value_table;

  double Re2_Cd(double reynolds_number); //Compute the drag coefficient times Reynolds number squared at a given Reynols number.
  double dRe2_Cddx(double reynolds_number); //Compute the deriveative of Re^2 * C_d with respect to Reynolds number
  VOID construct_equilibrium_reynolds_number_lookup_table();
  double lookup_equilibrium_reynolds_number(double K);
  
 public:
  sPARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE() {
    min_reynolds_number = 1e-3;
    max_reynolds_number = 1e6;
    K_tolerance = 1e-4;
    reynolds_number_tolerance = 1e-4;
    min_K = Re2_Cd(min_reynolds_number);
    max_K = Re2_Cd(max_reynolds_number);
    construct_equilibrium_reynolds_number_lookup_table();
  }

  //These methods are included for reference.

  double compute_drag_coefficient(double reynolds_number) {
    return Re2_Cd(reynolds_number) / reynolds_number / reynolds_number;
  }

  double compute_drag_force_magnitude(double diameter, 
                                      double rho_air,
                                      double relative_velocity_magnitude, 
                                      double kinematic_viscosity_of_air);
  
  
  double compute_terminal_velocity_magnitude(double gravity_magnitude,
                                   double rho_air,
                                   double kinematic_viscosity_of_air,
                                   double rho_particle,
                                   double particle_diameter);

}* PARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE;


#endif
