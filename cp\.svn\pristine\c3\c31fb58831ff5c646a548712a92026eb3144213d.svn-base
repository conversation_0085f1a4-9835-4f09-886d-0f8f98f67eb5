/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * This file describes the CP data structures
 *
 * Jim Salem, Exa Corporation 
 * Created Sat Jun  4 1994
 *--------------------------------------------------------------------------*/

#ifndef __CP_INFO_H
#define __CP_INFO_H

#include "common.h"
#include "window.h"
#include "checkpoint_control.h"
#include "table.h"
#include "jobctl.h"
#include "rotational_dynamics.h"
#include "cp_seed_from_meas.h"
#include "dsm_reader.h"
#include "transient_boundary_seeding.h"

// For 5G large pore
#include "porous_rock.h"

// For autostop
#include "monitor.h"
#include MSA_H

#include <map>
#include <vector>

#include RADIO_H


#if SURF_COUP
#include "surface_coupling.h"
#include "coupling_model.h"

typedef class cNATIVE_MODEL_WIP : public cMIO_WIP
{
public:
  VOID Warning(cSTRING msg)
  {
    msg_warn("PowerFLOW mesh: %s", msg);
  }
  VOID Error(cSTRING msg)
  {
    msg_error("PowerFLOW mesh: %s", msg);
  }
} *NATIVE_MODEL_WIP;

#endif

/* @@@ This seems to be dead and should be retired */
#define SIM_STATUS_UNKNOWN  ((SIM_STATUS) -12347)


#define OLD_LATTICE_GAMMA  1.5
#define CP_AIR 1007
#define GAS_CONSTANT 287.0

/*-----------------------------------------------*
 * Repository of CDI data used during parsing
 *-----------------------------------------------*/

typedef struct sCDI_DATA {
  int major_version;
  int minor_version;
  dFLOAT char_density;
  dFLOAT char_vel;
  dFLOAT char_temp;
  dFLOAT mean_temp; // only present for active scalar simulations
  dFLOAT specific_heat;
  dFLOAT lattice_gamma;
  dFLOAT kilos_pp;
  
  dFLOAT meters_per_cell;
  dFLOAT seconds_per_timestep;
  dFLOAT kelvins_per_lattice_temp;
  dFLOAT kilos_per_particle;
  //sINT32 n_dims; already in cp_info

  dFLOAT gas_constant;
  dFLOAT origin[3];

  //added for dgf //DMH
  dFLOAT fluid_Prandtl_number;
  dFLOAT turb_Prandtl_number;
  dFLOAT nu_molecular;
  dFLOAT reynolds_number;
  dFLOAT char_length;
  dFLOAT lattice_gas_const;
  dFLOAT max_exp_vel;

  //melting solver
  dFLOAT specific_heat_solid;
  dFLOAT thermal_conductivity_solid;
  dFLOAT thermal_conductivity_liquid;

  dFLOAT radiation_background_temp;
  dFLOAT radiation_update_ratio;

  sCDI_DATA() {
    char_vel                 = -1.0;
    char_density             = -1.0;
    char_temp                = -1.0;
    mean_temp                = -1.0;
    specific_heat            = CP_AIR;
    lattice_gamma            = OLD_LATTICE_GAMMA; 
    meters_per_cell          = 0;
    seconds_per_timestep     = 0;
    kelvins_per_lattice_temp = 0;
    kilos_per_particle       = 0;
    gas_constant             = GAS_CONSTANT;
    lattice_gas_const        = 1.0; 
    max_exp_vel              = -1.0;
    //melting solver
    specific_heat_solid      = 1560;     // J/(kg*degK)
    thermal_conductivity_solid = 0.75;   // W/(m*degK)
    thermal_conductivity_liquid = 0.57;  // W/(m*degK) 
    radiation_background_temp = 0.0;
    radiation_update_ratio = 1.0;
  }

  // see doc for cdi_is_special_condsm_cdi()
  bool is_pre_partitions_cdi() {
    return (major_version < 8) || 
           (major_version == 8 && minor_version < 3) ||
           (major_version == 9 && minor_version < 4); 
  }

} CDI_DATA;

extern sCDI_DATA cdi_data;

typedef struct sPRE_TABLE_CMD {
  cSTRING table_name;
  cSTRING cmd_string;
  sPRE_TABLE_CMD *next;
} *PRE_TABLE_CMD;

typedef struct sMEAS_TIMING_OPTION {
  cSTRING  meas_window_name;
  cBOOLEAN is_acoustic_timing;
  // -1 for any of the timing parameters means that the CDI value should be used
  dFLOAT start_time;
  dFLOAT end_time;
  dFLOAT period;
  dFLOAT average_interval;
  dFLOAT min_freq;
  dFLOAT max_freq;
  dFLOAT min_cycles;
  dFLOAT nyquist_multiplier;
  cSTRING time_unit;    // timestep by default
  cSTRING freq_unit;    // Hz by default
  sMEAS_TIMING_OPTION *next;
} *MEAS_TIMING_OPTION;

typedef struct sMEAS_INCLUDE_EXCLUDE_OPTION {
  STRING meas_window_name;
  sMEAS_INCLUDE_EXCLUDE_OPTION *next;
} *MEAS_INCLUDE_EXCLUDE_OPTION;

typedef struct sAUTOSTOP_ON_OFF_OPTION {
  STRING monitor_name;
  sAUTOSTOP_ON_OFF_OPTION *next;
} *AUTOSTOP_ON_OFF_OPTION;


typedef struct sVPoint {
//#ifdef PARTICLE_MODELING_DOUBLE_PRECISION_VERTEX
  dFLOAT pcoord[3]; 
//#else
//  sFLOAT pcoord[3]; 
//#endif
} *VPoint;


// For PowerTHERM adaptive coupling
typedef struct sADAPTIVE_PARAMS {
  //std::vector<int> adaptive_phases;
  dFLOAT adaptive_up_coeff;     // To scale up the PT/PF ratio. Used when the gradient is smaller than gradient_low
  dFLOAT adaptive_down_coeff;   // To scale down the PT/PF ratio. Used when the gradient is bigger than gradient_high
  dFLOAT gradient_low;      // If gradient < gradient_low, increase the PT/PF ratio
  dFLOAT gradient_high;     // If gradient > gradient_high, decrease the PT/PF ratio
  dFLOAT ratio_max;
  dFLOAT ratio_min;

  dFLOAT gradient_percentage_threshold;   // Ratio does not change if the gradient is within a small percentage of the previous gradient
  BOOLEAN use_max_temp;
  BOOLEAN fix_pt_time_p;      // Whether to adjust simulation time to make the total powertherm time fixed
  dFLOAT  total_pt_duration;
  dFLOAT  first_pt_start_time;    // PowerTHERM start time for the first coupling. Used to control whether to exit with max PT duration

  sADAPTIVE_PARAMS(): adaptive_up_coeff(1.0),
                      adaptive_down_coeff(1.0),
                      gradient_low (0.0),
                      gradient_high (10000.0),
                      ratio_max (10.0),
                      ratio_min (1.0),
                      gradient_percentage_threshold(0.10),
                      use_max_temp (0),
                      fix_pt_time_p (FALSE),
                      total_pt_duration (0.0),
                      first_pt_start_time (-1.0) {}
  BOOLEAN update_adaptive_params();
  dFLOAT get_pt_pf_ratio_scale_factor(dFLOAT current_pt_pf_ratio, dFLOAT t_gradient);
  inline BOOLEAN is_gradient_in_range(sFLOAT t_gradient) { return (t_gradient >= gradient_low && t_gradient <= gradient_high); } 
} *ADAPTIVE_PARAMS;

// For each heat exchanger, get its type, local index and list of hx monitors it contributes to.
typedef struct sHX_INFO {
  asINT32 type; // CDI_HXCH, CDI_CDSR, or CDI_AMHX
  asINT32 index;  // index with each type
  TIMESTEP period;  // Period of the heat exchanger measurement (coupling)
  std::vector<asINT32> monitors;  // monitor indices
  asINT32 table_index;    // index of the table associated with this hx
  cSTRING results_filename; // results file where the summary data can be found
  asINT64 old_pos;   // Position of the last read
} *HX_INFO;

// lattice_csys occupies slot 0 in a meas file. Thus CDI csys indices must
// be incremented by 1 when used in a meas file.
inline asINT32 cdi_csys_to_sri_csys(asINT32 cdi_csys) { return cdi_csys + 1; }

enum MEAS_COMPRESS {
  DO_NOT_FORCE = 0,
  ENABLE_COMPRESSION,
  DISABLE_COMPRESSION
};

/*--------------------------------------------------------------------------*
 * CP STRUCTURE to hold information ported from CDI
 *--------------------------------------------------------------------------*/
typedef struct sCP_DATA_ENCRYPTION : sSRI_ENCRYPTION_DATA {

  VOID parse_from_cdi_info(const CDI_INFO& cdi_info_obj);

  //Used in copying information from cp_info to SRI_PARAMS
  sSRI_ENCRYPTION_DATA get_sri_encryption_struct() const;

private:
  friend class sCP_DGF_READER;
  VOID verify_encryption_version_from_lgi(int lgi_encryption_version);
  VOID verify_part_protection_ids_from_lgi(const std::vector<uINT32>& lgi_part_protection_ids);

} sCP_DATA_ENCRYPTION;

struct sCALIBRATION_PARAMS {
  asINT32 face_index;
  asINT32 meas_window_index;
  asINT32 iterations;
  cBOOLEAN cancel_pressure_fluctuations;
  cBOOLEAN subtract_meas_velocity;
  cBOOLEAN reset_initial_condition;
  sCALIBRATION_PARAMS() {
    iterations = -1;
    meas_window_index = -1;
  }
  VOID init(CDI_CLBR clbr) {
    face_index = clbr->face_index;
    meas_window_index = clbr->meas_window_index;
    iterations = clbr->calibration_iterations;
    cancel_pressure_fluctuations = clbr->cancel_pressure_fluctuations;
    subtract_meas_velocity = clbr->subtract_mean_velocity;
    reset_initial_condition = clbr->reset_initial_condition;
  }
  VOID write_clbr_record(DGF_RW_CALIBRATION_PARAMETERS_REC &record, TIMESTEP &end_time) {
    record.face_index = face_index;
    record.meas_window_index = meas_window_index;
    record.calibration_iterations = iterations;
    record.cancel_pressure_fluctuations = cancel_pressure_fluctuations;
    record.subtract_mean_velocity = subtract_meas_velocity;
    record.reset_initial_condition = reset_initial_condition;
    record.clbr_end_time = end_time;
  }
};

struct sTBS_INFO {
  cBOOLEAN is_tbs;
  sTBS_INFO(): is_tbs(FALSE) {}
  VOID write_tbs_info(cDGF_TBS_INFO &record) {
    record.is_tbs = is_tbs;
  }
};

struct sCP_PARTITIONS_INFO {
  cCDI_PARTITIONS& partitions() { return m_cdi_partitions; }
  VOID set_is_pre_heirarchical_partitions_cdi(BOOLEAN v) { is_pre_partitions_cdi = v; }
  BOOLEAN is_pre_heirarchical_partitions_cdi() const { return is_pre_partitions_cdi; }
private:
  //This flag is needed because new CDI will construct a trivial partitions structure
  //even for older files.
  BOOLEAN is_pre_partitions_cdi;
  cCDI_PARTITIONS m_cdi_partitions;
};

/*--------------------------------------------------------------------------*
 * CP Lattice data structure
 *--------------------------------------------------------------------------*/
typedef struct sCP_INFO {
  // Tables that may be read during simulation
  sINT32             n_tables;          // Number of tables
  TABLE_DESC         table_descs;       // Vector of n_tables TABLE_DESC structs
  TABLE_QUEUE_ENTRY  table_queue;       // List of tables to be read when available
  CP_TABLE_COMM_DESC table_comm_descs;
  cSTRING            table_search_path;
  std::map<std::string,std::vector<int> >  physical_node_sp_ranks;

  cDGF_CONTROL  control_rec;

  // A record of when the CP was invoked
  time_t	launch_time;	

  cBOOLEAN is_some_bc_time_varying;

  // File parentage of input LGI
  LGI_PARENTAGE_REC ptge;
  STRING disc_platform;
  STRING decomp_target;
  STRING powercase_version;
  STRING disc_version;
  STRING decomp_version;
  STRING loadfactors_version;

  // For audit trail
  CHARACTER simeng_version[MAX_VERSION_STRING];

  sSIM_SOLVER_STATUS cp_status; // Overall simulation status

  // Number of SP's above which we no longer sleep in main loop

  sINT32 nosleep_spcount;   

  STP_LATTICE_TYPE lattice_type;
  SRI_HT_TYPE      sim_ht_type;
  cBOOLEAN         is_density_constant;
  cBOOLEAN         is_lattice_temperature_constant;
  dFLOAT           constant_lattice_temperature_value;

  // radiation solver
  dFLOAT stefan_boltzmann_const = -1.0;
  dFLOAT radiation_background_temp;
  RadIO::sPATCH_DECOMP patch_decomp;
  
  STP_SCALE        num_scales;

  // Time step information
  TIMESTEP start_time;        // First timestep (always 0 in current code)
  TIMESTEP time;              // Current timestep
  TIMESTEP flow_time;
  TIMESTEP cond_time;
  TIMESTEP restart_base_time; // Restart time in base timesteps
  TIMESTEP end_time;          // Last Timestep in the simulation
  TIMESTEP flow_end_time;          // Last Timestep in the simulation
  TIMESTEP cond_end_time;          // Last Timestep in the simulation

  std::string avg_mme_ckpt_preit_filename; // filename used before the end of initial transient
  std::string avg_mme_ckpt_filename;
  std::string avg_mme_ckpt_tmp_filename;
  BOOLEAN     avg_mme_ckpt_after_eit_p; // Whether the avg mme ckpt is written after EIT (end of initial transient) when 
                                        // the avg mme meas window is started via monitors. 
  std::string avg_mme_ckpt_preit_filename_cond; // filename used before the end of initial transient
  std::string avg_mme_ckpt_filename_cond;
  std::string avg_mme_ckpt_tmp_filename_cond;
 
  BOOLEAN  is_avg_mme_ckpt_compressed;

  TIMESTEP avg_mme_ckpt_interval; // Interval for averaging the mme checkpoints data. Used by write_mme_ckpt()
  BOOLEAN  has_average_mme_window;  // Whether the case has an average mme ckpt meas window
  BOOLEAN  average_mme_stopped;     // Whether the avg mme ckpt is already stopped. Used for full ckpt resume.
  
  BOOLEAN  exit_on_schedule; 
  EVENT_ID exit_event_id;

  BOOLEAN  request_to_exit_by_monitor;
  BOOLEAN  request_to_exit_by_monitor_convergence;  // Flag to tell if the exist request is triggered by monitor convergence. 
                                                    // Once true, the code will not check autostop by monitors.
  BOOLEAN  request_to_exit_by_greatest_meas_window_end_time;
  TIMESTEP exit_time_requested_by_monitor;

  BOOLEAN  request_to_exit_by_pt_time;
  BOOLEAN  exit_time_requested_by_pt_time;

  BOOLEAN  request_to_stop_avg_mme;
  BOOLEAN  request_to_reschedule_avg_mme;
  TIMESTEP requested_time_to_stop_avg_mme;
  TIMESTEP requested_time_to_reschedule_avg_mme;

  BOOLEAN  request_to_start_emitter;
  BOOLEAN  request_to_start_emitter_id;
  BOOLEAN  requested_time_to_start_emitter;

  BOOLEAN  request_to_start_wiper;
  BOOLEAN  request_to_start_wiper_id;
  BOOLEAN  requested_time_to_start_wiper;

  TIMESTEP time_to_stop_avg_mme;    // Real time to stop avg mme after negotiations between CP and SPs

  TIMESTEP avg_mme_duration;

  // solver timestep
  TIMESTEP  n_lb_base_steps;
  TIMESTEP  n_t_base_steps;
  TIMESTEP  n_ke_base_steps;
  TIMESTEP  n_uds_base_steps;
  TIMESTEP  n_conduction_base_steps;
  TIMESTEP  n_radiation_base_steps;
  TIMESTEP n_particle_base_steps;
  TIMESTEP n_user_base_steps;
  TIMESTEP n_lattice_base_steps;

  // initial solver mask
  STP_ACTIVE_SOLVER_MASK init_solver_mask;

  // Lighthill switch time
  TIMESTEP  acous_start_time;

  // Momentum freeze solver
  BOOLEAN   freeze_momentum_field;
  dFLOAT    thermal_timestep_ratio;
  sINT32    momentum_freeze_start_time;

  BOOLEAN   full_ckpt_with_avg_mme;
  BOOLEAN   full_ckpt_with_frozen_vars;
  // Custom vars
  sINT32    num_fluid_components;             //5g
  SRI_FLUID_COMPONENT s_fluid_components_5g;  //5g
  SRI_CUSTOM_VAR_ID_HELPER cvid_helper;       //custom_var_id_helper

  // Ice film development
  cBOOLEAN is_water_vapor_transport; // to avoid modifying UDS code
  dFLOAT film_acceleration_factor; // to avoid modifying UDS code

  // conduction solver
  cBOOLEAN is_flow;
  cBOOLEAN is_conduction;
  cBOOLEAN is_shell_conduction_solver;
  cBOOLEAN is_radiation;
  
  sREALM_PHASE_TIME_INFO realm_phase_time_info[STP_N_REALMS];

  uINT32 num_averaged_contacts;

  // new options from cdi 7.28
  cBOOLEAN variable_gamma;

  // UDS
  cBOOLEAN is_uds_transport;
  SRI_UDS_INFO s_scalar_materials;

  /**** Checkpoint information ****/
  /*    Regularly scheduled checkpoints */
  asINT64 parallel_io_one_write_buffer_size_mb;
  cBOOLEAN      periodic_ckpts_p;
  sTIME_DESC	ckpt_time_desc;
  /*      Value of the SP's next_ckpt_time slot */
  TIMESTEP	next_ckpt_time;
  TIMESTEP	last_next_ckpt_time;

  /*    "Asynchronous" checkpoints */
#if	0
  BOOLEAN	async_ckpt_request_p;
  BOOLEAN	exit_after_async_ckpt_p;
  BOOLEAN	full_async_ckpt_p;
  BOOLEAN 	async_ckpt_at_coarsest_time_p;
#else
  EVENT_ID	async_event_id_p;
  BOOLEAN 	async_event_at_coarsest_time_p;
#endif

  CKPT_QUEUE    async_ckpt_queue;

  cSTRING	checkpoint_filename;	// if ckpt filename overridden from command line

  DSM_READER dsm_reader; // for reading Dynamic Scalar Multiplier Values for 5G code

  sASYNC_EVENT_PKG async_event_pkg; 
  BOOLEAN       async_msg_reply_pending_p;

  MPI_Datatype  eMPI_ASYNC_MSG_TYPE;

  uINT8         n_signal_cmds_pending;
  uINT8         n_signal_cmds_read;
  std::vector <sCMD_STRING >* pending_signal_cmds;

  /**** Filenames and Files ****/
  cSTRING	lgi_filename;
  std::string   rad_filename;
  cSTRING	root_filename;	// lgi_filename without .lgi extension]
  cSTRING       run_dir;

  /**** Measurement windows ****/
  sINT32	  n_cdi_meas_windows;
  sINT32          n_non_surf_coupling_cdi_meas_windows; // surf coupling windows are last in cdi_meas_windows
  CDI_MEAS_WINDOW cdi_meas_windows;
  POST_MEAS_CMD   cmd_line_post_meas_cmds; // after meas cmds requested on the cmd line
  PRE_TABLE_CMD   cmd_line_pre_table_cmds; // before table cmds requested on the cmd line
  sINT32          n_hxchs;    // number of heat exchangers actually read
  sINT32          n_cdsrs;    // number of heat exchangers actually read
  sINT32          n_amhxs;    // number of AMESim heat exchangers actually read
  CDI_HXCH*       cdi_hxchs;  // array of pointers to heat exchangers (Radiator/CAC types)
  CDI_CDSR*       cdi_cdsrs;  // array of pointers to condensers (special type of heat exchanger) 
  CDI_AMHX*       cdi_amhxs;  // array of pointers to AMESim heat exchangers (Radiator/CAC/Condenser types)
  char            **hxch_cmd_strings; // pointer to array of char pointers
  char            **cdsr_cmd_strings; // pointer to array of char pointers
  char            **amhx_cmd_strings; // pointer to array of char pointers
  cBOOLEAN        powercool_dir_created_p; // a heat exchanger or a condenser can create a powercool directory
  cBOOLEAN        amesim_dir_created_p; // a heat exchanger can create a amesim directory
  cBOOLEAN        is_double_precision_meas_forced; // double precision meas output forced via command line

  cBOOLEAN        is_meas_window_inclusion_list;
  cBOOLEAN        has_autostop_on_list;
  cBOOLEAN        has_autostop_off_list;
  AUTOSTOP_ON_OFF_OPTION cmdline_autostop_off_monitors;
  AUTOSTOP_ON_OFF_OPTION cmdline_autostop_on_monitors;
  MEAS_INCLUDE_EXCLUDE_OPTION cmdline_include_exclude_meas_windows;
  MEAS_TIMING_OPTION          cmdline_meas_timing_options;

  cCDI_CSYS all_csys;      // coordinate systems
  std::vector<std::vector<asINT32>> meas_cell_index_per_window;
  std::vector<std::vector<asINT32>>  meas_surfel_index_per_window;
  sCP_MEAS_WINDOW_COLLECTION meas_windows;

  asINT32                    n_meas_windows() { return meas_windows.n_meas_windows(); }

  // This queue is used to transfer measurement windows from comm/main thread to write measurements thread
  WINDOW_QUEUE_MT write_sri_results_queue;
  // This queue is used to transfer measurement windows from write measurement thread to comm/main thread
  WINDOW_QUEUE_MT post_recvs_queue;

  AUDIT_TRAIL	audit_trail;

  // SRI measurement file related data
  std::vector <sSRI_CHAR_PARAM_DESC> sri_params;
  SRI_LATTICE_TRANSLATION sri_translations;
  sriINT n_sri_translations;
  SRI_CSYS sri_all_csys;
  sriSHORT sri_n_csys; 

  SRI_VEHICLE_DEFN sri_vehicle_defn;

  sSRI_ASSEMBLY *sri_assemblies;
  sriASSEMBLY_ID n_sri_assemblies;
  sSRI_PART *sri_parts;
  sINT32 *fluid_physics_desc_index_from_part_index;
  sINT32 *cdi_phys_type_from_fluid_physics_desc_index;
  sINT32 *cdi_phys_type_from_hcsd_physics_desc_index;
  
  sriPART_ID n_sri_parts;
  sSRI_FACE *sri_faces;
  sriFACE_ID n_sri_faces;
  std::vector<sINT8> face_index_is_front_only;
  std::vector<sINT32> front_flow_surface_phys_desc_index_from_face_index;
  std::vector<sINT32> front_thermal_surface_phys_desc_index_from_face_index;
  std::vector<sINT32> back_flow_surface_phys_desc_index_from_face_index;
  std::vector<sINT32> back_thermal_surface_phys_desc_index_from_face_index;
  std::vector<sINT32> cdi_phys_type_from_flow_surface_physics_desc_index;
  cBOOLEAN *is_face_tbs;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  DGF_VERTEX_INDEX n_original_surfel_vertices;
//#endif
  VMEM_VECTOR<sVPoint> vertex_array;
  SRI_SOLVER_TIME_PHASE sri_solver_phases;
  sriINT                n_sri_solver_phases;

  cCDI_UDDL m_dep_properties_info;
  cCDI_UDDL dep_properties() { return m_dep_properties_info; }
#if SURF_COUP
  //  VOID copy_vertex_array_from_native_mesh();
#endif

  asINT32 *part_to_movb_phys_desc_index_map;
  bool are_movbs_present() const {
    return n_movbs() > 0;
  }
  TIMESTEP convert_to_ts_flow(TIMESTEP base_time);
  TIMESTEP convert_to_ts_cond(TIMESTEP base_time);
  TIMESTEP convert_from_ts_flow(TIMESTEP flow_time);
  TIMESTEP convert_from_ts_cond(TIMESTEP cond_time);
  /**** Bounding box of all fluid */
  sriINT simvol_size[3];

  cBOOLEAN is_turb;		// simulation with turbulence modeling ?
  cBOOLEAN is_heat_transfer;
  cBOOLEAN create_full_ckpts;	// as opposed to MME ckpts
  cBOOLEAN is_incompressible_solver;
  cBOOLEAN is_sim_double_precision;
  cBOOLEAN is_high_subsonic_mach_regime;
  cBOOLEAN is_transonic_mach_regime;
  cBOOLEAN is_boundary_layer_transition_model;
  cBOOLEAN is_ht_off_in_powercase;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  cBOOLEAN is_particle_solver;
  cBOOLEAN is_thermal_particle_solver;
  cBOOLEAN is_accretion_simulation;
  cBOOLEAN is_film_solver;
  cBOOLEAN enable_film_trajectory_measurement;
//#endif
  cBOOLEAN is_5g_sim;
  cBOOLEAN is_large_pore_sim;
  cBOOLEAN input_rock_table;

  cBOOLEAN is_checkpoint_body_force;
  cBOOLEAN is_seed_body_force;

  cBOOLEAN is_melting_solver;

  sINT8    n_dims;
  sINT8    n_lattice_vectors;
  sINT8    n_states;

  sINT32 n_scalars;           // total number of scalars
  sINT32 sri_vars_per_scalar;  // read from simv
  sINT32 first_uds_sri_variable;
  sINT32 last_uds_sri_variable;

  cBOOLEAN is_full_checkpoint_restore;
  cBOOLEAN is_mme_checkpoint_restore;
  TIMESTEP restart_time;
  TIMESTEP restart_time_flow;
  TIMESTEP restart_time_cond;

  CDI_CONDENSED_UNITS_DB condensed_units_db; // lattice, dimless, and user-defined unit defs
  UNITS_DB units_db;

  /* Surface coupling stuff */
  asINT32    n_surface_couplings;
#if SURF_COUP
  cMIO_MODELf::cMESH *native_mesh;
  cMIO_MODELf *native_model;
  cNATIVE_MODEL_WIP native_model_wip;
  SURFACE_COUPLING surface_couplings;
  COUPLING_MODEL_DESC coupling_model_descs;
  COUPLING_MODEL_QUEUE_ENTRY coupling_model_queue;
  CP_COUPLING_MODEL_COMM_DESC coupling_model_comm_descs;
  sFLOAT *coupling_export_var_array; // array to hold the coupling export variables

#endif // SURF_COUP

  //  For 5G large pore
  std::vector<cPOROUS_ROCK> porous_rock;

  asINT32 coarsest_scale() { return 0; }
  asINT32 finest_scale() { return num_scales - 1; }

  /* Rotational dynamics stuff */
  asINT32                       n_rotational_dynamics_descs;
  CP_ROTATIONAL_DYNAMICS_DESC   rotational_dynamics_descs;

  // PowerTHERM specific options that override the global options 
  cSTRING ptherm_model_path;
  cSTRING ptherm_predecessor_run;
  cBOOLEAN ptherm_only_keep_orig_model;
  cBOOLEAN ptherm_wait_for_license;
  asINT32 ptherm_processes;
  asINT32 ptherm_threads;
  cSTRING ptherm_remote_host;
  cSTRING ptherm_remote_user;
  cSTRING ptherm_mpifile;
  cBOOLEAN ptherm_model_changed_p;
  cBOOLEAN ptherm_ckpt_skip_first_coupling_p;
  cSTRING ptherm_options;
  // radtherm path
  cSTRING radtherm_path;
  cBOOLEAN force_ptherm_p;
  cBOOLEAN force_radtherm_p;
  // general interpolation map options
  sFLOAT ptherm_max_unmatched_area_ratio;

  int amesim_timeout;
  cSTRING amesim_model_path;

  // support old style "standard" measurement variables
  // or new style explicit measurement variables
  cBOOLEAN is_std_meas_mme_p;

  cBOOLEAN is_global_ref_frame;
  cBOOLEAN is_global_ref_frame_time_varying;
  sGRF_SP_TO_CP_INIT global_nirf_info;

  sINT32 n_fpes_allowed_in_timestep;
  asINT32 n_lrfs() { return sri_lrfs.size(); }
  std::vector<sSRI_LRF> sri_lrfs;
  sriPOINT *lrf_n_fluid_like_voxels;
  cBOOLEAN *sri_lrf_has_constant_angular_vel_flags;       /* backup copy of the sSRI_LRF::has_constant_angular_vel flags */
  std::vector< sriDOUBLE * > sri_lrf_polyline_vertices;

  asINT32 n_time_varying_lrfs() { return time_varying_lrf_indices.size(); }
  std::vector<sriLRF_INDEX> time_varying_lrf_indices; // the indices of the time varying LRFs in sri_lrfs 

  asINT32 n_mbcs() { return sri_mbcs.size(); }
  std::vector<sSRI_MBC> sri_mbcs;

  asINT32 n_time_varying_mbcs() { return time_varying_mbc_indices.size(); }
  std::vector<sriMBC_INDEX> time_varying_mbc_indices; // the indices of the time varying MBCs in sri_mbcs

  cBOOLEAN *sri_mbc_has_constant_vel_flags;           // backup copy of the sSRI_MBC::has_constant_velocity flags

  std::vector<std::string> bseed_faces;                   // selective boundary seeding via -bseed_include_faces
  cBOOLEAN                 is_bseed_faces_inclusion_list; // as opposed to exclusion list

  // boundary seeding from sampled surface measurements
  asINT32             n_seed_from_meas_descs;
  asINT32             n_transient_seed_from_meas_descs;
  asINT32             *seed_from_meas_desc_index_from_face_index;
  auINT32             *seed_from_meas_mask_from_face_index;
  std::string         default_seed_from_meas_location;
  std::map<std::string, std::string> seed_from_meas_location_map;
  auINT32             m_n_surfels_on_transient_seeded_faces;

  // store the per surfel/per ublk processor ids
  std::vector<STP_PROC> surfel_procs[STP_N_REALMS];    
  std::vector<STP_PROC> ublk_procs[STP_N_REALMS];    
  std::vector<SEED_FROM_MEAS_DESC> seed_from_meas_descs;

  sINT64 total_fluid_like_voxels;      // STP_UBLK_ID is not large enough
  dFLOAT total_fluid_like_fevos;

  //new
  sINT64 total_nw_fluid_like_voxels;
  cBOOLEAN local_vel_freeze;
  cBOOLEAN do_split_seed;
  cBOOLEAN store_special_vars;
  
  std::array<sINT64,STP_N_REALMS>   total_fluid_like_voxels_in_each_realm;
  std::array<sINT64,STP_N_REALMS>   total_nw_fluid_like_voxels_in_each_realm;

  STP_SURFEL_ID total_regular_surfels; // excludes sampling surfels
  dFLOAT        total_regular_fesus;   // fine equivalent surfels

  STP_BSURFEL_ID total_bsurfels; // bsurfels for all scales 


  // cached from the DGF control record
  std::array<STP_UBLK_ID,STP_N_REALMS>   num_ublks;
  std::array<STP_SURFEL_ID,STP_N_REALMS> num_surfels;
  STP_BSURFEL_ID num_bsurfels;

  BOOLEAN is_realm_active(REALM realm) {
    if(num_ublks[realm] == 0 && num_surfels[realm] == 0)
    return false;
  else
    return true;
  }

  asINT32 n_movbs() const { return sri_movbs.size(); }
  std::vector<sSRI_MOVING_FACE> sri_movbs;

  asINT32 n_movb_xforms() { return sri_movb_xforms.size(); }
  std::vector<sSRI_MOVING_FACE_DESCRIPTOR> sri_movb_xforms;

  sSRI_DEFORMING_TIRE * sri_dtires;
  asINT32 n_dtires;

  bool has_deforming_tires() const { return n_dtires > 0; }

  // Before deforming tires were implemented, the deforming tire cvdp chunk was treated
  // as a boolean that was always 0. Now we treat it as an index. If the index is
  // 0, we need to check to see if we actually have tires before using the index
  bool is_deforming_tire_index(asINT32 deforming_tire_index) {
    if ( deforming_tire_index == -1 ) {
      return false;
    } else if ( deforming_tire_index == 0 ) {
      return this->n_dtires > 0;
    }
    else { // index > 0
      return true;
    }
  }

  // Updates an old school deforming tire index into the current form, which
  // uses -1 to mean "there is no deforming tire"
  asINT32 update_deforming_tire_index(asINT32 idx) {
    return is_deforming_tire_index(idx) ? idx : -1;
  }

  // backup copy of the sSRI_MOVING_FACE_DESCRIPTOR::has_constant_velocity flags 
  // This is used when writing probe files, see mark_all_lrf_mbc_and_movb_constant_vel()
  cBOOLEAN *sri_movb_xform_has_constant_velocity_flags; 
  asINT32         *face_to_parent_part_index_map;
  MEAS_COMPRESS meas_compression;
  //
  // Number of turbulent velocity history tables
  uINT16 num_turb_tables;
  STP_PROC m_turb_synth_ckpt_sp;
  
  // Vector of pointers to all monitors
  std::vector<MONITOR> monitors;
  std::string monitor_log_filename; 
  asINT32 longest_monitor_name_size;

  eCDI_SIM_DURATION_VIA::Enum sim_duration_via;

  TIMESTEP duration_after_init_transient;       // Simulation duration after the end of 
                                                // initial transient  
  //asINT32 n_monitors_end_sim_via_init_transient;  // Number of monitors which are used to find 
                                                       // the end of initial transient time of the whole simulation
  std::vector<asINT32> monitors_to_find_sim_end_init_transient;
  asINT32 n_monitors_to_check_sim_end_init_transient;  // Number of monitors still need to be checked 
                                                       // for finding the end of initial transient of the whole simulation.
  std::vector<MONITOR> monitors_to_end_sim;
 
  TIMESTEP last_timestep_to_check_autostop_monitors_converged; // Used to stop sim once all autostop monitors are converged.

  asINT32 n_meas_windows_to_check_end_time;     // Used to stop sim at greatest meas window end time
  TIMESTEP  greatest_meas_window_end_time;
  
  std::vector<FLOW_MONITOR> flow_monitors;
  std::vector<SOLID_MONITOR> solid_monitors;
  std::vector<POWERTHERM_MONITOR> pt_monitors;
  std::vector<HX_MONITOR> hx_monitors;

  // Global hx array which contains all types of heat exchangers including: hxch, cdsr, amhx
  std::vector<sHX_INFO> all_hx_info;
  
  BOOLEAN adaptive_coupling_p; // True if at least one coupling phase is adaptive
  sADAPTIVE_PARAMS m_adaptive_params;
  BOOLEAN ptherm_dump_gradient_p; // Dump PowerTHERM temperature temporal gradient for each coupling in file 
                                  // powertherm/"model_name".gradient_out

  dFLOAT next_powertherm_start_time;  // Next PowerTHERM thermal time, in seconds
  BOOLEAN is_ptherm_time_input_to_eqns; // Whether ptherm time is used for equations

  // map of moving face cdi_id to xform 
  std::map<asINT32, sSRI_XFORM> face_index_to_seed_xform_map;
  std::vector<sSRI_XFORM> ckpt_movb_xforms;
  cSTRING solver_version;
  sCP_DATA_ENCRYPTION encryption_struct;
  sCALIBRATION_PARAMS calibration_params;
  sTBS_INFO tbs_info;
  cBOOLEAN is_rwnc_seeded;
  
  // time coupling phases between realms
  STP_SCALE coarsest_coupled_scale;
  std::vector<sTIME_COUPLING_PHASE> time_coupling_phases;
  cCOUPLING_PHASES_STATS coupling_phase_timers;
  
  //Partitions heirarchy
  sCP_PARTITIONS_INFO m_partitions_info;
  cCDI_PARTITIONS& partitions() { return m_partitions_info.partitions(); }

  cBOOLEAN enable_tangential_shell_conduction;

  // implicit solver data
  cBOOLEAN use_implicit_shell_solver;
  cBOOLEAN use_implicit_solid_solver;
  long int num_implicit_shell_solver_states;
  long int num_implicit_solid_solver_states;
  std::vector<int> implicit_shell_solver_state_index_offset;
  std::vector<int> implicit_solid_solver_state_index_offset;

} *CP_INFO;

extern sCP_INFO cp_info;

inline asINT32 sim_num_scales() { return (cp_info.num_scales); }


inline asINT32 scale_to_cube_size(asINT32 scale) { return 1 << (cp_info.num_scales - 1 - scale); }

inline sINT64  scale_to_cube_volume(asINT32 scale) { return 1L << (cp_info.n_dims * (cp_info.num_scales - 1 - scale)); }

inline asINT32 voxel_scale_to_ublk_size(asINT32 scale) { return 1 << (cp_info.num_scales - scale); }

//inline asINT32 scale_to_delta_t(asINT32 scale) { return 1 << (cp_info.num_scales - (scale) - 1); }

inline asINT32 sim_scale_to_sri_scale(asINT32 scale) { return cp_info.num_scales - 1 - scale; }
inline asINT32 sri_scale_to_sim_scale(asINT32 scale) { return sim_scale_to_sri_scale(scale); }

inline asINT32 voxel_index_to_x_finest_offset(auINT32 voxel, auINT32 sri_scale)
{
  return (voxel >> 2) << sri_scale; // sri_scale - finest scale is 0
}
      
inline asINT32 voxel_index_to_y_finest_offset(auINT32 voxel, auINT32 sri_scale)
{
  return ((voxel >> 1) & 1) << sri_scale; // sri_scale - finest scale is 0
}
      
inline asINT32 voxel_index_to_z_finest_offset(auINT32 voxel, auINT32 sri_scale)
{
  return (voxel & 1) << sri_scale; // sri_scale - finest scale is 0
}

#define DO_REALMS(realm_var) \
  for(REALM realm_var = STP_FLOW_REALM; realm_var < STP_N_REALMS; realm_var++)

#define DO_ACTIVE_REALMS(realm_var) \
  for(REALM realm_var = STP_FLOW_REALM; realm_var < STP_N_REALMS; realm_var++) \
    if (cp_info.is_realm_active(realm_var))


/* Initializes the CP_INFO once the structure has been filled in */
VOID cp_info_basic_init();

extern BOOLEAN cp_info_has_been_initialized_p;

// creates the sri lattice translations, used by both checkpoint mme and
// window meas files
void cp_info_set_sri_lattice_translations();

// Convert the units info in the CDI file to the set of SRI lattice
// translations
VOID cp_info_set_lattice_translations();

using PHYSICS_INDEX_TO_ENTITY_ID_MAP = std::vector<std::vector<asINT32>>;

inline PHYSICS_INDEX_TO_ENTITY_ID_MAP build_surface_physics_index_to_face_ids_map(const std::vector<sINT32>& front_surface_phys_desc_index_from_face_index,
                                                                                  const std::vector<sINT32>& back_surface_phys_desc_index_from_face_index) {
  
  asINT32 max_surface_physics_desc_index = -1;
   
  auto max_it_front = std::max_element(front_surface_phys_desc_index_from_face_index.begin(), front_surface_phys_desc_index_from_face_index.end());
  if (max_it_front != front_surface_phys_desc_index_from_face_index.end()) {
    max_surface_physics_desc_index = *max_it_front;
  }

  auto max_it_back = std::max_element(back_surface_phys_desc_index_from_face_index.begin(), back_surface_phys_desc_index_from_face_index.end());
  if (max_it_back != back_surface_phys_desc_index_from_face_index.end() && *max_it_back > max_surface_physics_desc_index) {
    max_surface_physics_desc_index = *max_it_back;
  }

  asINT32 n_surface_physics_descs = max_surface_physics_desc_index+1; // indexed from 0
  PHYSICS_INDEX_TO_ENTITY_ID_MAP surface_physics_index_to_face_ids(n_surface_physics_descs);
  ccDOTIMES(i, cp_info.n_sri_faces) {
    sSRI_FACE& face = cp_info.sri_faces[i];
    //Front physics descriptor
    asINT32 si_front = -1;
    if (face.is_front) {
      si_front = front_surface_phys_desc_index_from_face_index[i];
      if (si_front != -1) {
        surface_physics_index_to_face_ids.at(si_front).push_back(face.cdi_id);
      }
    }
    //Back physics descriptor
    if (!cp_info.face_index_is_front_only[i]) {
      asINT32 si_back = back_surface_phys_desc_index_from_face_index[i];
      if (si_back != -1 && si_back != si_front) {
        surface_physics_index_to_face_ids.at(si_back).push_back(face.cdi_id);
      }
    }
  }

  for (auto& face_ids : surface_physics_index_to_face_ids) {
    std::sort(face_ids.begin(), face_ids.end());
  }

  return surface_physics_index_to_face_ids;
}

inline PHYSICS_INDEX_TO_ENTITY_ID_MAP build_fluid_physics_index_to_region_ids_map() {
  PHYSICS_INDEX_TO_ENTITY_ID_MAP fluid_physics_index_to_region_ids(cp_info.n_sri_parts);
  ccDOTIMES(i, cp_info.n_sri_parts) {
    if (cp_info.fluid_physics_desc_index_from_part_index[i] != -1) {
      asINT32 fi = cp_info.fluid_physics_desc_index_from_part_index[i];
      asINT32 pi = cp_info.sri_parts[i].cdi_id;
      fluid_physics_index_to_region_ids[fi].push_back(pi);
    }
  }

  for (auto& region_ids : fluid_physics_index_to_region_ids) {
    std::sort(region_ids.begin(), region_ids.end());
  }

  return fluid_physics_index_to_region_ids;
}

inline BOOLEAN is_entity_type_a_segment(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE type) {
  return type == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment;
}

inline BOOLEAN is_entity_type_a_part(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE type) {
  return type == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part;
}

#endif /* __CP_INFO_H */

