/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#include "common.h"
#include "trajectory_results.h"
#include "cp_stream_manager.h"
#include "particle_sim_cp.h"

//Guarantee that the IO thread can't access the receive buffers while the MPI thread may be trying 
//to fill them and vice versa by enabling the following define.
#define USE_TRAJECTORY_MANAGER_MUTEX 

sCP_TRAJECTORY_VERTEX_MANAGER cp_trajectory_vertex_manager;
sCP_TRAJECTORY_HITPOINT_MANAGER cp_trajectory_hitpoint_manager;
sCP_TRAJECTORY_STARTPOINT_MANAGER cp_trajectory_startpoint_manager;

#if 0
asINT32 debug_buffer_size = 0;
asINT32 debug_buffer_capacity = 0;
asINT32 debug_buffer_timestep = 0;
sTRAJECTORY_STARTPOINT* debug_buffer = NULL;
#endif


asINT32 g_total_pri_emitters;
volatile asINT32 g_num_unprocessed_startpoints = 0;
pthread_mutex_t  g_trajectory_manager_exclude;

std::vector<sINT32> g_window_to_trajectory_window;
std::vector<sINT32> g_n_parcels_first_measured_on_sp;
std::vector<CP_TRAJECTORY_WINDOW> g_trajectory_windows;
sINT32 g_ready_to_write_pri_count = 0;

std::vector<sINT32> g_window_emitter_children_cumulative_counts;
std::vector<sINT32> g_window_emitter_parent_cumulative_counts;
//std::vector<sINT32> g_window_emitter_states_cumulative_counts;

sCP_TRAJECTORY_ID_MAP g_trajectory_id_map;

// This sends the base global trajectory ids to all the SPs.
BOOLEAN sCP_TRAJECTORY_ID_MAP::maybe_send_base_global_ids_to_sps(BOOLEAN force)
{

  if (!force) {
    if (m_remap_in_progress)
      return FALSE;

    if (cp_trajectory_startpoint_manager.next_timestep_to_output() - m_current_start_timestep < TRAJECTORY_ID_MAP_SEND_PERIOD)
      return FALSE;
  }

  memcpy(m_send_buffer.data(), m_base_global_ids.data(), sizeof(TRAJECTORY_ID) * m_send_buffer.size());

  ccDOTIMES(sp, total_sps) {
    MPI_Request send_request;
    MPI_Isend(m_send_buffer.data(), m_send_buffer.size(), eMPI_asINT32, sp, eMPI_TRAJECTORY_GLOBAL_ID_TAG, eMPI_sp_cp_comm, &send_request);

    // We can free the request immediately because we know by construction when the SPs are guaranteed
    // to have received the message, and we won't send again until that point in time.
    if (!sim_args.no_mpi_request_free)
      MPI_Request_free(&send_request);
  }
  m_remap_in_progress = TRUE;
  return TRUE;
}

// This discards the low TRAJECTORY_ID_MAP_SEND_PERIOD timesteps worth of m_base_global_ids once
// we know the SPs have converted all parcel trajectory IDs in this time range.
BOOLEAN sCP_TRAJECTORY_ID_MAP::maybe_discard_sent_base_global_ids()
{
  if (!m_remap_in_progress)
    return FALSE;

  TIMESTEP delta_t = TRAJECTORY_ID_MAP_SEND_PERIOD + TRAJECTORY_ID_MAP_RECV_DELAY;

  // Most efficient to check in reverse order that the SPs send these 3 types of messages
  if (cp_trajectory_hitpoint_manager.next_timestep_to_output() - m_current_start_timestep < delta_t)
    return FALSE;
  if (cp_trajectory_vertex_manager.next_timestep_to_output() - m_current_start_timestep < delta_t)
    return FALSE;
  if (cp_trajectory_startpoint_manager.next_timestep_to_output() - m_current_start_timestep < delta_t)
    return FALSE;

  // Discard the portion that was previously sent to the SPs. Shift the portion beyond that down
  // to the beginning of m_base_global_ids.
  memcpy(m_base_global_ids.data(),
         m_base_global_ids.data() + m_send_buffer.size(),
         sizeof(TRAJECTORY_ID) * (m_base_global_ids.size() - m_send_buffer.size()));

  m_current_start_timestep += TRAJECTORY_ID_MAP_SEND_PERIOD;

  // Reset upper portion of m_base_global_ids to -1 to enable error detection in places
  // where IDs are converted from local to global
  memset(m_base_global_ids.data() + m_base_global_ids.size() - m_send_buffer.size(),
         -1,
         sizeof(TRAJECTORY_ID) * m_send_buffer.size());

  m_remap_in_progress = FALSE;
  return TRUE;
}


size_t sCP_TRAJECTORY_ID_MAP::ckpt_len()
{
  return  sizeof(size_t) + sizeof(TRAJECTORY_ID) *( m_base_global_ids.size() + m_next_available_global_index.size());
}

VOID sCP_TRAJECTORY_ID_MAP::write_ckpt(sCKPT_BUFFER& buff)
{
  size_t len = sCP_TRAJECTORY_ID_MAP::ckpt_len();
  buff.allocate(len);
  buff.write(&len);
  buff.write(m_base_global_ids.data(), sizeof(TRAJECTORY_ID) * m_base_global_ids.size());
  buff.write(m_next_available_global_index.data(), sizeof(TRAJECTORY_ID) * m_next_available_global_index.size());
}

VOID sCP_TRAJECTORY_ID_MAP::read_ckpt(LGI_STREAM ckpt_istream, asINT32 total_ckpt_sps)
{
  //only insist this record exist if there are trajectory windows expected.
  if(!has_trajectory_window() || sim_args.disable_particle_modeling)
    return;

  LGI_TAG tag;
  lgi_read_next_head(ckpt_istream, tag);

  if (tag.id != LGI_TRAJECTORY_ID_MAP_TAG) {
    msg_internal_error("Expected LGI record %s (tag %d) but found %s (tag %d)", lgi_tag_namestring(LGI_TRAJECTORY_ID_MAP_TAG),
                       LGI_TRAJECTORY_ID_MAP_TAG,
                       lgi_tag_namestring(tag.id), tag.id);
  }


  if (total_ckpt_sps != total_sps) {
    write_header_to_all_sps(tag);
    write_to_all_sps(total_ckpt_sps);

    asINT32 ckpt_base_global_ids_size = ((TRAJECTORY_ID_MAP_SEND_PERIOD + TRAJECTORY_ID_MAP_RECV_DELAY)
                                         * total_ckpt_sps * g_trajectory_windows.size() * g_total_pri_emitters);
    TRAJECTORY_ID *ckpt_base_global_ids = xnew TRAJECTORY_ID[ ckpt_base_global_ids_size ];
    lgi_read(ckpt_istream, ckpt_base_global_ids, sizeof(TRAJECTORY_ID) * ckpt_base_global_ids_size);
    write_to_all_sps(ckpt_base_global_ids, sizeof(TRAJECTORY_ID) * ckpt_base_global_ids_size);
    delete[] ckpt_base_global_ids;
  } else {
    // restarting on same number of SPs
    lgi_read(ckpt_istream, m_base_global_ids.data(), sizeof(TRAJECTORY_ID) * m_base_global_ids.size());

    if (m_remap_in_progress)
      // send the map to the SPs (replicating what was sent prior to the ckpt but not yet consumed by the SPs)
      maybe_send_base_global_ids_to_sps(true);
  }

  lgi_read(ckpt_istream, m_next_available_global_index.data(), sizeof(TRAJECTORY_ID) * m_next_available_global_index.size());
}

VOID sCP_TRAJECTORY_ID_MAP::read_ckpt(sCKPT_BUFFER& ckpt_buff, asINT32 total_ckpt_sps)
{
  //only insist this record exist if there are trajectory windows expected.
  if(!has_trajectory_window() || sim_args.disable_particle_modeling)
    return;

  LGI_TAG tag;
  tag.id = LGI_TRAJECTORY_ID_MAP_TAG;
  tag.length = ckpt_buff.m_capacity; // length read from hdf5 file and used to allocate exact buffer mem size.

  if (total_ckpt_sps != total_sps) {
    write_header_to_all_sps(tag);
    write_to_all_sps(total_ckpt_sps);

    asINT32 ckpt_base_global_ids_size = ((TRAJECTORY_ID_MAP_SEND_PERIOD + TRAJECTORY_ID_MAP_RECV_DELAY)
                                         * total_ckpt_sps * g_trajectory_windows.size() * g_total_pri_emitters);
    TRAJECTORY_ID *ckpt_base_global_ids = new TRAJECTORY_ID[ ckpt_base_global_ids_size ];
    ckpt_buff.read(ckpt_base_global_ids, sizeof(TRAJECTORY_ID) * ckpt_base_global_ids_size);
    write_to_all_sps(ckpt_base_global_ids, sizeof(TRAJECTORY_ID) * ckpt_base_global_ids_size);
    delete[] ckpt_base_global_ids;
  } else {
    // restarting on same number of SPs
    ckpt_buff.read(m_base_global_ids.data(), sizeof(TRAJECTORY_ID) * m_base_global_ids.size());

    if (m_remap_in_progress)
      // send the map to the SPs (replicating what was sent prior to the ckpt but not yet consumed by the SPs)
      maybe_send_base_global_ids_to_sps(true);
  }

  ckpt_buff.read( m_next_available_global_index.data(), sizeof(TRAJECTORY_ID) * m_next_available_global_index.size());
}

VOID initialize_trajectory_meas_data_comm()
{
  g_window_to_trajectory_window.resize(cp_info.n_meas_windows(), -1);
  asINT32 trajectory_window_index = 0;
  ccDOTIMES(i, cp_info.n_meas_windows()) {
    if (cp_info.meas_windows[i] && cp_info.meas_windows[i]->m_is_particle_trajectory_window) {
      g_window_to_trajectory_window[i] = trajectory_window_index++;
      g_trajectory_windows.push_back((CP_TRAJECTORY_WINDOW)cp_info.meas_windows[i]);
    }
  }

  // The +1 in g_total_pri_emitters is for PRI's special "merge" emitter. Particle data from SPs
  // may be tagged with this emitter.
  g_total_pri_emitters = cp_particle_sim.cdi_particle_emitters()->GetAllEmitters().size() + 1;

  g_n_parcels_first_measured_on_sp.resize(g_trajectory_windows.size() * g_total_pri_emitters * total_sps, 0);
#ifdef USE_TRAJECTORY_MANAGER_MUTEX
  pthread_mutex_init(&g_trajectory_manager_exclude, NULL);
#endif
  g_trajectory_id_map.initialize();
  cp_trajectory_startpoint_manager.initialize();
  cp_trajectory_vertex_manager.initialize();
  cp_trajectory_hitpoint_manager.initialize();
}

VOID synchronize_trajectory_window_data(TIMESTEP end_timestep)
{
  //Finish retrieving all startpoints before completing the other data types.
  do {
    cp_trajectory_startpoint_manager.maybe_receive_sp_data();
  } while(cp_trajectory_startpoint_manager.next_timestep_to_output() < end_timestep);

  do {
    cp_trajectory_vertex_manager.maybe_receive_sp_data();
  } while(cp_trajectory_vertex_manager.next_timestep_to_output() < end_timestep);

  do {
    cp_trajectory_hitpoint_manager.maybe_receive_sp_data();
  } while(cp_trajectory_hitpoint_manager.next_timestep_to_output() < end_timestep);
}

VOID maybe_receive_sp_trajectory_data()
{
  // Previously, the following was true:
  // These must all be called together. The order here is optimal since startpoints must
  // be received before vertices and hitpoints, and every time receives are completed
  // from all SPs, we must make the last 2 calls. Note that there is a failsafe mechanism
  // to ensure that startpoints must be received before vertices and hitpoints (see
  // m_ok_to_proceed_count).

  // But now that the IO thread is responsible for emptying the receive buffers and it is not synchronous with this 
  // function that runs in the MPI thread which fills the buffers, we can allow hitpoints or vertices to be written 
  // first if the buffer is from a timestep before the startpoint buffer. We can also blindly try to recieve more 
  // vertices or hitpoints at any point as long as we first received the startpoints which must come first in order to
  // populate the local to global particle ID map.
  
#ifdef USE_TRAJECTORY_MANAGER_MUTEX
  if(pthread_mutex_trylock(&g_trajectory_manager_exclude)) 
    return;
#endif

  //Always accept new startpoints.
  cp_trajectory_startpoint_manager.maybe_receive_sp_data();  
  //Only try to receive more vertices if they haven't caught up to the startpoints.
  if (cp_trajectory_vertex_manager.max_buffered_timestep() < cp_trajectory_startpoint_manager.completed_timestep() ) {
    cp_trajectory_vertex_manager.maybe_receive_sp_data();
  }
  //Only try to receive more hitpoints if they haven't caught up to the startpoints.
  if(cp_trajectory_hitpoint_manager.max_buffered_timestep() < cp_trajectory_startpoint_manager.completed_timestep() ) {
    cp_trajectory_hitpoint_manager.maybe_receive_sp_data();
  }

#ifdef USE_TRAJECTORY_MANAGER_MUTEX
  pthread_mutex_unlock(&g_trajectory_manager_exclude); 
#endif
  
  g_trajectory_id_map.maybe_send_base_global_ids_to_sps(); 
  g_trajectory_id_map.maybe_discard_sent_base_global_ids();

}

bool manager_order (CP_TRAJECTORY_MANAGER_ORDER const &a, CP_TRAJECTORY_MANAGER_ORDER const &b) {
  return (*a) < (*b);
}

VOID write_pri_result_buffers()
{

  // Now that the IO thread that emptys the receive buffers is not synchronous with the MPI thread filling the buffers,
  // we allow hitpoints or vertices to be written first if the buffer is from a timestep before the startpoint buffer. 
  // Previously, the hitpoint and vertex manager could only receive and write their data if the startpoints has already
  // been processed. The exact logic determining the order is encoded in the < operator of 
  // sCP_TRAJECTORY_MANAGER_ORDER.

#ifdef USE_TRAJECTORY_MANAGER_MUTEX
  if(pthread_mutex_trylock(&g_trajectory_manager_exclude)) 
    return;
#endif

  std::vector<CP_TRAJECTORY_MANAGER_ORDER> manager_list;

  if(cp_trajectory_startpoint_manager.buffer_ready())
    manager_list.push_back(&cp_trajectory_startpoint_manager);

  if(cp_trajectory_vertex_manager.buffer_ready()) {
    if(cp_trajectory_vertex_manager.max_buffered_timestep() <= cp_trajectory_startpoint_manager.completed_timestep() ) {
      manager_list.push_back(&cp_trajectory_vertex_manager);
    }
  }

  if(cp_trajectory_hitpoint_manager.buffer_ready()){
    if(cp_trajectory_hitpoint_manager.max_buffered_timestep() <= cp_trajectory_startpoint_manager.completed_timestep() )
      manager_list.push_back(&cp_trajectory_hitpoint_manager);
  }

  // Sort so that the earliest, highest priority data is processed firsr.  If startpoints for a given timestep aren't
  // processed first, the local to global ID remapping will fail.
  std::sort(manager_list.begin(), manager_list.end(), manager_order); 

  //Process the managers in the sorted order.
  ccDOTIMES(manager_index, manager_list.size()) {
    manager_list[manager_index]->write_receive_buffers_to_pri();
  }

#ifdef USE_TRAJECTORY_MANAGER_MUTEX
  pthread_mutex_unlock(&g_trajectory_manager_exclude);
#endif

  WALLCLOCK_TIME_SECS time_secs = wallclock_time_secs();
  ccDOTIMES(window_index, g_trajectory_windows.size()) {
    g_trajectory_windows[window_index]->actual_sync(time_secs);
  }
}

BOOLEAN trajectory_data_is_waiting_for_results_thread() {
  BOOLEAN any_manager_has_data_to_process =
    cp_trajectory_startpoint_manager.buffer_ready() ||
    cp_trajectory_vertex_manager.buffer_ready() ||
    cp_trajectory_hitpoint_manager.buffer_ready();
  return any_manager_has_data_to_process;
}

VOID wait_for_IO_thread_to_process_trajectory_window_data() {
  while(trajectory_data_is_waiting_for_results_thread())
    cp_sleep();
}

VOID receive_all_remaining_trajectory_window_measurements()
{
  TIMESTEP n_user_base_steps = cp_info.n_user_base_steps;
  TIMESTEP exit_timestep = n_user_base_steps * cp_info.end_time;
  synchronize_trajectory_window_data(exit_timestep);
}
