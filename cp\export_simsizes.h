#ifndef EXPORT_CP_SIZES_H
#define EXPORT_CP_SIZES_H
#include LGI_H

/*=================================================================================================
 * @class CP_SIZES_INTERFACE
 * This is a trivial interface to expose size of measurement windows
 * The logic behind writing this wrapper rather than including "window.h" and other CP headers
 * directly in client components such as DISC & DECOMP, is to avoid including dependent headers.
 * For example, directly including "window.h" would also require a plethora of headers such as
 * FOREST_H, CP_H (10+ files) etc. In some cases certain irrelevant globals would also be exposed
 * through headers.
 *
 * The functionality of this class is exposed via the SIMSIZES component
 *================================================================================================*/
//Forward decl
namespace SIM_SIZES {
  class SIM_OPTIONS;
}

struct CP_SIZES_INTERFACE {

  CP_SIZES_INTERFACE(const SIM_SIZES::SIM_OPTIONS& opts);
  
  size_t size_of_cdi_meas_window() const;
  size_t size_of_cp_meas_cell_reflist_elt() const;
  size_t size_of_cp_meas_cell_ref() const;
  size_t size_of_cp_composite_fluid_meas_window() const;
  size_t size_of_cp_fluid_meas_window() const;
  size_t size_of_cp_composite_surface_meas_window() const;
  size_t size_of_cp_surface_meas_window() const;
  size_t compute_cp_meas_storage() const;
  size_t compute_cp_misc_storage() const;

  VOID addMeasCellRefs(asINT64 count) { m_meas_cell_ref += count; }  
  VOID addCpCdiMeasWindows(asINT64 count) { m_cp_cdi_meas_window += count; }      
  VOID addCpMeasCellVars(asINT32 nVars, asINT64 count) { m_cpMeasCellVars += nVars * count; }
  VOID addNonCompositeWindowCells(LGI_MEAS_WINDOW_TYPE type, asINT64 count) { m_nonCompositeWindowCells[type] += count; }    
  VOID addCompositeWindowCells(LGI_MEAS_WINDOW_TYPE type, asINT64 count) { m_compositeWindowCells[type] += count; }
  VOID addNonCompositeWindows(LGI_MEAS_WINDOW_TYPE type, asINT64 count) { m_nonCompositeWindows[type] += count; }
  VOID addCompositeWindows(LGI_MEAS_WINDOW_TYPE type, asINT64 count) { m_compositeWindows[type] += count; }
  VOID addNonCompositeWindowVertexRefs(LGI_MEAS_WINDOW_TYPE type, asINT64 count) { m_nonCompositeWindowVertexRefs[type] += count; }
  VOID addSurfelVertices(asINT64 count) { m_surfelVertices += count; }    
  VOID addBsurfelVertices(asINT64 count) { m_bsurfelVertices += count; }
  VOID addUblks(asINT64 count) { m_ublks += count; }
  VOID addSurfels(asINT64 count) { m_surfels += count; }
  VOID addBsurfels(asINT64 count) { m_bsurfels += count; }  

  asINT64 m_nLrfs;
  asINT64 m_nDims;
  asINT64 m_ublks;
  asINT64 m_surfels;
  asINT64 m_bsurfels;
  asINT64 m_meas_cell_ref;
  asINT64 m_cp_cdi_meas_window;
  asINT64 m_cpMeasCellVars;
  asINT64 m_nonCompositeWindowCells[LGI_N_MEAS_WINDOW_TYPES];
  asINT64 m_compositeWindowCells[LGI_N_MEAS_WINDOW_TYPES];
  asINT64 m_nonCompositeWindows[LGI_N_MEAS_WINDOW_TYPES];
  asINT64 m_compositeWindows[LGI_N_MEAS_WINDOW_TYPES];
  asINT64 m_nonCompositeWindowVertexRefs[LGI_N_MEAS_WINDOW_TYPES];
  asINT64 m_surfelVertices;
  asINT64 m_bsurfelVertices;

  static asINT64 size_of_sriDOUBLE;
};

#endif


