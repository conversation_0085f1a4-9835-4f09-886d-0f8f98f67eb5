/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#include "trajectory_window.h"
#include "cp_info.h"
#include "cp_cdi_reader.h"
#include "cp_stream_manager.h"
#include "parse_args.h"
#include CDI_H

//PRI expects emitter id's do be assigned for each class of emitter independently while the 
//simulator assigns the same space of IDs to all emitter types. Therefore the following vector is 
//used to convert a simulator emitter id to a PRI emitter id.
std::vector<asINT32> g_cdi_to_pri_emitter_ids; 

//Also, a map from the order in which emitters are sent to the SPs (which defines the simulator's emitter IDs) to
//the order in which CDI provides the emitters in GetAllEmitters() vector is needed.  This is required because CDI 
//provides methods to return emitters of a specific type and these are what is used for sending the emitters to the SPs 
//in this function. There is no guarentee about the order in which different emitter types appear in CDI's vector 
//of all emitters (although it seems to be surface emitters, volume emitters, rain emitters, and lastly tire 
//emitters but this may change).
std::vector<asINT32> g_simulator_to_cdi_emitter_ids;

// This combines the above 2 vectors
std::vector<asINT32> g_simulator_to_pri_emitter_ids;

std::vector<PRI::EMITTER_TYPE> g_simulator_emitter_id_to_pri_emitter_types; 

extern sCDI_DATA cdi_data;
extern sCP_PARTICLE_SIM cp_particle_sim;
extern sCP_INFO cp_info;
sCDI_INFO g_cdi_info; //CDI version number was stashed here by cp_cdi_reader when opening the CDI file and is 
                      //needed for a fix to PR41662.

BOOLEAN g_has_trajectory_window = FALSE;

VOID sCP_TRAJECTORY_WINDOW::actual_sync(WALLCLOCK_TIME_SECS time_secs) {
  const asINT32 N_SECS_BEFORE_SYNC = 5 * 60;	// 5 minutes
  //Only take into acount time passed, and not something like number of veritces written since last flush.
  if(time_secs > m_time_of_last_sync + N_SECS_BEFORE_SYNC) {
    m_pmr.FlushDataToDisk();
    m_time_of_last_sync = time_secs;
  }
}
void sCP_TRAJECTORY_WINDOW::finish_init() {

  g_has_trajectory_window = TRUE;
  m_time_of_last_sync = wallclock_time_secs();
  if(sim_args.disable_particle_modeling) //PR39448
    return;
}

void sCP_TRAJECTORY_WINDOW::open_pmr_file(){
  
  if(m_pmr.IsOpen())
    return;
  
  BOOLEAN sim_has_protected_geometry = cp_info.encryption_struct.get_sri_encryption_struct().is_encrypted;
  
  //sim_has_protected_geometry = TRUE;  //Hacked for testing until PR43155 is fixed.

  //If resuming from a full checkpoint, open the file for append and don't repopulate the parameters. 
  //Otherwise, open the file and populate all the particle modeling parameters. 
  BOOLEAN file_exists = platform_file_present(output_filename);
  if(cp_info.is_full_checkpoint_restore && file_exists) {

    //If there are any protected parts in the simulation, let PRI know to use encryption (all hitpoints will be 
    //encrypted, not just ones hitting protected parts).
    m_pmr.SetEncryptionEnabled(sim_has_protected_geometry);

    PRI::ERROR_CODE status = m_pmr.OpenForAppend(output_filename); //output_filename is a member of the base class
    if(status != PRI::ERROR_CODE_SUCCESS) {
      msg_error("Failed to open trajectory file %s for appending (error code %s).\n",
                output_filename,
                m_pmr.GetErrorCodeString(status));
    }

    //Check that the checkpoint file has a checkpoint table entry to resume to.
    std::size_t num_checkpoint_table_entries = m_pmr.GetNumberOfCheckPoints();
    if(m_checkpoint_table_index >= num_checkpoint_table_entries) {
      std::string checkpoint_number = convert_to_ordinal(m_checkpoint_table_index + 1);
      msg_error("Could not resume with trajectory file %s.  The simulation is "
                "resuming from the %s checkpoint file previously written by the "
                "simulation, but the trajectory file only has a record of %zu "
                "checkpoint(s) being written.\n",
                output_filename,
                checkpoint_number.c_str(),
                num_checkpoint_table_entries);
    }

    //Attempt to rewind the pmr file to the relevant checkpoint index.
    if(!m_pmr.LoadCheckPoint((std::size_t)m_checkpoint_table_index)) {
      msg_error("Failed to restore PMR %s from checkpoint.\n",
                output_filename);
    
    } 
    //At this point, the PMR file has been opened and rewound without error.

    //Fix for PR41929 (that doesnt require a new checkpoint version number):
    //Get the offsets needed to compute first_parent_index_offset and last_parent_index_offset
    //needed when setting the parent indices for particles added to a cPARTICLE_TABLE from the
    //existing PMR file.  These offset accounts for the number of records already written to 
    //the file from the checkpointed run. One offset is needed per emitter.
    asINT32 num_emitters = g_cdi_to_pri_emitter_ids.size();
    m_parent_indices_offset.resize(num_emitters);
    m_child_indices_offset.resize(num_emitters);
    ccDOTIMES(emitter_index, num_emitters) {
      PRI::EMITTER_TYPE emitter_type = g_simulator_emitter_id_to_pri_emitter_types[emitter_index];
      asINT32 pri_emitter_id = g_simulator_to_pri_emitter_ids[emitter_index];
      m_parent_indices_offset[emitter_index] = m_pmr.GetNumberOfParentRecordsPerEmitter(emitter_type, pri_emitter_id);
      m_child_indices_offset[emitter_index]  = m_pmr.GetNumberOfChildRecordsPerEmitter(emitter_type, pri_emitter_id);
    }

    //Re-create the map between face id's and screen id's that is needed to correctly store hitpoint surface indices for 
    //hitpoints on screens. Normally this map is created by write_parameters() but that is not called if restoring from a checkpoint.
    std::vector<asINT32> *face_index_to_screen_index_map = cp_particle_sim.face_index_to_screen_index_map();
    face_index_to_screen_index_map->resize(cp_info.n_sri_faces, -1);
    ccDOTIMES(cdi_screen_index, cp_particle_sim.cdi_particle_screens()->size()) {
      sCDI_SCRN &cdi_screen = cp_particle_sim.cdi_particle_screens()->at(cdi_screen_index);
      //For each face index provided for this screen, set the map element for that index to the screen index for this
      //screen.
      auto face_list = cdi_screen.geom_selection.ExpandSelection(cp_info.partitions());
      ccDOTIMES(face_index, face_list.size()) {
        face_index_to_screen_index_map->at(face_list[face_index]) = cdi_screen_index;
      }
    }
  } else {

    //If there are any protected parts in the simulation, let PRI know to use encryption (all hitpoints will be 
    //encrypted, not just ones hitting protected parts).
    m_pmr.SetEncryptionEnabled(sim_has_protected_geometry);

    PRI::ERROR_CODE status = m_pmr.OpenForWrite(output_filename);
    if(status != PRI::ERROR_CODE_SUCCESS) {
      msg_error("Failed to open trajectory file %s for writing (error code %s).\n",
                output_filename,
                m_pmr.GetErrorCodeString(status));
    }
    write_parameters();

    //PR41929: Reset the offsets to zero if not resuming.
    asINT32 num_emitters = g_cdi_to_pri_emitter_ids.size();
    m_parent_indices_offset.resize(num_emitters, 0);
    m_child_indices_offset.resize(num_emitters, 0);
  }
  m_time_of_last_sync = wallclock_time_secs();
}


void sCP_TRAJECTORY_WINDOW::write_parameters() {
  //This method populates the PMR file with the entire set of particle modeling parameters read from the CDI file.
  
  if(sim_args.disable_particle_modeling) //PR39448
    return;

  PRI::cPMP_FILE::STATUS file_status = m_pmr.GetStatus();  
  if(file_status != PRI::cPMP_FILE::OPENED_FOR_WRITE)
    return; //Don't write the parameters if file was opening for appending (It would already have parameters written).

  if(m_pmr.HasParameters())
    return; //Don't write the parameters if the file already contains some. Ideally, the above check makes this 
            //unneccescary.

  PRI::cPARAMETERS pmr_parameters;

  //Add the particle globals into the PRI parameter structure.
  cp_particle_sim.cdi_particle_global_parameters()->ConvertToPRI(pmr_parameters);

  //Add the particle materials to the PRI parameters.
  std::vector<PRI::cPARTICLE_MATERIAL>& pri_materials = pmr_parameters.GetParticleMaterialList();
  const std::vector<sCDI_PRMT>& cdi_materials = *cp_particle_sim.cdi_particle_materials();
  pri_materials.resize(cdi_materials.size());
  ccDOTIMES(material_index, cdi_materials.size()) {
    cdi_materials[material_index].ConvertToPRI(pri_materials[material_index]); //Write each cdi material struct into a 
                                                                               //PRI material class.
  }

  // Populate the PRI cENTITY_GEOMETRY table. This table must include any region used for an emitter geometry and 
  // screens and also any region a hitpoint might occur on. Every sri face is therefore added.
  // This entity list must be populated before ConvertToPRI methods for emitters or screens are used.
  // Also, use this oppurtunity to populate a list of face names which are needed by the ConvertToPRI method for 
  // particle screens.
  std::vector<PRI::cENTITY_GEOMETRY>& pri_entity_geometry_list = pmr_parameters.GetEntityGeometryList();
  std::vector<std::string> sri_face_names(cp_info.n_sri_faces);
  ccDOTIMES(sri_face_index,  cp_info.n_sri_faces) {
    PRI::cENTITY_GEOMETRY pri_entity_geometry;
    pri_entity_geometry.SetName(cp_info.sri_faces[sri_face_index].name);
    pri_entity_geometry.SetType(PRI::ENTITY_TYPE_FACE);
    pri_entity_geometry_list.push_back(pri_entity_geometry);
    sri_face_names[sri_face_index] = cp_info.sri_faces[sri_face_index].name; //Passed to ConvertToPRI for particle screens.
  }

  //Populate the PRI emitter configurations.
  std::vector<std::size_t> cdi_to_pri_emitter_config_map;
  cp_particle_sim.cdi_emitter_configurations()->ConvertToPRI(g_cdi_info, pmr_parameters,
                                                             cdi_to_pri_emitter_config_map);

  std::vector<asINT32> emitter_start_time;
  std::vector<asINT32> emitter_end_time;

  // The last emitter should be ignored
  ccDOTIMES(cdi_emitter_id, g_cdi_to_pri_emitter_ids.size() - 1) {
    asINT32 start = TIMESTEP_MAX;
    asINT32 end = TIMESTEP_MAX;
    ccDOTIMES(emitter_id, cp_particle_sim.emitters.size()) {
      // Find the cdi emitters which are started via monitors 
      EMITTER cp_emitter = cp_particle_sim.emitters[emitter_id];
      if (cdi_emitter_id == g_simulator_to_cdi_emitter_ids[cp_emitter->id()])
      {
        start = cp_emitter->get_start_time();
        // Show start time 0 for emitters which are not started yet
        if (start == TIMESTEP_MAX)
          start = 0;
        end = cp_emitter->get_end_time();
      }
    }
#if DEBUG_START_EMITTERS
    msg_print("emitter start %d end %d", start, end);
#endif
    emitter_start_time.push_back(start);
    emitter_end_time.push_back(end);
  }

  //Add the particle emitters defined in the CDI dataset to the PRI dataset
  //This also adds the geometry references for each emitter.
  //g_cdi_region_names and sri_face_names are passed to resolve the names of sCDI_BOX_ geometries for volume and rain emitters provided 
  //by CDI.
  cp_particle_sim.cdi_particle_emitters()->ConvertToPRI(pmr_parameters,
                                                        g_cdi_region_names,
                                                        sri_face_names,  //fix for PR40740
                                                        cdi_to_pri_emitter_config_map,
                                                        emitter_start_time,
                                                        emitter_end_time);

  //Populate the surface interaction parameters.
  std::vector<sCDI_SRMI>* cdi_surface_interactions = cp_particle_sim.cdi_surface_interaction_parameters();
  std::vector<std::string>* cdi_surface_interaction_names = cp_particle_sim.cdi_surface_interaction_names();
  asINT32 num_surface_interactions = cdi_surface_interactions->size();

  std::vector<PRI::cSURFACE_MATERIAL_INTERACTIONS>& pri_surface_material_interactions = 
    pmr_parameters.GetSurfaceMaterialInteractionList();

  std::vector<PRI::cPARTICLE_SURFACE_INTERACTION>& pri_particle_surface_interactions = 
    pmr_parameters.GetParticleSurfaceInteractionList();

  pri_surface_material_interactions.resize(num_surface_interactions);
  pri_particle_surface_interactions.resize(num_surface_interactions);
  ccDOTIMES(i, num_surface_interactions) {
    cdi_surface_interactions->at(i).ConvertToPRI(i,
                                                 cdi_surface_interaction_names->at(i),
                                                 pri_surface_material_interactions[i],
                                                 pri_particle_surface_interactions[i]);
  }

  //Populate the pmr parameters with each particle screens provided in the CDI file.
  //Also create a map between face id's and screen id's that is needed to correctly store hitpoint surface indices for 
  //hitpoints on screens.
  std::vector<asINT32> *face_index_to_screen_index_map = cp_particle_sim.face_index_to_screen_index_map();
  face_index_to_screen_index_map->resize(cp_info.n_sri_faces, -1);
  ccDOTIMES(cdi_screen_index, cp_particle_sim.cdi_particle_screens()->size()) {
    sCDI_SCRN &cdi_screen = cp_particle_sim.cdi_particle_screens()->at(cdi_screen_index);
    cdi_screen.ConvertToPRI(pmr_parameters, sri_face_names, cp_info.partitions());

    //For each face index provided for this screen, set the map element for that index to the screen index for this
    //screen.
    auto face_list = cdi_screen.geom_selection.ExpandSelection(cp_info.partitions());
    ccDOTIMES(face_index, face_list.size()) {
      face_index_to_screen_index_map->at(face_list[face_index]) = cdi_screen_index;
    }
  }

  //Add at least a lattice_csys to the pri file.
  std::vector<PRI::cCOORDINATE_SYSTEM>& pri_coordinate_systems = pmr_parameters.GetCoordinateSystemList();
  {
    PRI::cCOORDINATE_SYSTEM pri_csys;
    pri_csys.SetName("lattice_csys");
    pri_csys.SetParentCoordinateSystem(""); //The lattice csys has no parent csys.
    PRI::cCOMPOUND_FLOAT3 translation;
    translation[0].SetValue(-cdi_data.origin[0]); //This is the origin of the lattice csys relative to the default csys.
    translation[1].SetValue(-cdi_data.origin[1]);
    translation[2].SetValue(-cdi_data.origin[2]);
    pri_csys.SetTranslation(translation);
    PRI::VECTOR3F euler_angles(0.0, 0.0, 0.0);
    pri_csys.SetRotation(euler_angles);
    pri_coordinate_systems.push_back(pri_csys); //Push the newly created pri_csys onto the vector of csys's in the 
                                                //pmr_parameters object.
  }

  //Append all the cdi coordinate systems (even ones not referenced by any emitter geometry)
  ccDOTIMES(cdi_csys_index, cp_info.all_csys->num_coord_systems) {
    CDI_CSYS cdi_csys = cp_info.all_csys->coord_systems + cdi_csys_index;
    PRI::cCOORDINATE_SYSTEM pri_csys;

    std::string csys_name(cdi_csys->name);
    pri_csys.SetName(csys_name);
    pri_csys.SetParentCoordinateSystem(cdi_csys_index == 0 ? "" : "default_csys");  //Don't indicate that the default 
                                                                                    //csys is a parent of itself.

    //Set the translation
    PRI::cCOMPOUND_FLOAT3 translation;
    translation[0].SetValue(cdi_csys->l_to_g_xform[0][3]);
    translation[1].SetValue(cdi_csys->l_to_g_xform[1][3]);
    translation[2].SetValue(cdi_csys->l_to_g_xform[2][3]);
    pri_csys.SetTranslation(translation);
    dFLOAT alpha = std::atan2(cdi_csys->l_to_g_xform[2][1], cdi_csys->l_to_g_xform[2][2]);
    dFLOAT beta = std::atan2(-cdi_csys->l_to_g_xform[2][0], 
                             std::sqrt(cdi_csys->l_to_g_xform[2][1] * cdi_csys->l_to_g_xform[2][1] + 
                                       cdi_csys->l_to_g_xform[2][2] * cdi_csys->l_to_g_xform[2][2]));
    dFLOAT gamma = std::atan2(cdi_csys->l_to_g_xform[1][0], cdi_csys->l_to_g_xform[0][0]);
    PRI::VECTOR3F euler_angles(alpha, beta, gamma);

    pri_csys.SetRotation(euler_angles);
    //Push the newly created pri_csys onto the vector of csys's in the pmr_parameters object.
    pri_coordinate_systems.push_back(pri_csys);
  }

  pmr_parameters.GetGlobalParametersList().at(0).SetMaxTimeStepBetweenVertices(TRAJECTORY_MAX_TIMESTEPS_BETWEEN_VERTICES);

  m_pmr.WriteParameters(pmr_parameters);
  PRI::cCHANGEABLE_UNIT_SET units(cp_info.units_db);
  m_pmr.WriteChangeableUnits(units);
  m_pmr.SetAuditTrail(audit_universal_rep(cp_info.audit_trail));
  m_pmr.FlushDataToDisk();
}

PRI::PARTICLE_SOURCE convert_particle_source(asINT32 event_type)
{

  PRI::PARTICLE_SOURCE pri_source_value;
  switch(event_type)
    {
    case EVENT_FILM:
      pri_source_value = PRI::PARTICLE_SOURCE_FILM;
      break;
    case EVENT_EMITTED:
      pri_source_value = PRI::PARTICLE_SOURCE_EMITTER;
      break;
   case EVENT_ENTERED_WINDOW:
     pri_source_value = PRI::PARTICLE_SOURCE_ENTERED_WINDOW;
      break;
    case EVENT_SPLASH:
      pri_source_value = PRI::PARTICLE_SOURCE_SPLASH;
      break;
    case EVENT_SCREEN_SPLASH:
      pri_source_value = PRI::PARTICLE_SOURCE_SPLASH;
      break;
    case EVENT_MERGE:
      pri_source_value = PRI::PARTICLE_SOURCE_FILM;  //The simulator's merge routine only operates on film.
      break;
    case EVENT_SPLIT:
      pri_source_value = PRI::PARTICLE_SOURCE_FILM;  //The simulator's unmerge routine only operates on film.
      break;
    case EVENT_BREAKUP:
      pri_source_value = PRI::PARTICLE_SOURCE_BREAKUP;
      break;
    case EVENT_REENTRAINMENT:
      pri_source_value = PRI::PARTICLE_SOURCE_REENTRAINMENT;
      break;

    case EVENT_ACCUMULATE_MASSLESS_TRACER:
    case EVENT_ACCUMULATE_ON_INLET_OR_OUTLET:
    case EVENT_ACCUMULATE_SPLASH_OFF:
    case EVENT_ACCUMULATE_REFLECTION_OFF:
    case EVENT_ACCUMULATE_LOW_MOMENTUM:
    case EVENT_ACCUMULATE_LOW_VNORM:
    case EVENT_ACCUMULATE_LOW_ANGLE:
    case EVENT_ACCUMULATE_REFLECTION_COUNT_EXCEEDED:
    case EVENT_REFLECT:
    case EVENT_SCREEN_ACCUMULATE_SPLASH_OFF:
    case EVENT_SCREEN_ACCUMULATE_REFLECTION_OFF:
    case EVENT_SCREEN_ACCUMULATE_LOW_MOMENTUM:
    case EVENT_SCREEN_ACCUMULATE_LOW_VNORM:
    case EVENT_SCREEN_ACCUMULATE_LOW_ANGLE:
    case EVENT_SCREEN_ACCUMULATE_REFLECTION_COUNT_EXCEEDED:
    case EVENT_SCREEN_REFLECT:
    case EVENT_SCREEN_PASS_THROUGH:
    default:
      pri_source_value = PRI::PARTICLE_SOURCE_HIT;
      break;
    }
  return pri_source_value;
}

