/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#ifndef __ERRBUF_H
#define __ERRBUF_H

#include	"common.h"

#define	SIM_INFO_INITIAL_ERROR_COUNT	64


/*--------------------------------------------------------------------------*
 * Simulator errors recorded in .simerr file
 *--------------------------------------------------------------------------*/

/* Initialize error handling, and values for ackwords && parmbufs above */
VOID			cp_process_sp_errors_init(cSTRING errFileNamePtr);

BOOLEAN			cp_process_sp_errors_initialized();

/* Perform routine processing of errors */
VOID			cp_process_sp_errors(WALLCLOCK_TIME_SECS time_secs);

/* Modify the count of acceptable errors for a particular error type */
VOID			cp_change_max_errors(SP_EEP_TYPES errorIndex,
                                             asINT32 new_max_count);

VOID                    wait_for_all_initialization_simerrs();
#endif

