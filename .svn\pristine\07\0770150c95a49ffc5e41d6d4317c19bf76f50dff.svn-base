/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#include "cdi_common.h"
#include "cdi_readwrite.h"
#include "cdi_internal.h"
#include "cdi_encrypted_io.h"
#include "lexer.h"
#include "cdi_fix_parallel_dev.h"
#include <iostream>
#include <memory>
#include <string>

#include ARG_HELPER_H
#include CCUTILS_H
#include PLATFORM_H

STRING cmd_name = NULL;
char cdi_file_name[BUFSIZ] = "";
int no_audt_mods = FALSE;

BOOLEAN undump_chunk (CDI_INFO cdi_info);
static VOID undump_psmv(CDI_INFO cdi_info, sCDI_PSMV& psmv);

template <typename T> static void undump_enum_with_id(T* value);

static BOOLEAN undump_encoded64_chunk(CDI_INFO cdi_info, CIO_CCCC cccc)
{
  BOOLEAN dump_in_clear = FALSE;
  
#ifdef ENCRYPT_OFF
  // Chunks were dumped in clear by do_not_release dump
  dump_in_clear = TRUE;
#endif

  if (dump_in_clear || !cdi_is_chunk_encrypted(cdi_info,cccc))
    return FALSE;

  lexer_parse_lbrace();

  // TBD: lexer_parse makes copies of the string, need to optimize it
  std::string encodedBuffer = lexer_parse_std_string();
  cdi_write_and_decode64(cdi_info, encodedBuffer, cccc);

  lexer_parse_rbrace();
  
  return TRUE;
}


static VOID undump_scct(CDI_INFO cdi_info)
{
  sCDI_SCCT scct;

  lexer_parse_lbrace();

  scct.n_models = lexer_parse_int();
  scct.n_bcs = lexer_parse_int();
  scct.n_walls = lexer_parse_int();
 
  lexer_parse_rbrace();
  
  WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SCCT) {
    cdi_write_scct(cdi_info, &scct);
  }
}


static VOID undump_cmdl(CDI_INFO cdi_info)
{
  sCDI_CMDL cmdl;

  lexer_parse_lbrace();

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 2)) {
    cmdl.model_name = lexer_parse_string();
  }
  cmdl.model_type = lexer_parse_string();
  cmdl.coupling_type = lexer_parse_int();
  cmdl.model_filename = lexer_parse_string();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 5))
    cmdl.absolute_model_filename = lexer_parse_string();
  cmdl.results_filename = lexer_parse_string();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 5))
    cmdl.absolute_results_filename = lexer_parse_string();
  cmdl.model_length_unit = lexer_parse_string();

  cmdl.n_export_variables = lexer_parse_int();
  cmdl.export_variables = new cdiINT32[cmdl.n_export_variables];
  cmdl.export_variable_units = new STRING[cmdl.n_export_variables];
  ccDOTIMES(i, cmdl.n_export_variables) {
    cmdl.export_variables[i] = lexer_parse_int();
    cmdl.export_variable_units[i] = lexer_parse_string();
  }

  cmdl.n_import_variables = lexer_parse_int();
  cmdl.import_variables = new cdiINT32[cmdl.n_import_variables];
  cmdl.import_variable_units = new STRING[cmdl.n_import_variables];
  ccDOTIMES(i, cmdl.n_import_variables) {
    cmdl.import_variables[i] = lexer_parse_int();
    cmdl.import_variable_units[i] = lexer_parse_string();
  }

  // Phase table is always used for CDI version >= 4.3
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 3))
  {
    cmdl.n_coupling_phases = lexer_parse_int();
    sCDI_COUPLING_PHASE_DESC time_desc;
    ccDOTIMES(i, cmdl.n_coupling_phases)
    {
      time_desc.start    = lexer_parse_int();
      time_desc.period   = lexer_parse_int();
      time_desc.interval = lexer_parse_int();
      time_desc.delay    = lexer_parse_int();
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 2))
        time_desc.therm_time_ratio = lexer_parse_dfloat();
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 4))
        time_desc.stepsize = lexer_parse_dfloat();
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 6)) {
        time_desc.exact_start = lexer_parse_dfloat();
        time_desc.exact_period = lexer_parse_dfloat();
        time_desc.exact_interval = lexer_parse_dfloat();
      }
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23)) {
        time_desc.adaptive_p = lexer_parse_int();
      }
      cmdl.m_coupling_phase_descs.push_back(time_desc);
    }
    cmdl.use_end_time_for_coupling = lexer_parse_int();
    cmdl.num_iterations = lexer_parse_int();
    cmdl.end_time = lexer_parse_int();

    double temp_global_powertherm_powerflow_time_ratio = -1.0;
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 4)) {
      eCDI_POWERTHERM_CALCULATION_TYPE::Enum calc_type;
      undump_enum_with_id(&calc_type);
      cmdl.m_calculation_type = calc_type;
      if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 2))
        temp_global_powertherm_powerflow_time_ratio = lexer_parse_dfloat();
      cmdl.m_powertherm_start_time = lexer_parse_dfloat();
      cmdl.m_use_default_powertherm_start_time = lexer_parse_int();
    }

    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23)) {
      cmdl.adaptive_up_coeff = lexer_parse_dfloat();
      cmdl.gradient_low = lexer_parse_dfloat();
      cmdl.ratio_max = lexer_parse_dfloat();
      cmdl.gradient_percentage_threshold = lexer_parse_dfloat();
      cmdl.fix_pt_time_p = lexer_parse_int();
      cmdl.total_pt_duration = lexer_parse_dfloat();
    }

    // If v < 6.2, assign all of the phase-based time ratios from the single value
    if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 2)) {
      ccDOTIMES(i, cmdl.n_coupling_phases) {
        cmdl.m_coupling_phase_descs[i].therm_time_ratio = temp_global_powertherm_powerflow_time_ratio;
      }
    }

  } else {  // CDI version <= 4.2  
    sCDI_COUPLING_PHASE_DESC time_desc;
    time_desc.start    = lexer_parse_int();
    cmdl.num_iterations = lexer_parse_int();
    time_desc.period   = lexer_parse_int();
    time_desc.interval = lexer_parse_int();
    time_desc.delay    = lexer_parse_int();
    cmdl.m_coupling_phase_descs.push_back(time_desc);
    cmdl.use_end_time_for_coupling = 0; // use number of iterations to stop coupling in old CDI
    cmdl.n_coupling_phases = 1;
  }

  ccDOTIMES(i, 4) {
    ccDOTIMES(j, 4) {
      cmdl.l_to_g_xform[i][j] = lexer_parse_dfloat();
    }
  }

  cmdl.num_coupling_model_bcs = lexer_parse_int();
  cmdl.num_pf_bcs = lexer_parse_int();
  cmdl.pf_faces_with_bcs = new cdiINT32[cmdl.num_pf_bcs];
  ccDOTIMES(i, cmdl.num_pf_bcs) {
    cmdl.pf_faces_with_bcs[i] = lexer_parse_int();
  }
  
  lexer_parse_rbrace();

  WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_CMDL) {
    cdi_write_cmdl(cdi_info, &cmdl);
  }
  cdi_empty_cmdl(&cmdl);
}


static VOID undump_scbc(CDI_INFO cdi_info)
{
  sCDI_SCBC scbc;

  lexer_parse_lbrace();

  scbc.model_index = lexer_parse_int();
  scbc.target_type = lexer_parse_int();
  scbc.target_name = lexer_parse_string();
  scbc.max_match_angle = lexer_parse_dfloat();
  scbc.max_match_distance = lexer_parse_dfloat();
  scbc.target_side_to_match = lexer_parse_int();

  scbc.n_source_constraints = lexer_parse_int();
  scbc.source_constraint_face_indices = new cdiINT32[scbc.n_source_constraints];
  ccDOTIMES(i, scbc.n_source_constraints) {
    scbc.source_constraint_face_indices[i] = lexer_parse_int();
  }
 
  lexer_parse_rbrace();

  WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SCBC) {
    cdi_write_scbc(cdi_info, &scbc);
  }
  cdi_empty_scbc(&scbc);
}


static VOID undump_cplw(CDI_INFO cdi_info)
{
  sCDI_CPLW cplw;

  lexer_parse_lbrace();

  cplw.face_index = lexer_parse_int();
  cplw.model_index = lexer_parse_int();
  cplw.max_match_angle = lexer_parse_dfloat();
  cplw.max_match_distance = lexer_parse_dfloat();

  cplw.n_source_constraints = lexer_parse_int();
  cplw.source_constraint_types = new cdiINT32[cplw.n_source_constraints];
  cplw.source_constraint_sides = new cdiINT32[cplw.n_source_constraints];
  cplw.source_constraint_names = new STRING[cplw.n_source_constraints];
  ccDOTIMES(i, cplw.n_source_constraints) {
    cplw.source_constraint_types[i] = lexer_parse_int();
    cplw.source_constraint_sides[i] = lexer_parse_int();
    cplw.source_constraint_names[i] = lexer_parse_string();
  }

  cplw.flags = lexer_parse_int();

  lexer_parse_rbrace();
  
  WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_CPLW) {
    cdi_write_cplw(cdi_info, &cplw);
  }
  cdi_empty_cplw(&cplw);
}


static VOID undump_fcmp(CDI_INFO cdi_info)
{
  sCDI_FCMP fcmp;

  lexer_parse_lbrace();

  fcmp.name = lexer_parse_string();
  fcmp.equation_of_state = lexer_parse_int();
  fcmp.viscosity = lexer_parse_dfloat();
  fcmp.molecular_weight = lexer_parse_dfloat();

  lexer_parse_rbrace();
  
  WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_FCMP) {
    cdi_write_fcmp(cdi_info, &fcmp);
  }
  cdi_empty_fcmp(&fcmp);
}


static VOID undump_ptge (CDI_INFO cdi_info)
{
  sCDI_PTGE ptge;

  lexer_parse_lbrace();

  ptge.cccc = CDI_CHUNK_TYPE_PTGE;
  ptge.case_file_id = lexer_parse_int64();
  ptge.case_geometry_id = lexer_parse_int64();
  ptge.cdi_file_id = lexer_parse_int64();
  ptge.powercase_version = lexer_parse_string();
  ptge.powercase_version_n_chars =  (asINT32) strlen(ptge.powercase_version);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 0)) {
    ptge.encryption_version_id = lexer_parse_int64();
  }

  lexer_parse_rbrace();

  cdi_write_ptge(cdi_info, &ptge);

  exa_free(ptge.powercase_version);

  return;
}


static void undump_bbox (CDI_INFO cdi_info, sCDI_BBOX &bbox)
{
  lexer_parse_lbrace();
  bbox.cccc = CDI_CHUNK_TYPE_BBOX;
  bbox.coord[0][0] = lexer_parse_dfloat();
  bbox.coord[1][0] = lexer_parse_dfloat();
  bbox.coord[2][0] = lexer_parse_dfloat();
  bbox.coord[0][1] = lexer_parse_dfloat();
  bbox.coord[1][1] = lexer_parse_dfloat();
  bbox.coord[2][1] = lexer_parse_dfloat();
  lexer_parse_rbrace();
}

static VOID undump_bbox (CDI_INFO cdi_info)
{
  sCDI_BBOX bbox;
  undump_bbox (cdi_info, bbox);
  cdi_write_bbox(cdi_info, &bbox);

  return;
}

static VOID undump_cmnt (CDI_INFO cdi_info)
{
  sCDI_CMNT cmnt;

  lexer_parse_lbrace();

  cmnt.cccc = CDI_CHUNK_TYPE_CMNT;
  cmnt.title = lexer_parse_string();
  cmnt.n_title_char = (asINT32) strlen(cmnt.title);
  cmnt.value = lexer_parse_string();
  cmnt.n_value_char = (asINT32) strlen(cmnt.value);

  lexer_parse_rbrace();

  cdi_write_cmnt(cdi_info, &cmnt);

  exa_free(cmnt.title);
  exa_free(cmnt.value);

  return;
}

static VOID undump_audt (CDI_INFO cdi_info)
{
  sCDI_AUDT audt;

  lexer_parse_lbrace();

  audt.cccc = CDI_CHUNK_TYPE_AUDT;

  audt.audit_ur = lexer_parse_string();

  if (!no_audt_mods) {
    AUDIT_TRAIL trail = audit_make(audt.audit_ur);
    audit_append_program_entry("undump_cdi", CDI_VERSION, NULL, NULL, (time_t) -1, trail);
    audit_append_file_entry ("cdi", cdi_file_name, FALSE, (time_t) -1, trail);
    exa_free(audt.audit_ur);
    audt.audit_ur = EXA_CALLOC_ARRAY(CHARACTER, strlen(audit_universal_rep(trail))+1);
    strcpy(audt.audit_ur, audit_universal_rep(trail));
    audit_free(trail);
  }

  audt.n_audit_ur_char = (asINT32) strlen(audt.audit_ur);

  lexer_parse_rbrace();

  cdi_write_audt(cdi_info, &audt);

  exa_free(audt.audit_ur);

  return;
}

static VOID undump_undb (CDI_INFO cdi_info)
{
  sCDI_UNDB undb;

  lexer_parse_lbrace();

  undb.cccc = CDI_CHUNK_TYPE_UNDB;

  undb.units_db_str = lexer_parse_string();
  undb.units_db_str_length = (asINT32) strlen(undb.units_db_str);

  lexer_parse_rbrace();

  cdi_write_undb(cdi_info, &undb);

  exa_free(undb.units_db_str);

  return;
}

static VOID undump_eqns (CDI_INFO cdi_info)
{
  sCDI_EQNS eqns;

  lexer_parse_lbrace();

  eqns.cccc = CDI_CHUNK_TYPE_EQNS;

  eqns.equations = lexer_parse_string();
  eqns.equations_length = (asINT32) strlen(eqns.equations);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23))
    eqns.pthermTimeAvailable = lexer_parse_int();
  lexer_parse_rbrace();

  cdi_write_eqns(cdi_info, &eqns);

  exa_free(eqns.equations);

  return;
}

static VOID undump_gtbl (CDI_INFO cdi_info)
{
  sCDI_GTBL gtbl;

  gtbl.cccc = CDI_CHUNK_TYPE_GTBL;

  lexer_parse_lbrace();
  gtbl.name = lexer_parse_string();
  gtbl.filename = lexer_parse_string();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 4))
    gtbl.absolute_filename = lexer_parse_string();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 1))
    gtbl.flags = lexer_parse_int();
  // No need to default flags if pre-3.1 b/c it won't be written out anyways
  gtbl.coord_sys = lexer_parse_int();
  gtbl.read_during_sim = lexer_parse_int();
  gtbl.read_after_meas = lexer_parse_int();
  gtbl.meas_window_index = lexer_parse_int();
  gtbl.first_meas_frame_to_read_after = lexer_parse_int();
  gtbl.num_meas_frames_between_reads = lexer_parse_int();
  gtbl.command_to_run_before_read = lexer_parse_string();
  gtbl.first_interval = lexer_parse_int();
  gtbl.subsequent_intervals = lexer_parse_int();
  gtbl.end_time = lexer_parse_int();
  gtbl.table_string = lexer_parse_string();

  lexer_parse_rbrace();

  cdi_write_gtbl(cdi_info, &gtbl);

  exa_free(gtbl.name);
  exa_free(gtbl.table_string);

  return;
}

static VOID undump_tabl (CDI_INFO cdi_info)
{
  scCDI_TABL tabl;

  tabl.cccc = CDI_CHUNK_TYPE_TABL;

  lexer_parse_lbrace();
  tabl.num_tables = lexer_parse_int();

  tabl.tables = (sCDI_GTBL *)exa_malloc(tabl.num_tables * sizeof(sCDI_GTBL),
				       "undump_tabl", "a table array");

  DOTIMES(i, tabl.num_tables, {
    tabl.tables[i].name = lexer_parse_string();
    //tabl.tables[i].name_length = (asINT32) strlen(tabl.tables[i].name);
    tabl.tables[i].table_string = lexer_parse_string();
    //tabl.tables[i].table_length = (asINT32) strlen(tabl.tables[i].table_string);
    tabl.tables[i].coord_sys = lexer_parse_int();
  });

  lexer_parse_rbrace();

  cdi_write_tabl(cdi_info, &tabl);

  DOTIMES(i, tabl.num_tables, {
    exa_free(tabl.tables[i].name);
    exa_free(tabl.tables[i].table_string);
  });

  exa_free(tabl.tables);

  return;
}

static VOID undump_csys (CDI_INFO cdi_info)
{
  scCDI_CSYS csys;

  csys.cccc = CDI_CHUNK_TYPE_CSYS;

  lexer_parse_lbrace();
  csys.num_coord_systems = lexer_parse_int();

  csys.coord_systems = (sCDI_CSYS *)exa_malloc(csys.num_coord_systems * sizeof(sCDI_CSYS),
					       "undump_csys", "a csys array");

  DOTIMES(i, csys.num_coord_systems, {
    csys.coord_systems[i].name = lexer_parse_string();
    csys.coord_systems[i].g_to_l_xform[0][0] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[0][1] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[0][2] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[0][3] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[1][0] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[1][1] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[1][2] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[1][3] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[2][0] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[2][1] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[2][2] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[2][3] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[3][0] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[3][1] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[3][2] = lexer_parse_dfloat();
    csys.coord_systems[i].g_to_l_xform[3][3] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[0][0] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[0][1] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[0][2] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[0][3] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[1][0] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[1][1] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[1][2] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[1][3] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[2][0] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[2][1] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[2][2] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[2][3] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[3][0] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[3][1] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[3][2] = lexer_parse_dfloat();
    csys.coord_systems[i].l_to_g_xform[3][3] = lexer_parse_dfloat();
  });

  lexer_parse_rbrace();

  cdi_write_csys(cdi_info, &csys);

  DOTIMES(i, csys.num_coord_systems, {
    exa_free(csys.coord_systems[i].name);
  });

  exa_free(csys.coord_systems);

  return;
}

static VOID undump_rgpn (CDI_INFO cdi_info)
{
 
  if (undump_encoded64_chunk(cdi_info, CDI_CHUNK_TYPE_RGPN))
    return;

  lexer_parse_lbrace();
  
  sCDI_RGPN rgpn;
  rgpn.cccc = CDI_CHUNK_TYPE_RGPN;
  int numRegions = lexer_parse_int();
  ccDOTIMES(i, numRegions) {
    rgpn.regionIds.push_back(lexer_parse_int());
    rgpn.protectionIds.push_back(lexer_parse_int());
  }    
  cdi_write_rgpn(cdi_info, &rgpn);
  
  lexer_parse_rbrace();
  return;
}

static VOID undump_vrtx (CDI_INFO cdi_info)
{
  
  if (undump_encoded64_chunk(cdi_info, CDI_CHUNK_TYPE_VRTX))
    return;

  lexer_parse_lbrace();

  sCDI_VRTX vrtx;
  vrtx.cccc = CDI_CHUNK_TYPE_VRTX;
  vrtx.coord = NULL;
  
  int numVertices = lexer_parse_int();
  vrtx.n_vertex = numVertices;
  vrtx.coord = (idFLOAT *)exa_malloc(3 * vrtx.n_vertex * sizeof(idFLOAT), 
                                     "undump_vrtx",
                                     "vertex array");
  
  ccDOTIMES(i, 3 * vrtx.n_vertex) {
    vrtx.coord[i] = lexer_parse_dfloat();
  };
  cdi_write_vrtx(cdi_info, &vrtx);
  exa_free(vrtx.coord);

  lexer_parse_rbrace();

  return;
}

static VOID undump_fact_old (CDI_INFO cdi_info)
{
  scCDI_FACT fact;

  lexer_parse_lbrace();

  fact.cccc = CDI_CHUNK_TYPE_FACT;
  fact.n_facet = lexer_parse_int();
  fact.facet = (sCDI_FACT_OLD *)exa_malloc(fact.n_facet * sizeof(sCDI_FACT_OLD),
					   "undump_fact",
					   "a facet array");

  DOTIMES(i, fact.n_facet, {
    fact.facet[i].face = lexer_parse_int();
    fact.facet[i].edgehalf[0] = lexer_parse_int();
    fact.facet[i].edgehalf[1] = lexer_parse_int();
    fact.facet[i].edgehalf[2] = lexer_parse_int();
    fact.facet[i].normal[0] = lexer_parse_dfloat();
    fact.facet[i].normal[1] = lexer_parse_dfloat();
    fact.facet[i].normal[2] = lexer_parse_dfloat();
  });

  lexer_parse_rbrace();

  cdi_write_fact(cdi_info, &fact);

  exa_free(fact.facet);

  return;
}

static VOID undump_fact (CDI_INFO cdi_info)
{
  if (cdi_get_major_version(cdi_info) < 3) {
    undump_fact_old(cdi_info);
  }
  else {
    static asINT32 max_n_vertices = 20;
    static asINT32 *facet_vertices = NULL;

    if (facet_vertices == NULL)
      facet_vertices = EXA_CALLOC_ARRAY(asINT32, max_n_vertices);
    {
      WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_FACT) {

	sCDI_FACT_HEADER facets_header;
	sCDI_FACT facet;

	lexer_parse_lbrace();

	facets_header.n_facets = lexer_parse_int();
	facets_header.n_vertex_refs = lexer_parse_int();
 
	cdi_write_facet_header(cdi_info, &facets_header);

	DOTIMES(i, facets_header.n_facets, {
	  facet.front_face = lexer_parse_int();
	  facet.back_face = lexer_parse_int();
	  facet.n_vertices = lexer_parse_int();
	  if (facet.n_vertices > max_n_vertices) {
	    max_n_vertices = 2 * facet.n_vertices;
	    facet_vertices = EXA_REALLOC_ARRAY(facet_vertices, asINT32, max_n_vertices);
	  }
	  DOTIMES(j, facet.n_vertices, {
	    facet_vertices[j] = lexer_parse_int();
	  });
	  facet.vertices = facet_vertices;
	  cdi_write_facets(cdi_info, &facet, 1);
	});

	lexer_parse_rbrace();
      }
    }
  }
  return;
}


static VOID undump_edge (CDI_INFO cdi_info)
{
  scCDI_EDGE edge;

  lexer_parse_lbrace();

  edge.cccc = CDI_CHUNK_TYPE_EDGE;
  edge.n_edgehalf = lexer_parse_int();
  edge.edgehalf = (sCDI_EDGE *)exa_malloc(edge.n_edgehalf * sizeof(sCDI_EDGE),
					  "undump_edge",
					  "an edgehalf array");

  DOTIMES(i, edge.n_edgehalf, {
    edge.edgehalf[i].conj_facet    = lexer_parse_int();
    edge.edgehalf[i].conj_edgehalf = lexer_parse_int();
    edge.edgehalf[i].head_vert     = lexer_parse_int();
    edge.edgehalf[i].tail_vert     = lexer_parse_int();
  });

  lexer_parse_rbrace();

  cdi_write_edge(cdi_info, &edge);

  exa_free(edge.edgehalf);

  return;
}

static VOID undump_face_old (CDI_INFO cdi_info)
{
  sCDI_FACE_OLD face;
  lexer_parse_lbrace();

  face.cccc = CDI_CHUNK_TYPE_FACE;

  if (cdi_get_major_version(cdi_info) < 1) {
    face.index = -1;
    face.name = NULL;
    face.color = NULL;
  }
  else {
    face.index = lexer_parse_int();
    face.name = lexer_parse_string();
    face.color = lexer_parse_string();
  }

  face.prop = lexer_parse_int();
  face.n_facet = lexer_parse_int();
  face.facet = (asINT32 *)exa_malloc(face.n_facet * sizeof(asINT32),
				     "undump_face_old",
				     "a facet table");

  DOTIMES(i, face.n_facet, {
    face.facet[i] = lexer_parse_int();
  });

  lexer_parse_rbrace();

  cdi_write_face_old(cdi_info, &face);

  exa_free(face.facet);

  return;
}

static VOID undump_face (CDI_INFO cdi_info)
{
  if (cdi_get_major_version(cdi_info) < 3) {
    undump_face_old(cdi_info);
    return;
  }

  // These two structures are used to track display property data from the old
  // partition views so we can correctly set model view display property data.
  std::vector<char*> mvColors;
  std::vector<CDI_DISPLAY_MODE> mvDisplayModes;

  sCDI_FACE face;
  
  lexer_parse_lbrace();
  bool rbraceParsed = false;

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    // FACP chunk
    lexer_parse_specific_id(CDI_CHUNK_TYPE_FACP);
    lexer_parse_lbrace();
  }
  
  face.index = lexer_parse_int();
  face.prop = lexer_parse_int();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 5))
    face.thermal_physics = lexer_parse_int();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 19)) {
    face.flow_physics_comp = lexer_parse_int();
    face.thermal_physics_comp = lexer_parse_int();
  }
  face.name = lexer_parse_string();
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0))
    mvColors.resize(1, lexer_parse_string());
  face.material_index = lexer_parse_int();
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0))
    mvDisplayModes.resize(1, (CDI_DISPLAY_MODE) lexer_parse_int());
  face.n_front_facets = lexer_parse_int();
  face.n_back_facets = lexer_parse_int();

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 0))
    face.n_layers = lexer_parse_int();

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    // end of FACP chunk
    lexer_parse_rbrace();
  }

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 13)) {
      cdiINT32 n_partition_segments = lexer_parse_int();
      ccDOTIMES(i, n_partition_segments) {
        cdiINT32 segment_index, effective_segment_index;
        segment_index = lexer_parse_int();
        if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
          mvColors.push_back(lexer_parse_string());
          mvDisplayModes.push_back((CDI_DISPLAY_MODE)lexer_parse_int());
        }
        effective_segment_index = lexer_parse_int();
        if (segment_index >= 0) {
          cCDI_SEGMENT_REF segref(i + 1, segment_index + 1);
          face.psmv.partition_segment_refs.push_back(segref);
        }
        face.psmv.effective_segments.push_back(effective_segment_index);
      }
    }

    face.psmv.n_model_views = 0;
    face.psmv.mv_display_props = NULL;

    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
      face.psmv.n_model_views = lexer_parse_int();

      face.psmv.mv_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, face.psmv.n_model_views);

      ccDOTIMES(i, face.psmv.n_model_views) {
        face.psmv.mv_display_props[i].color = lexer_parse_string();
        face.psmv.mv_display_props[i].display_mode = (CDI_DISPLAY_MODE)lexer_parse_int();
        face.psmv.mv_display_props[i].transparency = lexer_parse_dfloat();
      }
      if (!lexer_parse_string_or_rbrace(&face.psmv.mv_display_props[0].realisticLook))
        rbraceParsed = true;
      else {
        for (int i = 1; i < face.psmv.n_model_views; i++)
          face.psmv.mv_display_props[i].realisticLook = lexer_parse_string();
      }
    }
    else {
      face.psmv.n_model_views = mvColors.size();

      face.psmv.mv_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, face.psmv.n_model_views);

      ccDOTIMES(i, face.psmv.n_model_views) {
        face.psmv.mv_display_props[i].color = mvColors[i];
        face.psmv.mv_display_props[i].display_mode = mvDisplayModes[i];
      }
    }

    // NOTE: We don't free the strings in mvColors b/c they've been handed off to
    // the mv_display_props.
  }
  else {
    lexer_parse_specific_id(CDI_CHUNK_TYPE_PSMV);
    undump_psmv(cdi_info, face.psmv);
  }

  if (!rbraceParsed)
    lexer_parse_rbrace();

  
  {    
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_FACE) {
      cdi_write_face(cdi_info, &face);
    }
  }

  cdi_empty_face(&face);
}

static VOID undump_model_view (CDI_INFO cdi_info)
{
  lexer_parse_lbrace();
  bool rbraceParsed = false;

  sCDI_MDLV mdlv;
  mdlv.name = lexer_parse_string();
  mdlv.partition_index = lexer_parse_int();
  mdlv.color_ref_index = lexer_parse_int();
  mdlv.disp_mode_ref_index = lexer_parse_int();
  mdlv.n_partition_segments = lexer_parse_int();
  
  mdlv.segment_display_props = NULL;
  if (mdlv.n_partition_segments > 0) {
    mdlv.segment_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, mdlv.n_partition_segments);
    ccDOTIMES(i, mdlv.n_partition_segments) {
      mdlv.segment_display_props[i].color = lexer_parse_string();
      mdlv.segment_display_props[i].display_mode = (CDI_DISPLAY_MODE) lexer_parse_int();
      mdlv.segment_display_props[i].transparency = lexer_parse_dfloat();
      if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info))
        mdlv.segment_display_props[i].realisticLook = lexer_parse_string();
    }
    if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
      if (!lexer_parse_string_or_rbrace(&mdlv.segment_display_props[0].realisticLook))
        rbraceParsed = true;
      else {
        for (int i = 1; i < mdlv.n_partition_segments; i++)
          mdlv.segment_display_props[i].realisticLook = lexer_parse_string();
      }
    }
  }

  mdlv.n_partial_parts = 0;
  mdlv.partial_display_props = NULL;
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    mdlv.n_partial_parts = lexer_parse_int();
    if (mdlv.n_partial_parts > 0) {
      mdlv.partial_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, mdlv.n_partial_parts);
      ccDOTIMES(i, mdlv.n_partial_parts) {
        mdlv.partial_display_props[i].color = lexer_parse_string();
        mdlv.partial_display_props[i].display_mode = (CDI_DISPLAY_MODE)lexer_parse_int();
        mdlv.partial_display_props[i].transparency = lexer_parse_dfloat();
        mdlv.partial_display_props[i].realisticLook = lexer_parse_string();
      }
    }
  }
  
  if (!rbraceParsed)
    lexer_parse_rbrace();

  {    
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_MDLV) {
      cdi_write_model_view(cdi_info, &mdlv);
    }
  }
}

static void undump_sgrf(CDI_INFO cdi_info)
{
  cCDI_SEGMENT_REF segref(-1, -1);
  segref.Undump();
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SGRF) {
      segref.WriteToCDI(cdi_info);
    }
  }
}

static void
undump_segment_list_with_id(std::vector<cCDI_SEGMENT_REF>& segRefVec) {
  lexer_parse_specific_id(CDI_CHUNK_TYPE_SEGL);
  lexer_parse_lbrace();
  char cccc_string[5];
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4) &&
         cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_SGRF)
  {
    cCDI_SEGMENT_REF segref(-1, -1);
    segref.Undump();
    segRefVec.push_back(segref);
  }
}

static void undump_pprf(CDI_INFO cdi_info)
{
  cCDI_PARTIAL_PART_REF ppref(-1, -1);
  ppref.Undump();
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_PPRF) {
      ppref.WriteToCDI(cdi_info);
    }
  }
}

static VOID undump_psmv(CDI_INFO cdi_info, sCDI_PSMV& psmv)
{
  lexer_parse_lbrace();
  bool rbraceParsed = false;

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
    cdiINT32 n_partition_segments = lexer_parse_int();
    cCDI_SEGMENT_REF segref(0, 0);
    psmv.partition_segment_refs.push_back(segref);
    psmv.effective_segments.push_back(-1);
    if (n_partition_segments > 0) {
      ccDOTIMES(i, n_partition_segments) {
        cdiINT32 segment_index = lexer_parse_int();
        cdiINT32 effective_segment_index = lexer_parse_int();
        segref.m_partitionIndex = i + 1;
        segref.m_segmentIndex = segment_index + 1;
        psmv.partition_segment_refs.push_back(segref);
        psmv.effective_segments.push_back(effective_segment_index);
      }
    }
  }
  else {
    undump_segment_list_with_id(psmv.partition_segment_refs);
  }

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    // Writing model info in a MVDP chunk
    lexer_parse_specific_id(CDI_CHUNK_TYPE_MVDP);
    lexer_parse_lbrace();
  }

  psmv.n_model_views = lexer_parse_int();
  psmv.mv_display_props = NULL;
  if (psmv.n_model_views > 0) {
    psmv.mv_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, psmv.n_model_views);
    ccDOTIMES(i, psmv.n_model_views) {
      psmv.mv_display_props[i].color = lexer_parse_string();
      psmv.mv_display_props[i].display_mode = (CDI_DISPLAY_MODE) lexer_parse_int();
      psmv.mv_display_props[i].transparency = lexer_parse_dfloat();
      if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info))
        psmv.mv_display_props[i].realisticLook = lexer_parse_string();
    }

    if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
      if (!lexer_parse_string_or_rbrace(&psmv.mv_display_props[0].realisticLook))
        rbraceParsed = true;
      else {
        for (int i = 1; i < psmv.n_model_views; i++)
          psmv.mv_display_props[i].realisticLook = lexer_parse_string();
      }
    }
  }

  if (!rbraceParsed)
    lexer_parse_rbrace();

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    // End of MVDP chunk
    lexer_parse_rbrace();
  }
}

static void undump_psmv(CDI_INFO cdi_info)
{
  sCDI_PSMV psmv;
  undump_psmv(cdi_info, psmv);
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_PSMV) {
      cdi_write_psmv(cdi_info, &psmv);
    }
  }

  cdi_empty_psmv(&psmv);
}

static VOID undump_null (CDI_INFO cdi_info)
{
  sCDI_NULL null;

  lexer_parse_lbrace();

  null.cccc = CDI_CHUNK_TYPE_NULL;
  null.n_null_char = lexer_parse_int();
  null.null_char = (char *)exa_malloc(null.n_null_char,
				      "undump_null",
				      "a null char array");
  DOTIMES(i, null.n_null_char, {
    null.null_char[i] = (char)(lexer_parse_hex());
  });

  lexer_parse_rbrace();

  cdi_write_null(cdi_info, &null);

  exa_free(null.null_char);

  return;
}


static void undump_bool_helper(bool* value)
{
  lexer_parse_lbrace();
  sINT32 intValue = lexer_parse_int();
  *value = (intValue != 0);
  lexer_parse_rbrace();
}

static VOID undump_bool(CDI_INFO cdi_info)
{
  bool value;
  undump_bool_helper(&value);
  cdi_write_bool(cdi_info, value);
}

static void undump_bool_with_id(bool* value)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_BOOL);
  undump_bool_helper(value);
}

static void undump_int_helper(sINT32* value)
{
  lexer_parse_lbrace();
  *value = lexer_parse_int();
  lexer_parse_rbrace();
}

static VOID undump_int_(CDI_INFO cdi_info)
{
  sINT32 value;
  undump_int_helper(&value);
  cdi_write_int_(cdi_info, value);
}

static void undump_int_with_id(sINT32* value)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_INT_);
  undump_int_helper(value);
}



static void undump_strg_helper(std::string* value)
{
  lexer_parse_lbrace();
  *value = lexer_parse_std_string();
  lexer_parse_rbrace();
}

static void undump_strg(CDI_INFO cdi_info)
{
  std::string str;
  undump_strg_helper(&str);
  cdi_write_strg(cdi_info, str);
}

static void undump_strg_with_id(std::string* value)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_STRG);
  undump_strg_helper(value);
}


static VOID undump_name_helper(CDI_NAME name)
{
  lexer_parse_lbrace();

  name->cccc = CDI_CHUNK_TYPE_NAME;
  name->name = lexer_parse_string();
  name->n_char = (asINT32) strlen(name->name);

  lexer_parse_rbrace();

  return;
}

static VOID undump_name(CDI_INFO cdi_info)
{
  sCDI_NAME name;

  undump_name_helper(&name);
  cdi_write_name(cdi_info, &name);
  if (name.name != NULL)
    exa_free(name.name);
}

static void undump_namestr_with_id(std::string* value)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_NAME);
  undump_strg_helper(value);
}


static VOID undump_offs (CDI_INFO cdi_info)
{
  sCDI_OFFS offs;

  lexer_parse_lbrace();

  offs.cccc = CDI_CHUNK_TYPE_OFFS;
  offs.offset = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_offs(cdi_info, &offs);

  return;
}

static VOID undump_prgn (CDI_INFO cdi_info)
{
  sCDI_PRGN prgn;

  lexer_parse_lbrace();

  prgn.cccc = CDI_CHUNK_TYPE_PRGN;
  prgn.tire_index = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_prgn(cdi_info, &prgn);

  return;
}

static VOID undump_mprm (CDI_INFO cdi_info)
{
  sCDI_MPRM mprm;

  lexer_parse_lbrace();

  mprm.cccc = CDI_CHUNK_TYPE_MPRM;
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 12)) {
    mprm.start_via = static_cast<eCDI_MEAS_START_TIME_VIA::Enum>(lexer_parse_int());
    int monitorSize = lexer_parse_int();
    ccDOTIMES(i, monitorSize)
      mprm.monitors.push_back(lexer_parse_int());
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 1)) {
    mprm.time_rel_solver = static_cast<eCDI_COUPLED_SOLVER::Enum>(lexer_parse_int());
  }
  mprm.start_time = lexer_parse_int();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 12)) {
    mprm.end_via = static_cast<eCDI_MEAS_END_TIME_VIA::Enum>(lexer_parse_int());
    mprm.duration = lexer_parse_int();
  }
  mprm.end_time = lexer_parse_int();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 1))
    mprm.num_frames = lexer_parse_int();
  // No need to default num_frames if pre-3.2 b/c it won't be written out anyways
  mprm.period = lexer_parse_int();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 22))
    mprm.period_sync_group_index = lexer_parse_int();
  mprm.average_interval = lexer_parse_int();
  mprm.spacing = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_mprm(cdi_info, &mprm);

  return;
}

static void 
undump_mpsg (CDI_INFO cdi_info)
{
  lexer_parse_lbrace();
  
  sCDI_MPSG mpsg;
  mpsg.cccc = CDI_CHUNK_TYPE_MPSG;

  STRING str = lexer_parse_string();
  mpsg.name = str;
  exa_free(str);

  lexer_parse_rbrace();
  cdi_write_mpsg(cdi_info,&mpsg);
}


static void undump_mflt_helper(sCDI_MFLT* pMflt)
{
  lexer_parse_lbrace();
  pMflt->cccc = CDI_CHUNK_TYPE_MFLT;
  pMflt->min_pressure = lexer_parse_dfloat();
  pMflt->max_pressure = lexer_parse_dfloat();
  lexer_parse_rbrace();
}

static VOID undump_mflt (CDI_INFO cdi_info)
{
  sCDI_MFLT mflt;
  undump_mflt_helper(&mflt);
  cdi_write_mflt(cdi_info, &mflt);

  return;
}

static void undump_mflt_with_id(sCDI_MFLT** ppMflt)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_MFLT);
  *ppMflt = EXA_CALLOC_STRUCT(CDI_MFLT);
  undump_mflt_helper(*ppMflt);
}


static VOID undump_mref (CDI_INFO cdi_info)
{
  sCDI_MREF mref;

  lexer_parse_lbrace();

  mref.cccc = CDI_CHUNK_TYPE_MREF;
  mref.reference_point[0] = lexer_parse_dfloat();
  mref.reference_point[1] = lexer_parse_dfloat();
  mref.reference_point[2] = lexer_parse_dfloat();

  lexer_parse_rbrace();

  cdi_write_mref(cdi_info, &mref);

  return;
}

VOID undump_mdev (CDI_INFO cdi_info)
{
  sCDI_MDEV mdev;

  lexer_parse_lbrace();

  mdev.cccc = CDI_CHUNK_TYPE_MDEV;
 
  mdev.seg_size = lexer_parse_dfloat();
  mdev.icsys = lexer_parse_int();
  mdev.ilrf = lexer_parse_int();  

  lexer_parse_rbrace();

  cdi_write_mdev(cdi_info, &mdev);
}

static VOID undump_mfac (CDI_INFO cdi_info)
{
  sCDI_MFAC mfac;

  lexer_parse_lbrace();

  mfac.cccc = CDI_CHUNK_TYPE_MFAC;
  mfac.grouping = (CDI_MEAS_FACE_GROUPING_TYPE) lexer_parse_int();
  mfac.merge_to_input = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_mfac(cdi_info, &mfac);

  return;
}

void
cCDI_BASE_VAR_OPTIONS::Undump(CDI_INFO cdi_info)
{
  asINT32 num_options = lexer_parse_int();
  sCDI_VAR_OPTION readOption;
  for (size_t i = 0; i < num_options; ++i) {
    STRING optName = lexer_parse_string();
    if (GetVarOptionByName(optName, readOption))
      SetOption(readOption);
  }
}

static VOID undump_mstp (CDI_INFO cdi_info, CDI_MSTP mstp)
{
  lexer_parse_lbrace();

  mstp->cccc = CDI_CHUNK_TYPE_MSTP;
  mstp->standard_mask = lexer_parse_int();
  
  // For consistency, set the output in local reference frame bit to 0 
  // for older versions
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 17)) {
    mstp->standard_mask &= (~CDI_MEAS_OPT_LOCAL_CSYS);
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
    mstp->extended_std_var_options.Undump(cdi_info);
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 8)) {
    mstp->meas_type = (CDI_MEASTYPE)lexer_parse_int();
    
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 1) || 
        (cdi_info->major_version == 3 && cdi_info->minor_version == 24)) {
        
        int n_custom_fluid_vars = lexer_parse_int(); 
        int n_custom_fluid_particle_vars = lexer_parse_int(); 
        int n_custom_surface_vars = lexer_parse_int(); 
        int n_custom_surface_particle_vars = lexer_parse_int(); 
        int n_custom_porous_vars = lexer_parse_int(); 
          
        ccDOTIMES(ivar, n_custom_fluid_vars) {
          SRI_VARIABLE_TYPE var = (SRI_VARIABLE_TYPE)lexer_parse_int(); 
          mstp->custom_fluid_options.add_var(var);
        }
        ccDOTIMES(ivar, n_custom_fluid_particle_vars) {
          SRI_VARIABLE_TYPE var = (SRI_VARIABLE_TYPE)lexer_parse_int(); 
          mstp->custom_fluid_particle_options.add_var(var);
        }
        ccDOTIMES(ivar, n_custom_surface_vars) {
          SRI_VARIABLE_TYPE var = (SRI_VARIABLE_TYPE)lexer_parse_int(); 
          mstp->custom_surface_options.add_var(var);
        }
        ccDOTIMES(ivar, n_custom_surface_particle_vars) {
          SRI_VARIABLE_TYPE var = (SRI_VARIABLE_TYPE)lexer_parse_int(); 
          mstp->custom_surface_particle_options.add_var(var);
        }
        ccDOTIMES(ivar, n_custom_porous_vars) {
          SRI_VARIABLE_TYPE var = (SRI_VARIABLE_TYPE)lexer_parse_int(); 
          mstp->custom_porous_options.add_var(var);
        }

        if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 0)) {

          int n_custom_solid_volume_vars = lexer_parse_int();
          int n_custom_shell_vars = lexer_parse_int();

          ccDOTIMES(ivar, n_custom_solid_volume_vars) {
            SRI_VARIABLE_TYPE var = (SRI_VARIABLE_TYPE)lexer_parse_int();
            mstp->custom_solid_volume_options.add_var(var);
          }
          ccDOTIMES(ivar, n_custom_shell_vars) {
            SRI_VARIABLE_TYPE var = (SRI_VARIABLE_TYPE)lexer_parse_int();
            mstp->custom_shell_options.add_var(var);
          }
        }
        
    } else {
      
      int numBitmasks = cdi_meas_vars_num_bitmasks(cdi_info);
      
      ccDOTIMES(i, numBitmasks)
        mstp->custom_fluid_options.m_bitmasksObsolete[i] = lexer_parse_int();
      mstp->custom_fluid_options.convert_bit_mask_to_vars();
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 21)) {
        ccDOTIMES(i, numBitmasks)
          mstp->custom_fluid_particle_options.m_bitmasksObsolete[i] = lexer_parse_int();
        mstp->custom_fluid_particle_options.convert_bit_mask_to_vars();
      }
      ccDOTIMES(i, numBitmasks)
        mstp->custom_surface_options.m_bitmasksObsolete[i] = lexer_parse_int();
      mstp->custom_surface_options.convert_bit_mask_to_vars();
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 21)) {
        ccDOTIMES(i, numBitmasks)
          mstp->custom_surface_particle_options.m_bitmasksObsolete[i] = lexer_parse_int();
        mstp->custom_surface_particle_options.convert_bit_mask_to_vars();
      }
      ccDOTIMES(i, numBitmasks)
        mstp->custom_porous_options.m_bitmasksObsolete[i] = lexer_parse_int();
      mstp->custom_porous_options.convert_bit_mask_to_vars();
    }
  }

  lexer_parse_rbrace();

  
  return;
}


static VOID undump_mstp (CDI_INFO cdi_info)
{
  CDI_MSTP mstp = new sCDI_MSTP;
  undump_mstp (cdi_info, mstp);
  cdi_write_mstp(cdi_info, mstp);
  delete mstp;
 }


static VOID undump_ptyp (CDI_INFO cdi_info)
{
  sCDI_PTYP ptyp;

  lexer_parse_lbrace();

  ptyp.cccc = CDI_CHUNK_TYPE_PTYP;
  ptyp.type = lexer_parse_int();
  ptyp.n_integer = lexer_parse_int();
  ptyp.n_continuous = lexer_parse_int();
  ptyp.n_initial = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_ptyp(cdi_info, &ptyp);

  return;
}

static VOID undump_hxch_base (CDI_INFO cdi_info, sCDI_HXCH_BASE &hxch)
{
  lexer_parse_lbrace();

  hxch.name = lexer_parse_string();

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 7))
    hxch.part_name = lexer_parse_string();
  else 
    hxch.part_name = EXA_STRDUP(hxch.name);

  hxch.type = lexer_parse_int();
  hxch.tool = lexer_parse_int();
  hxch.flags = lexer_parse_int();
  hxch.table_csys_index = lexer_parse_int();
  hxch.medium_csys_index = lexer_parse_int();
  hxch.inlet_face_index = lexer_parse_int();
  hxch.outlet_face_index = lexer_parse_int();

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 19)) {
    hxch.top_exchanger_face_index = lexer_parse_int();
    hxch.coolant_entry_face_index = lexer_parse_int();
  } else {
    hxch.top_exchanger_face_index = -1;
    hxch.coolant_entry_face_index = -1;
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 3)) {
    hxch.inlet_facet_offset = lexer_parse_int();
    hxch.outlet_facet_offset = lexer_parse_int();
  }
  hxch.inlet_meas_index = lexer_parse_int();
  hxch.outlet_meas_index = lexer_parse_int();
  if ((hxch.flags & CDI_HXCH_HAS_HEAT_GEN_MEAS) != 0)
    hxch.heat_gen_meas_index = lexer_parse_int();
  hxch.table_index = lexer_parse_int();
  hxch.adiabatic_index = lexer_parse_int();
  hxch.medium_index = lexer_parse_int();
  hxch.upstream_index = lexer_parse_int();
  hxch.n_stages = lexer_parse_int();
  hxch.n_y_divisions = lexer_parse_int();

  hxch.n_pass_divisions = new cdiINT32[hxch.n_stages];
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 15)) {
    ccDOTIMES(i, hxch.n_stages)
      hxch.n_pass_divisions[i] = lexer_parse_int();
  }
  else {
    hxch.n_pass_divisions[0] = lexer_parse_int();
    if (hxch.n_stages == 2)  { // Pre version 3.15, only 1 or 2 stages were allowed
      cdiINT32 numDivisionsPerPass = (hxch.n_pass_divisions[0] / 2);
      hxch.n_pass_divisions[0] = hxch.n_pass_divisions[1] = numDivisionsPerPass;
    }
  }

  hxch.x_len = lexer_parse_dfloat();
  hxch.y_len = lexer_parse_dfloat();
  hxch.z_len = lexer_parse_dfloat();
}

static VOID undump_hxch (CDI_INFO cdi_info)
{
  if (undump_encoded64_chunk(cdi_info, CDI_CHUNK_TYPE_HXCH))
    return;

  sCDI_HXCH hxch;

  hxch.cccc = CDI_CHUNK_TYPE_HXCH;

  undump_hxch_base (cdi_info, hxch);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 2))
    hxch.experiment_area = lexer_parse_dfloat();
  hxch.percent_flow = lexer_parse_dfloat();
  hxch.mass_flow_rate = lexer_parse_dfloat();
  hxch.specific_heat = lexer_parse_dfloat();
  hxch.heat_rejection = lexer_parse_dfloat();
  hxch.entry_temp = lexer_parse_dfloat();
  hxch.experiment_ref_temp = lexer_parse_dfloat();
  hxch.min_air_flow = lexer_parse_dfloat();
  hxch.max_air_flow = lexer_parse_dfloat();
  hxch.kc_coeff = lexer_parse_dfloat();
  hxch.kh_coeff = lexer_parse_dfloat();
  hxch.alpha_coeff = lexer_parse_dfloat();
  hxch.beta_coeff = lexer_parse_dfloat();
  hxch.d_coeff = lexer_parse_dfloat();
  hxch.data_string = lexer_parse_string();

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 12)) {
    hxch.exp_coolant_sp_heat = lexer_parse_dfloat();
    hxch.exp_coolant_viscosity = lexer_parse_dfloat();
    hxch.exp_coolant_thermal_conductivity = lexer_parse_dfloat();
    hxch.user_coolant_viscosity = lexer_parse_dfloat();
    hxch.user_coolant_thermal_conductivity = lexer_parse_dfloat();
    hxch.experimental_height = lexer_parse_dfloat();
    hxch.experimental_width = lexer_parse_dfloat();
    hxch.experimental_depth = lexer_parse_dfloat();
  }

  bool parsedRBrace = false;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 8, 14)) {
    char cccc_string[5];
    BOOLEAN id_p = lexer_parse_n_char_id_or_rbrace(cccc_string, 4);
    parsedRBrace = true;
    if (id_p){
      uINT32 chunk_type = cio_string_to_type(cccc_string);

      std::string cChunkType(cccc_string, 4);
      // this should be the only one here
      if (cChunkType == "udst") {
        lexer_parse_lbrace();
        std::vector<sCDI_CVDP> cvdpVec;
        std::vector<sCDI_EQDP> eqdpVec;
        while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4)) {
          if (cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_CVDP) {
            lexer_parse_lbrace();
            sCDI_CVDP cvdp;
            cvdp.cccc = CDI_CHUNK_TYPE_CVDP;
            cvdp.type = lexer_parse_int();
            cvdp.value = lexer_parse_dfloat();
            cvdpVec.push_back(cvdp);
            lexer_parse_rbrace();
          }
          else if (cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_EQDP) {
            lexer_parse_lbrace();
            sCDI_EQDP eqdp;
            eqdp.cccc = CDI_CHUNK_TYPE_EQDP;
            eqdp.type = lexer_parse_int();
            eqdp.var_name = lexer_parse_string();
            eqdp.var_name_length = (asINT32)strlen(eqdp.var_name);
            lexer_parse_rbrace();
          }
        }

        hxch.udstValues.n_cvdp = cvdpVec.size();
        hxch.udstValues.n_eqdp = eqdpVec.size();

        hxch.udstValues.cvdp = EXA_CALLOC_ARRAY(CDI_CVDP, hxch.udstValues.n_cvdp);
        hxch.udstValues.eqdp = EXA_CALLOC_ARRAY(CDI_EQDP, hxch.udstValues.n_eqdp);

        for (int i = 0; i < cvdpVec.size(); i++) {
          hxch.udstValues.cvdp[i] = EXA_CALLOC_STRUCT(CDI_CVDP);
          hxch.udstValues.cvdp[i]->cccc = cvdpVec[i].cccc;
          hxch.udstValues.cvdp[i]->type = cvdpVec[i].type;
          hxch.udstValues.cvdp[i]->value = cvdpVec[i].value;
        }

        for (int i = 0; i < eqdpVec.size(); i++) {
          hxch.udstValues.eqdp[i] = EXA_CALLOC_STRUCT(CDI_EQDP);
          hxch.udstValues.eqdp[i]->cccc = eqdpVec[i].cccc;
          hxch.udstValues.eqdp[i]->type = eqdpVec[i].type;
          hxch.udstValues.eqdp[i]->var_name = eqdpVec[i].var_name;
          hxch.udstValues.eqdp[i]->var_name_length = eqdpVec[i].var_name_length;
        }
      }
    }
    // if we don't have any udst values, set the values of cvdp/eqdp arrays to zero.
    else {
      hxch.udstValues.n_eqdp = 0;
      hxch.udstValues.n_cvdp = 0;
    }
  }

  // we need to add this flag here otherwise heatexchangers would parse the rbrace with the call to lexer_parse_n_char_id_or_rbrace
  // and then fail here since the token is already processed.
  if (!parsedRBrace)
    lexer_parse_rbrace();

  cdi_write_hxch(cdi_info, &hxch);
}

static VOID undump_amhx (CDI_INFO cdi_info)
{
  sCDI_AMHX amhx;

  amhx.cccc = CDI_CHUNK_TYPE_AMHX;

  undump_hxch_base (cdi_info, amhx);

  amhx.model_filename = lexer_parse_string();
  amhx.absolute_model_filename = lexer_parse_string();
  amhx.amesim_hx_name = lexer_parse_string();

  bool parsedRBrace = false;
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 8, 14)) {
    char cccc_string[5];
    BOOLEAN id_p = lexer_parse_n_char_id_or_rbrace(cccc_string, 4);
    parsedRBrace = true;
    if (id_p) {

      uINT32 chunk_type = cio_string_to_type(cccc_string);
      std::string cChunkType(cccc_string, 4);
      // this should be the only one here
      if (cChunkType == "udst") {
        lexer_parse_lbrace();
        std::vector<sCDI_CVDP> cvdpVec;
        std::vector<sCDI_EQDP> eqdpVec;
        while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4)) {
          if (cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_CVDP) {
            lexer_parse_lbrace();
            sCDI_CVDP cvdp;
            cvdp.cccc = CDI_CHUNK_TYPE_CVDP;
            cvdp.type = lexer_parse_int();
            cvdp.value = lexer_parse_dfloat();
            cvdpVec.push_back(cvdp);
            lexer_parse_rbrace();
          }
          else if (cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_EQDP) {
            lexer_parse_lbrace();
            sCDI_EQDP eqdp;
            eqdp.cccc = CDI_CHUNK_TYPE_EQDP;
            eqdp.type = lexer_parse_int();
            eqdp.var_name = lexer_parse_string();
            eqdp.var_name_length = (asINT32)strlen(eqdp.var_name);
            lexer_parse_rbrace();
          }
        }

        amhx.udstValues.n_cvdp = cvdpVec.size();
        amhx.udstValues.n_eqdp = eqdpVec.size();

        amhx.udstValues.cvdp = EXA_CALLOC_ARRAY(CDI_CVDP, amhx.udstValues.n_cvdp);
        amhx.udstValues.eqdp = EXA_CALLOC_ARRAY(CDI_EQDP, amhx.udstValues.n_eqdp);

        for (int i = 0; i < cvdpVec.size(); i++) {
          amhx.udstValues.cvdp[i] = EXA_CALLOC_STRUCT(CDI_CVDP);
          amhx.udstValues.cvdp[i]->cccc = cvdpVec[i].cccc;
          amhx.udstValues.cvdp[i]->type = cvdpVec[i].type;
          amhx.udstValues.cvdp[i]->value = cvdpVec[i].value;
        }

        for (int i = 0; i < eqdpVec.size(); i++) {
          amhx.udstValues.eqdp[i] = EXA_CALLOC_STRUCT(CDI_EQDP);
          amhx.udstValues.eqdp[i]->cccc = eqdpVec[i].cccc;
          amhx.udstValues.eqdp[i]->type = eqdpVec[i].type;
          amhx.udstValues.eqdp[i]->var_name = eqdpVec[i].var_name;
          amhx.udstValues.eqdp[i]->var_name_length = eqdpVec[i].var_name_length;
        }
      }
    }
    // if we don't have any udst values, set the values of cvdp/eqdp arrays to zero.
    else {
      amhx.udstValues.n_eqdp = 0;
      amhx.udstValues.n_cvdp = 0;
    }
  }

  // we need to add this flag here otherwise heatexchangers would parse the rbrace with the call to lexer_parse_n_char_id_or_rbrace
  // and then fail here since the token is already processed.
  if (!parsedRBrace)
    lexer_parse_rbrace();

  cdi_write_amhx(cdi_info, &amhx);
}


static VOID undump_bsrg (CDI_INFO cdi_info)
{
  sCDI_BSRG bsrg;

  bsrg.cccc = CDI_CHUNK_TYPE_BSRG;

  lexer_parse_lbrace();

  bsrg.mea_name = lexer_parse_string();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 21)) {
    bsrg.cdi_file_id = lexer_parse_int64();
  } else
    bsrg.cdi_file_id = -1;

  bsrg.region_index = lexer_parse_int();
  bsrg.facet_offset = lexer_parse_int();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 10)) {
    bsrg.spatial_mapping = lexer_parse_int();
    bsrg.transient_boundary_seeding = (lexer_parse_int() != 0);  
    bsrg.is_merged_to_input = (lexer_parse_int() != 0);
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 1)) 
    bsrg.seed_via_mks = (lexer_parse_int() != 0);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 13)) { 
    bsrg.coord_offset[0] = lexer_parse_dfloat();
    bsrg.coord_offset[1] = lexer_parse_dfloat();
    bsrg.coord_offset[2] = lexer_parse_dfloat();
  }
  lexer_parse_rbrace();

  cdi_write_bsrg(cdi_info, &bsrg);
}


static VOID undump_vpnt (CDI_INFO cdi_info)
{
  sCDI_VPNT vpnt;

  vpnt.cccc = CDI_CHUNK_TYPE_VPNT;

  lexer_parse_lbrace();

  vpnt.name = lexer_parse_string();
  vpnt.csys_index = lexer_parse_int();
  vpnt.camera_pos_unit = lexer_parse_string();
  vpnt.camera_pos[0] = lexer_parse_dfloat();
  vpnt.camera_pos[1] = lexer_parse_dfloat();
  vpnt.camera_pos[2] = lexer_parse_dfloat();
  vpnt.view_dir[0] = lexer_parse_dfloat();
  vpnt.view_dir[1] = lexer_parse_dfloat();
  vpnt.view_dir[2] = lexer_parse_dfloat();
  vpnt.up_dir[0] = lexer_parse_dfloat();
  vpnt.up_dir[1] = lexer_parse_dfloat();
  vpnt.up_dir[2] = lexer_parse_dfloat();
  vpnt.projection_type = lexer_parse_int();
  vpnt.orthographic_fov_unit = lexer_parse_string();
  vpnt.orthographic_fov = lexer_parse_dfloat();
  vpnt.perspective_fov = lexer_parse_dfloat();
  vpnt.spin_center_unit = lexer_parse_string();
  vpnt.spin_center[0] = lexer_parse_dfloat();
  vpnt.spin_center[1] = lexer_parse_dfloat();
  vpnt.spin_center[2] = lexer_parse_dfloat();

  lexer_parse_rbrace();

  cdi_write_vpnt(cdi_info, &vpnt);
}


static VOID undump_cdsr (CDI_INFO cdi_info)
{
  sCDI_CDSR cdsr;

  lexer_parse_lbrace();

  cdsr.name = lexer_parse_string();

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 7))
    cdsr.part_name = lexer_parse_string();
  else 
    cdsr.part_name = EXA_STRDUP(cdsr.name);

  cdsr.type = lexer_parse_int();
  cdsr.tool = lexer_parse_int();
  cdsr.flags = lexer_parse_int();
  cdsr.table_csys_index = lexer_parse_int();
  cdsr.medium_csys_index = lexer_parse_int();
  cdsr.inlet_face_index = lexer_parse_int();
  cdsr.outlet_face_index = lexer_parse_int();
  
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 19)) {
    cdsr.top_exchanger_face_index = lexer_parse_int();
    cdsr.coolant_entry_face_index = lexer_parse_int();
  } else {
    cdsr.top_exchanger_face_index = -1;
    cdsr.coolant_entry_face_index = -1;
  }
  
  cdsr.inlet_facet_offset = lexer_parse_int();
  cdsr.outlet_facet_offset = lexer_parse_int();
  cdsr.inlet_meas_index = lexer_parse_int();
  cdsr.outlet_meas_index = lexer_parse_int();
  if ((cdsr.flags & CDI_HXCH_HAS_HEAT_GEN_MEAS) != 0)
    cdsr.heat_gen_meas_index = lexer_parse_int();
  cdsr.table_index = lexer_parse_int();
  cdsr.adiabatic_index = lexer_parse_int();
  cdsr.medium_index = lexer_parse_int();
  cdsr.n_passes = lexer_parse_int();
  cdsr.n_tubes = new cdiINT32[cdsr.n_passes];
  ccDOTIMES(i, cdsr.n_passes)
    cdsr.n_tubes[i] = lexer_parse_int();
  cdsr.x_len = lexer_parse_dfloat();
  cdsr.y_len = lexer_parse_dfloat();
  cdsr.z_len = lexer_parse_dfloat();
  cdsr.mass_flow_rate = lexer_parse_dfloat();
  cdsr.heat_rejection = lexer_parse_dfloat();
  cdsr.entry_temp = lexer_parse_dfloat();
  cdsr.entry_pressure = lexer_parse_dfloat();
  cdsr.pressure_drop = lexer_parse_dfloat();
  cdsr.experiment_exit_temp = lexer_parse_dfloat();
  cdsr.min_air_flow = lexer_parse_dfloat();
  cdsr.max_air_flow = lexer_parse_dfloat();
  cdsr.kc_coeff = lexer_parse_dfloat();
  cdsr.alpha_coeff = lexer_parse_dfloat();
  cdsr.d_coeff = lexer_parse_dfloat();
  cdsr.data_string = lexer_parse_string();

  bool parsedRBrace = false;
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 8, 14)) {
    char cccc_string[5];
    BOOLEAN id_p = lexer_parse_n_char_id_or_rbrace(cccc_string, 4);
    parsedRBrace = true;
    if (id_p){
      uINT32 chunk_type = cio_string_to_type(cccc_string);
      std::string cChunkType(cccc_string, 4);
      // this should be the only one here
      if (cChunkType == "udst") {
        lexer_parse_lbrace();
        std::vector<sCDI_CVDP> cvdpVec;
        std::vector<sCDI_EQDP> eqdpVec;
        while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4)) {
          if (cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_CVDP) {
            lexer_parse_lbrace();
            sCDI_CVDP cvdp;
            cvdp.cccc = CDI_CHUNK_TYPE_CVDP;
            cvdp.type = lexer_parse_int();
            cvdp.value = lexer_parse_dfloat();
            cvdpVec.push_back(cvdp);
            lexer_parse_rbrace();
          }
          else if (cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_EQDP) {
            lexer_parse_lbrace();
            sCDI_EQDP eqdp;
            eqdp.cccc = CDI_CHUNK_TYPE_EQDP;
            eqdp.type = lexer_parse_int();
            eqdp.var_name = lexer_parse_string();
            eqdp.var_name_length = (asINT32)strlen(eqdp.var_name);
            lexer_parse_rbrace();
          }
        }

        cdsr.udstValues.n_cvdp = cvdpVec.size();
        cdsr.udstValues.n_eqdp = eqdpVec.size();

        cdsr.udstValues.cvdp = EXA_CALLOC_ARRAY(CDI_CVDP, cdsr.udstValues.n_cvdp);
        cdsr.udstValues.eqdp = EXA_CALLOC_ARRAY(CDI_EQDP, cdsr.udstValues.n_eqdp);

        for (int i = 0; i < cvdpVec.size(); i++) {
          cdsr.udstValues.cvdp[i] = EXA_CALLOC_STRUCT(CDI_CVDP);
          cdsr.udstValues.cvdp[i]->cccc = cvdpVec[i].cccc;
          cdsr.udstValues.cvdp[i]->type = cvdpVec[i].type;
          cdsr.udstValues.cvdp[i]->value = cvdpVec[i].value;
        }

        for (int i = 0; i < eqdpVec.size(); i++) {
          cdsr.udstValues.eqdp[i] = EXA_CALLOC_STRUCT(CDI_EQDP);
          cdsr.udstValues.eqdp[i]->cccc = eqdpVec[i].cccc;
          cdsr.udstValues.eqdp[i]->type = eqdpVec[i].type;
          cdsr.udstValues.eqdp[i]->var_name = eqdpVec[i].var_name;
          cdsr.udstValues.eqdp[i]->var_name_length = eqdpVec[i].var_name_length;
        }
      }
    }
    // if we don't have any udst values, set the values of cvdp/eqdp arrays to zero.
    else {
      cdsr.udstValues.n_eqdp = 0;
      cdsr.udstValues.n_cvdp = 0;
    }
  }

  // we need to add this flag here otherwise heatexchangers would parse the rbrace with the call to lexer_parse_n_char_id_or_rbrace
  // and then fail here since the token is already processed.
  if(!parsedRBrace)
    lexer_parse_rbrace();

  WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_CDSR)
    cdi_write_condenser(cdi_info, &cdsr);
}

static VOID undump_ivdp (CDI_INFO cdi_info)
{
  sCDI_IVDP ivdp;

  lexer_parse_lbrace();

  ivdp.cccc = CDI_CHUNK_TYPE_IVDP;
  ivdp.type = lexer_parse_int();
  ivdp.value = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_ivdp(cdi_info, &ivdp);

  return;
}

static VOID undump_cvdp (CDI_INFO cdi_info, sCDI_CVDP &cvdp)
{
  lexer_parse_lbrace();
  cvdp.cccc = CDI_CHUNK_TYPE_CVDP;
  cvdp.type = lexer_parse_int();
  cvdp.value = lexer_parse_dfloat();
  lexer_parse_rbrace();
}

static VOID undump_cvdp (CDI_INFO cdi_info)
{
  if (undump_encoded64_chunk(cdi_info, CDI_CHUNK_TYPE_CVDP))
    return;

  sCDI_CVDP cvdp;
  undump_cvdp(cdi_info, cvdp);
  cdi_write_cvdp(cdi_info, &cvdp);
  return;
}

static VOID undump_bsdp (CDI_INFO cdi_info)
{
  sCDI_BSDP bsdp;

  lexer_parse_lbrace();

  bsdp.cccc = CDI_CHUNK_TYPE_BSDP;
  bsdp.type = lexer_parse_int();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 10))
    bsdp.value = lexer_parse_dfloat();

  lexer_parse_rbrace();

  cdi_write_bsdp(cdi_info, &bsdp);
}

static VOID undump_pnts (CDI_INFO cdi_info, CDI_PNTS pnts)
{
  lexer_parse_lbrace();

  pnts->cccc = CDI_CHUNK_TYPE_PNTS;
  pnts->dim = lexer_parse_int();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 14)) { 
    pnts->is_closed = lexer_parse_int();
  } else {
    pnts->is_closed = -1;
  }
  pnts->num_points = lexer_parse_int();
  if (pnts->num_points == 0 || pnts->dim <= 0)
    pnts->points = NULL;
  else {
    pnts->points = EXA_MALLOC_ARRAY(double, pnts->num_points*pnts->dim);
    ccDOTIMES(i, pnts->num_points*pnts->dim) {
      pnts->points[i] = lexer_parse_dfloat();
    }
  }

  lexer_parse_rbrace();
}

static VOID undump_pnts (CDI_INFO cdi_info)
{
  sCDI_PNTS pnts;
  
  undump_pnts(cdi_info, &pnts);
  
  cdi_write_pnts(cdi_info, &pnts);

  if (pnts.num_points > 0)
    exa_free(pnts.points);
  
  return;
}

static void UndumpVectorOfPoints(CDI_INFO cdi_info, sCDI_VECOF_PNTS* pPoints)
{
  lexer_parse_lbrace();

  pPoints->dim = lexer_parse_int();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 14)) {
    pPoints->is_closed = lexer_parse_int();
  }
  else {
    pPoints->is_closed = -1;
  }
  cdiINT32 num_points = lexer_parse_int();
  cdiINT32 numValues = num_points * pPoints->dim;
  if (numValues > 0) {
    pPoints->points.resize(numValues);
    ccDOTIMES(i, numValues) {
      pPoints->points[i] = lexer_parse_dfloat();
    }
  }

  lexer_parse_rbrace();
}

static VOID undump_eqdp (CDI_INFO cdi_info)
{
  sCDI_EQDP eqdp;

  lexer_parse_lbrace();

  eqdp.cccc = CDI_CHUNK_TYPE_EQDP;
  eqdp.type = lexer_parse_int();
  eqdp.var_name = lexer_parse_string();
  eqdp.var_name_length = (asINT32) strlen(eqdp.var_name);

  lexer_parse_rbrace();

  cdi_write_eqdp(cdi_info, &eqdp);

  return;
}

static void
undump_rgns_helper(sCDI_RGNS* rgns)
{
  lexer_parse_lbrace();

  rgns->cccc = CDI_CHUNK_TYPE_RGNS;
  rgns->n_region = lexer_parse_int();
  rgns->region = (asINT32 *)exa_malloc(rgns->n_region * sizeof(asINT32),
				      "undump_rgns",
				      "a region array");
  DOTIMES(i, rgns->n_region, {
    rgns->region[i] = lexer_parse_int();
  });

  lexer_parse_rbrace();
}

static void
undump_rgns(CDI_INFO cdi_info)
{
  sCDI_RGNS rgns;
  undump_rgns_helper(&rgns);
  cdi_write_rgns(cdi_info, &rgns);
  exa_free(rgns.region);
}

static void
undump_rgns_with_id(std::vector<cdiINT32>& regionsVec)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_RGNS);
  sCDI_RGNS rgns;
  undump_rgns_helper(&rgns);
  regionsVec = std::vector<cdiINT32>(rgns.region, rgns.region + rgns.n_region);
  if (rgns.n_region > 0)
    exa_free(rgns.region);
}


static VOID undump_facd (CDI_INFO cdi_info, CDI_FACD facd)
{
  facd->face_name = lexer_parse_string();
  facd->n_face_name_char = (asINT32) strlen(facd->face_name);
  facd->front_offset = lexer_parse_dfloat();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 7)) { // separate front/back offsets, region index, first introduced in 3.7
    facd->back_offset = lexer_parse_dfloat();
    facd->region_index = lexer_parse_int();
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 18)) // facet side constraint introduced in 3.18
      facd->facet_side = lexer_parse_int();
    else
      facd->facet_side = 0;
  }
  else {
    facd->back_offset = facd->front_offset;
    facd->region_index = -1;
    facd->facet_side = 0;
  }
}

static VOID undump_fdlt (CDI_INFO cdi_info)
{
  sCDI_FDLT fdlt;

  lexer_parse_lbrace();

  fdlt.cccc = CDI_CHUNK_TYPE_FDLT;
  fdlt.n_facd = lexer_parse_int();
  fdlt.facd = (CDI_FACD)exa_malloc(fdlt.n_facd * sizeof(sCDI_FACD),
				   "undump_fdlt",
				   "facd array");
  DOTIMES(i, fdlt.n_facd, {
    undump_facd(cdi_info, &(fdlt.facd[i]));
  });

  lexer_parse_rbrace();

  cdi_write_fdlt(cdi_info, &fdlt);

  cdi_clear_fdlt(&fdlt);

  return;
}

static void undump_parm_with_id(sCDI_PARM* pParm);
static std::vector<idFLOAT> undump_dbls_with_id(idFLOAT* values);

static sCDI_FARG undump_farg (CDI_INFO cdi_info)
{
  sCDI_FARG farg;
  undump_enum_with_id(&(farg.type));
  undump_parm_with_id(&(farg.min_param));
  undump_parm_with_id(&(farg.max_param));
  undump_enum_with_id(&(farg.voxel_size_via));
  undump_dbls_with_id((idFLOAT*)&farg.resolution);
  undump_int_with_id(&farg.scale);
  undump_int_with_id(&farg.thickness);
  return farg;
}

static VOID undump_gfar (CDI_INFO cdi_info)
{
  sCDI_GFAR gfar;

  lexer_parse_lbrace();

  gfar.cccc = CDI_CHUNK_TYPE_GFAR;

  gfar.n_farg = lexer_parse_int();
  char cccc_string[5];
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4)  && cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_FARG) {
     lexer_parse_lbrace();
     gfar.farg.push_back(undump_farg(cdi_info));
     lexer_parse_rbrace();
  }

  cdi_write_gfar(cdi_info, &gfar);

  return;
}

static VOID undump_gapd (CDI_INFO cdi_info)
{
  sCDI_GAPD gapd;

  lexer_parse_lbrace();

  gapd.cccc = CDI_CHUNK_TYPE_GAPD;

  undump_int_with_id(&(gapd.n_voxels));
  undump_parm_with_id(&(gapd.gap_size));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 29))
    undump_int_with_id(&(gapd.scale));
  lexer_parse_rbrace();

  cdi_write_gapd(cdi_info, &gapd);

  return;
}

static VOID undump_gscc (CDI_INFO cdi_info)
{
  sCDI_GSCC gscc;

  lexer_parse_lbrace();

  gscc.cccc = CDI_CHUNK_TYPE_GSCC;
  gscc.value = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_gscc(cdi_info, &gscc);

  return;
}

static VOID undump_gsep (CDI_INFO cdi_info)
{
  sCDI_GSEP gsep;

  lexer_parse_lbrace();

  gsep.cccc = CDI_CHUNK_TYPE_GSEP;
  gsep.scale = lexer_parse_int();
  gsep.separation_factor = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_gsep(cdi_info, &gsep);

  return;
}

static VOID undump_grdf (CDI_INFO cdi_info)
{
  sCDI_GRDF grdf;

  lexer_parse_lbrace();

  grdf.cccc = CDI_CHUNK_TYPE_GRDF;
  grdf.resolution_precedence = lexer_parse_int();
  grdf.separation_factor = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_grdf(cdi_info, &grdf);

  return;
}

static VOID undump_prdf (CDI_INFO cdi_info)
{
  sCDI_PRDF prdf;

  lexer_parse_lbrace();

  prdf.cccc = CDI_CHUNK_TYPE_PRDF;
  prdf.default_scale_precedence = lexer_parse_int();
  prdf.default_physics_precedence = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_prdf(cdi_info, &prdf);

  return;
}

static VOID undump_prec (CDI_INFO cdi_info)
{
  sCDI_PREC prec;

  lexer_parse_lbrace();

  prec.cccc = CDI_CHUNK_TYPE_PREC;
  prec.precedence = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_prec(cdi_info, &prec);

  return;
}


static VOID undump_simv (CDI_INFO cdi_info)
{
  sCDI_SIMV simv;

  lexer_parse_lbrace();

  simv.cccc = CDI_CHUNK_TYPE_SIMV;
  simv.max[0] = lexer_parse_dfloat();
  simv.max[1] = lexer_parse_dfloat();
  simv.max[2] = lexer_parse_dfloat();
  simv.flags = lexer_parse_int();
  simv.xform[0][0] = lexer_parse_dfloat();
  simv.xform[0][1] = lexer_parse_dfloat();
  simv.xform[0][2] = lexer_parse_dfloat();
  simv.xform[0][3] = lexer_parse_dfloat();
  simv.xform[1][0] = lexer_parse_dfloat();
  simv.xform[1][1] = lexer_parse_dfloat();
  simv.xform[1][2] = lexer_parse_dfloat();
  simv.xform[1][3] = lexer_parse_dfloat();
  simv.xform[2][0] = lexer_parse_dfloat();
  simv.xform[2][1] = lexer_parse_dfloat();
  simv.xform[2][2] = lexer_parse_dfloat();
  simv.xform[2][3] = lexer_parse_dfloat();
  simv.xform[3][0] = lexer_parse_dfloat();
  simv.xform[3][1] = lexer_parse_dfloat();
  simv.xform[3][2] = lexer_parse_dfloat();
  simv.xform[3][3] = lexer_parse_dfloat();

  lexer_parse_rbrace();

  cdi_write_simv(cdi_info, &simv);

  return;
}

static VOID undump_thma (CDI_INFO cdi_info)
{
  sCDI_THMA thma;

  lexer_parse_lbrace();

  thma.cccc = CDI_CHUNK_TYPE_THMA;
  thma.start_time = lexer_parse_int();
  thma.end_time = lexer_parse_int();
  thma.period = lexer_parse_int();
  thma.interval = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_thma(cdi_info, &thma);

  return;
}

static VOID undump_symp (CDI_INFO cdi_info)
{
  sCDI_SYMP symp;

  lexer_parse_lbrace();

  symp.cccc = CDI_CHUNK_TYPE_SYMP;

  symp.axes = lexer_parse_int();

  symp.min[0] = lexer_parse_int();
  symp.min[1] = lexer_parse_int();
  symp.min[2] = lexer_parse_int();

  symp.max[0] = lexer_parse_int();
  symp.max[1] = lexer_parse_int();
  symp.max[2] = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_symp(cdi_info, &symp);

  return;
}

static VOID undump_unit (CDI_INFO cdi_info)
{
  sCDI_UNIT unit;

  lexer_parse_lbrace();

  unit.cccc = CDI_CHUNK_TYPE_UNIT;
  unit.kilos_per_particle       = lexer_parse_dfloat();
  unit.meters_per_cell          = lexer_parse_dfloat();
  unit.seconds_per_timestep     = lexer_parse_dfloat();
  unit.kelvins_per_lattice_temp = lexer_parse_dfloat();
  unit.n_user_units = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_unit(cdi_info, &unit);

  return;
}

static VOID undump_uunt (CDI_INFO cdi_info)
{
  sCDI_UUNT uunt;

  lexer_parse_lbrace();

  uunt.cccc = CDI_CHUNK_TYPE_UUNT;
  uunt.unit_type = (CDI_UNIT_TYPES)lexer_parse_int();
  uunt.mass_exponent = lexer_parse_int();
  uunt.length_exponent = lexer_parse_int();
  uunt.time_exponent = lexer_parse_int();
  uunt.temp_exponent = lexer_parse_int();
  uunt.scale = lexer_parse_dfloat();
  uunt.offset = lexer_parse_dfloat();
  uunt.unit_name = lexer_parse_string();
  uunt.n_char = (asINT32) strlen(uunt.unit_name);

  lexer_parse_rbrace();

  cdi_write_uunt(cdi_info, &uunt);

  exa_free(uunt.unit_name);

  return;
}

static VOID undump_cprp (CDI_INFO cdi_info)
{
  sCDI_CPRP cprp;

  lexer_parse_lbrace();

  cprp.cccc = CDI_CHUNK_TYPE_CPRP;
  cprp.mass_exponent = lexer_parse_int();
  cprp.length_exponent = lexer_parse_int();
  cprp.time_exponent = lexer_parse_int();
  cprp.temp_exponent = lexer_parse_int();
  cprp.char_prop_name = lexer_parse_string();
  cprp.n_char = (asINT32) strlen(cprp.char_prop_name);
  cprp.value = lexer_parse_dfloat();

  lexer_parse_rbrace();

  cdi_write_cprp(cdi_info, &cprp);

  exa_free(cprp.char_prop_name);

  return;
}

static void undump_csph(CDI_INFO cdi_info)
{
  sCDI_CSPH csph;

  lexer_parse_lbrace();
  
  csph.time_rel_solver = static_cast<eCDI_COUPLED_SOLVER::Enum>(lexer_parse_int());
  csph.start = lexer_parse_int();
  csph.time_coupling = static_cast<eCDI_TIME_COUPLING_SCHEME::Enum>(lexer_parse_int());
  csph.therm_time_ratio = lexer_parse_dfloat();
  csph.flow_duration = lexer_parse_int();
  csph.flow_avg_interval = lexer_parse_int();
  csph.conduction_duration = lexer_parse_int();
  csph.conduction_avg_interval = lexer_parse_int();
  csph.frozen_solver = static_cast<eCDI_COUPLED_SOLVER::Enum>(lexer_parse_int());
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 7)) {
    if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11))
      cdiINT32 old_radiation_update_start_time = lexer_parse_int();
    csph.radiation_update_ratio = lexer_parse_dfloat();
  }

  lexer_parse_rbrace();

  cdi_write_csph(cdi_info, &csph);
}

static VOID undump_ghdr (CDI_INFO cdi_info)
{
  sCDI_GHDR ghdr;
  ghdr.include_second_block = false;
  ghdr.include_third_block = false;

  lexer_parse_lbrace();

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 11)) {
    ghdr.sim_duration_via = static_cast<eCDI_SIM_DURATION_VIA::Enum>(lexer_parse_int());
    cdiINT32 numMonitors = lexer_parse_int();
    for (cdiINT32 im = 0; im < numMonitors; im++) {
      ghdr.monitors.push_back(lexer_parse_int());
    }
    ghdr.n_timesteps_after_init_trans = lexer_parse_int();
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 1)) {
      ghdr.has_average_mme_window = lexer_parse_int();
      ghdr.local_vel_freeze = lexer_parse_int();
    }
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 27))
      ghdr.m_temperatureDependentGamma = lexer_parse_int();
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 28))
      ghdr.m_coolingAirOpt = static_cast<eCDI_COOLING_AIR_LEAKAGE_OPTION::Enum>(lexer_parse_int());
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 1))
      ghdr.timesteps_rel_solver = static_cast<eCDI_COUPLED_SOLVER::Enum>(lexer_parse_int());
  }
  ghdr.cccc = CDI_CHUNK_TYPE_GHDR;
  ghdr.n_timesteps = lexer_parse_int();
  ghdr.n_processors = lexer_parse_int();
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 10))
    ghdr.liquidMaterialType = lexer_parse_int();

  // Read an int or rbrace, integer implies Momentum Freeze Option.
  int freezeMomentumField;
  if (lexer_parse_int_or_rbrace(&freezeMomentumField)) {
    ghdr.freeze_momentum_field = freezeMomentumField;
    ghdr.physical_time_scaling = lexer_parse_dfloat();
    ghdr.include_second_block = true;

    if (lexer_parse_int_or_rbrace(&ghdr.numParticleEmitters)) {
      ghdr.numParticleMaterials = lexer_parse_int();
      ghdr.numParticleModelingVars = lexer_parse_int();
      ghdr.numUDScalars = lexer_parse_int();
      ghdr.numUDSVars = lexer_parse_int();
      ghdr.num5gFluidComponents = lexer_parse_int();
      ghdr.num5gVars = lexer_parse_int();
      ghdr.include_third_block = true;
      lexer_parse_rbrace();
    }
  }

  cdi_write_ghdr(cdi_info, &ghdr);

  return;
}

static VOID undump_cpnt (CDI_INFO cdi_info)
{
  sCDI_CPNT cpnt;

  lexer_parse_lbrace();

  cpnt.cccc = CDI_CHUNK_TYPE_CPNT;
  cpnt.start = lexer_parse_int();
  cpnt.end   = lexer_parse_int();
  cpnt.freq  = lexer_parse_int();

  lexer_parse_rbrace();

  cdi_write_cpnt(cdi_info, &cpnt);

  return;
}

static VOID undump_kesc(CDI_INFO cdi_info)//kEps_SuperCycle
{
  sCDI_KESC kesc;

  lexer_parse_lbrace();
  undump_bool_with_id(&(kesc.bKesc_activated));
  if(kesc.bKesc_activated)
    undump_int_with_id(&(kesc.super_cycling_factor));
  lexer_parse_rbrace();
  cdi_write_kesc(cdi_info, kesc);

  return;
}

static VOID undump_ment (CDI_INFO cdi_info) {
    sCDI_MENT ment;

    lexer_parse_lbrace();

    ment.cccc = CDI_CHUNK_TYPE_MENT;
    ment.options = lexer_parse_int();

    lexer_parse_rbrace();

    cdi_write_ment(cdi_info,&ment);

    return;
}

static VOID undump_prbe (CDI_INFO cdi_info) {
    sCDI_PRBE prbe;

    lexer_parse_lbrace();

    prbe.cccc = CDI_CHUNK_TYPE_PRBE;
    prbe.x    = lexer_parse_dfloat();
    prbe.y    = lexer_parse_dfloat();
    prbe.z    = lexer_parse_dfloat();
    prbe.diam = lexer_parse_dfloat();

    lexer_parse_rbrace();

    cdi_write_prbe(cdi_info,&prbe);

    return;
}

static void undump_parm_helper(sCDI_PARM* parm)
{
  lexer_parse_lbrace();
  parm->cccc = CDI_CHUNK_TYPE_PARM;
  int defaulted = lexer_parse_int();
  parm->defaulted = (defaulted == 1);
  int isVariable = lexer_parse_int();
  parm->isEqVariable = (isVariable == 1);
  parm->isDataCurve = (isVariable == 2);
  parm->variableName= lexer_parse_std_string();
  parm->value = lexer_parse_dfloat();
  parm->preferredUnit = lexer_parse_std_string();
  lexer_parse_rbrace();
}

static VOID undump_parm(CDI_INFO cdi_info)
{
  sCDI_PARM parm;
  undump_parm_helper(&parm);
  cdi_write_parm(cdi_info, &parm);
}

static void undump_parm_with_id(sCDI_PARM* pParm)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_PARM);
  undump_parm_helper(pParm);
}


static void undump_enum_helper(sINT32* value)
{
  lexer_parse_lbrace();
  *value = lexer_parse_int();
  lexer_parse_rbrace();
}

static VOID undump_enum(CDI_INFO cdi_info)
{
  sINT32 value;
  undump_enum_helper(&value);
  cdi_write_enum(cdi_info, value);
}

template <typename T>
static void undump_enum_with_id(T* value)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_ENUM);
  sINT32 intVal;
  undump_enum_helper(&intVal);
  *value = (T)intVal;
}


static std::vector<idFLOAT> undump_dbls_helper(idFLOAT* values,
                               cdiINT32 &count)
{
  lexer_parse_lbrace();
  
  idFLOAT value;
  std::vector<idFLOAT> vec;

  // read all floating point numbers until a right brace is encountered
  while (lexer_parse_dfloat_or_rbrace(value)) {
    vec.push_back(value);  
  }
  
  // fill values and count 
  count = vec.size();
  if (!values) values = EXA_CALLOC_ARRAY(idFLOAT, count);
  std::copy(vec.begin(), vec.end(), values);

  return vec;
}

static std::vector<idFLOAT> undump_dbls(CDI_INFO cdi_info)
{
  cdiINT32 count = 0;
  idFLOAT* values = NULL;
  std::vector<idFLOAT> vec = undump_dbls_helper(values, count);
  cdi_write_dbls(cdi_info, values, count);
  cdi_destroy_dbls(values);

  return vec;
}

// values should be NULL to avoid memory leak
static std::vector<idFLOAT> undump_dbls_with_id(idFLOAT* values)
{
  cdiINT32 count = 0;
  lexer_parse_specific_id(CDI_CHUNK_TYPE_DBLS);
  return undump_dbls_helper(values, count);
}

static VOID undump_dble_helper(idFLOAT* value)
{
  lexer_parse_lbrace();
  *value = lexer_parse_dfloat();
  lexer_parse_rbrace();
}

static VOID undump_dble(CDI_INFO cdi_info)
{
  idFLOAT value;
  undump_dble_helper(&value);
  cdi_write_dble(cdi_info, value);
}

static void undump_dble_with_id(idFLOAT* value)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_DBLE);
  undump_dble_helper(value);
}

static VOID undump_flst_helper (CDI_FLST flst)
{
  lexer_parse_lbrace();
  
  flst->cccc = CDI_CHUNK_TYPE_FLST;
  flst->n_face = lexer_parse_int();
  flst->face = (asINT32 *)exa_malloc(flst->n_face*sizeof(asINT32),
                                    "undump_flst",
                                    "a face array");
  DOTIMES(i,flst->n_face, {
    flst->face[i] = lexer_parse_int();
  });
  
  lexer_parse_rbrace();
}

static VOID undump_flst (CDI_INFO cdi_info)
{
  sCDI_FLST flst;
  undump_flst_helper(&flst);
  cdi_write_flst(cdi_info, &flst);
  if (flst.n_face > 0) exa_free(flst.face);
}

static void undump_flst_with_id(std::vector<cdiINT32> &values)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_FLST);
  sCDI_FLST flst;
  undump_flst_helper(&flst);
  values = std::vector<int>(flst.face, flst.face + flst.n_face);
  if (flst.n_face > 0) exa_free(flst.face);
}

// Face and Part Geometry References Containers
VOID cCDI_GEOMETRY_REF::Undump(CDI_INFO cdi_info)
{
  cdi_read_gmrf(this, cdi_get_major_version(cdi_info), cdi_get_minor_version(cdi_info));
}

static VOID undump_gmrf(CDI_INFO cdi_info)
{
  cCDI_GEOMETRY_REF gmrf(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment);
  gmrf.Undump(cdi_info);
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_GMRF) {
      gmrf.WriteToCDI(cdi_info);
    }
  }
}

static void undump_gmrf_with_id(CDI_INFO cdi_info, cCDI_GEOMETRY_REF* gmrf)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_GMRF);
  gmrf->Undump(cdi_info);
}

VOID cCDI_GEOM_SELECTION_TREE::Undump(CDI_INFO cdi_info)
{
  cdi_read_geos(this, cdi_get_major_version(cdi_info), cdi_get_minor_version(cdi_info));
}

static VOID undump_geos(CDI_INFO cdi_info)
{
  cCDI_GEOM_SELECTION_TREE geos(-1, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment);
  geos.Undump(cdi_info);
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_GEOS) {
      geos.WriteToCDI(cdi_info);
    }
  }
}

static void undump_geos_with_id(CDI_INFO cdi_info, cCDI_GEOM_SELECTION_TREE* geos)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_GEOS);
  geos->Undump(cdi_info);
}

static VOID undump_wipr (CDI_INFO info)
{
  sCDI_WIPR wipr;
  lexer_parse_lbrace();

  // name
  undump_namestr_with_id(&(wipr.name));

  // wiper blade
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(info))
    undump_flst_with_id(wipr.wiper_blade_geoms.m_selections.face_list);
  else
    undump_geos_with_id(info, &(wipr.wiper_blade_geoms));
  // wiped surface
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(info))
    undump_flst_with_id(wipr.wiped_surface_geoms.m_selections.face_list);
  else
    undump_geos_with_id(info, &(wipr.wiped_surface_geoms));

  // wiper axis
  undump_dbls_with_id((idFLOAT*)wipr.wiper_axis_origin);
  undump_dbls_with_id((idFLOAT*)wipr.wiper_axis_dir);

  // end points
  undump_dbls_with_id((idFLOAT*)wipr.inner_endpoint);
  undump_dbls_with_id((idFLOAT*)wipr.outer_endpoint);

  // wiper type
  undump_enum_with_id(&(wipr.wiper_type));

  // angles
  undump_parm_with_id(&(wipr.import_angle)); 
  undump_parm_with_id(&(wipr.start_angle)); 
  undump_parm_with_id(&(wipr.end_angle)); 

  // time
  undump_parm_with_id(&(wipr.stroke_duration)); 
  undump_parm_with_id(&(wipr.delay)); 

  if (CDI_INFO_VERSION_AT_LEAST(info, 7, 22)) {
    undump_enum_with_id(&(wipr.start_via));
    undump_parm_with_id(&(wipr.initial_delay));
    int monitorSize = lexer_parse_int();
    ccDOTIMES(i, monitorSize)
      wipr.monitors.push_back(lexer_parse_int());
    undump_int_with_id(&(wipr.emitter_index));
    undump_parm_with_id(&(wipr.emitter_delay));
    undump_parm_with_id(&(wipr.num_strokes));
  }
  else if (CDI_INFO_VERSION_AT_LEAST(info, 4, 15))
    undump_parm_with_id(&(wipr.initial_delay));

  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_WIPR) {
      cdi_write_wipr(info, &wipr);
    }
  }
}

static VOID undump_rgct (CDI_INFO cdi_info)
{
  sCDI_RGCT rgct;

  lexer_parse_lbrace();

  rgct.n_regions = lexer_parse_int();

  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_RGCT) {
      cdi_write_case_region_count(cdi_info, &rgct);
    }
  }

  return;
}

static VOID undump_rgnn (CDI_INFO cdi_info)
{
  sCDI_RGNN rgnn;

  lexer_parse_lbrace();

  rgnn.n_vertices = lexer_parse_int();
  rgnn.n_faces = lexer_parse_int();
  rgnn.n_facets = lexer_parse_int();
  rgnn.n_vertex_refs = lexer_parse_int();

  lexer_parse_rbrace();

  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_RGNN) {
      cdi_write_region_numeration(cdi_info, &rgnn);
    }
  }

  return;
}

static VOID undump_rgdp (CDI_INFO cdi_info)
{
  sCDI_RGDP rgdp;

  lexer_parse_lbrace();

  rgdp.color = lexer_parse_string();
  rgdp.material_index = lexer_parse_int();
  rgdp.display_mode = (CDI_DISPLAY_MODE) lexer_parse_int();

  lexer_parse_rbrace();

  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_RGDP) {
      cdi_write_region_display_properties(cdi_info, &rgdp);
    }
  }

  return;
}

static VOID undump_prtt (CDI_INFO cdi_info)
{
  sCDI_PRTT prtt;
  
  lexer_parse_lbrace();
  
  prtt.partition_type = (CDI_PARTITION_TYPE)lexer_parse_int();
  
  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_PRTT) {
      cdi_write_partition_type(cdi_info, &prtt);
    }
  }
  
  return;
}

static VOID undump_sgid (CDI_INFO cdi_info)
{
  sCDI_SGID sgid;
  
  lexer_parse_lbrace();
  
  sgid.segment_index = lexer_parse_int();
  
  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SGID) {
      cdi_write_segment_index(cdi_info, &sgid);
    }
  }
  
  return;
}

//Parse ice accreation parameters.
static VOID undump_accr(CDI_INFO info)
{
  sCDI_ACCR accr;
  accr.cccc = CDI_CHUNK_TYPE_ACCR;
  lexer_parse_lbrace();
  undump_parm_with_id(&(accr.m_total_accretion_duration));
  undump_parm_with_id(&(accr.m_liquid_water_content));
  undump_int_with_id(&(accr.m_material_index));
  undump_parm_with_id(&(accr.m_accretion_material_density));
  undump_int_with_id(&(accr.m_total_num_of_accretion_layers));
  undump_parm_with_id(&(accr.m_collection_duration_per_layer));
  undump_parm_with_id(&(accr.m_accretion_acceleration_factor));
  undump_int_with_id(&(accr.m_meas_window_index));
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8, 11>(info)) {
    undump_dble_with_id(&(accr.m_accretion_scale_factor));
  }
  undump_flst_with_id(accr.m_surface_ids);
  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_ACCR) {
      accr.cdi_write(info);
    }
  }
}

static void undump_box_ (CDI_INFO info, sCDI_BOX_* box_)
{
  lexer_parse_lbrace();
  box_->cccc = CDI_CHUNK_TYPE_BOX_;
  undump_rgns_with_id(box_->region.regions);
  lexer_parse_specific_id(CDI_CHUNK_TYPE_CVDP);
  undump_cvdp(info, box_->csys);
  lexer_parse_specific_id(CDI_CHUNK_TYPE_BBOX);
  undump_bbox(info, box_->bbox);
  lexer_parse_rbrace();
}

static VOID undump_pglb (CDI_INFO info)
{
  sCDI_PGLB pglb;
  lexer_parse_lbrace();

  pglb.cccc = CDI_CHUNK_TYPE_PGLB;
  lexer_parse_specific_id(CDI_CHUNK_TYPE_BOX_);
  undump_box_(info, &(pglb.dispersion_box));

  undump_parm_with_id(&(pglb.gravity_csys));
  for(int i = 0; i < 3; i++)
    undump_parm_with_id(pglb.gravity + i);
  undump_parm_with_id(&(pglb.coupled_momentum_solver));
  undump_parm_with_id(&(pglb.default_emitter_start));
  undump_parm_with_id(&(pglb.default_emitter_end));
  undump_parm_with_id(&(pglb.default_max_particle_age));
  undump_parm_with_id(&(pglb.default_max_num_reflections));
  undump_parm_with_id(&(pglb.default_min_particle_velocity));

  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_PGLB) {
      cdi_write_pglb(info, &pglb);
    }
  }
}


static void UndumpParticleGeneralParameterDistributionInfo(cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION* pDistributionInfo, CDI_INFO info)
{
  sCDI_PARM distributionType;
  undump_parm_with_id(&distributionType);

  sCDI_PARM param1, param2;
  undump_parm_with_id(&param1);
  if (distributionType.value != DISTRIBUTION_NONE)
    undump_parm_with_id(&param2);

  if (distributionType.value == DISTRIBUTION_NONE)
    pDistributionInfo->SetNoneDistribution(param1);
  else if (distributionType.value == DISTRIBUTION_UNIFORM)
    pDistributionInfo->SetUniformDistribution(param1, param2);
  else if (distributionType.value == DISTRIBUTION_GAUSSIAN)
    pDistributionInfo->SetGaussianDistribution(param1, param2);
  else if (distributionType.value == DISTRIBUTION_GAMMA)
    pDistributionInfo->SetGammaDistribution(param1, param2);
}

static VOID undump_prmt(CDI_INFO info)
{
  sCDI_PRMT prmt;
  lexer_parse_lbrace();
  prmt.cccc = CDI_CHUNK_TYPE_PRMT;

  undump_namestr_with_id(&(prmt.name));

  undump_parm_with_id(&(prmt.type));
  if (prmt.type.value == PARTICLE_MATERIAL_LIQUID) {
    undump_parm_with_id(&(prmt.viscosity));
    undump_parm_with_id(&(prmt.surface_tension));
  }
  UndumpParticleGeneralParameterDistributionInfo(&prmt.density_info, info);
  if (prmt.type.value == PARTICLE_MATERIAL_LIQUID)
    undump_parm_with_id(&(prmt.breakup_model));

  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_PRMT) {
      cdi_write_prmt(info, &prmt);
    }
  }
}


// =======================================================================================
// Particle Model - Emitter Configurations
// =======================================================================================

static void UndumpParticleDiameterDistributionInfo(cCDI_PARTICLE_DIAMETER_DISTRIBUTION* pDistributionInfo, CDI_INFO cdi_info)
{
  sCDI_PARM distributionType;
  undump_parm_with_id(&distributionType);

  sCDI_PARM param1, param2;
  sCDI_PARM param3, param4;//PR_48032
  undump_parm_with_id(&param1);
  if (distributionType.value != DISTRIBUTION_NONE)
    undump_parm_with_id(&param2);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31) &&
      distributionType.value != DISTRIBUTION_NONE && distributionType.value != DISTRIBUTION_UNIFORM)//PR_48032
	{
		undump_parm_with_id(&param3);
		undump_parm_with_id(&param4);
	}

  if (distributionType.value == DISTRIBUTION_NONE)
    pDistributionInfo->SetNoneDistribution(param1);
  else if (distributionType.value == DISTRIBUTION_UNIFORM)
    pDistributionInfo->SetUniformDistribution(param1, param2);
  else if (distributionType.value == DISTRIBUTION_GAUSSIAN)
  {
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31)) //PR_48032
			pDistributionInfo->SetGaussianDistribution(param1, param2, param3, param4);
	  else
      pDistributionInfo->SetGaussianDistribution(param1, param2);
  }
  else if (distributionType.value == DISTRIBUTION_GAMMA)
  {
	  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31)) //PR_48032
	    pDistributionInfo->SetGammaDistribution(param1, param2, param3, param4);//Pr_48032
	  else
      pDistributionInfo->SetGammaDistribution(param1, param2);
  }
  else if (distributionType.value == DISTRIBUTION_ROSIN_RAMMLER)
  {
	  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31)) //PR_48032
		  pDistributionInfo->SetRosinRammlerDistribution(param1, param2, param3, param4);//Pr_48032
	  else
      pDistributionInfo->SetRosinRammlerDistribution(param1, param2);
  }
  else if (distributionType.value == DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION) //PR_48032
  {
    // version *must* be >= 7.31
    pDistributionInfo->SetRosinRammlerVolumeFractionDistribution(param1, param2, param3, param4);//Pr_48032
  }
  else if (distributionType.value == DISTRIBUTION_LOG_NORMAL)
  {
	  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31)) //PR_48032
		  pDistributionInfo->SetLogNormalDistribution(param1, param2, param3, param4);//Pr_48032
	  else 
      pDistributionInfo->SetLogNormalDistribution(param1, param2);
  }
}


static void UndumpEmitterConfigurations(CDI_INFO info)
{
  lexer_parse_lbrace();   // Opening '{' after 'ecgs'

  cCDI_EMITTER_CONFIGURATIONS emitterConfigs;

  char cccc_string[5];
  while ( lexer_parse_n_char_id_or_rbrace(cccc_string, 4)
            && 
          cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_ECFG)
  {

    lexer_parse_lbrace();   // Opening '{' after 'ecfg'

    sCDI_PARM configurationType;
    undump_parm_with_id(&configurationType);

    std::unique_ptr<sCDI_EMITTER_CONFIG_BASE> upConfig;
    if (configurationType.value == cCDI_EMITTER_CONFIGURATIONS::NOZZLE)
      upConfig.reset(new sCDI_NOZZLE_EMITTER_CONFIG);
    else if (configurationType.value == cCDI_EMITTER_CONFIGURATIONS::RAIN)
      upConfig.reset(new sCDI_RAIN_EMITTER_CONFIG);
    else if (configurationType.value == cCDI_EMITTER_CONFIGURATIONS::TIRE)
      upConfig.reset(new sCDI_TIRE_EMITTER_CONFIG);
    else
      assert(false);

    //Base class data
    undump_namestr_with_id(&(upConfig->name));
    undump_parm_with_id(&(upConfig->material_index));


    if (configurationType.value == cCDI_EMITTER_CONFIGURATIONS::NOZZLE) {

      sCDI_NOZZLE_EMITTER_CONFIG* pConfig = dynamic_cast<sCDI_NOZZLE_EMITTER_CONFIG*>(upConfig.get());

      undump_parm_with_id(&(pConfig->nozzle_type));
      undump_parm_with_id(&(pConfig->angle_distribution));
      switch (int(pConfig->nozzle_type.value))
      {
      case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_FULL_CONE:
        undump_parm_with_id(&(pConfig->cone_half_angle));
        pConfig->outer_half_angle_limit.value = M_PI;
        pConfig->outer_half_angle_limit.preferredUnit = "deg";
        if (CDI_INFO_VERSION_AT_LEAST(info, 4, 19) && pConfig->angle_distribution.value == DISTRIBUTION_GAUSSIAN)
          undump_parm_with_id(&(pConfig->outer_half_angle_limit));
        break;
      case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_HOLLOW_CONE:
        undump_parm_with_id(&(pConfig->mean_angle));
        undump_parm_with_id(&(pConfig->angle_range));
        pConfig->outer_half_angle_limit.value = M_PI;
        pConfig->outer_half_angle_limit.preferredUnit = "deg";
        pConfig->inner_half_angle_limit.value = 0.0;
        pConfig->inner_half_angle_limit.preferredUnit = "deg";
        if (CDI_INFO_VERSION_AT_LEAST(info, 4, 19) && pConfig->angle_distribution.value == DISTRIBUTION_GAUSSIAN) {
          undump_parm_with_id(&(pConfig->inner_half_angle_limit));
          undump_parm_with_id(&(pConfig->outer_half_angle_limit));
        }
        break;
      case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_ELLIPTICAL_CONE:
        undump_parm_with_id(&(pConfig->major_half_angle));
        pConfig->major_outer_half_angle_limit.value = M_PI;
        pConfig->major_outer_half_angle_limit.preferredUnit = "deg";
        pConfig->minor_outer_half_angle_limit.value = M_PI;
        pConfig->minor_outer_half_angle_limit.preferredUnit = "deg";
        if (CDI_INFO_VERSION_AT_LEAST(info, 4, 19) && pConfig->angle_distribution.value == DISTRIBUTION_GAUSSIAN)
          undump_parm_with_id(&(pConfig->major_outer_half_angle_limit));
        undump_parm_with_id(&(pConfig->minor_half_angle));
        if (CDI_INFO_VERSION_AT_LEAST(info, 4, 19) && pConfig->angle_distribution.value == DISTRIBUTION_GAUSSIAN)
          undump_parm_with_id(&(pConfig->minor_outer_half_angle_limit));
        break;
      }
      UndumpParticleGeneralParameterDistributionInfo(&pConfig->velocity_info, info);
      UndumpParticleDiameterDistributionInfo(&pConfig->particle_diam_info, info);
      undump_parm_with_id(&(pConfig->emission_rate_type));
      undump_parm_with_id(&(pConfig->emission_rate));
      lexer_parse_rbrace();   // Closing 'ecfg'

    }

    else if (configurationType.value == cCDI_EMITTER_CONFIGURATIONS::RAIN) {

      sCDI_RAIN_EMITTER_CONFIG* pConfig = dynamic_cast<sCDI_RAIN_EMITTER_CONFIG*>(upConfig.get());

      UndumpParticleDiameterDistributionInfo(&(pConfig->particle_diam_info), info);
      undump_parm_with_id(&(pConfig->emission_rate_type));
      undump_parm_with_id(&(pConfig->emission_rate));
      if (CDI_INFO_VERSION_AT_LEAST(info, 7, 11)){
         undump_parm_with_id(&(pConfig->wind_x_velocity));
         undump_parm_with_id(&(pConfig->wind_y_velocity));
         undump_parm_with_id(&(pConfig->wind_z_velocity));
         undump_int_with_id(&(pConfig->csysIndex));
         undump_int_with_id(&(pConfig->refFrameIndex));
         undump_strg_with_id(&pConfig->refFrameName);
      }
      else {
         pConfig->wind_x_velocity.value = 0;
         pConfig->wind_y_velocity.value = 0;
         pConfig->wind_z_velocity.value = 0;
         pConfig->csysIndex = -1;
         pConfig->refFrameIndex = -1;
         pConfig->refFrameName = "Global Body-Fixed";
      } 

      lexer_parse_rbrace();   // Closing 'ecfg'

    }

    else if (configurationType.value == cCDI_EMITTER_CONFIGURATIONS::TIRE) {

      sCDI_TIRE_EMITTER_CONFIG* pConfig = dynamic_cast<sCDI_TIRE_EMITTER_CONFIG*>(upConfig.get());

      undump_parm_with_id(&(pConfig->emission_rate_type));
      undump_parm_with_id(&(pConfig->emission_rate));
      sCDI_PARM spatial_emission_distribution;
      undump_parm_with_id(&spatial_emission_distribution);
      if (spatial_emission_distribution.value == DISTRIBUTION_LINEAR)
        pConfig->SetSpatialEmissionLinearDistribution();
      else
      {
        sCDI_PARM spatial_emission_distribution_parameter;
        undump_parm_with_id(&spatial_emission_distribution_parameter);
        if (spatial_emission_distribution.value == DISTRIBUTION_GAUSSIAN)
          pConfig->SetSpatialEmissionGaussianDistribution(spatial_emission_distribution_parameter);
        else if (spatial_emission_distribution.value == DISTRIBUTION_1MX_POW_N)
          pConfig->SetSpatialEmission1MXDistribution(spatial_emission_distribution_parameter);
        else if (spatial_emission_distribution.value == DISTRIBUTION_HALF_COSINE)
          pConfig->SetSpatialEmissionHalfCosineDistribution(spatial_emission_distribution_parameter);
      }
      undump_parm_with_id(&(pConfig->emission_rate_ratio));

      char tanp_string[5];
      while (lexer_parse_n_char_id_or_rbrace(tanp_string, 4)
        &&
        cio_string_to_type(tanp_string) == CDI_CHUNK_TYPE_TANP)
      {
        sCDI_TIRE_NOZZLE_PROPS tireNozzle;

        lexer_parse_lbrace();   // Opening '{' after 'tanp'
        undump_parm_with_id(&(tireNozzle.tire_arc_position));
        UndumpParticleGeneralParameterDistributionInfo(&tireNozzle.emission_offset_angle_info, info);
        tireNozzle.outer_half_angle_limit.value = M_PI;
        tireNozzle.outer_half_angle_limit.preferredUnit = "deg";
        if (CDI_INFO_VERSION_AT_LEAST(info, 4, 19) && tireNozzle.emission_offset_angle_info.GetDistributionType().value == DISTRIBUTION_GAUSSIAN)
          undump_parm_with_id(&(tireNozzle.outer_half_angle_limit));
        undump_parm_with_id(&(tireNozzle.transverse_stretch_factor));
        UndumpParticleGeneralParameterDistributionInfo(&tireNozzle.velocity_info, info);
        UndumpParticleDiameterDistributionInfo(&tireNozzle.particle_diam_info, info);
        lexer_parse_rbrace();   // Closing 'tanp'

        pConfig->AddTireNozzle(tireNozzle);
      }

      // No need to explicitly parse the 'ecfg' right brace here, because
      // parsing that is what triggers the 'tanp' while loop exit condition. 

    }

    emitterConfigs.AddConfiguration(upConfig.release());
  }
    
  if (info)
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_ECGS) {
      emitterConfigs.WriteToCDI(info);
    }
  }
}



// =======================================================================================
// Particle Model - Emitters
// =======================================================================================

static void UndumpBaseEmitter(sCDI_PARTICLE_EMITTER_BASE* pEmitter, CDI_INFO info)
{
  undump_namestr_with_id(&(pEmitter->name));

  undump_parm_with_id(&(pEmitter->emitter_configuration));
  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 7))
    undump_parm_with_id(&(pEmitter->particles_per_parcel));
  undump_parm_with_id(&(pEmitter->subject_to_dispersion_box));
  undump_parm_with_id(&(pEmitter->subject_to_gravity));
  if (CDI_INFO_VERSION_AT_LEAST(info, 6, 3)) {
    pEmitter->start_via = static_cast<eCDI_MEAS_START_TIME_VIA::Enum>(lexer_parse_int());
    int monitorSize = lexer_parse_int();
    ccDOTIMES(i, monitorSize)
      pEmitter->monitors.push_back(lexer_parse_int());
    undump_parm_with_id(&(pEmitter->start));
    pEmitter->end_via = static_cast<eCDI_MEAS_END_TIME_VIA::Enum>(lexer_parse_int());
    undump_parm_with_id(&(pEmitter->end));
    undump_parm_with_id(&(pEmitter->duration));
  } else {
    undump_parm_with_id(&(pEmitter->start));
    undump_parm_with_id(&(pEmitter->end));
    pEmitter->duration.value = -1;
  }

  { // Even though 'frac_trajectory_recording' has been removed from sCDI_PARTICLE_EMITTER_BASE for 5.4b  (CDI v > 5.2), 
    // we keep file format the same, so that PowerVIZ 5.4a will have forward compatibility with files generated from 5.4b.
    sCDI_PARM fakeFracTrajRecord;
    undump_parm_with_id(&fakeFracTrajRecord);
  }
  undump_parm_with_id(&(pEmitter->max_age));
  undump_parm_with_id(&(pEmitter->max_num_reflections));
  undump_parm_with_id(&(pEmitter->min_particle_velocity));
  undump_parm_with_id(&(pEmitter->visible));
}

static void UndumpNozzleOrientation(sCDI_EMITTER_NOZZLE_ORIENTATION* pNozzleOrientation, CDI_INFO info)
{
  undump_parm_with_id(&(pNozzleOrientation->spray_direction_csys));
  undump_parm_with_id(&(pNozzleOrientation->mean_spray_direction[0]));
  undump_parm_with_id(&(pNozzleOrientation->mean_spray_direction[1]));
  undump_parm_with_id(&(pNozzleOrientation->mean_spray_direction[2]));
  undump_parm_with_id(&(pNozzleOrientation->elliptical_nozzle));
  if (pNozzleOrientation->elliptical_nozzle.value == 1)
  {
    pNozzleOrientation->major_ellipse_direction.resize(3);
    undump_parm_with_id(&(pNozzleOrientation->major_ellipse_direction[0]));
    undump_parm_with_id(&(pNozzleOrientation->major_ellipse_direction[1]));
    undump_parm_with_id(&(pNozzleOrientation->major_ellipse_direction[2]));
  }
}


static void UndumpNozzleVisibilitySettings(sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS* pVisibilitySettings, CDI_INFO info)
{
  undump_parm_with_id(&(pVisibilitySettings->nozzle_show));
  if ((pVisibilitySettings->nozzle_show.value == 1) || CDI_INFO_VERSION_AT_LEAST(info, 5, 5))
  {
    undump_strg_with_id(&(pVisibilitySettings->nozzle_cone_look));
    undump_strg_with_id(&(pVisibilitySettings->nozzle_body_color));
    undump_strg_with_id(&(pVisibilitySettings->nozzle_arrow_look));
    undump_parm_with_id(&(pVisibilitySettings->nozzle_size));
  }
}


static void UndumpEmissionBoundaryVisibilitySettings(sCDI_EMITTER_EMISSION_BOUNDARY_VISIBILITY_SETTINGS* pVisibilitySettings, CDI_INFO info)
{
  undump_parm_with_id(&(pVisibilitySettings->emission_boundary_show));
  if ((pVisibilitySettings->emission_boundary_show.value == 1) || CDI_INFO_VERSION_AT_LEAST(info, 5, 5))
  {
    undump_strg_with_id(&(pVisibilitySettings->emission_boundary_look));
    undump_parm_with_id(&(pVisibilitySettings->emission_boundary_size));
  }
}


static void UndumpEmissionPointVisibilitySettings(sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS* pVisibilitySettings, CDI_INFO info)
{
  undump_bool_with_id(&(pVisibilitySettings->emission_point_show));
  if (pVisibilitySettings->emission_point_show || CDI_INFO_VERSION_AT_LEAST(info, 5, 5))
  {
    undump_strg_with_id(&(pVisibilitySettings->emission_point_color));
    undump_parm_with_id(&(pVisibilitySettings->emission_point_size));
  }
}


static void UndumpCyl_Chunk(sCDI_CYL_* pCyl_, CDI_INFO info)
{
  undump_namestr_with_id(&(pCyl_->name));

  undump_rgns_with_id(pCyl_->region.regions);

  undump_parm_with_id(&(pCyl_->start_point[0]));
  undump_parm_with_id(&(pCyl_->start_point[1]));
  undump_parm_with_id(&(pCyl_->start_point[2]));
  undump_parm_with_id(&(pCyl_->end_point[0]));
  undump_parm_with_id(&(pCyl_->end_point[1]));
  undump_parm_with_id(&(pCyl_->end_point[2]));
  undump_parm_with_id(&(pCyl_->start_radius));
  undump_parm_with_id(&(pCyl_->end_radius));
  undump_parm_with_id(&(pCyl_->num_sides));
}


static void UndumpEmitterGeometries(std::vector<sCDI_CYL_>* pCylGeoms, std::vector<sCDI_BOX_>* pBoxGeoms, CDI_INFO info)
{
  char geom_chunk_string[5];

  if (pCylGeoms)
  {
    lexer_parse_specific_id(CDI_CHUNK_TYPE_CYLS);
    lexer_parse_lbrace();   // Opening '{' after 'cyls'
    while (lexer_parse_n_char_id_or_rbrace(geom_chunk_string, 4)
      &&
      cio_string_to_type(geom_chunk_string) == CDI_CHUNK_TYPE_CYL_)
    {
      lexer_parse_lbrace();   // Opening '{' after 'cyl_'
      sCDI_CYL_ cyl_;
      UndumpCyl_Chunk(&cyl_, info);
      lexer_parse_rbrace();   // Closing 'cyl_'
      pCylGeoms->push_back(cyl_);
    }
  }

  if (pBoxGeoms)
  {
    lexer_parse_specific_id(CDI_CHUNK_TYPE_BOXS);
    lexer_parse_lbrace();   // Opening '{' after 'boxs'
    while (lexer_parse_n_char_id_or_rbrace(geom_chunk_string, 4)
      &&
      cio_string_to_type(geom_chunk_string) == CDI_CHUNK_TYPE_BOX_)
    {
      sCDI_BOX_ box_;
      // parses its own '{' and '}'
      undump_box_(info, &box_);
      pBoxGeoms->push_back(box_);
    }
  }
}


static void UndumpSurfaceEmitter(sCDI_SURFACE_EMITTER* pEmitter, CDI_INFO info)
{
  UndumpBaseEmitter(pEmitter, info);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(info))
    undump_geos_with_id(info, &(pEmitter->geom_selection));
  else
    undump_flst_with_id(pEmitter->geom_selection.m_selections.face_list);

  undump_parm_with_id(&(pEmitter->fixed_release_points));
  undump_parm_with_id(&(pEmitter->user_specified_nozzle_orientation));
  if (pEmitter->user_specified_nozzle_orientation.value == 1 || CDI_INFO_VERSION_AT_LEAST(info, 4, 16))
    UndumpNozzleOrientation(&(pEmitter->nozzle_orientation), info);

  UndumpNozzleVisibilitySettings(&(pEmitter->nozzle_visibility_settings), info);
  if (CDI_INFO_VERSION_AT_LEAST(info, 5, 1))
    UndumpEmissionPointVisibilitySettings(&(pEmitter->emission_point_visibility_settings), info);
}


static void UndumpVolumeEmitter(sCDI_VOLUME_EMITTER* pEmitter, CDI_INFO info)
{
  UndumpBaseEmitter(pEmitter, info);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(info))
    undump_geos_with_id(info, &(pEmitter->geom_selection));
  else
    undump_rgns_with_id(pEmitter->geom_selection.m_selections.rgn_list);

  UndumpNozzleOrientation(&(pEmitter->nozzle_orientation), info);

  undump_parm_with_id(&(pEmitter->fixed_release_points));
  if (pEmitter->fixed_release_points.value == 1)
  {
    pEmitter->release_spacing.resize(3);
    undump_parm_with_id(&(pEmitter->release_spacing[0]));
    undump_parm_with_id(&(pEmitter->release_spacing[1]));
    undump_parm_with_id(&(pEmitter->release_spacing[2]));
  }

  UndumpEmitterGeometries(&(pEmitter->m_cylGeometries), &(pEmitter->m_boxGeometries), info);

  UndumpNozzleVisibilitySettings(&(pEmitter->nozzle_visibility_settings), info);
  UndumpEmissionBoundaryVisibilitySettings(&(pEmitter->emission_boundary_visibility_settings), info);
  if (CDI_INFO_VERSION_AT_LEAST(info, 5, 1))
    UndumpEmissionPointVisibilitySettings(&(pEmitter->emission_point_visibility_settings), info);
}


static void UndumpPointEmitter(sCDI_POINT_EMITTER* pEmitter, CDI_INFO info)
{
  UndumpBaseEmitter(pEmitter, info);

  lexer_parse_specific_id(CDI_CHUNK_TYPE_PNTS);
  UndumpVectorOfPoints(info, &(pEmitter->points));

  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 22))
    undump_int_with_id(&(pEmitter->preferred_csys_index));

  UndumpNozzleOrientation(&(pEmitter->nozzle_orientation), info);

  UndumpNozzleVisibilitySettings(&(pEmitter->nozzle_visibility_settings), info);
}


static void UndumpTireEmitter(sCDI_TIRE_EMITTER* pEmitter, CDI_INFO info)
{
  UndumpBaseEmitter(pEmitter, info);

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(info))
    undump_rgns_with_id(pEmitter->geom_selection.m_selections.rgn_list);
  else
    undump_geos_with_id(info, &(pEmitter->geom_selection));

  undump_parm_with_id(&(pEmitter->zero_angle_direction_csys));
  undump_parm_with_id(&(pEmitter->zero_angle_direction[0]));
  undump_parm_with_id(&(pEmitter->zero_angle_direction[1]));
  undump_parm_with_id(&(pEmitter->zero_angle_direction[2]));
  undump_parm_with_id(&(pEmitter->approx_rotation_axis_dir[0]));
  undump_parm_with_id(&(pEmitter->approx_rotation_axis_dir[1]));
  undump_parm_with_id(&(pEmitter->approx_rotation_axis_dir[2]));

  UndumpEmitterGeometries(&(pEmitter->m_cylGeometries), NULL, info);

  UndumpNozzleVisibilitySettings(&(pEmitter->nozzle_visibility_settings), info);
  UndumpEmissionBoundaryVisibilitySettings(&(pEmitter->emission_boundary_visibility_settings), info);

  undump_parm_with_id(&(pEmitter->tire_tread_show));
  if ((pEmitter->tire_tread_show.value == 1) || CDI_INFO_VERSION_AT_LEAST(info, 5, 5))
    undump_strg_with_id(&(pEmitter->tire_tread_look));

  if (CDI_INFO_VERSION_AT_LEAST(info, 5, 1))
    UndumpEmissionPointVisibilitySettings(&(pEmitter->emission_point_visibility_settings), info);
}


static void UndumpRainEmitter(sCDI_RAIN_EMITTER* pEmitter, CDI_INFO info)
{
  UndumpBaseEmitter(pEmitter, info);

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(info))
    undump_rgns_with_id(pEmitter->geom_selection.m_selections.rgn_list);
  else
    undump_geos_with_id(info, &(pEmitter->geom_selection));

  UndumpEmitterGeometries(&(pEmitter->m_cylGeometries), &(pEmitter->m_boxGeometries), info);
  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 21))
    UndumpNozzleVisibilitySettings(&(pEmitter->nozzle_visibility_settings), info);
  if (CDI_INFO_VERSION_AT_LEAST(info, 5, 1))
    UndumpEmissionPointVisibilitySettings(&(pEmitter->emission_point_visibility_settings), info);
}


static void UndumpEmitters(CDI_INFO info)
{
  lexer_parse_lbrace();   // Opening '{' after 'pemc'

  cCDI_PARTICLE_EMITTERS emitters;

  char cccc_string[5];
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4)
    &&
    cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_PEMT)
  {

    lexer_parse_lbrace();   // Opening '{' after 'pemt'

    sCDI_PARM emitterType;
    undump_parm_with_id(&emitterType);

    switch (cdiINT32(emitterType.value)) {
    case cCDI_PARTICLE_EMITTERS::EMITTER_SURFACE: {
                                                    std::unique_ptr<sCDI_SURFACE_EMITTER> pEmitter(new sCDI_SURFACE_EMITTER);
                                                    UndumpSurfaceEmitter(pEmitter.get(), info);
                                                    emitters.AddEmitter(pEmitter.release());
                                                    break;
    }
    case cCDI_PARTICLE_EMITTERS::EMITTER_VOLUME: {
                                                   std::unique_ptr<sCDI_VOLUME_EMITTER> pEmitter(new sCDI_VOLUME_EMITTER);
                                                   UndumpVolumeEmitter(pEmitter.get(), info);
                                                   emitters.AddEmitter(pEmitter.release());
                                                   break;
    }
    case cCDI_PARTICLE_EMITTERS::EMITTER_POINT: {
                                                  std::unique_ptr<sCDI_POINT_EMITTER> pEmitter(new sCDI_POINT_EMITTER);
                                                  UndumpPointEmitter(pEmitter.get(), info);
                                                  emitters.AddEmitter(pEmitter.release());
                                                  break;
    }
    case cCDI_PARTICLE_EMITTERS::EMITTER_TIRE: {
                                                 std::unique_ptr<sCDI_TIRE_EMITTER> pEmitter(new sCDI_TIRE_EMITTER);
                                                 UndumpTireEmitter(pEmitter.get(), info);
                                                 emitters.AddEmitter(pEmitter.release());
                                                 break;
    }
    case cCDI_PARTICLE_EMITTERS::EMITTER_RAIN: {
                                                 std::unique_ptr<sCDI_RAIN_EMITTER> pEmitter(new sCDI_RAIN_EMITTER);
                                                 UndumpRainEmitter(pEmitter.get(), info);
                                                 emitters.AddEmitter(pEmitter.release());
                                                 break;
    }
    }

    lexer_parse_rbrace();   // Closing 'pemt'

  }

  if (info)
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_PEMC) {
      emitters.WriteToCDI(info);
    }
  }

}


static VOID undump_srmi (CDI_INFO info)
{
  sCDI_SRMI srmi;
  lexer_parse_lbrace();
  srmi.cccc = CDI_CHUNK_TYPE_SRMI;
  undump_parm_with_id(&(srmi.particle_material));
  undump_parm_with_id(&(srmi.splash_model));

  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 2))
    undump_parm_with_id(&(srmi.enable_reflection));

  undump_parm_with_id(&(srmi.reflect_min_momentum));
  undump_parm_with_id(&(srmi.reflect_min_normal_vel));
  undump_parm_with_id(&(srmi.reflect_min_angle));
  undump_parm_with_id(&(srmi.normal_rest_coeff));
  undump_parm_with_id(&(srmi.tang_restitution_coeff));
  undump_parm_with_id(&(srmi.scatter_angle_distribution));
  undump_parm_with_id(&(srmi.scatter_angle_range));
  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_SRMI) {
      cdi_write_srmi(info, &srmi);
    }
  }
}


static VOID undump_scrn (CDI_INFO info)
{
  sCDI_SCRN scrn;
  lexer_parse_lbrace();
  scrn.cccc = CDI_CHUNK_TYPE_SCRN;

  undump_namestr_with_id(&(scrn.name));

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(info)) {
    lexer_parse_specific_id(CDI_CHUNK_TYPE_FLST);
    sCDI_FLST flst;
    undump_flst_helper(&flst);
    scrn.geom_selection.m_selections.face_list = std::vector<int>(flst.face, flst.face + flst.n_face);
    if (flst.n_face > 0)
      exa_free(flst.face);
  }
  else {
    undump_geos_with_id(info, &(scrn.geom_selection));
  }

  undump_parm_with_id(&(scrn.opening_size));
  undump_parm_with_id(&(scrn.pass_thru_fraction));
  undump_parm_with_id(&(scrn.surface_material));
  if (CDI_INFO_VERSION_AT_LEAST(info, 7, 26))
    undump_parm_with_id(&(scrn.measured_fraction)); //PR_42469
  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_SCRN) {
      cdi_write_scrn(info, &scrn);
    }
  }
}

static VOID undump_vhcl (CDI_INFO info)
{
  sCDI_VHCL vhcl;
  lexer_parse_lbrace();
  
  undump_int_with_id(&(vhcl.csys_index));
  undump_enum_with_id(&(vhcl.moments_via));
  
  undump_enum_with_id(&(vhcl.centerline_direction));
  undump_enum_with_id(&(vhcl.up_direction));
  undump_enum_with_id(&(vhcl.side_force_direction));
  
  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 14))
    undump_bool_with_id(&(vhcl.is_front_axle_applicable));
  undump_int_with_id(&(vhcl.front_axle_preferred_csys_index));
  undump_dbls_with_id((idFLOAT*)vhcl.front_axle_origin);
  undump_dbls_with_id((idFLOAT*)vhcl.front_axle_dir);
  
  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 14))
    undump_bool_with_id(&(vhcl.is_rear_axle_applicable));
  undump_int_with_id(&(vhcl.rear_axle_preferred_csys_index));
  undump_dbls_with_id((idFLOAT*)vhcl.rear_axle_origin);
  undump_dbls_with_id((idFLOAT*)vhcl.rear_axle_dir);
 
  undump_dbls_with_id((idFLOAT*)vhcl.moment_center);
  undump_int_with_id(&(vhcl.floor_part_index));
  undump_dbls_with_id((idFLOAT*)vhcl.floor_point);
  
  if (!CDI_INFO_VERSION_AT_LEAST(info, 4, 14)) {
    // VHCL chunk temporarily had a boolean 'user_defined_char_values' and parm 'char_area'
    bool dummy_bool;
    undump_bool_with_id(&dummy_bool);
    sCDI_PARM dummy_parm;
    undump_parm_with_id(&dummy_parm);
  }
  undump_parm_with_id(&(vhcl.wheelbase));

  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_VHCL) {
      cdi_write_vhcl(info, &vhcl);
    }
  }
}

static VOID undump_amw_(CDI_INFO info)
{
  sCDI_AMW_ amw_;
  lexer_parse_lbrace();
  
  undump_strg_with_id(&(amw_.name));
  undump_int_with_id(&(amw_.meas_window_to_average));
  undump_bool_with_id(&(amw_.average_fnc));
  undump_bool_with_id(&(amw_.average_pnc));
  undump_bool_with_id(&(amw_.average_snc));
  undump_enum_with_id(&(amw_.start_via));
  undump_parm_with_id(&(amw_.start));

  cdiINT32 numMonitors = 0;
  cdiINT32 monitorIndex = -1;
  undump_int_with_id(&numMonitors);
  ccDOTIMES(i, numMonitors)  {
    undump_int_with_id(&monitorIndex);
    amw_.monitors.push_back(monitorIndex);
  }
    
  undump_enum_with_id(&(amw_.end_via));
  undump_parm_with_id(&(amw_.end));
  undump_enum_with_id(&(amw_.avg_interval_via));
  undump_parm_with_id(&(amw_.avg_interval));
  if (CDI_INFO_VERSION_AT_LEAST(info, 7, 30))
    undump_bool_with_id(&(amw_.m_phaseAveraged));
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8, 10>(info)) {
    bool hasCpScreening(false);
    undump_bool_with_id(&hasCpScreening);
    if (hasCpScreening)
      undump_mflt_with_id(&(amw_.force_filter));
  }

  lexer_parse_rbrace();
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_AMW_) {
      cdi_write_amw_(info, &amw_);
    }
  }
}

void sCDI_MNTR::Undump(CDI_INFO cdi_info)
{
  lexer_parse_lbrace();

  undump_enum_with_id(&monitor_type);

  undump_strg_with_id(&name);
  undump_strg_with_id(&preferred_time_unit);

  // Flow monitor-specific parameters
  undump_int_with_id(&meas_window_index);
  undump_enum_with_id(&variable_source);
  undump_enum_with_id(&flow_variable);
  undump_bool_with_id(&variable_is_vector_component);
  undump_int_with_id(&reference_csys);
  undump_bool_with_id(&variable_is_moment_component);
  undump_dbls_with_id((idFLOAT*)reference_point);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 15))
    undump_bool_with_id(&use_wheelbase);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 11)) {
    undump_enum_with_id(&region_filtering);
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,7>(cdi_info)) {
      undump_geos_with_id(cdi_info, &(region_filtering_tree));
    }
    else {
      cdiINT32 region_filtering_partition_index = -1;
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 0))
        undump_int_with_id(&region_filtering_partition_index);
      region_filtering_tree.PartitionIndex(region_filtering_partition_index + 1);
      undump_rgns_with_id(region_filtering_tree.m_selections.rgn_list);
    }
    undump_enum_with_id(&face_filtering);
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,7>(cdi_info)) {
      undump_geos_with_id(cdi_info, &(face_filtering_tree));
    }
    else {
      cdiINT32 face_filtering_partition_index = -1;
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 0))
        undump_int_with_id(&face_filtering_partition_index);
      face_filtering_tree.PartitionIndex(face_filtering_partition_index + 1);
      undump_flst_with_id(face_filtering_tree.m_selections.face_list);
    }
  }

  // HX monitor-specific parameters
  undump_int_with_id(&heat_exchanger_index);
  undump_enum_with_id(&heat_exchanger_variable);

  // PowerTHERM monitor-specific parameters
  undump_int_with_id(&powertherm_model_index);
  undump_strg_with_id(&coupled_powertherm_part);
  undump_enum_with_id(&powertherm_part_side);
  undump_bool_with_id(&coupled);
  undump_enum_with_id(&powertherm_variable);

  // Signal Analysis parameters
  undump_enum_with_id(&signal_analysis);
  undump_bool_with_id(&automatically_stop);
  undump_enum_with_id(&analysis_scheme);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 18))
    undump_strg_with_id(&classic_autostop_analysis_scheme);
  undump_enum_with_id(&signal_conv_criteria);

  undump_enum_with_id(&monitor_flow_pass_via);
  undump_int_with_id(&coupling_phase);
  undump_parm_with_id(&monitor_flow_pass);
  undump_enum_with_id(&initial_transient_determined_via);
  undump_enum_with_id(&minimum_initial_transient_via);
  undump_parm_with_id(&minimum_initial_transient);
  undump_enum_with_id(&end_of_initial_transient_via);
  undump_parm_with_id(&end_of_initial_transient);
  undump_enum_with_id(&initial_transient_variance_window_via);
  undump_parm_with_id(&initial_transient_variance_window);

  // Accuracy/Confidence params
  undump_enum_with_id(&desired_accuracy_via);
  if (desired_accuracy_via == eCDI_MNTR_ACCY_VIA::PercentageOfMean) {
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 8, 13))
      undump_parm_with_id(&desired_accuracy_percentage);
    else
      undump_parm_with_id(&desired_accuracy);
  }
  else if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 8, 13) && (desired_accuracy_via == eCDI_MNTR_ACCY_VIA::LesserOfValueAndPercentageOfMean
      || desired_accuracy_via == eCDI_MNTR_ACCY_VIA::GreaterOfValueAndPercentageOfMean)) {
    undump_parm_with_id(&desired_accuracy);
    undump_parm_with_id(&desired_accuracy_percentage);
  } else
    undump_parm_with_id(&desired_accuracy);

  undump_enum_with_id(&confidence_level_via);
  undump_dbls_with_id((idFLOAT*)&custom_confidence_level);
  undump_enum_with_id(&minimum_averaging_time_via);
  undump_parm_with_id(&minimum_averaging_time);

  // Stabilization Window params
  undump_enum_with_id(&stabilization_window_via);
  undump_parm_with_id(&stabilization_window);
  undump_bool_with_id(&enable_subwindows);
  undump_enum_with_id(&subwindow_via);
  undump_parm_with_id(&subwindow);
  undump_enum_with_id(&subwindow_range_limit_via);
  undump_parm_with_id(&subwindow_range_limit);

  // Classic Autostop Support
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 17))
    undump_bool_with_id(&classic_autostop_algorithm);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 13)) {
    undump_parm_with_id(&variance_gradient_limit);
    undump_parm_with_id(&creep_limit);
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 18))
    undump_enum_with_id(&running_average_gradient_limit_via);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 13)) {
    undump_parm_with_id(&running_average_gradient_limit);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 20))
      undump_enum_with_id(&running_average_gradient_interval_via);
    undump_parm_with_id(&running_average_gradient_interval);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 20))
      undump_enum_with_id(&signal_processing_period_via);
    undump_parm_with_id(&signal_processing_period);
  }

  lexer_parse_rbrace();
}

void undump_mnts(CDI_INFO info)
{
  lexer_parse_lbrace();   // Opening '{' after 'mnts'

  cCDI_MONITORS monitors;

  char cccc_string[5];
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4)
    &&
    cio_string_to_type(cccc_string) == sCDI_MNTR::GetMonitorChunkType())
  {
    sCDI_MNTR monitor;
    monitor.Undump(info);
    monitors.m_monitors.push_back(monitor);
  }

  if (info)
  {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_MNTS) {
      monitors.WriteToCDI(info);
    }
  }
}


static VOID undump_mmbr( CDI_INFO cdi_info, sCDI_MMBR* mmbr){
  
  lexer_parse_lbrace();
  mmbr->name = lexer_parse_string();
  mmbr->value = lexer_parse_dfloat();
  mmbr->unit = lexer_parse_string();
  mmbr->unitclass = lexer_parse_string();
  lexer_parse_rbrace();
}

static VOID undump_mmbr (CDI_INFO cdi_info)
{
  sCDI_MMBR mmbr;
  undump_mmbr(cdi_info, &mmbr);
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_MMBR) {
      cdi_write_mmbr(cdi_info, &mmbr);
    }
  }  
}

static void undump_aftd(CDI_INFO cdi_info)
{
  sCDI_AFTD aftd;

  lexer_parse_lbrace();

  aftd.cccc = CDI_CHUNK_TYPE_AFTD;
  aftd.acceleration_factor = lexer_parse_dfloat();

  lexer_parse_rbrace();

  cdi_write_aftd(cdi_info, &aftd);
}


static void undump_look( CDI_INFO cdi_info, sCDI_LOOK* look) {
  lexer_parse_lbrace();
  look->name = lexer_parse_std_string();
  look->type = lexer_parse_std_string();
  look->origin = lexer_parse_std_string();

  std::string encSerializedLook = lexer_parse_std_string();
  std::string serializedLook = EXA_STR::Decode64(encSerializedLook);
  look->serializedLook.assign(serializedLook.data(),
                              serializedLook.data() + serializedLook.size());

  lexer_parse_rbrace();
}

static void undump_look (CDI_INFO cdi_info)
{
  sCDI_LOOK look;
  undump_look(cdi_info, &look);
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_LOOK) {
      cdi_write_realistic_look(cdi_info, &look);
    }
  }  
}

static VOID undump_wpdt (CDI_INFO cdi_info)
{
  sCDI_WPDT wpdt;

  lexer_parse_lbrace();

  wpdt.cccc = CDI_CHUNK_TYPE_WPDT;
  wpdt.index = lexer_parse_int();
  wpdt.filename = lexer_parse_string();
  wpdt.n_timesteps = lexer_parse_int();
  wpdt.velocity = (idFLOAT *)exa_malloc(3 * wpdt.n_timesteps * sizeof(idFLOAT),
                                        "undump_wpdt",
                                        "velocity array");

  DOTIMES(i, 3 * wpdt.n_timesteps, {
    wpdt.velocity[i] = lexer_parse_dfloat();
  });

  lexer_parse_rbrace();

  cdi_write_wpdt(cdi_info, &wpdt);

  exa_free(wpdt.velocity);

  return;
}

static VOID undump_tire (CDI_INFO cdi_info)
{
  sCDI_TIRE tire;
  lexer_parse_lbrace();
  tire.cccc = CDI_CHUNK_TYPE_TIRE;
  tire.csys_index = lexer_parse_int();
  tire.grooved_tire = lexer_parse_int();
  tire.n_knots[0] = lexer_parse_int();
  tire.n_knots[1] = lexer_parse_int();

  int nu = tire.n_knots[0];
  int nv = tire.n_knots[1];
  int ncoeff = cdi_tire_surface_coeff_array_size(3,nu,nv);
  tire.u_knots.reserve(nu);
  for(int i=0; i<nu; i++) {
    tire.u_knots.push_back( lexer_parse_dfloat() );
  }
  tire.v_knots.reserve(nv);
  for(int i=0; i<nv; i++) {
    tire.v_knots.push_back( lexer_parse_dfloat() );
  }
  tire.coeff_carcass.reserve(ncoeff);
  for(int i=0; i<ncoeff; i++) {
    tire.coeff_carcass.push_back( lexer_parse_dfloat() );
  }

  if (!tire.grooved_tire) {
    tire.coeff_wrap.reserve(ncoeff);
    for(int i=0; i<ncoeff; i++) {
      tire.coeff_wrap.push_back( lexer_parse_dfloat() );
    }
  }

  lexer_parse_rbrace();

  cdi_write_tire(cdi_info, &tire);
}


static VOID undump_clbr (CDI_INFO cdi_info)
{
  sCDI_CLBR clbr;
  lexer_parse_lbrace();

  clbr.cccc = CDI_CHUNK_TYPE_CLBR;
  clbr.face_index = lexer_parse_int();
  clbr.meas_window_index = lexer_parse_int();
  clbr.calibration_iterations = lexer_parse_int();
  clbr.cancel_pressure_fluctuations = (lexer_parse_int() != 0);
  clbr.subtract_mean_velocity = (lexer_parse_int() != 0);
  clbr.reset_initial_condition = (lexer_parse_int() != 0);
  lexer_parse_rbrace();

  cdi_write_clbr(cdi_info, &clbr);

  return;
}

static VOID undump_scmt(CDI_INFO info)
{
  sCDI_SCMT scmt;

  lexer_parse_lbrace();

  scmt.cccc = CDI_CHUNK_TYPE_SCMT;
  
  undump_namestr_with_id(&(scmt.name));
  if (CDI_INFO_VERSION_AT_LEAST(info, 9, 8)) {
    undump_enum_with_id(&(scmt.material_type));
  }
  undump_parm_with_id(&(scmt.density));
  undump_parm_with_id(&(scmt.specific_heat));
  if (CDI_INFO_VERSION_AT_LEAST(info, 9, 8)) {
    undump_enum_with_id(&(scmt.anisotropy_type));
  }
  undump_parm_with_id(scmt.therm_conductivity);
  undump_parm_with_id(scmt.therm_conductivity + 1);
  undump_parm_with_id(scmt.therm_conductivity + 2);
  
  lexer_parse_rbrace();
  
  cdi_write_scmt(info, &scmt);

  return;
}

static VOID undump_paxs(CDI_INFO info)
{
  sCDI_PAXS paxs;

  lexer_parse_lbrace();

  paxs.cccc = CDI_CHUNK_TYPE_PAXS;

  undump_int_with_id(&(paxs.part_index));
  undump_dbls_with_id((idFLOAT*)(paxs.axis_dir));

  lexer_parse_rbrace();

  cdi_write_paxs(info, &paxs);

  return;
}

static VOID undump_scma(CDI_INFO info)
{
  sCDI_SCMA scma;

  lexer_parse_lbrace();

  scma.cccc = CDI_CHUNK_TYPE_SCMA;

  undump_rgns_with_id(scma.regions);
  //lexer_parse_specific_id(CDI_CHUNK_TYPE_GMRF);
  //scma.geom_ref.Undump(info);
  undump_gmrf_with_id(info, &(scma.geom_ref));
  undump_int_with_id(&(scma.material));

  lexer_parse_rbrace();

  cdi_write_scma(info, &scma);

  return;
}

static VOID undump_mtpr(CDI_INFO info)
{
  sCDI_MTPR mtpr;

  lexer_parse_lbrace();

  mtpr.cccc = CDI_CHUNK_TYPE_MTPR;

  undump_namestr_with_id(&(mtpr.name));
  cdiINT32 numMaterials = lexer_parse_int();
  for (cdiINT32 im = 0; im < numMaterials; im++) {
    mtpr.materials.push_back(lexer_parse_int());
  }

  lexer_parse_rbrace();

  cdi_write_mtpr(info, &mtpr);

  return;
}

static VOID undump_rdsc(CDI_INFO info)
{
  sCDI_RDSC rdsc;

  lexer_parse_lbrace();

  rdsc.cccc = CDI_CHUNK_TYPE_RDSC;

  undump_namestr_with_id(&(rdsc.name));

  undump_parm_with_id(&(rdsc.emissivity));

  lexer_parse_rbrace();

  cdi_write_rdsc(info, &rdsc);

  return;
}

void undump_scpr(CDI_INFO cdi_info, CDI_SCPR scpr)
{
  lexer_parse_lbrace();
  scpr->cccc = CDI_CHUNK_TYPE_SCPR;

  undump_enum_with_id(&(scpr->criteria));
  undump_enum_with_id(&(scpr->mesh_keep_option));

  if (scpr->criteria == eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Material) {
    undump_enum_with_id(&(scpr->mesh_keep_property));
    undump_int_with_id(&(scpr->material_precedence_list_index));
  }

  lexer_parse_rbrace();
}

void undump_stcp(CDI_INFO cdi_info, sCDI_STCP& stcp)
{
  lexer_parse_lbrace();
  stcp.cccc = CDI_CHUNK_TYPE_STCP;
  
  undump_enum_with_id(&(stcp.enabled));
  undump_enum_with_id(&(stcp.contact_extent));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 3)) {
    undump_parm_with_id(&(stcp.max_separation));
    undump_parm_with_id(&(stcp.max_angle));
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 7)) {
    undump_bool_with_id(&(stcp.close_gaps));
  }
  undump_enum_with_id(&(stcp.heat_uniform));
  undump_enum_with_id(&(stcp.join_parts));
  undump_enum_with_id(&(stcp.mesh_to_keep));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 12)) {
    if (stcp.mesh_to_keep == eCDI_THERMAL_CONTACT_MESH::UserPrecedence) {
      lexer_parse_specific_id(CDI_CHUNK_TYPE_SCPL);
      lexer_parse_lbrace();
      char cccc_string[5];
      while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4) &&
        cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_SCPR)
      {
        sCDI_SCPR scpr;
        undump_scpr(cdi_info, &scpr);
        stcp.mesh_precedence_list.push_back(scpr);
      }
    }
  }
  else {
    // For older CDIs, need to convert mesh_to_keep into a precedence list
    if (stcp.mesh_to_keep != eCDI_THERMAL_CONTACT_MESH::SameAsParent) {
      sCDI_SCPR scpr = cdi_convert_old_mesh_enum_to_scpr(stcp.mesh_to_keep);
      stcp.mesh_precedence_list.push_back(scpr);
      // We can uncomment this once the discretizer starts using
      // the GetMeshPrecedenceForPair API
      //stcp.mesh_to_keep = eCDI_THERMAL_CONTACT_MESH::UserPrecedence;
    }
  }

  lexer_parse_rbrace();
}

void undump_stcr(CDI_INFO cdi_info)
{
  sCDI_STCR stcr;
  lexer_parse_lbrace();

  undump_namestr_with_id(&stcr.region_name);
  undump_int_with_id(&stcr.parent_index);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 14)) {
    lexer_parse_specific_id(CDI_CHUNK_TYPE_GMRF);
    stcr.geometry_1.Undump(cdi_info);
    lexer_parse_specific_id(CDI_CHUNK_TYPE_GMRF);
    stcr.geometry_2.Undump(cdi_info);
  }
  else {
    cdiINT32 face1_index = -1;
    cdiINT32 face2_index = -1;
    undump_int_with_id(&face1_index);
    undump_int_with_id(&face2_index);
    if (face1_index >= 0 && face2_index >= 0) {
      stcr.geometry_1.face_list.push_back(face1_index);
      stcr.geometry_2.face_list.push_back(face2_index);
    }
    undump_rgns_with_id(stcr.geometry_1.rgn_list);
    undump_rgns_with_id(stcr.geometry_2.rgn_list);
  }
  
  // Contact Parameters
  lexer_parse_specific_id(CDI_CHUNK_TYPE_STCP);
  undump_stcp(cdi_info, stcr.contact_parameters);

  // Physics type
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 2))
    undump_int_with_id(&stcr.surf_props_index);

  lexer_parse_rbrace();

  cdi_write_stcr(cdi_info, &stcr);
}

static void UndumpPowerByInfo(sCDI_INFO* pCDI_Info)
{
  sCDI_PWBY pwbyInfo;
  lexer_parse_lbrace();
  undump_bool_with_id(&pwbyInfo.m_isAPowerByFile);
  undump_strg_with_id(&pwbyInfo.m_itemName);
  undump_strg_with_id(&pwbyInfo.m_itemId);
  lexer_parse_rbrace();
  pwbyInfo.WriteToCDI(pCDI_Info);
}

void sCDI_SCLR::Undump(CDI_INFO cdi_info)
{
  lexer_parse_lbrace();

  undump_strg_with_id(&uds_name);
  undump_strg_with_id(&unit_type);
  undump_strg_with_id(&unit_class_expression);
  undump_parm_with_id(&diffusion_coefficient);
  undump_parm_with_id(&scalar_turb_schmidt_number);
  undump_parm_with_id(&scalar_source_term);
  undump_bool_with_id(&allow_negative_values);
  undump_parm_with_id(&minimum_value);
  undump_parm_with_id(&maximum_value);
  undump_parm_with_id(&default_initial_condition);
  undump_parm_with_id(&molecular_weight);
  lexer_parse_rbrace();
}

static VOID undump_scls(CDI_INFO cdi_info)
{
  lexer_parse_lbrace();   // Opening '{' after 'sclr'

  cCDI_SCALARS scalars;

  char cccc_string[5];
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4)
    &&
    cio_string_to_type(cccc_string) == sCDI_SCLR::GetScalarChunkType())
  {
    sCDI_SCLR scalar;
    scalar.Undump(cdi_info);
    scalars.m_scalars.push_back(scalar);
  }

  if (cdi_info)
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SCLS) {
      scalars.WriteToCDI(cdi_info);
    }
  }
}

static void UndumpPartitionsInfo(CDI_INFO cdi_info)
{
  // Older cases can just undump the chunks
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
    lexer_parse_lbrace();
    cdi_push(cdi_info, CDI_CHUNK_TYPE_PSDF);
    while (undump_chunk(cdi_info)) {};
    cdi_pop(cdi_info);
    return;
  }

  cCDI_PARTITIONS partitions;
  partitions.Undump(cdi_info);
}

// For some reason, using virtual and override methods will not compile. Probably
// something about being in multiple files (?) If someone wants to figure out how
// to get this to work, be my guest.

//void cCDI_RADIATION_PATCH_BASE::Undump(CDI_INFO cdi_info)
void UndumpRadiationPatchBase(cCDI_RADIATION_PATCH_BASE* basePatch, CDI_INFO cdi_info)
{
  undump_gmrf_with_id(cdi_info, &(basePatch->face_geom));
  undump_parm_with_id(&(basePatch->angle_tolerance));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
    undump_parm_with_id(&(basePatch->patch_size));
  }
}

//void cCDI_STD_RADIATION_PATCH::Undump(CDI_INFO cdi_info)
void UndumpRadiationPatch(cCDI_STD_RADIATION_PATCH* stdPatch, CDI_INFO cdi_info)
{
  //cCDI_RADIATION_PATCH_BASE::Undump(cdi_info);
  UndumpRadiationPatchBase(stdPatch, cdi_info);

  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
    undump_parm_with_id(&(stdPatch->patch_size));
  }
}

//void cCDI_AXISYM_RADIATION_PATCH::Undump(CDI_INFO cdi_info)
void UndumpRadiationPatch(cCDI_AXISYM_RADIATION_PATCH* axiPatch, CDI_INFO cdi_info)
{
  //cCDI_RADIATION_PATCH_BASE::Undump(cdi_info);
  UndumpRadiationPatchBase(axiPatch, cdi_info);

  undump_int_with_id(&(axiPatch->axis_preferred_csys_index));
  undump_dbls_with_id((idFLOAT*)(axiPatch->axis_origin));
  undump_dbls_with_id((idFLOAT*)(axiPatch->axis_dir));
}

void cCDI_RADIATION_PATCHES::Undump(CDI_INFO cdi_info)
{
  lexer_parse_lbrace();   // Opening '{' after 'rdps'

  char cccc_string[5];
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4))
  {
    if (cio_string_to_type(cccc_string) == cCDI_STD_RADIATION_PATCH::GetChunkType()) {
      lexer_parse_lbrace();
      cCDI_STD_RADIATION_PATCH* strp = new cCDI_STD_RADIATION_PATCH();
      UndumpRadiationPatch(strp, cdi_info);
      lexer_parse_rbrace();
      AddPatch(strp);
    }
    else if (cio_string_to_type(cccc_string) == cCDI_AXISYM_RADIATION_PATCH::GetChunkType()) {
      lexer_parse_lbrace();
      cCDI_AXISYM_RADIATION_PATCH* axrp = new cCDI_AXISYM_RADIATION_PATCH();
      UndumpRadiationPatch(axrp, cdi_info);
      lexer_parse_rbrace();
      AddPatch(axrp);
    }
  }

  if (cdi_info)
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_RDPS) {
      WriteToCDI(cdi_info);
    }
  }
}

void UndumpRadiationPatches(CDI_INFO cdi_info)
{
  cCDI_RADIATION_PATCHES radPatches;
  radPatches.Undump(cdi_info);
}

void cCDI_UDDC::Undump(CDI_INFO cdi_info)
{
  lexer_parse_lbrace();

  undump_strg_with_id(&m_name);
  undump_strg_with_id(&m_unitsClass);
  undump_strg_with_id(&m_units);

  // Retrieve column data
  m_data = undump_dbls_with_id(nullptr);

  lexer_parse_rbrace();
}

void cCDI_UDDS::Undump(CDI_INFO cdi_info)
{
  lexer_parse_lbrace();

  undump_strg_with_id(&m_name);

  char cccc_string[5];
  if (lexer_parse_n_char_id_or_rbrace(cccc_string, 4) && cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_UDDC) {
    m_xData.Undump(cdi_info);

    while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4) && cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_UDDC) {
      cCDI_UDDC y_col;
      y_col.Undump(cdi_info);
      m_yData.push_back(y_col);
    }
  }
}

void cCDI_UDDL::Undump(CDI_INFO cdi_info)
{
  lexer_parse_lbrace();

  char cccc_string[5];
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4) && cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_UDDS) {
    cCDI_UDDS dataset;
    dataset.Undump(cdi_info);
    m_userDataCurves.push_back(dataset);
  }
}

void undump_uddl(CDI_INFO cdi_info)
{
  cCDI_UDDL datacurves;
  datacurves.Undump(cdi_info);

  datacurves.WriteToCDI(cdi_info);
}

/*** Upon entry to this function, the ascii file is positioned just before the left brace
 *** which begins a chunk whose contents is exclusively other chunks (ie, no scalars).
 *** The CCCC of this chunk is given as an argument.  We undump this chunk by pushing the
 *** CCCC and undumping each chunk we find.
 */
BOOLEAN undump_chunk (CDI_INFO cdi_info);
VOID undump_general_chunk (CDI_INFO cdi_info, CIO_CCCC cccc)
{
  lexer_parse_lbrace();

  cdi_push(cdi_info, cccc);

  /** Loop dumping child chunks, until we pass the right brace (indicated by undump_chunk
   ** returning FALSE).
   */
  while (undump_chunk(cdi_info)) {};

  cdi_pop(cdi_info);

  return;
}


/*** Upon entry to this function, the ascii file may be positioned at the beginning of
 *** any kind of chunk, or at a right brace which ends an enclosing chunk.  If the former,
 *** we undump the chunk and return TRUE.  If the latter, we just parse past the right brace
 *** and return FALSE.
 */
BOOLEAN undump_chunk (CDI_INFO cdi_info)
{
  char cccc_string[5];
  BOOLEAN id_p = lexer_parse_n_char_id_or_rbrace(cccc_string, 4);

  if (!id_p) {
    return(FALSE);
  } else {
    uINT32 chunk_type = cio_string_to_type(cccc_string);
    
    std::string cChunkType(cccc_string, 4);
    
    switch(chunk_type) {
      case CDI_CHUNK_TYPE_SCCT: {undump_scct(cdi_info); break;}
      case CDI_CHUNK_TYPE_CMDL: {undump_cmdl(cdi_info); break;}
      case CDI_CHUNK_TYPE_SCBC: {undump_scbc(cdi_info); break;}
      case CDI_CHUNK_TYPE_CPLW: {undump_cplw(cdi_info); break;}
      case CDI_CHUNK_TYPE_FCMP: {undump_fcmp(cdi_info); break;}
      case CDI_CHUNK_TYPE_PTGE: {undump_ptge(cdi_info); break;}
      case CDI_CHUNK_TYPE_BBOX: {undump_bbox(cdi_info); break;}
      case CDI_CHUNK_TYPE_CMNT: {undump_cmnt(cdi_info); break;}
      case CDI_CHUNK_TYPE_AUDT: {undump_audt(cdi_info); break;}
      case CDI_CHUNK_TYPE_UNDB: {undump_undb(cdi_info); break;}
      case CDI_CHUNK_TYPE_EQNS: {undump_eqns(cdi_info); break;}
      case CDI_CHUNK_TYPE_GTBL: {undump_gtbl(cdi_info); break;}
      case CDI_CHUNK_TYPE_TABL: {undump_tabl(cdi_info); break;}
      case CDI_CHUNK_TYPE_CSYS: {undump_csys(cdi_info); break;}
      case CDI_CHUNK_TYPE_RGPN: {undump_rgpn(cdi_info); break;}
      case CDI_CHUNK_TYPE_VRTX: {undump_vrtx(cdi_info); break;}
      case CDI_CHUNK_TYPE_FACT: {undump_fact(cdi_info); break;}
      case CDI_CHUNK_TYPE_EDGE: {undump_edge(cdi_info); break;}
      case CDI_CHUNK_TYPE_FACE: {undump_face(cdi_info); break;}
      case CDI_CHUNK_TYPE_MDLV: {undump_model_view(cdi_info); break;}
      case CDI_CHUNK_TYPE_PSMV: {undump_psmv(cdi_info); break;}
      case CDI_CHUNK_TYPE_NULL: {undump_null(cdi_info); break;}
      case CDI_CHUNK_TYPE_NAME: {undump_name(cdi_info); break;}
      case CDI_CHUNK_TYPE_BOOL: {undump_bool(cdi_info); break;}
      case CDI_CHUNK_TYPE_INT_: {undump_int_(cdi_info); break;}
      case CDI_CHUNK_TYPE_ENUM: {undump_enum(cdi_info); break;}
      case CDI_CHUNK_TYPE_STRG: {undump_strg(cdi_info); break; }
      case CDI_CHUNK_TYPE_OFFS: {undump_offs(cdi_info); break;}
      case CDI_CHUNK_TYPE_PRGN: {undump_prgn(cdi_info); break;}
      case CDI_CHUNK_TYPE_MPRM: {undump_mprm(cdi_info); break;}
      case CDI_CHUNK_TYPE_MFLT: {undump_mflt(cdi_info); break;}
      case CDI_CHUNK_TYPE_MREF: {undump_mref(cdi_info); break;}
      case CDI_CHUNK_TYPE_MDEV: {undump_mdev(cdi_info); break;} 
      case CDI_CHUNK_TYPE_MSTP: {undump_mstp(cdi_info); break;}
      case CDI_CHUNK_TYPE_PTYP: {undump_ptyp(cdi_info); break;}
      case CDI_CHUNK_TYPE_HXCH: {undump_hxch(cdi_info); break;}
      case CDI_CHUNK_TYPE_AMHX: {undump_amhx(cdi_info); break;}
      case CDI_CHUNK_TYPE_VPNT: {undump_vpnt(cdi_info); break;}
      case CDI_CHUNK_TYPE_CDSR: {undump_cdsr(cdi_info); break;}
      case CDI_CHUNK_TYPE_IVDP: {undump_ivdp(cdi_info); break;}
      case CDI_CHUNK_TYPE_CVDP: {undump_cvdp(cdi_info); break;}
      case CDI_CHUNK_TYPE_BSDP: {undump_bsdp(cdi_info); break;}
      case CDI_CHUNK_TYPE_PNTS: {undump_pnts(cdi_info); break;}
      case CDI_CHUNK_TYPE_EQDP: {undump_eqdp(cdi_info); break;}
      case CDI_CHUNK_TYPE_RGNS: {undump_rgns(cdi_info); break;}
      case CDI_CHUNK_TYPE_FDLT: {undump_fdlt(cdi_info); break;}
      case CDI_CHUNK_TYPE_GFAR: {undump_gfar(cdi_info); break;}
      case CDI_CHUNK_TYPE_GAPD: {undump_gapd(cdi_info); break;}
      case CDI_CHUNK_TYPE_GSCC: {undump_gscc(cdi_info); break;}
      case CDI_CHUNK_TYPE_GSEP: {undump_gsep(cdi_info); break;}
      case CDI_CHUNK_TYPE_SIMV: {undump_simv(cdi_info); break;}
      case CDI_CHUNK_TYPE_THMA: {undump_thma(cdi_info); break;}
      case CDI_CHUNK_TYPE_SYMP: {undump_symp(cdi_info); break;}
      case CDI_CHUNK_TYPE_UNIT: {undump_unit(cdi_info); break;}
      case CDI_CHUNK_TYPE_UUNT: {undump_uunt(cdi_info); break;}
      case CDI_CHUNK_TYPE_CPRP: {undump_cprp(cdi_info); break;}
      case CDI_CHUNK_TYPE_GHDR: {undump_ghdr(cdi_info); break;}
      case CDI_CHUNK_TYPE_KESC: {undump_kesc(cdi_info); break;} //kEps_SuperCycle
      case CDI_CHUNK_TYPE_CPNT: {undump_cpnt(cdi_info); break;}
      case CDI_CHUNK_TYPE_MENT: {undump_ment(cdi_info); break;}
      case CDI_CHUNK_TYPE_PRBE: {undump_prbe(cdi_info); break;}
      case CDI_CHUNK_TYPE_FLST: {undump_flst(cdi_info); break;}
      case CDI_CHUNK_TYPE_MFAC: {undump_mfac(cdi_info); break;}
      case CDI_CHUNK_TYPE_RGCT: {undump_rgct(cdi_info); break;}
      case CDI_CHUNK_TYPE_RGNN: {undump_rgnn(cdi_info); break;}
      case CDI_CHUNK_TYPE_RGDP: {undump_rgdp(cdi_info); break;}
      case CDI_CHUNK_TYPE_GRDF: {undump_grdf(cdi_info); break;}
      case CDI_CHUNK_TYPE_PRDF: {undump_prdf(cdi_info); break;}
      case CDI_CHUNK_TYPE_PREC: {undump_prec(cdi_info); break;}
      case CDI_CHUNK_TYPE_PRTT: {undump_prtt(cdi_info); break;}
      case CDI_CHUNK_TYPE_SGID: {undump_sgid(cdi_info); break;}
      case CDI_CHUNK_TYPE_SGRF: {undump_sgrf(cdi_info); break;}
      case CDI_CHUNK_TYPE_PPRF: {undump_pprf(cdi_info); break;}
      case CDI_CHUNK_TYPE_PARM: {undump_parm(cdi_info); break;}
      case CDI_CHUNK_TYPE_DBLS: {undump_dbls(cdi_info); break;}
      case CDI_CHUNK_TYPE_ACCR: {undump_accr(cdi_info); break;}
      case CDI_CHUNK_TYPE_PGLB: {undump_pglb(cdi_info); break;}
      case CDI_CHUNK_TYPE_PRMT: {undump_prmt(cdi_info); break;}
      case CDI_CHUNK_TYPE_ECGS: {UndumpEmitterConfigurations(cdi_info); break;}
      case CDI_CHUNK_TYPE_PEMC: {UndumpEmitters(cdi_info); break;}
      case CDI_CHUNK_TYPE_SRMI: {undump_srmi(cdi_info); break;}
      case CDI_CHUNK_TYPE_SCRN: {undump_scrn(cdi_info); break;}
      case CDI_CHUNK_TYPE_VHCL: {undump_vhcl(cdi_info); break;}
      case CDI_CHUNK_TYPE_AMW_: {undump_amw_(cdi_info); break;}
      case CDI_CHUNK_TYPE_WIPR: {undump_wipr(cdi_info); break;}
      case CDI_CHUNK_TYPE_MNTS: {undump_mnts(cdi_info); break;}
      case CDI_CHUNK_TYPE_MMBR: {undump_mmbr(cdi_info); break;}
      case CDI_CHUNK_TYPE_MPSG: {undump_mpsg(cdi_info); break;}
      case CDI_CHUNK_TYPE_AFTD: {undump_aftd(cdi_info); break;}
      case CDI_CHUNK_TYPE_LOOK: {undump_look(cdi_info); break;}
      case CDI_CHUNK_TYPE_WPDT: {undump_wpdt(cdi_info); break;}
      case CDI_CHUNK_TYPE_TIRE: {undump_tire(cdi_info); break;}
      case CDI_CHUNK_TYPE_BSRG: {undump_bsrg(cdi_info); break;}
      case CDI_CHUNK_TYPE_CLBR: {undump_clbr(cdi_info); break;}
      case CDI_CHUNK_TYPE_SCMT: {undump_scmt(cdi_info); break;}
      case CDI_CHUNK_TYPE_RDSC: {undump_rdsc(cdi_info); break;}
      case CDI_CHUNK_TYPE_MTPR: {undump_mtpr(cdi_info); break;}
      case CDI_CHUNK_TYPE_STCR: {undump_stcr(cdi_info); break;}
      case CDI_CHUNK_TYPE_PWBY: {UndumpPowerByInfo(cdi_info); break;}
      case CDI_CHUNK_TYPE_CSPH: {undump_csph(cdi_info); break;}
      case CDI_CHUNK_TYPE_PSDF: {UndumpPartitionsInfo(cdi_info); break; }
      case CDI_CHUNK_TYPE_GMRF: {undump_gmrf(cdi_info); break; }
      case CDI_CHUNK_TYPE_GEOS: {undump_geos(cdi_info); break; }
      case CDI_CHUNK_TYPE_RDPS: {UndumpRadiationPatches(cdi_info); break; }
      case CDI_CHUNK_TYPE_SCLS: {undump_scls(cdi_info); break; }
      case CDI_CHUNK_TYPE_PAXS: {undump_paxs(cdi_info); break; }
      case CDI_CHUNK_TYPE_UDDL: {undump_uddl(cdi_info); break; }
      case CDI_CHUNK_TYPE_SCMA: {undump_scma(cdi_info); break; }
      default: {
        lexer_parse_lbrace();
        cdi_push(cdi_info, chunk_type);

        /** Loop dumping child chunks, until we pass the right brace (indicated by undump_chunk
         ** returning FALSE).
         */
        while (undump_chunk(cdi_info)) {};

        cdi_pop(cdi_info);
        break;
      }
    }

    return(TRUE);
  }
}

static VOID usage(VOID) {
  fprintf(stderr, "Usage: %s [-no_audt_mods] <cdi filename> < <cdi_dump compatible ascii input>\n",cmd_name);
  exit(1);
}

//#include <unistd.h> 
//#include <iostream>
//#include <chrono>
//#include <thread>
int main (int argc, char *argv[])
{ 
	// Handy pause for attaching from Visual Studio
	//clock_t endWait = clock() + 20 * CLOCKS_PER_SEC;
	//while (clock() < endWait) {}
//  std::this_thread::sleep_for(std::chrono::seconds(15));

  //  usleep(15000000);
  asINT32 major_version, minor_version;
  CDI_INFO cdi_info = NULL;
  asINT32 cdi_open_err_status;

  /* Standard debug preamble */
  platform_exa_debug_pfx(&argc, argv);

  if (!(cmd_name = getenv("EXA_CURRENT_CMD"))) {
    cmd_name = argv[0];
  }


  bool versions_only_flag = false;
  bool help_only_flag = false;
  bool no_audt_mods_flag = false;
  std::vector<std::string> filenames;

  cARG_HELPER cmndLineArgHelpr;
  cmndLineArgHelpr.new_flag(0x00, "help", "print this help message" , help_only_flag);
  cmndLineArgHelpr.new_flag(0x00, "no_audt_mods", "no audit trail modification" , no_audt_mods_flag);
  cmndLineArgHelpr.set_string_vector("files", "<CDI filename>", filenames, true);
  cmndLineArgHelpr.set_description("Re-create a CDI file from structured dump");
  {
    std::string versionDesc = EXA_STR::Sprintf("print the version of %s", argv[0]);
    cmndLineArgHelpr.new_flag(0x00, "version", versionDesc.c_str(), versions_only_flag);
  }
  {
    const char *distname = platform_exa_distname();
    char *buf = EXA_MALLOC_ARRAY(char, strlen(CDI_VERSION) + (distname ? strlen(distname) : 0) + 4);
    sprintf(buf, "%s." CDI_VERSION, distname ? distname : "empty");
    cmndLineArgHelpr.set_version(buf);
  }

  cmndLineArgHelpr.set_author("Exa Corp., http://www.exa.com");
  cmndLineArgHelpr.set_name("undump_cdi");
  {
          std::string usageString("Usage: ");
          usageString += "undump_cdi";
          usageString += " [--no_audt_mods] [options] <cdi_filename>";
          cmndLineArgHelpr.set_usage(usageString.c_str());
  }
  //  cmndLineArgHelpr.set_build_date(EXA_BUILD_DATE);
  cmndLineArgHelpr.process(argc, argv);
  if (help_only_flag) {
          cmndLineArgHelpr.write_usage(stdout, 26);
          exit(0);
  }
  if (versions_only_flag) {
          cmndLineArgHelpr.write_version(stdout);
          exit(0);
  }

  if (filenames.size() != 1) {
          cmndLineArgHelpr.write_usage(stderr, 26);
          exit(-1);
  }

  strcpy(cdi_file_name, filenames[0].c_str());

  lexer_set_lineno(1);
  lexer_parse_specific_id(CDI_CHUNK_TYPE_XCDI);
  lexer_parse_lbrace();
  major_version = lexer_parse_int();
  minor_version = lexer_parse_int();

  lexer_parse_specific_id(CDI_CHUNK_TYPE_CASE);
  lexer_parse_lbrace();

  // Use large file offsets if the target CDI version is 4.2 or higher
  BOOLEAN large_file_offsets = CDI_VERSION_AT_LEAST(major_version, minor_version, 4, 2);

  cdi_info = cdi_open_for_write(cdi_file_name, major_version, minor_version, 
                                &cdi_open_err_status, large_file_offsets);
 
  if(cdi_info == NULL) {
    fprintf(stderr, "cannot open file %s, error %s\n", cdi_file_name,
	cdi_get_error_state_string(cdi_open_err_status));
    usage();
  };

  /** Loop dumping child chunks, until we pass the right brace (indicated by undump_chunk
   ** returning FALSE).
   */
  fflush(cdi_info->cio_info->fp);
  while (undump_chunk(cdi_info)) {  
    fflush(cdi_info->cio_info->fp);
  }

  cdi_close(cdi_info);

  return(0);
}
