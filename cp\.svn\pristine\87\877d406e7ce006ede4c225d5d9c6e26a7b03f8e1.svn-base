/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Octree used to find nearest point in a set of points to a given point.
 *
 * Mohit Jain, Exa Corporation, Dassault Systemes Simulia Corp.
 * Created Thu July 24, 2019
 *--------------------------------------------------------------------------*/

#include NERO_CGAL_H
#include "cp_nero.h"
#define INVALID_MSI -1

VOID mci_surfel_centroid_to_bg(const sriFLOAT centroid[3], sBG_POINT3d &m_point)
{
  sBG_POINT3d point(centroid[0], centroid[1], centroid[2]);
  m_point = point;
}

class cMSI_OCTREE
{
public:
  cMSI_OCTREE(MSI_TABLE msi_table, sBG_BOX3d range):m_msi_table(msi_table) {
    m_partition = new cMSI_OCTREE_NERO(this, range, INVALID_MSI);
#ifndef TBS_MOHIT //Define tolerance by some valid number
    m_tol = 10.0F;
#endif
  }
  ~cMSI_OCTREE();

  inline asINT32 GetBucketIndex(int id) {
    return id;
  }
  dFLOAT SqDistancePointToElement(const sBG_POINT3d& lookupPoint,
                                   int id,
                                   dFLOAT currentMatchDistSq,
                                   sBG_POINT3d& matchPointReturn) const
   {
     mci_surfel_centroid_to_bg(m_msi_table->get_centroid(id), matchPointReturn);
     return BgSquaredDistance(lookupPoint, matchPointReturn);
   }

   cNERO_CGAL_OCTANT_SET ElementSuboctantMembership(int id, const sBG_BOX3d& range) const
   {
     asINT32 octantIndex = 0;
     const sBG_POINT3d rangeCenter = range.Center();
     ccDOTIMES(coord, 3) {
       if (m_msi_table->get_centroid(id)[coord] >= rangeCenter[coord])
         octantIndex += (1 << (int)coord);
     }
     return cNERO_CGAL_OCTANT_SET(octantIndex);
   }

   auINT32 FindNearestMSI(const sBG_POINT3d &searchPoint, sBG_POINT3d &pt_found, dFLOAT tol) {
     return m_partition->FindNearestMatch(searchPoint, sNERO_MSI_LOOKUP_AUX(), pt_found, tol);
   }

   BOOLEAN QualifiesAsMatch(int tentativeMatch,
                            const sBG_POINT3d& lookupPoint,
                            const sNERO_MSI_LOOKUP_AUX& lookupInfo) const
   {
     return TRUE;
   }
   VOID MarkElement(int id) {}
   VOID ResetAllElementMarks() {}
   BOOLEAN IsElementMarked(int id) { return FALSE; }

   VOID insert(auINT32 id) {
     m_partition->Insert(id);
   }

   sBG_BOX3d GetOctantRange(const sBG_BOX3d &box, sINT32 octantIndex) const {
     return ::GetOctantRange<sBG_BOX3d>(box, octantIndex);
   }
 private:
   MSI_TABLE m_msi_table;

   typedef tNERO_CGAL<dFLOAT, cMSI_OCTREE, asINT32, sNERO_MSI_LOOKUP_AUX> cMSI_OCTREE_NERO;
   cMSI_OCTREE_NERO* m_partition;
   dFLOAT m_tol;
};

auINT32 cMSI_TABLE::find_nearest_mci(sFLOAT centroid[3], dFLOAT tol) {
  sBG_POINT3d point(centroid[0], centroid[1], centroid[2]);
  sBG_POINT3d rt_point;
  return m_octree->FindNearestMSI(point, rt_point, tol);
}
cMSI_TABLE::cMSI_TABLE(auINT32 n_points, sBG_BOX3d range):m_range(range) {
  m_centroid.reserve(n_points * 3);
  m_octree = new cMSI_OCTREE(this, m_range);
}

VOID cMSI_TABLE::set_centroids(SRI_SURFACE_FILE surface_meas_file, auINT32 n_points) {
  surface_meas_file->get_centroids(&m_centroid[0],n_points,0);
}

VOID cMSI_TABLE::insert_msi(auINT32 msi) {
  m_octree->insert(msi);
}
