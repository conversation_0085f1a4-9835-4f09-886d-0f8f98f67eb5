/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Octree used to find nearest point in a set of points to a given point.
 *
 * Mohit Jain, Exa Corporation, Dassault Systemes Simulia Corp.
 * Created Thu Jul 24, 2019
 *--------------------------------------------------------------------------*/
#include NERO_CGAL_H
#include "common.h"
typedef struct {
  dFLOAT centroid[3];
} MSI_SURFEL_CENTROID;
class cMSI_OCTREE;

struct sNERO_MSI_LOOKUP_AUX {};

typedef cMSI_OCTREE *MSI_OCTREE;

auINT32 nearest_mci_to_surfel();

class cMSI_TABLE {
public:

  cMSI_TABLE(auINT32 n_points, sBG_BOX3d range);
  auINT32 find_nearest_mci(sFLOAT centroid[3], dFLOAT tol);
  sriFLOAT *get_centroid(auINT32 id) {
    return &m_centroid[3*id];
  }
  VOID set_centroids(SRI_SURFACE_FILE surface_meas_file, auINT32 n_points);
  VOID insert_msi(auINT32 msi);
private:
  std::vector<sriFLOAT> m_centroid;
  MSI_OCTREE m_octree;
  sBG_BOX3d m_range;
};

typedef cMSI_TABLE *MSI_TABLE;
