/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#include ARG_HELPER_H
#include CIPHER_H


#include <stdio.h>
#include "cdi_common.h"


/*********** in support of test_cdi_write **********/
void write_shell(CDI_INFO cdi_info,int which);
void write_edges(CDI_INFO cdi_info,int which);
void write_facetable(CDI_INFO cdi_info,int which);
void write_facets(CDI_INFO cdi_info,int which);
void write_name(CDI_INFO cdi_info,const char *name);

void write_vertex(CDI_INFO cdi_info);
void write_geom(CDI_INFO cdi_info);
void write_case(CDI_INFO cdi_info);
void write_bounding_box(CDI_INFO cdi_info);
void write_comment(CDI_INFO cdi_info,const char *name,const char *value);
void write_face(CDI_INFO cdi_info,asINT32 count,asINT32 a,asINT32 b);
void write_measure(CDI_INFO cdi_info);
void write_null(CDI_INFO cdi_info);
void write_region(CDI_INFO cdi_info);
void write_shell_ref(CDI_INFO cdi_info);

static asINT32 geom_edges[][4] = {
	{ 5, 7, 0, 2},
	{ 1, 3, 2, 3},
	{ 4, 6, 3, 0},
	{ 0, 1, 3, 2},
	{ 3, 8, 2, 4},
	{ 2, 9, 4, 3},
	{ 0, 2, 0, 3},
	{ 0, 0, 2, 0},
	{ 1, 4, 4, 2},
	{ 1, 5, 3, 4},
	{ 5,11, 2, 1},
	{ 3,10, 1, 2},
	{ 5,13, 1, 0},
	{ 4,12, 0, 1},
	{ 2,15, 3, 1},
	{ 4,14, 1, 3},
	{ 3,17, 1, 4},
	{ 2,16, 4, 1}
	};
static asINT32 geom_edge_count = sizeof(geom_edges)/sizeof(geom_edges[0]);
static asINT32 grid_edges[][4] = {
	{ 10, 30,  5,  6},
	{  1,  3,  6,  7},
	{  7, 23,  7,  5},
	{  0,  1,  7,  6},
	{  2,  6,  6,  8},
	{  9, 28,  8,  7},
	{  1,  4,  8,  6},
	{  3,  9,  6, 10},
	{  9, 29, 10,  8},
	{  2,  7, 10,  6},
	{ 11, 35,  6,  4},
	{  4, 12,  4, 10},
	{  3, 11, 10,  4},
	{ 11, 34,  4,  0},
	{  5, 15,  0, 10},
	{  4, 14, 10,  0},
	{  6, 18,  0,  9},
	{  8, 25,  9, 10},
	{  5, 16,  9,  0},
	{  7, 21,  0,  7},
        {  8, 26,  7,  9},
	{  6, 19,  7,  0},
	{ 10, 31,  0,  5},
	{  0,  2,  5,  7},
	{  9, 27,  7, 10},
	{ 11, 17, 10,  9},
	{  6, 20,  9,  7},
	{  8, 24, 10,  7},
	{  1,  5,  7,  8},
	{  2,  8,  8, 10},
	{  0,  0,  6,  5},
	{  7, 22,  5,  0},
	{ 11, 33,  0,  6},
	{ 10, 32,  6,  0},
	{  8, 13,  0,  4},
	{  3, 10,  4,  6}
	};
static asINT32 grid_edge_count = sizeof(grid_edges)/sizeof(grid_edges[0]);

static asINT32 geom_facets[][4] = {
        { 0, 0, 1, 2},
	{ 1, 3, 4, 5},
        { 2,15, 9,17}, /* ??? */
        { 3, 8,10,16},
        { 4,12, 6,14},
        { 5, 7,13,11},
	};
static asINT32 geom_facet_count = sizeof(geom_facets)/sizeof(geom_facets[0]);

static asINT32 grid_facets[][4] = {
        {  0,  0,  1,  2},
	{  0,  3,  4,  5},
	{  1,  6,  7,  8},
	{  1,  9, 10, 11},
	{  2, 12, 13, 14},
	{  2, 15, 16, 17},
	{  3, 18, 19, 20},
	{  3, 21, 22, 23},
	{  4, 24, 25, 26},
	{  4, 27, 28, 29},
	{  5, 30, 31, 32},
	{  5, 33, 34, 35}
	};
static asINT32 grid_facet_count = sizeof(grid_facets)/sizeof(grid_facets[0]);


void write_bounding_box(CDI_INFO cdi_info)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_BBOX;
    idFLOAT tmp[6] = {0.0, 0.0, -1.0, 1.0, 1.0, 1.0};

    cio_push(cdi_info->cio_info, cccc);

    cdi_write_idFLOAT(cdi_info, tmp, 6);

    cio_pop(cdi_info->cio_info);
}

void write_comment(CDI_INFO cdi_info, const char *name,const char *value)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_CMNT;
    asINT32 name_len, value_len;

    cio_push(cdi_info->cio_info, cccc);

    name_len = (sINT32)strlen(name);
    value_len = (sINT32)strlen(value);
    cdi_write_asINT32(cdi_info, &name_len, 1);
    cdi_write_asINT32(cdi_info, &value_len, 1);
    cdi_write_chars(cdi_info, name, name_len);
    cdi_write_chars(cdi_info, value, value_len);

    cio_pop(cdi_info->cio_info);
}

void write_case(CDI_INFO cdi_info)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_CASE;

    cio_push(cdi_info->cio_info, cccc);

    write_bounding_box(cdi_info);

    write_comment(cdi_info,"title","2 tetrahedra");
    write_null(cdi_info);
    /* flud */
    /* vprp */
    write_region(cdi_info);
    /* another regn */
    /* grid */
    write_measure(cdi_info);

    /* cio_push(cio,cccc, neutral); */

    write_geom(cdi_info);

    /* cio_pop(cio); */
}

void write_geom(CDI_INFO cdi_info)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_GEOM;

    cio_push(cdi_info->cio_info, cccc);
    write_vertex(cdi_info);
    write_shell(cdi_info, 0);
    write_shell(cdi_info, 1);
    cio_pop(cdi_info->cio_info);
}

void write_vertex(CDI_INFO cdi_info)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_VRTX;
    static idFLOAT coord[][3] = { 
        {0.0, 0.0, -1.0},
        {0.0, 0.0,  0.0},
        {0.0, 1.0,  0.0},
        {1.0, 0.0,  0.0},
        {0.0, 0.0,  1.0},
        {0.0, 1.0, -1.0},
        {0.0, 1.0,  1.0},
        {1.0, 1.0, -1.0},
        {1.0, 1.0,  1.0},
        {1.0, 0.0, -1.0},
        {1.0, 0.0,  1.0}
    };
    asINT32 count = sizeof(coord)/sizeof(coord[0]);

    cio_push(cdi_info->cio_info, cccc);

    cdi_write_asINT32(cdi_info, &count, 1);
    cdi_write_idFLOAT(cdi_info, (idFLOAT *) coord, 3 * count);

    cio_pop(cdi_info->cio_info);
}

void write_shell(CDI_INFO cdi_info, int which)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_SHLL;

    cio_push(cdi_info->cio_info, cccc);
    write_comment(cdi_info, "name","outer geometry shell");
    write_facetable(cdi_info, which);
    write_facets(cdi_info, which);
    write_edges(cdi_info, which);
    cio_pop(cdi_info->cio_info);
}

void write_facetable(CDI_INFO cdi_info, int which)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_FTAB;

    cio_push(cdi_info->cio_info, cccc);
    if(which)
    {	write_face(cdi_info,2, 0, 1);
	write_face(cdi_info,2, 2, 3);
	write_face(cdi_info,2, 4, 5);
	write_face(cdi_info,2, 6, 7);
	write_face(cdi_info,2, 8, 9);
	write_face(cdi_info,2,10,11);
    }
    else
    {	write_face(cdi_info,1,0,-1);
	write_face(cdi_info,1,1,-1);
	write_face(cdi_info,1,2,-1);
	write_face(cdi_info,1,3,-1);
	write_face(cdi_info,1,4,-1);
	write_face(cdi_info,1,5,-1);
    }
    cio_pop(cdi_info->cio_info);
}

void write_facets(CDI_INFO cdi_info,int which)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_FACT;
    asINT32 *facets;
    asINT32 count;

    if(which)
    {	facets = &grid_facets[0][0];
	count = grid_facet_count;
    }
    else
    {	facets = &geom_facets[0][0];
	count = geom_facet_count;
    }

    cio_push(cdi_info->cio_info, cccc);

    cdi_write_asINT32(cdi_info, &count, 1);
    cdi_write_asINT32(cdi_info, facets, 4 * count);

    cio_pop(cdi_info->cio_info);
}

void write_edges(CDI_INFO cdi_info, int which)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_EDGE;
    asINT32 count;
    asINT32 *edges;

    if(which)
    {	edges = &grid_edges[0][0];
	count = grid_edge_count;
    }
    else
    {	edges = &geom_edges[0][0];
	count = geom_edge_count;
    }

    cio_push(cdi_info->cio_info, cccc);

    cdi_write_asINT32(cdi_info, &count, 1);
    cdi_write_asINT32(cdi_info, edges, 4 * count);

    cio_pop(cdi_info->cio_info);
}

void write_face(CDI_INFO cdi_info, asINT32 count,asINT32 a,asINT32 b)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_FACE;
    asINT32 tmp = -1;

    cio_push(cdi_info->cio_info, cccc);

    cdi_write_asINT32(cdi_info, &tmp, 1);
    cdi_write_asINT32(cdi_info, &tmp, 1);
    tmp = 1;
    cdi_write_asINT32(cdi_info, &tmp, 1);
    tmp = 0;
    cdi_write_asINT32(cdi_info, &tmp, 1);
    cdi_write_asINT32(cdi_info, &count, 1);

    if(count >= 1) {
      cdi_write_asINT32(cdi_info, &a, 1);
    }
    if(count >= 2) {
      cdi_write_asINT32(cdi_info, &b, 1);
    }

    cio_pop(cdi_info->cio_info);
}

void write_measure(CDI_INFO cdi_info)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_MESR;
    asINT32 start_time = 0;
    asINT32 end_time = 20;
    asINT32 period = 1;
    asINT32 avg_period = 1;
    asINT32 variables = 1;
    const char *filename = "/usr/examples/output.nc";
    asINT32 tmp;

    cio_push(cdi_info->cio_info, cccc);

    cdi_write_asINT32(cdi_info, &start_time, 1);
    cdi_write_asINT32(cdi_info, &end_time, 1);
    cdi_write_asINT32(cdi_info, &period, 1);
    cdi_write_asINT32(cdi_info, &avg_period, 1);
    cdi_write_asINT32(cdi_info, &variables, 1);
    cdi_write_cdistring(cdi_info, filename);
    tmp = 1;
    cdi_write_asINT32(cdi_info, &tmp, 1);
    tmp = 0;
    cdi_write_asINT32(cdi_info, &tmp, 1);

    cio_pop(cdi_info->cio_info);
}

void write_null(CDI_INFO cdi_info)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_NULL;
    asINT32 data[] = { 0x01, 0xff, 0x02, 0xfe, 0x03, 0xfd, 0x04, 0xfc, 0xff, 0xfe };
    asINT32 tmp;

    cio_push(cdi_info->cio_info, cccc);

    tmp = sizeof(data)/sizeof(data[0]);
    cdi_write_asINT32(cdi_info, &tmp, 1);
    cdi_write_asINT32(cdi_info, data, tmp);

    cio_pop(cdi_info->cio_info);
}

void write_region(CDI_INFO cdi_info)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_REGN;

    cio_push(cdi_info->cio_info, cccc);
    write_shell_ref(cdi_info);
    write_name(cdi_info,"random name");
    cio_pop(cdi_info->cio_info);
}

void write_shell_ref(CDI_INFO cdi_info)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_SHLF;
    asINT32 outer = 1;
    asINT32 region = 0;

    cio_push(cdi_info->cio_info, cccc);

    cdi_write_asINT32(cdi_info, &outer, 1);
    cdi_write_asINT32(cdi_info, &region, 1);

    cio_pop(cdi_info->cio_info);
}

void write_name(CDI_INFO cdi_info,const char *name)
{   CIO_CCCC cccc = CDI_CHUNK_TYPE_NAME;

    cio_push(cdi_info->cio_info, cccc);

    cdi_write_cdistring(cdi_info, name);

    cio_pop(cdi_info->cio_info);
}

void test_cdi_write()
{
  CDI_INFO cdi_info = cdi_open_for_write("test.cdi", -1, -1, NULL);

  write_case(cdi_info);
  
  cdi_close(cdi_info);
}

/*********** in support of cdi_test_ **********/

/**
 * Guard class: prints on constructor, print on destructor
 * could add a timer
 */
class TestMarker 
{
private:  
  std::string m_tag;


public:

  static int ERROR;

  TestMarker(std::string tag):
    m_tag(tag)
  { 
    std::cout << " ************* start test " << tag << std::endl;
    TestMarker::ERROR = 0;
  }
  
  ~TestMarker()
  { 
    if (TestMarker::ERROR != 0)
      std::cout << " ************* failed " << std::endl; 
    else
      std::cout << " ************* success " << std::endl; 
    std::cout << " ************* end test " << m_tag << std::endl; 
  }

};

int TestMarker::ERROR = 0;

void
print_indent(int depth)
{
  int i;

  for(i=0;i<depth;i++)
    printf("  ");
}

/*** Given a string s, returns a string which is s surrounded by double quotes, and
 *** with a backslash inserted before each double quote and backslash contained in s.
 *** This effectively converts s into a representation which can be unambiguously
 *** parsed later.
 */
static STRING quotify_and_slashify_string (cSTRING src)
{
  if (src == NULL)
    src = "";

  /** Worst case is we have to add two quotes plus a backslash for every character! */
  STRING dest = (STRING)exa_malloc(2 * strlen(src) + 3, 
				   "quotify_and_slashify_string",
				   "a new string");

  dest[0] = '"';

  {
    CHARACTER *next_dest_char = dest + 1;
    const CHARACTER *next_src_char = src;

    /** For some unknown reason, the SGI compiler will complain unless the ugly
     ** casts to (int) are present in the next two lines...
     */
    while ((int)(*next_src_char) != (int)'\0') {
      if ((int)(*next_src_char) == (int)'"' || (int)(*next_src_char) == (int)'\\') {
	*next_dest_char = '\\';
	next_dest_char++;
      }
      *next_dest_char = *next_src_char;
      next_dest_char++;
      next_src_char++;
    }

    *next_dest_char = '"';
    next_dest_char++;
    *next_dest_char = '\0';
  }
  return(dest);
}


int open_cdi_check_version(const char *fileName, CDI_INFO &cdi_info)
{
  asINT32 error_code = 0;
  cdi_info = cdi_open_for_read(fileName, &error_code);
  if (cdi_info == NULL) {
    return 1;
  }

  if (CDI_VERSION_NEWER(cdi_major_version(cdi_info), cdi_minor_version(cdi_info))) {
    msg_error("This CDI file uses version %d.%d of the CDI format. This version\n"
              "of dump_cdi only works with CDI file versions %d.%d and earlier.\n",
              cdi_major_version(cdi_info), cdi_minor_version(cdi_info),
              CDI_MAJOR_VERSION, CDI_MINOR_VERSION);
    return 1;
  }
  
  printf("%s opened. Version (major minor) :  %5d %5d\n", fileName,
	 cdi_major_version(cdi_info), cdi_minor_version(cdi_info));
  
  return 0;
}
void
print_ptge(CDI_INFO cdi_info)
{
  /* allocate and read a ptge */
  CDI_PTGE ptge = cdi_read_ptge(cdi_info);
  STRING slashified_powercase_version = quotify_and_slashify_string(ptge->powercase_version);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 0)) {
    printf(" { %" SCALAR_PRINTF_INT64 "d"
           " %" SCALAR_PRINTF_INT64 "d"
           " %" SCALAR_PRINTF_INT64 "d %s "
           " %" SCALAR_PRINTF_INT64 "d }\n",
           ptge->case_file_id,
           ptge->case_geometry_id,
           ptge->cdi_file_id,
           slashified_powercase_version,
           ptge->encryption_version_id);
  } else {
  printf(" { %" SCALAR_PRINTF_INT64 "d"
           " %" SCALAR_PRINTF_INT64 "d"
           " %" SCALAR_PRINTF_INT64 "d %s }\n",
	 ptge->case_file_id,
	 ptge->case_geometry_id,
	 ptge->cdi_file_id,
	 slashified_powercase_version);
  }

  /* free the ptge memory */
  cdi_destroy_ptge(ptge);
}

void
print_rgpn(CDI_INFO cdi_info, int depth)
{
  /* allocate and read a rgpn */
  CDI_RGPN rgpn = cdi_read_rgpn(cdi_info);
  asINT32 num = rgpn->regionIds.size();
  printf(" { %d  (number of regions)\n", (int) num);
  //  print_indent(depth+1);
  for (size_t i = 0; i < rgpn->regionIds.size(); i++) {
    print_indent(depth+1);
    printf("   %5d %d (protection)\n", rgpn->regionIds[i], rgpn->protectionIds[i]);
  }

  print_indent(depth);
  printf("}\n");

  cdi_destroy_rgpn(rgpn);
}
static uINT16 uDUMP_CDI_DFLOAT_DIG=9;
static int Vrtx_number=0;
void
print_vrtx(CDI_VRTX vrtx)
{  
  int depth = 0;
  asINT32 num = vrtx->n_vertex;
    
  printf(" { %5d      (number of verts)\n",(int) num);
  print_indent(depth+1);
  
  int i,imax=3*vrtx->n_vertex;
  
  printf("     ( x         y         z)\n");
  for(i=0;i<imax;i+=3) {
    print_indent(depth+1);
    printf("%*.*g %*.*g %*.*g   (vrtx %d)\n",
           DFLOAT_DIG+4,
           uDUMP_CDI_DFLOAT_DIG,
           vrtx->coord[i],
           DFLOAT_DIG+4,
           uDUMP_CDI_DFLOAT_DIG,
           vrtx->coord[i+1],
           DFLOAT_DIG+4,
           uDUMP_CDI_DFLOAT_DIG,
           vrtx->coord[i+2],
           Vrtx_number++);
  }
  print_indent(depth);
  printf("}\n");
}

/*********** in support of cdi_test_find **********/

int cdi_test_find(CDI_INFO cdi_info)
{
  // Check what's in the file, pick a few random types
  std::set<CIO_CCCC> to_find;
  to_find.insert(CDI_CHUNK_TYPE_UNDB);
  to_find.insert(CDI_CHUNK_TYPE_VRTX);
  to_find.insert(CDI_CHUNK_TYPE_MEAS);
  
  std::set<CIO_CCCC> found;

  for (int iScan = 1; iScan < 3; ++iScan ) {
    printf( " scan the case for top level chunks\n");
    cdi_find_chunks(cdi_info, to_find, found);

    for (auto ctype: found) {
      char type_str[10];
      cio_type_to_string(ctype, type_str);
      std::string typeStr(&type_str[0],4);
      printf(" found chunk %s\n",typeStr.c_str());
    }
    found.clear();

    // And call find chunks again to verify it repositioned
    // the parser at its initial position
  }
  return 0;
}

/*********** in support of cdi_test_rewind **********/
int cdi_test_rewind(CDI_INFO cdi_info)
{
  CIO_ERRCODE error = CIO_ERR_SUCCESS;

  cdi_find_and_read_ptge(cdi_info, false);

  // Scan file
  for (int iScan = 1; iScan < 3; ++iScan ) {
    int numChunks = 0;
    do {
      error = cio_descend(cdi_info->cio_info);
      if (error != CIO_ERR_FAIL)
        error = cio_ascend(cdi_info->cio_info);
      if (error != CIO_ERR_FAIL)
        numChunks++;
    } while (error != CIO_ERR_FAIL);
    
    printf("Found %d chunks on scan %d \n", numChunks, iScan);

    // Rewind file and scan again
    cdi_rewind(cdi_info);
  }
  return 0;
}

/*********** in support of cdi_test_named_chunks **********/
int cdi_test_read_named_chunks(CDI_INFO cdi_info)
{

  // Check what's in the file
  std::set<CIO_CCCC> to_find;
  to_find.insert(CDI_CHUNK_TYPE_PTGE);
  to_find.insert(CDI_CHUNK_TYPE_VRTX);
  
  std::set<CIO_CCCC> found;
  cdi_find_chunks(cdi_info, to_find, found);
  if (found.size() != 2)
    std::cerr << "Plain cdi file is missing PTGE or VRTX\n" << std::endl;

  CIO_ERRCODE err1 = cdi_descend_in_chunk(cdi_info, CDI_CHUNK_TYPE_VRTX);
  if (err1 == CIO_ERR_SUCCESS){ 
    printf("found VRTX chunk\n");
    CDI_VRTX vrtx=cdi_read_vrtx(cdi_info);
    print_vrtx(vrtx);
    cdi_destroy_vrtx(vrtx);
  }

  err1 = cdi_ascend_out(cdi_info);

  return err1;
}

/*********** in support of cdi_test_encrypted_chunks **********/
int cdi_test_read_encrypted_chunks(CDI_INFO cdi_info)
{
  // Check what's in the file
  std::set<CIO_CCCC> to_find;
  to_find.insert(CDI_CHUNK_TYPE_PTGE);
  to_find.insert(CDI_CHUNK_TYPE_RGPN);
  to_find.insert(CDI_CHUNK_TYPE_VRTX);
  
  std::set<CIO_CCCC> found;
  cdi_find_chunks(cdi_info, to_find, found);
  if (found.size() != 3)
    std::cerr << "Encrypted file is missing PTGE or RGPN or VRTX\n" << std::endl;

  CIO_ERRCODE error = cdi_descend_in_chunk(cdi_info, CDI_CHUNK_TYPE_PTGE);
  if (error == CIO_ERR_SUCCESS) {
    //    print_ptge(cdi_info);
    CDI_PTGE ptge = cdi_read_ptge(cdi_info);
    printf("PTGE encryption version : %d\n",(int) ptge->encryption_version_id);
    cdi_destroy_ptge(ptge);
    error = cdi_ascend_out(cdi_info);
  } 

  cCIPHER_STREAM *cipher = cdi_get_cipher(cdi_info);
  printf( "PTGE creates cipher : %d \n", (int) (cipher != NULL));

  error = cdi_descend_in_chunk(cdi_info, CDI_CHUNK_TYPE_RGPN);
  if (error == CIO_ERR_SUCCESS){ 
    printf("found RGPN chunk\n ");
    print_rgpn(cdi_info,1);
    error = cdi_ascend_out(cdi_info);
  }

  error = cdi_descend_in_chunk(cdi_info, CDI_CHUNK_TYPE_VRTX);
  if (error == CIO_ERR_SUCCESS){ 
    printf("found VRTX chunk\n");
    CDI_VRTX vrtx = cdi_read_vrtx(cdi_info);
    print_vrtx(vrtx);
    cdi_destroy_vrtx(vrtx);
    error = cdi_ascend_out(cdi_info);
  }
  
  return error;
}

/*********** in support of cdi_test_read_ptge **********/
int  cdi_test_read_ptge(CDI_INFO cdi_info)
{
  // Check what's in the file
  std::set<CIO_CCCC> to_find;
  to_find.insert(CDI_CHUNK_TYPE_PTGE);
  to_find.insert(CDI_CHUNK_TYPE_RGPN);
  to_find.insert(CDI_CHUNK_TYPE_VRTX);
  
  std::set<CIO_CCCC> found;
  cdi_find_chunks(cdi_info, to_find, found);
  if (found.size() != 3)
    std::cerr << "Encrypted file is missing PTGE or RGPN or VRTX\n" << std::endl;

  // Read PTGE once
  CIO_ERRCODE error = cdi_find_and_read_ptge(cdi_info, false);

  // Check that cipher was created
  cCIPHER_STREAM *cipher = cdi_get_cipher(cdi_info);

  // Advance in the file
  cdi_descend_in_chunk(cdi_info, CDI_CHUNK_TYPE_RGPN);
  // In real code would read chunk and process its data
  cdi_ascend_out(cdi_info);

  cdi_descend_in_chunk(cdi_info, CDI_CHUNK_TYPE_VRTX);
  // In real code would read chunk and process its data
  cdi_ascend_out(cdi_info);

  // Read PTGE again
  // with rewind = true
  error = cdi_find_and_read_ptge(cdi_info, true);
  if (error == 0) {
    printf("PTGE second read does not change cipher (expect 1): %d\n", (int) (cipher == cdi_get_cipher(cdi_info)));
  } else {
    std::cerr << "could not find PTGE after rewind" << std::endl;
  }

  return error;
}

int test_cdi_find()
{
  std::string fileName("nano_5_7.cdi");
  CDI_INFO cdi_info = NULL;

  int err = open_cdi_check_version(fileName.c_str(), cdi_info);
  if (!err) {
    cdi_test_find(cdi_info);
    cdi_close(cdi_info);
  } else {
    TestMarker::ERROR = 1;
  }

  return err;
}

int test_cdi_rewind()
{
  std::string fileName("nano_5_7.cdi");
  CDI_INFO cdi_info = NULL;
  int err = open_cdi_check_version(fileName.c_str(), cdi_info);
  if (!err) {
    cdi_test_rewind(cdi_info);
    cdi_close(cdi_info);
  } else {
    TestMarker::ERROR = 1;
  }
  return err;
}

int test_cdi_read_named_chunks()
{
  std::string fileName("nano_5_7.cdi");
  CDI_INFO cdi_info = NULL;
  int err =   open_cdi_check_version(fileName.c_str(), cdi_info);
  if (!err) {
    cdi_test_read_named_chunks(cdi_info);
    cdi_close(cdi_info);
  } else {
    TestMarker::ERROR = 1;
  } 
  return err;
}

int test_cdi_read_encrypted_chunks()
{
  std::string fileName("nano_encrypted_7_0.cdi");
  CDI_INFO cdi_info = NULL;
  int err = open_cdi_check_version(fileName.c_str(), cdi_info);
  if (!err) {
    cdi_test_read_encrypted_chunks(cdi_info);
    cdi_close(cdi_info);
  } else {
    TestMarker::ERROR = 1;
  }
  return err;
}

int test_cdi_read_ptge()
{
  std::string fileName("nano_encrypted_7_0.cdi");
  CDI_INFO cdi_info = NULL;
  int err = open_cdi_check_version(fileName.c_str(), cdi_info);
  if (!err) {
    cdi_test_read_ptge(cdi_info);
    cdi_close(cdi_info);
  } else {
    TestMarker::ERROR = 1;
  }
  return err;
}


/*---------- test_functions -------------------------------------------*/

int main(int argc, char** argv)
{

  bool	versions_only_flag = false,
    help_flag = false;
  
  int nTests = 6;
  bool 
    test_0 = false,
    test_1 = false,
    test_2 = false,
    test_3 = false,
    test_4 = false,
    test_5 = false;
  
  std::vector<std::string> other_parameters;

  cARG_HELPER ah;
  ah.set_string_vector("other parameters", "<other_parameters>", other_parameters, true); /* Hide this part of usage */
  ah.new_flag(0x00, "help", "print this help message" , help_flag);

  ah.new_flag(0x00, "Test0", "Test" , test_0);
  ah.new_flag(0x00, "Test1", "Test" , test_1);
  ah.new_flag(0x00, "Test2", "Test" , test_2);
  ah.new_flag(0x00, "Test3", "Test" , test_3);
  ah.new_flag(0x00, "Test4", "Test" , test_4);
  ah.new_flag(0x00, "Test5", "Test" , test_5);

  ah.set_description("Unit test for CDI");

  /* Process parameters */
  ah.process(argc, argv);

  /* If versions, help, or manual info requested, then we
   * don't really run */
  if (versions_only_flag) {
    ah.write_version(stdout);
    exit(0);
  }

  if (argc == 1) {
    // Run all
    std::cout << " Running all " << nTests << " tests " << std::endl;
    test_0 = true;
    test_1 = true;
    test_2 = true;
    test_3 = true;
    test_4 = true;
    test_5 = true;
  }

  if (test_0) {
    TestMarker *m = new TestMarker(" write  ");
    test_cdi_write();
    delete m;
  }

  if (test_1) {
    TestMarker *m = new TestMarker(" find ");
    test_cdi_find();
    delete m;
  }

  if (test_2) {
    TestMarker *m = new TestMarker(" rewind ");
    test_cdi_rewind();
    delete m;
  }

  if (test_3) {
    TestMarker *m = new TestMarker(" read named chunks");
    test_cdi_read_named_chunks();
    delete m;
  }

  if (test_4) {
    TestMarker *m = new TestMarker(" read encrypted chunks");
    test_cdi_read_encrypted_chunks();
    delete m;
  }

  if (test_5) {
    TestMarker *m = new TestMarker(" read ptge");
    test_cdi_read_ptge();
    delete m;
  }

  return 0;
}
