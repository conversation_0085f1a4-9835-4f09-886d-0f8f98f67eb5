/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#if SURF_COUP
#include "common.h"
#include "surface_coupling.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "jobctl_status.h"
#include <iostream>
#include <fstream>
#include <string>

#define	PRESERVE_SEARCH_TREE				FALSE
#define	DEFAULT_NATIVE_TO_FOREIGN_ELEMENTS_PER_NODE	96
#define	DEFAULT_FOREIGN_TO_NATIVE_ELEMENTS_PER_NODE	20
#define	PURGE_ABSTRACT_SURFACE_ITERATOR_DATA		TRUE

static asINT32 map_elements_per_node_limit_native_to_foreign() {
  static asINT32 saved_map_elements_per_node_limit = -1;

  if (saved_map_elements_per_node_limit < 1) {
    cSTRING limit_str = platform_getenv("EXA_MAP_NATIVE_TO_FOREIGN_ELEMENTS_PER_NODE_LIMIT");
    if (limit_str) {
      saved_map_elements_per_node_limit = atoi(limit_str);
    };

    if (saved_map_elements_per_node_limit < 1) {
      saved_map_elements_per_node_limit = DEFAULT_NATIVE_TO_FOREIGN_ELEMENTS_PER_NODE;
    } else {
      msg_print(
	"native to foreign octree node limit set to %d\n",
	saved_map_elements_per_node_limit);
    }
  }

  return(saved_map_elements_per_node_limit);
}

static asINT32 map_elements_per_node_limit_foreign_to_native() {
  static asINT32 saved_map_elements_per_node_limit = -1;

  if (saved_map_elements_per_node_limit < 1) {
    cSTRING limit_str = platform_getenv("EXA_MAP_FOREIGN_TO_NATIVE_ELEMENTS_PER_NODE_LIMIT");
    if (limit_str) {
      saved_map_elements_per_node_limit = atoi(limit_str);
    };

    if (saved_map_elements_per_node_limit < 1) {
      saved_map_elements_per_node_limit = DEFAULT_FOREIGN_TO_NATIVE_ELEMENTS_PER_NODE;
    } else {
      msg_print(
	"foreign to native octree node limit set to %d\n",
	saved_map_elements_per_node_limit);
    }
  }

  return(saved_map_elements_per_node_limit);
}

static VOID parse_known_unit(cSTRING unit_str, UNITS_UNIT *unit)
{
  UNITS_STATUS status;
  if ((status = units_parse_unit(cp_info.units_db, unit_str, unit)) != UNITS_STATUS_OK)
    msg_internal_error("Unable to parse unit \"%s\": error code: %d", unit_str, status);
}

static VOID compute_units_conversion_coefficients(UNITS_UNIT unit1, UNITS_UNIT unit2,
						  dFLOAT *u_slope, dFLOAT *u_offset,
						  BOOLEAN unit1_is_user_unit)
{
  UNITS_STATUS status;
  if ((status = units_conversion_coefficients(cp_info.units_db, unit1, unit2, u_slope, u_offset))
      != UNITS_STATUS_OK) {
    cSTRING name1 = units_unit_name(unit1);
    cSTRING name2 = units_unit_name(unit2);
    msg_internal_error("Bad unit in CMDL chunk of CDI file: \"%s\": Not convertible to \"%s\": error code %d",
		       unit1_is_user_unit ? name1 : name2,
		       unit1_is_user_unit ? name2 : name1,
		       status);
  }
}

static void cmdl_unit_conversion_coeffs(eTPI_VARIABLE_TYPE var_type, cSTRING user_unit_str, 
					cBOOLEAN from_lattice_to_user_p, dFLOAT *u_slope, dFLOAT *u_offset)
{
  UNITS_UNIT user_u, lattice_u;
  UNITS_STATUS units_status;
  
  if (units_parse_unit(cp_info.units_db, user_unit_str, &user_u) != UNITS_STATUS_OK)
    msg_internal_error("Bad unit in CMDL chunk of CDI file: \"%s\"", user_unit_str);

  switch (var_type) {
    case TPI_VAR_TYPE_LENGTH:
      parse_known_unit("LatticeLength", &lattice_u);
      break;
    case TPI_VAR_TYPE_SURFACE_TEMPERATURE:
      parse_known_unit("LatticeTemperature", &lattice_u);
      break;
    case TPI_VAR_TYPE_HEAT_TRANSFER_COEFFICIENT:
      parse_known_unit("LatticeHeatTransferCoeff", &lattice_u);
      break;
    case TPI_VAR_TYPE_NEAR_WALL_TEMPERATURE:
      parse_known_unit("LatticeTemperature", &lattice_u);
      break;
    case TPI_VAR_TYPE_PRESSURE:
      parse_known_unit("LatticeStaticPressure", &lattice_u);
      break;
    default:
      msg_internal_error("Unknown variable type %d in CMDL chunk of CDI file.", var_type);
      break;
  }

  if (from_lattice_to_user_p) {
    compute_units_conversion_coefficients(lattice_u, user_u, u_slope, u_offset, FALSE);
  } else {
    compute_units_conversion_coefficients(user_u, lattice_u, u_slope, u_offset, TRUE);
  }
}

VOID cSURFACE_COUPLING::compute_unrounded_time_desc(BOOLEAN is_not_first_coupling_phase) // Only called for coupling windows
{
  // compute the exact (unrounded) times which are used for updating tdf file. See PR41598
  dFLOAT interval = unrounded_average_interval;
  dFLOAT period   = unrounded_period;
  dFLOAT avg = 0;
  if (interval < 0 || interval > period) {
    interval = period;
  }
  
  unrounded_time_desc.interval = interval;
  unrounded_time_desc.period = period;

  dFLOAT s = unrounded_start_time;
  dFLOAT e = unrounded_end_time;
  dFLOAT start;
  dFLOAT s2;  // center of frame
  dFLOAT half_avg = interval / 2.0;
  s2 = s - half_avg;  // s is the end of the first frame
  
  //dFLOAT min_start = this->unrounded_min_start_time; // should always be 0
  dFLOAT min_start = 0;
  if (s2 - half_avg < min_start)
    s2 = min_start + half_avg;
  if (s2 - half_avg < min_start)
    s2 += unrounded_time_desc.period;
  start = s2 - half_avg;

  if (is_not_first_coupling_phase) {
    unrounded_time_desc.start = s;
    start = s;
  } else {
    unrounded_time_desc.start = start;
  }
}

VOID cSURFACE_COUPLING::process_cmdl_var_units()
{
  import_var_unit_conversion_slopes = xnew sFLOAT[ n_import_vars()];
  import_var_unit_conversion_offsets = xnew sFLOAT[ n_import_vars()];
  
  ccDOTIMES(i, n_import_vars()) {
    dFLOAT slope, offset;
    eTPI_VARIABLE_TYPE tpi_var_type = eTPI_VARIABLE_TYPE((import_vars())[i]); // just a cast
    cmdl_unit_conversion_coeffs(tpi_var_type, (import_var_units())[i], FALSE, 
				&slope, &offset);
    import_var_unit_conversion_slopes[i] = slope;
    import_var_unit_conversion_offsets[i] = offset;
  }  

  export_var_unit_conversion_slopes = xnew sFLOAT[ n_export_vars()];
  export_var_unit_conversion_offsets = xnew sFLOAT[ n_export_vars()];
  
  ccDOTIMES(i, n_export_vars()) {
    dFLOAT slope, offset;
    eTPI_VARIABLE_TYPE tpi_var_type = eTPI_VARIABLE_TYPE((export_vars())[i]); // just a cast
    cmdl_unit_conversion_coeffs(tpi_var_type, (export_var_units())[i], TRUE, 
				&slope, &offset);
    export_var_unit_conversion_slopes[i] = slope;
    export_var_unit_conversion_offsets[i] = offset;
  }
}
    
VOID process_surface_coupling_units()
{
  ccDOTIMES(coupling, cp_info.n_surface_couplings) {
    cp_info.surface_couplings[coupling].process_cmdl_var_units();
  }
}

VOID cSURFACE_COUPLING::SetMeshTransform()
{
  cBREPf::sBG_TRANSFORM3 transform(cmdl.l_to_g_xform[0][0],
				   cmdl.l_to_g_xform[0][1],
				   cmdl.l_to_g_xform[0][2],
				   cmdl.l_to_g_xform[0][3],
				   cmdl.l_to_g_xform[1][0],
				   cmdl.l_to_g_xform[1][1],
				   cmdl.l_to_g_xform[1][2],
				   cmdl.l_to_g_xform[1][3],
				   cmdl.l_to_g_xform[2][0],
				   cmdl.l_to_g_xform[2][1],
				   cmdl.l_to_g_xform[2][2],
				   cmdl.l_to_g_xform[2][3]);

  // The transform is a property of a mesh, not a model. There should only be one
  // mesh per model at this point, but that may not always be true.
  MIO_MODEL_DO_MESHES(mesh, foreign_model, cMIO_MODELf)
    mesh->SetTransform(&transform);
}

#define CHECK_DUPLICATE_NAME()                                      \
if (dup_child == child || !dup_child || !child)                     \
  continue;                                                         \
if (!strcmp(child->m_frontAttributes->m_name,                       \
      dup_child->m_frontAttributes->m_name)) {                      \
  msg_error("The %s model (\"%s\") contains a duplicate part "      \
      "name \"%s\". Please run the \"fixpartnames\" utility "       \
      "on the model file.", child->m_frontAttributes->m_name,       \
      get_cmdl()->model_type, get_cmdl()->model_filename);          \
} 


VOID cSURFACE_COUPLING::CheckDuplicateNames(cMIO_MODEL_SKELETON *skeleton, 
    					    cMIO_COMPONENT_SKELETON *child)
{
  MIO_MODEL_SKELETON_DO_ROOT_COMPONENTS(dup_child,skeleton) {
    CHECK_DUPLICATE_NAME();
  }
}

VOID cSURFACE_COUPLING::CheckDuplicateNames(cMIO_COMPONENT_SKELETON *parent, 
    					    cMIO_COMPONENT_SKELETON *child)
{
  MIO_COMPONENT_SKELETON_DO_CHILDREN(dup_child,parent) {
    CHECK_DUPLICATE_NAME();
  }
}


MIO_COMPONENT_SKELETON cSURFACE_COUPLING::GetLeafComponentSkeleton (
    cSTRING full_hierarchical_name, cMIO_MODEL_SKELETON *skeleton)
{
  cSTRING old_partial_name;
  cSTRING next_partial_name;
  char current_component_name[MAX_COMPONENT_NAME];
  size_t current_component_name_length;
  size_t parent_name_length = 0;

  char separator = full_hierarchical_name[0];
  next_partial_name = full_hierarchical_name + 1;
  //next_partial_name = full_hierarchical_name;

  cMIO_COMPONENT_SKELETON *parent;    
  cMIO_COMPONENT_SKELETON *child;
  BOOLEAN leaf_p = FALSE;
  BOOLEAN success_p = FALSE;
  BOOLEAN root_p = TRUE;
  old_partial_name = next_partial_name;
  do {
    strcpy(current_component_name, "");
    BOOLEAN backslash_p = FALSE;
    do {
      next_partial_name = strchr(old_partial_name, separator);
      if(next_partial_name == NULL) {
        leaf_p = TRUE;
        current_component_name_length = strlen(old_partial_name);
        strcat(current_component_name, old_partial_name);
        break;
      } else {
        current_component_name_length = next_partial_name - old_partial_name;
        strncat(current_component_name, old_partial_name, current_component_name_length);
        if(current_component_name[current_component_name_length - 1] == '\\') {
          backslash_p = TRUE;
          current_component_name[current_component_name_length - 1] = separator;
        } else {
          backslash_p = FALSE;
        }
        old_partial_name = next_partial_name + 1;
      }
    } while(backslash_p);

    if(root_p) {
      child = skeleton->GetRootComponentByName(current_component_name);
      CheckDuplicateNames(skeleton,child);
      root_p = FALSE;
    } else {
      child = parent->GetChildComponentByName(current_component_name);
      CheckDuplicateNames(parent,child);
    }

    if(child != NULL) {
      if (leaf_p) {
        success_p = TRUE;
      } else { //keep iterating
        parent = child;
        parent_name_length += current_component_name_length + 1; //to include the initial separator
      }
    } else { //couldn't find the current component, so cannot proceed down the tree
      leaf_p = TRUE; //exit the loop without marking sucess to throw an error below
    }
  } while (!leaf_p);

  if (success_p) {
    return child;
  } else {
    if (parent_name_length > 0) parent_name_length--; //to remove the initial separator of the root parent
    char component_name[MAX_COMPONENT_NAME] = "";
    strncat(component_name, full_hierarchical_name + 1, parent_name_length + current_component_name_length);
    msg_error("Component \"%s\" not found in \"%s\", perhaps tdf has changed and cdi needs to be regenerated", 
              component_name, get_cmdl()->model_filename);
  }
  return NULL;
} 

COMPONENT cSURFACE_COUPLING::GetLeafComponent (STRING full_hierarchical_name, 
    cMIO_MODELf *model)
{
  STRING old_partial_name;
  STRING next_partial_name;
  char current_component_name[MAX_COMPONENT_NAME];
  size_t current_component_name_length; 

  char separator = full_hierarchical_name[0];
  next_partial_name = full_hierarchical_name + 1;
  // next_partial_name = full_hierarchical_name;

  COMPONENT parent;    
  COMPONENT child;
  BOOLEAN leaf_p = FALSE;
  BOOLEAN success_p = FALSE;
  BOOLEAN root_p = TRUE;
  old_partial_name = next_partial_name;
  do {
    strcpy(current_component_name, "");
    BOOLEAN backslash_p = FALSE;
    do {
      next_partial_name = strchr(old_partial_name, separator);
      if(next_partial_name == NULL) {
	leaf_p = TRUE;
	strcat(current_component_name,old_partial_name);
        break;
      }
      else {

	current_component_name_length = next_partial_name - old_partial_name;
	strncat(current_component_name, old_partial_name, current_component_name_length);
	if(current_component_name[current_component_name_length - 1] == '\\') {
	  backslash_p = TRUE;
	  current_component_name[current_component_name_length - 1] = separator;
	}
	else backslash_p = FALSE;
      }
      old_partial_name = next_partial_name + 1;
    } while(backslash_p);
    if(root_p) {
      child = model->GetRootComponentByName(current_component_name);
      root_p = FALSE;
    }
    else
      child = parent->GetChildComponentByName(current_component_name);

    if(child != NULL && leaf_p) {
      success_p = TRUE;
    }
    else
      if(child != NULL)
	parent = child;
  } while (!leaf_p);
  if(success_p)
    return(child);
  else
    msg_error("Component \"%s\" was not found in \"%s\"; perhaps it has changed.", current_component_name, get_cmdl()->model_filename);
  return NULL;
} 

VOID cSURFACE_COUPLING::BuildForeignModel()
{

  cMIO_MODEL_SKELETON skeleton;  
  cTPI_IMPORT_CONTROLLER skeleton_import_controller;
  cTPI_IMPORT_CONTROLLER model_import_controller;

  foreign_model_wip = xnew cFOREIGN_MODEL_WIP(this);
  skeleton.SetWIP(foreign_model_wip);
  eTPI_STATUS skeleton_import_status = tpi->ImportSkeleton(skeleton_import_controller,
							   skeleton);

  if(skeleton_import_status != (int)MIO_SUCCESS) {
    msg_error("Import of %s model file \"%s\" failed.",
	      get_cmdl()->model_type, get_cmdl()->model_filename);
  } 

  // mark up the skeleton      
  // is the entire model referenced anywhere in the scbc or cplw chunks?
  BOOLEAN import_entire_model = FALSE;
  ccDOTIMES(coupling_model_bc, cmdl.num_coupling_model_bcs) {
    if (scbcs[coupling_model_bc].target_type == CDI_MODEL_ITEM) {
      import_entire_model = TRUE;
      break;
    }
  }

  if (!import_entire_model) {
    ccDOTIMES(coupled_face,cmdl.num_pf_bcs) {
      ccDOTIMES(source_constraint, cplws[coupled_face].n_source_constraints) {
	if (CDI_MODEL_ITEM == cplws[coupled_face].source_constraint_types[source_constraint]) {
	  import_entire_model = TRUE;
	  break;
	}
      }
    }
  } 

  // only portions of the entire model are to be imported, go through each of
  // the scbc and cplw chunks to mark the chosen ones for loading
  if (!import_entire_model) {
    MIO_MODEL_SKELETON_DO_COMPONENTS(component, &skeleton) {
      component->m_load = FALSE;
    }
    // go through each of the scbc and cplw chunks and get the components to mark for loading
    ccDOTIMES(coupling_model_bc, cmdl.num_coupling_model_bcs) {
      if (cdi_data.major_version >= 4 || !tpi->isVolumePart(scbcs[coupling_model_bc].target_name)) { // PR 33406
        MIO_COMPONENT_SKELETON leaf = GetLeafComponentSkeleton(scbcs[coupling_model_bc].target_name, &skeleton);
        if(leaf != NULL) {
          leaf->m_load = TRUE;
        } else {
          msg_internal_error("Failed to get leaf component skeleton for %s",scbcs[coupling_model_bc].target_name); 
        }
      }
    }
    ccDOTIMES(coupled_face,cmdl.num_pf_bcs) {
      ccDOTIMES(source_constraint, cplws[coupled_face].n_source_constraints) {
        if (cdi_data.major_version >= 4 || !tpi->isVolumePart(cplws[coupled_face].source_constraint_names[source_constraint])) { // PR 34391
          MIO_COMPONENT_SKELETON leaf = GetLeafComponentSkeleton(cplws[coupled_face].source_constraint_names[source_constraint], &skeleton);
          if (leaf != NULL) {
            leaf->m_load = TRUE;
          } else {
            msg_internal_error("Failed to get leaf component skeleton for %s",cplws[coupled_face].source_constraint_names[source_constraint]);
          }
        }
      }
    }
  } else {  // Import the entire model
    MIO_MODEL_SKELETON_DO_COMPONENTS(component, &skeleton) {
      component->m_load = TRUE;
    }
  }


  // Done with skeleton markup.
  eTPI_STATUS model_import_status = tpi->ImportModel(model_import_controller, &skeleton, foreign_model_wip);

  if (model_import_status == TPI_FAILURE) {
    // @@@ Figure out to get error messages out of MIO...
    msg_error("Import of %s model \"%s\" failed.",
	      get_cmdl()->model_type, get_cmdl()->model_filename);
  } else {
    foreign_model = tpi->GetModel();
    SetMeshTransform();
    msg_print("Import of %s model file \"%s\" succeeded.", cmdl.model_type, cmdl.model_filename);
  }
}

/* Note that BuildNativeModel is not a method of the cSURFACE_COUPLING class. This is because there
 * is only one native model and it is not exclusive to any coupling.
 */

VOID BuildNativeModel() 
{
  
  /* There is only one native model, so we just add all the required faces to it */

  ccDOTIMES(coupling, cp_info.n_surface_couplings) {
    CDI_CMDL cmdl = &cp_info.surface_couplings[coupling].cmdl;

    /* Faces required for  foreign model -> PowerFLOW coupling */
    ccDOTIMES(coupled_face,cmdl->num_pf_bcs) {
      asINT32 face_index = cmdl->pf_faces_with_bcs[coupled_face];
      if (cp_info.native_model->GetComponentById(face_index) == NULL) {
        COMPONENT face = cp_info.native_model->CreateComponent();
        face->m_frontAttributes = new sMIO_COMPONENT_ATTRIBUTES();
        face->m_frontAttributes->m_id = face_index;
        face->SetMesh(cp_info.native_mesh);
        cp_info.native_mesh->AddRootComponent(face);
      }
    }

    /* Faces required for PowerFLOW -> foreign model coupling */
    ccDOTIMES(coupling_model_bc, cmdl->num_coupling_model_bcs) {
      CDI_SCBC scbc = &cp_info.surface_couplings[coupling].scbcs[coupling_model_bc] ;
      ccDOTIMES(source_face, scbc->n_source_constraints) {
        asINT32 face_index = scbc->source_constraint_face_indices[source_face];
        if (face_index < 0) {
          msg_internal_error("Invalid source face constraint index %d for part %s", face_index, scbc->target_name);
        }
        if(cp_info.native_model->GetComponentById(face_index) == NULL) {
          COMPONENT face = cp_info.native_model->CreateComponent();
          face->m_frontAttributes = new sMIO_COMPONENT_ATTRIBUTES();
          face->m_frontAttributes->m_id = face_index;
          face->SetMesh(cp_info.native_mesh);
          cp_info.native_mesh->AddRootComponent(face);
        }
      }
    }
  }
}

static cSTRING lots_o_dashes = "-----------------------------------------------------";


static dFLOAT ComputeComponentDiagLength(cCOMPONENT *component)
{
  if (component->NumFacets() < 1) return 0;

  cBREPf *mesh = component->GetMesh();
  cBREPf::sBG_BOX3 bbox = mesh->GetFacetBbox(component->GetFacet(0));

  ccDOTIMES(i, component->NumFacets()) {
    bbox |= mesh->GetFacetBbox(component->GetFacet(i));
  }
  
  return (bbox.Max() - bbox.Min()).Length();
}


VOID cSURFACE_COUPLING::BuildNativeToForeignMap()
{
  asINT32 num_matches = 0;
  asINT32 num_failed_matches = 0;
  dFLOAT map_max_match_distance = -1.0; 
  dFLOAT map_avg_match_distance = -1.0; 
  char base_name[PLATFORM_MAXPATHLEN];

  platform_get_file_base_name(cmdl.model_filename, base_name);

  foreign_target_indices = cnew asINT32[cmdl.num_coupling_model_bcs];
  foreign_target_temperature = cnew sFLOAT[cmdl.num_coupling_model_bcs];

  ccDOTIMES(coupling_model_bc, cmdl.num_coupling_model_bcs) {

    sSIM_ABSTRACT_SURFACE *native_surface = native_source_surface[coupling_model_bc];
    sSIM_ABSTRACT_SURFACE *foreign_surface = foreign_target_surface[coupling_model_bc];
    sSIM_SURFACE_DATA *foreign_data = foreign_target_data[coupling_model_bc];
    CDI_SCBC scbc = &scbcs[coupling_model_bc] ;

    BOOLEAN includeFront;
    BOOLEAN includeBack;

    switch(scbc->target_side_to_match) {
      case CDI_SURFACE_FRONT:
	includeFront = TRUE;
	includeBack = FALSE;
	break;
      case CDI_SURFACE_BACK:
	includeFront = FALSE;
	includeBack = TRUE;
	break;
      case CDI_SURFACE_FRONT_AND_BACK:
	includeFront = TRUE;
	includeBack = TRUE;
	break;
      default:
	msg_internal_error("Invalid target match specification for SCBC chunk.");
	break;
    }

    if (scbc->target_type == CDI_MODEL_ITEM) {
      MIO_MODEL_DO_COMPONENTS(target_component, foreign_model, cMIO_MODELf) {
	foreign_surface->Add(target_component,includeFront,includeBack);
      }
      if (scbc->max_match_distance < 0)
        scbc->max_match_distance = DFLOAT_MAX;
    } else {
      if (cdi_data.major_version >= 4 || !tpi->isVolumePart(scbc->target_name)) { // PR 33406
        COMPONENT target_component = GetLeafComponent(scbc->target_name, foreign_model);      
        foreign_surface->Add(target_component, includeFront,includeBack);
        if (scbc->max_match_distance < 0)
          scbc->max_match_distance = 0.02*ComputeComponentDiagLength(target_component);
      }
    }

    ccDOTIMES(source_face, scbc->n_source_constraints) {
      asINT32 face_index = scbc->source_constraint_face_indices[source_face];
      if (face_index < 0) {
	msg_internal_error("%s part \"%s\" in model \"%s\" has an invalid source constraint index %d.",cmdl.model_type,scbc->target_name,base_name,face_index);
      }
      COMPONENT source_component = cp_info.native_model->GetComponentById(face_index);
      native_surface->Add(source_component,TRUE,source_component->IsFacetFrontAndBack(0));
    }

    // native and foreign models are in lattice units
    if (TPI_NONE_ELIGIBLE == 
        tpi->GetBoundaryConditionEligibility(eTPI_COUPLED_SIM_TYPE(cmdl.coupling_type),*foreign_surface, TRUE)) {
      foreign_target_indices[coupling_model_bc] = -1;
    } else {
      if (foreign_surface->NumFacetFaces() > 0) {
        foreign_target_indices[coupling_model_bc] = native_to_foreign_map->AddTarget(foreign_surface, *native_surface, scbc->max_match_distance, scbc->max_match_angle);
	
        // Allocate data for this surface
        ccDOTIMES(var,n_export_vars()) {
          foreign_data->SetSurface(foreign_surface);
          foreign_data->SetLocation(TPI_VAR_LOCATION_ELEMENT_FACES);
          foreign_data->SetRank(TPI_VAR_RANK_SCALAR);
          foreign_data->AllocateData();
          foreign_data++;
        }
      } else {
        msg_warn("%s part \"%s\" in model \"%s\" has zero elements, hence it will not receive any PowerFLOW data.",cmdl.model_type,scbc->target_name,base_name);
        foreign_target_indices[coupling_model_bc] = -1;
      }
    }
  }
  if (tpi->NumMessages() > 0)
    dump_tpi_messages(tpi, get_cmdl()->model_filename);

  native_to_foreign_map->BuildMap(num_matches, num_failed_matches,
                                  map_max_match_distance, map_avg_match_distance, PRESERVE_SEARCH_TREE, TRUE, TRUE, FALSE,
                                  map_elements_per_node_limit_native_to_foreign());

  native_resulting_source_surface = native_to_foreign_map->GetResultingSourceSurface();
  // print out the match info per target
  if (!native_resulting_source_surface || 
      0 == native_resulting_source_surface->NumFacetFaces()) {
    msg_warn("No PowerFLOW data used as boundary conditions for the %s model \"%s\"! Please check your case setup.",
	     cmdl.model_type, base_name);
    return;
  }
  msg_print("\nPowerFLOW mesh mapped to %s mesh (%s)", cmdl.model_type, base_name);
  msg_print("---------------------------------%.*s", (int)(strlen(cmdl.model_type) + strlen(base_name)), lots_o_dashes);
  const cSTRING pad = "                        ";
  asINT32 pad_len = strlen(pad);
  asINT32 col1_hdr_len = strlen(cmdl.model_type) + strlen(" Component");
  asINT32 col1_len = col1_hdr_len + pad_len;
  msg_print("%s Component%*sMatched Elements", cmdl.model_type, pad_len, pad);
  msg_print("%.*s%*s----------------", 
	    col1_hdr_len, lots_o_dashes,
	    pad_len, pad);
  ccDOTIMES(coupling_model_bc, cmdl.num_coupling_model_bcs) {
    asINT32 nmatch, nfailed;
    dFLOAT max_distance, avg_distance;
    CDI_SCBC scbc = &scbcs[coupling_model_bc] ;
    asINT32 target_index = foreign_target_indices[coupling_model_bc];
    if (-1 == target_index) {
      continue;
    }
    native_to_foreign_map->GetMatchInfoForTarget(target_index, nmatch, nfailed, max_distance, avg_distance);
    msg_print("%-*s %d (of %d)", col1_len - 1, scbc->target_name, nmatch, nmatch + nfailed);
  }

  msg_print("\nMax match distance: \t\t\t    %g meters\n"
	      "                    \t\t\t    %g cells",
	    cdi_data.meters_per_cell * map_max_match_distance, 
	    map_max_match_distance); 
  msg_print("Avg match distance: \t\t\t    %g meters\n"
	    "                    \t\t\t    %g cells",
	    cdi_data.meters_per_cell * map_avg_match_distance, 
	    map_avg_match_distance); 

  msg_print("Total matched elements: \t\t    %d (of %d)", num_matches, num_matches + num_failed_matches); 
  msg_print("Total unmatched elements: \t\t    %d", num_failed_matches); 

  sFLOAT unmatched_area_ratio = native_to_foreign_map->UnmatchedAreaRatio();
  if (unmatched_area_ratio > 0.0) {
    msg_print("Unmatched area / total area: \t\t    %g", unmatched_area_ratio);
  }
  msg_print(""); // newline
  if (unmatched_area_ratio > cp_info.ptherm_max_unmatched_area_ratio && IS_TAI_APP(cmdl.model_type)) {
    msg_error("Unmatched area fraction (mapping PowerFLOW mesh to %s mesh) for model \"%s\" is %g,"
	      " which is larger than the allowable unmatched area ratio %g. To specify a higher allowable"
	      " ratio, please use the -ptherm_max_unmatched_ratio option to exaqsub.",
	      cmdl.model_type, cmdl.model_filename,unmatched_area_ratio, cp_info.ptherm_max_unmatched_area_ratio);
  }
}

VOID cSURFACE_COUPLING::BuildForeignToNativeMap()
{
  asINT32 num_matches = 0;
  asINT32 num_failed_matches = 0;
  dFLOAT map_max_match_distance = -1.0; 
  dFLOAT map_avg_match_distance = -1.0; 

  if (0 == cmdl.num_pf_bcs) {
    msg_warn("No mappings defined from the %s mesh \"%s\" to the PowerFLOW mesh."
	     " The PowerFLOW model will not update any boundary conditions from %s."
	     " This may be due to an incorrect setup.",
	     cmdl.model_type, cmdl.model_filename, cmdl.model_type);
    return;
  }
  char base_name[PLATFORM_MAXPATHLEN];
  platform_get_file_base_name(cmdl.model_filename, base_name);

  native_target_indices = cnew asINT32[cmdl.num_pf_bcs];
  ccDOTIMES(pf_bc, cmdl.num_pf_bcs) {

    sSIM_ABSTRACT_SURFACE *native_surface = native_target_surface[pf_bc];
    sSIM_ABSTRACT_SURFACE *foreign_surface = foreign_source_surface[pf_bc];
    sSIM_SURFACE_DATA *native_data = native_target_data[pf_bc];

    CDI_CPLW cplw = &cplws[pf_bc];

    asINT32 face_index = cplw->face_index; 
    COMPONENT target_component = cp_info.native_model->GetComponentById(face_index);
    native_surface->Add(target_component, TRUE, target_component->IsFacetFrontAndBack(0));
    ccDOTIMES(source_face, cplw->n_source_constraints) {
      BOOLEAN includeFront;
      BOOLEAN includeBack;
      switch(cplw->source_constraint_sides[source_face]) {
	case CDI_SURFACE_FRONT:
	  includeFront = TRUE;
	  includeBack = FALSE;
	  break;
	case CDI_SURFACE_BACK:
	  includeFront = FALSE;
	  includeBack = TRUE;
	  break;
	case CDI_SURFACE_FRONT_AND_BACK:
	  includeFront = TRUE;
	  includeBack = TRUE;
	  break;
	default:
	  msg_internal_error("Invalid target match specification for SCBC chunk.");
	  break;
      }


      if (cplw->source_constraint_types[source_face] == CDI_MODEL_ITEM) {
	MIO_MODEL_DO_COMPONENTS(source_component, foreign_model, cMIO_MODELf) {
	  foreign_surface->Add(source_component,includeFront,includeBack);
	}
      } else {
        if (cdi_data.major_version >= 4 || !tpi->isVolumePart(cplw->source_constraint_names[source_face])) { // PR 34391
          COMPONENT source_component = GetLeafComponent(cplw->source_constraint_names[source_face], foreign_model);
          foreign_surface->Add(source_component,includeFront,includeBack);
        }
      }
    }

    if (native_surface->NumFacetFaces() > 0) {
      ccDOTIMES(var,n_import_vars()) {

	native_data->SetSurface(native_surface);
	native_data->SetLocation(TPI_VAR_LOCATION_ELEMENT_FACES);
	native_data->SetRank(TPI_VAR_RANK_SCALAR);
	native_data->AllocateData();
	native_data++;
      }
    }

    eTPI_ELIGIBILITY eligibility = tpi->GetResultEligibility(eTPI_COUPLED_SIM_TYPE(cmdl.coupling_type),*foreign_surface);
    // if some_eligible is true, interp must be upgraded to handle invalid
    // values imported from tpi. They can be considered as unmatched elements 
    // in MapSourceToTarget for this PF face
    if (TPI_NONE_ELIGIBLE == eligibility || TPI_SOME_ELIGIBLE == eligibility) {
      // this PF target face will not receive any values from the coupling model
      msg_warn("No %s data from model \"%s\" can be used as boundary conditions for the PowerFLOW face \"%s\". The default value specified in the case setup will be used for this face.",cmdl.model_type,base_name,cp_info.sri_faces[cplws[pf_bc].face_index].name);
      native_target_indices[pf_bc] = -1;
    } else {
      if (native_surface->NumFacetFaces() > 0) {
      native_target_indices[pf_bc] = foreign_to_native_map->AddTarget(native_surface, *foreign_surface, cplw->max_match_distance, cplw->max_match_angle);
      } else {
	msg_warn("PowerFLOW face \"%s\" has zero elements, hence it will not receive %s data from model \"%s\".",cp_info.sri_faces[cplws[pf_bc].face_index].name,cmdl.model_type,base_name);
	native_target_indices[pf_bc] = -1; // disable this target face if it has zero elements, for example if it is an occluded face
      }
    }
  }
  if (tpi->NumMessages() > 0)
    dump_tpi_messages(tpi, get_cmdl()->model_filename);

  foreign_to_native_map->BuildMap(num_matches, num_failed_matches, 
                                  map_max_match_distance, map_avg_match_distance, PRESERVE_SEARCH_TREE, TRUE, TRUE, FALSE,
                                  map_elements_per_node_limit_foreign_to_native());

  // print out the match info per target
  foreign_resulting_source_surface = foreign_to_native_map->GetResultingSourceSurface();

  if (!foreign_resulting_source_surface || 
      0 == foreign_resulting_source_surface->NumFacetFaces()) {
    msg_warn("No %s data from model %s used as boundary conditions for the PowerFLOW model! Please check your case setup",cmdl.model_type,base_name);
    return;
  }

  msg_print("\n%s mesh (%s) mapped to PowerFLOW mesh", cmdl.model_type, base_name);
  msg_print("---------------------------------%.*s", (int)(strlen(cmdl.model_type) + strlen(base_name)), lots_o_dashes);
  const cSTRING pad = "                              ";
  asINT32 pad_len = strlen(pad);
  asINT32 col1_hdr_len = strlen("PowerFLOW Face");
  asINT32 col1_len = col1_hdr_len + pad_len;
  msg_print("PowerFLOW Face%*sMatched Surfels", pad_len, pad);
  msg_print("%.*s%*s---------------", 
	    col1_hdr_len, lots_o_dashes,
	    pad_len, pad);
  ccDOTIMES(pf_bc, cmdl.num_pf_bcs) {
    asINT32 nmatch, nfailed;
    dFLOAT max_distance,avg_distance;
    asINT32 target_index = native_target_indices[pf_bc];
    if (-1 == target_index)
      continue;
    foreign_to_native_map->GetMatchInfoForTarget(target_index, nmatch, nfailed, max_distance, avg_distance); 

    msg_print("%-*s %d (of %d)", col1_len - 1, cp_info.sri_faces[cplws[pf_bc].face_index].name, nmatch, nmatch + nfailed);
  }

  ccDOTIMES(var,n_import_vars()) {
    foreign_resulting_source_data[var].SetSurface(foreign_resulting_source_surface);
    foreign_resulting_source_data[var].SetLocation(TPI_VAR_LOCATION_ELEMENT_FACES);
    foreign_resulting_source_data[var].SetRank(TPI_VAR_RANK_SCALAR);
    foreign_resulting_source_data[var].AllocateData();
  }
  msg_print("\nMax match distance: \t\t\t    %g meters\n"
	      "                    \t\t\t    %g cells",
	    cdi_data.meters_per_cell * map_max_match_distance, 
	    map_max_match_distance); 
  msg_print("Avg match distance: \t\t\t    %g meters\n"
	    "                    \t\t\t    %g cells",
	    cdi_data.meters_per_cell * map_avg_match_distance, 
	    map_avg_match_distance); 

  msg_print("Total matched surfels: \t\t\t    %d (of %d)", num_matches, num_matches + num_failed_matches); 
  msg_print("Total unmatched surfels: \t\t    %d", num_failed_matches); 

  sFLOAT unmatched_area_ratio = foreign_to_native_map->UnmatchedAreaRatio();
  if (unmatched_area_ratio > 0.0) {
    msg_print("Unmatched area / total area: \t\t    %g", unmatched_area_ratio);
  }
  msg_print(""); // newline
  if (unmatched_area_ratio > cp_info.ptherm_max_unmatched_area_ratio && IS_TAI_APP(cmdl.model_type)) {
    msg_error("Unmatched area fraction (mapping %s mesh to PowerFLOW mesh) for"
	      " model %s is %g, which is larger than the allowable unmatched area"
	      " ratio %g. To specify a higher allowable ratio, please use the"
	      " -ptherm_max_unmatched_ratio option to exaqsub.",
	      cmdl.model_type, cmdl.model_filename,unmatched_area_ratio, cp_info.ptherm_max_unmatched_area_ratio);
  }
} 


VOID cSURFACE_COUPLING::PurgeSurfaceIteratorData() {
  ccDOTIMES(pf_bc, cmdl.num_pf_bcs) {
    sSIM_ABSTRACT_SURFACE *native_surface = native_target_surface[pf_bc];
    sSIM_ABSTRACT_SURFACE *foreign_surface = foreign_source_surface[pf_bc];

    if (native_surface)
      native_surface->DeleteIteratorDataStructures();
    if (foreign_surface)
      foreign_surface->DeleteIteratorDataStructures();
  }
}


BOOLEAN cSURFACE_COUPLING::any_pf_bc_init_p()
{
  CDI_CPLW cplw = get_cplws();
  ccDOTIMES(i,cmdl.num_pf_bcs) {
    if ((CDI_CPLW_PREFER_INITIAL_CONDITIONS_IN_MODEL == 
	  (cplw->flags & CDI_CPLW_PREFER_INITIAL_CONDITIONS_IN_MODEL))) {
      return TRUE;
    }
    cplw++;
  }
  return FALSE;
}

BOOLEAN cSURFACE_COUPLING::is_coupling_data_available()
{
  cTHIRD_PARTY_INTERFACE *coupling_interface = tpi;

  if (!foreign_resulting_source_surface ||
      0 == foreign_resulting_source_surface->NumFacetFaces()) {
    return FALSE;
  }

  // import the data
  eTPI_DATA_AVAILABLE availRtn = TPI_UNAVAILABLE;
  if (TPI_SUCCESS != (coupling_interface->CheckResultsAvailability(availRtn))) {
    return FALSE;
  }
  if (TPI_UNAVAILABLE == availRtn) {
    return FALSE;
  }
  return TRUE;
}


asINT32 get_cdi_face_index(uINT32 face_index)
{

  asINT32 next_face_index = face_index;
#if 0 // hold off on new sri stuff for now
  while(cp_info.sri_faces[next_face_index].is_parent_a_face)
    next_face_index = cp_info.sri_faces[next_face_index].parent_index;
#endif

  // face index for lookup cannot be greater than the number of faces in the
  // global face name record in the LGI file (n_sri_faces)
  if (face_index >= cp_info.n_sri_faces) {
    return -1;
  }

  return((asINT32)cp_info.sri_faces[next_face_index].cdi_id);

}

VOID purge_surface_iterator_data()
{
  ccDOTIMES(coupling, cp_info.n_surface_couplings) {
#if	PURGE_ABSTRACT_SURFACE_ITERATOR_DATA
    cp_info.surface_couplings[coupling].PurgeSurfaceIteratorData();

    if (cp_jobctl_memory_status_requested()) {
      char buf[128];
      sprintf(buf,
              "Done purge of surface iterator data for coupling %d of %d",
              coupling, cp_info.n_surface_couplings - 1);
      cp_jobctl_output_status(buf);
    }
#endif
  }

}


VOID build_surface_coupling_maps()
{
  ccDOTIMES(coupling, cp_info.n_surface_couplings) {

    if (cp_jobctl_memory_status_requested()) {
      char buf[128];
      sprintf(buf,
	"About to build native to foreign map for coupling %d of %d",
	coupling, cp_info.n_surface_couplings - 1);
      cp_jobctl_output_status(buf);
    }

    cp_info.surface_couplings[coupling].BuildNativeToForeignMap();

    if (cp_jobctl_memory_status_requested()) {
      char buf[128];
      sprintf(buf,
	"Done build of native to foreign map for coupling, building foreign "
	"to native map for coupling %d of %d",
	coupling, cp_info.n_surface_couplings - 1);
      cp_jobctl_output_status(buf);
    }

    cp_info.surface_couplings[coupling].BuildForeignToNativeMap();

    if (cp_jobctl_memory_status_requested()) {

      char buf[128];
      sprintf(buf,
	"Done build of foreign to native map for coupling %d of %d",
	coupling, cp_info.n_surface_couplings - 1);

      cp_jobctl_output_status(buf);
    }

  }
}

asINT32 coupling_face_model_index(asINT32 face_index)
{
  ccDOTIMES(model_index,cp_info.n_surface_couplings) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+model_index;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    ccDOTIMES(face,cmdl->num_pf_bcs) {
      if (face_index == cmdl->pf_faces_with_bcs[face]) {
	return model_index;
      }
    }
  }
  return -1;
}

BOOLEAN is_bc_init_from_coupling_model(asINT32 model_index, asINT32 face_index)
{
  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+model_index;
  CDI_CMDL cmdl = surface_coupling->get_cmdl();
  CDI_CPLW cplw = surface_coupling->get_cplws();
  ccDOTIMES(i,cmdl->num_pf_bcs) {
    if (cplw->face_index < 0) {
      msg_internal_error("Face index in cplw chunk for cmdl index %d cannot be negative",model_index);
    }
    if (face_index == cplw->face_index && 
	(CDI_CPLW_PREFER_INITIAL_CONDITIONS_IN_MODEL == 
	 (cplw->flags & CDI_CPLW_PREFER_INITIAL_CONDITIONS_IN_MODEL))) {
      return TRUE;
    }
    cplw++;
  }
  return FALSE;
}

static inline 
std::string trim_comment(std::string &in_str)
{
  std::string out_str = in_str;
  out_str.erase(std::find(out_str.begin(),out_str.end(), '#'), out_str.end());
  return out_str;
}

static VOID process_remote_exec_and_mpi_file_options(cTHIRD_PARTY_INTERFACE *tpi)
{
  if (cp_info.ptherm_remote_host) {
    tpi->SetRemoteHostParams(cp_info.ptherm_remote_host, cp_info.ptherm_remote_user);
  }

  // must be processed after the remote host option above
  if (cp_info.ptherm_mpifile)  {
    std::ifstream ptherm_mpifile(cp_info.ptherm_mpifile);
    if (!ptherm_mpifile.is_open()) 
      msg_error("Unable to open file \"%s\" specified for the -ptherm_mpifile option", cp_info.ptherm_mpifile);
    std::string tmp_string, tmp_string_stripped, first_host;
    int dummy_int;
    while (std::getline(ptherm_mpifile, tmp_string)) {
      tmp_string_stripped = trim_comment(tmp_string);
      if (tmp_string_stripped.empty())
        continue;
      std::istringstream parse_stream(tmp_string_stripped);
      parse_stream >> std::ws; // skip white space
      // blank line?
      if (parse_stream.eof())
        continue;
      if (!(parse_stream >> dummy_int >> first_host))
        msg_error("Error parsing file \"%s\" specified for the -ptherm_mpifile option", cp_info.ptherm_mpifile);
      break;
    }
    ptherm_mpifile.close();
    if (first_host.empty()) {
      msg_error("Error parsing file \"%s\" specified for the -ptherm_mpifile option", cp_info.ptherm_mpifile);
    }

    char cp_host_name[MAXHOSTNAMELEN];
    if (-1 == gethostname(&cp_host_name[0], MAXHOSTNAMELEN)) {
      msg_internal_error("Unable to determine hostname for CP: %s", strerror(errno)); 
    }
    if (cp_info.ptherm_remote_host) {
      if (first_host != cp_info.ptherm_remote_host) {
        msg_warn("Overriding the remote host specified on the command line \"%s\", by the first host specified in the mpifile \"%s\":\"%s\"", cp_info.ptherm_remote_host, cp_info.ptherm_mpifile, first_host.c_str());

        tpi->SetRemoteHostParams(first_host.c_str());
      }
    }
    else if (first_host != cp_host_name) {
      tpi->SetRemoteHostParams(first_host.c_str());
    }
    tpi->SetMPIFile(cp_info.ptherm_mpifile);
  }
}

VOID cSURFACE_COUPLING::apply_coupling_command_line_options()
{
  char cmd_path[PLATFORM_MAXPATHLEN];
  memset(cmd_path,'\0',PLATFORM_MAXPATHLEN*sizeof(char));
  if (!getenv("EXA_DIST")) {
    msg_error("EXA_DIST environment variable required to launch %s ",cmdl.model_type);
  } 
  // Overload the -ptherm options for radtherm also
  if (!strcmp(cmdl.model_type,"PowerTHERM")) {
    if (cp_info.ptherm_processes > 0) 
      tpi->SetNumberProcesses(cp_info.ptherm_processes);
    if (cp_info.ptherm_threads > 0) 
      tpi->SetNumberThreads(cp_info.ptherm_threads);
    if (cp_info.force_radtherm_p) {
      if (cp_info.radtherm_path) {
	strcpy(cmd_path,cp_info.radtherm_path);
	if (!cp_info.ptherm_remote_host && 
	    (!platform_file_present(cmd_path) ||
	     !is_cmd_executable_via_system(cmd_path))) {
	  msg_error("Command \"%s\" not found or is not executable",cmd_path);
	} 
      } else {
	strcpy(cmd_path,"radtherm");
	if (!cp_info.ptherm_remote_host && (0 != system_cmd("type radtherm"))) {
	  msg_error("Command \"%s\" not found in PATH",cmd_path);
	} 
      }
    } else {
      strcpy(cmd_path,getenv("EXA_DIST"));
      strcat(cmd_path,"/bin/powertherm");
      if (!cp_info.ptherm_remote_host && !platform_file_present(cmd_path)) {
	msg_internal_error("Command \"powertherm\" not found at \"%s\"", cmd_path);
      }
    }
    tpi->SetExecutablePath(cmd_path);

    process_remote_exec_and_mpi_file_options(tpi);

    if (cp_info.ptherm_options) {
      tpi->SetExtraRunArgs(cp_info.ptherm_options);
    }
    if (cp_info.ptherm_remote_host) {
      // Check to see if the remote computing setup is correct
      eTPI_REMOTE_PARAM_STATUS param_status = tpi->CheckRemoteExecParams();
      if (param_status != TPI_REMOTE_PARAM_NOERR) {
	cSTRING error = tpi->GetRemoteExecParamError(param_status);
	if (!error) {
	  msg_internal_error("Unknown error encountered while checking remote execution parameters");
	} else {
	  msg_error("%s", error);
	}
      }
    }
  } else if (!strcmp(cmdl.model_type,"RadTherm")) { // mostly identical to Ptherm
    if (cp_info.ptherm_processes > 0)
      tpi->SetNumberProcesses(cp_info.ptherm_processes);
    if (cp_info.ptherm_threads > 0)
      tpi->SetNumberThreads(cp_info.ptherm_threads);
    if (!cp_info.ptherm_remote_host && (0 != system_cmd("type radtherm"))) {
      msg_error("Command \"radtherm\" not found in PATH");
    } 

    process_remote_exec_and_mpi_file_options(tpi);

    if (cp_info.ptherm_options) {
      tpi->SetExtraRunArgs(cp_info.ptherm_options);
    }
    if (cp_info.force_ptherm_p) {
      strcpy(cmd_path,getenv("EXA_DIST"));
      strcat(cmd_path,"/bin/powertherm");
      if (!cp_info.ptherm_remote_host && !platform_file_present(cmd_path)) {
	msg_internal_error("Command \"powertherm\" not found at \"%s\"", cmd_path);
      }
    } else if (cp_info.radtherm_path) {
      strcpy(cmd_path,cp_info.radtherm_path);
      if (!cp_info.ptherm_remote_host && 
	  (!platform_file_present(cmd_path) ||
	   !is_cmd_executable_via_system(cmd_path))) {
	msg_error("Command \"%s\" not found or is not executable",cmd_path);
      } 
    } else {
      strcpy(cmd_path,"radtherm");
      if (!cp_info.ptherm_remote_host && (0 != system_cmd("type radtherm"))) {
	msg_error("Command \"%s\" not found in PATH",cmd_path);
      } 
    }
    tpi->SetExecutablePath(cmd_path);
    if (cp_info.ptherm_remote_host) {
      // Check to see if the remote computing setup is correct
      eTPI_REMOTE_PARAM_STATUS param_status = tpi->CheckRemoteExecParams();
      if (param_status != TPI_REMOTE_PARAM_NOERR) {
	cSTRING error = tpi->GetRemoteExecParamError(param_status);
	if (!error) {
	  msg_internal_error("Unknown error encountered while checking remote execution parameters");
	} else {
	  msg_error("%s", error);
	}
      }
    }
  } else {
    msg_internal_error("Unknown model type %s encountered while applying coupling options",cmdl.model_type);
  }
}

#endif // SURF_COUP

