/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
//----------------------------------------------------------------------------
// David Hall, Exa Corporation                       Created Mon, Dec 29, 2008
//----------------------------------------------------------------------------

#ifndef CP_CDI_READER_H
#define CP_CDI_READER_H

#include "common.h"
#include "cp_info.h"

//----------------------------------------------------------------------------
// sPHYSICS_DESCRIPTORS_STRING
//----------------------------------------------------------------------------
static const asINT32 PDS_DEFAULT_LENGTH = 2048;

static const asINT32 SHELL_CONFIG_MAX_N_LAYERS = 32;
static const asINT32 SHELL_LAYER_MAX_CONTINUOUS_DP = 8;
static const asINT32 SHELL_CONFIG_MAX_CONTINUOUS_DP = SHELL_CONFIG_MAX_N_LAYERS * SHELL_LAYER_MAX_CONTINUOUS_DP;
static const asINT32 SHELL_LAYER_MAX_INITIAL_CONDITIONS = 4;
static const asINT32 SHELL_CONFIG_MAX_INITIAL_CONDITIONS = SHELL_CONFIG_MAX_N_LAYERS * SHELL_LAYER_MAX_INITIAL_CONDITIONS;

typedef struct sPHYSICS_DESCRIPTORS_STRING {

  sPHYSICS_DESCRIPTORS_STRING() {
    size = 0;
    buffer = EXA_MALLOC_ARRAY(char, PDS_DEFAULT_LENGTH);
    allocated_size = PDS_DEFAULT_LENGTH;
  }

  ~sPHYSICS_DESCRIPTORS_STRING() { exa_free(buffer); }

  VOID write(VOID *ptr, asINT32 n_bytes) {
    if ((size + n_bytes) > allocated_size) {
      allocated_size = MAX(2 * allocated_size, size + n_bytes + PDS_DEFAULT_LENGTH);
      buffer = EXA_REALLOC_ARRAY(buffer, char, allocated_size);
    }
    memcpy(buffer + size, ptr, n_bytes);
    size += n_bytes;
  }

  char*   buffer;
  asINT32 size;
  asINT32 allocated_size;

  sPHYSICS_DESCRIPTORS_STRING(const sPHYSICS_DESCRIPTORS_STRING&) = delete;

} *PHYSICS_DESCRIPTORS_STRING;

//----------------------------------------------------------------------------
// sVARIABLE
//----------------------------------------------------------------------------
typedef struct sVARIABLE {
  dFLOAT value;
  cSTRING name; // NULL indicates not an eqn variable
}* VARIABLE;

using PHYSICS_INDEX_TO_ENTITY_ID_MAP = std::vector<std::vector<asINT32>>;

//----------------------------------------------------------------------------
// CP_CDI_READER
//----------------------------------------------------------------------------
typedef struct sCP_CDI_READER
{  
  sCP_CDI_READER();
  
  VOID    open_file(const char* cdi_filename);
  VOID    reopen_file();
  VOID    close_file() { cdi_close(m_cdi_info); m_cdi_info = NULL; }
  VOID    exit_cdi_chunk();
  BOOLEAN enter_cdi_chunk(CIO_CCCC target_type);
  VOID reread_ptge_after_reopen(CIO_CCCC target_type);
  VOID read_simv_chunk();
  VOID read_glob_chunk();
  VOID adjust_and_send_time_coupling_schemes_info();
  VOID read_units_database();
  VOID maybe_read_glob_chunk_to_define_dimless_units();
  VOID read_old_tables();
  VOID read_new_tables();
  VOID read_turb_vel_tables();
  VOID read_equations();
  VOID read_eqn_structs();
  VOID read_heat_exchangers_and_condensers();
  VOID read_coordinate_systems();
  VOID read_vehicle_defn();
  VOID read_fluid_physics_descriptors();
  VOID read_shell_config_physics_descriptors();
  VOID read_flud_chunk();
  VOID read_scas_chunk();
  VOID read_hcsd_chunk();
  VOID read_shell_config_chunk();
  VOID read_tigr_chunk();
  VOID read_seed_from_meas_descs();
  VOID read_transient_boundary_data_descs();
  VOID read_flow_surface_physics_descriptors();
  VOID read_thermal_surface_physics_descriptors();
  VOID read_thermal_contact_table();
  VOID read_local_reference_frames();
  VOID read_global_reference_frame();
  VOID read_gravity_and_buoyancy();
  VOID read_bfpr(CDI_BFPR bfpr, cSTRING physics_desc_name, 
                 CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc);
  VOID read_body_force_descriptors();
  VOID read_meas_chunk();
  VOID read_ptge_chunk();
  VOID read_psdf_chunk();
  VOID copy_encryption_data();
  VOID read_acceleration_factor();
  VOID init_checkpoint_time_desc();
#if SURF_COUP
  VOID read_scpl_chunk(BOOLEAN is_ckpt_restore_p, BOOLEAN is_full_ckpt_restore_p);
  VOID init_surface_coupling(SIM_ARGS sim_args, const char *pf_path);
#endif // SURF_COUP
  VOID read_cmps_chunk();
  VOID read_scls_chunk();  //for UDS material

  VOID read_monitors();
  cBOOLEAN read_calibration_parameters();
  VOID read_conduction_solid_materials();
  VOID read_anisotropic_part_axis();
  VOID read_radiation_surface_conditions();
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  VOID enable_particle_solver_features();
  VOID set_thermal_particle_solver_status();
  VOID set_film_solver_status();
  asINT32 read_accretion_chunk();
  asINT32 read_particle_tracking_chunk();
  asINT32 read_particle_globals();
  asINT32 read_particle_materials();
  asINT32 read_particle_emitter_configurations();
  asINT32 read_particle_emitters();
  asINT32 read_particle_surface_properties();
  asINT32 read_particle_screens();
  asINT32 read_simple_wiper_models();
  asINT32 send_wipers();
  asINT32 send_particle_emitters();
  asINT32 read_geometry_data_for_emitters();
  asINT32 read_trajectory_windows();
  VOID read_uddl_chunk();
  VOID send_data_curves();
//#endif

protected:
  VOID validate_cdi_version();
  VOID print_next_chunk_name();
  VOID read_global_header();
  VOID read_kesc_info();
  VOID read_characteristic_properties();
  VOID read_thermal_acceleration();
  VOID read_real_to_lattice_conversions();
  VOID read_checkpoint_info();
  VOID read_time_coupling_schemes_info();
  VOID convert_physics_chunk_vars(asINT32 cdi_physics_type, sVARIABLE *parameters, sVARIABLE *initial_conditions,BOOLEAN found_some_eqn);
  VOID maybe_compute_resistance_and_sandwich_coeffs(asINT32 phys_desc_index, CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc,
                                                    sVARIABLE *parameters);
  VOID convert_phys_type     (CDI_PTYP ptyp, BOOLEAN *is_old_tfloat, BOOLEAN *is_old_turb);
  asINT32 read_physics_chunk (BOOLEAN is_flow_surface_p, BOOLEAN is_thermal_surface_p, asINT32 phys_desc_index,
                              const std::vector<asINT32>& part_or_face_indices);
  VOID read_shell_layer_chunk (sVARIABLE *parameter, sVARIABLE *initial_condition, cSTRING &phys_desc_name);

  VOID write_shell_config_params_and_initial_condition (LGI_PHYSICS_DESCRIPTOR physics_desc, sVARIABLE parameters[], sVARIABLE initial_conditions[]);

  VOID assign_flow_surface_physics_by_face_id(const PHYSICS_INDEX_TO_ENTITY_ID_MAP& surface_physics_index_to_face_ids);
  std::vector<asINT32> assign_bseed_specs_by_face_id(const PHYSICS_INDEX_TO_ENTITY_ID_MAP& surface_physics_index_to_face_ids);

  VOID read_global_physics_chunk(cSTRING physics_desc_name, asINT32 lgi_record_tag, 
                                 CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc, BOOLEAN is_header, BOOLEAN is_name_present);
  VOID write_phys_desc_buffer_to_sps(int n_descriptors);
  VOID write_phys_desc_header_to_buffer(int TAG_ID);
  VOID read_movb_chunk();
  VOID read_movb_chunk_phys_subchunk(asINT32& deforming_tire_index);

  cSTRING read_name();
  BOOLEAN complain_about_unrecognized_physics_parameter(asINT32 physics_desc_type,asINT32 param_type);
  
  LGI_CDI_GLOBAL_HEADER header;
  CDI_INFO m_cdi_info;
  CDI_CPNT m_cpnt;
  
  cSTRING  m_filename;
  sINT16   m_num_chunks;
  sINT16   m_chunk_number;
  uINT32   m_simv_flags;
  cBOOLEAN m_accept_old_turb_cdi;
  cBOOLEAN m_is_pre_2_1_cdi;
  cBOOLEAN m_is_pre_2_6_cdi;
  cBOOLEAN m_is_pre_3_18_cdi;

  cBOOLEAN m_is_first_pass;
  cBOOLEAN m_are_expr_relative_to_user_csys;
  cBOOLEAN m_user_specified_dns_nu_over_t;

  cBOOLEAN m_skip_eqn_structs;

  int      m_is_undb_present:1;
  int      m_is_coup_present:1;
  //int      m_is_csys_present:1;
  int      m_is_meas_present:1;
  int      m_is_nirf_present:1;
  int      m_is_grav_present:1;
  int      m_is_bodf_present:1; 
  int      m_is_lrfs_present:1;
  int      m_is_srpt_present:1;
  int      m_is_thpt_present:1;
  int      m_is_stct_present:1;
  int      m_is_pcfg_present:1;
  int      m_is_ptge_present:1;
  int      m_is_eqns_present:1;
  int      m_is_vsrs_present:1;
  int      m_is_tabl_present:1; // old tables
  int      m_is_tbls_present:1; // new tables
  int      m_is_aftd_present:1;
  int      m_is_cmps_present:1; //5g
  int      m_is_stbl_present:1; // structured table containing turb. velocity history
#if SURF_COUP
  int      m_is_scpl_present:1;
#endif // SURF_COUP
  int      m_is_vhcl_present:1; // vehicle definition
  int      m_is_mnts_present:1; // autostop monitors
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  int      m_is_trac_present:1;
  int      m_is_rgpn_present:1;
  int      m_is_vrtx_present:1;
//#endif
  int      m_is_bsrs_present:1;
  int      m_is_clbr_present:1;
  int      m_is_tire_present:1;
  int      m_is_psdf_present:1;
  int      m_is_scls_present:1;   //chunk for UDS materials
  int      m_is_scms_present:1;
  int      m_is_paxl_present:1;
  int      m_is_rdsl_present:1;
  int      m_is_hcss_present:1;
  int      m_is_uddl_present:1;
  int      m_is_scas_present:1;  // Flag to indicate if SCAS chunk is present

  sPHYSICS_DESCRIPTORS_STRING m_pds;

}* CP_CDI_READER;

VOID sync_series_heat_exchanger_meas_windows();
VOID sync_condenser_meas_windows();

void print_coupled_phase_table();

extern std::vector<std::string> g_cdi_region_names;

#endif// CP_CDI_READER_H
