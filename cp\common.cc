/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * SIMENG CP support routines
 *
 * Jim Salem, Exa Corporation 
 * Created Tue Jun 14 1994
 *--------------------------------------------------------------------------*/

#include "common.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "errbuf.h"

#include TPI_H

int system_cmd(cSTRING cmd)
{
  // in case coupling/system commands are routed through the daemons
  // RT14450
  return cTHIRD_PARTY_INTERFACE::SystemCmd(cmd);

}

// Appends "." to the front of PATH before trying cmd.
BOOLEAN is_cmd_executable_via_system(cSTRING cmd)
{
  STRING test_cmd = xnew char [strlen(cmd) + 80];
  sprintf(test_cmd, "PATH=.:$PATH; export PATH; type %s > /dev/null 2>&1 ", cmd);

  int status = system_cmd(test_cmd);
  delete[] test_cmd;

  if (status != 0)
    return FALSE;
  else
    return TRUE;
}

VOID trim_trailing_ampersand_from_shell_cmd(STRING cmd)
{
  asINT32 len = strlen(cmd);
  if (len > 0) {
    asINT32 i = len - 1;
    while (i > 0 && isspace(cmd[i]))
      i--;
  
    if (cmd[i] == '&')
      cmd[i] = '\0';
  }
}

sSIM_ARGS sim_args;

void die_if_gpu(bool cond, cSTRING msg, bool plural)
{
  if (sim_args.gpu && cond) {
    cSTRING isare = plural ? "are" : "is";
    msg_error("%s %s not supported on GPU",msg,isare);
  }
}

/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/

BOOLEAN uval_to_lattice_value(cSTRING uval_str, 
			      cSTRING unit_class_name,
			      cSTRING printable_unit_class_name,
			      cSTRING lattice_unit_name,
			      cSTRING arg_name,
			      BOOLEAN use_msg_error, // as opposed to msg_warn
			      dFLOAT *result)
{
  typedef VOID (*MSG_FCN)(cSTRING format_string, ...);
  MSG_FCN msg_fcn = use_msg_error ? msg_error : msg_warn;

  STRING unit_str;
  dFLOAT value = strtod(uval_str, &unit_str);

  // skip white space
  while (*unit_str != '\0') {
    if (!isspace(*unit_str))
      break;
    unit_str++;
  }

  if (*unit_str == '\0') {
    msg_fcn("The %s option requires a unit.", arg_name);
    return FALSE;
  }

  if (cp_info.units_db == NULL) {
    if (strcmp(unit_str, lattice_unit_name) == 0) {
      *result = value;
      return TRUE;
    } else {
      msg_fcn("Your CDI file is old and lacks a units database, so the only unit"
	      " allowed for the %s option is %s.", arg_name, lattice_unit_name);
      return FALSE;
    }
  }

  UNITS_UNIT unit;
  UNITS_STATUS stat;

  // Ultimately we may interpret lack of a unit as "dimless"
  if (*unit_str == '\0') {
    // dimensionless unit
    stat = units_get_default_for_class(cp_info.units_db, "dimless", unit_class_name, &unit);
    if (stat != UNITS_STATUS_OK) {
      msg_fcn("Unable to parse %s option: %s", arg_name, units_error_string(stat));
      return FALSE;
    }
  } else {
    stat = units_parse_unit(cp_info.units_db, unit_str, &unit);
    if (stat != UNITS_STATUS_OK) {
      msg_fcn("Unable to parse %s option: %s: %s", arg_name, unit_str, units_error_string(stat));
      return FALSE;
    }
  }

  BOOLEAN is_convertible_to_class = FALSE;
  stat = units_convertible_to_class_p(cp_info.units_db, unit, unit_class_name, &is_convertible_to_class);
  if (stat != UNITS_STATUS_OK) {
    msg_fcn("Unable to parse %s option: %s: %s", arg_name, unit_str, units_error_string(stat));
    return FALSE;
  }

  if (!is_convertible_to_class) {
    msg_fcn("Unit included with %s option (%s) is not a %s unit.", arg_name, unit_str, printable_unit_class_name);
    return FALSE;
  } 

  // We don't support dimless units yet because the translations for dimless
  // units are not present in the CDI units database
  if (units_unit_get_type(cp_info.units_db, unit) == UNITS_UNIT_TYPE_DIMLESS_UNIT) {
    msg_fcn("The %s option does not yet support use of dimless units.", arg_name);
    return FALSE;
  }

  UNITS_UNIT lattice_unit;
  stat = units_parse_unit(cp_info.units_db, lattice_unit_name, &lattice_unit);
  if (stat != UNITS_STATUS_OK) {
    msg_fcn("Unable to parse %s option: %s: %s", arg_name, unit_str, units_error_string(stat));
    return FALSE;
  }

  dFLOAT lattice_value;
  stat = units_convert(cp_info.units_db, value, unit, lattice_unit, &lattice_value);
  if (stat != UNITS_STATUS_OK) {
    msg_fcn("Unable to parse %s option: %s: %s", arg_name, unit_str, units_error_string(stat));
    return FALSE;
  }

  *result = lattice_value;
  return TRUE;
}

/*----------------------------------------------------------------------------*
 * These routines ensure that asynchronous messages from the SPs (e.g., .simerr
 * entries) are serviced during initialization, preventing deadlock. 
 *----------------------------------------------------------------------------*/ 

// This is a wrapper on MPI_Recv that should be used during initialization. 
// MPI_Recv should not be used directly. This function listens for the desired
// message, but if not immediately available, services any messages that may 
// arrive asynchronously from the SPs. At the moment this only includes SP 
// errors destined for the .simerr file.

int cp_init_mpi_send(void *buf, int count, MPI_Datatype datatype, int rank, int tag, MPI_Comm mpi_comm)
{
  MPI_Request mpi_request;
  MPI_Isend(buf, count, datatype, rank, tag, mpi_comm, &mpi_request);

  while (1) {
    int flag = 0;
    MPI_Status status;
    MPI_Test(&mpi_request, &flag, &status);
    if (flag)
      return 0; // should return status code consistent with MPI_Send, not simply 0
    if (cp_process_sp_errors_initialized())
      cp_process_sp_errors(wallclock_time_secs());
  }
}


/* This function will be called when the CP sends an LGI
 * buffer to an SP if the send does not immediately succeed. The function 
 * should service asynchronous messages/RPCs that may be arriving from the SPs.
 */
VOID cp_process_init_time_simerrs_func() {
  cp_process_sp_errors(wallclock_time_secs());
}

// This function reserves sri_user_vars by any client
// that wishes to use SRI user defined variables.  
VOID reserve_sri_user_vars(asINT32 n_vars) {
  static asINT32 next_available_sri_user_var = (asINT32) SRI_VARIABLE_FIRST_USER_VARIABLE;
  next_available_sri_user_var += n_vars;
}
