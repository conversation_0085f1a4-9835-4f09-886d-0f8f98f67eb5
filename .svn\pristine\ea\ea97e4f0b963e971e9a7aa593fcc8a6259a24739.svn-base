/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#include "cdi_common.h"
#include "cdi_encrypted_io.h"

/*************************************************************************/
/** The ** open_file ** function takes in a filename for a cdi file     **/
/** attempts to open it, and if successful, sets the cio pointer at     **/
/** the case chunk of the cdi file.                                     **/
/*************************************************************************/

#define CIO_LARGE_FILE_OFFSETS 100000000

/***
 ** rewind a CDI file to the beginning to allow re-reading chunks
 **
 ***/
asINT32 cdi_rewind(CDI_INFO cdi_info)
{
  // Need guard to prevent if not read once already
  cio_rewind(cdi_info->cio_info);
  cdi_seed_cipher(cdi_info);

  return 0;
}

/***
 **  open a CDI file for reading, and create a CIO_INFO structure for it
 **/
CDI_INFO
cdi_open_for_read(cSTRING filename, asINT32 *user_err_ptr)
{
  asINT32 internal_err_value;
  asINT32 *err_value_ptr = user_err_ptr != NULL ? user_err_ptr : &internal_err_value;
  CDI_INFO cdi_info = EXA_CALLOC_STRUCT(CDI_INFO);
  cdi_info->init();
  CIO_CCCC ftype;		/* Type that will help us know if we've
				got a proper file type */

  *err_value_ptr = CDI_ERR_OK;

  cdi_info->check_pointer = (VOID *) cdi_info;

  assert(filename != NULL);
  cdi_info->io_mode = CDI_IO_MODE_READ;

  /* If unable to open the specified file for reading... */
  if ((cdi_info->cio_info = cio_open(filename, CIO_IO_MODE_READ)) == NULL) {
    EXA_FREE(cdi_info);		/* Release the CDI structure... */
    *err_value_ptr = CDI_ERR_OPEN_READ_FAILED;
    return(NULL);		/* Return the error state */
  }

  /* Read the xcdi chunk */
  cdi_read_chars(cdi_info, (char *) &ftype, sizeof(ftype));
  ftype = cio_string_to_type((char *) &ftype);

  /* If chunk read was not XCDI... */
  if (ftype != CDI_CHUNK_TYPE_XCDI) {
      cio_release(cdi_info->cio_info);
      EXA_FREE(cdi_info);
      *err_value_ptr = CDI_ERR_OPEN_READ_NOT_CDI;
      return(NULL);		/* Return in the error state */
    }

  /* Grab the XCDI structure (version numbers) */
  cdi_read_asINT32(cdi_info, &cdi_info->major_version, 1);
  cdi_read_asINT32(cdi_info, &cdi_info->minor_version, 1);

  /* Check for negative version numbers (incomplete CDI write) */
  if ((cdi_info->major_version < 0) || (cdi_info->minor_version < 0)) {
    cio_release(cdi_info->cio_info);
				/* Release the CIO structure... */
    EXA_FREE(cdi_info);	/* Release the CDI structure... */
    *err_value_ptr = CDI_ERR_OPEN_READ_CDI_INCOMPLETE;
    return(NULL);		/* Return in the error state */
  }

  if (cdi_info->major_version > CIO_LARGE_FILE_OFFSETS) {
    cdi_info->cio_info->large_file_offsets = TRUE;
    cdi_info->major_version -= CIO_LARGE_FILE_OFFSETS;
  }

#if	0
  /* If the file is beyond version numbers we know... */
  if (CDI_VERSION_NEWER(cdi_info->major_version, cdi_info->minor_version)) {
    cio_release(cdi_info->cio_info);
    EXA_FREE(cdi_info);
    *err_value_ptr = CDI_ERR_OPEN_READ_ADVANCED_VERSION;
				/* Flag the state, but don't fail */
    /* return(NULL); */		/* Return in the error state */
  }
#endif

  /* If unable to descend into a block as we would expect... */
  if (cio_descend(cdi_info->cio_info) != CIO_ERR_SUCCESS) {
    cio_release(cdi_info->cio_info);
    EXA_FREE(cdi_info);
    *err_value_ptr = CDI_ERR_OPEN_READ_CDI_DAMAGED;
    return(NULL);		/* Return in the error state */
  }

  return(cdi_info);
}


/***
 **  open a CDI file for writing, and create a CIO_INFO structure for it
 **/
CDI_INFO
cdi_open_for_write(cSTRING filename,
		   asINT32 major_version, asINT32 minor_version,
		   asINT32 *user_err_ptr, 
                   BOOLEAN use_large_file_offsets)
{
  asINT32 internal_err_value;
  asINT32 *err_value_ptr = user_err_ptr != NULL ? user_err_ptr : &internal_err_value;
  CDI_INFO cdi_info = EXA_CALLOC_STRUCT(CDI_INFO);
  cdi_info->init();

  CIO_CCCC chunk_type = CDI_CHUNK_TYPE_XCDI;
  char chunk_type_str[5];
  cio_type_to_string(chunk_type, chunk_type_str);

  *err_value_ptr = CDI_ERR_OK;

  cdi_info->check_pointer = (VOID *) cdi_info;

  assert(filename != NULL);
  cdi_info->io_mode = CDI_IO_MODE_WRITE;

  /* Perform initial CIO open, catch failure if any */
  if ((cdi_info->cio_info = cio_open(filename, CIO_IO_MODE_WRITE)) == NULL) {
    *err_value_ptr = CDI_ERR_OPEN_WRITE_FAILED;
    return(NULL);
  }

  cdi_info->cio_info->large_file_offsets = use_large_file_offsets;

  /* If requested to write specific version numbers... */
  if ((major_version >= 0 && minor_version >= 0) &&
      (major_version > 0 || minor_version > 0)) {
    if (CDI_VERSION_NEWER(major_version, minor_version)) {
      *err_value_ptr = CDI_ERR_OPEN_WRITE_UNK_VERSION;
      EXA_FREE(cdi_info);
      return(NULL);
    }
    cdi_info->major_version = major_version;
    cdi_info->minor_version = minor_version;
  }
  /* Else, providing a default version number... */
  else {
    cdi_info->major_version = CDI_MAJOR_VERSION;
    cdi_info->minor_version = CDI_MINOR_VERSION;
  }

  /**
   * While `xcdi' superficially looks like an outer CIO block, it really
   * isn't.  The first real CIO block (characterized by a CIO type/length
   * ordered pair) is actually `case'.  xcdi has no length.
   */
  cdi_write_chars(cdi_info, (char *) chunk_type_str, sizeof(chunk_type));

  fflush(cdi_info->cio_info->fp);

  /* Stash a bit of info in the major version to instruct CIO to use 64-bit file
     offsets.*/
  if (cdi_info->cio_info->large_file_offsets)
    cdi_info->major_version += CIO_LARGE_FILE_OFFSETS;

  /* Turn version vals negative to designate incomplete */
  cdi_info->major_version *= -1;
  cdi_info->minor_version *= -1;

  /* Write out the negative version values */
  cdi_write_asINT32(cdi_info, &cdi_info->major_version, 1);
  cdi_write_asINT32(cdi_info, &cdi_info->minor_version, 1);

  /* Restore version values to ordinary form */
  cdi_info->major_version *= -1;
  cdi_info->minor_version *= -1;
  if (cdi_info->cio_info->large_file_offsets)
    cdi_info->major_version -= CIO_LARGE_FILE_OFFSETS;

  /* Enter the case chunk */
  cdi_push(cdi_info, CDI_CHUNK_TYPE_CASE);

  return(cdi_info);
}


/***
 ** release the CIO_INFO structure, and close the file
 **/
VOID
cdi_close (CDI_INFO cdi_info)
{
  if (cdi_info->io_mode == CDI_IO_MODE_READ) {
    cio_ascend(cdi_info->cio_info);
  } else {
    CIO_CCCC chunk_type = CDI_CHUNK_TYPE_XCDI;
    char chunk_type_str[5];
    cio_type_to_string(chunk_type, chunk_type_str);

    cdi_pop(cdi_info);

    /* Reset to the beginning of the file */
    fseek(cdi_info->cio_info->fp, 0UL, SEEK_SET);

    cdi_write_chars(cdi_info, (char *) &chunk_type_str, sizeof(chunk_type));
    if (cdi_info->cio_info->large_file_offsets)
      cdi_info->major_version += CIO_LARGE_FILE_OFFSETS;
    cdi_write_asINT32(cdi_info, &cdi_info->major_version, 1);
    if (cdi_info->cio_info->large_file_offsets)
      cdi_info->major_version -= CIO_LARGE_FILE_OFFSETS;
    cdi_write_asINT32(cdi_info, &cdi_info->minor_version, 1);

  }
  cdi_destroy_cipher(cdi_info);
  cio_release(cdi_info->cio_info);
  EXA_FREE(cdi_info);
}

asINT32 cdi_find_and_read_ptge(CDI_INFO cdi_info, bool rewind)
{
  if (rewind)
    // PTGE is most likely at the beginning of the file
    cdi_rewind(cdi_info);
  
  asINT32 error = cdi_descend_in_chunk(cdi_info, CDI_CHUNK_TYPE_PTGE);
  if (error == CDI_ERR_OK) {
    CDI_PTGE ptge = cdi_read_ptge(cdi_info);
    cdi_destroy_ptge(ptge);
    error = cdi_ascend_out(cdi_info);
  } else {
    // No PTGE chunk
    // Turn off the encryption 
    cdi_set_encryption_off(cdi_info);
  }

  if (rewind)
    cdi_rewind(cdi_info);

  if (error != CDI_ERR_OK)
    error = CDI_ERR_BAD_STRUCT;

  return error;
}

asINT32 cdi_ascend_out(CDI_INFO cdi_info)
{
  CIO_ERRCODE error = cio_ascend(cdi_info->cio_info);

  if (error != CIO_ERR_SUCCESS)
    error = CDI_ERR_BAD_STRUCT;

  return error;
}

asINT32 cdi_descend_in_chunk(CDI_INFO cdi_info, CIO_CCCC type)
{
  CIO_INFO cio = cdi_info->cio_info;

  CIO_ERRCODE error = CIO_ERR_SUCCESS;
  bool found = false;
  do {
    error = cio_descend(cio);
    if (error == CIO_ERR_SUCCESS) {
      CIO_CCCC ctype = cio_get_type(cio);
      
      if (ctype == type) {
        found = true;

#if 0      
      // debug 
      char type_str[10];
      cio_type_to_string(ctype, type_str);
      std::string typeStr(&type_str[0],4);
      std::cerr << " descend in chunk " << typeStr << std:: endl;
#endif

        break;
      }
      cio_ascend(cio);
    }
  } while (error != CIO_ERR_FAIL && !found);

  // Either it's not in file or the file should be rewound beforehand
  if (!found || error == CIO_ERR_FAIL) {
    error = CDI_ERR_BAD_STRUCT;  
  }

  return error;
}

#define CURR_OFFSET(c)		(c)->stack[(c)->depth-1].curr_offset
void cdi_find_chunks(CDI_INFO cdi_info, 
                    std::set<CIO_CCCC> &types_to_find, 
                    std::set<CIO_CCCC> &types_found)
{
  CIO_INFO cio = cdi_info->cio_info;
  // Tested when called after open or rewind
  // Should be callable whenever as it returns the parser to its start position
  auINT64 start_offset = CURR_OFFSET(cio);

  // Count the number of children in the current chunk
  asINT32 count = cio_get_count(cio);
  for (asINT32 i = 0; i < count ; ++i ) {
    
    cio_descend(cio);
    CIO_CCCC type = cio_get_type(cio);
    if (types_to_find.find(type) != std::end(types_to_find)) {
      types_found.insert(type);
    }
    cio_ascend(cio);
  }

  // Restore start position
  CURR_OFFSET(cio) = start_offset;
  fseekf(cio->fp, start_offset, SEEK_SET);
}

/***
 ** This function returns cdi file dim (2 or 3), if the information has been captured
 ** successfully.  It returns 0 if it failed.  Origin is defined as the negative shift
 ** in the simv transformation
 **/
asINT32
cdi_get_origin(cSTRING filename, dFLOAT origin[3])
{
  asINT32 error_val;
  CDI_INFO cdi = cdi_open_for_read(filename, &error_val);

  if (cdi == NULL) {return 0;}

  CDI_DO_INNER_CHUNKS(case_child, "case", cdi, {
    asINT32 type = cdi_get_type(cdi);
    if (type == CDI_CHUNK_TYPE_SIMV) {
      CDI_SIMV simv = cdi_read_simv(cdi);
      asINT32 n_dims = simv->flags & CDI_IS_2D ? 2 : 3;
      DOTIMES(i, 3, {
        origin[i] = -simv->xform[i][3];
      });
      cdi_destroy_simv(simv);
      cdi_close(cdi);
      return n_dims;
    }
  });
  cdi_close(cdi);
  return 0;
}


/***
 ** returns -1 if the file cannot be opened, or meters_per_cell has not been captured
 **/
dFLOAT cdi_get_meters_per_cell(cSTRING filename)
{
  dFLOAT meters_per_cell = -1.0;
  CDI_INFO cdi = cdi_open_for_read(filename, NULL);

  if (cdi == NULL) {return meters_per_cell;}

  CDI_DO_INNER_CHUNKS(case_child, "case", cdi, {
    asINT32 type = cdi_get_type(cdi);
    
    if (type == CDI_CHUNK_TYPE_GLOB) {
      CDI_DO_INNER_CHUNKS(i, "glob", cdi, {
	if (cdi_get_type(cdi) == CDI_CHUNK_TYPE_UNIT) {
	  CDI_UNIT unit = cdi_read_unit(cdi);
	  meters_per_cell = unit->meters_per_cell;
	  if (meters_per_cell == 0.0) /* lattice units */
	    meters_per_cell = 1.0;
	  cdi_destroy_unit(unit);
	  cdi_close(cdi);
	  return meters_per_cell;
	}
      });
    }
  });
  cdi_close(cdi);
  return meters_per_cell;
}

static asINT32 get_grid_n_scales(CDI_INFO cdi_info)
{ 
  asINT32 max_scale = 0;

  CDI_DO_INNER_CHUNKS(i, "grid", cdi_info, {
    if ( cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GSCL) {
      CDI_RGNS rgns = NULL;
      bool usesSegment = false;
      CDI_DO_INNER_CHUNKS(j, "gscl", cdi_info, {
        if ( cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_RGNS) {
          rgns = cdi_read_rgns(cdi_info);
        }
        else if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GMRF) {
          cCDI_GEOMETRY_REF* geom_ref = cdi_read_gmrf(cdi_info);
          usesSegment = !geom_ref->segment_list.empty();
        }
        else if ( cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GSCC) {
          CDI_GSCC gscc = cdi_read_gscc(cdi_info);
          if (rgns || usesSegment) {
            if (gscc->value > max_scale)
              max_scale = gscc->value;
          }
          cdi_destroy_gscc(gscc);
        } 
      });

      if (rgns) 
        cdi_destroy_rgns(rgns);
    }
    if ( cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GFDS) {
      CDI_FDLT fdlt = NULL;

      CDI_DO_INNER_CHUNKS(j, "gfds", cdi_info, {
        if ( cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_FDLT) {
          fdlt = cdi_read_fdlt(cdi_info);
        }
        else if ( cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GSCC) {
          CDI_GSCC gscc = cdi_read_gscc(cdi_info);
          if (fdlt) {
            if (gscc->value > max_scale)
              max_scale = gscc->value;
          }
          cdi_destroy_gscc(gscc);
        } 
      });

      if (fdlt) 
        cdi_destroy_fdlt(fdlt);
    }
  });
 
  return max_scale+1;
}

asINT32
cdi_get_n_scales (cSTRING filename)
{
  asINT32 n_scales = 1;

  if (!filename) { 
    msg_error("Missing cdi interface filename %s.\n", filename); 
    return(n_scales); 
  }

  else {
    CDI_INFO cdi = cdi_open_for_read(filename, NULL);

    if (!cdi) {	
       msg_error("Couldn't open file %s for input.\n", filename); 
       return(n_scales); 
    }
 
    CDI_DO_INNER_CHUNKS(case_child, "case", cdi, {
      asINT32 type = cdi_get_type(cdi);
      if (type == CDI_CHUNK_TYPE_GRID) {
	n_scales = get_grid_n_scales(cdi);
      }
    });

    cdi_close(cdi);
    return (n_scales > 0) ? n_scales : 1;
  }
}


int
cdi_get_solver_version(asINT32 simv_flags)
{
  if ((simv_flags & CDI_19_STATE) == 0 &&
      (simv_flags & CDI_39_STATE) == 0)
    return 3;
  if (simv_flags & CDI_5_0_SOLVER)
    return 5;
  if (simv_flags & CDI_6_0_SOLVER)
    return 6;

  return 4; // Version 4 didn't have an easy indicator so we make it the fallthrough
}

// This function is used by both exafile and exasumcdi to report information about which version was
// used to generate the CDI file as well as which versions of the solver it's compatible with.
std::string cdi_get_pflow_compatibility_string(cdiINT32 major_version,
                                               cdiINT32 minor_version,
                                               asINT32 simv_flags)
{
  bool is_multi_phase = (simv_flags & CDI_IS_MULTI_PHASE);
  bool is_heat_transfer = (simv_flags & CDI_IS_HEAT_TRANSFER);
  bool isTurbLowMachRegime = ((simv_flags & CDI_IS_TURB_MODEL) &&
                              !(simv_flags & CDI_HIGH_SUBSONIC_MACH_REGIME) &&
                              !(simv_flags & CDI_39_STATE));

  // PowerFLOW 5.2 was the last release to have a matching 4.X version, it wrote CDI files of
  // version 4.2. Anything newer will not be usable with 4.X.
  bool after_4x_retired  = CDI_VERSION_AT_LEAST(major_version, minor_version, 4, 3);
  bool uses_5x_only_feature = (simv_flags & CDI_USES_5_X_ONLY_FEATURE) || after_4x_retired;

  bool after_5x_retired = false; // Set with appropriate logic once 5.X line is retired
  bool uses_6x_only_feature = (simv_flags & CDI_USES_6_X_ONLY_FEATURE) || after_5x_retired;

  if (is_multi_phase)               return "5.0+ (5g)";

  switch (cdi_get_solver_version(simv_flags)) {
  case 3:
                                    return "3.X";
  case 4:
    if (is_heat_transfer)           return "4.X";
    else if (isTurbLowMachRegime)   return "4.X (5.X compatible)";
    else                            return "4.X (5.X & 6.X compatible)";
  case 5:
    if (isTurbLowMachRegime) {
      if (is_heat_transfer || 
          uses_5x_only_feature)     return "5.X";
      else                          return "5.X (4.X compatible)";
    }
    else {
      if (is_heat_transfer || 
          uses_5x_only_feature)     return "5.X (4.X & 6.X compatible)";
      else                          return "5.X (6.X compatible)";
    }
  case 6:
    if (isTurbLowMachRegime ||
        uses_6x_only_feature)       return "6.X";
    else                            return "6.X (5.X compatible)";
  };
  return "<unknown>";
}
