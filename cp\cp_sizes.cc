#include "window.h"
#include "export_simsizes.h"
#include SIMSIZES_SHARED_H
/*=================================================================================================
 * See export_simsizes.h for explanation behind this trivial class
 *================================================================================================*/

CP_SIZES_INTERFACE::CP_SIZES_INTERFACE(const SIM_SIZES::SIM_OPTIONS& opts):
  m_nLrfs(opts.n_lrfs), m_nDims(opts.num_dimensions),
  m_ublks(opts.n_flow_ublks + opts.n_cond_ublks), m_surfels(opts.n_flow_surfels + opts.n_cond_surfels),
  m_bsurfels(opts.n_bsurfels)
{

}
  
size_t CP_SIZES_INTERFACE::size_of_cdi_meas_window() const {
  return sizeof(sCDI_MEAS_WINDOW);
}

size_t CP_SIZES_INTERFACE::size_of_cp_meas_cell_reflist_elt() const {
  return sizeof(sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF_LIST_ELT);
}

size_t CP_SIZES_INTERFACE::size_of_cp_meas_cell_ref() const {
  return sizeof(sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF);
}

size_t CP_SIZES_INTERFACE::size_of_cp_composite_fluid_meas_window() const {
  return sizeof(sCP_COMPOSITE_FLUID_MEAS_WINDOW);
}

size_t CP_SIZES_INTERFACE::size_of_cp_fluid_meas_window() const {
  return sizeof(sCP_FLUID_MEAS_WINDOW);
}

size_t CP_SIZES_INTERFACE::size_of_cp_composite_surface_meas_window() const {
  return sizeof(sCP_COMPOSITE_SURFACE_MEAS_WINDOW);
}

size_t CP_SIZES_INTERFACE::size_of_cp_surface_meas_window() const {
  return sizeof(sCP_SURFACE_MEAS_WINDOW);
}

size_t CP_SIZES_INTERFACE::compute_cp_misc_storage() const {
  size_t storage = 0;
  size_t surfel_vertex_storage = 3 * sizeof(sFLOAT); // Storage always allocated for 3-D
  storage += m_surfelVertices * surfel_vertex_storage;
  storage += m_ublks *  sizeof(STP_PROC);
  storage += m_surfels * sizeof(STP_PROC);

  return(storage);
}

size_t CP_SIZES_INTERFACE::compute_cp_meas_storage() const {

  size_t storage = 0;

  // This is identical to ComputeSPMeasStorage, except only a single float is allocated in the CP
  storage += m_cpMeasCellVars * sizeof(sriFLOAT);
  
  // These are compound storage sizes 

  asINT64  nonCompositeFluidCellHeaderStorage = 
      sizeof(sriBYTE)                             // scale
    + m_nDims * sizeof(sriINT)                    // coord
    + sizeof(sriFLOAT)                            // volume
    + sizeof(sriPART_INDEX);

  if(m_nLrfs > 0) nonCompositeFluidCellHeaderStorage +=sizeof(sriPART_ID);

  // nearWallCellStorage is not currently used

  asINT64   nearWallCellStorage = 
    sizeof(sriINT)  // cell index
    + m_nDims * sizeof(sriFLOAT); // centroid 

  asINT64 compositeFluidCellHeaderStorage = sizeof(sriFLOAT)        // volume
    + sizeof(sriPART_INDEX);

  asINT64 nonCompositeSurfaceCellHeaderStorage = 
    sizeof(sriBYTE)              // scale
    + m_nDims * sizeof(sriFLOAT) // normal
    + sizeof(sriFLOAT)           // area
    + sizeof(sriPOINT)
    + sizeof(sriINT);            // first vertex ref

  asINT64 compositeSurfaceCellHeaderStorage = 
    sizeof(sriFLOAT)             // face area
    + m_nDims * sizeof(sriFLOAT) // projected area 
    + sizeof(sriPOINT);


  ccDOTIMES(wtype, LGI_N_MEAS_WINDOW_TYPES) {
    storage += m_nonCompositeWindowCells[wtype] * size_of_cp_meas_cell_ref();
    storage += m_compositeWindowCells[wtype] * size_of_cp_meas_cell_ref();

    switch(wtype) {
    case LGI_FLUID_WINDOW:
    case LGI_POROUS_WINDOW: {
      storage += m_nonCompositeWindows[wtype] * size_of_cp_fluid_meas_window();
      storage += m_compositeWindows[wtype] * size_of_cp_composite_fluid_meas_window();
      storage += m_nonCompositeWindowCells[wtype] *  nonCompositeFluidCellHeaderStorage;
      storage += m_compositeWindowCells[wtype] *  compositeFluidCellHeaderStorage;
      break;
    }
    case LGI_SURFACE_WINDOW: {
      storage += m_nonCompositeWindows[wtype] * size_of_cp_surface_meas_window();
      storage += m_compositeWindows[wtype] * size_of_cp_composite_surface_meas_window();
      storage += m_nonCompositeWindowCells[wtype] *  nonCompositeSurfaceCellHeaderStorage;
      storage += m_compositeWindowCells[wtype] *  compositeSurfaceCellHeaderStorage;
      storage += m_nonCompositeWindowVertexRefs[wtype] * sizeof(sriINT);
      if(m_nLrfs > 0) storage += m_nonCompositeWindowCells[wtype] * sizeof(sriPART_ID);
      break;
    }
    case LGI_SHELL_WINDOW: {
      storage += m_nonCompositeWindows[wtype] * size_of_cp_surface_meas_window();
      storage += m_compositeWindows[wtype] * size_of_cp_composite_surface_meas_window();
      storage += m_nonCompositeWindowCells[wtype] *  nonCompositeSurfaceCellHeaderStorage;
      storage += m_compositeWindowCells[wtype] *  compositeSurfaceCellHeaderStorage;
      storage += m_nonCompositeWindowVertexRefs[wtype] * sizeof(sriINT);
      if(m_nLrfs > 0) storage += m_nonCompositeWindowCells[wtype] * sizeof(sriPART_ID);
      break;
    }
    case LGI_SAMPLING_SHELL_WINDOW:
    case LGI_SAMPLING_SURFACE_WINDOW: {
      storage += m_nonCompositeWindows[wtype] * size_of_cp_surface_meas_window();
      storage += m_compositeWindows[wtype] * size_of_cp_composite_surface_meas_window();
      storage += m_nonCompositeWindowCells[wtype] *  nonCompositeSurfaceCellHeaderStorage;
      storage += m_compositeWindowCells[wtype] *  compositeSurfaceCellHeaderStorage;
      storage += m_nonCompositeWindowVertexRefs[wtype] * sizeof(sriINT);
      if(m_nLrfs > 0) storage += m_nonCompositeWindowCells[wtype] * sizeof(sriPART_ID);
      storage += m_nonCompositeWindowCells[wtype] * sizeof(sriFACET_ID); 
      break;
    }
    default: {
      break;
    }
    }
  }

  storage += m_cp_cdi_meas_window * size_of_cdi_meas_window();

  return(storage);
}


asINT64  CP_SIZES_INTERFACE::size_of_sriDOUBLE = sizeof(sriDOUBLE);
