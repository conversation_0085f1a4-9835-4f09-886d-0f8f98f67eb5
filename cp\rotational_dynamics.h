/* ~~~CO<PERSON>Y<PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#ifndef __ROTATIONAL_DYNAMICS_H
#define __ROTATIONAL_DYNAMICS_H

#define CP_ROTDYN_STR(x) #x
#define CP_ROTDYN_PRINT_FLOAT(x) printf(CP_ROTDYN_STR(x) " = %.10e\n", x);
#define CP_ROTDYN_PRINT_INT(x) printf(CP_ROTDYN_STR(x) " = %d\n", x);

const asINT32 NUM_TIME_VARYING_TORQUE_VALUES = 1000; // if changed, edit this in SIMENG too

extern dFLOAT g_timestep_per_lat_time_inc;
extern sFLOAT g_user_max_vel;

typedef struct sCP_ROTATIONAL_DYNAMICS_DESC {
  dFLOAT          *m_resistive_torque;
  dFLOAT          *m_resistive_torque_buffer;
  dFLOAT          *m_external_torque;
  dFLOAT          *m_external_torque_buffer;
  cBOOLEAN        m_is_resistive_torque_time_varying;
  cBOOLEAN        m_is_external_torque_time_varying;
  MPI_Request     m_resistive_torque_mpi_request;
  MPI_Request     m_external_torque_mpi_request;
  asINT32         m_trq_idx;
  dFLOAT          m_initial_omega;
  dFLOAT          m_moment_of_inertia;
  dFLOAT          m_angular_acceleration;
  dFLOAT          m_angular_acceleration_m1;
  dFLOAT          m_force[3];
  dFLOAT          m_torque[3];
  dFLOAT          m_tau;
  dFLOAT          m_tau_m1;
  dFLOAT          m_tau_abs;
  dFLOAT          m_tau_abs_m1;
  dFLOAT          m_char_MI;
  dFLOAT          m_omega_max;
  CP_MEAS_WINDOW  m_window;
  cBOOLEAN        m_is_time_accurate;
  dFLOAT          m_cdi_start_time;
  dFLOAT          m_cdi_end_time;
  TIMESTEP        m_start_time;
  TIMESTEP        m_end_time;
  asINT32         m_lrf_index;
  dFLOAT          m_axis_origin[3];
  dFLOAT          m_axis_direction[3];
  dFLOAT          m_max_radius_lrf;
  dFLOAT          m_lrf_height;
  asINT32         m_count;
  dFLOAT          m_omega;
  dFLOAT          m_omega_next;
  dFLOAT          m_cur_alpha_weight;
  dFLOAT          m_mi_decrease_factor;
  dFLOAT          m_initial_mi_coeff;
  dFLOAT          m_min_mi_ratio;
  MPI_Request     *m_mpi_requests;

  void   read_user_inputs();
  void   update_dynamics();
  dFLOAT torque_about_axis();
  void   compute_angular_acceleration(asINT32);
  void   do_rotational_dynamics();

  sCP_ROTATIONAL_DYNAMICS_DESC()
  {
    m_is_resistive_torque_time_varying = FALSE;
    m_is_external_torque_time_varying  = FALSE;
    m_trq_idx            = 0;
    m_count              = 0;
    m_tau                = 0.0;
    m_mi_decrease_factor = 1.1;
    m_initial_mi_coeff   = 1.0;
    m_cur_alpha_weight   = 0.1;
    m_min_mi_ratio       = 1.0/30;
    m_angular_acceleration = 0.0;
    m_angular_acceleration_m1 = 0.0;

    m_resistive_torque = NULL;
    m_resistive_torque_buffer = NULL;
    m_external_torque  = NULL;
    m_external_torque_buffer  = NULL;    
    m_mpi_requests = NULL;
  }

  ~sCP_ROTATIONAL_DYNAMICS_DESC()
  {
    if (m_resistive_torque != NULL) delete [] m_resistive_torque;
    if (m_resistive_torque_buffer != NULL) delete [] m_resistive_torque_buffer;
    if (m_external_torque != NULL) delete [] m_external_torque;
    if (m_external_torque_buffer != NULL) delete [] m_external_torque_buffer;
    if (m_mpi_requests != NULL) delete [] m_mpi_requests;
  }

  void print()
  {
    CP_ROTDYN_PRINT_INT(m_start_time)
    CP_ROTDYN_PRINT_INT(m_end_time)
    CP_ROTDYN_PRINT_FLOAT(m_angular_acceleration)
    CP_ROTDYN_PRINT_FLOAT(m_angular_acceleration_m1)
    CP_ROTDYN_PRINT_INT(m_count)
    CP_ROTDYN_PRINT_INT(m_is_resistive_torque_time_varying)
    CP_ROTDYN_PRINT_INT(m_is_external_torque_time_varying)
    
    dFLOAT res_trq = (m_is_resistive_torque_time_varying)? 
                     m_resistive_torque[m_count % NUM_TIME_VARYING_TORQUE_VALUES] :
                     *m_resistive_torque;
    CP_ROTDYN_PRINT_FLOAT(res_trq)
    
    dFLOAT ext_trq = (m_is_external_torque_time_varying)? 
                     m_external_torque[m_count % NUM_TIME_VARYING_TORQUE_VALUES] :
                     *m_external_torque;
    CP_ROTDYN_PRINT_FLOAT(ext_trq)
  }

} *CP_ROTATIONAL_DYNAMICS_DESC;

#endif /* __ROTATIONAL_DYNAMICS_H */
