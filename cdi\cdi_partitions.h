/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#ifndef _CDI_PARTITIONS_H
#define _CDI_PARTITIONS_H

#include <memory>
#include <map>
#include <list>

#include "cdi_common.h"

class cCDI_SEGMENT;
class cCDI_SEGMENT_REF;
class cCDI_PARTIAL_PART;
class cCDI_PARTITION;
class cCDI_PARTITIONS;

struct sSEGMENT_ASSIGNMENT {
  enum eASSIGNMENT_TYPE { Assigned, SameAsParent, Excluded };
  eASSIGNMENT_TYPE type;
  cCDI_SEGMENT* segment;
};

class cCDI_SEGMENT
{
public:
  cCDI_SEGMENT(std::string& name, cCDI_PARTITION* partition, cCDI_SEGMENT* parentSegment);
  ~cCDI_SEGMENT();

  // Definitions and Methods for CDI support
#define CDI_CHUNK_TYPE_SGMT CIO_BUILDCCCC('s', 'g', 'm', 't')
  static CIO_CCCC GetSegmentChunkType() { return CDI_CHUNK_TYPE_SGMT; }
  CIO_CCCC GetChunkType() const { return GetSegmentChunkType(); }
#define CDI_CHUNK_TYPE_SGMI CIO_BUILDCCCC('s', 'g', 'm', 'i')
  CIO_CCCC GetSegmentInfoChunkType() const { return CDI_CHUNK_TYPE_SGMI; }
#define CDI_CHUNK_TYPE_SGMP CIO_BUILDCCCC('s', 'g', 'm', 'p')
  CIO_CCCC GetSegmentMappingChunkType() const { return CDI_CHUNK_TYPE_SGMP; }
#define CDI_CHUNK_TYPE_PPTS CIO_BUILDCCCC('p', 'p', 't', 's')
  CIO_CCCC GetPartialPartsChunkType() const { return CDI_CHUNK_TYPE_PPTS; }
  
  void ReadFromCDI(CDI_INFO cdi_info);
  void WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(std::ostream &output, int depth, int version) const;
  void Undump(int version);

  // Properties
  bool IsRoot() const { return m_parentSegmentIndex < 0; }
  const cCDI_PARTITION& GetPartition() const { return *m_partition; }
  cCDI_SEGMENT* GetParentSegment() const;
  cdiINT32 GetParentSegmentIndex() const { return m_parentSegmentIndex; }

  const std::string GetName(bool includePath = false) const;
  void SetName(std::string& name) { m_name = name; }

  // These methods return the name of the part/face accounting for duplicate names in a
  // segment (which can only happen in derived partitions). If "includePath" is true, then
  // the name is prepended with the partition name and path of the segment. It will return
  // false and an empty name if the part/face is not in the partition.
  bool GetQualifiedPartName(cdiINT32 partIndex, bool includePath, std::string* partName) const;
  bool GetQualifiedFaceName(cdiINT32 faceIndex, bool includePath, std::string* faceName) const;

  // Face, Parts, and Segment Membership Methods
  // ALL the faces (assigned and inherited) in the segment
  std::vector<cdiINT32> GetFaceIndices(bool recursive) const;
  // Faces explicitly assigned to the segment
  std::vector<cdiINT32> GetAssignedFaceIndices() const;
  // Faces that are in this segment due to inheritance
  std::vector<cdiINT32> GetInheritedFaceIndices() const;
  
  // ALL the regions/parts (assigned and mapped) in the segment
  std::vector<cdiINT32> GetRegionIndices(bool recursive) const;
  // Parts/Regions explicitly assigned to the segment
  std::vector<cdiINT32> GetAssignedRegionIndices() const { return m_assignedRegionIndices; }
  void AddAssignedRegionIndex(cdiINT32 iRegion) { m_assignedRegionIndices.push_back(iRegion); }
  // Parts/Regions in this segment due to inheritance
  std::vector<cdiINT32> GetInheritedRegionIndices() const { return m_inheritedRegionIndices; }
  void AddInheritedRegionIndex(cdiINT32 regionIndex) { m_inheritedRegionIndices.push_back(regionIndex); }
  
  // Segment "Assignments" are where this segment maps to in a derived partition
  sSEGMENT_ASSIGNMENT GetSegmentAssignment(const cCDI_PARTITION& derivedPartition);
  void AddSegmentAssignment(const cCDI_PARTITION& assignedPartition, cCDI_SEGMENT* assignedSegment);
  // Get where and how the parts/regions in this segment are assigned to a child partition
  std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> GetRegionAssignments(const cCDI_PARTITION& derivedPartition);
  // Get where and how the faces in this segment are assigned to a children partition
  std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> GetFaceAssignments(const cCDI_PARTITION& derivedPartition);
  
  const std::vector<cCDI_SEGMENT*>& GetChildSegments() const { return m_childSegments; }
  std::list<const cCDI_SEGMENT*> GetPath() const;

  // Use CreateChildSegment to create a new child segment. The "ownership" of
  // the created segments get passed to the cCDI_PARTITION
  cCDI_SEGMENT& CreateChildSegment(std::string& name);
  // AddChildSegment is a method to keep track of children. It should not necessarily be
  // called; it is used when loading CDIs
  void AddChildSegment(cCDI_SEGMENT* childSegment) { m_childSegments.push_back(childSegment); }

  // Use CreatePartialPart to create a new partial part. The "ownership" of
  // the created partial parts get passed to the cCDI_PARTITION
  cCDI_PARTIAL_PART& CreatePartialPart(cdiINT32 partIndex);
  // Find a partial part for the given part index
  cCDI_PARTIAL_PART* FindPartialPart(cdiINT32 partIndex) const;
  const std::vector<cCDI_PARTIAL_PART*>& GetPartialParts() const { return m_partialParts; }

  // Assigns application user data
  template <typename UserType>
  void SetUserData(const UserType& userData) { m_userData = std::make_shared<UserType>(userData); }

  // GetUserData<T>() returns a pointer to the application user data assigned
  // with setUserData<T>. Returns nullptr if no user data is assigned.
  template <typename UserType>
  UserType* GetUserData() const { return static_cast<UserType*>(m_userData.get()); }

  // Clears the user data
  void ClearUserData() { m_userData = nullptr; }

  struct DisplayProperties {
     std::string color;
     cdiINT32 materialIndex;
     CDI_DISPLAY_MODE displayMode;
  };

  // Legacy files may contains per segment display properties (RGDP) (e.g. found in 3.14 snowcone)
  const DisplayProperties* getDisplayProperties() const;

private:
  void SetParentSegmentIndex(cdiINT32 parentSegmentIndex);
  void ReadFromOldCDI(CDI_INFO cdi_info);
  
  std::string m_name;
  cCDI_PARTITION* m_partition;
  std::vector<cdiINT32> m_assignedRegionIndices;
  std::vector<cdiINT32> m_inheritedRegionIndices;
  std::vector<cCDI_SEGMENT_REF> m_segmentAssignments;
  cdiINT32 m_parentSegmentIndex = -1;
  std::vector<cCDI_SEGMENT*> m_childSegments;  // Pointers to children for convenience
  std::vector<cCDI_PARTIAL_PART*> m_partialParts;
  std::shared_ptr<void> m_userData;

  DisplayProperties m_displayProperties;
  bool m_hasDisplayProperties = false;
};

class cCDI_SEGMENT_REF
{
public:
  cCDI_SEGMENT_REF(cdiINT32 partitionIndex, cdiINT32 segmentIndex)
    : m_partitionIndex(partitionIndex), m_segmentIndex(segmentIndex) {}

#define CDI_CHUNK_TYPE_SGRF             CIO_BUILDCCCC('s', 'g', 'r', 'f')
  static CIO_CCCC GetSegmentRefChunkType() { return CDI_CHUNK_TYPE_SGRF; }
  CIO_CCCC GetChunkType() const { return GetSegmentRefChunkType(); }

  bool ReadFromCDI(CDI_INFO cdi_info);
  bool WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(int depth) const;
  void Undump();
  void Dump(std::ostream &output) const;

  const cCDI_PARTITION& GetPartition(const cCDI_PARTITIONS& partitions) const;
  // The return of GetSegment can be NULL if this reference is used to indicate an
  // excluded segment/part/face in the partition
  cCDI_SEGMENT* GetSegment(const cCDI_PARTITIONS& partitions) const;

  cdiINT32 m_partitionIndex;
  cdiINT32 m_segmentIndex;
};

class cCDI_ENTITY
{
  // An entity is a type that can be assigned/excluded from a segment.
  // It can be a segment, part, face
public:
  cdiINT32 GetIndex() const { return m_Index; }
  void AddSegmentAssignment(cCDI_SEGMENT_REF newRef) { m_segmentAssignments.push_back(newRef); }
  // If the caller wants the segment to be assigned when the type is "Same As Parent", then the parent
  // sSEGMENT_ASSIGNMENT needs to be passed in (keep NULL if the actual segment is not needed.) This
  // is not the most convenient but entities do not know about parents and the caller likely knows
  // the parent.
  sSEGMENT_ASSIGNMENT GetSegmentAssignment(const cCDI_PARTITION& derivedPartition, sSEGMENT_ASSIGNMENT* parentAssign = NULL) const;
  void RemoveSegmentAssignment(cdiINT32 partitionIndex);

  std::string GetName() const { return m_name; }
  void SetName(const std::string& eName) { m_name = eName; }
  
protected:
  // Should not create instances of this classs
  cCDI_ENTITY(cdiINT32 eIndex)
    : m_Index(eIndex) {}

private:
  cdiINT32 m_Index;
  std::vector<cCDI_SEGMENT_REF> m_segmentAssignments;
  std::string m_name;

  friend class cCDI_PARTITIONS;
};

class cCDI_PART_ENTITY : public cCDI_ENTITY
{
public:
  cCDI_PART_ENTITY(cdiINT32 eIndex)
    : cCDI_ENTITY(eIndex) {}

  void AddFace(cdiINT32 faceIndex) { m_faceList.push_back(faceIndex); }
  std::vector<cdiINT32> GetFaceList() const { return m_faceList; }

private:
  std::vector<cdiINT32> m_faceList;
};

class cCDI_FACE_ENTITY : public cCDI_ENTITY
{
public:
  cCDI_FACE_ENTITY(cdiINT32 eIndex)
    : cCDI_ENTITY(eIndex),
    m_partIndex(-1), m_surfacePropertyIndex(CDI_PHYS_TYPE_NONE) {}

  cdiINT32 GetPartIndex() const { return m_partIndex; }
  void SetPartIndex(cdiINT32 partIndex) { m_partIndex = partIndex; }

  cdiINT32 GetSurfacePropertyIndex() const { return m_surfacePropertyIndex; }
  void SetSurfacePropertyIndex(cdiINT32 surfaceIndex) { m_surfacePropertyIndex = surfaceIndex; }

private:
  cdiINT32 m_partIndex;
  cdiINT32 m_surfacePropertyIndex;
};

class cCDI_PARTIAL_PART
{
public:
  cCDI_PARTIAL_PART(cdiINT32 partIndex, cCDI_SEGMENT* parentSegment);
  ~cCDI_PARTIAL_PART();

  // Definitions and Methods for CDI support
#define CDI_CHUNK_TYPE_PLPT CIO_BUILDCCCC('p', 'l', 'p', 't')
  static CIO_CCCC GetPartialPartChunkType() { return CDI_CHUNK_TYPE_PLPT; }
  CIO_CCCC GetChunkType() const { return GetPartialPartChunkType(); }

  void ReadFromCDI(CDI_INFO cdi_info);
  void WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(std::ostream &output, int depth, int version) const;
  void Undump(int version);

  // Properties
  cCDI_SEGMENT* GetParentSegment() const { return m_parentSegment; }
  const cCDI_PARTITION& GetPartition() const { return m_parentSegment->GetPartition(); }

  cdiINT32 GetPartIndex() const { return m_partIndex; }
 
  std::list<const cCDI_SEGMENT*> GetPath() const;
  std::string GetPathName() const { return GetParentSegment()->GetName(true); }

  // Face and Segment Membership Methods
  // These are ALL the faces (assigned and inherited) in the partial part
  std::vector<cdiINT32> GetFaceIndices() const;
  // These are only the faces explicitly assigned to the partial part
  std::vector<cdiINT32> GetAssignedFaceIndices() const { return m_assignedFaceIndices; }
  void AddAssignedFaceIndex(cdiINT32 iFace) { m_assignedFaceIndices.push_back(iFace); }
  // Faces that are in this partial part due to inheritance
  std::vector<cdiINT32> GetInheritedFaceIndices() const { return m_inheritedFaceIndices; }
  void AddInheritedFaceIndex(cdiINT32 iFace) { m_inheritedFaceIndices.push_back(iFace); }

  // Segment "Assignments" are where this partial part is assigned to in a derived partition
  sSEGMENT_ASSIGNMENT GetSegmentAssignment(const cCDI_PARTITION& derivedPartition);
  void AddSegmentAssignment(const cCDI_PARTITION& assignedPartition, cCDI_SEGMENT* assignedSegment);

  // Get where and how the faces in this partial part are assigned in a child partition
  std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> GetFaceAssignments(const cCDI_PARTITION& derivedPartition);

  // Assigns application user data
  template <typename UserType> void SetUserData(const UserType& userData) {
     m_userData = std::make_shared<UserType>(userData);
  }

  // GetUserData<T>() returns a pointer to the application user data assigned
  // with setUserData<T>. Returns nullptr if no user data is assigned.
  template <typename UserType> UserType* GetUserData() const {
     return static_cast<UserType*>(m_userData.get());
  }

  // Clears the user data
  void ClearUserData() { m_userData = nullptr; }

private:
  cdiINT32 m_partIndex;
  cCDI_SEGMENT* m_parentSegment;
  std::vector<cdiINT32> m_assignedFaceIndices;
  std::vector<cdiINT32> m_inheritedFaceIndices;
  std::vector<cCDI_SEGMENT_REF> m_segmentAssignments;
  std::shared_ptr<void> m_userData;
};

class cCDI_PARTIAL_PART_REF
{
public:
  cCDI_PARTIAL_PART_REF(cdiINT32 partitionIndex, cdiINT32 partIndex)
    : m_partitionIndex(partitionIndex), m_partIndex(partIndex) {}

#define CDI_CHUNK_TYPE_PPRF             CIO_BUILDCCCC('p', 'p', 'r', 'f')
  static CIO_CCCC GetPartialPartRefChunkType() { return CDI_CHUNK_TYPE_PPRF; }
  CIO_CCCC GetChunkType() const { return GetPartialPartRefChunkType(); }

  bool ReadFromCDI(CDI_INFO cdi_info);
  bool WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(int depth) const;
  void Dump(std::ostream &output) const;
  void Undump();

  const cCDI_PARTITION& GetPartition(const cCDI_PARTITIONS& partitions) const;
  const cCDI_PARTIAL_PART& GetPartialPart(const cCDI_PARTITIONS& partitions) const;

  cdiINT32 m_partitionIndex;
  cdiINT32 m_partIndex;
};

class cCDI_PARTITION
{
  // This class represents a Partition. Upon creation, a "Root" Segment is created automatically.
  // Derived partitions should be created by the parent.
  // Segments in a partition should be created by the parent segment.
public:
  cCDI_PARTITION(std::string& name, cCDI_PARTITIONS* owner, cCDI_PARTITION* parentPartition);
  ~cCDI_PARTITION();

  // Definitions and Methods for CDI support
#define CDI_CHUNK_TYPE_PRTN CIO_BUILDCCCC('p', 'r', 't', 'n')
  static CIO_CCCC GetPartitionChunkType() { return CDI_CHUNK_TYPE_PRTN; }
  CIO_CCCC GetChunkType() const { return GetPartitionChunkType(); }
#define CDI_CHUNK_TYPE_SGDF CIO_BUILDCCCC('s', 'g', 'd', 'f')
  CIO_CCCC GetSegmentsChunkType() const { return CDI_CHUNK_TYPE_SGDF; }

  void ReadFromCDI(CDI_INFO cdi_info);
  void WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(std::ostream &output, int depth, int version) const;
  void Undump(int version);
  
  // Properties
  const std::string GetName() const { return m_name; }
  void SetName(std::string& name) { m_name = name; }

  const cCDI_PARTITIONS& GetOwner() const { return *m_owner; }
  cCDI_PARTITION* GetParentPartition() const;
  bool IsBaseAssembly() const { return m_parentPartitionIndex < 0; }

  const std::vector<cCDI_PARTITION*>& GetDerivedPartitions() const { return m_childPartitions; }

  // These methods return the name of the part/face accounting for duplicate names in a
  // segment (which can only happen in derived partitions). If "includePath" is true, then
  // the name is prepended with the partition name and path of the segment. It will return
  // false and an empty name if the part/face is not in the partition.
  bool GetQualifiedPartName(cdiINT32 partIndex, bool includePath, std::string* partName) const;
  bool GetQualifiedFaceName(cdiINT32 faceIndex, bool includePath, std::string* faceName) const;

  // Use CreateDerivedPartition to create a new child partition. The "ownership" of
  // the created partitions get passed to the cCDI_PARTITIONS
  cCDI_PARTITION& CreateDerivedPartition(std::string& name);
  // AddDerivedPartition is a method to keep track of children. It should not necessarily be
  // called; it is used when loading CDIs
  void AddDerivedPartition(cCDI_PARTITION* derivedPartition) { m_childPartitions.push_back(derivedPartition); }

  cCDI_SEGMENT& GetRoot() const;
  std::vector<cCDI_PARTIAL_PART*> GetPartialParts() const;

  // Returns a flat list of all segments in the partition
  std::vector<cCDI_SEGMENT*> GetSegments() const;

  // AddSegment transfers the ownership of a segment to the partition
  void AddSegment(std::unique_ptr<cCDI_SEGMENT> newSegment) { m_segments.push_back(std::move(newSegment)); }
  cdiINT32 GetSegmentIndex(const cCDI_SEGMENT& segment) const;
  cCDI_SEGMENT* GetSegment(cdiINT32 segmentIndex) const;
  cCDI_SEGMENT* GetSegment(const std::string& segmentPath) const;
  
  // Get where and how the segments in this partition are assigned to its children partitions
  std::vector<std::pair<cCDI_SEGMENT*, sSEGMENT_ASSIGNMENT>> GetSegmentAssignments(const cCDI_PARTITION& derivedPartition);
  // Get where and how the partial parts in this partition are assigned to its children partitions
  std::vector<std::pair<cCDI_PARTIAL_PART*, sSEGMENT_ASSIGNMENT>> GetPartialPartAssignments(const cCDI_PARTITION& derivedPartition);
  // Get where and how the parts/regions in this partition are assigned to its children partitions
  std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> GetRegionAssignments(const cCDI_PARTITION& derivedPartition);
  // Get where and how the faces in this partition are assigned to its children partitions
  std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> GetFaceAssignments(const cCDI_PARTITION& derivedPartition);
  // Helper method to build membership in the partition
  void BuildMembershipForChildren(bool addAssigned);
  // Helper method to fixup partial parts for old CDIs
  void FixupPartialParts();

  // AddPartialPart transfers the ownership of a partial part to the partition
  void AddPartialPart(std::unique_ptr<cCDI_PARTIAL_PART> newPartialPart) { m_partialParts.push_back(std::move(newPartialPart)); }
  cdiINT32 GetPartialPartIndex(const cCDI_PARTIAL_PART& partialPart) const;
  cCDI_PARTIAL_PART* GetPartialPart(cdiINT32 partialPartIndex) const;
  
  // Assigns application user data
  template <typename UserType> void SetUserData(const UserType& userData) {
     m_userData = std::make_shared<UserType>(userData);
  }

  // GetUserData<T>() returns a pointer to the application user data assigned
  // with setUserData<T>. Returns nullptr if no user data is assigned.
  template <typename UserType> UserType* GetUserData() const {
     return static_cast<UserType*>(m_userData.get());
  }

  // Clears the user data
  void ClearUserData() { m_userData = nullptr; }

  private:
  void SetParentPartitionIndex(cdiINT32 parentPartitionIndex);
  cCDI_SEGMENT& CreateSegmentForRecall();
  void ReadFromOldCDI(CDI_INFO cdi_info);

  std::string m_name;
  cCDI_PARTITIONS* m_owner;
  std::vector<std::unique_ptr<cCDI_SEGMENT>> m_segments;
  std::vector<std::unique_ptr<cCDI_PARTIAL_PART>> m_partialParts;
  cdiINT32 m_parentPartitionIndex = -1;
  std::vector<cCDI_PARTITION*> m_childPartitions;  // Pointers to children for convenience
  std::shared_ptr<void> m_userData;
};

class cCDI_PARTITIONS
{
public:
  cCDI_PARTITIONS();
  ~cCDI_PARTITIONS();

  // Definitions and methods for CDI support
#define CDI_CHUNK_TYPE_PSDF CIO_BUILDCCCC('p', 's', 'd', 'f')
  static CIO_CCCC GetPartitionsChunkType() { return CDI_CHUNK_TYPE_PRTN; }
  CIO_CCCC GetChunkType() const { return GetPartitionsChunkType(); }

  static bool CDIIsPartitionVersion(CDI_INFO cdi_info);

  void ReadFromCDI(CDI_INFO cdi_info);
  void WriteToCDI(CDI_INFO cdi_info) const;
  void Dump(CDI_INFO cdi_info, int depth) const;
  void Undump(CDI_INFO cdi_info);

  // Support for Read/Write from stream
  // The partition hierarchy and membership information is written
  // to a stream similar to the CDI format. This stream
  // can be converted to a string and written to another file, e.g. measurement.
  // -- Updating VERSION
  //      A. If the CDI format of the partitions, e.g. psdf chunk, is modified, then:
  //           1. Update the CDI version in cdi_readwrite.h
  //           2. Make appropriate change to ReadFromCDI, WriteToCDI, Dump, Undump, etc.
  //           3. Update the VERSION
  //           4. Update the map of VERSION to CDI major/minor in GetPrintVersionForCDI
  //      B. Only changing the print format that does not affect CDI, e.g. PrintPartInfo.
  //           1. Update the VERSION
  //           2. Update the appropriate Print/Read methods
  //           3. There is no need to update the CDI version
  static const int VERSION = 2;
  //   Version 2: Added physics type to the cCDI_FACE_ENTITY for Acoustics and Insight
  void PrintToStream(std::ostream &output, int depth) const;
  bool ReadFromStream(std::istream &input);

  // Helper methods for populating region and face indices for segments from the CDI
  // These must be called by clients for the "get" parts/regions and faces methods to work,
  // e.g. cCDI_SEGMENT::GetRegionIndices(bool recursive)
  //   * If the client is already processing the RGND and FACE chunks, then add the following
  //     calls during the processing
  void UpdateSegmentMembershipForRegion(CDI_RGND rgnd, cdiINT32 regionIndex);
  void UpdateSegmentMembershipForFace(cdiINT32 regionIndex, CDI_FACE face);
  // This needs to be used for pre-v3.0 cases
  void UpdateSegmentMembershipOldCases(cdiINT32 regionIndex, const std::string& regionName, CDI_FACE_OLD face);
  void FinalizeCDIRead();
  //   * If the client is not processing the RGND and FACE chunks, then use this method. This should
  //     be called when the cdi reader is processing children of "case" chunks and before the "rgdf"
  //     chunk is reached (if rgdf has been reached, cdi_rewind could be called before this)
  bool ReadRegionsAndFacesFromCDI(CDI_INFO cdi_info);

  cCDI_PARTITION& GetBaseAssembly() const;
  // AddPartition transfers the ownership of the newPartition to the cCDI_PARTITIONS
  void AddPartition(std::unique_ptr<cCDI_PARTITION> newPartition) { m_partitions.push_back(std::move(newPartition)); }

  cdiINT32 GetPartitionIndex(const cCDI_PARTITION& partition) const;
  cCDI_PARTITION* GetPartition(cdiINT32 partitionIndex) const;
  cCDI_PARTITION* GetPartition(const std::string& partitionName) const;

  std::vector<cCDI_PARTITION*> GetPartitions() const;

  cCDI_PART_ENTITY& GetPartEntity(cdiINT32 partIndex);
  const cCDI_PART_ENTITY& GetPartEntity(cdiINT32 partIndex) const;
  cCDI_FACE_ENTITY& GetFaceEntity(cdiINT32 faceIndex);
  const cCDI_FACE_ENTITY& GetFaceEntity(cdiINT32 faceIndex) const;
  std::vector<cdiINT32> GetPartFaceList(cdiINT32 partIndex) const;

  std::string GetShortPartName(cdiINT32 partIndex) const;
  std::string GetShortFaceName(cdiINT32 faceIndex, bool includePartName = false) const;
  // These return the full name with path in the base assembly. This will not prepend
  // the name with the partition, i.e. "Base Assembly/"
  std::string GetPartName(cdiINT32 partIndex) const;
  std::string GetFaceName(cdiINT32 faceIndex) const;

  cdiINT32 GetFaceEntityCount() const { return (cdiINT32)m_faceEntities.size(); }
  cdiINT32 GetPartEntityCount() const { return (cdiINT32)m_partEntities.size(); }

  bool IsHexSubRegion(cdiINT32 regionIndex) const
  { return m_hexSubRegionIndices.find(regionIndex) != m_hexSubRegionIndices.end(); }

private:
  cCDI_PARTITION& CreatePartitionForRecall();
  void DumpPSDF(std::ostream &output, int depth, int version) const;
  void UndumpPSDF(int version);
  void PrintEntityInfo(std::ostream &output, int depth, int version) const;
  void ReadEntityInfo(int version);
  int GetPrintVersionForCDI(int cdiMajorVersion, int cdiMinorVersion) const;

  void AddHexSubRegion(cdiINT32 regionIndex) { m_hexSubRegionIndices.insert(regionIndex); }

private:
  std::vector<std::unique_ptr<cCDI_PARTITION>> m_partitions;
  std::map<cdiINT32, cCDI_PART_ENTITY> m_partEntities;
  std::map<cdiINT32, cCDI_FACE_ENTITY> m_faceEntities;

  std::unordered_set<cdiINT32> m_hexSubRegionIndices;

  bool m_readingPreV8CDI;
  int m_printVersion;
};

// Container chunk for segment references
#define CDI_CHUNK_TYPE_SEGL             CIO_BUILDCCCC('s', 'e', 'g', 'l')

cdiBOOLEAN cdi_read_segment_reference_list(CDI_INFO cdi_info, std::vector<cCDI_SEGMENT_REF>& sgel);
cdiBOOLEAN cdi_write_segment_reference_list(CDI_INFO cdi_info, const std::vector<cCDI_SEGMENT_REF>& sgel);

// Container chunk for partial part references
#define CDI_CHUNK_TYPE_PPRL             CIO_BUILDCCCC('p', 'p', 'r', 'l')

cdiBOOLEAN cdi_read_partial_part_reference_list(CDI_INFO cdi_info, std::vector<cCDI_PARTIAL_PART_REF>& pprl);
cdiBOOLEAN cdi_write_partial_part_reference_list(CDI_INFO cdi_info, const std::vector<cCDI_PARTIAL_PART_REF>& pprl);

// Method for testing and debugging
void cdi_read_partitions_test(const std::string& cdiFilename);

// Helper methods to read and write the geometry selection tree to a string for measurement files
void cdi_print_gmrf(const cCDI_GEOMETRY_REF& gmrf, std::ostream &output, int depth, cdiINT32 majorVersion, cdiINT32 minorVersion);
void cdi_read_gmrf(cCDI_GEOMETRY_REF* gmrf, cdiINT32 majorVersion, cdiINT32 minorVersion);
void cdi_print_geos(const cCDI_GEOM_SELECTION_TREE& geos, std::ostream &output, int depth, bool printHeader, cdiINT32 majorVersion, cdiINT32 minorVersion);
bool cdi_read_geos(std::istream &input, cCDI_GEOM_SELECTION_TREE* geos, cdiINT32* majorVersion, cdiINT32* minorVersion);
void cdi_read_geos(cCDI_GEOM_SELECTION_TREE* geos, cdiINT32 majorVersion, cdiINT32 minorVersion);

// Method to get a cCDI_GEOMETRY_REF representation of a geometry (segment, partial part, part or face)
// from a path. The "geomRef" parameter will be updated such that its "GeometrySelectionType" property
// is set to the geometry that the path represents and the appropriate data member vector is populated.
// The geometryPath is preferably a fully, qualified path, e.g "Master/Vehicle/" (segment),
// "Base Assembly/simvol" (part), "Base Assembly/simvol::simvol" (face). However, this will accept a shorthand
// notation for geometry in the base assembly (mostly for supporting old CDIs, scripts, etc.). For example,
// a geometryPath "simvol" will return all of the parts in the base assembly (under all segments) with
// the name "simvol". For old cases, this will return a single part; however, new cases could return multiple
// parts because duplicate names are allowed as long as the parts are in different segments in the base assembly.
// For a full path, the "::" delimiter should be used between the part and face names. To support old CDIs, then
// a "short" part or face name can use either the "::" or "." delimiter, e.g. "simvol.simvol" or "simvol::simvol".
bool cdi_get_geometry_by_path(const cCDI_PARTITIONS& partitions, const std::string& geometryPath, cCDI_GEOMETRY_REF* geomRef);
// Rather than have the client enter a "partname<:: or . delimiter>facename" to get a face, add a parameter
// "expectedType" that allows the specification of the return type with a true "short" name.
// For example, "_Floor" with expectedType = cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face" will return the face with the
// name "_Floor::_Floor".
bool cdi_get_geometry_by_path(const cCDI_PARTITIONS& partitions, const std::string& geometryPath, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE expectedType, cCDI_GEOMETRY_REF* geomRef);
// TEMPORARY method to handle old paths with "." delimiter during the transition
bool cdi_get_geometry_by_old_path(const cCDI_PARTITIONS& partitions, const std::string& geometryPath, cCDI_GEOMETRY_REF* geomRef);
bool cdi_get_geometry_by_old_path(const cCDI_PARTITIONS& partitions, const std::string& geometryPath, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE expectedType, cCDI_GEOMETRY_REF* geomRef);

// Method to get a part or face by the short name. This supports the new format "partname::facename", the
// old format "partname.facename" (for old CDI files), and "partname" or "facename".
// The user must now use the "expectedType" parameter to tell whether it is a part or face.
bool cdi_get_geometry_by_shortname(const cCDI_PARTITIONS& partitions, const std::string& geometryName, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE expectedType, cCDI_GEOMETRY_REF* geomRef);

// Tests getQualified(Part|Face|Segment)Name and cdi_get_geometry_by_path for all faces, parts, and segments in all partitions.
// Returns false if the cCDI_GEOMETRY_REF cannot be found or does not match. Prints qualified names for all faces/parts/segments.
// TODO: test partial part names too
bool cdi_test_qualified_names(const cCDI_PARTITIONS&, bool abortOnFailure = false, std::ostream& os = std::cout);

#endif /* _CDI_PARTITIONS_H */
