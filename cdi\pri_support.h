/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#ifndef _PRI_SUPPORT_
#define  _PRI_SUPPORT_

#include PRI_H //For PRI struct definitions
#include "cdi_common.h"


inline size_t cdi_csys_to_pri_csys(const size_t cdi_csys_index) {
  //The lattice_csys occupies the first csys in a PRI file and CDI 
  //coordinate systems are indexed beyond that.
  return cdi_csys_index + 1; 
}

//----Functions to map CDI enumerated types to PRI enumerated types----.
PRI::DISTRIBUTION_TYPE cdi_to_pri_distribution_type(CDI_DISTRIBUTION_TYPE cdi_distribution_type);
PRI::DIRECTION cdi_to_pri_direction(const std::vector<sCDI_PARM>& cdi_direction);
PRI::PARTICLE_TYPE cdi_to_pri_material_type(CDI_PARTICLE_MATERIAL_TYPE cdi_material_type);
PRI::EMISSION_RATE_VIA cdi_to_pri_emission_rate_type(CDI_EMISSION_RATE_TYPE cdi_emission_rate_type);
PRI::NOZZLE_TYPE cdi_to_pri_nozzle_type(sCDI_NOZZLE_EMITTER_CONFIG::CDI_EMITTER_NOZZLE_TYPE cdi_nozzle_type);

//--------------------------------------------
//---------- Emitter Configurations -----------

//Function to initialize a PRI::cBASE_EMITTER_CONFIGURATION with data from any flavor of cdi emitter config.
//A template is used here for the cdi emitter config type because the emission rate data is not in the cdi's
//base class but is in the PRI's base class
template <typename sCDI_EMITTER_CONFIGURATION_TYPE>
VOID set_pri_base_emitter_config_data(PRI::cBASE_EMITTER_CONFIGURATION& pri_emitter_config,
                                      const sCDI_EMITTER_CONFIGURATION_TYPE* cdi_emitter_config)
{
  pri_emitter_config.SetName(cdi_emitter_config->name);
  pri_emitter_config.SetParticleMaterialIndex((size_t)cdi_emitter_config->material_index.value);
  pri_emitter_config.SetEmissionRate(cdi_emitter_config->emission_rate);
  pri_emitter_config.SetEmissionRateVia(cdi_to_pri_emission_rate_type((CDI_EMISSION_RATE_TYPE)(int)cdi_emitter_config->emission_rate_type.value));
}


//Function to initialize PRI::cRAIN_EMITTER_CONFIGURATION members from either a sCDI_NOZZLE_EMITTER_CONFIG
//or a sCDI_RAIN_EMITTER_CONFIG object. A sCDI_NOZZLE_EMITTER_CONFIG is supported in the template because
//PRI::cNOZZLE_EMITTER_CONFIGURATION inherets from a PRI::cRAIN_EMITTER_CONFIGURATION so to init a
//PRI nozzle config, all the values from its base class are set first using this template.
template <typename sCDI_EMITTER_CONFIGURATION_TYPE>
VOID set_pri_rain_emitter_config_data(PRI::cRAIN_EMITTER_CONFIGURATION& pri_emitter_config,
                                      const sCDI_EMITTER_CONFIGURATION_TYPE* cdi_emitter_config)
{
  //note: The following are specific to PRI's rain emitter configuration.
  //void SetParticleDiameterMean(const cCOMPOUND_FLOAT& particleDiameterMean);
  //void SetParticleDiameterRangeStdDev(const cCOMPOUND_FLOAT& particleDiameterRangeStdDev);
  //void SetParticleDiameterGammaTheta(const cCOMPOUND_FLOAT& particleDiameterGammaTheta);
  //void SetParticleDiameterRosinRammlerP80(const cCOMPOUND_FLOAT& particleDiameterRosinRammlerP80);
  //void SetParticleDiameterRosinRammlerM(const cCOMPOUND_FLOAT& particleDiameterRosinRammlerM);
  //void SetParticleDiameterDistribution(DISTRIBUTION_TYPE particleDiameterDistribution);

  CDI_DISTRIBUTION_TYPE distribution = (CDI_DISTRIBUTION_TYPE)(int)cdi_emitter_config->particle_diam_info.GetDistributionType().value;
  sCDI_PARM p1;
  sCDI_PARM p2;
  cdi_emitter_config->particle_diam_info.GetParameterValues(&p1, &p2);
  pri_emitter_config.SetParticleDiameterDistribution(cdi_to_pri_distribution_type(distribution));
  pri_emitter_config.SetParticleDiameterDistributionParameter1(p1);
  pri_emitter_config.SetParticleDiameterDistributionParameter2(p2);
  switch(distribution) {
  case DISTRIBUTION_UNIFORM:
  case DISTRIBUTION_GAUSSIAN:
  case DISTRIBUTION_GAMMA:
  case DISTRIBUTION_ROSIN_RAMMLER:
  case DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION://PR_48032
  case DISTRIBUTION_NONE:
  case DISTRIBUTION_LOG_NORMAL:
    break;
  case DISTRIBUTION_LINEAR:
  case DISTRIBUTION_1MX_POW_N:
  case DISTRIBUTION_HALF_COSINE:
  default:
    msg_error("Unsupported PDF specified for particle diameter distribution.");
  }
}

//Functions to initialize a PRI cNOZZLE_MEITTER_CONFIGURATION with data from sCDI_NOZZLE_EMITTER_CONFIG
VOID set_pri_nozzle_emitter_config_data(sCDI_INFO &cdi_info,
                                        PRI::cNOZZLE_EMITTER_CONFIGURATION& pri_emitter_config,
                                        const sCDI_NOZZLE_EMITTER_CONFIG* cdi_nozzle_config);

//Functions to initialize a PRI cTIRE_EMITTER_CONFIGURATION with data from sCDI_TIRE_EMITTER_CONFIG
VOID set_pri_tire_emitter_config_data(PRI::cTIRE_EMITTER_CONFIGURATION& pri_emitter_config,
                                      std::vector<PRI::cNOZZLE_PROPERTIES> &pri_arc_station_properties,
                                      std::vector<PRI::cANGLE_NOZZLE_PROP_MAP> &pri_nozzle_station_map,
                                      const sCDI_TIRE_EMITTER_CONFIG* cdi_nozzle_config);



//-------------------------------
//---------- Emitters -----------

//----The Following are a set of functions that populate various fields of a PRI emitter struct with----

//Function to set fields specific to a pri base-emitter from a cdi base-emitters derived types (cdi surface, volume, point, tire, and rain emitters)
//The fields in a pri base emitter have a one to one realtionship to fields in a cdi base emitter, so a template is not needed here.
VOID set_pri_base_emitter_data(PRI::cBASE_EMITTER& pri_emitter, const sCDI_PARTICLE_EMITTER_BASE& cdi_emitter);

//Function to set fields specific to a pri directed-emitter from a cdi surface, volume, or point emitter
template <typename CDI_EMITTER_TYPE>
VOID set_pri_directed_emitter_data(PRI::cDIRECTED_EMITTER& pri_emitter, const CDI_EMITTER_TYPE& cdi_emitter )
{
  pri_emitter.SetMeanEmissionDirectionCoordinateSystemIndex(cdi_csys_to_pri_csys((size_t)cdi_emitter.nozzle_orientation.spray_direction_csys.value));
  pri_emitter.SetMeanEmissionDirection(PRI::VECTOR3F((float)cdi_emitter.nozzle_orientation.mean_spray_direction[0].value,
                                                     (float)cdi_emitter.nozzle_orientation.mean_spray_direction[1].value,
                                                     (float)cdi_emitter.nozzle_orientation.mean_spray_direction[2].value));
  pri_emitter.SetSubjectToGravity(cdi_emitter.subject_to_gravity.value != 0); //this is from CDI emitter's base class but is not in PRI emitter's base calss
  pri_emitter.SetEmitterConfigurationIndex((size_t)cdi_emitter.emitter_configuration.value); //also in CDI's base but not PRI's base
  pri_emitter.SetNozzleConeLook(cdi_emitter.nozzle_visibility_settings.nozzle_cone_look);
  pri_emitter.SetNozzleBodyColor(cdi_emitter.nozzle_visibility_settings.nozzle_body_color);
  pri_emitter.SetNozzleArrowLook(cdi_emitter.nozzle_visibility_settings.nozzle_arrow_look);
  pri_emitter.SetNozzleSize(cdi_emitter.nozzle_visibility_settings.nozzle_size);
  pri_emitter.SetNozzleShow(cdi_emitter.nozzle_visibility_settings.nozzle_show.value != 0 );
}


//Function to set fields specific to a pri geometry-emitter from a cdi volume or surface emitter
template <typename CDI_EMITTER_TYPE>
VOID set_pri_geometry_emitter_data(PRI::cGEOMETRY_EMITTER& pri_emitter, const CDI_EMITTER_TYPE& cdi_emitter)
{
  pri_emitter.SetFixedReleasePoints(cdi_emitter.fixed_release_points.value != 0.0);
  pri_emitter.SetMajorEllipseDirection(PRI::VECTOR3F((float)cdi_emitter.nozzle_orientation.mean_spray_direction[0].value,
                                                     (float)cdi_emitter.nozzle_orientation.mean_spray_direction[1].value,
                                                     (float)cdi_emitter.nozzle_orientation.mean_spray_direction[2].value));
  pri_emitter.SetEllipticalNozzle(cdi_emitter.nozzle_orientation.elliptical_nozzle.value != 0.0);
}

//Function to set fields specific to a pri volume-emitter from a cdi volume emitter
VOID set_pri_volume_emitter_data(PRI::cVOLUME_EMITTER& pri_emitter, const sCDI_VOLUME_EMITTER& cdi_emitter);

//Function to set fields specific to a pri surface-emitterfrom a cdi surface emitter
VOID set_pri_surface_emitter_data(PRI::cSURFACE_EMITTER& pri_emitter, const sCDI_SURFACE_EMITTER& cdi_emitter);

//Function to set fields specific to a pri point-emitter specific from a cdi point emitter
VOID set_pri_point_emitter_data(PRI::cPOINT_EMITTER& pri_emitter, const sCDI_POINT_EMITTER& cdi_emitter);

//Function to set fields specific to a pri rain-emitter specific from a cdi rain emitter
VOID set_pri_rain_emitter_data(PRI::cRAIN_EMITTER& pri_emitter, const sCDI_RAIN_EMITTER& cdi_emitter);

//Function to set fields specific to a pri tire-emitter specific from a cdi tire emitter
VOID set_pri_tire_emitter_data(PRI::cTIRE_EMITTER& pri_emitter, const sCDI_TIRE_EMITTER& cdi_emitter);

#endif
