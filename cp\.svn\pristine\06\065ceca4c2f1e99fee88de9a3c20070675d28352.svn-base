/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Common definitions for the license process
 *
 *--------------------------------------------------------------------------*/

#include <errno.h>

#include "license_aux.h"

size_t write_all(int fd, void *_buf, size_t size)
{						 
  char *buf = (char *)_buf;
  ssize_t nleft = size;				       
  
  while (1) {
    ssize_t nbytes = write(fd, buf, nleft);
    if (nbytes >= 0) {
      buf += nbytes;				       
      nleft -= nbytes;
      if (nleft <= 0)
	break;
    } else { // nbytes < 0
      if (errno != EINTR)
	return 0; // failure
    }
  }
  return size;
}

	
size_t read_all(int fd, void *_buf, size_t size)
{						       
  char *buf = (char *)_buf;
  ssize_t nleft = size;

  while (1) {
    ssize_t nbytes = read(fd, buf, nleft);
    if (nbytes > 0) {
      buf += nbytes;
      nleft -= nbytes;
      if (nleft <= 0)
	break;
    } else if (nbytes == 0) { // EOF
      return 0;
    } else { // nbytes < 0
      if (errno != EINTR)
	return 0; // failure
    }
  }
  return size;
}

	

