/* ~~~CO<PERSON>Y<PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#ifndef _UBLK_OCTREE_H
#define _UBLK_OCTREE_H

#include "common.h"
#include "cp_info.h"

inline asINT32 scale_to_sri_scale(asINT32 scale) { return sim_num_scales() - 1 - scale; }
inline asINT32 scale_to_sim_scale(asINT32 scale) { return scale_to_sri_scale(scale); }
#define sim_scale_to_voxel_size(S)	(1 << ((sim_num_scales() - 1) - (S)))
#define sim_scale_to_ublk_size(S)	(2 * sim_scale_to_voxel_size(S))
#define voxel_to_num(_x,_y,_z) (((_x) << 2) + ((_y) << 1) + (_z))

struct sOCTREE_NODE; // forward declaration so type can be used in sOCTREE_NODE_CHILD

typedef struct sCP_DYNBLK {
  STP_UBLK_ID id;
  STP_SCALE scale;
  uINT8     empty_voxel_mask;
  uINT8     wasted;
  STP_COORD location[3];

  bool add_dynblk(cDGF_UBLK_BASE &ublk_base) 
  {
    if (((ublk_base.ublk_flags & DGF_UBLK_VR_INTERFACE_MASK) == DGF_UBLK_VR_FINE) ||
        (ublk_base.empty_voxel_mask == 0xff) || 
        (ublk_base.ublk_type != DGF_UBLK_SIMPLE && ublk_base.ublk_type != DGF_UBLK_REAL))
      return false;

    id = ublk_base.ublk_id;
    scale = ublk_base.voxel_scale;
    empty_voxel_mask = ublk_base.empty_voxel_mask;
    location[0] = ublk_base.location[0];
    location[1] = ublk_base.location[1];
    location[2] = ublk_base.location[2];
    return true;
  }

} *CP_DYNBLK;

typedef struct sCP_SPLIT_UBLK  {
  std::vector<CP_DYNBLK> m_ublks;

} *CP_SPLIT_UBLK;

union OCTREE_NODE_CHILD {
private:
  sCP_DYNBLK       *m_ublk;
  sCP_SPLIT_UBLK   *m_split_ublk;
  sOCTREE_NODE     *m_node;

public:
  BOOLEAN is_ublk()    { return scalar_mask_pointer_to_int(m_ublk, 1); }
  CP_DYNBLK  ublk()       { return (CP_DYNBLK)scalar_mask_pointer(m_ublk, ~3); }
  OCTREE_NODE_CHILD(CP_DYNBLK ublk) { m_ublk = (CP_DYNBLK)scalar_or_pointer(ublk, 1); }

  BOOLEAN is_node()    { return !is_null(); }
  sOCTREE_NODE *node() { return (sOCTREE_NODE *)scalar_mask_pointer(m_node, ~3); }
  OCTREE_NODE_CHILD(sOCTREE_NODE *node) { m_node = node; }

  BOOLEAN is_split_ublk()     { return scalar_mask_pointer_to_int(m_split_ublk, 2); }
  CP_SPLIT_UBLK  split_ublk() { return (CP_SPLIT_UBLK)scalar_mask_pointer(m_split_ublk, ~3); }
  OCTREE_NODE_CHILD(CP_SPLIT_UBLK ublk) { m_split_ublk = (CP_SPLIT_UBLK)scalar_or_pointer(ublk, 2); }

  BOOLEAN is_null()    { return ((CP_DYNBLK)scalar_mask_pointer(m_ublk, ~3)) == NULL; }
  OCTREE_NODE_CHILD()  { m_ublk = NULL; }
};

typedef struct sOCTREE_NODE {
  OCTREE_NODE_CHILD m_children[8];

  sOCTREE_NODE() { } // constructor - sets m_children to NULL
} *OCTREE_NODE;

// OCTREE is an octree with a build process that is convenient if all an SP's ublks are to
// be entered in the octree. Usage:
//
// 1. Call init(min, max, coarsest_voxel_scale, n_ublks_in_octree) to initialize the octree.
// 2. Enter all ublks into the octree via enter_ublk().
// 3. Call find() to locate the voxel that contains a particular 3D point.
// 4. Delete the octree if desired.
typedef class sOCTREE {
protected:
  STP_COORD     m_min[3];
  STP_COORD     m_max[3];
  STP_UBLK_ID   m_n_ublks_per_dim[3];
  STP_SCALE     m_coarsest_scale;
  STP_SCALE     m_sri_coarsest_ublk_scale;

  // octree nodes are allocated from an EARRAY so that the octree can be easily "deleted"
  tEARRAY< sOCTREE_NODE, 
           8 /* initial page table size */, 
           64 /* page size */ > octree_nodes;

  OCTREE_NODE_CHILD *top_level_grid; // 3D array

  asINT32 compute_top_level_index(STP_COORD x, STP_COORD y, STP_COORD z)
  {
    STP_COORD zz = (z - m_min[2]) >> m_sri_coarsest_ublk_scale;
    STP_COORD yy = (y - m_min[1]) >> m_sri_coarsest_ublk_scale;
    STP_COORD xx = (x - m_min[0]) >> m_sri_coarsest_ublk_scale;
    return (zz
            + (m_n_ublks_per_dim[2] * (yy
                                       + (m_n_ublks_per_dim[1] * xx))));
  }

  VOID build_top_level_grid(STP_UBLK_ID n_ublks_in_octree)
  {
    // Adjust m_min and m_max to coarsest ublk boundaries
    asINT32 coarsest_ublk_scale = coarsen_scale(m_coarsest_scale); // convert from voxel to ublk scale
    m_sri_coarsest_ublk_scale = scale_to_sri_scale(coarsest_ublk_scale);

    STP_UBLK_ID n_top_level;
    while(1) {
      STP_COORD coarsest_ublk_size = 1 << m_sri_coarsest_ublk_scale; /////scale_to_voxel_size(coarsest_ublk_scale);
      STP_COORD mask = ~(coarsest_ublk_size - 1); // mask used to achieve coarsest ublk alignment
      
      n_top_level = 1;
      ccDOTIMES(i, 3) {
        m_min[i] = m_min[i] & mask;
        m_max[i] = (m_max[i] + (coarsest_ublk_size - 1)) & mask;
        m_n_ublks_per_dim[i] = (m_max[i] - m_min[i]) >> m_sri_coarsest_ublk_scale;
        n_top_level *= m_n_ublks_per_dim[i];
      }

      // Ensure that the top-level grid does not have an excessive number of entries. If the top level  
      // grid is too sparse, coarsen it one level. The factor of 2 here is just a reasonable threshold.
      if (n_top_level <= 2 * n_ublks_in_octree)  //changed < to <= to account for the case when n_ublks_in_octree = 0 (wanderer 9/27/12)
        break;
      m_sri_coarsest_ublk_scale++; // one level coarser
    }

    top_level_grid = new OCTREE_NODE_CHILD[n_top_level]; // all entries set to NULL
  }

public:
  VOID init(STP_COORD min[3], STP_COORD max[3], asINT32 coarsest_voxel_scale, STP_UBLK_ID n_ublks_in_octree)
  {
    ccDOTIMES(i, 3) {
      m_min[i] = min[i];
      m_max[i] = max[i];
    }
    m_coarsest_scale = coarsest_voxel_scale;

    build_top_level_grid(n_ublks_in_octree);
  }

  VOID clear()
  {
    if (top_level_grid) {
      delete [] top_level_grid;
      top_level_grid = NULL;
    }
  }


  ~sOCTREE() { clear(); }

  VOID enter_ublk(CP_DYNBLK ublk)
  {
    asINT32 ublk_scale = 1 + scale_to_sri_scale(ublk->scale); // convert from voxel to ublk scale

    STP_COORD *ublk_location = ublk->location;
    asINT32 top_level_index = compute_top_level_index(ublk_location[0], ublk_location[1], ublk_location[2]);
                         
    if (m_sri_coarsest_ublk_scale == ublk_scale) {
      // This octree currently ignores all voxels cut by surfels - see discussion in find() method.
      // if (!top_level_grid[top_level_index].is_null())
      //   msg_internal_error("Have not yet added support for split ublks to octree");
      top_level_grid[top_level_index] = OCTREE_NODE_CHILD(ublk);
    } else {
      // adjust coords to be relative to the lower-left-rear of the top-level cube
      STP_COORD mask = (1 << m_sri_coarsest_ublk_scale) - 1;
      STP_COORD x = ublk_location[0] & mask;
      STP_COORD y = ublk_location[1] & mask;
      STP_COORD z = ublk_location[2] & mask;

      OCTREE_NODE_CHILD child = top_level_grid[top_level_index];
      if (child.is_null())
        child = top_level_grid[top_level_index] = OCTREE_NODE_CHILD( octree_nodes.append() );

      asINT32 scale = m_sri_coarsest_ublk_scale - 1; // one level finer
      while (1) {
        auINT32 scale_mask = 1 << scale;
        asINT32 child_index = ((  ((x & scale_mask) >> scale) << 2)
                               | (((y & scale_mask) >> scale) << 1)
                               | (((z & scale_mask) >> scale) << 0));
        if (ublk_scale == scale) {
          // This octree currently ignores all voxels cut by surfels - see discussion in find() method.
          if (child.node()->m_children[child_index].is_split_ublk()) { // split ublk
            CP_SPLIT_UBLK split_ublk = child.node()->m_children[child_index].split_ublk();
            split_ublk->m_ublks.push_back(ublk);
          } else if (!child.node()->m_children[child_index].is_null()) { // ublk --> split ublk
            CP_SPLIT_UBLK split_ublk = new sCP_SPLIT_UBLK;

            if (!child.node()->m_children[child_index].is_ublk())
              msg_internal_error("Did not find a ublk while inserting split ublk %d at [%d %d %d]",
                                 ublk->id, ublk->location[0], ublk->location[1], ublk->location[2]);

            split_ublk->m_ublks.push_back(child.node()->m_children[child_index].ublk());
            split_ublk->m_ublks.push_back(ublk);
            child.node()->m_children[child_index] = OCTREE_NODE_CHILD(split_ublk);
          } else {
            child.node()->m_children[child_index] = OCTREE_NODE_CHILD(ublk);
          }
          break;
        }
        OCTREE_NODE_CHILD grandchild = child.node()->m_children[child_index];
        if (grandchild.is_null())
          child = child.node()->m_children[child_index] = OCTREE_NODE_CHILD( octree_nodes.append() );
        else
          child = grandchild;
        scale--;  // one level finer
      }
    }
  }

  // This method can be called with x, y, and z as floats. The method args are declared as integers
  // because we immediately want to convert to integral finest voxel coordinates. This does
  // not compromise our ability to find the voxel that contains an arbitrary point.
  //
  // If a voxel is cut by surfels, we can't easily determine whether the point (x,y,z) lies above
  // the surface. Thus this routine does not consider such voxels (i.e. it won't return a voxel cut
  // by surfels). This problem becomes even more pronounced in the presence of split voxels. In the
  // future, one way around this limitation is to shoot a ray from (x,y,z) and find the first
  // surfel it crosses. If the surfel is backward-facing, the point is under the surface. For this
  // to work properly, we must consider not just surfels, but the complete polyhedron that defines
  // the voxel. This scheme extends naturally to split voxels, where we recognize that a given
  // surfel bounds one and only one voxel instance within a cube.
  CP_DYNBLK find(STP_COORD x, STP_COORD y, STP_COORD z, sINT8 &voxel_return) 
  {
    if (x < m_min[0] || y < m_min[1] || z < m_min[2]
        || x >= m_max[0] || y >= m_max[1] || z >= m_max[2]) // must be >= (not >) - see comment above
      return NULL;

    STP_UBLK_ID top_level_index = compute_top_level_index(x, y, z);

    // adjust coords to be relative to the lower-left-rear of the top-level cube
    STP_COORD mask = (1 << m_sri_coarsest_ublk_scale) - 1;
    x &= mask;
    y &= mask;
    z &= mask;
    
    OCTREE_NODE_CHILD child = top_level_grid[top_level_index];

    asINT32 scale = m_sri_coarsest_ublk_scale - 1;
    while (!child.is_null()) {
      auINT32 scale_mask = 1 << scale;
      if (child.is_split_ublk()) {
        // return the first ublk which has a voxel in this location
        // else return the first instance. Ublks in this slot appear 
        // in id order.
        voxel_return = voxel_to_num((x & scale_mask) >> scale,
                                    (y & scale_mask) >> scale,
                                    (z & scale_mask) >> scale);
        CP_SPLIT_UBLK split_ublk = child.split_ublk();
        ccDOTIMES(iublk, split_ublk->m_ublks.size()) {
          CP_DYNBLK cp_ublk = split_ublk->m_ublks[iublk];
          if ((cp_ublk->empty_voxel_mask & (1 << voxel_return)) == 0)
            return cp_ublk;
        }
        return split_ublk->m_ublks[0]; // if no ublk matches, return the first one.
      } else if (child.is_ublk()) {
        voxel_return = voxel_to_num((x & scale_mask) >> scale, 
                                    (y & scale_mask) >> scale, 
                                    (z & scale_mask) >> scale);
        CP_DYNBLK ublk = child.ublk();
     
        return ublk;
      } else {
        asINT32 child_index = ((  ((x & scale_mask) >> scale) << 2)
                               | (((y & scale_mask) >> scale) << 1)
                               | (((z & scale_mask) >> scale) << 0));
        scale--;
        OCTREE_NODE node = child.node();
        child = node->m_children[child_index];
      } 
    }
    
    return NULL;
  }
} *OCTREE;  

// SUB_OCTREE is an octree with a build process designed for the situation where the octree
// spans a subset of the ublks owned by an SP. Other than the build process, it is identical
// to OCTREE. Usage:
//
// 1. Call init() to initialize the octree.
// 2. Register all ublks included in the octree via register_ublk().
// 3. Call build_octree() to actually build the octree.
// 4. Call find() to locate the voxel that contains a particular 3D point.
// 5. Delete the octree if desired.
typedef class sSUB_OCTREE : public sOCTREE {
private:
  std::vector<CP_DYNBLK> dynblks;

public:
  VOID init() {
    ccDOTIMES(i, 3) {
      m_min[i] = cp_info.simvol_size[i];
    }
    vzero(m_max);
    m_coarsest_scale = FINEST_SCALE;

    vzero(m_n_ublks_per_dim);
    m_sri_coarsest_ublk_scale = 0;
    top_level_grid = NULL;
  }

  ~sSUB_OCTREE() { /* The destructors of the base class and child classes suffice */ }

  VOID register_ublk(CP_DYNBLK ublk) {
    dynblks.push_back(ublk);

    if (is_scale_coarser(ublk->scale, m_coarsest_scale))
      m_coarsest_scale = ublk->scale;

    STP_COORD *ublk_location = ublk->location;
    ccDOTIMES(i, 3) {
      if (ublk_location[i] < m_min[i]) {
        m_min[i] = ublk_location[i];
      }
      STP_COORD max = ublk_location[i] + sim_scale_to_ublk_size(ublk->scale);
      if (max > m_max[i])
        m_max[i] = max;
    }
  }

  VOID build_octree() 
  {
    build_top_level_grid(dynblks.size());
    ccDOTIMES(j, dynblks.size()) {
      CP_DYNBLK ublk = dynblks[j];
      enter_ublk(ublk);
    }

    // free storage associated with dynblks vector
    dynblks.resize(0);
    dynblks.shrink_to_fit();
    
  }

} *SUB_OCTREE;


#endif
