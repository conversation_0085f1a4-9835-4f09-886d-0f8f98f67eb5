%option never-interactive nounput
%p 3000

 #ifdef FLEX_SCANNER
 char **cdi_yytext_ptr_ptr = &yytext;
 #else
 char *cdi_yystuff = yytext;
 char **cdi_yytext_ptr_ptr = &cdi_yystuff;
 #endif
				/* This is a bit of slight of hand to allow us
				to refer to yytext whether this is a lex or a
				flex scanner.  lex has the absolute rule that
				the latest token lives in yytext.  Moreover,
				yytext is defined as an array (hence, &yytext
				is the same as yytext).   Flex does not have a
				static definition of yytext.  So, the above
				assignment provides a reliable externally
				referenceable method of getting us to the right
				sort of pointer, whatever scanner is in use. */

%%

[a-zA-Z_]+ {
  return(LEXER_TOKEN_IS_ID);
}

[+-]?[0-9]*(\.[0-9]*)?([eE][+-]?[0-9]+)? {
  /* Num */
  return(LEXER_TOKEN_IS_NUMBER);
}

"{" {
  /* lbrace */
  return(LEXER_TOKEN_IS_LBRACE);
}

"}" {
  /* rbrace */
  return(LEXER_TOKEN_IS_RBRACE);
}

[ \t\n]+ {
  /* whitespace */
  update_lineno(yytext);
}

\([^)]*\) {
  /* comment */
  update_lineno(yytext);
}

\"([^"]*(\\.)*)*\" {
  /* string */
  update_lineno(yytext);
  return(LEXER_TOKEN_IS_STRING);
}

. {
  /* badchar */
  return(LEXER_TOKEN_IS_BADCHAR);
}
