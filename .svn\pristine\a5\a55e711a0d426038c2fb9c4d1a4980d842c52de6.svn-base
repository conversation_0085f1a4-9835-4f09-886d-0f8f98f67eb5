/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#ifndef CDI_COMMON_H
#define CDI_COMMON_H
 
/*
 * The following line adds all the definitions of things like SCALAR_H
 */

#include <stdio.h>
#include <assert.h>
#include <string.h>
#include <math.h>
#include <ctype.h>

/* cdi_export.h should not depend on any Exa libraries since it is shipped
 * outside Exa
 */
#include "cdi_export.h"

#include SCALAR_H
#include LOOP_H
#include MSGERR_H
#include MALLOC_H
#include CIO_H
#include UNITS_H
#include AUDIT_H

class cGEOM_COMMON_ENTITY;
class cGEOM_COMMON_ENTITY_LIST;

#endif /* CDI_COMMON_H */

