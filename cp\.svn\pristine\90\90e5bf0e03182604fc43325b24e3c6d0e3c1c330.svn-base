/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
//----------------------------------------------------------------------------
// David Hall, Exa Corporation                       Created Mon, Dec 10, 2008
//----------------------------------------------------------------------------

#ifndef CP_DGF_READER_H
#define CP_DGF_READER_H
#include "common.h"
#include "window.h"
#include "cp_info.h"

// #include RADIO_H

typedef struct sSURFEL_COUPLING_INFO {
  STP_SURFEL_ID id;
  STP_SURFEL_ID clone_id;
  STP_SURFEL_ID inverted_id;
  STP_SURFEL_ID inverted_clone_id;
  sFLOAT area;
  STP_SCALE scale;
#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
  asINT32 ref_frame_index;
  asINT32 face_index;
#endif
} *SURFEL_COUPLING_INFO;

typedef struct sSURFEL_TO_FACET_INFO {
  COMPONENT m_face_component;
  sSURFEL_COUPLING_INFO m_coupling_info;
  std::vector<cDGF_SURFEL_VERTEX_INDEX> m_vertices;
  
  sSURFEL_TO_FACET_INFO() {};
  sSURFEL_TO_FACET_INFO (COMPONENT &face_component, const cDGF_SURFEL_DESC &surfel_desc) :
    m_face_component(face_component) {
    //
    m_coupling_info = {surfel_desc.s.surfel_id,
                       static_cast<STP_SURFEL_ID>(surfel_desc.s.clone_index),
                       static_cast<STP_SURFEL_ID>(surfel_desc.s.opposite_index),
                       static_cast<STP_SURFEL_ID>(-1), //opposite index initialized with default -1
                       static_cast<sFLOAT>(surfel_desc.s.area),
                       static_cast<STP_SCALE>(sim_scale_to_sri_scale(surfel_desc.s.surfel_scale))
  #if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
                       ,surfel_desc.s.lrf_index,
                       surfel_desc.s.face_index
  #endif
    };
    //
    m_vertices = surfel_desc.vertices;
  }
} *SURFEL_TO_FACET_INFO;

typedef struct sCP_DGF_READER
{
  public:
    sCP_DGF_READER() {
      m_beginning_of_file = -1;
      m_main_istream = NULL;
      m_ublk_table_istream = NULL;
      m_decomp_istream = NULL;
      m_ckpt_istream = NULL;
      m_version_number = (auINT32) -1; //cast to avoid compiler warning
    }

    enum reader_t
    {
      LEGACY,
      HDF5_SERIAL,
      DEBUG_MODE,
      UNKNOWN
    };

    BOOLEAN is_lgi_file_decomposed() { return m_table_of_contents.decomp_control_pos != -1; }

    asINT32 decomp_control_record_num_sps() { return m_decomp_control_rec.num_sps; }

    VOID open_inline_decomposer_stream();
    VOID open_file_streams (bool);
    VOID close_file_streams(bool);

    template <reader_t RT> VOID read_ckpt_audit_trail_record();
    template <reader_t RT> asINT32 read_ckpt_info_record(); // returns number of SPs in ckpt file
    template <reader_t RT> VOID read_ckpt_parentage_record();
    template <reader_t RT> VOID read_ckpt_random_seed_record(STP_PROC total_ckpt_sps);
    
    VOID read_ckpt_surfel_dyn_data_header();
    VOID read_ckpt_ublk_dyn_data_header();

    VOID read_audit_trail_record();
    VOID read_decomp_audit_trail_record();
    VOID read_parentage_record();
    VOID read_dgf_part_protection_subrec();
    VOID read_decomp_parentage_record();
    VOID read_lattice_type_record();
    VOID read_global_part_names_record();
    VOID read_global_face_names_record();
    VOID read_control_record();
    VOID send_control_record();
    VOID read_neighbor_mask_table();
    VOID read_table_of_contents_record(bool);
    VOID read_vertex_coordinates();
    VOID read_surfel_normals(REALM realm);
    template <reader_t RT, typename UBLK_PROC, typename UBLK_PROC_GHOST> VOID read_ublk_descriptors(REALM realm);
    template <reader_t RT> VOID read_surfel_descriptors(REALM realm);
    VOID read_gap_contact_table_descriptor();
    VOID read_averaged_contact_ckpt_data();
    VOID read_bsurfel_descriptors(bool is_restored_on_same_sps, 
                                  std::unordered_map<STP_MEAS_WINDOW_INDEX,std::vector<STP_PROC> >& moving_meas_cell_ckpt_sp);
    VOID read_mlrf_ring_sets(asINT32 n_ring_sets, STP_REALM realm);
    VOID read_lrf_containment();
    VOID read_meas_windows_header();
    VOID read_meas_windows_data();
    VOID send_meas_vars_to_sps();
    VOID send_meas_window_master_sps_and_output_times();
    VOID send_meas_cell_reference_frame_conflicts();
    template <reader_t RT> VOID read_cdi_meas_window_ckpt_data();
    template <reader_t RT>
    VOID read_meas_window_ckpt_data(asINT32 total_ckpt_sps,
                                    std::unordered_map<STP_MEAS_WINDOW_INDEX,std::vector<STP_PROC> >& moving_meas_cell_ckpt_sp);
    template <reader_t RT> VOID read_coupling_model_pt_pf_ratio_history();
    template <reader_t RT> VOID read_monitors_ckpt_data();
    template <reader_t RT> VOID read_global_nirf_ckpt_data();
    template <reader_t RT> VOID read_lrf_ckpt_data();
    VOID read_rotdyn_ckpt_data();
    template <reader_t RT> VOID read_cp_rotdyn_ckpt_data();
    template <reader_t RT> VOID read_tbs_desc_ckpt_data();
    template <reader_t RT> VOID read_turb_synth_info_ckpt_data();
    template <reader_t RT> VOID read_movb_ckpt_data();
    VOID read_radiation_tm_ckpt_data();
    template <reader_t RT> VOID read_thermal_accel_ckpt_data();
    template <reader_t RT> VOID read_fan_ckpt_data(asINT32 total_ckpt_sps);
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    template <reader_t RT> VOID read_random_particle_property_state(asINT32 total_sps_in_ckpt);
    template <reader_t RT> VOID read_particle_emitter_ckpt(asINT32 total_sps_in_ckpt);
    template <reader_t RT> VOID read_trajectory_id_map_ckpt(asINT32 total_ckpt_sps); 
//#endif
    VOID read_decomp_control_record();

    VOID rewind_to_beginning_of_file()   { 
      lgi_setpos(m_main_istream, &m_beginning_of_file); 
    }

    VOID copy_version_number_to_decomp_stream() {
      lgi_stream_set_version(m_decomp_istream, m_version_number);
    }

#if SURF_COUP
    SURFEL_COUPLING_INFO get_surfel_coupling_info(asINT32 facet_index) {
      return &(m_facet_index_surfel_coupling_info[facet_index]);
    };
    VOID free_surfel_coupling_info() {
      m_facet_index_surfel_coupling_info.resize(0);
      m_facet_index_surfel_coupling_info.shrink_to_fit();

    }
#endif // SURF_COUP
    
    STP_PROC rad_file_num_rps() const {
      return m_rad_file.file_params().num_rps();
    }

    RadIO::sPATCH_DECOMP read_patch_decomposition();

    RadIO::sPATCH_DECOMP allocate_patch_decomposition();

  protected:
    static VOID copy_record_to_sp(LGI_STREAM istream, LGI_TAG_ID tag_id, STP_PROC sp);
    static VOID copy_record_body_to_sp(LGI_STREAM istream, LGI_TAG &tag, STP_PROC sp);
    static VOID copy_record_to_all_sps(LGI_STREAM istream, LGI_TAG_ID tag_id);
    static VOID copy_record_body_to_all_sps(LGI_STREAM istream, LGI_TAG &tag);

    VOID copy_record_to_sp_range(LGI_STREAM istream, LGI_TAG_ID tag_id, Loop::cRANGE sp_range);
    VOID copy_record_body_to_sp_range(LGI_STREAM istream, LGI_TAG &tag, Loop::cRANGE sp_range);

    template <reader_t RT, typename UBLK_PROC_GHOST>
    VOID forward_ublk_full_ckpt_data(asINT32 home_sp, BOOLEAN is_vr_fine,
                                     std::vector<UBLK_PROC_GHOST>& ublk_proc_ghosts);
    template <reader_t RT, typename UBLK_PROC_GHOST>
    asINT32 process_dgf_simple_ublk_desc(REALM realm, cDGF_SIMPLE_UBLK_DESC *ublk_desc, STP_PROC ublk_home_sp,
                                         std::vector<UBLK_PROC_GHOST>& ublk_proc_ghosts);
    template <reader_t RT, typename UBLK_PROC_GHOST>
    asINT32 process_dgf_real_ublk_desc(REALM realm, cDGF_REAL_UBLK_DESC *ublk_desc, STP_PROC ublk_home_sp,
                                       std::vector<UBLK_PROC_GHOST>& ublk_proc_ghosts,
                                       asINT32 &n_nw_fluid_like_voxels);
       
    VOID seek_lgi_tag(LGI_TAG_ID tag_id);
    VOID jump_to_file_position(sLGI_POS& position);
    VOID seek_ckpt_tag(LGI_TAG_ID tag_id);
    VOID jump_to_ckpt_file_position(sLGI_POS& position);

    // TODO: these methods probably do not belong in a reader class. Move 'em up.
    VOID report_header_errors(BOOLEAN header_is_complete);
    VOID read_file_header();
    VOID create_surfel_mesh();
#if SURF_COUP
    // need to pick up the unscaled surface coupling measurement surfel areas
    // from the LGI file instead of computing from BREP (to avoid precision
    // problems - PR 10811)
    VOID get_surface_coupling_info_from_surfel(cDGF_SURFEL_DESC &surfel_desc,
                                               std::map<DGF_SURFEL_INDEX, sSURFEL_TO_FACET_INFO> &surfel_to_facet_map);
    VOID add_facet_and_coupling_info(sSURFEL_TO_FACET_INFO &surfel_info, BOOLEAN include_back);
    std::vector <sSURFEL_COUPLING_INFO> m_facet_index_surfel_coupling_info;
#endif // SURF_COUP

    sLGI_POS         m_beginning_of_file;
    cDGF_TOC         m_table_of_contents;
    cDGF_CKPT_TOC    m_ckpt_table_of_contents;
    LGI_STREAM       m_main_istream;       // data stream from file
    LGI_STREAM       m_ublk_table_istream; // second stream for type-specific ublk data
    LGI_STREAM       m_decomp_istream;     // third stream for decomposition data
    LGI_STREAM       m_ckpt_istream;       // fourth stream for full ckpt data
    auINT32          m_version_number;     // version number from the header

    cDGF_DECOMPOSITION_CONTROL m_decomp_control_rec;

    //Map filled temporary when parsing surfels, so can be used when parsing the surfel contact table
    typedef struct sSURFEL_CONTACT_INFO {
      //SP associated to the surfel can be retrieved from cp_info.surfel_procs. However, it is more convenient to 
      //store it locally here also to avoid the search within cp_info.surfel_procs when reading the contact table. 
      //In this way, only one search in m_contact_info_map is required, needed anyways to retrieve the contact info.
      //Moreover, this map is cleared after reading the contact table, so the extra storage is just temporary.
      STP_PROC proc_id;
      STP_SCALE scale;
      sFLOAT total_contact_area;
      uINT8 even_odd;
      //Under the current framework, contact is defined at the geometry level, i.e. we partition the domain in 
      //different regions, each enclosed by a shell, and contact is defined between regions. Since the regions
      //cannot overlap, a surfel cannot participate in more than one contact set. Moreover, they can only belong
      //to the primary or secondary subset, not to both.
      sINT32 primary_set_idx, primary_first_item_idx;
      sINT32 primary_num_items;
      std::vector<cDGF_GAP_CONTACT_AREA_ITEM> contact_area_items;
      sSURFEL_CONTACT_INFO() {}
      sSURFEL_CONTACT_INFO(STP_PROC id, 
                           STP_SCALE surfel_scale, 
                           uINT32 surfel_flags) 
          : proc_id(id), scale(surfel_scale) {
        primary_set_idx = -1;
        primary_first_item_idx = -1;
        primary_num_items = 0;
        total_contact_area = 0.0;
        even_odd = (surfel_flags & STP_PROCESS_ON_ALL_TIMES);
        contact_area_items.clear();
      }
    }* SURFEL_CONTACT_INFO;
    std::unordered_map<STP_SURFEL_ID,sSURFEL_CONTACT_INFO> m_contact_info_map;

    inline BOOLEAN is_reversed_contact_order(SURFEL_CONTACT_INFO primary_info, SURFEL_CONTACT_INFO secondary_info) {
      //Contact needs to be reversed when the secondary will be processed before the primary, happening if:
      //- secondary is in a finer scale (higher value) than primary
      //
      //Note that for surfels in the fringe, primaries and secondaries in the same scale can fall in different groups
      //if they have different dest_proc (the highest of all destination procs it interact with is assigned, and
      //primary and secondary can interact with different procs). However, each SP uses its local neighbor sp index
      //(cNEIGHBOR_SP::m_nsp), not the rank, so it falls on the SP to do the reordering since we cannot determine in
      //CP whether we need to reverse the order based on the same realm destination proc 
      return (secondary_info->scale > primary_info->scale);
    }

    RadIO::cRAD_FILE m_rad_file; 

}* CP_DGF_READER;

VOID write_meas_window_vars_to_sps(CP_MEAS_WINDOW meas_window, BOOLEAN flow_sps_only = FALSE);
#endif// CP_DGF_READER_H


