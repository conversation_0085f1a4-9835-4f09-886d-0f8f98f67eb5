/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2007 Exa Corporation.                                   ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#ifndef _CDI_INTERNAL_H
#define _CDI_INTERNAL_H

#include "cdi_readwrite.h"


// Internal read/write/destroy methods which are no longer exposed in
// CDI's public API, but which are preserved for internal use (e.g.,
// by dump/undump)


// The reading and writing of the following measurement chunks has
// been superceded by the cdi_read_mesr/cdi_write_mesr
void cdi_write_mprm(CDI_INFO cdi_info,CDI_MPRM);   // MPRM
CDI_MPRM cdi_read_mprm(CDI_INFO cdi_info);
void cdi_destroy_mprm(CDI_MPRM);
void cdi_write_mstp(CDI_INFO cdi_info,CDI_MSTP);   // MSTP
CDI_MSTP cdi_read_mstp(CDI_INFO cdi_info);
void cdi_destroy_mstp(CDI_MSTP);
void cdi_write_mflt(CDI_INFO cdi_info,CDI_MFLT);   // MFLT
CDI_MFLT cdi_read_mflt(CDI_INFO cdi_info);
void cdi_destroy_mflt(CDI_MFLT);
void cdi_write_mref(CDI_INFO cdi_info,CDI_MREF);   // MREF
CDI_MREF cdi_read_mref(CDI_INFO cdi_info);
void cdi_destroy_mref(CDI_MREF);
void cdi_write_mfac(CDI_INFO cdi_info,CDI_MFAC);   // MFAC
CDI_MFAC cdi_read_mfac(CDI_INFO cdi_info);
void cdi_destroy_mfac(CDI_MFAC);

#endif /* !_CDI_READWRITE_H */
