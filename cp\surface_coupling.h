/* ~~~CO<PERSON>Y<PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#ifndef __CP_SURFACE_COUPLING_H
#define __CP_SURFACE_COUPLING_H
#include TDFIO_H
#include MIO_H
#include MIO_BREP_INSTANCES_H
#include BREP_H
#include INTERP_H
#include TEARRAY_H
#include BG_H
#include FGEOM_H
#include TPI_H
#include TPI_INSTANCES_H

#define MAX_COMPONENT_NAME 4096

typedef cABSTRACT_SURFACE <cMIO_MODELf> sSIM_ABSTRACT_SURFACE, *SIM_ABSTRACT_SURFACE;
typedef cTPI_SURFACE_DATA sSIM_SURFACE_DATA, *SIM_SURFACE_DATA;
typedef cINTERP_MAP <cMIO_MODELf, cMIO_MODELf> sSIM_INTERP_MAP, *SIM_INTERP_MAP;

typedef cMIO_MODELf::cMESH::FACET FACET;

typedef cMIO_MODELf::cCOMPONENT cCOMPONENT;
typedef cMIO_MODELf::COMPONENT COMPONENT;

class cFOREIGN_MODEL_WIP; // so we can refer to this in cSURFACE_COUPLING

typedef class cSURFACE_COUPLING {

 private:

  //  cMIO_MODELf foreign_model;
  cTPI_MODEL *foreign_model;
  cFOREIGN_MODEL_WIP *foreign_model_wip;
  VOID CheckDuplicateNames(cMIO_COMPONENT_SKELETON *parent, cMIO_COMPONENT_SKELETON *child);
  VOID CheckDuplicateNames(cMIO_MODEL_SKELETON *skeleton, cMIO_COMPONENT_SKELETON *child);
  MIO_COMPONENT_SKELETON GetLeafComponentSkeleton(cSTRING full_name, cMIO_MODEL_SKELETON *skeleton);
  COMPONENT GetLeafComponent(STRING full_name, cMIO_MODELf *model);

 public:

  sCDI_CMDL cmdl;
  sCDI_SCBC *scbcs;
  sCDI_CPLW *cplws;

  // For surface coupling cdi meas windows. These are the raw exact times from cdi. After calling
  // compute_unrounded_time_desc(), the adjusted exact times for each phase are stored in 
  // unrounded_time_desc, and pushed back in cmdl where the adjusted exact times for all phases 
  // are stored together with the adjusted rounded times.
  dFLOAT    unrounded_start_time;
  dFLOAT    unrounded_end_time;
  dFLOAT    unrounded_average_interval;
  dFLOAT    unrounded_period;
  // Used to store adjusted exact timing parameters for all phases
  sUNROUNDED_TIME_DESC unrounded_time_desc;   // unrounded time desc for PowerTHERM coupled meas window. See PR41598.

  VOID compute_unrounded_time_desc(BOOLEAN is_not_first_coupling_phase);  // Does similar things as sCDI_MEAS_WINDOW::compute_rounded_time_descs() except that 
                                                                          // here we deal with unrounded (exact) times.

  // For cp meas windows
  dFLOAT    unrounded_output_time;      // Unrounded output time (Powertherm launch time) used for updating tdf file. See PR41598
  dFLOAT    unrounded_next_output_time; 

  cTHIRD_PARTY_INTERFACE *tpi;

  sSIM_ABSTRACT_SURFACE **native_target_surface;
  sSIM_ABSTRACT_SURFACE **native_source_surface;
  sSIM_ABSTRACT_SURFACE **foreign_target_surface;
  sSIM_ABSTRACT_SURFACE **foreign_source_surface;

  sSIM_SURFACE_DATA **native_target_data;

  sSIM_SURFACE_DATA **foreign_target_data;

  sSIM_INTERP_MAP *native_to_foreign_map;
  sSIM_INTERP_MAP *foreign_to_native_map;

  sSIM_ABSTRACT_SURFACE *native_resulting_source_surface;
  sSIM_ABSTRACT_SURFACE *foreign_resulting_source_surface;

  // could have multiple variables imported or exported for each coupling
  // model, so declare an array of sSIM_SURFACE_DATA here
  sSIM_SURFACE_DATA *foreign_resulting_source_data;

  asINT32 *native_target_indices;
  asINT32 *foreign_target_indices;

  sFLOAT *foreign_target_temperature; // For each coupled part

  cBOOLEAN init_pf_bc_coupling_p; // initialize PF BCs from coupling model
  cBOOLEAN launch_at_init_p;      // launch at init if start time == 0
  // the following two timers will be used in place of the meas 
  // window infrastructure for a one-way (Foreign->PF) coupling
  sMEAS_UPDATE_TIME next_update_time;
  TIMESTEP output_timestep;

  VOID BuildForeignModel();
  VOID BuildNativeToForeignMap();
  VOID BuildForeignToNativeMap();
  VOID SetMeshTransform();
  VOID PurgeSurfaceIteratorData();

  BOOLEAN is_coupling_data_available();

  sFLOAT *import_var_unit_conversion_slopes;
  sFLOAT *import_var_unit_conversion_offsets;
  sFLOAT *export_var_unit_conversion_slopes;
  sFLOAT *export_var_unit_conversion_offsets;
  // Fills the above 4 arrays based on contents of CDI CMDL chunk
  VOID process_cmdl_var_units();

  // for convenience
  sINT32 n_import_vars() { return cmdl.n_import_variables; }
  sINT32 n_export_vars() { return cmdl.n_export_variables; }
  DGF_COUPLING_VAR_TYPE *import_vars() { return cmdl.import_variables; }
  sINT32 *export_vars() { return cmdl.export_variables; }
  char **import_var_units() { return cmdl.import_variable_units; }
  char **export_var_units() { return cmdl.export_variable_units; }
  CDI_CMDL get_cmdl() {return &cmdl; }
  CDI_CPLW get_cplws() {return cplws; }
  CDI_SCBC get_scbcs() {return scbcs; }
  VOID apply_coupling_command_line_options();
  BOOLEAN any_pf_bc_init_p();
} *SURFACE_COUPLING;

typedef class cFOREIGN_MODEL_WIP : public cMIO_WIP
{
  cSURFACE_COUPLING *m_surface_coupling;
public:
  cFOREIGN_MODEL_WIP(cSURFACE_COUPLING *surface_coupling)
  { m_surface_coupling = surface_coupling; }

  VOID Warning(cSTRING msg)
  {
    msg_warn("%s: %s", m_surface_coupling->cmdl.model_filename, msg);
  }
  VOID Error(cSTRING msg)
  {
    msg_error("%s: %s", m_surface_coupling->cmdl.model_filename, msg);
  }
} *FOREIGN_MODEL_WIP;


typedef struct sPROC_SURFEL_ID {
  STP_PROC proc;
  STP_SURFEL_ID surfel_id;
} *PROC_SURFEL_ID;

typedef struct sPROC_SURFEL_ID_PAIR {
  STP_PROC proc;
  STP_SURFEL_ID surfel_id;
  asINT32 partner_id;
} *PROC_SURFEL_ID_PAIR;

VOID BuildNativeModel();
asINT32 get_cdi_face_index(uINT32 face_index);
VOID process_surface_coupling_units();
VOID build_surface_coupling_maps();
VOID purge_surface_iterator_data();
VOID create_mio_index_table();
asINT32 get_mio_model_facet_index(PROC_SURFEL_ID proc_surfel_id);
VOID create_hashed_surfel_areas();
VOID maybe_add_coupling_surfel_area(asINT32 proc, STP_SURFEL_ID surfel_id, 
    asINT32 scale, sFLOAT scaled_area);
sFLOAT get_hashed_surfel_area(PROC_SURFEL_ID psid);
VOID clear_hashed_surfel_areas();
VOID add_partner_proc_surfel_id(asINT32 proc, asINT32 id1, asINT32 id2);
VOID sort_partner_proc_surfel_ids();
asINT32 get_partner_proc_surfel_id(asINT32 proc, asINT32 surfel_id);
VOID free_partner_proc_surfel_ids();
asINT32 coupling_face_model_index(asINT32 face_index);
BOOLEAN is_bc_init_from_coupling_model(asINT32 model_index, asINT32 face_index);

#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
VOID create_vertex_id_table(asINT32 n_entries, asINT32 model_index);
asINT32 get_vindex(asINT32 vid);
VOID add_vid(asINT32 vid, asINT32 vindex);
#endif

#endif /* __CP_SURFACE_COUPLING_H */
