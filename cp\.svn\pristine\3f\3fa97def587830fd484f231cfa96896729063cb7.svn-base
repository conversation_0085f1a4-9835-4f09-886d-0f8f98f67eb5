/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */
#include "common.h"
#include "particle_sim_cp.h"
#include "cp_info.h"
#include "errbuf.h"
#include "parse_args.h"
#include "monitor.h"
#include "exa_sim_parse_cp.h"


extern sCP_INFO cp_info;
//BOOLEAN some_signal_was_received;

#define DEFAULT_EMITTER_START_DELAY 100       //Delay to start emitter after initial transient to make simulation more repeatable.
#define DEFAULT_WIPER_START_DELAY 100       //Same for wipers.


//Wipers
#if DEBUG_START_WIPERS
void sCP_PARTICLE_SIM::dump_wipers_to_start()
{
  std::map< TIMESTEP, std::vector<asINT32> > wipers_requested_to_start;
  for (std::map<TIMESTEP, std::vector<asINT32> >::iterator it=wipers_requested_to_start.begin(); it!=wipers_requested_to_start.end(); it++)  {
    std::vector<asINT32> &wipers = it->second;
    for (std::vector<asINT32>::iterator wiper_it = wipers.begin(); emitter_it != wipers.end(); wiper_it++) {
      printf("Request to start wiper %d at timestep %d", *wiper_it, it->first);
    }
  }
}
#endif

TIMESTEP sWIPER::start_delay = DEFAULT_WIPER_START_DELAY; // Could be overridden by the env variable EXA_WIPER_START_DELAY

void sWIPER::request_to_start(TIMESTEP start) {
  // Check if there is already an entry in wipers_requested_to_start
  if (cp_particle_sim.wipers_requested_to_start.find(start) == cp_particle_sim.wipers_requested_to_start.end()) {
    std::vector<asINT32> wipers;
    wipers.push_back(id());
    cp_particle_sim.wipers_requested_to_start.insert(std::pair< TIMESTEP, std::vector<asINT32> >(start, wipers));
  } else {
    cp_particle_sim.wipers_requested_to_start[start].push_back(id());
  }
  m_requested_to_start_p = TRUE;
  m_requested_start_time = start;
  cp_info.request_to_start_wiper = TRUE;
#if DEBUG_START_WIPERS
  cp_particle_sim.dump_wipers_to_start();
#endif
}

void sWIPER::remove_dummy_monitors(sCDI_WIPR& cdi_wiper, std::map<asINT32, asINT32>& cdi_monitor_index_to_sim_index) {
  //Make a list of the indices of the monitors controlling this wiper.
  std::vector<asINT32>& start_via_monitors = monitors();
  ccDOTIMES(i, start_via_monitors.size()) {
    start_via_monitors[i] = cdi_monitor_index_to_sim_index[start_via_monitors[i]];
  }
  //Remove the invalid indices with a value of  -1.
  start_via_monitors.erase(std::remove(start_via_monitors.begin(), start_via_monitors.end(), -1), start_via_monitors.end());
  m_n_master_monitors_to_check = start_via_monitors.size();

  //If there are no valid monitors to start this wiper, issue an error message.
  if (m_n_master_monitors_to_check == 0) {
    const std::vector<sCDI_WIPR> &cdi_wipers = *cp_particle_sim.cdi_wiper_models();
    msg_error("All monitors used to start wiper \"%s\" are invalid.", cdi_wipers[m_sim_wiper_id].name.c_str());
  }

  asINT32 latest_prescribed_end_of_transient;;
  BOOLEAN no_end_of_transients_known = TRUE;
  BOOLEAN all_end_of_transients_known = TRUE;
  ccDOTIMES(nth_controlling_monitor, monitors().size()) {
    // If the monitor end of initial transient is user specified and start time is known, its only
    // effect is to change the wiper start and end time, so the monitor is not really controlling
    // the wiper on the fly. Thus no need to tell the monitor to "control".
    int monitor_index = monitors()[nth_controlling_monitor];
    MONITOR monitor = cp_info.monitors[monitor_index];

    if(monitor->m_msap.m_isUserSpecifiedEndInitialTransient) {
      asINT32 end_of_transient_timestep = monitor->m_period * (0.5 + monitor->m_msap.m_userSpecifiedEndInitialTransient);
      if(no_end_of_transients_known) { //if this is the first one found...
        latest_prescribed_end_of_transient = end_of_transient_timestep;
      }

      no_end_of_transients_known = FALSE;
      latest_prescribed_end_of_transient = std::max(
                                                    latest_prescribed_end_of_transient,
                                                    end_of_transient_timestep);

    } else {
      all_end_of_transients_known = FALSE;
      monitor->wipers_to_start.push_back(this);  //Set this monitor to try to activate this wiper .
    }
  }

  //If no end-of-transients are known, no need to modify the wiper start time or mode,
  if(no_end_of_transients_known) {
    return;
  }

  //If all end-of-transients are known, the wiper should start at the earliest and can be ignorant of all monitors.
  if (all_end_of_transients_known) {
    cdi_wiper.start_via = eCDI_MEAS_START_TIME_VIA::StartTime;
    cdi_wiper.initial_delay.value = latest_prescribed_end_of_transient;
    unset_start_via_monitors_p();
    return;
  }

  //If some end of transients are known, the wiper should start at the earliest known or from a monitor signal
  //cdi_wiper->start_via = eCDI_MEAS_START_TIME_VIA::RelativeToEmitter + 1; //hack for lack of something like eCDI_MEAS_START_TIME_VIA::StartTimeOrAfterInitialTransient;
  cdi_wiper.initial_delay.value = latest_prescribed_end_of_transient;
}

void sWIPER::remove_control_monitor(MONITOR control_monitor) {
  m_n_master_monitors_to_check--;
#if DEBUG_MONITOR
  msg_print("wiper %d n_master_monitors_to_check = %d", id(), monitors_to_check());
#endif
  if (m_n_master_monitors_to_check < 0)
    msg_internal_error("wiper n_master_monitors_to_check can not be negative!");
  if (m_n_master_monitors_to_check == 0)  { // should start the wiper now
    // Find the maximal end of initial transient time among all monitors
    TIMESTEP end_init_transient_time = 0;
    ccDOTIMES(j, monitors().size()) {
      MONITOR monitor = cp_info.monitors[monitors()[j]];
      end_init_transient_time = MAX(end_init_transient_time,
                                    control_monitor->get_frame_time_from_signal_time(monitor->m_endInitialTransientTime));
    }

    TIMESTEP expected_start_time = end_init_transient_time + start_delay;   // Add delay to make the simulation more repeatable
#if DEBUG_MONITOR
    msg_print("Request to start wiper %d at timestep %d", id(), expected_start_time);
#endif
    // If this is a full ckpt resume, then the wiper start time may have been updated when reading in wipers
    // Wipers ckpt info should be read before monitors ckpt info is read, otherwise the code is broken.
    if (cp_info.is_full_checkpoint_restore && cp_info.time == cp_info.restart_time) {
      if (!(is_requested_to_start() || is_scheduled_to_start()))
        request_to_start(expected_start_time);    // Should never reach here
    } else {
      request_to_start(expected_start_time);
    }
  }
}

void sWIPER::set_start_time(TIMESTEP start) {
  m_start_time = start;
  msg_print("Wiper \"%s\" is scheduled to start at timestep %d.", m_name.c_str(), m_start_time);
  m_requested_to_start_p = FALSE;
}

//Emitters:
#if DEBUG_START_EMITTERS
void sCP_PARTICLE_SIM::dump_emitters_to_start()
{
  std::map< TIMESTEP, std::vector<asINT32> > emitters_requested_to_start;
  for (std::map<TIMESTEP, std::vector<asINT32> >::iterator it=emitters_requested_to_start.begin(); it!=emitters_requested_to_start.end(); it++)
    {
      std::vector<asINT32> &emitters = it->second;
      for (std::vector<asINT32>::iterator emitter_it = emitters.begin(); emitter_it != emitters.end(); emitter_it++)
        {
          printf("Request to start emitter %d at timestep %d", *emitter_it, it->first);
        }
    }
}
#endif

TIMESTEP sEMITTER::start_delay = DEFAULT_EMITTER_START_DELAY; // could be overridden by the env variable EXA_EMITTER_START_DELAY

void sEMITTER::request_to_start(TIMESTEP start)
{
  // Check if there is already an entry in emitters_requested_to_start
  if (cp_particle_sim.emitters_requested_to_start.find(start) == cp_particle_sim.emitters_requested_to_start.end()) {
    std::vector<asINT32> emitters;
    emitters.push_back(id());
    cp_particle_sim.emitters_requested_to_start.insert(std::pair< TIMESTEP, std::vector<asINT32> >(start, emitters));
  } else {
    cp_particle_sim.emitters_requested_to_start[start].push_back(id());
  }
  m_requested_to_start_p = TRUE;
  m_requested_start_time = start;
  cp_info.request_to_start_emitter = TRUE;
#if DEBUG_START_EMITTERS
  cp_particle_sim.dump_emitters_to_start();
#endif
}

void sEMITTER::remove_dummy_monitors(sCDI_PARTICLE_EMITTER_BASE &cdi_emitter, std::map<asINT32, asINT32>& cdi_monitor_index_to_sim_index) {

  //Make a list of indices of the monitors controlling this emitter.
  std::vector<asINT32>& start_via_monitors = monitors();
  ccDOTIMES(i, start_via_monitors.size()) {
    start_via_monitors[i] = cdi_monitor_index_to_sim_index[start_via_monitors[i]];
  }
  start_via_monitors.erase(std::remove(start_via_monitors.begin(), start_via_monitors.end(), -1), start_via_monitors.end()); //-1 indicates an invalid monitor
  m_n_master_monitors_to_check = start_via_monitors.size();

  // If all monitors used to start this emitter are invalid, issue error message
  if (m_n_master_monitors_to_check == 0) {
    const std::vector<const sCDI_PARTICLE_EMITTER_BASE*> &cdi_emitters = cp_particle_sim.cdi_particle_emitters()->GetAllEmitters();
    msg_error("All monitors used to start emitter \"%s\" are invalid.", cdi_emitters[m_sim_emitter_id]->name.c_str());
  }

  asINT32 latest_prescribed_end_of_transient;;
  BOOLEAN no_end_of_transients_known = TRUE;
  BOOLEAN all_end_of_transients_known = TRUE;
  ccDOTIMES(nth_controlling_monitor, monitors().size()) {
    // If the monitor end of initial transient is user specified and start time is known, its only
    // effect is to change the wiper start and end time, so the monitor is not really controlling
    // the wiper on the fly. Thus no need to tell the monitor to "control".
    int monitor_index = monitors()[nth_controlling_monitor];
    MONITOR monitor = cp_info.monitors[monitor_index];

    if(monitor->m_msap.m_isUserSpecifiedEndInitialTransient) {
      asINT32 end_of_transient_timestep = monitor->m_period * (0.5 + monitor->m_msap.m_userSpecifiedEndInitialTransient);
      if(no_end_of_transients_known) { //if this is the first one found...
        latest_prescribed_end_of_transient = end_of_transient_timestep;
      }
      no_end_of_transients_known = FALSE;
      latest_prescribed_end_of_transient = std::max(
                                                    latest_prescribed_end_of_transient,
                                                    end_of_transient_timestep);

    } else {
      all_end_of_transients_known = FALSE;
      monitor->emitters_to_start.push_back(this);  //Set this monitor to try to activate this emitter.
    }
  }

  //If no end-of-transients are known, no need to modify the emiter minimum start time or mode,
  if(no_end_of_transients_known) {
    return;
  }

  //If all end-of-transients are known, the emitter should start at the latest end of transient and can be ignorant of all monitors.
  if (all_end_of_transients_known) {
    cdi_emitter.start_via = eCDI_MEAS_START_TIME_VIA::StartTime;
    cdi_emitter.start.value = latest_prescribed_end_of_transient;
    unset_start_via_monitors_p();
    return;
  }

  //If some end of transients are known, the emitter should start no earlier than the ltest known end of transient.
  //cdi_wiper->start_via = eCDI_MEAS_START_TIME_VIA::RelativeToEmitter + 1; //hack for lack of something like eCDI_MEAS_START_TIME_VIA::StartTimeOrAfterInitialTransient;
  cdi_emitter.start.value = latest_prescribed_end_of_transient;

}

// Do not really remove the monitor from the list since we still need to keep that information.
// Just decrease the number of control monitors and check if it reaches 0. If so, request to
// start the emitter.
void sEMITTER::remove_control_monitor(MONITOR control_monitor)
{
  m_n_master_monitors_to_check--;
#if DEBUG_MONITOR
  msg_print("emitter %d n_master_monitors_to_check = %d", id(), monitors_to_check());
#endif
  if (m_n_master_monitors_to_check < 0)
    msg_internal_error("emitter n_master_monitors_to_check can not be negative!");
  if (m_n_master_monitors_to_check == 0)  { // should start the emitter now
    // Find the maximal end of initial transient time among all monitors
    TIMESTEP end_init_transient_time = 0;
    ccDOTIMES(j, monitors().size()) {
      MONITOR monitor = cp_info.monitors[monitors()[j]];
      end_init_transient_time = MAX(end_init_transient_time,
                                    control_monitor->get_frame_time_from_signal_time(monitor->m_endInitialTransientTime));
    }

    TIMESTEP expected_start_time = end_init_transient_time + start_delay;   // Add delay to make the simulation more repeatable
#if DEBUG_MONITOR
    msg_print("Request to start emitter %d at timestep %d", id(), expected_start_time);
#endif
    // If this is a full ckpt resume, then the emitter start time may have been updated when reading in emitters.
    // Emitters ckpt info should be read before monitors ckpt info is read, otherwise the code is broken.
    if (cp_info.is_full_checkpoint_restore && cp_info.time == cp_info.restart_time) {
      if (!(is_requested_to_start() || is_scheduled_to_start()))
        request_to_start(expected_start_time);    // Should never reach here
    } else {
      request_to_start(expected_start_time);
    }
  }
}

void sEMITTER::set_start_time(TIMESTEP start) {
  m_start_time = start;
  if (m_end_time_via_duration_p) {
    m_end_time = MIN((dFLOAT)start + m_duration, TIMESTEP_MAX);
    msg_print("Emitter \"%s\" is scheduled to start at timestep %d and end at timestep %d.", m_name.c_str(), m_start_time, m_end_time);
  } else {
    msg_print("Emitter \"%s\" is scheduled to start at timestep %d.", m_name.c_str(), m_start_time);
  }
  m_requested_to_start_p = FALSE;
}


sCP_PARTICLE_SIM cp_particle_sim;

sCP_PARTICLE_SIM::sCP_PARTICLE_SIM(VOID) {
  //m_some_sps_are_saturated = FALSE;
  //m_some_sps_were_saturated = FALSE;
  //some_signal_was_received = FALSE;
  //m_time_of_previous_parameter_update_send = -1;
  //m_release_rate_update_needed = FALSE;
  m_ice_accretion_parameters = NULL;
}
sCP_PARTICLE_SIM::~sCP_PARTICLE_SIM(VOID){}

VOID sCP_PARTICLE_SIM::initialize(VOID){
  //m_sp_runtime_data.resize(total_sps);
  //m_sp_runtime_data_recv_request.assign(total_sps, MPI_REQUEST_NULL);
  //m_sp_runtime_data_received.assign(total_sps, FALSE);
}

VOID sCP_PARTICLE_SIM::uninitialize(VOID) {}

VOID  sCP_PARTICLE_SIM::compute_lattice_time_correction_factor(UNITS_DB units_db) {
  UNITS_UNIT sLatticeTime;
  UNITS_UNIT sLatticeTimeInc;
  units_parse_unit(cp_info.units_db, "LatticeTime", &sLatticeTime);
  units_parse_unit(cp_info.units_db, "LatticeTimeInc", &sLatticeTimeInc);
  dFLOAT conversion_slope;
  dFLOAT conversion_offset;
  units_conversion_coefficients(cp_info.units_db, sLatticeTime, sLatticeTimeInc, &conversion_slope, &conversion_offset);
  //units_convert(cp_info.units_db, 1.0, sLatticeTime, sLatticeTimeInc, &conversion_factor);
  //if(conversion_offset != 0.0)
  //  msg_error("Affine transformation between LatticeTime and LatticeTimeIncrement detected.\n");
  this->m_lattice_time_correction = conversion_slope;
  this->m_one_over_lattice_time_correction = 1.0 / conversion_slope;
}

VOID sCP_PARTICLE_SIM::broadcast_vertices()
{
  DGF_VERTEX_INDEX n_vertices = cp_info.n_original_surfel_vertices;
  ccDO_FROM_BELOW_BY(i, 0, n_vertices, N_VERTICES_PER_BROADCAST) {
    DGF_VERTEX_INDEX n_to_write = MIN(N_VERTICES_PER_BROADCAST, n_vertices - i);
    //#ifdef PARTICLE_MODELING_DOUBLE_PRECISION_VERTEX
    MPI_Bcast(&cp_info.vertex_array[i], 3 * n_to_write, eMPI_dFLOAT, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
    //#else
    //    MPI_Bcast(&cp_info.vertex_array[i], 3 * n_to_write, eMPI_sFLOAT, eMPI_CP_RANK, eMPI_sp_cp_comm);
    //#endif
  }
}

VOID sCP_PARTICLE_SIM::wait_for_sps_to_build_stencils() {

  cp_jobctl_output_status("Generating film solver stencils.");
#if 1
  broadcast_vertices();
#ifdef MERGE_VINIT
  //MPI_Barrier(eMPI_sp_cp_comm);  //The SPs will use this a few times when creating film only ghost surfels and we need corresponding calls here.
  //MPI_Barrier(eMPI_sp_cp_comm);
#endif
  broadcast_vertices();
  if(total_sps > 1)
    cp_jobctl_output_status("Reconciling SP spanning film solver stencils.");
#ifdef MERGE_VINIT
  //MPI_Barrier(eMPI_sp_cp_comm);
#endif
  broadcast_vertices();
#ifdef MERGE_VINIT
  //MPI_Barrier(eMPI_sp_cp_comm);
#endif
  cp_jobctl_output_status("Computing film solver stencil weights.");
  broadcast_vertices();
#else
  //The intel MPI lib doesnt support non blocking barrier but the following would allow simerrs to be reported during the film solver stencil creation.
  //Instead the above is used for now and the simerrs are buffered on the SPs until the stencil creation is finished.
  MPI_Request request;
  MPI_Status status;
  asINT32 flag;
  MPI_Ibarrier(eMPI_sp_cp_comm, &request);
  do {
    cp_process_sp_errors(wallclock_time_secs());
    MPI_Test(&request, &flag, &status );
  } while(!flag);

  MPI_Ibarrier(eMPI_sp_cp_comm, &request);
  do {
    cp_process_sp_errors(wallclock_time_secs());
    MPI_Test(&request, &flag, &status );
  } while(!flag);

  if(total_sps > 1)
    cp_jobctl_output_status("Reconciling SP spanning film solver stencils.");
  MPI_Ibarrier(eMPI_sp_cp_comm, &request);
  do {
    cp_process_sp_errors(wallclock_time_secs());
    MPI_Test(&request, &flag, &status );
  } while(!flag);

  MPI_Ibarrier(eMPI_sp_cp_comm, &request);
  do {
    cp_process_sp_errors(wallclock_time_secs());
    MPI_Test(&request, &flag, &status );
  } while(!flag);
  cp_jobctl_output_status("Computing film solver stencil weights.");
#endif
}

VOID sCP_PARTICLE_SIM::strip_particle_modeling_meas_variables() {
  DO_CP_MEAS_WINDOWS(window) {
    if (window->is_average_mme)
      continue;
    asINT32 num_particle_tracking_var_types = 0;
    ccDOTIMES(var_index, window->n_variables) {
      if (sri_is_particle_tracking_vartype(window->var_types[var_index]))
        num_particle_tracking_var_types++;
    }

    if(num_particle_tracking_var_types == 0)
      continue;

    asINT32 num_original_variables = window->n_variables;
    asINT32 num_remaining_variables = num_original_variables - num_particle_tracking_var_types;

    window->n_variables = 0;
    //If there were only particle modeling variables in this window, the window is disabled in init_clear_and_output_times(). (PR39448)

    if(num_remaining_variables <= 0) {
      msg_warn("Measurement window \"%s\" only contains particle modeling variables and has been disabled.", window->output_filename);
    } else {
      //Push the non-particle vars to the beginning of the list
      ccDOTIMES(original_var_index, num_original_variables) {
        if (!sri_is_particle_tracking_vartype(window->var_types[original_var_index])) {
          window->var_types[window->n_variables] = window->var_types[original_var_index];
          window->population_var_indices[window->n_variables++] = window->population_var_indices[original_var_index];
        }
      }
    }
  }
}

VOID modify_particle_var_descriptor(CP_MEAS_WINDOW window, asINT32 var_index, SRI_VARIABLE_DESC descriptor) {

  //Modiy the vartype descriptors written to meas files for particle
  //modeling var types that are expanded for per emitter or per material measurements.

  //Any particle modeling built in var types that have multiple
  //components are translated from the buili-in var type to user
  //defined var types with modifications to the variable name and
  //variable type id.

  SRI_VARIABLE_TYPE built_in_var_type = window->var_types[var_index];

  if (!sri_is_particle_tracking_vartype(built_in_var_type)) {
    //If this isn't a buily-in particle tracking var type, do nothing.
    return;
  }

  //This bit of code is a temorary hack until VIZ can support new built in thermal particle var types.
  //The built in types are assigned user defined var type ids for now so that VIZ can display them.
  switch(built_in_var_type) {
  case SRI_VARIABLE_PRTCL_MEAN_TEMP:
    descriptor->var_type =  (SRI_VARIABLE_TYPE)(cp_info.cvid_helper->get_first_pm_var_id() + (int)eEXPERIMENTAL_VARIABLE_TYPES::MEAN_PARTICLE_TEMP);
    break;
  case SRI_VARIABLE_PRTCL_COMPOSITION_1:
    descriptor->var_type =  (SRI_VARIABLE_TYPE)(cp_info.cvid_helper->get_first_pm_var_id() + (int)eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_1);
    break;
  case SRI_VARIABLE_PRTCL_COMPOSITION_2:
    descriptor->var_type =  (SRI_VARIABLE_TYPE)(cp_info.cvid_helper->get_first_pm_var_id() + (int)eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_2);
    break;
  case SRI_VARIABLE_PRTCL_COMPOSITION_3:
    descriptor->var_type =  (SRI_VARIABLE_TYPE)(cp_info.cvid_helper->get_first_pm_var_id() + (int)eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_3);
    break;
  case SRI_VARIABLE_PRTCL_EVAPORATION_RATE:
    descriptor->var_type =  (SRI_VARIABLE_TYPE)(cp_info.cvid_helper->get_first_pm_var_id() + (int)eEXPERIMENTAL_VARIABLE_TYPES::EVAPORATION_RATE);
    break;
  default: break;
  }

  BOOLEAN per_emitter = window->cdi_meas_window->m_per_emitter_particle_measurements;
  BOOLEAN per_material = window->cdi_meas_window->m_per_material_particle_measurements;

  if( !per_emitter && !per_material)
    return;

  sINT8 component_index = window->m_var_component_ids[var_index];
  if(component_index == -1)
    return;

  //Assign a unique custom ID var type using the CVID helper from SRI.
  eSRI_VARIABLE_PM_OFFSET pm_var_offset = cp_info.cvid_helper->pm_sri_type_to_offset(built_in_var_type);
  SRI_VARIABLE_TYPE custom_var_type = cp_info.cvid_helper->get_pm_var_id(
                                                                         pm_var_offset,
                                                                         per_emitter ? component_index : -1,
                                                                         per_material ? component_index : -1);
  descriptor->var_type = custom_var_type;


  if(window->population_var_indices[var_index] != -1) {
    SRI_VARIABLE_TYPE built_in_pop_var_type = window->var_types[window->population_var_indices[var_index]];
    eSRI_VARIABLE_PM_OFFSET pop_var_offset = cp_info.cvid_helper->pm_sri_type_to_offset(built_in_pop_var_type);
    SRI_VARIABLE_TYPE custom_pop_var_type = cp_info.cvid_helper->get_pm_var_id(
                                                                               pop_var_offset,
                                                                               per_emitter ? component_index : -1,
                                                                               per_material ? component_index : -1);
    descriptor->mean_particle_count_variable_type = custom_pop_var_type;
  }

  //Augment the default names to indicate what component was measured.
  std::string component_suffix;
  if(per_material) {
    component_suffix = "-material-" + cp_particle_sim.cdi_particle_materials()->at(component_index).name;
  } else {
    const std::vector<const sCDI_PARTICLE_EMITTER_BASE*> &cdi_emitters = cp_particle_sim.cdi_particle_emitters()->GetAllEmitters();
    component_suffix = "-emitter-" + cdi_emitters[component_index]->name;
  }
  std::stringstream tiny_name;
  tiny_name << sri_variable_type_to_short_name(built_in_var_type) << "-" << (int)component_index;
  std::stringstream short_name;
  short_name << sri_variable_type_to_short_name(built_in_var_type) <<  component_suffix;
  std::stringstream long_name;
  long_name << sri_variable_type_to_short_name(built_in_var_type) <<  component_suffix;
  descriptor->tiny_name = strdup(tiny_name.str().c_str());
  descriptor->short_name = strdup(short_name.str().c_str());
  descriptor->long_name = strdup(long_name.str().c_str());
  //printf("Using custom vartype %d (from built in type %d) with name %s in nc file.\n",
  //       descriptor->var_type,
  //       built_in_var_type,long_name.str().c_str());
}


VOID sCP_PARTICLE_SIM::expand_particle_measurements() {
  //Create multiple volumetric measurement variables for particle
  //modeling var types if the user requests per-material or
  //per-emitter particle measurements.  When this happens, selected
  //var types are duplicated and distinguished by a component ID that is
  //otherwise -1.  The component IDs assigned here are inspected when the
  //var descriptors are initialized so that each instance can be given
  //unique names.

  //The SPs will also compute the component IDs to alter what is
  //measured for each duplicate var type.

  DO_CP_MEAS_WINDOWS(window) {

    if(window->sri_file_type != SRI_FLUID_TYPE &&
       window->sri_file_type != SRI_COMPOSITE_FLUID_TYPE
       && window->sri_file_type != SRI_FLUID_DEV_TYPE)
      continue; //only support expanded variables in fluid windows.

    int num_components = 1;
    if(window->cdi_meas_window->m_per_material_particle_measurements) {
      num_components *= m_cdi_particle_materials.size();
    }
    if(window->cdi_meas_window->m_per_emitter_particle_measurements) {
      num_components *= m_cdi_particle_emitters.GetAllEmitters().size();
    }

    if(num_components ==1 )
      continue; //No expansion needed.

    std::list<int> component_ids;
    std::list<SRI_VARIABLE_TYPE> var_types;
    std::list<int> pop_var_types;
    ccDOTIMES(var_index, window->n_variables) {
      SRI_VARIABLE_TYPE var_type = window->var_types[var_index];
      window->m_var_component_ids[var_index] = -1;
      if(!sri_is_particle_tracking_vartype(var_type))
        continue;

      switch(var_type) {
      case SRI_VARIABLE_PRTCL_XFORCE:
      case SRI_VARIABLE_PRTCL_YFORCE:
      case SRI_VARIABLE_PRTCL_ZFORCE:
        continue; //Don't expand these types.
      default:
        break;
      }

      int num_components_for_this_var = num_components;
      //        if(var_type == SRI_VARIABLE_PRTCL_COMPOSITION)
      //          num_components_for_this_var = num_components * NUM_PARTICLE_MASS_FRACTIONS;

      if(num_components_for_this_var == 1)
        continue;

      window->m_var_component_ids[var_index] = 0;
      ccDO_FROM_BELOW(component_id, 1, num_components_for_this_var ) {
        var_types.push_back(var_type);
        component_ids.push_back(component_id);
        pop_var_types.push_back(window->population_var_indices[var_index]);  //Temporarily record the population var index for the first component of this var type.
      }

    }

    //Update the window parametes with the new var types if needed.
    if(var_types.size() == 0)
      continue;

    std::list<SRI_VARIABLE_TYPE>::iterator var = var_types.begin();
    std::list<int>::iterator component = component_ids.begin();
    std::list<int>::iterator pop_var = pop_var_types.begin();
    for(; var != var_types.end() && component != component_ids.end() && pop_var != pop_var_types.end();
        ++var, ++component, ++pop_var) {
      window->append_var_type(*var, *pop_var, *component);
    }


    //Make a second pass over the var types and update the population var indices.
    for(asINT32 var_index  = 0; var_index < window->n_variables; var_index++) {
      asINT32 pop_var_index = window->population_var_indices[var_index];
      if (pop_var_index == -1)
        continue;

      //Find the index for the var type which matches this var types
      //population var_type and component id.
      SRI_VARIABLE_TYPE pop_var_type = window->var_types[pop_var_index];
      asINT32 component_id = window->m_var_component_ids[var_index];
      asINT32 new_pop_var_index = -1;
      for(asINT32 search_index = 0;  search_index < window->n_variables  ; search_index++) {
        if(window->var_types[search_index] == pop_var_type &&
           window->m_var_component_ids[search_index] == component_id) {
          new_pop_var_index = search_index;
          break;
        }
      }
      window->population_var_indices[var_index] = new_pop_var_index;
    }
  }
}

VOID sCP_PARTICLE_SIM::maybe_append_experimental_particle_var_types() {

  if(cp_info.is_accretion_simulation)
    maybe_append_rime_ice_var_types();

  if(g_is_erosion_simulation)
    maybe_append_erosion_var_types();

  if(cp_info.is_thermal_particle_solver)
    maybe_append_thermal_particle_var_types();
}

VOID sCP_PARTICLE_SIM::maybe_append_rime_ice_var_types() {
  //This routine checks if hte ice thickness variables is not in the accretion window and appends it as neccesecary .
  if(!cp_info.is_accretion_simulation)
    return;

  asINT32 accretion_window_cdi_index = cp_particle_sim.m_ice_accretion_parameters->m_meas_window_index;
  CDI_MEAS_WINDOW cdi_accretion_window = &cp_info.cdi_meas_windows[accretion_window_cdi_index];

  DO_CP_MEAS_WINDOWS(window) {

    //Only add the variable automatically if this is the accretion window
    if(window->cdi_meas_window != cdi_accretion_window)
      continue;

    //Check if the PowerCASE has  already added the accretion surface variable  SRI_VARIABLE_ICE_THICKNESS (PR47751)
    BOOLEAN accretion_variable_already_exists = FALSE;
    ccDOTIMES(var_index, window->n_variables) {
      if(window->var_types[var_index] == SRI_VARIABLE_ICE_THICKNESS) {
        accretion_variable_already_exists = TRUE;
        break;
      }
    }

    if(accretion_variable_already_exists)
      continue; //continue to the next window

    //If the predefined SRI accretion variable wasn't added by PowerCASE, add something suitable.
    if(window->sri_file_type == SRI_SURFACE_TYPE ) {
      window->append_var_type(SRI_VARIABLE_ICE_THICKNESS);
    }
  }
}

VOID sCP_PARTICLE_SIM::maybe_append_erosion_var_types() {
  //This routine checks if hte ice thickness variables is not in the accretion window and appends it as neccesecary .
  if(!g_is_erosion_simulation)
    return;

  DO_CP_MEAS_WINDOWS(window) {

    //Only add the variable automatically to surface windows
    if(!(window->meas_window_type == LGI_SURFACE_WINDOW))
      continue;

    //Check if the PowerCASE has already added the accretion surface variable
    BOOLEAN erosion_variable_already_exists = FALSE;
    ccDOTIMES(var_index, window->n_variables) {
      if(window->var_types[var_index] == SRI_VARIABLE_EROSION_DEPTH) {
        erosion_variable_already_exists = TRUE;
        break;
      }
    }

    if(erosion_variable_already_exists)
      continue; //continue to the next window

    //Add the needed variable
    if(window->sri_file_type == SRI_SURFACE_TYPE ) {
      SRI_VARIABLE_TYPE var_id = (SRI_VARIABLE_TYPE)((int)eEXPERIMENTAL_VARIABLE_TYPES::EROSION_VOLUME + cp_info.cvid_helper->get_first_pm_var_id());
      window->append_var_type(var_id);
    }
  }
}

VOID sCP_PARTICLE_SIM::set_experimental_particle_var_descriptors(SRI_VARIABLE_DESC descriptor) {
  eEXPERIMENTAL_VARIABLE_TYPES offset =
    (eEXPERIMENTAL_VARIABLE_TYPES)
    (descriptor->var_type - cp_info.cvid_helper->get_first_pm_var_id());
  switch(offset) {
  case eEXPERIMENTAL_VARIABLE_TYPES::EVAPORATION_RATE:
    descriptor->unit_class_name = "MassFlowPerVolume";
    descriptor->lattice_unit_name = "LatticeMassFlowPerVolume";
    descriptor->tiny_name = "pevr";
    descriptor->short_name = "particle_evaporation";
    descriptor->long_name = "Particle Evaporation Rate";
    descriptor->mean_particle_count_variable_type = SRI_VARIABLE_INVALID;
    break;
  case eEXPERIMENTAL_VARIABLE_TYPES::MEAN_PARTICLE_TEMP:
    descriptor->unit_class_name = "Temperature";
    descriptor->lattice_unit_name = "LatticeTemperture";
    descriptor->tiny_name = "ptmp";
    descriptor->short_name = "particle_temp";
    descriptor->long_name = "Mean Particle Temperature";
    descriptor->mean_particle_count_variable_type = SRI_VARIABLE_PRTCL_NUMBER;
    break;
  case eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_1:
    descriptor->unit_class_name = "Dimensionless";
    descriptor->tiny_name = "pcmp1";
    descriptor->short_name = "particle_fraction_1";
    descriptor->long_name = "Particle Composition Mass Fraction 1";
    descriptor->lattice_unit_name = "Dimensionless";
    descriptor->mean_particle_count_variable_type = SRI_VARIABLE_PRTCL_NUMBER;
    break;
  case eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_2:
    descriptor->unit_class_name = "Dimensionless";
    descriptor->tiny_name = "pcmp2";
    descriptor->short_name = "particle_fraction_1";
    descriptor->long_name = "Particle Composition Mass Fraction 2";
    descriptor->lattice_unit_name = "Dimensionless";
    descriptor->mean_particle_count_variable_type = SRI_VARIABLE_PRTCL_NUMBER;
    break;
  case eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_3:
    descriptor->unit_class_name = "Dimensionless";
    descriptor->tiny_name = "pcmp3";
    descriptor->short_name = "particle_fraction_1";
    descriptor->long_name = "Particle Composition Mass Fraction 3";
    descriptor->lattice_unit_name = "Dimensionless";
    descriptor->mean_particle_count_variable_type = SRI_VARIABLE_PRTCL_NUMBER;
    break;
  default:
    msg_error("Unimplmented experimental particle modeling var type encountered.");
  }
}

#if 0 //Use user defnied var types, otherwsie append built in SRI variable types.
VOID sCP_PARTICLE_SIM::maybe_append_thermal_particle_var_types() {

  if(!cp_info.is_thermal_particle_solver)
    return;

  DO_CP_MEAS_WINDOWS(window) {
    if (window->is_average_mme)
      continue;

    if(window->sri_file_type != SRI_FLUID_TYPE
       && window->sri_file_type != SRI_COMPOSITE_FLUID_TYPE
       && window->sri_file_type != SRI_FLUID_DEV_TYPE)
      continue;

    eEXPERIMENTAL_VARIABLE_TYPES possible_var_types[5] = {
      eEXPERIMENTAL_VARIABLE_TYPES::MEAN_TEMP,
      eEXPERIMENTAL_VARIABLE_TYPES::EVAPORATION_RATE,
      eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_1,
      eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_2,
      eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_3};

    BOOLEAN variable_already_exists[5] = {FALSE};
    ccDOTIMES(var_index, window->n_variables) {
      SRI_VARIABLE_TYPE var_type = window->var_types[var_index];
      switch(var_type) {
      case eEXPERIMENTAL_VARIABLE_TYPES::MEAN_TEMP:
        variable_already_exists[0] = TRUE;
        break;
      case  eEXPERIMENTAL_VARIABLE_TYPES::EVAPORATION_RATE:
        variable_already_exists[1] = TRUE;
        break;
      case  eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_1:
        variable_already_exists[2] = TRUE;
        break;
      case  eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_2:
        variable_already_exists[3] = TRUE;
        break;
      case  eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_3:
        variable_already_exists[4] = TRUE;
        break;
      default:
        break;
      }
    }

    int added_something = 0;
    ccDOTIMES(var_index, 5) {
      if(variable_already_exists[var_index])
        continue; //continue to the next window
      SRI_VARIABLE_TYPE var_id = (SRI_VARIABLE_TYPE)(possible_var_types[var_index] +  cp_info.cvid_helper->get_first_pm_var_id());
      window->append_var_type(var_id);
      added_something++;
    }

    if(added_something) {
      msg_warn(
               "Appended %d variables to measure thermal particle properties to fluid window \"%s\".",
               added_something,
               window->output_filename);
    }

  }
}

#else
VOID sCP_PARTICLE_SIM::maybe_append_thermal_particle_var_types() {

  if(!cp_info.is_thermal_particle_solver)
    return;

  DO_CP_MEAS_WINDOWS(window) {
    if (window->is_average_mme)
      continue;

    if(window->sri_file_type != SRI_FLUID_TYPE
       && window->sri_file_type != SRI_COMPOSITE_FLUID_TYPE
       && window->sri_file_type != SRI_FLUID_DEV_TYPE)
      continue;

    SRI_VARIABLE_TYPE possible_var_types[5] = {
      SRI_VARIABLE_PRTCL_MEAN_TEMP,
      SRI_VARIABLE_PRTCL_EVAPORATION_RATE,
      SRI_VARIABLE_PRTCL_COMPOSITION_1,
      SRI_VARIABLE_PRTCL_COMPOSITION_2,
      SRI_VARIABLE_PRTCL_COMPOSITION_3};

    BOOLEAN variable_already_exists[5] = {FALSE};
    ccDOTIMES(var_index, window->n_variables) {
      SRI_VARIABLE_TYPE var_type = window->var_types[var_index];
      switch(var_type) {
      case SRI_VARIABLE_PRTCL_MEAN_TEMP:
        variable_already_exists[0] = TRUE;
        break;
      case  SRI_VARIABLE_PRTCL_EVAPORATION_RATE:
        variable_already_exists[1] = TRUE;
        break;
      case  SRI_VARIABLE_PRTCL_COMPOSITION_1:
        variable_already_exists[2] = TRUE;
        break;
      case  SRI_VARIABLE_PRTCL_COMPOSITION_2:
        variable_already_exists[3] = TRUE;
        break;
      case  SRI_VARIABLE_PRTCL_COMPOSITION_3:
        variable_already_exists[4] = TRUE;
        break;
      default:
        break;
      }
    }

    int added_something = 0;
    ccDOTIMES(var_index, 5) {
      if(variable_already_exists[var_index])
        continue; //continue to the next window
      window->append_var_type(possible_var_types[var_index]);
      added_something++;
    }

    if(added_something) {
      msg_warn(
               "Appended %d variables to measure thermal particle properties to fluid window \"%s\".",
               added_something,
               window->output_filename);
    }

  }
}
#endif

VOID sCP_PARTICLE_SIM::maybe_append_particle_population_size_measurement_var_types() {
  // For each window:
  // If the window contains any var types related to the measurement of a mean particle property:
  // Then make sure the window also includes a measurement type for the particle population size
  // which would be needed to compute the mean for meas cells that span SPs and which would be
  // required for post processing by external tools (e.g. exa_meas_copy -sum)

  DO_CP_MEAS_WINDOWS(window) {
    if (window->is_average_mme)
      continue;

    asINT32 n_variables = window->n_variables;

    asINT32 add_fluid_population_var = 0;
    asINT32 add_inbound_population_var = 0;
    asINT32 add_outbound_population_var = 0;
    asINT32 add_pass_through_population_var = 0;
    asINT32 add_particle_mean_temperature = 0;

    asINT32 fluid_population_var_index;
    asINT32 inbound_population_var_index;
    asINT32 outbound_population_var_index;
    asINT32 pass_through_population_var_index;

    //Scan through the existing list and check if any population properties are needed.
    ccDOTIMES(var_index, n_variables) {
      SRI_VARIABLE_TYPE var_id = window->var_types[var_index];

      switch(window->sri_file_type) {
      case SRI_FLUID_TYPE:
      case SRI_COMPOSITE_FLUID_TYPE:
        //Some var types like MEAN_PARTICLE_DIAMETER can be measured
        //in the fluid, or on sample surfaces. If this is a fluid
        //file, use the voxel count as the population size.
        if (sri_is_particle_tracking_fluid_mean_vartype(var_id))  {
          add_fluid_population_var = 1;
          continue;
        }
      default:break; //Otherwise fall through and let the pass_through_mean_vartype conditon handle this var type.
      }

      if (sri_is_particle_tracking_inbound_mean_vartype(var_id)) {
        add_inbound_population_var = 1;
        continue;
      }

      if (sri_is_particle_tracking_outbound_mean_vartype(var_id)) {
        add_outbound_population_var = 1;
        continue;
      }

      if (sri_is_particle_tracking_pass_through_mean_vartype(var_id)) {
        add_pass_through_population_var = 1;
        continue;
      }

      //Also check if this is an experimental variable that needs to be averaged by particle population.
      if(cp_info.cvid_helper->is_pm_var_id(var_id)) {
        eEXPERIMENTAL_VARIABLE_TYPES offset =
          (eEXPERIMENTAL_VARIABLE_TYPES)(var_id - cp_info.cvid_helper->get_first_pm_var_id());
        switch(offset) {
        case eEXPERIMENTAL_VARIABLE_TYPES::ACCRETION_VOLUME:
        case eEXPERIMENTAL_VARIABLE_TYPES::EROSION_VOLUME:
          break;
        default:
          msg_error("Unexpected particle modeling variable id %d found in window %s\".",
                    var_id,
                    window->cdi_meas_window->name);
          break;
        }
      }
    }

    //Scan through the existing list and check if any population vars are already present.
    ccDOTIMES(var_index, n_variables) {
      switch(window->var_types[var_index]) {
      case SRI_VARIABLE_PRTCL_NUMBER:
        add_fluid_population_var = 0;
        fluid_population_var_index = var_index;
        break;
      case SRI_VARIABLE_PRTCL_RATE_INBOUND:
        add_inbound_population_var = 0;
        inbound_population_var_index = var_index;
        break;
      case SRI_VARIABLE_PRTCL_RATE_OUTBOUND:
        add_outbound_population_var = 0;
        outbound_population_var_index = var_index;
        break;
      case SRI_VARIABLE_PRTCL_FLUX:
        add_pass_through_population_var = 0;
        pass_through_population_var_index = var_index;
        break;
      default:
        break;
      }
    }

    asINT32 num_to_append = add_fluid_population_var + add_inbound_population_var + add_outbound_population_var + add_pass_through_population_var;

    //If anything required is missing, add it to the var types array.
    if (num_to_append > 0) {
      //Create a new var type array of the needed size.
      SRI_VARIABLE_TYPE* old_var_types = window->var_types;
      window->var_types = xnew SRI_VARIABLE_TYPE[n_variables + num_to_append];

      //Append the additional required var types.
      if (add_fluid_population_var) {
        fluid_population_var_index = n_variables;
        window->var_types[n_variables++] = SRI_VARIABLE_PRTCL_NUMBER;
      }
      if (add_inbound_population_var) {
        inbound_population_var_index = n_variables;
        window->var_types[n_variables++] = SRI_VARIABLE_PRTCL_RATE_INBOUND;
      }
      if (add_outbound_population_var) {
        outbound_population_var_index = n_variables;
        window->var_types[n_variables++] = SRI_VARIABLE_PRTCL_RATE_OUTBOUND;
      }
      if (add_pass_through_population_var) {
        pass_through_population_var_index = n_variables;
        window->var_types[n_variables++] = SRI_VARIABLE_PRTCL_FLUX;
      }
      //Copy the origional variables to the enlarged list.
      ccDOTIMES(var_index, window->n_variables) {
        window->var_types[var_index] = old_var_types[var_index];
      }
      window->n_variables = n_variables;
      window->compute_bytes_per_frame();
      delete [] old_var_types;
    }

    //Initialize the population variable indices for all var types.
    sINT16 *population_var_indices = xnew sINT16[n_variables];
    window->population_var_indices = population_var_indices;
    ccDOTIMES(var_index, window->n_variables) {
      population_var_indices[var_index] = -1;
      SRI_VARIABLE_TYPE var_id = window->var_types[var_index];
      if (sri_is_particle_tracking_fluid_mean_vartype(var_id)) {
        switch(window->sri_file_type) {
        case SRI_FLUID_TYPE:
        case SRI_COMPOSITE_FLUID_TYPE:
          population_var_indices[var_index] = fluid_population_var_index;
          continue;
        default:break;
        }
      }

      if (sri_is_particle_tracking_inbound_mean_vartype(var_id)) {
        population_var_indices[var_index] = inbound_population_var_index;
        continue;
      }
      if (sri_is_particle_tracking_outbound_mean_vartype(var_id)) {
        population_var_indices[var_index] = outbound_population_var_index;
        continue;
      }

      if (sri_is_particle_tracking_pass_through_mean_vartype(var_id)) {
        population_var_indices[var_index] = pass_through_population_var_index;
        continue;
      }
      //Also check if this is an experimental variable that needs to be averaged by particle population.
      if(cp_info.cvid_helper->is_pm_var_id(var_id)) {
        eEXPERIMENTAL_VARIABLE_TYPES offset =
          (eEXPERIMENTAL_VARIABLE_TYPES)(var_id - cp_info.cvid_helper->get_first_pm_var_id());
        switch(offset) {
        case eEXPERIMENTAL_VARIABLE_TYPES::ACCRETION_VOLUME:
        case eEXPERIMENTAL_VARIABLE_TYPES::EROSION_VOLUME:
        case eEXPERIMENTAL_VARIABLE_TYPES::EVAPORATION_RATE:
          break; //Not poulation averaged
        case eEXPERIMENTAL_VARIABLE_TYPES::MEAN_PARTICLE_TEMP:
        case eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_1:
        case eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_2:
        case eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_3:
          add_fluid_population_var = 1;
          break;
        default:
          msg_error("Unexpected particle modeling variable id %d found in window %s\".",
                    var_id,
                    window->cdi_meas_window->name);
          break;
        }
      }
    }

    //As part of PR41298, Particle Force is filtered out of surface measurments as to not break old cdi files.
    if(window->sri_file_type != SRI_FLUID_TYPE
       && window->sri_file_type != SRI_COMPOSITE_FLUID_TYPE
       && window->sri_file_type != SRI_FLUID_DEV_TYPE) {
      asINT32 filtered_variables = 0;
      ccDOTIMES(var_index, window->n_variables) {
        SRI_VARIABLE_TYPE var_id = window->var_types[var_index];
        if(var_id !=  SRI_VARIABLE_PRTCL_XFORCE &&
           var_id !=  SRI_VARIABLE_PRTCL_YFORCE &&
           var_id !=  SRI_VARIABLE_PRTCL_ZFORCE) {
          window->var_types[filtered_variables++] = var_id;
        }
      }
      window->n_variables = filtered_variables;
    }
  }
}
