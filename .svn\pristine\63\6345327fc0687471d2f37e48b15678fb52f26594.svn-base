/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */

#ifndef _CDI_GEOMETRY_READER_H_INCLUDED
#define _CDI_GEOMETRY_READER_H_INCLUDED

#include <unordered_set>

#include "cdi_export.h"


// cCDI_READER subclass that parses partition, segment, region, and face
// information out of PSDF, RGDF and other chunks, and parses measurement window
// information from MEAS chunks.  These are the basics one needs to build the
// geometry hierarchy for (e.g.) displaying a cQ_GEOM_SELECTION_DLG.
class cCDI_GEOMETRY_READER : public cCDI_READER {
  template<CIO_CCCC CHUNK_TYPE, class READER_TYPE>
  friend class tCDI_CHUNK_PARSER;

 public:
  cCDI_GEOMETRY_READER(cCDI_CHUNK_PARSER_MAP&& parsers,             // additional chunk parsers to call
                       cCDI_PARTITIONS& cdiPartitions,              // written to during Parse
                       cGEOM_COMMON_ENTITY_LIST& entities,          // written to during PostProcess
                       CDI_ENTITY_FILTER_FUNCTION regionFilterFunc, // pass cdi_match_any to disable filtering
                       CDI_ENTITY_FILTER_FUNCTION faceFilterFunc);  // "

  virtual ~cCDI_GEOMETRY_READER() {}

  // Open and parse the specified CDI file.
  bool Parse(const std::string& cdiFile) override;

  // Parse at 'ci' within an already open CDI file.
  void Parse(CDI_INFO ci, cCDI_CHUNK_PARSER* parent = nullptr) override;

  // Fill in m_entities at the conclusion of parsing.
  void PostProcess() override;

  std::unordered_set<cdiINT32> GetRegionsMeasured() { return m_regionsMeasured; }
  std::unordered_set<cdiINT32> GetFacesMeasured() { return m_facesMeasured; }
  std::unordered_set<cdiINT32> GetSolidRegions() { return m_solidRegions; }
  std::unordered_set<cdiINT32> GetFluidRegions() { return m_fluidRegions; }

 protected:
  cCDI_PARTITIONS& m_cdiPartitions;
  std::unordered_set<cdiINT32> m_regionsMeasured;       // regions included in measurement window
  std::unordered_set<cdiINT32> m_facesMeasured;         // faces included in measurement window
  std::unordered_set<cdiINT32> m_solidRegions;          // regions with solid physics
  std::unordered_set<cdiINT32> m_fluidRegions;          // regions with fluid physics
  cGEOM_COMMON_ENTITY_LIST& m_entities;
  CDI_ENTITY_FILTER_FUNCTION m_regionFilterFunc;
  CDI_ENTITY_FILTER_FUNCTION m_faceFilterFunc;
  bool m_foundPSDF = false;
};

#endif // _CDI_GEOMETRY_READER_H_INCLUDED
