/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2007 Exa Corporation.                                   ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#ifndef	CDI_IO_INCLUDE
#define	CDI_IO_INCLUDE

// Static variable used as the default value for uINT64 arguments passed by reference.
static uINT64 dummyBytesReadDefault = 0;

/**
 ** Functions to get and put absolute 32 bit big-endian format,
 ** regardless of the local endian-ness and size of the asINT32
 **
 */

asINT32
cdi_write_asINT32(CDI_INFO cdi_info, /* the info structure */
		              const asINT32 *values,
		              asINT32 count);

asINT32
cdi_read_asINT32(CDI_INFO cdi_info,
		 asINT32 *values,
		 asINT32 count,
		 uINT64& bytesRead = dummyBytesReadDefault);

asINT32
cdi_read_one_asINT32(CDI_INFO cdi_info,
                     uINT64& bytesRead=dummyBytesReadDefault);

VOID
cdi_write_one_asINT32(CDI_INFO cdi_info, asINT32 value);


/**
 ** Functions to get and put absolute 32 bit big-endian format,
 ** regardless of the local endian-ness and size of the asINT32
 **
 */

asINT32
cdi_write_auINT32(CDI_INFO cdi_info, /* the info structure */
		  auINT32 *values,
		  auINT32 count);

asINT32
cdi_read_auINT32(CDI_INFO cdi_info,
		 auINT32 *values,
		 auINT32 count,
                 uINT64& bytesRead=dummyBytesReadDefault);

auINT32
cdi_read_one_auINT32(CDI_INFO cdi_info,
                     uINT64& bytesRead=dummyBytesReadDefault);

VOID
cdi_write_one_auINT32(CDI_INFO cdi_info, auINT32 value);


/**
 ** Functions to get and put absolute 64 bit big-endian format,
 ** regardless of the local endian-ness and size of the sINT64
 **
 */
sINT64
cdi_write_sINT64(CDI_INFO cdi_info, /* the info structure */
		  sINT64 *values,
		  sINT64 count);

sINT64
cdi_read_sINT64(CDI_INFO cdi_info,
                sINT64 *values,
                sINT64 count,
                uINT64& bytesRead=dummyBytesReadDefault);

/**
 ** Functions to get and put ieee double floats (64 bits) in
 ** big-endian format
 **
 */

asINT32
cdi_write_idFLOAT(CDI_INFO cdi_info, /* The info structure */
		  const idFLOAT *values,
		  asINT32 count);
		  
asINT32
cdi_read_idFLOAT(CDI_INFO cdi_info,
		 idFLOAT *values,
		 asINT32 count,
                 uINT64 &bytesRead=dummyBytesReadDefault);

/**
 ** Functions to get and put characters
 **
 */
asINT32
cdi_write_chars(CDI_INFO cdi_info, cSTRING str, asINT32 str_len);

asINT32
cdi_read_chars(CDI_INFO cdi_info, CHARACTER *buf, asINT32 len);

// If binaryData is true, we won't sanity check the length of the string using strlen()
BOOLEAN cdi_write_cdichars(CDI_INFO cdi_info, cSTRING str, 
                           asINT32 str_len, bool binaryData=false);

STRING
cdi_read_cdichars(CDI_INFO cdi_info, CHARACTER *buf, 
                  asINT32 *str_len_ptr, bool binaryData=false);


/**
 ** Functions to get and put string objects (a length followed by the string)
 **
 */
BOOLEAN cdi_write_cdistring(CDI_INFO cdi_info, cSTRING str);
BOOLEAN cdi_read_cdistring(CDI_INFO cdi_info, STRING *str, 
                           uINT64& bytesRead=dummyBytesReadDefault);

BOOLEAN cdi_write_stdstring(CDI_INFO cdi_info, std::string str);
BOOLEAN cdi_read_stdstring(CDI_INFO cdi_info, std::string& str, 
                           uINT64& bytesRead=dummyBytesReadDefault);

/**
 ** Private
 ** Functions to exchange native-endian asINT32 w/big-endian uINT32
 ** and native-endian sINT64  w/big-endian uINT64
 */

#if	uINT64_EXISTS_P == 0
typedef	struct {
  unsigned char rep[8];
}	unsigned_64b;
#else
typedef	uINT64	unsigned_64b;
#endif

uINT32 ne_asINT32_2_be_uINT32(asINT32 value);
uINT64 ne_sINT64_2_be_uINT64(sINT64 value);
asINT32 be_uINT32_2_ne_asINT32(uINT32 value);
sINT64 be_uINT64_2_ne_sINT64(uINT64 value);

uINT32 ne_auINT32_2_be_uINT32(auINT32 value);
auINT32 be_uINT32_2_ne_auINT32(uINT32 inval);
unsigned_64b ne_idFLOAT_2_be_uINT64(idFLOAT value);
idFLOAT be_uINT64_2_ne_idFLOAT(unsigned_64b value);
#endif 

