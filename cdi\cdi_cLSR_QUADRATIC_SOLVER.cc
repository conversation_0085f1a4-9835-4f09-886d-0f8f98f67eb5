/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("cpc.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("cpc.copyright", "78") */ 

#include "cdi_cLSR_QUADRATIC_SOLVER.h"
#include SCALAR_H

#include <iostream>
#include <list>
#include <vector>
#include <math.h>

cLSR_QUADRATIC_SOLVER::cLSR_QUADRATIC_SOLVER()
  : m_aTerm(0.0), m_bTerm(0.0)
{
}

VOID cLSR_QUADRATIC_SOLVER::AddPoint(dFLOAT x, dFLOAT y)
{
  cPOINT point(2);
  point[X] = x;
  point[Y] = y;
  m_points.push_back(point);
}

VOID cLSR_QUADRATIC_SOLVER::Solve(bool pureQuadraticFit)
{
  if (pureQuadraticFit) {
    // PR - 44754.
    // Solver will do a pure Quadratic fit (y = ax^2) ignoring the linear term.
    
    dFLOAT sum40 = CalculateSum(4, 0);
    dFLOAT sum21 = CalculateSum(2, 1);

    m_aTerm = sum21 / sum40;
    m_bTerm = 0.0;

  } else {
    
    // Solver will do a Quadratic fit of the form y = ax^2 + bx.
    dFLOAT sum40 = CalculateSum(4, 0);
    dFLOAT sum30 = CalculateSum(3, 0);
    dFLOAT sum20 = CalculateSum(2, 0);
    dFLOAT sum21 = CalculateSum(2, 1);
    dFLOAT sum11 = CalculateSum(1, 1);

    m_aTerm = (sum21 * sum20 - sum11 * sum30) / (sum40 * sum20 - sum30 * sum30);
    m_bTerm = (sum11 - m_aTerm * sum30) / sum20;

  }
}

dFLOAT cLSR_QUADRATIC_SOLVER::CalculateRSquare() const
{
  dFLOAT totalSumOfSquares = 0.0;
  dFLOAT residualSumOfSquares = 0.0;
  dFLOAT yMean = CalculateSum(0, 1) / static_cast<dFLOAT>(m_points.size());

  cPOINT_CONTAINER::const_iterator it;
  for (it = m_points.begin(); it != m_points.end(); ++it) {
    const cPOINT& point(*it);
    totalSumOfSquares += pow(point[Y] - yMean, 2);
    residualSumOfSquares += pow(point[Y] - CalculateY(point[X]), 2);
  }

  return 1 - residualSumOfSquares / totalSumOfSquares;
}

dFLOAT cLSR_QUADRATIC_SOLVER::CalculateStandardDeviation() const
{
  dFLOAT residualSumOfSquares = 0.0;

  cPOINT_CONTAINER::const_iterator it;
  for (it = m_points.begin(); it != m_points.end(); ++it) {
    const cPOINT& point(*it);
    residualSumOfSquares += pow(point[Y] - CalculateY(point[X]), 2);
  }
  
  return sqrt(residualSumOfSquares / static_cast<dFLOAT>(m_points.size()));
}

dFLOAT cLSR_QUADRATIC_SOLVER::CalculateAverageDeviation() const
{
  dFLOAT aveSumOfSquares = 0.0;

  cPOINT_CONTAINER::const_iterator it;
  for (it = m_points.begin(); it != m_points.end(); ++it) {
    const cPOINT& point(*it);
    if (point[Y] != 0.0) {
      dFLOAT f = (CalculateY(point[X]) - point[Y]) / point[Y];
      aveSumOfSquares += f * f;
    }
  }
    
  return sqrt(aveSumOfSquares / static_cast<dFLOAT>(m_points.size()));
}

dFLOAT cLSR_QUADRATIC_SOLVER::CalculateY(dFLOAT x) const
{
  return GetA() * pow(x, 2) + GetB() * x;
}

dFLOAT cLSR_QUADRATIC_SOLVER::CalculateSum(int xPower, int yPower) const
{
  dFLOAT sum = 0.0;

  cPOINT_CONTAINER::const_iterator it;
  for (it = m_points.begin(); it != m_points.end(); ++it) {
    const cPOINT& point(*it);
    sum += pow(point[X], xPower) * pow(point[Y], yPower);
  }

  return sum;
}
