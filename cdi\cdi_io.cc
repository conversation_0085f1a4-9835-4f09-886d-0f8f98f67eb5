/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#include "cdi_common.h"
#include "cdi_io.h"
#include "cdi_readwrite.h"
#include "cdi_encrypted_io.h"

#include CCUTILS_H
#include CIPHER_H

/**
 ** Functions to exchange native-endian asINT32 w/big-endian uINT32
 ** and native-endian sINT64  w/big-endian uINT64
 */
uINT32
ne_asINT32_2_be_uINT32(asINT32 value) {
  /**
   * In order to avoid aliasing problems, just use the space
   * associated with these values.  Never reference them
   * directly
   */
  sINT32 value32;
  uINT32 rvalue32;
  STRING r = (STRING) &rvalue32;
  STRING v = (STRING) &value32;

  /**
   * Assign the value, cast from asINT32 to sINT32, to the
   * memory pointed to be `v'.  This forces a 32 bit
   * representation
   */
  * (sINT32 *) v = (sINT32) value;

  r[SCALAR_UINT32_BYTE(0)] = v[3];
  r[SCALAR_UINT32_BYTE(1)] = v[2];
  r[SCALAR_UINT32_BYTE(2)] = v[1];
  r[SCALAR_UINT32_BYTE(3)] = v[0];

  return(* (uINT32 *) r);
}

uINT64
ne_sINT64_2_be_uINT64(sINT64 value) {
  /**
   * In order to avoid aliasing problems, just use the space
   * associated with these values.  Never reference them
   * directly
   */
  sINT64 value64;
  uINT64 rvalue64;
  STRING r = (STRING) &rvalue64;
  STRING v = (STRING) &value64;

  /**
   * Assign the value, to the
   * memory pointed to be `v'.  This forces a 64 bit
   * representation
   */
  * (sINT64 *) v = value;

  r[SCALAR_UINT32_BYTE(0)] = v[7];
  r[SCALAR_UINT32_BYTE(1)] = v[6];
  r[SCALAR_UINT32_BYTE(2)] = v[5];
  r[SCALAR_UINT32_BYTE(3)] = v[4];
  r[SCALAR_UINT32_BYTE(4)] = v[3];
  r[SCALAR_UINT32_BYTE(5)] = v[2];
  r[SCALAR_UINT32_BYTE(6)] = v[1];
  r[SCALAR_UINT32_BYTE(7)] = v[0];

  return(* (uINT64 *) r);
}

asINT32
be_uINT32_2_ne_asINT32(uINT32 value) {
  sINT32 rvalue32;		/* Signed 32 bit value */
  STRING r = (STRING) &rvalue32;
  STRING v = (STRING) &value;

  r[SCALAR_UINT32_BYTE(0)] = v[3];
  r[SCALAR_UINT32_BYTE(1)] = v[2];
  r[SCALAR_UINT32_BYTE(2)] = v[1];
  r[SCALAR_UINT32_BYTE(3)] = v[0];

  return((asINT32) rvalue32);
}

sINT64
be_uINT64_2_ne_sINT64(uINT64 value) {
  sINT64 rvalue64;		/* Signed 64 bit value */
  STRING r = (STRING) &rvalue64;
  STRING v = (STRING) &value;

  r[SCALAR_UINT64_BYTE(0)] = v[7];
  r[SCALAR_UINT64_BYTE(1)] = v[6];
  r[SCALAR_UINT64_BYTE(2)] = v[5];
  r[SCALAR_UINT64_BYTE(3)] = v[4];
  r[SCALAR_UINT64_BYTE(4)] = v[3];
  r[SCALAR_UINT64_BYTE(5)] = v[2];
  r[SCALAR_UINT64_BYTE(6)] = v[1];
  r[SCALAR_UINT64_BYTE(7)] = v[0];

  return((sINT64) rvalue64);
}

#if	uINT64_EXISTS_P == 0
typedef	struct {
  unsigned char rep[8];
}	unsigned_64b;
#else
typedef	uINT64	unsigned_64b;
#endif

/**
 ** Functions to exchange native-endian auINT32 w/big-endian uINT32
 **
 */
uINT32
ne_auINT32_2_be_uINT32(auINT32 value) {
  /**
   * In order to avoid aliasing problems, just use the
   * space associated with these values.  Never reference
   * them directly
   */
  sINT32 value32;
  uINT32 rvalue32;

  STRING r = (STRING) &rvalue32;
  STRING v = (STRING) &value32;

  /**
   * Assign the value, cast from auINT32 to uINT32, to the
   * memory pointed to be `v'.  This forces a 32 bit
   * representation
   */
  * (uINT32 *) v = (uINT32) value;

  r[SCALAR_UINT32_BYTE(0)] = v[3];
  r[SCALAR_UINT32_BYTE(1)] = v[2];
  r[SCALAR_UINT32_BYTE(2)] = v[1];
  r[SCALAR_UINT32_BYTE(3)] = v[0];

  return(* (uINT32 *) r);
}

auINT32
be_uINT32_2_ne_auINT32(uINT32 inval) {
  uINT32 rvalue32;		/* Unsigned 32 bit value */
  STRING r = (STRING) &rvalue32;
  STRING v = (STRING) &inval;

  r[SCALAR_UINT32_BYTE(0)] = v[3];
  r[SCALAR_UINT32_BYTE(1)] = v[2];
  r[SCALAR_UINT32_BYTE(2)] = v[1];
  r[SCALAR_UINT32_BYTE(3)] = v[0];

  return((auINT32) rvalue32);
}


/**
 ** Function to return a 64 bit, big-endian representation
 ** (stuffed in a 64 bit unsigned value) of an input idFLOAT,
 ** native-endian, value
 */
unsigned_64b
ne_idFLOAT_2_be_uINT64(idFLOAT value) {
  unsigned_64b rvalue;
  unsigned char *r = (unsigned char *) &rvalue;
  unsigned char *v = (unsigned char *) &value;	/* idFLOAT must have 64 bit rep */

  r[SCALAR_IDFLOAT_BYTE(0)] = v[7];
  r[SCALAR_IDFLOAT_BYTE(1)] = v[6];
  r[SCALAR_IDFLOAT_BYTE(2)] = v[5];
  r[SCALAR_IDFLOAT_BYTE(3)] = v[4];
  r[SCALAR_IDFLOAT_BYTE(4)] = v[3];
  r[SCALAR_IDFLOAT_BYTE(5)] = v[2];
  r[SCALAR_IDFLOAT_BYTE(6)] = v[1];
  r[SCALAR_IDFLOAT_BYTE(7)] = v[0];
  
  return(* (unsigned_64b *) r);
}


/**
 ** Function to return a native-endian idFLOAT value,
 ** from a 64 bit unsigned value.
 */
idFLOAT
be_uINT64_2_ne_idFLOAT(unsigned_64b value) {
  
  idFLOAT rvalue;
  unsigned char *r = (unsigned char *) &rvalue;
  unsigned char *v = (unsigned char *) &value;

  r[SCALAR_IDFLOAT_BYTE(0)] = v[7];
  r[SCALAR_IDFLOAT_BYTE(1)] = v[6];
  r[SCALAR_IDFLOAT_BYTE(2)] = v[5];
  r[SCALAR_IDFLOAT_BYTE(3)] = v[4];
  r[SCALAR_IDFLOAT_BYTE(4)] = v[3];
  r[SCALAR_IDFLOAT_BYTE(5)] = v[2];
  r[SCALAR_IDFLOAT_BYTE(6)] = v[1];
  r[SCALAR_IDFLOAT_BYTE(7)] = v[0];

  return(* (idFLOAT *) r);
}


/**
 ** Functions to get and put absolute 32 bit big-endian format,
 ** regardless of the local endian-ness and size of the asINT32
 **
 */
asINT32
cdi_write_auINT32(CDI_INFO cdi_info, /* the info structure */
                  auINT32 *values,
                  auINT32 count) {
  asINT32 write_vals = 0;

  ccDOTIMES(i, count) {
    uINT32 outval = ne_auINT32_2_be_uINT32(values[i]);

    char *outchar = (CHARACTER*) &outval;
    if (cdi_is_encryption_on(cdi_info))
        cdi_get_cipher(cdi_info)->EncryptBytes(outchar, outchar, 4);

    if (cio_write_chars(cdi_info->cio_info, outchar, 4)) {
      write_vals++;
    } else {
      break;
    }
  }

  return(write_vals);
}

 
asINT32
cdi_read_auINT32(CDI_INFO cdi_info,
                 auINT32 *values,
                 auINT32 count,
                 uINT64& bytesRead) {
  asINT32 read_vals = 0;

  ccDOTIMES(i, count) {
    uINT32 inval;
    char *inchar = (char*) &inval;
    if (cio_read_chars(cdi_info->cio_info, inchar, 4)) {
      if (cdi_is_encryption_on(cdi_info))
        cdi_get_cipher(cdi_info)->DecryptBytes(inchar, inchar, 4);
      bytesRead += 4;
      values[i] = be_uINT32_2_ne_auINT32(inval);
      read_vals++;
    } else {
      break;
    }
  }

  return(read_vals);
}

auINT32
cdi_read_one_auINT32(CDI_INFO cdi_info, uINT64& bytesRead)
{
  auINT32 retval;
  cdi_read_auINT32(cdi_info, &retval, 1, bytesRead);
  return retval;
}

VOID
cdi_write_one_auINT32(CDI_INFO cdi_info, auINT32 value)
{
  cdi_write_auINT32(cdi_info, &value, 1);
}


/**
 ** Functions to get and put absolute 32 bit big-endian format,
 ** regardless of the local endian-ness and size of the asINT32
 **
 */
asINT32
cdi_write_asINT32(CDI_INFO cdi_info, /* the info structure */
		              const asINT32 *values,
		              asINT32 count)
{
  asINT32 write_vals = 0;

  ccDOTIMES(i, count) {
    uINT32 outval = ne_asINT32_2_be_uINT32(values[i]);

    char *outchar = (CHARACTER*) &outval;
    if (cdi_is_encryption_on(cdi_info)) {
      cdi_get_cipher(cdi_info)->EncryptBytes(outchar, outchar, 4);
    }
    if (cio_write_chars(cdi_info->cio_info, outchar, 4)) {
      write_vals++;
    } else {
      break;
    }
  }

  return(write_vals);
}

asINT32
cdi_read_asINT32(CDI_INFO cdi_info,
    asINT32 *values,
    asINT32 count,
    uINT64& bytesRead)
{
  asINT32 read_vals = 0;

  ccDOTIMES(i, count) {
    uINT32 inval;
    char *inchar = (char*) &inval;
    if (cio_read_chars(cdi_info->cio_info, inchar, 4)) {
      if (cdi_is_encryption_on(cdi_info)) {
        cdi_get_cipher(cdi_info)->DecryptBytes(inchar, inchar, 4);
      }
      bytesRead += 4;
      values[i] = be_uINT32_2_ne_asINT32(inval);
      read_vals++;
    } else {
      break;
    }
  }

  return(read_vals);
}

asINT32
cdi_read_one_asINT32(CDI_INFO cdi_info, uINT64& bytesRead)
{
  asINT32 retval;
  cdi_read_asINT32(cdi_info, &retval, 1, bytesRead);
  return retval;
}

VOID
cdi_write_one_asINT32(CDI_INFO cdi_info, asINT32 value)
{
  cdi_write_asINT32(cdi_info, &value, 1);
}

/**
 ** Functions to get and put absolute 64 bit big-endian format,
 ** regardless of the local endian-ness and size of the sINT64
 **
 */
sINT64
cdi_write_sINT64(CDI_INFO cdi_info, /* the info structure */
		  sINT64 *values,
		  sINT64 count)
{
  sINT64 write_vals = 0;

  ccDOTIMES(i, count) {
    uINT64 outval = ne_sINT64_2_be_uINT64(values[i]);

    char *outchar = (CHARACTER*) &outval;
    if (cdi_is_encryption_on(cdi_info))
      cdi_get_cipher(cdi_info)->EncryptBytes(outchar, outchar, 8);

    if (cio_write_chars(cdi_info->cio_info, outchar, 8)) {
      write_vals++;
    } else {
      break;
    }
  }

  return(write_vals);
}

sINT64
cdi_read_sINT64(CDI_INFO cdi_info,
                sINT64 *values,
                sINT64 count,
                uINT64& bytesRead)
{
  sINT64 read_vals = 0;

  ccDOTIMES(i, count) {
    uINT64 inval;
    if (cio_read_chars(cdi_info->cio_info, (CHARACTER *) &inval, 8)) {
      bytesRead += 8;
      values[i] = be_uINT64_2_ne_sINT64(inval);
      read_vals++;
    } else {
      break;
    }
  }

  return(read_vals);
}

/**
 ** Functions to get and put ieee double floats (64 bits) in
 ** big-endian format
 **
 */
asINT32
cdi_write_idFLOAT(CDI_INFO cdi_info,	/* The info structure */
		  const idFLOAT *values,
		  asINT32 count)
{
  asINT32 write_vals = 0;

  ccDOTIMES(i, count) {

    unsigned_64b outval = ne_idFLOAT_2_be_uINT64(values[i]);
				/* Create big-endian representation
				of native-endian idFLOAT input
				value. */
    char *outchar = (CHARACTER*) &outval;
    if (cdi_is_encryption_on(cdi_info))
        cdi_get_cipher(cdi_info)->EncryptBytes(outchar, outchar, 8);
    if (cio_write_chars(cdi_info->cio_info, outchar, 8)) {
      write_vals++;
    } else {
      break;
    }
  }

  return(write_vals);
}


asINT32
cdi_read_idFLOAT(CDI_INFO cdi_info,
		 idFLOAT *values,
		 asINT32 count,
                 uINT64& bytesRead)
{
  asINT32 read_vals = 0;

  ccDOTIMES(i, count) {
    unsigned_64b inval;

    char *inchar = (CHARACTER*) &inval;
    if (cio_read_chars(cdi_info->cio_info, inchar, 8)) {
      if (cdi_is_encryption_on(cdi_info))
        cdi_get_cipher(cdi_info)->DecryptBytes(inchar, inchar, 8);
      bytesRead += 8;
      values[i] = be_uINT64_2_ne_idFLOAT(inval);
      read_vals++;
    } else {
      break;
    }
  }

  return(read_vals);
}


/**
 ** Functions to get and put characters
 **
 */
asINT32
cdi_write_chars(CDI_INFO cdi_info, cSTRING str, asINT32 str_len)
{
    assert(str_len > 0 ? str!=NULL : 1);
    char *inchar = (CHARACTER*) str;
    if (cdi_is_encryption_on(cdi_info))
      cdi_get_cipher(cdi_info)->EncryptBytes(inchar, inchar, str_len);

    return((asINT32) cio_write_chars(cdi_info->cio_info, str, str_len));
}

asINT32
cdi_read_chars(CDI_INFO cdi_info, CHARACTER *buf, asINT32 len)
{
  assert(len > 0 ? buf!=NULL : 1);

  asINT32 nval = cio_read_chars(cdi_info->cio_info, buf, len);
  
  if (cdi_is_encryption_on(cdi_info))
    cdi_get_cipher(cdi_info)->EncryptBytes(buf, buf, nval);

  return (asINT32) nval;
}


BOOLEAN
cdi_write_cdichars(CDI_INFO cdi_info, cSTRING str, asINT32 str_len, bool binaryData)
{
  assert(str_len > 0 ? str!=NULL : 1);

  if (!binaryData) {
    // No encryption
    if (str && (strlen(str) < (uINT32) str_len))
      str_len = (sINT32)strlen(str);
  }

  if (str_len > 0) {
    return((cdi_write_asINT32(cdi_info, &str_len, 1)
	    && cdi_write_chars(cdi_info, str, str_len)) ? TRUE : FALSE);
  }
  else {
    return cdi_write_asINT32(cdi_info, &str_len, 1);
  }
}

STRING
cdi_read_cdichars(CDI_INFO cdi_info, CHARACTER *buf, asINT32 *str_len_ptr, bool binaryData)
{
  // No encryption
  bool bufAllocatedLocally = false;
  if (cdi_read_asINT32(cdi_info, str_len_ptr, 1)) {
    if (!buf) {
      buf = (char *)exa_calloc((*str_len_ptr)+1, sizeof(char), __FILE__, "cdi_read_cdichars");
      bufAllocatedLocally = true;
    }

    if (cdi_read_chars(cdi_info, buf, *str_len_ptr) == *str_len_ptr) {
      buf[*str_len_ptr] = (char) NULL;
    } else {
      return(NULL);
    }
  } else {
    return(NULL);
  }

  if (!binaryData &&
      !CDI_VERSION_AT_LEAST(cdi_info->major_version, cdi_info->minor_version, 5, 1) && 
      bufAllocatedLocally) {
    std::string convertedString = EXA_STR::ConvertFromLatin1ToUtf8(buf);
    exa_free(buf);
    buf = EXA_STRDUP(convertedString.c_str());
    *str_len_ptr = strlen(buf);
  }
  return buf;
}


/**
 ** Functions to get and put string objects (a length followed by the string)
 **
 */
BOOLEAN cdi_write_cdistring(CDI_INFO cdi_info,
			    cSTRING str)
{
  assert(str!=NULL);
  return cdi_write_cdichars(cdi_info, str, (asINT32) strlen(str));
}

BOOLEAN cdi_read_cdistring(CDI_INFO cdi_info,
			   STRING *str,
                           uINT64& bytesRead)
{
  asINT32 str_len;
  if (cdi_read_asINT32(cdi_info, &str_len, 1, bytesRead) != 1)
    return FALSE;
  *str = EXA_CALLOC_ARRAY(char, (str_len + 1));
  if (*str == NULL)
    return FALSE;
  if (cdi_read_chars(cdi_info, *str, str_len) != str_len)
    return FALSE;

  bytesRead += str_len;
  (*str)[str_len] = (char) NULL;
  if (!CDI_VERSION_AT_LEAST(cdi_info->major_version, cdi_info->minor_version, 5, 1)) {
    std::string convertedString = EXA_STR::ConvertFromLatin1ToUtf8(*str);
    exa_free(*str);
    *str = EXA_STRDUP(convertedString.c_str());

    // Note -- the length of the string may be different now than the number of bytes read. We
    // intentionally do *not* adjust bytesRead because that variable is used to track (in some
    // places) whether there's more to read in the chunk. The fact that we translated the string
    // doesn't affect how many bytes have been read.
  }
  return TRUE;
}

BOOLEAN cdi_write_stdstring(CDI_INFO cdi_info, std::string str)
{
  return cdi_write_cdistring(cdi_info, str.c_str());
}

BOOLEAN cdi_read_stdstring(CDI_INFO cdi_info, std::string& str, 
                           uINT64& bytesRead)
{
  STRING s;
  BOOLEAN result = cdi_read_cdistring(cdi_info, &s, bytesRead);
  if (result) {
    str = s;
    exa_free(s);
  }
  return result;
}
