/* ~~~CO<PERSON>Y<PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#ifndef TIMESTEP_SUBCYCLING_H_
#define TIMESTEP_SUBCYCLING_H_

#include "common.h"

TIMESTEP round_up_time  (dFLOAT t, dFLOAT ct);
TIMESTEP round_down_time(dFLOAT t, dFLOAT ct);
VOID maybe_adjust_realms_ts_ratio(asINT32 &n_lb_base_steps, asINT32 &n_conduction_base_steps);

VOID create_initial_solver_mask();
VOID assign_solver_timesteps();
VOID get_lighthill_solver_start_time();
VOID assign_momentum_freeze_params();

class sLCM_STEPS_REALMS {
 public:
  //Constructor fills the lcm base timesteps using cp_info, no need for inputs
  sLCM_STEPS_REALMS();
  //Sets ratio
  VOID set_ratio(double& cond_over_flow_ratio, bool bound_by_subcycled = false);
  //Return reference timestep periodicities
  TIMESTEP base() const { return lcm(m_flow_scale * m_lb_ref, m_cond_scale * m_cond_ref); }
  TIMESTEP flow() const { return m_flow_scale * m_flow_ref; } 
  TIMESTEP cond() const { return m_cond_scale * m_cond_ref; }
  //Return finest scale timestep periodicities, 
  TIMESTEP lb_base_steps() const { return m_flow_scale * m_lb_ref / m_coupled_scales_factor; }
  TIMESTEP cond_base_steps() const { return cond() / m_coupled_scales_factor; }
  
 private:
  //realm and solver reference base periodicities (realm_ref) and scales (realm_scale), based on the solver
  //ratios when advancing at same rate (realm_base_steps) such that realm_base_steps = realm_scale * realm_ref
  TIMESTEP m_lb_ref; 
  TIMESTEP m_flow_ref;
  TIMESTEP m_cond_ref;
  //The reference periodicity remains constant through the simulation, but the scale can be adjusted to accelerate/slow
  //the realm being supercycled. Thus, we store the scale corresponding to cond_over_flow_ratio=1 as reference
  TIMESTEP m_flow_scale_ref;
  TIMESTEP m_cond_scale_ref;
  //And for each phase compute the actual scale of a time coupling phase based on cond_over_flow_ratio
  //(needs to be an integer, since we scale in multiples of the flow or cond reference)
  TIMESTEP m_flow_scale;  
  TIMESTEP m_cond_scale;
  //Scale multiplying factor needed to determine the periodicity of the coarsest coupled scale
  TIMESTEP m_coupled_scales_factor;
};

#endif /*TIMESTEP_SUBCYCLING_H_*/
