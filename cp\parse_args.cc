/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Standard Argument list parsing
 *
 * Jim Salem, Exa Corporation 
 * Created Sat Jun  4 1994
 *--------------------------------------------------------------------------*/

#include "common.h"
#include "parse_args.h"

/*--------------------------------------------------------------------------*
 * Parsing utilities
 *--------------------------------------------------------------------------*/

/* This removes the numbered argument (arg 0 is the first one) from the
 * argument list and returns it. 
 * If there was no such argument, NULL is returned.
 */
cSTRING parse_arg_remove(asINT32 arg_number, int *argc, char *argv[])
{
  cSTRING retval;

  if (arg_number < 0 || arg_number >= *argc) return NULL;

  retval = argv[arg_number];

  -- *argc;
  memmove(argv + arg_number, argv + arg_number + 1 , 
	  (*argc - arg_number) * sizeof(char *));

  return retval;
}

/* This returns TRUE if the argument is present in the argument list.
 * If it is present, it removes the argument from the argument list by
 * modifing argc and argv.
 */
BOOLEAN parse_arg_present_p(cSTRING argname, int *argc, char *argv[])
{
  ccDOTIMES(i, *argc) {
    if (!strcmp(argv[i], argname)) {
      /* Found one! */
      parse_arg_remove(i, argc, argv);
      return TRUE;
    }
  }
  return FALSE;
}
/* This returns TRUE if the argument is present in the argument list.
 */
BOOLEAN parse_arg_present(cSTRING argname, int *argc, char *argv[])
{
  ccDOTIMES(i, *argc) {
    if (!strcmp(argv[i], argname)) {
      /* Found one! */
      return TRUE;
    }
  }
  return FALSE;
}

/* Same as parse_arg_present_p, but stops checking if a second argument is
 * found 
 */
BOOLEAN parse_arg_present_until(cSTRING argname, cSTRING argstop, int *argc, char *argv[])
{
  ccDOTIMES(i, *argc) {
    if (!strcmp(argv[i], argstop)) {
      break;
    }
    if (!strcmp(argv[i], argname)) {
      /* Found one! */
      parse_arg_remove(i, argc, argv);
      return TRUE;
    }
  }
  return FALSE;
}

/* This looks for an argument of the form: <argname> <argvalue>.  If the
 * argument is present, <argvalue> is returned, otherwise NULL is returned.
 * If it is present, it removes both argname and argvalue from the argument
 * list by modifing argc and argv.
 */
cSTRING parse_arg_string(cSTRING argname, int *argc, char *argv[])
{
  ccDOTIMES(i, *argc-1) {
    if (!strcmp(argv[i], argname)) {
      /* Found it! */
      parse_arg_remove(i, argc, argv);
      return parse_arg_remove(i, argc, argv);
    }
  }
  return NULL;
}

/* Same as parse_arg_string, but takes a stopping argument.
 */
cSTRING parse_arg_string_until(cSTRING argname, cSTRING argstop, int *argc, char *argv[])
{
  ccDOTIMES(i, *argc-1) {
    if (!strcmp(argv[i], argstop)) {
      break;
    }
    if (!strcmp(argv[i], argname)) {
      /* Found it! */
      parse_arg_remove(i, argc, argv);
      return parse_arg_remove(i, argc, argv);
    }
  }
  return NULL;
}

/* This looks for an argument of the form: <argname> <argvalue>.  If the
 * argument is present, <argvalue> is returned, otherwise 0xFFFFFFFF is 
 * returned. If it is present, this function removes both argname and  
 * argvalue from the argument list by modifing argc and argv.
 */
uINT32 parse_arg_uINT32(cSTRING argname, int *argc, char *argv[])
{
  ccDOTIMES(i, *argc-1) {
    if (!strcmp(argv[i], argname)) {
      /* Found it! */
      parse_arg_remove(i, argc, argv);
      return (uINT32) strtoul (parse_arg_remove(i, argc, argv), (char **)NULL, 10);
    }
  }
  return 0xFFFFFFFF;
}

BOOLEAN parse_arg_sINT32(cSTRING argname, sINT32 &result, int *argc, char *argv[])
{
  ccDOTIMES(i, *argc-1) {
    if (!strcmp(argv[i], argname)) {
      /* Found it! */
      parse_arg_remove(i, argc, argv);
      result = strtoul (parse_arg_remove(i, argc, argv), (char **)NULL, 10);
      return TRUE;
    }
  }
  return FALSE;
}

BOOLEAN parse_arg_sFLOAT(cSTRING argname, sFLOAT &result, int *argc, char *argv[])
{
  ccDOTIMES(i, *argc-1) {
    if (!strcmp(argv[i], argname)) {
      /* Found it! */
      parse_arg_remove(i, argc, argv);
      result = strtod (parse_arg_remove(i, argc, argv), (char **)NULL);
      return TRUE;
    }
  }
  return FALSE;
}


//returns TRUE if the string is int
static BOOLEAN string_is_int(char *string)
{
  if (string == NULL)
    return FALSE;
  asINT32 len = strlen(string);
  if (len == 0)
    return FALSE;
  //this is done to avoid platform dependency on the eos
  ccDOTIMES(i, len) {
    if (!isdigit(string[i]))
      return FALSE;
  }
  return TRUE;
}

//This looks for an argument of the form: <argname> <argvalue-string> <argvalue-int>.
//with the optional argvalue-int. If the argvalue-int is not found, *p_n is set to -1
//If the argvalue-string is present, <argvalue-string> is returned, otherwise NULL 
//is returned. 
//If the arguments are present, all three of them are removed from the argv list
cSTRING parse_arg_string_and_optnl_asINT32(cSTRING argname, 
					   int *argc, 
					   char *argv[],
					   asINT32 *p_n)
{
  ccDOTIMES(i, *argc-1) {
    if (!strcmp(argv[i], argname)) {
      /* Found it! */
      parse_arg_remove(i, argc, argv);
      cSTRING ret_string = parse_arg_remove(i, argc, argv);
      if (string_is_int(argv[i]))
	*p_n = (asINT32) strtoul (parse_arg_remove(i, argc, argv), (char **)NULL, 10);
      else 
	*p_n = -1;
      return ret_string;
    }
  }
  return NULL;
}

/*--------------------------------------------------------------------------*
 * Standard arguments
 *--------------------------------------------------------------------------*/

/* This string contains a description of the "standard" options, suitable
 * for printing in a "Usage:..." message */
cSTRING std_args_description_string =
"Standard debugging options: \n"
" -malloc_check          Check all allocations for errors\n"
" -malloc_verify         Verify heap on all allocations\n"
" -no_random             Disables all random number generators\n"
;

/* This parses an argument list for the "standard" arguments and fills in 
 * a static STD_ARGS structure.  It returns an arglist with the standard
 * arguments removed.  
 */
STD_ARGS parse_std_args(int *argc, char *argv[])
{
  static sSTD_ARGS args;

  /* Initialize args structure */
  args.debug_malloc_p 	= FALSE;
  args.disable_random_p = FALSE;

  /* Parse the arguments */
  if (parse_arg_present_p("-malloc_check", argc, argv)) {
    args.debug_malloc_p = TRUE;
    args.debug_malloc_mode = EXA_MALLOC_CHECK_PTR;
  }
  if (parse_arg_present_p("-malloc_verify", argc, argv)) {
    args.debug_malloc_p = TRUE;
    args.debug_malloc_mode = EXA_MALLOC_VERIFY_HEAP;
  }

  if (parse_arg_present_p("-no_random", argc, argv)) {
    args.disable_random_p = TRUE;
    msg_warn("The -no_random flag does not ensure identical results across"
	     " configurations with different numbers of SPs (due to variations"
	     " in the order of floating point operations). To ensure identical"
	     " results, you must compile with the RESULTS_INDEPENDENT_OF_NUM_SPS"
	     " flag set.");
  }

  /* Return the arguments */
  return &args;
}

std::string convert_to_ordinal(std::size_t i) {
  std::string dictionary[] = {"zeroth",
                              "first", 
                              "second", 
                              "third",
                              "th"};
  if(i < 4)
    return dictionary[i];
  
  std::ostringstream result;
  result << i << dictionary[4];
  return result.str();
}
