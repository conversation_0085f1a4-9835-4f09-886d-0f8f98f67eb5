/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * This file describes the Dynamic Scalar Multiplier structure
 *
 * Dalon Work, Exa Corporation 
 * Created May 25, 2017 
 *--------------------------------------------------------------------------*/

#ifndef __DSM_READER_H
#define __DSM_READER_H

#include "common.h"
// for creating mapping between ublk IDs and meas cell indices for reading
// dynamic scalar multipliers for 5G code.
typedef class cDSM_READER {
  std::string   m_dsm_data_filename;
  TIMESTEP      m_last_dsm_read_timestep;

  asINT32       m_meas_window_index;
  cSTRING       m_meas_window_name;
  asINT8        m_is_per_voxel;

  // This maps the ublk id to meas cell indices for dsm meas file
  std::vector<STP_MEAS_CELL_INDEX> m_ublk_to_mci;

  std::vector<std::vector<sFLOAT> > m_data_per_sp;
  // Stores the MPI send requests which may be pending and have to 
  // be cancelled during finalize
  MPI_Request *m_send_requests;

  // prevent copying
  cDSM_READER(const cDSM_READER&);

  public:

  cDSM_READER();
  ~cDSM_READER();
  void read_dsm_data_and_send_to_sps();
  void add_voxel_based_mci(STP_UBLK_ID ublk_id,
                           auINT32 meas_cell_voxel_mask,
                           STP_MEAS_CELL_INDEX meas_cell_index,
                           asINT32 cube_offset);
  void add_ublk_based_mci(STP_UBLK_ID ublk_id,
                          STP_MEAS_CELL_INDEX meas_cell_index, 
                          asINT32 cube_offset);
  void cancel_pending_send_requests();

  asINT32 get_meas_window_index() { return m_meas_window_index; }
  void set_meas_window_index(asINT32 index) { m_meas_window_index = index; }

  BOOLEAN is_meas_window_set() { return m_meas_window_name != NULL; }
  cSTRING get_meas_window_name() { return m_meas_window_name; }
  void set_meas_window_name(cSTRING name) { m_meas_window_name = name; }

  void set_last_dsm_read_timestep(TIMESTEP ts) { m_last_dsm_read_timestep = ts; }
  TIMESTEP get_last_dsm_read_timestep() { return m_last_dsm_read_timestep; }
  void add_null_ublk_entry();

  void set_filename(std::string binfilename) { m_dsm_data_filename = binfilename; }

} *DSM_READER;

#endif
