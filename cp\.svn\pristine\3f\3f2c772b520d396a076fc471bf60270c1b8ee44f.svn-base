/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * The basic CP data structure support
 *
 * Jim Salem, Exa Corporation 
 * Created Sat Jun  4 1994
 *--------------------------------------------------------------------------*/


#include "common.h"
#include "cp_lattice.h"
#include "cp_info.h"

sCDI_DATA cdi_data;

sCP_INFO cp_info = { 0 };

BOOLEAN cp_info_has_been_initialized_p = FALSE;

/*--------------------------------------------------------------------------*
 * Basic initialization
 *--------------------------------------------------------------------------*/

#define DEFAULT_NOSLEEP_SPCOUNT 512

VOID cp_info_basic_init() 
{
  if (!cp_info_has_been_initialized_p) {
    // cp_info.cp_status = SIM_STATUS_UNKNOWN;
    cp_info_has_been_initialized_p = TRUE;
 
    cSTRING nosleep_spcount = getenv("EXA_NOSLEEP_SPCOUNT");
    if(nosleep_spcount != NULL) {
      cp_info.nosleep_spcount = atol(nosleep_spcount);
      msg_print("User-specified no-sleep SP count is %d",cp_info.nosleep_spcount);
    } else {
      cp_info.nosleep_spcount = DEFAULT_NOSLEEP_SPCOUNT;
    }
  }
}
 

/*--------------------------------------------------------------------------*
 * Initialization Support
 *--------------------------------------------------------------------------*/

VOID cp_info_set_sri_lattice_translations()
{
  UNITS_STATUS units_status;
  double slope, offset;
  UNITS_UNIT from_unit, to_unit;
#define get_unit(unit, name) 									\
  if ((units_status = units_parse_unit(cp_info.units_db, name, &unit)) != UNITS_STATUS_OK)	\
    msg_error("UNITS failure parsing " name ": %s", units_error_string(units_status))

#define get_conversion_coefficients(from_name, to_name)									\
  get_unit(from_unit, from_name);											\
  get_unit(to_unit, to_name);												\
  if ((units_status = units_conversion_coefficients(cp_info.units_db, from_unit, to_unit, &slope, &offset))		\
      != UNITS_STATUS_OK) {												\
    if (units_status == UNITS_STATUS_UNDEFINED_CHANGEABLE)								\
      /* PowerCASE omits some changeable unit definitions from the CDI file for cases set up in lattice units */	\
      units_set_changeable_unit(cp_info.units_db, from_name, slope = 1.0, offset = 0);					\
    else														\
      msg_error("UNITS failure converting %s to %s: %s", from_name, to_name, units_error_string(units_status));		\
  }

  const int n_translations = 9;

  SRI_LATTICE_TRANSLATION translations = cnew sSRI_LATTICE_TRANSLATION [ n_translations];

  translations[0].name = strdup("LatticeLength");
  get_conversion_coefficients("LatticeLength", "m");
  translations[0].scale = slope;

  translations[1].name = strdup("LatticeDensity");
  get_conversion_coefficients("LatticeDensity", "kg/m^3");
  translations[1].scale = slope;

  translations[2].name = strdup("LatticeTemperature");
  get_conversion_coefficients("LatticeTemperature", "degK");
  translations[2].scale = slope;

  translations[3].name = strdup("LatticeVelocity");
  get_conversion_coefficients("LatticeVelocity", "m/s");
  translations[3].scale = slope;
  
  translations[4].name = strdup("LatticeStaticPressure");
  get_conversion_coefficients("LatticeStaticPressure", "LatticeDynamicPressure");
  translations[4].scale = slope;
  translations[4].offset = offset;

  translations[5].name = strdup("LatticeSpecificEnthalpy");
  get_conversion_coefficients("LatticeSpecificEnthalpy", "joule/kg");
  translations[5].scale = slope; 

  translations[6].name = strdup("LatticeTime");
  get_conversion_coefficients("LatticeTime", "LatticeTimeIncrement");
  translations[6].scale = slope;

  translations[7].name = strdup("timestepFlow");
  get_conversion_coefficients("timestepFlow", "LatticeTimeIncrement");
  translations[7].scale = slope;

  translations[8].name = strdup("timestepConduction");
  get_conversion_coefficients("timestepConduction", "LatticeTimeIncrement");
  translations[8].scale = slope;

  cp_info.sri_translations = translations;
  cp_info.n_sri_translations = n_translations;
}

/*VOID sCP_INFO::copy_vertex_array_from_native_mesh()
{
  if (!cp_info.native_mesh || cp_info.native_mesh->NumVertices() == 0)
    return;

  cMIO_MODELf::cMESH *surfel_brep = cp_info.native_mesh;
  cp_info.vertex_array.reserve(cp_info.native_mesh->NumVertices());
  ccDOTIMES(vindex, cp_info.native_mesh->NumVertices()) {
    cMIO_MODELf::cMESH::sBG_POINT3 vertex = surfel_brep->GetVertexPoint( vindex );
    sVPoint vp = {vertex.X(), vertex.Y(), n_dims == 2 ? 0.0F : vertex.Z()};
    cp_info.vertex_array.push_back(vp);
  }

}*/

sSRI_ENCRYPTION_DATA sCP_DATA_ENCRYPTION::get_sri_encryption_struct() const {
  sSRI_ENCRYPTION_DATA s;
  s.is_encrypted       = is_encrypted;
  s.encryption_version = encryption_version;
  s.protection_ids     = protection_ids;
  return s;
}

VOID sCP_DATA_ENCRYPTION::verify_encryption_version_from_lgi(int lgi_encryption_version) {
   if ( encryption_version != lgi_encryption_version ){
     msg_warn("There was a mismatch in LGI and CDI encryption versions, using LGI encryption version...");
     encryption_version = lgi_encryption_version;
     is_encrypted = encryption_version != 0;
   }
}

VOID sCP_DATA_ENCRYPTION::verify_part_protection_ids_from_lgi(const std::vector<uINT32>& lgi_part_protection_ids) {

  bool is_same = true;

  size_t n_parts = protection_ids.size();
  ccDOTIMES(part_index,n_parts){
    if (protection_ids[part_index] != (int) lgi_part_protection_ids[part_index]) {
      is_same = false;
      break;
    }
  }

  if ( !is_same ) {
    msg_warn("There was a mismatch in LGI and CDI part protection ids, using LGI protection ids...");
    protection_ids.assign(lgi_part_protection_ids.begin(),lgi_part_protection_ids.begin()+n_parts);
  }
}

VOID sCP_DATA_ENCRYPTION::parse_from_cdi_info(const CDI_INFO& cdi_info_obj) {

  //The API from CDI handles older CDI versions
  //We populate encryption data only when required
  is_encrypted = cdi_get_cipher(cdi_info_obj) != nullptr;

  if (is_encrypted) {
    CDI_RGPN rgpn_struct_ptr = cdi_read_rgpn(cdi_info_obj);

    auto cipher = cdi_get_cipher(cdi_info_obj);
    encryption_version = (cipher) ? cipher->GetVersion() : 0;
    //Populate protection ids
    protection_ids.resize(rgpn_struct_ptr->protectionIds.size());
    for (int i = 0; i < rgpn_struct_ptr->protectionIds.size(); i++) {
      protection_ids[i] = (int) rgpn_struct_ptr->protectionIds[i];
    }
  }
}

dFLOAT sADAPTIVE_PARAMS::get_pt_pf_ratio_scale_factor(dFLOAT current_pt_pf_ratio, dFLOAT T_gradient)
{
  assert(gradient_high >= gradient_low);
  if (T_gradient > gradient_high) {
    return (MAX(1 - (T_gradient - gradient_high) * adaptive_down_coeff, ratio_min/current_pt_pf_ratio));      
  }
  if (T_gradient < gradient_low) {
    return (MIN(1 + (gradient_low - T_gradient) * adaptive_up_coeff, ratio_max/current_pt_pf_ratio));
  }
  return 1.0;
}

BOOLEAN sADAPTIVE_PARAMS::update_adaptive_params()
{
  // Convert from lattice units to mks units
  UNITS_UNIT lattice_unit;
  UNITS_UNIT mks_unit; 
  UNITS_STATUS stat;
  dFLOAT duration;
  stat = units_get_default_for_class(cp_info.units_db, "lattice", "Time", &lattice_unit);
  if (stat != UNITS_STATUS_OK) {
    msg_print("Unable to parse lattice units for Time: %s", units_error_string(stat));
    return FALSE;
  }
  stat = units_get_default_for_class(cp_info.units_db, "mks", "Time", &mks_unit);
  if (stat != UNITS_STATUS_OK) {
    msg_print("Unable to parse mks units for Time: %s", units_error_string(stat));
    return FALSE;
  }
  units_convert(cp_info.units_db, total_pt_duration, lattice_unit, mks_unit, &total_pt_duration);

  stat = units_get_default_for_class(cp_info.units_db, "lattice", "TemperaturePerTime", &lattice_unit);
  if (stat != UNITS_STATUS_OK) {
    msg_print("Unable to parse lattice units for TemperaturePerTime: %s", units_error_string(stat));
    return FALSE;
  }

  stat = units_get_default_for_class(cp_info.units_db, "mks", "TemperaturePerTime", &mks_unit);
  if (stat != UNITS_STATUS_OK) {
    msg_print("Unable to parse mks units for TemperaturePerTime: %s", units_error_string(stat));
    return FALSE;
  }
  units_convert(cp_info.units_db, gradient_low, lattice_unit, mks_unit, &gradient_low);

#if DEBUG_ADAPTIVE_COUPLING
  msg_print("adaptive_up_coeff = %f", adaptive_up_coeff);
  msg_print("adaptive_down_coeff = %f", adaptive_down_coeff);
  msg_print("gradient_low = %15.14f", gradient_low);
  msg_print("gradient_high = %f", gradient_high);
  msg_print("ratio_max = %f", ratio_max);
  msg_print("ratio_min = %f", ratio_min);
  msg_print("gradient_percentage_threshold = %f", gradient_percentage_threshold);
  msg_print("use %s temperature", use_max_temp ? "maximal" : "average");
  msg_print("fix total PowerTHERM time");
  if (total_pt_duration < 0)
    msg_print("Total PowerTHERM duration is inferred from total PowerFLOW duration.");
  else
    msg_print("Total PowerTHERM duration is %15.14f seconds.", total_pt_duration);
  msg_print("------------------------------------------");
#endif
  return TRUE;
}


TIMESTEP sCP_INFO::convert_to_ts_flow(TIMESTEP base_time) {
  //phases are in user notion of time, so base_time needs to be scaled before doing the conversion
  return realm_phase_time_info[STP_FLOW_REALM].global_to_realm_timestep(base_time / n_user_base_steps);
}

TIMESTEP sCP_INFO::convert_to_ts_cond(TIMESTEP base_time) {
  //phases are in user notion of time, so base_time needs to be scaled before doing the conversion
  return realm_phase_time_info[STP_COND_REALM].global_to_realm_timestep(base_time / n_user_base_steps);
}

TIMESTEP sCP_INFO::convert_from_ts_flow(TIMESTEP flow_time) {
  return realm_phase_time_info[STP_FLOW_REALM].realm_to_global_timestep(flow_time);
}

TIMESTEP sCP_INFO::convert_from_ts_cond(TIMESTEP cond_time) {
  //phases are in user notion of time, so base_time needs to be scaled before doing the conversion
  return realm_phase_time_info[STP_COND_REALM].realm_to_global_timestep(cond_time);
}