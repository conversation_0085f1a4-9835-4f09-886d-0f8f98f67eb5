#!/bin/csh

# usage: proload case_dir assy_name case_name pro_cmd pro_args

if ($#argv < 4) then
  echo "usage: ${0} case_dir assy_name case_name pro_cmd pro_args"
  exit 1
endif

set dir=$1
set assy=$2
set case=$3

shift
shift
shift

cd $dir
set dir=`echo $cwd`

set pid=$$

set configdir=/tmp/proload${pid}
mkdir $configdir
set trailfile=${configdir}/proload_trail.txt

cd $configdir

# modify the trail file template (make a new copy)

sed -e "s:__dir_name:${dir}:g" \
 -e "s:__assy_name:${assy}:g" \
 -e "s:__case_name:${case}:g" \
 /proj/sw/cdi/010-hoch/proload.template > $trailfile

# run pro-engineer

echo "$* $trailfile"
$* $trailfile

'rm' -f $trailfile

# get out of configdir so it can be deleted
cd $dir
'rmdir' $configdir
