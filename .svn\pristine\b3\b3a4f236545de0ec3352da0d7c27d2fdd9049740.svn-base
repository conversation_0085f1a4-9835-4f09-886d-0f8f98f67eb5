/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */

#include <map>
#include <unordered_set>

#include "cdi_common.h"
#include "cdi_fix_parallel_dev.h"

#include GEOM_COMMON_H


static cdiBOOLEAN cdi_read_dprp(CDI_INFO cdi_info,sCDI_DPRP& dprp,
                                uINT64& bytesRead=dummyBytesReadDefault);

cdiBOOLEAN cdi_begin_write_chunk(CDI_INFO cdi_info, CIO_CCCC cccc)
{
  cdi_push(cdi_info, cccc);
  return TRUE;
}

cdiBOOLEAN cdi_end_write_chunk(CDI_INFO cdi_info)
{
  cdi_pop(cdi_info);
  return TRUE;
}


cdiBOOLEAN cdi_read_face(CDI_INFO cdi_info, CDI_FACE face)
{
  uINT64 bytesRead = 0;

  char facpChunkName[10];
  cio_type_to_string(CDI_CHUNK_TYPE_FACP, facpChunkName);
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    if (!cdi_cio_descend_with_error(cdi_info->cio_info, facpChunkName) == 0)
      return FALSE;
  }

  if (cdi_read_asINT32(cdi_info, &(face->index), 1, bytesRead) != 1 ||
      cdi_read_asINT32(cdi_info, &(face->prop), 1, bytesRead) != 1 ||
      !cdi_read_cdistring(cdi_info, &(face->name), bytesRead))
    return FALSE;

  // *********************************************
  // Two main branches for the remainder of this function.
  //
  // This first one is for files older than 4.0, second one is for 4.0 and
  // newer.
  // *********************************************
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
    char* color;
    asINT32 dispMode;

    if (!cdi_read_cdistring(cdi_info, &color, bytesRead) ||
        cdi_read_asINT32(cdi_info, &(face->material_index), 1, bytesRead) != 1 ||
        cdi_read_asINT32(cdi_info, &dispMode, 1, bytesRead) != 1 ||
        cdi_read_asINT32(cdi_info, &(face->n_front_facets), 1, bytesRead) != 1 ||
        cdi_read_asINT32(cdi_info, &(face->n_back_facets), 1, bytesRead) != 1)
      return FALSE;

    // These two structures are used to track display property data from the old
    // partition views so we can correctly set model view display property data.
    std::vector<char*> mvColors(1, color);
    std::vector<CDI_DISPLAY_MODE> mvDisplayModes(1,(CDI_DISPLAY_MODE)dispMode);

    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 13)) {
      cdiINT32 n_partition_segments = 0;
      if (cdi_read_asINT32(cdi_info, &n_partition_segments, 1, bytesRead) != 1)
        return FALSE;

      ccDOTIMES(i, n_partition_segments) {
        cdiINT32 segment_index, eff_segment_index;
        if (cdi_read_asINT32(cdi_info, &segment_index, 1, bytesRead) != 1 ||
            !cdi_read_cdistring(cdi_info, &color, bytesRead) ||
            cdi_read_asINT32(cdi_info, &dispMode, 1, bytesRead) != 1 ||
            cdi_read_asINT32(cdi_info, &eff_segment_index, 1, bytesRead) != 1)
          return FALSE;

        // With partition enhancements (v8.0)
        //  - Partition index increases by 1 due to base assembly
        //  - Segment index increases by 1 due to root segment
        //  - Effective segment index no longer used but need to store when writing back old versions
        //  - A "-1" segment index in old cases indicates the face is "Same As Parent"
        //    whereas it indicates an excluded face in new files
        if (segment_index >= 0) {
          cCDI_SEGMENT_REF segref(i + 1, segment_index + 1);
          face->psmv.partition_segment_refs.push_back(segref);
        }
        face->psmv.effective_segments.push_back(eff_segment_index);

        mvColors.push_back(color);
        mvDisplayModes.push_back((CDI_DISPLAY_MODE)dispMode);
      }
    }
    face->psmv.n_model_views = mvColors.size();
    face->psmv.mv_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, face->psmv.n_model_views);

    ccDOTIMES(i, face->psmv.n_model_views) {
      face->psmv.mv_display_props[i].color = mvColors[i];
      face->psmv.mv_display_props[i].display_mode = mvDisplayModes[i];
    }
    // NOTE: We don't free the strings in mvColors b/c they've been handed off
    // to the mv_display_props.
  }

  // *********************************************
  // Second branch -- file is 4.0 or newer
  // *********************************************
  else {
    if (cdi_read_asINT32(cdi_info, &(face->material_index), 1, bytesRead) != 1 ||
        cdi_read_asINT32(cdi_info, &(face->n_front_facets), 1, bytesRead) != 1 ||
        cdi_read_asINT32(cdi_info, &(face->n_back_facets), 1, bytesRead) != 1)
      return FALSE;

    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 0)) {
      if (cdi_read_asINT32(cdi_info, &(face->n_layers), 1, bytesRead) != 1)
        return FALSE;
    }

    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 5)) {
      if (cdi_read_asINT32(cdi_info, &(face->thermal_physics), 1, bytesRead) != 1)
        return FALSE;
    }

    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 19)) {
      if (cdi_read_asINT32(cdi_info, &(face->flow_physics_comp), 1, bytesRead) != 1)
        return FALSE;
      if (cdi_read_asINT32(cdi_info, &(face->thermal_physics_comp), 1, bytesRead) != 1)
        return FALSE;
    }

    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
      cdiINT32 pIndex = 0;
      cdi_cio_ascend_with_error(cdi_info->cio_info, facpChunkName, ++pIndex);
      CDI_WITH_INNER_CHUNK(cdi_info) {
        if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_PSMV) {
          cdi_read_psmv(cdi_info, &(face->psmv));
        }
      }
    }
    else {
      cdiINT32 n_partition_segments = 0;
      if (cdi_read_asINT32(cdi_info, &n_partition_segments, 1, bytesRead) != 1)
        return FALSE;

      ccDOTIMES(i, n_partition_segments) {
        cdiINT32 segment_index, eff_segment_index;
        if (cdi_read_asINT32(cdi_info, &segment_index, 1, bytesRead) != 1 ||
            cdi_read_asINT32(cdi_info, &eff_segment_index, 1, bytesRead) != 1)
          return FALSE;

        if (segment_index >= 0) {
          cCDI_SEGMENT_REF segref(i + 1, segment_index + 1);
          face->psmv.partition_segment_refs.push_back(segref);
        }
        face->psmv.effective_segments.push_back(eff_segment_index);
      }

      if (cdi_read_asINT32(cdi_info, &(face->psmv.n_model_views), 1, bytesRead) != 1)
        return FALSE;

      face->psmv.mv_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, face->psmv.n_model_views);
      ccDOTIMES(i, face->psmv.n_model_views) {
        if (!cdi_read_dprp(cdi_info, face->psmv.mv_display_props[i], bytesRead))
          return FALSE;
      }

      // If we haven't read all the info in the chunk, there must be more to
      // read. When realistic look information was introduced to this chunk (and
      // others), we added it here without bumping the CDI version number (so as
      // to not break compatibility with other VIZ versions unnecessarily). So if
      // there's more to read, it must be realistic look info (one per model view).
      if (bytesRead < cio_get_size(cdi_info->cio_info)) {
        ccDOTIMES(i, face->psmv.n_model_views) {
          if (!cdi_read_cdistring(cdi_info,
            &(face->psmv.mv_display_props[i].realisticLook),
            bytesRead))
            return FALSE;
        }
      }
    }
  }

  return TRUE;
}

static cdiBOOLEAN
cdi_write_dprp(CDI_INFO cdi_info,sCDI_DPRP& dprp)
{
  if  (!cdi_write_cdistring(cdi_info, dprp.color) ||
       cdi_write_asINT32(cdi_info, (asINT32*)&(dprp.display_mode), 1) != 1 ||
       cdi_write_idFLOAT(cdi_info, &(dprp.transparency),1) != 1)
    return FALSE;

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    if (!cdi_write_cdistring(cdi_info, dprp.realisticLook ? dprp.realisticLook : ""))
      return FALSE;
  }

 return TRUE;
}

cdiBOOLEAN
static cdi_read_dprp( CDI_INFO cdi_info,sCDI_DPRP& dprp, uINT64& bytesRead)
{
  if (!cdi_read_cdistring(cdi_info, &(dprp.color), bytesRead) ||
      cdi_read_asINT32(cdi_info, (asINT32*)&(dprp.display_mode), 1, bytesRead) != 1 ||
      cdi_read_idFLOAT(cdi_info, &(dprp.transparency), 1, bytesRead) != 1)
    return FALSE;

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    if (!cdi_read_cdistring(cdi_info, &(dprp.realisticLook)))
      return FALSE;
    // Some clients may still be doing a null check for realistic looks
    if (dprp.realisticLook && strlen(dprp.realisticLook) == 0) {
      EXA_FREE(dprp.realisticLook);
    }
  }

  return TRUE;
}

cdiBOOLEAN cdi_write_face(CDI_INFO cdi_info, CDI_FACE face)
{
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info))
    cdi_push(cdi_info, CDI_CHUNK_TYPE_FACP);

  if (cdi_write_asINT32(cdi_info, &(face->index), 1) != 1 ||
      cdi_write_asINT32(cdi_info, &(face->prop), 1) != 1 ||
      !cdi_write_cdistring(cdi_info, face->name))
    return FALSE;
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
    if (!cdi_write_cdistring(cdi_info, face->psmv.mv_display_props[0].color))
      return FALSE;
  }
  if (cdi_write_asINT32(cdi_info, &(face->material_index), 1) != 1)
    return FALSE;
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
    asINT32 display_mode = (asINT32) face->psmv.mv_display_props[0].display_mode;
    if  (cdi_write_asINT32(cdi_info, &(display_mode), 1) != 1)
      return FALSE;
  }
  if (cdi_write_asINT32(cdi_info, &(face->n_front_facets), 1) != 1 ||
      cdi_write_asINT32(cdi_info, &(face->n_back_facets), 1) != 1)
    return FALSE;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 0)) {
    if (cdi_write_asINT32(cdi_info, &(face->n_layers), 1) != 1)
      return FALSE;
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 5)) {
    if (cdi_write_asINT32(cdi_info, &(face->thermal_physics), 1) != 1)
      return FALSE;
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 19)) {
    if (cdi_write_asINT32(cdi_info, &(face->flow_physics_comp), 1) != 1)
      return FALSE;
    if (cdi_write_asINT32(cdi_info, &(face->thermal_physics_comp), 1) != 1)
      return FALSE;
  }

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    cdi_pop(cdi_info);
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_PSMV) {
      cdi_write_psmv(cdi_info, &(face->psmv));
    }
  }
  else {
    cdiINT32 n_partition_segments = face->psmv.effective_segments.size();
    if (cdi_write_asINT32(cdi_info, &n_partition_segments, 1) != 1)
      return FALSE;
    ccDOTIMES(i, n_partition_segments) {
      asINT32 segment_index = -1;
      ccDOTIMES(j, face->psmv.partition_segment_refs.size()) {
        if ((asINT32)(face->psmv.partition_segment_refs[j].m_partitionIndex - 1) == i) {
          // Old cases did not have a root segment so subtract 1
          segment_index = (asINT32)(face->psmv.partition_segment_refs[j].m_segmentIndex - 1);
          break;
        }
      }
      asINT32 effective_segment_index = (asINT32)face->psmv.effective_segments[i];
      if (cdi_write_asINT32(cdi_info, &(segment_index), 1) != 1)
        return FALSE;
      if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
        if (!cdi_write_cdistring(cdi_info, face->psmv.mv_display_props[i + 1].color))
          return FALSE;
        asINT32 display_mode = (asINT32)face->psmv.mv_display_props[i + 1].display_mode;
        if (cdi_write_asINT32(cdi_info, &(display_mode), 1) != 1)
          return FALSE;
      }
      if (cdi_write_asINT32(cdi_info, &(effective_segment_index), 1) != 1)
        return FALSE;
    }

    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
      if (cdi_write_asINT32(cdi_info, &(face->psmv.n_model_views), 1) != 1)
        return FALSE;
      ccDOTIMES(i, face->psmv.n_model_views) {
        if (!cdi_write_dprp(cdi_info, face->psmv.mv_display_props[i]))
          return FALSE;
      }

      ccDOTIMES(i, face->psmv.n_model_views) {
        if (face->psmv.mv_display_props[i].realisticLook == NULL)
          break;  // Must be dealing with a case with no realistic look info

        if (!cdi_write_cdistring(cdi_info, face->psmv.mv_display_props[i].realisticLook))
          return FALSE;
      }
    }
  }

  return TRUE;
}

void cdi_empty_face(CDI_FACE face)
{
  EXA_FREE(face->name);
  cdi_empty_psmv(&face->psmv);
}

cdiBOOLEAN cdi_read_rgnd(CDI_INFO cdi_info, CDI_RGND rgnd)
{
  std::vector<cdiINT32> segIndices;
  std::vector<std::string> mvColors;
  std::vector<CDI_DISPLAY_MODE> mvDisplayModes;
  bool psmvChunkWasRead = false;

  asINT32 count = cio_get_count(cdi_info->cio_info);
  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);

    switch(cio_get_type(cdi_info->cio_info)) {
    case CDI_CHUNK_TYPE_NAME: {
      CDI_NAME name = cdi_read_name(cdi_info);
      rgnd->name = name->name;
      cdi_destroy_name(name);
      break;
    }
    case CDI_CHUNK_TYPE_OFFS: {
      CDI_OFFS offs = cdi_read_offs(cdi_info);
      rgnd->is_offset = offs->offset;
      cdi_destroy_offs(offs);
      break;
    }
    case CDI_CHUNK_TYPE_PRGN: {
      CDI_PRGN prgn = cdi_read_prgn(cdi_info);
      rgnd->tire_index = prgn->tire_index;
      cdi_destroy_prgn(prgn);
      break;
    }
    case CDI_CHUNK_TYPE_BBOX: {
      CDI_BBOX bbox = cdi_read_bbox(cdi_info);
      ccDOTIMES(i, 3) {
        ccDOTIMES(j, 2)
          rgnd->bbox[i][j] = bbox->coord[i][j];
      }
      cdi_destroy_bbox(bbox);
      break;
    }
    case CDI_CHUNK_TYPE_RGNN: {
      cdi_read_region_numeration(cdi_info, &rgnd->rgnn);
      break;
    }
    case CDI_CHUNK_TYPE_SPRG: {
      asINT32 sprg_count = cio_get_count(cdi_info->cio_info);
      ccDOTIMES(sprg_i, sprg_count) {
        cio_descend(cdi_info->cio_info);

        switch(cio_get_type(cdi_info->cio_info)) {
        case CDI_CHUNK_TYPE_RGNS: {
          rgnd->sub_regions = cdi_read_rgns(cdi_info);
          break;
        }
        default:
          break;
        }

        cio_ascend(cdi_info->cio_info);
      }

      break;
    }
    case CDI_CHUNK_TYPE_RGDP: {   // only exists for CDI < 4.0
      sCDI_RGDP rgdp;
      cdi_read_region_display_properties(cdi_info, &rgdp);
      mvColors.insert(mvColors.begin(), rgdp.color);
      mvDisplayModes.insert(mvDisplayModes.begin(), rgdp.display_mode);
      exa_free(rgdp.color);
      break;
    }
    case CDI_CHUNK_TYPE_PSGS: {   // only exists for CDI < 4.0
      asINT32 psgs_count = cio_get_count(cdi_info->cio_info);
      ccDOTIMES(psgs_i, psgs_count) {
        cio_descend(cdi_info->cio_info);

        switch(cio_get_type(cdi_info->cio_info)) {
        case CDI_CHUNK_TYPE_PSEG: {
          asINT32 pseg_count = cio_get_count(cdi_info->cio_info);
          ccDOTIMES(pseg_i, pseg_count) {
            cio_descend(cdi_info->cio_info);

            switch(cio_get_type(cdi_info->cio_info)) {
            case CDI_CHUNK_TYPE_SGID: {
              sCDI_SGID sgid;
              cdi_read_segment_index(cdi_info, &sgid);
              segIndices.push_back(sgid.segment_index);
              break;
            }
            case CDI_CHUNK_TYPE_RGDP: {
              sCDI_RGDP rgdp;
              cdi_read_region_display_properties(cdi_info, &rgdp);
              mvColors.push_back(rgdp.color);
              mvDisplayModes.push_back(rgdp.display_mode);
              exa_free(rgdp.color);
              break;
            }
            default:
              break;
            }

            cio_ascend(cdi_info->cio_info);
          }

          break;
        }
        default:
          break;
        }

        cio_ascend(cdi_info->cio_info);
      }
      cdiINT32 n_partition_segments = segIndices.size();
      // Old cases did not have base assembly so need to assign to root segment
      cCDI_SEGMENT_REF segref(0, 0);
      rgnd->psmv.partition_segment_refs.push_back(segref);
      rgnd->psmv.effective_segments.push_back(-1);
      ccDOTIMES(i, n_partition_segments) {
        segref.m_partitionIndex = i + 1;
        segref.m_segmentIndex = segIndices[i] < 0 ? -1 : segIndices[i] + 1;
        rgnd->psmv.partition_segment_refs.push_back(segref);
        rgnd->psmv.effective_segments.push_back(-1);
      }

      break;
    }
    case CDI_CHUNK_TYPE_PSMV: {
      cdi_read_psmv(cdi_info, &rgnd->psmv);
      psmvChunkWasRead = true;
      break;
    }
    default:
      break;
    }

    cio_ascend(cdi_info->cio_info);
  }

  if (!psmvChunkWasRead) {
    rgnd->psmv.n_model_views = mvColors.size();
    rgnd->psmv.mv_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, rgnd->psmv.n_model_views);
    ccDOTIMES(i, rgnd->psmv.n_model_views) {
      rgnd->psmv.mv_display_props[i].color = EXA_CALLOC_ARRAY(char, (mvColors[i].size() + 1));
      strcpy(rgnd->psmv.mv_display_props[i].color, mvColors[i].c_str());
      rgnd->psmv.mv_display_props[i].display_mode = mvDisplayModes[i];
    }
    // Old cases did not have base assembly so need to assign to root segment
    if (rgnd->psmv.partition_segment_refs.empty()) {
      cCDI_SEGMENT_REF segref(0, 0);
      rgnd->psmv.partition_segment_refs.push_back(segref);
      rgnd->psmv.effective_segments.push_back(-1);
    }
  }

  return TRUE;
}

void cdi_empty_rgnd(CDI_RGND rgnd)
{
  EXA_FREE(rgnd->sub_regions);
  cdi_empty_psmv(&rgnd->psmv);
}

cdiBOOLEAN cdi_read_model_view(CDI_INFO cdi_info, CDI_MDLV mdlv)
{
  uINT64 bytesRead = 0;

  if (!cdi_read_cdistring(cdi_info, &(mdlv->name), bytesRead))
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &(mdlv->partition_index), 1, bytesRead) != 1)
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &(mdlv->color_ref_index), 1, bytesRead) != 1)
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &(mdlv->disp_mode_ref_index), 1, bytesRead) != 1)
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &(mdlv->n_partition_segments), 1, bytesRead) != 1)
    return FALSE;

  mdlv->segment_display_props = NULL;
  if (mdlv->n_partition_segments > 0) {
    mdlv->segment_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, mdlv->n_partition_segments);
    ccDOTIMES(i, mdlv->n_partition_segments)
      cdi_read_dprp(cdi_info,mdlv->segment_display_props[i], bytesRead);
  }

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
    // If we haven't read all the info in the chunk, there must be more to
    // read. When realistic look information was introduced to this chunk (and
    // others), we added it here without bumping the CDI version number (so as to
    // not break compatibility with other VIZ versions unnecessarily). So if
    // there's more to read, it must be realistic look info (one per model view).
    if (bytesRead < cio_get_size(cdi_info->cio_info)) {
      ccDOTIMES(i, mdlv->n_partition_segments) {
        if (!cdi_read_cdistring(cdi_info,
                                &(mdlv->segment_display_props[i].realisticLook),
                                bytesRead))
          return FALSE;
      }
    }
  }

  mdlv->n_partial_parts = 0;
  mdlv->partial_display_props = NULL;
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    if (cdi_read_asINT32(cdi_info, &(mdlv->n_partial_parts), 1) != 1)
      return FALSE;

    if (mdlv->n_partial_parts > 0) {
      mdlv->partial_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, mdlv->n_partial_parts);
      ccDOTIMES(i, mdlv->n_partial_parts)
        cdi_read_dprp(cdi_info, mdlv->partial_display_props[i], bytesRead);
    }
  }

  return TRUE;
}

cdiBOOLEAN cdi_write_model_view(CDI_INFO cdi_info, CDI_MDLV mdlv)
{
  if (!cdi_write_cdistring(cdi_info, mdlv->name) ||
      cdi_write_asINT32(cdi_info, &(mdlv->partition_index), 1) != 1 ||
      cdi_write_asINT32(cdi_info, &(mdlv->color_ref_index), 1) != 1 ||
      cdi_write_asINT32(cdi_info, &(mdlv->disp_mode_ref_index), 1) != 1 ||
      cdi_write_asINT32(cdi_info, &(mdlv->n_partition_segments), 1) != 1)
    return FALSE;

  ccDOTIMES(i, mdlv->n_partition_segments)
    cdi_write_dprp(cdi_info,mdlv->segment_display_props[i]);

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
    ccDOTIMES(i, mdlv->n_partition_segments) {
      if (mdlv->segment_display_props[i].realisticLook == NULL)
        break;  // Must be dealing with a case with no realistic look info

      if (!cdi_write_cdistring(cdi_info, mdlv->segment_display_props[i].realisticLook))
        return FALSE;
    }
  }

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info)) {
    if (cdi_write_asINT32(cdi_info, &(mdlv->n_partial_parts), 1) != 1)
      return FALSE;

    ccDOTIMES(i, mdlv->n_partial_parts)
      cdi_write_dprp(cdi_info, mdlv->partial_display_props[i]);
  }


  return TRUE;
}

void cdi_empty_model_view(CDI_MDLV mdlv)
{
  EXA_FREE(mdlv->name);
  ccDOTIMES(i, mdlv->n_partition_segments) {
    EXA_FREE(mdlv->segment_display_props[i].color);
    EXA_FREE(mdlv->segment_display_props[i].realisticLook);
  }
  EXA_FREE(mdlv->segment_display_props);
  ccDOTIMES(i, mdlv->n_partial_parts) {
    EXA_FREE(mdlv->partial_display_props[i].color);
    EXA_FREE(mdlv->partial_display_props[i].realisticLook);
  }
  EXA_FREE(mdlv->partial_display_props);
}

cdiBOOLEAN cdi_read_psmv(CDI_INFO cdi_info, CDI_PSMV psmv)
{
  uINT64 bytesRead = 0;

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
    cdiINT32 n_partition_segments = 0;
    if (cdi_read_asINT32(cdi_info, &n_partition_segments, 1, bytesRead) != 1)
      return FALSE;

    // Assign old to the root segment of the base assembly
    cCDI_SEGMENT_REF segref(0, 0);
    psmv->partition_segment_refs.push_back(segref);
    psmv->effective_segments.push_back(-1);

    if (n_partition_segments > 0) {
      cdiINT32 segment_index, eff_segment_index;
      ccDOTIMES(i, n_partition_segments) {
        if (cdi_read_asINT32(cdi_info, &segment_index, 1, bytesRead) != 1 ||
            cdi_read_asINT32(cdi_info, &eff_segment_index, 1, bytesRead) != 1)
          return FALSE;
        segref.m_partitionIndex = i + 1;
        segref.m_segmentIndex = segment_index < 0 ? -1 : segment_index + 1;
        psmv->partition_segment_refs.push_back(segref);
        psmv->effective_segments.push_back(eff_segment_index);
      }
    }
    if (cdi_read_asINT32(cdi_info, &(psmv->n_model_views), 1, bytesRead) != 1)
      return FALSE;

    psmv->mv_display_props = NULL;
    if (psmv->n_model_views > 0) {
      psmv->mv_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, psmv->n_model_views);
      ccDOTIMES(i, psmv->n_model_views)
        if (!cdi_read_dprp(cdi_info, psmv->mv_display_props[i], bytesRead))
          return FALSE;
    }

    // If we haven't read all the info in the chunk, there must be more to
    // read. When realistic look information was introduced to this chunk (and
    // others), we added it here without bumping the CDI version number (so as to
    // not break compatibility with other VIZ versions unnecessarily). So if
    // there's more to read, it must be realistic look info (one per model view).
    if (bytesRead < cio_get_size(cdi_info->cio_info)) {
      ccDOTIMES(i, psmv->n_model_views) {
        if (!cdi_read_cdistring(cdi_info,
          &(psmv->mv_display_props[i].realisticLook),
          bytesRead))
          return FALSE;
      }
    }
  }
  else {
    CDI_WITH_INNER_CHUNK(cdi_info) {
      if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_SEGL) {
        cdi_read_segment_reference_list(cdi_info, psmv->partition_segment_refs);
      }
    }
    CDI_WITH_INNER_CHUNK(cdi_info) {
      if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_MVDP) {
        if (cdi_read_asINT32(cdi_info, &(psmv->n_model_views), 1, bytesRead) != 1)
          return FALSE;

        psmv->mv_display_props = NULL;
        if (psmv->n_model_views > 0) {
          psmv->mv_display_props = EXA_CALLOC_ARRAY(sCDI_DPRP, psmv->n_model_views);
          ccDOTIMES(i, psmv->n_model_views)
            if (!cdi_read_dprp(cdi_info, psmv->mv_display_props[i], bytesRead))
              return FALSE;
        }
        if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
          // If we haven't read all the info in the chunk, there must be more to
          // read. When realistic look information was introduced to this chunk (and
          // others), we added it here without bumping the CDI version number (so as to
          // not break compatibility with other VIZ versions unnecessarily). So if
          // there's more to read, it must be realistic look info (one per model view).
          if (bytesRead < cio_get_size(cdi_info->cio_info)) {
            ccDOTIMES(i, psmv->n_model_views) {
              if (!cdi_read_cdistring(cdi_info,
                &(psmv->mv_display_props[i].realisticLook),
                bytesRead))
                return FALSE;
            }
          }
        }
      }
    }
  }

  return TRUE;
}

cdiBOOLEAN cdi_write_psmv(CDI_INFO cdi_info, CDI_PSMV psmv)
{
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
    // Old cases did not have a base assembly
    cdiINT32 n_partition_segments = psmv->partition_segment_refs.size() - 1;
    if (cdi_write_asINT32(cdi_info, &n_partition_segments, 1) != 1)
      return FALSE;

    ccDOTIMES(i, n_partition_segments) {
      cdiINT32 segment_index = psmv->partition_segment_refs[i + 1].m_segmentIndex;
      if (segment_index > 0)
        segment_index--;
      cdiINT32 effective_segment_index = psmv->effective_segments[i + 1];
      if (cdi_write_asINT32(cdi_info, &segment_index, 1) != 1 ||
          cdi_write_asINT32(cdi_info, &effective_segment_index, 1) != 1)
        return FALSE;
    }
    if (cdi_write_asINT32(cdi_info, &(psmv->n_model_views), 1) != 1)
      return FALSE;

    ccDOTIMES(i, psmv->n_model_views)
      if (!cdi_write_dprp(cdi_info, psmv->mv_display_props[i]))
        return FALSE;

    ccDOTIMES(i, psmv->n_model_views) {
      if (psmv->mv_display_props[i].realisticLook == NULL)
        break;  // Must be dealing with a case with no realistic look info

      if (!cdi_write_cdistring(cdi_info, psmv->mv_display_props[i].realisticLook))
        return FALSE;
    }
  }
  else {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SEGL) {
      cdi_write_segment_reference_list(cdi_info, psmv->partition_segment_refs);
    }
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_MVDP) {
      if (cdi_write_asINT32(cdi_info, &(psmv->n_model_views), 1) != 1)
        return FALSE;

      ccDOTIMES(i, psmv->n_model_views)
        if (!cdi_write_dprp(cdi_info, psmv->mv_display_props[i]))
          return FALSE;

      ccDOTIMES(i, psmv->n_model_views) {
        if (psmv->mv_display_props[i].realisticLook == NULL)
          break;  // Must be dealing with a case with no realistic look info

        if (!cdi_write_cdistring(cdi_info, psmv->mv_display_props[i].realisticLook))
          return FALSE;
      }
    }
  }

  return TRUE;
}

void cdi_empty_psmv(CDI_PSMV psmv)
{
  ccDOTIMES(i, psmv->n_model_views) {
    EXA_FREE(psmv->mv_display_props[i].color);
    EXA_FREE(psmv->mv_display_props[i].realisticLook);
  }
  EXA_FREE(psmv->mv_display_props);
}

cdiBOOLEAN cdi_read_facet(CDI_INFO cdi_info, CDI_FACT facet)
{
  static asINT32 max_n_vertices = 20;
  static asINT32 *facet_vertices = NULL;

  if (facet_vertices == NULL) {
    facet_vertices = EXA_CALLOC_ARRAY(asINT32, max_n_vertices);
    if (facet_vertices == NULL)
      return FALSE;
  }

  if (cdi_read_asINT32(cdi_info, &facet->front_face, 1) != 1)
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &facet->back_face, 1) != 1)
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &facet->n_vertices, 1) != 1)
    return FALSE;

  if (facet->n_vertices > max_n_vertices) {
    max_n_vertices = 2 * facet->n_vertices;
    facet_vertices = EXA_REALLOC_ARRAY(facet_vertices, asINT32, max_n_vertices);
    if (facet_vertices == NULL)
      return FALSE;
  }
  asINT32 *varray = (facet->vertices == NULL ? facet_vertices : facet->vertices);
  if (cdi_read_asINT32(cdi_info, varray, facet->n_vertices) != facet->n_vertices)
    return FALSE;
  facet->vertices = varray;

  return TRUE;
}

cdiBOOLEAN cdi_write_facets(CDI_INFO cdi_info, CDI_FACT facets, asINT32 n_facets)
{
  ccDOTIMES(i, n_facets) {
    if (cdi_write_asINT32(cdi_info, &(facets[i].front_face), 1) != 1)
      return FALSE;
    if (cdi_write_asINT32(cdi_info, &(facets[i].back_face), 1) != 1)
      return FALSE;
    if (cdi_write_asINT32(cdi_info, &(facets[i].n_vertices), 1) != 1)
      return FALSE;
    if (cdi_write_asINT32(cdi_info, facets[i].vertices, facets[i].n_vertices) != facets[i].n_vertices)
      return FALSE;
  }
  return TRUE;
}

cdiBOOLEAN cdi_read_facet_header(CDI_INFO cdi_info, CDI_FACT_HEADER header)
{
  if (cdi_read_asINT32(cdi_info, &header->n_facets, 1) != 1)
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &header->n_vertex_refs, 1) != 1)
    return FALSE;
  return TRUE;
}

cdiBOOLEAN cdi_write_facet_header(CDI_INFO cdi_info, CDI_FACT_HEADER header)
{
  if (cdi_write_asINT32(cdi_info, &header->n_facets, 1) != 1)
    return FALSE;
  if (cdi_write_asINT32(cdi_info, &header->n_vertex_refs, 1) != 1)
    return FALSE;
  return TRUE;
}

cdiBOOLEAN cdi_read_vertex_coords(CDI_INFO cdi_info, idFLOAT *coords, asINT32 n_vertex)
{
  asINT32 n_coords = (3 * n_vertex);
  return (cdi_read_idFLOAT(cdi_info, coords, n_coords) == n_coords);
}

cdiBOOLEAN cdi_write_vertex_coords(CDI_INFO cdi_info, idFLOAT *coords, asINT32 n_vertex)
{
  asINT32 n_coords = (3 * n_vertex);
  return (cdi_write_idFLOAT(cdi_info, coords, n_coords) == n_coords);
}

cdiBOOLEAN cdi_read_case_region_count(CDI_INFO cdi_info, CDI_RGCT rgct)
{
  return (cdi_read_asINT32(cdi_info, &rgct->n_regions, 1) == 1);
}

cdiBOOLEAN cdi_write_case_region_count(CDI_INFO cdi_info, CDI_RGCT rgct)
{
  return (cdi_write_asINT32(cdi_info, &rgct->n_regions, 1) == 1);
}

cdiBOOLEAN cdi_read_region_numeration(CDI_INFO cdi_info, CDI_RGNN rgnn)
{
  if (cdi_read_asINT32(cdi_info, &rgnn->n_vertices, 1) != 1)
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &rgnn->n_faces, 1) != 1)
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &rgnn->n_facets, 1) != 1)
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &rgnn->n_vertex_refs, 1) != 1)
    return FALSE;
  return TRUE;
}

cdiBOOLEAN cdi_write_region_numeration(CDI_INFO cdi_info, CDI_RGNN rgnn)
{
  if (cdi_write_asINT32(cdi_info, &rgnn->n_vertices, 1) != 1)
    return FALSE;
  if (cdi_write_asINT32(cdi_info, &rgnn->n_faces, 1) != 1)
    return FALSE;
  if (cdi_write_asINT32(cdi_info, &rgnn->n_facets, 1) != 1)
    return FALSE;
  if (cdi_write_asINT32(cdi_info, &rgnn->n_vertex_refs, 1) != 1)
    return FALSE;
  return TRUE;
}

cdiBOOLEAN cdi_read_region_display_properties(CDI_INFO cdi_info, CDI_RGDP rgdp)
{
  if (!cdi_read_cdistring(cdi_info, &(rgdp->color)))
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &(rgdp->material_index), 1) != 1)
    return FALSE;

  asINT32 display_mode;
  if (cdi_read_asINT32(cdi_info, &(display_mode), 1) != 1)
    return FALSE;
  rgdp->display_mode = (CDI_DISPLAY_MODE) display_mode;
  return TRUE;
}

cdiBOOLEAN cdi_write_region_display_properties(CDI_INFO cdi_info, CDI_RGDP rgdp)
{
  asINT32 display_mode = (asINT32) rgdp->display_mode;

  if (!cdi_write_cdistring(cdi_info, rgdp->color) ||
      cdi_write_asINT32(cdi_info, &(rgdp->material_index), 1) != 1 ||
      cdi_write_asINT32(cdi_info, &(display_mode), 1) != 1)
    return FALSE;

  return TRUE;
}

cdiBOOLEAN cdi_read_partition_type( CDI_INFO cdi_info, CDI_PRTT prtt )
{
  return (cdi_read_asINT32(cdi_info, (asINT32*)(&prtt->partition_type), 1) == 1);
}

cdiBOOLEAN cdi_write_partition_type( CDI_INFO cdi_info, CDI_PRTT prtt )
{
  return (cdi_write_asINT32(cdi_info, (asINT32*)(&prtt->partition_type), 1) == 1);
}

cdiBOOLEAN cdi_read_segment_index( CDI_INFO cdi_info, CDI_SGID sgid )
{
  return (cdi_read_asINT32(cdi_info, (asINT32*)(&sgid->segment_index), 1) == 1);
}

cdiBOOLEAN cdi_write_segment_index( CDI_INFO cdi_info, CDI_SGID sgid )
{
  return (cdi_write_asINT32(cdi_info, (asINT32*)(&sgid->segment_index), 1) == 1);
}

cdiBOOLEAN cdi_read_shininess(CDI_INFO cdi_info, CDI_SHIN shin)
{
  return (cdi_read_idFLOAT(cdi_info, &(shin->shininess), 1) == 1);
}

cdiBOOLEAN cdi_write_shininess(CDI_INFO cdi_info, CDI_SHIN shin)
{
  return (cdi_write_idFLOAT(cdi_info, &(shin->shininess), 1) == 1);
}

cdiBOOLEAN cdi_read_rgba(CDI_INFO cdi_info, CDI_RGBA rgba, asINT32 n_rgba)
{
  ccDOTIMES(i, n_rgba) {
    if (cdi_read_idFLOAT(cdi_info, &(rgba[i].red), 1) != 1)
      return FALSE;
    if (cdi_read_idFLOAT(cdi_info, &(rgba[i].green), 1) != 1)
      return FALSE;
    if (cdi_read_idFLOAT(cdi_info, &(rgba[i].blue), 1) != 1)
      return FALSE;
    if (cdi_read_idFLOAT(cdi_info, &(rgba[i].alpha), 1) != 1)
      return FALSE;
  }
  return TRUE;
}

cdiBOOLEAN cdi_write_rgba(CDI_INFO cdi_info, CDI_RGBA rgba, asINT32 n_rgba)
{
  ccDOTIMES(i, n_rgba) {
    if (cdi_write_idFLOAT(cdi_info, &(rgba[i].red), 1) != 1)
      return FALSE;
    if (cdi_write_idFLOAT(cdi_info, &(rgba[i].green), 1) != 1)
      return FALSE;
    if (cdi_write_idFLOAT(cdi_info, &(rgba[i].blue), 1) != 1)
      return FALSE;
    if (cdi_write_idFLOAT(cdi_info, &(rgba[i].alpha), 1) != 1)
      return FALSE;
  }
  return TRUE;
}

cdiBOOLEAN cdi_read_texture_map(CDI_INFO cdi_info, CDI_TXTR txtr)
{
  if (!cdi_read_cdistring(cdi_info, &(txtr->name)))
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &(txtr->xtxtr), 1) != 1)
    return FALSE;
  if (cdi_read_asINT32(cdi_info, &(txtr->ytxtr), 1) != 1)
    return FALSE;
  {
    asINT32 n_txtr = txtr->xtxtr * txtr->ytxtr;
    txtr->map = EXA_CALLOC_ARRAY(sCDI_RGBA, n_txtr);
    if (!cdi_read_rgba(cdi_info, txtr->map, n_txtr))
      return FALSE;
  }
  return TRUE;
}

cdiBOOLEAN cdi_write_texture_map(CDI_INFO cdi_info, CDI_TXTR txtr)
{
  if (!cdi_write_cdistring(cdi_info, txtr->name))
    return FALSE;
  if (cdi_write_asINT32(cdi_info, &(txtr->xtxtr), 1) != 1)
    return FALSE;
  if (cdi_write_asINT32(cdi_info, &(txtr->ytxtr), 1) != 1)
    return FALSE;
  {
    asINT32 n_txtr = txtr->xtxtr * txtr->ytxtr;
    if (!cdi_write_rgba(cdi_info, txtr->map, n_txtr))
      return FALSE;
  }
  return TRUE;
}

void cdi_report_read_error(const char *chunk_name)
{
  msg_error("Error reading CDI file: unable to read %s chunk.", chunk_name);
}

void cdi_report_write_error(const char *chunk_name)
{
  msg_error("Error writing CDI file: unable to write %s chunk.", chunk_name);
}


cdiBOOLEAN cdi_read_scct(CDI_INFO cdi_info, CDI_SCCT scct)
{
  return (cdi_read_asINT32(cdi_info, &scct->n_models, 1) == 1 &&
          cdi_read_asINT32(cdi_info, &scct->n_bcs, 1) == 1 &&
          cdi_read_asINT32(cdi_info, &scct->n_walls, 1) == 1);
}

cdiBOOLEAN cdi_write_scct(CDI_INFO cdi_info, CDI_SCCT scct)
{
  return (cdi_write_asINT32(cdi_info, &scct->n_models, 1) == 1 &&
          cdi_write_asINT32(cdi_info, &scct->n_bcs, 1) == 1 &&
          cdi_write_asINT32(cdi_info, &scct->n_walls, 1) == 1);
}

cdiBOOLEAN cdi_read_cmdl(CDI_INFO cdi_info, CDI_CMDL cmdl)
{
  cmdl->export_variables = cmdl->import_variables = NULL;
  cmdl->export_variable_units = cmdl->import_variable_units = NULL;
  cmdl->pf_faces_with_bcs = NULL;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 2)) {
    int32_t index = 0;
    cdi_inner_chunk_read_strg(index, "cmdl", cdi_info, &cmdl->model_name);
  }

  if (!(cdi_read_cdistring(cdi_info, &(cmdl->model_type)) &&
      cdi_read_asINT32(cdi_info, &cmdl->coupling_type, 1) == 1 &&
      cdi_read_cdistring(cdi_info, &(cmdl->model_filename))))
  return FALSE;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 5)) {
    if (!(cdi_read_cdistring(cdi_info, &(cmdl->absolute_model_filename))))
      return FALSE;
  }
  else
    cmdl->absolute_model_filename = EXA_STRDUP("");

  if (!(cdi_read_cdistring(cdi_info, &(cmdl->results_filename))))
    return FALSE;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 5)) {
    if (!(cdi_read_cdistring(cdi_info, &(cmdl->absolute_results_filename))))
      return FALSE;
  }
  else
    cmdl->absolute_results_filename = EXA_STRDUP("");

  if (!(cdi_read_cdistring(cdi_info, &(cmdl->model_length_unit)) &&
        cdi_read_asINT32(cdi_info, &cmdl->n_export_variables, 1) == 1))
    return FALSE;

  cmdl->export_variables = new cdiINT32[cmdl->n_export_variables];
  cmdl->export_variable_units = new STRING[cmdl->n_export_variables];
  ccDOTIMES(i, cmdl->n_export_variables)
    cmdl->export_variable_units[i] = NULL;
  ccDOTIMES(i, cmdl->n_export_variables) {
    if (!(cdi_read_asINT32(cdi_info, &cmdl->export_variables[i], 1) == 1 &&
          cdi_read_cdistring(cdi_info, &(cmdl->export_variable_units[i]))))
      return FALSE;
  }

  if (!(cdi_read_asINT32(cdi_info, &cmdl->n_import_variables, 1) == 1))
    return FALSE;

  cmdl->import_variables = new cdiINT32[cmdl->n_import_variables];
  cmdl->import_variable_units = new STRING[cmdl->n_import_variables];
  ccDOTIMES(i, cmdl->n_import_variables)
    cmdl->import_variable_units[i] = NULL;
  ccDOTIMES(i, cmdl->n_import_variables) {
    if (!(cdi_read_asINT32(cdi_info, &cmdl->import_variables[i], 1) == 1 &&
          cdi_read_cdistring(cdi_info, &(cmdl->import_variable_units[i]))))
      return FALSE;
  }

  // For CDI version >= 4.3, coupling phase table is always used
  sCDI_COUPLING_PHASE_DESC coupling_phase;
  if(CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 3)) {
    if (!(cdi_read_asINT32(cdi_info, &cmdl->n_coupling_phases, 1) == 1))
      return FALSE;

    // read in multiple coupling phases
    ccDOTIMES(i, cmdl->n_coupling_phases) {
      if (!(cdi_read_asINT32(cdi_info, &coupling_phase.start, 1) == 1 &&
            cdi_read_asINT32(cdi_info, &coupling_phase.period, 1) == 1 &&
            cdi_read_asINT32(cdi_info, &coupling_phase.interval, 1) == 1 &&
            cdi_read_asINT32(cdi_info, &coupling_phase.delay, 1) == 1))
        return FALSE;
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 2)) {
        if (!(cdi_read_idFLOAT(cdi_info, &coupling_phase.therm_time_ratio, 1) == 1))
          return FALSE;
      }
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 4)) {
        if (!(cdi_read_idFLOAT(cdi_info, &coupling_phase.stepsize, 1) == 1))
          return FALSE;
      } else {
        coupling_phase.stepsize = -1;
      }
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 6)) {
        if (!(cdi_read_idFLOAT(cdi_info, &coupling_phase.exact_start, 1) == 1 &&
              cdi_read_idFLOAT(cdi_info, &coupling_phase.exact_period, 1) == 1 &&
              cdi_read_idFLOAT(cdi_info, &coupling_phase.exact_interval, 1) == 1))
          return FALSE;
      } else {
        coupling_phase.exact_start = (double)coupling_phase.start;
        coupling_phase.exact_period = (double)coupling_phase.period;
        coupling_phase.exact_interval = (double)coupling_phase.interval;
      }

      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23)) {
        if (!(cdi_read_asINT32(cdi_info, &coupling_phase.adaptive_p, 1) == 1))
        return FALSE;
      } else {
        coupling_phase.adaptive_p = FALSE;
      }
      cmdl->m_coupling_phase_descs.push_back(coupling_phase);
    }
    if (!(cdi_read_asINT32(cdi_info, &cmdl->use_end_time_for_coupling, 1) == 1))
      return FALSE;
    if (!(cdi_read_asINT32(cdi_info, &cmdl->num_iterations, 1) == 1))
      return FALSE;
    if (!(cdi_read_asINT32(cdi_info, &cmdl->end_time, 1) == 1))
      return FALSE;

    double temp_global_powertherm_powerflow_time_ratio = -1.0;
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 4)) {
      if (!(cdi_read_asINT32(cdi_info, &cmdl->m_calculation_type, 1) == 1))
        return FALSE;
      if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 2)) {
        if (!(cdi_read_idFLOAT(cdi_info, &temp_global_powertherm_powerflow_time_ratio, 1) == 1))
          return FALSE;
      }
      if (!(cdi_read_idFLOAT(cdi_info, &cmdl->m_powertherm_start_time, 1) == 1))
        return FALSE;
      if (!(cdi_read_asINT32(cdi_info, &cmdl->m_use_default_powertherm_start_time, 1) == 1))
        return FALSE;
    } else {
      cmdl->m_calculation_type = eCDI_POWERTHERM_CALCULATION_TYPE::InferFromModel;
      cmdl->m_powertherm_start_time = -1;
      cmdl->m_use_default_powertherm_start_time = TRUE;
    }

    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23)) {
      if (!(cdi_read_idFLOAT(cdi_info, &cmdl->adaptive_up_coeff, 1) == 1))
        return FALSE;
      if (!(cdi_read_idFLOAT(cdi_info, &cmdl->gradient_low, 1) == 1))
        return FALSE;
      if (!(cdi_read_idFLOAT(cdi_info, &cmdl->ratio_max, 1) == 1))
        return FALSE;
      if (!(cdi_read_idFLOAT(cdi_info, &cmdl->gradient_percentage_threshold, 1) == 1))
        return FALSE;
      if (!(cdi_read_asINT32(cdi_info, &cmdl->fix_pt_time_p, 1) == 1))
        return FALSE;
      if (!(cdi_read_idFLOAT(cdi_info, &cmdl->total_pt_duration, 1) == 1))
        return FALSE;
    }

    // If v < 6.2, assign all of the phase-based time ratios from the single value
    if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 2)) {
      ccDOTIMES(i, cmdl->n_coupling_phases) {
        cmdl->m_coupling_phase_descs[i].therm_time_ratio = temp_global_powertherm_powerflow_time_ratio;
      }
    }

  } else {  // CDI version <= 4.2
    if (!(cdi_read_asINT32(cdi_info, &coupling_phase.start,    1) == 1 &&
          cdi_read_asINT32(cdi_info, &cmdl->num_iterations,    1) == 1 &&
          cdi_read_asINT32(cdi_info, &coupling_phase.period,   1) == 1 &&
          cdi_read_asINT32(cdi_info, &coupling_phase.interval, 1) == 1))
    return FALSE;
    cmdl->m_calculation_type = eCDI_POWERTHERM_CALCULATION_TYPE::InferFromModel;
  }

  if (!(cdi_read_idFLOAT(cdi_info, (idFLOAT *) &cmdl->l_to_g_xform, 16) == 16 &&
      cdi_read_asINT32(cdi_info, &cmdl->num_coupling_model_bcs, 1) == 1 &&
      cdi_read_asINT32(cdi_info, &cmdl->num_pf_bcs, 1) == 1))
  return FALSE;

  cmdl->pf_faces_with_bcs = new cdiINT32[cmdl->num_pf_bcs];
  ccDOTIMES(i, cmdl->num_pf_bcs) {
    if (cdi_read_asINT32(cdi_info, &cmdl->pf_faces_with_bcs[i], 1) != 1)
      return FALSE;
  }

  // For CDI ver <= 4.2, delay is stored after pf_faces_with_bcs[]
  if (! CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 3)) {
    if (!(cdi_read_asINT32(cdi_info, &coupling_phase.delay, 1) == 1))
      return FALSE;
    coupling_phase.exact_start = (double)coupling_phase.start;
    coupling_phase.exact_period = (double)coupling_phase.period;
    coupling_phase.exact_interval = (double)coupling_phase.interval;

    cmdl->m_coupling_phase_descs.push_back(coupling_phase);
    cmdl->n_coupling_phases = 1;
  }

  return TRUE;
}

cdiBOOLEAN cdi_write_cmdl(CDI_INFO cdi_info, CDI_CMDL cmdl)
{
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 2)) {
    cdi_write_strg(cdi_info, cmdl->model_name);
  }

  if (!(cdi_write_cdistring(cdi_info, cmdl->model_type) &&
        cdi_write_asINT32(cdi_info, &cmdl->coupling_type, 1) == 1 &&
        cdi_write_cdistring(cdi_info, cmdl->model_filename)))
    return FALSE;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 5))
    if (!(cdi_write_cdistring(cdi_info, cmdl->absolute_model_filename)))
      return FALSE;

  if (!(cdi_write_cdistring(cdi_info, cmdl->results_filename)))
    return FALSE;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 5))
    if (!(cdi_write_cdistring(cdi_info, cmdl->absolute_results_filename)))
      return FALSE;

  if (!(cdi_write_cdistring(cdi_info, cmdl->model_length_unit) &&
        cdi_write_asINT32(cdi_info, &cmdl->n_export_variables, 1) == 1))
    return FALSE;

  ccDOTIMES(i, cmdl->n_export_variables) {
    if (!(cdi_write_asINT32(cdi_info, &cmdl->export_variables[i], 1) == 1 &&
          cdi_write_cdistring(cdi_info, cmdl->export_variable_units[i])))
      return FALSE;
  }

  if (cdi_write_asINT32(cdi_info, &cmdl->n_import_variables, 1) != 1)
    return FALSE;

  ccDOTIMES(i, cmdl->n_import_variables) {
    if (!(cdi_write_asINT32(cdi_info, &cmdl->import_variables[i], 1) == 1 &&
          cdi_write_cdistring(cdi_info, cmdl->import_variable_units[i])))
      return FALSE;
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 3))
  {
    // write multiple phase coupling table
    if (cdi_write_asINT32(cdi_info, &cmdl->n_coupling_phases, 1) != 1)
      return FALSE;
    ccDOTIMES(i, cmdl->n_coupling_phases)
    {
      if(!(cdi_write_asINT32(cdi_info, &cmdl->m_coupling_phase_descs[i].start, 1) == 1 &&
           cdi_write_asINT32(cdi_info, &cmdl->m_coupling_phase_descs[i].period, 1) == 1 &&
           cdi_write_asINT32(cdi_info, &cmdl->m_coupling_phase_descs[i].interval, 1) == 1 &&
           cdi_write_asINT32(cdi_info, &cmdl->m_coupling_phase_descs[i].delay, 1) == 1))
        return FALSE;
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 2)) {
        if (!(cdi_write_idFLOAT(cdi_info, &cmdl->m_coupling_phase_descs[i].therm_time_ratio, 1) == 1))
          return FALSE;
      }
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 4)) {
        if (!(cdi_write_idFLOAT(cdi_info, &cmdl->m_coupling_phase_descs[i].stepsize, 1) == 1))
          return FALSE;
      }
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 6)) {
        if (!(cdi_write_idFLOAT(cdi_info, &cmdl->m_coupling_phase_descs[i].exact_start, 1) == 1 &&
              cdi_write_idFLOAT(cdi_info, &cmdl->m_coupling_phase_descs[i].exact_period, 1) == 1 &&
              cdi_write_idFLOAT(cdi_info, &cmdl->m_coupling_phase_descs[i].exact_interval, 1) == 1))
          return FALSE;
      }
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23)) {
        if (!(cdi_write_asINT32(cdi_info, &cmdl->m_coupling_phase_descs[i].adaptive_p, 1) == 1))
        return FALSE;
      }
    }
    if (!(cdi_write_asINT32(cdi_info, &cmdl->use_end_time_for_coupling, 1) == 1))
      return FALSE;
    if (!(cdi_write_asINT32(cdi_info, &cmdl->num_iterations, 1) == 1))
      return FALSE;
    if (!(cdi_write_asINT32(cdi_info, &cmdl->end_time, 1) == 1))
      return FALSE;
    //msg_print("write num_iterations %d end_time %d to cdi", cmdl->num_iterations, cmdl->end_time);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 4)) {
      if (!(cdi_write_asINT32(cdi_info, &cmdl->m_calculation_type, 1) == 1))
        return FALSE;
      // Before 6.2, there was only one Time Ratio value (i.e. not per phase)
      // So in those cases, just write time ratio from the first phase as *the* one value.
      if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 2)) {
        if (!(cdi_write_idFLOAT(cdi_info, &cmdl->m_coupling_phase_descs[0].therm_time_ratio, 1) == 1))
          return FALSE;
      }
      if (!(cdi_write_idFLOAT(cdi_info, &cmdl->m_powertherm_start_time, 1) == 1))
        return FALSE;
      if (!(cdi_write_asINT32(cdi_info, &cmdl->m_use_default_powertherm_start_time, 1) == 1))
        return FALSE;
    }
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23)) {
      if (!(cdi_write_idFLOAT(cdi_info, &cmdl->adaptive_up_coeff, 1) == 1))
        return FALSE;
      if (!(cdi_write_idFLOAT(cdi_info, &cmdl->gradient_low, 1) == 1))
        return FALSE;
      if (!(cdi_write_idFLOAT(cdi_info, &cmdl->ratio_max, 1) == 1))
        return FALSE;
      if (!(cdi_write_idFLOAT(cdi_info, &cmdl->gradient_percentage_threshold, 1) == 1))
        return FALSE;
      if (!(cdi_write_asINT32(cdi_info, &cmdl->fix_pt_time_p, 1) == 1))
        return FALSE;
      if (!(cdi_write_idFLOAT(cdi_info, &cmdl->total_pt_duration, 1) == 1))
        return FALSE;
    }
  } else { // CDI version <= 4.2 has no coupling phase table
    if (!(cdi_write_asINT32(cdi_info, &cmdl->m_coupling_phase_descs[0].start, 1) == 1 &&
        cdi_write_asINT32(cdi_info, &cmdl->num_iterations, 1) == 1 &&
        cdi_write_asINT32(cdi_info, &cmdl->m_coupling_phase_descs[0].period, 1) == 1 &&
        cdi_write_asINT32(cdi_info, &cmdl->m_coupling_phase_descs[0].interval, 1) == 1))
    return FALSE;
  }


  if (!(cdi_write_idFLOAT(cdi_info, (idFLOAT *) &cmdl->l_to_g_xform, 16) == 16 &&
      cdi_write_asINT32(cdi_info, &cmdl->num_coupling_model_bcs, 1) == 1 &&
      cdi_write_asINT32(cdi_info, &cmdl->num_pf_bcs, 1) == 1))
  return FALSE;

  ccDOTIMES(i, cmdl->num_pf_bcs) {
    if (cdi_write_asINT32(cdi_info, &cmdl->pf_faces_with_bcs[i], 1) != 1)
      return FALSE;
  }

  // For CDI version <= 4.2, delay is stored separately from other coupling parameters
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 3)) {
    if (cdi_write_asINT32(cdi_info, &cmdl->m_coupling_phase_descs[0].delay, 1) != 1)
      return FALSE;
  }
  return TRUE;
}

void cdi_empty_cmdl(CDI_CMDL cmdl)
{
  delete [] cmdl->export_variables;
  delete [] cmdl->import_variables;
  delete [] cmdl->pf_faces_with_bcs;

  if (cmdl->export_variable_units != NULL) {
    ccDOTIMES(i, cmdl->n_export_variables) {
      if (cmdl->export_variable_units[i] != NULL)
        exa_free(cmdl->export_variable_units[i]);
    }
    delete [] cmdl->export_variable_units;
  }

  if (cmdl->import_variable_units != NULL) {
    ccDOTIMES(i, cmdl->n_import_variables) {
      if (cmdl->import_variable_units[i] != NULL)
        exa_free(cmdl->import_variable_units[i]);
    }
    delete [] cmdl->import_variable_units;
  }
}


cdiBOOLEAN cdi_read_scbc(CDI_INFO cdi_info, CDI_SCBC scbc)
{
  scbc->source_constraint_face_indices = NULL;

  if (!(cdi_read_asINT32(cdi_info, &scbc->model_index, 1) == 1 &&
        cdi_read_asINT32(cdi_info, &scbc->target_type, 1) == 1 &&
        cdi_read_cdistring(cdi_info, &(scbc->target_name)) &&
        cdi_read_idFLOAT(cdi_info, &scbc->max_match_angle, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &scbc->max_match_distance, 1) == 1 &&
        cdi_read_asINT32(cdi_info, &scbc->target_side_to_match, 1) == 1 &&
        cdi_read_asINT32(cdi_info, &scbc->n_source_constraints, 1) == 1))
    return FALSE;

  scbc->source_constraint_face_indices = new cdiINT32[scbc->n_source_constraints];
  ccDOTIMES(i, scbc->n_source_constraints) {
    if (cdi_read_asINT32(cdi_info, &(scbc->source_constraint_face_indices[i]), 1) != 1)
      return FALSE;
  }
  return TRUE;
}

cdiBOOLEAN cdi_write_scbc(CDI_INFO cdi_info, CDI_SCBC scbc)
{
  if (!(cdi_write_asINT32(cdi_info, &scbc->model_index, 1) == 1 &&
        cdi_write_asINT32(cdi_info, &scbc->target_type, 1) == 1 &&
        cdi_write_cdistring(cdi_info, scbc->target_name) &&
        cdi_write_idFLOAT(cdi_info, &scbc->max_match_angle, 1) == 1 &&
        cdi_write_idFLOAT(cdi_info, &scbc->max_match_distance, 1) == 1 &&
        cdi_write_asINT32(cdi_info, &scbc->target_side_to_match, 1) == 1 &&
        cdi_write_asINT32(cdi_info, &scbc->n_source_constraints, 1) == 1))
    return FALSE;

  ccDOTIMES(i, scbc->n_source_constraints) {
    if (cdi_write_asINT32(cdi_info, &(scbc->source_constraint_face_indices[i]), 1) != 1)
      return FALSE;
  }
  return TRUE;
}

void cdi_empty_scbc(CDI_SCBC scbc)
{
  delete [] scbc->source_constraint_face_indices;
}

cdiBOOLEAN cdi_read_cplw(CDI_INFO cdi_info, CDI_CPLW cplw)
{
  cplw->source_constraint_types = NULL;
  cplw->source_constraint_names = NULL;

  if (!(cdi_read_asINT32(cdi_info, &cplw->face_index, 1) == 1 &&
        cdi_read_asINT32(cdi_info, &cplw->model_index, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cplw->max_match_angle, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cplw->max_match_distance, 1) == 1 &&
        cdi_read_asINT32(cdi_info, &cplw->n_source_constraints, 1) == 1))
    return FALSE;

  cplw->source_constraint_types = new cdiINT32[cplw->n_source_constraints];
  cplw->source_constraint_sides = new cdiINT32[cplw->n_source_constraints];
  cplw->source_constraint_names = new STRING[cplw->n_source_constraints];
  ccDOTIMES(i, cplw->n_source_constraints)
    cplw->source_constraint_names[i] = NULL;
  ccDOTIMES(i, cplw->n_source_constraints) {
    if (!(cdi_read_asINT32(cdi_info, &cplw->source_constraint_types[i], 1) == 1 &&
          cdi_read_asINT32(cdi_info, &cplw->source_constraint_sides[i], 1) == 1 &&
          cdi_read_cdistring(cdi_info, &(cplw->source_constraint_names[i]))))
      return FALSE;
  }

  if (!(cdi_read_asINT32(cdi_info, &cplw->flags, 1) == 1))
    return FALSE;

  return TRUE;
}

cdiBOOLEAN cdi_write_cplw(CDI_INFO cdi_info, CDI_CPLW cplw)
{
  if (!(cdi_write_asINT32(cdi_info, &cplw->face_index, 1) == 1 &&
        cdi_write_asINT32(cdi_info, &cplw->model_index, 1) == 1 &&
        cdi_write_idFLOAT(cdi_info, &cplw->max_match_angle, 1) == 1 &&
        cdi_write_idFLOAT(cdi_info, &cplw->max_match_distance, 1) == 1 &&
        cdi_write_asINT32(cdi_info, &cplw->n_source_constraints, 1) == 1))
    return FALSE;

  ccDOTIMES(i, cplw->n_source_constraints) {
    if (!(cdi_write_asINT32(cdi_info, &cplw->source_constraint_types[i], 1) == 1 &&
          cdi_write_asINT32(cdi_info, &cplw->source_constraint_sides[i], 1) == 1 &&
          cdi_write_cdistring(cdi_info, cplw->source_constraint_names[i])))
      return FALSE;
  }

  if (!(cdi_write_asINT32(cdi_info, &cplw->flags, 1) == 1))
    return FALSE;

  return TRUE;
}

void cdi_empty_cplw(CDI_CPLW cplw)
{
  delete [] cplw->source_constraint_types;
  delete [] cplw->source_constraint_sides;
  if (cplw->source_constraint_names != NULL) {
    ccDOTIMES(i, cplw->n_source_constraints) {
      if (cplw->source_constraint_names[i] != NULL)
        exa_free(cplw->source_constraint_names[i]);
    }
    delete [] cplw->source_constraint_names;
  }
}

cdiBOOLEAN cdi_read_fcmp(CDI_INFO cdi_info, CDI_FCMP fcmp)
{
  if (!cdi_read_cdistring(cdi_info, &fcmp->name) ||
      cdi_read_asINT32(cdi_info, &fcmp->equation_of_state, 1) != 1 ||
      cdi_read_idFLOAT(cdi_info, &fcmp->viscosity, 1) != 1 ||
      cdi_read_idFLOAT(cdi_info, &fcmp->molecular_weight, 1) != 1)
    return FALSE;

  return TRUE;
}

cdiBOOLEAN cdi_write_fcmp(CDI_INFO cdi_info, CDI_FCMP fcmp)
{
  if (!cdi_write_cdistring(cdi_info, fcmp->name) ||
      cdi_write_asINT32(cdi_info, &fcmp->equation_of_state, 1) != 1 ||
      cdi_write_idFLOAT(cdi_info, &fcmp->viscosity, 1) != 1 ||
      cdi_write_idFLOAT(cdi_info, &fcmp->molecular_weight, 1) != 1)
    return FALSE;

  return TRUE;
}

void cdi_empty_fcmp(CDI_FCMP fcmp)
{
  exa_free(fcmp->name);
}


cdiBOOLEAN cdi_read_condenser(CDI_INFO cdi_info, CDI_CDSR cdsr)
{
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info)) {
      cdsr->udstValues = cdi_read_udst(cdi_info);
  }
  else {
    cdsr->udstValues.n_cvdp = 0;
    cdsr->udstValues.n_eqdp = 0;
  }

  if (!cdi_read_cdistring(cdi_info, &cdsr->name))
    return FALSE;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 7)) {
    if (!cdi_read_cdistring(cdi_info, &cdsr->part_name))
      return FALSE;
  }
  else
    cdsr->part_name = EXA_STRDUP(cdsr->name);

  if ((cdi_read_asINT32(cdi_info, &cdsr->type, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->tool, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->flags, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->table_csys_index, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->medium_csys_index, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->inlet_face_index, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->outlet_face_index, 1) != 1))
    return FALSE;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 19)) {
    if (cdi_read_asINT32(cdi_info, &cdsr->coolant_entry_face_index, 1) != 1 ||
        cdi_read_asINT32(cdi_info, &cdsr->top_exchanger_face_index, 1) != 1)
        return FALSE;
  } else {
    cdsr->coolant_entry_face_index = -1;
    cdsr->top_exchanger_face_index = -1;
  }

  if  ((cdi_read_asINT32(cdi_info, &cdsr->inlet_facet_offset, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->outlet_facet_offset, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->inlet_meas_index, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->outlet_meas_index, 1) != 1))
    return FALSE;

  if ((cdsr->flags & CDI_HXCH_HAS_HEAT_GEN_MEAS) != 0) {
    if(cdi_read_asINT32(cdi_info, &cdsr->heat_gen_meas_index, 1) != 1)
      return FALSE;
  }
  else
    cdsr->heat_gen_meas_index = -1;

  if ((cdi_read_asINT32(cdi_info, &cdsr->table_index, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->adiabatic_index, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->medium_index, 1) != 1) ||
      (cdi_read_asINT32(cdi_info, &cdsr->n_passes, 1) != 1))
    return FALSE;

  cdsr->n_tubes = new cdiINT32[cdsr->n_passes];

  if (!(cdi_read_asINT32(cdi_info, cdsr->n_tubes, cdsr->n_passes) == cdsr->n_passes &&
        cdi_read_idFLOAT(cdi_info, &cdsr->x_len, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->y_len, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->z_len, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->mass_flow_rate, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->heat_rejection, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->entry_temp, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->entry_pressure, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->pressure_drop, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->experiment_exit_temp, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->min_air_flow, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->max_air_flow, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->kc_coeff, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->alpha_coeff, 1) == 1 &&
        cdi_read_idFLOAT(cdi_info, &cdsr->d_coeff, 1) == 1 &&
        cdi_read_cdistring(cdi_info, &cdsr->data_string)))
    return FALSE;

  return TRUE;
}

cdiBOOLEAN cdi_write_condenser(CDI_INFO cdi_info, CDI_CDSR cdsr)
{
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info)) {
    // write values only if we have udst chunks
    if (cdsr->udstValues.n_cvdp > 0 || cdsr->udstValues.n_eqdp > 0) {
      WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_UDST)
      {
        ccDOTIMES(i, cdsr->udstValues.n_cvdp) {
          cdi_write_cvdp(cdi_info, cdsr->udstValues.cvdp[i]);
        }

        ccDOTIMES(i, cdsr->udstValues.n_eqdp) {
          cdi_write_eqdp(cdi_info, cdsr->udstValues.eqdp[i]);
        }
      }
    }
  }

  //could exceptions help here?
  if (!cdi_write_cdistring(cdi_info, cdsr->name))
    return FALSE;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 7)) {
    if (!cdi_write_cdistring(cdi_info, cdsr->part_name))
      return FALSE;
  }

  if ((cdi_write_asINT32(cdi_info, &cdsr->type, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->tool, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->flags, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->table_csys_index, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->medium_csys_index, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->inlet_face_index, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->outlet_face_index, 1) != 1))
    return FALSE;

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 19)) {
    if (cdi_write_asINT32(cdi_info, &cdsr->coolant_entry_face_index, 1) != 1 ||
        cdi_write_asINT32(cdi_info, &cdsr->top_exchanger_face_index, 1) != 1)
        return FALSE;
  }

  if ((cdi_write_asINT32(cdi_info, &cdsr->inlet_facet_offset, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->outlet_facet_offset, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->inlet_meas_index, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->outlet_meas_index, 1) != 1))
    return FALSE;

  if ((cdsr->flags & CDI_HXCH_HAS_HEAT_GEN_MEAS) != 0) {
    if (cdi_write_asINT32(cdi_info, &cdsr->heat_gen_meas_index, 1) != 1)
      return FALSE;
  }

  if ((cdi_write_asINT32(cdi_info, &cdsr->table_index, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->adiabatic_index, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->medium_index, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, &cdsr->n_passes, 1) != 1) ||
      (cdi_write_asINT32(cdi_info, cdsr->n_tubes, cdsr->n_passes) != cdsr->n_passes) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->x_len, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->y_len, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->z_len, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->mass_flow_rate, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->heat_rejection, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->entry_temp, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->entry_pressure, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->pressure_drop, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->experiment_exit_temp, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->min_air_flow, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->max_air_flow, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->kc_coeff, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->alpha_coeff, 1) != 1) ||
      (cdi_write_idFLOAT(cdi_info, &cdsr->d_coeff, 1) != 1) ||
      (!cdi_write_cdistring(cdi_info, cdsr->data_string)))
    return FALSE;

  return TRUE;
}

void cdi_empty_condenser(CDI_CDSR cdsr)
{
  EXA_FREE(cdsr->name);
  delete [] cdsr->n_tubes;
  cdsr->n_tubes = NULL;
  cdi_destroy_udst(&cdsr->udstValues);
  EXA_FREE(cdsr->data_string);
}

cdiBOOLEAN cdi_read_mmbr(CDI_INFO cdi_info, CDI_MMBR mmbr)
{
  STRING name;
  if (!cdi_read_cdistring(cdi_info, &name))
    return FALSE;
  mmbr->name = name;
  exa_free(name);
  if (cdi_read_idFLOAT(cdi_info, &(mmbr->value), 1) != 1)
    return FALSE;
  if (!cdi_read_cdistring(cdi_info, &name))
    return FALSE;
  mmbr->unit = name;
  if (!cdi_read_cdistring(cdi_info, &name))
    return FALSE;
  mmbr->unitclass = name;

  return TRUE;
}


cdiBOOLEAN cdi_write_mmbr ( CDI_INFO cdi_info, CDI_MMBR mmbr)
{
  ASSERT_VALID_CDI_INFO(cdi_info);
  if (!cdi_write_cdistring(cdi_info, mmbr->name.c_str()))
    return FALSE;

  idFLOAT val = mmbr->value;
  if (!cdi_write_idFLOAT(cdi_info, &val, 1))
    return FALSE;

  if (!cdi_write_cdistring(cdi_info, mmbr->unit.c_str()))
    return FALSE;

  if (!cdi_write_cdistring(cdi_info, mmbr->unitclass.c_str()))
    return FALSE;

  return TRUE;
}

cdiBOOLEAN cdi_read_realistic_look(CDI_INFO cdi_info, CDI_LOOK look)
{
  CHARACTER *serializedLookBuf = NULL;
  asINT32 serializedLookSize;

  if (!cdi_read_stdstring(cdi_info, look->name) ||
      !cdi_read_stdstring(cdi_info, look->type) ||
      !cdi_read_stdstring(cdi_info, look->origin) ||
      !(serializedLookBuf = cdi_read_cdichars(cdi_info, serializedLookBuf, &serializedLookSize, true)))
    return FALSE;

  look->serializedLook.assign(serializedLookBuf, serializedLookBuf+serializedLookSize);
  exa_free(serializedLookBuf);

  return TRUE;
}

cdiBOOLEAN cdi_write_realistic_look(CDI_INFO cdi_info, CDI_LOOK look)
{
  if (!cdi_write_stdstring(cdi_info, look->name) ||
      !cdi_write_stdstring(cdi_info, look->type) ||
      !cdi_write_stdstring(cdi_info, look->origin) ||
      !cdi_write_cdichars(cdi_info, (const char*)(&look->serializedLook[0]),
                          look->serializedLook.size(), true))
    return FALSE;

  return TRUE;
}

// Default filter function.  Always returns true.
bool cdi_match_any(bool measured, CDI_ENTITY_FILTER_CLASS filterClass)
{
  return true;
}

// Filter function that returns true for entities that are categorized as solid.  These are
// interesting for the Solid Faces widget in the PowerINSIGHT Run Monitor editor.
bool cdi_is_solid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass)
{
  return filterClass == CDI_TREAT_AS_SOLID;
}

// Filter function that returns true for entities that not known to be fluid.  This filter is
// deliberately looser than cdi_is_solid.
bool cdi_is_not_fluid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass)
{
  return filterClass != CDI_TREAT_AS_FLUID;
}

// Filter function that returns true for solid entities that can show up in a measurement
// file, with pressure data. These are the interesting entities for PowerACOUSTICS.
bool cdi_is_measured_solid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass)
{
  return measured && (filterClass == CDI_TREAT_AS_SOLID);
}

// Filter function that returns true for solid entities or measured entities.  This is designed
// to capture measured sampled surfaces that are not solid, but will not exclude solid entities
// that are not measured.
bool cdi_is_measured_or_solid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass)
{
  return measured || (filterClass == CDI_TREAT_AS_SOLID);
}

// Filter function for entities that are measured and not known to be fluid.
bool cdi_is_measured_not_fluid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass)
{
  return measured && (filterClass != CDI_TREAT_AS_FLUID);
}

// Filter function that returns true for entities categorized as fluid.
// These are interesting in the Run Monitor editor.
bool cdi_is_fluid(bool measured, CDI_ENTITY_FILTER_CLASS filterClass)
{
  return filterClass == CDI_TREAT_AS_FLUID;
}

// cdi_find_geometry extracts the geometry hierarchy from a CDI File, in a form
// suitable for populating a cQ_GEOM_SELECTION_DLG.  The caller must provide a
// freshly-constructed cCDI_PARTITIONS object (as it likely wants to store that
// object for subsequent calls to cdi_find_geometry_by_path).
//
// Usage examples:
// n = cdi_find_geometry(cdiInfo, cdiPartitions, entities)                        => populates entities without filtering
// n = cdi_find_geometry(cdiInfo, cdiPartitions, entities, cdi_is_measured_solid) => populates entities and marks faces that satisfy
//                                                                                   cdi_is_measured_solid as interesting
//
bool cdi_find_geometry(CDI_INFO cdiInfo,
                       cCDI_PARTITIONS& cdiPartitions,
                       cGEOM_COMMON_ENTITY_LIST& entities,
                       CDI_ENTITY_FILTER_FUNCTION faceFilterFunc)
{
  entities.clear();
  cdi_rewind(cdiInfo);

  cCDI_GEOMETRY_READER reader = cCDI_GEOMETRY_READER(/*parsers=*/{},
                                                     cdiPartitions,
                                                     entities,
                                                     /*regionFilterFunc=*/cdi_match_any,
                                                     faceFilterFunc);
  reader.Parse(cdiInfo);
  reader.PostProcess();
  return true;
}

// For use on prepared cCDI_PARTITIONS and cCDI_GEOM_SELECTION_TREE objects, such as
// might be loaded from a measurement file.
// The selectionTree argument is not currently used, as it's better to figure out which
// regions and faces actually have data in a measurement file, and pass in valid
// regionsMeasured and facesMeasured sets.
bool cdi_find_geometry(cCDI_PARTITIONS& cdiPartitions,
                       const std::unordered_set<cdiINT32>& regionsMeasured,
                       const std::unordered_set<cdiINT32>& facesMeasured,
                       cCDI_GEOM_SELECTION_TREE& selectionTree,
                       cGEOM_COMMON_ENTITY_LIST& entities,
                       CDI_ENTITY_FILTER_FUNCTION regionFilterFunc,
                       CDI_ENTITY_FILTER_FUNCTION faceFilterFunc)
{
  entities.clear();

  cCDI_GEOMETRY_GENERATOR (cdiPartitions,
                           regionsMeasured,
                           facesMeasured,
                           regionsMeasured,
                           facesMeasured,
                           regionFilterFunc,
                           faceFilterFunc,
                           entities)();
  return true;
}

// cdi_find_geometry_by_path is what PowerACOUSTICS and PowerINSIGHT (Run Monitor) use to
// check to see if stored face names or full paths are valid and unambiguous according
// to the cCDI_PARTITIONS object.
//
// If there is exactly one match for the input path (which can also be a face or part
// name) then the function will return true. If the input path is just a name, then
// the candidates are further qualified by typeExpected. An invalid path/name
// (zero matches), or an ambiguous one (more than one match) will both result
// in cdi_find_geometry_by_path returning false.
//
// For a true return, if you pass nullptr for both pEntity and pFaceIndices, the function
// will simply return whether the match was valid and unambiguous.  If you pass pEntity,
// then *pEntity will be filled with the cGEOM_COMMON_ENTITY for the matching path.  If you
// pass pFaceIndices, then *pFaceIndices wlil be filled in with the constituent face indices
// for the match.
bool cdi_find_geometry_by_path(const std::string& path,               // can also be a face or part name
                               eGEOM_COMMON_ITEM_TYPE typeExpected,   // used ONLY if path is a name
                               const cCDI_PARTITIONS& cdiPartitions,
                               std::vector<cdiINT32>* pFaceIndices,   // optional out parameter
                               cGEOM_COMMON_ENTITY* pEntity)          // optional out parameter
{
  // The Face argument to the geomRef ctor is because the default constructor is hidden,
  // and we have to pass something.  The type will be updated on a successful call.
  cCDI_GEOMETRY_REF geomRef(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);

  // PR 51196/51247: If we have a naked face/part name, use the new overload of cdi_get_geometry_by_path
  // that lets us specify what we're looking for.
  if (path.find('/') == std::string::npos && path.find('.') == std::string::npos) {
    cCDI_GEOMETRY_REF::eGEOMETRY_TYPE expectedType;
    switch (typeExpected) {
    case eGEOM_COMMON_ITEM_TYPE::Part:
      expectedType = cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part;
      break;
    case eGEOM_COMMON_ITEM_TYPE::Face:
      expectedType = cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face;
      break;
    default:
      assert(false);  // no known use case
      return false;
    }
    if (!cdi_get_geometry_by_path(cdiPartitions, path, expectedType, &geomRef))
      return false;
  }
  else {
    if (!cdi_get_geometry_by_path(cdiPartitions, path, &geomRef))
      return false;
  }

  // See if we found exactly one matching entity, since we want to fail on
  // ambiguous matches, and collect some type-dependent information in case we
  // need to return it later.
  int partitionIndex = geomRef.PartitionIndex();
  cCDI_PARTITION* partition = cdiPartitions.GetPartition(partitionIndex);
  if (!partition)
    return false;
  cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geomType = geomRef.GeometrySelectionType();
  eGEOM_COMMON_ITEM_TYPE entityType;
  std::string fullPath;
  std::string shortName;
  cdiINT32 geomIndex = -1;
  bool interesting = false;

  switch (geomType) {
    case cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face: {
      if (geomRef.face_list.size() != 1)
        return false;
      entityType = eGEOM_COMMON_ITEM_TYPE::Face;
      geomIndex = geomRef.face_list[0];
      bool gotIt = partition->GetQualifiedFaceName(geomIndex, /*includePath=*/true, &fullPath);
      if (!gotIt)
        return false;
      shortName = cdiPartitions.GetShortFaceName(geomIndex);
      if (cGEOM_COMMON_ENTITY::FullPathIsForNonUniqueEntity(fullPath)) {
        std::string pathInBaseAssembly = cdiPartitions.GetFaceName(geomIndex);
        shortName += " (" + pathInBaseAssembly + ")";
      }
      interesting = true;
      break;
    }
    case cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part: {
      if (geomRef.rgn_list.size() != 1)
        return false;
      entityType = eGEOM_COMMON_ITEM_TYPE::Part;
      geomIndex = geomRef.rgn_list[0];
      bool gotIt = partition->GetQualifiedPartName(geomIndex, /*includePath=*/true, &fullPath);
      if (!gotIt)
        return false;
      shortName = cdiPartitions.GetShortPartName(geomIndex);
      if (cGEOM_COMMON_ENTITY::FullPathIsForNonUniqueEntity(fullPath)) {
        std::string pathInBaseAssembly = cdiPartitions.GetPartName(geomIndex);
        shortName += " (" + pathInBaseAssembly + ")";
      }
      interesting = true;
      break;
    }
    case cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart: {
      if (geomRef.partial_part_list.size() != 1)
        return false;
      entityType = eGEOM_COMMON_ITEM_TYPE::Partial_Part;
      geomIndex = geomRef.partial_part_list[0].m_partIndex;
      // There is no GetQualifiedPartialPartName, so we have to get the parent
      // segment's path and short name, then rebuild the full path. (Although
      // the full path ought to be the same as the input path. We can test this
      // with an assert before we drop code that might end up being useful.)
      cCDI_PARTIAL_PART* partialPart = partition->GetPartialPart(geomIndex);
      if (!partialPart)
        return false;
      cdiINT32 realPartIndex = partialPart->GetPartIndex();
      shortName = cdiPartitions.GetShortPartName(realPartIndex);
      fullPath = partition->GetName() + partialPart->GetPathName() + shortName;
      assert(path == fullPath);
      // Like segments, partial parts are not inherently interesting.
      interesting = false;
      break;
    }
    case cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment: {
      if (geomRef.segment_list.size() != 1)
        return false;
      entityType = eGEOM_COMMON_ITEM_TYPE::Segment;
      geomIndex = -1;
      cCDI_SEGMENT_REF segRef = geomRef.segment_list[0];
      cCDI_SEGMENT* segment = segRef.GetSegment(cdiPartitions);
      if (!segment)
        return false;
      fullPath = partition->GetName() + segment->GetName(/*includePath=*/true);
      // Since cdi_find_geometry_by_path is used to reconstitute entities from
      // saved selections, and not for tree population, we have a little latitude
      // with the display names. These show up in the synopsis in PowerACOUSTICS,
      // and maybe nowhere else. PowerACOUSTICS wants them to end in slashes
      // and for the root segment to show up as PartitionName/ instead of Root/.
      if (segment->IsRoot())
        shortName = partition->GetName() + "/";
      else
        shortName = segment->GetName(/*includePath=*/false) + "/";
      interesting = false;
      break;
    }
    default: {
      return false;
    }
  }

  // PR 51196/51247: If the path is a raw name, confirm that we got back the type of entity we're expecting.
  if (path.find('/') == std::string::npos && path.find('.') == std::string::npos) {
    if (entityType != typeExpected) {
      return false;
    }
  }

  // If we get here, the path was valid and unambiguous.  Fill in any
  // additional requested information.
  if (pFaceIndices) {
    *pFaceIndices = geomRef.ExpandSelection(cdiPartitions, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
  }
  if (pEntity) {
    *pEntity = cGEOM_COMMON_ENTITY(partitionIndex,
                                   entityType,
                                   fullPath,
                                   shortName,
                                   geomIndex,
                                   interesting);
  }
  return true;
}

// Expand a vector of selection paths, which may have a prefixed ! for an explicit deselection,
// to a vector (indexed by face index) of bool, where the bool means "effectively included".
std::vector<bool> cdi_expand_selections_to_face_indices(const std::vector<std::string>& selections,
                                                        const cCDI_PARTITIONS& cdiPartitions,
                                                        bool (*errorCallback)(const std::string& path))
{
  std::vector<bool> facesEnabled;
  facesEnabled.resize(cdiPartitions.GetFaceEntityCount(), false);

  // Use an ordered map to sort the path names, so that the set operations are applied
  // in the correct order.
  std::map<std::string, bool> sortedSelectionMap;
  for (auto str : selections) {
    bool selected = true;
    if (str[0] == '!') {
      str = str.substr(1);
      selected = false;
    }
    sortedSelectionMap.insert(std::make_pair(str, selected));
  }
  for (auto entry : sortedSelectionMap) {
    std::vector<cdiINT32> theseFaceIndices;
    // If the selection-map entries are not paths -- if they're face/part names --
    // then we have to tell cdi_find_geometry_by_path what they are. We can't
    // determine this from the entries themselves, but with the two current callers
    // (PowerACOUSTICS, and PowerINSIGHT run monitors) we know we have faces.
    // ACOUSTICS stores names only in old versions that knew nothing about parts.
    // And the run monitor code branches, and only calls this function with faces.
    // This lets us hard-code eGEOM_COMMON_ITEM_TYPE::Face here, and avoid
    // plumbing up another argument.
    bool pathIsValid = cdi_find_geometry_by_path(entry.first, eGEOM_COMMON_ITEM_TYPE::Face, cdiPartitions, &theseFaceIndices);
    if (pathIsValid) {
      for (auto index : theseFaceIndices) {
        facesEnabled[index] = entry.second;
      }
    }
    // When cdi_find_geometry_by_path fails and we have a callback function,
    // call it. If it returns false, abort: just return an empty vector.
    else if (errorCallback && !(*errorCallback)(entry.first))
      return {};
  }
  return facesEnabled;
}
