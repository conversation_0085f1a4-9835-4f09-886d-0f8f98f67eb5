/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("cpc.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("cpc.copyright", "78") */ 

#include "cdi_cPRESSURE_DROP_PARSER.h"

#include PHYSTYPES_H
#include UNITS_H

#include <string>
#include <sstream>
#include <vector>
#include <map>
#include <algorithm>

static 
std::string
TrimWhitespace(const std::string &s)
{
  if (s.empty()) return s;
  size_t left = 0, right = s.size() - 1;
  while(left < s.size() && isspace(s[left])) ++left;
  while(right > left && isspace(s[right])) --right;
  return s.substr(left, right - left + 1);
}

static
std::vector<std::string>
SplitAndTrim(const std::string &s, CHARACTER c)
{
  BOOLEAN trim = TRUE;
  BOOLEAN ignoreEscaped = FALSE;

  std::vector<std::string> rv;
  
  size_t start = 0;
  size_t loc = s.find(c);
  while (loc != std::string::npos) {
    BOOLEAN isEscaped = (loc > 0 && s[loc - 1] == '\\');
    if (isEscaped && ignoreEscaped) {
      loc = s.find(c, loc + 1);
      continue;
    }

    if (start == loc) {
      if (!trim)
        rv.push_back("");
    }
    else {
      std::string sub = s.substr(start, loc - start);    
      if (trim) {
        sub = TrimWhitespace(sub);
        if (!sub.empty())
          rv.push_back(sub);
      }
      else
        rv.push_back(sub);
    }

    start = loc + 1;
    loc = s.find(c, start);
  }
  
  // Pick up last section.
  std::string sub = s.substr(start, s.size() - start);
  if (trim) {
    sub = TrimWhitespace(sub);
    if (!sub.empty())
      rv.push_back(sub);
  }
  else
    rv.push_back(sub);
  
  return rv;
}


static dFLOAT
CVS(UNITS_DB unitsDb, dFLOAT fromValue,
                 const std::string &fromUnitName,
                 const std::string &toUnitName,
                 BOOLEAN &ok_p)
{
  UNITS_UNIT fromUnit;
  UNITS_UNIT toUnit;
  dFLOAT toValue;
  
  if (units_parse_unit(unitsDb, fromUnitName.c_str(), &fromUnit) != UNITS_STATUS_OK ||
      units_parse_unit(unitsDb, toUnitName.c_str(), &toUnit) != UNITS_STATUS_OK ||
      units_convert(unitsDb, fromValue, fromUnit, toUnit, &toValue) != UNITS_STATUS_OK) {
    ok_p = FALSE;
    return 0;
  }
  
  ok_p = TRUE;
  return toValue;
}

#define KELVIN_WARNING_THRESHOLD 200

cPRESSURE_DROP_PARSER::
cPRESSURE_DROP_PARSER(const std::string& dataStr)
  : m_dataStr(dataStr), m_numLinesParsed(0)
{
}

BOOLEAN
cPRESSURE_DROP_PARSER::
ParseData()
{
  std::istringstream dataStrStream(m_dataStr);
  std::string currentLine;
  BOOLEAN dataKeywordsFound = FALSE;
  BOOLEAN dataUnitsFound = FALSE;

  while (std::getline(dataStrStream, currentLine)) {
    m_lastLineParsed = currentLine;
    m_numLinesParsed++;
    
    StripComments(currentLine);
    if (currentLine.empty())
      continue;   // Skip blank lines.

    if (!dataKeywordsFound) {
      if (ParseParamLine(currentLine))
        continue;
      if (ParseDataKeywords(currentLine)) {
        dataKeywordsFound = TRUE;
        continue;
      }
    }
    else if (dataKeywordsFound && !dataUnitsFound) {
      if (ParseDataUnits(currentLine)) {
        dataUnitsFound = TRUE;
        continue;
      }
    }
    else if (dataKeywordsFound && dataUnitsFound) {
      if (ParseDataRow(currentLine))
        continue;
    }

    return FALSE;
  }

  return TRUE;
}

BOOLEAN
cPRESSURE_DROP_PARSER::
ValidateData(UNITS_DB unitsDb)
{
  m_error.clear();
  m_warnings.clear();
  BOOLEAN ok_p;

  if (3 > DataTableSize()) {
    m_error = "at least 3 pressure drop data rows must be provided";
    return FALSE;
  }

  //////////
  // area //
  //////////

  if (ParamExists("area") && ParamExists("width") && ParamExists("height"))
    m_warnings.push_back("the 'height' and 'width' parameters were ignored in favor of the 'area' parameter");

  if (ParamExists("area")) {
    cUVAL area(GetParam("area"));
    m_area = CVS(unitsDb, area.first, area.second, "m^2", ok_p);
    if (!ok_p) {
      m_error = "invalid unit specified for 'area' parameter";
      return FALSE;
    }
  }
  else if (ParamExists("width") && ParamExists("height")) {
    cUVAL width(GetParam("width"));
    dFLOAT cvtWidth = CVS(unitsDb, width.first, width.second, "m", ok_p);
    if (!ok_p) {
      m_error = "invalid unit specified for 'width' parameter";
      return FALSE;
    }
    if (cvtWidth <= 0) {
      m_error = "the 'width' parameter must be positive";
      return FALSE;
    }

    cUVAL height(GetParam("height"));
    dFLOAT cvtHeight = CVS(unitsDb, height.first, height.second, "m", ok_p);
    if (!ok_p) {
      m_error = "invalid unit specified for 'height' parameter";
      return FALSE;
    }
    if (cvtHeight <= 0) {
      m_error = "the 'height' parameter must be positive";
      return FALSE;
    }

    m_area = cvtWidth * cvtHeight;
  }
  else {
    if (m_leftColKeyword == "air mass flow rate"){ //PR_43764
	  m_error = "either the 'area' parameter or both the 'height' and 'width' parameters must be provided";
	  return FALSE;
	}
  }
  
  ///////////
  // depth //
  ///////////

  if (ParamExists("depth")) {
    cUVAL depth(GetParam("depth"));
    m_depth = CVS(unitsDb, depth.first, depth.second, "m", ok_p);
    if (!ok_p) {
      m_error = "invalid unit specified for the 'depth' parameter";
      return FALSE;
    }
    if (m_depth <= 0) {
      m_error = "the 'depth' parameter must be positive";
      return FALSE;
    }

  }
  else {
    m_error = "the 'depth' parameter is required and must be provided";
    return FALSE;
  }

  /////////////////////////////
  // ambient air temperature //
  /////////////////////////////
  
  if (ParamExists("ambient air temperature")) {
    cUVAL airTemp(GetParam("ambient air temperature"));
    dFLOAT airTemp_degK = CVS(unitsDb, airTemp.first, airTemp.second, "degK", ok_p);
    if (airTemp_degK < KELVIN_WARNING_THRESHOLD ) {
      m_error = "the 'ambient air temperature' parameter must be greater than 200 degK";
      return FALSE;
    }
    m_airTemp = CVS(unitsDb, airTemp.first, airTemp.second, "degC", ok_p);
    if (!ok_p) {
      m_error = "invalid unit specified for the 'ambient air temperature' parameter";
      return FALSE;
    }
  }
  else {
    m_error = "the 'ambient air temperature' parameter is required and must be provided";
    return FALSE;
  }

  /////////////////
  // air density //
  /////////////////

  if (ParamExists("air density") && ParamExists("ambient pressure"))
    m_warnings.push_back("the 'ambient pressure' parameter was ignored in favor of the 'air density' parameter");

  if (ParamExists("air density")) {
    cUVAL density(GetParam("air density"));
    m_airDensity = CVS(unitsDb, density.first, density.second, "kg/m^3", ok_p);
    if (!ok_p) {
      m_error = "invalid unit specified for the 'air density' parameter";
      return FALSE;
    }
  }
  else if (ParamExists("ambient pressure")) {
    cUVAL pressure(GetParam("ambient pressure"));
    dFLOAT cvtPressure = CVS(unitsDb, pressure.first, pressure.second, "Pa", ok_p);
    if (!ok_p) {
      m_error = "invalid unit specified for the 'ambient pressure' parameter";
      return FALSE;
    }

    dFLOAT cvtAirTemp = CVS(unitsDb, m_airTemp, "degC", "degK", ok_p);
    if (!ok_p)
      return FALSE;

    m_airDensity = cvtPressure / (GAS_CONSTANT_AIR * cvtAirTemp);
  }
  else {
    m_error = "either the 'air density' or 'ambient pressure' parameter must be provided";
    return FALSE;
  }
  
  for (size_t i =0; i <  DataTableSize(); ++i) {
    cDATA_ROW row(GetDataRow(i));

    //////////////
    // velocity //
    //////////////
    
    if (m_leftColKeyword == "air mass flow rate") {
      row.first = CVS(unitsDb, row.first, m_leftColUnit, "kg/s", ok_p);
      if (!ok_p) {
        m_error = "invalid unit specified for the 'air mass flow rate' column";
        return FALSE;
      }
      row.first = row.first / (m_airDensity * m_area);
    }
    else if (m_leftColKeyword == "air mass velocity" ||
             m_leftColKeyword == "air mass flux") {
      row.first = CVS(unitsDb, row.first, m_leftColUnit, "kg/(m^2*s)", ok_p);
      if (!ok_p) {
        m_error = "invalid unit specified for the '" + m_leftColKeyword + "' column";
        return FALSE;
      }
      row.first = row.first / m_airDensity;
    }
    else if (m_leftColKeyword == "air velocity") {
      row.first = CVS(unitsDb, row.first, m_leftColUnit, "m/s", ok_p);
      if (!ok_p) {
        m_error = "invalid unit specified for the 'air velocity' column";
        return FALSE;
      }
    }
    else {
      m_error = "invalid or missing keyword specified for the left data column";
      return FALSE;
    }

    //////////////////////
    // resistance coeff //
    //////////////////////

    if (m_rightColKeyword == "pressure drop") {
      row.second = CVS(unitsDb, row.second, m_rightColUnit, "Pa", ok_p);
      if (!ok_p) {
        m_error = "invalid unit specified for the 'pressure drop' column";
        return FALSE;
      }
      row.second = row.second / (m_airDensity * m_depth);
    }
    else if (m_rightColKeyword == "resistance coefficient") {
      row.second = CVS(unitsDb, row.second, m_rightColUnit, "m/s^2", ok_p);
      if (!ok_p) {
        m_error = "invalid unit specified for the 'resistance coefficient' column";
        return FALSE;
      }
    }
    else {
      m_error = "invalid or missing keyword specified for the right data column";
      return FALSE;
    }

    m_dataTable.at(i) = row;
  }

  return TRUE;
}

std::string&
cPRESSURE_DROP_PARSER::
StripComments(std::string& line, char commentChar)
{
  line.erase(std::find(line.begin(), line.end(), commentChar), line.end());
  return line;
}

BOOLEAN
cPRESSURE_DROP_PARSER::
ParseParamLine(std::string line)
{
  std::vector<std::string> nameValue(SplitAndTrim(line, '='));
  if (2 != nameValue.size())
    return FALSE;

  std::string paramName(nameValue[0]);
  if (paramName.empty())
    return FALSE;

  std::string paramValue(nameValue.at(1));
  if (paramValue.empty())
    return FALSE;

  // This will ensure we match tabs and other space-like characters.
  std::replace_if(paramValue.begin(), paramValue.end(), isspace, ' ');

  std::vector<std::string> valueUnit(SplitAndTrim(paramValue, ' '));
  if (2 != valueUnit.size())
    return FALSE;

  char* end;
  dFLOAT value = strtod(valueUnit[0].c_str(), &end);
  if (!std::string(end).empty())
    return FALSE;

  m_params[paramName] = std::make_pair(value, valueUnit[1]);
    
  return TRUE;
}

BOOLEAN
cPRESSURE_DROP_PARSER::
ParseDataKeywords(std::string line)
{
  std::vector<std::string> dataKeywords(SplitAndTrim(line, '|'));
  if (2 != dataKeywords.size())
    return FALSE;

  m_leftColKeyword = dataKeywords[0];
  m_rightColKeyword = dataKeywords[1];

  return TRUE;
}

BOOLEAN
cPRESSURE_DROP_PARSER::
ParseDataUnits(std::string line)
{
  std::vector<std::string> dataUnits(SplitAndTrim(line, '|'));
  if (2 != dataUnits.size())
    return FALSE;

  m_leftColUnit = dataUnits[0];
  m_rightColUnit = dataUnits[1];

  return TRUE;
}

BOOLEAN
cPRESSURE_DROP_PARSER::
ParseDataRow(std::string line)
{
  // This will ensure we match tabs and other space-like characters.
  std::replace_if(line.begin(), line.end(), isspace, ' ');

  std::vector<std::string> dataRow(SplitAndTrim(line, ' '));
  if (2 != dataRow.size())
    return FALSE;

  char* endLeft;
  dFLOAT left = strtod(dataRow[0].c_str(), &endLeft);
  if (!std::string(endLeft).empty())
    return FALSE;

  char* endRight;
  dFLOAT right = strtod(dataRow[1].c_str(), &endRight);
  if (!std::string(endRight).empty())
    return FALSE;

  if (!(left == 0.0 && right == 0.0))   // Skip 0, 0.
    m_dataTable.push_back(std::make_pair(left, right));

  return TRUE;
}

BOOLEAN
cPRESSURE_DROP_PARSER::
ParamExists(const std::string& paramName) const
{
  return m_params.end() != m_params.find(paramName);
}

cPRESSURE_DROP_PARSER::cUVAL
cPRESSURE_DROP_PARSER::
GetParam(const std::string& paramName) const
{
  cUVAL uval(std::make_pair(0.0, std::string("")));
  cPARAM_MAP::const_iterator it(m_params.find(paramName));
  if (it != m_params.end())
    uval = it->second;
  return uval;
}

BOOLEAN
cPRESSURE_DROP_PARSER::PressureDropEstimate(
                                            dFLOAT &inertialCoef,
                                            dFLOAT &viscousCoef, 
                                            UNITS_DB unitsDb,
                                            UNITS_UNIT velUnit,
                                            UNITS_UNIT accelUnit,
                                            bool fitInertialCoefOnly)

{
  // Before calling parser.PressureDropEstimate(), call
  // parser(dataString)
  // parser.ParseData();
  // parser.ValidateData(unitsDb)
  if (getenv("EXA_CDI_PRESSURE_DROP_PARSER_DEBUG")) {
    printf("m/s | m/s^2\n");
    for (size_t i =0; i < DataTableSize(); ++i) {
      cDATA_ROW row(GetDataRow(i));
      printf("%.4g %.4g\n", row.first, row.second);
    }
  }

  UNITS_UNIT mksVelUnit;
  UNITS_UNIT mksAccelUnit;
  if (unitsDb) {
      units_parse_unit(unitsDb, "m/s", &mksVelUnit);
      units_parse_unit(unitsDb, "m/s^2", &mksAccelUnit);
  }

  for (size_t i =0; i < DataTableSize(); ++i) {
    cDATA_ROW row(GetDataRow(i));
    dFLOAT x = row.first;
    dFLOAT y = row.second;
    if (unitsDb) {
      units_convert(unitsDb, x, mksVelUnit, velUnit, &x);
      units_convert(unitsDb, y, mksAccelUnit, accelUnit, &y);
    }
    m_solver.AddPoint(x, y);
  }
  m_solver.Solve(fitInertialCoefOnly);  

  inertialCoef = m_solver.GetA();
  viscousCoef = m_solver.GetB();

  if (getenv("EXA_CDI_PRESSURE_DROP_PARSER_DEBUG")) {
    printf("units used for computation: x: %s y: %s\n",units_unit_name(velUnit), units_unit_name(accelUnit));
    printf("computed coefs (inertial, viscous) :\n");
    printf("%.4g \t %.4g\n",inertialCoef,viscousCoef);
  }
  return TRUE;
}

dFLOAT cPRESSURE_DROP_PARSER::CalculateSolvedY(dFLOAT x) const
{
  return m_solver.CalculateY(x);
}

dFLOAT cPRESSURE_DROP_PARSER::CalculateRSquare() const
{
  return m_solver.CalculateRSquare();
}

dFLOAT cPRESSURE_DROP_PARSER::CalculateStandardDeviation() const
{
  return m_solver.CalculateStandardDeviation();
}

dFLOAT cPRESSURE_DROP_PARSER::CalculateAverageDeviation() const
{
  return m_solver.CalculateAverageDeviation();
}

