/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Guts of license manager functions
 *
 *--------------------------------------------------------------------------*/

#include <time.h>

#include EXALIC_WEBSERVICE_DATASTREAM_H
#include EXALIC_H
#include EXATIME_H

#ifdef	__cplusplus
extern "C" {
#endif
#include MSGERR_H
#ifdef	__cplusplus
}
#endif
#include JOBCTL_SERVER_H



#ifdef LICENSE_ENABLED

/*--------------------------------------------------------------------------*
 * Code used by both built-in and separate process licensing
 *--------------------------------------------------------------------------*/

asINT32 secs_between_license_heartbeats = MAX_SECS_BETWEEN_LICENSE_HEARTBEATS;

/* proc_connect is the connection for all licenses */
static EXALIC_CONNECTION proc_connect;

static asINT32 license_n_sps = 0;

static cSTRING esp  = "\n\n"; // error string prefix: \n\n for DSLS, " for RLM
static cSTRING esp2 = "\n";   // error string prefix: \n for DSLS, nothing for RLM
static cSTRING ess  = "";     // error string suffix: nothing for DSLS, ". for RLM

static char *standard_feat_name = NULL; 		  

// RLM only - standard_feat_name points to either base_feat_name or opt_feat_name
static char *base_feat_name = NULL;			  
static char *opt_feat_name = NULL;

// DSLS only - standard_feat_name points to either sru_feat_name or sun_feat_name
static char *sru_feat_name = NULL;      // DSLS universal tokens (trigram SRU)
static char *sun_feat_name = NULL;      // DSLS universal credits (trigram SUN)
			  
static char *all_feat_version = NULL;			  
static asINT32 n_standard_licenses = 0;
// current_license_checkout is identical to n_standard_licenses except when using DSLS credits
static asINT32 current_license_checkout = 0;

static bool using_dsls_credits = false;
static dFLOAT dsls_n_credits_per_second = 0;
static WALLCLOCK_TIME_SECS dsls_credits_start_time;
static dFLOAT dsls_credits_committed = 0;
static asINT32 dsls_credits_reserved = 0; // reserved but not yet committed
static EXALIC_LICENSE_MODEL dsls_license_model = LICENSE_MODEL_TOKEN; // license model of standard licenses (not aux licenses)

static bool on_3dx_cloud = false;
static bool disable_hw_licensing = false;
static EXALIC_WEBCLIENT_DATASTREAM wds;

typedef struct sLICENSE_DESC {
  STRING feature_name;
  sINT32 license_count;
  sINT32 current_checkout;
  sLICENSE_DESC *next; // linked together in list
} *LICENSE_DESC;

static LICENSE_DESC aux_license_descs = NULL;

#define init_license_strings(alt_str)                           \
{                                                               \
  if (!exalic_use_dsls_feature_names()) {                       \
    standard_feat_name = new char[20];                          \
    opt_feat_name      = new char[20];                          \
    base_feat_name = standard_feat_name;                        \
    standard_feat_name[0] = 'p';                                \
    standard_feat_name[1] = 'o';                                \
    standard_feat_name[2] = 'w';                                \
    standard_feat_name[3] = 'e';                                \
    standard_feat_name[4] = 'r';                                \
    standard_feat_name[5] = 'f';                                \
    standard_feat_name[6] = 'l';                                \
    standard_feat_name[7] = 'o';                                \
    standard_feat_name[8] = 'w';                                \
    standard_feat_name[9] = '-';                                \
    standard_feat_name[10] = 's';                               \
    standard_feat_name[11] = 'i';                               \
    standard_feat_name[12] = 'm';                               \
                                                                \
    standard_feat_name[13] = '\0';                              \
    strcpy(opt_feat_name,   standard_feat_name);                \
    opt_feat_name[13] = '-';                                    \
    opt_feat_name[14] = 'o';                                    \
    opt_feat_name[15] = 'p';                                    \
    opt_feat_name[16] = 't';                                    \
    opt_feat_name[17] = '\0';                                   \
  } else {                                                      \
    sun_feat_name = new char[4];				\
    sru_feat_name = new char[4];				\
    sru_feat_name[0] = 'S';					\
    sru_feat_name[1] = 'R';					\
    sru_feat_name[2] = 'U';					\
    sru_feat_name[3] = '\0';					\
    sun_feat_name[0] = 'S';					\
    sun_feat_name[1] = 'U';					\
    sun_feat_name[2] = 'N';					\
    sun_feat_name[3] = '\0';					\
  }                                                             \
                                                                \
  all_feat_version = new char[4];                               \
  all_feat_version[0] = '1';                                    \
  all_feat_version[1] = '.';                                    \
  all_feat_version[2] = '0';                                    \
  all_feat_version[3] = '\0';                                   \
}
 

#define check_license_error(license_count, seize_status, feat_name1, feat_name2)                        \
    if (seize_status != EXALIC_STATUS_SUCCESS) {                                                        \
      CHARACTER licstat_buf[1024];                                                                      \
      cSTRING error_string = exalic_error_string(proc_connect, seize_status);                           \
      CHARACTER license_type[256];                                                                      \
      if (feat_name2 == NULL) {                                                                         \
        strcpy(license_type, feat_name1);                                                               \
      } else {                                                                                          \
        sprintf(license_type, "%s or %s", feat_name1, feat_name2);                                      \
      }                                                                                                 \
      if (using_dsls_credits) {                                                                         \
	sprintf(licstat_buf, "PowerFLOW simulator licenses not available.  Aborted: %s",                \
		error_string);                                                                          \
	jobctl_server_set_phase(licstat_buf,0.0,0.0);                                                   \
	msg_error ("Unable to obtain licenses (%s) to run the PowerFLOW simulator. "                    \
		   "The license system returned this error: "                                           \
		   "%s%s%s",                                                                            \
		   license_type,                                                                        \
		   esp, error_string, ess);				                        	\
      } else {                                                                                          \
        cSTRING plural = license_count > 1 ? "s" : "";                                                  \
	sprintf(licstat_buf, "PowerFLOW simulator licenses (%d) not available.  Aborted: %s",           \
		license_count, error_string);                                                           \
	jobctl_server_set_phase(licstat_buf,0.0,0.0);                                                   \
	msg_error ("Unable to obtain licenses (%s) to run the PowerFLOW simulator. "                    \
		   "The case requires %d license%s. "                                                   \
		   "The license system returned this error: "                                           \
		   "%s%s%s",                                                                            \
		   license_type, license_count, plural,                                                 \
		   esp, error_string, ess);				                        	\
      }                                                                                                 \
      exit (EXIT_FAILURE);                                                                              \
    }

static VOID checkout_credit_licenses(asINT32 n_credits);
static VOID checkin_credit_licenses(bool commit_credits);
static VOID heartbeat_failed_handler();

#define seize_licenses(feature_name, license_count)		\
  exalic_seize(proc_connect,					\
	       feature_name,					\
	       all_feat_version,				\
	       license_count,					\
	       EXALIC_GROUP_NONE,                               \
               license_n_sps)

static void terminate_licenses() 
{
  static bool already_terminated = false;

  EXALIC_STATUS status;

  if (already_terminated) return;

  if (using_dsls_credits) {
    WALLCLOCK_TIME_SECS time_now = wallclock_time_secs();
    dFLOAT elapsed_time = WALLCLOCK_TIME_DIFF(time_now, dsls_credits_start_time);

    dFLOAT credits_now = dsls_n_credits_per_second * elapsed_time;
    if (credits_now < 1.0)
      credits_now = 1.0;
      
    asINT32 credits_to_commit = credits_now - dsls_credits_committed;

    if (dsls_credits_reserved > 0) {
      // do not commit the reserved credits
      checkin_credit_licenses(false);
    }
    dsls_credits_reserved = 0;
    current_license_checkout = credits_to_commit;
    if (credits_to_commit > 0) {
      checkout_credit_licenses(credits_to_commit);
      // commit the reserved credits
      checkin_credit_licenses(true);
      dsls_credits_committed += credits_to_commit;
    }
    if (on_3dx_cloud) {
      wds.numSwCredits = 0.5 + dsls_credits_committed * wds.rateSwCredits / (wds.rateSwCredits + wds.rateHwCredits);
      wds.numHwCredits = dsls_credits_committed - wds.numSwCredits;
    }
  } else {
    if (n_standard_licenses > 0) {
      if ((status = exalic_relinquish (proc_connect, standard_feat_name)) != EXALIC_STATUS_SUCCESS)
	msg_warn ("Failed to check in %s licenses: %s%s", standard_feat_name, esp2, exalic_error_string(proc_connect, status));
    }
  }

  for (LICENSE_DESC license_desc = aux_license_descs; license_desc != NULL; license_desc = license_desc->next) {
    if ((status = exalic_relinquish (proc_connect, license_desc->feature_name)) != EXALIC_STATUS_SUCCESS)
      msg_warn ("Failed to check in %s licenses: %s%s", license_desc->feature_name, esp2, exalic_error_string(proc_connect, status));
  }

  if ((status = exalic_disconnect (proc_connect)) != EXALIC_STATUS_SUCCESS)
    msg_warn ("Failed to disconnect from license manager: %s%s", esp2, exalic_error_string(proc_connect, status));

  if (on_3dx_cloud) {
    wds.jobDone = true;
    exalic_send_3dorchestrate_data(wds);
  }

  already_terminated = true;
}

static VOID detect_proc_license_still_okay_guts()
{
  if (using_dsls_credits) {
    WALLCLOCK_TIME_SECS time_now = wallclock_time_secs();
    dFLOAT elapsed_time = WALLCLOCK_TIME_DIFF(time_now, dsls_credits_start_time);

    dFLOAT credits_now = dsls_n_credits_per_second * elapsed_time;
    asINT32 credits_to_commit = credits_now - dsls_credits_committed;

    if (credits_to_commit >= dsls_credits_reserved) {
      if (dsls_credits_reserved > 0) {
	// commit the reserved credits
        checkin_credit_licenses(true);
	dsls_credits_committed += dsls_credits_reserved;
      }
      // If there considerably more credits to commit than previously reserved, commit all these extra credits.
      // Otherwise, wait until the next call to this function to catch up.
      if (credits_to_commit > 1.2 * dsls_credits_reserved) {
	asINT32 extra_credits_to_commit = credits_to_commit - dsls_credits_reserved;
	current_license_checkout = extra_credits_to_commit;
        checkout_credit_licenses(extra_credits_to_commit);
	// commit the reserved credits
        checkin_credit_licenses(true);
	dsls_credits_committed += extra_credits_to_commit;
      }
      dsls_credits_reserved = 0;
   
      const dFLOAT reserve_credits_secs = secs_between_license_heartbeats; // 5 minutes into the future
      dFLOAT credits_target = dsls_n_credits_per_second * (reserve_credits_secs + elapsed_time);
      asINT32 credits_to_reserve = credits_target - dsls_credits_committed;
      if (credits_to_reserve > 0) {
	dsls_credits_reserved = credits_to_reserve;
	current_license_checkout = credits_to_reserve;
        checkout_credit_licenses(credits_to_reserve);
      }
    }
  } else {
    if (exalic_heartbeat(proc_connect) != 0) {
      heartbeat_failed_handler();
    }
  }
}
	

#define EXPIRE_DAYS_TO_WARN 30

#define check_license_expiration(connection, feature)                   \
{                                                                       \
  int days_left = exalic_expire_days(connection, feature);              \
  if ((days_left >= 0) /* no error */                                   \
      && (days_left <= EXPIRE_DAYS_TO_WARN))                            \
    msg_warn("Your %s license expires in %d days.", feature, days_left);\
}

inline cSTRING time_string() // not thread safe since ctime() uses static storage
{
  time_t now = time(NULL);
  STRING ctime_now = ctime(&now);
  ctime_now[strlen(ctime_now) - 1] = '\0'; // whack newline

  return ctime_now;
}
  
static VOID checkout_credit_licenses(asINT32 n_credits)
{
  bool issue_warning = true;
  asINT32 license_count = current_license_checkout;
  cSTRING feature_name = standard_feat_name;
  cSTRING plural = license_count > 1 ? "s" : "";
  while (seize_licenses(feature_name, n_credits) != EXALIC_STATUS_SUCCESS) {
    if (issue_warning) {
      issue_warning = false;
      msg_print("\nFailed to reserve %s credits (%s). Waiting until %d credit%s reserved.",
                feature_name, time_string(), 
                license_count, plural);        
      jobctl_server_set_phase("License server connection lost.  Suspended.",0.0,0.0);
    }
    sleep(10);
  }
  if (!issue_warning)
    msg_print("\nReserved %d %s credit%s (%s). Continuing.\n",
              license_count, feature_name, plural, time_string());
}

static VOID checkin_credit_licenses(bool commit_credits)
{
  bool issue_warning = true;
  asINT32 license_count = current_license_checkout;
  cSTRING feature_name = standard_feat_name;
  cSTRING plural = license_count > 1 ? "s" : "";
  while (exalic_relinquish(proc_connect, feature_name, commit_credits) != EXALIC_STATUS_SUCCESS) {
    if (issue_warning) {
      issue_warning = false;
      msg_print("\nFailed to commit %s credits (%s). Waiting until %d credit%s committed.",
                feature_name, time_string(), 
                license_count, plural);        
      jobctl_server_set_phase("License server connection lost.  Suspended.",0.0,0.0);
    }
    sleep(10);
  }
  if (!issue_warning)
    msg_print("\nCommitted %d %s credit%s (%s). Continuing.\n",
              license_count, feature_name, plural, time_string());
}

// This only cares about standard ExaSIM licenses and aux licenses
static VOID heartbeat_failed_handler()
{
  if (exalic_use_dsls_feature_names()) {
    bool issue_warning = true;
    asINT32 license_count = n_standard_licenses;
    cSTRING feature_name = standard_feat_name;
    cSTRING plural = license_count > 1 ? "s" : "";
    while (exalic_heartbeat(proc_connect) != 0) {
      if (issue_warning) {
        issue_warning = false;
	msg_print("\nLost %s license%s (%s). Waiting until %d license%s reacquired.",
		  feature_name, plural, time_string(), 
		  license_count, plural);        
	jobctl_server_set_phase("License server connection lost.  Suspended.",0.0,0.0);
      }
      sleep(10);
    }
    if (!issue_warning)
      msg_print("\nReacquired %d %s license%s (%s). Continuing.\n",
                license_count, feature_name, plural, time_string());
    return;
  }

  sLICENSE_DESC standard_license_desc;
  LICENSE_DESC license_desc;
  if (n_standard_licenses > 0) {
    standard_license_desc.feature_name = standard_feat_name;
    standard_license_desc.license_count = n_standard_licenses;
    standard_license_desc.current_checkout = current_license_checkout;
    standard_license_desc.next = aux_license_descs;
    license_desc = &standard_license_desc;
  } else {
    license_desc = aux_license_descs;
  }

  for ( ; license_desc != NULL; license_desc = license_desc->next) {
    asINT32 license_count = license_desc->license_count;
    asINT32 current_checkout = license_desc->current_checkout;
    cSTRING feature_name = license_desc->feature_name;

    // Relinquish the current licenses in case new licenses were installed
    EXALIC_STATUS relinquish_status = exalic_relinquish (proc_connect, feature_name, false); // do not commit any reserved credits

#if 0 // Telling the user that the license check-in failed is likely to be confusing
    if (relinquish_status != EXALIC_STATUS_SUCCESS)
      msg_warn("Failed to check in %s licenses: %s%s", feature_name, esp2, exalic_error_string(proc_connect, relinquish_status));
#endif

    EXALIC_STATUS seize_status = seize_licenses(feature_name, current_checkout);

    if (seize_status != EXALIC_STATUS_SUCCESS) {
      // If the licenses have expired, we allow the simulation to proceed.
      if (!exalic_is_status_license_expired(seize_status)) {
	cSTRING plural = license_count > 1 ? "s" : "";
	msg_print("\nLost %s license%s (%s). This case is using %d %s license%s."
		  " The license system returned this error: %s%s%s"
		  " Waiting until %d license%s reacquired.",
		  feature_name, plural, time_string(), 
		  license_count, feature_name, plural,
		  esp, exalic_error_string(proc_connect, seize_status), ess, license_count, plural);

	jobctl_server_set_phase("License server connection lost.  Suspended.",0.0,0.0);

	asINT32 last_seize_status = seize_status;
	while (1) {
	  sleep(10);
	  seize_status = seize_licenses(feature_name, current_checkout);

	  if (seize_status == EXALIC_STATUS_SUCCESS)
	    break;

	  if (seize_status != last_seize_status)					
	    msg_print ("\nStill unable to obtain %s license%s."	
		       " The case requires %d %s license%s."		
		       " The license system now returns this error:"	
		       " %s%s%s",					
		       feature_name, plural, license_count, feature_name, plural,
		       esp, exalic_error_string(proc_connect, seize_status), ess);
	  last_seize_status = seize_status;
	}	

	jobctl_server_set_phase("License server connection recovered.  Resumed.",0.0,0.0);

	msg_print("\nReacquired %d %s license%s (%s). Continuing.\n",
		  license_count, feature_name, plural, time_string());
      }
    }
    // All aux licenses are token, not credit. The standard licenses are first in the list followed
    // by the aux licenses.
    if (exalic_use_dsls_feature_names())
      exalic_license_model(proc_connect, LICENSE_MODEL_TOKEN);
  }

  // restore license model for the standard licenses
  if (exalic_use_dsls_feature_names())
    exalic_license_model(proc_connect, dsls_license_model);

}

static dFLOAT dsls_n_sru_tokens(asINT32 n_sps)
{
  // 1.0 is for solver SW; 0.2 is for GUI SW
  // return n_sps <= 32 ? 50.0 : 70.0 * log2(n_sps) - 300.0; // Old scheme

  return (n_sps <= 128 
          ? 50
          : (n_sps <= 512
             ? (dFLOAT)n_sps / 128.0 * 50
             : 100.0 * log2(n_sps) - 700.0));
}

static dFLOAT dsls_n_sru_gui_tokens(asINT32 n_sps)
{
  // 1.0 is for solver SW; 0.2 is for GUI SW
  return 0.2 * dsls_n_sru_tokens(n_sps);
}

static asINT32 dsls_n_sru_hw_tokens(asINT32 n_sps) // only called when on 3DX Cloud
{
  asINT32 tokens;
  cSTRING tokens_string = getenv("GRID_DSLS_HARDWARE_LICENSES");
  if (tokens_string == NULL
      || ((tokens = (asINT32)strtod(tokens_string, NULL)) < 0)) {
    // old scheme
    // 0.12 is for solver HW; 0.0024 is for GUI HW
    tokens = 0.5 + n_sps * (0.12 * 365 * 24 * 0.9 / 1000.0); // round this component separately from GUI component
    if (!getenv("GRID_DISABLE_HW_GUI_LICENSING"))
      tokens += (asINT32)(0.5 + n_sps * (0.0024 * 365 * 24 * 0.9 / 1000.0));
  }
  return tokens;
}

const asINT32 dsls_credits_uplift = 3;

static dFLOAT dsls_n_sun_credits_per_hour(dFLOAT n_sru_tokens)
{
  // The uplift for tokens in 3. The extra factor of 1000 is just to makes the credit/hour rates 
  // more reasonable looking to we humans.
  return dsls_credits_uplift * n_sru_tokens * (1000.0 / (365.0 * 24.0));
}

static dFLOAT dsls_n_sun_hw_credits_per_hour(asINT32 n_sps) // only called when on 3DX Cloud
{
  dFLOAT credits_per_hour;
  cSTRING credits_per_hour_string = getenv("GRID_DSLS_HARDWARE_LICENSES");
  if (credits_per_hour_string == NULL
      || ((credits_per_hour = strtod(credits_per_hour_string, NULL)) < 0)) {
    // old scheme
    if (getenv("GRID_DISABLE_HW_GUI_LICENSING"))
      credits_per_hour = n_sps * 0.14;
    else
      credits_per_hour = n_sps * (0.14 + 0.0024); // 0.14 is for solver HW; 0.0024 is for GUI HW
  }
  return credits_per_hour;
}

static VOID seize_exasim_licenses(asINT32 n_sps, BOOLEAN use_opt_licenses, 
                                  BOOLEAN opt_license_failover, // if opt licenses not available
                                  cSTRING job_name)
{
  license_n_sps = n_sps;
  n_standard_licenses = n_sps;  // will be changed in DSLS mode due to universal tokens and credits
  current_license_checkout = n_sps;

  if (exalic_use_dsls_feature_names()) {
    EXALIC_STATUS token_seize_status = EXALIC_STATUS_SUCCESS;
    EXALIC_STATUS credit_seize_status = EXALIC_STATUS_SUCCESS;
    use_opt_licenses = FALSE; // Not supported with DSLS yet
    cSTRING license_model_str = getenv("GRID_DSLS_LICENSE_MODEL");
    on_3dx_cloud = license_model_str;
    if (license_model_str == NULL)
      license_model_str = getenv("EXA_DSLS_LICENSE_MODEL");
    dFLOAT n_tokens = dsls_n_sru_tokens(n_sps);
    if (on_3dx_cloud && !getenv("GRID_DISABLE_SW_GUI_LICENSING")) {
      dFLOAT n_tokens_gui = dsls_n_sru_gui_tokens(n_sps);
      n_standard_licenses = (asINT32)(n_tokens + 0.5) + (asINT32)(n_tokens_gui + 0.5); // round components separately
      n_tokens += n_tokens_gui;
    } else
      n_standard_licenses = n_tokens + 0.5; // round
    disable_hw_licensing = getenv("GRID_DISABLE_HW_LICENSING");
    if (on_3dx_cloud && !disable_hw_licensing)
      n_standard_licenses += dsls_n_sru_hw_tokens(n_sps);
    bool force_use_credits = license_model_str && (strcmp(license_model_str, "CREDIT") == 0);
    if (force_use_credits)
      using_dsls_credits = true; // set here so any error messages are correct
    // Try SRU (tokens) first, and if not available, try SUN (credits), unless GRID_DSLS_LICENSE_MODEL is set
    if (!force_use_credits 
        && ((token_seize_status = exalic_license_model(proc_connect, LICENSE_MODEL_TOKEN)) == EXALIC_STATUS_SUCCESS)
	&& ((token_seize_status = seize_licenses(sru_feat_name, n_standard_licenses)) == EXALIC_STATUS_SUCCESS)) {
      dsls_license_model = LICENSE_MODEL_TOKEN;
      standard_feat_name = sru_feat_name;
      current_license_checkout = n_standard_licenses;
      msg_print("Using %d universal SRU token licenses", n_standard_licenses);
      wds.numTokens = n_standard_licenses;
    } else {
      dFLOAT n_credits_per_hour = dsls_n_sun_credits_per_hour(n_tokens); // n_tokens does not include HW tokens
      wds.rateSwCredits = n_credits_per_hour;
      if (on_3dx_cloud && !disable_hw_licensing) {
        dFLOAT n_hw_credits_per_hour = dsls_n_sun_hw_credits_per_hour(n_sps);
        wds.rateHwCredits = n_hw_credits_per_hour;
        n_credits_per_hour += n_hw_credits_per_hour;
      }
      // n_standard_licenses only used in error messages when using credits
      dsls_n_credits_per_second = n_credits_per_hour / (60.0 * 60.0);
      const dFLOAT reserve_credits_secs = secs_between_license_heartbeats; // 5 minutes into the future
      current_license_checkout = dsls_n_credits_per_second * reserve_credits_secs;
      if (current_license_checkout < 1)
	current_license_checkout = 1;
      bool force_use_tokens = !force_use_credits && license_model_str; // any setting of GRID_DSLS_LICENSE_MODEL other than "CREDIT"
      if (!force_use_tokens
          && ((credit_seize_status = exalic_license_model(proc_connect, LICENSE_MODEL_CREDIT)) == EXALIC_STATUS_SUCCESS)
	  && ((credit_seize_status = seize_licenses(sun_feat_name, current_license_checkout)) == EXALIC_STATUS_SUCCESS)) {
	dsls_license_model = LICENSE_MODEL_CREDIT;
	standard_feat_name = sun_feat_name;
        if (!on_3dx_cloud)
          msg_print("Using universal SUN credit licenses (rate is %g credits/hour, corresponding to %d SRU tokens)", 
                    n_credits_per_hour, n_standard_licenses);
        else
          // On 3DX Cloud, we don't know the corresponding number of hardware related SRU tokens, so don't print the
          // corresponding number of SRU tokens
          msg_print("Using universal SUN credit licenses (rate is %g credits/hour)", 
                    n_credits_per_hour);
	dsls_credits_start_time = wallclock_time_secs();
	using_dsls_credits = true;
	dsls_credits_reserved = current_license_checkout;
      } else {
        EXALIC_STATUS seize_status = token_seize_status;
        if (force_use_credits
            || (exalic_does_not_support_feature(token_seize_status)
                && !exalic_does_not_support_feature(credit_seize_status)))
          seize_status = credit_seize_status;
	check_license_error(n_standard_licenses, seize_status, 
                            (force_use_credits ? "SUN credits" : "SRU tokens"), 
                            ((force_use_credits || force_use_tokens) ? NULL : "SUN credits"));	
      }
    }
    wds.jobDone = false;
    wds.licenseFeatureName = standard_feat_name;
    wds.jobName = job_name;
    if (on_3dx_cloud)
      exalic_send_3dorchestrate_data(wds);
  } else {
    // RLM
    EXALIC_STATUS seize_status;
    if (use_opt_licenses
	&& ((seize_status = seize_licenses(opt_feat_name, n_sps)) == EXALIC_STATUS_SUCCESS)) {
      standard_feat_name = opt_feat_name;
      msg_print("Using optimization licenses instead of regular simulation licenses"); 
    } else if ((!use_opt_licenses || opt_license_failover)
	       && ((seize_status = seize_licenses(base_feat_name, n_sps)) == EXALIC_STATUS_SUCCESS)) {
      standard_feat_name = base_feat_name;
      if (use_opt_licenses)
	msg_print("Optimization licenses unavailable. Failing over to regular simulation licenses."); 
    } else {
      standard_feat_name = NULL;
      n_standard_licenses = 0;
      char *feat_name1 = use_opt_licenses ? opt_feat_name : base_feat_name;
      char *feat_name2 = use_opt_licenses && opt_license_failover ? base_feat_name : NULL;
      check_license_error(n_sps, seize_status, feat_name1, feat_name2);				
    }
  }										
}

#define try_until_success(expr, msg, block)								\
{													\
  EXALIC_STATUS status = expr;										\
  if (status != EXALIC_STATUS_SUCCESS) {								\
    block;												\
    /* loop until success */										\
    EXALIC_STATUS last_status = status + 1; /* init last_status to something different */		\
    while (status != EXALIC_STATUS_SUCCESS) {								\
      if (status != last_status) {									\
	msg_print ("\n%s (%s)."										\
		   " The license system returned this error: %s%s%s"					\
		   " Waiting until contact is reestablished.",						\
		   msg, time_string(), esp, exalic_error_string(proc_connect, status), ess);            \
													\
	jobctl_server_set_phase("License server connection lost.  Suspended.",0.0,0.0);			\
	last_status = status;										\
      }													\
      sleep(10);											\
      status = expr;											\
    }													\
    jobctl_server_set_phase("License server connection reestablished.  Resumed.",0.0,0.0);		\
													\
    msg_print("\nContact reestablished with license server (%s). Continuing.\n",			\
	      time_string());										\
  }													\
}
  
static VOID detect_proc_license_guts(asINT32 n_sps, BOOLEAN use_opt_licenses, 
                                     BOOLEAN opt_license_failover, // if opt licenses not available
                                     cSTRING job_name)
{
  char env_string[] = "EXA_ALLOW_RLM_QUEUE=1";
  putenv(env_string);
  EXALIC_STATUS connect_status = exalic_connect(FALSE,		     
						NULL,		     
					        &proc_connect);  
								     
  if (connect_status != EXALIC_STATUS_SUCCESS) {		     
    jobctl_server_set_phase("License server not found.  Aborted.",0.0,0.0);  
    msg_error ("Unable to contact the license server for the PowerFLOW simulator."  
	       " The license system returned this error: %s%s%s",
	       esp, exalic_error_string(proc_connect, connect_status), ess);		     
    exit (EXIT_FAILURE);
  }
  if (exalic_use_rlm_feature_names()) {
    esp  = "\"";   // error string prefix: \n\n for DSLS, \" for RLM
    esp2 = "";     // error string prefix: \n for DSLS, nothing for RLM
    ess  = "\".";  // error string suffix: nothing for DSLS, \". for RLM
  }
  exalic_override_user_host_display(proc_connect,		     
				    getenv("EXA_LM_USER"),	     
				    getenv("EXA_LM_HOST"),	     
				    getenv("EXA_LM_DISPLAY"));     
  seize_exasim_licenses(n_sps, use_opt_licenses, opt_license_failover, job_name);				     
  if (n_standard_licenses > 0)				     
    check_license_expiration(proc_connect, standard_feat_name);
}

inline STRING lstrsave(cSTRING s) 
{
  STRING t = new char[strlen(s) + 1];
  strcpy(t, s);
  return t;
}

static VOID detect_aux_license_guts(cSTRING feature_name, asINT32 license_count, cSTRING used_for)
{
  // all aux licenses are token, not credit
  if (exalic_use_dsls_feature_names())
    exalic_license_model(proc_connect, LICENSE_MODEL_TOKEN);
    
  EXALIC_STATUS seize_status = seize_licenses(feature_name, license_count);

  // restore license model for the standard licenses
  if (exalic_use_dsls_feature_names())
    exalic_license_model(proc_connect, dsls_license_model);

  if (seize_status != EXALIC_STATUS_SUCCESS) {
    CHARACTER licstat_buf[1024];						
    cSTRING error_string = exalic_error_string(proc_connect, seize_status);			
    sprintf(licstat_buf, "%s licenses (%d) not available.  Aborted: %s",	
	    feature_name, license_count, error_string);					
    jobctl_server_set_phase(licstat_buf,0.0,0.0);				
    msg_error ("Unable to obtain %d %s license%s"	
               "%s%s. "
	       "The license system returned this error: "			
	       "%s%s%s",							
	       license_count, feature_name,  license_count > 1 ? "s" : "",
               used_for ? " " : "",
               used_for ? used_for : "",
	       esp, error_string, ess);				
    exit (EXIT_FAILURE);							
  }    
  
  check_license_expiration(proc_connect, feature_name);

  LICENSE_DESC license_desc = new sLICENSE_DESC;
  license_desc->feature_name = lstrsave(feature_name);
  license_desc->license_count = license_count;
  license_desc->current_checkout = license_count;
  license_desc->next = aux_license_descs;
  aux_license_descs = license_desc;
}

#else

#define detect_proc_license_guts(license_count)
#define detect_aux_license_guts(feature_name, license_count)
#define detect_proc_license_still_okay_guts(license_count)
#define terminate_licenses()

#endif

