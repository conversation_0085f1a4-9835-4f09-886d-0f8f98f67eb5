/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#include "assert.h"
#include "cdi_common.h"
#include "cdi_io.h"
#include "cdi_encrypted_io.h"

#include <iostream>
#include <set>

#include CCUTILS_H
#include CIPHER_H


/****************************************************************        \
|
| Function name:cdi_is_encryption_on, off, etc
|
| Purpose: cache and access encryption state
|
\****************************************************************/

bool cdi_is_encryption_on(CDI_INFO cdi_info)
{
  return (cdi_get_cipher(cdi_info) && cdi_info->encryption_on == 1);
}

void cdi_set_encryption_off(CDI_INFO cdi_info)
{
  cdi_info->encryption_on = 0;
  return;
}

void cdi_set_encryption_on_if_needed(CDI_INFO cdi_info, CIO_CCCC type)
{
  if (cdi_is_chunk_encrypted(cdi_info, type)) {
    cdi_set_encryption_on(cdi_info);
  }

  return;
}

void cdi_set_encryption_on(CDI_INFO cdi_info)
{
  if (cdi_get_cipher(cdi_info)) {
    cdi_seed_cipher(cdi_info);
    cdi_info->encryption_on = 1 ;
  }
  
  return;
}

void cdi_seed_cipher(CDI_INFO cdi_info)
{
  if (!cdi_get_cipher(cdi_info))
    return;

  cdi_get_cipher(cdi_info)->Seed();
}

bool cdi_is_chunk_encrypted(CDI_INFO cdi_info, CIO_CCCC type)
{
  bool encrypted = false;

  if (!cdi_get_cipher(cdi_info))
    return false;

  if (type == CDI_CHUNK_TYPE_HXCH) {
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 12))
      encrypted = true;
  } else if (type == CDI_CHUNK_TYPE_RGPN || 
             type == CDI_CHUNK_TYPE_VRTX) {
    encrypted = true;
  } else  if (type == CDI_CHUNK_TYPE_CVDP) {
    // Gather parents of the stack
    CIO_INFO cio = cdi_info->cio_info;
    std::set<CIO_CCCC> parents;
    for (auto i = cio->depth; i > 0 ; --i) {
      parents.insert(cio->stack[cio->depth-i].cccc);
    }

    // Chunk is encrypted if it has all these parents
    encrypted = parents.find(CDI_CHUNK_TYPE_PHRG) != parents.end() &&
      parents.find(CDI_CHUNK_TYPE_DPRM) != parents.end() &&
      parents.find(CDI_CHUNK_TYPE_PHYS) != parents.end();
  } // else if (more special cases)
 
  return encrypted;
}

cCIPHER_STREAM * cdi_get_cipher(CDI_INFO cdi_info)
{
  if (cdi_info)
    return reinterpret_cast<cCIPHER_STREAM*>(cdi_info->cipher);
  else
    return NULL;
}

sINT64 cdi_get_encryption_version(CDI_INFO cdi_info)
{
  cCIPHER_STREAM* cipher = cdi_get_cipher(cdi_info);
  if (cipher)
    return cipher->GetVersion();
  else
    return 0;
}

bool cdi_is_file_encrypted(CDI_INFO cdi_info)
{
  return (cdi_get_cipher(cdi_info) != NULL);
}

/****************************************************************        \
|
| Function name:cdi_create_cipher
|
| Purpose: create cipher used to encrypt data
| Can only be called once the CDI file id is known
| The cipher is used to encode and decode the RGPN and VRTX chunks in that order
|
\****************************************************************/
VOID cdi_create_cipher(CDI_INFO cdi_info, sINT64 cdi_file_id, sINT64 encryption_version)
{
  if (encryption_version != 0) {
    // Version 1 is invalid, was only used in DENSO prototype
    // However version 1 and 2 are equivalent for other files (NASTRAN, CASE)
    if (!cCIPHER_STREAM::IsVersionValid(encryption_version)) {
      msg_error("Invalid encryption version for CDI file, the encrypted data cannot be read.\n");  
    } else if (encryption_version == 1 ) {
      msg_error("Invalid encryption version for CDI file, the encrypted data cannot be read.\n The file was generated by a prototype version of PowerFLOW.");  
    }
    cdi_info->cipher = new cCIPHER_STREAM(cdi_file_id, encryption_version);
  } 
  else
    cdi_info->cipher = NULL;
}

/****************************************************************        \
|
| Function name:cdi_destroy_cipher
|
| Purpose: destroy cipher used to encrypt data
|
\****************************************************************/
void cdi_destroy_cipher(CDI_INFO cdi_info)
{
  cCIPHER_STREAM *cipher = cdi_get_cipher(cdi_info);
  delete cipher;
  cdi_info->cipher = NULL;
}

/** For undump_cdi 
 * At the beginning of each possibly encrypted chunk, add the call
 *
 * if (undump_encoded64_chunk(cdi_info, CDI_CHUNK_TYPE_RGPN))
 *   return;
 * 
 * which will call cdi_write_and_decode64
 */
void 
cdi_write_and_decode64(CDI_INFO cdi_info,
                       std::string &encoded,
                       CIO_CCCC type)
{
  cdi_push(cdi_info, type);
  std::string buffer = EXA_STR::Decode64(encoded);
  cio_write_chars(cdi_info->cio_info, buffer.c_str(), buffer.size());

  cdi_pop(cdi_info);

  //  std::cerr << __func__ << " encoded64 size " << encoded.length() << std::endl;
}

/** For dump_cdi
 * At the beginning of each possibly encrypted chunk, add the call
 *
 * if (print_encoded64_chunk(cdi_info,CDI_CHUNK_TYPE_RGPN, depth))
 *     return;
 *
 * which will call cdi_read_chunk_and_encode64
 */
asINT32
cdi_read_chunk_and_encode64(CDI_INFO cdi_info,
                            std::string &encoded)
{
  encoded.clear();
  size_t num_chars = cio_get_size(cdi_info->cio_info);
  char *buffer = new char[num_chars];
  if(cio_read_chars(cdi_info->cio_info, buffer, num_chars))
    encoded = EXA_STR::Encode64(buffer, num_chars);
  delete[] buffer;

  //  std::cerr << __func__ << " buffer size " << num_chars << " -> encoded64 size " << encoded.length() << std::endl;

  return static_cast<asINT32>(encoded.size());
}
