#
# CMAKE Input File
#
# Used for generating a Visual Studio Project which then can be combined
# with other components into a single VS Solution on the user's local hard
# drive or some other convenient location
#

# This section divides the (large number of) source files into sub-folders
# in the Visual Studio "Solution Explorer" window
file (GLOB CDI_HEADER_FILES "*.h")
source_group("Header Files" FILES ${CDI_HEADER_FILES})

file (GLOB CDI_IMPLEMENTATION_FILES "*.cc")
source_group("CC Files" FILES ${CDI_IMPLEMENTATION_FILES})

include_directories (.)

# This is a static library
add_library (cdi STATIC ${CDI_HEADER_FILES} ${CDI_IMPLEMENTATION_FILES})
