/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * This file describes the Dynamic Scalar Multiplier structure
 *
 * Dalon Work, Exa Corporation 
 * Created May 25, 2017 
 *--------------------------------------------------------------------------*/

#include "dsm_reader.h"
#include "cp_info.h"
#include <vector>
#include <iostream>
#include <fstream>

cDSM_READER::cDSM_READER() : 
  m_last_dsm_read_timestep(-1),
  m_meas_window_index(-1),
  m_meas_window_name(NULL),
  m_is_per_voxel(-1),
  m_send_requests(NULL){};

cDSM_READER::~cDSM_READER() {
}

void cDSM_READER::cancel_pending_send_requests() {
  // Canceling all the dsm pending send requests
  if (m_send_requests != NULL) {
    ccDOTIMES(sp, total_sps) {
      MPI_Cancel(&m_send_requests[sp]);
    }
    delete [] m_send_requests;
  }
}

void cDSM_READER::read_dsm_data_and_send_to_sps() {

  if (m_meas_window_index < 0) {
    msg_internal_error("DSMs cannot be read. Either measurement window name not specified or did not match any existing windows.");
  }

  BOOLEAN is_called_first_time = m_data_per_sp.empty();
  std::vector< std::vector<sFLOAT>::iterator > curr_data_loc;

  if (is_called_first_time) {
    m_data_per_sp.resize(total_sps);
    m_send_requests = cnew MPI_Request [total_sps];

    // Pass the # of voxels and the meas window index to the sps
    ccDOTIMES(sp, total_sps) {
      m_data_per_sp[sp].push_back(m_is_per_voxel);
      m_data_per_sp[sp].push_back(m_meas_window_index);
    }
  } 
  else {
    curr_data_loc.resize(total_sps);
    ccDOTIMES(sp, total_sps) {
      int flag;
      MPI_Test(&m_send_requests[sp],&flag, MPI_STATUS_IGNORE);
      if (!flag) {
        msg_warn("DSM send request to SP %d has not been completed. Refusing to send dsm data.", sp);
        return;
      }

      // skip is_per_voxel and meas_window_index data
      curr_data_loc[sp] = m_data_per_sp[sp].begin()+2;
    }
  }

  FILE *bin_fp = fopen(m_dsm_data_filename.c_str(), "rb");
  if (bin_fp == NULL) {
    msg_internal_error("Could not open file %s", m_dsm_data_filename.c_str());
  }

  asINT32 n_meas_cubes = 0;
  msg_print("Reading DSMs from file %s", m_dsm_data_filename.c_str());
  fread(&n_meas_cubes, sizeof(int), 1, bin_fp);
  asINT32 max_meas_cell_index = *max_element(m_ublk_to_mci.begin(), m_ublk_to_mci.end());
  if(n_meas_cubes != max_meas_cell_index) {
    msg_internal_error("Number of DSMs %d in file does not match internal mci count of %d", n_meas_cubes, max_meas_cell_index);
  }
  asINT32 home_sp = 0;
  asINT32 nreads = -1;
  asINT32 last_nreads = -1;
  sFLOAT dsm_float[8]; 

  asINT32 last_mci = 0;

  ccDOTIMES(nu, cp_info.num_ublks[STP_FLOW_REALM]) {
    asINT32 curr_mci = m_ublk_to_mci[nu];
    // ublks that do not contribute to dsm meas_window have mci = 0,
    // so we can skip these, and we do not update last_mci or last_nreads
    if (curr_mci == 0) { 
      continue; 
    }

    // split ublks point to the same mci, old data is used instead of reading
    if (last_mci != curr_mci) { 
      nreads = curr_mci - last_mci;

      fread(dsm_float, sizeof(sFLOAT), nreads, bin_fp);
      if ( feof(bin_fp) ) {
        msg_internal_error("Error reading DSMs from file %s: End-Of-File reached", m_dsm_data_filename.c_str());
      }

      last_mci = curr_mci;
    }
    else {
      nreads = last_nreads;
    }

    if (total_sps > 1) {
      home_sp = cp_info.ublk_procs[STP_FLOW_REALM][nu];
    }

    last_nreads = nreads; 

    // build vectors the first time, overwrite the rest of the time
    if (is_called_first_time) {
      m_data_per_sp[home_sp].insert(m_data_per_sp[home_sp].end(), dsm_float, dsm_float+nreads);
    } else {
      ccDOTIMES(n, nreads) {
        *(curr_data_loc[home_sp]++) = dsm_float[n];
      }
    }
  }

  fclose(bin_fp);
  ccDOTIMES(sp, total_sps) {
#if DEBUG
    printf("Sending %ld floats to SP %d first float: %e\n", m_data_per_sp[sp].size(), sp, m_data_per_sp[sp][2] );
    fflush(stdout);
#endif
    MPI_Isend(m_data_per_sp[sp].data(), m_data_per_sp[sp].size(), eMPI_sFLOAT, sp,
        eMPI_ASYNC_DSM_DATA_TAG, eMPI_sp_cp_comm, &m_send_requests[sp]);
  }
  //free(n_dsms_per_sp);
}

void cDSM_READER::add_null_ublk_entry() {
    m_ublk_to_mci.push_back(0);
}

void cDSM_READER::add_voxel_based_mci(STP_UBLK_ID ublk_id,
                                      auINT32 meas_cell_voxel_mask,
                                      STP_MEAS_CELL_INDEX meas_cell_index,
                                      asINT32 cube_offset) {
  m_is_per_voxel = 1;

  asINT32 n_bits_in_meas_voxel_mask = bitcount8(meas_cell_voxel_mask);

  STP_MEAS_CELL_INDEX meas_cell_index_for_dsm_read =
    meas_cell_index + (cube_offset+1)*n_bits_in_meas_voxel_mask;

  m_ublk_to_mci.push_back(meas_cell_index_for_dsm_read);
}

void cDSM_READER::add_ublk_based_mci(STP_UBLK_ID ublk_id,
                                     STP_MEAS_CELL_INDEX meas_cell_index, 
                                     asINT32 cube_offset) {
  m_is_per_voxel = 0;

  STP_MEAS_CELL_INDEX  meas_cell_index_for_dsm_read = meas_cell_index + cube_offset+1;

  m_ublk_to_mci.push_back(meas_cell_index_for_dsm_read);
}

