/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Separate process talking to license manager
 *--------------------------------------------------------------------------*/

#include <stdarg.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <strings.h>
#include <errno.h>
#include <ctype.h>

#include "license_aux.h"
#include "license.h"
#include "license_guts.h"

#include MSGERR_H
#include PLATFORM_H
#include LOOP_H
#include CCUTILS_H

#include JOBCTL_SERVER_H

static int license_pipe_fd = -1;

static pid_t my_pid;

static void terminate_handler(int signal)
{
  // We want the main loop to read EOF from license_pipe_fd and relinquish all licenses
  if (license_pipe_fd >= 0) {
    close(license_pipe_fd);
    license_pipe_fd = -1;
  }
  return;
}

static inline VOID read_lm_chars(STRING buf, asINT32 n_bytes_to_read)
{
  asINT32 n_bytes = read_all(license_pipe_fd, buf, n_bytes_to_read);
  if (n_bytes != n_bytes_to_read) {
    terminate_licenses();
    exit(0);
  }
}

static inline LM_INT read_lm_int()
{
  LM_INT i;
  asINT32 nbytes = read_all(license_pipe_fd, &i, sizeof(i));
  if (nbytes != sizeof(i)) {
    terminate_licenses();
    exit(0);
  }
  
  return i;
}

static inline LM_INT read_and_decrypt_lm_int()
{
  LM_INT i = read_lm_int();
  return lm_decrypt_int(i, my_pid);
}

static inline VOID write_lm_int(LM_INT i)
{
  asINT32 nbytes = write_all(license_pipe_fd, &i, sizeof(i));

  if (nbytes != sizeof(i)) {
    terminate_licenses();
    exit(0);
  }
}

static inline VOID encrypt_and_write_lm_int(LM_INT i)
{
  LM_INT j = lm_encrypt_int(i, my_pid);
  write_lm_int(j);
}

// The license process exits with an error code of 0 if it did not issue
// an error message and jobctl status before exiting. Thus it exits with
// an exit code of 0 whenever there is a problem with communication over
// the pipe to the parent process.
int main(int argc, char **argv)
{
  // See if the license diagnostics alone are requested...
  if (EXA_ENV::HaveEnv("EXA_LICENSE_DIAGNOSTICS_PATH")) {
    // Collect location for the license diagnostics
    std::string license_diagnostics = EXA_ENV::GetEnv("EXA_LICENSE_DIAGNOSTICS_PATH");

    // Activate the standard diagnostics
    EXA_ENV::SetEnv("EXA_DIAGNOSTICS_PATH", license_diagnostics);

    // Provide for DSLS diagnostics
    EXA_ENV::SetEnv("DSLCC_TRACE_PATH", EXA_PATH::Join(license_diagnostics, "dslcc" + EXA_STR::GetPID() + ".log"));
    EXA_ENV::SetEnv("DSLCC_VERBOSE", "True");
  }

  platform_exa_debug_pfx(&argc, argv);

  if (argc != 3) {
    msg_print("Usage: %s socket_fd job_name", argv[0]);
    exit(EXIT_FAILURE);
  }

  jobctl_server_start(); // Ignore SIGUSR1, etc

  // Upon an untimely death, this process is relaunched by its parent (the CP).
  // We do not want this process to dump core.
  jobctl_server_sigset_die_signals(terminate_handler);
  jobctl_server_sigset(SIGPIPE, terminate_handler);

  license_pipe_fd = atoi(argv[1]);
  my_pid = getpid();

  get_license_heartbeat_interval();
  
  auINT32 opt_licence_flags = read_and_decrypt_lm_int();
  BOOLEAN use_opt_licenses = opt_licence_flags & 1;
  BOOLEAN opt_license_failover = opt_licence_flags & 2;
  asINT32 license_count = read_and_decrypt_lm_int();

  cSTRING job_name = argv[2];

  cSTRING lm_alt = getenv("EXA_LM_ALT");
  init_license_strings(lm_alt);
  detect_proc_license_guts(license_count, use_opt_licenses, opt_license_failover, job_name);


  // Send number of standard licenses checked out
  encrypt_and_write_lm_int(n_standard_licenses);

  // Loop forever looking for messages from the parent process.
  // Upon receipt of a message, verify that the license connection
  // is still ok, and write an indicator back to the parent.
  //
  // If the parent has exited, we will fail on either a read or
  // write and thus exit. Alternatively, we may take a SIGPIPE
  // causing an exit.

  LM_INT tag_ok = lm_tag_ok(my_pid);
  LM_INT tag_heartbeat = lm_tag_heartbeat(my_pid);
  LM_INT tag_aux_license = lm_tag_aux_license(my_pid);
  LM_INT tag_release = lm_tag_release(my_pid);

  while (1) {
    LM_INT tag = read_lm_int();

    // We never fail: we just wait for the licenses to become
    // available again
    if (tag == tag_heartbeat) {
      detect_proc_license_still_okay_guts();
    } 
    else if (tag == tag_aux_license) {
      asINT32 n_licenses = read_and_decrypt_lm_int();
      asINT32 feature_name_len = read_and_decrypt_lm_int();
      if (feature_name_len > 1024)
	msg_internal_error("Aux license feature name exceeds max length");
      char feature_name[1024];
      read_lm_chars(feature_name, feature_name_len);

      asINT32 used_for_len = read_and_decrypt_lm_int();
      if (used_for_len == 0) {
        detect_aux_license_guts(feature_name, n_licenses, NULL);
      } else {
        if (used_for_len > 1024)
          msg_internal_error("Aux license feature name exceeds max length");
        char used_for[1024];
        read_lm_chars(used_for, used_for_len);

        detect_aux_license_guts(feature_name, n_licenses, used_for);
      }
    }
    else if (tag == tag_release) {
      terminate_licenses();
    }
    else {
      msg_error("License manager inconsistency.");
    }
         
    write_lm_int(tag_ok);
  }
}

