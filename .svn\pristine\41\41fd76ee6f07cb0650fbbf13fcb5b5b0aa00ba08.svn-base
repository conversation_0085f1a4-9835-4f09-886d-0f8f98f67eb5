/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#include "cdi_tempDepParms.h"
#include "cdi_common.h"
// Liquid Water Properties
#define CDI_LM_WATER_T_F                       0.01   // degC
#define CDI_LM_WATER_T_B                       99.99  // degC
#define CDI_LM_WATER_DENSITY                   998    // kg/m^3
#define CDI_LM_WATER_SPEC_HEAT                 4182   // J/(kg * K)

// Liquid ETHYLENE_GLYCOL_30 Properties
#define CDI_LM_ETHYLENE_GLYCOL_30_T_F          -15.0  // degC
#define CDI_LM_ETHYLENE_GLYCOL_30_T_B          104.0  // degC
#define CDI_LM_ETHYLENE_GLYCOL_30_DENSITY      1047   // kg/m^3
#define CDI_LM_ETHYLENE_GLYCOL_30_SPEC_HEAT    3647   // J/(kg * K)

// Liquid ETHYLENE_GLYCOL_50 Properties
#define CDI_LM_ETHYLENE_GLYCOL_50_T_F          -30.0  // degC
#define CDI_LM_ETHYLENE_GLYCOL_50_T_B          107.0  // degC
#define CDI_LM_ETHYLENE_GLYCOL_50_DENSITY      1075   // kg/m^3
#define CDI_LM_ETHYLENE_GLYCOL_50_SPEC_HEAT    3282   // J/(kg * K)

// Liquid FC_77 Properties
#define CDI_LM_FC_77_T_F                       5.0    // degC
#define CDI_LM_FC_77_T_B                       85.0   // degC
#define CDI_LM_FC_77_DENSITY                   1788   // kg/m^3
#define CDI_LM_FC_77_SPEC_HEAT                 1041   // J/(kg * K)

dFLOAT cdi_get_boiling_temp_in_degC(CDI_LIQUID_MATERIAL liquidMaterialType)
{
  switch (liquidMaterialType) {
    case CDI_LM_WATER:
      return CDI_LM_WATER_T_B;
    case CDI_LM_ETHYLENE_GLYCOL_30:
      return CDI_LM_ETHYLENE_GLYCOL_30_T_B;
    case CDI_LM_ETHYLENE_GLYCOL_50:
      return CDI_LM_ETHYLENE_GLYCOL_50_T_B;
    case CDI_LM_FC_77:
      return CDI_LM_FC_77_T_B;
    default:
      return -273.15;
  }
}

dFLOAT cdi_get_freezing_temp_in_degC(CDI_LIQUID_MATERIAL liquidMaterialType)
{
  switch (liquidMaterialType) {
    case CDI_LM_WATER:
      return CDI_LM_WATER_T_F;
    case CDI_LM_ETHYLENE_GLYCOL_30:
      return CDI_LM_ETHYLENE_GLYCOL_30_T_F;
    case CDI_LM_ETHYLENE_GLYCOL_50:
      return CDI_LM_ETHYLENE_GLYCOL_50_T_F;
    case CDI_LM_FC_77:
      return CDI_LM_FC_77_T_F;
    default:
      return -273.15;
  }
}

dFLOAT cdi_get_liquid_material_density(CDI_LIQUID_MATERIAL liquidMaterialType)
{
  switch (liquidMaterialType) {
    case CDI_LM_WATER:
      return CDI_LM_WATER_DENSITY;
    case CDI_LM_ETHYLENE_GLYCOL_30:
      return CDI_LM_ETHYLENE_GLYCOL_30_DENSITY;
    case CDI_LM_ETHYLENE_GLYCOL_50:
      return CDI_LM_ETHYLENE_GLYCOL_50_DENSITY;
    case CDI_LM_FC_77:
      return CDI_LM_FC_77_DENSITY;
    default:
      return 0.0;
  }
}

dFLOAT cdi_get_liquid_material_spec_heat(CDI_LIQUID_MATERIAL liquidMaterialType)
{
  switch (liquidMaterialType) {
    case CDI_LM_WATER:
      return CDI_LM_WATER_SPEC_HEAT;
    case CDI_LM_ETHYLENE_GLYCOL_30:
      return CDI_LM_ETHYLENE_GLYCOL_30_SPEC_HEAT;
    case CDI_LM_ETHYLENE_GLYCOL_50:
      return CDI_LM_ETHYLENE_GLYCOL_50_SPEC_HEAT;
    case CDI_LM_FC_77:
      return CDI_LM_FC_77_SPEC_HEAT;
    default:
      return 0.0;
  }
}

VOID sCDI_LIQUID_PARAMS::init(CDI_LIQUID_MATERIAL liquidMaterialType)
{
  switch (liquidMaterialType) {
  case CDI_LM_WATER: {
    liq_T_b = CDI_LM_WATER_T_B;
    liq_T_f = CDI_LM_WATER_T_F;
    x1 = 0.1;
    x2 = 25.0;
    x3 = 50.0;
    x4 = 75.0;
    x5 = 99.0;
    liq_v1 = 1.72;
    liq_v2 = 0.89;
    liq_v3 = 0.54;
    liq_v4 = 0.38;
    liq_v5 = 0.29;
    liq_v6 = 0.0;
    liq_v7 = 0.0;
    liq_pr1 = 13.0;
    liq_pr2 = 6.1;
    liq_pr3 = 3.46;
    liq_pr4 = 2.355;
    liq_pr5 = 1.666;
    liq_pr6 = 0.0;
    liq_pr7 = 0.0;
    break;
  }
  case CDI_LM_ETHYLENE_GLYCOL_30: {
    liq_T_b = CDI_LM_ETHYLENE_GLYCOL_30_T_B;
    liq_T_f = CDI_LM_ETHYLENE_GLYCOL_30_T_F;
    x1 = 0.1;
    x2 = 30.0;
    x3 = 60.0;
    x4 = 80.0;
    x5 = 103.0;
    liq_v1 = 3.9;
    liq_v2 = 1.6;
    liq_v3 = 0.87;
    liq_v4 = 0.64;
    liq_v5 = 0.47;
    liq_v6 = 0.000023;
    liq_v7 = 0.0;
    liq_pr1 = 35.22;
    liq_pr2 = 13.65;
    liq_pr3 = 7.1;
    liq_pr4 = 5.09;
    liq_pr5 = 3.7;
    liq_pr6 = 0.00023;
    liq_pr7 = 0.0; 
    break;
  }
  case CDI_LM_ETHYLENE_GLYCOL_50: {
    liq_T_b = CDI_LM_ETHYLENE_GLYCOL_50_T_B;
    liq_T_f = CDI_LM_ETHYLENE_GLYCOL_50_T_F;
    x1 = 0.0;
    x2 = 40.0;
    x3 = 70.0;
    x4 = 90.0;
    x5 = 105.0;
    liq_v1 = 7.5;
    liq_v2 = 2.12;
    liq_v3 = 1.12;
    liq_v4 = 0.79;
    liq_v5 = 0.637;
    liq_v6 = 0.000021;
    liq_v7 = 0.066667;
    liq_pr1 = 73.7;
    liq_pr2 = 20.1;
    liq_pr3 = 10.4;
    liq_pr4 = 7.36;
    liq_pr5 = 5.9;
    liq_pr6 = 0.00021;
    liq_pr7 = 1.0; 
    break;
  }
  case CDI_LM_FC_77: {
    liq_T_b = CDI_LM_FC_77_T_B;
    liq_T_f = CDI_LM_FC_77_T_F;
    x1 = 5.0;
    x2 = 25.0;
    x3 = 45.0;
    x4 = 65.0;
    x5 = 85.0;
    liq_v1 = 1.0;
    liq_v2 = 0.69;
    liq_v3 = 0.5;
    liq_v4 = 0.37;
    liq_v5 = 0.31;
    liq_v6 = 0.0;
    liq_v7 = 0.0;
    liq_pr1 = 28.87;
    liq_pr2 = 20.48;
    liq_pr3 = 15.25;
    liq_pr4 = 11.58;
    liq_pr5 = 9.95;
    liq_pr6 = 0.0;
    liq_pr7 = 0.0;
    break;
  }
  default:
    return;
  }

  cx1 = (x1 - x2) * (x1 - x3) * (x1 - x4) * (x1 - x5);
  cx2 = (x2 - x1) * (x2 - x3) * (x2 - x4) * (x2 - x5);
  cx3 = (x3 - x1) * (x3 - x2) * (x3 - x4) * (x3 - x5);
  cx4 = (x4 - x1) * (x4 - x2) * (x4 - x3) * (x4 - x5);
  cx5 = (x5 - x1) * (x5 - x2) * (x5 - x3) * (x5 - x4);
 
  return;
}

dFLOAT cdi_get_temp_dep_prop_value(CDI_LIQUID_MATERIAL liquidMaterialType,
                                   CDI_TEMP_DEP_PROP tempDepPropType,
                                   dFLOAT tempInDegK)
{

  if (liquidMaterialType == CDI_LM_USER_DEFINED)
    return -1.0;

  sCDI_LIQUID_PARAMS lpms;
  lpms.init(liquidMaterialType);

  dFLOAT yy = tempInDegK - 273.15;

  if (yy < lpms.liq_T_f) yy = lpms.liq_T_f;
  if (yy > lpms.liq_T_b) yy = lpms.liq_T_b;

  dFLOAT yy0 = -yy;
  dFLOAT yy4 = yy * yy * yy * yy;
  if (yy > 0.0) {
    yy0 = 0.0;
    yy4 = 0.0;
  }

  dFLOAT xz1 = yy - lpms.x1;
  dFLOAT xz2 = yy - lpms.x2;
  dFLOAT xz3 = yy - lpms.x3;
  dFLOAT xz4 = yy - lpms.x4;
  dFLOAT xz5 = yy - lpms.x5;

  dFLOAT y1 = xz2 * xz3 * xz4 * xz5 / lpms.cx1;
  dFLOAT y2 = xz1 * xz3 * xz4 * xz5 / lpms.cx2;
  dFLOAT y3 = xz1 * xz2 * xz4 * xz5 / lpms.cx3;
  dFLOAT y4 = xz1 * xz2 * xz3 * xz5 / lpms.cx4;
  dFLOAT y5 = xz1 * xz2 * xz3 * xz4 / lpms.cx5;

  switch (tempDepPropType) {
  case CDI_PROP_VISCOSITY: { //kinematic viscosity at T_char in (m^2/s)*10^6
    dFLOAT nu_aix = y1 * lpms.liq_v1 + y2 * lpms.liq_v2 + y3 * lpms.liq_v3 + y4 * lpms.liq_v4 + y5 * lpms.liq_v5;
    nu_aix = nu_aix + lpms.liq_v6 * yy4 + lpms.liq_v7 * yy0;
    return nu_aix;
  }
  case CDI_PROP_PRANDTL_NUMBER: {
    dFLOAT pr_aix = y1 * lpms.liq_pr1 + y2 * lpms.liq_pr2 + y3 * lpms.liq_pr3 + y4 * lpms.liq_pr4 + y5 * lpms.liq_pr5;
    pr_aix = pr_aix + lpms.liq_pr6 * yy4 + lpms.liq_pr7 * yy0;
    return pr_aix;
  }
  default:
    return -1.0;
  }
}
