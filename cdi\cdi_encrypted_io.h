/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#ifndef	CDI_ENCRYPTED_IO_INCLUDE
#define	CDI_ENCRYPTED_IO_INCLUDE


/**
*
* Function name:cdi_is_encryption_on
*
* Purpose: 
* Return true if encryption is currently turned on
* Note that encryption is on for certain chunks only, so this
* function may return false even if some other chunks in the CDI
* file are encrypted
* To check that nothing is encrypted at all, call cdi_get_cipher,
* which will return null when there is no encryption at all
* or call cdi_is_file_encrypted
*
*/
bool cdi_is_encryption_on(CDI_INFO cdi_info);

/**
*
* Function name:cdi_set_encryption_on
*
* Purpose: 
* Sets the encryption on or off as requested
*
*/
void cdi_set_encryption_on(CDI_INFO cdi_info);
void cdi_set_encryption_off(CDI_INFO cdi_info);

/**
*
* Function name:cdi_set_encryption_on_if_needed
*
* Purpose: 
* To toggle encryption on before a chunk of specified type
* The call is a no-op if that type of chunk is not encrypted
* The list of encrypted types is in the source
*
*/
void cdi_set_encryption_on_if_needed(CDI_INFO cdi_info, CIO_CCCC type);

/**
*
* Function name:cdi_is_chunk_encrypted
*
* Purpose: 
* To check if a chunk should be (on write) or was (on read)
* encrypted
*
*/
bool cdi_is_chunk_encrypted(CDI_INFO cdi_info, CIO_CCCC type);

/**
*
* Function name:cdi_get_cipher
*
* Purpose: 
* To query if anything at all is encrypted in the CDI file
* 
* Return:
*    nullptr if no encryption at all in this file
*
*/
class cCIPHER_STREAM;
cCIPHER_STREAM * cdi_get_cipher(CDI_INFO cdi_info);

/**
 *
 * Function name: cdi_get_encryption_version
 *
 **/
asINT64 cdi_get_encryption_version(CDI_INFO cdi_info);

/**
 *
 * Function name: cdi_is_file_encrypted
 *
 **/
bool cdi_is_file_encrypted(CDI_INFO cdi_info);

/**
*
* Function name:cdi_create_cipher, cdi_destroy_cipher
*
*/
VOID cdi_create_cipher(CDI_INFO cdi_info, sINT64 cdi_file_id, sINT64 encryption_version);
void cdi_destroy_cipher(CDI_INFO cdi_info);

/**
*
* Call to reset the cipher to start, thus allowing encrypted chunks
* to be read in any order
* Automatically called before a new chunk is encrypted by set_encryption_on
*
*/
void cdi_seed_cipher(CDI_INFO cdi_info);

/**
 * Function to read and write encrypted data as string without decrypting it
 * In order to be printable it is encrypted in base 64
 * The encoding makes the string longer as it only uses 6 bits out of every 8 in a byte.
 */
asINT32
cdi_read_chunk_and_encode64(CDI_INFO cdi_info,
                            std::string &buffer);

void 
cdi_write_and_decode64(CDI_INFO cdi_info,
                       std::string &buffer,
                       CIO_CCCC type);

#endif

