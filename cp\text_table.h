#pragma once

#include <string>
#include <vector>

//Class to prints a consistently formatted data table similar to
//tables printed by the discretizer.
class cTEXT_TABLE {
public:

  enum class eVAR_TYPE {INT, FLOAT, STRING};

private:
  std::string m_table_name;
  std::vector<std::string> m_column_labels;
  std::vector<eVAR_TYPE> m_var_types;
  int m_num_records_printed;
  const int m_indent_level;
  
  int table_width() {
    int table_width = m_indent_level + m_column_labels.size();
    for(const auto &field : m_column_labels)
      table_width += field.length();
    return table_width;
  }
  void print_header();
  
public:
  
  cTEXT_TABLE(
              std::string table_name, //Name of the table
              std::vector<std::string> column_labels, //Vector of strings to label each column
              std::vector<eVAR_TYPE> var_types); //Vector of datatypes for each column
  ~cTEXT_TABLE();
  
  //Prints a line in the table given a list of field values
  void print_record(int count, ... );
  
  //Append an additional column of data to the table format after construction.
  void add_column(std::string column_label, eVAR_TYPE var_type);
  
};
