/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

//----------------------------------------------------------------------------
// CP_STREAM_MANAGER reads the dgf and checkpoint files 
// and passes the info on to the sp's as data streams.
//
// David Hall                                           Dec 9, 2008
//----------------------------------------------------------------------------
#include "common.h"
#include "cp_stream_manager.h"
#include "jobctl.h"
#include "timestep_subcycling.h"
#include "cp_dgf_reader.h"
#include "cp_cdi_reader.h"
#include "window.h"
#include "cp_info.h"
#include "seed.h"
#include "rotational_dynamics.h"
#include "cp_seed_from_meas.h"
#include "trajectory_results.h"
#include "exa_sim_parse_cp.h"

#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
#include <vector>
#include <iterator>
#include <numeric>

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
#include "particle_sim_cp.h"
extern sCP_PARTICLE_SIM cp_particle_sim;
//#endif

LGI_STREAM* g_sp_streams;      // pointer to vector of sp write data streams

//----------------------------------------------------------------------------
// open_file_streams
//----------------------------------------------------------------------------
VOID sCP_STREAM_MANAGER::open_sp_data_streams()
{
  g_sp_streams = cp_wait_for_sp_lgi_input_connect();
}

//----------------------------------------------------------------------------
// close_sp_data_streams
//----------------------------------------------------------------------------
VOID sCP_STREAM_MANAGER::close_sp_data_streams()
{
  if(g_sp_streams) {
    ccDOTIMES(i, total_sps)
      lgi_close_stream(g_sp_streams[i]);

    exa_free(g_sp_streams);
  }
}

//----------------------------------------------------------------------------
// send_synchronization_message_to_all_sps
//----------------------------------------------------------------------------
VOID sCP_STREAM_MANAGER::send_synchronization_message_to_all_sps()
{
  LGI_SYNCHRONIZATION_REC sync_record;

  sync_record.tag.id = LGI_SYNCHRONIZATION_TAG;
  sync_record.tag.length = lgi_pad_and_encode_record_length(sizeof(sync_record));

  write_header_to_all_sps(sync_record);
  ccDOTIMES(i,total_sps)
    lgi_flush(g_sp_streams[i]);
}

static VOID add_new_record_to_audit_trail()
{
  if (cp_info.audit_trail == NULL) 
    cp_info.audit_trail = audit_make("");
  AUDIT_TRAIL audit_trail = cp_info.audit_trail;
  audit_new_record(audit_trail);

  // Need to get the simeng version at runtime via a message from the SP
  cSTRING product_version = jobctl_get_distname();
  if (product_version == NULL) product_version = "DIST";

  char full_cp_version[4096];
  char full_sp_version[4096];

  sprintf(full_cp_version, "%s.%s", product_version, CP_VERSION);
  sprintf(full_sp_version, "%s.%s", product_version, cp_info.simeng_version);

  audit_append_program_entry("simulator (cp)", full_cp_version, NULL, NULL, cp_info.launch_time, audit_trail);
  audit_append_program_entry("simulator (sp)", full_sp_version, NULL, NULL, cp_info.launch_time, audit_trail);

  cSTRING *argv = sim_args.argv;
  asINT32 argc = sim_args.argc;
  audit_append_cmd_entry(argv[0], audit_trail);
  if (argc > 1) 
    audit_append_cmdlineopt_entry((argc-1), &(argv[1]), audit_trail);
  audit_append_file_entry("lgi", cp_info.lgi_filename, FALSE, -1, audit_trail);
}

static inline TIMESTEP read_mme_ckpt_restart_time_internal(SRI_FILE resume_file)
{
  asINT32 last_frame = resume_file->n_frames() - 1;
  resume_file->set_meas_frame(last_frame);
  sriINT start_time, end_time;
  resume_file->read_meas_frame_time(&start_time,&end_time);
  asINT32 restart_time = (start_time + end_time + 1) / 2;   //in user's timestep
  asINT32 coarse_time_step = 1 << (cp_info.num_scales - 1);
  // Wind time back to the nearest multiple of the coarse time step and the timesteps of all solvers
  // cp_info.restart_time = restart_time & ~(coarse_time_step - 1);

  // restart_base_time and lcm_steps are defined in base timesteps
  asINT32 restart_base_time = restart_time * cp_info.n_user_base_steps;
  asINT32 lcm_steps = coarse_time_step * lcm(cp_info.n_lb_base_steps,
                                             cp_info.n_t_base_steps,
                                             cp_info.n_ke_base_steps,
                                             cp_info.n_uds_base_steps,
                                             cp_info.n_conduction_base_steps,
					     cp_info.n_particle_base_steps);
  return round_down_time(restart_base_time, lcm_steps) / cp_info.n_user_base_steps;  //in user's timestep
}
static inline VOID check_mme_ckpt_files_present_for_all_active_realms()
{
  BOOLEAN is_both_realm_active = cp_info.is_realm_active(STP_FLOW_REALM) && cp_info.is_realm_active(STP_COND_REALM);
  if (is_both_realm_active && g_seed_ctl.number_of_seed_files() <= 1) { 
    cSTRING casename = filename_name(g_seed_ctl.seed_filename(0)); 
    cSTRING extension = filename_type(g_seed_ctl.seed_filename(0));
    char *maybe_another_resume_filename = new char[strlen(casename) + strlen(extension) + 6];
    strcpy(maybe_another_resume_filename, casename);

    //mme resume should seed from two files if both realms active. 
    if (strcmp(extension, "fnc") == 0)
      strcat(maybe_another_resume_filename, ".vnc");
    else if (strcmp(extension,"vnc") == 0)
      strcat(maybe_another_resume_filename, ".fnc");
    
    msg_error("Only %s is present for MME checkpoint resume. " 
              "However for a flow+conduction case, "
              "file %s is also expected for resuming the simulation. "
              "Check your files and file names or use --seed instead.", 
               g_seed_ctl.seed_filename(0), maybe_another_resume_filename);
  }

}
static inline VOID read_mme_ckpt_restart_time()
{
  cp_info.restart_time = read_mme_ckpt_restart_time_internal(g_seed_ctl.seed_file(0));

  cp_info.time = cp_info.restart_time;   // in user's timestep
  cp_info.restart_base_time = cp_info.restart_time * cp_info.n_user_base_steps;

  if (cp_info.restart_time >= cp_info.end_time)
    msg_error("The number of timesteps specified for the resumed simulation is %d, but the checkpoint file was written at timestep %d.", cp_info.end_time, cp_info.restart_time); 


  msg_print("Restarting from MME checkpoint at timestep %d.", cp_info.restart_time);
}

static inline VOID write_start_time_to_sps()
{
  DGF_START_TIME_REC record;
  record.tag.id = DGF_START_TIME_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(sizeof(record));

  //time sent to SPs in base time units, which are the same for flow and conduction SPs
  record.time = (cp_info.time + 1) * cp_info.n_user_base_steps - 1;

  /* Pass it on to the SPs */
  write_header_to_all_sps(record);
}

static inline VOID write_momentum_freeze_params_to_sps()
{
  DGF_MOMENTUM_FREEZE_REC record;
  record.tag.id = DGF_MOMENTUM_FREEZE_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(sizeof(record));

  record.freeze_momentum_field = cp_info.freeze_momentum_field;
  record.thermal_timestep_ratio = cp_info.thermal_timestep_ratio;
  record.momentum_freeze_start_time = cp_info.momentum_freeze_start_time;

  /* Pass it on to the SPs */
  write_header_to_all_sps(record);
}

static inline VOID write_large_pore_params_to_sps_and_set_input_rock_table()
{
  DGF_LARGE_PORE_REC record;
  record.tag.id = DGF_LARGE_PORE_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(sizeof(record));

  record.is_large_pore_sim = cp_info.is_large_pore_sim;

  /* Pass it on to the SPs */
  write_header_to_all_sps(record);

  /* set cp_info.input_rock_table */
  cp_info.input_rock_table = FALSE;  //only for large pore simulation
  if (cp_info.is_large_pore_sim) {
    std::string line;
    BOOLEAN input_table = TRUE;
    std::ifstream ifile("user_input.init");

    if (ifile.is_open())
    {
      while ( std::getline (ifile,line) )
      {
        std::istringstream stm(line) ; 
        std::vector<std::string> result ;
        std::string token ;
        while( stm >> token ){
          result.push_back(token) ;
        }
        if( result.size() <3 ) continue;
        if(result[1]=="mp_pm_table_input" && result[2]=="0"){
          msg_warn("mp_pm_table_input=0 is found, porous rock tables will not be read");
          input_table = FALSE;
        }
      }
    }
    ifile.close();
    
    cp_info.input_rock_table = input_table;
  }
}


static inline VOID write_avg_mme_ckpt_to_sps()
{
  DGF_AVG_MME_REC record;
  record.tag.id = DGF_AVG_MME_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(sizeof(record));
  record.full_ckpt_with_avg_mme = cp_info.full_ckpt_with_avg_mme;
  /* Pass it on to the SPs */
  write_header_to_all_sps(record);
}

static inline VOID write_local_vel_freeze_ckpt_to_sps()
{
  DGF_LOCAL_VEL_FREEZE_REC record;
  record.tag.id = DGF_LOCAL_VEL_FREEZE_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(sizeof(record));
  record.full_ckpt_with_frozen_vars = cp_info.full_ckpt_with_frozen_vars;

  // Pass it on to the SPs
  write_header_to_all_sps(record);
}

static inline VOID write_implicit_solver_data() {
  LGI_IMPLICIT_SOLVER_DATA record;
  record.tag.id = LGI_IMPLICIT_SOLVER_DATA_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_IMPLICIT_SOLVER_DATA)
                                                       + 2 * total_sps * sizeof(int));
  record.num_implicit_shell_solver_states = cp_info.num_implicit_shell_solver_states;
  record.num_implicit_solid_solver_states = cp_info.num_implicit_solid_solver_states;
  /* Pass it on to the SPs */
  write_header_to_all_sps(record);

  // Write the offset vector to the SPs
  write_to_all_sps(cp_info.implicit_shell_solver_state_index_offset.data(), total_sps * sizeof(int));
  write_to_all_sps(cp_info.implicit_solid_solver_state_index_offset.data(), total_sps * sizeof(int)); 
}

static inline VOID write_vapor_transport_to_sps()
{
  DGF_FILM_VAPOR_REC record;
  record.tag.id = DGF_FILM_VAPOR_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(sizeof(record));

  record.is_water_vapor_transport = cp_info.is_water_vapor_transport;
  // This is not read in glob chunk -> call this in pass 3 after calling read_acceleration_factor
  record.film_acceleration_factor = cp_info.film_acceleration_factor;

  /* Pass it on to the SPs */
  write_header_to_all_sps(record);
}
// Realistic wind calibration parameters
static inline VOID write_calibration_parameters_to_sps()
{
  DGF_RW_CALIBRATION_PARAMETERS_REC record;
  record.tag.id = DGF_RW_CALIBRATION_PARAMETERS_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(sizeof(record));
  cp_info.calibration_params.write_clbr_record(record, cp_info.end_time);
  int meas_index = 0;
  DO_CDI_MEAS_WINDOWS(cdi_meas_win) {
    if(meas_index == record.meas_window_index) {
      record.meas_interval = cdi_meas_win->fluid_time_desc.interval;
      cdi_meas_win->fluid_time_desc.start = (cp_info.calibration_params.iterations - 1 ) *cp_info.end_time / cp_info.calibration_params.iterations;
      cdi_meas_win->is_rwnc_mean_vel_added = !cp_info.calibration_params.subtract_meas_velocity;
    }
    meas_index++;
  }

  /* Pass it on to the SPs */
  write_header_to_all_sps(record);
}

static inline VOID write_tbs_info_parameters_to_sps()
{
  cDGF_TBS_INFO record;
  cp_info.tbs_info.write_tbs_info(record);
  write_header_to_all_sps(record);
}

static VOID process_checkpoint_override()
{
  if (!sim_args.checkpoint_override) 
    return;

  asINT32 checkpoint_interval = sim_args.checkpoint_interval;

  if (checkpoint_interval <= 0) {
    msg_print("Periodic checkpoints disabled");
    cp_info.periodic_ckpts_p = FALSE;
  }
  else {
    cp_info.periodic_ckpts_p = TRUE;
    align_ckpt_time_parameters(0, cp_info.end_time, checkpoint_interval);
    if (sim_args.allow_immediate_checkpoints)
      cp_info.ckpt_time_desc.start = 0;
    msg_print("Periodic checkpoint interval set to %ld timesteps", (long)cp_info.ckpt_time_desc.period);
  }
}

#if SURF_COUP
// Name (init_xtr) chosen to obfuscate intended purpose. Otherwise would have been
// called detect_surface_coupling_model_connector_licenses.
//
// This is a reference to a definition in exa_sim.cc
VOID init_xtr();

VOID maybe_append_experimental_vars() {
  //Add custom variable types that aren't supported by PowerCASE for
  //debugging or prototyping. These will be hidden from customers.
  DO_CP_MEAS_WINDOWS(window) {
    switch(window->meas_window_type) {
    case LGI_FLUID_WINDOW:
    case LGI_POROUS_WINDOW:
    case LGI_SURFACE_WINDOW:
    case LGI_SAMPLING_SURFACE_WINDOW:
    case LGI_TRAJECTORY_WINDOW:
    case LGI_AVERAGE_MME_WINDOW:
      break;
    case LGI_VOLUME_WINDOW:
    case LGI_SHELL_WINDOW: 
    case LGI_SAMPLING_SHELL_WINDOW:
      if (g_measure_conduction_passthrough) { //Enabled via an exa_turb_d19.init option
        SRI_VARIABLE_TYPE var_id = (SRI_VARIABLE_TYPE)((int)eEXPERIMENTAL_VARIABLE_TYPES::CONDUCTION_PASSTHROUGH + cp_info.cvid_helper->get_first_unassigned_var_id());
        window->append_var_type(var_id);
      }
      if (g_shell_tangential_measurements) { //Enabled via an exa_turb_d19.init option
        SRI_VARIABLE_TYPE var_id = (SRI_VARIABLE_TYPE)((int)eEXPERIMENTAL_VARIABLE_TYPES::TANGENTIAL_GRAD + cp_info.cvid_helper->get_first_unassigned_var_id());
        window->append_var_type(var_id);
      }
      break;
    default:
      break;
    }
  }
}

sINT32 sCP_STREAM_MANAGER::coarsest_scale_for_cmdl_meas(asINT32 model_index)
{
  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
  SIM_ABSTRACT_SURFACE source_surf = surface_coupling->native_resulting_source_surface;
  if (!source_surf) {
    msg_internal_error("Interpolation map not yet constructed for coupling model index %d",model_index);
  }

  sINT32 coarsest_scale = sINT32_MAX;
  ABSTRACT_SURFACE_DO_FACET_FACES(source_surf,facetFace,cMIO_MODELf::cMESH) {
    SURFEL_COUPLING_INFO surfel_coupling_info = m_dgf_reader.get_surfel_coupling_info(facetFace->m_index);
    // Suface coupling implies STP_FLOW_REALM
    STP_PROC proc = (total_fsps > 1) ? cp_info.surfel_procs[STP_FLOW_REALM][surfel_coupling_info->id] : 0;
    STP_SCALE scale = sri_scale_to_sim_scale(surfel_coupling_info->scale);
    if (proc < 0 || proc >= total_sps)
      msg_internal_error("Invalid processor ID %d for coupling model index %d and mesh index %d in map",
                         proc,model_index,facetFace->m_index);

    if (-1 == scale) {
      msg_internal_error("Invalid surfel scale for coupling model index %d and surfel ID %d",
                         model_index,surfel_coupling_info->id);
    }
    coarsest_scale = MIN(coarsest_scale,scale);
  }

  if ( sINT32_MAX == coarsest_scale) {
    msg_internal_error("Coarsest measurement scale for coupling model index %d is %d",model_index,coarsest_scale);
  }

  return coarsest_scale;

}

VOID sCP_STREAM_MANAGER::add_surface_coupling_cdi_meas_windows()
{
  // create one cdi meas window for every coupling model chunk read in
  // must be called after all regular cdi meas windows have been created
  // and the interp map has been defined, however, storage has been allocated
  // already in read_cdi_meas_windows since the SCPL chunk appears in the CDI
  // file before the MESR chunks. If the order changes, this will break;
  if (cp_info.n_surface_couplings <= 0) return;

  CDI_MEAS_WINDOW cdi_meas_win = cp_info.cdi_meas_windows + cp_info.n_cdi_meas_windows;
  asINT32 n_unmapped_meas = 0;
  ccDOTIMES (model_index, cp_info.n_surface_couplings) {

    // if it is not the first coupling phase, DO NOT change the start time
    cdi_meas_win->is_not_first_coupling_phase = FALSE;
    TIME_DESC time_desc = &cdi_meas_win->fluid_time_desc;

    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    UNROUNDED_TIME_DESC unrounded_time_desc = &surface_coupling->unrounded_time_desc;

    SIM_ABSTRACT_SURFACE source_surf = surface_coupling->native_resulting_source_surface;
    cdi_meas_win->min_pressure = -1;
    cdi_meas_win->max_pressure = -1;

    cdi_meas_win->reference_point[0] = 0;
    cdi_meas_win->reference_point[1] = 0;
    cdi_meas_win->reference_point[2] = 0;
    cdi_meas_win->name = cmdl->model_filename;

    cdi_meas_win->start_time = cmdl->m_coupling_phase_descs[0].start;   //in user's timestep
    // handle the special case where the start time is specified as zero
    if (cdi_meas_win->start_time == 0) {
      surface_coupling->launch_at_init_p = TRUE;
    }
    cdi_meas_win->period = cmdl->m_coupling_phase_descs[0].period;   //in user's timestep
    cdi_meas_win->average_interval = cmdl->m_coupling_phase_descs[0].interval;   //in user's timestep

    // The unrounded times are for modifying the tdf file so that the time agrees with what 
    // user specifies. See PR41598.
    //
    surface_coupling->unrounded_start_time = cmdl->m_coupling_phase_descs[0].exact_start;
    surface_coupling->unrounded_period     = cmdl->m_coupling_phase_descs[0].exact_period;
    surface_coupling->unrounded_average_interval = cmdl->m_coupling_phase_descs[0].exact_interval;

    if (cmdl->n_coupling_phases == 1) {
      if (cmdl->use_end_time_for_coupling) {
        cdi_meas_win->end_time = cmdl->end_time;
        cdi_meas_win->num_frames = -1;
      } else {
        cdi_meas_win->end_time = -1;
        cdi_meas_win->num_frames = cmdl->num_iterations;
      }
    } else {
      // Check if the coupling ends in the first phase. If so, it is actually constant coupling with only one phase.
      if (cmdl->use_end_time_for_coupling) {
        if (cmdl->end_time <= cmdl->m_coupling_phase_descs[1].start) {
          cmdl->n_coupling_phases = 1;
          cdi_meas_win->num_frames = -1;
          cdi_meas_win->end_time = cmdl->end_time;
        } else {
          cdi_meas_win->num_frames = -1;
          cdi_meas_win->end_time = cmdl->m_coupling_phase_descs[1].start; // start of the 2nd phase is the end of the 1st phase, or the user specified end time
        }
      } else { // use number of frames to end coupling
        asINT32 n_first_phase_coupling = (cmdl->m_coupling_phase_descs[1].start - cmdl->m_coupling_phase_descs[0].start)/cmdl->m_coupling_phase_descs[0].period + 1;
        if (cmdl->num_iterations <= n_first_phase_coupling) {
          cmdl->n_coupling_phases = 1;
          cdi_meas_win->num_frames = cmdl->num_iterations;
          cdi_meas_win->end_time = -1;
        } else {
          cdi_meas_win->num_frames = -1;
          cdi_meas_win->end_time = cmdl->m_coupling_phase_descs[1].start; // start of the 2nd phase is the end of the 1st phase, or the user specified end time
        }
      }
    }

    // set it to 1, so that write_sri_window results( to buffer)
    // and any post meas commands are handled the same way as regular meas wins
    cdi_meas_win->n_meas_files = 1;
    //initialize options
    cdi_meas_win->mstp.standard_mask = 0;
    // treat this like a heat exchanger coupling measurement window
    cdi_meas_win->mstp.standard_mask |= CDI_MEAS_OPT_DONT_SHIFT_START_TIME;

    if (!source_surf || 0 == source_surf->NumFacetFaces()) {
      // set it to the finest time-scale in the simulation if no meas surfels
      // are defined.
      cdi_meas_win->coarsest_fluid_shob_scale = cp_info.num_scales - 1;
      cdi_meas_win->coarsest_cond_shob_scale = cp_info.num_scales - 1;
    } else {
      cdi_meas_win->coarsest_fluid_shob_scale = coarsest_scale_for_cmdl_meas(model_index);
      cdi_meas_win->coarsest_cond_shob_scale = cp_info.num_scales - 1;
    }

    /* surface coupling meas windows only collect htc and near_wall_temp, here we enforce it
     * associated with LB and T_PDE solvers to keep consistent with coupling time parameters
     * that should be in term of user's timestep (here is LB timestep). The better way is to
     * connect it with user's timer setting.
     */
    STP_ACTIVE_SOLVER_MASK surface_coupling_window_solver_mask = (LB_ACTIVE | T_PDE_ACTIVE);
    cdi_meas_win->solver_mask = surface_coupling_window_solver_mask;

    asINT32 n_vars = surface_coupling->n_export_vars();
    if (n_vars == 0) {
      msg_internal_error("No variables defined for export for the surface coupling model %s", 
                         surface_coupling->get_cmdl()->model_filename);
    }

    SRI_VARIABLE_TYPE *var_types = cnew SRI_VARIABLE_TYPE[n_vars];
    asINT32 *meas_export_vars = surface_coupling->export_vars();

    ccDOTIMES (var_index, n_vars) {
      eTPI_VARIABLE_TYPE tpi_var_type = eTPI_VARIABLE_TYPE(meas_export_vars[var_index]);
      switch (tpi_var_type) {
      case TPI_VAR_TYPE_HEAT_TRANSFER_COEFFICIENT:
        var_types[var_index] = SRI_VARIABLE_HTC_NEAR_WALL_TEMP;
        break;
      case TPI_VAR_TYPE_NEAR_WALL_TEMPERATURE:
        var_types[var_index] = SRI_VARIABLE_NEAR_WALL_TEMP;
        break;
      default:
        msg_internal_error("Unknown variable specified for export for the surface coupling model %s",
                           surface_coupling->get_cmdl()->model_filename);
        break;
      }
    }

    // Save the cdi_meas_win->start, period, etc. so that we can restore them once finish rounding 
    // all phase timing parameters. This is necessary as we may call align_meas_window_time_parameters()
    // twice (for meas windows started via monitors, the start time may be changed due to the monitor
    // minimal end of initial transient time, thus need to align the meas windows timing parameters
    // again), and we want to get the same timing parameters for meas windows not started via monitors.
    TIMESTEP cdi_win_start_time = cdi_meas_win->start_time;
    TIMESTEP cdi_win_period = cdi_meas_win->period;
    TIMESTEP cdi_win_average_interval = cdi_meas_win->average_interval;
    TIMESTEP cdi_win_num_frames = cdi_meas_win->num_frames;
    TIMESTEP cdi_win_end_time = cdi_meas_win->end_time;


    cdi_meas_win->surface_var_set.var_types = var_types;
    cdi_meas_win->surface_var_set.n_vars    = n_vars;
    cdi_meas_win->compute_rounded_time_desc();
    surface_coupling->compute_unrounded_time_desc(cdi_meas_win->is_not_first_coupling_phase);
    // Now time_desc contains the rounded timing parameters for the 1st phase


    // DO NOT shift the start time for other phases
    cdi_meas_win->is_not_first_coupling_phase = TRUE;

    TIMESTEP n_user_base_steps     = cp_info.n_user_base_steps;
    TIMESTEP first_phase_starttime = time_desc->start / n_user_base_steps;    // in user's steps
    TIMESTEP first_phase_period    = time_desc->period / n_user_base_steps;   // in user's steps
    TIMESTEP first_phase_interval  = time_desc->interval / n_user_base_steps; // in user's steps
    TIMESTEP first_phase_repeat    = time_desc->repeat;

    dFLOAT   first_phase_exact_starttime = surface_coupling->unrounded_time_desc.start / n_user_base_steps;
    dFLOAT   first_phase_exact_period = surface_coupling->unrounded_time_desc.period / n_user_base_steps;
    dFLOAT   first_phase_exact_interval = surface_coupling->unrounded_time_desc.interval / n_user_base_steps;

    // This is the first PT launching (=measurement output) timestep which equals to (first_interval - delay)
    TIMESTEP first_pt_launch_time = first_phase_starttime + first_phase_interval; // in user's steps
    dFLOAT   first_pt_launch_exact_time = first_phase_exact_starttime + first_phase_exact_interval;

    asINT32 num_iterations_remained = cmdl->num_iterations;

    asINT32 last_phase = 0; // If the coupling end_time is in the 1st phase, then the last phase is 0;

    // temporary vector for rounding
    std::vector<sCDI_COUPLING_PHASE_DESC> rounded_coupling_phase_descs;

    ccDO_FROM_TO(i, 1, cmdl->n_coupling_phases-1) {
      TIMESTEP previous_phase_period = time_desc->period;
      // the first phase start time is treated separately
      TIMESTEP previous_phase_starttime = (i==1)? first_pt_launch_time : time_desc->start / n_user_base_steps; // in user's steps
      TIMESTEP previous_phase_interval = time_desc->interval / n_user_base_steps;  // in user's steps

      dFLOAT previous_phase_exact_period = surface_coupling->unrounded_time_desc.period;
      dFLOAT previous_phase_exact_starttime = (i==1)? first_pt_launch_exact_time : surface_coupling->unrounded_time_desc.start / n_user_base_steps;
      dFLOAT previous_phase_exact_interval = surface_coupling->unrounded_time_desc.interval / n_user_base_steps;
      dFLOAT previous_phase_exact_endtime;

      TIMESTEP previous_phase_endtime;
      // If there are more than 1 phases, the last coupling in the first phase belongs to the 2nd phase,so the repeat number is subtractd by 1.
      asINT32  previous_phase_repeat = (i==1)? (first_phase_repeat - 1) : time_desc->repeat;

      // store previous phase
      sCDI_COUPLING_PHASE_DESC coupling_phase = { previous_phase_starttime,
        previous_phase_period,
        previous_phase_interval,
        cmdl->m_coupling_phase_descs[i-1].delay, // delay is not changed yet, in user's steps
        cmdl->m_coupling_phase_descs[i-1].therm_time_ratio,
        cmdl->m_coupling_phase_descs[i-1].stepsize,
        previous_phase_exact_starttime,
        previous_phase_exact_period,
        previous_phase_exact_interval,
        cmdl->m_coupling_phase_descs[i-1].adaptive_p
      };

      // use the time_desc->repeat from the previous phase to find the end time
      previous_phase_endtime = previous_phase_starttime + previous_phase_period * previous_phase_repeat;
      previous_phase_exact_endtime = previous_phase_exact_starttime + previous_phase_exact_period * previous_phase_repeat;

      // check if this is the last active phase
      if (cmdl->use_end_time_for_coupling) {
        if (previous_phase_endtime >= cmdl->end_time)
          break;
      }
      else // use num_iterations to stop coupling
      {
        if (num_iterations_remained <= previous_phase_repeat)
          break;
        else
          num_iterations_remained -= previous_phase_repeat;
      }

      rounded_coupling_phase_descs.push_back(coupling_phase); // in user's steps

      // use the new time_desc to round
      cdi_meas_win->start_time = previous_phase_endtime; // in user's steps
      cdi_meas_win->period = cmdl->m_coupling_phase_descs[i].period;
      cdi_meas_win->average_interval = cmdl->m_coupling_phase_descs[i].interval;

      surface_coupling->unrounded_start_time = previous_phase_exact_endtime;
      surface_coupling->unrounded_period = cmdl->m_coupling_phase_descs[i].exact_period;
      surface_coupling->unrounded_average_interval = cmdl->m_coupling_phase_descs[i].exact_interval;

      if ( i== (cmdl->n_coupling_phases-1) ) { // last phase
        if (cmdl->use_end_time_for_coupling) {
          cdi_meas_win->num_frames = -1;
          cdi_meas_win->end_time = cmdl->end_time;
        } else {
          cdi_meas_win->num_frames = num_iterations_remained;
          cmdl->num_iterations = num_iterations_remained;
          cdi_meas_win->end_time = -1;
        }
      } else { // If this is not the last phase in the phase table, always use the end_time to stop this phase
        cdi_meas_win->num_frames = -1;
        if (cmdl->use_end_time_for_coupling)
          cdi_meas_win->end_time = MIN(cmdl->m_coupling_phase_descs[i+1].start, cmdl->end_time);
        else {
          // avoid overflow
          cdi_meas_win->end_time = MIN(previous_phase_endtime + (dFLOAT)num_iterations_remained*cmdl->m_coupling_phase_descs[i+1].period, cmdl->end_time);
          cdi_meas_win->end_time = MIN(cdi_meas_win->end_time, cmdl->m_coupling_phase_descs[i+1].start);
        }
      }

      cdi_meas_win->compute_rounded_time_desc(); // if it is not the first phase, do not shift the start time
      surface_coupling->compute_unrounded_time_desc(cdi_meas_win->is_not_first_coupling_phase);

      last_phase++;
    }

    // If there is only one phase, also use time_desc->repeat since the last coupling belongs to the 1st phase 
    asINT32 last_phase_repeat = time_desc->repeat;

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
    msg_print("Last phase (0 is the 1st phase) %d number of iterations = %d", last_phase, last_phase_repeat);
#endif
    // Store the last phase
    // Note for the first phase, the start time is the PT launch time = time_desc.start + time_desc.interval
    // For other phases, the start time is time_desc.start from the rounded descs of the previous phase.
    sCDI_COUPLING_PHASE_DESC coupling_phase = { (last_phase == 0)? 
      (time_desc->start + time_desc->interval) / n_user_base_steps
        :
        time_desc->start / n_user_base_steps,
        time_desc->period / n_user_base_steps,
        time_desc->interval / n_user_base_steps,
        cmdl->m_coupling_phase_descs[last_phase].delay, // in users' steps
        cmdl->m_coupling_phase_descs[last_phase].therm_time_ratio,
        cmdl->m_coupling_phase_descs[last_phase].stepsize, // in users' steps
        (last_phase == 0)? (surface_coupling->unrounded_time_desc.start + surface_coupling->unrounded_time_desc.interval) / n_user_base_steps : surface_coupling->unrounded_time_desc.start / n_user_base_steps,
        surface_coupling->unrounded_time_desc.period / n_user_base_steps,
        surface_coupling->unrounded_time_desc.interval / n_user_base_steps,
        cmdl->m_coupling_phase_descs[last_phase].adaptive_p
    };

    rounded_coupling_phase_descs.push_back(coupling_phase);

    // Make sure delay is smaller than the period for each phase
    ccDOTIMES(phase_index, cmdl->n_coupling_phases)
    {
      asINT32 &delay = rounded_coupling_phase_descs[phase_index].delay;
      delay = MIN(delay, rounded_coupling_phase_descs[phase_index].period - 1);
    }

    // copy rounded table to the CMDL table
    swap(cmdl->m_coupling_phase_descs, rounded_coupling_phase_descs);

    asINT32 last_phase_start = cmdl->m_coupling_phase_descs[last_phase].start;
    asINT32 last_phase_period = cmdl->m_coupling_phase_descs[last_phase].period;
    asINT32 last_phase_delay = cmdl->m_coupling_phase_descs[last_phase].delay;

    // convert last_phase_repeat to end_time
    const TIMESTEP max_timestep = ~((TIMESTEP)1 << (8 * sizeof(TIMESTEP) - 1));
    
    // last_phase_repeat is the number of measurement intervals. If there is only one phase, should exclude the 1st meas interval before the first coupling
    asINT32 last_phase_coupling_repeat = (last_phase == 0)? (last_phase_repeat - 1) : last_phase_repeat;
    dFLOAT end_time = (dFLOAT)last_phase_start + (dFLOAT)last_phase_period * (dFLOAT)last_phase_coupling_repeat + (dFLOAT)last_phase_delay;
    cmdl->end_time = MIN(end_time, max_timestep);
    
    // Coupling end time should not be contrained by cp_info.end_time (which could be specified by --num_timesteps)
    //cmdl->end_time = MIN(cmdl->end_time, cp_info.end_time) / n_user_base_steps;  // in user's steps

    // ignore the coupling phases after the end_time or the num_iterations
    cmdl->n_coupling_phases = last_phase+1;

    // restore the first phase time desc so that first_interval can be correctly calculated later
    time_desc->start    = first_phase_starttime;
    time_desc->period   = first_phase_period;
    time_desc->interval = first_phase_interval;
    time_desc->repeat   = first_phase_repeat;

    surface_coupling->unrounded_time_desc.start = first_phase_exact_starttime;
    surface_coupling->unrounded_time_desc.period = first_phase_exact_period;
    surface_coupling->unrounded_time_desc.interval = first_phase_exact_interval;

    // restore the cdi meas window timing parameters
    cdi_meas_win->start_time = cdi_win_start_time;
    cdi_meas_win->period = cdi_win_period;
    cdi_meas_win->average_interval = cdi_win_average_interval;
    cdi_meas_win->num_frames = cdi_win_num_frames;
    cdi_meas_win->end_time = cdi_win_end_time;

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
    msg_print("First phase number of meas iterations = %d", fluid_time_desc->repeat);    
    msg_print("CP: new cmdl->end_time = %d", cmdl->end_time);
#endif

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
    msg_print("Rounded coupling measurement timesteps in cmdl");
    ccDOTIMES(i, cmdl->n_coupling_phases)
    {
      sCDI_COUPLING_PHASE_DESC coupling_phase = cmdl->m_coupling_phase_descs[i];
      msg_print("coupling phase %d start=%d period=%d interval=%d delay=%d therm_time_ratio %f stepsize=%f exact_start = %f exact_period = %f exact_interval = %f", i,
                coupling_phase.start,
                coupling_phase.period,
                coupling_phase.interval,
                coupling_phase.delay,
                coupling_phase.therm_time_ratio,
                coupling_phase.stepsize,
                coupling_phase.exact_start,
                coupling_phase.exact_period,
                coupling_phase.exact_interval);
    }
#endif

    // move on to the next coupling model
    cdi_meas_win++;
  }

  asINT32 n_valid_windows = cp_info.n_surface_couplings - n_unmapped_meas;
  cp_info.n_cdi_meas_windows += n_valid_windows;
}

VOID sCP_STREAM_MANAGER::cp_send_coupling_models(BOOLEAN is_checkpoint_restore_p)
{
  if (cp_info.n_surface_couplings <= 0)  // non surface coupling case
    return;

  char *ineligible_pf_bc_names = NULL;
  asINT32 n_ineligible_pf_bcs;
  asINT32 ineligible_pf_bc_names_length;

  cDGF_COUPLING_MODELS dgf_coupling_models;
  cp_info.coupling_model_descs = cnew sCOUPLING_MODEL_DESC[cp_info.n_surface_couplings];

  dgf_coupling_models.tag.id = DGF_COUPLING_MODELS_TAG;
  dgf_coupling_models.tag.length = lgi_pad_and_encode_record_length(sizeof(cDGF_COUPLING_MODELS));
  dgf_coupling_models.n_coupling_models = cp_info.n_surface_couplings;

  ccDOTIMES(sp, total_fsps) {
    dgf_coupling_models.write(g_sp_streams[sp], FALSE);
  }

  asINT32 n_user_base_steps = cp_info.n_user_base_steps;
  ccDOTIMES (model_index, cp_info.n_surface_couplings) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+model_index;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    cDGF_COUPLING_MODEL dgf_coupling_model = { 0 };
    n_ineligible_pf_bcs = 0;
    ineligible_pf_bc_names_length = 0;
    dgf_coupling_model.tag.id = DGF_COUPLING_MODEL_TAG;
    // if the foreign to native map has no source faces
    // turn off reading of the coupling model data
    if ((0 == cmdl->num_pf_bcs) ||
        !surface_coupling->foreign_resulting_source_surface ||
        (0 == surface_coupling->foreign_resulting_source_surface->NumFacetFaces()) ) {
      dgf_coupling_model.couple_during_sim_p = FALSE;
    } else {
      dgf_coupling_model.couple_during_sim_p = TRUE;
    }
    BOOLEAN is_cda = surface_coupling->is_coupling_data_available();
    if (surface_coupling->any_pf_bc_init_p() && !is_cda) {
      msg_warn("Results unavailable in the PowerTHERM model file \"%s\" for use as initial"
               " PowerFLOW boundary condition", cmdl->model_filename);
    }
    if (surface_coupling->launch_at_init_p ||
        (surface_coupling->any_pf_bc_init_p() && is_cda)) {
      dgf_coupling_model.init_pf_bc_coupling_p = TRUE;
    } else {
      dgf_coupling_model.init_pf_bc_coupling_p = FALSE;
    }

    dgf_coupling_model.name_length = strlen(cmdl->model_filename)+1;
    dgf_coupling_model.n_vars = cmdl->n_import_variables;

    // the number of windows before the addition of the
    // pseudo-meas windows by the coupling models is to be subtracted
    COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs + model_index;
    coupling_model_desc->coupling_model_index = model_index;
    CDI_MEAS_WINDOW cdi_meas_win =
      &cp_info.cdi_meas_windows[cp_info.n_cdi_meas_windows - cp_info.n_surface_couplings + model_index];
    coupling_model_desc->cdi_meas_window = cdi_meas_win;

    TIME_DESC window_time_desc = &cdi_meas_win->fluid_time_desc;  //in base steps

    TIMESTEP window_start = window_time_desc->start / n_user_base_steps;  //in user's timesteps
    TIMESTEP window_interval = window_time_desc->interval / n_user_base_steps;  //in user's timesteps
    TIMESTEP window_period = window_time_desc->period / n_user_base_steps;  //in user's timesteps

    TIMESTEP delay;
    if (cmdl->n_coupling_phases > 0)
    {
      // ignore the first phase if it has zero length
      if((cmdl->n_coupling_phases >= 2) &&
         (cmdl->m_coupling_phase_descs[1].start == cmdl->m_coupling_phase_descs[0].start)) {
        delay = cmdl->m_coupling_phase_descs[1].delay;
        dgf_coupling_model.period = cmdl->m_coupling_phase_descs[1].period; // in user's timesteps
      }
      else {
        delay = cmdl->m_coupling_phase_descs[0].delay;
        dgf_coupling_model.period = cmdl->m_coupling_phase_descs[0].period; // in user's timesteps
      }
    }


    // cmdl->delay is already in user's timesteps
    if (delay >= window_period) {
      msg_warn("Reducing the specified delay from %d timesteps to %d timesteps for model \"%s\"", delay, window_period - 1, cmdl->model_filename);
      delay = window_period - 1;
    }

    dgf_coupling_model.first_interval = window_start + window_interval + delay;  //in user's timesteps

    dFLOAT end_time;
    //end_time = (dFLOAT)window_start + (dFLOAT)window_period * (dFLOAT)window_time_desc->repeat + delay;//in user's timesteps
    end_time = (dFLOAT)cmdl->end_time;  // in user's timesteps

    dgf_coupling_model.end_time = MIN(end_time,sINT32_MAX);
    if (dgf_coupling_model.first_interval > dgf_coupling_model.end_time) {
      dgf_coupling_model.couple_during_sim_p = FALSE;
    }

    coupling_model_desc->n_coupling_models_read = 0;
    coupling_model_desc->n_runs_launched = 0;
    coupling_model_desc->n_runs_before_ckpt_restore = 0;
    //TIMESTEP last_read_time = 0;
    TIMESTEP next_read_time = dgf_coupling_model.first_interval;
    if (dgf_coupling_model.couple_during_sim_p
        && (cp_info.restart_time > 0)) {
      // If restoring from checkpoint, figure out how many times to increment
      asINT32 period = dgf_coupling_model.period;
      std::vector<bool> phase_passed;
      ccDOTIMES(index, cmdl->n_coupling_phases)
        phase_passed.push_back(false);
      while ((next_read_time <= cp_info.restart_time)
             && (next_read_time <= dgf_coupling_model.end_time)) {
        coupling_model_desc->n_coupling_models_read++;

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
        msg_print("n_coupling_models_read  = %d next_read_time = %d", coupling_model_desc->n_coupling_models_read, next_read_time);
#endif
        //last_read_time = next_read_time;
        next_read_time += period;
        ccDO_FROM_TO(i, 1, cmdl->n_coupling_phases-1)
        {
          if (!phase_passed[i] && (next_read_time - period) <= cmdl->m_coupling_phase_descs[i].start && next_read_time > cmdl->m_coupling_phase_descs[i].start)
          {
            period = cmdl->m_coupling_phase_descs[i].period;
            // Shift the next_read_time so that the next loop finds the correct value
            //last_read_time = next_read_time;
            next_read_time = cmdl->m_coupling_phase_descs[i].start + cmdl->m_coupling_phase_descs[i].delay;
            phase_passed[i] = true;
          }
        }
      }
      if (cmdl->n_coupling_phases > 1 && phase_passed[1] == true) {   // Only if the ckpt happens within the 2nd or high phase
        coupling_model_desc->n_coupling_models_read--;
      }

      // if it is a restart from check point, find the phase in which the checkpoint is located and use the corresponding time_desc
      asINT32 index = 0;
      ccDO_FROM_TO(i, 1, cmdl->n_coupling_phases-1)
      {
        if (cp_info.restart_time >= cmdl->m_coupling_phase_descs[i].start)
          index++;
      }
      // cdi_meas_win->time_desc->start is the start of the clear time. For the 1st phase, it is the output time
      //
      // XDU TODO: change the surface_coupling->unrounded_time_desc accordingly here to make it in sync with cdi_meas_win->time_desc
      if (index > 0)
        window_time_desc->start = cmdl->m_coupling_phase_descs[index].start + cmdl->m_coupling_phase_descs[index].period
          - cmdl->m_coupling_phase_descs[index].interval;
      window_time_desc->period = cmdl->m_coupling_phase_descs[index].period;
      window_time_desc->interval = cmdl->m_coupling_phase_descs[index].interval;

      // XDU TODO: save time_desc.repeat when rounding the coupling times so that 
      // we can avoid the repeative calculation here. The code is borrowed from window.cc
      // If the ckpt is within the first phase, window_time_desc->repeat does not need to be changed.
      if (index > 0) {
        if (index == (cmdl->n_coupling_phases - 1)) {
          if (cmdl->use_end_time_for_coupling) {
            window_time_desc->repeat = (cmdl->end_time - cmdl->m_coupling_phase_descs[index].delay 
                                              - cmdl->m_coupling_phase_descs[index].start)/cmdl->m_coupling_phase_descs[index].period;
          }
          else
            window_time_desc->repeat = cmdl->num_iterations; // cmdl->num_iterations now stores the 
                                                                   // number of iterations for the last phase 
        } else {
          window_time_desc->repeat = (cmdl->m_coupling_phase_descs[index+1].start 
                                            - cmdl->m_coupling_phase_descs[index].start)/cmdl->m_coupling_phase_descs[index].period;
        }
      }

      if (index > 0)
        surface_coupling->unrounded_time_desc.start = cmdl->m_coupling_phase_descs[index].exact_start + cmdl->m_coupling_phase_descs[index].period
          - cmdl->m_coupling_phase_descs[index].exact_interval;   // in user's steps
      surface_coupling->unrounded_time_desc.start *= cp_info.n_user_base_steps;   // in base steps
      surface_coupling->unrounded_time_desc.period = cmdl->m_coupling_phase_descs[index].exact_period * cp_info.n_user_base_steps; // in base steps
      surface_coupling->unrounded_time_desc.interval = cmdl->m_coupling_phase_descs[index].exact_interval * cp_info.n_user_base_steps; // in base steps


      // if the ckpt is before the delayed coupling, use the previous phase, otherwise use the next phase
      TIMESTEP end_of_current_phase = (index == (cmdl->n_coupling_phases-1))? cmdl->end_time : cmdl->m_coupling_phase_descs[index+1].start;
      TIMESTEP last_coupling_time = end_of_current_phase - cmdl->m_coupling_phase_descs[index].period
        + cmdl->m_coupling_phase_descs[index].delay;
      if (cp_info.restart_time >= last_coupling_time && index < (cmdl->n_coupling_phases-1))
        dgf_coupling_model.period = cmdl->m_coupling_phase_descs[index+1].period;
      else
        dgf_coupling_model.period = cmdl->m_coupling_phase_descs[index].period;

      coupling_model_desc->n_runs_launched = coupling_model_desc->n_coupling_models_read;
      coupling_model_desc->n_runs_before_ckpt_restore = coupling_model_desc->n_runs_launched;

      // Set init_pf_bc_coupling_p if the original simulation already consumed coupling data.
      if (next_read_time > dgf_coupling_model.first_interval)
        dgf_coupling_model.init_pf_bc_coupling_p = TRUE;
    }

    if (coupling_model_desc->n_runs_launched <= 0) {
      // check to see if all other dependent files referenced by the model file can be located
      check_coupling_dependent_files(cp_info.surface_couplings[model_index].tpi,
                                     cp_info.surface_couplings[model_index].cmdl.model_filename,
                                     cp_info.surface_couplings[model_index].cmdl.model_type);
    }

    if (IS_TAI_APP(cmdl->model_type)) {
      if (cp_info.ptherm_ckpt_skip_first_coupling_p) {
        //last_read_time = next_read_time;
        next_read_time += dgf_coupling_model.period;
      }
    }

    dgf_coupling_model.next_periodic_read = next_read_time;

    // check for any PF bcs which have been invalidated
    CDI_CPLW cplws = surface_coupling->get_cplws();
    ccDOTIMES (pf_bc,surface_coupling->get_cmdl()->num_pf_bcs) {
      if (-1 == surface_coupling->native_target_indices[pf_bc]) {
        ineligible_pf_bc_names_length += strlen(cp_info.sri_faces[cplws[pf_bc].face_index].name)+1;
        n_ineligible_pf_bcs++;
      }
    }
    if (n_ineligible_pf_bcs > 0) {
      ineligible_pf_bc_names = cnew char[ineligible_pf_bc_names_length];
      asINT32 offset = 0;
      ccDOTIMES (pf_bc,surface_coupling->get_cmdl()->num_pf_bcs) {
        if (-1 == surface_coupling->native_target_indices[pf_bc]) {
          strcat(ineligible_pf_bc_names+offset,cp_info.sri_faces[cplws[pf_bc].face_index].name);
          offset += strlen(cp_info.sri_faces[cplws[pf_bc].face_index].name)+1;
        }
      }
    }
    dgf_coupling_model.n_ineligible_pf_bcs = n_ineligible_pf_bcs;
    dgf_coupling_model.ineligible_pf_bc_names_length = ineligible_pf_bc_names_length;

    if (n_ineligible_pf_bcs > 0) {
      dgf_coupling_model.tag.length = lgi_pad_and_encode_record_length
        (sizeof(dgf_coupling_model) + dgf_coupling_model.name_length + dgf_coupling_model.ineligible_pf_bc_names_length+ dgf_coupling_model.n_vars*sizeof(asINT32));
    } else {
      dgf_coupling_model.tag.length = lgi_pad_and_encode_record_length
        (sizeof(dgf_coupling_model) + dgf_coupling_model.name_length + dgf_coupling_model.n_vars*sizeof(asINT32));
    }


    dgf_coupling_model.n_coupling_phases = cmdl->n_coupling_phases;

    // now send to all the SPs
    ccDOTIMES(sp, total_fsps) {
      dgf_coupling_model.write(g_sp_streams[sp], FALSE);
    }

    write_to_all_flow_sps(cmdl->model_filename, dgf_coupling_model.name_length);
    write_to_all_flow_sps(cmdl->import_variables, sizeof(DGF_COUPLING_VAR_TYPE)*dgf_coupling_model.n_vars);

    if (n_ineligible_pf_bcs > 0) {
      write_to_all_flow_sps(ineligible_pf_bc_names, dgf_coupling_model.ineligible_pf_bc_names_length);
      if (ineligible_pf_bc_names) {
        delete[] ineligible_pf_bc_names;
        ineligible_pf_bc_names = NULL;
      }
    }

    cDGF_COUPLING_PHASE dgf_coupling_phase = { 0 };
    dgf_coupling_phase.tag.id = DGF_COUPLING_PHASE_TAG;

    asINT32 n_coupling_phase = cmdl->m_coupling_phase_descs.size();
    ccDO_FROM_TO(i, 0, n_coupling_phase-1)
    {
      //PHASE_TIME_DESC phase_time_desc = (PHASE_TIME_DESC)&cmdl->m_coupling_phase_descs[i];  //in user's steps
      CDI_COUPLING_PHASE_DESC coupling_phase_desc = &cmdl->m_coupling_phase_descs[i];  //in user's steps

      dgf_coupling_phase.start = (i==0)? dgf_coupling_model.first_interval : (coupling_phase_desc->start + coupling_phase_desc->delay); // in user's steps
      dgf_coupling_phase.period = coupling_phase_desc->period;  // in user's steps
      dgf_coupling_phase.delay =  coupling_phase_desc->delay;   // in user's steps

      // now send to all the SPs
      ccDOTIMES(sp, total_fsps) {
        dgf_coupling_phase.write(g_sp_streams[sp], FALSE);
      }
    }

    if (!surface_coupling->tpi->GetModelPath()) {
      msg_error("Could not find the %s model file for model %d",surface_coupling->get_cmdl()->model_type,model_index);
    } else {
      coupling_model_desc->model_filename = strsave(surface_coupling->tpi->GetModelPath());
      if (IS_TAI_APP(cmdl->model_type)) {
        CHARACTER viewfactor_filename[PLATFORM_MAXPATHLEN];
        CHARACTER slf_filename[PLATFORM_MAXPATHLEN];
        CHARACTER tlf_filename[PLATFORM_MAXPATHLEN];
        memset(viewfactor_filename,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
        memset(slf_filename,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
        memset(tlf_filename,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
        compose_viewfactor_filename(coupling_model_desc->model_filename,viewfactor_filename);
        coupling_model_desc->viewfactor_filename = strsave(viewfactor_filename);
        compose_slf_filename(coupling_model_desc->model_filename,slf_filename);
        coupling_model_desc->slf_filename = strsave(slf_filename);
        compose_tlf_filename(coupling_model_desc->model_filename, tlf_filename);
        coupling_model_desc->tlf_filename = strsave(tlf_filename);
      }
    }
    if (!surface_coupling->tpi->GetResultPath()) {
      msg_error("Could not find the %s results file for model %d",surface_coupling->get_cmdl()->model_type,model_index);
    } else {
      coupling_model_desc->results_filename = cnew char[strlen(surface_coupling->tpi->GetResultPath()) + 1];
      strcpy(coupling_model_desc->results_filename, surface_coupling->tpi->GetResultPath());
    }
    coupling_model_desc->couple_during_sim_p = dgf_coupling_model.couple_during_sim_p;
    coupling_model_desc->init_pf_bc_coupling_p = dgf_coupling_model.init_pf_bc_coupling_p;
    coupling_model_desc->period = dgf_coupling_model.period;
    coupling_model_desc->first_interval = dgf_coupling_model.first_interval;
    //coupling_model_desc->last_run_read_time = last_read_time;
    coupling_model_desc->next_periodic_read = dgf_coupling_model.next_periodic_read;
    coupling_model_desc->end_time = dgf_coupling_model.end_time;

    sCOUPLING_PHASE_DESC coupling_time_desc;
    sPT_PF_RATIO_RECORDS coupling_ratios;
    ccDO_FROM_TO(i, 0, n_coupling_phase-1)
    {
      // start is the timestep to read in the surface data from PT (PT launch time + delay)
      coupling_time_desc.start    = (i==0)? 
                                    coupling_model_desc->first_interval 
                                    :
                                    cmdl->m_coupling_phase_descs[i].start + cmdl->m_coupling_phase_descs[i].delay;
      coupling_time_desc.period   = cmdl->m_coupling_phase_descs[i].period;
      coupling_time_desc.delay    = cmdl->m_coupling_phase_descs[i].delay;
      coupling_model_desc->m_coupling_phase_descs.push_back(coupling_time_desc);
      coupling_model_desc->m_coupling_phase_ratios.push_back(coupling_ratios);
      coupling_model_desc->m_previous_gradient = 0.0;
      coupling_model_desc->m_current_ratio_scale_factor = 1.0;
    }

    //ignore the first phase if it has zero length
    if((cmdl->n_coupling_phases >=2) &&
       (cmdl->m_coupling_phase_descs[1].start == cmdl->m_coupling_phase_descs[0].start))
      coupling_model_desc->period = cmdl->m_coupling_phase_descs[1].period;  // in user's steps

    coupling_model_desc->end_time = cmdl->end_time;

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
    //msg_print("the start is shifted by the first phase interval %d", surface_coupling->m_coupling_phase_descs[0].interval);
    msg_print("Coupling timesteps in coupling_model_desc:");
    ccDOTIMES(i, cmdl->n_coupling_phases)
    {
      sCOUPLING_PHASE_DESC coupling_phase = coupling_model_desc->m_coupling_phase_descs[i];
      msg_print("coupling phase %d start=%d period=%d delay=%d", i,
                coupling_phase.start,
                coupling_phase.period,
                coupling_phase.delay);
    }
#endif
  }

  open_coupling_model_summary_files(is_checkpoint_restore_p);
  allocate_shared_export_var_array();
}

VOID sCP_STREAM_MANAGER::define_surface_coupling_measurements_and_send_windows_to_sps()
{

  if (cp_info.n_surface_couplings <= 0)
    return;

  log_message_with_time("define_surface_coupling_measurements");
  // define surface coupling measurements based on cmdl chunks and cdi meas
  // windows created previously

  cDGF_MEAS_WINDOW dgf_window;
  asINT32 n_global_vertices = cp_info.native_mesh->NumVertices();
#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
  std::vector< bool > vertex_marks;
  vertex_marks.reserve(n_global_vertices);
  // We accumulate the vertex refs into a temporary vector and then
  // copy them into the meas window once we know how many we have.
  VMEM_VECTOR <DGF_VERTEX_INDEX> vertex_refs;
  vertex_refs.reserve(1024 * 1024);
#endif

  dgf_window.tag.id = DGF_COUPLING_MEAS_WINDOW_TAG;
  dgf_window.tag.length = 0;
  dgf_window.meas_type = LGI_SURFACE_WINDOW;
  dgf_window.meas_window_flags = 0x0; 

  TIMESTEP n_user_base_steps = cp_info.n_user_base_steps;
  //in base_steps, but here cp_info.start_time should be 0
  TIMESTEP start_base_time = cp_info.start_time * n_user_base_steps; 
  TIMESTEP current_base_time = cp_info.restart_base_time;

  ccDOTIMES(model_index, cp_info.n_surface_couplings) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
    SIM_ABSTRACT_SURFACE source_surf = surface_coupling->native_resulting_source_surface;
    dgf_window.cdi_meas_window_index = cp_info.n_cdi_meas_windows - cp_info.n_surface_couplings + model_index;
    CDI_MEAS_WINDOW cdi_meas_win = &cp_info.cdi_meas_windows[dgf_window.cdi_meas_window_index];
#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
    vertex_marks.assign(n_global_vertices, false);
    vertex_refs.resize(0);
    DGF_VERTEX_INDEX first_vertex_ref = 0;
#endif


    if (!source_surf || 0 == source_surf->NumFacetFaces()) {
      // initialize timers for one-way coupling (foreign->PF)
      TIMESTEP clear_time, output_time;
      asINT32 n_prior_frames;
      output_time = compute_next_time(start_base_time, current_base_time, &cdi_meas_win->fluid_time_desc, &clear_time, &n_prior_frames);  //in base steps
      surface_coupling->output_timestep = output_time / n_user_base_steps; //in user's timestep
      output_time = compute_next_time(output_time, output_time, &cdi_meas_win->fluid_time_desc, &clear_time,NULL); //in base steps
      surface_coupling->next_update_time.output_time = output_time / n_user_base_steps; //in user's timestep
      // maybe launch any surface coupling jobs with start time specified at t==0
      if (surface_coupling->launch_at_init_p && (cp_info.restart_time <= 0)) {
        run_surface_coupling_job(NULL, model_index, 0);
      }
      continue; // unmapped PF meas window for this coupling model
    }

    // fill in commom parameters to each window
    // window ids start from one in the LGI file, creating new window
    // automatically increments window_count for the next coupling model
    dgf_window.num_meas_cells = source_surf->NumFacetFaces();
    dgf_window.num_measurements = source_surf->NumFacetFaces();
    dgf_window.num_moving_meas_cells = 0;
    dgf_window.coarsest_shob_scale = ((dgf_window.meas_type == LGI_VOLUME_WINDOW) || (dgf_window.meas_type == LGI_SHELL_WINDOW) || (dgf_window.meas_type == LGI_SAMPLING_SHELL_WINDOW))
                                      ? cdi_meas_win->coarsest_cond_shob_scale : cdi_meas_win->coarsest_fluid_shob_scale;

    // coupling window still uses single floats
    CP_SURFACE_MEAS_WINDOW_SFLOAT meas_window = static_cast<CP_SURFACE_MEAS_WINDOW_SFLOAT> (cp_info.meas_windows.add_meas_window(&dgf_window, TRUE));
    meas_window->coupling_window_p = TRUE;
    if (meas_window->n_moving_meas_cells > 0) {
      msg_internal_error("Surface coupling measurement cannot include moving measurement surfels");
    }
    meas_window->coupling_model_index = model_index;
    meas_window->allocate_prelim_data_for_sri();

    cp_info.coupling_model_descs[model_index].window = meas_window;

#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
    meas_window->allocate_coupling_sri_data();
    sG3_BOX box = source_surf->GetBbox();
    if (!box.is_valid) {
      msg_internal_error("Cannot obtain PowerFLOW surface bounding box for coupling model %d",model_index);
    }
    ccDOTIMES(dim,3) {
      meas_window->min_bound[dim] = STP_COORD(box.min_bound.pcoord[dim]);
      meas_window->max_bound[dim] = STP_COORD(box.max_bound.pcoord[dim]);
    }
    meas_window->cdi_meas_window->flush_every_frame = true;
#endif

    meas_window->cdi_meas_window = cdi_meas_win;
    cdi_meas_win->one_window = meas_window->index; 

    asINT32 meas_surfel_index = 0;
    meas_window->m_n_vertex_refs = 0;
    meas_window->m_n_vertices = 0;

    // Start sending the coupling measurement window information to each SP
    write_header_to_all_flow_sps(dgf_window);

    ABSTRACT_SURFACE_DO_FACET_FACES(source_surf,facetFace,cMIO_MODELf::cMESH) {
      // area already in lattice units
      SURFEL_COUPLING_INFO surfel_coupling_info = m_dgf_reader.get_surfel_coupling_info(facetFace->m_index);
      STP_SURFEL_ID surfel_id, clone_id;
      if (facetFace->IsFront()) {
        surfel_id = surfel_coupling_info->id; 
        clone_id = surfel_coupling_info->clone_id;
      }
      else {
        surfel_id = surfel_coupling_info->inverted_id;
        clone_id = surfel_coupling_info->inverted_clone_id;
      }
      // Surface coupling implies STP_FLOW_REALM
      STP_PROC surfel_home_sp = (total_fsps > 1 ) ? cp_info.surfel_procs[STP_FLOW_REALM][surfel_id] : 0;
      meas_window->m_d_surfel_areas[meas_surfel_index] = surfel_coupling_info->area;
      if (meas_window->m_d_surfel_areas[meas_surfel_index] < DFLOAT_MIN) {
        msg_internal_error("Invalid surfel area for surface coupling measurement surfel ID %d",surfel_id);
      }

      if (is_sri_scale_finer(surfel_coupling_info->scale, meas_window->m_scales[meas_surfel_index])) {
        meas_window->m_scales[meas_surfel_index] = surfel_coupling_info->scale;
        meas_window->m_scales[meas_surfel_index] |= 0x80;
      }

#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
      cMIO_MODELf::cMESH::sBG_VECTOR3 normal = cp_info.native_mesh->GetFacetNormal(facetFace->m_index);
      asINT32 normal_index = meas_surfel_index*cp_info.n_dims;
      ccDOTIMES(n_i,cp_info.n_dims) {
        meas_window->m_surfel_normals[normal_index++] = normal[n_i];
      }
      if (cp_info.n_lrfs() > 0) {
        meas_window->m_ref_frame_indices.at(meas_surfel_index) = surfel_coupling_info->ref_frame_index;
      }
      cMIO_MODELf::cMESH::FACET my_facet = facetFace->GetFacet();
      meas_window->m_first_vertex_refs[meas_surfel_index] = first_vertex_ref;
      meas_window->m_faces[meas_surfel_index] = surfel_coupling_info->face_index;

      BREP_FACET_DO_VERTICES(vertex,my_facet,cMIO_MODELf::cMESH) {
        asINT32 vid = vertex->GetIndex();
        vertex_refs.push_back(vid);
        if (!vertex_marks[vid]) {
          vertex_marks[vid] = true;
          meas_window->m_n_vertices++;
        }
        first_vertex_ref++;
      }
#endif

      if (!meas_window->is_meas_cell_on_sp(meas_surfel_index, surfel_home_sp)) {
        meas_window->m_sp_n_meas_cells[surfel_home_sp] ++;

        if ((meas_surfel_index + 1) > meas_window->n_meas_cells)
          msg_internal_error("Coupling measurement surfel index %d for window \"%s\" is out of range [0,%lu)",
                             meas_surfel_index, meas_window->cdi_meas_window->name, meas_window->n_meas_cells);

        meas_window->add_sp_meas_cell_ref(meas_surfel_index, surfel_home_sp);
      }
      // send the meas_cell index and surfel id to each home sp. Send a sentinel
      // surfel_id of -1 to denote end of this record
      cDGF_SURFEL_IDS_MEAS_INDEX surfel_meas = {surfel_id, clone_id, meas_surfel_index};
      surfel_meas.write(g_sp_streams[surfel_home_sp]);
      meas_surfel_index++;
    }

    ccDOTIMES (sp, total_fsps) {
      // Setting signed values to unsigned integers ??
      cDGF_SURFEL_IDS_MEAS_INDEX surfel_meas = {(STP_SURFEL_ID) -1, (STP_SURFEL_ID) -1,  -1};
      surfel_meas.write(g_sp_streams[sp]);
    }

    // write the measurement window variable list
    write_meas_window_vars_to_sps(meas_window, TRUE);

#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
    if (0 == meas_window->m_n_vertices) {
      msg_warn("Number of vertices for measurement window for coupling model %d is zero",model_index);
    }
    meas_window->m_n_vertex_refs = vertex_refs.size();
    meas_window->m_vertex_refs = xnew sriINT [ vertex_refs.size() ];
    ccDOTIMES64(k, vertex_refs.size()) {
      meas_window->m_vertex_refs[k] = vertex_refs[k];
    }
#endif

    // maybe launch any surface coupling jobs with start time specified at t==0
    if (surface_coupling->launch_at_init_p && (cp_info.restart_time <= 0)) {
      launch_surface_coupling_app(meas_window, 0, TRUE);
    }

  } // n_surface_couplings

}


#endif // SURF_COUP

VOID sCP_STREAM_MANAGER::cp_send_rotational_dynamics_descs()
{
  if (cp_info.n_rotational_dynamics_descs <= 0)  // no rotational dynamics in case
    return;

  TIMESTEP delay = -1;
  // read the delay from the file user_input.init
  const asINT32 LINE_BUFFER_SIZE = 160;
  char in_line[LINE_BUFFER_SIZE];
  const char *delim = " ,\t\r\n";
  const char *input_file = "user_input.init";

  FILE *fp = fopen(input_file, "r");
  if (fp != NULL) {
    cBOOLEAN stop_reading = FALSE;
    while (fgets(in_line, LINE_BUFFER_SIZE-1, fp) != NULL && !stop_reading) {
      if (in_line[0] == '#' ) {
        continue;
      }
      else if (in_line[0] == '$') {
        printf("A $ sign is found.\n");
        stop_reading = TRUE;
      }
      else {
        char *parm_type = strtok(in_line, delim);
        if (parm_type == NULL) //empty line
          continue;
        char *parm_name = strtok(NULL, delim);
        char *parm_value = strtok(NULL, delim);

        if (strcmp(parm_name, "delay")==0) {
          delay = atof(parm_value);
          printf("delay = %d \n", delay);
        }
      }
    }
    printf("Closing the file %s.\n",  input_file);
    fclose (fp);
  }

  cDGF_ROTATIONAL_DYNAMICS_HEADER header;
  header.tag.id = DGF_ROTATIONAL_DYNAMICS_HEADER_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(cDGF_ROTATIONAL_DYNAMICS_HEADER) + cp_info.n_rotational_dynamics_descs*sizeof(cDGF_ROTATIONAL_DYNAMICS_DESC));
  header.n_rotational_dynamics_descs = cp_info.n_rotational_dynamics_descs;

  ccDOTIMES(sp, total_sps) {
    header.write(g_sp_streams[sp], FALSE);
  }

  ccDOTIMES (i, cp_info.n_rotational_dynamics_descs) {
    CP_ROTATIONAL_DYNAMICS_DESC rotdyn_desc = cp_info.rotational_dynamics_descs + i;
    cDGF_ROTATIONAL_DYNAMICS_DESC dgf_rot_dyn_desc = {0};

    dgf_rot_dyn_desc.initial_omega = rotdyn_desc->m_initial_omega;
    dgf_rot_dyn_desc.lrf_index     = rotdyn_desc->m_lrf_index;

    CP_MEAS_WINDOW window = rotdyn_desc->m_window;
    dgf_rot_dyn_desc.window_index = window->index;

    // calculate the start and end times for rotational dynamics
    dFLOAT period             = window->cdi_meas_window->fluid_time_desc.period;
    dFLOAT window_output_time = window->m_output_timestep;
    // For non-default values of cdi_start_time and cdi_end_time, PowerCASE should ensure
    // that they are bounded by the start and end times of the measurement window
    // MAX takes care of the default value when cdi_start_time==-1
    rotdyn_desc->m_start_time = MAX(window_output_time, window_output_time + period*floor((rotdyn_desc->m_cdi_start_time-window_output_time)/period));
    dFLOAT cdi_end_time = (rotdyn_desc->m_cdi_end_time>=0)? rotdyn_desc->m_cdi_end_time : window->cdi_meas_window->end_time;
    dFLOAT solver_end_time = cdi_end_time;
    if(cp_info.is_conduction && cp_info.is_flow) {
      if(window->is_cond_window())
        solver_end_time = cp_info.convert_to_ts_cond(cdi_end_time);
      else
        solver_end_time = cp_info.convert_to_ts_flow(cdi_end_time);
    }
    rotdyn_desc->m_end_time = window_output_time + period*floor((solver_end_time-window_output_time)/period);

    if (delay < 0) delay = period;
    dgf_rot_dyn_desc.delay = delay;
    dgf_rot_dyn_desc.meas_start_time = rotdyn_desc->m_start_time;
    dgf_rot_dyn_desc.end_time = (rotdyn_desc->m_end_time < sINT32_MAX - delay)? rotdyn_desc->m_end_time + delay : rotdyn_desc->m_end_time;
    if (cp_info.is_full_checkpoint_restore && cp_info.time <= rotdyn_desc->m_start_time - period + delay) {
      dgf_rot_dyn_desc.start_time = rotdyn_desc->m_start_time - period + delay;
      dgf_rot_dyn_desc.angular_acceleration = rotdyn_desc->m_angular_acceleration_m1;
      dgf_rot_dyn_desc.angular_acceleration_buffer = rotdyn_desc->m_angular_acceleration;
    } else {
      dgf_rot_dyn_desc.start_time = rotdyn_desc->m_start_time + delay;
      dgf_rot_dyn_desc.angular_acceleration = rotdyn_desc->m_angular_acceleration;
      dgf_rot_dyn_desc.angular_acceleration_buffer = rotdyn_desc->m_angular_acceleration;
    }

    // now send to all the SPs
    ccDOTIMES(sp, total_sps) {
      dgf_rot_dyn_desc.write(g_sp_streams[sp]);
    }
  }
}

VOID allocate_meas_window_ref_indices()
{
  DO_CP_MEAS_WINDOWS(window) {
    window->allocate_ref_frame_indices();
  }
}

VOID tag_any_curved_hx_meas_win_indices()
{

  // Tag the associated CDI meas window referenced by a curved heat exchanger
  // This will be used later to identify which sampling surface measurement windows
  // need to send their geometry information over to the SPs
  ccDOTIMES (i, cp_info.n_hxchs) {
    if ((cp_info.cdi_hxchs[i]->flags & CDI_HXCH_IS_CURVED)) {
      CDI_MEAS_WINDOW cdi_meas_win = &(cp_info.cdi_meas_windows[cp_info.cdi_hxchs[i]->inlet_meas_index]);
      cdi_meas_win->is_curved_hx_inlet = TRUE;
      cdi_meas_win->inlet_facet_offset = cp_info.cdi_hxchs[i]->inlet_facet_offset;
    }
  }
  ccDOTIMES (i, cp_info.n_cdsrs) {
    if ((cp_info.cdi_cdsrs[i]->flags & CDI_HXCH_IS_CURVED)) { 
      CDI_MEAS_WINDOW cdi_meas_win = &(cp_info.cdi_meas_windows[cp_info.cdi_cdsrs[i]->inlet_meas_index]);
      cdi_meas_win->is_curved_hx_inlet = TRUE;
      cdi_meas_win->inlet_facet_offset = cp_info.cdi_cdsrs[i]->inlet_facet_offset;
    }
  }
}

static std::string find_rad_file(const std::string& root_filename)
{
  if (cp_info.is_radiation) {
  std::string rad_filename = root_filename + ".rad";

  std::ifstream test(rad_filename,std::ios::in | std::ios::binary);

  if (!test.is_open()) {
    msg_unix_error("Could not open the RAD file \"%s\"", rad_filename.c_str());
  }

  return rad_filename;
  } else {
    return std::string{};
  }
}

static void exclusive_scan(std::vector<int>& v) 
{
  int previous_value_before_replacement = v[0];
  v[0] = 0; // Replace first element
  for (int i = 1; i < v.size(); i++) {
    int current_value_before_replacement = v[i];
    v[i] = previous_value_before_replacement + v[i-1];
    previous_value_before_replacement = current_value_before_replacement;
  }
  return;
}

//----------------------------------------------------------------------------
// read_dgf_and_cdi_files
// Open and read the CDI and LGI files, sending data to SP streams.
//----------------------------------------------------------------------------
VOID sCP_STREAM_MANAGER::read_dgf_and_cdi_files()
{
  // Function to automatically detect when a full checkpoint is HDF5 or it doesn't
  auto get_reader_type = []() -> sCP_DGF_READER::reader_t
  {
    if (!cp_info.is_full_checkpoint_restore)
      return sCP_DGF_READER::LEGACY;

    if (getenv("EXA_READER_ON_DEBUG_COMPARE")) {
      msg_print("Resume in Debug mode.");
      return sCP_DGF_READER::DEBUG_MODE;
    }
    else {
      if (H5Fis_hdf5(sim_args.resume_filename) > 0) {
        msg_print("Resuming from full checkpoint file written in parallel.");
        return sCP_DGF_READER::HDF5_SERIAL;
      }
      else {
        // Legacy mode, default. No info message.
        return sCP_DGF_READER::LEGACY;
      }
    }
  };
  const sCP_DGF_READER::reader_t reader_type = get_reader_type();

  // Opening files and creating streams
  cp_jobctl_output_status("Begin reading LGI and CDI files");
  m_cdi_reader.open_file(sim_args.cdi_filename);
  m_dgf_reader.open_file_streams(reader_type == sCP_DGF_READER::LEGACY ||
                                 reader_type == sCP_DGF_READER::DEBUG_MODE);

  // This code pretends that the chunks of the CDI file can be randomly accessed, but since
  // in reality, this is not true (chunks must be read in order), we access the chunks in an
  // order that minimizes the number of required passes over the CDI file (currently 3).

  // CDI file pass #1 (sCP_CDI_READER::enter_cdi_chunk assumes pass #1 only includes SIMV and GLOB)
  m_cdi_reader.read_simv_chunk();
//  read_standard_units_database();
//  m_cdi_reader.read_units_database();
  create_initial_solver_mask();         // time subcycling
  assign_solver_timesteps();            // time subcycling

  send_case_origin_to_sps();

  // read immediately after file header
  m_dgf_reader.read_table_of_contents_record(reader_type == sCP_DGF_READER::LEGACY ||
                                             reader_type == sCP_DGF_READER::DEBUG_MODE);

  if (total_sps > 1 || total_rps > 1) {
    // If the LGI is not decomposed for the desired number of SPs, open an LGI membuf stream
    // that will be connected to the inline decomposer.
    BOOLEAN launch_inline_decomposer = TRUE;
    if (total_sps > 1 && m_dgf_reader.is_lgi_file_decomposed()) {
      // read decomposer control record from LGI file
      m_dgf_reader.read_decomp_control_record();
      
      asINT32 num_sps_lgi_file = m_dgf_reader.decomp_control_record_num_sps();
      if (num_sps_lgi_file == total_sps)
        launch_inline_decomposer = FALSE;
    }

    if (total_rps > 1 && total_rps != m_dgf_reader.rad_file_num_rps()) {
      launch_inline_decomposer = TRUE;
    }

    if (launch_inline_decomposer) {
      if (!sim_args.decomp_file) {
        msg_error("Decomposition required, but no decomposer executable"
                  " specified via -decomp_file option");
        exit(exit_failure_code());
      }
      m_dgf_reader.open_inline_decomposer_stream();
    }
  }

   // Reading of parentage record depends on version number of the stream
   m_dgf_reader.copy_version_number_to_decomp_stream();

  // Read audit and parentage records before the control record. Note that this means
  // that the reads below of audit and parentage records either from the LGI file or
  // the checkpoint file never actually save audit or parentage information. This seems
  // correct but is rather misleading.
  m_dgf_reader.read_decomp_audit_trail_record();
  m_dgf_reader.read_decomp_parentage_record();

  if (total_sps > 1) {
    m_dgf_reader.read_neighbor_mask_table();
    // If not running the inline decomposer, we read the decomp control record from the
    // LGI file a second time simply to position the LGI file properly.
    m_dgf_reader.read_decomp_control_record();
  }

  m_dgf_reader.read_lattice_type_record();
  m_dgf_reader.read_control_record();           // sets cp_info.num_scales & cp_info.coarsest_scaled_coupled
  get_lighthill_solver_start_time();            // lighthill solver, after read_control_record()

  m_cdi_reader.read_coordinate_systems();
  read_standard_units_database();
  m_cdi_reader.read_units_database();

  m_cdi_reader.read_glob_chunk();               // relies on cp_info.num_scales
  assign_momentum_freeze_params();              // must after read_lattice_type_record() and read_control_record() and after read_glob_chuck 
  m_cdi_reader.read_cmps_chunk();               // 5g
  m_cdi_reader.read_scls_chunk();               // LB_UDS
  m_dgf_reader.send_control_record();   

  // Adjusts user specified phase intervales to align properly with coarsest coupled scale and send time coupling info.
  // Must be done after read_glob_chunk(), lighthill solver, and send_control_record(), so SPs have all info to process it.
  m_cdi_reader.adjust_and_send_time_coupling_schemes_info();

  maybe_terminate_simulation();
  m_cdi_reader.init_checkpoint_time_desc();     // relies on cp_info.num_scales
  process_checkpoint_override(); 

  asINT32 total_ckpt_sps = -1;
  if (cp_info.is_full_checkpoint_restore) {
    switch (reader_type) {
      case sCP_DGF_READER::LEGACY :
      {
        total_ckpt_sps = m_dgf_reader.read_ckpt_info_record<sCP_DGF_READER::LEGACY>();
        m_dgf_reader.read_ckpt_audit_trail_record          <sCP_DGF_READER::LEGACY>();
        m_dgf_reader.read_ckpt_parentage_record            <sCP_DGF_READER::LEGACY>();
        m_dgf_reader.read_ckpt_random_seed_record          <sCP_DGF_READER::LEGACY>(total_ckpt_sps);
        break;
      }
      case sCP_DGF_READER::HDF5_SERIAL :
      {
        total_ckpt_sps = m_dgf_reader.read_ckpt_info_record<sCP_DGF_READER::HDF5_SERIAL>();
        m_dgf_reader.read_ckpt_audit_trail_record          <sCP_DGF_READER::HDF5_SERIAL>();
        m_dgf_reader.read_ckpt_parentage_record            <sCP_DGF_READER::HDF5_SERIAL>();
        m_dgf_reader.read_ckpt_random_seed_record          <sCP_DGF_READER::HDF5_SERIAL>(total_ckpt_sps);
        break;
      }
      case sCP_DGF_READER::DEBUG_MODE :
      {
        total_ckpt_sps = m_dgf_reader.read_ckpt_info_record<sCP_DGF_READER::DEBUG_MODE>();
        m_dgf_reader.read_ckpt_audit_trail_record          <sCP_DGF_READER::DEBUG_MODE>();
        m_dgf_reader.read_ckpt_parentage_record            <sCP_DGF_READER::DEBUG_MODE>();
        m_dgf_reader.read_ckpt_random_seed_record          <sCP_DGF_READER::DEBUG_MODE>(total_ckpt_sps);
        break;
      }
      default:
        msg_internal_error("Unknown checkpoint mode");
    }
    write_avg_mme_ckpt_to_sps();
    write_local_vel_freeze_ckpt_to_sps();
  } else {
    m_dgf_reader.read_audit_trail_record();
    m_dgf_reader.read_parentage_record();
  }
  write_momentum_freeze_params_to_sps();     //must after read_ckpt_info_record
  write_large_pore_params_to_sps_and_set_input_rock_table();     //must after read_ckpt_info_record() and before smart_seed_ctl.setup()
  // The audit trail must be updated before any simerr entries are generated

  add_new_record_to_audit_trail();
  read_fluid_seed_vars_specs();   //new on 12/13/2021, support 5G fluid seeding which requires flag of is_5g_sim

  g_seed_ctl.setup();
  
  if (cp_info.is_mme_checkpoint_restore) {
    check_mme_ckpt_files_present_for_all_active_realms();
    read_mme_ckpt_restart_time(); // must be after seed file (a.k.a. MME restart file) is opened
  }
  write_start_time_to_sps();
  m_dgf_reader.rewind_to_beginning_of_file();

  // CDI file pass #2
  m_cdi_reader.read_ptge_chunk();
  m_cdi_reader.copy_encryption_data();
  m_cdi_reader.read_psdf_chunk();
  m_cdi_reader.read_tigr_chunk();

//  // Units database must be read before calling send_cp_to_sp_info_record
//  read_standard_units_database();
//  m_cdi_reader.read_units_database();

  cp_info.rad_filename = find_rad_file(cp_info.root_filename);

  send_cp_to_rp_info();

  if (total_rps > 1){
    char job_status[512];
    sprintf(job_status, "%s (Decomposing radiation patches for %d RPs)",                                                                                                                                                        
              cp_jobctl_status_string(),
              total_rps);
    cp_jobctl_output_status(job_status);
    
    msg_print("Decomposing radiation patches for %d RPs",total_rps);
    cp_info.patch_decomp = m_dgf_reader.allocate_patch_decomposition();
    recv_patch_decomp();

    sprintf(job_status, "%s (Done decomposing radiation patches)",                                                                                                                                                        
              cp_jobctl_status_string());
    cp_jobctl_output_status(job_status);
  }

  send_cp_to_sp_info_record();

#if SURF_COUP
  if (cTHIRD_PARTY_INTERFACE::IsConnectToPortDisabled()) {
    m_cdi_reader.read_scpl_chunk((cp_info.is_full_checkpoint_restore || cp_info.is_mme_checkpoint_restore), 
                                 cp_info.is_full_checkpoint_restore);
  }
  // Check out the PowerTHERM connector license
  init_xtr();
#endif
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  m_cdi_reader.read_particle_tracking_chunk();
//#endif
  m_cdi_reader.read_meas_chunk();

  if (cp_info.is_full_checkpoint_restore) {
    switch (reader_type)
    {
      case sCP_DGF_READER::LEGACY :
      {
        m_dgf_reader.read_turb_synth_info_ckpt_data<sCP_DGF_READER::LEGACY>();
        // This must precede read_meas_windows_header, which establishes meas window time descs
        m_dgf_reader.read_cdi_meas_window_ckpt_data<sCP_DGF_READER::LEGACY>();
        break;
      }
      case sCP_DGF_READER::HDF5_SERIAL :
      {
        m_dgf_reader.read_turb_synth_info_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
        // This must precede read_meas_windows_header, which establishes meas window time descs
        m_dgf_reader.read_cdi_meas_window_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
        break;
      }
      case sCP_DGF_READER::DEBUG_MODE :
      {
        m_dgf_reader.read_turb_synth_info_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();
        // This must precede read_meas_windows_header, which establishes meas window time descs
        m_dgf_reader.read_cdi_meas_window_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();
        break;
      }
      default:
        msg_internal_error("Unknown checkpoint mode");
    }
    m_dgf_reader.read_radiation_tm_ckpt_data();
  }

  m_cdi_reader.read_heat_exchangers_and_condensers();  
  tag_any_curved_hx_meas_win_indices();

  // This must be placed after the read of the units database
  m_cdi_reader.maybe_read_glob_chunk_to_define_dimless_units();
  send_units_db_to_sps();

  // CDI file pass #3

  m_cdi_reader.read_vehicle_defn();

  m_dgf_reader.read_global_part_names_record();
  m_dgf_reader.read_global_face_names_record();
  m_dgf_reader.read_dgf_part_protection_subrec();

  maybe_terminate_simulation();

  m_dgf_reader.read_vertex_coordinates();

  maybe_terminate_simulation();

  // split up the window read operations so that we read the header
  // information first and send it over to the SPs. We only allocate enough
  // data for surface windows at this stage to read the surfel descriptor
  // data to build the surface coupling interpolation maps.  The rest of the
  // measurement data is allocated and read after the surface coupling maps
  // have been built. This is done to minimize peak memory usage (see PR 22846).
  m_dgf_reader.read_meas_windows_header();  // calls align_meas_window_time_parameters

  // Must call the following hxch sync functions before tables are read, and
  // after meas window time descriptors have been computed after rounding in
  // read_meas_windows_header() above since HX related tables use measurement window
  // timing parameters which may need to be synced if the inlet/outlet
  // measurement windows are in different VR regions
  sync_series_heat_exchanger_meas_windows();
  sync_condenser_meas_windows();
  m_cdi_reader.read_old_tables();
  m_cdi_reader.read_new_tables();
  m_cdi_reader.read_equations();
  m_cdi_reader.read_eqn_structs();

  m_cdi_reader.read_turb_vel_tables();
  // NOTE: This order is important since equations must be read after tables
  // and coordinate systems, but before physics descriptors and reference
  // frames. 
  m_cdi_reader.read_global_reference_frame();
  m_cdi_reader.read_local_reference_frames();

  // LRF containment indices must be set before setup_mlrf_initial_position
  // This should also precede read_surfel_descriptors in order to assign the correct LRF index
  m_dgf_reader.read_lrf_containment();

  g_seed_ctl.setup_mlrf_initial_position();

  //allocate_surface_window_normals has been moved into
  //read_meas_window_header
  allocate_meas_window_ref_indices();
  m_cdi_reader.read_radiation_surface_conditions();
  m_cdi_reader.read_gravity_and_buoyancy();
  m_cdi_reader.read_body_force_descriptors();
  m_cdi_reader.read_scas_chunk(); // must be called before read_fluid_physics_descriptors
  m_cdi_reader.read_fluid_physics_descriptors();
  m_cdi_reader.read_shell_config_physics_descriptors();
  g_seed_ctl.setup_movb_initial_position();
  m_cdi_reader.read_seed_from_meas_descs();
  m_cdi_reader.read_flow_surface_physics_descriptors();
  m_cdi_reader.read_thermal_contact_table();
  m_cdi_reader.read_thermal_surface_physics_descriptors();
  m_cdi_reader.read_conduction_solid_materials();
  m_cdi_reader.read_uddl_chunk();
  m_cdi_reader.send_data_curves();
  if (g_seed_ctl.is_smart_seed()) {
    g_seed_ctl.assign_part_seed_var_specs();
  }
  m_cdi_reader.read_anisotropic_part_axis();
  

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  //This must occur before the meas windows are finalizes because PRI 
  //files, when opened, need to write the emitter geometry to the file.
  //m_cdi_reader.read_geometry_data_for_emitters();
//#endif

  m_cdi_reader.read_acceleration_factor();
  write_vapor_transport_to_sps();     // must after read_ckpt_info_record() and after accelration_factor

  //5G read rock type tables for large pore simulation with input_rock_table=TRUE
  if (cp_info.input_rock_table)   //before read_ublk_descriptors() where voxel is seeded. This order may not be necessary because now we check rock type in seed_voxel in Physics.
    write_porous_rock_tables();

  // read the sri data for windows
  m_dgf_reader.read_meas_windows_data();

  // Surfel normals are needed for split seeding, thus need to read in before seeding ublks
  if (cp_info.do_split_seed) {
    DO_REALMS(realm ) {
      m_dgf_reader.read_surfel_normals(realm);
    }
  }
  
  // For implicit solid solver
  // The implicit_solid_solver_state_index_offset vector contains the number of conduction voxels for each SP. It
  // will be used on the SPS side to get the global degree of freedom index for the implicit system.
  ccDOTIMES(i, total_sps) {
    cp_info.implicit_solid_solver_state_index_offset.push_back(0);
  }
  
  // Reading ublocks
  DO_REALMS(realm) {
    if (!cp_info.is_full_checkpoint_restore || reader_type == sCP_DGF_READER::LEGACY) {
      if (realm == STP_FLOW_REALM) {
        m_dgf_reader.read_ublk_descriptors<sCP_DGF_READER::LEGACY, cDGF_FLOW_UBLK_PROC, cDGF_FLOW_UBLK_PROC_GHOST>(realm);
      } else {
        m_dgf_reader.read_ublk_descriptors<sCP_DGF_READER::LEGACY, cDGF_COND_UBLK_PROC, cDGF_COND_UBLK_PROC_GHOST>(realm);
      }
    } else {
      if (reader_type == sCP_DGF_READER::HDF5_SERIAL) {
        if (realm == STP_FLOW_REALM) {
          m_dgf_reader.read_ublk_descriptors<sCP_DGF_READER::HDF5_SERIAL, cDGF_FLOW_UBLK_PROC, cDGF_FLOW_UBLK_PROC_GHOST>(realm);
        } else {
          m_dgf_reader.read_ublk_descriptors<sCP_DGF_READER::HDF5_SERIAL, cDGF_COND_UBLK_PROC, cDGF_COND_UBLK_PROC_GHOST>(realm);
        }
      } else if (reader_type == sCP_DGF_READER::DEBUG_MODE) {
        if (realm == STP_FLOW_REALM) {
          m_dgf_reader.read_ublk_descriptors<sCP_DGF_READER::DEBUG_MODE, cDGF_FLOW_UBLK_PROC, cDGF_FLOW_UBLK_PROC_GHOST>(realm);
        } else {
          m_dgf_reader.read_ublk_descriptors<sCP_DGF_READER::DEBUG_MODE, cDGF_COND_UBLK_PROC, cDGF_COND_UBLK_PROC_GHOST>(realm);
        }
      }
    }
  }
  
  // For implicit solid solver
  exclusive_scan(cp_info.implicit_solid_solver_state_index_offset); 

  if (m_cdi_reader.read_calibration_parameters())
    write_calibration_parameters_to_sps();
  //  m_cdi_reader.read_calibration_parameters();

  // Reading surfels
  cp_info.m_n_surfels_on_transient_seeded_faces = 0;



  // For the implicit shell solver. The implicit_shell_solver_state_index_offset vector contains the number of conduction shell surfels for each SP.
  // will be used on the SP side to get the global degree of freedom index for the implicit system.
  ccDOTIMES(i, total_sps) {
    cp_info.implicit_shell_solver_state_index_offset.push_back(0);
  }

  DO_REALMS(realm) {
    if (!cp_info.is_full_checkpoint_restore || reader_type == sCP_DGF_READER::LEGACY) {
      m_dgf_reader.read_surfel_descriptors<sCP_DGF_READER::LEGACY>(realm);
    } else if (reader_type == sCP_DGF_READER::HDF5_SERIAL) {
        m_dgf_reader.read_surfel_descriptors<sCP_DGF_READER::HDF5_SERIAL>(realm);
    } else if (reader_type == sCP_DGF_READER::DEBUG_MODE) {
        m_dgf_reader.read_surfel_descriptors<sCP_DGF_READER::DEBUG_MODE>(realm);
    }
  }

  // For implicit shell solver
  // The exclusive scan below is used to sort the shell solver state index vector so that each entry for the conduction
  // SPs indicates the global index in the Petsc vector for that degree of freedom. Each surfel descriptor contains the
  // local degree of freedom index. The implicit_shell_solver_state_index_offset will be sent to each SP and this will
  // be indexed by each processor to provide the offset to the global degree of freedom index in the Petsc vector. This
  // approach works with flow-only cases, conduction-only cases, and flow-conduction cases.
  exclusive_scan(cp_info.implicit_shell_solver_state_index_offset); // would be nice to use std::exclusive_scan, but our compiler doesn't support that; need gcc > 11

  m_dgf_reader.read_gap_contact_table_descriptor();
  
  m_dgf_reader.read_mlrf_ring_sets(cp_info.control_rec.num_flow_mlrf_ring_sets,STP_FLOW_REALM);
  m_dgf_reader.read_mlrf_ring_sets(cp_info.control_rec.num_cond_mlrf_ring_sets,STP_COND_REALM);

#if SURF_COUP
  // define and send measurement window records to the SPs for the surface
  // coupling measurement windows. This entire sequence below enclosed in
  // SURF_COUP must appear immediately after the surfel descriptors have been read 
  if (cp_info.n_surface_couplings > 0) {
    if (cp_jobctl_memory_status_requested()) {
      cp_jobctl_output_status("Done read of surfel descriptors");
      if (cp_info.native_mesh != NULL) {
        cp_info.native_mesh->MemUsage(stderr);
      }
    }

    process_surface_coupling_units(); // need a units database

    if (cp_jobctl_memory_status_requested()) {
      cp_jobctl_output_status("Done processing coupling units, building surface coupling maps");
    }

    build_surface_coupling_maps();

    if (cp_jobctl_memory_status_requested()) {
      cp_jobctl_output_status("Done build of coupling maps, about to send coupling meas windows");
    }

    // define equivalent CDI surface coupling meas window chunks
    add_surface_coupling_cdi_meas_windows();

    if (cp_jobctl_memory_status_requested()) {
      cp_jobctl_output_status("Done send of coupling windows, now the coupling models");
    }

    // send surface coupling model related information
    cp_send_coupling_models(cp_info.is_full_checkpoint_restore || cp_info.is_mme_checkpoint_restore);

    if (cp_jobctl_memory_status_requested()) {
      cp_jobctl_output_status("Coupling models sent");
    }

    // must send these before the ublk descriptors are sent, since all
    // measurement window processing on the SPs happens right after the ublk
    // descriptors are sent
    define_surface_coupling_measurements_and_send_windows_to_sps();
    // allocate coupling buffer map and store the facet indices for later
    // mapping so we can delete the facet storage from the native mesh
    allocate_coupling_buffer_storage();

    // delete some facet iterator data associated with the PowerTHERM->PowerFLOW mapping
    purge_surface_iterator_data();

    cp_info.native_mesh->ZeroHalfEdgeStorage();
    cp_info.native_mesh->ZeroFacetStorage();

    // copy the vertices from the brep mesh into a separate table and delete the
    // brep model
    // cp_info.copy_vertex_array_from_native_mesh();

    cp_info.native_mesh->ZeroVertexStorage();

    if (cp_info.native_model != NULL) {
      delete cp_info.native_model;
      cp_info.native_model = NULL;
      cp_info.native_mesh = NULL;
    }

    if (cp_jobctl_memory_status_requested()) {
      cp_jobctl_output_status("Native model deleted");
    }
  }

#endif // SURF_COUP

  maybe_append_experimental_vars();

  if(cp_info.is_particle_solver) {
    //Add any measurements that may be needed for unreleased solvers that don't yet have case or cdi support.
    cp_particle_sim.maybe_append_experimental_particle_var_types();
   
    //If there are any particle properties (which are averaged over particle populations) being measured,
    //make sure that population sizes are also recorded
    cp_particle_sim.maybe_append_particle_population_size_measurement_var_types();

    //Duplicate some meaurement variable types if the case requests measurements be
    //separated for each particle emitter or particle material.
    cp_particle_sim.expand_particle_measurements(); 


  }
  if(sim_args.disable_particle_modeling) {
    //Strip particle modeling variables from each window's meas variables if particle modeling has been diabled via a
    //command line argument. Also, if a window is set to only record particle modeling variables, disable the window.
    //Must be called before send_meas_vars_to_sps() and finish_init_of_meas_windows(). The latter sets the output
    //time for a window with no variables to TIMESTEP_MAX.
    cp_particle_sim.strip_particle_modeling_meas_variables();
  }


  // send the window meas vars before the bsurfel descriptors since the window
  // n_variables must be known for dynamic meas cell allocation on the SPs
  // during bsurfel descriptor parsing
  m_dgf_reader.send_meas_vars_to_sps();
  if (cp_info.is_full_checkpoint_restore) {
    // read the checkpointed xforms so that the bsurfels can be rotated to
    // their checkpointed position before sending them to their respective SPs
    if (reader_type == sCP_DGF_READER::LEGACY)
      m_dgf_reader.read_movb_ckpt_data<sCP_DGF_READER::LEGACY>();
    else if (reader_type == sCP_DGF_READER::HDF5_SERIAL)
      m_dgf_reader.read_movb_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
    else if (reader_type == sCP_DGF_READER::DEBUG_MODE)
      m_dgf_reader.read_movb_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();
  }

  std::unordered_map<STP_MEAS_WINDOW_INDEX,std::vector<STP_PROC> > moving_meas_cell_ckpt_sp;
  m_dgf_reader.read_bsurfel_descriptors(total_ckpt_sps == total_sps, moving_meas_cell_ckpt_sp);

  msg_print("\nCase Size\n---------");
  msg_print("Scales                   %9d",     (sINT32)cp_info.num_scales);
  msg_print("Voxels                   %9ld",   (sINT64)cp_info.total_fluid_like_voxels);
  msg_print("Fine Equivalent Voxels   %9ld",   (sINT64)cp_info.total_fluid_like_fevos);
  msg_print("Surfels                  %9ld",   (sINT64)cp_info.total_regular_surfels);
  msg_print("Fine Equivalent Surfels  %9ld",   (sINT64)cp_info.total_regular_fesus);
  msg_print("Bsurfels                 %9ld",   (sINT64)cp_info.total_bsurfels);

  // If sim_duration_via is set to AfterInitialTransient or GreatestMeasurementEndTime, do not 
  // print the number of timesteps.
  if (cp_info.sim_duration_via == eCDI_SIM_DURATION_VIA::SpecifiedBelow)
    msg_print("Timesteps                %9ld\n",  (long)  (cp_info.end_time - cp_info.start_time));
  else
    msg_print("\n");

  g_seed_ctl.wrapup();

  // Monitors should be read before finishing init meas windows, since it may change the start time
  // of meas windows which are started via monitors.
  m_cdi_reader.read_monitors();
  
  //Send the virtual wiper and emitter data to the SPs. This must
  //happen after the monitors are read becasue they may have changed
  //the activation times.
  m_cdi_reader.send_wipers();
  m_cdi_reader.send_particle_emitters();
  m_cdi_reader.read_geometry_data_for_emitters();

  // This must preceed read_meas_window_ckpt_data, which may reset meas window initial clear times.
  // Also, this must occur after read_geometry_for_surface_emitters() so that surface 
  // emitter geometry can be written to the header of PMR files (for trajectory measurement windows) 
  // which occurs within this function.
  finish_init_of_meas_windows(); 

  m_dgf_reader.send_meas_cell_reference_frame_conflicts();
  m_dgf_reader.send_meas_window_master_sps_and_output_times();

  // This must be called before m_dgf_reader.read_trajectory_id_map_ckpt
  initialize_trajectory_meas_data_comm();

  if (cp_info.is_full_checkpoint_restore) {
    if (reader_type == sCP_DGF_READER::LEGACY)
    {
      // sync record introduces ckpt data - allows SPs to exchange fan info without deadlock
      send_synchronization_message_to_all_sps();
      m_dgf_reader.read_meas_window_ckpt_data<sCP_DGF_READER::LEGACY>(total_ckpt_sps, moving_meas_cell_ckpt_sp); // needs meas window master SPs
      m_dgf_reader.read_global_nirf_ckpt_data<sCP_DGF_READER::LEGACY>();
      m_dgf_reader.read_lrf_ckpt_data<sCP_DGF_READER::LEGACY>();
      if(cp_info.lattice_type == STP_LATTICE_D19) {
        m_dgf_reader.read_thermal_accel_ckpt_data<sCP_DGF_READER::LEGACY>();
      }
      m_dgf_reader.read_fan_ckpt_data<sCP_DGF_READER::LEGACY>(total_ckpt_sps);
    if (cp_info.num_averaged_contacts > 0) {
      m_dgf_reader.read_averaged_contact_ckpt_data();
    }
      //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
      m_dgf_reader.read_particle_emitter_ckpt<sCP_DGF_READER::LEGACY>(total_ckpt_sps);
      m_dgf_reader.read_random_particle_property_state<sCP_DGF_READER::LEGACY>(total_ckpt_sps);
      m_dgf_reader.read_trajectory_id_map_ckpt<sCP_DGF_READER::LEGACY>(total_ckpt_sps);
      //#endif
      m_dgf_reader.read_coupling_model_pt_pf_ratio_history<sCP_DGF_READER::LEGACY>();
      m_dgf_reader.read_monitors_ckpt_data<sCP_DGF_READER::LEGACY>();
      m_dgf_reader.read_cp_rotdyn_ckpt_data<sCP_DGF_READER::LEGACY>();
      m_dgf_reader.read_tbs_desc_ckpt_data<sCP_DGF_READER::LEGACY>();
    }
    else if (reader_type == sCP_DGF_READER::HDF5_SERIAL)
    {
      // sync record introduces ckpt data - allows SPs to exchange fan info without deadlock
      send_synchronization_message_to_all_sps();
      m_dgf_reader.read_meas_window_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>(total_ckpt_sps, moving_meas_cell_ckpt_sp); // needs meas window master SPs
      m_dgf_reader.read_global_nirf_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
      m_dgf_reader.read_lrf_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
      if(cp_info.lattice_type == STP_LATTICE_D19) {
        m_dgf_reader.read_thermal_accel_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
      }
      m_dgf_reader.read_fan_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>(total_ckpt_sps);
      //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
      m_dgf_reader.read_particle_emitter_ckpt<sCP_DGF_READER::HDF5_SERIAL>(total_ckpt_sps);
      m_dgf_reader.read_random_particle_property_state<sCP_DGF_READER::HDF5_SERIAL>(total_ckpt_sps);
      m_dgf_reader.read_trajectory_id_map_ckpt<sCP_DGF_READER::HDF5_SERIAL>(total_ckpt_sps);
      //#endif
      m_dgf_reader.read_coupling_model_pt_pf_ratio_history<sCP_DGF_READER::HDF5_SERIAL>();
      m_dgf_reader.read_monitors_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
      m_dgf_reader.read_cp_rotdyn_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
      m_dgf_reader.read_tbs_desc_ckpt_data<sCP_DGF_READER::HDF5_SERIAL>();
    }
    else if (reader_type == sCP_DGF_READER::DEBUG_MODE)
    {
      // sync record introduces ckpt data - allows SPs to exchange fan info without deadlock
      send_synchronization_message_to_all_sps();
      m_dgf_reader.read_meas_window_ckpt_data<sCP_DGF_READER::DEBUG_MODE>(total_ckpt_sps, moving_meas_cell_ckpt_sp); // needs meas window master SPs
      m_dgf_reader.read_global_nirf_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();
      m_dgf_reader.read_lrf_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();
      if(cp_info.lattice_type == STP_LATTICE_D19) {
        m_dgf_reader.read_thermal_accel_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();
      }
      m_dgf_reader.read_fan_ckpt_data<sCP_DGF_READER::DEBUG_MODE>(total_ckpt_sps);
      //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
      m_dgf_reader.read_particle_emitter_ckpt<sCP_DGF_READER::DEBUG_MODE>(total_ckpt_sps);
      m_dgf_reader.read_random_particle_property_state<sCP_DGF_READER::DEBUG_MODE>(total_ckpt_sps);
      m_dgf_reader.read_trajectory_id_map_ckpt<sCP_DGF_READER::DEBUG_MODE>(total_ckpt_sps);
      //#endif
      m_dgf_reader.read_coupling_model_pt_pf_ratio_history<sCP_DGF_READER::DEBUG_MODE>();
      m_dgf_reader.read_monitors_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();
      m_dgf_reader.read_cp_rotdyn_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();
      m_dgf_reader.read_tbs_desc_ckpt_data<sCP_DGF_READER::DEBUG_MODE>();
    }
  }

  if (cp_info.is_mme_checkpoint_restore) {
    asINT32 n_monitors = cp_info.monitors.size();
    ccDOTIMES(i, n_monitors) { 
      cp_info.monitors[i]->read_monitor_signals_from_file(cp_info.restart_time);
    }
  }

  // We need to call cp_send_rotational_dynamics_descs() after reading the 
  // checkpoint data for rotational dynamics. This is because it sends the 
  // checkpointed angular acceleration to all the SPs
  //
  // Also note that it needs the start and end times of the measurement windows
  // to calculate the start and end times of the corresponding rotational 
  // dynamics. Hence, it should be called after finish_init_of_meas_windows()
  if (cp_info.n_rotational_dynamics_descs > 0) {
    cp_send_rotational_dynamics_descs();
  }
 
  // NOTE: the adaptive code assumes there is only one coupling model, i.e. n_surface_couplings = 1.
  //       Should be extended in the future to support multiple coupling models.
  std::vector<int> adaptive_phases;
  ccDOTIMES (model_index, cp_info.n_surface_couplings) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    ccDOTIMES(i, cmdl->n_coupling_phases) {
      if (cmdl->m_coupling_phase_descs[i].adaptive_p) {
        adaptive_phases.push_back(i); 
      }
    }
  } 

  if (adaptive_phases.size() > 0) {
#if DEBUG_ADAPTIVE_COUPLING
    msg_print("------------------------------------------");
    std::cout << "Adaptive phases: ";
    for (auto& phase : adaptive_phases)
      std::cout << phase << " ";
    std::cout << std::endl;
#endif
    // Update adaptive PowerTHERM coupling parameters to mks units
    cp_info.m_adaptive_params.update_adaptive_params();
  }

  DO_CP_MEAS_WINDOWS(window)
    window->warn_about_meas_window_timing_changes_and_maybe_set_prev_nsets_written();

  DO_CP_MEAS_WINDOWS(window) {
    if (window->coupling_window_p) {
      //msg_print("Coupling window %s", window->cdi_meas_window->name);
      dFLOAT pt_end_time = run_surface_coupling_job(window,
                                                    window->coupling_model_index,
                                                    cp_info.end_time,
                                                    cp_info.end_time,
                                                    TRUE);
      //msg_print("PT end time = %f", pt_end_time);
      if (cp_info.adaptive_coupling_p && cp_info.m_adaptive_params.fix_pt_time_p) {
        // If user specifies total pt duration, use that instead of the calculated pt_end_time which is inferred from PF duration
        if (cp_info.m_adaptive_params.total_pt_duration <= 0)
          cp_info.m_adaptive_params.total_pt_duration = pt_end_time;
      }
    }
  }

  write_implicit_solver_data();

  m_dgf_reader.close_file_streams(reader_type == sCP_DGF_READER::LEGACY ||
                                  reader_type == sCP_DGF_READER::DEBUG_MODE);
  m_cdi_reader.close_file();

  cp_jobctl_output_status("Done reading LGI and CDI files"); // Don't use word "Finished"
  send_synchronization_message_to_all_sps();
}

//----------------------------------------------------------------------------
// send_case_origin_to_sps
//----------------------------------------------------------------------------
VOID sCP_STREAM_MANAGER::send_case_origin_to_sps()
{
  LGI_CASE_ORIGIN case_origin;
  case_origin.tag.id         = LGI_CASE_ORIGIN_TAG;
  case_origin.tag.length     = lgi_pad_and_encode_record_length(sizeof(case_origin));

  case_origin.case_origin[0] = cdi_data.origin[0];
  case_origin.case_origin[1] = cdi_data.origin[1];
  case_origin.case_origin[2] = cdi_data.origin[2];

  write_header_to_all_sps(case_origin);
}

//----------------------------------------------------------------------------
// read_standard_units_database
//----------------------------------------------------------------------------
VOID sCP_STREAM_MANAGER::read_standard_units_database()
{ 
  // First read the standard units database
  cSTRING units_file_name = getenv("EXA_CPC_UNITS_FILE");

  if (units_file_name == NULL) 
    msg_internal_error("Environment variable EXA_CPC_UNITS_FILE not set");

  FILE *units_file = fopen(units_file_name, "r");

  if (units_file == NULL) 
    msg_internal_error("Failure to open units database file: %s", units_file_name);

  cp_info.units_db = units_create_units_db();

  UNITS_STATUS ustatus = units_initialize_db_from_file(cp_info.units_db, units_file, 
    [](asINT32 lineno, ucSTRING error) 
    {
      msg_internal_error("Failed to parse standard units database line %d: %s", lineno, error); 
      return TRUE;
    }
  );

  fclose(units_file);
}

//----------------------------------------------------------------------------
// send_units_db_to_sps
//----------------------------------------------------------------------------
VOID sCP_STREAM_MANAGER::send_units_db_to_sps()
{
  LGI_UNITS_DB_HEADER header;
  STRING new_units_db_str;

  units_write_db_to_string(cp_info.units_db, &new_units_db_str);
  asINT32 units_db_length = strlen(new_units_db_str);

  header.tag.id          = LGI_UNITS_DB_TAG;
  header.tag.length      = lgi_pad_and_encode_record_length(sizeof(LGI_UNITS_DB_HEADER)+ units_db_length);
  header.units_db_length = units_db_length;

  write_header_to_all_sps(header);
  write_to_all_sps(new_units_db_str, units_db_length*sizeof(char));

  exa_free(new_units_db_str);
}

static dFLOAT compute_stefan_boltzmann_constant()
{
  UNITS_UNIT mks_unit;
  UNITS_UNIT lat_unit;
  int status = 0;
  status = units_parse_unit(cp_info.units_db,  "watt/((meter^2)*(kelvin^4))", &mks_unit);
  if (status != UNITS_STATUS_OK) {
    msg_internal_error("Cannot parse Stefan-Boltzmann constant");
  }

  // By using LatticeWallHeatFlux, we use the correct scaling for thermal quantities
  // Instead of using LatticePower/LatticeArea, which uses a mechanical scaling instead
  status = units_parse_unit(cp_info.units_db,  "LatticeWallHeatFlux/(LatticeTemperature^4)", &lat_unit);
  if (status != UNITS_STATUS_OK) {
    msg_internal_error("Cannot parse Stefan-Boltzmann constant");
  }

  dFLOAT value;
  // Before 2019, this constant was defined by NIST as 5.670367(13)e-8 W/(m^2*K^4)
  // After 2019, it is defined exactly as 5.670374419...e-8 W/(m^2*K^4)
  // The last few decimal places probably don't change much, and this was written in
  // 2023, so we will use the new value.
  status = units_convert(cp_info.units_db, 5.670'374'419'184'429'453'970e-8, mks_unit, lat_unit, &value);
  if (status != UNITS_STATUS_OK) {
    msg_internal_error("Cannot convert Stefan-Boltzmann constant");
  }

  return value;
}

VOID sCP_STREAM_MANAGER::send_cp_to_rp_info()
{
  if (!cp_info.is_radiation) {
    return;
  }

  int64_t restart_time = -1;

  if (cp_info.is_full_checkpoint_restore) {
    restart_time = cp_info.restart_time;
  }

  cp_info.stefan_boltzmann_const = compute_stefan_boltzmann_constant();

  // uint64_t n_lb_base_steps = cp_info.n_lb_base_steps;
  // uint64_t n_conduction_base_steps = cp_info.n_conduction_base_steps;
  // uint64_t n_radiation_base_steps = cp_info.n_radiation_base_steps;
  int report_rp_time = sim_args.run_options & SIM_REPORT_RP_TIME;
  int report_affinity= sim_args.run_options & SIM_REPORT_CPU_AFFINITY;

  for (int rp=0; rp < total_rps; rp++) {
    MPI_Send(&restart_time, 1, MPI_INT64_T, rp, eMPI_CP_TO_RP_INIT_TAG, eMPI_rp_cp_comm);
    MPI_Send(&cp_info.stefan_boltzmann_const, 1, MPI_DOUBLE, rp, eMPI_CP_TO_RP_INIT_TAG, eMPI_rp_cp_comm);
    MPI_Send(&cdi_data.radiation_background_temp, 1, MPI_DOUBLE, rp, eMPI_CP_TO_RP_INIT_TAG, eMPI_rp_cp_comm);
    MPI_Send(cp_info.rad_filename.data(), cp_info.rad_filename.size(), MPI_CHAR, rp, eMPI_CP_TO_RP_INIT_TAG, eMPI_rp_cp_comm);
    MPI_Send(&report_rp_time , 1, MPI_INT, rp, eMPI_CP_TO_RP_INIT_TAG, eMPI_rp_cp_comm);
    MPI_Send(&report_affinity, 1, MPI_INT, rp, eMPI_CP_TO_RP_INIT_TAG, eMPI_rp_cp_comm );
  }

}

VOID sCP_STREAM_MANAGER::recv_patch_decomp()
{
  
  std::vector<int> recvcounts(eMPI_rp_cp_rank()+1); // includes cp

  MPI_Gather(MPI_IN_PLACE, 0, MPI_INT, 
            recvcounts.data(), 1, MPI_INT, 
            eMPI_rp_cp_rank(), eMPI_rp_cp_comm);

  std::vector<int> displc(recvcounts.size());
  for(int i=1; i<recvcounts.size(); ++i){
    displc[i] = displc[i-1] + recvcounts[i-1];
  }

  if(!cp_info.patch_decomp.decomp()){
    msg_internal_error("sCP_STREAM_MANAGER::cp_info.patch_decomp has not been allocated!");
  }

  if(displc.back() != cp_info.patch_decomp.num_patches()){
    msg_internal_error("Recv count of decomp patches is wrong!");
  }

  int dataSize;
  MPI_Type_size(MPI_INT16_T,&dataSize);
  if(dataSize != sizeof(RadIO::PATCH_RP)){
    msg_internal_error("Something is wrong with size of RadIO::PATCH_RP.");
  }
  
  MPI_Gatherv(MPI_IN_PLACE,
                0, // no contribution from this rank (CP)
                MPI_INT16_T, 
                cp_info.patch_decomp.decomp(),
                recvcounts.data(),
                displc.data(),
                MPI_INT16_T,
                eMPI_rp_cp_rank(),
                eMPI_rp_cp_comm);

}

//----------------------------------------------------------------------------
// send_cp_to_sp_info_record
//----------------------------------------------------------------------------
VOID sCP_STREAM_MANAGER::send_cp_to_sp_info_record()
{
  LGI_CP_TO_SP_INFO_REC record;

  record.tag.id     = LGI_CP_TO_SP_INFO_TAG;
  record.tag.length = lgi_pad_and_encode_record_length(sizeof(record));

  record.max_vel    = -1;
  record.max_temp   = -1;
  record.min_temp   = -1;
  record.ignore_vel_n_timesteps  = sim_args.ignore_vel_n_timesteps;
  record.ignore_temp_n_timesteps = sim_args.ignore_temp_n_timesteps;
  record.ignore_uds_n_timesteps = sim_args.ignore_uds_n_timesteps;
  record.timers_on_timestep = sim_args.timers_on_timestep;
  record.timers_off_timestep = sim_args.timers_off_timestep;

  if (sim_args.max_vel  != NULL)
    uval_to_lattice_value(sim_args.max_vel , "Velocity"   , "velocity"   , "LatticeVelocity"   , "--maxvel" , TRUE, &record.max_vel);

  if (sim_args.max_temp != NULL)
    uval_to_lattice_value(sim_args.max_temp, "Temperature", "temperature", "LatticeTemperature", "--maxtemp", TRUE, &record.max_temp);

  if (sim_args.min_temp != NULL) 
    uval_to_lattice_value(sim_args.min_temp, "Temperature", "temperature", "LatticeTemperature", "--mintemp", TRUE, &record.min_temp);

  record.stefan_boltzmann_const = cp_info.stefan_boltzmann_const;

  record.enable_tangential_shell_conduction = cp_info.enable_tangential_shell_conduction;

  write_header_to_all_sps(record);

}

#if SURF_COUP
static int cmp_surfel_ids(const VOID *i, const VOID *j)
{
  return ((((SURFEL_INDEX_MAP) i)->surfel_id) - (((SURFEL_INDEX_MAP) j)->surfel_id));
}

static asINT32 get_coupling_buf_index(COUPLING_MODEL_DESC coupling_model_desc, PROC_SURFEL_ID ps_id)
{
  sSURFEL_INDEX_MAP key;
  if (!coupling_model_desc || !ps_id)
    return -1;

  asINT32 proc = ps_id->proc;
  key.surfel_id = ps_id->surfel_id;

  SURFEL_INDEX_MAP result = SURFEL_INDEX_MAP(bsearch
                                             (&key,coupling_model_desc->surfel_index_map_per_sp[proc],
                                              coupling_model_desc->n_surfels_per_sp[proc], 
                                              sizeof(sSURFEL_INDEX_MAP),cmp_surfel_ids));

  if (!result)
    return -1;
  else
    return result->surfel_index;
}

VOID sCP_STREAM_MANAGER::allocate_coupling_buffer_storage()
{
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
  ccDOTIMES(i,cp_info.n_surface_couplings) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_desc->coupling_model_index;

    // one-way coupling
    if (surface_coupling->cmdl.num_pf_bcs <= 0)
      continue;

    coupling_model_desc->nsurfels_per_target = cnew asINT32[surface_coupling->cmdl.num_pf_bcs];
    coupling_model_desc->buf_indices_per_target = cnew asINT32*[surface_coupling->cmdl.num_pf_bcs];
    coupling_model_desc->proc_indices_per_target = cnew asINT32*[surface_coupling->cmdl.num_pf_bcs];
    coupling_model_desc->is_front_per_target = cnew cBOOLEAN*[surface_coupling->cmdl.num_pf_bcs];

    ccDOTIMES (pf_bc, surface_coupling->cmdl.num_pf_bcs) {
      // skip invalid native targets for which result eligibility failed
      if (-1 == surface_coupling->native_target_indices[pf_bc]) {
        continue;
      }

      if ((surface_coupling->native_target_surface[pf_bc])->NumFacetFaces() <= 0) {
        surface_coupling->native_target_surface[pf_bc] = NULL;
        continue;
      }

      coupling_model_desc->nsurfels_per_target[pf_bc] = (surface_coupling->native_target_surface[pf_bc])->NumFacetFaces();
      coupling_model_desc->buf_indices_per_target[pf_bc] = cnew asINT32[(surface_coupling->native_target_surface[pf_bc])->NumFacetFaces()];
      coupling_model_desc->proc_indices_per_target[pf_bc] = cnew asINT32[(surface_coupling->native_target_surface[pf_bc])->NumFacetFaces()];
      coupling_model_desc->is_front_per_target[pf_bc] = cnew cBOOLEAN[(surface_coupling->native_target_surface[pf_bc])->NumFacetFaces()];

      asINT32 target_index = 0;
      ABSTRACT_SURFACE_DO_FACET_FACES((surface_coupling->native_target_surface[pf_bc]), facetFace, cMIO_MODELf::cMESH) {
        coupling_model_desc->buf_indices_per_target[pf_bc][target_index] = facetFace->m_index;
        coupling_model_desc->is_front_per_target[pf_bc][target_index] = (cBOOLEAN)facetFace->IsFront();
        target_index++;
      }
    }
    coupling_model_desc++; // for each coupling model
  }
}

VOID sCP_STREAM_MANAGER::build_coupling_buffer_map()
{
  // sort the index map by surfel id
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
  ccDOTIMES(i,cp_info.n_surface_couplings) {
    ccDOTIMES(proc,total_fsps) {
      qsort(coupling_model_desc->surfel_index_map_per_sp[proc],coupling_model_desc->n_surfels_per_sp[proc], sizeof(sSURFEL_INDEX_MAP),cmp_surfel_ids);
      // do not need these anymore
      delete[] coupling_model_desc->surfel_ids_per_sp[proc];
    } // for each proc
    delete[] coupling_model_desc->surfel_ids_per_sp;
    coupling_model_desc->surfel_ids_per_sp = NULL;

    // now fill in the coupling data buffer map 
    // (surfel_proc_id -> coupling buf index) 
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_desc->coupling_model_index;

    // one-way coupling
    if (surface_coupling->cmdl.num_pf_bcs <= 0)
      continue;

    ccDOTIMES (pf_bc, surface_coupling->cmdl.num_pf_bcs) {

      // skip invalid native targets for which result eligibility failed
      if (-1 == surface_coupling->native_target_indices[pf_bc]) {
        continue;
      }

      // skip target faces with no mesh mapped
      if (NULL == surface_coupling->native_target_surface[pf_bc]) {
        continue;
      }

      asINT32 target_index = 0;
      ccDOTIMES (target_index, coupling_model_desc->nsurfels_per_target[pf_bc]) {
        // The BREP facet index is cached temporarily in the buf_indices array. 
        asINT32 facetIndex = coupling_model_desc->buf_indices_per_target[pf_bc][target_index];
        cBOOLEAN is_front = coupling_model_desc->is_front_per_target[pf_bc][target_index];
        SURFEL_COUPLING_INFO surfel_coupling_info = m_dgf_reader.get_surfel_coupling_info(facetIndex);
        STP_SURFEL_ID surfel_id = (is_front) ? surfel_coupling_info->id : surfel_coupling_info->inverted_id;
        sPROC_SURFEL_ID ps_id = {static_cast<STP_PROC>(0), static_cast<STP_SURFEL_ID>(surfel_id)};
        if (total_fsps > static_cast<STP_PROC>(1)) {
          // Surface coupling implies STP_FLOW_REALM
          ps_id.proc = cp_info.surfel_procs[STP_FLOW_REALM][surfel_id];
        }
        asINT32 buf_index = get_coupling_buf_index(coupling_model_desc,&ps_id);
        if (buf_index < 0) {
          msg_internal_error("Invalid coupling data buffer index for SP %d surfel ID %d mesh index %d",
              ps_id.proc, ps_id.surfel_id, facetIndex);
        }

        // overwrite the facet index with the actual coupling buffer index
        coupling_model_desc->buf_indices_per_target[pf_bc][target_index] = buf_index;
        coupling_model_desc->proc_indices_per_target[pf_bc][target_index] = ps_id.proc;
      }
      //is_front info no longer needed, we can clean it up
      delete coupling_model_desc->is_front_per_target[pf_bc];
    }
    delete coupling_model_desc->is_front_per_target;
    coupling_model_desc->is_front_per_target = NULL;
    coupling_model_desc++; // for each coupling model
  }
}

VOID sCP_STREAM_MANAGER::exchange_cp_coupling_info()
{
  asINT32 length[STP_MAX_PROCS];
  VOID *data[STP_MAX_PROCS];


  if (cp_info.n_surface_couplings <= 0) 
    return;

  /* Coupling model data comm - allocate and clear */
  cp_info.coupling_model_comm_descs = cnew sCP_COUPLING_MODEL_COMM_DESC[total_fsps];

  // zero it out to initialize
  asINT32 *max_surfel_values_per_sp = cnew asINT32[total_fsps];

  // now receive the coupling model size and surfel ids
  {
    MPI_Status status;
    ccDOTIMES (model_index, cp_info.n_surface_couplings) {
      COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs + model_index;
      SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+coupling_model_desc->coupling_model_index;
      coupling_model_desc->n_surfels_per_sp = cnew asINT32[total_fsps];
      coupling_model_desc->surfel_ids_per_sp = cnew STP_SURFEL_ID*[total_fsps];
      coupling_model_desc->surfel_index_map_per_sp = cnew SURFEL_INDEX_MAP[total_fsps];
      ccDOTIMES(proc, (total_fsps)) {
        RECV_EXA_SIM_MSG<asINT32, 1> nsurfels(eMPI_COUPLING_DATA_SIZE_TAG, proc);
        cp_init_mpi_recv<asINT32, 1>(nsurfels.mpi_msg, proc, eMPI_COUPLING_DATA_SIZE_TAG, eMPI_sp_cp_comm, &status);
        coupling_model_desc->n_surfels_per_sp[proc] = *nsurfels.return_buffer();
        if (*nsurfels.return_buffer() > 0) {
          (coupling_model_desc->surfel_ids_per_sp)[proc] = cnew STP_SURFEL_ID[*nsurfels.return_buffer()];
          (coupling_model_desc->surfel_index_map_per_sp)[proc] = cnew sSURFEL_INDEX_MAP[*nsurfels.return_buffer()];
          RECV_EXA_SIM_MSG<STP_SURFEL_ID> surfel_ids_per_sp(eMPI_COUPLING_SURFEL_IDS_TAG, *nsurfels.return_buffer(), 
                                                            (coupling_model_desc->surfel_ids_per_sp)[proc], proc);
          cp_init_mpi_recv<STP_SURFEL_ID>(surfel_ids_per_sp.mpi_msg, proc, eMPI_COUPLING_SURFEL_IDS_TAG, eMPI_sp_cp_comm, &status);
          ccDOTIMES(surfel_index, *nsurfels.return_buffer()) {
            (coupling_model_desc->surfel_index_map_per_sp)[proc][surfel_index].surfel_index = surfel_index;
            (coupling_model_desc->surfel_index_map_per_sp)[proc][surfel_index].surfel_id = (coupling_model_desc->surfel_ids_per_sp)[proc][surfel_index];
          }
          max_surfel_values_per_sp[proc] = MAX(max_surfel_values_per_sp[proc],
                                               *nsurfels.return_buffer()*surface_coupling->n_import_vars());
        }
      }
    }
  }

  // reuse coupling model data array for all coupling models, and all SPs. Will be refilled
  // each time the coupling data is available for each SP and each coupling model
  ccDOTIMES(proc,total_fsps) {
    if (max_surfel_values_per_sp[proc] > 0) {
      cp_info.coupling_model_comm_descs[proc].coupling_data_buf = cnew sFLOAT[max_surfel_values_per_sp[proc]];
    }
  }

  build_coupling_buffer_map();
  delete[] max_surfel_values_per_sp;
  max_surfel_values_per_sp = NULL;
  m_dgf_reader.free_surfel_coupling_info();

  // Need to keep the native mesh around, since it is used by the measurement code. 
  // so do not delete the native mesh

  dbg_msg (3, "Done sending cp_coupling_info to SP");
}
#endif // SURF_COUP

