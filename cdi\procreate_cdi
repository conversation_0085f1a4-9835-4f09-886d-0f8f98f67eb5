#!/bin/csh

# usage: procreate_cdi case_dir assy_name case_name run_dir pro_cmd pro_args

if ($#argv < 5) then
  echo "usage: ${0} case_dir assy_name case_name run_dir pro_cmd pro_args"
  exit 1
endif

set dir=$1
set assy=$2
set case=$3
set rundir=$4

shift
shift
shift
shift

cd $dir
set dir=`echo $cwd`

set pid=$$

set configdir=/tmp/procreate${pid}
mkdir $configdir
set trailfile=${configdir}/procreate_trail.txt

cd $configdir
echo "bell no" > config.pro
echo "trail_delay 0" >> config.pro
echo "graphics no_graphics" >> config.pro

# modify the trail file template (make a new copy)

sed -e "s:__dir_name:${dir}:g" \
 -e "s:__assy_name:${assy}:g" \
 -e "s:__case_name:${case}:g" \
 -e "s:__run_dir_name:${rundir}:g" \
 /proj/sw/cdi/010-hoch/procreate_cdi.template > $trailfile

# run pro-engineer

echo "$* $trailfile"
$* $trailfile

'rm' -f $trailfile
'rm' -f config.pro

# get out of configdir so it can be deleted
cd $dir
'rmdir' $configdir
