/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

/*--------------------------------------------------------------------------*
 * Autostop monitor support
 *--------------------------------------------------------------------------*/

#ifndef __MONITOR_H
#define __MONITOR_H

#include "common.h"

#include "window.h"

#include MSA_H

#define DEBUG_MONITOR 0

#define DEBUG_MONITOR_CKPT 0

// Use this option to print everything in lattice units. For debugging purpose only.
#define USE_LATTICE_UNITS_FOR_FLOW_MONITORS    0

const char* const monitors_dir_name = "monitors";


struct MONITOR_SIGNAL {
  sFLOAT signal;
  TIMESTEP signal_timestep;
  TIMESTEP next_signal_timestep;
};

inline bool is_powertherm_var_type_surface_htc (eCDI_MNTR_PT_VAR::Enum pt_var_type)
{ 
  return (pt_var_type == eCDI_MNTR_PT_VAR::MeanHTC 
          || pt_var_type == eCDI_MNTR_PT_VAR::MaxHTC 
          || pt_var_type == eCDI_MNTR_PT_VAR::MinHTC);
}

inline bool is_powertherm_var_type_surface_temp (eCDI_MNTR_PT_VAR::Enum pt_var_type)
{ 
  return (pt_var_type == eCDI_MNTR_PT_VAR::MeanTemp 
          || pt_var_type == eCDI_MNTR_PT_VAR::MaxTemp  
          || pt_var_type == eCDI_MNTR_PT_VAR::MinTemp);
}

static BOOLEAN var_is_force_or_moment(SRI_VARIABLE_TYPE fluid_var, SRI_VARIABLE_TYPE surface_var)
{
  if (fluid_var < 0 && surface_var < 0)
    msg_internal_error("Neither fluid_var nor surface_var is valid!");
  return sri_is_variable_force_or_moment((fluid_var >= 0)? fluid_var : surface_var);
}

static VOID make_monitors_dir(cSTRING monitors_dir_name)
{
  // make a monitors directory if it does not exist 
  struct stat buf;
  if (platform_file_present(monitors_dir_name)) {
    if (-1 == stat(monitors_dir_name,&buf)) {
      msg_error("Error while creating \"%s\" subdirectory: %s", monitors_dir_name, strerror(errno));
    }
    if (!S_ISDIR(buf.st_mode)) {
      msg_error("Cannot create directory \"%s\" for monitors. A file by that name already exists.",monitors_dir_name);
    }
    // The directory exists, delete all the signal data files
    CHARACTER fullcmd[16 * 1024];
    sprintf(fullcmd, "rm -f %s/*.dat", monitors_dir_name);
    system_cmd(fullcmd);

  } else {
    if (!platform_createdir(monitors_dir_name)) {
      msg_error("Unable to create \"%s\" subdirectory for monitors.", monitors_dir_name);
    }
  } 
}


typedef class sMONITOR {
  public:
    sMONITOR(cSTRING name,
             bool analyze_signal, 
             bool analyze_convergence, 
             bool end_sim_via_init_transient,
             bool end_sim_if_converged,
             cMONITOR_SIGNAL_ANALYZER_PARAMS p) :
             m_analyze_signal(analyze_signal), 
             m_analyze_convergence(analyze_convergence), 
             m_msap(p),
             m_end_sim_if_converged(end_sim_if_converged),
             m_msa(p),
             m_end_sim_via_init_transient(end_sim_via_init_transient)
    {
      m_name = strsave(name);
      m_end_init_transient_reached = false;
      m_endInitialTransientTime = -1;
      m_endInitialTransientDetectionTime = -1;
      m_convergenceTime = -1;
 
      m_disabled_autostop_by_on_off = FALSE;
      m_enabled_autostop_by_on_off = FALSE;

      m_is_var_force_or_moment = false;

      // Initialize filenames of signal output files
      make_monitors_dir(monitors_dir_name);
      sprintf(m_signal_it_filename, "%s/%s.it.dat", monitors_dir_name, m_name);
      sprintf(m_signal_filename, "%s/%s.dat", monitors_dir_name, m_name);
      sprintf(m_signal_tmp_filename, "%s/%s.dat.tmp", monitors_dir_name, m_name);
 
      m_signal_it_fout = NULL;
      m_signal_tmp_fout = NULL;
      m_signal_fout = NULL;
      m_last_flushing_time = 0;
      
      m_use_var_diff_lattice_unit = false;
      m_use_var_diff_monitor_unit = false;

      m_header_written_p = false;

      m_converged_and_ended_sim_before_ckpt = FALSE;
    }
    
    cSTRING  m_name;
    eCDI_MNTR_TYPE::Enum m_type;    

    bool m_header_written_p;            // Tells if the monitor data file header has been written. 

    std::vector<sFLOAT>   m_signal;     // Used for chained monitors, i.e. monitors which are started by other monitors. 
                                        // Store the signals temporarily before the associated cdi meas window has been started.
    std::vector<TIMESTEP> m_timesteps;  // Timesteps when the signals are received and processed
    std::vector<TIMESTEP> m_next_timesteps;  // Timestep to process the next signal

    CDI_MEAS_WINDOW m_cdi_meas_win;     // The CDI meas window of the CP meas window being monitored. 
                                        // Used by flow and heat exchanger monitors.
    CDI_CMDL        m_cmdl;             // PowerTHERM monitors need this to get the coupling phase descs.
    TIMESTEP m_period;    // Period of the monitor signal. For flow monitors, it is the cdi meas
                          // window period; For heat exchanger monitors, it is the PowerCOOL(AmeSIM)
                          // coupling period; For PowerTHERM monitors, it is the period of the first
                          // coupling phase.

    TIMESTEP m_interval;  // Measurement interval for flow monitors.
    TIMESTEP m_end_of_first_meas_frame;


    TIMESTEP m_classic_signal_processing_interval; // For classic autostop, this is the number for measurement frames between each 
                                                   // monitor status check.

    // Open the signal file in the beginning and keep it open for appending data.
    // Always open and close the log file when writing data since it is shared by many monitors. 
    FILE*   m_signal_it_fout;            // Monitor signal it (initial transient) file 
    FILE*   m_signal_tmp_fout;           // Monitor signal tmp file 
    FILE*   m_signal_fout;               // Monitor signal file 
    asINT32 m_end_init_transient_it_pos; // Position of "end_init_transient" in the .it.dat file
    asINT32 m_end_init_transient_detection_it_pos; // Position of "end_init_transient_detection" in the .it.dat file

    asINT32 m_monitor_log_file_pos; // Position of this monitor status in the log file 
    std::vector<asINT64> m_signal_file_pos;  // Position of each signal in the output file

    cMONITOR_SIGNAL_ANALYZER_PARAMS m_msap;
    cMONITOR_SIGNAL_ANALYZER m_msa;
    
    cSTRING m_var_unit_class_name;
    UNITS_UNIT m_var_unit;                  // Unit of the raw variable
    UNITS_UNIT m_var_lattice_unit;          // Lattice unit of the variable
    UNITS_UNIT m_var_diff_lattice_unit;     // Lattice unit of the diff variable (e.g. DynamicsPressure for pressure)
    bool       m_use_var_diff_lattice_unit; // true for pressure as DynamicPressure is used in cdi for var difference
    UNITS_UNIT m_var_monitor_unit;          // Unit used by monitor: mks_degC for all variables
    UNITS_UNIT m_var_diff_monitor_unit;     // Unit used by monitor for var difference (e.g. degK for temperature difference) 
    bool       m_use_var_diff_monitor_unit; // true for temperature as degK should be used for var difference
                                            // except forces and moments for which we use dimless units
    bool  m_is_var_force_or_moment;
    
    bool m_analyze_signal;           // If True, analyze signal initial transient and 
                                        // possibly signal convergence
    bool m_analyze_convergence;      // If True, analyze signal convergence

    bool m_end_init_transient_reached; // If the EIT is user specified, it becomes true after the user specific EIT.
                                       // If the EIT is determined automatically, it becomes true after EIT is detected.

    // The monitor stores real timesteps while the monitor analyzer msa stores the frame #
    TIMESTEP m_endInitialTransientTime;
    TIMESTEP m_endInitialTransientDetectionTime;
    TIMESTEP m_convergenceTime;

    bool m_end_sim_via_init_transient; // If the monitor is used to find the end of whole
                                          // simulation initial transient (further used to 
                                          // find the sim end time).
    
    cBOOLEAN m_disabled_autostop_by_on_off;   // Monitor autostop turned off by exaqsub --autostop_off option.
    cBOOLEAN m_enabled_autostop_by_on_off;   // Monitor autostop turned on by exaqsub --autostop_on option.
 
    bool m_end_sim_if_converged;   // If the sim should stop if this monitor is converged.
                                      // If multiple monitors have this option enabled, we
                                      // should stop the simulator if all monitors are converged.
    bool m_is_moving_avg;          // If using moving avg or cumulative running avg
    asINT32 m_running_avg_size;    // For moving avg, it is smaller than the signal size
                                   // For cumulative running avg, it equals to the signal size

    BOOLEAN m_converged_and_ended_sim_before_ckpt;  // If the simulation is ended by converging monitors, and a ckpt is written in the end, then 
                                                    // this monitor will not be used to end the simulation when resuming from ckpt. It will also 
                                                    // not be used later if another ckpt is written. This information will be carried on in all
                                                    // future ckpt files once it becomes true;

    WALLCLOCK_TIME_SECS m_last_flushing_time;

    VOID maybe_update_desired_accuracy();
    VOID maybe_update_classic_autostop_parameters();
    template <class FLOAT_TYPE>
    VOID maybe_scale_vehicle_moments(FLOAT_TYPE& moment_value);
    template <class FLOAT_TYPE>
    VOID maybe_scale_vehicle_moments_units(FLOAT_TYPE& scale_factor);

    VOID append_and_analyze_signal(sFLOAT signal, TIMESTEP signal_timestep, TIMESTEP next_signal_timestep,
                       BOOLEAN write_monitor_data_file_p = TRUE, BOOLEAN convert_signal_unit_p = TRUE,
                       BOOLEAN restoring_from_ckpt_p = FALSE);
    VOID compute_init_transient() { m_msa.ComputeInitialTransient(); }
    VOID compute_convergence() { m_msa.ComputeSignalConvergence(); }
    // Update monitor initial transient end and convergence status
    VOID update_status(BOOLEAN write_monitor_data_file_p, BOOLEAN restoring_from_ckpt_p);

    VOID maybe_start_meas_windows_via_monitor();
    VOID maybe_start_emitters();
    VOID maybe_start_wipers();
    VOID maybe_find_sim_stoptime_via_monitor();

    // List of meas windows controlled by this monitor
    std::vector<CDI_MEAS_WINDOW> cdi_meas_windows_to_start;
    std::vector<EMITTER> emitters_to_start;
    std::vector<WIPER> wipers_to_start;

    CHARACTER m_signal_it_filename[PLATFORM_MAXPATHLEN];  // <monitor_name>.it.dat: only contain signals and timesteps
    CHARACTER m_signal_filename[PLATFORM_MAXPATHLEN];     // <monitor_name>.dat: contain the full data set including running_average, is_signal_converged and percent_complete.
    CHARACTER m_signal_tmp_filename[PLATFORM_MAXPATHLEN];     // <monitor_name>.dat.tmp: temporary .dat file

    TIMESTEP find_next_signal_timestep(TIMESTEP signal_timestep); // Find the next signal timestep using the signal timestep
    VOID read_monitor_signals_from_file(TIMESTEP last_timestep); // Read signals up to last_timestep from the monitor data file

    VOID write_signal_file_header(BOOLEAN is_it_file, BOOLEAN is_moving_avg=TRUE);
    VOID write_monitor_signal_to_file(BOOLEAN is_it_file, 
                                      BOOLEAN is_tmp_file,
                                      BOOLEAN is_running_avg_available,
                                      asINT32 signal_index);
    bool is_flow_or_solid_monitor() { return m_type == eCDI_MNTR_TYPE::Flow || m_type == eCDI_MNTR_TYPE::Solid; }
    // The signal time is the end of measurement frame. For consistency, we need 
    // to write the center of the frame in <monitor_name>.dat file for flow and solid monitors,
    // and write the signal time for PowerTHERM and heat exchanger monitors.
    dFLOAT get_frame_time_from_signal_time(TIMESTEP time) {
      if (time <= 0)
        return (sFLOAT)time;
      return (is_flow_or_solid_monitor()? (time - m_interval/2.0) : time);          
    }

    TIMESTEP get_signal_time_from_frame_time(sFLOAT time) {
      if (time <= 0)
        return (TIMESTEP)time;
      return (is_flow_or_solid_monitor()? (time + m_interval/2.0) : time);          
    }

} *MONITOR;

typedef class sFLOW_MONITOR : public sMONITOR {
  public:
    sFLOW_MONITOR(cSTRING name,
                  bool analyze_signal, 
                  bool analyze_convergence, 
                  bool end_sim_via_init_transient,
                  bool end_sim_if_converged,
                  SRI_VARIABLE_TYPE flow_var_type,
                  eCDI_MNTR_FLOW_VAR_SRC::Enum var_source, 
                  bool use_wheelbase_for_dimless_vehicle_moments,
                  asINT32 cdi_meas_window_index, 
                  cMONITOR_SIGNAL_ANALYZER_PARAMS p
                  );
    
    SRI_VARIABLE_TYPE m_fluid_var_type; // Also used for porous(fan) var type
    SRI_VARIABLE_TYPE m_surface_var_type;
    SRI_VARIABLE_TYPE m_uds_var_type;   // Used for user-defined scalar variables

    eCDI_MNTR_FLOW_VAR_SRC::Enum m_var_source;

    asINT32 m_cdi_meas_window_index;
    sriBYTE  m_n_cp_meas_windows_written; // Used to tell if the two cp meas windows data 
                                          // are ready for SURFACE+FAN(POROUS) data source
    sriFLOAT m_porous_data;
    sriFLOAT m_surface_data;

    // For all meas points
    std::vector<bool> m_is_meas_surfel_in_monitor; // For surface measurement
    std::vector<bool> m_is_meas_cell_in_monitor;   // For fluid or porous measurement

    asINT32 m_csys_index;     // User specified csys index
    sriFLOAT m_ref_point[3];  // User specified reference point for moment type 
                              // variables; It is w.r.t. lattice csys
    bool m_use_wheelbase_for_dimless_vehicle_moments;
} *FLOW_MONITOR;

typedef class sSOLID_MONITOR : public sMONITOR {
  public:
    sSOLID_MONITOR(cSTRING name,
                  bool analyze_signal, 
                  bool analyze_convergence, 
                  bool end_sim_via_init_transient,
                  bool end_sim_if_converged,
                  SRI_VARIABLE_TYPE solid_var_type,
                  eCDI_MNTR_FLOW_VAR_SRC::Enum var_source, 
                  asINT32 cdi_meas_window_index, 
                  cMONITOR_SIGNAL_ANALYZER_PARAMS p
                  );
    
    SRI_VARIABLE_TYPE m_solid_var_type; 
    SRI_VARIABLE_TYPE m_shell_var_type;
    
    eCDI_MNTR_FLOW_VAR_SRC::Enum m_var_source;

    asINT32 m_cdi_meas_window_index;
    sriBYTE  m_n_cp_meas_windows_written; // Used to tell if the two cp meas windows data 
                                          // are ready for SURFACE+VOLUME(SOLID) data source
    sriFLOAT m_solid_data;
    sriFLOAT m_shell_data;

    // For all meas points
    std::vector<bool> m_is_meas_surfel_in_monitor; // For shell measurement
    std::vector<bool> m_is_meas_cell_in_monitor;   // For solid measurement

    asINT32 m_csys_index;     // User specified csys index
    sriFLOAT m_ref_point[3];  // User specified reference point for moment type 
                              // variables; It is w.r.t. lattice csys
} *SOLID_MONITOR;

typedef class sHX_MONITOR : public sMONITOR {
  public:
    sHX_MONITOR(cSTRING name,
                bool analyze_signal,
                bool analyze_convergence,
                bool end_sim_via_init_transient,
                bool end_sim_if_converged,
                eCDI_MNTR_HX_VAR::Enum hx_var_type, 
                asINT32 hx_index, 
                cMONITOR_SIGNAL_ANALYZER_PARAMS p
                ); 
    eCDI_MNTR_HX_VAR::Enum m_hx_var_type;
    asINT32 m_hx_index;
} *HX_MONITOR;

typedef class sPOWERTHERM_MONITOR : public sMONITOR {
  public:
    sPOWERTHERM_MONITOR(cSTRING name,
                        bool analyze_signal,
                        bool analyze_convergence,
                        bool end_sim_via_init_transient,
                        bool end_sim_if_converged,
                        asINT32 pt_model_index, 
                        cSTRING pt_part, 
                        eCDI_MNTR_PT_SIDE::Enum surface_sides, 
                        eCDI_MNTR_PT_VAR::Enum pt_var_type, 
                        cMONITOR_SIGNAL_ANALYZER_PARAMS p
                        );
    asINT32 m_powertherm_model_index;
    std::string m_powertherm_part;
    eCDI_MNTR_PT_SIDE::Enum m_surface_sides;
    eCDI_MNTR_PT_VAR::Enum m_powertherm_var_type;
} *POWERTHERM_MONITOR;


VOID maybe_end_sim_by_monitors(TIMESTEP current_timestep);

#endif /* __MONITOR_H */


