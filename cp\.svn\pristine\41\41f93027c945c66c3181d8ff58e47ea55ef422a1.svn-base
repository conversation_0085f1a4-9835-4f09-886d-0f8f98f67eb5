/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Communication tree
 *
 * Samuel Watson, Exa Corporation
 * Created June 2012
 *--------------------------------------------------------------------------*/

#include "sim_tree.h"

// A parent node calls makenode for each of its child nodes with the size of 
// that child's subtree, which includes itself and its descendants. 
// After subtracting 1 for itself, a child node partitions its descendants
// into subtrees, where the number of subtrees is the lesser of the degree of the tree
// and the number of remaining descendants. It assigns the highest-numbered child in 
// each subtree as parent of that subtree, and calls makenode recursively. 

// The partitioning scheme attempts to achieve balance by ensuring that the sizes of
// subtrees within a partition do not differ by more than 1, and that there is
// no bias towards one side of the tree.

// Partition of integer i into n parts of sizes sa and sb, where sa = sb + 1:

// n = a + b
// sa = sb + 1
// i = a * sa + b * sb
// sa = ceiling(i/n)
// a = i - n * (sa - 1)
// b = n * sa - i

asINT32 partition(asINT32  i, asINT32 n, asINT32 &a, asINT32 &b) {
  asINT32 sa = ((i % n == 0) ? i / n : (i / n) + 1); // ceiling of i/n
  b = n * sa - i;
  a = n - b;
  return sa;
}

// make_subtree builds a subtree recursively when called with a node index, a subtree
// size, and degree, and a pointer to an array of sTREE_NODE structures. It then fill in
// the node structure for each subnode. In practise it only needs to be called once per tree, 
// with the root node.


VOID make_subtree(asINT32 node, asINT32 size, asINT32 degree, sTREE_NODE *tree) {
    asINT32 n_descendants = size - 1;
    asINT32 n_children = (n_descendants <= degree) ? n_descendants : degree;
    tree[node].n_children = n_children; 
    tree[node].n_descendants = n_descendants;
    if(n_children > 0) {
        int a, b;
        int sa = partition(n_descendants, n_children, a, b);
        int sb = sa - 1;

        // We now have the sizes of the subtrees; there are a subtrees of size
        // sa and b subtrees of size sb. To keep the tree balanced these need to be interspersed
        // as regularly as possible. This is done by determining the larger of the
        // two counts a and b (designated hi) and dividing it into segments, where the number of segments
        // is the lower count (designated lo) + 1. The length of these segments are called strides;
        // there are at most 2.

        int hi, lo;   
        int subsize[2]; 
        int stride[2];

        if(a >= b) {
          hi = a;
          subsize[1] = sa;
          lo = b;
          subsize[0] = sb;
        } else {
          hi = b;
          subsize[1] = sb;
          lo = a;
          subsize[0] = sa;
        }
        if(lo == 0) {
          stride[0] = n_children;
          stride[1] = n_children;
        } else if(hi == lo) {
          stride[0] = 1;
          stride[1] = 1;
        } else {
          stride[0] = hi / (lo + 1);                                               // floor(hi/(lo + 1)
          stride[1] = (hi % (lo + 1) == 0) ? hi / (lo + 1) : (hi / (lo + 1) + 1); // ceiling(hi/(lo + 1)
        }

        asINT32 hilo = node & 0x1; // bias up for odd nodes & down for even ones
        asINT32 remaining_children = n_children;
        asINT32 child = node - size;


        asINT32 sum = 0;
        asINT32 child_stree_index = 0;
        while(remaining_children > 0) {
          asINT32 pchild;
          ccDOTIMES(s,stride[hilo]) {
            pchild = subsize[1];
            sum += pchild;
            child += pchild;
            tree[node].children[child_stree_index] = child;
            tree[child].parent_node = node;
            make_subtree(child, pchild, degree, tree);
            child_stree_index++;
            remaining_children--;
            if(remaining_children == 0 && s < (stride[hilo] - 1)) {
              break;
            }
          }
          if(remaining_children == 0) {
            break;
          }  
            
          hilo ^= 0x1;
          pchild = subsize[0];
          sum += pchild;
          child += pchild;
          tree[node].children[child_stree_index] = child;
          tree[child].parent_node = node;
          make_subtree(child, pchild, degree, tree);
          child_stree_index++;
          remaining_children--;
          if(remaining_children == 0) {
            break;
          } 
        }
        if(sum != n_descendants) {
          msg_internal_error("makenode: the number of descendants %d should be %d",
                                                    sum, n_descendants);
        }
    }
}


VOID print_tree_array(asINT32 nodes, sTREE_NODE *tree) {
  ccDO_FROM_DOWNTO(node, nodes - 1, 0) {
    msg_print_nnl("tree[%2d]: parent = %3d, n_descendants = %2d, n_children = %2d",
              node,tree[node].parent_node,tree[node].n_descendants,tree[node].n_children);
    if(tree[node].n_children > 0) {
      printf(", children =  ");
      ccDOTIMES(child,tree[node].n_children) {
        printf("%2d ",tree[node].children[child]);
      }
    }
    printf("\n");
  }
}
    

VOID print_subtree(asINT32 node, asINT32 indent,  sTREE_NODE *tree)
{
  
  asINT32 n_children = tree[node].n_children;

  ccDOTIMES(i,indent) {
    printf(" ");
  }

  if(n_children == 0) printf("node %d -- no children",node); 
  else printf("node %d -- children",node);
  ccDOTIMES(i,n_children) {
    printf(" %d",tree[node].children[i]);
  }
  printf("\n");
  asINT32 child = n_children - 1;
  while(child >= 0) {
    print_subtree(tree[node].children[child], indent + 2, tree);
    child--;
  }
}
