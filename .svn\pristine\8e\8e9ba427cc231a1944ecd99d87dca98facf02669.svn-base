/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#include "cdi_partitions.h"
#include "lexer.h"
#include "cdi_fix_parallel_dev.h"

#include<algorithm>
#include<iomanip>
#include<stack>
#include<iterator>

namespace {
const std::string FACE_DELIMITER = "::";
const std::string OLD_FACE_DELIMITER = ".";
}

static
std::string
TrimWhitespace(const std::string &s)
{
  if (s.empty()) return s;
  size_t left = 0, right = s.size() - 1;
  while (left < s.size() && isspace(s[left])) ++left;
  while (right > left && isspace(s[right])) --right;
  return s.substr(left, right - left + 1);
}

static
std::vector<std::string>
SplitAndTrim(const std::string &s, CHARACTER c)
{
  BOOLEAN trim = TRUE;
  BOOLEAN ignoreEscaped = FALSE;

  std::vector<std::string> rv;

  size_t start = 0;
  size_t loc = s.find(c);
  while (loc != std::string::npos) {
    BOOLEAN isEscaped = (loc > 0 && s[loc - 1] == '\\');
    if (isEscaped && ignoreEscaped) {
      loc = s.find(c, loc + 1);
      continue;
    }

    if (start == loc) {
      if (!trim)
        rv.push_back("");
    }
    else {
      std::string sub = s.substr(start, loc - start);
      if (trim) {
        sub = TrimWhitespace(sub);
        if (!sub.empty())
          rv.push_back(sub);
      }
      else
        rv.push_back(sub);
    }

    start = loc + 1;
    loc = s.find(c, start);
  }

  // Pick up last section.
  std::string sub = s.substr(start, s.size() - start);
  if (trim) {
    sub = TrimWhitespace(sub);
    if (!sub.empty())
      rv.push_back(sub);
  }
  else
    rv.push_back(sub);

  return rv;
}

// Printing helpers
static void
PrintIndent(std::ostream &output, int depth)
{
  output << std::string(2 * depth, ' ');
}

static void
PrintElementStart(std::ostream &output, bool newLine, bool leadSpace)
{
  if (leadSpace) output << " ";
  output << "{";
  if (newLine) output << "\n";
}

static void
PrintElementStart(std::ostream &output, const std::string& elementTag, bool newLine, int depth)
{
  PrintIndent(output, depth);
  output << elementTag;
  PrintElementStart(output, newLine, true);
}

static void
PrintElementEnd(std::ostream &output, bool newLine)
{
  output << "}";
  if (newLine) output << "\n";
}

static void
PrintElementEnd(std::ostream &output, bool newLine, int depth)
{
  PrintIndent(output, depth);
  PrintElementEnd(output, newLine);
}

static void
PrintNameWithID(std::ostream &output, const std::string &name, int depth)
{
  char nameType_str[10];
  cio_type_to_string(CDI_CHUNK_TYPE_NAME, nameType_str);
  PrintElementStart(output, nameType_str, false, depth);
  output << " \"" << name << "\" ";
  PrintElementEnd(output, true);
}

static void
ReadNameWithID(std::string* name)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_NAME);
  lexer_parse_lbrace();
  *name = lexer_parse_std_string();
  lexer_parse_rbrace();
}

static void 
PrintAnnotatedInt(std::ostream &output, cdiINT32 value,
                  const::std::string& paramDescription, int depth)
{
  char intType_str[10];
  cio_type_to_string(CDI_CHUNK_TYPE_INT_, intType_str);
  PrintElementStart(output, intType_str, false, depth);
  output << " " << value << " ";
  PrintElementEnd(output, false);
  if (!paramDescription.empty())
    output << " ( " << paramDescription << " )";
  output << std::endl;
}

static void
ReadAnnotatedInt(sINT32* value)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_INT_);
  lexer_parse_lbrace();
  *value = lexer_parse_int();
  lexer_parse_rbrace();
}

static void
PrintAnnotatedEnum(std::ostream &output, cdiINT32 value,
  const::std::string& paramDescription, const::std::string& valueDescription, int depth)
{
  char enumType_str[10];
  cio_type_to_string(CDI_CHUNK_TYPE_ENUM, enumType_str);
  PrintElementStart(output, enumType_str, false, depth);
  output << " " << value << " ";
  PrintElementEnd(output, false);
  if (!paramDescription.empty()) {
    if (!valueDescription.empty())
      output << " ( " << paramDescription << ": " << valueDescription << " )";
    else
      output << " ( " << paramDescription << " )";
  }
  output << std::endl;
}

static void
ReadAnnotatedEnum(sINT32* value)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_ENUM);
  lexer_parse_lbrace();
  *value = lexer_parse_int();
  lexer_parse_rbrace();
}

static void
PrintAnnotatedRgns(std::ostream &output, const std::vector<cdiINT32>& rgnVec, int depth)
{
  char rgnsType_str[10];
  cio_type_to_string(CDI_CHUNK_TYPE_RGNS, rgnsType_str);
  PrintElementStart(output, rgnsType_str, false, depth);
  output << " ";
  output << std::setw(5) << rgnVec.size() << "     (number of regions)\n";
  for (size_t i = 0; i < rgnVec.size(); i++)
  {
    PrintIndent(output, depth + 1);
    output << std::string(5, ' ');
    output << std::setw(5) << rgnVec[i] << " (region " << i << ")\n";
  }
  PrintElementEnd(output, true, depth);
}

static void
ReadAnnotatedRgns(std::vector<cdiINT32>& regionsVec)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_RGNS);
  lexer_parse_lbrace();
  cdiINT32 n_region = lexer_parse_int();
  
  DOTIMES(i, n_region, {
    regionsVec.push_back(lexer_parse_int());
  });

  lexer_parse_rbrace();
}

static void
PrintAnnotatedFlst(std::ostream &output, const std::vector<cdiINT32> &values, int depth)
{
  char flstType_str[10];
  cio_type_to_string(CDI_CHUNK_TYPE_FLST, flstType_str);
  PrintElementStart(output, flstType_str, false, depth);
  output << " ";
  output << std::setw(5) << values.size() << "     (number of faces)\n";
  for (size_t i = 0; i < values.size(); i++)
  {
    PrintIndent(output, depth + 1);
    output << std::string(5, ' ');
    output << std::setw(5) << values[i] << " (face " << i << ")\n";
  }
  PrintElementEnd(output, true, depth);
}

static void
ReadAnnotatedFlst(std::vector<cdiINT32> &values)
{
  lexer_parse_specific_id(CDI_CHUNK_TYPE_FLST);
  lexer_parse_lbrace();

  cdiINT32 n_face = lexer_parse_int();
  DOTIMES(i, n_face, {
    values.push_back(lexer_parse_int());
  });

  lexer_parse_rbrace();
}

static void
PrintAnnotatedSegmentList(std::ostream &output, const std::vector<cCDI_SEGMENT_REF>& segRefVec, int depth) {
  char seglType_str[10];
  cio_type_to_string(CDI_CHUNK_TYPE_SEGL, seglType_str);
  PrintElementStart(output, seglType_str, true, depth);
  depth++;
  for (cCDI_SEGMENT_REF sgrf : segRefVec) {
    char sgrfType_str[10];
    cio_type_to_string(CDI_CHUNK_TYPE_SGRF, sgrfType_str);
    PrintIndent(output, depth);
    output << sgrfType_str << " ";
    sgrf.Dump(output);
  }
  depth--;
  PrintElementEnd(output, true, depth);  // end of (segl)
}

static void
ReadAnnotatedSegmentList(std::vector<cCDI_SEGMENT_REF>& segRefVec) {
  lexer_parse_specific_id(CDI_CHUNK_TYPE_SEGL);
  lexer_parse_lbrace();
  char cccc_string[5];
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4) &&
    cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_SGRF)
  {
    cCDI_SEGMENT_REF segref(-1, -1);
    segref.Undump();
    segRefVec.push_back(segref);
  }
}

static void
PrintAnnotatedPartialPartList(std::ostream &output, const std::vector<cCDI_PARTIAL_PART_REF>& ppRefVec, int depth) {
  char pprlType_str[10];
  cio_type_to_string(CDI_CHUNK_TYPE_PPRL, pprlType_str);
  PrintElementStart(output, pprlType_str, true, depth);
  depth++;
  for (cCDI_PARTIAL_PART_REF pprf : ppRefVec) {
    char pprfType_str[10];
    cio_type_to_string(CDI_CHUNK_TYPE_PPRF, pprfType_str);
    PrintIndent(output, depth);
    output << pprfType_str << " ";
    pprf.Dump(output);
  }
  depth--;
  PrintElementEnd(output, true, depth);  // end of (pprl)
}

static void
ReadAnnotatedPartialPartList(std::vector<cCDI_PARTIAL_PART_REF>& ppRefVec) {
  lexer_parse_specific_id(CDI_CHUNK_TYPE_PPRL);
  lexer_parse_lbrace();
  char cccc_string[5];
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4) &&
    cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_PPRF)
  {
    cCDI_PARTIAL_PART_REF ppref(-1, -1);
    ppref.Undump();
    ppRefVec.push_back(ppref);
  }
}

sSEGMENT_ASSIGNMENT
cCDI_ENTITY::GetSegmentAssignment(const cCDI_PARTITION& derivedPartition, sSEGMENT_ASSIGNMENT* parentAssign) const
{
  sSEGMENT_ASSIGNMENT returnAssign;
  returnAssign.type = sSEGMENT_ASSIGNMENT::SameAsParent;
  returnAssign.segment = NULL;
  for (cCDI_SEGMENT_REF segRef : m_segmentAssignments) {
    cCDI_PARTITION* refPartition = derivedPartition.GetOwner().GetPartition(segRef.m_partitionIndex);
    if (refPartition == &derivedPartition) {
      if (segRef.m_segmentIndex < 0) {
        returnAssign.type = sSEGMENT_ASSIGNMENT::Excluded;
      }
      else {
        returnAssign.type = sSEGMENT_ASSIGNMENT::Assigned;
        returnAssign.segment = refPartition->GetSegment(segRef.m_segmentIndex);
      }
      break;
    }
  }
  if (returnAssign.type == sSEGMENT_ASSIGNMENT::SameAsParent) {
    if (parentAssign) {
      returnAssign.segment = parentAssign->segment;
    }
  }
  return returnAssign;
}

void
cCDI_ENTITY::RemoveSegmentAssignment(cdiINT32 partitionIndex)
{
  auto it = std::find_if(m_segmentAssignments.begin(), m_segmentAssignments.end(),
    [&](const cCDI_SEGMENT_REF& segRef) { return segRef.m_partitionIndex == partitionIndex; });
  if (it != m_segmentAssignments.end())
    m_segmentAssignments.erase(it);
}

cCDI_SEGMENT::cCDI_SEGMENT(std::string& name, cCDI_PARTITION* partition, cCDI_SEGMENT* parentSegment)
  : m_name(name),
    m_partition(partition)
{
  m_parentSegmentIndex = parentSegment ? m_partition->GetSegmentIndex(*parentSegment) : -1;
}

cCDI_SEGMENT::~cCDI_SEGMENT()
{
  m_partition = NULL;
}

void
cCDI_SEGMENT::SetParentSegmentIndex(cdiINT32 parentSegmentIndex)
{
  m_parentSegmentIndex = parentSegmentIndex;
  if (m_parentSegmentIndex >= 0)
    GetParentSegment()->AddChildSegment(this);
}

cCDI_SEGMENT*
cCDI_SEGMENT::GetParentSegment() const
{
  cCDI_SEGMENT* parentSeg = NULL;
  if (m_parentSegmentIndex >= 0)
    parentSeg = m_partition->GetSegment(m_parentSegmentIndex);
  return parentSeg;
}

const std::string
cCDI_SEGMENT::GetName(bool includePath) const
{
  if (!includePath)
    return m_name;

  std::string retPathName = "";
  const cCDI_SEGMENT* parent = this;
  while (parent) {
    std::string segName = "/";
    if (!parent->IsRoot())
      segName = parent->GetName(false) + "/";
    retPathName.insert(0, segName);
    parent = parent->GetParentSegment();
  }
  return retPathName;
}

bool
cCDI_SEGMENT::GetQualifiedPartName(cdiINT32 partIndex, bool includePath, std::string* partName) const
{
  partName->clear();
  std::vector<cdiINT32> segParts = GetRegionIndices(false);
  if (std::find(segParts.begin(), segParts.end(), partIndex) == segParts.end()) {
    // It could be a partial part
    if (!FindPartialPart(partIndex))
      return false;
  }

  const cCDI_PARTITIONS& partitions = m_partition->GetOwner();
  std::string shortPartName = partitions.GetShortPartName(partIndex);
  if (!m_partition->IsBaseAssembly()) {
    for (cdiINT32 iPart : segParts) {
      if (iPart != partIndex && shortPartName == partitions.GetShortPartName(iPart)) {
        shortPartName = partitions.GetPartName(partIndex);
#if 0
        std::string basePath = partitions.GetPartName(partIndex);

        // Replace slashes with colons
        if (basePath.front() == '/')
          basePath.erase(0, 1);
        size_t lookHere = 0;
        size_t foundHere;

        std::string from = "/";
         std::string to = "::";
        while ((foundHere = basePath.find(from, lookHere)) != std::string::npos) {
          basePath.replace(foundHere, from.size(), to);
          lookHere = foundHere + to.size();
        }

        shortPartName = basePath;
#endif
        break;
      }
    }
  }
  if (!includePath)
    *partName = shortPartName;
  else
    *partName = m_partition->GetName() + GetName(true) + shortPartName;
  
  return true;
}

bool
cCDI_SEGMENT::GetQualifiedFaceName(cdiINT32 faceIndex, bool includePath, std::string* faceName) const
{
  faceName->clear();
  std::vector<cdiINT32> segFaces = GetFaceIndices(false);
  if (std::find(segFaces.begin(), segFaces.end(), faceIndex) == segFaces.end())
    return false;

  const cCDI_PARTITIONS& partitions = m_partition->GetOwner();
  const cCDI_FACE_ENTITY& faceEntity = partitions.GetFaceEntity(faceIndex);
  std::string qualPartName;
  GetQualifiedPartName(faceEntity.GetPartIndex(), includePath, &qualPartName);
  *faceName = qualPartName + FACE_DELIMITER + faceEntity.GetName();
  
  return true;
}

cCDI_SEGMENT&
cCDI_SEGMENT::CreateChildSegment(std::string& name)
{
  std::unique_ptr<cCDI_SEGMENT> newSegment(new cCDI_SEGMENT(name, m_partition, this));
  cCDI_SEGMENT* returnSegment = newSegment.get();
  m_childSegments.push_back(returnSegment);
  // Give "ownership" to the partition
  m_partition->AddSegment(std::move(newSegment));
  return *returnSegment;
}

cCDI_PARTIAL_PART&
cCDI_SEGMENT::CreatePartialPart(cdiINT32 partIndex)
{
  std::unique_ptr<cCDI_PARTIAL_PART> newPartialPart(new cCDI_PARTIAL_PART(partIndex, this));
  cCDI_PARTIAL_PART* returnPart = newPartialPart.get();
  m_partialParts.push_back(returnPart);
  // Give "ownership" to the partition
  m_partition->AddPartialPart(std::move(newPartialPart));
  return *returnPart;
}

cCDI_PARTIAL_PART*
cCDI_SEGMENT::FindPartialPart(cdiINT32 partIndex) const
{
  cCDI_PARTIAL_PART* retPartialPart = NULL;
  for (cCDI_PARTIAL_PART* partialPart : m_partialParts) {
    if (partialPart->GetPartIndex() == partIndex) {
      retPartialPart = partialPart;
      break;
    }
  }
  return retPartialPart;
}

std::vector<cdiINT32>
cCDI_SEGMENT::GetRegionIndices(bool recursive) const
{
  const cCDI_PARTITIONS& partitions = GetPartition().GetOwner();

  // Segments do not "volunteer" the presence of heat exchanger sub-regions.  (However,
  // elsewhere in the interface, if the client has an index that is a sub-region, then the API
  // will answer all queries related to that index as it would any other region.)
  std::vector<cdiINT32> returnVec;
  std::copy_if(m_assignedRegionIndices.begin(),
               m_assignedRegionIndices.end(),
               std::back_inserter(returnVec),
               [&partitions](cdiINT32 regionIndex) { return !partitions.IsHexSubRegion(regionIndex); });
  std::copy_if(m_inheritedRegionIndices.begin(),
               m_inheritedRegionIndices.end(),
               std::back_inserter(returnVec),
               [&partitions](cdiINT32 regionIndex) { return !partitions.IsHexSubRegion(regionIndex); });
  if (recursive) {
    for (cCDI_SEGMENT* childSeg : m_childSegments) {
      std::vector<cdiINT32> childRegions = childSeg->GetRegionIndices(recursive);
      returnVec.insert(returnVec.end(), childRegions.begin(), childRegions.end());
    }
  }
  return returnVec;
}

std::vector<cdiINT32>
cCDI_SEGMENT::GetFaceIndices(bool recursive) const
{
  std::vector<cdiINT32> returnVec = GetAssignedFaceIndices();
  std::vector<cdiINT32> inheritedFaces = GetInheritedFaceIndices();
  returnVec.insert(returnVec.end(), inheritedFaces.begin(), inheritedFaces.end());
  if (recursive) {
    for (cCDI_SEGMENT* childSeg : m_childSegments) {
      std::vector<cdiINT32> childFaces = childSeg->GetFaceIndices(recursive);
      returnVec.insert(returnVec.end(), childFaces.begin(), childFaces.end());
    }
  }
  return returnVec;
}

std::vector<cdiINT32>
cCDI_SEGMENT::GetAssignedFaceIndices() const
{
  std::vector<cdiINT32> returnVec;
  for (cCDI_PARTIAL_PART* partialPart : m_partialParts) {
    std::vector<cdiINT32> partFaces = partialPart->GetAssignedFaceIndices();
    returnVec.insert(returnVec.end(), partFaces.begin(), partFaces.end());
  }
  return returnVec;
}

std::vector<cdiINT32>
cCDI_SEGMENT::GetInheritedFaceIndices() const
{
  std::vector<cdiINT32> returnVec;
  std::vector<cdiINT32> partIndices = GetRegionIndices(false);
  for (cdiINT32 partIndex : partIndices) {
    std::vector<cdiINT32> faceList = m_partition->GetOwner().GetPartFaceList(partIndex);
    returnVec.insert(returnVec.end(), faceList.begin(), faceList.end());
  }
  for (cCDI_PARTIAL_PART* partialPart : m_partialParts) {
    std::vector<cdiINT32> partFaces = partialPart->GetInheritedFaceIndices();
    returnVec.insert(returnVec.end(), partFaces.begin(), partFaces.end());
  }
  return returnVec;
}

sSEGMENT_ASSIGNMENT
cCDI_SEGMENT::GetSegmentAssignment(const cCDI_PARTITION& derivedPartition)
{
  sSEGMENT_ASSIGNMENT returnAssign;
  returnAssign.type = sSEGMENT_ASSIGNMENT::SameAsParent;
  returnAssign.segment = NULL;
  for (cCDI_SEGMENT_REF segRef : m_segmentAssignments) {
    cCDI_PARTITION* partition = m_partition->GetOwner().GetPartition(segRef.m_partitionIndex);
    if (partition == &derivedPartition) {
      if (segRef.m_segmentIndex < 0) {
        returnAssign.type = sSEGMENT_ASSIGNMENT::Excluded;
      }
      else {
        returnAssign.type = sSEGMENT_ASSIGNMENT::Assigned;
        returnAssign.segment = partition->GetSegment(segRef.m_segmentIndex);
      }
      break;
    }
  }
  if (returnAssign.type == sSEGMENT_ASSIGNMENT::SameAsParent) {
    cCDI_SEGMENT* segment = GetParentSegment();
    if (segment) {
      returnAssign.segment = segment->GetSegmentAssignment(derivedPartition).segment;
    }
  }
  return returnAssign;
}

void
cCDI_SEGMENT::AddSegmentAssignment(const cCDI_PARTITION& assignedPartition, cCDI_SEGMENT* assignedSegment)
{
  cdiINT32 ptnIndex = assignedPartition.GetOwner().GetPartitionIndex(assignedPartition);
  cdiINT32 segIndex = -1;
  if (assignedSegment)
    segIndex = assignedPartition.GetSegmentIndex(*assignedSegment);
  m_segmentAssignments.push_back(cCDI_SEGMENT_REF(ptnIndex, segIndex));
}

std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>>
cCDI_SEGMENT::GetRegionAssignments(const cCDI_PARTITION& derivedPartition)
{
  std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> returnVector;
  if (derivedPartition.GetParentPartition() == &GetPartition()) {
    std::vector<cdiINT32> segPartIndices = GetRegionIndices(false);
    sSEGMENT_ASSIGNMENT segAssign = GetSegmentAssignment(derivedPartition);
    for (cdiINT32 partIndex : segPartIndices) {
      cCDI_PART_ENTITY partEntity = GetPartition().GetOwner().GetPartEntity(partIndex);
      returnVector.push_back(std::make_pair(partIndex, partEntity.GetSegmentAssignment(derivedPartition, &segAssign)));
    }
  }
  return returnVector;
}

std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>>
cCDI_SEGMENT::GetFaceAssignments(const cCDI_PARTITION& derivedPartition)
{
  std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> returnVector;
  if (derivedPartition.GetParentPartition() == &GetPartition()) {
    // First do faces from parts.
    std::vector<cdiINT32> partIndices = GetRegionIndices(false);
    sSEGMENT_ASSIGNMENT segAssign = GetSegmentAssignment(derivedPartition);
    for (cdiINT32 partIndex : partIndices) {
      cCDI_PART_ENTITY partEntity = GetPartition().GetOwner().GetPartEntity(partIndex);
      sSEGMENT_ASSIGNMENT partAssign = partEntity.GetSegmentAssignment(derivedPartition, &segAssign);
      std::vector<cdiINT32> faceList = m_partition->GetOwner().GetPartFaceList(partIndex);
      for (cdiINT32 faceIndex : faceList) {
        cCDI_FACE_ENTITY faceEntity = GetPartition().GetOwner().GetFaceEntity(faceIndex);
        sSEGMENT_ASSIGNMENT faceAssign = faceEntity.GetSegmentAssignment(derivedPartition, &partAssign);
        returnVector.push_back(std::make_pair(faceIndex, faceAssign));
      }
    }
    // Do partial parts
    for (cCDI_PARTIAL_PART* partialPart : m_partialParts) {
      std::vector<cdiINT32> partFaces = partialPart->GetFaceIndices();
      sSEGMENT_ASSIGNMENT parentAssign = partialPart->GetSegmentAssignment(derivedPartition);
      for (cdiINT32 faceIndex : partFaces) {
        cCDI_FACE_ENTITY faceEntity = GetPartition().GetOwner().GetFaceEntity(faceIndex);
        sSEGMENT_ASSIGNMENT faceAssign = faceEntity.GetSegmentAssignment(derivedPartition, &parentAssign);
        returnVector.push_back(std::make_pair(faceIndex, faceAssign));
      }
    }
  }
  return returnVector;
}

std::list<const cCDI_SEGMENT*>
cCDI_SEGMENT::GetPath() const
{
  std::list<const cCDI_SEGMENT*> returnList;
  returnList.push_front(this);
  cCDI_SEGMENT* parentSegment = GetParentSegment();
  while (parentSegment) {
    returnList.push_front(parentSegment);
    parentSegment = parentSegment->GetParentSegment();
  }
  return returnList;
}

void
cCDI_SEGMENT::ReadFromOldCDI(CDI_INFO cdi_info)
{
  // This is for reading segments from pre-V8.0 CDIs
  asINT32 count = cio_get_count(cdi_info->cio_info);
  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);

    switch (cio_get_type(cdi_info->cio_info)) {
    case CDI_CHUNK_TYPE_NAME: {
      CDI_NAME name = cdi_read_name(cdi_info);
      m_name = name->name;
      cdi_destroy_name(name);
      break;
    }
    case CDI_CHUNK_TYPE_RGDP: {
       m_hasDisplayProperties = true;
       sCDI_RGDP rgdp;
       cdi_read_region_display_properties(cdi_info, &rgdp);
       m_displayProperties.color = rgdp.color;
       m_displayProperties.materialIndex = rgdp.material_index;
       m_displayProperties.displayMode = rgdp.display_mode;
       //cdi_destroy_rgdp(rgdp);
       break;
    }

    default:
      break;
    }

    cio_ascend(cdi_info->cio_info);
  }
  // For old versions, all segments will derive from the new root segment, which is index 0
  SetParentSegmentIndex(0);
}

void
cCDI_SEGMENT::ReadFromCDI(CDI_INFO cdi_info)
{
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
    ReadFromOldCDI(cdi_info);
    return;
  }

  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  cdiINT32 index = 0;
  // name
  ccCDI_DO_INNER_CHUNK(index, chunkName, cdi_info) {
    CDI_NAME name = cdi_read_name(cdi_info);
    m_name = name->name;
    cdi_destroy_name(name);
  }
  // parent index
  cdiINT32 parentSegmentIndex = -1;
  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &parentSegmentIndex);
  SetParentSegmentIndex(parentSegmentIndex);
  // segment information (sgmi)
  char segInfoChunkName[10];
  cio_type_to_string(GetSegmentInfoChunkType(), segInfoChunkName);
  if (cdi_cio_descend_with_error(cdi_info->cio_info, segInfoChunkName) == 0) {
    // Regions
    cdi_inner_chunk_read_rgns(index, segInfoChunkName, cdi_info, m_assignedRegionIndices);
    // Partial Parts
    char partialPartsChunkName[10];
    cio_type_to_string(GetPartialPartsChunkType(), partialPartsChunkName);
    if (cdi_cio_descend_with_error(cdi_info->cio_info, partialPartsChunkName) == 0) {
      CIO_INFO cio = cdi_info->cio_info;
      asINT32 nPartialParts = cio_get_count(cio);
      for (int i = 0; i < nPartialParts; i++)
      {
        cio_descend(cio);
        cCDI_PARTIAL_PART& partialPart = CreatePartialPart(-1);
        partialPart.ReadFromCDI(cdi_info);
        cio_ascend(cio);
      }
      cdi_cio_ascend_with_error(cdi_info->cio_info, partialPartsChunkName, ++index);
    }
    // Segment Mapping
    char segMapChunkName[10];
    cio_type_to_string(GetSegmentMappingChunkType(), segMapChunkName);
    if (cdi_cio_descend_with_error(cdi_info->cio_info, segMapChunkName) == 0) {
      // Read a segment list
      char seglChunkName[10];
      cio_type_to_string(CDI_CHUNK_TYPE_SEGL, seglChunkName);
      if (cdi_cio_descend_with_error(cdi_info->cio_info, seglChunkName) == 0) {
        cdi_read_segment_reference_list(cdi_info, m_segmentAssignments);
      }
      cdi_cio_ascend_with_error(cdi_info->cio_info, seglChunkName, ++index);
    }
    cdi_cio_ascend_with_error(cdi_info->cio_info, segMapChunkName, ++index);
  }
  cdi_cio_ascend_with_error(cdi_info->cio_info, segInfoChunkName, ++index);
  // end of segment info (sgmi)
  std::sort(m_assignedRegionIndices.begin(), m_assignedRegionIndices.end());
}

void
cCDI_SEGMENT::WriteToCDI(CDI_INFO cdi_info) const
{
  // name
  sCDI_NAME name;
  name.name = (char*)m_name.c_str();
  name.n_char = m_name.size();
  cdi_write_name(cdi_info, &name);
  // parent index
  cdi_write_int_(cdi_info, m_parentSegmentIndex);
  // segment information (sgmi)
  WITH_CDI_CHUNK(cdi_info, GetSegmentInfoChunkType()) {
    // Regions
    cdi_write_rgns(cdi_info, const_cast<std::vector<int>&>(m_assignedRegionIndices));
    // Partial Parts
    WITH_CDI_CHUNK(cdi_info, GetPartialPartsChunkType()) {
      for (cCDI_PARTIAL_PART* partialPart : m_partialParts) {
        WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_PLPT) {
          partialPart->WriteToCDI(cdi_info);
        }
      }
    }
    // Segment Mapping
    WITH_CDI_CHUNK(cdi_info, GetSegmentMappingChunkType()) {
      // Write a segment list
      WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SEGL) {
        cdi_write_segment_reference_list(cdi_info, m_segmentAssignments);
      }
    }
  }
}

void
cCDI_SEGMENT::Dump(std::ostream &output, int depth, int version) const
{
  PrintElementStart(output, true, true);
  depth++;
  // name
  PrintNameWithID(output, m_name, depth);
  // parent segment index
  PrintAnnotatedInt(output, m_parentSegmentIndex, "parent segment index", depth);
  // segment information (sgmi)
  char segmentInfoType_str[10];
  cio_type_to_string(GetSegmentInfoChunkType(), segmentInfoType_str);
  PrintElementStart(output, segmentInfoType_str, true, depth);
  depth++;
  PrintAnnotatedRgns(output, m_assignedRegionIndices, depth);
  // Partial Parts
  char pptsType_str[10];
  cio_type_to_string(GetPartialPartsChunkType(), pptsType_str);
  PrintElementStart(output, pptsType_str, true, depth);
  depth++;
  for (cCDI_PARTIAL_PART* partialPart : m_partialParts) {
    char plptType_str[10];
    cio_type_to_string(CDI_CHUNK_TYPE_PLPT, plptType_str);
    PrintIndent(output, depth);
    output << plptType_str;
    partialPart->Dump(output, depth, version);
  }
  depth--;
  PrintElementEnd(output, true, depth);
  // segment mapping (sgmp)
  char segmentMapType_str[10];
  cio_type_to_string(GetSegmentMappingChunkType(), segmentMapType_str);
  PrintElementStart(output, segmentMapType_str, true, depth);
  // segment list (segl)
  PrintAnnotatedSegmentList(output, m_segmentAssignments, depth + 1);

  PrintElementEnd(output, true, depth);

  depth--;
  PrintElementEnd(output, true, depth);   // end of segment info (sgmi)

  depth--;
  PrintElementEnd(output, true, depth);
}

void
cCDI_SEGMENT::Undump(int version)
{
  lexer_parse_lbrace();
  ReadNameWithID(&m_name);
  cdiINT32 parentIndex;
  ReadAnnotatedInt(&parentIndex);
  SetParentSegmentIndex(parentIndex);
  // segment information (sgmi)
  lexer_parse_specific_id(GetSegmentInfoChunkType());
  lexer_parse_lbrace();
  // Regions
  ReadAnnotatedRgns(m_assignedRegionIndices);
  // Partial Parts
  lexer_parse_specific_id(GetPartialPartsChunkType());
  lexer_parse_lbrace();
  char cccc_string[5];
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4) &&
    cio_string_to_type(cccc_string) == CDI_CHUNK_TYPE_PLPT)
  {
    cCDI_PARTIAL_PART& partialPart = CreatePartialPart(-1);
    partialPart.Undump(version);
  }
  // end of Partial Parts
  // Segment mapping (sgmp)
  lexer_parse_specific_id(GetSegmentMappingChunkType());
  lexer_parse_lbrace();
  // segment list (segl)
  ReadAnnotatedSegmentList(m_segmentAssignments);

  lexer_parse_rbrace();
  // end of segment map (sgmp)

  lexer_parse_rbrace();
  // end segment information (sgmi)
  lexer_parse_rbrace();
}

bool
cCDI_SEGMENT_REF::ReadFromCDI(CDI_INFO cdi_info)
{
  if (cdi_read_asINT32(cdi_info, &m_partitionIndex, 1) != 1)
    return false;
  if (cdi_read_asINT32(cdi_info, &m_segmentIndex, 1) != 1)
    return false;

  return true;
}

bool
cCDI_SEGMENT_REF::WriteToCDI(CDI_INFO cdi_info) const
{
  if (cdi_write_asINT32(cdi_info, &m_partitionIndex, 1) != 1)
    return false;
  if (cdi_write_asINT32(cdi_info, &m_segmentIndex, 1) != 1)
    return false;

  return true;
}

void
cCDI_SEGMENT_REF::Dump(std::ostream &output) const
{
  PrintElementStart(output, false, false);
  output << " " << m_partitionIndex << " " << m_segmentIndex << " ";
  PrintElementEnd(output, false);
  output << "   (partition index, segment index)\n";
}

void
cCDI_SEGMENT_REF::Undump()
{
  lexer_parse_lbrace();
  m_partitionIndex = lexer_parse_int();
  m_segmentIndex = lexer_parse_int();
  lexer_parse_rbrace();
}

const cCDI_PARTITION&
cCDI_SEGMENT_REF::GetPartition(const cCDI_PARTITIONS& partitions) const
{
  cCDI_PARTITION* partition = partitions.GetPartition(m_partitionIndex);
  return *partition;
}

cCDI_SEGMENT*
cCDI_SEGMENT_REF::GetSegment(const cCDI_PARTITIONS& partitions) const
{
  cCDI_PARTITION* partition = partitions.GetPartition(m_partitionIndex);
  cCDI_SEGMENT* segment = NULL;
  if (partition && m_segmentIndex >= 0) {
    segment = partition->GetSegment(m_segmentIndex);
  }
  return segment;
}

cCDI_PARTIAL_PART::cCDI_PARTIAL_PART(cdiINT32 partIndex, cCDI_SEGMENT* parentSegment)
  : m_partIndex(partIndex),
    m_parentSegment(parentSegment)
{
}

cCDI_PARTIAL_PART::~cCDI_PARTIAL_PART()
{
  m_parentSegment = NULL;
}

std::vector<cdiINT32>
cCDI_PARTIAL_PART::GetFaceIndices() const
{
  std::vector<cdiINT32> returnVec = m_assignedFaceIndices;
  returnVec.insert(returnVec.end(), m_inheritedFaceIndices.begin(), m_inheritedFaceIndices.end());
  return returnVec;
}

sSEGMENT_ASSIGNMENT
cCDI_PARTIAL_PART::GetSegmentAssignment(const cCDI_PARTITION& derivedPartition)
{
  sSEGMENT_ASSIGNMENT returnAssign;
  returnAssign.type = sSEGMENT_ASSIGNMENT::SameAsParent;
  returnAssign.segment = NULL;
  for (cCDI_SEGMENT_REF segRef : m_segmentAssignments) {
    cCDI_PARTITION* partition = GetPartition().GetOwner().GetPartition(segRef.m_partitionIndex);
    if (partition == &derivedPartition) {
      if (segRef.m_segmentIndex < 0) {
        returnAssign.type = sSEGMENT_ASSIGNMENT::Excluded;
      }
      else {
        returnAssign.type = sSEGMENT_ASSIGNMENT::Assigned;
        returnAssign.segment = partition->GetSegment(segRef.m_segmentIndex);
      }
      break;
    }
  }
  if (returnAssign.type == sSEGMENT_ASSIGNMENT::SameAsParent) {
    cCDI_SEGMENT* segment = GetParentSegment();
    if (segment) {
      returnAssign.segment = segment->GetSegmentAssignment(derivedPartition).segment;
    }
  }
  return returnAssign;
}

std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>>
cCDI_PARTIAL_PART::GetFaceAssignments(const cCDI_PARTITION& derivedPartition)
{
  std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> returnVector;
  if (derivedPartition.GetParentPartition() == &(GetPartition())) {
    sSEGMENT_ASSIGNMENT parentAssign = GetSegmentAssignment(derivedPartition);
    std::vector<cdiINT32> faceIndices = GetFaceIndices();
    for (cdiINT32 faceIndex : faceIndices) {
      cCDI_FACE_ENTITY faceEntity = GetPartition().GetOwner().GetFaceEntity(faceIndex);
      returnVector.push_back(std::make_pair(faceIndex, faceEntity.GetSegmentAssignment(derivedPartition, &parentAssign)));
    }
  }
  return returnVector;
}

void
cCDI_PARTIAL_PART::AddSegmentAssignment(const cCDI_PARTITION& assignedPartition, cCDI_SEGMENT* assignedSegment)
{
  cdiINT32 ptnIndex = assignedPartition.GetOwner().GetPartitionIndex(assignedPartition);
  cdiINT32 segIndex = -1;
  if (assignedSegment)
    segIndex = assignedPartition.GetSegmentIndex(*assignedSegment);
  m_segmentAssignments.push_back(cCDI_SEGMENT_REF(ptnIndex, segIndex));
}

std::list<const cCDI_SEGMENT*>
cCDI_PARTIAL_PART::GetPath() const
{
  std::list<const cCDI_SEGMENT*> returnList;
  cCDI_SEGMENT* parentSegment = GetParentSegment();
  while (parentSegment) {
    returnList.push_front(parentSegment);
    parentSegment = parentSegment->GetParentSegment();
  }
  return returnList;
}

void
cCDI_PARTIAL_PART::ReadFromCDI(CDI_INFO cdi_info)
{
  char chunkName[10];
  cdiINT32 index = 0;
  cio_type_to_string(GetChunkType(), chunkName);
  // part index
  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &m_partIndex);
  // Assigned Faces
  cdi_inner_chunk_read_flst(index, chunkName, cdi_info, m_assignedFaceIndices);
  // Mapped Faces
  cdi_inner_chunk_read_flst(index, chunkName, cdi_info, m_inheritedFaceIndices);
  // Segment Mapping
  char segMapChunkName[10];
  cio_type_to_string(CDI_CHUNK_TYPE_SGMP, segMapChunkName);
  if (cdi_cio_descend_with_error(cdi_info->cio_info, segMapChunkName) == 0) {
    // Read a segment list
    char seglChunkName[10];
    cio_type_to_string(CDI_CHUNK_TYPE_SEGL, seglChunkName);
    if (cdi_cio_descend_with_error(cdi_info->cio_info, seglChunkName) == 0) {
      cdi_read_segment_reference_list(cdi_info, m_segmentAssignments);
    }
    cdi_cio_ascend_with_error(cdi_info->cio_info, seglChunkName, ++index);
  }
  cdi_cio_ascend_with_error(cdi_info->cio_info, segMapChunkName, ++index);

  std::sort(m_assignedFaceIndices.begin(), m_assignedFaceIndices.end());
}

void
cCDI_PARTIAL_PART::WriteToCDI(CDI_INFO cdi_info) const
{
  // part index
  cdi_write_int_(cdi_info, m_partIndex);
  // Assigned Faces
  cdi_write_flst(cdi_info, const_cast<std::vector<int>&>(m_assignedFaceIndices));
  // Mapped Faces
  cdi_write_flst(cdi_info, const_cast<std::vector<int>&>(m_inheritedFaceIndices));
  // Segment Mapping
  WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SGMP) {
    // Write a segment list
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SEGL) {
      cdi_write_segment_reference_list(cdi_info, m_segmentAssignments);
    }
  }
}

void
cCDI_PARTIAL_PART::Dump(std::ostream &output, int depth, int version) const
{
  PrintElementStart(output, true, true);
  depth++;
  // part index
  PrintAnnotatedInt(output, m_partIndex, "part index", depth);
  // Assigned Faces
  PrintAnnotatedFlst(output, m_assignedFaceIndices, depth);
  // Inherited Faces
  PrintAnnotatedFlst(output, m_inheritedFaceIndices, depth);
  // segment mapping (sgmp)
  char segmentMapType_str[10];
  cio_type_to_string(CDI_CHUNK_TYPE_SGMP, segmentMapType_str);
  PrintElementStart(output, segmentMapType_str, true, depth);
  // segment list (segl)
  PrintAnnotatedSegmentList(output, m_segmentAssignments, depth + 1);

  PrintElementEnd(output, true, depth);  // end of segment map (sgmp)

  depth--;
  PrintElementEnd(output, true, depth);
}

void
cCDI_PARTIAL_PART::Undump(int version)
{
  lexer_parse_lbrace();
  ReadAnnotatedInt(&m_partIndex);
  // Assigned Faces
  ReadAnnotatedFlst(m_assignedFaceIndices);
  // Inherited Faces
  ReadAnnotatedFlst(m_inheritedFaceIndices);
  // Segment mapping (sgmp)
  lexer_parse_specific_id(CDI_CHUNK_TYPE_SGMP);
  lexer_parse_lbrace();
  // segment list (segl)
  ReadAnnotatedSegmentList(m_segmentAssignments);

  lexer_parse_rbrace();
  // end of segment map (sgmp)

  lexer_parse_rbrace();
}

bool
cCDI_PARTIAL_PART_REF::ReadFromCDI(CDI_INFO cdi_info)
{
  if (cdi_read_asINT32(cdi_info, &m_partitionIndex, 1) != 1)
    return false;
  if (cdi_read_asINT32(cdi_info, &m_partIndex, 1) != 1)
    return false;

  return true;
}

bool
cCDI_PARTIAL_PART_REF::WriteToCDI(CDI_INFO cdi_info) const
{
  if (cdi_write_asINT32(cdi_info, &m_partitionIndex, 1) != 1)
    return false;
  if (cdi_write_asINT32(cdi_info, &m_partIndex, 1) != 1)
    return false;

  return true;
}

void
cCDI_PARTIAL_PART_REF::Dump(std::ostream &output) const
{
  PrintElementStart(output, false, false);
  output << " " << m_partitionIndex << " " << m_partIndex << " ";
  PrintElementEnd(output, false);
  output << "   (partition index, part index)\n";
}

void
cCDI_PARTIAL_PART_REF::Undump()
{
  lexer_parse_lbrace();

  m_partitionIndex = lexer_parse_int();
  m_partIndex = lexer_parse_int();

  lexer_parse_rbrace();
}

const cCDI_PARTITION&
cCDI_PARTIAL_PART_REF::GetPartition(const cCDI_PARTITIONS& partitions) const
{
  cCDI_PARTITION* partition = partitions.GetPartition(m_partitionIndex);
  return *partition;
}

const cCDI_PARTIAL_PART&
cCDI_PARTIAL_PART_REF::GetPartialPart(const cCDI_PARTITIONS& partitions) const
{
  cCDI_PARTITION* partition = partitions.GetPartition(m_partitionIndex);
  cCDI_PARTIAL_PART* partialPart = NULL;
  if (partition) {
    partialPart = partition->GetPartialPart(m_partIndex);
  }
  return *partialPart;
}

cCDI_PARTITION::cCDI_PARTITION(std::string& name, cCDI_PARTITIONS* owner, cCDI_PARTITION* parentPartition)
  : m_name(name),
    m_owner(owner)
{
  m_parentPartitionIndex = parentPartition ? m_owner->GetPartitionIndex(*parentPartition) : -1;
  // Create the Root segment
  std::string rootName = "Root";
  m_segments.push_back(std::unique_ptr<cCDI_SEGMENT>(new cCDI_SEGMENT(rootName, this, NULL)));
}

cCDI_PARTITION::~cCDI_PARTITION()
{
  m_owner = NULL;
}

cCDI_SEGMENT&
cCDI_PARTITION::GetRoot() const
{
  assert(!m_segments.empty());
  return *(m_segments[0].get());
}

std::vector<cCDI_PARTIAL_PART*>
cCDI_PARTITION::GetPartialParts() const {
   std::vector<cCDI_PARTIAL_PART*> partialParts;
   partialParts.reserve(m_partialParts.size());
   for (auto&& cPartialPart : m_partialParts)
      partialParts.push_back(cPartialPart.get());
   return partialParts;
}

std::vector<cCDI_SEGMENT*>
cCDI_PARTITION::GetSegments() const {
  std::vector<cCDI_SEGMENT*> segments;
  segments.reserve(m_segments.size());
  for (auto&& cSegment : m_segments)
    segments.push_back(cSegment.get());
  return segments;
}

cCDI_PARTITION*
cCDI_PARTITION::GetParentPartition() const
{
  cCDI_PARTITION* parentPartition = NULL;
  if (m_parentPartitionIndex >= 0)
    parentPartition = m_owner->GetPartition(m_parentPartitionIndex);
  return parentPartition;
}

void
cCDI_PARTITION::SetParentPartitionIndex(cdiINT32 parentPartitionIndex)
{
  m_parentPartitionIndex = parentPartitionIndex;
  if (m_parentPartitionIndex >= 0)
    GetParentPartition()->AddDerivedPartition(this);
}

bool
cCDI_PARTITION::GetQualifiedPartName(cdiINT32 partIndex, bool includePath, std::string* partName) const
{
  bool inPartition = false;
  if (IsBaseAssembly()) {
    if (includePath)
      *partName = GetName() + GetOwner().GetPartName(partIndex);
    else
      *partName = GetOwner().GetShortPartName(partIndex);
    inPartition = true;
  }
  else {
    const cCDI_PART_ENTITY& partEntity = GetOwner().GetPartEntity(partIndex);
    sSEGMENT_ASSIGNMENT partSeg = partEntity.GetSegmentAssignment(*this);
    if (partSeg.type == sSEGMENT_ASSIGNMENT::Excluded) {
      inPartition = false;
    }
    else if (partSeg.type == sSEGMENT_ASSIGNMENT::Assigned) {
      inPartition = partSeg.segment->GetQualifiedPartName(partIndex, includePath, partName);
    }
    else {
      for (cCDI_SEGMENT* segment : GetSegments()) {
        inPartition = segment->GetQualifiedPartName(partIndex, includePath, partName);
        if (inPartition)
          break;
      }
    }
  }
  return inPartition;
}

bool
cCDI_PARTITION::GetQualifiedFaceName(cdiINT32 faceIndex, bool includePath, std::string* faceName) const
{
  bool inPartition = false;
  if (IsBaseAssembly()) {
    if (includePath)
      *faceName = GetName() + GetOwner().GetFaceName(faceIndex);
    else
      *faceName = GetOwner().GetShortFaceName(faceIndex, true);
    inPartition = true;
  }
  else {
    const cCDI_FACE_ENTITY& faceEntity = GetOwner().GetFaceEntity(faceIndex);
    sSEGMENT_ASSIGNMENT faceSeg = faceEntity.GetSegmentAssignment(*this);
    if (faceSeg.type == sSEGMENT_ASSIGNMENT::Excluded) {
      inPartition = false;
    }
    else if (faceSeg.type == sSEGMENT_ASSIGNMENT::Assigned) {
      inPartition = faceSeg.segment->GetQualifiedFaceName(faceIndex, includePath, faceName);
    }
    else {
      // Same as parent
      std::string partName;
      inPartition = GetQualifiedPartName(faceEntity.GetPartIndex(), includePath, &partName);
      if (inPartition)
        *faceName = partName + FACE_DELIMITER + GetOwner().GetShortFaceName(faceIndex, false);
    }
  }
  return inPartition;
}

cCDI_PARTITION&
cCDI_PARTITION::CreateDerivedPartition(std::string& name)
{
  std::unique_ptr<cCDI_PARTITION> newPartition(new cCDI_PARTITION(name, m_owner, this));
  cCDI_PARTITION* returnPartition = newPartition.get();
  m_childPartitions.push_back(returnPartition);
  // Give "ownership" to the cCDI_PARTITIONS
  m_owner->AddPartition(std::move(newPartition));
  return *returnPartition;
}

cCDI_SEGMENT&
cCDI_PARTITION::CreateSegmentForRecall()
{
  std::string emptyName = "";
  std::unique_ptr<cCDI_SEGMENT> newSegment(new cCDI_SEGMENT(emptyName, this, NULL));
  cCDI_SEGMENT* returnSegment = newSegment.get();
  m_segments.push_back(std::move(newSegment));
  return *returnSegment;
}

cdiINT32
cCDI_PARTITION::GetSegmentIndex(const cCDI_SEGMENT& segment) const
{
  cdiINT32 parentSegmentIndex = 0;
  for (auto&& cSegment : m_segments) {
    if (&segment == cSegment.get())
      break;
    ++parentSegmentIndex;
  }
  return parentSegmentIndex < (cdiINT32)m_segments.size() ? parentSegmentIndex : -1;
}

cCDI_SEGMENT*
cCDI_PARTITION::GetSegment(cdiINT32 segmentIndex) const
{
  cCDI_SEGMENT* returnSegment = NULL;
  if (segmentIndex >= 0 && segmentIndex < (cdiINT32)m_segments.size())
    returnSegment = m_segments[segmentIndex].get();
  return returnSegment;
}

cCDI_SEGMENT*
cCDI_PARTITION::GetSegment(const std::string& segmentPath) const
{
  cCDI_SEGMENT* returnSegment = m_segments[0].get();
  std::vector<std::string> segmentNames = SplitAndTrim(segmentPath, '/');
  if (!segmentNames.empty()) {
    for (std::string segName : segmentNames) {
      bool segmentFound = false;
      for (cCDI_SEGMENT* childSeg : returnSegment->GetChildSegments()) {
        if (segName == childSeg->GetName(false)) {
          returnSegment = childSeg;
          segmentFound = true;
          break;
        }
      }
      if (!segmentFound)
        return nullptr;
    }
  }
  return returnSegment;
}

std::vector<std::pair<cCDI_SEGMENT*, sSEGMENT_ASSIGNMENT>>
cCDI_PARTITION::GetSegmentAssignments(const cCDI_PARTITION& derivedPartition)
{
  std::vector<std::pair<cCDI_SEGMENT*, sSEGMENT_ASSIGNMENT>> returnVector;
  if (derivedPartition.GetParentPartition() == this) {
    for (auto&& segment : m_segments) {
      returnVector.push_back(std::make_pair(segment.get(), segment->GetSegmentAssignment(derivedPartition)));
    }
  }
  return returnVector;
}

std::vector<std::pair<cCDI_PARTIAL_PART*, sSEGMENT_ASSIGNMENT>>
cCDI_PARTITION::GetPartialPartAssignments(const cCDI_PARTITION& derivedPartition)
{
  std::vector<std::pair<cCDI_PARTIAL_PART*, sSEGMENT_ASSIGNMENT>> returnVector;
  if (derivedPartition.GetParentPartition() == this) {
    for (auto&& ppart : m_partialParts) {
      returnVector.push_back(std::make_pair(ppart.get(), ppart->GetSegmentAssignment(derivedPartition)));
    }
  }
  return returnVector;
}

std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>>
cCDI_PARTITION::GetRegionAssignments(const cCDI_PARTITION& derivedPartition)
{
  std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> returnVector;
  if (derivedPartition.GetParentPartition() == this) {
    for (auto&& segment : m_segments) {
      std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> segAssigns = segment->GetRegionAssignments(derivedPartition);
      returnVector.insert(returnVector.end(), segAssigns.begin(), segAssigns.end());
    }
  }
  return returnVector;
}

std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>>
cCDI_PARTITION::GetFaceAssignments(const cCDI_PARTITION& derivedPartition)
{
  std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> returnVector;
  if (derivedPartition.GetParentPartition() == this) {
    for (auto&& segment : m_segments) {
      std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> segAssigns = segment->GetFaceAssignments(derivedPartition);
      returnVector.insert(returnVector.end(), segAssigns.begin(), segAssigns.end());
    }
  }
  return returnVector;
}

void
cCDI_PARTITION::BuildMembershipForChildren(bool addAssigned)
{
  for (cCDI_PARTITION* childPartition : m_childPartitions) {
    for (auto&& segment : m_segments) {
      std::vector<cdiINT32> segPartIndices = segment->GetRegionIndices(false);
      sSEGMENT_ASSIGNMENT parentAssign = segment->GetSegmentAssignment(*childPartition);
      for (cdiINT32 partIndex : segPartIndices) {
        cCDI_PART_ENTITY part = m_owner->GetPartEntity(partIndex);
        sSEGMENT_ASSIGNMENT partAssign = part.GetSegmentAssignment(*childPartition, &parentAssign);
        if (partAssign.type == sSEGMENT_ASSIGNMENT::Assigned) {
          if (addAssigned)
            partAssign.segment->AddAssignedRegionIndex(part.GetIndex());
        }
        else if (partAssign.type == sSEGMENT_ASSIGNMENT::SameAsParent) {
          cCDI_SEGMENT* segAssign = partAssign.segment;
          if (segAssign)
            segAssign->AddInheritedRegionIndex(part.GetIndex());
        }
      }
    }
    childPartition->BuildMembershipForChildren(addAssigned);
  }
}

void
cCDI_PARTITION::FixupPartialParts()
{
  // In old CDIs before the partition enhancement, parts were assigned to the "Unassigned"
  // segment in face partitions even if some or all of the faces were assigned to a different
  // segment. Therefore, these types of parts need to be turned into "partial" parts and their
  // assignment to the "Unassigned" segment removed. If all of the faces are assigned
  // to segments, then the part is still assigned to "Unassigned". In that case, we do not want a
  // partial part.
  std::set<cdiINT32> partsToCheck;
  for (auto&& ppart : m_partialParts) {
    partsToCheck.insert(ppart->GetPartIndex());
  }
  cdiINT32 partitionIndex = m_owner->GetPartitionIndex(*this);
  for (cdiINT32 partIndex : partsToCheck) {
    cCDI_PART_ENTITY& part = m_owner->GetPartEntity(partIndex);
    sSEGMENT_ASSIGNMENT partAssign = part.GetSegmentAssignment(*this);
    // If this part is assigned to the partition, then it is supposed to be a partial part
    cCDI_PARTIAL_PART* newPartialPart = nullptr;
    if (partAssign.type == sSEGMENT_ASSIGNMENT::Assigned) {
      // Now check all the faces. Any faces assigned to a segment are good. If they
      // are same as parent, then that means they are inheriting from the part; these
      // are the ones that now need to be added to a partial part.
      cCDI_SEGMENT_REF segRef(partitionIndex, GetSegmentIndex(*(partAssign.segment)));
      for (cdiINT32 faceIndex : part.GetFaceList()) {
        cCDI_FACE_ENTITY& face = m_owner->GetFaceEntity(faceIndex);
        sSEGMENT_ASSIGNMENT faceAssign = face.GetSegmentAssignment(*this);
        if (faceAssign.type == sSEGMENT_ASSIGNMENT::SameAsParent) {
          face.AddSegmentAssignment(segRef);
          if (!newPartialPart)
            newPartialPart = &(partAssign.segment->CreatePartialPart(partIndex));
          newPartialPart->AddAssignedFaceIndex(faceIndex);
        }
      }
      // Do not have the part assigned to the "Unassigned" segment
      part.RemoveSegmentAssignment(partitionIndex);
    }
  }
}

cdiINT32
cCDI_PARTITION::GetPartialPartIndex(const cCDI_PARTIAL_PART& partialPart) const
{
  cdiINT32 partialIndex = 0;
  for (auto&& cPartial : m_partialParts) {
    if (&partialPart == cPartial.get())
      break;
    ++partialIndex;
  }
  return partialIndex < (cdiINT32)m_partialParts.size() ? partialIndex : -1;
}

cCDI_PARTIAL_PART*
cCDI_PARTITION::GetPartialPart(cdiINT32 partialPartIndex) const
{
  cCDI_PARTIAL_PART* returnPart = NULL;
  if (partialPartIndex >= 0 && partialPartIndex < (cdiINT32)m_partialParts.size())
    returnPart = m_partialParts[partialPartIndex].get();
  return returnPart;
}

void
cCDI_PARTITION::ReadFromOldCDI(CDI_INFO cdi_info)
{
  // This is for reading partitions from pre-V8.0 CDIs
  // For old versions, all partitions will be derived from the base assembly, which is index 0
  SetParentPartitionIndex(0);

  asINT32 count = cio_get_count(cdi_info->cio_info);
  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);

    switch (cio_get_type(cdi_info->cio_info)) {
    case CDI_CHUNK_TYPE_NAME: {
      CDI_NAME name = cdi_read_name(cdi_info);
      m_name = name->name;
      cdi_destroy_name(name);
      break;
    }
    case CDI_CHUNK_TYPE_PRTT: {
      // No longer needed
      sCDI_PRTT prtt;
      cdi_read_partition_type(cdi_info, &prtt);
      break;
    }
    case CDI_CHUNK_TYPE_SGDF: {
      // SGDF
      CIO_INFO cio = cdi_info->cio_info;
      asINT32 nSegments = cio_get_count(cio);
      for (int i = 0; i < nSegments; i++)
      {
        cio_descend(cio);
        cCDI_SEGMENT& segment = CreateSegmentForRecall();
        segment.ReadFromCDI(cdi_info);
        cio_ascend(cio);
      }
      break;
    }
    default:
      break;
    }

    cio_ascend(cdi_info->cio_info);
  }
}

void
cCDI_PARTITION::ReadFromCDI(CDI_INFO cdi_info)
{
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(cdi_info)) {
    ReadFromOldCDI(cdi_info);
    return;
  }

  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  cdiINT32 index = 0;
  // name
  ccCDI_DO_INNER_CHUNK(index, chunkName, cdi_info) {
    CDI_NAME name = cdi_read_name(cdi_info);
    m_name = name->name;
    cdi_destroy_name(name);
  }
  // parent index
  cdiINT32 parentPartitionIndex = -1;
  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &parentPartitionIndex);
  SetParentPartitionIndex(parentPartitionIndex);
  // segments (sgdf)
  char segmentsChunkName[10];
  cio_type_to_string(GetSegmentsChunkType(), segmentsChunkName);
  if (cdi_cio_descend_with_error(cdi_info->cio_info, segmentsChunkName) == 0) {
    CIO_INFO cio = cdi_info->cio_info;
    asINT32 nSegments = cio_get_count(cio);
    for (int i = 0; i < nSegments; i++)
    {
      cio_descend(cio);
      // The root segment should always be first
      if (i == 0) {
        GetRoot().ReadFromCDI(cdi_info);
      }
      else {
        cCDI_SEGMENT& segment = CreateSegmentForRecall();
        segment.ReadFromCDI(cdi_info);
      }

      cio_ascend(cio);
    }
  }
  cdi_cio_ascend_with_error(cdi_info->cio_info, segmentsChunkName, ++index);
}

void
cCDI_PARTITION::WriteToCDI(CDI_INFO cdi_info) const
{
  // This should only be used for versions 8.0 and up
  // name
  sCDI_NAME name;
  name.name = (char*)m_name.c_str();
  name.n_char = m_name.size();
  cdi_write_name(cdi_info, &name);
  // parent index
  cdi_write_int_(cdi_info, m_parentPartitionIndex);
  // segments (sgdf)
  WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SGDF) {
    for (auto&& segment : m_segments) {
      WITH_CDI_CHUNK(cdi_info, segment->GetChunkType()) {
        segment->WriteToCDI(cdi_info);
      }
    }
  }
}

void
cCDI_PARTITION::Dump(std::ostream &output, int depth, int version) const
{
  PrintElementStart(output, true, true);
  depth++;
  PrintNameWithID(output, m_name, depth);
  PrintAnnotatedInt(output, m_parentPartitionIndex, "parent partition index", depth);

  // segments (sgdf)
  char segmentsType_str[10];
  cio_type_to_string(GetSegmentsChunkType(), segmentsType_str);
  PrintElementStart(output, segmentsType_str, true, depth);
  depth++;
  for (auto&& segment : m_segments) {
    char type_str[10];
    cio_type_to_string(segment->GetChunkType(), type_str);
    PrintIndent(output, depth);
    output << type_str;
    segment->Dump(output, depth, version);
  }
  depth--;
  PrintElementEnd(output, true, depth);  // end of segments (sgdf)

  depth--;
  PrintElementEnd(output, true, depth);
}

void
cCDI_PARTITION::Undump(int version)
{
  lexer_parse_lbrace();
  ReadNameWithID(&m_name);
  cdiINT32 parentIndex;
  ReadAnnotatedInt(&parentIndex);
  SetParentPartitionIndex(parentIndex);

  // segments (sgdf)
  lexer_parse_specific_id(GetSegmentsChunkType());
  lexer_parse_lbrace();
  char cccc_string[5];
  cdiINT32 iSegment = 0;
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4) &&
    cio_string_to_type(cccc_string) == cCDI_SEGMENT::GetSegmentChunkType())
  {
    if (iSegment > 0) {
      cCDI_SEGMENT& segment = CreateSegmentForRecall();
      segment.Undump(version);
    }
    else {
      GetRoot().Undump(version);
    }
    ++iSegment;
  }
  // end of segments (sgdf)

  lexer_parse_rbrace();
}

cCDI_PARTITIONS::cCDI_PARTITIONS()
{
  m_readingPreV8CDI = true;
  m_printVersion = cCDI_PARTITIONS::VERSION;
  // Always create the base assembly
  std::string baseAssemblyName = "Base Assembly";
  m_partitions.push_back(std::unique_ptr<cCDI_PARTITION>(new cCDI_PARTITION(baseAssemblyName, this, NULL)));
}

cCDI_PARTITIONS::~cCDI_PARTITIONS()
{
}

bool cCDI_PARTITIONS::CDIIsPartitionVersion(CDI_INFO cdi_info)
{
  // Due to the parallel development of partitions and conduction, the CDI versioning got stepped on.
  // Hierarchical partitions introduced versions 8.3-8.7.
  // Conduction CDIs started with 9.0 but there was a period when conduction cases were created without hierarchy.
  // Partitions and conduction were merged at 9.4. Therefore, CDIs created on the "cond_sm"
  // branch between v9.0-9.3 did not have partitions. So we need to make sure that CDIs with versions 
  // between 9.0 and 9.3 do not try to read/write hierarchical information.
  return cdi_version_is_at_least_and_not_parallel_dev_cdi<8,3>(cdi_info);
}

int
cCDI_PARTITIONS::GetPrintVersionForCDI(int cdiMajorVersion, int cdiMinorVersion) const
{
  // This method maps the CDI version to the appropriate print version. This is needed for
  // calls to Dump/Undump.
  // If the CDI format for the partitions is updated, then the CDI version and VERSION should be
  // updated and the map below should reflect that change.
  //   key = VERSION -> value = (CDI Major Version, CDI Minor Version)
  static const std::map<int, std::pair<int, int>> versionMap = {
    {2, {1, 0}}
  };
  int foundVersion = -1;
  for (auto const& mapitem : versionMap) {
    int itemMajor = mapitem.second.first;
    int itemMinor = mapitem.second.second;
    if ((cdiMajorVersion < itemMajor) ||
        (cdiMajorVersion == itemMajor && cdiMinorVersion < itemMinor))
      break;

    foundVersion = mapitem.first;
  }
  return foundVersion;
}

cCDI_PARTITION&
cCDI_PARTITIONS::GetBaseAssembly() const
{
  assert(!m_partitions.empty());
  return *(m_partitions[0].get());
}

cCDI_PARTITION&
cCDI_PARTITIONS::CreatePartitionForRecall()
{
  std::string emptyName = "";
  std::unique_ptr<cCDI_PARTITION> newPartition(new cCDI_PARTITION(emptyName, this, NULL));
  cCDI_PARTITION* returnPartition = newPartition.get();
  m_partitions.push_back(std::move(newPartition));
  return *returnPartition;
}

void
cCDI_PARTITIONS::ReadFromCDI(CDI_INFO cdi_info)
{
  m_readingPreV8CDI = !CDIIsPartitionVersion(cdi_info);
  m_printVersion = GetPrintVersionForCDI(cdi_info->major_version, cdi_info->minor_version);
  CIO_INFO cio = cdi_info->cio_info;

  cdiINT32 nPartitions = cio_get_count(cio);
  for (int i = 0; i < nPartitions; i++)
  {
    cio_descend(cio);

    // The base assembly did not exist in old versions
    bool readBaseAssembly = (i == 0 && !m_readingPreV8CDI);
    if (readBaseAssembly) {
      GetBaseAssembly().ReadFromCDI(cdi_info);
    }
    else {
      cCDI_PARTITION& partition = CreatePartitionForRecall();
      partition.ReadFromCDI(cdi_info);
    }

    cio_ascend(cio);
  }
}

void
cCDI_PARTITIONS::WriteToCDI(CDI_INFO cdi_info) const
{
  for (auto&& partition : m_partitions)
  {
    WITH_CDI_CHUNK(cdi_info, partition->GetChunkType()) {
      partition->WriteToCDI(cdi_info);
    }
  }
}

void
cCDI_PARTITIONS::PrintToStream(std::ostream &output, int depth) const
{
  PrintElementStart(output, "PARTITIONS", true, depth);
  depth++;
  // Print Version
  PrintAnnotatedInt(output, m_printVersion, "file version", depth);
  // Print the PSDF information
  char psdfType_str[10];
  cio_type_to_string(CDI_CHUNK_TYPE_PSDF, psdfType_str);
  PrintIndent(output, depth);
  output << psdfType_str;
  DumpPSDF(output, depth, m_printVersion);
  // Print the part and face information
  PrintEntityInfo(output, depth, m_printVersion);
  depth--;
  PrintElementEnd(output, true, depth);
}

void
cCDI_PARTITIONS::Dump(CDI_INFO cdi_info, int depth) const
{
  DumpPSDF(std::cout, depth, GetPrintVersionForCDI(cdi_info->major_version, cdi_info->minor_version));
}

void
cCDI_PARTITIONS::DumpPSDF(std::ostream &output, int depth, int version) const
{
  PrintElementStart(output, true, true); // Partitions (psdf) bracing
  depth++;

  for (auto&& partition : m_partitions) {
    char type_str[10];
    cio_type_to_string(partition->GetChunkType(), type_str);
    PrintIndent(output, depth);
    output << type_str;
    partition->Dump(output, depth, version);
  }

  depth--;
  PrintElementEnd(output, true, depth);  // Close outer psdf bracing
}

void
cCDI_PARTITIONS::PrintEntityInfo(std::ostream &output, int depth, int version) const
{
  // Print part and face table
  PrintElementStart(output, "ENTITY_INFO", true, depth);
  depth++;
  PrintElementStart(output, "PARTS", true, depth);
  depth++;
  for (auto const& mapitem : m_partEntities) {
    cCDI_PART_ENTITY partEntity = mapitem.second;
    PrintElementStart(output, "PART", true, depth);
    depth++;
    PrintNameWithID(output, partEntity.GetName(), depth);
    PrintAnnotatedInt(output, partEntity.GetIndex(), "index", depth);
    PrintAnnotatedSegmentList(output, partEntity.m_segmentAssignments, depth);
    depth--;
    PrintElementEnd(output, true, depth);
  }
  depth--;
  PrintElementEnd(output, true, depth);  // End "PARTS"
  PrintElementStart(output, "FACES", true, depth);
  depth++;
  for (auto const& mapitem : m_faceEntities) {
    cCDI_FACE_ENTITY faceEntity = mapitem.second;
    PrintElementStart(output, "FACE", true, depth);
    depth++;
    PrintNameWithID(output, faceEntity.GetName(), depth);
    PrintAnnotatedInt(output, faceEntity.GetIndex(), "index", depth);
    PrintAnnotatedInt(output, faceEntity.GetPartIndex(), "part index", depth);
    if (version >= 2)
      PrintAnnotatedInt(output, faceEntity.GetSurfacePropertyIndex(), "surface property index", depth);
    PrintAnnotatedSegmentList(output, faceEntity.m_segmentAssignments, depth);
    depth--;
    PrintElementEnd(output, true, depth);
  }
  depth--;
  PrintElementEnd(output, true, depth);  // End "FACES"
  depth--;
  PrintElementEnd(output, true, depth);  // End "ENTITY_INFO"
}

void
cCDI_PARTITIONS::ReadEntityInfo(int version)
{
  char mappingStr[12];   // "ENTITY_INFO"
  lexer_parse_n_char_id_or_rbrace(mappingStr, 11);
  lexer_parse_lbrace();
  char partsStr[6];   // "PARTS"
  lexer_parse_n_char_id_or_rbrace(partsStr, 5);
  lexer_parse_lbrace();
  char label[5];         // "PART"
  while (lexer_parse_n_char_id_or_rbrace(label, 4))
  {
    lexer_parse_lbrace();
    std::string partName;
    cdiINT32 partIndex;
    std::vector<cCDI_SEGMENT_REF> segmentAssignments;
    ReadNameWithID(&partName);
    ReadAnnotatedInt(&partIndex);
    ReadAnnotatedSegmentList(segmentAssignments);
    cCDI_PART_ENTITY& partEntity = GetPartEntity(partIndex);
    partEntity.SetName(partName);
    for (cCDI_SEGMENT_REF segRef : segmentAssignments)
      partEntity.AddSegmentAssignment(segRef);
    lexer_parse_rbrace();
  }
  char facesStr[6];   // "FACES"
  lexer_parse_n_char_id_or_rbrace(facesStr, 5);
  lexer_parse_lbrace();
  char facelabel[5];         // "FACE"
  while (lexer_parse_n_char_id_or_rbrace(facelabel, 4))
  {
    lexer_parse_lbrace();
    std::string faceName;
    cdiINT32 faceIndex;
    cdiINT32 partIndex;
    cdiINT32 surfaceIndex = CDI_PHYS_TYPE_NONE;
    std::vector<cCDI_SEGMENT_REF> segmentAssignments;
    ReadNameWithID(&faceName);
    ReadAnnotatedInt(&faceIndex);
    ReadAnnotatedInt(&partIndex);
    if (version >= 2)
      ReadAnnotatedInt(&surfaceIndex);
    ReadAnnotatedSegmentList(segmentAssignments);
    cCDI_FACE_ENTITY& faceEntity = GetFaceEntity(faceIndex);
    faceEntity.SetName(faceName);
    faceEntity.SetPartIndex(partIndex);
    faceEntity.SetSurfacePropertyIndex(surfaceIndex);
    for (cCDI_SEGMENT_REF segRef : segmentAssignments)
      faceEntity.AddSegmentAssignment(segRef);
    cCDI_PART_ENTITY& partEntity = GetPartEntity(partIndex);
    partEntity.AddFace(faceIndex);
    lexer_parse_rbrace();
  }
  lexer_parse_rbrace(); // End of ENTITY_INFO
}

bool
cCDI_PARTITIONS::ReadFromStream(std::istream &input)
{
  std::ostringstream ostr;
  input >> ostr.rdbuf();
  STRING pstring = EXA_STRDUP(ostr.str().c_str());
  lexer_set_scan_string(pstring);
  lexer_set_lineno(1);

  // Read Header
  char partitionsStr[11]; // "PARTITIONS"
  lexer_parse_n_char_id_or_rbrace(partitionsStr, 10);
  lexer_parse_lbrace();
  // Read version
  ReadAnnotatedInt(&m_printVersion);
  if (m_printVersion <= 0 || m_printVersion > VERSION)
    return false;
  // Read PSDF
  lexer_parse_specific_id(CDI_CHUNK_TYPE_PSDF);
  UndumpPSDF(m_printVersion);
  // Read part and face info
  ReadEntityInfo(m_printVersion);
  lexer_parse_rbrace();

  // Need to finalize the read to build the segment membership
  m_readingPreV8CDI = false;
  FinalizeCDIRead();

  return true;
}

void
cCDI_PARTITIONS::Undump(CDI_INFO cdi_info)
{
  UndumpPSDF(GetPrintVersionForCDI(cdi_info->major_version, cdi_info->minor_version));

  if (cdi_info)
  {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_PSDF) {
      WriteToCDI(cdi_info);
    }
  }
}

void
cCDI_PARTITIONS::UndumpPSDF(int version)
{
  if (version <= 0 || version > VERSION)
    return;

  lexer_parse_lbrace();   // Opening '{' after 'psdf'
  char cccc_string[5];
  cdiINT32 iPartition = 0;
  while (lexer_parse_n_char_id_or_rbrace(cccc_string, 4) &&
    cio_string_to_type(cccc_string) == cCDI_PARTITION::GetPartitionChunkType())
  {
    if (iPartition > 0) {
      cCDI_PARTITION& partition = CreatePartitionForRecall();
      partition.Undump(version);
    }
    else {
      GetBaseAssembly().Undump(version);
    }
    ++iPartition;
  }
}

void
cCDI_PARTITIONS::UpdateSegmentMembershipForRegion(CDI_RGND rgnd, cdiINT32 regionIndex)
{
  // This method needs to be called by clients while reading the CDI_RGND chunks so
  // that the partitions class knows about the part assignments to segments.
  // For pre-V8.0 files, the parts also need to be assigned in the base assembly since
  // the SGMI chunk did not exist. (The later call to FinalizeCDIRead will populate
  // the assigned parts to derived partitions for old cases.)
  cCDI_PART_ENTITY& partEntity = GetPartEntity(regionIndex);
  partEntity.SetName(rgnd->name);
  for (cCDI_SEGMENT_REF sgrf : rgnd->psmv.partition_segment_refs) {
    partEntity.AddSegmentAssignment(sgrf);
    if (m_readingPreV8CDI) {
      // Populate regions/parts in the base assembly
      if (sgrf.m_partitionIndex == 0) {
        cdiINT32 segIndex = sgrf.m_segmentIndex;
        if (segIndex >= 0) {
          cCDI_PARTITION* partition = GetPartition(sgrf.m_partitionIndex);
          if (partition) {
            cCDI_SEGMENT* segment = partition->GetSegment(segIndex);
            if (segment)
              segment->AddAssignedRegionIndex(regionIndex);
          }
        }
      }
    }
  }
  // Note any sub-regions of rgnd
  const sCDI_RGNS* pSubRegions = rgnd->sub_regions;
  if (pSubRegions)
    for (cdiINT32 i = 0; i < pSubRegions->n_region; i++)
      AddHexSubRegion(pSubRegions->region[i]);
}

void
cCDI_PARTITIONS::UpdateSegmentMembershipForFace(cdiINT32 regionIndex, CDI_FACE face)
{
  // This method needs to be called by clients while reading the CDI_FACE chunks so
  // that the partitions class knows about the face assignments to segments and which
  // faces belong to which parts
  // For old cases, the partial parts need to be created
  cCDI_FACE_ENTITY& faceEntity = GetFaceEntity(face->index);
  cCDI_PART_ENTITY& partEntity = GetPartEntity(regionIndex);
  std::string faceName = face->name;
  if (m_readingPreV8CDI) {
    // Need to remove the part name and the "." delimiter
    std::string partPrefix = partEntity.GetName() + OLD_FACE_DELIMITER;
    if (faceName.find(partPrefix) == 0)
      faceName.erase(0, partPrefix.length());
  }
  faceEntity.SetName(faceName);
  faceEntity.SetPartIndex(regionIndex);
  faceEntity.SetSurfacePropertyIndex(face->prop);
  for (cCDI_SEGMENT_REF sgrf : face->psmv.partition_segment_refs) {
    faceEntity.AddSegmentAssignment(sgrf);
    if (m_readingPreV8CDI) {
      // Create any partial parts (not necessary in base assembly)
      if (sgrf.m_partitionIndex > 0) {
        cdiINT32 segIndex = sgrf.m_segmentIndex;
        if (segIndex >= 0) {
          cCDI_PARTITION* partition = GetPartition(sgrf.m_partitionIndex);
          if (partition) {
            cCDI_SEGMENT* segment = partition->GetSegment(segIndex);
            if (segment) {
              cCDI_PARTIAL_PART* partialPart = segment->FindPartialPart(regionIndex);
              if (!partialPart)
                partialPart = &(segment->CreatePartialPart(regionIndex));
              partialPart->AddAssignedFaceIndex(face->index);
            }
          }
        }
      }
    }
  }
  partEntity.AddFace(face->index);
}

void
cCDI_PARTITIONS::UpdateSegmentMembershipOldCases(cdiINT32 regionIndex, const std::string& regionName, CDI_FACE_OLD face)
{
  // This method needs to be called by clients that are parsing very old CDIs (pre v3.0)
  // that have the CDI_FACE_OLD chunk
  // In old CDIs (< 0.8?), faces did not have indices stored. So just count up
  cdiINT32 faceIndex = face->index;
  if (faceIndex < 0)
    faceIndex = GetFaceEntityCount();
  cCDI_FACE_ENTITY& faceEntity = GetFaceEntity(faceIndex);
  cCDI_PART_ENTITY& partEntity = GetPartEntity(regionIndex);
  // For these old CDIs UpdateSegmentMembershipForRegion was not called. So the part entity
  // could have just been created so assign the name and it needs a segment assignment in the base assembly
  if (partEntity.GetName().empty()) {
    partEntity.SetName(regionName);
    cCDI_SEGMENT_REF sgrf(0, 0);  // Root of the Base Assembly
    partEntity.AddSegmentAssignment(sgrf);
    cCDI_PARTITION* partition = GetPartition(0);
    if (partition) {
      cCDI_SEGMENT* segment = partition->GetSegment(0);
      if (segment)
        segment->AddAssignedRegionIndex(regionIndex);
    }
  }
  // In old CDIs (< 0.8?), faces did not have names.
  std::string faceName;
  if (face->name == NULL || strlen(face->name) == 0)
    faceName = exa_printf_strdup("face%d", faceIndex);
  else
    faceName = face->name;
  // Need to remove the part name and the "." delimiter
  std::string partPrefix = partEntity.GetName() + OLD_FACE_DELIMITER;
  if (faceName.find(partPrefix) == 0)
    faceName.erase(0, partPrefix.length());
  faceEntity.SetName(faceName);
  faceEntity.SetPartIndex(regionIndex);
  faceEntity.SetSurfacePropertyIndex(face->prop);
  partEntity.AddFace(faceIndex);
}

void
cCDI_PARTITIONS::FinalizeCDIRead()
{
  // Need to check and potentially fix partial parts of old cases
  if (m_readingPreV8CDI) {
    for (auto&& partition : m_partitions) {
      partition->FixupPartialParts();
    }
  }
  // V8.0+ versions have assigned regions in the SGMI chunk, which has already been read
  m_partitions[0]->BuildMembershipForChildren(m_readingPreV8CDI);
}

bool
cCDI_PARTITIONS::ReadRegionsAndFacesFromCDI(CDI_INFO cdi_info)
{
  bool foundRegions = false;
  bool foundFaces = false;

  m_readingPreV8CDI = !CDIIsPartitionVersion(cdi_info);
  m_printVersion = GetPrintVersionForCDI(cdi_info->major_version, cdi_info->minor_version);
  CIO_INFO cio = cdi_info->cio_info;

  // Read the regions and faces chunks if the user requested
  auINT64 start_depth = cio->depth - 1;
  auINT64 start_offset = cio->stack[cio->depth - 1].curr_offset;
  bool finishedRead = false;
  CIO_ERRCODE error = CIO_ERR_SUCCESS;
  cdiINT32 regionIndexForFaces = 0;
  std::string regionName;
  do {
    error = cio_descend(cio);
    if (error == CIO_ERR_SUCCESS) {
      CIO_CCCC ctype = cio_get_type(cio);
      if (ctype == CDI_CHUNK_TYPE_RGDF) {
        cdiINT32 regionIndex = 0;
        ccCDI_DO_INNER_CHUNKS(i, "rgdf", cdi_info) {
          if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_RGND) {
            foundRegions = true;
            sCDI_RGND rgnd;
            cdi_read_rgnd(cdi_info, &rgnd);
            UpdateSegmentMembershipForRegion(&rgnd, regionIndex);
            regionIndex++;
          }
        }
      }
      else if (ctype == CDI_CHUNK_TYPE_REGN) {
        ccCDI_DO_INNER_CHUNKS(regn_child, "regn", cdi_info) {
          if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_FTAB) {
            ccCDI_DO_INNER_CHUNKS(regn_child, "ftab", cdi_info) {
              if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_FACE) {
                foundFaces = true;
                sCDI_FACE face;
                cdi_read_face(cdi_info, &face);
                UpdateSegmentMembershipForFace(regionIndexForFaces, &face);
              }
            }
          }
          else if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_NAME) {
            CDI_NAME name = cdi_read_name(cdi_info);
            regionName = name->name;
            cdi_destroy_name(name);
          }
          else if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_BODY) {
            // Only necessary for old cases
            if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 0)) {
              ccCDI_DO_INNER_CHUNKS(body_child, "body", cdi_info) {
                if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_SHLL) {
                  ccCDI_DO_INNER_CHUNKS(shll_child, "shll", cdi_info) {
                    if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_FTAB) {
                      ccCDI_DO_INNER_CHUNKS(ftab_child, "ftab", cdi_info) {
                        if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_FACE) {
                          foundFaces = true;
                          CDI_FACE_OLD face = cdi_read_face_old(cdi_info);
                          UpdateSegmentMembershipOldCases(regionIndexForFaces, regionName, face);
                          cdi_destroy_face_old(face);
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        regionIndexForFaces++;
      }
      else if (regionIndexForFaces > 0) {
        // If found a new chunk type and already read regions and faces, then done
        finishedRead = true;
      }
      cio_ascend(cio);
    }
  } while (error != CIO_ERR_FAIL && !finishedRead);
  if (finishedRead && error != CIO_ERR_FAIL) {
    FinalizeCDIRead();
  }
  // Restore the reader back to where it was
  cio->stack[start_depth].curr_offset = start_offset;
  fseekf(cio->fp, start_offset, SEEK_SET);
  
  return (error != CIO_ERR_FAIL) && (foundRegions && foundFaces);
}

cdiINT32
cCDI_PARTITIONS::GetPartitionIndex(const cCDI_PARTITION& partition) const
{
  cdiINT32 partitionIndex = 0;
  for (auto&& uPartition : m_partitions) {
    if (&partition == uPartition.get())
      break;
    ++partitionIndex;
  }
  return partitionIndex < (cdiINT32)m_partitions.size() ? partitionIndex : -1;
}

cCDI_PARTITION*
cCDI_PARTITIONS::GetPartition(cdiINT32 partitionIndex) const
{
  cCDI_PARTITION* returnPartition = NULL;
  if (partitionIndex >= 0 && partitionIndex < (cdiINT32)m_partitions.size())
    returnPartition = m_partitions[partitionIndex].get();
  return returnPartition;
}

cCDI_PARTITION*
cCDI_PARTITIONS::GetPartition(const std::string& partitionName) const
{
  cCDI_PARTITION* returnPartition = NULL;
  for (auto&& partition : m_partitions)
  {
    if (partition->GetName() == partitionName) {
      returnPartition = partition.get();
      break;
    }
  }
  return returnPartition;
}

std::vector<cCDI_PARTITION *>
cCDI_PARTITIONS::GetPartitions() const
{
  std::vector<cCDI_PARTITION*> partitions;
  partitions.reserve(m_partitions.size());
  for (auto&& cPartition : m_partitions)
    partitions.push_back(cPartition.get());
  return partitions;
}

cCDI_PART_ENTITY&
cCDI_PARTITIONS::GetPartEntity(cdiINT32 partIndex)
{
  auto it = m_partEntities.find(partIndex);
  if (it == m_partEntities.end()) {
    cCDI_PART_ENTITY newPartEntity(partIndex);
    it = m_partEntities.insert(std::make_pair(partIndex, newPartEntity)).first;
  }
  return it->second;
}

const cCDI_PART_ENTITY&
cCDI_PARTITIONS::GetPartEntity(cdiINT32 partIndex) const
{
  return m_partEntities.at(partIndex);
}

cCDI_FACE_ENTITY&
cCDI_PARTITIONS::GetFaceEntity(cdiINT32 faceIndex)
{
  auto it = m_faceEntities.find(faceIndex);
  if (it == m_faceEntities.end()) {
    cCDI_FACE_ENTITY newFaceEntity(faceIndex);
    it = m_faceEntities.insert(std::make_pair(faceIndex, newFaceEntity)).first;
  }
  return it->second;
}

const cCDI_FACE_ENTITY&
cCDI_PARTITIONS::GetFaceEntity(cdiINT32 faceIndex) const
{
  return m_faceEntities.at(faceIndex);
}

std::vector<cdiINT32>
cCDI_PARTITIONS::GetPartFaceList(cdiINT32 partIndex) const
{
  return GetPartEntity(partIndex).GetFaceList();
}

std::string
cCDI_PARTITIONS::GetShortPartName(cdiINT32 partIndex) const
{
  return GetPartEntity(partIndex).GetName();
}

std::string
cCDI_PARTITIONS::GetShortFaceName(cdiINT32 faceIndex, bool includePartName) const
{
  const cCDI_FACE_ENTITY& faceEntity = GetFaceEntity(faceIndex);
  if (includePartName)
    return GetShortPartName(faceEntity.GetPartIndex()) + FACE_DELIMITER + faceEntity.GetName();

  return faceEntity.GetName();
}

std::string
cCDI_PARTITIONS::GetPartName(cdiINT32 partIndex) const
{
  // A part is always explicitly assigned in the base assembly so can take advantage of segment assignments
  const cCDI_PART_ENTITY& partEntity = GetPartEntity(partIndex);
  sSEGMENT_ASSIGNMENT partSeg = partEntity.GetSegmentAssignment(GetBaseAssembly());
  std::string segPath = partSeg.segment->GetName(true);
  return segPath + partEntity.GetName();
}

std::string
cCDI_PARTITIONS::GetFaceName(cdiINT32 faceIndex) const
{
  const cCDI_FACE_ENTITY& faceEntity = GetFaceEntity(faceIndex);
  return GetPartName(faceEntity.GetPartIndex()) + FACE_DELIMITER + faceEntity.GetName();
}

// Global Helper Methods

cdiBOOLEAN cdi_read_segment_reference_list(CDI_INFO cdi_info, std::vector<cCDI_SEGMENT_REF>& sgel)
{
  CIO_INFO cio = cdi_info->cio_info;
  asINT32 nSegRefs = cio_get_count(cio);
  for (int i = 0; i < nSegRefs; i++)
  {
    cio_descend(cio);
    cCDI_SEGMENT_REF sgrf(-1, -1);
    sgrf.ReadFromCDI(cdi_info);
    sgel.push_back(sgrf);
    cio_ascend(cio);
  }
  return TRUE;
}

cdiBOOLEAN cdi_write_segment_reference_list(CDI_INFO cdi_info, const std::vector<cCDI_SEGMENT_REF>& sgel)
{
  for (cCDI_SEGMENT_REF sgrf : sgel) {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SGRF) {
      sgrf.WriteToCDI(cdi_info);
    }
  }

  return TRUE;
}

cdiBOOLEAN cdi_read_partial_part_reference_list(CDI_INFO cdi_info, std::vector<cCDI_PARTIAL_PART_REF>& pprl)
{
  CIO_INFO cio = cdi_info->cio_info;
  asINT32 nPPRefs = cio_get_count(cio);
  for (int i = 0; i < nPPRefs; i++)
  {
    cio_descend(cio);
    cCDI_PARTIAL_PART_REF pprf(-1, -1);
    pprf.ReadFromCDI(cdi_info);
    pprl.push_back(pprf);
    cio_ascend(cio);
  }
  return TRUE;
}

cdiBOOLEAN cdi_write_partial_part_reference_list(CDI_INFO cdi_info, const std::vector<cCDI_PARTIAL_PART_REF>& pprl)
{
  for (cCDI_PARTIAL_PART_REF pprf : pprl) {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_PPRF) {
      pprf.WriteToCDI(cdi_info);
    }
  }

  return TRUE;
}

void cdi_read_partitions_test(const std::string& cdiFilename)
{
  // This is a method that can be used to read a CDI for the partitions
  // information. It also goes through the regions and faces to build
  // the membership information.
  cCDI_PARTITIONS partitions;
  CDI_INFO cdi_info = cdi_open_for_read(cdiFilename.c_str(), NULL);
  cdiINT32 regionIndexForFaces = 0;
  ccCDI_DO_INNER_CHUNKS(case_child, "case", cdi_info) {
    if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_PSDF) {
      partitions.ReadFromCDI(cdi_info);
    }
    else if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_RGDF) {
      cdiINT32 regionIndex = 0;
      ccCDI_DO_INNER_CHUNKS(i, "rgdf", cdi_info) {
        CIO_CCCC chunkType = cdi_get_type(cdi_info);
        if (chunkType == CDI_CHUNK_TYPE_RGND) {
          CDI_RGND rgnd = EXA_CALLOC_STRUCT(CDI_RGND);
          cdi_read_rgnd(cdi_info, rgnd);
          partitions.UpdateSegmentMembershipForRegion(rgnd, regionIndex);
          cdi_empty_rgnd(rgnd);
          exa_free(rgnd);
          regionIndex++;
        }
      }
    }
    else if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_REGN) {
      ccCDI_DO_INNER_CHUNKS(regn_child, "regn", cdi_info) {
        if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_FTAB) {
          ccCDI_DO_INNER_CHUNKS(regn_child, "ftab", cdi_info) {
            if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_FACE) {
              sCDI_FACE face;
              cdi_read_face(cdi_info, &face);
              partitions.UpdateSegmentMembershipForFace(regionIndexForFaces, &face);
            }
          }
        }
      }
      regionIndexForFaces++;
    }
  }
  cdi_close(cdi_info);
  partitions.FinalizeCDIRead();
  // Reading and Building is done. This is to look at results.
  for (cCDI_PARTITION* partition : partitions.GetPartitions()) {
    for (cCDI_SEGMENT* segment : partition->GetSegments()) {
      std::vector<cdiINT32> parts = segment->GetRegionIndices(false);
      std::vector<cdiINT32> faces = segment->GetFaceIndices(false);
    }
    for (cCDI_PARTITION* derivedPartition : partition->GetDerivedPartitions()) {
      std::vector<std::pair<cCDI_SEGMENT*, sSEGMENT_ASSIGNMENT>> segAssigns = 
        partition->GetSegmentAssignments(*derivedPartition);
      std::vector<std::pair<cCDI_PARTIAL_PART*, sSEGMENT_ASSIGNMENT>> partialAssign = 
        partition->GetPartialPartAssignments(*derivedPartition);
      std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> regionAssing =
        partition->GetRegionAssignments(*derivedPartition);
      std::vector<std::pair<cdiINT32, sSEGMENT_ASSIGNMENT>> faceAssign = 
        partition->GetFaceAssignments(*derivedPartition);
    }
  }
}

const cCDI_SEGMENT::DisplayProperties*
cCDI_SEGMENT::getDisplayProperties() const {
   if (m_hasDisplayProperties)
      return &m_displayProperties;
   else
      return nullptr;
}

void cdi_print_gmrf(const cCDI_GEOMETRY_REF& gmrf, std::ostream &output, int depth, cdiINT32 majorVersion, cdiINT32 minorVersion)
{
  PrintElementStart(output, true, true);
  depth++;
  // Selection Type
  PrintAnnotatedEnum(output, gmrf.GeometrySelectionType(), "geometry selection type", "", depth);
  // Reference Partition
  if (CDI_VERSION_AT_LEAST(majorVersion, minorVersion, 8, 6))
    PrintAnnotatedInt(output, gmrf.PartitionIndex(), "partition index", depth);
  // Face list
  PrintAnnotatedFlst(output, gmrf.face_list, depth);
  // Region list
  PrintAnnotatedRgns(output, gmrf.rgn_list, depth);
  // Partial part list
  PrintAnnotatedPartialPartList(output, gmrf.partial_part_list, depth);
  // Segment list
  PrintAnnotatedSegmentList(output, gmrf.segment_list, depth);
  // Prohibited Geometry
  if (CDI_VERSION_AT_LEAST(majorVersion, minorVersion, 8, 7)) {
    std::vector<cdiINT32> geomIndices;
    if (!gmrf.prohibited_selections.empty()) {
      geomIndices.reserve(gmrf.prohibited_selections.size());
      for (cdiINT32 prohibit : gmrf.prohibited_selections)
        geomIndices.push_back(prohibit);
    }
    if (gmrf.GeometrySelectionType() == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face)
      PrintAnnotatedFlst(output, geomIndices, depth);
    else if (gmrf.GeometrySelectionType() == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part)
      PrintAnnotatedRgns(output, geomIndices, depth);
  }
  
  depth--;
  PrintElementEnd(output, true, depth);
}

void cdi_read_gmrf(cCDI_GEOMETRY_REF* gmrf, cdiINT32 majorVersion, cdiINT32 minorVersion)
{
  lexer_parse_lbrace();

  sINT32 intValue;
  ReadAnnotatedEnum(&intValue);
  gmrf->GeometrySelectionType((cCDI_GEOMETRY_REF::eGEOMETRY_TYPE)intValue);
  if (CDI_VERSION_AT_LEAST(majorVersion, minorVersion, 8, 6)) {
    cdiINT32 partitionIndex;
    ReadAnnotatedInt(&partitionIndex);
    gmrf->PartitionIndex(partitionIndex);
  }
  ReadAnnotatedFlst(gmrf->face_list);
  ReadAnnotatedRgns(gmrf->rgn_list);
  ReadAnnotatedPartialPartList(gmrf->partial_part_list);
  ReadAnnotatedSegmentList(gmrf->segment_list);
  // Prohibited Geometry
  if (CDI_VERSION_AT_LEAST(majorVersion, minorVersion, 8, 7)) {
    std::vector<cdiINT32> geomIndices;
    if (gmrf->GeometrySelectionType() == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face) {
      ReadAnnotatedFlst(geomIndices);
    }
    else if (gmrf->GeometrySelectionType() == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) {
      ReadAnnotatedRgns(geomIndices);
    }
    for (cdiINT32 geom : geomIndices)
      gmrf->prohibited_selections.insert(geom);
  }

  lexer_parse_rbrace();
}

void cdi_print_geos(const cCDI_GEOM_SELECTION_TREE& geos, std::ostream &output, int depth, bool printHeader, cdiINT32 majorVersion, cdiINT32 minorVersion)
{
  if (printHeader) {
    PrintElementStart(output, true, true);
    depth++;
    PrintAnnotatedInt(output, majorVersion, "major version", depth);
    PrintAnnotatedInt(output, minorVersion, "minor version", depth);
    depth--;
    PrintElementEnd(output, true, depth);
  }

  PrintElementStart(output, true, true);
  depth++;
  
  PrintAnnotatedInt(output, geos.PartitionIndex(), "partition index", depth);
  // Selections
  char type_str[10];
  cio_type_to_string(geos.m_selections.GetChunkType(), type_str);
  PrintIndent(output, depth);
  output << type_str;
  cdi_print_gmrf(geos.m_selections, output, depth, majorVersion, minorVersion);
  // Exclusions
  cio_type_to_string(geos.m_exclusions.GetChunkType(), type_str);
  PrintIndent(output, depth);
  output << type_str;
  cdi_print_gmrf(geos.m_exclusions, output, depth, majorVersion, minorVersion);

  depth--;
  PrintElementEnd(output, true, depth);
}

bool cdi_read_geos(std::istream &input, cCDI_GEOM_SELECTION_TREE* geos, cdiINT32* majorVersion, cdiINT32* minorVersion)
{
  if (!geos)
    return false;

  std::ostringstream ostr;
  input >> ostr.rdbuf();
  STRING pstring = EXA_STRDUP(ostr.str().c_str());
  lexer_set_scan_string(pstring);
  lexer_set_lineno(1);

  // Print header with version
  lexer_parse_lbrace();
  ReadAnnotatedInt(majorVersion);
  ReadAnnotatedInt(minorVersion);
  lexer_parse_rbrace();
  // Print the geos chunk
  cdi_read_geos(geos, *majorVersion, *minorVersion);

  return true;
}

void cdi_read_geos(cCDI_GEOM_SELECTION_TREE* geos, cdiINT32 majorVersion, cdiINT32 minorVersion)
{
  lexer_parse_lbrace();
  cdiINT32 partitionIndex;
  ReadAnnotatedInt(&partitionIndex);
  geos->PartitionIndex(partitionIndex);
  // Selections
  lexer_parse_specific_id(geos->m_selections.GetChunkType());
  cdi_read_gmrf(&(geos->m_selections), majorVersion, minorVersion);
  // Exclusions
  lexer_parse_specific_id(geos->m_exclusions.GetChunkType());
  cdi_read_gmrf(&(geos->m_exclusions), majorVersion, minorVersion);

  lexer_parse_rbrace();
}

static
bool GetGeometryByPath(const cCDI_PARTITIONS& partitions, const std::string& geometryPath, cCDI_GEOMETRY_REF* geomRef)
{
  auto FindGeomInSegment = [&partitions](cCDI_SEGMENT* segment, const std::string& partName, const std::string& faceName, cCDI_GEOMETRY_REF* geomRef) -> bool {
    bool found = false;
    if (faceName.empty()) {
      std::vector<cdiINT32> regions = segment->GetRegionIndices(false);
      std::string name;
      for (cdiINT32 regId : regions) {
        segment->GetQualifiedPartName(regId, false, &name);
        if (name == partName) {
          found = true;
          geomRef->GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
          geomRef->rgn_list.push_back(regId);
          break;
        }
      }
      // It could be a partial part
      if (!found) {
        for (cCDI_PARTIAL_PART* partialPart : segment->GetPartialParts()) {
          cdiINT32 partIndex = partialPart->GetPartIndex();
          segment->GetQualifiedPartName(partIndex, false, &name);
          if (name == partName) {
            found = true;
            cdiINT32 partialIndex = segment->GetPartition().GetPartialPartIndex(*partialPart);
            geomRef->GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart);
            geomRef->partial_part_list.push_back(cCDI_PARTIAL_PART_REF(geomRef->PartitionIndex(), partialIndex));
          }
        }
      }
    }
    else {
      std::vector<cdiINT32> faces = segment->GetFaceIndices(false);
      std::string name;
      for (cdiINT32 faceId : faces) {
        if (partitions.GetShortFaceName(faceId, false) == faceName) {
          cdiINT32 regId = partitions.GetFaceEntity(faceId).GetPartIndex();
          segment->GetQualifiedPartName(regId, false, &name);
          if (name == partName) {
            found = true;
            geomRef->GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
            geomRef->face_list.push_back(faceId);
            break;
          }
        }
      }
    }
    return found;
  };

  bool geomFound = false;
  if (!geomRef)
    return geomFound;

  geomRef->face_list.clear();
  geomRef->rgn_list.clear();
  geomRef->partial_part_list.clear();
  geomRef->segment_list.clear();

  auto firstSlash = geometryPath.find('/');
  auto lastSlash = geometryPath.rfind('/');
  auto doubleSlash = geometryPath.find("//");
  auto faceDelimiter = geometryPath.rfind(FACE_DELIMITER);
  if (firstSlash != std::string::npos && lastSlash != std::string::npos) {
    // First find the partition
    cCDI_PARTITION* cdiPartition = nullptr;
    if (firstSlash == 0) {
      cdiPartition = &(partitions.GetBaseAssembly());
    }
    else {
      std::string partitionName = geometryPath.substr(0, firstSlash);
      cdiPartition = partitions.GetPartition(partitionName);
    }
    if (!cdiPartition)
      return false;
    cdiINT32 partitionIndex = partitions.GetPartitionIndex(*cdiPartition);
    geomRef->PartitionIndex(partitionIndex);

    const size_t partStart = (doubleSlash < lastSlash ? doubleSlash : lastSlash) + 1;

    // Find segment
    std::string segmentPath = lastSlash == firstSlash ? "/" : geometryPath.substr(firstSlash, partStart - firstSlash);
    std::string partName;
    std::string faceName;

    if (faceDelimiter == std::string::npos) {
      partName = geometryPath.substr(partStart);
    }
    else {
      partName = geometryPath.substr(partStart, faceDelimiter - partStart);
      faceName = geometryPath.substr(faceDelimiter + FACE_DELIMITER.size());
    }
    cCDI_SEGMENT* cdiSegment = cdiPartition->GetSegment(segmentPath);
    if (cdiSegment && !partName.empty()) {
      geomFound = FindGeomInSegment(cdiSegment, partName, faceName, geomRef);
    }
    else if (cdiSegment) {
      geomFound = true;
      cdiINT32 segmentIndex = cdiPartition->GetSegmentIndex(*cdiSegment);
      geomRef->GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment);
      geomRef->segment_list.push_back(cCDI_SEGMENT_REF(partitionIndex, segmentIndex));
    }
  }
  
  return geomFound;
}

bool cdi_get_geometry_by_path(const cCDI_PARTITIONS& partitions, const std::string& geometryPath, cCDI_GEOMETRY_REF* geomRef)
{
  bool isFound = GetGeometryByPath(partitions, geometryPath, geomRef);
  if (!isFound) {
    // If the user hasn't given a type, first try parts
    isFound = cdi_get_geometry_by_shortname(partitions, geometryPath, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part, geomRef);
    // If still not found, try faces
    if (!isFound) {
      isFound = cdi_get_geometry_by_shortname(partitions, geometryPath, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, geomRef);
    }
  }
  return isFound;
}

bool cdi_get_geometry_by_path(const cCDI_PARTITIONS& partitions, const std::string& geometryPath, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE expectedType, cCDI_GEOMETRY_REF* geomRef)
{
  bool isFound = GetGeometryByPath(partitions, geometryPath, geomRef);
  if (!isFound) {
    isFound = cdi_get_geometry_by_shortname(partitions, geometryPath, expectedType, geomRef);
  }
  return isFound;
}

static
bool GetGeometryByOldPath(const cCDI_PARTITIONS& partitions, const std::string& geometryPath, cCDI_GEOMETRY_REF* geomRef)
{
  // TEMPORARY: This is needed for the GFDS chunk which writes out face names in the CDI. When powercase
  // updates writing the GFDS, then this can be deleted.
  // This find is necessary because part and face paths can be ambiguous. A part or face name can contain
  // a '.' character, which is also the delimiter between a part and face. Until this changes, we need
  // to do a search. Unfortunately, this can fail if a part has the same name as an existing "partname.facename".
  // That would be highly unusual so hopefully no one did that.
  auto FindGeomInSegment = [&partitions](cCDI_SEGMENT* segment, const std::string& partName, const std::string& faceName, cCDI_GEOMETRY_REF* geomRef) -> bool {
    bool found = false;
    if (faceName.empty()) {
      std::vector<cdiINT32> regions = segment->GetRegionIndices(false);
      std::string name;
      for (cdiINT32 regId : regions) {
        segment->GetQualifiedPartName(regId, false, &name);
        if (name == partName) {
          found = true;
          geomRef->GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
          geomRef->rgn_list.push_back(regId);
          break;
        }
      }
      // It could be a partial part
      if (!found) {
        for (cCDI_PARTIAL_PART* partialPart : segment->GetPartialParts()) {
          cdiINT32 partIndex = partialPart->GetPartIndex();
          segment->GetQualifiedPartName(partIndex, false, &name);
          if (name == partName) {
            found = true;
            cdiINT32 partialIndex = segment->GetPartition().GetPartialPartIndex(*partialPart);
            geomRef->GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart);
            geomRef->partial_part_list.push_back(cCDI_PARTIAL_PART_REF(geomRef->PartitionIndex(), partialIndex));
          }
        }
      }
    }
    else {
      std::vector<cdiINT32> faces = segment->GetFaceIndices(false);
      std::string name;
      for (cdiINT32 faceId : faces) {
        if (partitions.GetShortFaceName(faceId, false) == faceName) {
          cdiINT32 regId = partitions.GetFaceEntity(faceId).GetPartIndex();
          segment->GetQualifiedPartName(regId, false, &name);
          if (name == partName) {
            found = true;
            geomRef->GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
            geomRef->face_list.push_back(faceId);
            break;
          }
        }
      }
    }
    return found;
  };

  bool geomFound = false;
  if (!geomRef)
    return geomFound;

  geomRef->face_list.clear();
  geomRef->rgn_list.clear();
  geomRef->partial_part_list.clear();
  geomRef->segment_list.clear();

  auto firstSlash = geometryPath.find('/');
  auto lastSlash = geometryPath.rfind('/');
  auto lastDot = geometryPath.rfind('.');
  if (firstSlash != std::string::npos && lastSlash != std::string::npos) {
    // First find the partition
    cCDI_PARTITION* cdiPartition = nullptr;
    if (firstSlash == 0) {
      cdiPartition = &(partitions.GetBaseAssembly());
    }
    else {
      std::string partitionName = geometryPath.substr(0, firstSlash);
      cdiPartition = partitions.GetPartition(partitionName);
    }
    if (!cdiPartition)
      return false;
    cdiINT32 partitionIndex = partitions.GetPartitionIndex(*cdiPartition);
    geomRef->PartitionIndex(partitionIndex);
    // Find segment
    std::string segmentPath = lastSlash == firstSlash ? "/" : geometryPath.substr(firstSlash, lastSlash - firstSlash);
    std::string partName;
    std::string faceName;
    if (lastDot == std::string::npos) {
      partName = geometryPath.substr(lastSlash + 1);
    }
    else {
      partName = geometryPath.substr(lastSlash + 1, lastDot - lastSlash - 1);
      faceName = geometryPath.substr(lastDot + 1);
    }
    cCDI_SEGMENT* cdiSegment = cdiPartition->GetSegment(segmentPath);
    if (cdiSegment && !partName.empty()) {
      geomFound = FindGeomInSegment(cdiSegment, partName, faceName, geomRef);
      // If not found, then there could be '.' characters in the part or face name
      if (!geomFound) {
        std::string partAndFaceName = geometryPath.substr(lastSlash + 1);
        partName = partAndFaceName;
        faceName = "";
        size_t next = partAndFaceName.size();
        while (!geomFound && !partName.empty()) {
          geomFound = FindGeomInSegment(cdiSegment, partName, faceName, geomRef);
          next = partAndFaceName.rfind('.', next - 1);
          partName = next != std::string::npos ? partAndFaceName.substr(0, next) : "";
          faceName = next != std::string::npos ? partAndFaceName.substr(next + 1) : "";
        }
      }
    }
    else if (cdiSegment) {
      geomFound = true;
      cdiINT32 segmentIndex = cdiPartition->GetSegmentIndex(*cdiSegment);
      geomRef->GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment);
      geomRef->segment_list.push_back(cCDI_SEGMENT_REF(partitionIndex, segmentIndex));
    }
  }
  
  return geomFound;
}

bool cdi_get_geometry_by_old_path(const cCDI_PARTITIONS& partitions, const std::string& geometryPath, cCDI_GEOMETRY_REF* geomRef)
{
  bool isFound = GetGeometryByOldPath(partitions, geometryPath, geomRef);
  if (!isFound) {
    // If the user hasn't given a type, first try parts
    isFound = cdi_get_geometry_by_shortname(partitions, geometryPath, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part, geomRef);
    // If still not found, try faces
    if (!isFound) {
      isFound = cdi_get_geometry_by_shortname(partitions, geometryPath, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, geomRef);
    }
  }
  return isFound;
}

bool cdi_get_geometry_by_old_path(const cCDI_PARTITIONS& partitions, const std::string& geometryPath, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE expectedType, cCDI_GEOMETRY_REF* geomRef)
{
  bool isFound = GetGeometryByOldPath(partitions, geometryPath, geomRef);
  if (!isFound) {
    isFound = cdi_get_geometry_by_shortname(partitions, geometryPath, expectedType, geomRef);
  }
  return isFound;
}

bool cdi_get_geometry_by_shortname(const cCDI_PARTITIONS& partitions, const std::string& geometryName, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE expectedType, cCDI_GEOMETRY_REF* geomRef)
{
  bool geomFound = false;
  // Do not bother if someone passed in path
  if (!geomRef || geometryName.find('/') != std::string::npos)
    return geomFound;
  // Do not bother if someone is asking for a segment or partial part
  if (expectedType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment ||
      expectedType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart)
    return geomFound;

  geomRef->face_list.clear();
  geomRef->rgn_list.clear();
  geomRef->partial_part_list.clear();
  geomRef->segment_list.clear();

  if (expectedType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) {
    std::vector<cdiINT32> allRegions = partitions.GetBaseAssembly().GetRoot().GetRegionIndices(true);
    for (cdiINT32 regionIndex : allRegions) {
      if (partitions.GetShortPartName(regionIndex) == geometryName) {
        geomFound = true;
        geomRef->PartitionIndex(0);
        geomRef->GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
        geomRef->rgn_list.push_back(regionIndex);
      }
    }
  }
  else if (expectedType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face) {
    // For a face, the user could have passed "partname::facename", "partname.facename", "facename"
    auto faceDelimiter = geometryName.rfind(FACE_DELIMITER);
    std::vector<cdiINT32> allFaces = partitions.GetBaseAssembly().GetRoot().GetFaceIndices(true);
    bool isNewFormat = faceDelimiter != std::string::npos;
    for (cdiINT32 faceIndex : allFaces) {
      std::string partAndFaceName;
      if (isNewFormat) {
        partAndFaceName = partitions.GetShortFaceName(faceIndex, true);
      }
      else {
        const cCDI_FACE_ENTITY& faceEntity = partitions.GetFaceEntity(faceIndex);
        partAndFaceName = partitions.GetShortPartName(faceEntity.GetPartIndex()) + OLD_FACE_DELIMITER + faceEntity.GetName();
      }
      // Check if the name is partName<delimiter>facename
      // or if the user asked for face using the only facename that has a "." in it
      if ((partAndFaceName == geometryName) || (partitions.GetShortFaceName(faceIndex, false) == geometryName)) {
        geomFound = true;
        geomRef->PartitionIndex(0);
        geomRef->GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
        geomRef->face_list.push_back(faceIndex);
      }
    }
  }

  return geomFound;
}

namespace{

std::string indentString(size_t indent) {
   return std::string(indent, ' ');
}

bool testSegment(cCDI_SEGMENT& segment, bool abortOnFailure, std::ostream& os, size_t indent) {
  // Segment
  std::string qualifiedSegmentName = segment.GetName(true);
  if (!segment.GetPartition().IsBaseAssembly())
     qualifiedSegmentName = segment.GetPartition().GetName() + qualifiedSegmentName;
  cCDI_GEOMETRY_REF geomRef(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment);
  const bool ok = cdi_get_geometry_by_path(segment.GetPartition().GetOwner(), qualifiedSegmentName, &geomRef) && (geomRef.segment_list.size() == 1) && geomRef.segment_list[0].GetSegment(segment.GetPartition().GetOwner()) == &segment;
  os << indentString(indent) << "Segment \"" << qualifiedSegmentName << "\" [" << (ok ? "OK" : "FAILED") << "]\n";
  if (!ok && abortOnFailure)
    return false;

  // Child Segments
  for (auto childSegment : segment.GetChildSegments()) {
    if (!testSegment(*childSegment, abortOnFailure, os, indent + 1))
      return false;
  }

  // Faces
  for (int faceIndex : segment.GetFaceIndices(false)) {
    std::string qualifiedName;
    segment.GetQualifiedFaceName(faceIndex, true, &qualifiedName);
    cCDI_GEOMETRY_REF geomRef(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
    const bool ok = cdi_get_geometry_by_path(segment.GetPartition().GetOwner(), qualifiedName, &geomRef) && (geomRef.face_list.size() == 1) && geomRef.face_list[0] == faceIndex;
    os << indentString(indent + 1) << "Face \"" << qualifiedName << "\" [" << (ok ? "OK" : "FAILED") << "]\n";
    if (!ok && abortOnFailure)
      return false;
  }

  // Parts
  for (int partIndex : segment.GetRegionIndices(false)) {
    std::string qualifiedName;
    segment.GetQualifiedPartName(partIndex, true, &qualifiedName);
    cCDI_GEOMETRY_REF geomRef(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
    const bool ok = cdi_get_geometry_by_path(segment.GetPartition().GetOwner(), qualifiedName, &geomRef) && (geomRef.rgn_list.size() == 1) && geomRef.rgn_list[0] == partIndex;
    os << indentString(indent + 1) << "Part \"" << qualifiedName << "\" [" << (ok ? "OK" : "FAILED") << "]\n";
    if (!ok && abortOnFailure)
      return false;
  }

  // Partial Parts - TODO
  for (auto partialPart : segment.GetPartialParts()) {
    //std::string qualifiedName = ...;
    //cCDI_GEOMETRY_REF geomRef(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart);
    //const bool ok = cdi_get_geometry_by_path(segment.GetPartition().GetOwner(), qualifiedName, &geomRef) && (geomRef.rgn_list.size() == 1) && &geomRef.partial_part_list[0].GetPartialPart(segment.GetPartition().GetOwner()) == partialPart;
    //os << indentString(indent + 1) << "Partial Part \"" << qualifiedName << "\" [" << (ok ? "OK" : "FAILED") << "]\n";
    //if (!ok && abortOnFailure)
    //  return false;
  }

  return true;
};
} //namespace


bool cdi_test_qualified_names(const cCDI_PARTITIONS& partitions, bool abortOnFailure, std::ostream& os) {
   for (const auto& partition : partitions.GetPartitions()) {
      os << "Partition \"" << partition->GetName() << "\"\n";
      if (!testSegment(partition->GetRoot(), abortOnFailure, os, 1))
         return false;
   }
   return true;
}
