/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#include "common.h"
#include "cp_info.h"
#include "rotational_dynamics.h"

dFLOAT g_timestep_per_lat_time_inc;
sFLOAT g_user_max_vel;

dFLOAT sCP_ROTATIONAL_DYNAMICS_DESC::torque_about_axis()
{
  dFLOAT axis_origin_rel_ref_pt[3],
         torque_about_axis_origin[3],
         temp[3];

  // Torque_B = Torque_A + (r_A - r_B) x Force_A
  vsub(axis_origin_rel_ref_pt, cp_info.sri_lrfs[m_lrf_index].axis_origin, m_window->cdi_meas_window->reference_point);
  vcross(temp, axis_origin_rel_ref_pt, m_force);
  vadd(torque_about_axis_origin, m_torque, temp);

  return vdot(torque_about_axis_origin, cp_info.sri_lrfs[m_lrf_index].axis_direction);
}

void sCP_ROTATIONAL_DYNAMICS_DESC::read_user_inputs()
{
  const asINT32 LINE_BUFFER_SIZE = 160;
  char in_line[LINE_BUFFER_SIZE];
  const char *delim = " ,\t\r\n";
  const char *input_file = "user_input.init";

  FILE *fp = fopen(input_file, "r");
  if (fp != NULL) {
    while (fgets(in_line, LINE_BUFFER_SIZE-1, fp) != NULL) {
      if (in_line[0] == '#' ) {
        continue;
      }
      else if (in_line[0] == '$') {
        printf("A $ sign is found, closing the %s file.\n", input_file);
        fclose (fp);
        return;
      }
      else {
        //sscanf(in_line, "%s %s %s", parm_type, parm_name, parm_value);
        char *parm_type = strtok(in_line, delim);
        if (parm_type == NULL) //empty line
          continue;
        char *parm_name = strtok(NULL, delim);
        char *parm_value = strtok(NULL, delim);

        if (strcmp(parm_name, "initial_mi_coeff")==0) {
          m_initial_mi_coeff = atof(parm_value);
          printf("%s = %f \n", parm_name, m_initial_mi_coeff);
        }
        else if (strcmp(parm_name, "cur_alpha_weight")==0) {
          m_cur_alpha_weight = atof(parm_value);
          printf("%s = %f \n", parm_name, m_cur_alpha_weight);
        }
        else if (strcmp(parm_name, "mi_decrease_factor")==0) {
          m_mi_decrease_factor = atof(parm_value);
          printf("%s = %f \n", parm_name, m_mi_decrease_factor);
        }
        else if (strcmp(parm_name, "min_mi_ratio")==0) {
          m_min_mi_ratio = atof(parm_value);
          printf("%s = %f \n", parm_name, m_min_mi_ratio);
        }
      }
    }
    printf("End of file reached, closing the %s file.\n",  input_file);
    fclose (fp);
  }
}

void sCP_ROTATIONAL_DYNAMICS_DESC::compute_angular_acceleration(asINT32 idx = 0)
{
  dFLOAT res_trq = (m_is_resistive_torque_time_varying)? m_resistive_torque[idx] : m_resistive_torque[0];
  dFLOAT ext_trq = (m_is_external_torque_time_varying)?  m_external_torque[idx]  : m_external_torque[0];
  dFLOAT aero_ext_torque = torque_about_axis() + ext_trq;

  m_angular_acceleration_m1 = m_angular_acceleration;
  // friction cannot cause a fan to accelerate. Hence, if the fan is at or near 
  // rest, and if the magnitude of friction exceeds the other forces, set alpha/effective torque to be zero
  if (fabs(m_omega) < 1e-15) {
    if (fabs(res_trq) > fabs(aero_ext_torque)) {
      m_tau = 0;
    } else {
      // if the fan is at rest and is made to accelerate, the resistive force acts 
      // in the direction opposite to the initial angular acceleration/velocity
      m_tau = aero_ext_torque - ((aero_ext_torque > 1e-15) - (aero_ext_torque < -1e-15))*res_trq;
    }
  } else { // non-zero angular velocity
    // in this case, the resistive force always acts in the direction opposite to the angular velocity
    m_tau = aero_ext_torque - ((m_omega > 1e-15) - (m_omega < -1e-15))*res_trq;
  }
  m_angular_acceleration = m_tau/m_moment_of_inertia;
  m_omega_next = m_omega + m_angular_acceleration*g_timestep_per_lat_time_inc*(m_window->cdi_meas_window->fluid_time_desc.period);
  if (m_omega_next*m_omega < 0) {
    m_angular_acceleration = -m_omega/(g_timestep_per_lat_time_inc*(m_window->cdi_meas_window->fluid_time_desc.period));
  }
}

void sCP_ROTATIONAL_DYNAMICS_DESC::update_dynamics()
{
  if(m_is_time_accurate) {
    if (m_is_resistive_torque_time_varying || m_is_external_torque_time_varying) {
      if (m_trq_idx == 0 || m_window->m_output_timestep - cp_info.restart_time <= m_window->cdi_meas_window->fluid_time_desc.period) {
        asINT32 tag = make_mpi_tag<eMPI_MSG::ROTDYN>(m_window->rotational_dynamics_index);
        if (m_is_resistive_torque_time_varying) {
          MPI_Wait(&m_resistive_torque_mpi_request, MPI_STATUS_IGNORE);
          ccDOTIMES(i, NUM_TIME_VARYING_TORQUE_VALUES) {
            m_resistive_torque[i] = m_resistive_torque_buffer[i];
          }
          xMPI_Irecv(m_resistive_torque_buffer,
                     NUM_TIME_VARYING_TORQUE_VALUES,
                     eMPI_dFLOAT, 0, tag, eMPI_sp_cp_comm,
                     &m_resistive_torque_mpi_request);
        }
        if (m_is_external_torque_time_varying) {
          MPI_Wait(&m_external_torque_mpi_request, MPI_STATUS_IGNORE);
          ccDOTIMES(i, NUM_TIME_VARYING_TORQUE_VALUES) {
            m_external_torque[i] = m_external_torque_buffer[i];
          }
          xMPI_Irecv(m_external_torque_buffer,
                     NUM_TIME_VARYING_TORQUE_VALUES,
                     eMPI_dFLOAT, 0, tag, eMPI_sp_cp_comm,
                     &m_external_torque_mpi_request);
        }
      }
      compute_angular_acceleration(m_trq_idx % NUM_TIME_VARYING_TORQUE_VALUES);
      m_trq_idx++;
    }
    else {
      compute_angular_acceleration();
    }
  }
  else { // steady-state algorithm
    if (m_count == 0) {
      // initialize constants and starting moment of inertia
      m_char_MI = (cdi_data.char_density*m_max_radius_lrf*m_max_radius_lrf*m_max_radius_lrf*m_max_radius_lrf*m_max_radius_lrf);
      m_omega_max = (g_user_max_vel - cdi_data.char_vel)/m_max_radius_lrf;
      m_moment_of_inertia = m_initial_mi_coeff*m_char_MI;
    }
    
    m_tau_m1 = m_tau;
    m_tau_abs_m1 = m_tau_abs;
    compute_angular_acceleration();
    m_tau_abs = fabs(m_tau);
    
    if (m_count == 0) {
      m_tau_abs_m1 = m_tau_abs;
    } else {
      // decrease MI (because tau is heading in the right direction)
      if (m_tau_abs < m_tau_abs_m1) {
        m_moment_of_inertia /= m_mi_decrease_factor;
        if (m_moment_of_inertia/m_char_MI < m_min_mi_ratio) {
          m_moment_of_inertia = m_min_mi_ratio*m_char_MI;
        }
      }
      // under-relax the angular acceleration
      m_angular_acceleration = (1.0-m_cur_alpha_weight)*m_angular_acceleration_m1 + m_cur_alpha_weight*m_angular_acceleration;
    }

    // check if m_omega_next satisfies the numerical stability limit
    if (fabs(m_omega_next) > m_omega_max) {
      m_angular_acceleration = 0.0;
    }
    else {
      m_omega = m_omega_next;
    }
  }

  m_count++;
}

void sCP_ROTATIONAL_DYNAMICS_DESC::do_rotational_dynamics()
{
  if(m_window->m_output_timestep >= m_start_time && m_window->m_output_timestep <= m_end_time) {
    if (m_window->is_meas_vars_output_dp) {
      ccDOTIMES(k, m_window->n_variables) {
        switch(m_window->var_types[k]) {
          case SRI_VARIABLE_XFORCE:  m_force[0]  = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriDOUBLE>* >(m_window)->m_meas_variable_sums[k]; break;
          case SRI_VARIABLE_YFORCE:  m_force[1]  = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriDOUBLE>* >(m_window)->m_meas_variable_sums[k]; break;
          case SRI_VARIABLE_ZFORCE:  m_force[2]  = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriDOUBLE>* >(m_window)->m_meas_variable_sums[k]; break;
          case SRI_VARIABLE_XTORQUE: m_torque[0] = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriDOUBLE>* >(m_window)->m_meas_variable_sums[k]; break;
          case SRI_VARIABLE_YTORQUE: m_torque[1] = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriDOUBLE>* >(m_window)->m_meas_variable_sums[k]; break;
          case SRI_VARIABLE_ZTORQUE: m_torque[2] = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriDOUBLE>* >(m_window)->m_meas_variable_sums[k]; break;
          default: break;                           
        }
      }
    }
    else {
      ccDOTIMES(k, m_window->n_variables) {
        switch(m_window->var_types[k]) {
          case SRI_VARIABLE_XFORCE:  m_force[0]  = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriFLOAT>* >(m_window)->m_meas_variable_sums[k]; break;
          case SRI_VARIABLE_YFORCE:  m_force[1]  = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriFLOAT>* >(m_window)->m_meas_variable_sums[k]; break;
          case SRI_VARIABLE_ZFORCE:  m_force[2]  = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriFLOAT>* >(m_window)->m_meas_variable_sums[k]; break;
          case SRI_VARIABLE_XTORQUE: m_torque[0] = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriFLOAT>* >(m_window)->m_meas_variable_sums[k]; break;
          case SRI_VARIABLE_YTORQUE: m_torque[1] = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriFLOAT>* >(m_window)->m_meas_variable_sums[k]; break;
          case SRI_VARIABLE_ZTORQUE: m_torque[2] = static_cast< tCP_REDUCTION_MEAS_WINDOW<sriFLOAT>* >(m_window)->m_meas_variable_sums[k]; break;
          default: break;                           
        }
      }
    }
    update_dynamics();
    ccDOTIMES(proc, total_sps) {
      MPI_Wait(&m_mpi_requests[proc], MPI_STATUS_IGNORE);
      TIMESTEP solver_end_time = cp_info.end_time;
      if(m_window->is_cond_window())
        solver_end_time = cp_info.convert_to_ts_cond(cp_info.end_time);
      else
        solver_end_time = cp_info.convert_to_ts_flow(cp_info.end_time);
      if (m_window->m_output_timestep != solver_end_time) {
        asINT32 tag = make_mpi_tag<eMPI_MSG::ROTDYN>(m_window->rotational_dynamics_index);
        MPI_Isend(&m_angular_acceleration,
                  1, eMPI_dFLOAT, proc, tag, eMPI_sp_cp_comm,
                  &m_mpi_requests[proc]);
      }
    }
  }
}
