#define _STRINGIFY(x) #x
#define STRINGIFY(x) _STRINGIFY(x)

//#define ICONFILE icons/ ## APPNAME ## .ico

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.

//AppIcon ICON STRINGIFY(ICONFILE)

// Dirc suggests the following to specify the correct binary type
#ifdef IS_DLL
# define FILE_SUFFIX ".dll"
# define ORIG_FILENAME STRINGIFY(INTERNAL_NAME) FILE_SUFFIX
# define FILE_TYPE 0x2L
#else
# ifdef IS_PYD
#  define FILE_SUFFIX ".pyd"
#  define ORIG_FILENAME "_" STRINGIFY(INTERNAL_NAME) FILE_SUFFIX
#  define FILE_TYPE 0x2L
# else
#  define FILE_SUFFIX ".exe"
#  define ORIG_FILENAME STRINGIFY(INTERNAL_NAME) FILE_SUFFIX
#  define FILE_TYPE 0x1L
# endif
#endif



1 VERSIONINFO
// FILEVERSION 1, 0, 0, 0
// PRODUCTVERSION 1, 0, 0
 FILEFLAGSMASK 0x3fL
 FILEFLAGS 0x0L
 FILEOS 0x4L
 FILETYPE FILE_TYPE
 FILESUBTYPE 0x0L
BEGIN
 BLOCK "StringFileInfo"
 BEGIN
  BLOCK "040904B0"
  BEGIN
   VALUE "CompanyName", "Dassault Systemes Simulia Corp.\0"
   VALUE "FileDescription", STRINGIFY(FILE_DESCRIPTION) "\0"
   VALUE "InternalName", STRINGIFY(INTERNAL_NAME) "\0"
   VALUE "LegalCopyright", "Copyright (C) " STRINGIFY(CREATION_YEAR) "-" STRINGIFY(CURRENT_YEAR) " Dassault Systemes Simulia Corp.\0"
   VALUE "OriginalFilename", ORIG_FILENAME "\0"
   VALUE "ProductName", STRINGIFY(APPNAME) "\0"
  END
 END
 BLOCK "VarFileInfo"
 BEGIN
  VALUE "Translation", 0x409, 1200
 END
END

