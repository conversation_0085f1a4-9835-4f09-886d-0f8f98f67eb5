/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2007 Exa Corporation.                                   ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("copyright.text", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("copyright.text", "78") */ 
#ifndef CDI_EXPORT_H
#define CDI_EXPORT_H

#include CIO_H

typedef short cdiINT16;
typedef cioINT32 cdiINT32;
typedef cioUINT32 cdiUINT32;
typedef cioINT64 cdiINT64;
typedef int cdiBOOLEAN;
typedef double cdiDOUBLE;

enum CDI_IO_MODE {
  CDI_IO_MODE_UNDEFINED,
  CDI_IO_MODE_READ,
  CDI_IO_MODE_WRITE
};

#define CDI_ERR_OK                              0
#define CDI_ERR_OPEN_READ_FAILED                1
#define CDI_ERR_OPEN_READ_NOT_CDI               2
#define CDI_ERR_OPEN_READ_CDI_INCOMPLETE        3
#define CDI_ERR_OPEN_READ_ADVANCED_VERSION      4
#define CDI_ERR_OPEN_READ_CDI_DAMAGED           5
#define CDI_ERR_OPEN_WRITE_FAILED               6
#define CDI_ERR_OPEN_WRITE_UNK_VERSION          7
#define CDI_ERR_BAD_STRUCT                      8

typedef struct sCDI_INFO {
  CIO_INFO cio_info;
  CDI_IO_MODE io_mode;

  cdiINT32 major_version;
  cdiINT32 minor_version;
  cdiINT32 encryption_on; // 0 means no, 1 means yes, -1 I don't know yet
                               // Value is at -1 until the PTGE chunk is read
  cdiINT64 cdi_file_id;
  void *cipher;
  void *check_pointer;
  
  void init() {
    cio_info = NULL;
    io_mode = CDI_IO_MODE_UNDEFINED;
    major_version = -1;
    major_version = -1;
    encryption_on = -1;
    cdi_file_id = -1;
    cipher = NULL;
    check_pointer = NULL;
  }    
} *CDI_INFO;

#define	ASSERT_VALID_CDI_INFO(cdi_info) assert((cdi_info)->check_pointer == (cdi_info))
#define	cdi_major_version(p)	(((CDI_INFO) (p))->major_version)
#define	cdi_minor_version(p)	(((CDI_INFO) (p))->minor_version)

#include "cdi_readwrite.h"
#include "cdi_encrypted_io.h"
#include "cdi_interface.h"
#include "cdi_physics.h"
#include "cdi_get.h"
#include "cdi_accessers.h"
#include "cdi_cPRESSURE_DROP_PARSER.h"
#include "cdi_cLSR_QUADRATIC_SOLVER.h"
#include "cdi_tempDepParms.h"
#include "cdi_partitions.h"
#include "cCDI_READER.h"
#include "cCDI_GEOMETRY_GENERATOR.h"
#include "cCDI_GEOMETRY_READER.h"

#endif /* CDI_EXPORT_H */

