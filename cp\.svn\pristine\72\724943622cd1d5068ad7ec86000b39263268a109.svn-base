/* ~~~COPY<PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#ifndef TRAJECTORY_WINDOW_H
#define TRAJECTORY_WINDOW_H

#include CP_H
#include PRI_H
#include CDI_H

#include "window.h"
#include "particle_sim_cp.h"

extern BOOLEAN g_has_trajectory_window;
inline BOOLEAN has_trajectory_window() { return g_has_trajectory_window; }

extern std::vector<asINT32> g_cdi_to_pri_emitter_ids;
extern std::vector<asINT32> g_simulator_to_cdi_emitter_ids;
extern std::vector<asINT32> g_simulator_to_pri_emitter_ids;
extern std::vector<PRI::EMITTER_TYPE> g_simulator_emitter_id_to_pri_emitter_types; 

typedef class sCP_TRAJECTORY_WINDOW : public sCP_MEAS_WINDOW
{

 private:

  asINT32 m_cdi_window_index;
  std::string m_name;
  PRI::cPARAMETERS m_pri_parameters;
  PRI::cPMR_FILE m_pmr;
  asINT32 m_checkpoint_table_index;
  
 public:

  //PR41929:
  //These need to be initialzed to zero once it's know how many emitters
  //there are (including the merged emitter).
  //If resuming from a checkpoint, they need to be set to the number of pre existing 
  //parent or child table entries existing in the PRI file.
  std::vector<std::size_t> m_parent_indices_offset; //one element per emitter
  std::vector<std::size_t> m_child_indices_offset;

  sCP_TRAJECTORY_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index,
                        cBOOLEAN is_meas_vars_output_double_precision,
                        std::string name) :
  sCP_MEAS_WINDOW(dgf_window, window_index, is_meas_vars_output_double_precision)
    {
      meas_window_type = LGI_TRAJECTORY_WINDOW;
      m_is_particle_trajectory_window = TRUE;
      m_cdi_window_index = dgf_window->cdi_meas_window_index;
      m_checkpoint_table_index = -1; 
      n_meas_cells = 0;  //The discretizer sets this to 1 but 0 is needed to keep the window from being insterted into 
                         //the output queue (PR)
      m_name = name;
    }

  PRI::cPMR_FILE &pmr_file() { 
    if(!m_pmr.IsOpen()) {
      open_pmr_file();
    }
    return m_pmr; 
  }
  
  ~sCP_TRAJECTORY_WINDOW() {
    if(m_pmr.IsOpen())
      m_pmr.Close();
  }

  void finish_init();
  void write_parameters();
  void open_pmr_file(); 
  void close_pmr_file() {
    if(!m_pmr.IsOpen())
      return;
    m_pmr.Close();
  }

  sINT32 create_checkpoint_table_entry(asINT32 ckpt_timestep) {
    pmr_file().CreateCheckPoint((double)ckpt_timestep); 
    m_checkpoint_table_index++;
    return(m_checkpoint_table_index);
  }

  asINT32 current_checkpoint_index() {return m_checkpoint_table_index;}

  VOID set_checkpoint_index(asINT32 checkpoint_table_index){
    m_checkpoint_table_index = checkpoint_table_index;
  }

  //Overloads base class' function.

  //This sync implementation does nothing because the proper implementation is not thread safe and I caught it being 
  //called by window_output from the main thread.
  VOID sync(WALLCLOCK_TIME_SECS time_secs) {};
  VOID actual_sync(WALLCLOCK_TIME_SECS time_secs);  //This method actually does the synching

#if 1
  //None of this is needed for trajectory windows but were pure virtual in the base class.
  //Need to make a new base class which other windows can be derived from.
  VOID allocate_sp_meas_cells(){}
  BOOLEAN has_sp_var_data_arrived() {return TRUE;}
  SRI_STATUS open_sri_file(SRI_FILE *sri_file_return) {return SRI_SUCCESS;}
  SRI_STATUS write_sri_header(){ return SRI_SUCCESS; }
  sriFLOAT   *cell_scale_factors(){ return NULL;}
  sriFLOAT   cell_scale_factor_5g(){ return 1.0; }

  VOID _precompute_stationary_cell_scale_factors() {}
  VOID _precompute_moving_cell_scale_factors() {}
  VOID write_sri_variables(BOOLEAN some_std_dev_var, sriFLOAT time_scale){}
  VOID post_var_data_recvs() {}
  //VOID post_recvs() {}
  VOID init_clear_and_output_times() {}

  //These two were made virtual in the base class when trajectory support was added
  VOID write_sri_results(WALLCLOCK_TIME_SECS time_secs){} 
  VOID open_sri_file_for_resume(){}
  VOID insert_in_output_queue(){}
#endif

}* CP_TRAJECTORY_WINDOW;


typedef class sCP_TRAJECTORY_WINDOW sCP_TRAJECTORY_WINDOW_SFLOAT;
typedef class sCP_TRAJECTORY_WINDOW sCP_TRAJECTORY_WINDOW_DFLOAT;


PRI::PARTICLE_SOURCE convert_particle_source(asINT32 event_type);

#endif
