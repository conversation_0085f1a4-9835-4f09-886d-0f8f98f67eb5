/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Measurement windows
 *
 * James Hoch, Exa Corporation 
 * Created April, 2009
 *--------------------------------------------------------------------------*/

#ifndef __WINDOW_H
#define __WINDOW_H

#include VMEM_VECTOR_H
#include <map>

#include "common.h"
#include "table.h"
#include "cp_lattice.h"
#include "particle_sim_cp.h"

#ifdef _EXA_MPI
#ifndef _EXA_HPMPI
#include "MPI_mapping.h"
#endif
#endif

#define eMPI_MEAS_CELL_INDEX eMPI_sINT32 /* definition needs to be consistent w/ STP_MEAS_CELL_INDEX */

#define eMPI_REDUCTION_FLOAT eMPI_dFLOAT
typedef dFLOAT REDUCTION_MEAS_CELL_VAR;
typedef size_t STP_MEAS_COORD_INDEX;

typedef struct sPOST_MEAS_CMD {
  cSTRING        meas_window_name;
  sINT32         issue_frame; 	// the next frame after which to issue the cmd
  sINT32         period; 	// in frames (-1 means one-time event)
  cSTRING        cmd_string;
  sPOST_MEAS_CMD *next;
  TABLE_DESC     table_desc;
} *POST_MEAS_CMD;

typedef struct sCDI_MEAS_WINDOW_VAR_SET {
  SRI_VARIABLE_TYPE *var_types; // vector of var types
  sINT32            n_vars;     // number of vars
} *CDI_MEAS_WINDOW_VAR_SET;

typedef struct sMEAS_SURFEL_BLOCK_MAP {
	sINT32 start_surfel_this_file;
	sINT32 start_surfel_other_file;
	sINT32 n_surfels;
	sMEAS_SURFEL_BLOCK_MAP() {
		start_surfel_this_file = 0;
		start_surfel_other_file = 0;
		n_surfels = 0;
	}
} *MEAS_SURFEL_BLOCK_MAP;

typedef struct sCDI_MEAS_WINDOW {
  // This struct is cleared on allocation, which is relied upon to properly initialize some fields.
  cSTRING    name;
  sCDI_MSTP  mstp;
  cBOOLEAN   flush_every_frame;

  cBOOLEAN   is_average_mme;
  
  cBOOLEAN   start_time_via_monitors_p; // Whether to star the measurement using the monitor
  cBOOLEAN   meas_started_p;            // Whether the meas window is already started
  std::vector<asINT32>  master_monitors;   // Indices of monitors used to start this meas window.

  asINT32    n_master_monitors_to_check;   // Number of monitors not reached end of init transient yet.
                                           // Initially it equals master_monitors.size(). Once it reaches 0, the 
                                           // meas window should start.

  eCDI_MEAS_END_TIME_VIA::Enum meas_end_time_via; 
  TIMESTEP   duration;
  TIMESTEP   specified_end_time;
   
  asINT32    rank;                  // Rank in the monitor/meas_window dependency tree

  // overridden_by_meas_timing indicates whether the next 4 timing params were overridden by 
  // a --meas_timing command line option. If this simulation is subsequently restored from 
  // checkpoint, these parameters must be taken from the ckpt file, not from the CDI file.
  cBOOLEAN   overridden_by_meas_timing;
  cBOOLEAN   disabled_by_meas_include_exclude;
  dFLOAT     start_time;
  dFLOAT     end_time;
  dFLOAT     average_interval;
  dFLOAT     period;
  // min_start_time is set to the ckpt restart time if start_time is overridden by a --meas_timing
  // command line option; otherwise it is zero.
  TIMESTEP   min_start_time;
  sINT32     num_frames;
  sINT32     period_sync_group_index;

  cSTP_SCALE coarsest_fluid_shob_scale;
  cSTP_SCALE coarsest_cond_shob_scale;
  sINT8      n_meas_files; 	 	// a CDI meas pset may correspond to several meas files
  sINT8      n_meas_files_written;      // counter tracking each frame

  POST_MEAS_CMD cmd_queue;
  // one_window is an index, not a pointer because the array of CP meas windows may be moved
  // during initialization.
  STP_MEAS_WINDOW_INDEX one_window;	// one of the windows related to this CDI meas window

  // Used by monitors to find the associated CP meas windows
  STP_MEAS_WINDOW_INDEX fluid_meas_window_index;
  STP_MEAS_WINDOW_INDEX porous_meas_window_index;
  STP_MEAS_WINDOW_INDEX surface_meas_window_index;
  STP_MEAS_WINDOW_INDEX shell_meas_window_index;
  STP_MEAS_WINDOW_INDEX solid_meas_window_index; // conduction solid

  eCDI_COUPLED_SOLVER::Enum start_time_defined_by;

  sTIME_DESC fluid_time_desc;   	        // start, end, average, and period after rounding of fluid meas windows
  sTIME_DESC cond_time_desc;   	        // start, end, average, and period after rounding of conduction meas windows
  sTIME_DESC superseded_time_desc;   	// original time_desc that has been overridden by --meas_timing option
  cBOOLEAN   is_time_desc_superseded;   // has original time_desc been overridden by --meas_timing option
  cBOOLEAN   superseded_time_desc_warning_issued;
  cBOOLEAN   is_rwnc_mean_vel_added;
  
  dFLOAT     min_pressure;              // for composite force windows, the user-defined filter
  dFLOAT     max_pressure;

  dFLOAT     reference_point[3];        // for composite moments windows, the default reference point

  dFLOAT     seg_size;                  // development window info
  sINT32     icsys;
  sINT32     ilrf;

  sCDI_MEAS_WINDOW_VAR_SET fluid_var_set;
  sCDI_MEAS_WINDOW_VAR_SET conduction_solid_var_set;
  sCDI_MEAS_WINDOW_VAR_SET porous_var_set;
  sCDI_MEAS_WINDOW_VAR_SET surface_var_set;
  sCDI_MEAS_WINDOW_VAR_SET shell_var_set;

  STP_ACTIVE_SOLVER_MASK   solver_mask;
  sINT32                   lcm_flow_tsteps;  // least common multiple (lcm) of timesteps of all associated flow solvers
  sINT32                   lcm_cond_tsteps;  // least common multiple (lcm) of timesteps of all associated cond solvers

  VOID compute_rounded_time_desc(); // only called for coupling windows
  VOID insert_post_meas_cmd_in_queue(POST_MEAS_CMD entry);

  asINT32 cyclic_buffer_size;

  // Find the end of meas window once we know the end init transient.
  VOID find_meas_window_end_time_via_monitors(TIMESTEP end_init_transient);
  // Start all CP meas windows associated with this cdi meas window
  VOID start_meas_windows(TIMESTEP end_init_transient_detected_time, 
                          TIMESTEP end_init_transient);
  BOOLEAN mated_flow_conduction_surface_files();
  VOID rename_tmp_meas_files();
  dFLOAT period_in_cond_ts();
  dFLOAT end_time_in_cond_ts();
  dFLOAT avg_int_in_cond_ts();
  dFLOAT start_time_in_cond_ts();
  dFLOAT period_in_flow_ts();
  dFLOAT end_time_in_flow_ts();
  dFLOAT avg_int_in_flow_ts();
  dFLOAT start_time_in_flow_ts();


  // tag curved heat exchanger inlet meas window, and store its inlet face
  // facet offset
  cBOOLEAN is_curved_hx_inlet;
  sINT32 inlet_facet_offset;

    // indicate whether the measurement window is the first one in the coupling timestep table
  BOOLEAN is_not_first_coupling_phase;

  // Flag this window if it is used for particle trajectory measurements
  BOOLEAN m_is_particle_trajectory_window;

  // Also, store the particle trajectory options
  BOOLEAN m_record_trajectories;
  BOOLEAN m_record_velocities;
  BOOLEAN m_use_dynamic_decimation;
  dFLOAT m_decimation_tolerance;
  asINT32 m_static_decimation_rate;
  HITPOINT_OPTIONS m_hitpoint_options;
  BOOLEAN m_record_only_adhering_hitpoints;
  BOOLEAN m_record_hitpoint_normal_impulse;
  BOOLEAN m_record_normal_impulse;
  BOOLEAN m_mated_flow_conduction_surface_files;
  dFLOAT m_fraction_eligible_for_measurement; //Added to support changes for CDI 3.2 (2/28/17 -wanderer)

  BOOLEAN m_per_emitter_particle_measurements;
  BOOLEAN m_per_material_particle_measurements;

  cCDI_GEOM_SELECTION_TREE* m_geom_selection;


} *CDI_MEAS_WINDOW;


// This is temporary storage used to remap the vertex indices of a meas window from the global
// table of vertices to the local table found in a surface meas file.
typedef struct sMEAS_WINDOW_VERTEX_MAP {
  sriINT *m_map;
  
  // The create() method is called as the very last step in initialization. We could
  // defer allocation of this memory until the first frame of a surface meas window is written
  // (this is the first time the memory is used), but we like to know if we've run out of
  // memory as early as possible.
  VOID create();
  VOID destroy() { 
    delete m_map; 
    m_map = NULL;
  }

  sriINT &operator[] (asINT32 index) { return m_map[index]; }

  // clear() sets all entries in the "map" to -1
  VOID clear();

  sMEAS_WINDOW_VERTEX_MAP() { m_map = NULL; }

} *MEAS_WINDOW_VERTEX_MAP;
  

extern sMEAS_WINDOW_VERTEX_MAP g_meas_window_vertex_map;

typedef dFLOAT SP_MEAS_CELL_VAR;

enum eQUEUE_TYPE {
  FLOW_OUTPUT_QUEUE,
  COND_OUTPUT_QUEUE,
  ROTDYN_OUTPUT_QUEUE,
  NUM_QUEUES
};

enum eMT_QUEUE_TYPE {
  POST_RECVS_QUEUE,
  WRITE_SRI_RESULTS_QUEUE,
  NUM_MT_QUEUES
};

typedef struct sCP_MEAS_WINDOW {
  
  // Declare friend functions and classes responsible for checkpointing
  friend VOID write_meas_windows(TIMESTEP ckpt_timestep);
  friend struct sCP_DGF_READER;

  sINT8                 meas_cell_scale;
  cBOOLEAN              is_per_voxel;
  cBOOLEAN              is_composite;
  cBOOLEAN              is_development;
  cBOOLEAN              is_valid;               // set to FALSE once we are past the window's end time

  cBOOLEAN              is_average_mme;

  LGI_MEAS_WINDOW_TYPE  meas_window_type;
  STP_MEAS_WINDOW_INDEX index;
  STP_MEAS_WINDOW_INDEX cdi_window_index;
  SRI_FILE_TYPE         sri_file_type;

  SRI_FILE              sri_file;
  sriINT                prev_nsets_written;     // the number of frames already written to the file
  // number of frames already written to meas files at start of simulation (0 if not checkpoint restore)
  sriINT                initial_prev_nsets_written;
  sriINT                first_set_to_write_in_file; // Used for meas windows started by monitors

  sriINT                min_bound[3];           // LLR of meas window bounding box
  sriINT                max_bound[3];           // URF of meas window bounding box

  STP_PROC              m_master_sp;
  sINT16                n_variables;
  SRI_VARIABLE_TYPE     *var_types;
  sINT16                *population_var_indices;// index of vars used to normalize each var (should be all -1 for non particle simulations which means no normalization)
  sINT16 *m_var_component_ids; //These IDs distinguishes between quantities with non unique var types used for multi-component measurements (like per-emitter or per-material particle measurements).

  cBOOLEAN m_is_particle_trajectory_window;

  sriPOINT              n_meas_cells;
  sriPOINT              n_stationary_meas_cells;
  sriPOINT              n_moving_meas_cells;

  STRING                output_filename;
  cBOOLEAN              file_per_frame_p;       // output format is header file + 1 file per frame
  cBOOLEAN              is_output_in_local_csys; 
  cBOOLEAN              is_meas_vars_output_dp;
  cBOOLEAN              calc_htc_for_adb_walls;  //calculate htc_XRA for adiabatic wall  

  cBOOLEAN              is_probe;
  cBOOLEAN              coupling_window_p;      // is window used for surface coupling
  sINT16                coupling_model_index;   // surface coupling model index

  sINT16                rotational_dynamics_index; // rotational dynamics descriptor index

  // We store the measurement scale_factors value in volumes for fluid files, surfel_areas 
  // for surface files, face_areas for composite surface files, and part_volumes for
  // composite fluid files.
  cBOOLEAN              stationary_scale_factors_precomputed_p;
  cBOOLEAN              moving_scale_factors_precomputed_p;
  cBOOLEAN              m_some_meas_cell_spans_procs;

  // The number of SPs that contribute to the meas window
  STP_PROC              m_n_participating_sps;

  TIMESTEP              m_clear_timestep;       // beginning of avg interval
  TIMESTEP              m_output_timestep;      // end of averaging interval
  sMEAS_UPDATE_TIME     m_next_update_time;     // next clear and output timesteps

  /* Indices of flow monitors which this CP meas window could contribute to */
  std::vector<asINT32>  m_flow_monitors;
  std::vector<asINT32>  m_solid_monitors;

  /* Reduction tree for meas window data */
  BOOLEAN use_tree_reduction() { return (is_composite || is_development); }

 cExaMsg<sGRF_MEAS_FRAME_SP_TO_CP_MSG, 1> global_nirf_info_msg;

 cExaMsg<sLRF_MEAS_FRAME_SP_TO_CP_MSG, 1> *lrf_info_msg;

 cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1> *mbc_info_msg;

 cExaMsg<sMOVB_MEAS_FRAME_SP_TO_CP_MSG, 1> *movb_info_msg;

  sGRF_MEAS_FRAME_SP_TO_CP_MSG *grf_return_buffer();
  sLRF_MEAS_FRAME_SP_TO_CP_MSG *lrf_return_buffer(int N);
  sMBC_MEAS_FRAME_SP_TO_CP_MSG *mbc_return_buffer(int N);
  sMOVB_MEAS_FRAME_SP_TO_CP_MSG *movb_return_buffer(int N);

  sINT64              n_bytes_per_frame;
  sINT64              n_bytes_since_last_sync;
  WALLCLOCK_TIME_SECS m_time_of_last_sync;
  
  CDI_MEAS_WINDOW       cdi_meas_window;

  // base_frame is the CP's notion of the first frame written to the meas file. Usually
  // this is equivalent to SRI's notion of current meas frame, but will be different
  // when a simulation is restored from ckpt and the meas file was missing.
  sINT32                base_frame;

  // This is the number of empty slots in the sri file. Need to store it here since the 
  // number in the sri file may not reflect the real number at checkpoint time. When restoring
  // from ckpt, read this number from ckpt file while sri_file is not available, and then use 
  // it to set sri_file->m_n_empty_cyclic_buffer_slots later. 
  asINT32               n_empty_cyclic_buffer_slots;
  std::vector<asINT32> 	n_surfels;
  std::vector<asINT32> 	first_surfel;
  std::vector<asINT32> 	first_point;
  std::vector<asINT32> 	n_surfels_with_two_meas;

  STP_MEAS_CELL_INDEX   *m_sp_n_meas_cells;// m_sp_n_meas_cells[n_sps]
  STP_MEAS_CELL_INDEX   *m_n_moving_meas_cells_per_sp; // [n_sps]

  MPI_Request           *moving_meas_count_receive_requests; 
  MPI_Request           *moving_meas_data_receive_requests; 


  sCP_MEAS_WINDOW *m_next_in_queue[NUM_QUEUES]; // next window in queue. See eQUEUE_TYPE for the types of queues

#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  virtual VOID insert_in_queue(eQUEUE_TYPE queue_type);
#else
  VOID insert_in_output_queue();
#endif

  cSTRING type_name()
  {
    static char s[48];
    sprintf(s, "%s%s%s",
            is_composite ? "composite " : "",
            lgi_meas_window_type_to_name(meas_window_type),
            is_probe ? " probe" : "");
    return s;
  }         

  BOOLEAN is_per_voxel_for_scale(asINT32 voxel_scale) 
  { 
    return (is_per_voxel
            && (sim_is_scale_same_or_finer(meas_cell_scale, voxel_scale))); 
  }

public:
  std::vector<sriLRF_INDEX>  m_ref_frame_indices;     // ref_frame_indices[n_meas_cells] (ref frame index of each meas cell)

  // This is called after all ublk and surfel meas cell refs have been processed, at which
  // point we have final values in m_sp_n_meas_cells.
  virtual VOID allocate_sp_meas_cells() = 0;
  VOID allocate_ref_frame_indices();

  BOOLEAN has_all_sp_data_arrived();
  virtual BOOLEAN has_sp_var_data_arrived() = 0;
  VOID compute_bytes_per_frame() {
    if (is_meas_vars_output_dp)  
      n_bytes_per_frame = n_meas_cells * n_variables * sizeof(sriDOUBLE);
    else 
      n_bytes_per_frame = n_meas_cells * n_variables * sizeof(sriFLOAT);
  }
  BOOLEAN is_cond_window() {
    if(meas_window_type == LGI_VOLUME_WINDOW || meas_window_type == LGI_SHELL_WINDOW || meas_window_type == LGI_SAMPLING_SHELL_WINDOW)
      return true;
    else
      return false;
  }
  //Append a single variable type to the existing set of measurement variables.
  VOID append_var_type(SRI_VARIABLE_TYPE var_type, sINT16 population_var_index = -1, sINT16 var_component_id = -1);

private:
  STRING compose_output_filename();  

public:
  // constructor
  sCP_MEAS_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index, cBOOLEAN is_meas_vars_output_dp);

  STRING compose_output_pathname();

  VOID warn_about_meas_window_timing_changes_and_maybe_set_prev_nsets_written();

  // This cannot be called until the CDI meas window time descs are updated, which cannot 
  // occur until after ALL the DGF meas windows are read.
#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  virtual VOID init_clear_and_output_times(); //made virtual to support trajectory windows.
#else
  VOID init_clear_and_output_times();
#endif

  virtual SRI_STATUS open_sri_file(SRI_FILE *sri_file_return) = 0;
  virtual SRI_STATUS write_sri_header() = 0;
  virtual sriFLOAT   *cell_scale_factors() = 0;
  virtual sriFLOAT   cell_scale_factor_5g() = 0;
  virtual VOID       finish_init() = 0;
  virtual VOID       _precompute_stationary_cell_scale_factors() = 0;
  virtual VOID       _precompute_moving_cell_scale_factors() = 0;

  virtual VOID       allocate_window_data_for_sri() { }
  virtual VOID       composite_moving_meas_cells() { }

#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  virtual VOID write_sri_results(WALLCLOCK_TIME_SECS time_secs);
  virtual VOID open_sri_file_for_resume();
#else
  VOID write_sri_results(WALLCLOCK_TIME_SECS time_secs);
  VOID open_sri_file_for_resume();
#endif
  virtual VOID write_sri_variables(BOOLEAN some_std_dev_var, sriFLOAT time_scale) = 0;

  VOID post_recvs();
  virtual VOID post_var_data_recvs() = 0;

  virtual VOID sync(WALLCLOCK_TIME_SECS time_secs); //specialized or trajectory windows
    
  // We store measurement scale factors in volumes for fluid files, surfel_areas 
  // for surface files, face_areas for composite surface files, and part_volumes for
  // composite fluid files.

  VOID precompute_cell_scale_factors()
  {
    if (!stationary_scale_factors_precomputed_p) {
      _precompute_stationary_cell_scale_factors();
      stationary_scale_factors_precomputed_p = TRUE;
    }

    if (!moving_scale_factors_precomputed_p) {
      _precompute_moving_cell_scale_factors();
      moving_scale_factors_precomputed_p = TRUE;
    }
  }

  // Calculate z-dimenstion for 5g dynamic data processing (DDP)
  sriFLOAT compute_z_dimension() 
  { 
    return (sriFLOAT)(max_bound[2] - min_bound[2]);
  }

  // SP meas cell references...
public:
  static VOID validate_sp_meas_cell_index_pair_layout()
  {
    if (sizeof(STP_PROC) != 2
        || sizeof(STP_MEAS_CELL_INDEX) != 4)
      msg_internal_error("Assumption in sSP_MEAS_CELL_REF violated.");
  }

  // This struct is ultimately used inside sSP_MEAS_CELL_REF_LIST_ELT where
  // it is union'ed with a pointer. We reserve the low bit of both the pointer
  // and this object to indicate whether we have this object or a pointer to 
  // a list of these objects. 
  struct sSP_MEAS_CELL_REF {
  private:
    // The low bit of m_sp_meas_cell_offset is reserved for use in
    // sSP_MEAS_CELL_REF_LIST_ELT.
  #if SCALAR_LITTLE_ENDIAN
    // SP meas cell byte offset - set by resolve_sp_meas_cell_ptrs
    sINT64              m_sp_meas_cell_offset : 48; 
    STP_PROC            m_sp : 16;
  #else
    STP_PROC            m_sp : 16;
    sINT64              m_sp_meas_cell_offset : 48; 
  #endif

  public:
    STP_PROC sp()                { return m_sp; }
    VOID     set_sp(STP_PROC sp) { m_sp = sp; }
    // function overloaded for single and double floats
    sriDOUBLE *sp_meas_cell(sriDOUBLE *base) { return (sriDOUBLE *)((char *)base + m_sp_meas_cell_offset); }
    sriFLOAT  *sp_meas_cell(sriFLOAT *base)  { return (sriFLOAT *)((char *)base + m_sp_meas_cell_offset); }
    VOID      set_sp_meas_cell_offset(sriDOUBLE *base, sriDOUBLE *cell) { m_sp_meas_cell_offset = (char *)cell - (char *)base; }
    VOID      set_sp_meas_cell_offset(sriFLOAT *base, sriFLOAT *cell) { m_sp_meas_cell_offset = (char *)cell - (char *)base; }
  };
  
  struct sSP_MEAS_CELL_REF_LIST_ELT {
    sSP_MEAS_CELL_REF_LIST_ELT *m_next;
    sSP_MEAS_CELL_REF           m_ref;
    sSP_MEAS_CELL_REF &ref() { return m_ref; }
  };

  union uSP_MEAS_CELL_REF_OR_LIST {
  private:
    sSP_MEAS_CELL_REF_LIST_ELT *m_list;
    sSP_MEAS_CELL_REF           m_ref;

  public:
    // List corresponds to the low bit set, allowing us to simply zero these objects to initialize
    BOOLEAN is_list() { return scalar_mask_pointer_to_int(m_list, 1); }
    BOOLEAN is_ref()  { return !is_list(); }

    sSP_MEAS_CELL_REF_LIST_ELT *list()               { return (sSP_MEAS_CELL_REF_LIST_ELT *)scalar_mask_pointer(m_list, ~1); }
    VOID set_list(sSP_MEAS_CELL_REF_LIST_ELT *list)  { m_list = (sSP_MEAS_CELL_REF_LIST_ELT *)scalar_or_pointer(list, 1); }

    sSP_MEAS_CELL_REF &ref()                         { return m_ref; }
  };

  // For each meas cell, m_sp_meas_cell_refs contains either a single SP meas cell ref, or 
  // a list of SP meas cell refs. The low bit in the pointer distinguishes between the two.
  //
  // During initialization, we initially store just an SP in each
  // reference instead of an SP meas cell pointer. This is necessary because the storage for
  // the SP meas cells is not allocated until initialization is complete. At that point, we
  // add an SP meas cell pointer in resolve_sp_meas_cell_ptrs().

  uSP_MEAS_CELL_REF_OR_LIST *m_sp_meas_cell_refs; // m_sp_meas_cell_refs[n_stationary_meas_cells]

protected:

  VOID allocate_meas_cell_refs() {
    // We zero the SP meas cell refs on allocation ensuring the low bit is clear. See type
    // definitions for an explanation of why this is important.
    if (n_stationary_meas_cells > 0) {
      m_sp_meas_cell_refs = cnew uSP_MEAS_CELL_REF_OR_LIST [n_stationary_meas_cells];
      uSP_MEAS_CELL_REF_OR_LIST *m_sp_meas_cell_ref = m_sp_meas_cell_refs;
      ccDOTIMES(i, n_stationary_meas_cells) {
        m_sp_meas_cell_ref->ref().set_sp(-1); // clear all refs
        m_sp_meas_cell_ref++;
      }
    }
  }

public:

  uSP_MEAS_CELL_REF_OR_LIST *sp_meas_cell_refs() { return m_sp_meas_cell_refs; }

private:
  // These 2 variables serve as a cache for is_meas_cell_on_sp()
  STP_MEAS_CELL_INDEX last_meas_cell_index;
  STP_PROC            last_sp;

public:
  BOOLEAN is_meas_cell_on_sp(STP_MEAS_CELL_INDEX meas_cell_index, asINT32 sp)
  {
    uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &m_sp_meas_cell_refs[meas_cell_index];

    if (ref_or_list->is_list()) {
      // We cache the last cell index we found to optimize for composite meas windows where
      // it may be common for meas cells to span many processors. Since ublks are oct tree 
      // ordered in a DGF file (and surfels roughly oct tree ordered), it is highly likely 
      // to find back-to-back references to the same meas cell by ublks/surfels on the same 
      // SP for a given meas window.
      //
      // We also rely on the fact that as SP meas cell refs are encountered in the DGF file, 
      // they are added to the front of the associated list of refs. This further exploits
      // the assumed locality in the DGF file.

      if ((last_meas_cell_index == meas_cell_index) && (last_sp == sp))
        return TRUE;

      sSP_MEAS_CELL_REF_LIST_ELT *list = ref_or_list->list();
      while (list != NULL) {
        sSP_MEAS_CELL_REF ref = list->m_ref;
        if (ref.sp() == sp) {
          last_meas_cell_index = meas_cell_index;
          last_sp = sp;
          return TRUE;
        }
        list = list->m_next;
      }
    } else {
      sSP_MEAS_CELL_REF &ref = ref_or_list->ref();
      if (ref.sp() == sp)
        return TRUE;
    }
    return FALSE;
  }
  sriINT get_meas_surfel_index(sriINT mci) {
    sriINT msi = 0;
    if(mci == 0)
      return msi;
    asINT32 index = 0;
    sriINT point_distance = 0;
    for(int i = 0; i < first_point.size(); i++) {
      if(mci < (first_point[i] + n_surfels[i] + n_surfels_with_two_meas[i]) ) {
        index = i;
        break;
      }
    }
    point_distance = mci - first_point[index] + 1;
    if(point_distance > n_surfels[index])
      msi = first_surfel[index] + (point_distance - n_surfels[index] - 1);
    else
      msi = first_surfel[index] + point_distance;
    return msi;
  }
private:
  // Support for a very simple pool allocator for sSP_MEAS_CELL_REF_LIST_ELT objects.
  // The scheme does not allow these objects to be freed.
  static sINT32 n_remaining_sp_meas_cell_ref_list_elts;
  static sSP_MEAS_CELL_REF_LIST_ELT *sp_meas_cell_ref_list_elt_pool;

  sSP_MEAS_CELL_REF_LIST_ELT *alloc_sp_meas_cell_ref_list_elt()
  {
    if (n_remaining_sp_meas_cell_ref_list_elts <= 0) {
      // Allocate 128 objects at a time
      sp_meas_cell_ref_list_elt_pool = xnew sSP_MEAS_CELL_REF_LIST_ELT [ 128 ];
      n_remaining_sp_meas_cell_ref_list_elts = 128;
    }
    n_remaining_sp_meas_cell_ref_list_elts--;
    
    return sp_meas_cell_ref_list_elt_pool++;
  }

public:
  VOID add_sp_meas_cell_ref(STP_MEAS_CELL_INDEX meas_cell_index, asINT32 sp)
  {
    uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &m_sp_meas_cell_refs[meas_cell_index];

    if (ref_or_list->is_list()) {
      // Add new entry to list. We add the latest entry to the front of the list because it is easiest
      // and because it allows is_meas_cell_on_sp() to better exploit the locality in the DGF file.
      // See comment in is_meas_cell_on_sp().
      sSP_MEAS_CELL_REF_LIST_ELT *list = ref_or_list->list();
      sSP_MEAS_CELL_REF_LIST_ELT *elt  = alloc_sp_meas_cell_ref_list_elt();
      elt->m_next = list;
      elt->m_ref.set_sp( sp );
      ref_or_list->set_list(elt);
    } else {
      sSP_MEAS_CELL_REF &ref = ref_or_list->ref();
      if (ref.sp() < 0) {
        ref.set_sp(sp);
      } else {
        // create a list
        m_some_meas_cell_spans_procs = TRUE;
        sSP_MEAS_CELL_REF_LIST_ELT *elt1 = alloc_sp_meas_cell_ref_list_elt();
        sSP_MEAS_CELL_REF_LIST_ELT *elt2 = alloc_sp_meas_cell_ref_list_elt();
        
        elt2->m_ref.set_sp( ref.sp() );
        elt2->m_next = NULL;
        elt1->m_ref.set_sp( sp );
        elt1->m_next = elt2;

        ref_or_list->set_list(elt1);
      }
    }
  }
  
  asINT32 first_sp_housing_meas_cell(STP_MEAS_CELL_INDEX meas_cell_index)
  { 
    uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &m_sp_meas_cell_refs[meas_cell_index];

    if (ref_or_list->is_list()) {
      sSP_MEAS_CELL_REF_LIST_ELT *list = ref_or_list->list();
      sSP_MEAS_CELL_REF ref = list->m_ref;
      return ref.sp();
    } else {
      sSP_MEAS_CELL_REF &ref = ref_or_list->ref();
      return ref.sp();
    }
  }

  virtual VOID process_flow_monitors() {};
  virtual VOID process_solid_monitors() {};
  
  template <typename MEAS_FLOAT_TYPE>
  VOID process_flow_monitors_internal();
  
  template <typename MEAS_FLOAT_TYPE>
  VOID process_solid_monitors_internal();

  // For meas windows started via monitors
  VOID start_meas_window(TIMESTEP end_init_transient_detected_time,
                         TIMESTEP end_init_transient);
  VOID rename_tmp_meas_file();

  asINT32 first_frame_after_ckpt;
#ifdef _EXA_MPI
#ifndef _EXA_HPMPI
  struct
  {
    /**
     * Reserve memory for maps and communications
     */
    VOID allocate_maps()
    {
      // Initialize index of the last element of the maps
      last_map_idx = 0;

      // Reserve memory and initialize empty maps with capacity 1024
      sp_maps_src = (MPI_Map_hdf5*) malloc(total_sps * sizeof(MPI_Map_hdf5));
      for (STP_PROC sp = 0; sp < total_sps; ++sp)
        new (sp_maps_src + sp) MPI_Map_hdf5(1024);

      // Reserve memory for essential data
      essential_data = new sESSENTIAL_DATA[total_sps];

      // Reserve memory and fill with null request (finished communication value)
      requests = new MPI_Request[2 * total_sps];
      for (STP_PROC sp = 0; sp < 2 * total_sps; ++sp)
        requests[sp] = MPI_REQUEST_NULL;
    }

    /**
     * Include one more element into the map
     * @param[in] sp: index of the SP where the element exists
     * @param[in] size: size of the element to be included in bytes
     */
    VOID append_to_map(STP_PROC sp, MPI_Map_hdf5::sizes_t size)
    {
      sp_maps_src[sp].appendElement<true>(last_map_idx++, size, 0);
    }

    /**
     * This function sends the maps and the required data to build them to each SP
     */
    VOID open_comm(STP_PROC sp, bool sp_has_meas_cells)
    {
      // Fillinig essential data. This data is required to allocate the memory in the remote
      // SPs and to complete the maps (only a portion of them can be completed from the CP)
      essential_data[sp].map_size = sp_maps_src[sp].size();
      essential_data[sp].map_length = sp_maps_src[sp].length();
      essential_data[sp].last_map_idx = last_map_idx;

      if(sp_has_meas_cells)
      {
        // Sending essential data to each SP. Non-blocking
        MPI_Isend(essential_data + sp,
                sizeof(sESSENTIAL_DATA),
                MPI_BYTE,
                sp,
                eMPI_MEAS_WIN_CP_TO_SP_ESS_TAG,
                eMPI_sp_cp_comm,
                requests + sp);
        // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"DEBUG MPI_Isend eMPI_MEAS_WIN_CP_TO_SP_ESS_TAG to SP%d\n",sp);
      }
      // Sending maps to each SP. Non-blocking (only if there is data in the map to send)
      if (sp_maps_src[sp].length() > 0)
        MPI_Isend(sp_maps_src[sp].getRefToData(),
                  sp_maps_src[sp].size(),
                  MPI_BYTE,
                  sp,
                  eMPI_MEAS_WIN_CP_TO_SP_MAPS_TAG,
                  eMPI_sp_cp_comm,
                  requests + total_sps + sp);
      else
        requests[total_sps + sp] = MPI_REQUEST_NULL;
    }

    /**
     * Check for each map if it has been received. If received, then deallocate the data for the communications.
     * This function is non-blocking and it can be used to free partially the memory while computing other things.
     */
    VOID test_comm()
    {
      // Nothing to do if the data does not exists (probably already sent)
      if (sp_maps_src)
      {
        // Check if all the maps have been received
        int flag;
        MPI_Testall(total_sps, requests + total_sps, &flag, MPI_STATUSES_IGNORE);

        // If all the communications are finished, then deallocate the data
        if (flag)
          deallocate();
      }
    }

    /**
     * Blocking function until send all the maps. It also frees all the memory used
     * for map creation once all the data have been send to each SP.
     */
    VOID close_comm()
    {
      // Nothing to do if the data does not exists (probably already sent)
      if (sp_maps_src)
      {
        MPI_Waitall(total_sps, requests + total_sps, MPI_STATUSES_IGNORE);
        deallocate();
      }
    }

    /**
     * Free memory required to create and send tha maps in the CP
     */
    VOID deallocate()
    {
      // If allocated then free memory
      if (sp_maps_src != nullptr)
      {
#ifdef NDEBUG
        // Before free all the memory, the communications have to be finished
        int flag;
        MPI_Testall(total_sps, requests + total_sps, &flag, MPI_STATUSES_IGNORE);
        assert(flag);
#endif
        // Free memory...
        free(sp_maps_src);
        delete [] essential_data;
        delete [] requests;
      }
      // Reset variables
      sp_maps_src = nullptr;
      requests = nullptr;
      essential_data = nullptr;
      last_map_idx = 0;
    }

    /// Last global index. A requirement to complete the maps by each SP
    MPI_Map_hdf5::indexes_t last_map_idx;
    /// Pointer to the array with all the maps
    MPI_Map_hdf5 * sp_maps_src = nullptr;
    /// Pointer to the array with all the requests for MPI communications
    MPI_Request * requests = nullptr;
    /// Struct with the essential data to be send to each SP before sending the map
    struct sESSENTIAL_DATA
    {
      /// Size in bytes of the maps
      size_t map_size;
      /// Map length
      size_t map_length;
      /// Last global index of the maps
      size_t last_map_idx;
    } * essential_data = nullptr;

  } cp_sp_comm;
#endif
#endif

} *CP_MEAS_WINDOW;

// A meas window that uses a tree reduction
template <typename MEAS_FLOAT_TYPE>
struct tCP_REDUCTION_MEAS_WINDOW : public sCP_MEAS_WINDOW {
  // constructor
  tCP_REDUCTION_MEAS_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index,
                            cBOOLEAN is_meas_vars_output_dp);

  sINT8    m_n_child_sps;  // 0, 1, or 2
  STP_PROC m_n_child_sps_ready;
  STP_PROC m_child_sps[2]; // The SP number is also the MPI rank

  STP_MEAS_CELL_INDEX *m_child_global_meas_cell_indices[2]; // binary tree
  STP_MEAS_CELL_INDEX m_n_child_meas_cells[2];

  // reduction tree always uses dFLOAT for transfer
  REDUCTION_MEAS_CELL_VAR *m_child_meas_cells[2];
  //MEAS_FLOAT_TYPE *m_child_meas_cells[2];
  MPI_Request     m_child_receive_requests[2];

  MEAS_FLOAT_TYPE *m_meas_variable_sums; // this is used to store the sums of all variables over all measurement cells

  MEAS_FLOAT_TYPE *m_moving_meas_cells; // [n_moving_meas_cells * n_variables] contains moving meas data
  std::vector<REDUCTION_MEAS_CELL_VAR> *m_moving_data_recv_buffer; // Recv buffer which contains the global meas cell indices and the data

  // This is used by windows_output to keep track of which moving meas
  // receives have been completed 
  STP_PROC            m_next_sp_moving_index_to_test_for_count;
  STP_PROC            m_next_sp_moving_index_to_test_for_data;
  virtual VOID post_var_data_recvs();

  virtual sriFLOAT  cell_scale_factor_5g() { return 1.0; };

  virtual VOID allocate_sp_meas_cells();
  MEAS_FLOAT_TYPE     *all_moving_meas_cells() { return m_moving_meas_cells; }
  virtual VOID write_sri_variables(BOOLEAN some_std_dev_var, sriFLOAT time_scale);

  bool has_moving_meas_cell_data_arrived();
  bool has_stationary_meas_cell_data_arrived();
  virtual BOOLEAN has_sp_var_data_arrived();

  MEAS_FLOAT_TYPE scale_variable(const MEAS_FLOAT_TYPE var,
                                 sriFLOAT& spatial_avg_factor,
                                 const sriFLOAT time_avg_factor,
                                 const SRI_VARIABLE_TYPE var_type,
                                 const sriFLOAT scale_factor_for_porosity);

  virtual VOID process_flow_monitors();
  virtual VOID process_solid_monitors();

  // m_meas_variable_sums is allocated here instead of in the constructor because n_variables may have changed.
  virtual VOID finish_init() { m_meas_variable_sums = cnew MEAS_FLOAT_TYPE[n_variables]; }
};

typedef tCP_REDUCTION_MEAS_WINDOW<sriFLOAT> sCP_REDUCTION_MEAS_WINDOW, *CP_REDUCTION_MEAS_WINDOW;

// A meas window that does not use a tree reduction
template <typename MEAS_FLOAT_TYPE>
struct tCP_NO_REDUCTION_MEAS_WINDOW : public sCP_MEAS_WINDOW {
  // constructor
  tCP_NO_REDUCTION_MEAS_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index,
                               cBOOLEAN is_meas_vars_output_dp);

  // For non-tree-reduced measurements; one per participating SP
  MPI_Request         *receive_requests; 

  // The SP meas cells are allocated contiguously (SP 0's cells followed by SP 1's cells,
  // etc). This allows a single offset to identify an SP meas cell, which is utilized in
  // sSP_MEAS_CELL_REF::set_sp_meas_cell. It also allows client code to use a single index 
  // to identify an SP meas cell, which may come in handy for surface coupling.

  MEAS_FLOAT_TYPE     **m_sp_meas_cells; // m_sp_meas_cells[n_sps][m_sp_n_meas_cells * n_variables]

  MEAS_FLOAT_TYPE     *m_moving_meas_cells; // [n_moving_meas_cells * n_variables] contains moving meas data
  std::vector<MEAS_FLOAT_TYPE> *m_moving_data_recv_buffer; // Recv buffer which contains the global meas cell indices and the data

  // This is used by windows_output to keep track of where it is in the SP list
  STP_PROC            m_next_sp_index_to_test;

  // This is used by windows_output to keep track of which moving meas
  // receives have been completed 
  STP_PROC            m_next_sp_moving_index_to_test_for_count;
  STP_PROC            m_next_sp_moving_index_to_test_for_data;

  STP_PROC            *m_participating_sps;

  virtual VOID post_var_data_recvs();

public:
  // This is called after all ublk and surfel meas cell refs have been processed, at which
  // point we have final values in m_sp_n_meas_cells.
  VOID allocate_sp_meas_cells();
  virtual VOID write_sri_variables(BOOLEAN some_std_dev_var, sriFLOAT time_scale);

  bool has_moving_meas_cell_data_arrived();
  bool has_stationary_meas_cell_data_arrived();
  virtual BOOLEAN has_sp_var_data_arrived();

  MEAS_FLOAT_TYPE     *sp_meas_cell(asINT32 sp, STP_MEAS_CELL_INDEX sp_meas_cell_index)
  { return &m_sp_meas_cells[sp][sp_meas_cell_index * n_variables]; }

  MEAS_FLOAT_TYPE     *moving_meas_cell(STP_MEAS_CELL_INDEX moving_meas_cell_index)
                      { return &m_moving_meas_cells[moving_meas_cell_index * n_variables]; }

  MEAS_FLOAT_TYPE     *sp_meas_cells(asINT32 sp)
  { return m_sp_meas_cells[sp]; }

  MEAS_FLOAT_TYPE     *all_sp_meas_cells() { return m_sp_meas_cells[0]; }

  MEAS_FLOAT_TYPE     *all_moving_meas_cells() { return m_moving_meas_cells; }
      
  VOID resolve_sp_meas_cell_ptrs()
  {
    // Convert each SP meas cell ref from an SP and SP meas cell index into an SP meas
    // cell pointer. This code assumes that each SP will sort its meas cells by global
    // index order.
    MEAS_FLOAT_TYPE *current_sp_meas_cells[STP_MAX_PROCS];
    ccDOTIMES(sp, total_sps)
      current_sp_meas_cells[sp] = sp_meas_cells(sp);


    asINT32 dev_meas_cells_empty = 0;
    asINT32 dev_meas_cells_full = 0;


    ccDOTIMES(i, n_stationary_meas_cells) {
      uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &m_sp_meas_cell_refs[i];

      if (ref_or_list->is_list()) {
        sSP_MEAS_CELL_REF_LIST_ELT *list = ref_or_list->list();
        while (list != NULL) {
          sSP_MEAS_CELL_REF   &ref = list->ref();
          asINT32             sp   = ref.sp();

          MEAS_FLOAT_TYPE *sp_cell = current_sp_meas_cells[sp];
          current_sp_meas_cells[sp] += n_variables;
          ref.set_sp_meas_cell_offset(all_sp_meas_cells(), sp_cell);

          list = list->m_next;
        }
      } else {
        sSP_MEAS_CELL_REF  &ref = ref_or_list->ref();
        asINT32             sp  = ref.sp();


        if ((!is_development) && sp < 0)
          msg_internal_error("Meas %s index %d of %s meas window \"%s\"index %d is not referenced by any %s %s",
                             is_lgi_meas_window_type_surface(meas_window_type) ? "surfel" : "cell",
                             get_meas_surfel_index(i), type_name(),
                             cdi_meas_window->name, this->index, this->meas_window_type == LGI_VOLUME_WINDOW || this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW ? "conduction" : "flow",
                             is_lgi_meas_window_type_surface(meas_window_type) ? "surfels" : "ublks");


        // Development meas windows are allowed to have empty measurment cells corresponding to segments which receive no surfel contribution.
        // Count the number of empty meas cells (don't send them either) and then issue a warning outside of the meas_cells loop
        else if (is_development && sp < 0)
          dev_meas_cells_empty++;
        else if (sp>= 0) {

          if (is_development)
            dev_meas_cells_full++;

          MEAS_FLOAT_TYPE *sp_cell = current_sp_meas_cells[sp];
          current_sp_meas_cells[sp] += n_variables;
          ref.set_sp_meas_cell_offset(all_sp_meas_cells(), sp_cell);
        }
      }
    }

    if (is_development && dev_meas_cells_empty >0) {
      asINT32 dev_meas_cells_total = dev_meas_cells_empty + dev_meas_cells_full;
      sriFLOAT per_dev_meas_cells_empty = (sriFLOAT) dev_meas_cells_empty / (sriFLOAT) dev_meas_cells_total;
      if (per_dev_meas_cells_empty > 0.50)
        msg_warn("%d of %d development measurment cells inlcude no surfels and/or ublks. Perhaps a larger segment size would be more appropriate.",
                 dev_meas_cells_empty,dev_meas_cells_total);
    }
  }

  MEAS_FLOAT_TYPE scale_variable(const MEAS_FLOAT_TYPE var,
                                 const sriFLOAT spatial_avg_factor,
                                 const sriFLOAT time_avg_factor,
                                 const SRI_VARIABLE_TYPE var_type,
                                 const BOOLEAN some_std_dev_var)
  {
    sriFLOAT scale_factor;
    switch (var_type) {
      case SRI_VARIABLE_STD_DEV_XVEL:
      case SRI_VARIABLE_STD_DEV_YVEL:
      case SRI_VARIABLE_STD_DEV_ZVEL:
      case SRI_VARIABLE_STD_DEV_VEL_MAG:
      case SRI_VARIABLE_STD_DEV_PRESSURE:
      case SRI_VARIABLE_STD_DEV_TEMP:
      case SRI_VARIABLE_STD_DEV_DENSITY: 
        {
          sriFLOAT scale_factor = spatial_avg_factor * time_avg_factor;
          /* std dev variables from the SPs are scaled by cell volume/area squared */
          return (std::sqrt( var * spatial_avg_factor * scale_factor ));
        }
      case SRI_VARIABLE_N_SCREENED_SURFELS:
      case SRI_VARIABLE_SCREENED_AREA:
      case SRI_VARIABLE_AREA:
      case SRI_VARIABLE_N_SCREENED_VOXELS:
      case SRI_VARIABLE_SCREENED_VOLUME:
      case SRI_VARIABLE_VOLUME:
        {
          return (var * time_avg_factor);
        }
      case SRI_VARIABLE_DEFROST_TIME:
        return var; // Do not scale defrost time
      default: 
        {
          sriFLOAT scale_factor = spatial_avg_factor * time_avg_factor;
          return (var * scale_factor);
        }
    }
  }

  virtual VOID process_flow_monitors();
  virtual VOID process_solid_monitors();
};

typedef tCP_NO_REDUCTION_MEAS_WINDOW<sriFLOAT> sCP_NO_REDUCTION_MEAS_WINDOW, *CP_NO_REDUCTION_MEAS_WINDOW;

template <typename MEAS_FLOAT_TYPE>
struct tCP_FLUID_MEAS_WINDOW : public tCP_NO_REDUCTION_MEAS_WINDOW <MEAS_FLOAT_TYPE> {

  STP_COORD m_current_meas_cube_location[3]; // coordinates of meas cube housing current DGF ublk desc

  // Begin of data required by the SRI interface (formerly housed in a nested "sri" struct).
  // Note that ref_frame_indices in base class is also required by the SRI interface.
  VMEM_VECTOR < sriINT >   m_nw_meas_cell_indices;  // near wall meas cell indices
  VMEM_VECTOR < sriFLOAT > m_nw_centroids;          // near wall meas cell centroids

  sriINT n_nw_meas_cells() { return m_nw_meas_cell_indices.size(); }

  sriINT        m_n_nw_facet_sides;       // number of facet sides bounding near wall meas cells
  sriINT        m_n_nw_neighbors;         // number of neighbors of near wall meas cells

#if LGI_COMPLETE_NEAR_WALL_MEAS_CELL_DATA // de-activated near wall meas cell info
  sriBYTE       *m_nw_face_neighbors_masks;// nw_face_neighbors_mask[n_nw_meas_cells]
  sSRI_FACET_SIDE *m_nw_facet_sides;      // nw_facet_sides[n_nw_facet_sides]
  sriINT        *m_nw_first_facet_sides;  // nw_first_facet_sides[n_nw_meas_cells]
  sriINT        *m_nw_neighbors;          // nw_neighbors[n_nw_neighbors]
  sriBYTE       *m_nw_neighbor_faces;     // nw_neighbor_faces[n_nw_neighbors]
  sriINT        *m_nw_first_neighbors;    // nw_first_neighbors[n_nw_meas_cells]
#endif

  sriBYTE       *m_scales; 
  // The index of m_coords will hit the 32 bit limit sooner than other arrays
  // because of n_dims factor. STP_MEAS_COORD_INDEX should be used to index
  // into this array to avoid 32 bit limit.
  sriINT        *m_coords;                // coords[n_meas_cells][n_dims] (LLR of associated meas cube)
  sriFLOAT      *m_volumes;               // volumes[n_meas_cells] (volume of each meas cell)
  dFLOAT        *m_d_volumes;             // tmp storage to accumulate cell volumes during init
  sriPART_INDEX *m_part_indices;          // part[n_meas_cells] (part index of each meas cell)
  // End of data required by the SRI interface 

  virtual SRI_STATUS open_sri_file(SRI_FILE *sri_file);
  virtual SRI_STATUS write_sri_header();
  virtual sriFLOAT   *cell_scale_factors() { return m_volumes; }
  virtual sriFLOAT   cell_scale_factor_5g(){ return 1.0; }
  virtual VOID       finish_init();
  virtual VOID       _precompute_stationary_cell_scale_factors();
  virtual VOID       _precompute_moving_cell_scale_factors() {};
  virtual VOID       allocate_window_data_for_sri();

public:
  // constructor
  tCP_FLUID_MEAS_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index,
                        cBOOLEAN is_meas_vars_output_dp);
  
  // map from part_index (uINT16) to part_volume (sFLOAT)
  typedef std::map< uINT16, sFLOAT >                                  sPART_INDEX_TO_VOLUME_MAP;
  typedef std::map< STP_MEAS_CELL_INDEX, sPART_INDEX_TO_VOLUME_MAP >  sMEAS_CELL_INDEX_TO_PART_VOLUME_MAP;
  sMEAS_CELL_INDEX_TO_PART_VOLUME_MAP m_meas_cell_part_contributions_map;

  VOID add_meas_cell_part_contribution(STP_MEAS_CELL_INDEX meas_cell_index, uINT16 part_index, sFLOAT volume)
  {
    sMEAS_CELL_INDEX_TO_PART_VOLUME_MAP::iterator it = m_meas_cell_part_contributions_map.find(meas_cell_index);

    if (it == m_meas_cell_part_contributions_map.end()) {
      static sPART_INDEX_TO_VOLUME_MAP empty_part_to_volume_map;
      sPART_INDEX_TO_VOLUME_MAP &part_to_volume_map
        = m_meas_cell_part_contributions_map[meas_cell_index] 
        = empty_part_to_volume_map;
      part_to_volume_map[part_index] = volume;
    } else {
      sPART_INDEX_TO_VOLUME_MAP &part_to_volume_map = it->second;
      part_to_volume_map[part_index] += volume;
    }
  }

  VOID clear_meas_cell_part_contributions_map() { m_meas_cell_part_contributions_map.clear(); }

  struct sMEAS_CELL_CENTROID {
    dFLOAT  m_centroid[3];
    BOOLEAN m_is_partial_cell;
  };
  typedef std::map< STP_MEAS_CELL_INDEX, sMEAS_CELL_CENTROID >  sMEAS_CELL_INDEX_TO_CENTROID_MAP;
  sMEAS_CELL_INDEX_TO_CENTROID_MAP m_meas_cell_centroids_map;

  // We stash the centroid of the first meas cell within a meas cube here instead of inside the
  // the above map. This optimizes the most common case: a single meas cell per meas cube.
  STP_MEAS_CELL_INDEX m_current_meas_cube_first_meas_cell_index;
  dFLOAT              m_current_meas_cube_first_meas_cell_centroid[3];
  cBOOLEAN            m_current_meas_cube_first_meas_cell_is_partial;

  VOID add_meas_cell_centroid_contribution(STP_MEAS_CELL_INDEX meas_cell_index, 
                                           dFLOAT centroid[3], // weighted by associated ublk/voxel volume
                                           BOOLEAN is_partial_cell)
  {
    if (m_current_meas_cube_first_meas_cell_index == meas_cell_index) {
      ccDOTIMES(i, 3)
        m_current_meas_cube_first_meas_cell_centroid[i] += centroid[i];
      if (is_partial_cell)
        m_current_meas_cube_first_meas_cell_is_partial = TRUE;
    } else if (m_current_meas_cube_first_meas_cell_index < 0) {
      m_current_meas_cube_first_meas_cell_index = meas_cell_index;
      ccDOTIMES(i, 3)
        m_current_meas_cube_first_meas_cell_centroid[i] = centroid[i];
      m_current_meas_cube_first_meas_cell_is_partial = is_partial_cell;
    } else {
      typename sMEAS_CELL_INDEX_TO_CENTROID_MAP::iterator it = 
        m_meas_cell_centroids_map.find(meas_cell_index);
      if (it == m_meas_cell_centroids_map.end()) {
        sMEAS_CELL_CENTROID mcc;
        ccDOTIMES(i, 3)
          mcc.m_centroid[i] = centroid[i];
        mcc.m_is_partial_cell = is_partial_cell;
        m_meas_cell_centroids_map[meas_cell_index] = mcc;
      } else {
        sMEAS_CELL_CENTROID &c = it->second;
        ccDOTIMES(i, 3)
          c.m_centroid[i] += centroid[i];
        if (is_partial_cell)
          c.m_is_partial_cell = TRUE;
      }
    }
  }

  VOID clear_meas_cell_centroids_map() 
  { 
    m_meas_cell_centroids_map.clear();
    m_current_meas_cube_first_meas_cell_index = -1;
    m_current_meas_cube_first_meas_cell_is_partial = FALSE;
  }

};

typedef tCP_FLUID_MEAS_WINDOW <sriFLOAT>  sCP_FLUID_MEAS_WINDOW_SFLOAT,
                                          *CP_FLUID_MEAS_WINDOW_SFLOAT;
typedef tCP_FLUID_MEAS_WINDOW <sriDOUBLE> sCP_FLUID_MEAS_WINDOW_DFLOAT,
                                          *CP_FLUID_MEAS_WINDOW_DFLOAT;

// Here we are cheating a bit, exploiting the knowledge that sCP_FLUID_MEAS_WINDOW_SFLOAT and
// sCP_FLUID_MEAS_WINDOW_DFLOAT are identical except for their m_sp_meas_cells field.
// This is not technically true because the sSP_MEAS_CELL_REF type defined within the meas window
// uses pointers of type (MEAS_FLOAT_TYPE*), but in practice, this does not create an issue because
// aside from the return type of its sp_meas_cell() method, sSP_MEAS_CELL_REF is independent
// of the actual pointer type.
typedef sCP_FLUID_MEAS_WINDOW_SFLOAT      sCP_FLUID_MEAS_WINDOW, 
                                          *CP_FLUID_MEAS_WINDOW;

#define DO_MEAS_CELL_PART_CONTRIBUTIONS(iterator, fluid_meas_window)                    \
  DO_STD_MAP(STP_MEAS_CELL_INDEX, sCP_FLUID_MEAS_WINDOW::sPART_INDEX_TO_VOLUME_MAP,     \
             iterator, fluid_meas_window->m_meas_cell_part_contributions_map)

#define DO_MEAS_CELL_PART_TO_VOLUME_MAP(part_index, volume, part_to_volume_map) \
  DO_STD_MAP_VALUES(uINT16, sFLOAT, part_index, volume, part_to_volume_map)    


template <typename MEAS_FLOAT_TYPE>
struct tCP_SURFACE_MEAS_WINDOW : public tCP_NO_REDUCTION_MEAS_WINDOW <MEAS_FLOAT_TYPE> {

  // Begin of data required by the SRI interface (formerly housed in a nested "sri" struct).
  // Note that ref_frame_indices in base class is also required by the SRI interface.
  sriINT        m_n_vertices;             // number of vertices
  sriINT        m_n_surfels;
  sriINT        m_n_vertex_refs;          // number of vertex_refs
  sriINT        *m_first_vertex_refs;     // first_vertex_refs[n_meas_cells]
  // index of 1st vertex_refs for each meas surfel
  sriINT        *m_vertex_refs;           // vertex_refs[nvertex_refs]
  // list of indices into cp_info.native_mesh vertices
  sriFACE_INDEX *m_faces;                 // faces[n_meas_cells] (face index of each meas surfel)
  sriFACET_ID   *m_facet_ids;             // facet_ids[n_meas_cells] (CDI facet ID of each meas surfel)
  sriFLOAT      *m_surfel_areas;          // surfel_areas[n_meas_cells] 
  dFLOAT        *m_d_surfel_areas;        // tmp storage to accumulate surfel areas during init
  // STP_MEAS_COORD_INDEX should be used to index into m_surfel_normals array
  sriFLOAT      *m_surfel_normals;        // surfel_normals[n_meas_cells][n_dims]
  sriBOOL       *m_inv_phys_norm;         // normal inverse of physics surfel ? [n_meas_cells]
  sriBYTE       *m_scales; 
  BOOLEAN	m_mate_window_read;
  std::map<asINT32, sriINT> m_moving_face_n_surfels;
  // End of data required by the SRI interface 
  std::vector<sSRI_SURFEL_BLOCK_MAP> surfel_block_map;
  std::vector<sriINT> n_mated_surfels_per_face;
  tCP_SURFACE_MEAS_WINDOW *mate_window;

  // constructor
  tCP_SURFACE_MEAS_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index,
                          cBOOLEAN is_meas_vars_output_dp);

  virtual SRI_STATUS open_sri_file(SRI_FILE *sri_file);
  virtual SRI_STATUS write_sri_header();
  virtual sriFLOAT   *cell_scale_factors() { return m_surfel_areas; }
  virtual sriFLOAT   cell_scale_factor_5g(){ return 1.0; }
  virtual VOID       finish_init();
  virtual VOID       _precompute_stationary_cell_scale_factors();
  virtual VOID       _precompute_moving_cell_scale_factors();
  virtual VOID       allocate_window_data_for_sri();
  virtual VOID       composite_moving_meas_cells();

  VOID convert_for_surface_coupling(sFLOAT** meas_variables);
  VOID allocate_prelim_data_for_sri();
#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
  VOID allocate_coupling_sri_data();
#endif

    
};

typedef tCP_SURFACE_MEAS_WINDOW <sriFLOAT>  sCP_SURFACE_MEAS_WINDOW_SFLOAT,
                                            *CP_SURFACE_MEAS_WINDOW_SFLOAT;
typedef tCP_SURFACE_MEAS_WINDOW <sriDOUBLE> sCP_SURFACE_MEAS_WINDOW_DFLOAT,
                                            *CP_SURFACE_MEAS_WINDOW_DFLOAT;
typedef sCP_SURFACE_MEAS_WINDOW_SFLOAT      sCP_SURFACE_MEAS_WINDOW,
                                            *CP_SURFACE_MEAS_WINDOW;

template <typename MEAS_FLOAT_TYPE>
struct tCP_COMPOSITE_FLUID_MEAS_WINDOW : public tCP_REDUCTION_MEAS_WINDOW <MEAS_FLOAT_TYPE> {

  // Begin of data required by the SRI interface (formerly housed in a nested "sri" struct).
  // Note that ref_frame_indices in base class is also required by the SRI interface.
  sriINT        *m_part_indices;
  sriFLOAT      *m_part_volumes;
  sriFLOAT      m_scale_factor_5g;
  // End of data required by the SRI interface

  // constructor
  tCP_COMPOSITE_FLUID_MEAS_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index,
                            cBOOLEAN is_meas_vars_output_dp);

  virtual SRI_STATUS open_sri_file(SRI_FILE *sri_file);
  virtual SRI_STATUS write_sri_header();
  virtual sriFLOAT   *cell_scale_factors() { return m_part_volumes; }
  virtual sriFLOAT   cell_scale_factor_5g() { return m_scale_factor_5g; }

  virtual VOID       _precompute_stationary_cell_scale_factors();
  virtual VOID       _precompute_moving_cell_scale_factors() {}
  virtual VOID       allocate_window_data_for_sri();

};

typedef tCP_COMPOSITE_FLUID_MEAS_WINDOW <sriFLOAT>  sCP_COMPOSITE_FLUID_MEAS_WINDOW_SFLOAT,
                                                    *CP_COMPOSITE_FLUID_MEAS_WINDOW_SFLOAT;
typedef tCP_COMPOSITE_FLUID_MEAS_WINDOW <sriDOUBLE> sCP_COMPOSITE_FLUID_MEAS_WINDOW_DFLOAT,
                                                    *CP_COMPOSITE_FLUID_MEAS_WINDOW_DFLOAT;
typedef sCP_COMPOSITE_FLUID_MEAS_WINDOW_SFLOAT      sCP_COMPOSITE_FLUID_MEAS_WINDOW,
                                                    *CP_COMPOSITE_FLUID_MEAS_WINDOW;

template <typename MEAS_FLOAT_TYPE>
struct tCP_COMPOSITE_SURFACE_MEAS_WINDOW : public tCP_REDUCTION_MEAS_WINDOW <MEAS_FLOAT_TYPE> {

  // Begin of data required by the SRI interface (formerly housed in a nested "sri" struct).
  // Note that ref_frame_indices in base class is also required by the SRI interface.
  sriFACE_INDEX *m_faces;                 // faces[n_meas_cells] (face index of each meas surfel)
  sriFLOAT      *m_projected_areas;	  // projected_areas[n_meas_cells][n_dims]
  sriFLOAT      *m_face_areas;		  // face_areas[n_meas_cells][n_dims]
  std::map<asINT32, sriINT> m_moving_face_n_surfels;
  // End of data required by the SRI interface 

  // constructor
  tCP_COMPOSITE_SURFACE_MEAS_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index,
                                    cBOOLEAN is_meas_vars_output_dp);

  virtual SRI_STATUS open_sri_file(SRI_FILE *sri_file);
  virtual SRI_STATUS write_sri_header();
  virtual sriFLOAT   *cell_scale_factors() { return m_face_areas; }
  virtual sriFLOAT   cell_scale_factor_5g(){ return 1.0; }
  virtual VOID       _precompute_stationary_cell_scale_factors();
  virtual VOID       _precompute_moving_cell_scale_factors();
  virtual VOID       allocate_window_data_for_sri();
  virtual VOID       composite_moving_meas_cells(); 
};

typedef tCP_COMPOSITE_SURFACE_MEAS_WINDOW <sriFLOAT>  sCP_COMPOSITE_SURFACE_MEAS_WINDOW_SFLOAT,
                                                      *CP_COMPOSITE_SURFACE_MEAS_WINDOW_SFLOAT;
typedef tCP_COMPOSITE_SURFACE_MEAS_WINDOW <sriDOUBLE> sCP_COMPOSITE_SURFACE_MEAS_WINDOW_DFLOAT,
                                                      *CP_COMPOSITE_SURFACE_MEAS_WINDOW_DFLOAT;
typedef sCP_COMPOSITE_SURFACE_MEAS_WINDOW_SFLOAT      sCP_COMPOSITE_SURFACE_MEAS_WINDOW,
                                                      *CP_COMPOSITE_SURFACE_MEAS_WINDOW;

template <typename MEAS_FLOAT_TYPE>
struct tCP_SURFACE_DEV_MEAS_WINDOW : public tCP_REDUCTION_MEAS_WINDOW <MEAS_FLOAT_TYPE> {

  sriINT *m_face_first_meas_cell[3];    // measurement cell corresponding to the first segment of a particular face
  sriINT *m_face_n_segments[3];
  sriINT *m_face_first_segment[3];
  std::vector<cBOOLEAN> m_face_is_movb; // checks if a face is an immersed boundary
  sriFLOAT  m_start_pt[3][3];        // first index is the axis, second index is the coord
  sriFLOAT  m_end_pt[3][3];
  sriINT   m_num_segments[3];
  sriINT   m_csys;
  sriINT   m_lrf;

  // constructor
  tCP_SURFACE_DEV_MEAS_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index,
                              cBOOLEAN is_meas_vars_output_dp);

  VOID fill_segment_data(cDGF_DEV_WINDOW_LINE_SEGMENTS *dgf_segment, sINT8 n_dims) {
    ccDOTIMES(icoord,3) {
      m_start_pt[0][icoord] = dgf_segment->start_pt_x[icoord];
      m_start_pt[1][icoord] = dgf_segment->start_pt_y[icoord];
      if (n_dims == 3)
        m_start_pt[2][icoord] = dgf_segment->start_pt_z[icoord];

      m_end_pt[0][icoord] = dgf_segment->end_pt_x[icoord];
      m_end_pt[1][icoord] = dgf_segment->end_pt_y[icoord];
      if (n_dims == 3)
          m_end_pt[2][icoord] = dgf_segment->end_pt_z[icoord];
    }
    m_num_segments[0] = dgf_segment->num_segments_x;
    m_num_segments[1] = dgf_segment->num_segments_y;
    if (n_dims == 3) 
       m_num_segments[2] = dgf_segment->num_segments_z;
  }

  virtual SRI_STATUS open_sri_file(SRI_FILE *sri_file);
  virtual SRI_STATUS write_sri_header() {return SRI_SUCCESS;}
  virtual sriFLOAT   *cell_scale_factors() { return NULL; }
  virtual sriFLOAT   cell_scale_factor_5g(){ return 1.0; }
  virtual VOID       _precompute_stationary_cell_scale_factors(){}
  virtual VOID       _precompute_moving_cell_scale_factors(){}
  virtual VOID       allocate_window_data_for_sri() {}

};

typedef tCP_SURFACE_DEV_MEAS_WINDOW <sriFLOAT>  sCP_SURFACE_DEV_MEAS_WINDOW_SFLOAT,
                                                *CP_SURFACE_DEV_MEAS_WINDOW_SFLOAT;
typedef tCP_SURFACE_DEV_MEAS_WINDOW <sriDOUBLE> sCP_SURFACE_DEV_MEAS_WINDOW_DFLOAT,
                                                *CP_SURFACE_DEV_MEAS_WINDOW_DFLOAT;
typedef sCP_SURFACE_DEV_MEAS_WINDOW_SFLOAT      sCP_SURFACE_DEV_MEAS_WINDOW,
                                                *CP_SURFACE_DEV_MEAS_WINDOW;

template <typename MEAS_FLOAT_TYPE>
struct tCP_FLUID_DEV_MEAS_WINDOW : public tCP_REDUCTION_MEAS_WINDOW <MEAS_FLOAT_TYPE> {

  sriINT *m_part_first_meas_cell[3];
  sriINT *m_part_n_segments[3];
  sriINT *m_part_first_segment[3];
  sriFLOAT  m_start_pt[3][3];
  sriFLOAT  m_end_pt[3][3];
  sriINT   m_num_segments[3];
  sriINT   m_csys;
  sriINT   m_lrf;

  std::vector<sriFLOAT> m_volumes;

  // For volume averaging
  sriFLOAT m_scale_factor_5g;
  sriFLOAT scale_factor() { return 1.0; }

  // constructor
  tCP_FLUID_DEV_MEAS_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index,
                            cBOOLEAN is_meas_vars_output_dp);

  VOID fill_segment_data(cDGF_DEV_WINDOW_LINE_SEGMENTS *dgf_segment, sINT8 n_dims) {
    ccDOTIMES(icoord,3) {
      m_start_pt[0][icoord] = dgf_segment->start_pt_x[icoord];
      m_start_pt[1][icoord] = dgf_segment->start_pt_y[icoord];
      if (n_dims == 3)
        m_start_pt[2][icoord] = dgf_segment->start_pt_z[icoord];

      m_end_pt[0][icoord] = dgf_segment->end_pt_x[icoord];
      m_end_pt[1][icoord] = dgf_segment->end_pt_y[icoord];
       if (n_dims == 3)
        m_end_pt[2][icoord] = dgf_segment->end_pt_z[icoord];
    }
    m_num_segments[0] = dgf_segment->num_segments_x;
    m_num_segments[1] = dgf_segment->num_segments_y;
     if (n_dims == 3)
      m_num_segments[2] = dgf_segment->num_segments_z;
  }

  virtual SRI_STATUS open_sri_file(SRI_FILE *sri_file);
  virtual SRI_STATUS write_sri_header() {return SRI_SUCCESS;}
  virtual sriFLOAT   *cell_scale_factors() { return m_volumes.data(); }
  virtual sriFLOAT   cell_scale_factor_5g(){ return m_scale_factor_5g; }
  virtual VOID       _precompute_stationary_cell_scale_factors();
  virtual VOID       _precompute_moving_cell_scale_factors() {};
  virtual VOID       allocate_window_data_for_sri();

  sriFLOAT compute_z_slice_dimension() {
    return (sriFLOAT)(this->max_bound[2] - this->min_bound[2]) / m_num_segments[2];
  }

};

typedef tCP_FLUID_DEV_MEAS_WINDOW <sriFLOAT>  sCP_FLUID_DEV_MEAS_WINDOW_SFLOAT,
                                              *CP_FLUID_DEV_MEAS_WINDOW_SFLOAT;
typedef tCP_FLUID_DEV_MEAS_WINDOW <sriDOUBLE> sCP_FLUID_DEV_MEAS_WINDOW_DFLOAT,
                                              *CP_FLUID_DEV_MEAS_WINDOW_DFLOAT;
typedef sCP_FLUID_DEV_MEAS_WINDOW_SFLOAT      sCP_FLUID_DEV_MEAS_WINDOW,
                                              *CP_FLUID_DEV_MEAS_WINDOW;

typedef class sCP_MEAS_WINDOW_COLLECTION {
private:
  std::vector < CP_MEAS_WINDOW >  m_meas_windows; // vector of pointers to meas windows

public:
  CP_MEAS_WINDOW m_queues[NUM_QUEUES]; // queue of meas windows sorted by output time, and for posting recvs and writing meas files

  BOOLEAN some_surface_meas_window;

  asINT32 n_meas_windows()     { return m_meas_windows.size(); }

  sCP_MEAS_WINDOW_COLLECTION() {  // constructor
    some_surface_meas_window = FALSE; 
    ccDOTIMES(i, NUM_QUEUES) {
      m_queues[i] = NULL;
    }
  }

  ~sCP_MEAS_WINDOW_COLLECTION() {  // destructor
  }

  // total_meas_windows is the total of all fluid and surface meas windows
  VOID reserve(asINT32 total_meas_windows) { m_meas_windows.reserve(total_meas_windows); }

  CP_MEAS_WINDOW &operator[](asINT32 index) { return m_meas_windows[index]; }

  // Meas windows must be added in index order...
  CP_MEAS_WINDOW add_meas_window(cDGF_MEAS_WINDOW *dgf_window, BOOLEAN is_coupling = FALSE);

} *CP_MEAS_WINDOW_COLLECTION;

#define DO_CDI_MEAS_WINDOWS(cdi_window)                                                                                 \
  asINT32 ___(i) = 0;                                                                                                   \
  asINT32 ___(n_cdi_windows) = cp_info.n_cdi_meas_windows;                                                              \
  for (CDI_MEAS_WINDOW cdi_window = cp_info.cdi_meas_windows; ___(i)<___(n_cdi_windows); ___(i)++, cdi_window++)

#define DO_CP_MEAS_WINDOWS(window)                                                                                       \
  asINT32 ___(i) = 0;                                                                                                    \
  asINT32 ___(n_meas_windows) = cp_info.n_meas_windows();                                                                \
  for (CP_MEAS_WINDOW window; (___(i) < ___(n_meas_windows)) && (window = cp_info.meas_windows[___(i)], TRUE); ___(i)++) if (window)

VOID finish_init_of_meas_windows();
VOID allocate_meas_window_lrf_mbc_and_movb_info();

/*--------------------------------------------------------------------------*
 * Operations
 *--------------------------------------------------------------------------*/
/* Outputs all of the windows that have output pending */
template <eQUEUE_TYPE queue_type>
VOID windows_output(WALLCLOCK_TIME_SECS time_secs, BOOLEAN output_all_remaining_windows);
VOID output_windows_up_to_timestep(TIMESTEP last_output_timestep);
#if COPY_TMP_MEAS_FILES_AT_CKPT
VOID ckpt_tmp_meas_windows();
#endif

extern "C" VOID close_all_meas_windows();

/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/

VOID insert_cmd_line_post_meas_cmds_in_queues();
VOID insert_pre_table_read_cmds_in_queues();
VOID check_for_missing_measurement_files();
VOID verify_precision_of_meas_var_data();

VOID align_meas_window_time_parameters();
// This is in window.cc because the code is closely related to align_meas_window_time_parameters()
VOID align_avg_mme_ckpt_time_parameters(TIME_DESC time_desc, TIMESTEP start, TIMESTEP end, TIMESTEP average, TIMESTEP period);
VOID align_ckpt_time_parameters(TIMESTEP start, TIMESTEP end, TIMESTEP period);

VOID windows_post_initial_recvs();
VOID windows_build_reduction_trees();
VOID windows_finalize();

// Sets window time parameters after full checkpoint restore
VOID window_update_times(CP_MEAS_WINDOW info,
			 asINT32 num_nsets_written,
			 TIMESTEP current_clear,
			 TIMESTEP next_clear,
			 TIMESTEP current_output,
			 TIMESTEP next_output);

#if SURF_COUP
VOID launch_surface_coupling_app(CP_SURFACE_MEAS_WINDOW window, TIMESTEP timestep, BOOLEAN run_only_p, dFLOAT unrounded_timestep = 0.);
dFLOAT run_surface_coupling_job(CP_MEAS_WINDOW window, asINT32 model_index, TIMESTEP timestep, dFLOAT unrounded_timestep = 0., BOOLEAN dry_run = FALSE);
#endif

STP_ACTIVE_SOLVER_MASK window_set_solver_mask_from_sri_vars(asINT32 n_vars, 
							    SRI_VARIABLE_TYPE *var_types, 
							    BOOLEAN is_ht, 
							    BOOLEAN is_turb,
							    BOOLEAN is_active_ht);


typedef class sWINDOW_QUEUE_MT {

private:
  CP_MEAS_WINDOW *m_entries;
  std::atomic<asINT32> m_head;
  std::atomic<asINT32> m_tail;
  // Actual size of queue is m_capacity + 1 to avoid aliasing full == empty
  asINT32 m_capacity;
  eMT_QUEUE_TYPE m_queue_type;

public:
  bool is_empty() {
    asINT32 head = m_head.load();
    asINT32 tail = m_tail.load();
    return (head == tail);
  }

  int n_entries()  {
    asINT32 head = m_head.load();
    asINT32 tail = m_tail.load();
    if (tail >= head)
      return (tail - head);
    else
      return (m_capacity + 1  - head + tail);
  }

  void add_entry(CP_MEAS_WINDOW new_entry) {
#ifdef ENABLE_CONSISTENCY_CHECKS
    if (n_entries() == m_capacity) {
      msg_internal_error("Queue is full!");
    }
#endif
    asINT32 tail = m_tail.load();

    m_entries[tail] = new_entry;

    if (tail == m_capacity) {
      m_tail.store(0);
    }
    else {
      m_tail.store(tail+1);
    }
  }

  CP_MEAS_WINDOW remove_entry() {
    asINT32 head = m_head.load();
    asINT32 tail = m_tail.load();
    if (head != tail) {
      CP_MEAS_WINDOW window = m_entries[head];
      if (head == m_capacity) {
        m_head.store(0);
      }
      else {
        m_head.store(head+1);
      }
      return window;
    }
    else {
      return nullptr;
    }
  }

  VOID process(CP_MEAS_WINDOW window, WALLCLOCK_TIME_SECS time_secs);

  sWINDOW_QUEUE_MT(int capacity, eMT_QUEUE_TYPE queue_type) : 
    m_entries(new CP_MEAS_WINDOW[capacity+1]{nullptr}),
    m_head(0),
    m_tail(0),
    m_capacity(capacity),
    m_queue_type(queue_type)
  {}

} *WINDOW_QUEUE_MT;

#endif /* __WINDOW_H */

