#ifndef EXA_CDI_FIX_PARALLEL_DEV_H
#define EXA_CDI_FIX_PARALLEL_DEV_H

#include "cdi_export.h"

#include <array>

// Due to the parallel development of large projects on different simultaneous
// branches, CDI versioning got stepped on.  This is BAD! Please try to keep
// CDI development as linear and serial as possible.  These workarounds are
// really annoying to keep straight.  For example:

// Hierarchical partitions was introduced in versions 8.3-8.7.  Conduction CDIs
// started with 9.0 but there was a period when conduction cases were created
// without hierarchy.  Partitions and conduction were merged at 9.4. Therefore,
// CDIs created on the "cond_sm" branch between v9.0-9.3 did not have
// partitions. So we need to make sure that CDIs with versions between 9.0 and
// 9.3 do not try to read/write hierarchical information.

struct CDI_VERSION_PAIR
{
  cdiINT32 major = 0;
  cdiINT32 minor = 0;
};

inline constexpr bool operator == (CDI_VERSION_PAIR a, CDI_VERSION_PAIR b) {
  return a.major == b.major && a.minor == b.minor;
}

struct CDI_PARALLEL_VERSION_RANGE
{
  CDI_VERSION_PAIR min;
  CDI_VERSION_PAIR max;
};

using CDI_MAINLINE_PARALLEL_PAIR = std::pair<CDI_VERSION_PAIR, CDI_PARALLEL_VERSION_RANGE>;

// The parallel version range is an open range
inline constexpr std::array<CDI_MAINLINE_PARALLEL_PAIR,9> cdi_ml_cond_sm_map = {{
    {{8,3}, CDI_PARALLEL_VERSION_RANGE{ {9,0}, {9,4} }},
    {{8,5}, CDI_PARALLEL_VERSION_RANGE{ {9,0}, {9,4} }},
    {{8,7}, CDI_PARALLEL_VERSION_RANGE{ {9,0}, {9,4} }},
    {{8,9}, CDI_PARALLEL_VERSION_RANGE{ {9,0}, {9,10} }},
    {{8,10}, CDI_PARALLEL_VERSION_RANGE{ {9,0}, {9,10} }},
    {{8,11}, CDI_PARALLEL_VERSION_RANGE{ {9,0}, {9,10} }},
    {{8,12}, CDI_PARALLEL_VERSION_RANGE{ {9,0}, {9,14} }},
    {{8,13}, CDI_PARALLEL_VERSION_RANGE{ {9,0}, {9,15} }},
    {{8,14}, CDI_PARALLEL_VERSION_RANGE{ {9,0}, {9,16} }}
}};

constexpr CDI_PARALLEL_VERSION_RANGE cdi_find_ml_number(CDI_VERSION_PAIR ml_version)
{
  for (auto& el: cdi_ml_cond_sm_map) {
    if (el.first == ml_version) {
      return el.second;
    }
  }

  return CDI_PARALLEL_VERSION_RANGE();

  // throw "Unable to find matching mainline version in cdi_ml_cond_sm_map!";
}

// Checks if the current cdi version (in cdi_info) is at least the 
// version given as the template parameter, and it is not a version
// in the matching parallel development version range.
template<cdiINT32 MAINLINE_MAJOR_VERSION, cdiINT32 MAINLINE_MINOR_VERSION>
inline bool cdi_version_is_at_least_and_not_parallel_dev_cdi(CDI_INFO cdi_info)
{
  static constexpr CDI_PARALLEL_VERSION_RANGE parallel_range = cdi_find_ml_number({MAINLINE_MAJOR_VERSION, MAINLINE_MINOR_VERSION});
  static_assert( parallel_range.min.major != 0, "Unable to find matching mainline version in cdi_ml_cond_sm_map!" );

  return CDI_INFO_VERSION_AT_LEAST(cdi_info, MAINLINE_MAJOR_VERSION, MAINLINE_MINOR_VERSION) &&
          // This range is the 'bad range' from the parallel version
          // min denotes the start, max when the mainline cdi version was merged in
         !(CDI_INFO_VERSION_AT_LEAST(cdi_info, parallel_range.min.major, parallel_range.min.minor) && !CDI_INFO_VERSION_AT_LEAST(cdi_info, parallel_range.max.major, parallel_range.max.minor));
}

// Checks if the current cdi version (in cdi_info) is less than the version
// given as the template parameter, or is a version in the matching parallel development range.
// With some boolean algebra and De Morgan's Laws, you can show this is just the
// negation of the previous version, but the double negatives are hard to parse,
// so I made a function with a different name.
//
// !(A && !B) -> !A || !!B -> !A || B
template<cdiINT32 MAINLINE_MAJOR_VERSION, cdiINT32 MAINLINE_MINOR_VERSION>
inline bool cdi_version_is_not_at_least_or_is_parallel_dev_cdi(CDI_INFO cdi_info)
{
  return !cdi_version_is_at_least_and_not_parallel_dev_cdi<MAINLINE_MAJOR_VERSION,MAINLINE_MINOR_VERSION>(cdi_info);
}


#endif
