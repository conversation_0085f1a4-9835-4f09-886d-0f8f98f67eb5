/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Routines shared by both the standalone and CP/SP versions of the simeng
 *
 * Jim Salem, Exa Corporation 
 * Created Fri Jul  1 1994
 *--------------------------------------------------------------------------*/
#include "common.h"
#include "exa_sim.h"
#include "window.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "jobctl.h"
#include "seed.h"
#ifdef NOT_DEFINED
#include <unistd.h>
#include <sys/param.h>
#endif
#include <sys/types.h>
#include <sys/stat.h>
#include "errbuf.h"

/*--------------------------------------------------------------------------*
 * Usage message 
 *--------------------------------------------------------------------------*/


/* Handy macro to avoid screen wraparound */
#define BU(string) fprintf(stderr, string)

static VOID bad_usage(cSTRING progname)
{
  fprintf(stderr, "Usage: %s <options...> <LGI file>\n", progname);
  {
    BU("Reporting Options:\n");
    BU(" -report_time             Report total CP execution time (default)\n");
    BU(" -no_report_time          Do not report total CP execution time\n");
    BU(" -no_report_performance   Do not report Performance at the end of the simulation\n");
    BU(" -report_mme              Report total MME for every timestep\n");
    BU(" -report_mme_verbose      Report detailed MME for every timestep\n");
    BU(" -report_memory           Reports memory used after each timestep\n");
    BU(" -report_total_memory     Reports total dynamically allocated memory\n");
    BU(" -h or -help              Print this message\n");

    BU("\nInitialization Options:\n");
    BU(" -host_sp_file <file>     SP binary filename\n");
    BU(" -umask <value>           Use <value> as the umask for output file permissions\n");
    BU(" -unlink_input_after_init Unlink input file after initialization complete\n");
    BU(" -lm_user                 Override the user name that is recorded in license logs\n");
    BU(" -lm_host                 Override the host name that is recorded in license logs\n");
    BU(" -n_cpnode_ranks <N>      Total number of ranks on CP node\n");
    BU(" -n_cpnode_sockets <N>    Number of sockets on CP node\n");
    BU(" -n_cpnode_cores_per_socket <N>      Number of physical cores on CP node\n");
    BU(" -n_cpnode_threads_per_core <N>      Number of threads per physical core on CP node\n");
    // BU(" -lm_alt                  Select alternate license 1, 2, or 3\n");
    // BU(" -lm_log_consumption      Log consumption in license log if no consumption licenses found\n");
    
    BU("\nIntegrated Decomposition Options:\n");
    BU(" -decomp_file <file>      Decomposer binary filename\n");
    BU(" -n_processors <N>        Decompose for <N> processors\n");
    //    BU(" -pmodel_file <filename>  Performance model filename\n");
    BU(" -target_platform <platform>  Target platform specification\n");
    BU(" -decomp_message_log <file> Decomposer messages output file\n");

    BU("\nRun Options:\n");
    BU(" -no_run                  Initialize the simulation only\n");
    BU(" -no_measure              Don't record measurements\n");
    BU(" -double_precision        This simulation is double precision\n");
    BU(" -double_precision_meas   Output measurement variables with double precision\n");
    BU(" -no_locations            Ignore the locations records\n");
    BU(" -no_dynamics             Don't perform dynamics\n");
    BU(" -no_timeout              Don't time out lengthy synchronizations\n");
    BU(" -timeout                 Time out lengthy synchronizations (-no_timeout is default)\n");
    BU(" -no_jobctl_file          Do not produce a job control status file\n");
    BU(" -jobctl_file <file>      Job control status output filename\n");
    BU(" -num_timesteps <N>       Override total number of timesteps\n");
    BU(" -n_lb_base_steps <N>     number of base timesteps in one LB timestep\n");
    BU(" -n_t_base_steps <N>      number of base timesteps in one T timestep\n");
    BU(" -n_ke_base_steps <N>     number of base timesteps in one KE timestep\n");
    BU(" -n_uds_base_steps <N>    number of base timesteps in one UDS timestep\n");
    BU(" -n_flow_base_steps <N>   number of base timesteps in one flow timestep\n");
    BU(" -n_conduction_base_steps <N> number of base timesteps in one CONDUCTION timestep\n");
    BU(" -n_radiation_base_steps <N>  number of base timesteps per RADIATION solve\n");
    BU(" -acous_start_time <N>    timestep at which the simulator starts LightHill\n");
    BU(" -momentum_freeze_start_time has been removed. To start a momentum freeze simulation use \n");
    BU("                             the PowerCASE options to generate a cdi file and --seed or \n");
    BU("                             --resume with a MME checkpoint file or measurement file\n");
    BU(" -freeze_momentum_field   has been superseded by an option in PowerCASE\n");
    BU(" -thermal_timestep_ratio  has been superseded by an option in PowerCASE\n");
    BU(" -n_send_elements <N>     Number of send elements in buffer\n");
    BU(" -cdi_file <filename>     CDI filename (required)\n");
    BU(" -read_old_cdi            Allow use of older CDI versions\n");
    // Smart seeding
    BU(" -mseed <fname> ['|'<subparm>] Smart seed filename and optional parameters and keywords\n");
    BU(" -seedframe               Frame number to seed from\n");
    BU(" -no_extrapolate          Do not extrapolate seed values while smart seeding (default)\n");
    BU(" -extrapolate             Extrapolate seed values while smart seeding\n");
    BU(" -smart_seed_boundaries   Seed boundaries that fall within the smart seed dataset\n");
    BU(" -bseed_exclude_faces <face1>,<face2>,<face3>,... Exclusion list of faces for boundary seeding\n");
    BU(" -bseed_include_faces <face1>,<face2>,<face3>,... Inclusion list of faces for boundary seeding\n");
    BU(" -bseed_exclude_vars <face>,<var1>,<var2>,...     Exclusion list of variables for boundary seeding\n");
    BU(" -bseed_include_vars <face>,<var1>,<var2>,...     Inclusion list of variables for boundary seeding\n");
    BU(" -seed_exclude_vars <region>,<var1>,<var2>,...    Exclusion list of variables for seeding\n");
    BU(" -seed_include_vars <region>,<var1>,<var2>,...    Inclusion list of variables for seeding\n");
    BU(" -seed_mks                Seed velocity and temperature in MKS units, and pressure in dimensionless units. \n");
    BU(" -seed_rotate_vel <coord_system>,<x|y|z>,<angle>  Rotate seed velocity about the x, y, or z axis of <coord_system> by <angle> (in degrees).\n");
    BU(" -disable_particle_modeling Disable particle modeling even if it's included in the CDI file.\n");
    BU(" -enable_film_trajectory_measurement  Enable trajectory measurement of film particles.\n");

    BU(" -no_swirl_model          Turn off the swirl model found in the fluid turbulence model\n");
    BU(" -ablm_cap <length>       Set the advanced boundary layer model (ABLM) length scale cap\n");
    BU(" -fluid_turb_solver_1     Revert to the fluid turbulence solver found in PowerFLOW 3.2 and 3.3\n");
    BU(" -fixed_temp_walls        True fixed temperature walls for non heat transfer simulations\n");
    BU(" -local_vel_fan_model     Use local velocity (not mean fan velocity) in polynomial fan model \n");
    BU(" -max_vel <uval>          Exceeding this velocity yields warning in simerr file\n");
    BU(" -delay_vel_warnings <N>  Wait N timesteps before issuing velocity warnings\n");
    BU(" -limit_vel_warnings <N>  Do not issue more than N velocity warnings\n");
    BU(" -fpe_exit_limit <N>      Exit after N floating point exceptions in a timestep\n");    
    BU(" -max_temp <uval>         Exceeding this temperature yields warning in simerr file\n");
    BU(" -min_temp <uval>         Exceeding this temperature yields warning in simerr file\n");
    BU(" -delay_temp_warnings <N> Wait N timesteps before issuing temperature warnings\n");
    BU(" -limit_temp_warnings <N> Do not issue more than N temperature range warnings\n");
    BU(" -dns_nu_over_t_floor <floor> Set floor on lattice nu/T for DNS simulations\n");
    BU(" -table_path <path>       Define search path for tables read during simulation\n");
    BU(" -before_table \"table_name,cmd\"\n"
       "                          Issue cmd before table is read (after associated meas frame is written)\n"); 
    BU(" -after_meas \"meas_window_name,<S>,<N>,cmd\"\n"
       "                          Issue cmd after every N frames of meas window starting with frame S\n"); 
    BU(" -abort_if_missing_meas_files   Halt resumed simulation if measurement files are missing\n");
    BU(" -no_reserve_addr_space   Do not reserve extra address space for non-shob items\n");
    BU(" -no_mpi_request_free     Do not call MPI_Request_free for MPI send requests\n");
    BU(" -short_mpi_thread_sleep  Use minimum sleep time when comm thread waits\n");
    BU(" -sim_opt_licenses        Use optimization licenses\n");
    BU(" -sim_opt_license_failover      Failover to regular simulation licenses\n");
    BU(" -sim_dsls_credits        Force the use of SUN credits when using DSLS\n");
    BU(" -sim_dsls_tokens         Force the use of SRU tokens when using DSLS\n");

    BU("\nSurface coupling options\n");

    BU(" -ptherm_model_path <search_path> 	Search path for PowerTHERM/RadTherm model files\n");
    BU(" -ptherm_predecessor_run <run_dir> 	Find all PowerTHERM/RadTherm model files here\n");
    BU(" -ptherm_only_keep_orig_model 		Do not make backup copies of PowerTHERM/RadTherm model files\n");
    BU(" -ptherm_wait_for_license 		During initialization, if required, wait for PowerTHERM license to become available.\n");
    BU(" -ptherm_dump_gradient.       Write the PowerTHERM temperature gradient log in file powertherm/model_name.gradient_out.\n");
    BU(" -ptherm_processes <nprocesses> 		Number of processes used by PowerTHERM/RadTherm\n");
    BU(" -ptherm_threads <nthreads> 		Number of threads per process used by PowerTHERM/RadTherm\n");
    BU(" -ptherm_remote_host <hostname> 	Hostname of remote machine for running PowerTHERM/RadTherm\n"); 
    BU(" -ptherm_remote_user <user> 		Username on remote machine running PowerTHERM/RadTherm\n"); 
    BU(" -ptherm_mpifile <mpifile> 		Hostfile for the distributed PowerTHERM/RadTHERM solver\n"); 
    BU(" -ptherm_model_changed 			PowerTHERM/RadTherm model file has changed before restarting from a full checkpoint\n"); 
    BU(" -ptherm_ckpt_skip_first_coupling 	Skip the first surface coupling iteration\n"); 
    BU(" -force_radtherm 	Force the RadTherm application to be used even for PowerTHERM models specified in the CDI file\n"); 
    BU(" -force_ptherm 		Force the PowerTHERM application to be used even for Radtherm models specified in the CDI file\n"); 
    BU(" -radtherm_path 	Specify the path to the RadTherm application\n"); 
    BU(" -ptherm_max_unmatched_ratio 		Specify maximum allowable unmatched area ratio for the surface coupling interpolation map\n");

    BU(" -meas_timing \"<window_name>,<start_time>[,<end_time>[,<period>[,<interval>[,<time_unit>]]]]\"\n");
    BU("                                        Override meas window timing parameters\n");
    BU(" -meas_timing_acoustics \"<window_name>,<start_time>,<end_time>,<min_freq>,<max_freq>,<min_cycles>,<nyquist_multiplier>[,<interval>[,<time_unit>[,<freq_unit>]]]\"\n");
    BU("                                        Override meas window timing parameters\n");
    BU(" -amesim_model_path <search_path> 	Search path for AMESim model files\n");
    BU(" -amesim_timeout <timeout> 	        Timeout for AMESim\n");

    BU("\nCheckpoint File Options\n");
    BU(" -checkpoint_interval <interval>        Override checkpoint generation interval\n");
    BU(" -checkpoint_file <file>  Override root of checkpoint file name\n");
    BU(" -full_checkpoints        Force full checkpoint generation rather than MME checkpoints\n");
    BU(" -resume_file <file>      Resume simulation from full or MME checkpoint\n");
    BU(" -smart_seed <filename> [frame] Smart seed filename and optional frame\n");
    BU(" -small_mem_seed          Use file caching to reduce memory use during seeding (default)\n");
    BU(" -no_small_mem_seed       Use file caching to reduce memory use during seeding\n");
    BU(" -fluid_checkpoint_at_end   Generate MME checkpoint after last timestep");
    BU(" -full_checkpoint_at_end  Generate full checkpoint after last timestep");
    BU(" -no_final_checkpoint_retry  Do not retry writing end of simulation full checkpoint data upon failure, for example due to a full disk");
    BU(" -allow_immediate_checkpoints Allow checkpoints before initial timestep");

    BU("\nDetailed Reporting Options:\n");
    BU(" -report_cp_time          Report CP execution time at each timestep\n");
    BU(" -report_sp_time          Report SP execution time at each timestep\n");
    BU(" -report_sp_time_verbose  Report SP execution time at each timestep\n");
    BU(" -report_rp_time          Report RP execution time\n");
    BU(" -timers_on_timestep <timestep>     Turn on timers at given timestep\n");
    BU(" -timers_off_timestep <timestep>    Turn off timers at given timestep\n");
    BU(" -report_sp_threadtime    Report SP whole-run execution time by thread (summary)\n");
    BU(" -report_sp_threadtime_verbose    Report SP whole-run execution time by thread, per SP\n");
    BU(" -debug_sps               Print SP PIDs\n");
    BU(" -diag <N>                Average diagnostics over N timesteps\n");
    BU(" -ri_diagnostics          Dump diagnostics into spare slots of meas windows\n");
    BU(" -suppress_final_status   Don't print Finished message\n");
    BU(" -report_phase_basetime   Report time coupling phase start times in base timesteps\n");

    BU(" -delay_uds_warnings <N>  Wait N timesteps before issuing uds warnings\n");

    BU(" -disable_scalar_diffusivity_bound  Disable the diffusivity lower bound for user defined scalar\n");

    //BU(" -disable_calm            Disable cooling air leakage model for the simulation\n");

    //BU(" -smart_seed_contact_angle   Seed boundary voxels with contact angle of component 0\n");
    //BU(" -output_surface_contact_angle   output contact angle of component 0 in surface measurement\n");
    //BU(" -output_surface_contact_angle_only   output contact angle of component 0 only in surface measurement\n");
    //BU(" -output_component_velocity   output component velocity in measurement\n");
    //BU(" -output_mass_exchange    output mass exchange in fluid measurement\n");
    //BU(" -major_flow_direction <direction> Set major flow direction. 0 turns off hysteresys model. 1 2 3 represent x y z direction\n");
    //BU(" -large_pore          trigger 5G large pore solver (porous media solver)\n");

    BU("-enable_tangential_shell_conduction    Compute tangential heat flux in conduction shell in addition to normal heat flux");
    BU("-use_implicit_shell_solver    Use the implicit shell solver for tangential heat conduction on shells");
    BU("-use_implicit_solid_solver    Use the implicit solid solver for heat conduction in solids");

#ifdef NOT_DEFINED
    {
      char name[MAXHOSTNAMELEN];
      int status = gethostname(&name[0], MAXHOSTNAMELEN);
      cSTRING result = strstr(name, "exa.com");
      if (result) {
#endif
	BU("\nDebugging Options:\n");
#ifdef NOT_DEFINED
      }
    }
#endif
  }

  fprintf(stderr, "\n%s", std_args_description_string);
  err_exit();
}


/*--------------------------------------------------------------------------*
 * Printing the header
 *--------------------------------------------------------------------------*/
static VOID print_header(VOID)
{
  /* Print title */
  cSTRING product_version = jobctl_get_distname();
  if (product_version == NULL) product_version = "DIST";
  msg_print("The PowerFLOW simulator (CP Version %s.%s)", 
	    product_version, CP_VERSION);

  /* Print copyright notice */
  copyright_print();
}


/*--------------------------------------------------------------------------*
 * Timers
 *--------------------------------------------------------------------------*/

/* The timers must be entered in this table according to their ID. */
sTIMER cp_timers[CP_NUM_TIMERS] = {
  WTIMER_INIT(CP_TOTAL_TIMER, "Total run time"),
  WTIMER_INIT(CP_INITIALIZATION_TIMER, "Initialization time"),
  WTIMER_INIT(CP_TIMESTEP_TIMER, "Time for timestep"),
  WTIMER_INIT(CP_SIM_TIMER, "Simulation time"),
  WTIMER_INIT(CP_SEED_TIMER, "Seeding time"),
  WTIMER_INIT(CP_CKPT_TIMER, "Checkpoint time")
};

static VOID convert_tildas_to_spaces(STRING str)
{
  char *tilda_ptr = str;
  while ((tilda_ptr = strchr(tilda_ptr, '~')))
    *tilda_ptr++ = ' ';
}

BOOLEAN allows_overcommit()
{
  FILE *fp = fopen("/proc/sys/vm/overcommit_memory","r");
  BOOLEAN ret_val = TRUE;
  if (fp) {
    int val;
    fscanf(fp,"%d",&val);
    if (val == 2) {
      ret_val = FALSE;
      msg_warn("Kernel parameter \"overcommit_memory\" is set to 2. This may result in a minor increase in initialization time.");
    }
    fclose(fp);
  }
  return ret_val;

}

static inline dFLOAT strtod_and_skip_whitespace(cSTRING &str, cSTRING field_name, cSTRING option_name)
{
  char *field_end;
  dFLOAT value = strtod(str, &field_end);
  while (isspace(*field_end))
    field_end++;
  
  if (*field_end == ',' || *field_end == '\0') {
    str = field_end;
    return value;
  }
  msg_error("%s field of --%s option is invalid", field_name, option_name);
  return -1; // keep compiler happy
}

static inline dFLOAT strtod_and_skip_whitespace_and_advance_to_next(cSTRING &str, cSTRING field_name, 
                                                                    cSTRING next_field_name, cSTRING option_name)
{
  char *field_end;
  dFLOAT value = strtod(str, &field_end);
  while (isspace(*field_end))
    field_end++;
  
  if (*field_end == ',') {
    str = field_end + 1;
    return value;
  }
  if (*field_end == '\0') {
    msg_error("%s field of --%s option is missing", next_field_name, option_name);
    return -1; // keep compiler happy
  }
  msg_error("%s field of --%s option is invalid", field_name, option_name);
  return -1; // keep compiler happy
}

static VOID parse_meas_timing(int *argc, char *argv[])
{
  cSTRING meas_timing;
  while ((meas_timing = parse_arg_string("-meas_timing", argc, argv))) {
    MEAS_TIMING_OPTION option = cnew sMEAS_TIMING_OPTION;
    asINT32 field_len = strcspn(meas_timing, ",");
    if (field_len <= 0)
      msg_error("Improperly formatted --meas_timing option.");
    STRING meas_window_name = xnew char[ field_len + 1 ];
    strncpy(meas_window_name, meas_timing, field_len);
    meas_window_name[field_len] = '\0';
    option->meas_window_name = meas_window_name;
    if (meas_timing[field_len] == '\0')
      msg_error("Improperly formatted --meas_timing option.");
    meas_timing += field_len + 1;

    option->end_time = -1;
    option->period = -1;
    option->average_interval = -1;

    option->next = cp_info.cmdline_meas_timing_options;
    cp_info.cmdline_meas_timing_options = option;

    option->start_time = strtod_and_skip_whitespace(meas_timing, "start_time", "meas_timing");
    if (*meas_timing == '\0')
      continue;
    meas_timing++; // skip past comma

    option->end_time = strtod_and_skip_whitespace(meas_timing, "end_time", "meas_timing");
    if (*meas_timing == '\0')
      continue;
    meas_timing++; // skip past comma

    option->period = strtod_and_skip_whitespace(meas_timing, "period", "meas_timing");
    if (*meas_timing == '\0')
      continue;
    meas_timing++; // skip past comma

    option->average_interval = strtod_and_skip_whitespace(meas_timing, "interval", "meas_timing");
    if (*meas_timing == '\0')
      continue;
    meas_timing++; // skip past comma

    option->time_unit = strsave(meas_timing);
  }
}

static VOID parse_meas_timing_acoustics(int *argc, char *argv[])
{
  cSTRING meas_timing;
  while ((meas_timing = parse_arg_string("-meas_timing_acoustics", argc, argv))) {
    MEAS_TIMING_OPTION option = cnew sMEAS_TIMING_OPTION;
    option->is_acoustic_timing = TRUE;
    asINT32 field_len = strcspn(meas_timing, ",");
    if (field_len <= 0)
      msg_error("Improperly formatted --meas_timing_acoustics option.");
    STRING meas_window_name = xnew char[ field_len + 1 ];
    strncpy(meas_window_name, meas_timing, field_len);
    meas_window_name[field_len] = '\0';
    option->meas_window_name = meas_window_name;
    if (meas_timing[field_len] == '\0')
      msg_error("Improperly formatted --meas_timing_acoustics option.");
    meas_timing += field_len + 1;

    option->average_interval = -1;

    option->next = cp_info.cmdline_meas_timing_options;
    cp_info.cmdline_meas_timing_options = option;

    option->start_time = strtod_and_skip_whitespace_and_advance_to_next(meas_timing, "start_time", "end_time", "meas_timing_acoustics");
    option->end_time   = strtod_and_skip_whitespace_and_advance_to_next(meas_timing, "end_time",   "min_freq", "meas_timing_acoustics");
    option->min_freq   = strtod_and_skip_whitespace_and_advance_to_next(meas_timing, "min_freq",   "max_freq", "meas_timing_acoustics");
    option->max_freq   = strtod_and_skip_whitespace_and_advance_to_next(meas_timing, "max_freq",   "min_cycles", "meas_timing_acoustics");
    option->min_cycles = strtod_and_skip_whitespace_and_advance_to_next(meas_timing, "min_cycles", "nyquist_multiplier", "meas_timing_acoustics");

    option->nyquist_multiplier = strtod_and_skip_whitespace            (meas_timing, "nyquist_multiplier", "meas_timing_acoustics");
    if (*meas_timing == '\0')
      continue;
    meas_timing++; // skip past comma

    option->average_interval = strtod_and_skip_whitespace              (meas_timing, "interval", "meas_timing_acoustics");
    if (*meas_timing == '\0')
      continue;
    meas_timing++; // skip past comma

    field_len = strcspn(meas_timing, ",");
    option->time_unit = xnew char[field_len + 1]; 
    strncpy((STRING)option->time_unit, meas_timing, field_len);
    ((STRING)option->time_unit)[field_len] = '\0';
    if (meas_timing[field_len] == '\0')
      continue;
    meas_timing += field_len + 1; // skip past comma

    option->freq_unit = strsave(meas_timing);
  }
}

// PR 38335: exaqsub option to support BCs via sampled face meas file
// The user provides the folder locations of the input measurement files for various faces.
static void parse_sampled_face_meas_bc_args(int *argc, char *argv[])
{
  cp_info.default_seed_from_meas_location = "";

  cSTRING sampled_face_meas_bc_arg = parse_arg_string("-sampled_face_meas_bc", argc, argv);

  while(sampled_face_meas_bc_arg != NULL) {
    std::vector<std::string> args_list;
    
    while(1) {
      asINT32 field_len = strcspn(sampled_face_meas_bc_arg, ",");
      if (field_len <= 0) {
        if (*sampled_face_meas_bc_arg != '\0')
          msg_error("Improperly formatted --sampled_face_meas_bc option");
        break;
      }
      args_list.push_back(std::string(sampled_face_meas_bc_arg, field_len));

      if (sampled_face_meas_bc_arg[field_len] == '\0')
        break;
      sampled_face_meas_bc_arg += field_len + 1;
    }

    switch(args_list.size()) {
      case 0:
        // This probably means that the user provided an empty string as the argument
        // An error is thrown earlier in the code if this is the case, and this part is never reached
        break;
      case 1:
        if (cp_info.default_seed_from_meas_location == "") {
          cp_info.default_seed_from_meas_location = *(args_list.end()-1);
        } else {
          msg_error("Paths \"%s\" and \"%s\" specified via -sampled_face_meas_bc do not have any accompanying "
                    "list of part names. Choose only one path to use as the default location for the sampled face "
                    "measurement files.",
                    cp_info.default_seed_from_meas_location.c_str(),
                    args_list[args_list.size()-1].c_str());
        }
      default:
        ccDOTIMES(i, args_list.size()-1) {
          if (cp_info.seed_from_meas_location_map.find(args_list[i]) == cp_info.seed_from_meas_location_map.end()) {
            cp_info.seed_from_meas_location_map[args_list[i]] = args_list[args_list.size()-1];
          } else {
            msg_error("Multiple sampled face measurement file locations have been provided for part \"%s\" "
                      "via -sampled_face_meas_bc. Please specify only one path.", args_list[i].c_str());
          }
        }
        break;
    }

    sampled_face_meas_bc_arg = parse_arg_string("-sampled_face_meas_bc", argc, argv);
  }
  if (cp_info.default_seed_from_meas_location == "") {
    cp_info.default_seed_from_meas_location = ".";
  }
}

/*--------------------------------------------------------------------------*
 * Simulation arguments
 *--------------------------------------------------------------------------*/
/* This parses an argument list for the standard "simulation" arguments and
 * fills in a static SIM_ARGS structure.  It removes the simulation
 * arguments from the argument list. 
 */
static SIM_ARGS parse_sim_args(cSTRING progname, int *argc, char *argv[])
{
  static sSIM_ARGS args;

  /* Initialize the default arguments */
  /*    Shared initialization */
  args.mpi_environment_p 	= FALSE;
  args.report_total_time_p 	= TRUE;
  args.report_performance_p = TRUE;
  args.time_every 		= 0;
  args.ignore_locations_p 	= FALSE;
  args.ignore_measurements_p 	= FALSE;
  args.clear_all_p		= FALSE;
  args.sp_binary_filename 	= NULL;
  args.run_options 		= SIM_DEFAULT;
  args.run_options             |= SIM_NO_TIMEOUT;
  args.checkpoint_interval      = 0;
  args.debug_sps_p		= FALSE;
  args.n_send_elements          = N_SEND_BUFFER_ELEMENTS;
  args.num_timesteps            = 0;
  args.timers_on_timestep       = TIMESTEP_NEVER;
  args.timers_off_timestep      = TIMESTEP_NEVER;
  args.n_lb_base_steps          = 1;
  args.n_t_base_steps           = 1;
  args.n_ke_base_steps          = 1;
  args.n_uds_base_steps         = 1;
  args.n_uds_base_steps         = 1;
  args.n_conduction_base_steps  = 1;
  args.n_radiation_base_steps  = -1;
  args.acous_start_time         = -1;
  args.momentum_freeze_start_time = -1;
  args.freeze_momentum_field    = FALSE;
  args.thermal_timestep_ratio = -1;
  args.suppress_seed_requirement    = FALSE;
  args.use_seed_cache           = TRUE;
  //args.do_extrapolate 	        = FALSE;
  //args.smart_seed_boundaries	= FALSE;
  //args.smart_seed_contact_angle	= FALSE;
  args.output_surface_contact_angle = FALSE;
  args.output_surface_contact_angle_only = FALSE;
  args.output_component_velocity = FALSE;
  args.output_mass_exchange      = FALSE;
  args.disable_calm              = FALSE;
  args.use_PF6_VLES_model        = FALSE;
  args.checkpoint_body_force     = FALSE;
  args.seed_body_force           = FALSE;
  args.laplace                   = FALSE;
  args.seed_body_force_scale         = 0;
  args.seed_mks                 = FALSE;
  args.autostop_all_off     = FALSE;
  args.umask                    = 0;
  args.fluid_turb_solver_1	= FALSE;
  args.fixed_temp_walls		= FALSE;
  args.local_vel_fan_model	= FALSE;
  args.no_swirl_model		= FALSE;
  args.is_ke_base_step_arg_read = FALSE;
  args.is_cond_base_step_arg_read = FALSE;
  args.is_rad_base_step_arg_read = FALSE;
  args.ablm_cap			= -1;
  args.mme_chkpnt_at_end	= FALSE;
  args.full_chkpnt_at_end	= FALSE;
  args.parallel_io	= FALSE;
  args.allow_immediate_checkpoints = FALSE;
  args.barrier_instead_of_finalize = FALSE;
  args.read_old_cdi_p 	        = FALSE;
  args.max_vel = NULL;
  args.ignore_vel_n_timesteps = 1000;  
  args.vel_warning_limit = 100000;
  args.fpe_exit_limit = 100;
  args.max_temp = NULL;
  args.min_temp = NULL;
  args.ignore_temp_n_timesteps = 1000;
  args.temp_warning_limit = 100000;
  args.ignore_uds_n_timesteps = 1000;
  args.dns_nu_over_t_floor = -1;
  args.major_flow_direction = 0;
  args.allow_missing_meas_files = TRUE;
  args.accept_5_0_thermal_feedback_p = FALSE;
  args.accept_old_ht_cdi_p = FALSE;
  args.accept_old_turb_cdi_p = FALSE;
  args.suppress_final_status_p = FALSE;
  args.report_coupling_phase_basetime = FALSE;
  
  /* for UDS */
  args.disable_scalar_diffusivity_bound = FALSE;

   /* For rotation of seed data */
  //args.is_seed_rotate_vel = FALSE;
  //args.seed_rotate_vel_angle = 0.0;

  /* For particle modeling */
  args.disable_particle_modeling = FALSE;
  args.enable_film_trajectory_measurement = FALSE;

  args.disable_all_meas_windows = FALSE;
  args.n_cpnode_ranks = 0;
  args.n_cpnode_sockets = 0;
  args.n_cpnode_cores_per_socket = 0;
  args.n_cpnode_threads_per_core = 0;

#if POINTER_IS_64BITS
  // if the kernel does not allow overcommit, then we do not reserve large
  // chunks of vm addresses for non-shob items (RT 16927)
  args.no_reserve_addr_space           = !allows_overcommit();
#else
  args.no_reserve_addr_space           = TRUE;
#endif
  args.no_mpi_request_free = FALSE;
  args.short_mpi_thread_sleep = FALSE;

#if 0
  // PR 27870: altus cluster at EXA uses MX, which leads to problems when calling
  // MPI_Request_free. Disable calls to MPI_Request_free on altus*.exa.com
  {
    char host_name[MAXHOSTNAMELEN];
    char domain_name[MAXHOSTNAMELEN];
    int status = gethostname(&host_name[0], MAXHOSTNAMELEN);
    if (!status) {
      status = getdomainname(&domain_name[0], MAXHOSTNAMELEN);
      if (!status && strstr(domain_name, "exa.com") && strstr(host_name, "altus")) {
        args.no_mpi_request_free = TRUE;
      }
    }
    // UDS
    if (cp_info.is_uds_transport && !cp_info.is_5g_sim && IS_FLUID_SEED) {
      sINT32 total_n_uds_vars = cp_info.n_scalars * (int)DGF_N_SEED_UDS_VARS;
      seed_var_spec->n_uds_vars = total_n_uds_vars - n_uds_vars;
      seed_var_spec->seed_uds_var_indices = xnew sINT32[seed_var_spec->n_uds_vars];
      bool is_uds_var_excluded[ MAX_N_SEED_UDS_VARS] = { false };
      ccDOTIMES(i, n_uds_vars)
	is_uds_var_excluded[uds_var_indicies[i]] = true;

      asINT32 v = 0;
      ccDOTIMES(i, total_n_uds_vars) {
	if (!is_uds_var_excluded[i])
	  seed_var_spec->seed_uds_var_indices[v++] = i;
      }     
    } else {
      seed_var_spec->n_uds_vars = 0;
      seed_var_spec->seed_uds_var_indices = NULL;
    }
  }
#endif

  args.gpu = FALSE;
  args.gpu_repeatable = FALSE;
  
  BOOLEAN found_ignore_vel = FALSE;
  BOOLEAN found_ignore_temp = FALSE;
  BOOLEAN found_ignore_uds = FALSE;


  if (parse_arg_present_p("-h", argc, argv) || 
      parse_arg_present_p("-help", argc, argv) ||
      parse_arg_present_p("-?", argc, argv))
    bad_usage(progname);

  /* Parse arguments */
  /*   Shared arguments */
  if (parse_arg_present_p("-report_mme", argc, argv))
    args.run_options |= SIM_REPORT_MME;
  if (parse_arg_present_p("-ri_diagnostics", argc, argv))
    args.run_options |= SIM_RI_DIAGNOSTICS;
  if (parse_arg_present_p("-report_mme_verbose", argc, argv))
    args.run_options |= SIM_REPORT_MME_VERBOSE;
  if (parse_arg_present_p("-report_occupied", argc, argv))
    args.run_options |= SIM_REPORT_OCCUPIED;
  if (parse_arg_present_p("-report_time", argc, argv))
    args.report_total_time_p = TRUE;  /* This is now the default. */
  if (parse_arg_present_p("-no_report_time", argc, argv))
    args.report_total_time_p = FALSE;
  if (parse_arg_present_p("-no_report_performance", argc, argv))
    args.report_performance_p = FALSE;
  if (parse_arg_present_p("-report_cpu_affinity", argc, argv))
    args.run_options |= SIM_REPORT_CPU_AFFINITY;
  if (parse_arg_present_p("-debug_sps", argc, argv))
    args.debug_sps_p = TRUE;
  if (parse_arg_present_p("-report_total_memory", argc, argv))
    args.run_options |= SIM_REPORT_TOTAL_MEMORY;
  if (parse_arg_present_p("-small_mem_seed", argc, argv))
    args.use_seed_cache = TRUE;
  if (parse_arg_present_p("-no_small_mem_seed", argc, argv))
    args.use_seed_cache = FALSE;
  if (parse_arg_present_p("-disable_meas", argc, argv))
    args.disable_all_meas_windows = TRUE;


  // By default collect new style measurements
  cp_info.is_std_meas_mme_p = FALSE;

  // Halt resumed simulation if any measurement files are missing.
  if (parse_arg_present_p("-abort_if_missing_meas_files",argc,argv)) 
    args.allow_missing_meas_files = FALSE;

  // These are used for PowerTHERM jobs to determine cores available for binding
  args.n_cpnode_ranks = parse_arg_uINT32("-n_cpnode_ranks", argc, argv);
  args.n_cpnode_sockets = parse_arg_uINT32("-n_cpnode_sockets", argc, argv);
  args.n_cpnode_cores_per_socket = parse_arg_uINT32("-n_cpnode_cores_per_socket", argc, argv);
  args.n_cpnode_threads_per_core = parse_arg_uINT32("-n_cpnode_threads_per_core", argc, argv);

  args.decomp_file = parse_arg_string("-decomp_file", argc, argv);
  args.n_processors = parse_arg_string("-n_processors", argc, argv);
  //  args.pmodel_file = parse_arg_string("-pmodel_file", argc, argv);
  args.target_platform = parse_arg_string("-target_platform", argc, argv);
  args.decomp_message_log = parse_arg_string("-decomp_message_log", argc, argv);

  // This used to tell the CP whether to perform an inline decomposition. Now we just
  // ignore the flag.
  parse_arg_present_p("-decomp", argc, argv);

  args.unlink_input_after_init = parse_arg_present_p("-unlink_input_after_init", argc, argv);

  args.read_old_cdi_p = parse_arg_present_p("-read_old_cdi", argc, argv);
  args.cdi_filename = parse_arg_string("-cdi_file", argc, argv);
  if (args.cdi_filename && (strlen(args.cdi_filename) == 0)) {
    msg_error("CDI filename is an empty string");
    args.cdi_filename = NULL;
  }

  if (args.cdi_filename == NULL) {
    msg_error("Missing required CDI file argument (via -cdi_file option)");
  } else if (access(args.cdi_filename, R_OK) != 0){
    msg_error("Unable to open CDI file \"%s\"", args.cdi_filename);
  }

  cp_info.table_search_path = parse_arg_string("-table_path", argc, argv);

  cp_info.is_meas_window_inclusion_list = FALSE;
  while (1) { // process all -meas_exclude and -meas_include options
    cSTRING meas_windows_excluded = parse_arg_string("-meas_exclude", argc, argv);
    cSTRING meas_windows_included = parse_arg_string("-meas_include", argc, argv);
    if (meas_windows_excluded && meas_windows_included)
      msg_error("--meas_exclude and --meas_include options are mutually exclusive");
    cSTRING meas_win_names = meas_windows_excluded;
    if (meas_windows_included) {
      meas_win_names = meas_windows_included;
      cp_info.is_meas_window_inclusion_list = TRUE;
    }

    if (meas_win_names == NULL) {
      break;
    } else {
      while (1) {
        MEAS_INCLUDE_EXCLUDE_OPTION option = xnew sMEAS_INCLUDE_EXCLUDE_OPTION;
        asINT32 field_len = strcspn(meas_win_names, ",");
        if (field_len <= 0) {
          if (*meas_win_names != '\0')
            msg_error("Improperly formatted --%s option", cp_info.is_meas_window_inclusion_list ? "meas_include" : "meas_exclude");
          break;
        }
        STRING meas_window_name = xnew char[ field_len + 1 ];
        strncpy(meas_window_name, meas_win_names, field_len);
        meas_window_name[field_len] = '\0';
        option->meas_window_name = meas_window_name;
        option->next = cp_info.cmdline_include_exclude_meas_windows;
        cp_info.cmdline_include_exclude_meas_windows = option;
        if (meas_win_names[field_len] == '\0')
          break;
        meas_win_names += field_len + 1;
      }
    }
  }

  cp_info.has_autostop_on_list = FALSE;
  cp_info.has_autostop_off_list = FALSE;
  cSTRING off_monitor_names = parse_arg_string("-autostop_off", argc, argv);
  cSTRING on_monitor_names  = parse_arg_string("-autostop_on", argc, argv);
  if (off_monitor_names) {
    cp_info.has_autostop_off_list = TRUE;
    while (1) {
      AUTOSTOP_ON_OFF_OPTION off_option = xnew sAUTOSTOP_ON_OFF_OPTION;
      asINT32 field_len = strcspn(off_monitor_names, ",");
      if (field_len <= 0) {
        if (*off_monitor_names != '\0')
          msg_error("Improperly formatted --autostop_off option");
        break;
      }
      STRING monitor_name = xnew char[ field_len + 1 ];
      strncpy(monitor_name, off_monitor_names, field_len);
      monitor_name[field_len] = '\0';
      off_option->monitor_name = monitor_name;
      off_option->next = cp_info.cmdline_autostop_off_monitors;
      cp_info.cmdline_autostop_off_monitors = off_option;
      if (off_monitor_names[field_len] == '\0')
        break;
      off_monitor_names += field_len + 1;
    }
  }
  if (on_monitor_names) {
    cp_info.has_autostop_on_list = TRUE;
    while(1) {
      AUTOSTOP_ON_OFF_OPTION on_option = xnew sAUTOSTOP_ON_OFF_OPTION;
      asINT32 field_len = strcspn(on_monitor_names, ",");
      if (field_len <= 0) {
        if (*on_monitor_names != '\0')
          msg_error("Improperly formatted --autostop_on option");
        break;
      }
      STRING monitor_name = xnew char[ field_len + 1 ];
      strncpy(monitor_name, on_monitor_names, field_len);
      monitor_name[field_len] = '\0';
      on_option->monitor_name = monitor_name;
      on_option->next = cp_info.cmdline_autostop_on_monitors;
      cp_info.cmdline_autostop_on_monitors = on_option;
      if (on_monitor_names[field_len] == '\0')
        break;
      on_monitor_names += field_len + 1;
    }
  }
  
  parse_meas_timing(argc, argv);
  parse_meas_timing_acoustics(argc, argv);

  cSTRING before_table;
  while ((before_table = parse_arg_string("-before_table", argc, argv))) {
    PRE_TABLE_CMD cmd = xnew sPRE_TABLE_CMD;

    asINT32 field_len = strcspn(before_table, ",");
    if (field_len <= 0)
      msg_error("Improperly formatted --before_table option.");
    STRING table_name = xnew char[ field_len + 1];
    strncpy(table_name, before_table, field_len);
    table_name[field_len] = '\0';
    cmd->table_name = table_name;
    before_table += field_len + 1;

    cmd->cmd_string = before_table;

    if (!is_cmd_executable_via_system(cmd->cmd_string))
      msg_error("The shell command \"%s\" provided with the --before_table option"
		" either cannot be found or is not executable.",
		cmd->cmd_string);

    cmd->next = cp_info.cmd_line_pre_table_cmds;
    cp_info.cmd_line_pre_table_cmds = cmd;
  }

  cSTRING after_meas;
  while ((after_meas = parse_arg_string("-after_meas", argc, argv))) {
    POST_MEAS_CMD cmd = cnew sPOST_MEAS_CMD;

    asINT32 field_len = strcspn(after_meas, ",");
    if (field_len <= 0)
      msg_error("Improperly formatted --after_meas option.");
    STRING meas_win_name = xnew char[ field_len + 1];
    strncpy(meas_win_name, after_meas, field_len);
    meas_win_name[field_len] = '\0';
    cmd->meas_window_name = meas_win_name;
    after_meas += field_len + 1;

    field_len = strcspn(after_meas, ",");
    if (field_len <= 0) msg_error("Improperly formatted --after_meas option.");
    cmd->issue_frame = strtol(after_meas, NULL, 0);
    after_meas += field_len + 1;

    if (cmd->issue_frame < 0)
      msg_error("Negative start frame in --after_meas option.");

    field_len = strcspn(after_meas, ",");
    if (field_len <= 0) msg_error("Improperly formatted --after_meas option.");
    cmd->period = strtol(after_meas, NULL, 0);
    after_meas += field_len + 1;

    if (cmd->period < 0)
      msg_error("Negative period in --after_meas option.");

    cmd->cmd_string = after_meas;

    if (!is_cmd_executable_via_system(cmd->cmd_string))
      msg_error("The shell command \"%s\" provided with the --after_meas option"
		" either cannot be found or is not executable.",
		cmd->cmd_string);

    cmd->next = cp_info.cmd_line_post_meas_cmds;
    cp_info.cmd_line_post_meas_cmds = cmd;
  }

  cSTRING octal_umask = parse_arg_string("-umask", argc, argv);
  if (octal_umask != NULL) {
    args.umask_override = TRUE;
    unsigned umask;
    sscanf(octal_umask, "%o", &umask);	
    args.umask = umask;
  } else {
    args.umask_override = FALSE;
    args.umask = 0;
  }

  if (parse_arg_present_p("-no_swirl_model", argc, argv))
    args.no_swirl_model = TRUE;

  uINT32 ablm_cap = parse_arg_uINT32("-ablm_cap", argc, argv);
  if (ablm_cap != 0xFFFFFFFF)
    args.ablm_cap = ablm_cap;

  if (parse_arg_present_p("-fluid_turb_solver_1", argc, argv))
    args.fluid_turb_solver_1 = TRUE;

  if (parse_arg_present_p("-local_vel_fan_model", argc, argv))
    args.local_vel_fan_model = TRUE;

  if (args.no_swirl_model || (args.ablm_cap >= 0) 
      || args.fluid_turb_solver_1 || args.local_vel_fan_model) {
    msg_print("Active physics switches:  %s%s%s%s",
	      args.no_swirl_model ? "-no_swirl_model " : "",
	      (args.ablm_cap > 0) ? "-ablm_cap " : "",
	      args.fluid_turb_solver_1 ? "-fluid_turb_solver_1 " : "",
	      args.local_vel_fan_model ? "-local_vel_fan_model " : "");
  }
	      

  args.max_vel = parse_arg_string("-max_vel", argc, argv);
  if (args.max_vel) // exarun was forced to convert spaces to tildas because of shell weirdness
    convert_tildas_to_spaces((STRING)args.max_vel);

  sINT32 ignore_vel_n_timesteps;
  if (parse_arg_sINT32("-delay_vel_warnings", ignore_vel_n_timesteps, argc, argv)) {
    found_ignore_vel = TRUE;
    args.ignore_vel_n_timesteps = ignore_vel_n_timesteps;
  }

  sINT32 vel_warning_limit;
  if (parse_arg_sINT32("-limit_vel_warnings", vel_warning_limit, argc, argv)) {
    args.vel_warning_limit = vel_warning_limit < 0 ? sINT32_MAX : vel_warning_limit;
  }

  sINT32 ignore_uds_n_timesteps;
  if (parse_arg_sINT32("-delay_uds_warnings", ignore_uds_n_timesteps, argc, argv)) {
    found_ignore_uds = TRUE;
    args.ignore_uds_n_timesteps = ignore_uds_n_timesteps;
  }

  sINT32 fpe_exit_limit;
  if (parse_arg_sINT32("-fpe_exit_limit", fpe_exit_limit, argc, argv)) {
    args.fpe_exit_limit = fpe_exit_limit <= 0 ? sINT32_MAX : fpe_exit_limit;
  }
  cp_info.n_fpes_allowed_in_timestep = args.fpe_exit_limit;

#if	0
  spSetEerMaxErrors(SP_EER_VELOCITY_MAX, args.vel_warning_limit);
  spSetEerMaxErrors(SP_EER_VEL_TOO_BIG, args.vel_warning_limit);
#else
  cp_change_max_errors(SP_EER_VEL_TOO_BIG, args.vel_warning_limit);
  cp_change_max_errors(SP_EER_VEL_OUT_OF_RANGE, args.vel_warning_limit);
  cp_change_max_errors(SP_EER_MACH_TOO_BIG, args.vel_warning_limit);
#endif

  args.max_temp = parse_arg_string("-max_temp", argc, argv);
  if (args.max_temp) // exarun was forced to convert spaces to tildas because of shell weirdness
    convert_tildas_to_spaces((STRING)args.max_temp);

  args.min_temp = parse_arg_string("-min_temp", argc, argv);
  if (args.min_temp) // exarun was forced to convert spaces to tildas because of shell weirdness
    convert_tildas_to_spaces((STRING)args.min_temp);

  sINT32 ignore_temp_n_timesteps;
  if (parse_arg_sINT32("-delay_temp_warnings", ignore_temp_n_timesteps, argc, argv)) {
    found_ignore_temp = TRUE;
    args.ignore_temp_n_timesteps = ignore_temp_n_timesteps;
  }

  sINT32 temp_warning_limit;
  if (parse_arg_sINT32("-limit_temp_warnings", temp_warning_limit, argc, argv)) {
    args.temp_warning_limit = temp_warning_limit < 0 ? sINT32_MAX : temp_warning_limit;
  }

#if	0
  spSetEerMaxErrors(SP_EER_TEMP_TOO_BIG, args.temp_warning_limit);
  spSetEerMaxErrors(SP_EER_TEMP_TOO_SMALL, args.temp_warning_limit);
#else
  cp_change_max_errors(SP_EER_TEMP_TOO_BIG, args.temp_warning_limit);
  cp_change_max_errors(SP_EER_TEMP_TOO_SMALL, args.temp_warning_limit);
#endif

  cSTRING dns_nu_over_t_floor_str = parse_arg_string("-dns_nu_over_t_floor", argc, argv);
  if (dns_nu_over_t_floor_str != NULL)
    args.dns_nu_over_t_floor = atof(dns_nu_over_t_floor_str);

  if ((args.major_flow_direction = parse_arg_uINT32("-major_flow_direction", argc, argv)) == 0xFFFFFFFF)
    args.major_flow_direction = 0;
  if (args.major_flow_direction < 0 || args.major_flow_direction > 3)
    args.major_flow_direction = 0;

  if ((args.momentum_freeze_start_time = parse_arg_uINT32("-momentum_freeze_start_time", argc, argv)) == 0xFFFFFFFF)
    args.momentum_freeze_start_time = -1;

  if (args.momentum_freeze_start_time > 0)
    msg_error("The -momentum_freeze_start_time option is no longer supported.\n");
   
  if ((args.thermal_timestep_ratio = parse_arg_uINT32("-thermal_timestep_ratio", argc, argv)) == 0xFFFFFFFF)
    args.thermal_timestep_ratio = -1;

  if (parse_arg_present_p("-freeze_momentum_field", argc, argv))
    args.freeze_momentum_field = TRUE;

  if (args.freeze_momentum_field == TRUE)
    msg_error("The -freeze_momentum_field has been superseded by an option in PowerCASE.\n");

  if ( args.thermal_timestep_ratio > 0)
    msg_error("The -thermal_timestep_ratio has been superseded by an option in PowerCASE.\n");

  if (parse_arg_present_p("-suppress_seed_requirement", argc, argv))
    args.suppress_seed_requirement = TRUE;

  if (parse_arg_present_p("-fixed_temp_walls", argc, argv))
    args.fixed_temp_walls = TRUE;

  // smart seeding
  g_seed_ctl.set_has_boundary_seed_controller(FALSE);

  // smart seed with a single file
  cSTRING seed_fname = NULL;
  asINT32 seed_frame = -1;
  while (seed_fname = parse_arg_string_and_optnl_asINT32("-smart_seed", 
				                                                  argc, argv,
				                                                  &seed_frame)) {
    if (strlen(seed_fname) == 0) {
      msg_error("Smart seed filename is an empty string");
    }

    SMART_SEED_CONTROL seed_controller = g_seed_ctl.add_seed_controller(seed_fname, seed_frame); 

    // Now parse the seeding arguments pertaining to this seed file
    seed_controller->parse_smart_seed_options(argc, argv);

  }

  // Read in parameters for multiple smart seed controllers
  // This is distinct from the original smart seeding with a single file
  cSTRING multi_seed_string = NULL;
  const std::string delims("|");
  while (multi_seed_string = parse_arg_string("-mseed", argc, argv)) {

    std::vector<std::string> multi_seed_args = EXA_STR::StripSplit(multi_seed_string, delims);

    cSTRING seed_fname = multi_seed_args[0].c_str();
    if (strlen(seed_fname) == 0) {
      msg_error("Smart seed filename is an empty string");
    }
    SMART_SEED_CONTROL seed_controller = g_seed_ctl.add_seed_controller(seed_fname); 

    // Now parse the seeding arguments pertaining to this seed file
    //seed_controller->parse_multi_smart_seed_options(argc, argv);
    multi_seed_args.erase(multi_seed_args.begin()); // remove the filename from the argument vector
    seed_controller->parse_multi_smart_seed_options(multi_seed_args);
  }

  if (g_seed_ctl.is_sim_seeded()) {
    if (!found_ignore_vel) // default for smart seed case is 0 timesteps
      args.ignore_vel_n_timesteps = 0;
    if (!found_ignore_temp) // default for smart seed case is 0 timesteps
      args.ignore_temp_n_timesteps = 0;
    if (!found_ignore_uds) // default for smart seed case is 0 timesteps
      args.ignore_uds_n_timesteps = 0;
  }

  // for creating ublk to mci map for reading dsm later on
  cSTRING dsm_meas_window_name = parse_arg_string("-dsm_meas_window", argc, argv);
  if(dsm_meas_window_name) {
    cp_info.dsm_reader = new cDSM_READER;
    cp_info.dsm_reader->set_meas_window_name(dsm_meas_window_name);
  }

  if (parse_arg_present_p("-large_pore", argc, argv))
    cp_info.is_large_pore_sim = TRUE;

  if (parse_arg_present_p("-output_surface_contact_angle", argc, argv))
    args.output_surface_contact_angle = TRUE;

  if (parse_arg_present_p("-output_surface_contact_angle_only", argc, argv))
    args.output_surface_contact_angle_only = TRUE;

  if (parse_arg_present_p("-output_component_velocity", argc, argv))
    args.output_component_velocity = TRUE;

  if (parse_arg_present_p("-output_mass_exchange", argc, argv))
    args.output_mass_exchange = TRUE;

  if (parse_arg_present_p("-autostop_all_off", argc, argv)) {
    args.autostop_all_off = TRUE;
  }

  if (parse_arg_present_p("-disable_calm", argc, argv))
    args.disable_calm = TRUE;

  if (parse_arg_present_p("-disable_scalar_diffusivity_bound", argc, argv))
    args.disable_scalar_diffusivity_bound = TRUE;

  if (parse_arg_present_p("-use_PF6_VLES_model", argc, argv))
    args.use_PF6_VLES_model = TRUE;

  if (parse_arg_present_p("-checkpoint_body_force", argc, argv))
    args.checkpoint_body_force = TRUE;

  if (parse_arg_present_p("-seed_body_force", argc, argv))
    args.seed_body_force = TRUE;

  if (parse_arg_present_p("-laplace", argc, argv))
    args.laplace = TRUE;

  sFLOAT seed_body_force_scale;
  if (parse_arg_sFLOAT("-seed_body_force_scale", seed_body_force_scale, argc, argv)) {
    args.seed_body_force_scale = seed_body_force_scale;
  }

  cp_info.is_bseed_faces_inclusion_list = FALSE;

  cSTRING bseed_faces = parse_arg_string("-bseed_exclude_faces", argc, argv);
  if (bseed_faces) {
    if (strlen(bseed_faces) == 0)
      msg_error("Empty face list passed to -bseed_exclude_faces");
    cp_info.is_bseed_faces_inclusion_list = FALSE;
  }

  cSTRING bseed_include_faces = parse_arg_string("-bseed_include_faces", argc, argv);
  if (bseed_include_faces) {
    if (bseed_faces)
      msg_error("--bseed_exclude_faces and --bseed_include_faces options are mutually exclusive");
    bseed_faces = bseed_include_faces;
    if (strlen(bseed_faces) == 0)
      msg_error("Empty face list passed to -bseed_include_faces");
    cp_info.is_bseed_faces_inclusion_list = TRUE;
  }
  
  if (bseed_faces && g_seed_ctl.smart_seed_boundaries()) {
    if (cp_info.is_bseed_faces_inclusion_list)
      msg_print("Only these faces will be subject to boundary seeding: %s", bseed_faces);
    else
      msg_print("These faces will be excluded from boundary seeding: %s", bseed_faces);
    const std::string delims(" ,;");
    std::string line(bseed_faces);
    std::string::size_type begIdx, endIdx;
    begIdx = line.find_first_not_of(delims);
    endIdx = line.find_first_of(delims, begIdx);
    
    while(begIdx != std::string::npos) {
      std::string face = line.substr(begIdx, endIdx-begIdx);
      if (face != "")
        cp_info.bseed_faces.push_back(face);
      begIdx = line.find_first_not_of(delims, endIdx);
      endIdx = line.find_first_of(delims, begIdx);
    }
    msg_print("");
  }

  cSTRING checkpoint_interval_str = parse_arg_string("-checkpoint_interval", argc, argv);
  if (checkpoint_interval_str == NULL)
    checkpoint_interval_str = parse_arg_string("-checkpoint", argc, argv); // old arg

  if (checkpoint_interval_str != NULL) {
    // Try to be robust in presence of very large values
    dFLOAT d_interval = atof(checkpoint_interval_str) + 0.5;
    TIMESTEP max_timestep = ~((TIMESTEP)1 << (8 * sizeof(TIMESTEP) - 1));
    if (d_interval > max_timestep)
      args.checkpoint_interval = 0; // Turn off checkpointing
    else
      args.checkpoint_interval = d_interval;

    args.checkpoint_override = TRUE;
  } else {
    args.checkpoint_override = FALSE;
    args.checkpoint_interval = 0;
  }

  cp_info.checkpoint_filename = parse_arg_string("-checkpoint_file", argc, argv);
  args.resume_filename = parse_arg_string("-resume_file", argc, argv);

  if (args.resume_filename && g_seed_ctl.is_sim_seeded())
    msg_error("Cannot both smart seed and resume from checkpoint");

  if (args.resume_filename && (SRI_SUCCESS == sri_probe_file(args.resume_filename))) {
    // Setting both smart seed filename and resume filename as identical
    // implies checkpoint resume from a MME file.

    SMART_SEED_CONTROL seed_controller = g_seed_ctl.add_seed_controller(args.resume_filename, -1, TRUE);

    // Now parse the seeding arguments pertaining to this seed file
    seed_controller->parse_mme_options(argc, argv);
    
  }
  
  if (args.resume_filename) {
    //mme resume should seed from two files if both realms active. 
    //Below will only check if another mme file is present and add to seed controller
    //if only one file present and later we found both realms are active,
    //it will throw an error and suggest use --seed instead in cp_stream_managers.cc
    cSTRING casename = filename_name(args.resume_filename); 
    cSTRING extension = filename_type(args.resume_filename);
    char *maybe_another_resume_filename = new char[strlen(casename) + strlen(extension) + 6];
    strcpy(maybe_another_resume_filename, casename);

    if (strcmp(extension, "fnc") == 0)
      strcat(maybe_another_resume_filename, ".vnc");
    else if (strcmp(extension,"vnc") == 0)
      strcat(maybe_another_resume_filename, ".fnc");
   
    if (g_seed_ctl.is_mme_checkpoint() && platform_file_present(maybe_another_resume_filename) && (SRI_SUCCESS == sri_probe_file(maybe_another_resume_filename))) {
      SMART_SEED_CONTROL seed_controller = g_seed_ctl.add_seed_controller(maybe_another_resume_filename, -1, TRUE);
      seed_controller->parse_mme_options(argc, argv); //COND_MME_CHECK: use the same seeding arguments?
    }
  } 
  
  if (g_seed_ctl.is_sim_seeded()) {
    if (args.cdi_filename == NULL) {
      if (g_seed_ctl.is_mme_checkpoint())
	      msg_error("Unable to resume from an MME results file without a CDI file (via -cdi_file option)");
      else
	      msg_error("Unable to smart seed without a CDI file (via -cdi_file option)");
    }
    else {
      asINT32	user_err_value = CDI_ERR_OK;
      CDI_INFO	cdi_info = cdi_open_for_read(args.cdi_filename, &user_err_value);

      if (cdi_info) {
	      cdi_close(cdi_info);
      } else {
	      msg_error("Unable to open CDI file %s, %s", args.cdi_filename,
		    cdi_get_error_state_string(user_err_value));
      }
    }
  }

  /* For particle modeling */
  if (parse_arg_present_p("-disable_particle_modeling", argc, argv))  //PR39448
    args.disable_particle_modeling = TRUE;

  if (parse_arg_present_p("-enable_film_trajectory_measurement", argc, argv))  //PR41929
    args.enable_film_trajectory_measurement = TRUE;


  std::map <std::string, DGF_BOUNDARY_SEED_VAR_TYPE > boundary_seed_var_string_to_seed_var_type;
  boundary_seed_var_string_to_seed_var_type["pressure"]            = DGF_BOUNDARY_SEED_VAR_PRESSURE;
  boundary_seed_var_string_to_seed_var_type["x_velocity"]          = DGF_BOUNDARY_SEED_VAR_XVEL;
  boundary_seed_var_string_to_seed_var_type["y_velocity"]          = DGF_BOUNDARY_SEED_VAR_YVEL;
  boundary_seed_var_string_to_seed_var_type["z_velocity"]          = DGF_BOUNDARY_SEED_VAR_ZVEL;
  boundary_seed_var_string_to_seed_var_type["temperature"]         = DGF_BOUNDARY_SEED_VAR_TEMP;
  boundary_seed_var_string_to_seed_var_type["turb_kinetic_energy"] = DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY;
  boundary_seed_var_string_to_seed_var_type["turb_dissipation"]    = DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION;
  boundary_seed_var_string_to_seed_var_type["x_mass_flux"]         = DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX;
  boundary_seed_var_string_to_seed_var_type["y_mass_flux"]         = DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX;
  boundary_seed_var_string_to_seed_var_type["z_mass_flux"]         = DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX;
  boundary_seed_var_string_to_seed_var_type["mass_flow"]           = DGF_BOUNDARY_SEED_VAR_MASS_FLOW;

  static DGF_BOUNDARY_SEED_VAR_TYPE default_boundary_seed_vars[] = { DGF_BOUNDARY_SEED_VAR_PRESSURE,
                                                                     DGF_BOUNDARY_SEED_VAR_XVEL,
                                                                     DGF_BOUNDARY_SEED_VAR_YVEL,
                                                                     DGF_BOUNDARY_SEED_VAR_ZVEL,
                                                                     DGF_BOUNDARY_SEED_VAR_TEMP,
                                                                     DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY,
                                                                     DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION,
                                                                     DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX,
                                                                     DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX,
                                                                     DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX,
                                                                     DGF_BOUNDARY_SEED_VAR_MASS_FLOW};

  static DGF_BOUNDARY_SEED_VAR_TYPE default_boundary_solid_seed_vars[] = {DGF_BOUNDARY_SEED_VAR_TEMP};

  g_seed_ctl.parse_seed_var_specs< DGF_BOUNDARY_SEED_VAR_TYPE, DGF_N_BOUNDARY_SEED_VARS, FALSE >
    (g_seed_ctl.smart_seed_boundaries(), 
     argc, 
     argv, 
     "-bseed_include_vars", 
     "-bseed_exclude_vars",
     false /*Face*/,
     boundary_seed_var_string_to_seed_var_type, 
     default_boundary_seed_vars,
     sizeof(default_boundary_seed_vars) / sizeof(DGF_BOUNDARY_SEED_VAR_TYPE),
     default_boundary_solid_seed_vars, 
     sizeof(default_boundary_solid_seed_vars) / sizeof(DGF_BOUNDARY_SEED_VAR_TYPE),
     0, " for boundary seeding");

  if (getenv("EXA_CREATE_FULL_CKPTS")
      || (parse_arg_present_p("-full_checkpoints", argc, argv))) {
    cp_info.create_full_ckpts = TRUE;
    args.run_options |= SIM_FULL_CKPTS;
  }
  else
    cp_info.create_full_ckpts = FALSE;

  args.lm_user = parse_arg_string("-lm_user", argc, argv);
  args.lm_host = parse_arg_string("-lm_host", argc, argv);

  args.lm_log_consumption = parse_arg_present_p("-lm_log_consumption", argc, argv);

  args.lm_alt = parse_arg_string("-lm_alt", argc, argv);
  if (args.lm_alt != NULL) {
    if ((strlen(args.lm_alt) != 1)
	|| (args.lm_alt[0] < '1')
	|| (args.lm_alt[0] > '3'))
      msg_error("Alternate license key (via -lm_alt) must be 1, 2, or 3.");
  }

  args.use_opt_licenses = parse_arg_present_p("-sim_opt_licenses", argc, argv);
  args.opt_license_failover = parse_arg_present_p("-sim_opt_license_failover", argc, argv);
  
  if (parse_arg_present_p("-sim_dsls_tokens", argc, argv))
    EXA_ENV::SetEnv("EXA_DSLS_LICENSE_MODEL","TOKEN"); // mimic GRID_DSLS_LICENSE_MODEL
  if (parse_arg_present_p("-sim_dsls_credits", argc, argv))
    EXA_ENV::SetEnv("EXA_DSLS_LICENSE_MODEL","CREDIT"); // mimic GRID_DSLS_LICENSE_MODEL

  /* Default binary filename as necessary */

  args.sp_binary_filename = parse_arg_string("-host_sp_file", argc, argv);
  if (! args.sp_binary_filename || ! strlen(args.sp_binary_filename)) {
    args.sp_binary_filename = getenv(SIMENG_HOST_SP_FILE_ENVVAR);
    if (! args.sp_binary_filename || ! strlen(args.sp_binary_filename)) 
      args.sp_binary_filename = DEFAULT_SIMENG_HOST_SP_FILE;
  }

  {
    if ((args.time_every = parse_arg_uINT32("-time_every", argc, argv)) == 0xFFFFFFFF)
      args.time_every = 0;

    {
      cSTRING avg;
      if ((avg = parse_arg_string("-diag", argc, argv))) {
	STRING env = xnew char[100];
	sprintf(env, "EXA_DIAG_AVG=%s", avg);
	putenv(env);	/* cannot free env */
      }
    }
    if ((args.num_timesteps = parse_arg_uINT32("-num_timesteps", argc, argv)) == 0xFFFFFFFF) 
      args.num_timesteps = 0;

    /** If n_flow_base_steps is specified, use that as default for all
     *  fluid solver _base_steps values. Then, overwrite any individual
     *  solver values if provided */
    asINT32 n_flow_base_steps;
    if ((n_flow_base_steps = parse_arg_uINT32("-n_flow_base_steps", argc, argv)) == 0xFFFFFFFF)
      n_flow_base_steps = 1;

    if ((args.n_lb_base_steps = parse_arg_uINT32("-n_lb_base_steps", argc, argv)) == 0xFFFFFFFF)
      args.n_lb_base_steps = n_flow_base_steps;

    if ((args.n_t_base_steps = parse_arg_uINT32("-n_t_base_steps", argc, argv)) == 0xFFFFFFFF)
      args.n_t_base_steps = n_flow_base_steps;
    if(parse_arg_present("-n_ke_base_steps", argc, argv))
      args.is_ke_base_step_arg_read = TRUE;

    if ((args.n_ke_base_steps = parse_arg_uINT32("-n_ke_base_steps", argc, argv)) == 0xFFFFFFFF) {
      args.is_ke_base_step_arg_read = FALSE;
      args.n_ke_base_steps = n_flow_base_steps;
    }

    if ((args.n_uds_base_steps = parse_arg_uINT32("-n_uds_base_steps", argc, argv)) == 0xFFFFFFFF)
      args.n_uds_base_steps = n_flow_base_steps;

    // We currently disallow UDS base steps being different than LB base steps
    // Issue a warning if UDS base steps are different from LB base steps, and change to be equal to LB base steps
    if (args.n_uds_base_steps != args.n_lb_base_steps) {
      msg_warn("Currently UDS cannot be subcycled wrt LB, changing UDS base steps to %d", args.n_lb_base_steps);
      args.n_uds_base_steps = args.n_lb_base_steps;
    }
    if(parse_arg_present("-n_conduction_base_steps", argc, argv))
      args.is_cond_base_step_arg_read = TRUE;
    if ((args.n_conduction_base_steps = parse_arg_uINT32("-n_conduction_base_steps", argc, argv)) == 0xFFFFFFFF)
      args.n_conduction_base_steps = 1;

    if (parse_arg_present("-n_radiation_base_steps", argc, argv))
      args.is_rad_base_step_arg_read = TRUE;
    if ((args.n_radiation_base_steps = parse_arg_uINT32("-n_radiation_base_steps", argc, argv)) == 0xFFFFFFFF)
      args.n_radiation_base_steps = 1;

    if ((args.acous_start_time = parse_arg_uINT32("-acous_start_time", argc, argv)) == 0xFFFFFFFF)
      args.acous_start_time = -1;

    if (parse_arg_present_p("-mpi_environment", argc, argv))
      args.mpi_environment_p = TRUE;
    if ((args.n_send_elements = 
	 parse_arg_uINT32("-n_send_elements", argc, argv)) == 0xFFFFFFFF) 
      args.n_send_elements = N_SEND_BUFFER_ELEMENTS;
    if (parse_arg_present_p("-report_sp_time", argc, argv))
      args.run_options |= SIM_REPORT_TIME;
    if (parse_arg_present_p("-report_sp_time_verbose", argc, argv))
      args.run_options |= SIM_REPORT_TIME_VERBOSE;
    if (parse_arg_present_p("-report_rp_time", argc, argv))
      args.run_options |= SIM_REPORT_RP_TIME;
    if ((args.timers_on_timestep = parse_arg_uINT32("-timers_on_timestep", argc, argv)) == 0xFFFFFFFF) 
      args.timers_on_timestep = TIMESTEP_NEVER;
    if ((args.timers_off_timestep = parse_arg_uINT32("-timers_off_timestep", argc, argv)) == 0xFFFFFFFF) 
      args.timers_off_timestep = TIMESTEP_NEVER;
    if (parse_arg_present_p("-report_sp_threadtime", argc, argv))
      args.run_options |= SIM_REPORT_THREADTIME;
    if (parse_arg_present_p("-report_sp_threadtime_verbose", argc, argv))
      args.run_options |= SIM_REPORT_THREADTIME_VERBOSE;
    if (parse_arg_present_p("-report_memory", argc, argv))
      args.run_options |= SIM_REPORT_MEMORY;
    if (parse_arg_present_p("-no_random", argc, argv))
      args.run_options |= SIM_DISABLE_RANDOM;
    if (parse_arg_present_p("-no_avx2", argc, argv))
      args.run_options |= SIM_NO_AVX2;
    if (parse_arg_present_p("-no_run", argc, argv))
      args.run_options |= SIM_NO_RUN;
    if (parse_arg_present_p("-no_dynamics", argc, argv))
      args.run_options |= SIM_NO_DYNAMICS;
    if (parse_arg_present_p("-no_timeout", argc, argv))
      args.run_options |= SIM_NO_TIMEOUT;
    if (parse_arg_present_p("-timeout", argc, argv))
      args.run_options &= ~SIM_NO_TIMEOUT;
    if (parse_arg_present_p("-no_locations", argc, argv))
      args.ignore_locations_p = TRUE;
    if (parse_arg_present_p("-accept_5_0_thermal_feedback", argc, argv))
      args.accept_5_0_thermal_feedback_p = TRUE;
    if (parse_arg_present_p("-accept_old_ht_cdi", argc, argv))
      args.accept_old_ht_cdi_p = TRUE;
    if (parse_arg_present_p("-accept_old_turb_cdi", argc, argv))
      args.accept_old_turb_cdi_p = TRUE;
    if (parse_arg_present_p("-suppress_final_status",argc, argv))
      args.suppress_final_status_p = TRUE;
    if(parse_arg_present_p("-report_phase_basetime", argc, argv))
      args.report_coupling_phase_basetime = TRUE;
    if (parse_arg_present_p("-no_measure", argc, argv))
      args.ignore_measurements_p = TRUE;
    if (parse_arg_present_p("-double_precision", argc, argv))
      cp_info.is_sim_double_precision = TRUE;
    if (parse_arg_present_p("-double_precision_meas", argc, argv))
      cp_info.is_double_precision_meas_forced = TRUE;
    if (parse_arg_present_p("-clear_all", argc, argv))
      args.clear_all_p = TRUE;
    if (parse_arg_present_p("-fluid_checkpoint_at_end", argc, argv))
      args.mme_chkpnt_at_end = TRUE;
    if (parse_arg_present_p("-mme_checkpoint_at_end", argc, argv))
      args.mme_chkpnt_at_end = TRUE;
    if (parse_arg_present_p("-full_checkpoint_at_end", argc, argv))
      args.full_chkpnt_at_end = TRUE;
    if (parse_arg_present_p("-no_final_checkpoint_retry", argc, argv))
      args.no_final_checkpoint_retry = TRUE;
    if (parse_arg_present_p("-parallel_io", argc, argv))
    {
#ifdef _EXA_HPMPI
      msg_warn( "HPMPI does not support parallel I/O. Simulaton will use serial mode.\n");
      parse_arg_uINT32("-parallel_io_max_buffer_size", argc, argv);//process it to avoid it getting to Unrecognized parameters
#else
      args.parallel_io = TRUE;
      uINT32 parallel_io_max_buffer_size;
      if ((parallel_io_max_buffer_size = parse_arg_uINT32("-parallel_io_max_buffer_size", argc, argv)) != 0xFFFFFFFF) {
        args.parallel_io_max_buffer_size = parallel_io_max_buffer_size;
      } else {
        args.parallel_io_max_buffer_size = 0;
      }
#endif
    }


    if (parse_arg_present_p("-allow_immediate_checkpoints", argc, argv))
      args.allow_immediate_checkpoints = TRUE;
    if (parse_arg_present_p("-barrier_instead_of_finalize", argc, argv))
      args.barrier_instead_of_finalize = TRUE;

    if (parse_arg_present_p("-no_reserve_addr_space", argc, argv))
      args.no_reserve_addr_space = TRUE;

    if (parse_arg_present_p("-short_mpi_thread_sleep", argc, argv))
      args.short_mpi_thread_sleep = TRUE;

    if (parse_arg_present_p("-no_mpi_request_free", argc, argv)) 
      args.no_mpi_request_free = TRUE;
  }
 
  // parse surface coupling command line options
  cp_info.ptherm_model_path = parse_arg_string("-ptherm_model_path",argc,argv);
  cp_info.ptherm_predecessor_run = parse_arg_string("-ptherm_predecessor_run",argc,argv);

  cp_info.ptherm_processes = 0;
  asINT32 scoup_processes;
  if ((scoup_processes = parse_arg_uINT32("-ptherm_processes", argc, argv)) != 0xFFFFFFFF) {
    cp_info.ptherm_processes = scoup_processes;
  } 

  cp_info.ptherm_threads = 0;
  asINT32 scoup_threads;  
  // Parse ptherm_nprocs here for backward compatibility. Should use ptherm_threads instead.
  if ((scoup_threads = parse_arg_uINT32("-ptherm_nprocs", argc, argv)) != 0xFFFFFFFF) {
    cp_info.ptherm_threads = scoup_threads;
  }

  if ((scoup_threads = parse_arg_uINT32("-ptherm_threads", argc, argv)) != 0xFFFFFFFF) {
    cp_info.ptherm_threads = scoup_threads;
  }

  cp_info.ptherm_only_keep_orig_model = FALSE;
  if (parse_arg_present_p("-ptherm_only_keep_orig_model",argc,argv)) {
    cp_info.ptherm_only_keep_orig_model = TRUE;
  }
  cp_info.ptherm_wait_for_license = FALSE;
  if (parse_arg_present_p("-ptherm_wait_for_license",argc,argv)) {
    cp_info.ptherm_wait_for_license = TRUE;
  }
  cp_info.ptherm_dump_gradient_p = FALSE;
  if (parse_arg_present_p("-ptherm_dump_gradient",argc,argv)) {
    cp_info.ptherm_dump_gradient_p = TRUE;
  }

  cp_info.m_adaptive_params.use_max_temp = FALSE;
  if (parse_arg_present_p("-ptherm_use_tmax_gradient",argc,argv)) {
    cp_info.m_adaptive_params.use_max_temp = TRUE;
  }

  cp_info.ptherm_model_changed_p = FALSE;
  if (parse_arg_present_p("-ptherm_model_changed",argc,argv)) {
    cp_info.ptherm_model_changed_p = TRUE;
  }
  cp_info.ptherm_ckpt_skip_first_coupling_p = FALSE;
  if (parse_arg_present_p("-ptherm_ckpt_skip_first_coupling",argc,argv)) {
    cp_info.ptherm_ckpt_skip_first_coupling_p = TRUE;
  }
  cp_info.force_radtherm_p = FALSE;
  if (parse_arg_present_p("-force_radtherm",argc,argv)) {
    cp_info.force_radtherm_p = TRUE;
  }
  cp_info.force_ptherm_p = FALSE;
  if (parse_arg_present_p("-force_ptherm",argc,argv)) {
    cp_info.force_ptherm_p = TRUE;
  }
  cp_info.radtherm_path = parse_arg_string("-radtherm_path", argc, argv);
  cp_info.ptherm_remote_host = parse_arg_string("-ptherm_remote_host", argc, argv);
  cp_info.ptherm_remote_user = parse_arg_string("-ptherm_remote_user", argc, argv);
  cp_info.ptherm_mpifile = parse_arg_string("-ptherm_mpifile", argc, argv);
  cp_info.ptherm_options = parse_arg_string("-ptherm_options", argc, argv);
  if (cp_info.ptherm_options) // exarun was forced to convert spaces to tildas because of shell weirdness
    convert_tildas_to_spaces((STRING)cp_info.ptherm_options);
  sFLOAT scoup_unmatched;
  // default total unmatched area ratio of all target faces for surface
  // coupling map in either direction (PowerFLOW->foreign, foreign->PowerFLOW)
  cp_info.ptherm_max_unmatched_area_ratio = 0.1;
  if (parse_arg_sFLOAT("-ptherm_max_unmatched_ratio", scoup_unmatched, argc, argv)
      && scoup_unmatched >= 0.0) {
    cp_info.ptherm_max_unmatched_area_ratio = MIN(scoup_unmatched,1.0);
  } 

  // PHYSICS_INIT_ARG is obsolete, but we allow for its presence
  cSTRING physics_init_arg = parse_arg_string("-physics", argc, argv);
  
  cp_info.amesim_model_path = parse_arg_string("-amesim_model_path",argc,argv);

  sFLOAT amesim_timeout;
  if (parse_arg_sFLOAT("-amesim_timeout", amesim_timeout, argc, argv)) {
    cp_info.amesim_timeout = amesim_timeout;
  } else {
    cp_info.amesim_timeout = 1 * 60 * 60; // 1 hour
  }

  // PR 38335: exaqsub option to support BCs via sampled face meas file
  // The user provides the folder locations of the input measurement files for various faces.
  parse_sampled_face_meas_bc_args(argc, argv);

  if (parse_arg_present_p("-gpu", argc, argv)) {
    args.gpu = TRUE;
  }

  if (parse_arg_present_p("-gpu_repeatable", argc, argv)) {
    args.gpu_repeatable = TRUE;
  }

  uINT32 gpu_lrf_max_isends;
  if ((gpu_lrf_max_isends = parse_arg_uINT32("-gpu_lrf_max_isends", argc, argv)) != 0xFFFFFFFF) {
    args.gpu_lrf_max_isends = gpu_lrf_max_isends;
  } else {
    args.gpu_lrf_max_isends = 2500;
  }

  uINT32 gpu_max_buffer_size;
  if ((gpu_max_buffer_size = parse_arg_uINT32("-gpu_max_buffer_size", argc, argv)) != 0xFFFFFFFF) {
    args.gpu_max_buffer_size = gpu_max_buffer_size;
  } else {
    args.gpu_max_buffer_size = 1000;
  }

  args.enable_tangential_shell_conduction = FALSE;
  if (parse_arg_present_p("-enable_tangential_shell_conduction", argc, argv)) 
    args.enable_tangential_shell_conduction = TRUE;

  args.use_implicit_shell_solver = FALSE;
  if (parse_arg_present_p("-use_implicit_shell_solver", argc, argv)) {
    args.use_implicit_shell_solver = TRUE;
  }

  args.use_implicit_solid_solver = FALSE;
  if (parse_arg_present_p("-use_implicit_solid_solver", argc, argv)) {
    args.use_implicit_solid_solver = TRUE;
  }

  return &args;
}

/*--------------------------------------------------------------------------*
 * LGI Filename parsing
 *--------------------------------------------------------------------------*/

static cSTRING parse_lgi_filename_arg
   (cSTRING progname, int *argc, char *argv[])
/* Removes the first argument from the argument list and treats it as an
 * LGI filename.  Checks to make sure it exits.  
 * If no more arguments, NULL is returned.
 * If the filename does not exist, an error message is
 * printed to stderr and the program exits. If the file exists and is
 * readable, its name is returned.
 */
{
  cSTRING filename = parse_arg_remove(0, argc, argv);

  if (!filename) return NULL;

#ifndef __alpha
  {
    FILE *file = fopen(filename, "r");
    if (!file) {
      if ((sizeof(FILE_POS) == 4) && (errno == EOVERFLOW)) {
	msg_unix_error("Could not open LGI file \"%s\". File size exceeds"
		       " 2GB limit. This file can only be read by a large-file aware"
		       " version of this software.",
		       filename);
      } else {
	msg_unix_error("Could not open the LGI file \"%s\"\n", 
		       filename);
      }
    }
    fclose(file);
  }
#endif
  return filename;
}

/*--------------------------------------------------------------------------*
 * Shared initialization function
 *--------------------------------------------------------------------------*/
/* This parses all of the arguments and does the basic initialization up to
 * starting the SPs and reading the LGI file.
 * Returns the LGI filename and sets the passed in SIM_ARGS and STD_ARGS
 * structures. (If either is NULL, they are not set)
 *
 * The LGI filename is checked to make sure it exists.
 * The following initializations are called:
 *    - cp_initialize
 *    - cp_jobctl_initialize
 */
cSTRING exa_sim_initialize(int argc, char *argv_orig[], char *argv[], 
			   sSIM_ARGS *sim_args_ret, sSTD_ARGS *std_args_ret)
{
  // Print simulator version number and copyright information
  print_header(); 
  {
    int argc_orig = argc;

    // Parse program arguments
    cSTRING  filename = argv[argc - 1]; // Save this for reference
    cSTRING  progname = parse_arg_remove(0, &argc, argv);
    STD_ARGS std_args = parse_std_args(&argc, argv);
    SIM_ARGS sim_args = parse_sim_args(progname, &argc, argv);

    if (!getenv("EXA_USE_STRICT_SCHEDULING") && (sim_args->run_options & (SIM_REPORT_TIME | SIM_REPORT_TIME_VERBOSE))) {
      msg_error("SP timers are not accurate with dynamic scheduling.");
    }

    cSTRING  jobctl_filename = parse_arg_string("-jobctl_file", &argc, argv);
    BOOLEAN  write_jobctl_file_p = ! parse_arg_present_p("-no_jobctl_file", &argc, argv);

    // Check that only the filename argument is left
    if (argc != 1 || filename != argv[0]) {
      if (argc == 0)
        msg_print ("Too few parameters");
      else {
        msg_print ("Unrecognized parameters: ");
        DOTIMES (i, argc, {
                 if (filename != argv[i])
                 msg_print ("\t%s", argv [i]);
                 });
        msg_print ("\n");
      }
      bad_usage(progname);
    }

    // Get the LGI filename
    filename = parse_lgi_filename_arg(progname, &argc, argv);
    if (! filename) bad_usage(progname);
    cp_info.lgi_filename  = filename;
    cp_info.root_filename = filename_no_type(filename);
    cp_info.run_dir       = platform_get_file_dir_name(filename, new char[strlen(filename) + 1] );

    // Initialize

    // set umask as requested prior to opening any new files
    if (sim_args->umask_override == TRUE) {
      umask ((mode_t)sim_args->umask);
    }

    cp_jobctl_initialize(jobctl_filename, write_jobctl_file_p);

    // Return values
    if (sim_args_ret) memcpy(sim_args_ret, sim_args, sizeof(sSIM_ARGS));
    if (std_args_ret) memcpy(std_args_ret, std_args, sizeof(sSTD_ARGS));

    dbg_msg(1, "Done with CP startup");
    return filename;
  }
}

VOID read_fluid_seed_vars_specs()     //new
{

  static DGF_SEED_VAR_TYPE default_fluid_seed_vars[] = { DGF_SEED_VAR_PRESSURE,
                                                         DGF_SEED_VAR_XVEL,
                                                         DGF_SEED_VAR_YVEL,
                                                         DGF_SEED_VAR_ZVEL,
                                                         DGF_SEED_VAR_TEMP,
                                                         DGF_SEED_VAR_TURB_KINETIC_ENERGY,
                                                         DGF_SEED_VAR_TURB_DISSIPATION,
                                                         DGF_SEED_VAR_DENSITY,
                                                         DGF_SEED_VAR_STRESS_TENSOR_MAG,
                                                         DGF_SEED_VAR_WATER_VAPOR_MFRAC,
                                                         DGF_SEED_VAR_COMP0_DENSITY,
                                                         DGF_SEED_VAR_COMP1_DENSITY,
                                                         DGF_SEED_VAR_CONTACT_ANGLE,
                                                         DGF_SEED_VAR_DYNAMIC_SCALAR_MULTIPLIER,
                                                         DGF_SEED_VAR_DENSITY_FROZEN,
                                                         DGF_SEED_VAR_USTAR,
  							 DGF_SEED_VAR_XPRESSURE_GRADIENT,
  							 DGF_SEED_VAR_YPRESSURE_GRADIENT,
  							 DGF_SEED_VAR_ZPRESSURE_GRADIENT};

  static DGF_SEED_VAR_TYPE default_solid_seed_vars[] = { DGF_SEED_VAR_TEMP};

  // This is a map which maps strings to SEED_VAR_TYPE
  std::map <std::string, DGF_SEED_VAR_TYPE > fluid_seed_var_string_to_seed_var_type;
  fluid_seed_var_string_to_seed_var_type["static_pressure"]     = DGF_SEED_VAR_PRESSURE;
  fluid_seed_var_string_to_seed_var_type["x_velocity"]          = DGF_SEED_VAR_XVEL;
  fluid_seed_var_string_to_seed_var_type["y_velocity"]          = DGF_SEED_VAR_YVEL;
  fluid_seed_var_string_to_seed_var_type["z_velocity"]          = DGF_SEED_VAR_ZVEL;
  fluid_seed_var_string_to_seed_var_type["x_pressure_gradient"]          = DGF_SEED_VAR_XPRESSURE_GRADIENT;
  fluid_seed_var_string_to_seed_var_type["y_pressure_gradient"]          = DGF_SEED_VAR_YPRESSURE_GRADIENT;
  fluid_seed_var_string_to_seed_var_type["z_pressure_gradient"]          = DGF_SEED_VAR_ZPRESSURE_GRADIENT;
  fluid_seed_var_string_to_seed_var_type["density_frozen"]      = DGF_SEED_VAR_DENSITY_FROZEN;
  fluid_seed_var_string_to_seed_var_type["ustar"]               = DGF_SEED_VAR_USTAR;
  fluid_seed_var_string_to_seed_var_type["water_vapor_mfrac"]   = DGF_SEED_VAR_WATER_VAPOR_MFRAC;
  if (cp_info.is_5g_sim) {
    fluid_seed_var_string_to_seed_var_type["porosity"]          = DGF_SEED_VAR_TURB_KINETIC_ENERGY;   //borrow
    fluid_seed_var_string_to_seed_var_type["pm_mode_selector"]  = DGF_SEED_VAR_TURB_DISSIPATION;   //borrow
    fluid_seed_var_string_to_seed_var_type["rock_type"]         = DGF_SEED_VAR_TEMP;    //borrow
    fluid_seed_var_string_to_seed_var_type["dynamic_scalar_multiplier"] = DGF_SEED_VAR_DYNAMIC_SCALAR_MULTIPLIER;
    fluid_seed_var_string_to_seed_var_type["comp0_density"]       = DGF_SEED_VAR_COMP0_DENSITY;
    fluid_seed_var_string_to_seed_var_type["comp1_density"]       = DGF_SEED_VAR_COMP1_DENSITY;
    fluid_seed_var_string_to_seed_var_type["contact_angle"]       = DGF_SEED_VAR_CONTACT_ANGLE;
  } else {
    fluid_seed_var_string_to_seed_var_type["turb_kinetic_energy"] = DGF_SEED_VAR_TURB_KINETIC_ENERGY;
    fluid_seed_var_string_to_seed_var_type["turb_dissipation"]    = DGF_SEED_VAR_TURB_DISSIPATION;
    fluid_seed_var_string_to_seed_var_type["temperature"]         = DGF_SEED_VAR_TEMP;
  }

  // We don't allow the user to request DENSITY or STRESS_TENSOR_MAG. The decision of
  // whether these variables are necessary is computed in cSMART_SEED_CONTROL::setup().

  asINT32 n_extra_inclusion_seed_vars = 2; //for DENSITY and STRESS_TENSOR_MAG
  if (!cp_info.is_5g_sim)
    n_extra_inclusion_seed_vars++;  //for DYNAMIC_SCALAR_MULTIPLIER

  // This is a map which maps strings to SEED_VAR_TYPE for solid SPS
  std::map <std::string, DGF_SEED_VAR_TYPE > solid_seed_var_string_to_seed_var_type;
  solid_seed_var_string_to_seed_var_type["temperature"] = DGF_SEED_VAR_TEMP;

  g_seed_ctl.parse_seed_var_specs< DGF_SEED_VAR_TYPE, DGF_N_SEED_VARS, TRUE > //instantiated in seed.cc
    (g_seed_ctl.seed_filename(0) != NULL, &(sim_args.argc), (char **)sim_args.argv, "-seed_include_vars", "-seed_exclude_vars",
     true /*"Fluid region"*/,
     fluid_seed_var_string_to_seed_var_type, 
     default_fluid_seed_vars,  
     sizeof(default_fluid_seed_vars) / sizeof(DGF_SEED_VAR_TYPE),
     default_solid_seed_vars,  
     sizeof(default_solid_seed_vars) / sizeof(DGF_SEED_VAR_TYPE),
     n_extra_inclusion_seed_vars, "");// n_extra_inclusion_seed_vars --> space for DENSITY and STRESS_TENSOR_MAG

  // Add DENSITY and STRESS_TENSOR_MAG to all inclusion-list fluid region seed var specs. These may be
  // filtered out in cSMART_SEED_CONTROL::setup().
  g_seed_ctl.maybe_add_var_seed_specs();

  g_seed_ctl.maybe_rotate_vel();
}
