<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject RootNamespace="cdi" ProjectType="Visual C++" Name="cdi" Keyword="Win32Proj" Version="9.00" ProjectGUID="{86093BBF-115B-A794-C53B-B8773F2E9D45}">
    <Platforms>
        <Platform Name="Win32"/>
        <Platform Name="x64"/>
    </Platforms>
    <Configurations>
        <Configuration Name="Debug|Win32" CharacterSet="2" OutputDirectory="$(SolutionDir)" IntermediateDirectory="x86_vs2008_mdd" ConfigurationType="4">
            <Tool Name="VCCLCompilerTool" PreprocessorDefinitions="__AMD64_VS2008_MD__;__MSVC__;WINNT;NOMINMAX;EXA_CURRENT_YEAR=2015;CDI_VERSION=&quot;&quot;413-rstairs-03&quot;&quot;;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;" RuntimeLibrary="3" ForceConformanceInForLoopScope="TRUE" AdditionalIncludeDirectories="..\amd64_vs2008_md;..;." ExceptionHandling="TRUE" DebugInformationFormat="3" ProgramDatabaseFileName="cdi.pdb" AdditionalOptions="/EHsc /nologo /MP8" Optimization="0" MinimalRebuild="TRUE" WarningLevel="2" Detect64BitPortabilityProblems="TRUE" RuntimeTypeInfo="TRUE" UsePrecompiledHeader=" 0" BasicRuntimeChecks="3"/>
            <Tool Name="VCCustomBuildTool"/>
            <Tool Name="VCLinkerTool" SubSystem="1" OutputFile="" AdditionalLibraryDirectories="" LinkIncremental="2" GenerateDebugInformation="TRUE" TargetMachine="1" ProgramDatabaseFile="" IgnoreAllDefaultLibraries="FALSE" IgnoreDefaultLibraryNames="" AdditionalDependencies=""/>
            <Tool Name="VCMIDLTool"/>
            <Tool Name="VCPostBuildEventTool"/>
            <Tool Name="VCPreBuildEventTool"/>
            <Tool Name="VCPreLinkEventTool"/>
            <Tool Name="VCResourceCompilerTool"/>
            <Tool Name="VCWebServiceProxyGeneratorTool"/>
            <Tool Name="VCXMLDataGeneratorTool"/>
            <Tool Name="VCWebDeploymentTool"/>
            <Tool Name="VCManagedWrapperGeneratorTool"/>
            <Tool Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
            <Tool Name="VCCLCompilerTool" PreprocessorDefinitions="__AMD64_VS2008_MD__;__MSVC__;WINNT;NOMINMAX;EXA_CURRENT_YEAR=2015;CDI_VERSION=&quot;&quot;413-rstairs-03&quot;&quot;;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;" RuntimeLibrary="3" ForceConformanceInForLoopScope="TRUE" AdditionalIncludeDirectories="..\amd64_vs2008_md;..;." ExceptionHandling="TRUE" DebugInformationFormat="3" ProgramDatabaseFileName="cdi.pdb" AdditionalOptions="/EHsc /nologo /MP8" Optimization="0" MinimalRebuild="TRUE" WarningLevel="2" Detect64BitPortabilityProblems="TRUE" RuntimeTypeInfo="TRUE" UsePrecompiledHeader=" 0" BasicRuntimeChecks="3"/>
            <Tool Name="VCLibrarianTool" OutputFile="$(IntDir)\cdi.lib"/>
        </Configuration>
        <Configuration Name="Release|Win32" CharacterSet="2" OutputDirectory="$(SolutionDir)" IntermediateDirectory="x86_vs2008_md" ConfigurationType="4">
            <Tool Name="VCCLCompilerTool" PreprocessorDefinitions="__AMD64_VS2008_MD__;__MSVC__;WINNT;NDEBUG;NOMINMAX;QT_NO_DEBUG;EXA_CURRENT_YEAR=2015;CDI_VERSION=&quot;&quot;413-rstairs-03&quot;&quot;;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;" Detect64BitPortabilityProblems="TRUE" ForceConformanceInForLoopScope="TRUE" AdditionalIncludeDirectories="..\amd64_vs2008_md;..;." ExceptionHandling="TRUE" DebugInformationFormat="0" ProgramDatabaseFileName="cdi.pdb" AdditionalOptions="/EHsc /nologo /MP8" Optimization="3" MinimalRebuild="FALSE" WarningLevel="2" RuntimeLibrary="2" RuntimeTypeInfo="TRUE" UsePrecompiledHeader=" 0" BasicRuntimeChecks="0"/>
            <Tool Name="VCCustomBuildTool"/>
            <Tool Name="VCLinkerTool" SubSystem="1" OutputFile="" AdditionalLibraryDirectories="" LinkIncremental="1" GenerateDebugInformation="TRUE" TargetMachine="1" OptimizeReferences="2" EnableCOMDATFolding="2" IgnoreAllDefaultLibraries="FALSE" IgnoreDefaultLibraryNames="" AdditionalDependencies=""/>
            <Tool Name="VCMIDLTool"/>
            <Tool Name="VCPostBuildEventTool"/>
            <Tool Name="VCPreBuildEventTool"/>
            <Tool Name="VCPreLinkEventTool"/>
            <Tool Name="VCResourceCompilerTool"/>
            <Tool Name="VCWebServiceProxyGeneratorTool"/>
            <Tool Name="VCXMLDataGeneratorTool"/>
            <Tool Name="VCWebDeploymentTool"/>
            <Tool Name="VCManagedWrapperGeneratorTool"/>
            <Tool Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
            <Tool Name="VCCLCompilerTool" PreprocessorDefinitions="__AMD64_VS2008_MD__;__MSVC__;WINNT;NDEBUG;NOMINMAX;QT_NO_DEBUG;EXA_CURRENT_YEAR=2015;CDI_VERSION=&quot;&quot;413-rstairs-03&quot;&quot;;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;" Detect64BitPortabilityProblems="TRUE" ForceConformanceInForLoopScope="TRUE" AdditionalIncludeDirectories="..\amd64_vs2008_md;..;." ExceptionHandling="TRUE" DebugInformationFormat="0" ProgramDatabaseFileName="cdi.pdb" AdditionalOptions="/EHsc /nologo /MP8" Optimization="3" MinimalRebuild="FALSE" WarningLevel="2" RuntimeLibrary="2" RuntimeTypeInfo="TRUE" UsePrecompiledHeader=" 0" BasicRuntimeChecks="0"/>
            <Tool Name="VCLibrarianTool" OutputFile="$(IntDir)\cdi.lib"/>
        </Configuration>
        <Configuration Name="Release_with_symbols|Win32" CharacterSet="2" OutputDirectory="$(SolutionDir)" IntermediateDirectory="x86_vs2008_md" ConfigurationType="4">
            <Tool Name="VCCLCompilerTool" PreprocessorDefinitions="__AMD64_VS2008_MD__;__MSVC__;WINNT;NDEBUG;NOMINMAX;QT_NO_DEBUG;EXA_CURRENT_YEAR=2015;CDI_VERSION=&quot;&quot;413-rstairs-03&quot;&quot;;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;" RuntimeLibrary="2" ForceConformanceInForLoopScope="TRUE" AdditionalIncludeDirectories="..\amd64_vs2008_md;..;." ExceptionHandling="TRUE" DebugInformationFormat="3" ProgramDatabaseFileName="cdi.pdb" AdditionalOptions="/EHsc /nologo /MP8" Optimization="0" MinimalRebuild="TRUE" WarningLevel="2" Detect64BitPortabilityProblems="TRUE" RuntimeTypeInfo="TRUE" UsePrecompiledHeader=" 0" BasicRuntimeChecks="3"/>
            <Tool Name="VCCustomBuildTool"/>
            <Tool Name="VCLinkerTool" SubSystem="1" OutputFile="" AdditionalLibraryDirectories="" LinkIncremental="2" GenerateDebugInformation="TRUE" TargetMachine="1" ProgramDatabaseFile="" IgnoreAllDefaultLibraries="FALSE" IgnoreDefaultLibraryNames="" AdditionalDependencies=""/>
            <Tool Name="VCMIDLTool"/>
            <Tool Name="VCPostBuildEventTool"/>
            <Tool Name="VCPreBuildEventTool"/>
            <Tool Name="VCPreLinkEventTool"/>
            <Tool Name="VCResourceCompilerTool"/>
            <Tool Name="VCWebServiceProxyGeneratorTool"/>
            <Tool Name="VCXMLDataGeneratorTool"/>
            <Tool Name="VCWebDeploymentTool"/>
            <Tool Name="VCManagedWrapperGeneratorTool"/>
            <Tool Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
            <Tool Name="VCCLCompilerTool" PreprocessorDefinitions="__AMD64_VS2008_MD__;__MSVC__;WINNT;NDEBUG;NOMINMAX;QT_NO_DEBUG;EXA_CURRENT_YEAR=2015;CDI_VERSION=&quot;&quot;413-rstairs-03&quot;&quot;;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;" RuntimeLibrary="2" ForceConformanceInForLoopScope="TRUE" AdditionalIncludeDirectories="..\amd64_vs2008_md;..;." ExceptionHandling="TRUE" DebugInformationFormat="3" ProgramDatabaseFileName="cdi.pdb" AdditionalOptions="/EHsc /nologo /MP8" Optimization="0" MinimalRebuild="TRUE" WarningLevel="2" Detect64BitPortabilityProblems="TRUE" RuntimeTypeInfo="TRUE" UsePrecompiledHeader=" 0" BasicRuntimeChecks="3"/>
            <Tool Name="VCLibrarianTool" OutputFile="$(IntDir)\cdi.lib"/>
        </Configuration>
        <Configuration Name="Release_with_symbols_64|x64" CharacterSet="2" OutputDirectory="$(SolutionDir)" IntermediateDirectory="amd64_vs2008_md" ConfigurationType="4">
            <Tool Name="VCCLCompilerTool" PreprocessorDefinitions="__AMD64_VS2008_MD__;__MSVC__;WINNT;NDEBUG;NOMINMAX;QT_NO_DEBUG;EXA_CURRENT_YEAR=2015;CDI_VERSION=&quot;&quot;413-rstairs-03&quot;&quot;;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;" RuntimeLibrary="2" ForceConformanceInForLoopScope="TRUE" AdditionalIncludeDirectories="..\amd64_vs2008_md;..;." ExceptionHandling="TRUE" DebugInformationFormat="3" ProgramDatabaseFileName="cdi.pdb" AdditionalOptions="/EHsc /nologo /MP8" Optimization="0" MinimalRebuild="TRUE" WarningLevel="2" Detect64BitPortabilityProblems="TRUE" RuntimeTypeInfo="TRUE" UsePrecompiledHeader=" 0" BasicRuntimeChecks="3"/>
            <Tool Name="VCCustomBuildTool"/>
            <Tool Name="VCLinkerTool" SubSystem="1" OutputFile="" AdditionalLibraryDirectories="" LinkIncremental="2" GenerateDebugInformation="TRUE" TargetMachine="17" ProgramDatabaseFile="" IgnoreAllDefaultLibraries="FALSE" IgnoreDefaultLibraryNames="" AdditionalDependencies=""/>
            <Tool Name="VCMIDLTool"/>
            <Tool Name="VCPostBuildEventTool"/>
            <Tool Name="VCPreBuildEventTool"/>
            <Tool Name="VCPreLinkEventTool"/>
            <Tool Name="VCResourceCompilerTool"/>
            <Tool Name="VCWebServiceProxyGeneratorTool"/>
            <Tool Name="VCXMLDataGeneratorTool"/>
            <Tool Name="VCWebDeploymentTool"/>
            <Tool Name="VCManagedWrapperGeneratorTool"/>
            <Tool Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
            <Tool Name="VCCLCompilerTool" PreprocessorDefinitions="__AMD64_VS2008_MD__;__MSVC__;WINNT;NDEBUG;NOMINMAX;QT_NO_DEBUG;EXA_CURRENT_YEAR=2015;CDI_VERSION=&quot;&quot;413-rstairs-03&quot;&quot;;PHYSTYPES_H=&lt;X:/proj/registry/$(R)/vccset/phystypes/export.h&gt;;SCALAR_H=&lt;X:/proj/registry/$(R)/vccset/scalar/scalar.h&gt;;LOOP_H=&lt;X:/proj/registry/$(R)/vccset/loop/loop.h&gt;;MSGERR_H=&lt;X:/proj/registry/$(R)/vccset/msgerr/msgerr.h&gt;;MALLOC_H=&lt;X:/proj/registry/$(R)/vccset/malloc/malloc.h&gt;;DEBUG_H=&lt;X:/proj/registry/$(R)/vccset/debug/debug.h&gt;;EARRAY_H=&lt;X:/proj/registry/$(R)/vccset/earray/earray.h&gt;;CIO_H=&lt;X:/proj/registry/$(R)/vccset/cio/cio.h&gt;;ESTRING_H=&lt;X:/proj/registry/$(R)/vccset/estring/estring.h&gt;;UNITS_H=&lt;X:/proj/registry/$(R)/vccset/units/units.h&gt;;AUDIT_H=&lt;X:/proj/registry/$(R)/vccset/audit/audit.h&gt;;CCUTILS_H=&lt;X:/proj/registry/$(R)/vccset/ccutils/ccutils.h&gt;;PLATFORM_H=&lt;X:/proj/registry/$(R)/vccset/platform/platform.h&gt;;SRI_H=&lt;X:/proj/registry/$(R)/vccset/sri/export.h&gt;;NETCDF_H=&lt;X:/proj/registry/$(R)/vccset/netcdf/amd64_vs2008_md/include/netcdf.h&gt;;ARG_HELPER_H=&lt;X:/proj/registry/$(R)/vccset/arg_helper/cARG_HELPER.h&gt;" RuntimeLibrary="2" ForceConformanceInForLoopScope="TRUE" AdditionalIncludeDirectories="..\amd64_vs2008_md;..;." ExceptionHandling="TRUE" DebugInformationFormat="3" ProgramDatabaseFileName="cdi.pdb" AdditionalOptions="/EHsc /nologo /MP8" Optimization="0" MinimalRebuild="TRUE" WarningLevel="2" Detect64BitPortabilityProblems="TRUE" RuntimeTypeInfo="TRUE" UsePrecompiledHeader=" 0" BasicRuntimeChecks="3"/>
            <Tool Name="VCLibrarianTool" OutputFile="$(IntDir)\cdi.lib"/>
        </Configuration>
    </Configurations>
    <Files>
        <Filter Name="Source Files" Filter="cpp;c;cxx;cc" UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}">
            <File RelativePath="cdi_get.cc">
                <FileConfiguration Name="Release|Win32"/>
                <FileConfiguration Name="Debug|Win32"/>
                <FileConfiguration Name="Release_with_symbols|Win32"/>
                <FileConfiguration Name="Release_with_symbols_64|x64"/>
            </File>
            <File RelativePath="cdi_interface.cc">
                <FileConfiguration Name="Release|Win32"/>
                <FileConfiguration Name="Debug|Win32"/>
                <FileConfiguration Name="Release_with_symbols|Win32"/>
                <FileConfiguration Name="Release_with_symbols_64|x64"/>
            </File>
            <File RelativePath="cdi_physics.cc">
                <FileConfiguration Name="Release|Win32"/>
                <FileConfiguration Name="Debug|Win32"/>
                <FileConfiguration Name="Release_with_symbols|Win32"/>
                <FileConfiguration Name="Release_with_symbols_64|x64"/>
            </File>
            <File RelativePath="cdi_readwrite.cc">
                <FileConfiguration Name="Release|Win32"/>
                <FileConfiguration Name="Debug|Win32"/>
                <FileConfiguration Name="Release_with_symbols|Win32"/>
                <FileConfiguration Name="Release_with_symbols_64|x64"/>
            </File>
            <File RelativePath="dump_cdi.cc">
                <FileConfiguration Name="Release|Win32"/>
                <FileConfiguration Name="Debug|Win32"/>
                <FileConfiguration Name="Release_with_symbols|Win32"/>
                <FileConfiguration Name="Release_with_symbols_64|x64"/>
            </File>
            <File RelativePath="dump_cdi_lrf.cc">
                <FileConfiguration Name="Release|Win32"/>
                <FileConfiguration Name="Debug|Win32"/>
                <FileConfiguration Name="Release_with_symbols|Win32"/>
                <FileConfiguration Name="Release_with_symbols_64|x64"/>
            </File>
            <File RelativePath="test_cdi.cc">
                <FileConfiguration Name="Release|Win32"/>
                <FileConfiguration Name="Debug|Win32"/>
                <FileConfiguration Name="Release_with_symbols|Win32"/>
                <FileConfiguration Name="Release_with_symbols_64|x64"/>
            </File>
            <File RelativePath="undump_cdi.cc">
                <FileConfiguration Name="Release|Win32"/>
                <FileConfiguration Name="Debug|Win32"/>
                <FileConfiguration Name="Release_with_symbols|Win32"/>
                <FileConfiguration Name="Release_with_symbols_64|x64"/>
            </File>
        </Filter>
        <Filter Name="Header Files" Filter="h;hpp;hxx" UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}">
            <File RelativePath="cdi_accessers.h">
            </File>
            <File RelativePath="cdi_common.h">
            </File>
            <File RelativePath="cdi_export.h">
            </File>
            <File RelativePath="cdi_get.h">
            </File>
            <File RelativePath="cdi_interface.h">
            </File>
            <File RelativePath="cdi_io.h">
            </File>
            <File RelativePath="cdi_physics.h">
            </File>
            <File RelativePath="cdi_readwrite.h">
            </File>
        </Filter>
        <Filter Name="Dummy CPP File" Filter="" UniqueIdentifier="">
            <File RelativePath="ignore_me.cpp">
                <FileConfiguration Name="Release|Win32" ExcludedFromBuild="TRUE"/>
                <FileConfiguration Name="Debug|Win32"/>
                <FileConfiguration Name="Release_with_symbols|Win32" ExcludedFromBuild="TRUE"/>
                <FileConfiguration Name="Release_with_symbols_64|x64" ExcludedFromBuild="TRUE"/>
            </File>
        </Filter>
    </Files>
    <Globals>
    </Globals>
</VisualStudioProject>
