/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#include CCUTILS_H
#include ARG_HELPER_H
#include "cdi_common.h"
#include "cdi_internal.h"

static uINT16 uDUMP_CDI_DFLOAT_DIG=0;

static STRING quotify_and_slashify_string (cSTRING src);

void print_indent(int depth);
void dump_chunk(CDI_INFO cdi_info, int depth);


bool roundoff_cdi = false;
bool no_audt_mods = false;
char file_name[BUFSIZ] = "";
STRING cmd_name = NULL;

int main(int argc,char *argv[])
{
  CDI_INFO cdi_info = NULL;
  asINT32 error_code;

  bool versions_only_flag = false;
  bool help_only_flag = false;
  bool no_audt_mods_flag = false;
  std::vector<std::string> filenames;
  cARG_HELPER cmndLineArgHelpr;

  platform_exa_debug_pfx(&argc, argv);

  if (!(cmd_name = getenv("EXA_CURRENT_CMD"))) {
    cmd_name = argv[0];
  };


  cmndLineArgHelpr.new_flag(0x00, "help", "print this help message" , help_only_flag);
  cmndLineArgHelpr.new_flag(0x00, "no_audt_mods", "no audit trail modification" , no_audt_mods_flag);
  cmndLineArgHelpr.new_flag('r', "round_off", "round off cdi values" , roundoff_cdi);
  cmndLineArgHelpr.set_string_vector("files", "<CDI filename>", filenames, true);
  cmndLineArgHelpr.set_description("Dump a CDI file into a structured dump w/LRF info");
  {
    std::string versionDesc = EXA_STR::Sprintf("print the version of %s", argv[0]);
    cmndLineArgHelpr.new_flag(0x00, "version", versionDesc.c_str(), versions_only_flag);
  }

  cmndLineArgHelpr.set_author("Exa Corp., http://www.exa.com");
  cmndLineArgHelpr.set_name("undump_cdi_lrf");
  {
          std::string usageString("Usage: ");
          usageString += "undump_cdi_lrf";
          usageString += " [--no_audt_mods] [options] <cdi_filename>";
          cmndLineArgHelpr.set_usage(usageString.c_str());
  }

  {
    const char *distname = platform_exa_distname();
    char *buf = EXA_MALLOC_ARRAY(char, strlen(CDI_VERSION) + (distname ? strlen(distname) : 0) + 4);
    sprintf(buf, "%s." CDI_VERSION, distname ? distname : "empty");
    cmndLineArgHelpr.set_version(buf);
  }

  //  cmndLineArgHelpr.set_build_date(EXA_BUILD_DATE);
  cmndLineArgHelpr.process(argc, argv);
  if (help_only_flag) {
          cmndLineArgHelpr.write_usage(stdout, 26);
          return false;
  }
  if (versions_only_flag) {
          cmndLineArgHelpr.write_version(stdout);
          return false;
  }


  if (filenames.size() == 1) {
                                /* If ok command line parse, and a file name was found */

    if(roundoff_cdi) 
      uDUMP_CDI_DFLOAT_DIG = 9;
    else
      uDUMP_CDI_DFLOAT_DIG = DFLOAT_DIG;

    cdi_info = cdi_open_for_read(file_name, &error_code);

    if (cdi_info == NULL) {
      msg_error("Unable to open cdi-type file named %s, %s", file_name,
		cdi_get_error_state_string(error_code));
      msg_error("Usage: %s [-no_audt_mods] <cdi_filename>", cmd_name);
      exit(1);
    }
  } else {
    msg_error("Usage: %s [-no_audt_mods] <filename>.cdi", cmd_name);
    exit(1);
  }

  printf("xcdi {\n  %5d %5d\n  case {\n",
	 cdi_major_version(cdi_info), cdi_minor_version(cdi_info));

  /* recursively dump the chunks */
  {
    asINT32 count = cio_get_count(cdi_info->cio_info);
    DOTIMES(i, count, {dump_chunk(cdi_info, 2);});
  }

  /* release the cdi system */
  cdi_close(cdi_info);
  
  printf("  }\n}\n");
  /* close the cdi file */

  return(0);
}

/****************************************************************\
|
| Function name:print_lrf
|
| Purpose:prints 
|
\****************************************************************/
void print_lrf(CDI_INFO cdi_info, int depth)
{
  /* allocate and read a eqns */
  CDI_LRF lrf=cdi_read_lrf(cdi_info);
  STRING slashified_name;
  
  slashified_name = quotify_and_slashify_string(lrf->name);
  
  printf(" { \n" );
  
  print_indent(depth+1);
  printf("name { %s }\n", slashified_name);
  
  exa_free(slashified_name);
  
#if 0
  print_indent(depth+1);
  printf("ang_vel     { %f } \n", lrf->ang_vel    );
  print_indent(depth+1);
  printf("ang_vel_eqn { %s } \n", lrf->ang_vel_eqn);
#endif
  
  print_indent(depth+1);
  printf("cyl_begin { %f %f %f } \n", lrf->cyl_begin[0], lrf->cyl_begin[1], lrf->cyl_begin[2] );
  print_indent(depth+1);
  printf("cyl_end   { %f %f %f } \n", lrf->cyl_end[0], lrf->cyl_end[1], lrf->cyl_end[2] );
  
  print_indent(depth+1);
  printf("half_plane   { %f %f %f } \n", lrf->half_plane_dir[0], lrf->half_plane_dir[1], lrf->half_plane_dir[2] );
  
  print_indent(depth+1);
  printf("type   { %s } \n", lrf->lrf_type == CDI_STATIC_LRF ? "STATIC" : "SLIDING" );
  
  
  print_indent(depth+1);
  printf("num_points { %d }\n", lrf->pnts.num_points );
  
  DOTIMES(i,  
	  lrf->pnts.num_points , 
	  {
	    print_indent(depth+2);
	    printf("{ %f %f }\n",  lrf->pnts.points[i*2], lrf->pnts.points[i*2+1]);
	  }
	  );
  
  print_indent(depth);
  printf("}\n");
  
  /* free the csys memory */
  cdi_destroy_lrf(lrf);
}


/****************************************************************	\
|
| Function name:print_indent
|
| Purpose:prints indent
|
\****************************************************************/
void
print_indent(int depth)
{
  int i;

  for(i=0;i<depth;i++)
    printf("  ");
}

/****************************************************************\
|
| Function name:dump_chunk
|
| Purpose:dumps chunk
|
\****************************************************************/
void dump_chunk(CDI_INFO cdi_info, int depth)
{
  CIO_INFO cio = cdi_info->cio_info;
  CIO_CCCC type;
  char type_str[10];
  int count, i;
  cio_descend(cio);

  type = cio_get_type(cio);
  cio_type_to_string(type, type_str);


  switch(type) {
    /* look for children */

  case CDI_CHUNK_TYPE_LRFS: 
    {
      count = cio_get_count(cio);
      for(i=0;i<count;i++)
        dump_chunk(cdi_info, depth+1);
      break;
    }
  }
  
  switch(type) {
  case CDI_CHUNK_TYPE_SLRF:
  case CDI_CHUNK_TYPE_MLRF: {print_lrf(cdi_info, depth) ; break;}
  default:          break;       
  }
  
  cio_ascend(cio);
}
 

/*** Given a string s, returns a string which is s surrounded by double quotes, and
 *** with a backslash inserted before each double quote and backslash contained in s.
 *** This effectively converts s into a representation which can be unambiguously
 *** parsed later.
 */
static STRING quotify_and_slashify_string (cSTRING src)
{
  /** Worst case is we have to add two quotes plus a backslash for every character! */
  STRING dest = (STRING)exa_malloc(2 * strlen(src) + 3, 
				   "quotify_and_slashify_string",
				   "a new string");

  dest[0] = '"';

  {
    CHARACTER *next_dest_char = dest + 1;
    const CHARACTER *next_src_char = src;

    /** For some unknown reason, the SGI compiler will complain unless the ugly
     ** casts to (int) are present in the next two lines...
     */
    while ((int)(*next_src_char) != (int)'\0') {
      if ((int)(*next_src_char) == (int)'"' || (int)(*next_src_char) == (int)'\\') {
	*next_dest_char = '\\';
	next_dest_char++;
      }
      *next_dest_char = *next_src_char;
      next_dest_char++;
      next_src_char++;
    }

    *next_dest_char = '"';
    next_dest_char++;
    *next_dest_char = '\0';
  }
  return(dest);
}

