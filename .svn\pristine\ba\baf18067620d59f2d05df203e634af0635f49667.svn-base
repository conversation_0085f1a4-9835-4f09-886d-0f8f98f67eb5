/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#include "cdi_common.h"


// Call with a map from chunk types to parser objects.
//
// The chunk types are those a consumer wants to parse (chunks that have useful information).
// Other chunk types are ignored.
//
// The parser objects are classes derived from cCDI_CHUNK_PARSER that know how to read
// a chunk.
//
// You must pass the map in to the constructor, e.g.:
//
// cCDI_READER cdiReader = cCDI_READER({ { CDI_CHUNK_TYPE_UNDB, new cMY_UNDB_CHUNK_PARSER(&cdiReader) },
//                                       { CDI_CHUNK_TYPE_CSYS, new cMY_CSYS_CHUNK_PARSER(&cdiReader) } });
//
cCDI_READER::cCDI_READER(cCDI_CHUNK_PARSER_MAP&& parsers)
    : m_parsers(std::move(parsers))
{
}

cCDI_READER::~cCDI_READER()
{
  for (auto parser : m_parsers) {
    delete parser.second;
  }
}

// Process a CDI file, call PostProcess at the end, and close it.
bool cCDI_READER::Parse(const std::string& cdiFile)
{
  asINT32 cdiError;
  CDI_INFO ci = cdi_open_for_read(cdiFile.c_str(), &cdiError);

  bool status = (cdiError == CDI_ERR_OK) && (ci != nullptr);

  if (status) {
    Parse(ci);        // process the file
    PostProcess();    // do any necessary finalization
    cdi_close(ci);
  }
  return status;
}

// Parse CDI, starting with a specific context instead of opening the file.
// Note that this overload does not call PostProcess.  I copied the pattern
// cRMC_CDI_READER, hoping to make it easy to refactor cRMC_CDI_READER to a
// derived class, but it might be better to call PostProcess here and not
// make any caller do it.
void cCDI_READER::Parse(CDI_INFO ci, cCDI_CHUNK_PARSER* parent)
{
  asINT32 count = cio_get_count(ci->cio_info);
  CIO_CCCC chunkType = cio_get_type(ci->cio_info);

  for (asINT32 i = 0; i < count; i++) {
    cio_descend(ci->cio_info);
    chunkType = cio_get_type(ci->cio_info);

    if (parent) {
      parent->parseChild(ci, chunkType);
    }
    else if (m_parsers.find(chunkType) != m_parsers.end()) {
      m_parsers[chunkType]->parse(ci);
    }
    cio_ascend(ci->cio_info);
  }
}


std::string cCDI_CHUNK_PARSER::ReadName(CDI_INFO ci)
{
  CIO_CCCC chunkType = cio_get_type(ci->cio_info);
  std::string namestr;

  if (chunkType == CDI_CHUNK_TYPE_NAME) {
    CDI_NAME name = cdi_read_name(ci);
    namestr = name->name;
    cdi_destroy_name(name);
  }
  return namestr;
}
