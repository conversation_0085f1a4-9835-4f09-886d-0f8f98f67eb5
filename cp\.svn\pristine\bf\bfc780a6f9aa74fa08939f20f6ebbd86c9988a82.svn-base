/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Job Control Support (runs on the CP)
 *
 * Jim Salem, Exa Corporation 
 * Created Mon Jun 20 1994
 *--------------------------------------------------------------------------*/

#include "common.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "exa_sim.h"
#include "jobctl.h"
#include "jobctl_status.h"
#include "window.h"
#if SURF_COUP
#include "coupling_model.h"
#endif
#include "errbuf.h"
#include "sim_tree.h"
#include <signal.h>
#include <sys/wait.h>
#include <string>
#include <iostream>
#include <fstream>
#include <sstream>

#include "text_table.h"

#include JOBCTL_PATHLOCKS_H

/*--------------------------------------------------------------------------*
 * Exit functions
 *--------------------------------------------------------------------------*/ 

// This allows us to return a special error code on
// Alpha MPI runs for improved termination behavior

asINT32 exit_failure_code(VOID)
{
#if defined(__alpha)
  return(123);     // GLOBAL ERROR exit code; see prun man page
#else
  return(EXIT_FAILURE);
#endif
}

int simulator_finish_status = 0;
static BOOLEAN inside_err_exit = FALSE;
/* Overrides the err_exit function from the msg_error library */

static BOOLEAN we_killed_our_children = FALSE;
				/* Flag, indicating that the decomposer died
				 * BECAUSE the cp killed it.  This suppresses
				 * a secondary error message associated with
				 * CP `discovering' that the decomp has
				 * died */


VOID err_exit(VOID)
{
  CHARACTER status_string[256];
  BOOLEAN output_status_p = FALSE;
  if (!inside_err_exit) {
    inside_err_exit = TRUE;
    /* Don't bother printing anything if the lattice was never cleared */
    if (cp_info_has_been_initialized_p) {
      sprintf(status_string, "Error during %s", cp_jobctl_status_string());
      jobctl_server_terminate_program("Simulator: ", status_string); // Must call this instead of cp_jobctl_output_status
      output_status_p = TRUE;
      msg_print("%s", status_string);
    }
  }

  we_killed_our_children = TRUE;	/* Flag that we sent a kill */
  if (decomposer_child_pid != ((pid_t) -1)) {
				/* If we ever ran a decomposer, better try to
				 * kill it.  Benign if decomposer already gone */
    kill(decomposer_child_pid, SIGTERM);
				/* Send terminate signal to decomposer process */
    waitpid(decomposer_child_pid, NULL, 0);
    // Dump the status message again in case the decomposer overwrote the status
    if (output_status_p)
      jobctl_server_terminate_program("Simulator: ", status_string); // Must call this instead of cp_jobctl_output_status
  }

#if SEPARATE_LICENSE_PROCESS
  // Try to kill license process. Benign if process already gone.
  if (license_child_pid != ((pid_t) -1)) {
    kill(license_child_pid, SIGTERM);
  }
#endif

  // kill all coupling applications still running
#if SURF_COUP
  maybe_terminate_surface_coupling_jobs();
#endif
  exit(simulator_finish_status = exit_failure_code());
}

VOID cp_jobctl_terminate(cSTRING message, asINT32 exit_code, BOOLEAN print_status)
{
  // Since we are about to explicitly kill all our children, we don't want to
  // see SIGCHLD. In particular, a SIGCHLD from the license process would
  // cause it to be restarted, which is not appropriate when we are shutting
  // everything down.
  // jobctl_server_sigset(JOBCTL_CHLD_SIGNAL, SIG_IGN);
  we_killed_our_children = TRUE;	/* Flag that we sent a kill */

  if (print_status) {
    CHARACTER status_string[256];
    cp_recompute_status();
    if (message != NULL)
      sprintf(status_string, "Terminated at %s: %s", jobctl_server_get_status(), message);
    else
      sprintf(status_string, "Terminated at %s", jobctl_server_get_status());

    jobctl_server_terminate_program("Simulator: ", status_string); // Must call this instead of cp_jobctl_output_status
    msg_print("%s", status_string);
  }

  if (decomposer_child_pid != ((pid_t) -1)) {
				/* If we ever ran a decomposer, better try to
				 * kill it.  Benign if decomposer already gone */
    kill(decomposer_child_pid, SIGTERM);
				/* Send terminate signal to decomposer process */
  }

#if SEPARATE_LICENSE_PROCESS
  // Try to kill license process. Benign if process already gone.
  if (license_child_pid != ((pid_t) -1)) {
    kill(license_child_pid, SIGTERM);
  }
#endif
  
  // kill any running surface coupling applications
#if SURF_COUP
  maybe_terminate_surface_coupling_jobs();
#endif
  exit(exit_code);
}

/*--------------------------------------------------------------------------*
 * Updating the CP status from the SP status
 *--------------------------------------------------------------------------*/
static sSIM_SOLVER_STATUS g_status_child_status[MAX_SIM_STATUS_TREE_DEGREE];
static STP_PROC g_status_n_child_sps;     // how many SPs send me status messages

static inline BOOLEAN recv_status_from_children()
{
  BOOLEAN received_status = FALSE;
  while (1) {
    int flag;
    MPI_Status mpi_status;
    MPI_Iprobe(MPI_ANY_SOURCE, eMPI_SP_STATUS_TAG, eMPI_sp_cp_comm, &flag, &mpi_status);
    if (!flag)
      break;
    received_status = TRUE;
    sSP_SIM_STATUS child_status;
    RECV_EXA_SIM_MSG<sSP_SIM_STATUS, 1> recv_child_status(eMPI_SP_STATUS_TAG, MPI_ANY_SOURCE);
    g_exa_sp_cp_comm.recv(recv_child_status.mpi_msg);
    child_status = *recv_child_status.return_buffer();
    g_status_child_status[child_status.source_index] = child_status.status;
  }
  return received_status;
}

VOID force_cp_status_to_time_zero()
{
  cp_info.cp_status = 0;
  cp_info.time = 0;
}

VOID cp_recompute_status()
{
  BOOLEAN received_status = recv_status_from_children();
  if (received_status) {
    sSIM_SOLVER_STATUS new_cp_status(SIM_STATUS_DONE); // SIM_STATUS_DONE is greater than all real timesteps
    ccDOTIMES(i, g_status_n_child_sps) {
      sSIM_SOLVER_STATUS child_status = g_status_child_status[i];
      if (cp_info.is_conduction && cp_info.is_flow) {
        //Overall status updated both by flow and conduction SPs
        if (child_status.status < new_cp_status.status) {
          new_cp_status.status = child_status.status;
        }
        //Checks independently flow & conduction status 
        // if (child_status.flow_status == SIM_STATUS_INACTIVE && child_status.cond_status == SIM_STATUS_INACTIVE) {
        //   msg_internal_error("Flow and Conduction SPs cannot be frozen at the same time.");
        // }
        if (child_status.flow_status != SIM_STATUS_INACTIVE && child_status.flow_status < new_cp_status.flow_status) {
          new_cp_status.flow_status = child_status.flow_status;
        } 
        if (child_status.cond_status != SIM_STATUS_INACTIVE && child_status.cond_status < new_cp_status.cond_status) {
          new_cp_status.cond_status = child_status.cond_status;
        }
      } else {
        if (child_status.status < new_cp_status.status) {
          new_cp_status = child_status;
        }
      }
    }
    // Done getting info from all children, time to update cp_info with new status
    if (new_cp_status.status == SIM_STATUS_DONE) {
      // Since status messages float up reduction tree from the SPs, it's possible
      // that a status message with the last timestep never reached the CP. Thus we
      // explcitly set the status to the last timestep.
      cp_info.cp_status.status = SIM_STATUS_DONE;
      cp_info.time = cp_info.end_time;
      cp_info.flow_time = cp_info.flow_end_time;
      cp_info.cond_time = cp_info.cond_end_time;
      cp_jobctl_output_current_status();
      cp_info.coupling_phase_timers.sample(cp_info.cp_status.status == SIM_STATUS_DONE);
    } else if ((new_cp_status.status > cp_info.cp_status.status) || 
               (new_cp_status.flow_status > cp_info.cp_status.flow_status) ||
               (new_cp_status.cond_status > cp_info.cp_status.cond_status)) {
      if (new_cp_status.status != SIM_STATUS_INACTIVE)
        cp_info.cp_status.status = new_cp_status.status;
      if (new_cp_status.flow_status != SIM_STATUS_INACTIVE && new_cp_status.flow_status != SIM_STATUS_DONE)
        cp_info.cp_status.flow_status = new_cp_status.flow_status;
      if (new_cp_status.cond_status != SIM_STATUS_INACTIVE && new_cp_status.cond_status != SIM_STATUS_DONE)
        cp_info.cp_status.cond_status = new_cp_status.cond_status;
      cp_info.time = cp_info.cp_status.status;
      cp_info.flow_time = cp_info.cp_status.flow_status;
      cp_info.cond_time = cp_info.cp_status.cond_status;
      cp_jobctl_output_current_status();
      cp_info.coupling_phase_timers.sample();
    }
  }
}

void update_rp_status()
{
  int flag;
  MPI_Status mpi_status;
  MPI_Iprobe(MPI_ANY_SOURCE, eMPI_RP_STATUS_TAG, eMPI_rp_cp_comm, &flag, MPI_STATUS_IGNORE);
  if (!flag) {
    return;
  }

  sRP_STATUS rp_status;

  MPI_Recv(&rp_status,
           sizeof(rp_status),
           MPI_BYTE,
           MPI_ANY_SOURCE,
           eMPI_RP_STATUS_TAG,
           eMPI_rp_cp_comm,
           MPI_STATUS_IGNORE);

  CHARACTER rp_status_string[512];

  if (rp_status.solution_time > 0) {
    sprintf(rp_status_string, "%s [Radiation solution finished] (%.3e secs)",
            cp_jobctl_status_string(),
            rp_status.solution_time);
  } else {

    SIM_STATUS s;
    if (cp_info.is_flow && cp_info.is_conduction) {
      s = 0;
    } else {
      s = cp_info.is_flow ? rp_status.flow_timestep : rp_status.cond_timestep;
    }

    int count = cp_jobctl_timestep_status_string(s, rp_status.flow_timestep, rp_status.cond_timestep, rp_status_string);
    sprintf(&rp_status_string[count], " [Radiation solution started]");
  }

  if (sim_args.run_options & SIM_REPORT_RP_TIME) {
    msg_print("%s", rp_status_string);
  }

  cp_jobctl_output_status(rp_status_string);
}

void update_sp_radiation_wait_time()
{
  static constexpr int MAX_NUM_WAITS = 100;
  static int number_of_waits = 0;

  if (number_of_waits == MAX_NUM_WAITS) {
    return;
  }

  int flag;
  MPI_Status mpi_status;
  MPI_Iprobe(MPI_ANY_SOURCE, eMPI_RP_STATUS_TAG, eMPI_sp_cp_comm, &flag, MPI_STATUS_IGNORE);
  if (!flag) {
    return;
  }

  sRP_STATUS rp_status;

  MPI_Recv(&rp_status,
           sizeof(rp_status),
           MPI_BYTE,
           MPI_ANY_SOURCE,
           eMPI_RP_STATUS_TAG,
           eMPI_sp_cp_comm,
           MPI_STATUS_IGNORE);

  CHARACTER sp_wait_string[512];

  SIM_STATUS s;
  if (cp_info.is_flow && cp_info.is_conduction) {
    s = 0;
  } else {
    s = cp_info.is_flow ? rp_status.flow_timestep : rp_status.cond_timestep;
  }

  int count = cp_jobctl_timestep_status_string(s, rp_status.flow_timestep, rp_status.cond_timestep, sp_wait_string);

  sprintf(&sp_wait_string[count], " [Waited %.3f seconds for radiation data]", rp_status.solution_time);

  msg_warn("%s", sp_wait_string);

  cp_jobctl_output_status(sp_wait_string);

  number_of_waits++;

  if (number_of_waits == MAX_NUM_WAITS) {
    msg_warn("Too many warnings waiting for radiation data, disabling reporting.");
  }

}

VOID initialize_status_tree()
{
  MPI_Datatype mpi_type;
  sCP_TO_TREE_NODE_INIT_MSG::mpi_type_init(&mpi_type);

  STP_PROC total_ranks = total_sps + 1;
  sTREE_NODE *tree = xnew sTREE_NODE[total_ranks];

  STP_PROC cp_node = total_sps;
  tree[cp_node].parent_node = NO_PARENT;

  make_subtree(cp_node, total_ranks, SIM_STATUS_TREE_DEGREE, tree);

  g_status_n_child_sps = tree[cp_node].n_children;
  ccDOTIMES(i, g_status_n_child_sps)
    g_status_child_status[i] = SIM_STATUS_INIT;
  
  ccDOTIMES(sp, total_sps) {
    sCP_TO_TREE_NODE_INIT_MSG msg;
    msg.parent_rank = tree[sp].parent_node;
    msg.n_children = tree[sp].n_children; 

    msg.source_index = -1;
    sTREE_NODE *parent = &tree[ tree[sp].parent_node ];
    ccDOTIMES(i, parent->n_children) {
      if (parent->children[i] == sp) {
        msg.source_index = i;
        break;
      }
    }
    if (msg.source_index == -1)
      msg_internal_error("Failed to find SP %d as child of parent in status tree", sp);
    
    ccDOTIMES(child, msg.n_children) {
      msg.child_rank[child] = tree[sp].children[child];
    }
    cp_init_mpi_send(&msg, 1, mpi_type, sp, eMPI_SP_STATUS_TAG, eMPI_sp_cp_comm);
  }

#if DEBUG_STATUS_TREE
  msg_print("=========> Status reduction tree");
  print_tree_array(total_ranks, tree);
  print_subtree(cp_node, 2,  tree);
#endif

  delete[] tree; 
  MPI_Type_free(&mpi_type);
}

#if 0
VOID initialize_status_tree()
{
  // First determine the subtree size at each level and the number of levels
  const int MAX_TREE_LEVELS = 32; // allow for max possible levels
  STP_PROC subtree_sizes[MAX_TREE_LEVELS]; 
  asINT32 degree = SIM_STATUS_TREE_DEGREE;
  asINT32 top_level = 0;
  asINT32 child_subtree_size = 0;
  subtree_sizes[top_level] = child_subtree_size;
  asINT32 p = 1;
  while (child_subtree_size < total_sps) {
    child_subtree_size = degree * (child_subtree_size + 1);
    top_level++;
    subtree_sizes[top_level] = child_subtree_size;      
  }

  asINT32 subtree_size = subtree_sizes[top_level - 1];
  asINT32 sp_inc = subtree_size + 1; //13  4

  g_status_n_child_sps = (total_sps + sp_inc - 1) / sp_inc;

  ccDOTIMES(i, g_status_n_child_sps)
    g_status_child_status[i] = SIM_STATUS_INIT;

#if DEBUG_STATUS_TREE
  msg_print("# children = %d", g_status_n_child_sps);
#endif
}
#endif

VOID handle_pending_async_event_request(VOID)
{
  static STP_PROC next_sp_to_test = 0;
  if (cp_info.async_msg_reply_pending_p) {
    static TIMESTEP max_timestep = -1;
    ccDO_FROM_BELOW(sp,next_sp_to_test,total_sps) {
      MPI_Status eMPI_status;
      int new_status;
      MPI_Iprobe(sp, eMPI_ASYNC_EVENT_REPLY_TAG, eMPI_sp_cp_comm, &new_status, &eMPI_status);
      if(new_status) {
        TIMESTEP ts;
        next_sp_to_test = sp+1;
        RECV_EXA_SIM_MSG<TIMESTEP, 1> recv_ts(eMPI_ASYNC_EVENT_REPLY_TAG, sp);
        g_exa_sp_cp_comm.recv(recv_ts.mpi_msg);
        ts = *recv_ts.return_buffer();
        if (max_timestep < ts)
          max_timestep = ts;
      } else {
        return;
      }
    }

    // If we reach here, the CP has received a ASYNC_EVENT_REPLY message from all SPs.
    {
      TIMESTEP next_event_timestep = max_timestep + 1;
      max_timestep = -1;
      {
        sASYNC_EVENT_PKG &pkg = cp_info.async_event_pkg;
        TIMESTEP this_event_timestep;          
        BOOLEAN do_special_for_thermal_accel = FALSE;
        if (cp_info.lattice_type == STP_LATTICE_D19) {
          do_special_for_thermal_accel = ((pkg.msg.event_id == EVENT_ID_THERMAL_ACCEL_ON) || (pkg.msg.event_id == EVENT_ID_THERMAL_ACCEL_OFF));
        }

        if (do_special_for_thermal_accel) {
          if (pkg.msg.timestep > next_event_timestep) {
            this_event_timestep = pkg.msg.timestep;
          } else {
            this_event_timestep = next_event_timestep + 1; //need to process event one timestep prior to thermal accel on/off to allow the seeding of associated T_solver
          }
        } else if (pkg.msg.event_id == EVENT_ID_READ_DSM_FILE) {
          if (pkg.msg.timestep >= next_event_timestep) {
            /* If the event is requested at a specific time step
               in the future, then use that instead */
            this_event_timestep = pkg.msg.timestep;
          } else {
            this_event_timestep = next_event_timestep;
            msg_warn("Scheduling DSM read at timestep %d even though it was requested at timestep %d",
                      next_event_timestep, pkg.msg.timestep);
          }
        } else {
          if (pkg.msg.timestep >= next_event_timestep) {
            /* If the event is requested at a specific time step
               in the future, then use that instead */
            this_event_timestep = pkg.msg.timestep;
          } else {
            this_event_timestep = next_event_timestep;
          }
        }

        if ((this_event_timestep != TIMESTEP_LAST) && pkg.coarsest_time) {

          /* Provided that the specified time step is not
             set to be the magic `end' step, adjust to the
             next synchronous step if needed */

          TIMESTEP coarse_time_step = 1 << (cp_info.num_scales - 1);
          /* In subcycling, the synchronous step should be not only the multiple of the coarse_time_step
           * but also the multiple of timesteps of all solvers in the simulator
           */
          TIMESTEP lcm_steps = coarse_time_step * lcm(cp_info.n_lb_base_steps,
                                                      cp_info.n_t_base_steps,
                                                      cp_info.n_ke_base_steps,
                                                      cp_info.n_uds_base_steps,
                                                      cp_info.n_conduction_base_steps, cp_info.n_particle_base_steps);  //in base steps
          TIMESTEP this_event_base_steps = this_event_timestep 
            * cp_info.n_user_base_steps;
          this_event_timestep = lcm_steps 
            * ceil((dFLOAT)this_event_base_steps / (dFLOAT)lcm_steps) / cp_info.n_user_base_steps;  //in user's time
        }

        if (do_special_for_thermal_accel)
          pkg.msg.timestep = this_event_timestep - 1;  //will add "1" back in sp 
        else
          pkg.msg.timestep = this_event_timestep;

        // msg can never be freed because of how it is used below in the MPI_Isend.
        // We'll live with this tiny memory leak. The alternative is allocating
        // total_sps MPI send requests.
        sASYNC_EVENT_MSG *msg = xnew sASYNC_EVENT_MSG;
        *msg = pkg.msg;
        ccDOTIMES(k, total_sps) {
          MPI_Request send_request;
          MPI_Isend(msg, 1, cp_info.eMPI_ASYNC_MSG_TYPE,
                    k, eMPI_ASYNC_EVENT_MSG_TAG, eMPI_sp_cp_comm, &send_request);
          // We can free the request immediately because we never have to verify that 
          // the message was sent and we never intend to free the send buffer or 
          // read/write the send buffer.
#if defined(_EXA_HPMPI)
          // For now, never free the request on Platform MPI. See PR 32012 and 40853.
          if (!sim_args.no_mpi_request_free && FALSE)
#else
          if (!sim_args.no_mpi_request_free)
#endif
            MPI_Request_free(&send_request);
        }

        TIMESTEP initial_end_time = cp_info.end_time;

        switch (pkg.msg.event_id) {
          case EVENT_ID_EXIT: 
	  case EVENT_ID_HALT: {
            CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
            new_queue_entry->id = pkg.msg.event_id;
            new_queue_entry->timestep = this_event_timestep;
            new_queue_entry->recur_rate = 0;
            new_queue_entry->initial_condition = FALSE;
            new_queue_entry->precious = FALSE;
            cp_info.end_time = this_event_timestep;
#if DEBUG_MONITOR
            msg_print("New EXIT/HALT event at timestep %d", this_event_timestep);
#endif

            cp_info.async_ckpt_queue->add_entry(new_queue_entry);
            break;
          }

          case EVENT_ID_MME_CKPT: {
            CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
            new_queue_entry->id = EVENT_ID_MME_CKPT;
            new_queue_entry->timestep = this_event_timestep;
            new_queue_entry->recur_rate = 0;
            new_queue_entry->initial_condition = FALSE;
            new_queue_entry->precious = FALSE;
            
            cp_info.async_ckpt_queue->add_entry(new_queue_entry);
            break;
          }

          case EVENT_ID_MME_CKPT_EXIT: {
           CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
           new_queue_entry->id = EVENT_ID_MME_CKPT;
           new_queue_entry->timestep = TIMESTEP_LAST;
           new_queue_entry->recur_rate = 0;
           new_queue_entry->initial_condition = FALSE;
           new_queue_entry->precious = TRUE;
           cp_info.end_time = this_event_timestep;

           cp_info.async_ckpt_queue->add_entry(new_queue_entry);

           new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
           new_queue_entry->id = EVENT_ID_EXIT;
           new_queue_entry->timestep = this_event_timestep;
           new_queue_entry->recur_rate = 0;
           new_queue_entry->initial_condition = FALSE;
           new_queue_entry->precious = FALSE;

           cp_info.async_ckpt_queue->add_entry(new_queue_entry);
           break;
          }

          case EVENT_ID_FULL_CKPT: {
            CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
            new_queue_entry->id = EVENT_ID_FULL_CKPT;
            new_queue_entry->timestep = this_event_timestep;
            new_queue_entry->recur_rate = 0;
            new_queue_entry->initial_condition = FALSE;
            new_queue_entry->precious = FALSE;

            cp_info.async_ckpt_queue->add_entry(new_queue_entry);
            break;
          }

          case EVENT_ID_FULL_CKPT_EXIT: {
            CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
            new_queue_entry->id = EVENT_ID_FULL_CKPT;
            new_queue_entry->timestep = TIMESTEP_LAST;
            new_queue_entry->recur_rate = 0;
            new_queue_entry->initial_condition = FALSE;
            new_queue_entry->precious = (sim_args.no_final_checkpoint_retry? FALSE: TRUE);
            cp_info.end_time = this_event_timestep;

            cp_info.async_ckpt_queue->add_entry(new_queue_entry);

            new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
            new_queue_entry->id = EVENT_ID_EXIT;
            new_queue_entry->timestep = this_event_timestep;
            new_queue_entry->recur_rate = 0;
            new_queue_entry->initial_condition = FALSE;
            new_queue_entry->precious = FALSE;

            cp_info.async_ckpt_queue->add_entry(new_queue_entry);
            break;
          }

          case EVENT_ID_CKPT_INTERVAL: {
            EVENT_ID ckpt_event_id = cp_info.async_ckpt_queue->remove_recurring_ckpt_event(this_event_timestep);
            TIMESTEP period = pkg.msg.arg;
            // period <= 0 --> disable periodic checkpoints
            if (period > 0) {
              if (ckpt_event_id == EVENT_ID_INVALID) // not found in queue
                ckpt_event_id = sim_args.run_options & SIM_FULL_CKPTS ? EVENT_ID_FULL_CKPT : EVENT_ID_MME_CKPT;
              TIMESTEP start = period * (this_event_timestep / period + 1);

              CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
              new_queue_entry->id = ckpt_event_id;
              new_queue_entry->timestep = start;
              new_queue_entry->recur_rate = period;
              new_queue_entry->initial_condition = TRUE;
              new_queue_entry->precious = FALSE;
              cp_info.async_ckpt_queue->add_entry(new_queue_entry);
            }
            break;
          }

          case EVENT_ID_STOP_AVG_MME_CKPT: {                                 
#if DEBUG_AVG_MME
            msg_print("Stop avg fluid ckpt scheduled for timestep %d", this_event_timestep);
#endif
            cp_info.time_to_stop_avg_mme = this_event_timestep;
            // If the current avg mme ckpt timestep is bigger than the time to stop avg mme, should remove the avg mme ckpt event immediately.
            EVENT_ID ckpt_event_id = cp_info.async_ckpt_queue->remove_recurring_average_mme_ckpt_event_after_time(this_event_timestep);
            if (ckpt_event_id == EVENT_ID_AVG_MME_CKPT)
              cp_info.average_mme_stopped = TRUE;
            break;
          }


          case EVENT_ID_RESCHEDULE_AVG_MME_CKPT: {
            EVENT_ID ckpt_event_id = cp_info.async_ckpt_queue->remove_recurring_ckpt_event(this_event_timestep, TRUE);
            TIMESTEP period = pkg.msg.arg;
            if (period > 0) {
              if (ckpt_event_id == EVENT_ID_INVALID) { // not found in queue
                msg_warn("Recurrsive avg fluid ckpt event not found in the queue");
                ckpt_event_id = EVENT_ID_AVG_MME_CKPT;
              }
              // Add a new avg mme ckpt event
              CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
              new_queue_entry->id = ckpt_event_id;

              // The scheduled time could overlap with the current timestep if both are aligning with coarsest_timestep. Use the 
              // next aligned timestep since it may be too late to use the current one.
              TIMESTEP coarsest_timestep = 1 << (cp_info.num_scales - 1);
              TIMESTEP scheduled_timestep = this_event_timestep + coarsest_timestep;
 
              new_queue_entry->timestep = scheduled_timestep + period; // scheduled_timestep is the clear time
              new_queue_entry->recur_rate = cp_info.avg_mme_ckpt_interval;
              new_queue_entry->initial_condition = TRUE;
              new_queue_entry->precious = FALSE;
              cp_info.async_ckpt_queue->add_entry(new_queue_entry);
              
              cp_info.time_to_stop_avg_mme = MIN((dFLOAT)scheduled_timestep + cp_info.avg_mme_duration, TIMESTEP_MAX);
#if DEBUG_AVG_MME
              msg_print("Add new avg fluid ckpt event at timestep %d with period %d duration %d", new_queue_entry->timestep, period, cp_info.avg_mme_duration);
              msg_print("Time to stop avg fluid ckpt is %d", cp_info.time_to_stop_avg_mme);
#endif

              // XDU TODO: add a global variable to tell the avg mme meas window index
              asINT32 avg_mme_meas_window_index = -1;
              ccDOTIMES(i, cp_info.n_cdi_meas_windows) {
                CDI_MEAS_WINDOW cdi_meas_win = &cp_info.cdi_meas_windows[i];
                if (cdi_meas_win->is_average_mme) {
                  avg_mme_meas_window_index = i;
                  break;
                }
              }

              if (avg_mme_meas_window_index < 0)
                msg_internal_error("Average fluid meas window does not exist.");

              CDI_MEAS_WINDOW cdi_meas_win = &cp_info.cdi_meas_windows[avg_mme_meas_window_index];

              // Update the cdi window start and end time so that they are written in full ckpt
              cdi_meas_win->start_time = scheduled_timestep;
              cdi_meas_win->end_time = cp_info.time_to_stop_avg_mme;

#if DEBUG_AVG_MME
              msg_print("AVG MME CKPT: cdi win start %d end %d period %d", cdi_meas_win->start_time, cdi_meas_win->end_time, cdi_meas_win->period);
#endif

              cdi_meas_win->find_meas_window_end_time_via_monitors(scheduled_timestep);
            }
            break;
          }

          case EVENT_ID_START_EMITTER: {
            asINT32 n_emitters = pkg.msg.arg;
#if DEBUG_START_EMITTERS
            msg_print("Start %d sim emitters scheduled for timestep %d", n_emitters, this_event_timestep);
#endif
            //cp_particle_sim.dump_emitters_to_start();
            std::vector<asINT32> &emitters_to_start = cp_particle_sim.emitters_requested_to_start.begin()->second;
            ccDOTIMES(i, emitters_to_start.size()) {
              // Find the emitter in cp_particle_sim.emitters and start it
              ccDOTIMES(emitter_id, cp_particle_sim.emitters.size()) {
                if (emitters_to_start[i] == cp_particle_sim.emitters[emitter_id]->id()) {
                  cp_particle_sim.emitters[emitter_id]->set_start_time(this_event_timestep);
                  break;
                }
              }
            }

            MPI_Request *send_requests = cnew MPI_Request [total_sps];
            ccDOTIMES(i, total_sps)
              send_requests[i] = MPI_REQUEST_NULL;

            asINT32 *send_buffer = new asINT32 [emitters_to_start.size()];
            ccDOTIMES(i, emitters_to_start.size()) {
              send_buffer[i] = emitters_to_start[i];
            }

            // Send the emitters ids which are started to all SPs
            ccDOTIMES(sp, total_sps) {
              MPI_Isend(&send_buffer[0], emitters_to_start.size(), eMPI_sINT32, sp,
                eMPI_EMITTER_ID_TAG, eMPI_sp_cp_comm, (send_requests + sp));
            }

            // Remove the current entry since the emitters start time is scheduled
            cp_particle_sim.emitters_requested_to_start.erase(cp_particle_sim.emitters_requested_to_start.begin());
            cp_info.request_to_start_emitter = cp_particle_sim.emitters_requested_to_start.size() == 0? FALSE : TRUE;
            break;
          }

          case EVENT_ID_START_WIPER: {
            asINT32 n_wipers = pkg.msg.arg;
            std::vector<asINT32> &wipers_to_start = cp_particle_sim.wipers_requested_to_start.begin()->second;
            ccDOTIMES(i, wipers_to_start.size()) {
              cp_particle_sim.wipers[wipers_to_start[i]]->set_start_time(this_event_timestep);
            }
          
            MPI_Request *send_requests = cnew MPI_Request [total_sps];
            ccDOTIMES(i, total_sps)
              send_requests[i] = MPI_REQUEST_NULL;

            // Send the wiper IDs to start to each SP.
            asINT32 *send_buffer = new asINT32 [wipers_to_start.size()];
            ccDOTIMES(i, wipers_to_start.size()) {
              send_buffer[i] = wipers_to_start[i];
            }

            ccDOTIMES(sp, total_sps) {
              MPI_Isend(&send_buffer[0], wipers_to_start.size(), eMPI_sINT32, sp,
                eMPI_WIPER_ID_TAG, eMPI_sp_cp_comm, (send_requests + sp));
            }

            // Remove the current entry since the wipers' start time is scheduled
            cp_particle_sim.wipers_requested_to_start.erase(cp_particle_sim.wipers_requested_to_start.begin());
            cp_info.request_to_start_wiper = cp_particle_sim.wipers_requested_to_start.size() == 0? FALSE : TRUE;
            break;
          }

        case EVENT_ID_READ_DSM_FILE: {
            cp_info.dsm_reader->read_dsm_data_and_send_to_sps();
            break;
          }
          case EVENT_ID_ENABLE_WARNING:
          case EVENT_ID_DISABLE_WARNING: {
#if 0 // Message already issued
            asINT32 error_type = pkg.msg.arg;
            msg_print("Disabling errors of type %d starting at timestep %d",
                      error_type, this_event_timestep);
#endif
            break;
          }

          /* Nothing to do for these events - probably shouldn't happen */
          case EVENT_ID_TIMERS_ON:
          case EVENT_ID_TIMERS_OFF:
          case EVENT_ID_DIAGS_ON:
          case EVENT_ID_DIAGS_OFF: 
          case EVENT_ID_MAXVEL:
          case EVENT_ID_MAXTEMP:
          case EVENT_ID_MINTEMP:
          case EVENT_ID_DELAY_VEL_WARNINGS:
          case EVENT_ID_DELAY_TEMP_WARNINGS:
	  case EVENT_ID_DELAY_UDS_WARNINGS: {
            break;
          }
 
          /* Nothing to do for these either - also, probably shouldn't happen */
          case EVENT_ID_INVALID:
          default: {
            break;
          }
        }

        if (this_event_timestep == TIMESTEP_LAST) {
          /* If event scheduled for END... */
          if (pkg.user_initiated) {
            msg_print("Timestep %d: User signal \"%s\" scheduled for last timestep",
                      cp_info.time, get_event_signal_name(pkg.msg.event_id));
          }
        } else {                /* Else, event scheduled for an absolute step... */
          if (pkg.user_initiated) {
            msg_print("Timestep %d: User signal \"%s\" scheduled for timestep %d",
                cp_info.time, get_event_signal_name(pkg.msg.event_id), this_event_timestep);
          }
          if (initial_end_time == cp_info.end_time) {
            /* If end time of sim unchanged... */
            if (this_event_timestep > cp_info.end_time) {
              msg_warn("Event is scheduled for after the end of simulation "
                       "(last timestep = %ld)", (long) cp_info.end_time);
            }
          } else {
            if (initial_end_time > cp_info.end_time
                && cp_info.async_ckpt_queue->some_non_exit_event_later_than(cp_info.end_time)) {
              /* If end time of sim reduced... */
              msg_warn ("The end of the simulation has been changed from "
                        "%d to %d timesteps. "
                        "Events scheduled after timestep %d will not be processed "
                        "unless the simulation time is extended. End-of-simulation "
                        "events will still be processed.",
                        initial_end_time, cp_info.end_time,
                        cp_info.end_time);
            } else {
              msg_warn("The end of the simulation has been changed from "
                       "%d to %d timesteps.",
                       initial_end_time, cp_info.end_time);
            }
          }
        }
      }

      cp_info.async_msg_reply_pending_p = FALSE;
      next_sp_to_test = 0;
    }
  }
}


/*--------------------------------------------------------------------------*
 * Signal handling
 *--------------------------------------------------------------------------*/

static void terminate_immediately_handler(int signal)
{
  cp_jobctl_terminate(jobctl_sim_signal_description(signal), EXIT_SUCCESS);
}

static void force_exit_handler(int signal)
{
  if (cp_info.time > 0) {
    // Create empty file so that downstream software knows this was a force_exit. Use
    // exa_lockpath both here and in downstream software to eliminate the race conditions
    // that can arise with NFS. There are only 2 callers of exa_lockpath, here in the CP
    // and the final code that determines the exit status of the simulation. If there 
    // were more callers, this scheme would not work.
    exa_lockpath(".exa_force_exit");
    //FILE *f = fopen(".exa_force_exit", "w");
    //if (f)
    //  fclose(f);

    CHARACTER final_status[1024];
    if(cp_info.is_conduction && cp_info.is_flow)
      sprintf(final_status, "Finished %d Flow Timesteps, %d Conduction Timesteps: Force Exit Request", cp_info.flow_time, cp_info.cond_time);
    else
      sprintf(final_status, "Finished %d Timesteps: Force Exit Request", cp_info.time);


    msg_print("%s", final_status);
    jobctl_server_terminate_program("Simulator: ", final_status); // Must call this instead of cp_jobctl_output_status
    cp_jobctl_terminate(jobctl_sim_signal_description(signal), EXIT_SUCCESS, FALSE);
  } else {
    cp_jobctl_terminate(jobctl_sim_signal_description(signal), exit_failure_code());
  }
}

static void terminate_immediately_handler_exit_failure(int signal)
{
  // Some queuing systems cannot send SIGUSR1 or SIGWINCH so they create .exa_force_exit
  // hoping that the CP will see it and report the exit reason as "Force Exit Request".
  // There is a race, however, and the CP may not see the file. 
  // 
  // One option would be for the queuing system to create .exa_force_exit via exa_lockpath,
  // and for the CP to test for its presence here with exa_lockpath, but then the CP would
  // always create .exa_force_exit (and immediately delete it) which then risks confusing
  // downstream software due to a race associated with deleting the file.
  if (platform_file_present_for_read(".exa_force_exit")
      && cp_info.time > 0) {
    force_exit_handler(SIGUSR1);
  } else {
    cp_jobctl_terminate(jobctl_sim_signal_description(signal), exit_failure_code());
  }
}

// Make it available to all files in CP
//static
BOOLEAN register_async_event_request(EVENT_ID id, dFLOAT arg, TIMESTEP timestep,
                                  BOOLEAN user_initiated, BOOLEAN coarsest_time) 
{
  // Do not register the next event until the previous one is cleared.
  if (!cp_info.async_msg_reply_pending_p) {
    sASYNC_EVENT_PKG &pkg = cp_info.async_event_pkg;
    pkg.msg.event_id = id;
    pkg.msg.arg = arg;
    pkg.msg.timestep = timestep;
    pkg.user_initiated = user_initiated;
    pkg.coarsest_time = coarsest_time;

    ccDOTIMES(i, total_sps) {
      static asINT32 dummy_msg = 0xDEAF;
      MPI_Request send_request;
      MPI_Isend(&dummy_msg, 1, eMPI_sINT32, i, eMPI_ASYNC_EVENT_REQ_TAG,
                eMPI_sp_cp_comm, &send_request); 
      // Free the request immediately because we never have to verify that the message was sent,
      // and we never have to free the send buffer or read/write the send buffer.
#if defined(_EXA_HPMPI)
      // For now, never free the request on Platform MPI. See PR 32012 and 40853.
      if (!sim_args.no_mpi_request_free && FALSE)
#else
      // For now, never free the request for other MPI like Intel MPI. See PR42157
      if (!sim_args.no_mpi_request_free && FALSE)
#endif
        MPI_Request_free(&send_request);
    }
    cp_info.async_msg_reply_pending_p = TRUE;
    return TRUE;
  } else {
    //msg_warn("Unable to register event %d for timestep %d at %d since other event is pending.", id, timestep, cp_info.time);
    return FALSE;
  }
}

static TIMESTEP parse_timestep(cSTRING checkpoint_parm) {
  TIMESTEP step;

  if (!checkpoint_parm) {
    step = TIMESTEP_INVALID;
  } else if ((strncasecmp(checkpoint_parm, "end", strlen("end")) == 0) ||
	     (strncasecmp(checkpoint_parm, "atend", strlen("atend")) == 0) ||
	     (strncasecmp(checkpoint_parm, "at_end", strlen("at_end")) == 0) ||
	     (strncasecmp(checkpoint_parm, "at-end", strlen("at-end")) == 0)) {
    step = TIMESTEP_LAST;
  } else if (sscanf(checkpoint_parm, "%d", &step) != 1) {
    step = TIMESTEP_INVALID;
  }

  return(step);
}

static inline VOID maybe_warn_async_ckpt(BOOLEAN is_full_ckpt,
                                         BOOLEAN ckpt_at_coarsest_time)
{

  if (!is_full_ckpt && !ckpt_at_coarsest_time) {
    BOOLEAN some_rotating_lrf = FALSE;
    ccDOTIMES(i, cp_info.n_lrfs()) {
      if (!cp_info.sri_lrfs[i].has_constant_angular_vel
          || cp_info.sri_lrfs[i].constant_angular_vel_mag != 0) {
        some_rotating_lrf = TRUE;
        break;
      }
    }

    BOOLEAN is_non_inertial_grf = (cp_info.is_global_ref_frame
                                   && (!cp_info.global_nirf_info.is_angular_vel_constant
                                       || cp_info.global_nirf_info.angular_vel[0] != 0
                                       || cp_info.global_nirf_info.angular_vel[1] != 0
                                       || cp_info.global_nirf_info.angular_vel[2] != 0
                                       || !cp_info.global_nirf_info.is_ref_point_vel_constant));
    
    if (some_rotating_lrf
        || is_non_inertial_grf
        || cp_info.is_some_bc_time_varying)
      msg_warn("Asynchronous fluid checkpoints are not recommended for"
               " simulations with time-varying boundary conditions or non-inertial"
               " references frames. In the future, consider using synchronous checkpoints"
               " (exasignal -fluid_sync_ckpt) instead. Proceeding with"
               " asynchronous fluid checkpoint.");
  }
}
 
static VOID checkpoint_and_terminate_handler(BOOLEAN create_full_async_ckpt,
                                             BOOLEAN ckpt_at_coarsest_time,
                                             cSTRING checkpoint_parm,
                                             BOOLEAN user_initiated)
{
  if (cp_info.cp_status.status == SIM_STATUS_INIT)
    /* If we're still initializing, then terminate immediately */
    cp_jobctl_terminate("No checkpoint file created", EXIT_SUCCESS);
  else {
    EVENT_ID id;
    maybe_warn_async_ckpt(create_full_async_ckpt, ckpt_at_coarsest_time);

    if(create_full_async_ckpt) 
      id = EVENT_ID_FULL_CKPT_EXIT;
    else 
      id = EVENT_ID_MME_CKPT_EXIT;

    register_async_event_request(id, 0, parse_timestep(checkpoint_parm), user_initiated, ckpt_at_coarsest_time);
  }
}

static void read_dsm_file_handler(cSTRING parms)
{

  cSTRING dsm_filename = parms;
  std::ifstream dsm_fstream(dsm_filename);

  if (!dsm_fstream.is_open()) {
    msg_warn("DSM file %s does not exist or cannot be opened\n", dsm_filename);
    return;
  }
  std::string tmp_string, tmp_string_stripped;
  std::getline(dsm_fstream, tmp_string);

  if (tmp_string.compare(0, 13, "DSM_INFO_FILE") != 0) {
    msg_warn("First line does not contain DSM_INFO_FILE\n");
    return;
  }
  TIMESTEP timestep;
  while (std::getline(dsm_fstream, tmp_string)) {
    if (tmp_string.empty())
      continue;
    std::istringstream parse_stream(tmp_string);
    parse_stream >> std::ws; // skip white space
    // blank line?
    if (parse_stream.eof())
      continue;
    std::string lhs, rhs;
    if (!(parse_stream >> lhs)) {
      msg_warn("skipping line\n");
      continue;
    }
    if ("readtimestep" == lhs) {
      if (!(parse_stream >> timestep)) {
        msg_warn("timestep invalid %d\n", timestep);
      }
    } else if ("readfilename" == lhs) {
      std::string binfilename;
      if (!(parse_stream >> binfilename)) {
        msg_warn("bin filename invalid %s\n", binfilename.c_str());
      }
      // If the path to the datafile is relative, prepend the path of the EFF
      // file to the datafile path
      if (!platform_is_absolute_path(binfilename.c_str())) {
        char dest_path[PLATFORM_MAXPATHLEN];
        memset(dest_path, 0, PLATFORM_MAXPATHLEN);
        if (!platform_get_file_dir_name(dsm_filename, dest_path)) {
          msg_warn("Unable to get path to the dsm file:  %s", dsm_filename);
          return;
        }
        std::string dest_path_str = dest_path;
        if (dest_path_str.empty())
          dest_path_str = ".";
        binfilename = dest_path_str + '/' + binfilename;
      }
      if (!platform_file_present_for_read(binfilename.c_str())) {
          msg_warn("Unable to locate data file %s references in %s",
              binfilename.c_str(), dsm_filename);
          return;
      }
      cp_info.dsm_reader->set_filename(binfilename);

    } else {
      msg_warn("Skipping line %s\n", tmp_string.c_str());
    }
  }

  dsm_fstream.close();
  if (timestep > cp_info.dsm_reader->get_last_dsm_read_timestep() ) {
    register_async_event_request(EVENT_ID_READ_DSM_FILE, 0, timestep, TRUE, FALSE);
    cp_info.dsm_reader->set_last_dsm_read_timestep(timestep);
  } else {
    msg_warn("Last DSM read event was scheduled for timestep %d and next read cannot be scheduled at requested timestep %d",
              cp_info.dsm_reader->get_last_dsm_read_timestep(), timestep);
  }
}

static void checkpoint_handler(BOOLEAN create_full_async_ckpt,
			       BOOLEAN ckpt_at_coarsest_time,
			       cSTRING checkpoint_parm,
			       BOOLEAN user_initiated)
{
  EVENT_ID id = create_full_async_ckpt ? EVENT_ID_FULL_CKPT : EVENT_ID_MME_CKPT;

  maybe_warn_async_ckpt(create_full_async_ckpt, ckpt_at_coarsest_time);

  register_async_event_request(id, 0, parse_timestep(checkpoint_parm), user_initiated, ckpt_at_coarsest_time);
}

static void thermal_accel_on_handler(cSTRING parms, BOOLEAN user_initiated)
{
  BOOLEAN start_on_coarsest_timestep = FALSE;
  if (cp_info.lattice_type == STP_LATTICE_D19)
    start_on_coarsest_timestep = TRUE;

  dFLOAT d_start_time;
  dFLOAT d_duration;
  // Be robust in presence of very large values
  if (parms != NULL) {
    asINT32 n_parms = sscanf(parms, "%lg %lg", &d_start_time, &d_duration);
    if (n_parms == 0) {
      d_start_time = TIMESTEP_INVALID;
      d_duration = TIMESTEP_INVALID;      
    } else if (n_parms == 1) {
      d_duration = TIMESTEP_INVALID;
    }
  } else {
    d_start_time = TIMESTEP_INVALID;
    d_duration = TIMESTEP_INVALID;      
  }    

  const TIMESTEP max_timestep = ~((TIMESTEP)1 << (8 * sizeof(TIMESTEP) - 1));
  TIMESTEP duration = TIMESTEP_INVALID;
  TIMESTEP start_time = 0;
  if (d_start_time > 0) {
    if (d_start_time > max_timestep)
      start_time = max_timestep;
    else {
      start_time = d_start_time;
    }
  }
  if (d_duration > 0) {
    if (d_duration > max_timestep)
      duration = TIMESTEP_INVALID;
    else {
      if (cp_info.lattice_type == STP_LATTICE_D19) {
	TIMESTEP ct         = 1 << (cp_info.num_scales - 1); // coarsest timestep
	TIMESTEP lcm_steps  = ct * lcm(cp_info.n_lb_base_steps,
                                       cp_info.n_t_base_steps,
                                       cp_info.n_ke_base_steps,
                                       cp_info.n_uds_base_steps,
                                       cp_info.n_conduction_base_steps, cp_info.n_particle_base_steps);//in base steps
	duration = d_duration * cp_info.n_user_base_steps;                                              //in base steps
	duration = lcm_steps * ceil((dFLOAT)duration / (dFLOAT)lcm_steps) / cp_info.n_user_base_steps;  //in user's time
      } else {
	duration = d_duration;
      }
    }
  }

#if 0
  if (d_duration > 0) {
    // round to multiple of coarsest timestep
    TIMESTEP coarse_time_step = 1 << (cp_info.num_scales - 1);
    d_duration = MAX((dFLOAT)coarse_time_step * floor(d_duration / coarse_time_step + 0.5), (dFLOAT)coarse_time_step);
    if (d_duration > max_timestep)
      duration = TIMESTEP_INVALID;
    else
      duration = d_duration;
    /* In subcycling, the synchronous step should be not only the multiple of the coarse_time_step
     * but also the multiple of timesteps of all solvers in the simulator
     */
    TIMESTEP lcm_steps = coarse_time_step 
      * lcm(cp_info.n_lb_base_steps, cp_info.n_t_base_steps, cp_info.n_ke_base_steps, cp_info.n_conduction_base_steps, cp_info.n_particle_base_steps);  //in base steps
    TIMESTEP duration_base_steps = duration
      * cp_info.n_user_base_steps;
    duration = lcm_steps 
      * floor((dFLOAT)duration_base_steps / (dFLOAT)lcm_steps) / cp_info.n_user_base_steps;  //in user's time
  }
#endif
  register_async_event_request(EVENT_ID_THERMAL_ACCEL_ON, duration, start_time, user_initiated, start_on_coarsest_timestep);
}

static void thermal_accel_off_handler(cSTRING parms, BOOLEAN user_initiated)
{
  const BOOLEAN stop_on_coarsest_timestep = TRUE;
  dFLOAT d_stop_time;

  // Be robust in presence of very large values
  if (parms != NULL) {
    asINT32 n_parms = sscanf(parms, "%lg", &d_stop_time);
    if (n_parms == 0)
      d_stop_time = TIMESTEP_INVALID;
  } else {
    d_stop_time = TIMESTEP_INVALID;
  }    

  const TIMESTEP max_timestep = ~((TIMESTEP)1 << (8 * sizeof(TIMESTEP) - 1));
  TIMESTEP stop_time = 0;
  if (d_stop_time > 0) {
    if (d_stop_time > max_timestep)
      stop_time = max_timestep;
    else
      stop_time = d_stop_time;
  }    
  
  register_async_event_request(EVENT_ID_THERMAL_ACCEL_OFF, 0, stop_time, user_initiated, stop_on_coarsest_timestep);
}

static void checkpoint_interval_handler(cSTRING checkpoint_parm)
{
  // Set user_initiated to FALSE because we don't want the CP to print a message saying
  // that it scheduled the event to change the interval. Instead when the event is processed,
  // we want SP0 to print a message saying that the interval has changed.
  BOOLEAN user_initiated = FALSE; 

  // Be robust in presence of very large values
  // Round interval to a multiple of the coarsest timestep
  TIMESTEP coarse_time_step = 1 << (cp_info.num_scales - 1);
  dFLOAT d_interval = atof(checkpoint_parm);
  if (d_interval > 0)
    d_interval = MAX((dFLOAT)coarse_time_step * floor(d_interval / coarse_time_step + 0.5), (dFLOAT)coarse_time_step);
  const TIMESTEP max_timestep = ~((TIMESTEP)1 << (8 * sizeof(TIMESTEP) - 1));
  TIMESTEP interval;
  if (d_interval > max_timestep)
    interval = 0;
  else
    interval = d_interval;

  // @@@ In DGF simulator, make sure we have a shared routine to round/floor/ceil to a coarsest timestep
  // Finish rounding interval to a multiple of the coarsest timestep
  /* In subcycling, the synchronous step should be not only the multiple of the coarse_time_step
   * but also the multiple of timesteps of all solvers in the simulator
   */
  TIMESTEP lcm_steps = coarse_time_step * lcm(cp_info.n_lb_base_steps,
                                              cp_info.n_t_base_steps,
                                              cp_info.n_ke_base_steps,
                                              cp_info.n_uds_base_steps,
                                              cp_info.n_conduction_base_steps, cp_info.n_particle_base_steps);  //in base steps
  TIMESTEP interval_base_steps = interval
    * cp_info.n_user_base_steps;
  interval = lcm_steps 
    * floor((dFLOAT)interval_base_steps / (dFLOAT)lcm_steps) / cp_info.n_user_base_steps;  //in user's time

  register_async_event_request(EVENT_ID_CKPT_INTERVAL, interval, TIMESTEP_INVALID, user_initiated, FALSE);
}

static STRING strip_whitespace_from_end(STRING s)
{
  asINT32 i = strlen(s) - 1;
  while (isspace(s[i]))
    i--;

  s[i + 1] = '\0';
  return s;
}


/* Handles any pending timers on request by delivering it to the SPs */
static VOID timers_on_handler(BOOLEAN on_p, BOOLEAN user_initiated)
{
  msg_print("Received request to turn %s detailed timers at timestep %d.",
	    on_p ? "on" : "off", sim_status_timestep(cp_info.cp_status.status));
  
  EVENT_ID id;
  if(on_p) id = EVENT_ID_TIMERS_ON;
  else id = EVENT_ID_TIMERS_OFF;
  register_async_event_request(id, 0, TIMESTEP_INVALID, user_initiated, FALSE);
}

static VOID diag_avg_handler(sINT32 diag_avg, BOOLEAN user_initiated)
{
  BOOLEAN on_p;
  if (diag_avg > 0) {
    msg_print("Received request to turn on diagnostic reports (averaged over %d timesteps) at timestep %d.",
	      diag_avg, sim_status_timestep(cp_info.cp_status.status));
    msg_print("Note that enabling diagnostic reports causes"
	      " hard synchronizations that invalidate timers.");
    register_async_event_request(EVENT_ID_DIAGS_ON, diag_avg, TIMESTEP_INVALID, user_initiated, FALSE);
  }
  else {
    msg_print("Received request to turn off diagnostic reports at timestep %d.",
	      sim_status_timestep(cp_info.cp_status.status));
    diag_avg = 10000000;
    register_async_event_request(EVENT_ID_DIAGS_OFF, diag_avg, TIMESTEP_INVALID, user_initiated, FALSE);
  }
}

static VOID maxvel_handler(dFLOAT maxvel, BOOLEAN user_initiated)
{
  register_async_event_request(EVENT_ID_MAXVEL, maxvel, TIMESTEP_INVALID, user_initiated, FALSE);
}

static VOID maxtemp_handler(dFLOAT maxtemp, BOOLEAN user_initiated)
{
  register_async_event_request(EVENT_ID_MAXTEMP, maxtemp, TIMESTEP_INVALID, user_initiated, FALSE);
}

static VOID mintemp_handler(dFLOAT mintemp, BOOLEAN user_initiated)
{
  register_async_event_request(EVENT_ID_MINTEMP, mintemp, TIMESTEP_INVALID, user_initiated, FALSE);
}

static VOID exit_handler(cSTRING exit_parm, BOOLEAN user_initiated)
{
  TIMESTEP step = parse_timestep(exit_parm);
  register_async_event_request(EVENT_ID_EXIT, 0, step, user_initiated, FALSE);
}

static VOID halt_handler(BOOLEAN user_initiated)
{
  TIMESTEP step = 0;
  register_async_event_request(EVENT_ID_HALT, 0, step, user_initiated, FALSE);
}

// Checks exa_signal file for kill, abort, exit (not exit #time), force_exit, or halt and 
// terminates the simulation during initialization.
VOID maybe_terminate_simulation() {
  if (jobctl_server_is_termination_signal_cmd_pending()) {
    msg_print("Termination signal received from user");
    exit(1);
  }
}

static BOOLEAN cp_process_signal_cmds(std::vector <sCMD_STRING> *cmds_read,
                                      BOOLEAN defer_processing_p, BOOLEAN user_initiated)
{  
  // Creating a reference to the vector. This makes accessing the objects in
  // the vector easier.
  std::vector <sCMD_STRING> &cmds = *cmds_read;
  asINT32 n_cmds = cmds.size();
  if (!n_cmds || cp_info.n_signal_cmds_pending || cp_info.async_msg_reply_pending_p) {
    return FALSE;
  }

  cp_info.pending_signal_cmds = cmds_read; // points to a static vector of strings
  cp_info.n_signal_cmds_read  = n_cmds;

  asINT32 n_pending_cmds = n_cmds;
  ccDOTIMES(nc, n_cmds) {
    // Abort gets precendence over defer_processing_p. As per Jamie, kill
    // will go away from exasignal, then defer_processing_p can be moved
    // outside the loop. However, defer_processing_p is always FALSE at the
    // call sites. May be it is a remenant from some deprecated
    // functionality.
    if (strcmp(cmds[nc].cmd, "kill") == 0
        || strcmp(cmds[nc].cmd, "abort") == 0) {
      cp_jobctl_terminate("Abort Request", exit_failure_code());
    } else if (strcmp(cmds[nc].cmd, "force_exit") == 0) {
      FILE *f = fopen(".exa_force_exit", "w"); // create file so wrappers know this was a force_exit
      fclose(f);
      cp_jobctl_terminate("Force Exit Request", EXIT_SUCCESS);
    } else {
      if (!defer_processing_p) {
        cp_finish_signal_cmd(user_initiated, cmds[nc].cmd);
        n_pending_cmds --;
        // Still processing this command, no point in trying
        // other commands. Come back once this is processed.
        if (cp_info.async_msg_reply_pending_p)
          break;
      }
    }
  }
  // n_signal_cmds_pending keeps on reducing as commands are processed.
  cp_info.n_signal_cmds_pending = n_pending_cmds; 
  return TRUE;
}

BOOLEAN cp_issue_signal_cmd(cSTRING cmd) {
  static std::vector <sCMD_STRING> cmd_strings;

  cmd_strings.clear();
  if (!cmd || cp_info.n_signal_cmds_pending ||
      cp_info.async_msg_reply_pending_p) {
    return(FALSE);
  } else {
    sCMD_STRING input_cmd;
    // This is the maximum the character array can fit.
    strncpy(input_cmd.cmd, cmd, sizeof(input_cmd.cmd)-1);
    // All the characters are 0 by  construction
    //input_cmd[sizeof(input_cmd)-1] = (char) 0x00;
    cmd_strings.push_back(input_cmd);
    return(cp_process_signal_cmds(&cmd_strings, FALSE, FALSE));
  }
}

VOID cp_handle_signal_cmds(BOOLEAN defer_processing_p)
{  
  // If some previous commands are still pending, return from function.
  if (cp_info.n_signal_cmds_pending || cp_info.async_msg_reply_pending_p) { 
    return;
  }

  // read commands from .exa_signal_cmd file
  std::vector <sCMD_STRING> *cmds_read;
  cmds_read = jobctl_server_get_signal_cmds();

  if (cmds_read->size() > 0) {
    if ((*cmds_read)[0].cmd[0] == '\0')
      msg_warn("User signal received, but the signal command file (" 
	       EXA_SIGNAL_CMD_FILE ") was empty. Perhaps the disk is full.");
    else {
      ccDOTIMES(nc, cmds_read->size()) {
        msg_print("Timestep %d: User signal \"%s\" received", cp_info.time, (*cmds_read)[nc].cmd);
      }
      cp_process_signal_cmds(cmds_read, defer_processing_p, TRUE);
    }
  }
}

VOID cp_finish_signal_cmd(BOOLEAN user_initiated, cSTRING cmdptr)
{
  char cmd[EXA_SIGNAL_CMD_MAX_LENGTH + 1];
  char msgbuf[1024] = "";

  STRING parms = NULL;

  cSTRING ptr = cmdptr;

  /* Spin past any initial white space */ 
  while (*ptr && isspace(*ptr)) ptr++;
  if (*ptr) {			/* If anything after the whitespace... */
    strcpy(cmd, ptr);		/* Make a copy */
    parms = NULL;		/* Initially assume no parameters */
    STRING ptr = cmd;		/* Use pointer on locally allocated space now... */

    /* Spin past the initial non-space characters */
    while (*ptr && !isspace(*ptr)) ptr++;

    if (*ptr) {		/* If ended with a space... */
      *ptr = (char) NULL;	/* Null terminate cmd */
      ptr++;			/* Advance a character */

      /* Spin past any intervening space */
      while (*ptr && isspace(*ptr)) ptr++;
      if (*ptr) {		/* If found anything, call it the parm string */
	parms = ptr;
      }
    }
  } else {
    return;
  }

#define CMDNCMP(str) strncmp(cmd, str, strlen(str))

  if (strcmp(cmd, "ckptexit") == 0)
    checkpoint_and_terminate_handler(cp_info.create_full_ckpts, FALSE, parms, user_initiated);
  else if (strcmp(cmd, "fluid_ckptexit") == 0)
    checkpoint_and_terminate_handler(FALSE, FALSE, parms, user_initiated);
  else if (strcmp(cmd, "full_ckptexit") == 0) {
    if (sim_args.gpu) {
      msg_warn("Full checkpoints are not supported on GPU. Please use \"--fluid_ckptexit\" instead.");
    } else {
      checkpoint_and_terminate_handler(TRUE, FALSE, parms, user_initiated);
    }
  }
  else if (strcmp(cmd, "ckpt") == 0) 
    checkpoint_handler(cp_info.create_full_ckpts, FALSE, parms, user_initiated);
  else if (strcmp(cmd, "fluid_ckpt") == 0) 
    checkpoint_handler(FALSE, FALSE, parms, user_initiated);
  else if (strcmp(cmd, "full_ckpt") == 0) {
    if (sim_args.gpu) {
      msg_warn("Full checkpoints are not supported on GPU. Please use \"--fluid_ckpt\" instead.");
    } else {
      checkpoint_handler(TRUE, FALSE, parms, user_initiated);
    }
  }
  else if (strcmp(cmd, "ckpt_interval") == 0) 
    checkpoint_interval_handler(parms);
  else if (strcmp(cmd, "thermal_accel_on") == 0)
    thermal_accel_on_handler(parms, user_initiated);
  else if (strcmp(cmd, "thermal_accel_off") == 0)
    thermal_accel_off_handler(parms, user_initiated);

  else if (strcmp(cmd, "fluid_sync_ckpt") == 0) 
    checkpoint_handler(FALSE, TRUE, parms, user_initiated);
  else if (strcmp(cmd, "fluid_sync_ckptexit") == 0)
    checkpoint_and_terminate_handler(FALSE, TRUE, parms, user_initiated);

  else if (CMDNCMP("time_every") == 0) {
    int n_timesteps = 0;
    sscanf(parms, "%d", &n_timesteps);
    sprintf(msgbuf, "Received request to time every %d timesteps at timestep %d.",
            MAX(0, n_timesteps), sim_status_timestep(cp_info.cp_status.status));
    sim_args.time_every = MAX(0, n_timesteps);
  }
  else if (strcmp(cmd, "read_dsm_file") == 0) {
    //cp_info.dsm_filename = strsave(parms);
    sprintf(msgbuf, "Received request at timestep %d to read dsm file \"%s\"",
            sim_status_timestep(cp_info.cp_status.status), parms);
    read_dsm_file_handler(parms);
  }
  else if (strcmp(cmd, "ckpt_file") == 0) {
    cp_info.checkpoint_filename = strsave(parms);
    sprintf(msgbuf, "Received request at timestep %d to set the base name of checkpoint files to \"%s\"",
            sim_status_timestep(cp_info.cp_status.status), cp_info.checkpoint_filename);
  }    
  else if (strcmp(cmd, "timers_on") == 0)
    timers_on_handler(TRUE, user_initiated);
  else if (strcmp(cmd, "timers_off") == 0)
    timers_on_handler(FALSE, user_initiated);
  else if (CMDNCMP("diag") == 0) {
    int diag_avg = 0;
    sscanf(parms, "%d", &diag_avg);
    sprintf(msgbuf, "Received request to turn on diagnostics with averaging interval of %d.", diag_avg);
    diag_avg_handler(diag_avg, user_initiated);
  } else if (CMDNCMP("maxvel") == 0) {
    sprintf(msgbuf, "Received request at timestep %d to set max velocity threshold for warnings to %s.", 
            sim_status_timestep(cp_info.cp_status.status), parms);
    dFLOAT max_vel;
    if (uval_to_lattice_value(parms, "Velocity", "velocity", 
			      "LatticeVelocity", "-maxvel", FALSE, &max_vel)) {
      maxvel_handler(max_vel, user_initiated);
    }
  } else if (CMDNCMP("maxtemp") == 0) {
    sprintf(msgbuf, "Received request at timestep %d to set max temperature threshold for warnings to %s.", 
            sim_status_timestep(cp_info.cp_status.status), parms);
    dFLOAT max_temp;
    if (uval_to_lattice_value(parms, "Temperature", "temperature", 
			      "LatticeTemperature", "-maxtemp", FALSE, &max_temp)) {
      maxtemp_handler(max_temp, user_initiated);
    }
  } else if (CMDNCMP("mintemp") == 0) {
    sprintf(msgbuf, "Received request at timestep %d to set min temperature threshold for warnings to %s.", 
	    sim_status_timestep(cp_info.cp_status.status), parms);
    dFLOAT min_temp;
    if (uval_to_lattice_value(parms, "Temperature", "temperature", 
			      "LatticeTemperature", "-mintemp", FALSE, &min_temp)) {
      mintemp_handler(min_temp, user_initiated);
    }
  } else if (CMDNCMP("delay_vel_warnings") == 0) {
    int ts = 0;
    sscanf(parms, "%d", &ts);    
    sprintf(msgbuf, "Received request at timestep %d to delay out-of-range velocity warnings to timestep %d.", 
            sim_status_timestep(cp_info.cp_status.status), ts);
    register_async_event_request(EVENT_ID_DELAY_VEL_WARNINGS, ts, TIMESTEP_INVALID, user_initiated, FALSE);    
  } else if (CMDNCMP("limit_vel_warnings") == 0) {
    int count = 0;
    sscanf(parms, "%d", &count);    
    sprintf(msgbuf, "Received request at timestep %d to limit the number of out-of-range velocity warnings to %d.", 
            sim_status_timestep(cp_info.cp_status.status), count);
    cp_change_max_errors(SP_EER_VEL_TOO_BIG, count);
    cp_change_max_errors(SP_EER_VEL_OUT_OF_RANGE, count);
    cp_change_max_errors(SP_EER_MACH_TOO_BIG, count);
  } else if (CMDNCMP("limit_temp_warnings") == 0) {
    int count = 0;
    sscanf(parms, "%d", &count);    
    sprintf(msgbuf, "Received request at timestep %d to limit the number of out-of-range temperature warnings to %d.", 
            sim_status_timestep(cp_info.cp_status.status), count);
    cp_change_max_errors(SP_EER_TEMP_TOO_BIG, count);
    cp_change_max_errors(SP_EER_TEMP_TOO_SMALL, count);
    cp_change_max_errors(SP_EER_TEMP_ABOVE_DYN_RANGE, count);
    cp_change_max_errors(SP_EER_TEMP_BELOW_DYN_RANGE, count);
  } else if (CMDNCMP("delay_temp_warnings") == 0) {
    int ts = 0;
    sscanf(parms, "%d", &ts);    
    sprintf(msgbuf, "Received request at timestep %d to delay out-of-range temperature warnings to timestep %d.", 
            sim_status_timestep(cp_info.cp_status.status), ts);
    register_async_event_request(EVENT_ID_DELAY_TEMP_WARNINGS, ts, TIMESTEP_INVALID, user_initiated, FALSE);    
  } else if (CMDNCMP("delay_uds_warnings") == 0) {
    int ts = 0;
    sscanf(parms, "%d", &ts);    
    sprintf(msgbuf, "Received request at timestep %d to delay out-of-range uds warnings to timestep %d.", 
            sim_status_timestep(cp_info.cp_status.status), ts);
    register_async_event_request(EVENT_ID_DELAY_UDS_WARNINGS, ts, TIMESTEP_INVALID, user_initiated, FALSE);    
  } else if (CMDNCMP("disable_warning") == 0) {
    int warning_id = 0;
    sscanf(parms, "%d", &warning_id);    
    sprintf(msgbuf, "Disabling errors of type %d", warning_id);
    register_async_event_request(EVENT_ID_DISABLE_WARNING, warning_id, TIMESTEP_INVALID, user_initiated, FALSE);
    user_initiated = TRUE; // so msgbuf is printed below
  } else if (CMDNCMP("enable_warning") == 0) {
    int warning_id = 0;
    sscanf(parms, "%d", &warning_id);    
    sprintf(msgbuf, "Enabling errors of type %d", warning_id);
    register_async_event_request(EVENT_ID_ENABLE_WARNING, warning_id, TIMESTEP_INVALID, user_initiated, FALSE);   
    user_initiated = TRUE; // so msgbuf is printed below
  } else if (strcmp(cmd, "exit") == 0) {
    exit_handler(parms, user_initiated);
  } else if (strcmp(cmd, "halt") == 0) {
    halt_handler(user_initiated);
  } else if (strcmp(cmd, "noop") == 0) {
    sprintf(msgbuf, "Finished processing of user signal \"noop\".");
  } else {
    sprintf(msgbuf, "Error: User signal command \"%s\" not recognized.", cmd);
  } 
  if (user_initiated && *msgbuf) msg_print("%s", msgbuf);
}

#if SEPARATE_LICENSE_PROCESS

// Only returns if license process already issued an error message and status.
VOID cp_wait_on_license_process()
{
  pid_t license_pid = license_child_pid;

  if ((license_pid != (pid_t) -1) && !we_killed_our_children) {
    int stat;
    
    if (waitpid(license_pid, &stat, WNOHANG) == license_pid) {
      license_child_pid = -1;
    }
  }
}
#endif

// This handler only handles the license manager and the decomposer.
// Waiting for anything else causes errors in TPI and SRI.
static void child_status_handler(int signal)
{
#if SEPARATE_LICENSE_PROCESS
  cp_wait_on_license_process();
#endif

  pid_t decomp_pid = decomposer_child_pid;

  if ((decomp_pid != (pid_t) -1) && !we_killed_our_children) {
    /* If there's a decomposer pid to think about... */
    int decomp_stat;

    if ((waitpid(decomp_pid, &decomp_stat, WNOHANG) == decomp_pid) &&
	((WEXITSTATUS(decomp_stat) != 0) || WIFSIGNALED(decomp_stat))) {
				/* If we got something associated with the decomposer
				 * process, and it's either a bad status or an indication
				 * that the process died due to a signal... */
      cp_jobctl_terminate("Decomposer died", exit_failure_code());
    }
  }
}

static VOID install_signal_handlers(VOID)
{
  jobctl_server_sigset(JOBCTL_CHLD_SIGNAL,	child_status_handler);

  jobctl_server_sigset_die_signals(terminate_immediately_handler_exit_failure);
  jobctl_server_sigset(JOBCTL_INT_SIGNAL,     	terminate_immediately_handler_exit_failure);
  jobctl_server_sigset(JOBCTL_ABRT_SIGNAL,     	terminate_immediately_handler_exit_failure);

  // Some job queuing systems react poorly to SIGUSR1, so we use SIGWINCH instead
  // The SIGWINCH handler must be conditionally installed to avoid killing the simulator when
  // changing windows or tabs. See PRs 46933 / 45778
  jobctl_server_sigset(SIGUSR1, force_exit_handler); // exasignal --force_exit

  const char* is_queued_job = getenv("EXA_IS_QUEUED_JOB");
  if (is_queued_job &&
      ((strcmp(is_queued_job, "yes") == 0) ||
       (strcmp(is_queued_job, "YES") == 0))
      ){
    jobctl_server_sigset(SIGWINCH, force_exit_handler); // exasignal --force_exit
  } else {
    jobctl_server_sigset(SIGWINCH, SIG_IGN);
  }  
  
  // We don't want to take a SIGPIPE if the license process goes down and we attempt
  // to write the license process socket
  jobctl_server_sigset(SIGPIPE, SIG_IGN);
}

/*--------------------------------------------------------------------------*
 * Initializes the job control system
 *--------------------------------------------------------------------------*/
VOID cp_jobctl_initialize(cSTRING jobctl_filename, BOOLEAN write_jobctl_file_p)
{
  jobctl_server_start();
  atexit(jobctl_server_end);
  jobctl_server_set_program("Simulator", "", 0.0, 0.0);

  /* Initialize */
  cp_info.cp_status = SIM_STATUS_INIT;
  cp_jobctl_output_current_status();
  
  /* Install signal handlers */
  install_signal_handlers();
}

//Samples if any phases have completed.
void cCOUPLING_PHASES_STATS::sample(bool sim_is_done) { 

  // Do nothing if the conduction solver is not needed.
  if (!cp_info.is_conduction)
    return;
  
  // Do nothing if there are not multiple phases.
  if(cp_info.time_coupling_phases.size() == 1)
    return;
    
  TIMESTEP current_basetime = cp_info.time;
  bool phase_has_ended = false;
    
  //It's possible that the CPs notion of the timestep has progressed
  //past multiple coupling phases.  We can't accuratly measure the
  //duration of each coupling phase from the CP when that happens. We
  //have to loop over each phase anyways to get to the currently
  //active one.
    
  do {
    // Get the start time for the next phase if there is one.
    std::optional<TIMESTEP> next_phase_start_basetime;
    if(m_next_phase < cp_info.time_coupling_phases.size())
      next_phase_start_basetime = cp_info.time_coupling_phases[m_next_phase].start;
      
    // If there is a next phase, check if the CPs notion of the current basetime is after the start time of the next phase.
    phase_has_ended = next_phase_start_basetime.has_value() && current_basetime >= next_phase_start_basetime.value(); // The last phase never ends.
      
    // Only do something at the end of the simulation or when the current coupling phase has ended.
    if( ! (sim_is_done || phase_has_ended) )
      break;
      
    // Measure the wallclock time since the previous phase ended.
    WALLCLOCK_TIME_SECS current_time_usecs = wallclock_time();
    double phase_duration_hours = WALLCLOCK_TIME_DIFF(current_time_usecs, m_phase_start_time_usecs) / (double)WALLCLOCK_TICKS_PER_SEC / 3600.0;
    m_phase_start_time_usecs = current_time_usecs;
    
    // Collect info about the current phase.
    int idx_phase = m_next_phase - 1;
    eTIME_COUPLING_SCHEME::Enum scheme = cp_info.time_coupling_phases[idx_phase].time_coupling;
    eCOUPLED_SOLVER::Enum frozen_solver = cp_info.time_coupling_phases[idx_phase].frozen_solver;
    std::string phase_description = eTIME_COUPLING_SCHEME::GetName(scheme, frozen_solver);
    //replace spaces with dashes so space can be reserved for a field seperator (users may want to use awk with the sim.o output).
    std::replace(phase_description.begin(), phase_description.end(), ' ', '-');
    
    // If the simulation is done, or there is no next phase, use the CP's current notion of basestep as the last step in
    // the phase, otherwise use the start of the next phase's basetime as the end of this phase. 
    TIMESTEP num_basesteps_in_phase, num_flowsteps_in_phase, num_condsteps_in_phase;
    if (sim_is_done || !next_phase_start_basetime.has_value()) {
      num_basesteps_in_phase = current_basetime - cp_info.time_coupling_phases[idx_phase].start;
      num_flowsteps_in_phase = cp_info.convert_to_ts_flow(current_basetime) - cp_info.sri_solver_phases[idx_phase].start_time_tsflow;
      num_condsteps_in_phase = cp_info.convert_to_ts_cond(current_basetime) - cp_info.sri_solver_phases[idx_phase].start_time_tscond;
    } else {
      num_basesteps_in_phase = next_phase_start_basetime.value() - cp_info.time_coupling_phases[idx_phase].start;
      num_flowsteps_in_phase = cp_info.time_coupling_phases[idx_phase].num_timesteps_flow;
      num_condsteps_in_phase = cp_info.time_coupling_phases[idx_phase].num_timesteps_conduction;
    }

    // Compute the amount of physical time covered by each solver during this phase.
    double flow_physical_duration = num_flowsteps_in_phase * cp_info.n_lb_base_steps * cdi_data.seconds_per_timestep;
    double cond_physical_duration = num_condsteps_in_phase * cp_info.n_conduction_base_steps * cdi_data.seconds_per_timestep;
    
    // Write the new timing info to the sim.o without linewrap
    if(sim_args.report_coupling_phase_basetime) {
      msg_print_nf("Completed phase %d (%s), %d flow timesteps, %d conduction timesteps, %d base timesteps, %f hours (wallclock)",
                   m_next_phase - 1,
                   phase_description.c_str(),
                   num_flowsteps_in_phase,
                   num_condsteps_in_phase,
                   num_basesteps_in_phase,
                   phase_duration_hours);
    } else {
      msg_print_nf("Completed phase %d (%s), %d flow timesteps, %d conduction timesteps, %f hours (wallclock)",
                   m_next_phase - 1,
                   phase_description.c_str(),
                   num_flowsteps_in_phase,
                   num_condsteps_in_phase,
                   phase_duration_hours);
    }
    
    // Aggregate the data for each unique type of coupling phase.
    sPHASE_SIGNATURE phase_signature(scheme, frozen_solver);
    sPHASE_STATS phase_stats(num_flowsteps_in_phase,
                             num_condsteps_in_phase,
                             num_basesteps_in_phase,
                             phase_duration_hours,
                             flow_physical_duration,
                             cond_physical_duration);
    if(m_aggregated_phase_stats.find(phase_signature) ==  m_aggregated_phase_stats.end()) {
      m_aggregated_phase_stats[phase_signature] = phase_stats;
    } else {
      m_aggregated_phase_stats[phase_signature] += phase_stats;
    }
      
    if(phase_has_ended)
      m_next_phase++;
      
  } while(phase_has_ended && !sim_is_done);
}
  
void cCOUPLING_PHASES_STATS::print(){
  cTEXT_TABLE phase_table("Thermal Solver Coupling Phases Timing Summary", //table title
                          {"            Phase Type            ",
                           " Flow Timesteps",
                           " Conduction Timesteps",
                           " Physical Flow Time (s)",
                           " Physical Conduction Time (s)",
                           " Wallclock Time (hour)"}, //column labels (also dictates field width)
                          {cTEXT_TABLE::eVAR_TYPE::STRING,
                           cTEXT_TABLE::eVAR_TYPE::INT,
                           cTEXT_TABLE::eVAR_TYPE::INT,
                           cTEXT_TABLE::eVAR_TYPE::FLOAT,
                           cTEXT_TABLE::eVAR_TYPE::FLOAT,
                           cTEXT_TABLE::eVAR_TYPE::FLOAT
                          }); //field data types

  if(sim_args.report_coupling_phase_basetime) {
    phase_table.add_column("Total Timesteps (basetime)", cTEXT_TABLE::eVAR_TYPE::INT);
  }
  
 for(const auto &phase_data : m_aggregated_phase_stats) {
   std::string phase_description(phase_data.first.description());
   std::replace(phase_description.begin(), phase_description.end(), ' ', '-'); //replace spaces with dashes so spaces can be reserved for a field seperator.
   if(sim_args.report_coupling_phase_basetime) {
   phase_table.print_record(7,
                            phase_description.c_str(),
                            phase_data.second.m_num_flowsteps,
                            phase_data.second.m_num_condsteps,
                            phase_data.second.m_flow_physical_duration,
                            phase_data.second.m_cond_physical_duration,
                            phase_data.second.m_wallclock_duration,
                            phase_data.second.m_num_basesteps);
   } else {
     phase_table.print_record(6,
                              phase_description.c_str(),
                              phase_data.second.m_num_flowsteps,
                              phase_data.second.m_num_condsteps,
                              phase_data.second.m_flow_physical_duration,
                              phase_data.second.m_cond_physical_duration,
                              phase_data.second.m_wallclock_duration);
   }

 }
}

