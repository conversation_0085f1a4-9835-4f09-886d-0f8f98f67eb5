/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#include MSGERR_H
#include "pri_support.h"
#include PLATFORM_H

//--------Emitter Configurations-------

//Instantiate templates that write from any kind of CDI emitter config to fields that exist in a PRI::cBASE_EMITTER_CONFIGURATION
template VOID set_pri_base_emitter_config_data(PRI::cBASE_EMITTER_CONFIGURATION& pri_emitter_config,
                                               const sCDI_NOZZLE_EMITTER_CONFIG* cdi_emitter_config);
template VOID set_pri_base_emitter_config_data(PRI::cBASE_EMITTER_CONFIGURATION& pri_emitter_config,
                                               const sCDI_RAIN_EMITTER_CONFIG* cdi_emitter_config);
template VOID set_pri_base_emitter_config_data(PRI::cBASE_EMITTER_CONFIGURATION& pri_emitter_config,
                                               const sCDI_TIRE_EMITTER_CONFIG* cdi_emitter_config);

//Instantiate a template to set fields in a PRI::cRAIN_EMITTER_CONFIGURATION which are common to either a CDI nozzle emitter config or a CDI rain emitter config.
template VOID set_pri_rain_emitter_config_data<sCDI_RAIN_EMITTER_CONFIG>(PRI::cRAIN_EMITTER_CONFIGURATION& pri_emitter_config,
                                                                         const sCDI_RAIN_EMITTER_CONFIG* cdi_emitter_config);
template VOID set_pri_rain_emitter_config_data<sCDI_NOZZLE_EMITTER_CONFIG>(PRI::cRAIN_EMITTER_CONFIGURATION& pri_emitter_config,
                                                                           const sCDI_NOZZLE_EMITTER_CONFIG* cdi_emitter_config);


VOID set_pri_nozzle_angle_dist_params(PRI::cNOZZLE_EMITTER_CONFIGURATION& pri_emitter_config,
                                      const CDI_DISTRIBUTION_TYPE dist_type, const sCDI_PARM &p1, const sCDI_PARM &p2);

//Set PRI nozzle emitter configuration fields from a CDI nozzle emitter configuration.
VOID set_pri_nozzle_emitter_config_data(sCDI_INFO &cdi_info, PRI::cNOZZLE_EMITTER_CONFIGURATION& pri_emitter_config,
                                        const sCDI_NOZZLE_EMITTER_CONFIG* cdi_nozzle_config)
{

  sCDI_NOZZLE_EMITTER_CONFIG::CDI_EMITTER_NOZZLE_TYPE cdi_nozzle_type =
    (sCDI_NOZZLE_EMITTER_CONFIG::CDI_EMITTER_NOZZLE_TYPE)(int)cdi_nozzle_config->nozzle_type.value;
  pri_emitter_config.SetType(cdi_to_pri_nozzle_type(cdi_nozzle_type));

  CDI_DISTRIBUTION_TYPE angle_distribution = (CDI_DISTRIBUTION_TYPE)(int)cdi_nozzle_config->angle_distribution.value;
  pri_emitter_config.SetSprayPatternDistribution(cdi_to_pri_distribution_type(angle_distribution));


  sCDI_PARM null_parameter;

  PRI::cCOMPOUND_FLOAT default_outer_limit((float)M_PI);
  PRI::cCOMPOUND_FLOAT default_inner_limit((float)0.0);
  pri_emitter_config.SetOuterHalfAngleLimit(default_outer_limit);
  pri_emitter_config.SetInnerHalfAngleLimit(default_inner_limit);

  switch(cdi_nozzle_type) {
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_NONE:
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_FULL_CONE:
    set_pri_nozzle_angle_dist_params(pri_emitter_config, angle_distribution, null_parameter, cdi_nozzle_config->cone_half_angle);
    pri_emitter_config.SetOuterHalfAngleLimit(cdi_nozzle_config->cone_half_angle); //last fix for PR37835?
    if (angle_distribution == DISTRIBUTION_GAUSSIAN) 
      if(CDI_INFO_VERSION_AT_LEAST(&cdi_info, 4, 16)) 
        // If CDI v4.19 or greater, do something with cdi_nozzle_config->outer_half_angle_limit.value
        pri_emitter_config.SetOuterHalfAngleLimit(cdi_nozzle_config->outer_half_angle_limit);
    break;
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_HOLLOW_CONE:
    set_pri_nozzle_angle_dist_params(pri_emitter_config, angle_distribution, cdi_nozzle_config->mean_angle, cdi_nozzle_config->angle_range);
    if (angle_distribution == DISTRIBUTION_GAUSSIAN) 
      if(CDI_INFO_VERSION_AT_LEAST(&cdi_info, 4, 16)) { 
        // If CDI v4.19 or greater, do something with cdi_nozzle_config->inner_half_angle_limit and
        // with cdi_nozzle_config->outer_half_angle_limit.
        pri_emitter_config.SetOuterHalfAngleLimit(cdi_nozzle_config->outer_half_angle_limit);
        pri_emitter_config.SetInnerHalfAngleLimit(cdi_nozzle_config->inner_half_angle_limit);
      }
    break;
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_ELLIPTICAL_CONE:
    set_pri_nozzle_angle_dist_params(pri_emitter_config, angle_distribution, null_parameter, cdi_nozzle_config->cone_half_angle);
    pri_emitter_config.SetMajorHalfAngle(cdi_nozzle_config->major_half_angle);
    pri_emitter_config.SetMinorHalfAngle(cdi_nozzle_config->minor_half_angle);
    if (angle_distribution == DISTRIBUTION_GAUSSIAN) {
      if(CDI_INFO_VERSION_AT_LEAST(&cdi_info, 4, 16)) { 
        // If CDI v4.19 or greater, do something with cdi_nozzle_config->major_outer_half_angle_limit and
        // with cdi_nozzle_config->minor_outer_half_angle_limit.
        pri_emitter_config.SetMajorHalfAngleLimit(cdi_nozzle_config->major_outer_half_angle_limit);
        pri_emitter_config.SetMinorHalfAngleLimit(cdi_nozzle_config->minor_outer_half_angle_limit);
      }
    }
    break;
  default:
    msg_error("Unknown nozzle angle distribution encountered.");
  }

  CDI_DISTRIBUTION_TYPE velocity_distribution = (CDI_DISTRIBUTION_TYPE)(int)cdi_nozzle_config->velocity_info.GetDistributionType().value;
  pri_emitter_config.SetVelocityMagnitudeDistribution(cdi_to_pri_distribution_type(velocity_distribution));

  sCDI_PARM p1;
  sCDI_PARM p2;
  cdi_nozzle_config->velocity_info.GetParameterValues(&p1, &p2);
  pri_emitter_config.SetVelocityMagnitudeDistributionParameter1(p1);
  pri_emitter_config.SetVelocityMagnitudeDistributionParameter2(p2);
 
  switch(velocity_distribution) {
  case DISTRIBUTION_UNIFORM:
  case DISTRIBUTION_GAUSSIAN:
  case DISTRIBUTION_NONE:
    break;
  default: msg_error("Unsupported velocity distribution encountered in emitter configuration.");
  }

}

//This is a helper function used in several of the nozzle type switch cases in the above function.
VOID set_pri_nozzle_angle_dist_params(PRI::cNOZZLE_EMITTER_CONFIGURATION& pri_emitter_config, CDI_DISTRIBUTION_TYPE dist_type, const sCDI_PARM &p1, const sCDI_PARM &p2) {
  pri_emitter_config.SetSprayPatternDistributionParameter1(p1);
  pri_emitter_config.SetSprayPatternDistributionParameter2(p2);
  switch(dist_type) {
  case DISTRIBUTION_UNIFORM:
  case DISTRIBUTION_GAUSSIAN:
  case DISTRIBUTION_NONE:
    break;
  default: msg_error("Unsupported angle distribution encountered in emitter configuration.");
  }
}

VOID set_pri_tire_emitter_config_data(PRI::cTIRE_EMITTER_CONFIGURATION &pri_emitter_config, 
                                      std::vector<PRI::cNOZZLE_PROPERTIES> &pri_arc_station_properties,
                                      std::vector<PRI::cANGLE_NOZZLE_PROP_MAP> &pri_nozzle_station_map,
                                      const sCDI_TIRE_EMITTER_CONFIG* cdi_tire_config )
{

  //note: The following are specific to a PRI tire emitter configuration:
  //void SetNozzleType(NOZZLE_TYPE nozzleType);
  //void SetParticleDiameterDistribution(DISTRIBUTION_TYPE particleDiameterDistribution);
  //void SetAngleDistribution(DISTRIBUTION_TYPE angleDistribution);
  //void SetVelocityMagnitudeDistribution(DISTRIBUTION_TYPE velocityMagnitudeDistribution);
  //void SetSpatialEmissionDistribution(DISTRIBUTION_TYPE spatialEmissionDistribution);
  //void SetSpatialEmissionRangeStdDev(float spatialEmissionRangeStdDev);
  //void SetSpatialEmissionMean(float spatialEmissionMean);
  //void SetSpatialEmissionExponent(float spatialEmissionExponent);
  //void SetEmissionRateRatio(float emissionRateRatio);
  //void SetNozzlePropertyMapIndices(VECTOR2_INDEX nozzlePropertyMapIndices);

  const std::vector<sCDI_TIRE_NOZZLE_PROPS>& arc_station_properties = cdi_tire_config->GetTireNozzles();

  pri_emitter_config.SetNozzleType(PRI::NOZZLE_TYPE_FULL_CONE);    //PowerCASE only allows full cone.
 
  CDI_DISTRIBUTION_TYPE diameter_distribution = (CDI_DISTRIBUTION_TYPE)(int)(arc_station_properties[0]).particle_diam_info.GetDistributionType().value;
  pri_emitter_config.SetParticleDiameterDistribution(cdi_to_pri_distribution_type(diameter_distribution));

  CDI_DISTRIBUTION_TYPE velocity_distribution = (CDI_DISTRIBUTION_TYPE)(int)(arc_station_properties[0]).velocity_info.GetDistributionType().value;
  pri_emitter_config.SetVelocityMagnitudeDistribution(cdi_to_pri_distribution_type(velocity_distribution));

  CDI_DISTRIBUTION_TYPE spatial_emission_distribution = (CDI_DISTRIBUTION_TYPE)(int)cdi_tire_config->GetSpatialEmissionDistributionType().value;
  pri_emitter_config.SetSpatialEmissionDistribution(cdi_to_pri_distribution_type(spatial_emission_distribution));

  sCDI_PARM p1;
  sCDI_PARM zero;
  p1 = cdi_tire_config->GetSpatialEmissionDistributionParameter();
  pri_emitter_config.SetSpatialEmissionDistributionParameter1(zero.value);

  switch(spatial_emission_distribution) {
  case DISTRIBUTION_GAUSSIAN: 
     pri_emitter_config.SetSpatialEmissionDistributionParameter2(p1.value);
     break;
  case DISTRIBUTION_LINEAR: 
    pri_emitter_config.SetEmissionRateRatio(cdi_tire_config->emission_rate_ratio.value);
    break;
  case DISTRIBUTION_1MX_POW_N: 
  case DISTRIBUTION_HALF_COSINE: 
    pri_emitter_config.SetSpatialEmissionDistributionParameter1(p1.value); //PRI doesnt have a specific method for the cosine distribution parameters.
    break;
  default:
    msg_error("Unsupported spatial emission distribution encountered in tire emitter configuration.");
  }
 
 
  //Now create a bunch of cNOZZLE_PROPERTIES and cANGLE_NOZZLE_PROP_MAPs and record the index range.
  CDI_DISTRIBUTION_TYPE global_angle_distribution = (CDI_DISTRIBUTION_TYPE)-1;
  size_t first_nozzle_prop = pri_nozzle_station_map.size();
  ccDOTIMES(arc_station_index, arc_station_properties.size()) {
    PRI::cNOZZLE_PROPERTIES station_properties;
    PRI::cANGLE_NOZZLE_PROP_MAP station_map_element;
    CDI_DISTRIBUTION_TYPE angle_distribution = arc_station_properties[arc_station_index].ConvertToPRI(station_properties, station_map_element);
    if(arc_station_index == 0) {
      global_angle_distribution = angle_distribution;
    } else {
      if(global_angle_distribution != angle_distribution)
        msg_error("Multiple offset angle distribution types encountered in a tire emitters nozzle properties.");
    }

    pri_arc_station_properties.push_back(station_properties);
    station_map_element.SetNozzlePropertiesIndex(first_nozzle_prop + arc_station_index);
    pri_nozzle_station_map.push_back(station_map_element);
  }
  size_t last_nozzle_prop = pri_nozzle_station_map.size();
  
  pri_emitter_config.SetNozzlePropertyMapIndices(PRI::VECTOR2_INDEX(first_nozzle_prop, last_nozzle_prop));

  //CDI_DISTRIBUTION_TYPE angle_distribution = (CDI_DISTRIBUTION_TYPE)(int)arc_station_properties[0]->angle_distribution.value; //PowerCASE only allows DISTRIBUTION_UNIFORM
  pri_emitter_config.SetOffsetAngleDistribution(cdi_to_pri_distribution_type(global_angle_distribution));



}

//--------Emitters---------

//------Write pri base-emitter specific data from cdi base emiters--------
VOID set_pri_base_emitter_data(PRI::cBASE_EMITTER& pri_emitter, const sCDI_PARTICLE_EMITTER_BASE& cdi_emitter) {

  pri_emitter.SetName(cdi_emitter.name);
  //pri_emitter.SetEmitterGeometryIndices(0); //This is in PRI emitter's base classes but can't be set from a CDI emitter base.
  pri_emitter.SetSubjectToDispersionBox(cdi_emitter.subject_to_dispersion_box.value != 0.0);
  pri_emitter.SetStartTime(cdi_emitter.start);
  pri_emitter.SetEndTime(cdi_emitter.end);
  pri_emitter.SetMaxParticleAge(cdi_emitter.max_age);
  pri_emitter.SetMaxNumReflections((size_t)cdi_emitter.max_num_reflections.value);
  pri_emitter.SetMinParticleVelocity(cdi_emitter.min_particle_velocity);
  pri_emitter.SetVisible(cdi_emitter.visible.value != 0.0);

}

//--------Write pri directed-emitter specific data from cdi surface, volume, or point emitters--------
template VOID set_pri_directed_emitter_data<sCDI_SURFACE_EMITTER>(PRI::cDIRECTED_EMITTER& pri_emitter, const sCDI_SURFACE_EMITTER& cdi_emitter );
template VOID set_pri_directed_emitter_data<sCDI_VOLUME_EMITTER>(PRI::cDIRECTED_EMITTER& pri_emitter, const sCDI_VOLUME_EMITTER& cdi_emitter);
template VOID set_pri_directed_emitter_data<sCDI_POINT_EMITTER>(PRI::cDIRECTED_EMITTER& pri_emitter, const sCDI_POINT_EMITTER& cdi_emitter);


//--------Write pri geometry-emitter specific data from cdi volume or surface emitters-------------
template VOID set_pri_geometry_emitter_data<sCDI_VOLUME_EMITTER>(PRI::cGEOMETRY_EMITTER& pri_emitter, const sCDI_VOLUME_EMITTER& cdi_emitter);
template VOID set_pri_geometry_emitter_data<sCDI_SURFACE_EMITTER>(PRI::cGEOMETRY_EMITTER& pri_emitter, const sCDI_SURFACE_EMITTER& cdi_emitter);


//----------Write pri volume-emitter specific values from a cdi volume emitter----------
VOID set_pri_volume_emitter_data(PRI::cVOLUME_EMITTER& pri_emitter, const sCDI_VOLUME_EMITTER& cdi_emitter) {
}

//----------Write pri surface-emitter specific values from a cdi surface emitter----------
VOID set_pri_surface_emitter_data(PRI::cSURFACE_EMITTER& pri_emitter, const sCDI_SURFACE_EMITTER& cdi_emitter) {
  pri_emitter.SetMeanEmissionDirectionVia(cdi_emitter.user_specified_nozzle_orientation.value !=0 ?
                                          PRI::EMISSION_DIRECTION_VIA_CUSTOMIZE : PRI::EMISSION_DIRECTION_VIA_SURFACE_NORMAL);
}

//----------Write pri point-emitter specific from a cdi point emitter----------
VOID set_pri_point_emitter_data(PRI::cPOINT_EMITTER& pri_emitter, const sCDI_POINT_EMITTER& cdi_emitter) {
  //set the points csys and the point emitter preffered units.
  pri_emitter.SetCoordinateSystemIndex(cdi_csys_to_pri_csys((size_t)cdi_emitter.preferred_csys_index));
  //pri_emitter.SetPointsUnit(cdi_emitter.preferred_units); //waiting for PR39038.
  pri_emitter.SetPointsUnit("m");                           //waiting for PR39038 before removal.
}

//----------Write pri rain-emitter specific from a cdi rain emitter----------
VOID set_pri_rain_emitter_data(PRI::cRAIN_EMITTER& pri_emitter, const sCDI_RAIN_EMITTER& cdi_emitter) {
  pri_emitter.SetEmitterConfigurationIndex((size_t)cdi_emitter.emitter_configuration.value); //Added for fix to PR40128/PR40179.
}

//----------Write fields specific to a pri tire-emitter from a cdi tire emitter----------
VOID set_pri_tire_emitter_data(PRI::cTIRE_EMITTER& pri_emitter, const sCDI_TIRE_EMITTER& cdi_emitter) {

  pri_emitter.SetEmitterConfigurationIndex((size_t)cdi_emitter.emitter_configuration.value); //Added for fix to PR40128/PR40179.
  pri_emitter.SetZeroAngleDirectionCoordSysIndex(cdi_csys_to_pri_csys((size_t)cdi_emitter.zero_angle_direction_csys.value));
  pri_emitter.SetZeroAngleDirection(cdi_to_pri_direction(cdi_emitter.zero_angle_direction));
  pri_emitter.SetTreadOffset(PRI::cCOMPOUND_FLOAT(5.0,"mm","")); //Dummy value until corresponding info is added to the cdi
  pri_emitter.SetApproxRotationAxisDirection(cdi_to_pri_direction(cdi_emitter.approx_rotation_axis_dir));
  pri_emitter.SetTireTreadShow(cdi_emitter.tire_tread_show.value != 0);
  pri_emitter.SetTireTreadLook(cdi_emitter.tire_tread_look);

  pri_emitter.SetNozzleShow(cdi_emitter.nozzle_visibility_settings.nozzle_show.value != 0.0);
  pri_emitter.SetNozzleConeLook(cdi_emitter.nozzle_visibility_settings.nozzle_cone_look);
  pri_emitter.SetNozzleBodyColor(cdi_emitter.nozzle_visibility_settings.nozzle_body_color);
  pri_emitter.SetNozzleArrowLook(cdi_emitter.nozzle_visibility_settings.nozzle_arrow_look);
  pri_emitter.SetNozzleSize(cdi_emitter.nozzle_visibility_settings.nozzle_size);

  pri_emitter.SetEmissionBoundaryShow(cdi_emitter.emission_boundary_visibility_settings.emission_boundary_show.value != 0.0);
  pri_emitter.SetEmissionBoundaryLook(cdi_emitter.emission_boundary_visibility_settings.emission_boundary_look);
  pri_emitter.SetEmissionBoundarySize(cdi_emitter.emission_boundary_visibility_settings.emission_boundary_size);
}

//------Collection of functions to convert between various PRI and CDI enumerated types---------------
PRI::DISTRIBUTION_TYPE cdi_to_pri_distribution_type(CDI_DISTRIBUTION_TYPE cdi_distribution_type)
{
  switch(cdi_distribution_type) {

  case DISTRIBUTION_UNIFORM:
    return PRI::DISTRIBUTION_TYPE_UNIFORM;

  case DISTRIBUTION_GAUSSIAN:
    return PRI::DISTRIBUTION_TYPE_GAUSSIAN;

  case DISTRIBUTION_GAMMA:
    return PRI::DISTRIBUTION_TYPE_GAMMA;

  case DISTRIBUTION_NONE:
    return PRI::DISTRIBUTION_TYPE_CONSTANT;

  case DISTRIBUTION_ROSIN_RAMMLER:
    return PRI::DISTRIBUTION_TYPE_ROSIN_RAMMLER;

  case DISTRIBUTION_LOG_NORMAL:
    return PRI::DISTRIBUTION_TYPE_LOG_NORMAL;

  case DISTRIBUTION_LINEAR:
    return PRI::DISTRIBUTION_TYPE_LINEAR;

  case DISTRIBUTION_1MX_POW_N:      // (1-x)^n
    return PRI::DISTRIBUTION_TYPE_EXPONENTIAL;

  case DISTRIBUTION_HALF_COSINE:
    return PRI::DISTRIBUTION_TYPE_HALF_COSINE;

  case DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION://PR_48032
	return PRI::DISTRIBUTION_TYPE_ROSIN_RAMMLER_VOLUME_FRACTION;
  }
  return PRI::DISTRIBUTION_TYPE_UNDEFINED;
}

PRI::DIRECTION cdi_to_pri_direction(const std::vector<sCDI_PARM>& cdi_direction) {
  if(cdi_direction[0].value > 0)
    return PRI::DIRECTION_X_POS;

  if(cdi_direction[0].value < 0)
    return PRI::DIRECTION_X_NEG;

  if(cdi_direction[1].value > 0)
    return PRI::DIRECTION_Y_POS;

  if(cdi_direction[1].value < 0)
    return PRI::DIRECTION_Y_NEG;

  if(cdi_direction[2].value > 0)
    return PRI::DIRECTION_Z_POS;

  if(cdi_direction[2].value < 0)
    return PRI::DIRECTION_Z_NEG;

  return PRI::DIRECTION_UNDEFINED;
}

PRI::PARTICLE_TYPE cdi_to_pri_material_type(CDI_PARTICLE_MATERIAL_TYPE cdi_material_type) {
  switch(cdi_material_type) {
  case PARTICLE_MATERIAL_LIQUID:
    return PRI::PARTICLE_TYPE_LIQUID;
  case PARTICLE_MATERIAL_SOLID:
    return PRI::PARTICLE_TYPE_SOLID;
  default:
    return static_cast<PRI::PARTICLE_TYPE>(-1);
  }
  return (PRI::PARTICLE_TYPE)-1; //Maybe add a PARTICLE_TYPE_INVALID enum.
}

PRI::EMISSION_RATE_VIA cdi_to_pri_emission_rate_type(CDI_EMISSION_RATE_TYPE cdi_emission_rate_type)
{
  switch(cdi_emission_rate_type){

  case EMISSION_RATE_NONE:
    return(PRI::EMISSION_RATE_VIA_UNDEFINED);
  case EMISSION_RATE_MASSFLOW:
    return(PRI::EMISSION_RATE_VIA_MASS_FLOW);
  case EMISSION_RATE_PARTICLE:
    return(PRI::EMISSION_RATE_VIA_PARTICLE);
  case EMISSION_RATE_VOLUMETRIC:
    return(PRI::EMISSION_RATE_VIA_VOLUMETRIC_FLOW);
  case EMISSION_RATE_DEPTH:
    return(PRI::EMISSION_RATE_VIA_DEPTH);
  case EMISSION_RATE_MASSFLOW_PER_UNIT_VOLUME: // mass flow per unit volume seems to not be supported by PRI
  case EMISSION_RATE_LWC:                      // Ditto for Liquid Water Content (RIME Icing)
  default:
    return(PRI::EMISSION_RATE_VIA_UNDEFINED);
  }
  return(PRI::EMISSION_RATE_VIA_UNDEFINED);
}


PRI::NOZZLE_TYPE cdi_to_pri_nozzle_type(sCDI_NOZZLE_EMITTER_CONFIG::CDI_EMITTER_NOZZLE_TYPE cdi_nozzle_type)
{
  switch(cdi_nozzle_type) {
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_NONE:
    return(PRI::NOZZLE_TYPE_UNDEFINED);
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_FULL_CONE:
    return(PRI::NOZZLE_TYPE_FULL_CONE);
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_HOLLOW_CONE:
    return(PRI::NOZZLE_TYPE_HOLLOW_CONE);
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_ELLIPTICAL_CONE:
    return(PRI::NOZZLE_TYPE_ELLIPTICAL_CONE);
  default:
    return(PRI::NOZZLE_TYPE_UNDEFINED);
  }
  return(PRI::NOZZLE_TYPE_UNDEFINED);
}

