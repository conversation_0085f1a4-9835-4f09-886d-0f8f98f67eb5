/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#ifndef _CDI_TEMPDEPPARMS_H
#define _CDI_TEMPDEPPARMS_H

#include SCALAR_H

// This file is intended for the calculation of temperature-dependent properties for non-air liquid.

enum CDI_LIQUID_MATERIAL {
  CDI_LM_USER_DEFINED,
  CDI_LM_WATER,
  CDI_LM_ETHYLENE_GLYCOL_30,
  CDI_LM_ETHYLENE_GLYCOL_50,
  CDI_LM_FC_77
};

enum CDI_TEMP_DEP_PROP {
  CDI_PROP_VISCOSITY,
  CDI_PROP_PRANDTL_NUMBER
};

typedef struct sCDI_LIQUID_PARAMS
{ /* liquid parameters */
  dFLOAT liq_T_b;
  dFLOAT liq_T_f;
  dFLOAT x1;
  dFLOAT x2;
  dFLOAT x3;
  dFLOAT x4;
  dFLOAT x5;
  dFLOAT liq_v1;
  dFLOAT liq_v2;
  dFLOAT liq_v3;
  dFLOAT liq_v4;
  dFLOAT liq_v5;
  dFLOAT liq_v6;
  dFLOAT liq_v7;
  dFLOAT liq_pr1;
  dFLOAT liq_pr2;
  dFLOAT liq_pr3;
  dFLOAT liq_pr4;
  dFLOAT liq_pr5;
  dFLOAT liq_pr6;
  dFLOAT liq_pr7;
  dFLOAT cx1;
  dFLOAT cx2;
  dFLOAT cx3;
  dFLOAT cx4;
  dFLOAT cx5;

  VOID init(CDI_LIQUID_MATERIAL liquidMaterialType);
} *CDI_LIQUID_PARAMS;

// Helper function to get the boiling temperature of the specified liquid material.
// Return -273.15 if user_defined type or other invalid type has been used as input
dFLOAT cdi_get_boiling_temp_in_degC(CDI_LIQUID_MATERIAL liquidMaterialType);

// Helper function to get the freezing temperature of the specified liquid material.
// Return -273.15 if user_defined type or other invalid type has been used as input
dFLOAT cdi_get_freezing_temp_in_degC(CDI_LIQUID_MATERIAL liquidMaterialType);

// Helper function to get the density (unit in kg/m^3) of the specified liquid material.
// Return 0.0 if user_defined type or other invalid type has been used as input
dFLOAT cdi_get_liquid_material_density(CDI_LIQUID_MATERIAL liquidMaterialType);

// Helper function to get the specific heat (unit in J/(kg * K)) of the specified liquid material.
// Return 0.0 if user_defined type or other invalid type has been used as input
dFLOAT cdi_get_liquid_material_spec_heat(CDI_LIQUID_MATERIAL liquidMaterialType);

// Return -1 if user_defined type or other invalid type has been used as input
dFLOAT cdi_get_temp_dep_prop_value(CDI_LIQUID_MATERIAL liquidMaterialType,
                                   CDI_TEMP_DEP_PROP tempDepPropType,
                                   dFLOAT tempInDegK);

#endif /* _CDI_TEMPDEPPARMS_H */
