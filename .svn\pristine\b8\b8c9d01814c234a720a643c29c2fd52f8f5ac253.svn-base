# ~~~COPYWRITE~~~+ boxcomment("fx1.copyright", "78")
##############################################################################
### Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.       ###
### All Rights Reserved.                                                   ###
### US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                     ###
###        5,640,335; 5,848,260; 5,910,902; 5,953,239;                     ###
###        6,089,744; 7,558,714                                            ###
### UK FR DE Pat 0 538 415                                                 ###
###                                                                        ###
### This computer program is the property of Exa Corporation and contains  ###
### its confidential trade secrets.  Use, examination, copying, transfer   ###
### and disclosure to others, in whole or in part, are prohibited except   ###
### with the express prior written consent of Exa Corporation.             ###
##############################################################################
# ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78")

include $(R)/registry.mak
include $(MAKEINCLUDE_DIR)/$T/makeinclude.mak
include $(SCALAR_DIR)/export.mak
include $(LOOP_DIR)/export.mak
include $(MALLOC_DIR)/export.mak
include $(MSGERR_DIR)/export.mak
include $(DEBUG_DIR)/export.mak
include $(CIO_DIR)/export.mak
include $(UNITS_DIR)/export.mak
include $(VHASH_DIR)/export.mak
include $(EARRAY_DIR)/export.mak
include $(XRAND_DIR)/export.mak
include $(CIPHER_DIR)/export.mak
include $(AUDIT_DIR)/export.mak
include $(PLATFORM_DIR)/export.mak
include $(ESTRING_DIR)/export.mak
include $(GEOM_COMMON_DIR)/export.mak
include $(SRI_DIR)/export.mak    #NOLINKDEPEND  <-- comment used by componentinfo
include $(NETCDF_DIR)/export.mak
include $(PHYSTYPES_DIR)/export.mak
include $(ARG_HELPER_DIR)/export.mak
include $(TRIO_DIR)/export.mak
include $(CCUTILS_DIR)/export.mak
include $(CDI_DIR)/export.mak
include $(HDF5_DIR)/export.mak
include $(PRI_DIR)/export.mak
include $(G1_DIR)/export.mak
include $(G2_DIR)/export.mak
include $(G3_DIR)/export.mak
include $(SIMUTILS_DIR)/export.mak

ifdef MAKEINCLUDE_WINDOWS
  STD_FLAG=-std:c++17
endif

RUNTESTS_TMPDIR = /tmp

GENERATED_D_FLAGS = -DCDI_VERSION=\"$(CDI_VERSION)\"
#USE_C++11 = 1
# Currently Windows builds don't allow C++17 features, which included
# components require, so for now we need to override the C++ standard.
ifdef MAKEINCLUDE_WINDOWS
    STD_FLAG=-std:c++17
endif


# CDI_D = -DDEBUG
D_FLAGS = $(GENERATED_D_FLAGS) $(CDI_D) $(PHYSTYPES_D) $(SCALAR_D) $(LOOP_D) $(MSGERR_D) $(MALLOC_D) $(DEBUG_D) $(EARRAY_D) $(CIPHER_D) $(XRAND_D) $(CIO_D) \
			$(ESTRING_D) $(UNITS_D) $(SIMUTILS_D) $(AUDIT_D) $(CCUTILS_D) $(GEOM_COMMON_D) $(PLATFORM_D) $(SRI_D) $(NETCDF_D) $(ARG_HELPER_D) $(PRI_D) $(HDF5_D) $(G1_D) $(G2_D) $(G3_D)

I_FLAGS = $(HDF5_I)

A_FILES = $(SIMUTILS_A) $(UNITS_A) $(VHASH_A) $(EARRAY_A)  $(CIPHER_A) $(XRAND_A) $(CIO_A) $(AUDIT_A) $(ESTRING_A) \
			$(CCUTILS_A) $(TRIO_A) $(PLATFORM_A) $(MALLOC_A) $(MSGERR_A) $(DEBUG_A) $(SCALAR_A) $(ARG_HELPER_A) $(PRI_A)
LIBS    = $(A_FILES) -lm $(PLATFORM_LINKOPTS) $(HDF5_SO) $(ZLIB_SO)

CC_FILES = cdi_readwrite.cc cdi_interface.cc cdi_get.cc cdi_physics.cc cdi_io.cc cdi_encrypted_io.cc cdi_accessers.cc cdi_tempDepParms.cc undump_cdi.cc dump_cdi.cc dump_cdi_lrf.cc test_cdi.cc pri_support.cc cdi_cPRESSURE_DROP_PARSER.cc cdi_cLSR_QUADRATIC_SOLVER.cc cdi_partitions.cc cCDI_READER.cc cCDI_GEOMETRY_READER.cc cCDI_GEOMETRY_GENERATOR.cc
H_FILES = cdi_common.h $(CDI_H) cdi_readwrite.h cdi_interface.h cdi_get.h cdi_physics.h cdi_encrypted_io.h cdi_accessers.h cdi_tempDepParms.h pri_support.h cdi_cPRESSURE_DROP_PARSER.h cdi_cLSR_QUADRATIC_SOLVER.h cdi_partitions.h cCDI_READER.h cCDI_GEOMETRY_READER.h cCDI_GEOMETRY_GENERATOR.h

SRC_FILES = $(CC_FILES) $(H_FILES) master.mak
SRC_OBJS = cdi_readwrite.o cdi_interface.o cdi_get.o cdi_physics.o cdi_tempDepParms.o test_cdi.o undump_cdi.o dump_cdi.o dump_cdi_lrf.o pri_support.o cdi_cPRESSURE_DROP_PARSER.o cdi_cLSR_QUADRATIC_SOLVER.o cdi_partitions.o cCDI_READER.o cCDI_GEOMETRY_READER.o cCDI_GEOMETRY_GENERATOR.o
OBJS = $(SRC_OBJS) lexer.o dump_cdi_do_not_release.o undump_cdi_do_not_release.o

TARGET_LINK_OPTS += $(TARGET_LINKOPTS)

all:		$(CDI_A) $(TARGET_BUILDS)

cdi_lex.yy.c:	../cdi.l
		$(FLEX) -Pcdi_yy -B --outfile=cdi_lex.yy.c ../cdi.l
		../hack_unistd cdi_lex.yy.c

lexer.o:	cdi_lex.yy.c ../lexer.h ../lexer.cc
		$(COMPILE.cc) -c -DLEX_YY_C=\"$(CDI_DIR)/$T/cdi_lex.yy.c\" ../lexer.cc

dump_cdi_do_not_release.o:	../dump_cdi.cc
		$(COMPILE.cc) -o dump_cdi_do_not_release.o -c -DENCRYPT_OFF ../dump_cdi.cc

undump_cdi_do_not_release.o:	../undump_cdi.cc 
		$(COMPILE.cc) -o undump_cdi_do_not_release.o -c -DENCRYPT_OFF ../undump_cdi.cc

test_lexer:	test_lexer.o lexer.o
		$(LINK.cc) -o test_lexer test_lexer.o lexer.o $(LIBS) $(TARGET_LINK_OPTS)

test_cdi:	test_cdi.o $(CDI_A) $(A_FILES)
		$(LINK.cc) -o test_cdi test_cdi.o $(CDI_A) $(LIBS) $(TARGET_LINK_OPTS)

undump_cdi:	undump_cdi.o $(CDI_A) $(A_FILES) $(TARGET_OBJECTS)
		$(LINK.cc) -o undump_cdi undump_cdi.o $(CDI_A) $(LIBS) $(TARGET_LINK_OPTS) $(TARGET_OBJECTS)

dump_cdi:	dump_cdi.o $(CDI_A) $(A_FILES) $(TARGET_OBJECTS)
		$(LINK.cc) -o dump_cdi dump_cdi.o $(CDI_A) $(LIBS) $(TARGET_LINK_OPTS) $(TARGET_OBJECTS)

dump_cdi_do_not_release:	dump_cdi_do_not_release.o  $(CDI_A) $(A_FILES) $(TARGET_OBJECTS)
		$(LINK.cc) -o dump_cdi_do_not_release dump_cdi_do_not_release.o $(CDI_A) $(LIBS) $(TARGET_LINK_OPTS) $(TARGET_OBJECTS)

undump_cdi_do_not_release:	undump_cdi_do_not_release.o  $(CDI_A) $(A_FILES) $(TARGET_OBJECTS)
		$(LINK.cc) -o undump_cdi_do_not_release undump_cdi_do_not_release.o $(CDI_A) $(LIBS) $(TARGET_LINK_OPTS) $(TARGET_OBJECTS)

dump_cdi_lrf:	dump_cdi_lrf.o  $(CDI_A) $(A_FILES)
		$(LINK.cc) -o dump_cdi_lrf dump_cdi_lrf.o $(CDI_A) $(LIBS) $(TARGET_LINK_OPTS)

test_dump:	./dump_cdi ./undump_cdi
		./undump_cdi $(RUNTESTS_TMPDIR)/sample.cdi.undump <../sample.cdi.ascii
		./dump_cdi $(RUNTESTS_TMPDIR)/sample.cdi.undump \
		  > $(RUNTESTS_TMPDIR)/sample.cdi.undump.dump 
		@ if diff ../sample.cdi.ascii $(RUNTESTS_TMPDIR)/sample.cdi.undump.dump ; then \
		  echo 'PASS: test_dump: undump->dump->undump comparison passed.' ; \
		else \
		  echo 'FAIL: test_dump: undump->dump->undump comparison failed.' ; \
		fi

test:		test_dump
 
quicktest:	test

$(CDI_A):	cdi_readwrite.o cdi_interface.o cdi_physics.o cdi_get.o cdi_io.o cdi_encrypted_io.o cdi_accessers.o cdi_tempDepParms.o pri_support.o cdi_cPRESSURE_DROP_PARSER.o cdi_cLSR_QUADRATIC_SOLVER.o lexer.o cdi_partitions.o cCDI_READER.o cCDI_GEOMETRY_READER.o cCDI_GEOMETRY_GENERATOR.o

clean:
		-$(RM_RF) `ls | egrep -v "Makefile"`

#vsproject_master:
#	echo "COMPONENT: $(COMP)" > $(VSPROJECT_DESC); \
#	echo "PLATFORM: $(T)" >> $(VSPROJECT_DESC); \
#	echo "SRC_FILES: $(filter-out $(H_EXT_FILTER), $(filter-out %.mak, $(SRC_FILES))) $(LIBCPC_UI_SRC)" >> $(VSPROJECT_DESC); \
#	echo "H_FILES: $(filter $(H_EXT_FILTER), $(SRC_FILES))" >> $(VSPROJECT_DESC); \
#	echo "I_FLAGS: $(I_FLAGS) $(filter -I%, $(C_OPT))" >> $(VSPROJECT_DESC); \
#	echo "CC_DBG: $(filter-out -I%, $(CC_DBG))" >> $(VSPROJECT_DESC); \
#	echo "D_FLAGS: $(EXA_YEAR_DFLAG) $(D_FLAGS)" >> $(VSPROJECT_DESC); \
#	echo "CC_LDFLAGS: $(CC_LDFLAGS)" >> $(VSPROJECT_DESC); \
#	echo "CC_DBG_LDFLAGS: $(CC_DBG_LDFLAGS)" >> $(VSPROJECT_DESC); \
#	echo "MOC_OBJS: $(MOC_OBJS)" >> $(VSPROJECT_DESC); \
#	echo "RCC_OBJS: $(RCC_OBJS)" >> $(VSPROJECT_DESC); \
#	echo "SWIG_FILES: $(SWIG_FILES)" >> $(VSPROJECT_DESC); \
#	echo "CLASS_IMP_FILES: $(CLASS_IMP_FILES)" >> $(VSPROJECT_DESC); \
#	echo "TARGET: $(CDI_A) $(SRC_OBJS) $(A_FILES) $(LIBS)" >> $(VSPROJECT_DESC)
#
#vsproject: vsproject_master
#	$(GENERATE_VSPROJECT)


