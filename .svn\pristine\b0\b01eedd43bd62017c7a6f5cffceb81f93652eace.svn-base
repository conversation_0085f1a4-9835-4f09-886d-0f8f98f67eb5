/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Unpublished Work Copyright (C) 1995, 1994 Exa Corporation.            ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129                                                      ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#include "cdi_common.h"
#include "lexer.h"

extern "C" int cdi_yylex (void);
extern "C" char **cdi_yytext_ptr_ptr;

int main (int argc, char *argv[]) {
  int lex_class;

  lexer_set_lineno(1);

  while ((lex_class = cdi_yylex()) != 0) {
    printf("Line %2d: Class = %d (%s)\n", lexer_get_lineno(), lex_class, *cdi_yytext_ptr_ptr);
  }

  return(0);
}
