/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#ifndef _CDI_READER_H_INCLUDED
#define _CDI_READER_H_INCLUDED

#include <map>
#include <string>
#include <utility>

#include "cdi_export.h"

class cCDI_CHUNK_PARSER;


typedef std::map<CIO_CCCC, cCDI_CHUNK_PARSER*> cCDI_CHUNK_PARSER_MAP;
typedef cCDI_CHUNK_PARSER_MAP::value_type cCDI_CHUNK_PARSER_MAP_ENTRY;


// Base class with which you can roll your own parser.
class cCDI_READER {
 public:
  cCDI_READER(cCDI_CHUNK_PARSER_MAP&& parsers);

  virtual ~cCDI_READER();

  // Open and parse the specified CDI file.
  virtual bool Parse(const std::string& cdiFile);

  // Parse at 'ci' within an already open CDI file.
  virtual void Parse(CDI_INFO ci, cCDI_CHUNK_PARSER* parent = nullptr);

  // Called at the end of parsing.
  virtual void PostProcess() {}

 protected:
  cCDI_CHUNK_PARSER_MAP m_parsers;
};


// Base class for chunk parsers.
class cCDI_CHUNK_PARSER {
 public:
  cCDI_CHUNK_PARSER(cCDI_READER* reader)
      : m_reader(reader)
  {
  }

  virtual ~cCDI_CHUNK_PARSER() {}

  virtual bool parse(CDI_INFO ci) { return true; }

  virtual bool parseChild(CDI_INFO ci, CIO_CCCC chunkType) { return true; }

 protected:
  std::string ReadName(CDI_INFO ci);

  cCDI_READER* m_reader;
};


// Standard parser that just descends into a chunk.
class cCDI_DESCEND_PARSER : public cCDI_CHUNK_PARSER {
 public:
  cCDI_DESCEND_PARSER(cCDI_READER* reader)
      : cCDI_CHUNK_PARSER(reader)
  {
  }

  bool parse(CDI_INFO ci) override
  {
    m_reader->Parse(ci);
    return true;
  }
};


// Template for making chunk parsers needed by derived classes.  The
// parse() method will have to be implemented elsewhere.
template <CIO_CCCC CHUNK_TYPE, class READER_TYPE = cCDI_READER>
class tCDI_CHUNK_PARSER : public cCDI_CHUNK_PARSER {
 public:
  tCDI_CHUNK_PARSER(READER_TYPE* reader)
      : cCDI_CHUNK_PARSER(reader)
  {
  }

  READER_TYPE* GetReader() { return dynamic_cast<READER_TYPE*>(m_reader); }

  bool parse(CDI_INFO ci) override;

  static CIO_CCCC ChunkType() { return CHUNK_TYPE; }

  // For eliminating redundant code, when creating parser maps in derived classes.
  static cCDI_CHUNK_PARSER_MAP_ENTRY Create(READER_TYPE* reader)
  {
    return std::make_pair(ChunkType(), new tCDI_CHUNK_PARSER(reader));
  }
};

#endif // _CDI_READER_H_INCLUDED
