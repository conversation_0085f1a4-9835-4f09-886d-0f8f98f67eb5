/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

//----------------------------------------------------------------------------
// David Hall                                             Dec 10, 2008
//----------------------------------------------------------------------------
#include <algorithm>
#include <vector>
#include <fstream>
#include <sstream>
#include <string>

#include SIMUTILS_H
#include CCUTILS_H
#include TIRE_H

#include "cp_cdi_reader.h"
#include "common.h"
#include "cp_stream_manager.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "cp_scalar.h"
#include "jobctl.h"
#include "window.h"
#include "timestep_subcycling.h"
#include "heat_exchangers.h"
#include "seed.h"
#include "exa_sim.h"

#if SURF_COUP
#include "surface_coupling.h"
#include "coupling_model.h"
#endif // SURF_COUP

#include "particle_sim_cp.h"
#include "trajectory_window.h"
#include "write_pm_lgi_records.h"
#include "exa_sim_parse_cp.h"
#include "text_table.h"

#include CDI_H
#include CDI_FIX_PARALLEL_DEV_H

extern sCP_PARTICLE_SIM cp_particle_sim;
extern sCDI_INFO g_cdi_info; //Added to stash a copy of cdi version needed to fix PR41662
                             //(space reserved in trajectory_window.cc).

//Allocate a vector of the names of each region. This is needed when populating pmr file parameters in which pri emitters
//refer by name to their geometry contained in a CDI file.
std::vector<std::string> g_cdi_region_names;

//----------------------------------------------------------------------------
// constructor
//----------------------------------------------------------------------------
sCP_CDI_READER::sCP_CDI_READER()
{
  // Initialize member variables
  memset(this, 0, sizeof(*this)); // only works as long as there isn't a vtable
  m_is_first_pass = TRUE;
  m_skip_eqn_structs = FALSE; 
#if 0
  m_accept_old_turb_cdi   = FALSE;
  m_is_pre_2_1_cdi        = FALSE;
  m_is_pre_2_6_cdi        = FALSE;
#endif
}

//----------------------------------------------------------------------------
// open_file
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::open_file(const char* cdi_filename)
{
  m_filename = cdi_filename;

  asINT32 error_code;
  
  m_cdi_info = cdi_open_for_read(cdi_filename, &error_code);
  if (m_cdi_info == NULL) 
    msg_error("Cannot open CDI file \"%s\": %s\n", cdi_filename, strerror(error_code)); 

  m_num_chunks = cio_get_count(m_cdi_info->cio_info);
  m_chunk_number = 0;

  //A copy of cdi_info is needed later when opening a PMR file (for fix of PR41662 regarding 
  //reverse compatability of cone angle limits).
  memcpy(&g_cdi_info, m_cdi_info, sizeof (sCDI_INFO));

  validate_cdi_version();
}

//----------------------------------------------------------------------------
// reopen_file
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::reopen_file()
{
#if DEBUG
  msg_print("Debug: Rewinding CDI file"); // Actually re-opening, buy why scare people
#endif

  close_file();

  asINT32 error_code;
  m_cdi_info = cdi_open_for_read(m_filename, &error_code);

  if (m_cdi_info == NULL) 
    msg_error("Cannot open CDI file \"%s\": %s\n", m_filename, strerror(error_code)); 

  m_chunk_number = 0;
}

//----------------------------------------------------------------------------
// validate_cdi_version
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::validate_cdi_version()
{
  cdi_data.major_version = m_cdi_info->major_version;
  cdi_data.minor_version = m_cdi_info->minor_version;

  asINT32 major_v = m_cdi_info->major_version;
  asINT32 minor_v = m_cdi_info->minor_version;

  // Reject CDI files that are too new
  if ( (major_v > 9) || ((major_v == 6) && (minor_v > 3))
     ) {
    msg_error("The CDI file \"%s\" uses version %d.%d of the CDI format,"
              " which is not compatible with this version of PowerFLOW.",
              m_filename, major_v, minor_v);
  }

  if ((major_v < 2) || ((major_v <= 3) && (minor_v < 18)))
    m_is_pre_3_18_cdi = TRUE;

  // backdoor for allowing for older cdi versions
  BOOLEAN accept_old_cdi = ( sim_args.read_old_cdi_p ||
                             (getenv("EXA_DISC_READ_OLD_CDI")      != NULL) ||
                             (getenv("EXA_DISC_READ_OLD_TURB_CDI") != NULL) ||
                             (getenv("EXA_READ_OLD_CDI")           != NULL) ||
                             (getenv("EXA_READ_OLD_TURB_CDI")      != NULL));

  m_accept_old_turb_cdi = ((getenv("EXA_DISC_READ_OLD_TURB_CDI") != NULL) || (getenv("EXA_READ_OLD_TURB_CDI") != NULL));

  if (!accept_old_cdi) {
    // range of CDI version numbers restricted to release v3.5 (v3.3 physics)
    if ((major_v == 2) &&
        ((minor_v == 2) ||(minor_v == 3) || (minor_v == 4))) {
      msg_error("The CDI file \"%s\" uses version %d.%d of the CDI format,"
                " which uses a beta physics model not compatible with this"
                " version of PowerFLOW.",
                m_filename, major_v, minor_v);
    }
    BOOLEAN cdi_version_is_ok = (((major_v == 9) && (minor_v <= 19)) ||
                                 ((major_v == 8) && (minor_v <= 14)) ||
                                 ((major_v == 7) && (minor_v <= 32)) ||
                                 ((major_v == 6) && (minor_v <= 3)) ||
                                 ((major_v == 5) && (minor_v <= 7)) ||
                                 ((major_v == 4) && (minor_v <= 22)) || ((major_v == 3) && (minor_v <= 23)) ||
                                 ((major_v == 2) && ((minor_v == 1) ||  ((minor_v >= 5) || (minor_v <= 12)))));

    if (!cdi_version_is_ok) {
      msg_error("The CDI file \"%s\" uses version %d.%d of the CDI format,"
                " which is not compatible with this version of PowerFLOW.",
                m_filename, major_v, minor_v);
    }
    return;
  }

  // Set flags based on cdi version range 
  if ((major_v < 2) || ((major_v == 2) && (minor_v < 1))) 
    m_is_pre_2_1_cdi = TRUE;

  if ((major_v < 2) || ((major_v == 2) && (minor_v < 6))) 
    m_is_pre_2_6_cdi = TRUE;
}


//----------------------------------------------------------------------------
// reread_ptge_after_reopen
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::reread_ptge_after_reopen(CIO_CCCC target_type){

  /* "read_ptge_chunk" could itself cause the file to be reopened if it is not
   * the first chunk read from the CDI file. In that case this method would execute and
   * if we were to read the PTGE chunk again, then the file pointer would be advanced past
   * the PTGE chunk. Hence the outermost read_ptge_chunk call would cause the file to be
   * reopened again, leading to infinite recursion.
   *
   * To avoid the above situation, only read ptge chunk if target type is not PTGE
   */
  if (target_type != CDI_CHUNK_TYPE_PTGE) {
    read_ptge_chunk();
    //We also want to make sure that PTGE chunk is the first chunk in the
    //sequence. If not, then this would potentially cause enter_cdi_chunk to infinitely
    // recurse; any chunks that preceed PTGE cannot be read out of order since the
    // file pointer would be advanced beyond the PTGE chunk
    if (m_is_ptge_present && (m_chunk_number != 1)){
      msg_internal_error("Parentage data should be read from the first chunk in the CDI file\n");
    }
  }
}

//----------------------------------------------------------------------------
// enter_cdi_chunk
//----------------------------------------------------------------------------
BOOLEAN sCP_CDI_READER::enter_cdi_chunk(CIO_CCCC target_type)
{
  char chunk_name[5];
  cio_type_to_string(target_type, chunk_name);
  for (int i=0; i<m_num_chunks; i++) {
    if(m_chunk_number >= m_num_chunks) {
      m_is_first_pass = FALSE;
      reopen_file();
      reread_ptge_after_reopen(target_type);
    }

    if (cdi_cio_descend_with_error(m_cdi_info->cio_info, chunk_name) == CIO_ERR_SUCCESS) {
      // Read the chunk type
      CIO_CCCC type = cdi_get_type(m_cdi_info);
      if (m_is_first_pass) {
        // In the first pass thru the file, this code assumes the CP only looks for the SIMV, 
        // CSYS, and GLOB chunks. Thus, there can be no is_*_present flags for those chunks. 
        // We rely on the fact that the GLOB chunk always appears last in a CDI file.
        switch(type) {
        case CDI_CHUNK_TYPE_UNDB:       
          m_is_undb_present = TRUE; break;
        case CDI_CHUNK_TYPE_COUP:
          m_is_coup_present = TRUE; break;
#if 0
        case CDI_CHUNK_TYPE_CSYS:
          m_is_csys_present = TRUE; break;
#endif
        case CDI_CHUNK_TYPE_MEAS:
          m_is_meas_present = TRUE; break;
        case CDI_CHUNK_TYPE_NIRF:
          m_is_nirf_present = TRUE; break;
        case CDI_CHUNK_TYPE_GRAV:
          m_is_grav_present = TRUE; break;
        case CDI_CHUNK_TYPE_BODF:
          m_is_bodf_present = TRUE; break;
        case CDI_CHUNK_TYPE_LRFS:
          m_is_lrfs_present = TRUE; break;
        case CDI_CHUNK_TYPE_SRPT:
          m_is_srpt_present = TRUE; break;
        case CDI_CHUNK_TYPE_THPT:
          m_is_thpt_present = TRUE; break;
        case CDI_CHUNK_TYPE_STCT:
          m_is_stct_present = TRUE; break;
        case CDI_CHUNK_TYPE_PCFG:
          m_is_pcfg_present = TRUE; break;
        case CDI_CHUNK_TYPE_HCSS:
          m_is_hcss_present = TRUE; break;
        case CDI_CHUNK_TYPE_SCMS:
          m_is_scms_present = TRUE; break;
        case CDI_CHUNK_TYPE_PAXL:
          m_is_paxl_present = TRUE; break;
        case CDI_CHUNK_TYPE_RDSL:
          m_is_rdsl_present = TRUE; break;
        case CDI_CHUNK_TYPE_PTGE:
          m_is_ptge_present = TRUE; break;
        case CDI_CHUNK_TYPE_EQNS:
          m_is_eqns_present = TRUE; break;
        case CDI_CHUNK_TYPE_VSRS:
          m_is_vsrs_present = TRUE; break;
        case CDI_CHUNK_TYPE_TABL:
          m_is_tabl_present = TRUE; break;
        case CDI_CHUNK_TYPE_TBLS:
          m_is_tbls_present = TRUE; break;
        case CDI_CHUNK_TYPE_AFTD:
          m_is_aftd_present = TRUE; break;
        case CDI_CHUNK_TYPE_CMPS:
          m_is_cmps_present = TRUE; break;
	case CDI_CHUNK_TYPE_SCLS:
	  m_is_scls_present = TRUE; break;  
        case CDI_CHUNK_TYPE_WPTS:
          m_is_stbl_present = TRUE; break;
        case CDI_CHUNK_TYPE_WPDT:
          m_is_stbl_present = TRUE; break;
        case CDI_CHUNK_TYPE_SCPL:
#if SURF_COUP
          m_is_scpl_present = TRUE; break;
#else
          msg_error("Surface coupling CDI files not supported on this platform"); break;
#endif // SURF_COUP
        case CDI_CHUNK_TYPE_VHCL:
          m_is_vhcl_present = TRUE; break;
        case CDI_CHUNK_TYPE_MNTS:
          m_is_mnts_present = TRUE; break;

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
        case CDI_CHUNK_TYPE_TRAC:
          m_is_trac_present = TRUE;
          break;
        case CDI_CHUNK_TYPE_RGPN:
          m_is_rgpn_present = TRUE;
        case CDI_CHUNK_TYPE_VRTX:
          m_is_vrtx_present = TRUE;
          break;
//#endif
        case CDI_CHUNK_TYPE_BSRS:
          m_is_bsrs_present = TRUE;
          break;
        case CDI_CHUNK_TYPE_CLBR:
          m_is_clbr_present = TRUE;
          break;
        case CDI_CHUNK_TYPE_TIGR:
          m_is_tire_present = TRUE; 
          break;
        case CDI_CHUNK_TYPE_PSDF:
          m_is_psdf_present = TRUE;
          break;
        case CDI_CHUNK_TYPE_UDDL:
          m_is_uddl_present = TRUE;
          break;
        case CDI_CHUNK_TYPE_SCAS:
          m_is_scas_present = TRUE;
          break;
        default:
          break;
        }
      }

      if (type == target_type)
        return TRUE;
      else 
        cdi_cio_ascend_with_error(m_cdi_info->cio_info, chunk_name, ++m_chunk_number);
    }
  }
  return FALSE;
}

//----------------------------------------------------------------------------
// exit_cdi_chunk
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::exit_cdi_chunk() 
{
  const char chunk_name[5] = "    ";
  cdi_cio_ascend_with_error(m_cdi_info->cio_info, chunk_name, ++m_chunk_number);
}

VOID init_rfc(asINT32 license_count);

//----------------------------------------------------------------------------
// read_simv_chunk
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_simv_chunk()
{
  if (!enter_cdi_chunk(CDI_CHUNK_TYPE_SIMV))
    msg_internal_error("SIMV chunk not present in CDI file");
  CDI_SIMV simv = cdi_read_simv(m_cdi_info);

  m_are_expr_relative_to_user_csys = (simv->flags & CDI_EXPR_RELATIVE_TO_USER_CSYS) != 0;
  m_user_specified_dns_nu_over_t   = (simv->flags & CDI_USER_SPECIFIED_DNS_NU_OVER_T) != 0;

  cp_info.n_dims                             = simv->flags & CDI_IS_2D ? 2 : 3;
  cp_info.is_lattice_temperature_constant    = ((simv->flags & CDI_19_STATE) != 0)|| ((simv->flags & CDI_IS_MULTI_PHASE) != 0);
  cp_info.is_density_constant                = (simv->flags & CDI_TRUE_INCOMPRESSIBLE) != 0;
  cp_info.is_incompressible_solver           = (simv->flags & CDI_TRUE_INCOMPRESSIBLE) != 0;
  cp_info.constant_lattice_temperature_value = CONSTANT_LATTICE_TEMPERATURE;

  // Parse simulation flags
  cp_info.is_turb                            = (simv->flags & CDI_IS_TURB_MODEL) != 0;
  cp_info.is_heat_transfer                   = (simv->flags & CDI_IS_HEAT_TRANSFER) != 0;
  cp_info.is_water_vapor_transport           = (simv->flags & CDI_WATER_VAPOR_TRANSPORT) != 0;  //PDE_UDS
  cp_info.is_uds_transport                   = (simv->flags &  CDI_USER_DEFINED_SCALAR_TRANSPORT) != 0; //LB_UDS
  cp_info.is_flow                            = (simv->flags & CDI_ENABLE_FLOW_SOLVER) != 0;
  cp_info.is_conduction                      = (simv->flags & CDI_ENABLE_CONDUCTION_SOLVER) != 0;

  total_fsps = total_sps; // Initialize this for PT coupled cases
  if (cp_info.is_flow && cp_info.is_conduction) {
    total_fsps = total_sps/2;
  }
  else if (cp_info.is_conduction) {
    total_fsps = 0;
  }
  
  cp_info.is_radiation                       = (simv->flags & CDI_ENABLE_RADIATION) != 0;
  cp_info.n_scalars                          =  0;
  cp_info.first_uds_sri_variable             =  0;
  cp_info.last_uds_sri_variable              =  0;        
  cp_info.is_high_subsonic_mach_regime       = (simv->flags & CDI_HIGH_SUBSONIC_MACH_REGIME) != 0;
  cp_info.is_transonic_mach_regime           = (simv->flags & CDI_39_STATE) != 0 || (simv->flags & CDI_33_STATE) != 0;
  cp_info.is_boundary_layer_transition_model = (simv->flags & CDI_BOUNDARY_LAYER_TRANSITION) != 0;
  cp_info.is_5g_sim                          = (simv->flags & CDI_IS_MULTI_PHASE) != 0;

  cp_info.is_melting_solver                  = (simv->flags & CDI_EUTECTIC) != 0;

  cp_info.is_particle_solver = (simv->flags & CDI_PARTICLE_MODELING_ENABLED) != 0; 
  if(sim_args.disable_particle_modeling) { //PR39448
    if(cp_info.is_particle_solver) {
      msg_warn("Particle modeling solvers are disabled for this simulation.");
      simv->flags &= ~CDI_PARTICLE_MODELING_ENABLED; 
      cp_info.is_particle_solver = FALSE;
    }
  }
  cp_info.enable_film_trajectory_measurement = FALSE; //PR41946
  cp_info.is_accretion_simulation =  FALSE; //set true if an ACCR chunk is found in the cdi file.
  cp_info.is_thermal_particle_solver = FALSE;
  cp_info.is_film_solver = FALSE; //This is set true later if any particle materials not used for accretion are liquid.

  if (cp_info.is_5g_sim) {  //keep the following until PowerCase supports UDS in 5G
    std::string line;
    BOOLEAN input_table = TRUE;
    std::ifstream ifile("user_input.init");

    if (ifile.is_open())
    {
      while ( std::getline (ifile,line) )
      {
        std::istringstream stm(line) ; 
        std::vector<std::string> result ;
        std::string token ;
        while( stm >> token ){
          result.push_back(token) ;
        }
        if( result.size() <3 ) continue;
        if(result[1]=="n_user_defined_scalars" && result[2]!="0"){
          msg_print("A UDS case.");
	  cp_info.n_scalars = std::stoi(result[2]);
	  if (cp_info.n_scalars > 0)
	    cp_info.is_uds_transport = TRUE;     //LB_UDS
	  else
	    msg_error("Invalid n_user_defined_scalars %d.", cp_info.n_scalars);	  	  
        }
      }
    }
  }
 
  init_rfc(total_sps); // check out extra licenses - must be called after above flags are set

  if (cp_info.is_heat_transfer && (simv->flags & CDI_5_0_SOLVER || simv->flags & CDI_6_0_SOLVER) == 0
      && !sim_args.accept_old_ht_cdi_p)
    msg_error("The CDI file \"%s\" is compatible with the heat transfer"
              " model found in older versions of PowerFLOW. Please recreate"
              " the CDI file with PowerCASE version 5.0 or beyond.", m_filename);

  if (cp_info.is_high_subsonic_mach_regime && g_seed_ctl.seed_via_mks())
    msg_error("The --seed_mks option is incompatible with high subsonic simulations because in "
              "such a simulation, the simulation Mach number matches the real world experiment, "
              "so it is not possible to scale the simulation velocity to match the seed data.");

  if (cp_info.is_5g_sim && cp_info.is_incompressible_solver)
    msg_error("5G does not support incompressible solver.");
  
  if (cp_info.is_5g_sim && g_seed_ctl.seed_via_mks())
    msg_error("5G does not support the --seed_mks option.");

  m_simv_flags = simv->flags;

  // Set the new CDI_ENABLE_FLOW_SOLVER flag for old CDI files (which were obviously flow simulations)
  if ((m_simv_flags & CDI_ENABLE_CONDUCTION_SOLVER) == 0){
    m_simv_flags |= CDI_ENABLE_FLOW_SOLVER;
    cp_info.is_flow = TRUE; 
  }

  if (cp_info.is_radiation && total_rps == 0) {
    msg_error("The CDI file requires radiation, but the simulation was not launched with Radiation Processes.");
  } else if (!cp_info.is_radiation && total_rps > 0) {
    msg_error("The CDI file does not have radiation, but the simulation was launched with Radiation Processes.");
  }

  // set the LGI read flags based on dim and lattice type
  cDGF_GLOBAL_STATE::is_2d = (cp_info.n_dims == 2); 
  cDGF_GLOBAL_STATE::lattice_type = (simv->flags & CDI_39_STATE)? STP_LATTICE_D39: ((simv->flags & CDI_IS_MULTI_PHASE) ? STP_LATTICE_5G : STP_LATTICE_D19);
  cDGF_GLOBAL_STATE::latvec_mask_n_bytes = (cDGF_GLOBAL_STATE::lattice_type == STP_LATTICE_D39 ? 4 : 3);
  
  // can be overridden when reading the cprp chunk
  if (cp_info.is_heat_transfer) 
    cp_info.sim_ht_type = SRI_HT_PASSIVE_SCALAR; 
  else
    cp_info.sim_ht_type = SRI_HT_ISOTHERMAL;
      
  cp_info.is_ht_off_in_powercase = FALSE;
  if (cp_info.is_high_subsonic_mach_regime){
    if(!cp_info.is_heat_transfer) {
      cp_info.is_heat_transfer = TRUE;
      cp_info.is_ht_off_in_powercase = TRUE;
    }
    cp_info.sim_ht_type = SRI_HT_ACTIVE_SCALAR; 
  } else if (cp_info.is_transonic_mach_regime)
    cp_info.sim_ht_type = SRI_HT_ACTIVE_SCALAR;

  if (cp_info.is_conduction && !cp_info.is_heat_transfer)
     msg_error("Conduction solver must be used in heat transfer cases.");

  ccDOTIMES(i, 3) cdi_data.origin[i] = -simv->xform[i][3];
  if (cp_info.n_dims == 2) 
    cdi_data.origin[2] = 0;

  //Read the base coordinate system for measurements
  cp_info.sri_all_csys = xnew sSRI_CSYS [1];
  cp_info.sri_all_csys[0].name = EXA_STRDUP("lattice_csys");
  ccDOTIMES(i,4) {
    ccDOTIMES(j,4) {
      cp_info.sri_all_csys[0].xform[i][j] = simv->xform[i][j];
    }
  }
  cp_info.sri_n_csys = 1;

  asINT32 major_v = m_cdi_info->major_version;
  asINT32 minor_v = m_cdi_info->minor_version;

  if (cp_info.is_water_vapor_transport > 0) {
    cp_info.n_scalars           =  1;
  }
  // PRE 2.0 CDI FILES marked as internal flow cases were implicitly turbulence modeling cases.
  if ((major_v < 2) && ((simv->flags & CDI_IS_INTERNAL_FLOW) != 0)) {
    if (!m_accept_old_turb_cdi) {
      msg_error("The CDI file \"%s\" uses version %d.%d of the CDI format,"
                " which is not compatible with this version of PowerFLOW"
                " for cases with turbulence modeling."
                " This CDI file is only compatible with older versions of PowerFLOW.",
                m_filename, major_v, minor_v);
    }
    else {
      msg_warn("\"%s\" is a release 3.1 CDI file: Using defaults for turbulence"
               " intensity (0.01) and turbulence length scale (1 voxel).",
               m_filename);
      cp_info.is_turb = TRUE;
      m_simv_flags |= CDI_IS_TURB_MODEL;
    }
  }

  // PRE 2.6 CDI FILE used a different heat transfer model
  if (cp_info.is_heat_transfer && m_is_pre_2_6_cdi) {
    BOOLEAN accept_old_cdi = ((getenv("EXA_DISC_READ_OLD_CDI") != NULL) ||
                              (getenv("EXA_READ_OLD_CDI") != NULL));
    if (!accept_old_cdi) {
      msg_error("The CDI file \"%s\" uses version %d.%d of the CDI format,"
                " which is not compatible with this version of the PowerFLOW Simulator"
                " for heat transfer cases."
                " The CDI file should be prepared with Release 3.4p1 or later"
                " of ExaCASE.",
                m_filename, major_v, minor_v);
    }
  }  

  cSTRING solver_version = getenv("EXA_POWERFLOW_SOLVER_VERSION");
  if (solver_version == NULL)
    msg_internal_error("Environment variable EXA_POWERFLOW_SOLVER_VERSION"
                       " is not set");

  if (strcmp(solver_version, "5") == 0) {
    if (simv->flags & CDI_6_0_SOLVER
        && (simv->flags & CDI_IS_TURB_MODEL)
        && (simv->flags & CDI_HIGH_SUBSONIC_MACH_REGIME) == 0
        && (simv->flags & CDI_39_STATE) == 0
        && (simv->flags & CDI_33_STATE) == 0)
      msg_error("The CDI file \"%s\" is compatible with the turbulence"
                " model found in PowerFLOW version 6.0 and beyond.",
                m_filename);
    else if (simv->flags & CDI_USES_6_X_ONLY_FEATURE)
      msg_error("The CDI file \"%s\" contains one or more features that"
                " are only supported by PowerFLOW version 6.0 and beyond.",
                m_filename);
  } else if (strcmp(solver_version, "6") == 0) {
    if ((simv->flags & CDI_6_0_SOLVER) == 0
        && (simv->flags & CDI_IS_TURB_MODEL)
        && (simv->flags & CDI_HIGH_SUBSONIC_MACH_REGIME) == 0
        && (simv->flags & CDI_39_STATE) == 0
        && (simv->flags & CDI_33_STATE) == 0) {
      if (sim_args.accept_old_turb_cdi_p)
        msg_warn("The CDI file \"%s\" is compatible with the turbulence"
                 " model found in older versions of PowerFLOW, but will"
                 " be accepted because of the --accept_old_turb_cdi flag.",
                 m_filename);
      else
        msg_error("The CDI file \"%s\" is compatible with the turbulence"
                  " model found in the 5.X versions of PowerFLOW. Please"
                  " recreate the CDI file with PowerCASE version 6.0 or beyond.",
                  m_filename);
    }
  } else {
    msg_internal_error("Environment variable EXA_POWERFLOW_SOLVER_VERSION"
                       " is not set to 5 or 6");
  }

  cdi_destroy_simv(simv);
  exit_cdi_chunk();

#if defined(_EXA_HPMPI)
  die_if_gpu(total_sps > 1, "Multi process simulation with HPMPI", false);
#endif
  // die_if_gpu(cp_info.is_lattice_temperature_constant    = ((simv->flags & CDI_19_STATE) != 0)|| ((simv->flags & CDI_IS_MULTI_PHASE) != 0);
  // die_if_gpu(cp_info.is_density_constant                = (simv->flags & CDI_TRUE_INCOMPRESSIBLE) != 0;
  // die_if_gpu(cp_info.is_incompressible_solver           = (simv->flags & CDI_TRUE_INCOMPRESSIBLE) != 0;
  // die_if_gpu(cp_info.constant_lattice_temperature_value = CONSTANT_LATTICE_TEMPERATURE;

  // die_if_gpu(cp_info.is_turb                            = (simv->flags & CDI_IS_TURB_MODEL) != 0;
  die_if_gpu(cp_info.is_heat_transfer, "Heat transfer", false);//                   = (simv->flags & CDI_IS_HEAT_TRANSFER) != 0;
  die_if_gpu(cp_info.is_water_vapor_transport, "Water vapor transport", false);//           = (simv->flags & CDI_WATER_VAPOR_TRANSPORT) != 0;
  die_if_gpu(cp_info.is_uds_transport, "User-defined scalars", true);//                          =  0;
  die_if_gpu(cp_info.is_conduction, "Conduction simulations", true);
  die_if_gpu(cp_info.is_radiation, "Radiation simulations", true);
  // die_if_gpu(cp_info.first_uds_sri_variable             =  0;
  // die_if_gpu(cp_info.last_uds_sri_variable              =  0;        
  // die_if_gpu(cp_info.is_high_subsonic_mach_regime       = (simv->flags & CDI_HIGH_SUBSONIC_MACH_REGIME) != 0;
  die_if_gpu(cp_info.is_transonic_mach_regime, "Transonic simulations", true);//           = (simv->flags & CDI_39_STATE) != 0 || (simv->flags & CDI_33_STATE) != 0;
  // die_if_gpu(cp_info.is_boundary_layer_transition_model = (simv->flags & CDI_BOUNDARY_LAYER_TRANSITION) != 0;
  die_if_gpu(cp_info.is_5g_sim, "5G simulations", true);//                          = (simv->flags & CDI_IS_MULTI_PHASE) != 0;

  die_if_gpu(cp_info.is_melting_solver, "Melting simulations", true);//                  = (simv->flags & CDI_EUTECTIC) != 0;

  die_if_gpu(cp_info.is_particle_solver, "Particle simulations", true);// = (simv->flags & CDI_PARTICLE_MODELING_ENABLED) != 0; 
  // die_if_gpu(cp_info.enable_film_trajectory_measurement = FALSE; //PR41946
  // die_if_gpu(cp_info.is_accretion_simulation =  FALSE; //set true if an ACCR chunk is found in the cdi file.
  // die_if_gpu(cp_info.is_thermal_particle_solver = FALSE;
  // die_if_gpu(cp_info.is_film_solver = FALSE; //This is set true later if any particle materials not used for accretion are liquid.
}

static void write_tire_to_all_sps(const sCDI_TIRE &tire, const sBG_TRANSFORM3d& xform) {
  cDGF_DEFORMING_TIRE dgf_tire;
  ccDOTIMES(i,4) {
    ccDOTIMES(j,4) {
      dgf_tire.xform[i][j] = xform.M(i,j);
    }
  }
  dgf_tire.grooved_tire = tire.grooved_tire;
  dgf_tire.n_knots[0] = tire.n_knots[0];
  dgf_tire.n_knots[1] = tire.n_knots[1];

  ccDOTIMES(sp, total_sps) {
    dgf_tire.write(g_sp_streams[sp]);
  }

  cDGF_DEFORMING_TIRE_VALUE d;

  ccDOTIMES(i,tire.n_knots[0]) {
    d.value = tire.u_knots[i];
    ccDOTIMES(sp, total_sps) {
      d.write(g_sp_streams[sp]);
    }
  }

  ccDOTIMES(i,tire.n_knots[1]) {
    d.value = tire.v_knots[i];
    ccDOTIMES(sp, total_sps) {
      d.write(g_sp_streams[sp]);
    }
  }

  ccDOTIMES(i,tire.coeff_carcass.size()) {
    d.value = tire.coeff_carcass[i];
    ccDOTIMES(sp, total_sps) {
      d.write(g_sp_streams[sp]);
    }
  }

  if ( !tire.grooved_tire ) {
    ccDOTIMES(i,tire.coeff_wrap.size()) {
      d.value = tire.coeff_wrap[i];
      ccDOTIMES(sp, total_sps) {
        d.write(g_sp_streams[sp]);
      }
    }
  }
}


VOID sCP_CDI_READER::read_tigr_chunk()
{
  if (!m_is_tire_present)
    return;

  die_if_gpu(true, "Deforming tires", true);

  enter_cdi_chunk(CDI_CHUNK_TYPE_TIGR);

  cp_info.n_dtires = cio_get_count(m_cdi_info->cio_info);

  if ( cp_info.n_dtires == 0 ) {
    msg_warn("TIGR chunk exists with no tires in it!");
  }

  cp_info.sri_dtires = new sSRI_DEFORMING_TIRE[cp_info.n_dtires];

  // Case generates deforming tires in a local coordinate system
  // with the origin at (0,0,0) and the rotation axis along the x-axis. 
  double origin[3] = { 0.0, 0.0, 0.0 };
  double rotaxis[3] = { 1.0, 0.0, 0.0 };

  cDGF_DEFORMING_TIRE_TABLE record;
  record.tag.id = DGF_DEFORMING_TIRE_TABLE_TAG;
  record.tag.length = 0; // we don't know how long this will be
  record.num_tires = cp_info.n_dtires;

  write_header_to_all_sps(record);

  ccCDI_DO_INNER_CHUNKS(i, "tire", m_cdi_info) {
    sSRI_DEFORMING_TIRE * sri_dtire = cp_info.sri_dtires + i;
    sCDI_TIRE * tire;
    tire = cdi_read_tire(m_cdi_info);

    // construct the local -> lattice xform to be used by this tire
    double (*simv_xform)[4] = cp_info.sri_all_csys[0].xform;
    sBG_TRANSFORM3d simv(simv_xform[0][0], simv_xform[0][1], simv_xform[0][2], simv_xform[0][3],
                              simv_xform[1][0], simv_xform[1][1], simv_xform[1][2], simv_xform[1][3],
                              simv_xform[2][0], simv_xform[2][1], simv_xform[2][2], simv_xform[2][3]);

    double (*xform)[4] = cp_info.all_csys->coord_systems[tire->csys_index].l_to_g_xform;
    sBG_TRANSFORM3d csys(xform[0][0], xform[0][1], xform[0][2], xform[0][3],
                              xform[1][0], xform[1][1], xform[1][2], xform[1][3],
                              xform[2][0], xform[2][1], xform[2][2], xform[2][3]);

    sBG_TRANSFORM3d fullxform = simv.Inverse() * csys;

    write_tire_to_all_sps(*tire, fullxform);

    // increment by 1 to set the right index in surface files
    sri_dtire->csys_index = tire->csys_index + 1;
    sri_dtire->dtire = new Tire::cDEFORMING_TIRE(origin, 
                       rotaxis,
                       tire->grooved_tire,
                       tire->u_knots,
                       tire->v_knots,
                       tire->coeff_wrap,
                       tire->coeff_carcass,
                       fullxform);
    cdi_destroy_tire(tire);
  }

  exit_cdi_chunk();

}

    




//----------------------------------------------------------------------------
// read_ptge_chunk
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_ptge_chunk()
{
  if (!m_is_ptge_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_PTGE);

  CDI_PTGE ptge = cdi_read_ptge(m_cdi_info);
  //IDs loaded previously from the LGI_PARENTAGE_TAG and should match the CDI values
  if (cp_info.ptge.case_file_id     != ptge->case_file_id || 
      cp_info.ptge.case_geometry_id != ptge->case_geometry_id ||
      cp_info.ptge.cdi_file_id      != ptge->cdi_file_id) {
    msg_warn("Inconsistent CDI/LGI file pair that can potentially lead to errors. It may be necessary to re-run the discretizer.");
    //Load the CDI values as the correct ones to avoid issuing this warning again
    cp_info.ptge.case_file_id     = ptge->case_file_id;
    cp_info.ptge.case_geometry_id = ptge->case_geometry_id;
    cp_info.ptge.cdi_file_id      = ptge->cdi_file_id;  
  }

  //m_cdi_info is dirty after ptge chunk is read
  m_cdi_info->cdi_file_id = ptge->cdi_file_id;

  cdi_destroy_ptge(ptge);

  exit_cdi_chunk();
}

//----------------------------------------------------------------------------
// Copy encryption data
// Has to be read only once parentage information has
// been populated. This internally creates the cipher for CDI based on file id
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::copy_encryption_data(){

  if ( m_is_rgpn_present ) {
    enter_cdi_chunk(CDI_CHUNK_TYPE_RGPN);
    cp_info.encryption_struct.parse_from_cdi_info(m_cdi_info);
    exit_cdi_chunk();
  }
}

//----------------------------------------------------------------------------
// read_psdf_chunk
// Populate partitions structure
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_psdf_chunk(){

  if ( m_is_psdf_present ) {
    enter_cdi_chunk(CDI_CHUNK_TYPE_PSDF);
    cp_info.partitions().ReadFromCDI(m_cdi_info);    
    exit_cdi_chunk();
  }  

  cp_info.m_partitions_info.set_is_pre_heirarchical_partitions_cdi(cdi_data.is_pre_partitions_cdi());
  
  //ReadRegionsAndFacesFromCDI call is independent of whether there
  //is a PSDF chunk or not In the event there is no PSDF chunk, the
  //partitions structure created is trivial but needs to know about
  //regions and faces by reading these chunks
  cp_info.partitions().ReadRegionsAndFacesFromCDI(m_cdi_info);
}

//----------------------------------------------------------------------------
// read_characteristic_properties
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_characteristic_properties()
{
  sSRI_CHAR_PARAM_DESC sri_param;
  CDI_CPRP cprp = cdi_read_cprp(m_cdi_info);
  char* name = cprp->char_prop_name;
  if (strcmp(name, CDI_CASE_CHAR_LENGTH_NAME) == 0) {
    cdi_data.char_length = header.char_length = cprp->value;
    sri_param.param_type = SRI_CHAR_LENGTH;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_CHAR_AREA_NAME) == 0) {
    sri_param.param_type = SRI_CHAR_AREA;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_CHAR_SPEED_NAME) == 0) {
    cdi_data.char_vel = header.char_vel = cprp->value;
    sri_param.param_type = SRI_CHAR_VELOCITY;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_LATTICE_SPECIFIC_HEAT_RATIO_NAME) == 0) {
    cdi_data.lattice_gamma = cprp->value;
    sri_param.param_type = SRI_LATTICE_GAMMA;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_SPECIFIC_HEAT_RATIO_NAME) == 0) {
    sri_param.param_type = SRI_GAMMA;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_CHAR_TEMPERATURE_NAME) == 0) {
    cdi_data.char_temp = header.char_temp = cprp->value;
    sri_param.param_type = SRI_CHAR_TEMP;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_SPECIFIC_HEAT_NAME) == 0) {
    cdi_data.specific_heat = cprp->value;
    sri_param.param_type = SRI_SPECIFIC_HEAT;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_CHAR_DENSITY_NAME) == 0) {
    if (cp_info.is_5g_sim)
      cdi_data.char_density = header.char_density = cprp->value;  //char_density is not scaled by DENSITY_CONVERSION_FACTOR in 5G cdi file
    else
      cdi_data.char_density = header.char_density = cprp->value / DENSITY_CONVERSION_FACTOR;
    sri_param.param_type = SRI_CHAR_DENSITY;
    sri_param.value = cdi_data.char_density;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_GAS_CONSTANT_NAME) == 0) {
    cdi_data.gas_constant = cprp->value;
    sri_param.param_type = SRI_GAS_CONSTANT;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_REYNOLDS_NUMBER_NAME) == 0) {
    cdi_data.reynolds_number = header.reynolds_number = cprp->value;
    sri_param.param_type = SRI_REYNOLDS_NUMBER;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_FLUID_PRANDTL_NUMBER_NAME) == 0) {
    cdi_data.fluid_Prandtl_number = header.fluid_Prandtl_number = cprp->value;
    sri_param.param_type = SRI_FLUID_PRANDTL_NUMBER;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
#if 0 /* PR 31405: don't write turb prandtl # to meas files */
  else if (strcmp(name, CDI_CASE_TURB_PRANDTL_NUMBER_NAME) == 0) {
    cdi_data.turb_Prandtl_number = header.turb_Prandtl_number = cprp->value;
    sri_param.param_type = SRI_TURBULENT_PRANDTL_NUMBER;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
#endif
  else if (strcmp(name, CDI_CASE_MEAN_TEMPERATURE_NAME) == 0) {
    header.mean_temp    = cprp->value;
    cdi_data.mean_temp  = cprp->value;
    cp_info.sim_ht_type = SRI_HT_ACTIVE_SCALAR;
    sri_param.param_type = SRI_CHAR_MEAN_TEMP;
    sri_param.value = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_LATTICE_GAS_CONSTANT_NAME) == 0) {
    header.lattice_gas_const    = cprp->value;
    cdi_data.lattice_gas_const  = cprp->value;
    sri_param.param_type        = SRI_LATTICE_GAS_CONSTANT;
    sri_param.value             = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_MAX_EXP_VEL_NAME) == 0) {
    header.max_exp_vel    = cprp->value;
    cdi_data.max_exp_vel  = cprp->value;
    sri_param.param_type  = SRI_MAX_EXP_VEL;
    sri_param.value       = cprp->value;
    cp_info.sri_params.push_back(sri_param);
  }
  else if (strcmp(name, CDI_CASE_SPECIFIC_HEAT_SOLID_NAME) == 0) { //Melting solver
    cdi_data.specific_heat_solid = cprp->value;
  }
  else if (strcmp(name, CDI_CASE_MELTING_TEMP_NAME) == 0) { //Melting solver
    header.melting_temp = cprp->value;
  }
  else if (strcmp(name, CDI_CASE_BOILING_TEMP_NAME) == 0) { //Melting solver
    header.boiling_temp = cprp->value;
  }
  else if (strcmp(name, CDI_CASE_LATENT_HEAT_OF_FUSION_NAME) == 0) { //Melting solver
    header.latent_heat_of_fusion = cprp->value;
  }
  else if (strcmp(name, CDI_CASE_THERMAL_CONDUCTIVITY_SOLID_NAME) == 0) { //Melting solver
    cdi_data.thermal_conductivity_solid = cprp->value;
  }
  else if (strcmp(name, CDI_CASE_THERMAL_CONDUCTIVITY_LIQUID_NAME) == 0) { //Melting solver
    cdi_data.thermal_conductivity_liquid = cprp->value;
  }
  else if (strcmp(name, CDI_CASE_DENSITY_SOLID_NAME) == 0) { //Melting solver
    header.density_solid = cprp->value;
  } 
  else if (strcmp(name, CDI_CASE_RADIATION_BACKGROUND_TEMP) == 0) {
    cdi_data.radiation_background_temp = cprp->value;
  } 
  else if (strcmp(name, CDI_CASE_RADIATION_UPDATE_RATIO) == 0) {
    cdi_data.radiation_update_ratio = cprp->value;
  }
  cdi_destroy_cprp(cprp);
}

//----------------------------------------------------------------------------
// read_thermal_acceleration
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_thermal_acceleration()
{
  CDI_THMA cdi_thma = cdi_read_thma(m_cdi_info);

  if (cdi_thma->end_time < cdi_thma->start_time) {
    cdi_destroy_thma(cdi_thma); 
    return;
  }

  TIMESTEP ct                = 1 << (cp_info.num_scales - 1); // coarsest timestep
  TIMESTEP lcm_steps         = ct * lcm(cp_info.n_lb_base_steps,
                                        cp_info.n_t_base_steps,
                                        cp_info.n_ke_base_steps,
                                        cp_info.n_uds_base_steps,
                                        cp_info.n_particle_base_steps);//multiple of ct & all flow solvers base steps
  TIMESTEP n_user_base_steps = cp_info.n_user_base_steps;
  TIMESTEP n_lattice_base_steps = cp_info.n_lattice_base_steps;

  // round start, end, period, and interval up to a multiple of lcm_steps
  TIMESTEP start    = cdi_thma->start_time * n_lattice_base_steps; //in base steps
  TIMESTEP period   = cdi_thma->period     * n_lattice_base_steps; //in base steps
  TIMESTEP interval = cdi_thma->interval   * n_lattice_base_steps; //in base steps
  TIMESTEP end      = cdi_thma->end_time;                       //in cdi steps

  dFLOAT end_base_steps = (dFLOAT)end * (dFLOAT)n_lattice_base_steps;  //transfer to base steps

  end           = MIN(end_base_steps, sINT32_MAX);  
  start         = round_up_time(start   , lcm_steps) / n_user_base_steps; //in user's timestep
  period        = round_up_time(period  , lcm_steps) / n_user_base_steps; //in user's timestep
  interval      = round_up_time(interval, lcm_steps) / n_user_base_steps; //in user's timestep
  end           = round_up_time(end     , lcm_steps) / n_user_base_steps; //in user's timestep

  if (period < interval) 
    interval = period;

  header.thermal_accel.start    = start;
  header.thermal_accel.repeat   = ceil((dFLOAT)(end - start) / (dFLOAT)period);
  header.thermal_accel.period   = period;
  header.thermal_accel.interval = interval;

  cdi_destroy_thma(cdi_thma);
}
//----------------------------------------------------------------------------
// read_acceleration_factor
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_acceleration_factor()
{
  if (!m_is_aftd_present){
    cp_info.film_acceleration_factor = 1.0;
    return;
  }
  enter_cdi_chunk(CDI_CHUNK_TYPE_AFTD);

  CDI_AFTD cdi_aftd = cdi_read_aftd(m_cdi_info);
  cp_info.film_acceleration_factor = cdi_aftd->acceleration_factor;
  exit_cdi_chunk();
  //cdi_destroy_aftd(cdi_aftd);
}

//----------------------------------------------------------------------------
// read_global_header
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_global_header()
{
  CDI_GHDR ghdr_chunk = cdi_read_ghdr(m_cdi_info);

  cp_info.sim_duration_via = ghdr_chunk->sim_duration_via;
  cp_info.duration_after_init_transient = ghdr_chunk->n_timesteps_after_init_trans;

  ccDOTIMES(i, ghdr_chunk->monitors.size()) {
    cp_info.monitors_to_find_sim_end_init_transient.push_back(ghdr_chunk->monitors[i]);
  }

  if (sim_args.num_timesteps > 0) { 
    ghdr_chunk->n_timesteps = sim_args.num_timesteps;
  } else {
    // If user does not specify number of timesteps in exaqsub, and the simulation duration is set 
    // to greatest measurement end time, overwrite the end_time here
    if (cp_info.sim_duration_via == eCDI_SIM_DURATION_VIA::GreatestMeasurementEndTime) {
      ghdr_chunk->n_timesteps     = TIMESTEP_MAX;
    }
  }
  
  cp_info.end_time     = ghdr_chunk->n_timesteps;
  header.num_timesteps = ghdr_chunk->n_timesteps;

  cp_info.start_time   = 0;
  cp_info.time         = 0;
  // header.start_time = 0; //simulation time-span starts from 0, no need to pass it to SPs

  cp_info.freeze_momentum_field = ghdr_chunk->freeze_momentum_field;
  if(cp_info.freeze_momentum_field && !(g_seed_ctl.is_smart_seed() || sim_args.resume_filename) )
    msg_error("This case will freeze the velocity and turbulence fields after"
              " initialization. Either --seed or --resume with a MME"
              " checkpoint file or measurement file is required.");

  cp_info.thermal_timestep_ratio= ghdr_chunk->physical_time_scaling;

  cp_info.local_vel_freeze = ghdr_chunk->local_vel_freeze;
  if (cp_info.local_vel_freeze)
    msg_print("Simulation is using the selective velocity freeze feature");
  header.local_vel_freeze = cp_info.local_vel_freeze;
  cp_info.do_split_seed = cp_info.local_vel_freeze;
  if (cp_info.local_vel_freeze) {
    if (!(g_seed_ctl.is_smart_seed() || sim_args.resume_filename))
      msg_error("For selective velocity freeze, either --seed or --resume option is required.");
    if (cp_info.is_5g_sim)
      msg_error("Selective velocity freeze does not support 5G case.");
    if (cp_info.is_transonic_mach_regime)
      msg_error("Selective velocity freeze does not support transonic case.");
    if (cp_info.freeze_momentum_field)
      msg_error("Selective velocity freeze does not support velocity-frozen case.");
  }

  //liquid
  header.liquid_type            = ghdr_chunk->liquidMaterialType;
  
  cp_info.has_average_mme_window = ghdr_chunk->has_average_mme_window;
  header.has_average_mme_window  = ghdr_chunk->has_average_mme_window;
  //ghdr_chunk->include_third_block = FALSE;
  cp_info.variable_gamma        =  ghdr_chunk->m_temperatureDependentGamma;
  header.variable_gamma         =  ghdr_chunk->m_temperatureDependentGamma;

  if(sim_args.disable_calm)
    header.cooling_air_opt      =  eCDI_COOLING_AIR_LEAKAGE_OPTION::Off;
  else
    header.cooling_air_opt      =  ghdr_chunk->m_coolingAirOpt;

  if (sim_args.use_PF6_VLES_model)
    header.use_PF6_VLES_model   = TRUE;
  else
    header.use_PF6_VLES_model   = FALSE;

  if (sim_args.checkpoint_body_force) {
    header.checkpoint_body_force   = TRUE;
    cp_info.is_checkpoint_body_force = TRUE;
  }
  else {
    header.checkpoint_body_force   = FALSE;
    cp_info.is_checkpoint_body_force = FALSE;
  }

  if (sim_args.seed_body_force) {
    header.seed_body_force   = TRUE;
    cp_info.is_seed_body_force = TRUE;
  }
  else {
    header.seed_body_force   = FALSE;
    cp_info.is_seed_body_force = FALSE;
  }

  if (sim_args.laplace)
    header.laplace = TRUE;
  else
    header.laplace = FALSE;

  if(sim_args.seed_body_force_scale > 0)
    header.seed_body_force_scale = sim_args.seed_body_force_scale;
  else
    header.seed_body_force_scale = 0;

  if (ghdr_chunk->include_third_block) {//for computing custom var IDs, used in LB_UDS and 5G
    header.custom_vars.numParticleEmitters     = ghdr_chunk->numParticleEmitters;
    header.custom_vars.numParticleMaterials    = ghdr_chunk->numParticleMaterials;
    header.custom_vars.numParticleModelingVars = ghdr_chunk->numParticleModelingVars;
    header.custom_vars.numUDScalars            = ghdr_chunk->numUDScalars;
    header.custom_vars.numUDSVars              = ghdr_chunk->numUDSVars;
    header.custom_vars.num5gFluidComponents    = ghdr_chunk->num5gFluidComponents;
    header.custom_vars.num5gVars               = ghdr_chunk->num5gVars;

    cp_info.num_fluid_components   = header.custom_vars.num5gFluidComponents;
    if (!cp_info.is_5g_sim)
      cp_info.n_scalars = header.custom_vars.numUDScalars;                     //5G LB_UDS will read from "user_input.init" file
    
    cp_info.cvid_helper = new sSRI_CUSTOM_VAR_ID_HELPER(header.custom_vars.numParticleEmitters,
							header.custom_vars.numParticleMaterials,
							header.custom_vars.numUDScalars,
							header.custom_vars.num5gFluidComponents,
							header.custom_vars.numParticleModelingVars,
							header.custom_vars.numUDSVars,
							header.custom_vars.num5gVars);
    //debug
    //printf("SRI_VARIABLE_MASS_EXCHANGE=%d, num5gVars=%d\n", SRI_VARIABLE_MASS_EXCHANGE, header.custom_vars.num5gVars);


  } else {
    header.custom_vars.numParticleEmitters     = -1;
    cp_info.num_fluid_components   = 0;
    cp_info.cvid_helper = new sSRI_CUSTOM_VAR_ID_HELPER(0,0,0,0);
  }
  cp_info.s_fluid_components_5g = NULL;
  cp_info.s_scalar_materials = NULL;
  
  if (cp_info.is_5g_sim && (cp_info.num_fluid_components<=0 
			    || cp_info.num_fluid_components>MAX_NUM_COMPONENTS_5G))
    msg_error("The number of fluid components (%d) is out of range [1,%d].", 
	      cp_info.num_fluid_components, MAX_NUM_COMPONENTS_5G);

  if (MAX_N_USER_DEFINED_SCALARS < CDI_MAX_N_USER_DEFINED_SCALARS)
    msg_warn("CDI_MAX_N_USER_DEFINED_SCALARS is greater than MAX_N_USER_DEFINED_SCALARS.");
  if (cp_info.is_uds_transport && (cp_info.n_scalars<=0 || cp_info.n_scalars>MAX_N_USER_DEFINED_SCALARS))
    msg_error("The number of User Defined Scalars (%d) is out of range [1,%d].", cp_info.n_scalars, MAX_N_USER_DEFINED_SCALARS);
  
  cdi_destroy_ghdr(ghdr_chunk);
}
//----------------------------------------------------------------------------
// read_kesc_info
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_kesc_info()
{
  sCDI_KESC kesc_chunk = cdi_read_kesc(m_cdi_info);
  if(sim_args.is_ke_base_step_arg_read) {
    msg_warn("Turbulence solver super-cycling factor set using -n_ke_base_steps argument will take precedence over super cycling factor %d provided in CDI file.", kesc_chunk.super_cycling_factor);
    return;
  }
  if(kesc_chunk.bKesc_activated) {
    cp_info.n_ke_base_steps = kesc_chunk.super_cycling_factor;
    msg_print("Turbulence solver super-cycling factor set to %d", cp_info.n_ke_base_steps);
  }

}
//----------------------------------------------------------------------------
// read_real_to_lattice_conversions
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_real_to_lattice_conversions()
{
  CDI_UNIT unit_chunk = cdi_read_unit(m_cdi_info);

  header.meters_per_cell            = unit_chunk->meters_per_cell == 0          ? 1.0 : unit_chunk->meters_per_cell;
  header.seconds_per_timestep       = unit_chunk->seconds_per_timestep == 0     ? 1.0 : unit_chunk->seconds_per_timestep;      
  header.kelvins_per_lattice_temp   = unit_chunk->kelvins_per_lattice_temp == 0 ? 1.0 : unit_chunk->kelvins_per_lattice_temp;
  header.kilos_per_particle         = unit_chunk->kilos_per_particle == 0       ? 1.0 : unit_chunk->kilos_per_particle;

  cdi_data.meters_per_cell          = header.meters_per_cell;
  cdi_data.seconds_per_timestep     = header.seconds_per_timestep;
  cdi_data.kelvins_per_lattice_temp = header.kelvins_per_lattice_temp;
  cdi_data.kilos_per_particle       = header.kilos_per_particle;

  cdi_destroy_unit(unit_chunk);
}

//----------------------------------------------------------------------------
// read_checkpoint_info
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_checkpoint_info()
{
  if (!sim_args.checkpoint_override)
    m_cpnt = cdi_read_cpnt(m_cdi_info);
}

VOID sCP_CDI_READER::init_checkpoint_time_desc() // relies on sim.num_scales being set
{
  if (sim_args.gpu && (sim_args.checkpoint_override || m_cpnt != NULL)) {
    if (cp_info.create_full_ckpts || sim_args.full_chkpnt_at_end) {
      msg_warn("Full checkpoints are not supported on GPU, and will be converted to fluid checkpoints");
      if (sim_args.full_chkpnt_at_end) {
        sim_args.full_chkpnt_at_end = FALSE;
        sim_args.mme_chkpnt_at_end = TRUE;
      }
      if (cp_info.create_full_ckpts) {
        cp_info.create_full_ckpts = FALSE;
        sim_args.run_options &= ~SIM_FULL_CKPTS;
      }

    }
  }
  if (!sim_args.checkpoint_override && m_cpnt != NULL) {
    cp_info.periodic_ckpts_p = TRUE;
    align_ckpt_time_parameters(m_cpnt->start, m_cpnt->end, m_cpnt->freq);
  }
}
//----------------------------------------------------------------------------
// read_time_coupling_schemes_info
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_time_coupling_schemes_info()
{
  ccCDI_DO_INNER_CHUNKS(i, "csps", m_cdi_info) {
    sTIME_COUPLING_PHASE phase;
    CDI_CSPH csph = cdi_read_csph(m_cdi_info);
    phase.time_rel_solver         = (eCOUPLED_SOLVER::Enum)(csph->time_rel_solver);
    phase.start                   = csph->start;
    phase.time_coupling           = (eTIME_COUPLING_SCHEME::Enum)(csph->time_coupling);
    phase.frozen_solver           = (eCOUPLED_SOLVER::Enum)(csph->frozen_solver);
    phase.therm_time_ratio        = csph->therm_time_ratio;
    phase.radiation_update_ratio  = csph->radiation_update_ratio;
    phase.flow_duration           = csph->flow_duration;
    phase.flow_avg_interval       = csph->flow_avg_interval;
    phase.conduction_duration     = csph->conduction_duration;
    phase.conduction_avg_interval = csph->conduction_avg_interval;
    // msg_print("TIME_COUPLING_PHASE %d %d %d %f",i, phase.start, phase.time_rel_solver, phase.radiation_update_ratio);
    cp_info.time_coupling_phases.push_back(phase);
  }
}


//----------------------------------------------------------------------------
// print_next_chunk_name
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::print_next_chunk_name()
{
  char type_str[5];
  CIO_CCCC type = cdi_get_type(m_cdi_info);
  cio_type_to_string(type, type_str);
  msg_print("Next chunk name = %s", type_str);
}
 
// Always round up
static TIMESTEP align_with_coarsest_timesteps(TIMESTEP time) {
  TIMESTEP coarsest_timesteps = 1 << (cp_info.num_scales - 1);
  //scales time provided in "user time" to timesteps
  TIMESTEP n_user_steps_per_timestep = std::min(cp_info.n_lb_base_steps, cp_info.n_conduction_base_steps) / cp_info.n_user_base_steps;
  time /= n_user_steps_per_timestep;
  //rounds the timesteps value, and then converts it back to "user's time"
  TIMESTEP n_period = ((dFLOAT)time - 1)/coarsest_timesteps + 1;
  dFLOAT rounded_time = (dFLOAT)n_period * coarsest_timesteps * n_user_steps_per_timestep;
  return MIN(rounded_time, TIMESTEP_MAX);
}

static inline VOID round_up_to_nearest_valid_avg_interval(TIMESTEP& interval, 
                                                          const TIMESTEP& base_factor, 
                                                          const TIMESTEP& phase_base_span) {
  if (phase_base_span < base_factor || interval <= 0) {
    //uses the minimum averaging interval
    interval = base_factor;
  } else {
    //rounds up the interval using base factor, capping it by the the phase span
    interval = std::min((interval-1)/base_factor + 1, (phase_base_span / base_factor)) * base_factor;
  }
}

static BOOLEAN maybe_load_time_coupling_phase(TIMESTEP &next_start,
                                              sLCM_STEPS_REALMS& lcm_steps,
                                              sTIME_COUPLING_PHASE& this_phase,
                                              sTIME_COUPLING_PHASE* next_phase,
                                              std::vector<sTIME_COUPLING_PHASE>& valid_phases) {
  //If this phase was absorbed by the previous one, in which case tags it to be skipped and return
  //Otherwise, sets the start time for this phase determined in the previous iteration
  if (next_phase != nullptr && next_phase->start < next_start) { 
    return TRUE;
  } else {
    this_phase.start = next_start;
  }
  
  //SameRate & DifferentRate coupling phases can adjust the time-ratio between realms, accounts for it
  if (this_phase.time_coupling == eTIME_COUPLING_SCHEME::SameRate) {
    this_phase.therm_time_ratio = 1.0;
    lcm_steps.set_ratio(this_phase.therm_time_ratio, false);
  } else if (this_phase.time_coupling == eTIME_COUPLING_SCHEME::DifferentRate) { 
    //for ratios != 1.0, internally adjusts the value to satisfy same and cross-realm dependencies
    lcm_steps.set_ratio(this_phase.therm_time_ratio, false);
  } else if (this_phase.time_coupling == eTIME_COUPLING_SCHEME::DifferentRateConservative) {
    //needs to bound the ratio so the supercycled solver does not become subcycled to avoid conservation issues  
    lcm_steps.set_ratio(this_phase.therm_time_ratio, true);
  } 
  
  //Some coupling phases defined by user can be exploded into a sequence of freeze/non-freeze steps
  if (this_phase.time_coupling == eTIME_COUPLING_SCHEME::Stagger) {
    //will break the span depending on the duration of flow and conduction
    //COND-TODO: throw an error for now, but remember to do the following when enabled:
    //- make sure to add all phases except the last sync one, which should be inserted after running the code below,
    //  since it is the one that will be followed by next phase.
    msg_internal_error("time coupling %d not supported", eTIME_COUPLING_SCHEME::Stagger);
  }
  
  //Peeks into the next phase to determine if :
  //- next phase start to determine the duration of current phase
  //- needs to enable averaging if the next phase will have a solver frozen (FreezeOne or Stagger)
  //- need to include a SameRate phase between this and next phase to get both realms in sync
  BOOLEAN insert_sync_phase = FALSE;
  if (next_phase != nullptr) {
    //Determines the rounding multiple of the lcm_steps_base nearest to the user defined start
    //Note also that start times have already been converted to basetime and can be used directly
    TIMESTEP lcm_steps_base = lcm_steps.base();
    TIMESTEP n_base = std::round((float)(next_phase->start - this_phase.start)/(float)lcm_steps_base);
    // msg_print("%d->%d %d %d, n_base %d", this_phase.start,next_phase->start,next_phase->start,lcm_steps_base,n_base);
    if (n_base <= 0) n_base = 1;
    BOOLEAN enable_averaging = ((next_phase->time_coupling == eTIME_COUPLING_SCHEME::FreezeOne) ||
                                (next_phase->time_coupling == eTIME_COUPLING_SCHEME::Stagger));
    
    if (this_phase.time_coupling == eTIME_COUPLING_SCHEME::FreezeOne) {
      //inserts a sync phase at the end of this phase before freezing to the other realm, no averaging needed
      insert_sync_phase = (enable_averaging && (next_phase->frozen_solver != this_phase.frozen_solver));
      enable_averaging = FALSE;
    }
    
    //If the next phase requires to enable averaging, activates it and makes sure that the span of the phase is enough
    //Note that lcm_steps_base is picked to align the end of the phase to when the coarsest coupled scale is active.
    //This ensures that averaging will be computed for all involved scales and only one averaging window is needed.
    //Works also for even/odd surfels, since both are commed and we extend the averaging window to cover their
    //periodicity.
    if (enable_averaging) {
      TIMESTEP avg_interval_base;
      TIMESTEP phase_base_span = n_base * lcm_steps_base;
      if (next_phase->frozen_solver == eCOUPLED_SOLVER::FlowSolver) {
        TIMESTEP lcm_steps_flow = lcm_steps.flow();
        // msg_print("lcm_steps_flow %d", lcm_steps_flow);
        avg_interval_base = next_phase->flow_avg_interval * cp_info.n_lattice_base_steps;
        round_up_to_nearest_valid_avg_interval(avg_interval_base, lcm_steps_flow, phase_base_span);
        this_phase.flow_avg_interval = avg_interval_base / lcm_steps.lb_base_steps(); //in flow timesteps
        this_phase.conduction_avg_interval = -1;
      } else { //eCOUPLED_SOLVER::ConductionSolver
        TIMESTEP lcm_steps_cond = lcm_steps.cond();
        // msg_print("lcm_steps_cond %d", lcm_steps_cond);
        avg_interval_base = next_phase->conduction_avg_interval * cp_info.n_lattice_base_steps;
        round_up_to_nearest_valid_avg_interval(avg_interval_base, lcm_steps_cond, phase_base_span);
        this_phase.flow_avg_interval = -1;
        this_phase.conduction_avg_interval = avg_interval_base/lcm_steps.cond_base_steps(); //in conduction timesteps
      }
      //Averaging is chosen based on lcm_steps_flow or lcm_steps_cond, while n_base is defined based on lcm_steps_base.
      //Though unlikely, the latter could be smaller than the formers (if other flow solvers aside the lb has larger,
      //this could happen). Thus, does an additional check to determine if the span of the phase should be extended
      TIMESTEP n_base_min = (avg_interval_base-1)/lcm_steps_base + 1; //rounds up, should be 1+ since avg interval >=1
      if (n_base < n_base_min) n_base = n_base_min;
      // msg_print("n_base_min %d", n_base_min);
    } else {
      //just disables averaging
      this_phase.flow_avg_interval = -1;
      this_phase.conduction_avg_interval = -1;
    }
    //Adjust expected start of next phase 
    next_start += n_base * lcm_steps_base;
  } else {
    //last phase, disables averaging and sets next start to last time step
    this_phase.flow_avg_interval = -1;
    this_phase.conduction_avg_interval = -1;
    next_start = TIMESTEP_LAST;
  }

  //Determine the extension of the phase in flow and conduction timesteps now that the next_start has been determined
  BOOLEAN is_flow_frozen, is_cond_frozen;
  if ((this_phase.time_coupling == eTIME_COUPLING_SCHEME::FreezeOne) || 
      (this_phase.time_coupling == eTIME_COUPLING_SCHEME::Stagger)) {
    is_flow_frozen = (this_phase.frozen_solver == eCOUPLED_SOLVER::FlowSolver);
    is_cond_frozen = (this_phase.frozen_solver == eCOUPLED_SOLVER::ConductionSolver);
  } else { 
    is_flow_frozen = FALSE;
    is_cond_frozen = FALSE;
  }
  TIMESTEP this_phase_basetime_span = (next_start - this_phase.start);
  this_phase.num_timesteps_flow = (is_flow_frozen) ? 0 : this_phase_basetime_span / lcm_steps.lb_base_steps();
  this_phase.num_timesteps_conduction = (is_cond_frozen) ? 0 : this_phase_basetime_span / lcm_steps.cond_base_steps();

  //Loads the phase into the valid phases array unless it has been converted into a sequence of phases above
  valid_phases.push_back(this_phase);
  
  //Potentially, insert a sync phase before the next user defined phase if deemed necessary above
  if (insert_sync_phase) {
    sTIME_COUPLING_PHASE sync_phase;
    maybe_load_time_coupling_phase(next_start, lcm_steps, sync_phase, next_phase, valid_phases);
  }
  return FALSE;
}

static void set_radiation_timestep()
{
  if (sim_args.is_rad_base_step_arg_read) {
    auto numToRound = cp_info.n_radiation_base_steps;
    auto multiple = cp_info.n_conduction_base_steps;
    cp_info.n_radiation_base_steps = ((numToRound + multiple - 1) / multiple) * multiple;
  } else {
    cp_info.n_radiation_base_steps = int32_t(cdi_data.radiation_update_ratio) * cp_info.n_conduction_base_steps;
  } 
}

static void maybe_scale_solver_base_steps()
{
  if (cp_info.n_lb_base_steps == cp_info.n_conduction_base_steps) {
    //Any ratio provided will be treated as delaying the solver not being accelerated, i.e. increasing
    //its n_base_timesteps, so no need to do any scaling
    return;
  } 
  
  double scale;
  if (cp_info.n_lb_base_steps < cp_info.n_conduction_base_steps) {
    //Conduction supercycled, we search for the max cond_over_flow time ratio, to check if it would require decreasing
    //the conduction n_base_steps below 1
    double ref_ratio = cp_info.n_conduction_base_steps / cp_info.n_lb_base_steps;
    double max_cond_over_flow_time_ratio = 1.0;
    for (auto & phase : cp_info.time_coupling_phases) {
      if ((phase.time_coupling == eTIME_COUPLING_SCHEME::DifferentRate) &&
          (phase.therm_time_ratio > max_cond_over_flow_time_ratio)) { 
        max_cond_over_flow_time_ratio = phase.therm_time_ratio;
      }
    }
    scale = max_cond_over_flow_time_ratio / ref_ratio;
  } else {
    //Flow supercycled, we search for the min cond_over_flow ratio to check if it would 
    double ref_ratio = cp_info.n_lb_base_steps / cp_info.n_conduction_base_steps;
    double min_cond_over_flow_time_ratio = 1.0;
    for (auto & phase : cp_info.time_coupling_phases) {
      if ((phase.time_coupling == eTIME_COUPLING_SCHEME::DifferentRate) &&
          (phase.therm_time_ratio < min_cond_over_flow_time_ratio)) { 
            min_cond_over_flow_time_ratio = phase.therm_time_ratio;
      }
    }
    scale = min_cond_over_flow_time_ratio * ref_ratio;
  }

  if (scale <= 1.0) {
    //Different rate will not require going below the existing conduction/flow timestep ratio, no need to do any scaling
    return;
  } else {
    //Rounds up to the closest odd value, so we maintain the odd timestep ratio requirement, and converts to timestep
    //(uses same expression than used by sLCM_STEPS_REALMS::set_ratio when ratio is appplied later)
    TIMESTEP base_steps_scale = std::ceil((scale + 1.0)/2.0) * 2.0 - 1.0;
    //Time to scale all solver base timesteps
    cp_info.n_lb_base_steps *= base_steps_scale; 
    cp_info.n_t_base_steps *= base_steps_scale;
    cp_info.n_ke_base_steps *= base_steps_scale;
    cp_info.n_uds_base_steps *= base_steps_scale;
    cp_info.n_conduction_base_steps *= base_steps_scale;
    cp_info.n_radiation_base_steps *= base_steps_scale;
    cp_info.n_particle_base_steps *= base_steps_scale;
    //For the subcycled realm (the one with the smallest physical timestep), there might be a solver that is subcycled
    //with respect to the realm representative solver (lb for flow, condduction for conduction). In this_case, we
    //traverse "n_user_base_steps" for each realm timestep. However, if we reach here, it means that the supercycled
    //solver is accelerated so much that it does multiple timesteps per timestep of the subcycled one. As such, any
    //timestep of the supercycled realm traverses a fraction of the subcycled one, which would imply a fractional
    //m_time. Since we keep all integers, let's set here n_user_base_steps to 1, so we scale down the users's notion of
    //time to provide enough granularity during the phase where we accelerate solvers.
    cp_info.n_user_base_steps = 1;
    //cdi data still needs to be scaled to be converted properly to base-steps, so adjusts scale here accordingly
    cp_info.n_lattice_base_steps *= base_steps_scale;
    // msg_print("adjusted base steps, scale %d, LB(%d) COND(%d)", 
    //            base_steps_scale, cp_info.n_lb_base_steps, cp_info.n_conduction_base_steps);
  }
}

//----------------------------------------------------------------------------
// read_glob_chunk
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_glob_chunk()
{
  if (!enter_cdi_chunk(CDI_CHUNK_TYPE_GLOB))
    msg_internal_error("GLOB chunk not present in CDI file");

  //LGI_CDI_GLOBAL_HEADER header;
  header.tag.id                   = LGI_CDI_GLOBAL_TAG;
  header.tag.length               = lgi_pad_and_encode_record_length(sizeof(header));

  header.thermal_accel.start      = -1;
  header.thermal_accel.repeat     = -1;
  header.thermal_accel.period     = -1;
  header.thermal_accel.interval   = -1;

  // Negative mean_temp indicates the use of the passive scalar solver.
  header.mean_temp                = -1.0;

  header.num_sps                  = total_sps;

  header.cdi_major_version        = m_cdi_info->major_version;
  header.cdi_minor_version        = m_cdi_info->minor_version;

  header.simv_flags               = m_simv_flags;

  //header.n_user_defined_scalars   = cp_info.n_scalars;
  cp_info.enable_film_trajectory_measurement = FALSE; //PR41946
  cp_info.is_particle_solver = (header.simv_flags & CDI_PARTICLE_MODELING_ENABLED) != 0;
  cp_info.is_thermal_particle_solver = FALSE;
  cp_info.is_accretion_simulation =  FALSE; //set true if an ACCR chunk is found in the cdi file.
  cp_info.is_film_solver = FALSE; //This is set true later if any particle materials not used for accretion are liquid.
//#endif

  cp_info.is_shell_conduction_solver = TRUE;

  header.cmdline_flags            = 0;
  if (g_seed_ctl.is_smart_seed()) {
    header.cmdline_flags |= LGI_DO_SMART_SEED;
    

    if (g_seed_ctl.do_extrapolate()) {
      header.cmdline_flags |= LGI_EXTRAPOLATE_SEED;
    }

    if (g_seed_ctl.smart_seed_boundaries()) {
      header.cmdline_flags |= LGI_SMART_SEED_BOUNDARIES;
    }

    if (g_seed_ctl.smart_seed_contact_angle()) {
      header.cmdline_flags |= LGI_SMART_SEED_CONTACT_ANGLE;
    }
  }

  if (cp_info.is_full_checkpoint_restore)
    header.cmdline_flags |= LGI_FULL_CKPT_RESTORE;

  if (cp_info.is_mme_checkpoint_restore)
    header.cmdline_flags |= LGI_MME_CKPT_RESTORE;

  if (!cp_info.is_5g_sim)
    cp_info.is_large_pore_sim = FALSE;

  if (sim_args.no_swirl_model)
    header.cmdline_flags |= LGI_NO_SWIRL_MODEL;
  
  if (sim_args.fluid_turb_solver_1)
    header.cmdline_flags |= LGI_FLUID_TURB_SOLVER_1;
  
  if (sim_args.fixed_temp_walls)
    header.cmdline_flags |= LGI_FIXED_TEMP_WALLS;
  
  if (sim_args.local_vel_fan_model)
    header.cmdline_flags |= LGI_LOCAL_VEL_FAN_MODEL;

  if (sim_args.no_reserve_addr_space)
    header.cmdline_flags |= LGI_NO_RESERVE_ADDR_SPACE;

  if (sim_args.short_mpi_thread_sleep)
    header.cmdline_flags |= LGI_SHORT_MPI_THREAD_SLEEP;
 
  if(sim_args.enable_film_trajectory_measurement) {
    if((header.simv_flags & CDI_PARTICLE_MODELING_ENABLED) != 0 ) {
      msg_warn("Trajectory measurements enabled for film particles."); 
      cp_info.enable_film_trajectory_measurement = TRUE;
      header.cmdline_flags |= LGI_ENABLE_FILM_TRAJECTORY_MEASUREMENTS; //PR41964
    }
  }

  if (sim_args.disable_scalar_diffusivity_bound)
    header.cmdline_flags |= LGI_DISABLE_SCALAR_DIFFUSIVITY_BOUND;

  // time-subcycling-info from simargs
  header.n_lb_base_steps      = cp_info.n_lb_base_steps; 
  header.n_t_base_steps       = cp_info.n_t_base_steps;
  header.n_ke_base_steps      = cp_info.n_ke_base_steps;
  header.n_uds_base_steps     = cp_info.n_uds_base_steps;
  header.n_conduction_base_steps  = cp_info.n_conduction_base_steps;
  header.n_radiation_base_steps  = cp_info.n_radiation_base_steps;
  header.n_particle_base_steps = cp_info.n_particle_base_steps;
  header.n_user_base_steps    = cp_info.n_user_base_steps;
  header.initial_solver_mask  = cp_info.init_solver_mask;
  header.acous_start_time     = cp_info.acous_start_time;
  cp_info.sri_params.reserve(SRI_CHAR_COUNT);

  ccCDI_DO_INNER_CHUNKS(i, "glob", m_cdi_info) {
    switch(cdi_get_type(m_cdi_info)) {

    case CDI_CHUNK_TYPE_GHDR:
      read_global_header();
      break;

    case CDI_CHUNK_TYPE_KESC:
      read_kesc_info();

    case CDI_CHUNK_TYPE_UNIT:
      read_real_to_lattice_conversions();
      break;

    case CDI_CHUNK_TYPE_UUNT: // user units (obsolete)
      break;

    case CDI_CHUNK_TYPE_CPRP: 
      read_characteristic_properties();
      break;

    case CDI_CHUNK_TYPE_THMA:
      read_thermal_acceleration();
      break;

    case CDI_CHUNK_TYPE_CPNT:
      read_checkpoint_info();
      break;
    
    case CDI_CHUNK_TYPE_CSPS:
      read_time_coupling_schemes_info();

    default:
      break;
    }
  }
  
  header.n_user_defined_scalars   = cp_info.n_scalars;  //for LB_UDS, this must be after read_global_header()

  set_radiation_timestep();

  // If phases advancing at different rate, scales the base steps to provide enough granulaty for phase with the largest
  // acceleration
  maybe_scale_solver_base_steps();

  // time-subcycling-info from simargs
  header.n_lb_base_steps       = cp_info.n_lb_base_steps; 
  header.n_t_base_steps        = cp_info.n_t_base_steps;
  header.n_ke_base_steps       = cp_info.n_ke_base_steps;
  header.n_uds_base_steps      = cp_info.n_uds_base_steps;
  header.n_conduction_base_steps  = cp_info.n_conduction_base_steps;
  header.n_radiation_base_steps  = cp_info.n_radiation_base_steps;
  header.n_particle_base_steps = cp_info.n_particle_base_steps;
  header.n_user_base_steps     = cp_info.n_user_base_steps;
  header.initial_solver_mask   = cp_info.init_solver_mask;
  header.acous_start_time      = cp_info.acous_start_time;
  //DFG-TODO: this alignment with base steps won't work if we change the advance rate, remove it
  asINT32 lcm_steps = lcm(cp_info.n_lb_base_steps, cp_info.n_conduction_base_steps);
  header.num_timesteps *= cp_info.n_lattice_base_steps; //in base steps
  header.num_timesteps = round_up_time(header.num_timesteps + 1, lcm_steps) - 1;
  header.num_timesteps /= cp_info.n_user_base_steps; //in user's timesteps
  // header.num_timesteps = ((int) header.num_timesteps / lcm_steps) * lcm_steps;
  cp_info.end_time = header.num_timesteps;
 
  if (cp_info.local_vel_freeze) {
    if (cp_info.sim_ht_type == SRI_HT_ACTIVE_SCALAR)  //after read_characteristic_properties()
      msg_error("Selective velocity freeze does not support coupled temperature/momentum case.");
  }

  if (cp_info.is_melting_solver) {
    header.specific_heat_ratio_liquid_to_solid = cdi_data.specific_heat / cdi_data.specific_heat_solid;
    header.thermal_conductivity_ratio_solid_to_liquid = cdi_data.thermal_conductivity_solid / cdi_data.thermal_conductivity_liquid;

    if (cp_info.is_heat_transfer) {
      if (cp_info.sim_ht_type == SRI_HT_ACTIVE_SCALAR)
        msg_error("Melting solver does not support coupled temperature/momentum case.");
      if (cp_info.is_5g_sim)
        msg_error("Melting solver does not support 5G case.");
      if (cp_info.is_transonic_mach_regime || cp_info.is_high_subsonic_mach_regime)
        msg_error("Melting solver does not support TS or HS case.");
      if (cp_info.freeze_momentum_field || cp_info.local_vel_freeze)
        msg_error("Melting solver does not support Velocity Freeze or Selective Velocity Freeze case.");
      if (cp_info.is_water_vapor_transport)
        msg_error("Melting solver does not support User Defined Scalar case.");
      
      if (header.liquid_type!=CDI_LM_USER_DEFINED)
        msg_error("Melting solver does not support liquid type %d. ", header.liquid_type);      
    } else {
      msg_error("Melting solver only works on heat transfer case.");
    }
  }
  
  // With the introduction of the CDI_5_0_SOLVER flag, we will never enter this
  // conditional because earlier in the code we reject all heat transfer CDI
  // files without this flag set. The code is left here for the historical record.
  if (cp_info.sim_ht_type == SRI_HT_ACTIVE_SCALAR && (m_simv_flags & CDI_5_0_THERMAL_FEEDBACK) == 0) {
    dFLOAT temp = (cdi_data.char_temp > 0.0
                   ? cdi_data.char_temp
                   : (cdi_data.lattice_gamma == OLD_LATTICE_GAMMA ? 0.42 : 1.0/3.0));
    dFLOAT cdi_mach = cdi_data.char_vel / sqrt(cdi_data.lattice_gamma * temp);
    if (cdi_mach < (0.2 + 1e-6)
        && cdi_mach > (0.2 - 1e-6))
      msg_warn("The CDI file \"%s\" is compatible with the coupled temperature/momentum"
               " calculation found in older versions of PowerFLOW. If you recreate the CDI file using"
               " PowerCASE version 5.0 or beyond, the simulation may be able to use a higher"
               " simulation Mach number, which will improve performance.", m_filename);
  }

  sSRI_CHAR_PARAM_DESC sri_param;
  sri_param.param_type = SRI_CHAR_PRESSURE;
  //sri_param.value = cdi_data.char_density * cdi_data.char_temp;
  sri_param.value = cdi_data.char_density * cdi_data.char_temp * cdi_data.lattice_gas_const;
  cp_info.sri_params.push_back(sri_param);

  header.nu_molecular = header.char_vel * header.char_length / header.reynolds_number;
  header.lattice_gas_const = cdi_data.lattice_gas_const;
  header.lattice_gamma = cdi_data.lattice_gamma;

  if (cp_info.is_high_subsonic_mach_regime && header.lattice_gamma == 1.0) {
    msg_error("Lattice gamma can not be unity for high subsonic flow simulation.");
    exit(1);
  }

  header.need_to_scale_viscosity = m_is_pre_3_18_cdi; // See PR43628: for CDI version < 3.18 prepared with pre-4.4a POWERFLOW, we need to rescale viscosity for
                                                      // DNS active scalar case as a workaround for PowerCASE bug reported in PR21724. For newer CDI, the workaround
                                                      // should not be applied in the simulator. 

  cp_info.enable_tangential_shell_conduction = FALSE;
  if(sim_args.enable_tangential_shell_conduction) {
    msg_warn("Tangential heat conduction in shell solver is enabled.");
    cp_info.enable_tangential_shell_conduction = TRUE;
  }

  cp_info.use_implicit_shell_solver = FALSE;
  header.use_implicit_shell_solver = FALSE;
  if (sim_args.use_implicit_shell_solver) {
    msg_warn("Implicit shell solver is enabled.");
    cp_info.use_implicit_shell_solver = TRUE;
    header.use_implicit_shell_solver = TRUE;
    if (!cp_info.enable_tangential_shell_conduction) {
      msg_warn("Turning on tangential shell conduction.");
      cp_info.enable_tangential_shell_conduction = TRUE;
    }
  }

  cp_info.use_implicit_solid_solver = FALSE;
  header.use_implicit_solid_solver = FALSE;
  if (sim_args.use_implicit_solid_solver) {
    msg_warn("Implicit solid solver is enabled.");
    cp_info.use_implicit_solid_solver = TRUE;
    header.use_implicit_solid_solver = TRUE;
  }

  //Time to send info to all the the SPs
  write_header_to_all_sps(header);

  //NOTE:
  //Time coupling phases read here by read_time_coupling_schemes_info() need to be adjusted to ensure that they align
  //properly with the coarsest coupled scale. The CP has all the info it needs here (coarsest_coupled_scale is already
  //known, read from lgi as part of the dgf_control record), but the SPs do not yet. Thus, we defer the call to
  //adjust_and_send_time_coupling_schemes_info() till after the dgf_control record is sent to the SPS.
  
  exit_cdi_chunk();
}


// Print a table to the sim.o containing the coupling phase info.
// This table doesn't expose the user to the notion of basetime unless
// the simulation is started with -simoption
// "-report_phase_basetime".
void print_coupled_phase_table() {

  // If the conduction and flow solvers are on and more than one phase
  // is defined, the start times of each phase is written to the sim.o
  // to let the user know the adjusted start of each of the phases.
  
  if(!cp_info.is_conduction || !cp_info.is_flow)
    return;

  if(cp_info.time_coupling_phases.size() <= 1)
    return;

  cTEXT_TABLE phase_table("Thermal Solver Coupling Phases", //table title
                          {"Phase",
                           "            Phase Type            ",
                           "Start Timestep (flow)",
                           "Start Timestep (conduction)",
                           "Physical Flow Time (s)",
                           "Physical Conduction Time (s)"}, //column labels
                          {cTEXT_TABLE::eVAR_TYPE::INT, //field data types
                           cTEXT_TABLE::eVAR_TYPE::STRING,
                           cTEXT_TABLE::eVAR_TYPE::INT,
                           cTEXT_TABLE::eVAR_TYPE::INT,
                           cTEXT_TABLE::eVAR_TYPE::FLOAT,
                           cTEXT_TABLE::eVAR_TYPE::FLOAT}); 
  if(sim_args.report_coupling_phase_basetime) {
    phase_table.add_column("Start Timestep (basetime)", cTEXT_TABLE::eVAR_TYPE::INT);
  }
  
  ccDOTIMES(nth_phase, cp_info.time_coupling_phases.size()) {
    eTIME_COUPLING_SCHEME::Enum scheme = cp_info.time_coupling_phases[nth_phase].time_coupling;
    eCOUPLED_SOLVER::Enum frozen_solver = cp_info.time_coupling_phases[nth_phase].frozen_solver;
    std::string phase_type = eTIME_COUPLING_SCHEME::GetName(scheme, frozen_solver);
    //replace spaces with dashes so space can be reserved for a field seperator.
    std::replace(phase_type.begin(), phase_type.end(), ' ', '-');
    double flow_duration_seconds, cond_duration_seconds;
    if(nth_phase < cp_info.time_coupling_phases.size() - 1) {
      TIMESTEP num_flowsteps_in_phase = cp_info.time_coupling_phases[nth_phase].num_timesteps_flow;
      TIMESTEP num_condsteps_in_phase = cp_info.time_coupling_phases[nth_phase].num_timesteps_conduction;
      // Compute how much physical time will advance for each solver in this phase in seconds
      flow_duration_seconds = num_flowsteps_in_phase * cp_info.n_lb_base_steps * cdi_data.seconds_per_timestep;
      cond_duration_seconds = num_condsteps_in_phase * cp_info.n_conduction_base_steps * cdi_data.seconds_per_timestep;
    } else {
      //last phase extends to the end of simulation, which can be pushed further during simulation, so report it as unbounded
      flow_duration_seconds = INFINITY;
      cond_duration_seconds = INFINITY;
    }
    
    if(sim_args.report_coupling_phase_basetime) {
      phase_table.print_record(7,
                               nth_phase,
                               phase_type.c_str(),
                               cp_info.sri_solver_phases[nth_phase].start_time_tsflow,
                               cp_info.sri_solver_phases[nth_phase].start_time_tscond,
                               flow_duration_seconds,
                               cond_duration_seconds,
                               cp_info.time_coupling_phases[nth_phase].start);
    } else {
      phase_table.print_record(6,
                               nth_phase,
                               phase_type.c_str(),
                               cp_info.sri_solver_phases[nth_phase].start_time_tsflow,
                               cp_info.sri_solver_phases[nth_phase].start_time_tscond,
                               flow_duration_seconds,
                               cond_duration_seconds);
    }
  }
}

// Disable this if everyone is satisfied with the info being printed
// to the simulator.o instead of time_coupling_phases.o.
#define PRINT_TIME_COUPLING_PHASE_TABLE_TO_SEPARATE_FILE 0

VOID sCP_CDI_READER::adjust_and_send_time_coupling_schemes_info() {

  // Time phases read as part of the glob chunk, but their properties need to be adjusted before being sent to the SPs,  
  // based on the nature of the phase and the periodicity of the coarsest scale participating in the cross-realm comm.
  // Thus, are sent now as a different LGI record
  //
  // For each user-defined phase:
  // - Adjust start of the phase to provide proper alignment between realms and scales
  // - Adjust duration of the phase so it is enough to average coupling parameters
  // - Converts staggered phases into a sequence of freeze & same rate phases
  size_t n_time_coupling_phases = cp_info.time_coupling_phases.size();
  std::vector<sTIME_COUPLING_PHASE> valid_phases;
  valid_phases.reserve(n_time_coupling_phases);

#if PRINT_TIME_COUPLING_PHASE_TABLE_TO_SEPARATE_FILE  
  // Prints phase info into a file if more than one phase is defined to let the user know the 
  // adjusted start of each of the phases.  Waiting on go-ahead from Saif to move this to the sim.o
  FILE *fout = (n_time_coupling_phases>1) ? fopen("time_coupling_phases.o","w") : nullptr;
  if (fout != nullptr) {
    fprintf(fout, "Thermal Solver Coupling Phases\n------------------------------\n");
    fprintf(fout, "Num scales: %d\n", cp_info.num_scales);
    fprintf(fout, "Coarsest coupled scale: %d\n", cp_info.coarsest_coupled_scale);
    fprintf(fout, "Time coupling phases:\n");
  }
#endif
  
  TIMESTEP next_start = 0;
  sLCM_STEPS_REALMS lcm_steps; //initializes internally the different values based on cp_info
  
  //If there is an acoustic switch scheduled, the first timesteps are run as tightly coupled, so postpones the 
  //start of the time coupling phases till after this initial acustic phase to keep things properly aligned.
  if (cp_info.acous_start_time > 0) {
    next_start = cp_info.acous_start_time;
  }
  
  //Enforces that first time phase is SameRate if the user has defined no phases or inital phase with one solver frozen
  BOOLEAN insert_initial_phase = (n_time_coupling_phases==0) ||
                                 (cp_info.time_coupling_phases[0].time_coupling == eTIME_COUPLING_SCHEME::FreezeOne) ||
                                 (cp_info.time_coupling_phases[0].time_coupling == eTIME_COUPLING_SCHEME::Stagger);
  if (insert_initial_phase) {
    sTIME_COUPLING_PHASE *next_phase = (n_time_coupling_phases==0) ? nullptr : &cp_info.time_coupling_phases[0];
    sTIME_COUPLING_PHASE first_phase; //initialized with default parameters as SameRate
    maybe_load_time_coupling_phase(next_start, lcm_steps, first_phase, next_phase, valid_phases);
#if PRINT_TIME_COUPLING_PHASE_TABLE_TO_SEPARATE_FILE
    if (fout != nullptr) {
      fprintf(fout, "Initialization phase added, starting at 0 : %s\n", 
              eTIME_COUPLING_SCHEME::GetName(first_phase.time_coupling).c_str());
    }
#endif
  }

  //First loop through the user-defined phases to scale start time, provided by cdi in lattice time, to base time which
  //is how it is used in the simulator
  for (auto & phase : cp_info.time_coupling_phases) {
    phase.start = phase.start * cp_info.n_lattice_base_steps;
  }
  
  //Second loop through the user-defined phases, loading the valid ones
  for (size_t i = 0; i<n_time_coupling_phases; i++) {
    // stores the start of the phase to be printed later, in case the phase is stored into multiple or an additional
    // sync phase is added when loading the user defined phase
    TIMESTEP this_start = next_start;
    sTIME_COUPLING_PHASE *next_phase = (i < n_time_coupling_phases-1) ? &cp_info.time_coupling_phases[i+1] : nullptr;
    BOOLEAN phase_skipped = maybe_load_time_coupling_phase(next_start, lcm_steps, 
                                                           cp_info.time_coupling_phases[i], next_phase, valid_phases);
#if PRINT_TIME_COUPLING_PHASE_TABLE_TO_SEPARATE_FILE
    if (fout != nullptr) {
      if (phase_skipped) {
        //throw a warning since the phase is too short so it is skipped
        fprintf(fout, "Coupling phase %zu too short, skipped\n", i);
      } else {
        fprintf(fout, "Coupling phase %zu start basetime %d : %s\n", i, this_start, 
                eTIME_COUPLING_SCHEME::GetName(cp_info.time_coupling_phases[i].time_coupling,
                    cp_info.time_coupling_phases[i].frozen_solver).c_str());
      }
    }
#endif
    // if (next_start >= cp_info.end_time) {
    //   if (fout != nullptr && i < n_time_coupling_phases-1) {
    //     fprintf(fout, "reached end of simulation time, no more phases loaded");
    //   } 
    //   break; //this phase already goes to the end of simulation, no need to load any more phases
    // }
  }

#if PRINT_TIME_COUPLING_PHASE_TABLE_TO_SEPARATE_FILE
  if (fout != nullptr) fclose(fout);
#endif
  
  //If there is an initial acoustic phase, we delayed the start of the time coupling phases till after the acoustic
  //switch. Ensure now that valid phases are defined that we start from the begining.
  valid_phases[0].start = 0;

  //Now that we have the phases properly sorted, loads them in cp_info
  n_time_coupling_phases = valid_phases.size();
  cp_info.time_coupling_phases.swap(valid_phases); //valid phases should not be used anymore
  
  //Time to send info to all the the SPs
  LGI_TIME_COUPLING_PHASES_HEADER header;
  header.tag.id = LGI_TIME_COUPLING_PHASES_TAG;
  header.tag.length = sizeof(LGI_TIME_COUPLING_PHASE_REC) * n_time_coupling_phases;
  header.n_time_coupling_phases = n_time_coupling_phases;
  header.coarsest_coupled_scale = cp_info.coarsest_coupled_scale;
  
  write_header_to_all_sps(header);
  
  LGI_TIME_COUPLING_PHASE_REC record;
  for (size_t i = 0; i < n_time_coupling_phases; i++) {
    auto &coupling_phase = cp_info.time_coupling_phases[i];
    record.time_rel_solver         = (asINT32)(coupling_phase.time_rel_solver);
    record.start                   = coupling_phase.start;
    record.time_coupling           = (asINT32)(coupling_phase.time_coupling);
    record.frozen_solver           = (asINT32)(coupling_phase.frozen_solver);
    record.therm_time_ratio        = coupling_phase.therm_time_ratio;
    record.radiation_update_ratio  = coupling_phase.radiation_update_ratio;
    record.flow_duration           = coupling_phase.flow_duration;
    record.flow_avg_interval       = coupling_phase.flow_avg_interval;
    record.conduction_duration     = coupling_phase.conduction_duration;
    record.conduction_avg_interval = coupling_phase.conduction_avg_interval;
    write_to_all_sps(record);
  }

  // Additionally, filled related variables in cp_info depending on the time coupling phases:
  // - Conversions between global (user) and realm specific timesteps are done based on the time increments of each realm
  //   within each time coupling phase. To simplify conversions, the time increments are loaded the realm_phase_time_info
  //   so we can use methods within to do the conversions.
  // - SRI needs also to know the solver phases, which are written to the measurement files, so we load here the info
  //   needed
  // We start by initializing the loader data struct and then the sri time phases
  sREALMS_PHASE_TIME_INFO_LOADER realms_phase_time_info_loader(cp_info.realm_phase_time_info, 
                                                               cp_info.n_user_base_steps, 
                                                               cp_info.n_lb_base_steps, 
                                                               cp_info.n_conduction_base_steps);
  SRI_SOLVER_TIME_PHASE solver_phases = xnew sSRI_SOLVER_TIME_PHASE[n_time_coupling_phases];
  cp_info.n_sri_solver_phases = n_time_coupling_phases;
  cp_info.sri_solver_phases = solver_phases;
  // Now we can traverse the coupling phases adding the info
  for (size_t i = 0; i < n_time_coupling_phases; i++) {
    auto &coupling_phase = cp_info.time_coupling_phases[i];
    realms_phase_time_info_loader.load_phase(coupling_phase);
    //sri does not have a "user time", only flow or conduction, so need to keep track at each phase of the ratio between both realms
    solver_phases[i].coupling_scheme = (SRI_TIME_COUPLING_SCHEME::Enum)coupling_phase.time_coupling;
    solver_phases[i].conduction_to_flow_ts_ratio = realms_phase_time_info_loader.current_cond_to_flow_ts_ratio();
    //if ratio does not change for a realm, we don't actually add a new phase in the realms_phase since nothing change,
    //so need here to convert to ts flow the current phase start rather than pickin git from the realms phases
    solver_phases[i].start_time_tsflow = cp_info.convert_to_ts_flow(coupling_phase.start);
    solver_phases[i].start_time_tscond = cp_info.convert_to_ts_cond(coupling_phase.start);
    //for staggered solvers, duration of the interval for each realm within each cycle in the phase (not used for now)
    solver_phases[i].flow_solver_duration = coupling_phase.flow_duration;
    solver_phases[i].conduction_solver_duration = coupling_phase.conduction_duration;
  }
  // Now that we can do conversions, get the end times for each realm
  cp_info.flow_end_time = cp_info.convert_to_ts_flow(cp_info.end_time);
  cp_info.cond_end_time = cp_info.convert_to_ts_cond(cp_info.end_time);

  //Prints info to simulator.o about any coupled phases in the setup 
  //(needs solver phases above to convert to flow & conduction timesteps)
  print_coupled_phase_table();
}

//----------------------------------------------------------------------------
// read_units_database
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_units_database()
{
  if (!m_is_undb_present) {
    // Some ancient CDI files do not have a units database - these are no longer supported
    msg_internal_error("CDI file does not contain a units database");
    return;
  }
  if (!enter_cdi_chunk(CDI_CHUNK_TYPE_GLOB))
    msg_internal_error("GLOB chunk not present in CDI file");
  ccCDI_DO_INNER_CHUNKS(i, "glob", m_cdi_info) {
    switch(cdi_get_type(m_cdi_info)) {
    case CDI_CHUNK_TYPE_UNIT:
      read_real_to_lattice_conversions();
      break;

    default:
      break;
    }
  }

    exit_cdi_chunk();


  enter_cdi_chunk(CDI_CHUNK_TYPE_UNDB);

  UNITS_STATUS ustatus;

  cp_info.condensed_units_db = cdi_read_condensed_units_db(m_cdi_info);
  cp_info.condensed_units_db->update_units_db(cp_info.units_db);
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  cp_particle_sim.compute_lattice_time_correction_factor(cp_info.units_db);
//#endif

  double specific_enthalpy_slope = units_unit_get_slope(cp_info.units_db, "LatticeSpecificEnthalpy");
  double conduction_base_steps = units_unit_get_slope(cp_info.units_db, "timestepConduction");

  if(conduction_base_steps <= 0) {
    conduction_base_steps = 1;
  }

  if(!sim_args.is_cond_base_step_arg_read) {
    if (conduction_base_steps > 1.0) {
      cp_info.n_conduction_base_steps = std::floor(conduction_base_steps);
    } else {
      cp_info.n_conduction_base_steps = 1;
    }
    maybe_adjust_realms_ts_ratio(cp_info.n_lb_base_steps, cp_info.n_conduction_base_steps);
  }

  if (specific_enthalpy_slope <= 0) {
    // We are using an old CDI file lacking a translation for LatticeSpecificEnthalpy
    specific_enthalpy_slope = (cdi_data.specific_heat / LATTICE_SPECIFIC_HEAT_CP_THERMAL) * cdi_data.kelvins_per_lattice_temp;
    ustatus = units_set_changeable_unit(cp_info.units_db, "LatticeSpecificEnthalpy", specific_enthalpy_slope, 0);
    if (ustatus != UNITS_STATUS_OK) msg_internal_error("Failure to define LatticeSpecificEnthalpy: %s", units_error_string(ustatus));
  }

  if (m_cdi_info->major_version < 3
      || (m_cdi_info->major_version == 3 && m_cdi_info->minor_version < 10)) {
    dFLOAT lat_time_over_lat_time_inc = !cp_info.is_turb ? 1.0 : 0.98;
    ustatus = units_set_changeable_unit(cp_info.units_db, "LatticeTime", lat_time_over_lat_time_inc, 0.0);
    if (ustatus != UNITS_STATUS_OK) msg_internal_error("Failure to define LatticeTime unit: %s", units_error_string(ustatus));         
  }

  UNITS_STATUS units_status;

  // scale the lattice power density value by an additional 6.67% (see PR11084) for old cdi files
  if (m_cdi_info->major_version <= 3 || (m_cdi_info->major_version == 3 && m_cdi_info->minor_version <= 5)) {
    double powerdensity_slope = units_unit_get_slope(cp_info.units_db, "LatticePowerDensity");
    if (powerdensity_slope > 0.0) {
      if (UNITS_STATUS_OK != (units_status = units_set_changeable_unit(cp_info.units_db, "LatticePowerDensity", (powerdensity_slope/1.0667), 0.0))) { 
        if (units_status != UNITS_STATUS_NOT_CHANGEABLE) {             
          msg_internal_error("Failure to adjust LatticePowerDensity: %s", units_error_string(units_status));         
          return;                                                    
        }                                                           
      }
    }
  }

  exit_cdi_chunk();
}

VOID sCP_CDI_READER::maybe_read_glob_chunk_to_define_dimless_units()
{
  // Use the presence of dimlessLength as a changeable unit in the condensed units database
  // as an indicator of whether the CDI file has dimless units in its database.
  CDI_CONDENSED_UNITS_DB cdb = cp_info.condensed_units_db;
  if (!cdb)
    return;

  bool foundDimlessLength = false;
  ccDOTIMES(i, cdb->changeable_units.size()) {
    if (strcmp("dimlessLength", cdb->changeable_units[i].name.c_str()) == 0) {
      foundDimlessLength = true;
      break;
    }
  }

  if (!foundDimlessLength) {
    if (!enter_cdi_chunk(CDI_CHUNK_TYPE_GLOB))
      msg_internal_error("GLOB chunk not present in CDI file");

    UNITS_DB db = cp_info.units_db;

    if (cdb) { // check in case we revert to the old scheme w/o condensed_units_db
      cCDI_GLOB_BUNDLE bundle;
      bundle.read_glob_chunk_into_bundle(m_cdi_info, cp_info.is_5g_sim, cdb);
      bundle.define_dimless_units(cdb);
    }

    exit_cdi_chunk();

    ccDOTIMES(i, cdb->changeable_units.size()) {
      UNITS_STATUS ustatus = units_set_changeable_unit(db, cdb->changeable_units[i].name.c_str(),
                                                       cdb->changeable_units[i].scale, cdb->changeable_units[i].offset);
      // If a unit is no longer changeable (due to a change in units.dat) do not issue an error
      if (ustatus != UNITS_STATUS_OK && ustatus != UNITS_STATUS_NOT_CHANGEABLE)
        msg_internal_error("CDI file contains invalid definition of unit \"%s\": %s", 
                           cdb->changeable_units[i].name.c_str(), units_error_string(ustatus));
    }
  }
}

static STP_PROC vtable_stream_to_sp = STP_PROC_INVALID;

static VOID vtable_stream_write_chars_to_sp(const VOID *buf, size_t n_bytes)
{
  // write_to_all_sps((VOID*)buf, n_bytes);
  lgi_write(g_sp_streams[vtable_stream_to_sp], buf, n_bytes);
}

static std::string get_shared_mem_prefix()
{
    char cp_hostname[MPI_MAX_PROCESSOR_NAME];
    int cp_hostname_length = 0;
    memset(cp_hostname,MPI_MAX_PROCESSOR_NAME,'\0');
    MPI_Get_processor_name(cp_hostname,&cp_hostname_length);

    std::stringstream smem_prefix_ss;
    smem_prefix_ss << cp_hostname << '_' << platform_get_pid() << '-';

    std::string smem_prefix = smem_prefix_ss.str();

    // -1 for the null-terminator, -11 for the '/powerflow_' prefix, -2 for the '_h' on the shared memory header block
    if (smem_prefix.length() > LGI_TABLE_SMEM_NAME_LEN-14) { 
      int diff = smem_prefix.length() - (LGI_TABLE_SMEM_NAME_LEN-14);
      smem_prefix = smem_prefix.substr(diff); // cut-off the front, not the back
    }

    smem_prefix = "/powerflow_" + smem_prefix;

    // this is to make a nice linux-y name
    std::replace( smem_prefix.begin(), smem_prefix.end(), ' ', '_' ); 

    return smem_prefix;
}

VOID sCP_CDI_READER::read_old_tables()
{
  if (!m_is_tabl_present)
    return;

  enter_cdi_chunk(CDI_CHUNK_TYPE_TABL);

  // Old tables don't show up in cp_info.table_descs because they cannot be updated
  cCDI_TABL cdi_tables = cdi_read_tabl(m_cdi_info);

  asINT32 n_tables = cdi_tables->num_tables;

  if (n_tables > 0) {
    LGI_TABLE_COUNT lgi_table_count;
    lgi_table_count.tag.id = LGI_TABLE_COUNT_TAG;
    lgi_table_count.tag.length = lgi_pad_and_encode_record_length(sizeof(lgi_table_count));
    lgi_table_count.n_tables = n_tables;

    std::string smem_prefix = get_shared_mem_prefix();

    write_header_to_all_sps(lgi_table_count);

    ccDOTIMES(n, n_tables) {
      LGI_TABLE lgi_table;
      lgi_table.tag.id = LGI_TABLE_TAG;

      lgi_table.name_length = strlen(cdi_tables->tables[n].name);
      lgi_table.table_length = cdi_tables->tables[n].table_length;

      lgi_table.read_during_sim = FALSE;
      // lgi_table.read_after_meas = FALSE;
      // lgi_table.cdi_meas_window_index = -1;
      // lgi_table.num_meas_frames_between_reads = -1;
      lgi_table.first_interval = -1;
      lgi_table.period = -1;

      STRING msg;
      BOOLEAN status = TRUE;
      EXPRLANG_VALUE vtable = exprlang_construct_v_table(cdi_tables->tables[n].table_string,
                                                         cp_info.units_db,
                                                         DEFAULT_VTABLE_ITERATIONS,
                                                         &status, &msg);

      lgi_table.table_length = exprlang_v_table_serial_length(vtable);

      lgi_table.tag.length = lgi_pad_and_encode_record_length(sizeof(lgi_table)
                                                              + lgi_table.name_length
                                                              + lgi_table.table_length);

      write_header_to_all_sps(lgi_table);
      write_to_all_sps(cdi_tables->tables[n].name, lgi_table.name_length);

#if 0
      char error_msg[200];
      sprintf(error_msg, "Failed to write table \"%s\" to SPs.", cdi_tables->tables[n].name);
      vtable_stream_error_msg = error_msg;
#endif

      std::map<std::string,std::vector<int> >::iterator node_it = cp_info.physical_node_sp_ranks.begin();
      std::map<std::string,std::vector<int> >::iterator node_end = cp_info.physical_node_sp_ranks.end();

      // There is 1 process on each node that is the "owner" of a given table
      // These are assigned in a round-robin fashion. We only send the actual
      // table data to the owner for each node.
      for( ; node_it != node_end; ++node_it) {
        int n_processes_on_node = node_it->second.size();
        int owning_sp_index = n % n_processes_on_node;
        vtable_stream_to_sp = node_it->second[owning_sp_index];
        exprlang_serialize_v_table_to_stream(vtable, vtable_stream_write_chars_to_sp);
      }

      exprlang_v_table_free(vtable);
    }
  }

  // We need to flush any data so that all sps will
  // finish reading table data. Otherwise, deadlocks 
  // will occur between the sps.
  for(int i=0; i<total_sps; i++) {
    lgi_flush(g_sp_streams[i]);
  }

  cdi_destroy_tabl(cdi_tables);
  exit_cdi_chunk();
}

static STRING table_filename_prefix(auINT32 cdi_flags)
{
  if ((cdi_flags & CDI_GTBL_COUPLE_TO_POWERCOOL) == CDI_GTBL_COUPLE_TO_POWERCOOL) {
    return STRING((const char*)"powercool/");
  }
  else if ((cdi_flags & CDI_GTBL_COUPLE_TO_AMESIM)  == CDI_GTBL_COUPLE_TO_AMESIM) {
    return STRING((const char*)"amesim/");
  }
  else {
    return STRING((const char*)"");
  }
}

static VOID check_table_read_against_cdi_table(EXPRLANG_VALUE vtable, EXPRLANG_VALUE vtable_in_cdi, 
                                               cSTRING table_file_name, cSTRING table_cdi_name,
                                               UNITS_DB units_db)
{

  if (exprlang_v_table_num_measurements(vtable) != exprlang_v_table_num_measurements(vtable_in_cdi)) {
    msg_error("The value of \"num_measurements\" in the initial table file read \"%s\" is different from table \"%s\" in the CDI file.",
              table_file_name, table_cdi_name);
  }

  ccDOTIMES(i, exprlang_v_table_num_measurements(vtable)) {
    if (!units_equal_p(units_db, exprlang_v_table_get_measurement_unit(vtable, i), 
                       exprlang_v_table_get_measurement_unit(vtable_in_cdi, i)))
      msg_error("The units of measurement variable index %d in the initial table file read \"%s\" are  different from table \"%s\" in the CDI file.",
                i, table_file_name, table_cdi_name);
  }

#if 0 // need access to exprlang internal data structures to perform the following checks
  udFLOAT slope[3], offset[3];
  ccDOTIMES(i, 3) {
    if (UNITS_STATUS_OK != units_conversion_coefficients(units_db, vtable_in_cdi->grid_units[i], vtable->grid_units[i], &slope[i], &offset[i]))
      msg_error("The length units of grid dimension %d in the initial table file read \"%s\" are not compatible with those from table \"%s\" in the CDI file.",
                i, table_file_name, table_cdi_name);
  }

  ccDOTIMES(i, 3) {
    if (fabs(vtable->base[i] - (vtable_in_cdi->base[i] * slope[i] + offset[i])) > 1e-5) {
      msg_warn("The origin of the initial table file read \"%s\" is different from table \"%s\" in the CDI file.",
               table_file_name, table_cdi_name);
      break;
    }
  }

  ccDOTIMES(i, 3) {
    if (fabs(vtable->incr[i] *vtable->gridsize[i] - ((vtable_in_cdi->incr[i] * vtable_in_cdi->gridsize[i]) * slope[i] + offset[i])) > 1e-5) {
      msg_warn("The dimensions of the initial table file read \"%s\" are different from table \"%s\" in the CDI file.",
               table_file_name, table_cdi_name);
      break;
    }
  }
#endif


}

VOID sCP_CDI_READER::read_turb_vel_tables()
{
  if (!m_is_stbl_present)
      return;

  enter_cdi_chunk(CDI_CHUNK_TYPE_WPTS);
  dFLOAT v[3];
  ccCDI_DO_INNER_CHUNKS(n, "wpts", m_cdi_info) {
    // WPDT is the only sub-chunk, but may change later (?)
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_WPDT) {
      CDI_WPDT wpdt = cdi_read_wpdt(m_cdi_info);
      LGI_TURB_VEL_HEADER turb_vel_header;
      turb_vel_header.nvels = wpdt->n_timesteps;
      turb_vel_header.tag.length = 0;
      turb_vel_header.tag.id = LGI_TURB_SYNTH_VEL_TAG;
      write_header_to_all_sps(turb_vel_header);
      ccDOTIMES(i, wpdt->n_timesteps) {
        std::copy(&wpdt->velocity[3 * i], &wpdt->velocity[3 * i] + 3, v);
        ccDOTIMES(sp, total_sps) {
          lgi_write(g_sp_streams[sp], v, sizeof(v));
        }
      }
    }
  }

  exit_cdi_chunk();
}


static void update_table_hx_index(TABLE_DESC table_desc, const char *table_name) {
    sINT32 matched_hx_index = -1;
    ccDOTIMES(hx, cp_info.all_hx_info.size()) {
#if DEBUG_MONITOR
        msg_print("cp_info.all_hx_info[%d].table_index = %d", hx, cp_info.all_hx_info[hx].table_index);
#endif
        if (cp_info.all_hx_info[hx].table_index == table_desc->table_index) {
            matched_hx_index = hx;
            break;
        }
    }
    if (matched_hx_index < 0) {
        msg_warn("Cannot find hx for table %s", table_name);
    } else {
        table_desc->hx_index = matched_hx_index;
#if DEBUG_MONITOR
        msg_print("Found hx_index (global) %d for table %s", matched_hx_index, table_name);
#endif
    }
}

VOID sCP_CDI_READER::read_new_tables()
{
  if (!m_is_tbls_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_TBLS);

  // N_TABLES is the number of tables except if the TBLS chunk is filled
  // with a NULL chunk.
  asINT32 n_tables = cio_get_count(m_cdi_info->cio_info);

  if (n_tables > TAG_MAX_INDEX_VALUE) {
    msg_error("Use of more than %zu tables is unsupported.", TAG_MAX_INDEX_VALUE);
  }

  if (n_tables > 0) {
    asINT32 nth_table = 0;

    // Setup the shared memory prefix for the tables
    std::string smem_prefix = get_shared_mem_prefix();

    asINT32 n_user_base_steps = cp_info.n_user_base_steps;  //user's timer

    TIMESTEP lattice_to_user_steps = cp_info.n_lattice_base_steps / cp_info.n_user_base_steps;
    
    ccCDI_DO_INNER_CHUNKS(n, "tbls", m_cdi_info) {
      asINT32 type = cdi_get_type(m_cdi_info);
      if (n == 0) {
        if (type == CDI_CHUNK_TYPE_NULL) {
          exit_cdi_chunk();
          return; // no tables present
        } else if (type != CDI_CHUNK_TYPE_GTBL) {
          msg_internal_error("Unrecognized subchunk of TBLS chunk in CDI file.");
        } else {
          cp_info.n_tables = n_tables;
          cp_info.table_descs = cnew sTABLE_DESC [n_tables];

          LGI_TABLE_COUNT lgi_table_count;
          lgi_table_count.tag.id = LGI_TABLE_COUNT_TAG;
          lgi_table_count.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_TABLE_COUNT));
          lgi_table_count.n_tables = n_tables;

          write_header_to_all_sps(lgi_table_count);
        }
      } else if (type != CDI_CHUNK_TYPE_GTBL) {
        msg_internal_error("Unrecognized subchunk of TBLS chunk in CDI file.");
      }

      LGI_TABLE lgi_table = { 0 };
      CDI_GTBL gtbl = cdi_read_gtbl(m_cdi_info);
      BOOLEAN read_during_sim = gtbl->read_during_sim;

      asINT32 table_name_length = strlen(gtbl->name);

      lgi_table.tag.id = LGI_TABLE_TAG;

      lgi_table.name_length = table_name_length;

      strncpy(lgi_table.smem_prefix, smem_prefix.c_str(), LGI_TABLE_SMEM_NAME_LEN);

      TABLE_DESC table_desc = cp_info.table_descs + nth_table;
      table_desc->cdi_flags = gtbl->flags;
      table_desc->table_index = nth_table;

      if (read_during_sim) {
        if (!does_table_filename_include_digit(gtbl->filename))
          msg_error("Table filename \"%s\" (table \"%s\") must include a number if it is to be"
                    " read during simulation.", gtbl->filename, gtbl->name);

        if (gtbl->read_after_meas) {
          if ((gtbl->meas_window_index < 0)
              || (gtbl->meas_window_index >= cp_info.n_cdi_meas_windows))
            msg_internal_error("Invalid meas window index (%d) in CDI table chunk", gtbl->meas_window_index);
          if (gtbl->num_meas_frames_between_reads <= 0)
            msg_internal_error("Invalid period (%d) in CDI table chunk", gtbl->num_meas_frames_between_reads);

          CDI_MEAS_WINDOW cdi_meas_win = &cp_info.cdi_meas_windows[gtbl->meas_window_index];

          // associated meas window is empty (perhaps masked off)
          if (cdi_meas_win->one_window < 0)
            read_during_sim = FALSE;

          TIME_DESC window_time_desc = &cdi_meas_win->fluid_time_desc;// in base steps

          TIMESTEP window_period = window_time_desc->period / n_user_base_steps;  //transfer to user's timesteps
          TIMESTEP window_interval = window_time_desc->interval / n_user_base_steps;  //transfer to user's timesteps
          TIMESTEP window_start = window_time_desc->start / n_user_base_steps;  //transfer to user's timesteps

          table_desc->cdi_meas_window = cdi_meas_win;
          table_desc->num_meas_frames_between_reads = gtbl->num_meas_frames_between_reads;
          table_desc->first_frame = gtbl->first_meas_frame_to_read_after;
          STRING cmd_string = gtbl->command_to_run_before_read;
          if ((gtbl->flags & CDI_GTBL_COUPLE_TO_POWERCOOL) == CDI_GTBL_COUPLE_TO_POWERCOOL ||
              (gtbl->flags & CDI_GTBL_COUPLE_TO_AMESIM) == CDI_GTBL_COUPLE_TO_AMESIM) {
            // for coupling tables do not check command syntax upon table read
            // dummy command will be replaced by appropriate command later (eg.
            // write_heat_{exchanger,condenser}_coupling. Should add other
            // coupling tools to the above bitcheck
          } else {
            if (cmd_string && (cmd_string[0] != '\0')) {
              STRING cmd_string_copy = xnew char [strlen(cmd_string) + 1];

              // This doesn't deal with escaped characters, but such is life
              asINT32 cmdroot_len = strcspn(cmd_string, " ;\n\t|<>&");
              strncpy(cmd_string_copy, cmd_string, cmdroot_len);
              cmd_string_copy[cmdroot_len] = '\0';

              if (!is_cmd_executable_via_system(cmd_string_copy))
                msg_error("The shell command \"%s\" associated with table \"%s\""
                          " either cannot be found or is not executable.",
                          cmd_string_copy, gtbl->name);

              // copy complete cmd string into place
              strcpy(cmd_string_copy, cmd_string);
              trim_trailing_ampersand_from_shell_cmd(cmd_string_copy);
              table_desc->cmd_string = cmd_string_copy;
            }
          }

          lgi_table.period = gtbl->num_meas_frames_between_reads * window_period;   //in user's timesteps
          lgi_table.first_interval = window_start + window_interval
            + (table_desc->first_frame * window_period);  //in user's timesteps
          dFLOAT end_time = (dFLOAT)window_start + (dFLOAT)window_period * (dFLOAT)window_time_desc->repeat;  //in user's timesteps
          lgi_table.end_time = MIN(end_time, sINT32_MAX);

        } else {
          if (gtbl->first_interval < 0)
            msg_internal_error("Invalid first interval (%d) in CDI table chunk", gtbl->first_interval);
          if (gtbl->subsequent_intervals < 0)
            msg_internal_error("Invalid period (%d) in CDI table chunk", gtbl->subsequent_intervals);

          lgi_table.first_interval = gtbl->first_interval * lattice_to_user_steps;  //in user's timesteps
          lgi_table.period = gtbl->subsequent_intervals * lattice_to_user_steps; //in user's timesteps
          lgi_table.end_time = gtbl->end_time * lattice_to_user_steps; //in user's timesteps
        }

        if (lgi_table.first_interval > lgi_table.end_time)
          read_during_sim = FALSE;
      }
      lgi_table.read_during_sim = read_during_sim;

      // Allocate enough space in filename to allow for the last number being incremented
      cSTRING prefix = table_filename_prefix(gtbl->flags);
      asINT32 table_filename_len = strlen(prefix) + strlen(gtbl->filename) + 10 ;
      asINT32 abs_table_filename_len = strlen(prefix) + strlen(gtbl->absolute_filename) + 10 ;
      STRING table_filename = xnew char [ table_filename_len];
      STRING abs_table_filename = xnew char [ abs_table_filename_len];
      strcpy(table_filename, prefix);
      strcat(table_filename, gtbl->filename);
      strcpy(abs_table_filename, gtbl->absolute_filename);

      STRING msg;
      BOOLEAN status = TRUE;
      EXPRLANG_VALUE vtable;
      BOOLEAN is_table_in_cdi = gtbl->table_length > 0;

      table_desc->n_tables_read = 0;
      table_desc->index = xnew char [ table_filename_len]; // more than enough space

      TIMESTEP next_read_time = lgi_table.first_interval;
      BOOLEAN is_hx_table = (((gtbl->flags & CDI_GTBL_COUPLE_TO_POWERCOOL) == CDI_GTBL_COUPLE_TO_POWERCOOL) ||
                             ((gtbl->flags & CDI_GTBL_COUPLE_TO_AMESIM) == CDI_GTBL_COUPLE_TO_AMESIM));
      lgi_table.is_hx_table = is_hx_table;

      if (is_table_in_cdi
          && (!read_during_sim
              || (cp_info.restart_time < lgi_table.first_interval))) {
        if (is_hx_table) {
          // Check if the initial table exists in the simulation run directory,
          // and replace the initial table in the CDI file with the version on
          // disk
          CHARACTER base_table_name[PLATFORM_MAXPATHLEN];
          memset(base_table_name,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
          FILE *fp = NULL;
          if (platform_get_file_base_name(table_filename,base_table_name) &&
              platform_file_present(base_table_name)) {
            fp = fopen(base_table_name, "r");
          }
          if (fp == NULL) {
            vtable = exprlang_construct_v_table(gtbl->table_string,
                                                cp_info.units_db,
                                                DEFAULT_VTABLE_ITERATIONS,
                                                &status, &msg);
          }
          else {
            vtable = exprlang_v_table_read_from_file(fp,
                                                     cp_info.units_db,
                                                     DEFAULT_VTABLE_ITERATIONS,
                                                     &status, &msg);

            EXPRLANG_VALUE vtable_in_cdi = exprlang_construct_v_table(gtbl->table_string,
                                                                      cp_info.units_db,
                                                                      DEFAULT_VTABLE_ITERATIONS,
                                                                      &status, &msg);
            // Check if the table read from disk is consistent with that in the
            // CDI file
            check_table_read_against_cdi_table(vtable, vtable_in_cdi, base_table_name, gtbl->name, cp_info.units_db);

            msg_print("Read initial table file \"%s\" (table \"%s\") at timestep %d",
                      base_table_name, gtbl->name, cp_info.restart_time);
            fclose(fp);
          }
        
          update_table_hx_index(table_desc, gtbl->name);

        }
        else {
          // @@@ We really should let the parser read the string from the file...

          vtable = exprlang_construct_v_table(gtbl->table_string,
                                              cp_info.units_db,
                                              DEFAULT_VTABLE_ITERATIONS,
                                              &status, &msg);
        }
      } else {
        table_desc->n_tables_read = 1;
        if (read_during_sim) {
          // If restoring from checkpoint, figure out how many times to increment the
          // filename.

          // If the initial table did not include data, increment the trailing number
          // in the filename before reading it for the first time.
          asINT32 n_files_to_skip = 0; // !gtbl->init_table_has_data ? 1 : 0
          while ((next_read_time <= cp_info.restart_time)
                 && (next_read_time <= lgi_table.end_time)) {
            n_files_to_skip++;
            next_read_time += lgi_table.period;
          }
          if (n_files_to_skip > 0) {
            increment_table_filename(table_filename, n_files_to_skip, NULL);
            increment_table_filename(abs_table_filename, n_files_to_skip, NULL);
          }
          table_desc->n_tables_read += n_files_to_skip;
        }

        char table_location[PLATFORM_MAXPATHLEN];
        FILE *fp = open_table_file(table_filename, abs_table_filename, table_location);
        if (fp == NULL)
          msg_error("Unable to open initial table file \"%s\" (table \"%s\"): %s.",
                    table_filename, gtbl->name, strerror(errno));

        vtable = exprlang_v_table_read_from_file(fp,
                                                 cp_info.units_db,
                                                 DEFAULT_VTABLE_ITERATIONS,
                                                 &status, &msg);
        fclose(fp);
        msg_print("Read initial table file \"%s\" (table \"%s\") at timestep %d",
                  table_location, gtbl->name, cp_info.restart_time);

        if (is_hx_table) {
          update_table_hx_index(table_desc, gtbl->name);
        }
      }

      if (!status) {
        msg_error("Error in table \"%s\": %s", gtbl->name, msg);
        exit(EXIT_FAILURE);
      }

      table_desc->n_variables = exprlang_v_table_num_measurements(vtable);
      table_desc->variable_units = xnew UNITS_UNIT [ table_desc->n_variables];
      ccDOTIMES(v, table_desc->n_variables) {
        table_desc->variable_units[v] = exprlang_v_table_get_measurement_unit(vtable, v);
      }

      increment_table_filename(table_filename, 1, table_desc->index);
      increment_table_filename(abs_table_filename, 1, NULL);
      lgi_table.next_periodic_read = next_read_time;

      lgi_table.table_length = exprlang_v_table_serial_length(vtable);

      lgi_table.tag.length = lgi_pad_and_encode_record_length(sizeof(lgi_table)
                                                              + table_name_length
                                                              + lgi_table.table_length);
      write_header_to_all_sps(lgi_table);
      write_to_all_sps(gtbl->name, table_name_length);

      std::map<std::string,std::vector<int> >::iterator node_it = cp_info.physical_node_sp_ranks.begin();
      std::map<std::string,std::vector<int> >::iterator node_end = cp_info.physical_node_sp_ranks.end();

      // There is 1 process on each node that is the "owner" of a given table
      // These are assigned in a round-robin fashion. We only send the actual
      // table data to the owner for each node.
      for( ; node_it != node_end; ++node_it) {
        int n_processes_on_node = node_it->second.size();
        int owning_sp_index = table_desc->table_index % n_processes_on_node;
        vtable_stream_to_sp = node_it->second[owning_sp_index];
        exprlang_serialize_v_table_to_stream(vtable, vtable_stream_write_chars_to_sp);
      }

      exprlang_v_table_free(vtable);

      table_desc->name = xnew char [ strlen(gtbl->name) + 1];
      strcpy(table_desc->name, gtbl->name);
      table_desc->filename = table_filename;
      table_desc->abs_filename = abs_table_filename;
      table_desc->read_during_sim = read_during_sim;
      table_desc->period = lgi_table.period;
      table_desc->first_interval = lgi_table.first_interval;
      table_desc->next_periodic_read = lgi_table.next_periodic_read;
      table_desc->end_time = lgi_table.end_time;

      cdi_destroy_gtbl(gtbl);

      nth_table++;
    }
  }

  // We need to flush any data so that all sps will
  // finish reading table data. Otherwise, deadlocks 
  // will occur between the sps.
  for(int i=0; i<total_sps; i++) {
    lgi_flush(g_sp_streams[i]);
  }


  initialize_table_queue();
  exit_cdi_chunk();
}

VOID sCP_CDI_READER::read_eqn_structs()
{
  if (!m_is_vsrs_present || m_skip_eqn_structs)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_VSRS);
  
  // N_STRUCTS is the number of structs except if the VSRS chunk is filled
  // with a NULL chunk.
  asINT32 n_structs = cio_get_count(m_cdi_info->cio_info);

  if (n_structs > 0) {
    LGI_EQN_STRUCT lgi_struct;
    lgi_struct.tag.id = LGI_EQN_STRUCT_TAG;
    lgi_struct.tag.length = 0; // SPs will infer length

    ccCDI_DO_INNER_CHUNKS(i, "vsrs", m_cdi_info) { // loop over structs
      asINT32 type = cdi_get_type(m_cdi_info);
      if (type == CDI_CHUNK_TYPE_NULL) {
        exit_cdi_chunk();
        return; // no structs present
      } else if (type != CDI_CHUNK_TYPE_VSTR) {
        msg_internal_error("Unrecognized subchunk of VSRS chunk in CDI file.");
      }

      asINT32 n_members = cio_get_count(m_cdi_info->cio_info) - 1; // don't count the name subchunk
      lgi_struct.n_members = n_members;

      BOOLEAN found_name = FALSE;

      ccCDI_DO_INNER_CHUNKS(j, "vstr", m_cdi_info) { // loop over members
        asINT32 type = cdi_get_type(m_cdi_info);
        if (type == CDI_CHUNK_TYPE_NAME) {
          CDI_NAME cdi_name = cdi_read_name(m_cdi_info);
          lgi_struct.name_length = strlen(cdi_name->name);
          write_header_to_all_sps(lgi_struct);
          write_to_all_sps(cdi_name->name, strlen(cdi_name->name));
          cdi_destroy_name(cdi_name);
          found_name = TRUE;
        } else if (type == CDI_CHUNK_TYPE_MMBR) {
          if (!found_name)
            msg_internal_error("NAME must be first subchunk of VSTR chunk in CDI file");
          sCDI_MMBR cdi_mmbr;
          cdi_read_mmbr(m_cdi_info, &cdi_mmbr);
          LGI_EQN_STRUCT_MEMBER lgi_member;

          lgi_member.name_length = strlen(cdi_mmbr.name.c_str());
          lgi_member.value = cdi_mmbr.value;
          lgi_member.unit_length = strlen(cdi_mmbr.unit.c_str());
          lgi_member.unit_class_length = strlen(cdi_mmbr.unitclass.c_str());
          write_to_all_sps(lgi_member);
          write_to_all_sps((VOID *)cdi_mmbr.name.c_str(), lgi_member.name_length);
          write_to_all_sps((VOID *)cdi_mmbr.unit.c_str(), lgi_member.unit_length);
          write_to_all_sps((VOID *)cdi_mmbr.unitclass.c_str(), lgi_member.unit_class_length);
        }
      }
    }
  }

  exit_cdi_chunk();
}

VOID sCP_CDI_READER::read_equations()
{
  if (!m_is_eqns_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_EQNS);

  CDI_EQNS cdi_eqns = cdi_read_eqns(m_cdi_info);
  
  asINT32 equations_length = cdi_eqns->equations_length;
  if (equations_length < 0)
    msg_internal_error("Failure reading equations length from CDI file.");

  if (equations_length == 0)
    m_skip_eqn_structs = TRUE;

  if (equations_length > 0) {
    LGI_EQUATIONS_HEADER header;

    header.tag.id = LGI_EQUATIONS_TAG;
    header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_EQUATIONS_HEADER)
                                                         + equations_length);
    header.eqns_length = equations_length;
    header.normal_is_input = TRUE; // ! is_pre_2_1_cdi

    // When creating CDI files prior to version 2.6, ExaCASE screwed up time-varying
    // parameters by evaluating them at t=0 before writing the CDI file. To alert the
    // user of this error, we will not define t as an input to pre 2.6 programs, so
    // that any references to t will cause a fatal error. This works because even if
    // a time-varying paremeter incorrectly is constant in the CDI file, t will appear
    // in the body of the equations.
    header.time_is_input = ! m_is_pre_2_6_cdi;

    // t_ptherm could be an input variable to eqns starting from cdi version 7.23
    header.ptherm_time_is_input = cdi_eqns->pthermTimeAvailable;

    cp_info.is_ptherm_time_input_to_eqns = header.ptherm_time_is_input;
    
    header.are_expr_relative_to_user_csys = m_are_expr_relative_to_user_csys;

    write_header_to_all_sps(header);

    write_to_all_sps(cdi_eqns->equations, equations_length);
  }

  cdi_destroy_eqns(cdi_eqns);

  exit_cdi_chunk();
}


//----------------------------------------------------------------------------
// read_heat_exchangers_and_condensers: heat-exchangers and condensers
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_heat_exchangers_and_condensers()
{
  if (!m_is_coup_present)
    return;

  enter_cdi_chunk(CDI_CHUNK_TYPE_COUP);
  
  asINT32 max_n_hxchs_or_cdsrs = cio_get_count(m_cdi_info->cio_info);
  cp_info.n_hxchs              = 0;
  cp_info.n_cdsrs              = 0;
  cp_info.n_amhxs              = 0;

  if (max_n_hxchs_or_cdsrs <0) {
    exit_cdi_chunk();
    return;
  }

  // store array of ptrs to hxch or cdsr structs
  cp_info.cdi_hxchs = cnew CDI_HXCH [max_n_hxchs_or_cdsrs];
  cp_info.cdi_cdsrs = cnew CDI_CDSR [max_n_hxchs_or_cdsrs];
  cp_info.cdi_amhxs = cnew CDI_AMHX [max_n_hxchs_or_cdsrs];

  sHX_INFO hx_info;
  char results_filename[PLATFORM_MAXPATHLEN];

  ccCDI_DO_INNER_CHUNKS(i, "coup", m_cdi_info) {

    asINT32 cdi_type = cdi_get_type(m_cdi_info);
    char clean_hx_name[PLATFORM_MAXPATHLEN];
    switch(cdi_type) {

    case CDI_CHUNK_TYPE_HXCH:
      hx_info.type = CDI_CHUNK_TYPE_HXCH;
      hx_info.index = cp_info.n_hxchs;
      cp_info.cdi_hxchs[cp_info.n_hxchs] = cdi_read_hxch(m_cdi_info);
      hx_info.table_index = cp_info.cdi_hxchs[cp_info.n_hxchs]->table_index;
      
      convert_hxch_name_slash_to_underscore ( cp_info.cdi_hxchs[cp_info.n_hxchs]->name, clean_hx_name );      
      sprintf(results_filename, "powercool/%s.out", clean_hx_name);
      convert_hxch_part_name_to_base_assembly_name( cp_info.cdi_hxchs[cp_info.n_hxchs]->part_name );
      hx_info.results_filename = strsave(results_filename);
      if (cp_info.restart_time > 0)
        remove_future_lines_from_powercool_results_file(results_filename);

      cp_info.all_hx_info.push_back(hx_info);
      cp_info.n_hxchs++;
      break;

    case CDI_CHUNK_TYPE_AMHX:
      hx_info.type = CDI_CHUNK_TYPE_AMHX;
      hx_info.index = cp_info.n_amhxs;
      cp_info.cdi_amhxs[cp_info.n_amhxs] = cdi_read_amhx(m_cdi_info);
      convert_hxch_part_name_to_base_assembly_name( cp_info.cdi_amhxs[cp_info.n_amhxs]->part_name );
      hx_info.table_index = cp_info.cdi_amhxs[cp_info.n_amhxs]->table_index;
      cp_info.all_hx_info.push_back(hx_info);
      cp_info.n_amhxs++;
      break;

    case CDI_CHUNK_TYPE_CDSR:
      hx_info.type = CDI_CHUNK_TYPE_CDSR;
      hx_info.index = cp_info.n_cdsrs;
      cp_info.cdi_cdsrs[cp_info.n_cdsrs] = cnew sCDI_CDSR;
      if (!cdi_read_condenser(m_cdi_info,cp_info.cdi_cdsrs[cp_info.n_cdsrs])) 
        msg_error("Failed while reading condenser chunk %d",cp_info.n_cdsrs);
      
      hx_info.table_index = cp_info.cdi_cdsrs[cp_info.n_cdsrs]->table_index;

      convert_hxch_name_slash_to_underscore ( cp_info.cdi_cdsrs[cp_info.n_cdsrs]->name, clean_hx_name );
      sprintf(results_filename, "powercool/%s.out", clean_hx_name);
      convert_hxch_part_name_to_base_assembly_name( cp_info.cdi_cdsrs[cp_info.n_cdsrs]->part_name );
      hx_info.results_filename = strsave(results_filename);
      if (cp_info.restart_time > 0)
        remove_future_lines_from_powercool_results_file(results_filename);

      cp_info.all_hx_info.push_back(hx_info);
      cp_info.n_cdsrs++;
      break;
    }   
  }

  exit_cdi_chunk();
}

//----------------------------------------------------------------------------
// read_coordinate_systems
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_coordinate_systems()
{ 
#if 0
  if (!m_is_csys_present)
    return;
#endif
  if (!enter_cdi_chunk(CDI_CHUNK_TYPE_CSYS))
    return;
  
  cp_info.all_csys = cdi_read_csys(m_cdi_info);

  asINT32 n_csys   = cp_info.all_csys->num_coord_systems;

  cp_info.sri_n_csys  += n_csys; // simv chunk is the base csys for measurements
  cp_info.sri_all_csys = EXA_REALLOC_ARRAY(cp_info.sri_all_csys, sSRI_CSYS, cp_info.sri_n_csys);

  if (n_csys > 0) {

    LGI_CSYS_HEADER header;
    header.tag.id   = LGI_CSYS_TAG;

    asINT32 length = sizeof(header);

    ccDOTIMES(i, n_csys) {
      auINT32 namelength = strlen(cp_info.all_csys->coord_systems[i].name);

      length += sizeof(LGI_CSYS);
      length += strlen(cp_info.all_csys->coord_systems[i].name);

      cp_info.sri_all_csys[i+1].name = xnew char [ namelength + 1];
      strcpy(cp_info.sri_all_csys[i+1].name, cp_info.all_csys->coord_systems[i].name);
    }

    header.tag.length = lgi_pad_and_encode_record_length(length);
    header.n_csys     = n_csys;

    write_header_to_all_sps(header);

    ccDOTIMES(n, n_csys) {
      LGI_CSYS csys;
      ccDOTIMES(i,4) {
        ccDOTIMES(j,4) {
          cp_info.sri_all_csys[n+1].xform[i][j] 
            = cp_info.all_csys->coord_systems[n].g_to_l_xform[i][j];
          csys.g_to_l_xform[i][j] = cp_info.all_csys->coord_systems[n].g_to_l_xform[i][j];
          csys.l_to_g_xform[i][j] = cp_info.all_csys->coord_systems[n].l_to_g_xform[i][j];
        }
      }
      csys.name_length = strlen(cp_info.all_csys->coord_systems[n].name);

      write_to_all_sps(csys);
      write_to_all_sps(cp_info.all_csys->coord_systems[n].name, csys.name_length);
    }
  }

  // heat exchangers need the coord systems to compute dominant air-flow direction, so do not destroy
  //cdi_destroy_csys(all_csys);
  exit_cdi_chunk();
}

//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_vehicle_defn()
{
  if (!m_is_vhcl_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_VHCL);
  sCDI_VHCL vhcl;
  cdi_read_vhcl(m_cdi_info, &vhcl);

  cp_info.sri_vehicle_defn = xnew sSRI_VEHICLE_DEFN;
  SRI_VEHICLE_DEFN vehicle_defn = cp_info.sri_vehicle_defn;

  vehicle_defn->csys_index = cdi_csys_to_sri_csys(vhcl.csys_index);
  vehicle_defn->moments_via = vhcl.moments_via;
  vehicle_defn->centerline_dir = vhcl.centerline_direction;
  vehicle_defn->up_dir = vhcl.up_direction;
  vehicle_defn->side_force_dir = vhcl.side_force_direction;
  
  vehicle_defn->is_front_axle_applicable = vhcl.is_front_axle_applicable;
  vehicle_defn->is_rear_axle_applicable = vhcl.is_rear_axle_applicable;
  
  if (vehicle_defn->is_front_axle_applicable) {
    vehicle_defn->front_axle_preferred_csys_index = cdi_csys_to_sri_csys(vhcl.front_axle_preferred_csys_index);
    vehicle_defn->front_axle_origin[0] = vhcl.front_axle_origin[0];
    vehicle_defn->front_axle_origin[1] = vhcl.front_axle_origin[1];
    vehicle_defn->front_axle_origin[2] = vhcl.front_axle_origin[2];
    vehicle_defn->front_axle_dir[0] = vhcl.front_axle_dir[0];
    vehicle_defn->front_axle_dir[1] = vhcl.front_axle_dir[1];
    vehicle_defn->front_axle_dir[2] = vhcl.front_axle_dir[2];
  }

  if (vehicle_defn->is_rear_axle_applicable) {
    vehicle_defn->rear_axle_origin[0] = vhcl.rear_axle_origin[0];
    vehicle_defn->rear_axle_preferred_csys_index = cdi_csys_to_sri_csys(vhcl.rear_axle_preferred_csys_index);
    vehicle_defn->rear_axle_origin[0] = vhcl.rear_axle_origin[0];
    vehicle_defn->rear_axle_origin[1] = vhcl.rear_axle_origin[1];
    vehicle_defn->rear_axle_origin[2] = vhcl.rear_axle_origin[2];
    vehicle_defn->rear_axle_dir[0] = vhcl.rear_axle_dir[0];
    vehicle_defn->rear_axle_dir[1] = vhcl.rear_axle_dir[1];
    vehicle_defn->rear_axle_dir[2] = vhcl.rear_axle_dir[2];
  }

  vehicle_defn->moment_center[0] = vhcl.moment_center[0];
  vehicle_defn->moment_center[1] = vhcl.moment_center[1];
  vehicle_defn->moment_center[2] = vhcl.moment_center[2];
  
  vehicle_defn->floor_point = vhcl.floor_point[sri_direction_to_axis(vehicle_defn->up_dir)];
  
  vehicle_defn->wheelbase = vhcl.wheelbase.value;

  exit_cdi_chunk();
}

//----------------------------------------------------------------------------
// read_cmps_chunk
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_cmps_chunk()
{
  if (!m_is_cmps_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_CMPS);

  asINT32 n_components = cio_get_count(m_cdi_info->cio_info);

  if (n_components <= 0) {
    exit_cdi_chunk();
    return;
  }

  if (n_components != cp_info.num_fluid_components)
    msg_error("The number of fluid components is not consistent between cdi ghdr and cmps chunks");  

  cp_info.s_fluid_components_5g = new sSRI_FLUID_COMPONENT [n_components];

  LGI_CMPS_HEADER header;
  header.tag.id   = LGI_CMPS_TAG;
  header.n_components = n_components;

  asINT32 length = sizeof(header) + n_components * sizeof(LGI_FCMP);
  header.tag.length = lgi_pad_and_encode_record_length(length);

  write_header_to_all_sps(header);

  sCDI_FCMP cdi_fcmp;
  ccCDI_DO_INNER_CHUNKS(i, "fcmp", m_cdi_info) {
    cdi_read_fcmp(m_cdi_info, &cdi_fcmp);
    LGI_FCMP lgi_fcmp;
    lgi_fcmp.eos = cdi_fcmp.equation_of_state;
    lgi_fcmp.viscosity = cdi_fcmp.viscosity;
    lgi_fcmp.molecular_weight = cdi_fcmp.molecular_weight;

    {
      cp_info.s_fluid_components_5g[i].name = new char [strlen(cdi_fcmp.name)+1];
      strcpy(cp_info.s_fluid_components_5g[i].name, cdi_fcmp.name);
      cp_info.s_fluid_components_5g[i].equation_of_state = (SRI_EQUATION_OF_STATE)cdi_fcmp.equation_of_state;
      cp_info.s_fluid_components_5g[i].molecular_weight = cdi_fcmp.molecular_weight;
    }

    write_to_all_sps(lgi_fcmp);
  }
  exit_cdi_chunk();
}

//----------------------------------------------------------------------------
// read_scls_chunk (for LB_UDS, not include 5G UDS)
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_scls_chunk()
{
  //5G UDS will not go to this chuck, because no PowerCase support for 5G yet
  if (!m_is_scls_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_SCLS);

  asINT32 n_scalars = cio_get_count(m_cdi_info->cio_info);
  
  if (n_scalars != cp_info.n_scalars) {
     msg_error("The number of User Defined Scalars is not consistent between cdi ghdr and scls chunks"); 
  }

  cp_info.s_scalar_materials = new sSRI_UDS_INFO [n_scalars];

  LGI_SCLS_HEADER header;
  header.tag.id   = LGI_SCLS_TAG;
  header.n_scalars = n_scalars;

  asINT32 length = sizeof(header) + n_scalars * sizeof(LGI_SCLR);
  header.tag.length = lgi_pad_and_encode_record_length(length);

  write_header_to_all_sps(header);

  sCDI_SCLR cdi_sclr;
  ccCDI_DO_INNER_CHUNKS(i, "sclr", m_cdi_info) {    
    asINT32 index = 0;
    cdi_sclr.ReadFromCDI(m_cdi_info, &index);

    LGI_SCLR lgi_sclr;
    lgi_sclr.diffusion_coefficient = cdi_sclr.diffusion_coefficient.value;
    lgi_sclr.scalar_turb_schmidt_number = cdi_sclr.scalar_turb_schmidt_number.value;
    lgi_sclr.minimum_value = cdi_sclr.minimum_value.value;  
    lgi_sclr.maximum_value = cdi_sclr.maximum_value.value;
    lgi_sclr.allow_negative_values = cdi_sclr.allow_negative_values;
    strncpy(lgi_sclr.scalar_name, cdi_sclr.uds_name.c_str(), LGI_SCALAR_NAME_LEN-1);
    lgi_sclr.scalar_name[LGI_SCALAR_NAME_LEN-1] = '\0';

    {
      cp_info.s_scalar_materials[i].name = strsave(cdi_sclr.uds_name.c_str());
      cp_info.s_scalar_materials[i].unit_class = strsave(cdi_sclr.unit_type.c_str());
      cp_info.s_scalar_materials[i].molecular_weight = cdi_sclr.molecular_weight.value;
    }

    write_to_all_sps(lgi_sclr);
  }
  exit_cdi_chunk();
}


static VOID extract_desired_confidence_level(sCDI_MNTR &cdi_monitor, cMONITOR_SIGNAL_ANALYZER_PARAMS &msap)
{
  switch(cdi_monitor.confidence_level_via) {
  case eCDI_MNTR_CONFIDENCE_LEVEL_VIA::OneStdDev:
    msap.m_desiredConfidenceLevel = MSA_CONFIDENCE_1;
    break;

  case eCDI_MNTR_CONFIDENCE_LEVEL_VIA::TwoStdDevs:
    msap.m_desiredConfidenceLevel = MSA_CONFIDENCE_2;
    break;

  case eCDI_MNTR_CONFIDENCE_LEVEL_VIA::Custom:
    msap.m_desiredConfidenceLevel = MSA_CONFIDENCE_CUSTOM;
    msap.m_customConfidenceLevel = 0.01 * cdi_monitor.custom_confidence_level;
    break;
  default:    // eCDI_MNTR_CONFIDENCE_LEVEL_VIA::Invalid
    msap.m_desiredConfidenceLevel = MSA_CONFIDENCE_INVALID;
    break;
  }
}

static dFLOAT cdi_monitor_time_to_timesteps(eCDI_MNTR_TIME_VIA::Enum time_via, 
                                            dFLOAT monitor_time, 
                                            sCDI_MNTR &cdi_monitor, 
                                            TIMESTEP coupling_period)
{
  if (time_via == eCDI_MNTR_TIME_VIA::Time)
    return monitor_time;
  else if (time_via == eCDI_MNTR_TIME_VIA::NumMonitorFlowPasses) {
    if (cdi_monitor.monitor_flow_pass_via == eCDI_MNTR_FLOW_PASS_VIA::Time)
      return monitor_time * cdi_monitor.monitor_flow_pass.value;
    else if (cdi_monitor.monitor_flow_pass_via == eCDI_MNTR_FLOW_PASS_VIA::NumCouplingPeriods)
      return monitor_time * cdi_monitor.monitor_flow_pass.value * coupling_period;
    else
      return 0;
  } else
    return 0;
}


static VOID extract_stabilization_subwindow(sCDI_MNTR &cdi_monitor, TIMESTEP monitor_period, cMONITOR_SIGNAL_ANALYZER_PARAMS &msap)
{
  dFLOAT period = monitor_period; // For rounding
  dFLOAT stabilization_window = cdi_monitor_time_to_timesteps(cdi_monitor.stabilization_window_via, 
                                                              cdi_monitor.stabilization_window.value,
                                                              cdi_monitor,
                                                              monitor_period);
  msap.m_stabilizationWindow = stabilization_window / period + 0.5;
  if (cdi_monitor.enable_subwindows) {
    dFLOAT subwindow = cdi_monitor_time_to_timesteps(cdi_monitor.subwindow_via, 
                                                     cdi_monitor.subwindow.value,
                                                     cdi_monitor,
                                                     monitor_period);
    msap.m_stabilizationSubWindow = subwindow / period + 0.5;
    switch (cdi_monitor.subwindow_range_limit_via) {
      case eCDI_SUBWINDOW_RANGE_VIA::Value:
        msap.m_subWindowRangeLimitViaFractionMidpoint = false;
        msap.m_stabilizationSubWindowRangeLimit = cdi_monitor.subwindow_range_limit.value;
        break;
      case eCDI_SUBWINDOW_RANGE_VIA::PercentageOfMean:
        msap.m_subWindowRangeLimitViaFractionMidpoint = true;
        msap.m_stabilizationSubWindowRangeLimitFraction = 0.01 * cdi_monitor.subwindow_range_limit.value;
        break;
      case eCDI_SUBWINDOW_RANGE_VIA::PercentageOfAccuracy:
        msap.m_subWindowRangeLimitViaFractionMidpoint = false;
        msap.m_stabilizationSubWindowRangeLimit = 0.01 * cdi_monitor.subwindow_range_limit.value * cdi_monitor.desired_accuracy.value;
        break;
      default:
        break;
    }
  }
}

// Extract signal analyzer params from the cdi_monitor
static VOID extract_signal_analyzer_params(CDI_INFO cdi_info,
                                           sCDI_MNTR &cdi_monitor, 
                                           TIMESTEP monitor_period, 
                                           TIMESTEP monitor_end_of_first_meas_frame, 
                                           cMONITOR_SIGNAL_ANALYZER_PARAMS &msap)
{
  dFLOAT period = monitor_period;  // For rounding
  // Always compute the confidence interval since we need to write the MeanError field in the monitor data file
  msap.m_computeConfidenceInterval = true;
  extract_desired_confidence_level(cdi_monitor, msap);    
  if (cdi_monitor.signal_conv_criteria == eCDI_MNTR_SGNL_CONV_CRIT::StabilizationWindow ||
      cdi_monitor.signal_conv_criteria == eCDI_MNTR_SGNL_CONV_CRIT::ConfidenceIntervalAndStabilizationWindow)
    extract_stabilization_subwindow(cdi_monitor, monitor_period, msap);

  dFLOAT min_averaging_time = cdi_monitor_time_to_timesteps(cdi_monitor.minimum_averaging_time_via, 
                                                            cdi_monitor.minimum_averaging_time.value,
                                                            cdi_monitor,
                                                            monitor_period);
  msap.m_minAveragingTime = min_averaging_time / period + 0.5;

  switch (cdi_monitor.desired_accuracy_via) {
  case eCDI_MNTR_ACCY_VIA::PercentageOfMean:
    msap.m_accuracyType = eMSA_ACCURACY_TYPE::MSA_ACCURACY_FRACTION_MEAN;
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info)) {  //mainline valid range
      msap.m_desiredAccuracyFraction = 0.01 * cdi_monitor.desired_accuracy_percentage.value;
    } else {
      //old cdi values only considered either Value or Percentage of the Mean and only used one parameter,
      //desired_accuracy, to define the accuracy for both types
      msap.m_desiredAccuracyFraction = 0.01 * cdi_monitor.desired_accuracy.value;
    }
    break;
  case eCDI_MNTR_ACCY_VIA::Value:
    msap.m_accuracyType = eMSA_ACCURACY_TYPE::MSA_ACCURACY_VALUE;
    msap.m_desiredAccuracy = cdi_monitor.desired_accuracy.value;
    break;
  case eCDI_MNTR_ACCY_VIA::LesserOfValueAndPercentageOfMean:
    msap.m_accuracyType = eMSA_ACCURACY_TYPE::MSA_ACCURACY_MIN_FRACTION_AND_VALUE;
    msap.m_desiredAccuracy = cdi_monitor.desired_accuracy.value;
    msap.m_desiredAccuracyFraction = 0.01 * cdi_monitor.desired_accuracy_percentage.value;
    break;
  case eCDI_MNTR_ACCY_VIA::GreaterOfValueAndPercentageOfMean:
    msap.m_accuracyType = eMSA_ACCURACY_TYPE::MSA_ACCURACY_MAX_FRACTION_AND_VALUE;
    msap.m_desiredAccuracy = cdi_monitor.desired_accuracy.value;
    msap.m_desiredAccuracyFraction = 0.01 * cdi_monitor.desired_accuracy_percentage.value;
    break;
  default: //case eCDI_MNTR_ACCY_VIA::Invalid:
    //set when no signal convergence is analyzed, which is a valid scenario
    break;
  }

  msap.m_isUserSpecifiedEndInitialTransient = 
    (cdi_monitor.initial_transient_determined_via == eCDI_MNTR_IT_VIA::UserSpecified);

  if (msap.m_isUserSpecifiedEndInitialTransient) {
    dFLOAT user_end_init_transient = cdi_monitor_time_to_timesteps(cdi_monitor.end_of_initial_transient_via, 
                                                                   cdi_monitor.end_of_initial_transient.value,
                                                                   cdi_monitor,
                                                                   monitor_period);
  
    msap.m_userSpecifiedEndInitialTransient = user_end_init_transient / period + 0.5;
  }

  dFLOAT min_init_transient = cdi_monitor_time_to_timesteps(cdi_monitor.minimum_initial_transient_via, 
                                                            cdi_monitor.minimum_initial_transient.value,
                                                            cdi_monitor,
                                                            monitor_period);
  
  msap.m_minInitialTransient = min_init_transient / period + 0.5;

  dFLOAT init_transient_variance_window = cdi_monitor_time_to_timesteps(cdi_monitor.initial_transient_variance_window_via, 
                                                                        cdi_monitor.initial_transient_variance_window.value,
                                                                        cdi_monitor,
                                                                        monitor_period);
 
  msap.m_initialTransientMomentInterval = init_transient_variance_window / period + 0.5;

  // For classic autostop
  if (cdi_monitor.classic_autostop_algorithm) {

    // For classic autostop monitor, the desired confidence level must be INVALID
    if (msap.m_desiredConfidenceLevel != MSA_CONFIDENCE_INVALID)
      msg_internal_error("Desired confidence level should not be set for classic autostop monitor %s!", cdi_monitor.name.c_str());

    msap.m_useClassicAlgorithm = true;
    if (cdi_monitor.monitor_type == eCDI_MNTR_TYPE::HeatExchanger ||
        cdi_monitor.monitor_type == eCDI_MNTR_TYPE::PowerTHERM)
        msap.m_classicHeatExchangerOrPtherm = true;

    // The units for the following 3 variables are converted in maybe_update_classic_autostop_parameters()
    msap.m_classicVarianceGradLimit = cdi_monitor.variance_gradient_limit.value;
    msap.m_classicCreepLimit = cdi_monitor.creep_limit.value;

    if (cdi_monitor.running_average_gradient_limit_via == eCDI_RUN_AVG_GRAD_LIMIT_VIA::Percentage)
      msap.m_classicRunningAvgGradLimit = 0.01 * cdi_monitor.running_average_gradient_limit.value * msap.m_desiredAccuracy;
    else
      msap.m_classicRunningAvgGradLimit = cdi_monitor.running_average_gradient_limit.value;
   
    msap.m_classicRunningAvgGradInterval = cdi_monitor_time_to_timesteps(cdi_monitor.running_average_gradient_interval_via,
                                                                         cdi_monitor.running_average_gradient_interval.value,
                                                                         cdi_monitor,
                                                                         monitor_period) / period;

    dFLOAT stabilization_window = cdi_monitor_time_to_timesteps(cdi_monitor.stabilization_window_via, 
                                                                cdi_monitor.stabilization_window.value,
                                                                cdi_monitor,
                                                                monitor_period);
    msap.m_classicStabilizationWindow = stabilization_window / period;
    msap.m_classicFlowPass = cdi_monitor.monitor_flow_pass.value / period;
    msap.m_classicTimestepsPerFrame = monitor_period;
  }
}

// Functions which are used to sort CDI meas windows according to the monitor/meas_window dependency
static bool sort_cdi_windows_by_rank(const CDI_MEAS_WINDOW lhs, const CDI_MEAS_WINDOW& rhs)
  { return lhs->rank < rhs->rank; }


VOID update_cdi_meas_window_ranks(CDI_MEAS_WINDOW cdi_win, asINT32 rank)
{
  if (cdi_win->rank < rank)
    cdi_win->rank = rank;

  // Check if the monitors attached to this cdi meas window start some other cdi meas windows.
  std::vector<CDI_MEAS_WINDOW> children_cdi_meas_windows;
  if (cdi_win->fluid_meas_window_index >= 0) {
    CP_MEAS_WINDOW cp_meas_win = cp_info.meas_windows[cdi_win->fluid_meas_window_index];
    ccDOTIMES(i, cp_meas_win->m_flow_monitors.size())
    {
      MONITOR monitor = cp_info.monitors[cp_meas_win->m_flow_monitors[i]];
      ccDOTIMES(j, monitor->cdi_meas_windows_to_start.size())
      {
        children_cdi_meas_windows.push_back(monitor->cdi_meas_windows_to_start[j]);
      }
    }
  }

  if (cdi_win->solid_meas_window_index >= 0) {
    CP_MEAS_WINDOW cp_meas_win = cp_info.meas_windows[cdi_win->solid_meas_window_index];
    ccDOTIMES(i, cp_meas_win->m_solid_monitors.size())
    {
      MONITOR monitor = cp_info.monitors[cp_meas_win->m_solid_monitors[i]];
      ccDOTIMES(j, monitor->cdi_meas_windows_to_start.size())
      {
        children_cdi_meas_windows.push_back(monitor->cdi_meas_windows_to_start[j]);
      }
    }
  }

  if (cdi_win->surface_meas_window_index >= 0) {
    CP_MEAS_WINDOW cp_meas_win = cp_info.meas_windows[cdi_win->surface_meas_window_index];
    ccDOTIMES(i, cp_meas_win->m_flow_monitors.size())
    {
      MONITOR monitor = cp_info.monitors[cp_meas_win->m_flow_monitors[i]];
      ccDOTIMES(j, monitor->cdi_meas_windows_to_start.size())
      {
        children_cdi_meas_windows.push_back(monitor->cdi_meas_windows_to_start[j]);
      }
    }
  }

  if (cdi_win->shell_meas_window_index >= 0) {
    CP_MEAS_WINDOW cp_meas_win = cp_info.meas_windows[cdi_win->shell_meas_window_index];
    ccDOTIMES(i, cp_meas_win->m_solid_monitors.size())
    {
      MONITOR monitor = cp_info.monitors[cp_meas_win->m_solid_monitors[i]];
      ccDOTIMES(j, monitor->cdi_meas_windows_to_start.size())
      {
        children_cdi_meas_windows.push_back(monitor->cdi_meas_windows_to_start[j]);
      }
    }
  }

  if (cdi_win->porous_meas_window_index >= 0) {
    CP_MEAS_WINDOW cp_meas_win = cp_info.meas_windows[cdi_win->porous_meas_window_index];
    ccDOTIMES(i, cp_meas_win->m_flow_monitors.size())
    {
      MONITOR monitor = cp_info.monitors[cp_meas_win->m_flow_monitors[i]];
      ccDOTIMES(j, monitor->cdi_meas_windows_to_start.size())
      {
        children_cdi_meas_windows.push_back(monitor->cdi_meas_windows_to_start[j]);
      }
    }
  }

  if (children_cdi_meas_windows.size() == 0)
    return;

  ccDOTIMES(i, children_cdi_meas_windows.size()) {
    update_cdi_meas_window_ranks(children_cdi_meas_windows[i], rank + 1);
  }
}

static BOOLEAN read_flow_monitor_geometry_filter(CP_MEAS_WINDOW cp_window, FLOW_MONITOR& monitor, sCDI_MNTR& cdi_monitor)
{
  asINT32 n_meas_cells = cp_window->n_meas_cells;
  asINT32* part_indices;  // parts for fluid and porous meas window, faces for surface meas window
  eCDI_MNTR_GEOM_FILTER_SENSE::Enum part_filtering = eCDI_MNTR_GEOM_FILTER_SENSE::Invalid; // region_filtering for fluid and porous meas window, face_filtering for surface meas window_
  std::vector<cdiINT32> cdi_monitor_parts;  // regions for fluid and porous meas window, faces for surface meas window
                                             // Could be either included parts or excluded parts, dependig on part_filtering
  std::vector<bool>* is_meas_cell_in_monitor;      // m_is_meas_cell_in_monitor for fluid and porous meas window, m_is_meas_surfel_in_monitor for surface meas window
  if (cp_window->meas_window_type == LGI_SURFACE_WINDOW || cp_window->meas_window_type == LGI_SAMPLING_SURFACE_WINDOW) {
    part_filtering = cdi_monitor.face_filtering;
    cdi_monitor_parts = cdi_monitor.face_filtering_tree.ExpandSelection(cp_info.partitions());
    is_meas_cell_in_monitor = &monitor->m_is_meas_surfel_in_monitor;
    if (cp_window->is_composite) {
      CP_COMPOSITE_SURFACE_MEAS_WINDOW surface_window = static_cast<CP_COMPOSITE_SURFACE_MEAS_WINDOW> (cp_window);
      part_indices = surface_window->m_faces;
    } else if (cp_window->is_probe) { // Include all meas cells for probe measurement
      ccDOTIMES(i, n_meas_cells)
        is_meas_cell_in_monitor->push_back(true);
      return TRUE;
    } else {
      msg_internal_error("Monitor only supports composite or probe measurement windows.");
    }
  } else { // Fluid or porous media meas window
    part_filtering = cdi_monitor.region_filtering;
    cdi_monitor_parts = cdi_monitor.region_filtering_tree.ExpandSelection(cp_info.partitions());
    is_meas_cell_in_monitor = &monitor->m_is_meas_cell_in_monitor;
    if (cp_window->is_composite) {
      CP_COMPOSITE_FLUID_MEAS_WINDOW fluid_window = static_cast<CP_COMPOSITE_FLUID_MEAS_WINDOW> (cp_window);
      part_indices = fluid_window->m_part_indices;
    } else if (cp_window->is_probe) { // Include all meas cells for probe measurement
      ccDOTIMES(i, n_meas_cells)
        is_meas_cell_in_monitor->push_back(true);
      return TRUE;
    } else {
      msg_internal_error("Monitor only supports composite or probe measurement windows.");
    }
  }

  if (part_filtering == eCDI_MNTR_GEOM_FILTER_SENSE::Include) {
    ccDOTIMES(i, n_meas_cells) {
      if (std::find(cdi_monitor_parts.begin(), cdi_monitor_parts.end(), part_indices[i]) != cdi_monitor_parts.end()) 
        is_meas_cell_in_monitor->push_back(true);
      else
        is_meas_cell_in_monitor->push_back(false);
    }

  } else if (part_filtering == eCDI_MNTR_GEOM_FILTER_SENSE::Exclude) { 
    ccDOTIMES(i, n_meas_cells) {
      if (std::find(cdi_monitor_parts.begin(), cdi_monitor_parts.end(), part_indices[i]) != cdi_monitor_parts.end()) 
        is_meas_cell_in_monitor->push_back(false);
      else
        is_meas_cell_in_monitor->push_back(true);
    }
  } else {
    msg_internal_error("Monitor \"%s\" geometry filter is invalid.", cdi_monitor.name.c_str());
  }

  if (std::find(is_meas_cell_in_monitor->begin(), is_meas_cell_in_monitor->end(), true) == is_meas_cell_in_monitor->end())
    return FALSE;
  else
    return TRUE;
}

static BOOLEAN read_solid_monitor_geometry_filter(CP_MEAS_WINDOW cp_window, SOLID_MONITOR& monitor, sCDI_MNTR& cdi_monitor)
{
  asINT32 n_meas_cells = cp_window->n_meas_cells;
  asINT32* part_indices;  // parts for solid meas window, faces for shell meas window
  eCDI_MNTR_GEOM_FILTER_SENSE::Enum part_filtering = eCDI_MNTR_GEOM_FILTER_SENSE::Invalid; // region_filtering for solid meas window, face_filtering for shell meas window_
  std::vector<cdiINT32> cdi_monitor_parts;  // regions for solid meas window, faces for shell meas window
                                             // Could be either included parts or excluded parts, dependig on part_filtering
  std::vector<bool>* is_meas_cell_in_monitor;      // m_is_meas_cell_in_monitor for solid meas window, m_is_meas_surfel_in_monitor for shell meas window
  if (cp_window->meas_window_type == LGI_SHELL_WINDOW || cp_window->meas_window_type == LGI_SAMPLING_SHELL_WINDOW) {
    part_filtering = cdi_monitor.face_filtering;
    cdi_monitor_parts = cdi_monitor.face_filtering_tree.ExpandSelection(cp_info.partitions());
    is_meas_cell_in_monitor = &monitor->m_is_meas_surfel_in_monitor;
    if (cp_window->is_composite) {
      CP_COMPOSITE_SURFACE_MEAS_WINDOW surface_window = static_cast<CP_COMPOSITE_SURFACE_MEAS_WINDOW> (cp_window);
      part_indices = surface_window->m_faces;
    } else if (cp_window->is_probe) { // Include all meas cells for probe measurement
      ccDOTIMES(i, n_meas_cells)
        is_meas_cell_in_monitor->push_back(true);
      return TRUE;
    } else {
      msg_internal_error("Monitor only supports composite or probe measurement windows.");
    }
  } else { // Solid meas window
    part_filtering = cdi_monitor.region_filtering;
    cdi_monitor_parts = cdi_monitor.region_filtering_tree.ExpandSelection(cp_info.partitions());
    is_meas_cell_in_monitor = &monitor->m_is_meas_cell_in_monitor;
    if (cp_window->is_composite) {
      CP_COMPOSITE_FLUID_MEAS_WINDOW solid_window = static_cast<CP_COMPOSITE_FLUID_MEAS_WINDOW> (cp_window);
      part_indices = solid_window->m_part_indices;
    } else if (cp_window->is_probe) { // Include all meas cells for probe measurement
      ccDOTIMES(i, n_meas_cells)
        is_meas_cell_in_monitor->push_back(true);
      return TRUE;
    } else {
      msg_internal_error("Monitor only supports composite or probe measurement windows.");
    }
  }

  if (part_filtering == eCDI_MNTR_GEOM_FILTER_SENSE::Include) {
    ccDOTIMES(i, n_meas_cells) {
      if (std::find(cdi_monitor_parts.begin(), cdi_monitor_parts.end(), part_indices[i]) != cdi_monitor_parts.end()) 
        is_meas_cell_in_monitor->push_back(true);
      else
        is_meas_cell_in_monitor->push_back(false);
    }

  } else if (part_filtering == eCDI_MNTR_GEOM_FILTER_SENSE::Exclude) { 
    ccDOTIMES(i, n_meas_cells) {
      if (std::find(cdi_monitor_parts.begin(), cdi_monitor_parts.end(), part_indices[i]) != cdi_monitor_parts.end()) 
        is_meas_cell_in_monitor->push_back(false);
      else
        is_meas_cell_in_monitor->push_back(true);
    }
  } else {
    msg_internal_error("Monitor \"%s\" geometry filter is invalid.", cdi_monitor.name.c_str());
  }

  if (std::find(is_meas_cell_in_monitor->begin(), is_meas_cell_in_monitor->end(), true) == is_meas_cell_in_monitor->end())
    return FALSE;
  else
    return TRUE;
}

static TIMESTEP get_classic_signal_processing_interval(sCDI_MNTR& cdi_monitor,
                                                       TIMESTEP monitor_period)
{
  TIMESTEP classic_signal_processing_interval = -1;
  if (cdi_monitor.signal_processing_period_via == eCDI_MNTR_TIME_VIA::Time)
    classic_signal_processing_interval = cdi_monitor.signal_processing_period.value / (dFLOAT)monitor_period + 0.5;
  else if (cdi_monitor.signal_processing_period_via ==  eCDI_MNTR_TIME_VIA::NumMonitorFlowPasses)
    classic_signal_processing_interval = (cdi_monitor.signal_processing_period.value * cdi_monitor.monitor_flow_pass.value) / (dFLOAT)monitor_period + 0.5;
  else
    msg_internal_error("Signal processing period via is invalid!");
  return classic_signal_processing_interval;
}

static VOID validate_cmdline_autostop_on_off_options()
{
  AUTOSTOP_ON_OFF_OPTION disable_option = cp_info.cmdline_autostop_off_monitors;
  AUTOSTOP_ON_OFF_OPTION enable_option = cp_info.cmdline_autostop_on_monitors;
  while (disable_option != NULL) {
    BOOLEAN found = FALSE;
    ccDOTIMES(i, cp_info.monitors.size()) {
      if (strcmp(disable_option->monitor_name, cp_info.monitors[i]->m_name) == 0) {
        found = TRUE;
        break;
      }
    }
    if (!found)
      msg_warn("--autostop_off option refers to an invalid monitor \"%s\"",
                disable_option->monitor_name);
    disable_option = disable_option->next;
  }

  while (enable_option != NULL) {
    BOOLEAN found = FALSE;
    ccDOTIMES(i, cp_info.monitors.size()) {
      if (strcmp(enable_option->monitor_name, cp_info.monitors[i]->m_name) == 0) {
        found = TRUE;
        break;
      }
    }
    if (!found)
      msg_warn("--autostop_on option refers to an invalid monitor \"%s\"",
                enable_option->monitor_name);
    enable_option = enable_option->next;
  }
}


static VOID maybe_disable_autostop_via_autostop_all_off_option()
{
  if (sim_args.autostop_all_off) {
    ccDOTIMES(i, cp_info.monitors.size()) 
    {
      cp_info.monitors[i]->m_end_sim_if_converged = FALSE;
      cp_info.monitors[i]->m_disabled_autostop_by_on_off = TRUE;
    }
#if DEBUG_MONITOR
    msg_print("Disabled autostop for all monitors.");
#endif
  }
}

static VOID disable_enable_autostop_via_autostop_on_off_options()
{
  asINT32 n_monitors = cp_info.monitors.size();

  std::string msg;
  BOOLEAN first = TRUE;
  if (cp_info.has_autostop_off_list) {
    AUTOSTOP_ON_OFF_OPTION autostop_off = cp_info.cmdline_autostop_off_monitors;

    while (autostop_off != NULL) {
      if (!first)
        msg += ", ";
      first = FALSE;
      ccDOTIMES(i, n_monitors) {
        if (strcmp(autostop_off->monitor_name, cp_info.monitors[i]->m_name) == 0) {
          cp_info.monitors[i]->m_end_sim_if_converged = FALSE;
          cp_info.monitors[i]->m_disabled_autostop_by_on_off = TRUE;
          msg += cp_info.monitors[i]->m_name;
          break;
        }
      }
      autostop_off = autostop_off->next;
    }
#if DEBUG_MONITOR
    msg_print("Monitors with autostop disabled: %s", msg.c_str());
#endif
  } 
 
  first = TRUE;
  msg.clear();
  if (cp_info.has_autostop_on_list) {
    AUTOSTOP_ON_OFF_OPTION autostop_on = cp_info.cmdline_autostop_on_monitors;

    // Set all monitors to autostop off first if autostop_on option is used
    ccDOTIMES(i, n_monitors) {
      cp_info.monitors[i]->m_end_sim_if_converged = FALSE;
      cp_info.monitors[i]->m_enabled_autostop_by_on_off = FALSE;
    }

    while (autostop_on != NULL) {
      if (!first)
        msg += ", ";
      first = FALSE;
      ccDOTIMES(i, n_monitors) {
        if (strcmp(autostop_on->monitor_name, cp_info.monitors[i]->m_name) == 0) {
          if (!cp_info.monitors[i]->m_analyze_convergence) {
            msg_warn("Monitor %s is not enabled for autostop since its convergence is not analyzed.", cp_info.monitors[i]->m_name);
          } else {
            cp_info.monitors[i]->m_end_sim_if_converged = TRUE;
            cp_info.monitors[i]->m_enabled_autostop_by_on_off = TRUE;
            msg += cp_info.monitors[i]->m_name;
          }
          break;
        }
      }
      autostop_on = autostop_on->next;
    }
#if DEBUG_MONITOR
    msg_print("Monitors with autostop enabled: %s", msg.c_str());
#endif
  }
}

//----------------------------------------------------------------------------
// read_calibration_chunk_
//----------------------------------------------------------------------------
cBOOLEAN sCP_CDI_READER::read_calibration_parameters()
{
  if(!m_is_clbr_present)
    return FALSE;
  enter_cdi_chunk(CDI_CHUNK_TYPE_CLBR);
  CDI_CLBR clbr = cdi_read_clbr(m_cdi_info);
  clbr->calibration_iterations++;
  if(!cp_info.is_full_checkpoint_restore) {
    TIMESTEP coarsest_timestep = 1 << (cp_info.num_scales - 1);
    if(cp_info.end_time % coarsest_timestep != 0) {
      cp_info.end_time = coarsest_timestep * (cp_info.end_time / coarsest_timestep + 1);
      msg_warn("End time will be adjusted to the nearest multiple of coarsest timestep %d", cp_info.end_time);
    }
    cp_info.end_time *= clbr->calibration_iterations;
  }
  cp_info.calibration_params.init(clbr);
  exit_cdi_chunk();
  return TRUE;
}

//----------------------------------------------------------------------------
// read_monitors
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_monitors()
{
  if (!m_is_mnts_present)
    return;

  enter_cdi_chunk(CDI_CHUNK_TYPE_MNTS);
  
  cCDI_MONITORS cdi_monitors;
  cdi_monitors.ReadFromCDI(m_cdi_info);

  TIMESTEP monitor_period = 0;
  TIMESTEP monitor_interval = 0;
  TIMESTEP monitor_end_of_first_meas_frame = 0;
  TIMESTEP monitor_max_period = 0; // for PT monitors with variable PT coupling phases


  std::map<asINT32, asINT32> cdi_monitor_index_to_sim_index;
  ccDOTIMES(i, cdi_monitors.m_monitors.size()) {
    sCDI_MNTR &cdi_monitor = cdi_monitors.m_monitors[i]; 
#if DEBUG_MONITOR  
    msg_print("Monitor %s type %d", cdi_monitor.name.c_str(), cdi_monitor.monitor_type);
#endif

    BOOLEAN analyze_signal = FALSE;
    BOOLEAN analyze_signal_convergence = FALSE;
    switch(cdi_monitor.signal_analysis) {
      case eCDI_MNTR_SIGNAL_ANALYSIS::RecordSignalOnly:
        break;
      case eCDI_MNTR_SIGNAL_ANALYSIS::DetectInitialTransientOnly:
        analyze_signal = TRUE;
        break;
      case eCDI_MNTR_SIGNAL_ANALYSIS::DetectInitialTransientAndSignalConvergence:
        analyze_signal = TRUE;
        analyze_signal_convergence = TRUE;
      default:
        break;
    }

    cMONITOR_SIGNAL_ANALYZER_PARAMS msap;

    switch(cdi_monitor.monitor_type) {
      case eCDI_MNTR_TYPE::Flow:
      {
        CDI_MEAS_WINDOW cdi_window = &cp_info.cdi_meas_windows[cdi_monitor.meas_window_index];
        monitor_period = cdi_window->fluid_time_desc.period;
        monitor_interval = cdi_window->fluid_time_desc.interval;
        monitor_end_of_first_meas_frame = cdi_window->fluid_time_desc.start + monitor_period;      
        
        if (analyze_signal) 
          extract_signal_analyzer_params(m_cdi_info, cdi_monitor, monitor_period, monitor_end_of_first_meas_frame, msap);
  
        FLOW_MONITOR monitor = new sFLOW_MONITOR(cdi_monitor.name.c_str(), 
                                                 analyze_signal, 
                                                 analyze_signal_convergence,
                                                 FALSE,
                                                 cdi_monitor.automatically_stop,
                                                 cdi_monitor.flow_variable,
                                                 cdi_monitor.variable_source,
                                                 cdi_monitor.use_wheelbase,
                                                 cdi_monitor.meas_window_index,
                                                 msap);  
        monitor->m_cdi_meas_win = cdi_window;
        monitor->m_period = monitor_period;
        monitor->m_interval = monitor_interval;
        monitor->m_end_of_first_meas_frame = monitor_end_of_first_meas_frame;
        if (cdi_monitor.classic_autostop_algorithm && analyze_signal)
          monitor->m_classic_signal_processing_interval = get_classic_signal_processing_interval(cdi_monitor, monitor_period);

        monitor->m_csys_index = cdi_csys_to_sri_csys(cdi_monitor.reference_csys);
        ccDOTIMES(j, 3) {
          monitor->m_ref_point[j] = cdi_monitor.reference_point[j];
        }

        // Check if the cdi meas windows are excluded in the exaqsub command line
        if (cdi_window->disabled_by_meas_include_exclude ) {
          msg_warn("Monitor \"%s\" refers to a cdi meas window which is excluded by exaqsub, thus it will be inactive.", cdi_monitor.name.c_str());
          cdi_monitor_index_to_sim_index[i] = -1;
          continue;
        }

        if (cdi_monitor.variable_source == eCDI_MNTR_FLOW_VAR_SRC::Fluid)
        {
          if (cdi_window->fluid_meas_window_index < 0) {
            msg_warn("Monitor \"%s\" refers to an invalid fluid meas window (may contain no meas cells), thus it will be inactive.", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          }
          CP_MEAS_WINDOW cp_window = cp_info.meas_windows[cdi_window->fluid_meas_window_index];
          if (!read_flow_monitor_geometry_filter(cp_window, monitor, cdi_monitor)) {
            msg_warn("Monitor \"%s\" does not have any geometry selected, thus it will be inactive!", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          }

          // Check if the cp window is a sampling surface measurement window. If so, then the meas type is fluid meas for this monitor in the (old) cdi file, we
          // should change the meas type to surface meas so that it is consistent with the new cdi format.
          
          if (cp_window->meas_window_type == LGI_SAMPLING_SURFACE_WINDOW) {
            monitor->m_var_source = eCDI_MNTR_FLOW_VAR_SRC::Surface; 
            monitor->m_surface_var_type = monitor->m_fluid_var_type;
          }
        }
        if (cdi_monitor.variable_source == eCDI_MNTR_FLOW_VAR_SRC::FanAndPorous)
        {
          if (cdi_window->porous_meas_window_index < 0) {
            msg_warn("Monitor \"%s\" refers to an invalid porous media or fan meas window (may contain no meas cells), thus it will be inactive.", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          }
          CP_MEAS_WINDOW cp_window = cp_info.meas_windows[cdi_window->porous_meas_window_index];
          //read_monitor_geometry_filter(cp_window, monitor, cdi_monitor);
          if (!read_flow_monitor_geometry_filter(cp_window, monitor, cdi_monitor)) {
            msg_warn("Monitor \"%s\" does not have any geometry selected, thus it will be inactive!", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          }

        }
        if (cdi_monitor.variable_source == eCDI_MNTR_FLOW_VAR_SRC::Surface)
        {
          if (cdi_window->surface_meas_window_index < 0) {
            msg_warn("Monitor \"%s\" refers to an invalid surface meas window (may contain no meas cells), thus it will be inactive.", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          }
          CP_MEAS_WINDOW cp_window = cp_info.meas_windows[cdi_window->surface_meas_window_index];
          //read_monitor_geometry_filter(cp_window, monitor, cdi_monitor);
          if (!read_flow_monitor_geometry_filter(cp_window, monitor, cdi_monitor)) {
            msg_warn("Monitor \"%s\" does not have any geometry selected, thus it will be inactive!", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          }
        }

        if (cdi_monitor.variable_source == eCDI_MNTR_FLOW_VAR_SRC::SurfacePlusFanAndPorous)
        {
          bool is_porous_meas_window_valid = true;
          bool is_surface_meas_window_valid = true;
          bool is_porous_geometry_valid = true;
          bool is_surface_geometry_valid = true;

          is_porous_meas_window_valid = (cdi_window->porous_meas_window_index >= 0);
          if (is_porous_meas_window_valid)
            is_porous_geometry_valid = read_flow_monitor_geometry_filter(cp_info.meas_windows[cdi_window->porous_meas_window_index], monitor, cdi_monitor);
          is_surface_meas_window_valid = (cdi_window->surface_meas_window_index >= 0);
          if (is_surface_meas_window_valid)
            is_surface_geometry_valid = read_flow_monitor_geometry_filter(cp_info.meas_windows[cdi_window->surface_meas_window_index], monitor, cdi_monitor);

          if (!is_porous_meas_window_valid && !is_surface_meas_window_valid) {
            msg_warn("Monitor \"%s\" refers to invalid surface and porous media or fan meas windows (may contain no meas cells), thus it will be inactive.", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          } else if (!is_porous_meas_window_valid) {
            msg_warn("Monitor \"%s\" refers to an invalid porous media or fan meas window (may contain no meas cells), thus only the surface data will be used.", cdi_monitor.name.c_str());
            monitor->m_var_source = eCDI_MNTR_FLOW_VAR_SRC::Surface;
            if (!is_surface_geometry_valid) {
              msg_warn("Monitor \"%s\" does not have any geometry selected for surface measurement, thus it will be inactive.", cdi_monitor.name.c_str());
              cdi_monitor_index_to_sim_index[i] = -1;
            }
          } else if (!is_surface_meas_window_valid) {
            msg_warn("Monitor \"%s\" refers to an invalid surface meas window (may contain no meas cells), thus only the fan or porous media data will be used.", cdi_monitor.name.c_str());
            monitor->m_var_source = eCDI_MNTR_FLOW_VAR_SRC::FanAndPorous;
            if (!is_porous_geometry_valid) {
              msg_warn("Monitor \"%s\" does not have any geometry selected for fan or porous media measurement, thus it will be inactive.", cdi_monitor.name.c_str());
              cdi_monitor_index_to_sim_index[i] = -1;
            }
          } else {  // both surface and porous media measurement windows are valid
            if (!is_surface_geometry_valid && !is_porous_geometry_valid) {
              msg_warn("Monitor \"%s\" does not have any geometry selected for surface and fan or porous media measurement, thus it will be inactive.", cdi_monitor.name.c_str());
              cdi_monitor_index_to_sim_index[i] = -1;
            } else if (!is_porous_geometry_valid) {
              msg_warn("Monitor \"%s\" does not have any geometry selected for fan or porous media measurement, thus only the surface data will be used.", cdi_monitor.name.c_str());
              monitor->m_var_source = eCDI_MNTR_FLOW_VAR_SRC::Surface;
            } else if (!is_surface_geometry_valid) {
              msg_warn("Monitor \"%s\" does not have any geometry selected for surface measurement, thus only the fan or porous media data will be used.", cdi_monitor.name.c_str());
              monitor->m_var_source = eCDI_MNTR_FLOW_VAR_SRC::FanAndPorous;
            }
          }
        }
  
        cdi_monitor_index_to_sim_index[i] = cp_info.monitors.size();
        cp_info.monitors.push_back(monitor);
        cp_info.flow_monitors.push_back(monitor);
        break;
      }      
      case eCDI_MNTR_TYPE::Solid:
      {
        CDI_MEAS_WINDOW cdi_window = &cp_info.cdi_meas_windows[cdi_monitor.meas_window_index];
        monitor_period = cdi_window->cond_time_desc.period;
        monitor_interval = cdi_window->cond_time_desc.interval;
        monitor_end_of_first_meas_frame = cdi_window->cond_time_desc.start + monitor_period;      
        
        if (analyze_signal) 
          extract_signal_analyzer_params(m_cdi_info, cdi_monitor, monitor_period, monitor_end_of_first_meas_frame, msap);
  
        SOLID_MONITOR monitor = new sSOLID_MONITOR(cdi_monitor.name.c_str(), 
                                                 analyze_signal, 
                                                 analyze_signal_convergence,
                                                 FALSE,
                                                 cdi_monitor.automatically_stop,
                                                 cdi_monitor.flow_variable,
                                                 cdi_monitor.variable_source,
                                                 cdi_monitor.meas_window_index,
                                                 msap);  
        monitor->m_cdi_meas_win = cdi_window;
        monitor->m_period = monitor_period;
        monitor->m_interval = monitor_interval;
        monitor->m_end_of_first_meas_frame = monitor_end_of_first_meas_frame;
        if (cdi_monitor.classic_autostop_algorithm && analyze_signal)
          monitor->m_classic_signal_processing_interval = get_classic_signal_processing_interval(cdi_monitor, monitor_period);

        monitor->m_csys_index = cdi_csys_to_sri_csys(cdi_monitor.reference_csys);
        ccDOTIMES(j, 3) {
          monitor->m_ref_point[j] = cdi_monitor.reference_point[j];
        }

        // Check if the cdi meas windows are excluded in the exaqsub command line
        if (cdi_window->disabled_by_meas_include_exclude ) {
          msg_warn("Monitor \"%s\" refers to a cdi meas window which is excluded by exaqsub, thus it will be inactive.", cdi_monitor.name.c_str());
          cdi_monitor_index_to_sim_index[i] = -1;
          continue;
        }

        if (cdi_monitor.variable_source == eCDI_MNTR_FLOW_VAR_SRC::Solid)
        {
          if (cdi_window->solid_meas_window_index < 0) {
            msg_warn("Monitor \"%s\" refers to an invalid solid meas window (may contain no meas cells), thus it will be inactive.", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          }
          CP_MEAS_WINDOW cp_window = cp_info.meas_windows[cdi_window->solid_meas_window_index];
          if (!read_solid_monitor_geometry_filter(cp_window, monitor, cdi_monitor)) {
            msg_warn("Monitor \"%s\" does not have any geometry selected, thus it will be inactive!", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          }

          if (cp_window->meas_window_type == LGI_SAMPLING_SHELL_WINDOW) {
            monitor->m_var_source = eCDI_MNTR_FLOW_VAR_SRC::Shell; 
            monitor->m_shell_var_type = monitor->m_solid_var_type;
          }
        }
        if (cdi_monitor.variable_source == eCDI_MNTR_FLOW_VAR_SRC::Shell)
        {
          if (cdi_window->shell_meas_window_index < 0) {
            msg_warn("Monitor \"%s\" refers to an invalid shell meas window (may contain no meas cells), thus it will be inactive.", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          }
          CP_MEAS_WINDOW cp_window = cp_info.meas_windows[cdi_window->shell_meas_window_index];
          if (!read_solid_monitor_geometry_filter(cp_window, monitor, cdi_monitor)) {
            msg_warn("Monitor \"%s\" does not have any geometry selected, thus it will be inactive!", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          }
        }
        if (cdi_monitor.variable_source == eCDI_MNTR_FLOW_VAR_SRC::ShellPlusSolid)
        {
          bool is_solid_meas_window_valid = true;
          bool is_shell_meas_window_valid = true;
          bool is_solid_geometry_valid = true;
          bool is_shell_geometry_valid = true;

          is_solid_meas_window_valid = (cdi_window->solid_meas_window_index >= 0);
          if (is_solid_meas_window_valid)
            is_solid_geometry_valid = read_solid_monitor_geometry_filter(cp_info.meas_windows[cdi_window->solid_meas_window_index], monitor, cdi_monitor);
          is_shell_meas_window_valid = (cdi_window->shell_meas_window_index >= 0);
          if (is_shell_meas_window_valid)
            is_shell_geometry_valid = read_solid_monitor_geometry_filter(cp_info.meas_windows[cdi_window->shell_meas_window_index], monitor, cdi_monitor);

          if (!is_solid_meas_window_valid && !is_shell_meas_window_valid) {
            msg_warn("Monitor \"%s\" refers to invalid shell and solid meas windows (may contain no meas cells), thus it will be inactive.", cdi_monitor.name.c_str());
            cdi_monitor_index_to_sim_index[i] = -1;
            continue;
          } else if (!is_solid_meas_window_valid) {
            msg_warn("Monitor \"%s\" refers to an invalid solid meas window (may contain no meas cells), thus only the shell data will be used.", cdi_monitor.name.c_str());
            monitor->m_var_source = eCDI_MNTR_FLOW_VAR_SRC::Shell;
            if (!is_shell_geometry_valid) {
              msg_warn("Monitor \"%s\" does not have any geometry selected for shell measurement, thus it will be inactive.", cdi_monitor.name.c_str());
              cdi_monitor_index_to_sim_index[i] = -1;
            }
          } else if (!is_shell_meas_window_valid) {
            msg_warn("Monitor \"%s\" refers to an invalid shell meas window (may contain no meas cells), thus only the solid data will be used.", cdi_monitor.name.c_str());
            monitor->m_var_source = eCDI_MNTR_FLOW_VAR_SRC::Solid;
            if (!is_solid_geometry_valid) {
              msg_warn("Monitor \"%s\" does not have any geometry selected for solid measurement, thus it will be inactive.", cdi_monitor.name.c_str());
              cdi_monitor_index_to_sim_index[i] = -1;
            }
          } else {  // both shell and solid measurement windows are valid
            if (!is_shell_geometry_valid && !is_solid_geometry_valid) {
              msg_warn("Monitor \"%s\" does not have any geometry selected for shell and solid measurement, thus it will be inactive.", cdi_monitor.name.c_str());
              cdi_monitor_index_to_sim_index[i] = -1;
            } else if (!is_solid_geometry_valid) {
              msg_warn("Monitor \"%s\" does not have any geometry selected for solid measurement, thus only the shell data will be used.", cdi_monitor.name.c_str());
              monitor->m_var_source = eCDI_MNTR_FLOW_VAR_SRC::Shell;
            } else if (!is_shell_geometry_valid) {
              msg_warn("Monitor \"%s\" does not have any geometry selected for shell measurement, thus only the solid data will be used.", cdi_monitor.name.c_str());
              monitor->m_var_source = eCDI_MNTR_FLOW_VAR_SRC::Solid;
            }
          }
        }

        cdi_monitor_index_to_sim_index[i] = cp_info.monitors.size();
        cp_info.monitors.push_back(monitor);
        cp_info.solid_monitors.push_back(monitor);
        break;
      }
      case eCDI_MNTR_TYPE::HeatExchanger:
      {  
        HX_INFO hx_info = &cp_info.all_hx_info[cdi_monitor.heat_exchanger_index];
        CDI_MEAS_WINDOW monitor_cdi_meas_win = NULL;
        switch(hx_info->type)
        {
        case CDI_CHUNK_TYPE_HXCH:
          {
            // Assume that the inlet, outlet and heatgen cdi meas windows for the same heat 
            // exchanger have been synced already. Same for condensers and AMESIM heat exchangers.
            CDI_HXCH hxch = cp_info.cdi_hxchs[hx_info->index];
            CDI_MEAS_WINDOW hxch_cdi_win = &cp_info.cdi_meas_windows[hxch->inlet_meas_index];
            monitor_cdi_meas_win = hxch_cdi_win;
            monitor_period = hxch_cdi_win->fluid_time_desc.period;
            monitor_end_of_first_meas_frame = hxch_cdi_win->fluid_time_desc.start + monitor_period;
            break;
          }
        case CDI_CHUNK_TYPE_CDSR:
          {
            CDI_CDSR cdsr = cp_info.cdi_cdsrs[hx_info->index];
            CDI_MEAS_WINDOW cdsr_cdi_win = &cp_info.cdi_meas_windows[cdsr->inlet_meas_index];
            monitor_cdi_meas_win = cdsr_cdi_win;
            monitor_period = cdsr_cdi_win->fluid_time_desc.period;
            monitor_end_of_first_meas_frame = cdsr_cdi_win->fluid_time_desc.start + monitor_period;
            break;
          }
        case CDI_CHUNK_TYPE_AMHX:
          {
            CDI_AMHX amhx = cp_info.cdi_amhxs[hx_info->index];
            CDI_MEAS_WINDOW amhx_cdi_win = &cp_info.cdi_meas_windows[amhx->inlet_meas_index];
            monitor_cdi_meas_win = amhx_cdi_win;
            monitor_period = amhx_cdi_win->fluid_time_desc.period;
            monitor_end_of_first_meas_frame = amhx_cdi_win->fluid_time_desc.start + monitor_period;
            break;
          }
        default:
          msg_internal_error("Unsupported heat exchanger type!");
          break;
        }
        if (analyze_signal) 
          extract_signal_analyzer_params(m_cdi_info, cdi_monitor, monitor_period, monitor_end_of_first_meas_frame, msap);
  
  
        HX_MONITOR hx_monitor = cnew sHX_MONITOR(cdi_monitor.name.c_str(), 
                                                 analyze_signal,
                                                 analyze_signal_convergence,
                                                 FALSE,
                                                 cdi_monitor.automatically_stop,
                                                 cdi_monitor.heat_exchanger_variable,
                                                 cdi_monitor.heat_exchanger_index,
                                                 msap);
        hx_monitor->m_cdi_meas_win = monitor_cdi_meas_win;
        hx_monitor->m_period = monitor_period;
        hx_monitor->m_end_of_first_meas_frame = monitor_end_of_first_meas_frame; 
        if (cdi_monitor.classic_autostop_algorithm && analyze_signal)
          hx_monitor->m_classic_signal_processing_interval = get_classic_signal_processing_interval(cdi_monitor, monitor_period);
        
        cdi_monitor_index_to_sim_index[i] = cp_info.monitors.size();
        cp_info.monitors.push_back(hx_monitor);
        cp_info.hx_monitors.push_back(hx_monitor);
  
        break;
      }
      case eCDI_MNTR_TYPE::PowerTHERM:
      {  
        asINT32 model_index = cdi_monitor.powertherm_model_index;
        SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
        CDI_CMDL cmdl = surface_coupling->get_cmdl();
  
        // For PT monitors, use the period of the last phase for converting timesteps to # of frames
        monitor_period = cmdl->m_coupling_phase_descs[cmdl->m_coupling_phase_descs.size()-1].period;
        monitor_end_of_first_meas_frame = cmdl->m_coupling_phase_descs[0].start + cmdl->m_coupling_phase_descs[0].period;
        // For vairable PowerTHERM coupling, find the max period
        ccDOTIMES(phase, cmdl->m_coupling_phase_descs.size()) {
          monitor_max_period = MAX(monitor_max_period, cmdl->m_coupling_phase_descs[phase].period);
        }
        if (analyze_signal) 
          extract_signal_analyzer_params(m_cdi_info, cdi_monitor, monitor_period, monitor_end_of_first_meas_frame, msap);
  
        POWERTHERM_MONITOR pt_monitor = cnew sPOWERTHERM_MONITOR(cdi_monitor.name.c_str(), 
                                                 analyze_signal,
                                                 analyze_signal_convergence,
                                                 FALSE,
                                                 cdi_monitor.automatically_stop,
                                                 cdi_monitor.powertherm_model_index,
                                                 cdi_monitor.coupled_powertherm_part.c_str(),
                                                 cdi_monitor.powertherm_part_side,
                                                 cdi_monitor.powertherm_variable,
                                                 msap);
        // For PT monitors, we use the first period for timestep->number_of_frames conversions 
        // when constructing msap, and use the max period for all the other calculations, 
        // e.g. number of buffer frames in the tmp measurement file. 
        pt_monitor->m_cmdl = cmdl;
        pt_monitor->m_period = monitor_max_period; 
        pt_monitor->m_end_of_first_meas_frame = monitor_end_of_first_meas_frame;
        if (cdi_monitor.classic_autostop_algorithm && analyze_signal)
          pt_monitor->m_classic_signal_processing_interval = get_classic_signal_processing_interval(cdi_monitor, monitor_period);
  
        cdi_monitor_index_to_sim_index[i] = cp_info.monitors.size();
        cp_info.monitors.push_back(pt_monitor);
        cp_info.pt_monitors.push_back(pt_monitor);
  
        break;
      }
      default:
        msg_internal_error("Unknown monitor type %d in CDI", cdi_monitor.monitor_type);
        break;
    }
  }

  validate_cmdline_autostop_on_off_options();
  maybe_disable_autostop_via_autostop_all_off_option();
  // If --autostop_all_off is used on the command line, we ignore --autostop_on and --autostop_off
  if (!sim_args.autostop_all_off)
    disable_enable_autostop_via_autostop_on_off_options();

  exit_cdi_chunk();

  // Append monitors to composite or probe meas windows which are contributing 
  // to the corresponding monitors.
  asINT32 n_flow_monitors = cp_info.flow_monitors.size();
  ccDOTIMES(j, n_flow_monitors) {
    FLOW_MONITOR monitor = cp_info.flow_monitors[j];
    CDI_MEAS_WINDOW cdi_meas_window = &cp_info.cdi_meas_windows[monitor->m_cdi_meas_window_index];

    if (monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::Fluid) {
      if (cdi_meas_window->fluid_meas_window_index < 0)
        msg_error("Flow monitor %s is referring to an invalid fluid meas window", monitor->m_name);
      CP_MEAS_WINDOW window = cp_info.meas_windows[cdi_meas_window->fluid_meas_window_index];
      window->m_flow_monitors.push_back(j);
    }
    if (monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::FanAndPorous || 
        monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::SurfacePlusFanAndPorous) {
      if (cdi_meas_window->porous_meas_window_index < 0)
        msg_error("Flow monitor %s is referring to an invalid fan(porous) meas window", monitor->m_name);
      CP_MEAS_WINDOW window = cp_info.meas_windows[cdi_meas_window->porous_meas_window_index];
      window->m_flow_monitors.push_back(j);
    }
    if (monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::Surface || 
        monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::SurfacePlusFanAndPorous) {
      if (cdi_meas_window->surface_meas_window_index < 0)
        msg_error("Flow monitor %s is referring to an invalid surface meas window", monitor->m_name);
      CP_MEAS_WINDOW window = cp_info.meas_windows[cdi_meas_window->surface_meas_window_index];
      window->m_flow_monitors.push_back(j);
    }
  }

  asINT32 n_solid_monitors = cp_info.solid_monitors.size();
  ccDOTIMES(j, n_solid_monitors) {
    SOLID_MONITOR monitor = cp_info.solid_monitors[j];
    CDI_MEAS_WINDOW cdi_meas_window = &cp_info.cdi_meas_windows[monitor->m_cdi_meas_window_index];

    if (monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::Solid || monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::ShellPlusSolid) {
      if (cdi_meas_window->solid_meas_window_index < 0)
        msg_error("Solid monitor %s is referring to an invalid solid meas window", monitor->m_name);
      CP_MEAS_WINDOW window = cp_info.meas_windows[cdi_meas_window->solid_meas_window_index];
      window->m_solid_monitors.push_back(j);
    }
    if (monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::Shell || 
        monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::ShellPlusSolid) {
      if (cdi_meas_window->shell_meas_window_index < 0)
        msg_error("Solid monitor %s is referring to an invalid shell meas window", monitor->m_name);
      CP_MEAS_WINDOW window = cp_info.meas_windows[cdi_meas_window->shell_meas_window_index];
      window->m_solid_monitors.push_back(j);
    }
  }

  cp_info.n_monitors_to_check_sim_end_init_transient = 0;

  TIMESTEP sim_end_init_transient = 0;

  // Some cdi monitors may be not used (e.g. monitors which refer to meas windows with no meas cells), thus 
  // we need to remove those monitors from the monitor list for finding the simulation end of initial transient.  
  std::vector<asINT32>& cdi_eit_monitors = cp_info.monitors_to_find_sim_end_init_transient;
  ccDOTIMES(i, cdi_eit_monitors.size()) {
    cdi_eit_monitors[i] = cdi_monitor_index_to_sim_index[cdi_eit_monitors[i]];
  }
  cdi_eit_monitors.erase(std::remove(cdi_eit_monitors.begin(), cdi_eit_monitors.end(), -1), cdi_eit_monitors.end());

  ccDOTIMES(i, cdi_eit_monitors.size()) {

      MONITOR monitor = cp_info.monitors[cdi_eit_monitors[i]];
      monitor->m_end_sim_via_init_transient = TRUE;

      // EffectiveMinInitialTransient() returns the end of init transient (in frames) if it is "User Specified"
      if (monitor->m_msap.m_isUserSpecifiedEndInitialTransient)
        sim_end_init_transient = MAX(sim_end_init_transient, monitor->m_end_of_first_meas_frame 
                                     + monitor->m_msap.m_userSpecifiedEndInitialTransient * monitor->m_period);

      // Only need to check those monitors with end of initial transient set to "Automatic", and those with user 
      // specified end of initial transient but not started by other monitors (chained monitors).
      if (!(monitor->m_msap.m_isUserSpecifiedEndInitialTransient && !monitor->m_cdi_meas_win->start_time_via_monitors_p))
        cp_info.n_monitors_to_check_sim_end_init_transient++;
  }

  ccDOTIMES(i, cp_info.monitors.size())
  {
    MONITOR monitor = cp_info.monitors[i];
    if (monitor->m_end_sim_if_converged) {
      cp_info.monitors_to_end_sim.push_back(monitor);
    }
  }
  // If all the monitors used to find the sim end init transient have "User Specified" end of 
  // initial transient and known start time (not started by other monitors), we can calculate the simulation end time here.
  if (cdi_eit_monitors.size() > 0 &&
      cp_info.n_monitors_to_check_sim_end_init_transient == 0) {
    TIMESTEP sim_end_time = sim_end_init_transient + cp_info.duration_after_init_transient;
    if (sim_end_time < cp_info.end_time) {
      msg_print("All the monitors used to determine the simulation end time have a user-specified end "
              "of initial transient. Thus the simulation end time has been set to timestep %d.", sim_end_time);
      register_async_event_request(EVENT_ID_EXIT, 0, sim_end_time, FALSE, FALSE);
    } else {  // If the current end time (either from exaqsub or from exasignal) is smaller, do not request to stop. See PR41559
      msg_print("All the monitors used to determine the simulation end time have a user-specified end "
              "of initial transient. The simulation is supposed to end at timestep %d. However the current "
              "end time is earlier, and the simulation will end at timestep %d.", sim_end_time, cp_info.end_time);    
    }
  }

#if DEBUG_MONITOR  
  msg_print("%d monitors are used to find the whole sim end initial transient, while %d monitors will be "
            "checked for the end of inital transient on the fly.", 
            cdi_eit_monitors.size(), cp_info.n_monitors_to_check_sim_end_init_transient);
            //cp_info.n_monitors_to_find_sim_end_init_transient, cp_info.n_monitors_to_check_sim_end_init_transient);
  if (cp_info.monitors_to_end_sim.size() > 0)
    msg_print("%d monitors are used to end the simulation if all converged.", cp_info.monitors_to_end_sim.size());
#endif

  // If a CDI meas window is started via monitors, find its tmp start time
  // and the maximum number of buffer frames we keep in the tmp meas file.
  //
  // To handle multiple chained monitors and meas windows (e.g. meas window W3 is started 
  // via monitors M2(attached to meas window W2) and W2 is started via monitor M1 (attached 
  // to meas window W1, i.e. W1 (M1) -> W2 (M2) -> W3 where "->" meas "starts"), we need to 
  // sort meas windows according to the dependency. 

  // Some cdi monitors may be not used (e.g. monitors which refer to meas windows with no meas cells), thus 
  // we need to remove those monitors from the monitor list for starting the cdi meas window.  
  DO_CDI_MEAS_WINDOWS(cdi_meas_win) {
    std::vector<asINT32>& start_via_monitors = cdi_meas_win->master_monitors;
    ccDOTIMES(i, cdi_meas_win->master_monitors.size()) {
      start_via_monitors[i] = cdi_monitor_index_to_sim_index[start_via_monitors[i]];
    }
    start_via_monitors.erase(std::remove(start_via_monitors.begin(), start_via_monitors.end(), -1), start_via_monitors.end());
  }

  // Find all the meas windows started via each monitor
  DO_CDI_MEAS_WINDOWS(cdi_meas_win) {
    if (!cdi_meas_win->start_time_via_monitors_p)
      continue;

    BOOLEAN all_monitors_end_init_transient_user_specified_p = TRUE;
    TIMESTEP meas_window_start_time = 0;
    cdi_meas_win->n_master_monitors_to_check = 0;
    ccDOTIMES(i, cdi_meas_win->master_monitors.size()) {
      // If the monitor end of initial transient is user specified and start time is known, its only 
      // effect is to change the window start and end time, so the monitor is not really controlling 
      // the meas window start on the fly. Thus no need to tell the monitor to "control".
      MONITOR monitor = cp_info.monitors[cdi_meas_win->master_monitors[i]];

      if (!(monitor->m_msap.m_isUserSpecifiedEndInitialTransient && !monitor->m_cdi_meas_win->start_time_via_monitors_p)) {
        all_monitors_end_init_transient_user_specified_p = FALSE;
        // Tell each monitor which meas windows it is controlling
        monitor->cdi_meas_windows_to_start.push_back(cdi_meas_win);
        cdi_meas_win->n_master_monitors_to_check++;
      } else {
        // monitor->m_endInitialTransientTime is the real timestep, but it is not initialized yet at this point
        meas_window_start_time = MAX(meas_window_start_time, monitor->m_period * monitor->m_msap.m_userSpecifiedEndInitialTransient);
      }
    }

    // If all monitors used to start this meas window are invalid, issue error message
    if (cdi_meas_win->master_monitors.size() == 0)
      msg_error("All monitors used to start measurement window \"%s\" are invalid.", cdi_meas_win->name);

    // If all the monitors end init transient is user specified, then we know the real start time
    // of the meas window. Thus the meas window is not really started via monitors.
    if (all_monitors_end_init_transient_user_specified_p) {
#if DEBUG_MONITOR
      msg_print("Meas win started by monitors with user specified EIT, time desc start %d changed to %d",
                  cdi_meas_win->fluid_time_desc.start,
                  meas_window_start_time);
#endif
      cdi_meas_win->fluid_time_desc.start = meas_window_start_time;
      cdi_meas_win->rename_tmp_meas_files();
      if (cdi_meas_win->meas_end_time_via == eCDI_MEAS_END_TIME_VIA::Duration) {
        cdi_meas_win->fluid_time_desc.repeat = (cdi_meas_win->duration - 1)/cdi_meas_win->fluid_time_desc.period + 1;
      } else {  // end via specified time
        cdi_meas_win->fluid_time_desc.repeat = (cdi_meas_win->end_time - cdi_meas_win->fluid_time_desc.start - 1)/cdi_meas_win->fluid_time_desc.period + 1;
      }
      cdi_meas_win->start_time_via_monitors_p = FALSE;
    } else {
      cdi_meas_win->fluid_time_desc.repeat = TIMESTEP_MAX;
    }
  }

  ccDOTIMES(i, cp_info.n_cdi_meas_windows)
  {
    cp_info.cdi_meas_windows[i].rank = 0;
  }
  
  // Walk over the dependency tree for each root node and update the cdi meas window ranks accordingly
  DO_CDI_MEAS_WINDOWS(cdi_meas_win)
  {
    if (!cdi_meas_win->start_time_via_monitors_p) {
      update_cdi_meas_window_ranks(cdi_meas_win, 0);  
    }
  }

  CDI_MEAS_WINDOW cdi_windows[cp_info.n_cdi_meas_windows];
  ccDOTIMES(i, cp_info.n_cdi_meas_windows)
  {
    cdi_windows[i] = cp_info.cdi_meas_windows + i;
  }
  
  // Sort cdi meas windows according to the dependency rank
  if (cp_info.n_cdi_meas_windows > 0)
    std::sort(cdi_windows, cdi_windows + cp_info.n_cdi_meas_windows - 1, sort_cdi_windows_by_rank);  

  BOOLEAN some_meas_win_started_via_monitors = FALSE;
  ccDOTIMES(i, cp_info.n_cdi_meas_windows)
  {
    CDI_MEAS_WINDOW cdi_meas_win = cdi_windows[i];
    if (!cdi_meas_win->start_time_via_monitors_p)
      continue;
    some_meas_win_started_via_monitors = TRUE;
    BOOLEAN all_monitors_end_init_transient_user_specified_p = TRUE;
    TIMESTEP delta_t = 0;
    TIMESTEP tmp_start_time = 0;
    ccDOTIMES(i, cdi_meas_win->master_monitors.size()) {
      MONITOR monitor = cp_info.monitors[cdi_meas_win->master_monitors[i]];
      asINT32 n_max_sets_by_monitor = monitor->m_msa.UpperBoundEndInitialTransientToDetectionTime() + 1; // Max number of buffer frames determined by this monitor

      delta_t = MAX(delta_t, n_max_sets_by_monitor * monitor->m_period);
      
      asINT32 tmp_start_via_monitor;
      tmp_start_via_monitor = monitor->m_end_of_first_meas_frame 
                            + monitor->m_msa.EffectiveMinInitialTransient() * monitor->m_period;
      tmp_start_time = MAX(tmp_start_time, tmp_start_via_monitor);
    }

    // Update cdi meas window start time. For average mme window, always start from timestep 0   
    if (!cdi_meas_win->is_average_mme) {
      cdi_meas_win->fluid_time_desc.start += cdi_meas_win->fluid_time_desc.period * 
                                       (tmp_start_time - cdi_meas_win->fluid_time_desc.start) /cdi_meas_win->fluid_time_desc.period;
#if DEBUG_MONITOR
      msg_print("cdi_meas_win->time_desc.start set to %d", cdi_meas_win->fluid_time_desc.start);
#endif 
      cdi_meas_win->cyclic_buffer_size = (delta_t - 1)/cdi_meas_win->fluid_time_desc.period + 1;
    }
  }

  if (cp_info.sim_duration_via == eCDI_SIM_DURATION_VIA::AfterInitialTransient &&
      cdi_eit_monitors.size() == 0) {
      //cp_info.n_monitors_to_find_sim_end_init_transient == 0) {
    msg_internal_error("Simulation is ended via (end of init transient + duration)."
             "However no monitors are selected for finding the end of initial transient.");
  }

  //Create sWIPER objects for  any cdi wiper that is started after the end of an initial transient.
  //Also, update the CDI wiper start times if the end of initial transient is prescribed.
  std::vector<sCDI_WIPR> &cdi_wipers = *cp_particle_sim.cdi_wiper_models();
  ccDOTIMES(wiper_index, cdi_wipers.size()) {
    sCDI_WIPR& cdi_wiper = cdi_wipers[wiper_index];

    if (cdi_wiper.start_via != eCDI_MEAS_START_TIME_VIA::AfterInitialTransient) 
      continue;

    WIPER wiper = new sWIPER(wiper_index, 
                             cdi_wiper.name,
                             TRUE, 
                             cdi_wiper.monitors, 
                             cdi_wiper.monitors.size(), 
                             TIMESTEP_MAX);
    cp_particle_sim.wipers.push_back(wiper);

    //Update cdi wipers, before they are sent to the SPs, incase the end of initial transient is prescribed.
    wiper->remove_dummy_monitors(cdi_wiper, cdi_monitor_index_to_sim_index);
  }

  //Setup emitters controlled by monitors.
  cSTRING emitter_start_delay_time = getenv("EXA_EMITTER_START_DELAY");
  if(emitter_start_delay_time != NULL) {
    sEMITTER::start_delay = atol(emitter_start_delay_time);
    msg_print("User-specified emitter start delay is %d", sEMITTER::start_delay);
  }
 
  // Find all the emitters started via each monitor
  const std::vector<const sCDI_PARTICLE_EMITTER_BASE*> &cdi_emitters = cp_particle_sim.cdi_particle_emitters()->GetAllEmitters();
  asINT32 total_cdi_emitters = cdi_emitters.size();

  ccDOTIMES(i, total_cdi_emitters) {
    sCDI_PARTICLE_EMITTER_BASE* cdi_emitter = (sCDI_PARTICLE_EMITTER_BASE *)cdi_emitters[i]; //Hack to get a reference to a non-const.

    EMITTER emitter = NULL;
    // Only store emitters started after end of initial transient
    if (cdi_emitter->start_via != eCDI_MEAS_START_TIME_VIA::AfterInitialTransient) {
      continue;
    } else {
      emitter = new sEMITTER(i, 
                             cdi_emitter->name,
                             TRUE, 
                             cdi_emitter->monitors, 
                             cdi_emitter->monitors.size(), 
                             TIMESTEP_MAX,
                             cdi_emitter->end_via == eCDI_MEAS_END_TIME_VIA::Duration,
                             cdi_emitter->duration.value,
                             cdi_emitter->end.value
                             );
      cp_particle_sim.emitters.push_back(emitter);
    }

    emitter->remove_dummy_monitors(*cdi_emitter, cdi_monitor_index_to_sim_index); // Remove monitors with end of initial transient already known.
  }

  cp_info.monitor_log_filename = cp_info.root_filename + std::string(".monitors");

  // Heat exchanger monitors sanity check
  asINT32 n_hx_monitors = cp_info.hx_monitors.size();
  ccDOTIMES(j, n_hx_monitors) {
    // Check if this hx monitor contributes to this CP meas window
    HX_MONITOR monitor = cp_info.hx_monitors[j];

    // Check the heat exchanger type: HXCH, CDSR or AMHX
    // monitor.hx_index is the global index among all heat exchangers. 
    sHX_INFO &hx_info = cp_info.all_hx_info[monitor->m_hx_index];
    if (hx_info.type == CDI_CHUNK_TYPE_HXCH) {
      if (hx_info.index >= cp_info.n_hxchs)
        msg_error("Monitor hxch index out of range.");
    } else if (hx_info.type == CDI_CHUNK_TYPE_CDSR) {
      if (hx_info.index >= cp_info.n_cdsrs)
        msg_error("Monitor cdsr index out of range.");
    } else if (hx_info.type == CDI_CHUNK_TYPE_AMHX) {
      if (hx_info.index >= cp_info.n_amhxs)
        msg_error("Monitor amhx index out of range.");
    } else {
      msg_error("Unknow heat exchanger type %d", hx_info.type);
    }

    // Append the monitor to the corresponding heat exchanger
    cp_info.all_hx_info[monitor->m_hx_index].monitors.push_back(j);
  }

  // Open and clear monitors log file
  if (cp_info.monitor_log_filename.empty())
    msg_internal_error("Monitor log file name is not specified.");
  FILE *monitor_file = fopen(cp_info.monitor_log_filename.c_str(), "w");
  if (!monitor_file)
    msg_warn("Unable to open monitor log file \"%s\". Monitor initial transient end times and signal convergence times"
             " will not be recorded.", cp_info.monitor_log_filename.c_str());
  else {
    fprintf(monitor_file, "%s    %s    %s\n", "#monitor_name", "initial_transient_end", "signal_convergence");
    // Find the longest monitor name
    asINT32 longest_name_size = 0;
    ccDOTIMES(i, cp_info.monitors.size()) {
      MONITOR monitor = cp_info.monitors[i];
      longest_name_size = MAX(longest_name_size, strlen(monitor->m_name));
    }
    cp_info.longest_monitor_name_size = longest_name_size;
    ccDOTIMES(i, cp_info.monitors.size()) {
      MONITOR monitor = cp_info.monitors[i];
      monitor->m_monitor_log_file_pos = ftell(monitor_file);
      fprintf(monitor_file, "\"%-.*s\"    %16d    %16d\n", cp_info.longest_monitor_name_size, monitor->m_name, monitor->m_endInitialTransientTime, monitor->m_convergenceTime);
    }
  }
  fclose(monitor_file);
}

static VOID validate_cmdline_meas_timing_options()
{
  MEAS_TIMING_OPTION meas_timing_option = cp_info.cmdline_meas_timing_options;
  while (meas_timing_option != NULL) {
    cSTRING cmdline_option = meas_timing_option->is_acoustic_timing ? "meas_timing_acoustics" : "meas_timing";
    CDI_MEAS_WINDOW cdi_meas_win = NULL;
    DO_CDI_MEAS_WINDOWS(cdi_win) {
      if (strcmp(meas_timing_option->meas_window_name, cdi_win->name) == 0) {
        cdi_meas_win = cdi_win;
        break;
      }
    }
    if (cdi_meas_win == NULL)
      msg_error("--%s option refers to invalid measurement window \"%s\"",
                cmdline_option, meas_timing_option->meas_window_name);

    UNITS_STATUS units_status;
    UNITS_UNIT user_unit;
    UNITS_UNIT lattice_unit;
    dFLOAT factor;
    dFLOAT offset;

    // convert all time parameters to timesteps
    if (meas_timing_option->time_unit != NULL) {
      if ((units_status = units_parse_unit(cp_info.units_db, "timestep", &lattice_unit)) != UNITS_STATUS_OK)
        msg_internal_error("UNITS failure parsing \"timestep\": %s", units_error_string(units_status));
      if ((units_status = units_parse_unit(cp_info.units_db, meas_timing_option->time_unit, &user_unit)) != UNITS_STATUS_OK)
        msg_error("Invalid time unit (%s) for --%s option for window \"%s\"",
                  meas_timing_option->time_unit, cmdline_option, meas_timing_option->meas_window_name);
      if ((units_status = units_conversion_coefficients(cp_info.units_db, user_unit, lattice_unit, &factor, &offset))
          != UNITS_STATUS_OK)
        msg_error("Invalid time unit (%s) for --%s option for window \"%s\"",
                  meas_timing_option->time_unit, cmdline_option, meas_timing_option->meas_window_name);

      if (meas_timing_option->start_time > 0)
        meas_timing_option->start_time *= factor;
      if (meas_timing_option->end_time > 0)
        meas_timing_option->end_time *= factor;
      if (meas_timing_option->period > 0)
        meas_timing_option->period *= factor;
      if (meas_timing_option->average_interval > 0)
        meas_timing_option->average_interval *= factor;
    }

    // handle acoustics timing fields
    if (meas_timing_option->is_acoustic_timing) {
      cSTRING freq_unit = "Hz";
      if (meas_timing_option->freq_unit != NULL)
        freq_unit = meas_timing_option->freq_unit;

      if ((units_status = units_parse_unit(cp_info.units_db, "1/timestep", &lattice_unit)) != UNITS_STATUS_OK)
        msg_internal_error("UNITS failure parsing \"1/timestep\": %s", units_error_string(units_status));
      if ((units_status = units_parse_unit(cp_info.units_db, freq_unit, &user_unit)) != UNITS_STATUS_OK)
        msg_error("Invalid frequency unit (%s) for --%s option for window \"%s\"",
                  freq_unit, cmdline_option, meas_timing_option->meas_window_name);
      if ((units_status = units_conversion_coefficients(cp_info.units_db, user_unit, lattice_unit, &factor, &offset))
          != UNITS_STATUS_OK)
        msg_error("Invalid frequency unit (%s) for --%s option for window \"%s\"",
                  freq_unit, cmdline_option, meas_timing_option->meas_window_name);
      
      meas_timing_option->min_freq *= factor;
      meas_timing_option->max_freq *= factor;

      if (meas_timing_option->end_time == 0) // 0 is shorthand for end_time = start_time + min_cycles / min_freq
        meas_timing_option->end_time = (meas_timing_option->start_time  
                                        + meas_timing_option->min_cycles / meas_timing_option->min_freq);
      meas_timing_option->period = 1 / (meas_timing_option->max_freq * meas_timing_option->nyquist_multiplier);
    }

    // round to integral user timesteps - a value of -1 is shorthand for retaining the old value
    TIMESTEP lattice_to_user_steps = cp_info.n_lattice_base_steps / cp_info.n_user_base_steps;
    if (meas_timing_option->start_time > 0)
      meas_timing_option->start_time = floor(meas_timing_option->start_time + 0.5) * lattice_to_user_steps;
    if (meas_timing_option->end_time > 0)
      meas_timing_option->end_time = floor(meas_timing_option->end_time + 0.5) * lattice_to_user_steps;
    if (meas_timing_option->period > 0)
      meas_timing_option->period = floor(meas_timing_option->period + 0.5) * lattice_to_user_steps;
    if (meas_timing_option->average_interval > 0)
      meas_timing_option->average_interval = floor(meas_timing_option->average_interval + 0.5) * lattice_to_user_steps;

    if (meas_timing_option->start_time == 0)
      meas_timing_option->start_time = cp_info.restart_time; // 0 is a short-hand
    else if (meas_timing_option->start_time > 0
             && meas_timing_option->start_time < cp_info.restart_time)
      msg_error("For --%s option for window \"%s\", the start time (%g timesteps) is less than"
                " the checkpoint restart time (%d timesteps)", 
                cmdline_option, meas_timing_option->meas_window_name, meas_timing_option->start_time, cp_info.restart_time);

    if (meas_timing_option->end_time >= 0) {
      dFLOAT start_time = meas_timing_option->start_time >= 0 ? meas_timing_option->start_time : cdi_meas_win->start_time;
      if (meas_timing_option->end_time < start_time)
        msg_warn("For --%s option for window \"%s\", the end time (%g timesteps) is less than"
                 " the%s start time (%g timesteps)", 
                 cmdline_option, meas_timing_option->meas_window_name,
                 meas_timing_option->end_time, 
                 meas_timing_option->start_time >= 0 ? "" : " existing",
                 start_time);
    }

    if (meas_timing_option->average_interval >= 0
        || meas_timing_option->period >= 0) {
      dFLOAT avg = meas_timing_option->average_interval >= 0 ? meas_timing_option->average_interval : cdi_meas_win->average_interval;
      dFLOAT period = meas_timing_option->period >= 0 ? meas_timing_option->period : cdi_meas_win->period;
      if (avg > period)
        msg_warn("For --%s option for window \"%s\", the%s averaging interval (%g timesteps) is"
                 " greater than the%s period (%g timesteps)", 
                 cmdline_option, meas_timing_option->meas_window_name,
                 meas_timing_option->average_interval >= 0 ? "" : " existing", avg,
                 meas_timing_option->period >= 0 ? "" : " existing", period);
    }
      
    meas_timing_option = meas_timing_option->next;
  }
}

static VOID validate_cmdline_include_exclude_meas_windows()
{
  MEAS_INCLUDE_EXCLUDE_OPTION option = cp_info.cmdline_include_exclude_meas_windows;
  while (option != NULL) {
    BOOLEAN found = FALSE;
    ccDOTIMES(i, cp_info.n_cdi_meas_windows) {
      if (strcmp(option->meas_window_name, cp_info.cdi_meas_windows[i].name) == 0) {
        found = TRUE;
        break;
      }
    }
    if (!found)
      msg_error("--%s option refers to invalid measurement window \"%s\"",
                cp_info.is_meas_window_inclusion_list ? "meas_include" : "meas_exclude",
                option->meas_window_name);
    option = option->next;
  }
}

static VOID disable_all_meas_windows_if_requested()
{  
  if (sim_args.disable_all_meas_windows) {
    asINT32 n_windows = cp_info.n_cdi_meas_windows;
    ccDOTIMES(i, n_windows) {
      cp_info.cdi_meas_windows[i].start_time = TIMESTEP_MAX;
      cp_info.cdi_meas_windows[i].disabled_by_meas_include_exclude = TRUE;
    }
    msg_print("Disabled all measurement windows");
  }
}

static VOID disable_meas_windows_via_meas_include_exclude_options()
{
  asINT32 n_windows = cp_info.n_cdi_meas_windows;

  std::string msg;
  BOOLEAN first = TRUE;
  if (!cp_info.is_meas_window_inclusion_list) {
    MEAS_INCLUDE_EXCLUDE_OPTION meas_exclude = cp_info.cmdline_include_exclude_meas_windows;

    while (meas_exclude != NULL) {
      if (!first)
        msg += ", ";
      first = FALSE;
      ccDOTIMES(i, n_windows) {
        if (strcmp(meas_exclude->meas_window_name, cp_info.cdi_meas_windows[i].name) == 0) {
          cp_info.cdi_meas_windows[i].start_time = TIMESTEP_MAX;
          cp_info.cdi_meas_windows[i].disabled_by_meas_include_exclude = TRUE;
          msg += cp_info.cdi_meas_windows[i].name;
          break;
        }
      }
      meas_exclude = meas_exclude->next;
    }
  } else {
    ccDOTIMES(i, n_windows) {
      BOOLEAN found = FALSE;
      MEAS_INCLUDE_EXCLUDE_OPTION meas_include = cp_info.cmdline_include_exclude_meas_windows;
      while (meas_include != NULL) {
        if (strcmp(meas_include->meas_window_name, cp_info.cdi_meas_windows[i].name) == 0) {
          found = TRUE;
          break;
        }
        meas_include = meas_include->next;
      }
      if (!found) {
        cp_info.cdi_meas_windows[i].start_time = TIMESTEP_MAX;
        cp_info.cdi_meas_windows[i].disabled_by_meas_include_exclude = TRUE;
        if (!first)
          msg += ", ";
        first = FALSE;
        msg += cp_info.cdi_meas_windows[i].name;
      }
    }
  }

  if (cp_info.cmdline_include_exclude_meas_windows != NULL)
    msg_print("Disabled measurement windows: %s", msg.c_str());
}

//---------------------------------------------------------------------------
// Iterate through the given array of var_types and expand per-component vars.
//---------------------------------------------------------------------------
static SRI_VARIABLE_TYPE *expand_multi_component_vars (
    asINT32& n_vars,
    SRI_VARIABLE_TYPE *original,
    BOOLEAN include_contact_angle,
    BOOLEAN include_component_velocity,
    BOOLEAN include_mass_exchange,
    BOOLEAN is_fluid_composite,
    BOOLEAN is_surface,
    BOOLEAN is_fluid_development,
    BOOLEAN is_sampled_meas,
    BOOLEAN is_uds_transport)
{
  asINT32 max_n_vars = n_vars;

  if (!is_surface && !is_sampled_meas) {
    if (!is_fluid_composite && !is_fluid_development && cp_info.num_fluid_components > 1)
      max_n_vars += 1; //add dynamic_scalar_multiplier
    if (include_mass_exchange)
      max_n_vars += 1; //add mass_exchange
  }

  if(is_sampled_meas) {
    max_n_vars += cp_info.num_fluid_components; //add component mass_flux
    if (is_uds_transport)
      max_n_vars += 1;   // add water varor_mfrac  (uds_value for ONLY one scalar!)
  }

  if(include_contact_angle)
    max_n_vars += 1; //add contact angle of component 0

  if(include_component_velocity) {
    if (is_sampled_meas)
      max_n_vars += (3 * cp_info.num_fluid_components); //add compnent xvel, yvel, zvel
    else
      max_n_vars += (4 * cp_info.num_fluid_components); //add compnent xvel, yvel, zvel and vel_mag
  }

  if (!is_surface && !is_sampled_meas) {
    if (!is_fluid_composite && !is_fluid_development) {
      max_n_vars += 1; //porosity
      if (is_uds_transport)
	max_n_vars += 1;   // add water varor_mfrac  (uds_value for ONLY one scalar!)
    }      
    if (is_fluid_composite)
      max_n_vars += (2 * cp_info.num_fluid_components);  //add Sw_pore, Sw_pm

    if (is_fluid_development)
      max_n_vars += (2 * cp_info.num_fluid_components);  //add V_pore, V_pm  
  }

  if (is_surface) {
    if (is_uds_transport)
      max_n_vars += 2; //add water_vapor_mfrac and _mflux (uds_value and uds_flux for ONLY one scalar!)
  }

  SRI_VARIABLE_TYPE *expanded = sri_new_vector(SRI_VARIABLE_TYPE, max_n_vars);
  int dest_index = 0;

  for (int i = 0; i < n_vars; ++i) {  
    expanded[dest_index++] = original[i];
  }

  if (include_contact_angle) {
    expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_CONTACT_ANGLE);
  }

  if (!is_surface && !is_sampled_meas) {
    if (!is_fluid_composite && !is_fluid_development && cp_info.num_fluid_components > 1)
      expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_DYNAMIC_SCALAR_MULTIPLIER);
    if (include_mass_exchange)
      expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_MASS_EXCHANGE);
  }

  if (include_component_velocity) { //so far all 5g cases are 3D cases
    for (asINT32 i = 0; i < cp_info.num_fluid_components; i++) {
      expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_XVEL, i);
      expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_YVEL, i);
      
      if (cp_info.n_dims == 3)
	expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_ZVEL, i);

      if(!is_sampled_meas)
	expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_VEL_MAG, i);
    }
  }

  if(is_sampled_meas){
    for (asINT32 i = 0; i < cp_info.num_fluid_components; i++)
      expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_MASS_FLUX, i);
    if (is_uds_transport)
      expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFRAC);
  }

  if (!is_surface && !is_sampled_meas) {
    if (!is_fluid_composite && !is_fluid_development) {
      expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_POROSITY);
      if (is_uds_transport)
	expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFRAC);
    }

    if (is_fluid_composite) {
      for (asINT32 i = 0; i < cp_info.num_fluid_components; i++) {
	expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_PORE_SATURATION, -1, i);  //per-phase
	expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_PM_SATURATION, -1, i);  //per-phase
      }
    }

    if (is_fluid_development) {
      for (asINT32 i = 0; i < cp_info.num_fluid_components; i++) {
	expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_PORE_VOLUME, -1, i);  //per-phase
	expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_PM_VOLUME, -1, i);  //per-phase
      }
    }
  }

  if (is_surface) {
    if (is_uds_transport) {
      expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFRAC);
      expanded[dest_index++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFLUX);
    }
  }

  n_vars = dest_index;
  
  sri_delete_vector(original);
  
  return expanded;
}

static SRI_VARIABLE_TYPE *add_special_vars_to_fluid(asINT32& n_vars, SRI_VARIABLE_TYPE *original) 
{
  asINT32 max_n_vars = n_vars + 2 + cp_info.n_dims;  //u*, den_frozen, xvel_frozen, yvel_forzen and zvel_frozen (if 3D)

  SRI_VARIABLE_TYPE *expanded = sri_new_vector(SRI_VARIABLE_TYPE, max_n_vars);
  int dest_index = 0;

  for (int i = 0; i < n_vars; ++i) {
    expanded[dest_index++] = original[i];
  }

  expanded[dest_index++] = SRI_VARIABLE_USTAR;
  expanded[dest_index++] = SRI_VARIABLE_DENSITY_FROZEN;
  expanded[dest_index++] = SRI_VARIABLE_XVEL_FROZEN;
  expanded[dest_index++] = SRI_VARIABLE_YVEL_FROZEN;
  if (cp_info.n_dims == 3)
    expanded[dest_index++] = SRI_VARIABLE_ZVEL_FROZEN;


  n_vars = dest_index;
  
  sri_delete_vector(original);
  
  return expanded;
}

static SRI_VARIABLE_TYPE *add_total_temperature_to_meas(asINT32& n_vars, SRI_VARIABLE_TYPE *original) 
{
  asINT32 max_n_vars = n_vars + 1;  //total_temperature

  SRI_VARIABLE_TYPE *expanded = sri_new_vector(SRI_VARIABLE_TYPE, max_n_vars);
  int dest_index = 0;
  BOOLEAN is_total_temp_present = FALSE;

  for (int i = 0; i < n_vars; ++i) {
    if (original[i] == SRI_VARIABLE_TOTAL_TEMPERATURE)
      is_total_temp_present = TRUE;
    expanded[dest_index++] = original[i];
  }

  if (!is_total_temp_present)
    expanded[dest_index++] = SRI_VARIABLE_TOTAL_TEMPERATURE;

  n_vars = dest_index;
  
  sri_delete_vector(original);
  
  return expanded;
}


static SRI_VARIABLE_TYPE *add_density_or_pressure_to_meas(asINT32& n_vars, SRI_VARIABLE_TYPE *original) 
{
  asINT32 max_n_vars = n_vars + 1;  //density or pressure

  SRI_VARIABLE_TYPE *expanded = sri_new_vector(SRI_VARIABLE_TYPE, max_n_vars);
  int dest_index = 0;
  BOOLEAN is_density_present = FALSE;
  BOOLEAN is_pressure_present = FALSE;

  for (int i = 0; i < n_vars; ++i) {
    if (original[i] == SRI_VARIABLE_DENSITY)
      is_density_present = TRUE;
    if (original[i] == SRI_VARIABLE_PRESSURE)
      is_pressure_present = TRUE;
    expanded[dest_index++] = original[i];
  }

  if (!is_density_present)
    expanded[dest_index++] = SRI_VARIABLE_DENSITY;
  if (!is_pressure_present)
    expanded[dest_index++] = SRI_VARIABLE_PRESSURE;

  n_vars = dest_index;
  
  sri_delete_vector(original);
  
  return expanded;
}


VOID sCP_CDI_READER::read_meas_chunk()
{
  if (!m_is_meas_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_MEAS);

  BOOLEAN is_active_ht = cp_info.sim_ht_type == SRI_HT_ACTIVE_SCALAR;
  BOOLEAN is_ht        = cp_info.is_heat_transfer;
  BOOLEAN is_turb      = cp_info.is_turb;
  BOOLEAN is_3d        = cp_info.n_dims == 3 ? TRUE : FALSE;
  BOOLEAN is_5g_sim    = cp_info.is_5g_sim;

  asINT32 max_n_windows = cio_get_count(m_cdi_info->cio_info);
  // the scpl chunk *must* be present before the mesr chunks in the CDI file
  // even though the coupling measurement windows are added towards the end of
  // the measurement windows in the LGI file
#if SURF_COUP
  asINT32 n_coupling_windows = cp_info.n_surface_couplings;
#else
  asINT32 n_coupling_windows = 0;
#endif
  asINT32 n_windows = 0;
  if (max_n_windows + n_coupling_windows > 0) {
    cp_info.cdi_meas_windows = cnew sCDI_MEAS_WINDOW [max_n_windows + n_coupling_windows];
  }
  if (max_n_windows > 0) {

    CDI_MEAS_WINDOW cdi_meas_win = cp_info.cdi_meas_windows;

    TIMESTEP lattice_to_user_steps = cp_info.n_lattice_base_steps / cp_info.n_user_base_steps;

    ccCDI_DO_INNER_CHUNKS(i, "meas", m_cdi_info) {
      if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_MESR) {

        cdi_meas_win->min_pressure = -1;
        cdi_meas_win->max_pressure = -1;

        cdi_meas_win->one_window   = -1;

        cdi_meas_win->fluid_meas_window_index = -1;
        cdi_meas_win->porous_meas_window_index = -1;
        cdi_meas_win->surface_meas_window_index = -1;
        cdi_meas_win->shell_meas_window_index = -1;
        cdi_meas_win->solid_meas_window_index = -1;

        cdi_meas_win->reference_point[0] = 0;
        cdi_meas_win->reference_point[1] = 0;
        cdi_meas_win->reference_point[2] = 0;

        // coarsest_shob_scale is updated as each DGF meas window is read
        cdi_meas_win->coarsest_fluid_shob_scale = FINEST_SCALE;
        cdi_meas_win->coarsest_cond_shob_scale = FINEST_SCALE;
        // cdi_meas_win->coarsest_shob_scale = sim_num_scales() - 1;

        CDI_MESR mesr = cdi_read_mesr(m_cdi_info, cp_info.n_dims == 2, cp_info.is_heat_transfer);
        CDI_MPRM mprm = mesr->params;
        CDI_MSTP mstp = mesr->options;

        cdi_meas_win->name = strsave(mesr->file_name->name);
        cdi_meas_win->m_geom_selection = mesr->geom_selection;
        cdi_meas_win->start_time_defined_by = mprm->time_rel_solver;
        // cp_info.m_partitions_info.add_geom_selection_tree(i, mesr->geom_selection);

        if (mprm->start_via == eCDI_MEAS_START_TIME_VIA::AfterInitialTransient) {
          cdi_meas_win->start_time_via_monitors_p = TRUE;
          cdi_meas_win->meas_started_p = FALSE;
          ccDOTIMES(i, mprm->monitors.size()) {
            cdi_meas_win->master_monitors.push_back(mprm->monitors[i]);
          }
          cdi_meas_win->n_master_monitors_to_check = mprm->monitors.size();
        } else { // Start time via specifed time
          cdi_meas_win->start_time_via_monitors_p = FALSE;
          cdi_meas_win->start_time = mprm->start_time * lattice_to_user_steps;
        }

        cdi_meas_win->meas_end_time_via = mprm->end_via;

        if (cdi_meas_win->meas_end_time_via == eCDI_MEAS_END_TIME_VIA::Duration) {
          if (mprm->duration < mprm->period) {
            msg_warn("User specified duration %d timesteps is too short to allow at least one frame"
                     " for meas window \"%s\", thus it is extended to one meas window period.", 
                     mprm->duration,
                     cdi_meas_win->name);

            mprm->duration = mprm->period;
          }

          if (mprm->start_via == eCDI_MEAS_START_TIME_VIA::AfterInitialTransient)
            cdi_meas_win->end_time = TIMESTEP_MAX;
          else {
            cdi_meas_win->end_time = MIN(cdi_meas_win->start_time + (dFLOAT)mprm->duration * lattice_to_user_steps, TIMESTEP_MAX);
          }
        } else {
          cdi_meas_win->end_time = mprm->end_time * lattice_to_user_steps;
        }
        cdi_meas_win->duration = mprm->duration * lattice_to_user_steps;

        cdi_meas_win->num_frames = mprm->num_frames;
        cdi_meas_win->period = mprm->period * lattice_to_user_steps;
        cdi_meas_win->average_interval = mprm->average_interval * lattice_to_user_steps;
        cdi_meas_win->period_sync_group_index = mprm->period_sync_group_index;


        cdi_meas_win->mstp.meas_type = mstp->meas_type;
        cdi_meas_win->mstp.standard_mask = mstp->standard_mask;
        cdi_meas_win->mstp.custom_fluid_options = mstp->custom_fluid_options;
        cdi_meas_win->mstp.custom_surface_options = mstp->custom_surface_options;
        cdi_meas_win->mstp.custom_porous_options = mstp->custom_porous_options;
        cdi_meas_win->mstp.custom_shell_options = mstp->custom_shell_options;
        cdi_meas_win->mstp.custom_solid_volume_options = mstp->custom_solid_volume_options;
        cdi_meas_win->mstp.extended_std_var_options = mstp->extended_std_var_options;

        //The existence of a PRTJ chunk implies this window is a particle trajectory window.
        if(mesr->prtclTrajec != NULL) {
          cdi_meas_win->m_is_particle_trajectory_window = TRUE;
          cdi_meas_win->m_record_trajectories = mesr->prtclTrajec->trajectories.value;
          cdi_meas_win->m_record_velocities = mesr->prtclTrajec->recordVel.value;
          cdi_meas_win->m_use_dynamic_decimation = mesr->prtclTrajec->dynTrajDecimation.value;

          cdi_meas_win->m_decimation_tolerance = -1.0;
          cdi_meas_win->m_static_decimation_rate = -1;

          if(cdi_meas_win->m_use_dynamic_decimation)
            cdi_meas_win->m_decimation_tolerance = mesr->prtclTrajec->decimErrortol.value;
          else
            cdi_meas_win->m_static_decimation_rate = (asINT32)mprm->period * lattice_to_user_steps;

          cdi_meas_win->m_hitpoint_options = (HITPOINT_OPTIONS)mesr->prtclTrajec->hitPoints.value;
          cdi_meas_win->m_record_only_adhering_hitpoints = mesr->prtclTrajec->adherParticles.value;
          cdi_meas_win->m_record_hitpoint_normal_impulse = mesr->prtclTrajec->recordNormImpulse.value;
          cdi_meas_win->m_fraction_eligible_for_measurement = mesr->prtclTrajec->frac_trajectory_recording.value; //Added 2/28/17 to support changes for CDI 3.2 (-wanderer)
        }

        cdi_meas_win->mstp.custom_fluid_particle_options = mstp->custom_fluid_particle_options;
        cdi_meas_win->mstp.custom_surface_particle_options = mstp->custom_surface_particle_options;

        cdi_meas_win->m_per_emitter_particle_measurements = false;
        cdi_meas_win->m_per_material_particle_measurements = false;

        //Remove after CDI support complete. For testing per emitter an per material windows meas for particles.
        if(getenv("EXA_TEST_PER_EMITTER_PARTICLE_MEAS")  != NULL) { 
          msg_warn("Setting window %s to use per emitter measurements.", cdi_meas_win->name);
          cdi_meas_win->m_per_emitter_particle_measurements = true;
        } else  if(getenv("EXA_TEST_PER_MATERIAL_PARTICLE_MEAS")  != NULL) { 
          msg_warn("Setting window %s to use per material measurements.", cdi_meas_win->name);
          cdi_meas_win->m_per_material_particle_measurements = true;
        }

        if (mesr->force_filter) {
          cdi_meas_win->min_pressure = mesr->force_filter->min_pressure;
          cdi_meas_win->max_pressure = mesr->force_filter->max_pressure;
        }

        if (mesr->rp) {
          cdi_meas_win->reference_point[0] = mesr->rp->reference_point[0];
          cdi_meas_win->reference_point[1] = mesr->rp->reference_point[1];
          cdi_meas_win->reference_point[2] = mesr->rp->reference_point[2];
        }

        if (mesr->dev_win) {
          cdi_meas_win->seg_size = mesr->dev_win->seg_size;
          cdi_meas_win->icsys = mesr->dev_win->icsys;
          cdi_meas_win->ilrf = mesr->dev_win->ilrf;
        }

        asINT32 options = cdi_meas_win->mstp.standard_mask;
        CDI_MEASTYPE meas_type = cdi_meas_win->mstp.meas_type;
        BOOLEAN is_fluid_window = cdi_meas_opt_fluid_meas(options);
        BOOLEAN is_surface_window = cdi_meas_opt_surface_meas(options);
        BOOLEAN is_solid_vol_window = (cdi_meas_win->mstp.custom_solid_volume_options.num_vars() > 0);
        BOOLEAN is_shell_window = cdi_meas_opt_shell_meas(options);
        BOOLEAN is_porous_window = cdi_meas_opt_porous_meas(options);
        BOOLEAN is_composite = cdi_meas_type_composite_meas(meas_type);
        BOOLEAN is_development = cdi_meas_type_development_meas(meas_type);
        //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
        BOOLEAN is_trajectory = cdi_meas_win->m_is_particle_trajectory_window;
        cdi_meas_win->is_average_mme = cdi_meas_opt_average_mme_meas(options);
        //#endif
        cdi_meas_win->solver_mask = 0;

        if (cdi_meas_win->is_average_mme) {
          if (!cp_info.has_average_mme_window) {
            msg_internal_error("Unexpected average mme meas window.");
          }

          cp_info.is_avg_mme_ckpt_compressed = (options & CDI_MEAS_OPT_COMPRESS) > 0; 

          cdi_meas_win->start_time       = align_with_coarsest_timesteps(cdi_meas_win->start_time);
          cdi_meas_win->period           = align_with_coarsest_timesteps(cdi_meas_win->period);
          cdi_meas_win->average_interval = align_with_coarsest_timesteps(cdi_meas_win->average_interval);
          if (cdi_meas_win->average_interval > cdi_meas_win->period || cdi_meas_win->average_interval <= 0)
            cdi_meas_win->average_interval = cdi_meas_win->period;


          if (mprm->start_via == eCDI_MEAS_START_TIME_VIA::AfterInitialTransient)
            cp_info.avg_mme_ckpt_after_eit_p = FALSE;
          else
            cp_info.avg_mme_ckpt_after_eit_p = TRUE;    // If the avg mme meas window is not started via monitors, always use the regular avg mme ckpt filename.

          if (cdi_meas_win->meas_end_time_via == eCDI_MEAS_END_TIME_VIA::Duration) {
            if (mprm->start_via == eCDI_MEAS_START_TIME_VIA::AfterInitialTransient) {
              cdi_meas_win->end_time = TIMESTEP_MAX;
              cdi_meas_win->duration = align_with_coarsest_timesteps(mprm->duration * lattice_to_user_steps);
            } else {
              cdi_meas_win->end_time = MIN(cdi_meas_win->start_time + (dFLOAT)mprm->duration * lattice_to_user_steps, TIMESTEP_MAX);
              cdi_meas_win->end_time = align_with_coarsest_timesteps(cdi_meas_win->end_time);
              cdi_meas_win->duration = cdi_meas_win->end_time - cdi_meas_win->start_time;
            }
          } else {
            cdi_meas_win->end_time = mprm->end_time * lattice_to_user_steps;
            cdi_meas_win->end_time = align_with_coarsest_timesteps(cdi_meas_win->end_time);
          }

#if DEBUG_AVG_MME
          msg_print("Avg mme cdi meas win: start %f period %f interval %f end %f duration %d",
                    cdi_meas_win->start_time,
                    cdi_meas_win->period,
                    cdi_meas_win->average_interval,
                    cdi_meas_win->end_time,
                    cdi_meas_win->duration);
#endif
        }

        //Do not destroy the mesr chunk, we need the geometry selection tree
        //to propagate to measurement files
        //cdi_destroy_mesr(mesr);

        if (is_fluid_window) {
          CDI_MEAS_WINDOW_TYPE cdi_meas_window_type;
          BOOLEAN is_sampled_face = FALSE;
          if ((meas_type == CDI_MEASTYPE_SAMPLED_FACE) 
              || (meas_type == CDI_MEASTYPE_COMPOSITE_SAMPLED_FACE)) {
            cdi_meas_window_type = CDI_SURFACE_WINDOW;
            is_sampled_face = TRUE;
          } else
            cdi_meas_window_type = CDI_FLUID_WINDOW;

          cSRI_MEAS_VARS sri_meas_vars = cdi_mstp_vars(m_cdi_info,
                                                       &(cdi_meas_win->mstp), 
                                                       cdi_meas_window_type, 
                                                       asINT32(cp_info.lattice_type), 
                                                       cp_info.sim_ht_type, 
                                                       cp_info.is_ht_off_in_powercase,
                                                       is_3d, 
                                                       cp_info.is_std_meas_mme_p, 
                                                       cp_info.is_density_constant, 
                                                       cp_info.is_water_vapor_transport,
                                                       *(cp_info.cvid_helper),
                                                       1, 0, cp_info.is_melting_solver);
          asINT32 n_vars;
          SRI_VARIABLE_TYPE *var_types          = (SRI_VARIABLE_TYPE *)sri_meas_vars.get_vars(n_vars);
          cdi_meas_win->fluid_var_set.var_types = var_types;
          cdi_meas_win->fluid_var_set.n_vars    = n_vars;

          if (is_5g_sim) {
            if (is_composite)
              cdi_meas_win->flush_every_frame = TRUE;
            cdi_meas_win->fluid_var_set.var_types = expand_multi_component_vars(n_vars, var_types, FALSE, sim_args.output_component_velocity, sim_args.output_mass_exchange, is_composite, FALSE, is_development, is_sampled_face, cp_info.is_uds_transport);
            cdi_meas_win->fluid_var_set.n_vars    = n_vars;
            var_types = cdi_meas_win->fluid_var_set.var_types;
          } /*else if (cp_info.store_special_vars && !is_sampled_face && !is_composite && !is_development) {
              cdi_meas_win->fluid_var_set.var_types = add_special_vars_to_fluid(n_vars, var_types);
              cdi_meas_win->fluid_var_set.n_vars    = n_vars;
              var_types = cdi_meas_win->fluid_var_set.var_types;
              }*/
          else if (cp_info.variable_gamma && is_ht) {
            cdi_meas_win->fluid_var_set.var_types = add_total_temperature_to_meas(n_vars, var_types);
            cdi_meas_win->fluid_var_set.n_vars    = n_vars;
            var_types = cdi_meas_win->fluid_var_set.var_types;
          } else if (cp_info.is_seed_body_force){
            cdi_meas_win->fluid_var_set.var_types = add_density_or_pressure_to_meas(n_vars, var_types);
            cdi_meas_win->fluid_var_set.n_vars    = n_vars;
            var_types = cdi_meas_win->fluid_var_set.var_types;
	  }

          if (n_vars > 0)
            cdi_meas_win->solver_mask |= window_set_solver_mask_from_sri_vars(n_vars, var_types, is_ht, is_turb, is_active_ht);
        }

        const cSRI_MEAS_VARS *custom_conduction_solid_options = NULL;
        custom_conduction_solid_options =  &cdi_meas_win->mstp.custom_solid_volume_options;
        if(custom_conduction_solid_options) {
          cSRI_MEAS_VARS var_types;
          asINT32 n_vars = 0;
          if (is_solid_vol_window && ((meas_type == CDI_MEASTYPE_SAMPLED_FACE) 
              || (meas_type == CDI_MEASTYPE_COMPOSITE_SAMPLED_FACE))) {
            var_types.add_var(SRI_VARIABLE_TEMP); //PR53945 & 55646: What variables are needed? 
          } else {
            ccDOTIMES(i, custom_conduction_solid_options->num_vars()) {
              sriINT var = custom_conduction_solid_options->var(i);
              var_types.add_var(var);
            }
          }
          cdi_meas_win->conduction_solid_var_set.var_types = (SRI_VARIABLE_TYPE *)var_types.get_vars(n_vars);
          cdi_meas_win->conduction_solid_var_set.n_vars = n_vars;
          if (n_vars > 0 && cp_info.is_conduction)
            cdi_meas_win->solver_mask |= CONDUCTION_PDE_ACTIVE ;
        }
        if (is_surface_window) {
          asINT32 n_vars = 0;
          SRI_VARIABLE_TYPE *var_types = 0;
          if (is_5g_sim) {
            if (sim_args.output_surface_contact_angle_only) {
              n_vars = 1;
              var_types = sri_new_vector(SRI_VARIABLE_TYPE, n_vars);
              var_types[0] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_CONTACT_ANGLE);

              cdi_meas_win->surface_var_set.var_types = var_types;
              cdi_meas_win->surface_var_set.n_vars    = n_vars;
            } else {
              cSRI_MEAS_VARS sri_meas_vars = cdi_mstp_vars(m_cdi_info,
                                                           &(cdi_meas_win->mstp), 
                                                           CDI_SURFACE_WINDOW, 
                                                           asINT32(cp_info.lattice_type), 
                                                           cp_info.sim_ht_type,
                                                           cp_info.is_ht_off_in_powercase,
                                                           is_3d, cp_info.is_std_meas_mme_p, 
                                                           cp_info.is_density_constant, 
                                                           cp_info.is_water_vapor_transport,
                                                           *(cp_info.cvid_helper),
                                                           1, 0, cp_info.is_melting_solver);
              var_types            = (SRI_VARIABLE_TYPE *)sri_meas_vars.get_vars(n_vars);

              cdi_meas_win->surface_var_set.var_types = expand_multi_component_vars(n_vars, var_types,sim_args.output_surface_contact_angle, sim_args.output_component_velocity, FALSE, FALSE, TRUE, FALSE, FALSE, cp_info.is_uds_transport);
              cdi_meas_win->surface_var_set.n_vars    = n_vars;
              var_types = cdi_meas_win->surface_var_set.var_types;
            }
          } else {
            cSRI_MEAS_VARS sri_meas_vars = cdi_mstp_vars(m_cdi_info,
                                                         &(cdi_meas_win->mstp), 
                                                         CDI_SURFACE_WINDOW, 
                                                         asINT32(cp_info.lattice_type), 
                                                         cp_info.sim_ht_type,
                                                         cp_info.is_ht_off_in_powercase,
                                                         is_3d, cp_info.is_std_meas_mme_p, 
                                                         cp_info.is_density_constant, 
                                                         cp_info.is_water_vapor_transport,
                                                         *(cp_info.cvid_helper),
                                                         1, 0, cp_info.is_melting_solver);

            var_types            = (SRI_VARIABLE_TYPE *)sri_meas_vars.get_vars(n_vars);
            cdi_meas_win->surface_var_set.var_types = var_types;
            cdi_meas_win->surface_var_set.n_vars    = n_vars;

            if (cp_info.variable_gamma && is_ht) {
              cdi_meas_win->surface_var_set.var_types = add_total_temperature_to_meas(n_vars, var_types);
              cdi_meas_win->surface_var_set.n_vars    = n_vars;
              var_types = cdi_meas_win->surface_var_set.var_types;
            }
          }

          if (n_vars > 0)
            cdi_meas_win->solver_mask |= window_set_solver_mask_from_sri_vars(n_vars, var_types, is_ht, is_turb, is_active_ht);
        }

        const cSRI_MEAS_VARS *custom_shell_options = NULL;
        custom_shell_options = &cdi_meas_win->mstp.custom_shell_options;
        if(custom_shell_options) {
          cSRI_MEAS_VARS var_types;
          asINT32 n_vars = 0;
          ccDOTIMES(i, custom_shell_options->num_vars()) {
            sriINT var = custom_shell_options->var(i);
            var_types.add_var(var);
          }
            cdi_meas_win->shell_var_set.var_types = (SRI_VARIABLE_TYPE *)var_types.get_vars(n_vars);
            cdi_meas_win->shell_var_set.n_vars = n_vars;
            if(cp_info.is_conduction)
              cdi_meas_win->solver_mask |= CONDUCTION_PDE_ACTIVE;
        }

        if (is_porous_window) {
          cSRI_MEAS_VARS sri_meas_vars = cdi_mstp_vars(m_cdi_info,
                                                       &(cdi_meas_win->mstp), 
                                                       CDI_POROUS_WINDOW, 
                                                       asINT32(cp_info.lattice_type), 
                                                       cp_info.sim_ht_type,
                                                       cp_info.is_ht_off_in_powercase,
                                                       is_3d, cp_info.is_std_meas_mme_p, 
                                                       cp_info.is_density_constant, 
                                                       cp_info.is_water_vapor_transport,
                                                       *(cp_info.cvid_helper),
                                                       1, 0, cp_info.is_melting_solver);
          asINT32 n_vars;
          SRI_VARIABLE_TYPE *var_types           = (SRI_VARIABLE_TYPE *)sri_meas_vars.get_vars(n_vars);
          cdi_meas_win->porous_var_set.var_types = var_types;
          cdi_meas_win->porous_var_set.n_vars    = n_vars;

          if (is_5g_sim) {
            if (is_composite)
              cdi_meas_win->flush_every_frame = TRUE;
            cdi_meas_win->porous_var_set.var_types = expand_multi_component_vars(n_vars, var_types, FALSE, sim_args.output_component_velocity, sim_args.output_mass_exchange, is_composite, FALSE, FALSE, FALSE, cp_info.is_uds_transport);
            cdi_meas_win->porous_var_set.n_vars    = n_vars;
            var_types = cdi_meas_win->porous_var_set.var_types;
          } /*else if (cp_info.store_special_vars && !is_composite && !is_development) {
              cdi_meas_win->porous_var_set.var_types = add_special_vars_to_fluid(n_vars, var_types);
              cdi_meas_win->porous_var_set.n_vars    = n_vars;
              var_types = cdi_meas_win->porous_var_set.var_types;
              }*/
          else if (cp_info.variable_gamma && is_ht) {
            cdi_meas_win->porous_var_set.var_types = add_total_temperature_to_meas(n_vars, var_types);
            cdi_meas_win->porous_var_set.n_vars    = n_vars;
            var_types = cdi_meas_win->porous_var_set.var_types;
          }

          if (n_vars > 0) 
            cdi_meas_win->solver_mask |= window_set_solver_mask_from_sri_vars(n_vars, var_types, is_ht, is_turb, is_active_ht);
        }

        //Disable trajectory windows if particle modeling has been disabled via command line argument
        if(sim_args.disable_particle_modeling == TRUE && cdi_meas_win->m_is_particle_trajectory_window) { //PR 49448
          msg_warn("Disabling trajectory window \"%s\".", cdi_meas_win->name);
          cdi_meas_win->start_time = TIMESTEP_MAX;
        }

        cdi_meas_win++;
        n_windows++;
      }
    }
  }

  cp_info.n_cdi_meas_windows = n_windows;
  cp_info.n_non_surf_coupling_cdi_meas_windows = n_windows;

  validate_cmdline_meas_timing_options();
  validate_cmdline_include_exclude_meas_windows();
  disable_all_meas_windows_if_requested();
  disable_meas_windows_via_meas_include_exclude_options();

  // Only check if sim duration is determined by meas windows
  cp_info.n_meas_windows_to_check_end_time = 0;
  if (cp_info.sim_duration_via == eCDI_SIM_DURATION_VIA::GreatestMeasurementEndTime) {
    TIMESTEP sim_end_time = TIMESTEP_INVALID;
    DO_CDI_MEAS_WINDOWS(cdi_meas_win) {
      if (cdi_meas_win->disabled_by_meas_include_exclude)
        continue;
      if (cdi_meas_win->meas_end_time_via == eCDI_MEAS_END_TIME_VIA::EndTime ||
          (cdi_meas_win->meas_end_time_via == eCDI_MEAS_END_TIME_VIA::Duration && !cdi_meas_win->start_time_via_monitors_p)) {
        // Ignore hx meas windows and meas windows with default end time
        if (cdi_meas_win->end_time != CDI_INFINITE_END_TIME) {
          sim_end_time = MAX(sim_end_time, cdi_meas_win->end_time);
        }
      } else {
        cp_info.n_meas_windows_to_check_end_time++;
      }
    }
   
    cp_info.greatest_meas_window_end_time = sim_end_time;
    if (cp_info.n_meas_windows_to_check_end_time == 0) {
      if (sim_end_time == TIMESTEP_INVALID)
        msg_error("At least one measurement window should have non-default end time when the simulation duration is via greatest measurement end time.");
      if ((TIMESTEP)cp_info.end_time == TIMESTEP_MAX)  // If current end time is set by user (either from exaqsub or from exasignal)
                                             // do not request to stop. See PR41559.
        register_async_event_request(EVENT_ID_EXIT, 0, sim_end_time, FALSE, FALSE);
    } else {
#if DEBUG_MONITOR
      msg_print("%d CDI meas windows need to be checked for finding the greatest meas window end time", 
                cp_info.n_meas_windows_to_check_end_time);
#endif
    }
  }

  exit_cdi_chunk();
}

#if SURF_COUP
VOID sCP_CDI_READER::read_scpl_chunk(BOOLEAN is_ckpt_restore_p, BOOLEAN is_full_ckpt_restore_p)
{

  asINT32 n_inner_chunks = cio_get_count(m_cdi_info->cio_info);

  if (n_inner_chunks == 0)
    return;

  sCDI_SCCT scct;
  CDI_WITH_INNER_CHUNK(m_cdi_info) {
    if (cdi_get_type(m_cdi_info) != CDI_CHUNK_TYPE_SCCT) {
      msg_internal_error("Expected SCCT as first sub-chunk of CDI SCPL chunk.");
    } else {
      if (!cdi_read_scct(m_cdi_info, &scct))
        cdi_report_read_error("scct");
    }
  }

  if (scct.n_models + scct.n_bcs +  scct.n_walls + 1 != n_inner_chunks) {
    msg_internal_error("Number of subchunks of SCPL is inconsistent with SCCT chunk.");
  }
  cp_info.n_surface_couplings = scct.n_models;
  cp_info.surface_couplings = cnew cSURFACE_COUPLING[scct.n_models];

  if (cp_info.n_surface_couplings > TAG_MAX_INDEX_VALUE) {
    msg_error("Use of more than %zu surface coupling models is unsupported.", TAG_MAX_INDEX_VALUE);
  }

  ccDOTIMES(model_ix, scct.n_models) {
    CDI_WITH_INNER_CHUNK(m_cdi_info) {
      if (cdi_get_type(m_cdi_info) != CDI_CHUNK_TYPE_CMDL) {
        msg_internal_error("Sub-chunk %d of CDI SCPL chunk should be a CMDL chunk.", model_ix + 1);
      } else {
        if (!cdi_read_cmdl(m_cdi_info, &cp_info.surface_couplings[model_ix].cmdl))
          cdi_report_read_error("cmdl"); 

        // If coupling phase table is used, do sanity checks here.
        CDI_CMDL cmdl = &cp_info.surface_couplings[model_ix].cmdl;
        if (cmdl->n_coupling_phases <= 0)
          msg_internal_error("Coupling phases are missing for the coupling model \"%s\".", cmdl->model_filename);
        ccDOTIMES(phase_index, cmdl->n_coupling_phases)
        {
          asINT32 period = cmdl->m_coupling_phase_descs[phase_index].period;
          if (cmdl->m_coupling_phase_descs[phase_index].interval > period)
          {
            msg_warn("Coupling phase %d interval > period. Changed interval from %d to %d.", phase_index+1,
                      cmdl->m_coupling_phase_descs[phase_index].interval, period);
            cmdl->m_coupling_phase_descs[phase_index].interval = period;
          }
          if (cmdl->m_coupling_phase_descs[phase_index].delay >= period)
          {
            msg_warn("Coupling phase %d delay >= period. Changed delay from %d to %d.", phase_index+1,
                      cmdl->m_coupling_phase_descs[phase_index].delay, period - 1);
            cmdl->m_coupling_phase_descs[phase_index].delay = period - 1;
          }
          if (cmdl->m_coupling_phase_descs[phase_index].adaptive_p) {
            cp_info.adaptive_coupling_p = TRUE;
            if (cmdl->m_coupling_phase_descs[phase_index].therm_time_ratio > cmdl->ratio_max) {
              msg_warn("Coupling phase %d ratio %f > max adaptive ratio %f. Changed the max adaptive ratio to %f",
                       phase_index, cmdl->m_coupling_phase_descs[phase_index].therm_time_ratio, cmdl->ratio_max, 
                       cmdl->m_coupling_phase_descs[phase_index].therm_time_ratio);
              cmdl->ratio_max = cmdl->m_coupling_phase_descs[phase_index].therm_time_ratio;
            }
          }
        }

        cp_info.m_adaptive_params.adaptive_up_coeff = cmdl->adaptive_up_coeff;
        cp_info.m_adaptive_params.gradient_low = cmdl->gradient_low;
        cp_info.m_adaptive_params.ratio_max = cmdl->ratio_max;
        cp_info.m_adaptive_params.gradient_percentage_threshold = cmdl->gradient_percentage_threshold;
        cp_info.m_adaptive_params.fix_pt_time_p = cmdl->fix_pt_time_p;
        cp_info.m_adaptive_params.total_pt_duration = cmdl->total_pt_duration;
      }
    }
  }

  std::vector<asINT32> model_num_cplws(scct.n_models);
  std::vector<asINT32> model_num_scbcs(scct.n_models);
  std::fill(model_num_cplws.begin(), model_num_cplws.end(), 0);
  std::fill(model_num_scbcs.begin(), model_num_scbcs.end(), 0);


  cTHIRD_PARTY_INSTANCE_LIST &tpiList = cTHIRD_PARTY_INSTANCE_LIST::Instance();
  ccDOTIMES(model_ix, scct.n_models) {
    asINT32 num_cplws = cp_info.surface_couplings[model_ix].cmdl.num_pf_bcs;
    asINT32 num_scbcs = cp_info.surface_couplings[model_ix].cmdl.num_coupling_model_bcs;
    cSTRING model_type = cp_info.surface_couplings[model_ix].cmdl.model_type;
    STRING model_filename = cp_info.surface_couplings[model_ix].cmdl.model_filename;
    STRING abs_model_filename = cp_info.surface_couplings[model_ix].cmdl.absolute_model_filename;
    STRING results_filename = cp_info.surface_couplings[model_ix].cmdl.results_filename;
    STRING abs_results_filename = cp_info.surface_couplings[model_ix].cmdl.absolute_results_filename;
    if (!model_filename || !results_filename) {
      msg_error("Model filename or results filename not specified for coupling model %d",model_ix);
    }

    CHARACTER tdf_file[PLATFORM_MAXPATHLEN];
    CHARACTER tpi_file[PLATFORM_MAXPATHLEN];
    CHARACTER orig_model_file_dir[PLATFORM_MAXPATHLEN];
    if (IS_TAI_APP(model_type)) {
      // copies the model tdf and view factor file (if available), result file is
      // the same as the model file
      copy_initial_powertherm_coupling_files(model_filename,abs_model_filename,model_type,
                                             is_ckpt_restore_p, is_full_ckpt_restore_p, tpi_file, orig_model_file_dir, tdf_file);
      model_filename = tpi_file;
      results_filename = tpi_file;
    } else {
      msg_error("Unknown coupling model type %s",model_type);
    }

    cp_info.surface_couplings[model_ix].tpi = tpiList.CreateAppInterface(model_type,model_filename); 
    if (!cp_info.surface_couplings[model_ix].tpi) {
      msg_error("Could not create interface to %s",model_type);
    }
    cp_info.surface_couplings[model_ix].tpi->SetResultPath(results_filename); 
    if (strlen(orig_model_file_dir) > 1) {
      cp_info.surface_couplings[model_ix].tpi->SetDependentFileSearchPath(orig_model_file_dir); 
    }

    // For human comfort case, copy or make symlinks of relevant files.
    maybe_copy_human_comfort_files(tpi_file, orig_model_file_dir, model_ix, is_full_ckpt_restore_p);
    maybe_copy_battery_config_file(tdf_file, tpi_file, orig_model_file_dir, model_ix, is_ckpt_restore_p, is_full_ckpt_restore_p);
    // Check if distributed solver is supported by the tdf file if the env variable is not set
    if (getenv("EXA_DISABLE_DISTRIBUTED_SOLVER_CHECK") == NULL)
      check_distributed_solver(tpi_file, model_ix);

    cp_info.surface_couplings[model_ix].apply_coupling_command_line_options();
    // Check to see if the coupling application has been licensed
    if (TPI_LICENSE_UNAVAILABLE == cp_info.surface_couplings[model_ix].tpi->GetLicenseStatus()) {
      msg_error("License not present for %s",model_type);
    }

    maybe_warn_disable_adaptive_stepsize(tpi_file, model_ix);

    cp_info.surface_couplings[model_ix].native_to_foreign_map = new sSIM_INTERP_MAP(INTERP_TYPE_EF2EF_NEAREST, NULL);
    cp_info.surface_couplings[model_ix].foreign_to_native_map = new sSIM_INTERP_MAP(INTERP_TYPE_EF2EF_BASIS, NULL);
    cp_info.surface_couplings[model_ix].scbcs = cnew sCDI_SCBC[num_scbcs];
    cp_info.surface_couplings[model_ix].native_source_surface = cnew SIM_ABSTRACT_SURFACE[num_scbcs];
    cp_info.surface_couplings[model_ix].foreign_target_surface = cnew SIM_ABSTRACT_SURFACE[num_scbcs];
    cp_info.surface_couplings[model_ix].cplws = cnew sCDI_CPLW[num_cplws];
    cp_info.surface_couplings[model_ix].native_target_surface = cnew SIM_ABSTRACT_SURFACE[num_cplws];
    cp_info.surface_couplings[model_ix].foreign_source_surface = cnew SIM_ABSTRACT_SURFACE[num_cplws];

    // allocate data for the target and source surfaces as appropriate
    cp_info.surface_couplings[model_ix].native_target_data = cnew SIM_SURFACE_DATA[num_cplws];
    cp_info.surface_couplings[model_ix].foreign_target_data = cnew SIM_SURFACE_DATA[num_scbcs];
    cp_info.surface_couplings[model_ix].native_resulting_source_surface = 
      cp_info.surface_couplings[model_ix].foreign_resulting_source_surface = NULL;
    cp_info.surface_couplings[model_ix].foreign_resulting_source_data = 
      cnew sSIM_SURFACE_DATA[cp_info.surface_couplings[model_ix].n_import_vars()];

  }

  ccDOTIMES(model_ix, scct.n_models) {
    // check for duplicate model filenames to prevent overwriting the same TDF file
    STRING model_filename = cp_info.surface_couplings[model_ix].cmdl.model_filename;
    ccDOTIMES(model_ix2, scct.n_models) {
      STRING model_filename2 = cp_info.surface_couplings[model_ix2].cmdl.model_filename;
      if (model_ix2 == model_ix)
        continue;
      if (!strcmp(model_filename2, model_filename))
        msg_error("Found duplicate surface coupling model \"%s\".", model_filename); 
    }
  }

  ccDOTIMES(scbc_ix, scct.n_bcs) {
    CDI_WITH_INNER_CHUNK(m_cdi_info) {
      if (cdi_get_type(m_cdi_info) != CDI_CHUNK_TYPE_SCBC) {
        msg_internal_error("Sub-chunk %d of CDI SCPL chunk should be a SCBC chunk.",scct.n_models + scbc_ix + 1);
      } 
      sCDI_SCBC scbc;
      if (!cdi_read_scbc(m_cdi_info, &scbc))
        cdi_report_read_error("scbc");

      asINT32 model_ix = scbc.model_index;
      asINT32 model_scbc_ix = model_num_scbcs[model_ix]++;

      // invoke the default copy constructor on the SCBC struct
      cp_info.surface_couplings[model_ix].scbcs[model_scbc_ix] = scbc; 
      cp_info.surface_couplings[model_ix].native_source_surface[model_scbc_ix] = new sSIM_ABSTRACT_SURFACE;
      cp_info.surface_couplings[model_ix].foreign_target_surface[model_scbc_ix] = new sSIM_ABSTRACT_SURFACE;
      cp_info.surface_couplings[model_ix].foreign_target_data[model_scbc_ix] = new sSIM_SURFACE_DATA[cp_info.surface_couplings[model_ix].n_export_vars()];
    }
  }

  ccDOTIMES(cplw_ix, scct.n_walls) {
    CDI_WITH_INNER_CHUNK(m_cdi_info) {
      if (cdi_get_type(m_cdi_info) != CDI_CHUNK_TYPE_CPLW) {
        msg_internal_error("Sub-chunk %d of CDI SCPL chunk should be a CPLW chunk.",scct.n_models + scct.n_bcs + cplw_ix + 1);
      } 
      sCDI_CPLW cplw;
      if (!cdi_read_cplw(m_cdi_info, &cplw))
        cdi_report_read_error("cplw");

      asINT32 model_ix = cplw.model_index;
      asINT32 model_cplw_ix = model_num_cplws[model_ix]++;

      // invoke the default copy constructor on the CPLW struct
      cp_info.surface_couplings[model_ix].cplws[model_cplw_ix] = cplw; 
      cp_info.surface_couplings[model_ix].native_target_surface[model_cplw_ix] = new sSIM_ABSTRACT_SURFACE;
      cp_info.surface_couplings[model_ix].foreign_source_surface[model_cplw_ix] = new sSIM_ABSTRACT_SURFACE;
      cp_info.surface_couplings[model_ix].native_target_data[model_cplw_ix] = new sSIM_SURFACE_DATA[cp_info.surface_couplings[model_ix].n_import_vars()];
    }
  }

  ccDOTIMES(model_ix, scct.n_models) {
    cp_info.surface_couplings[model_ix].BuildForeignModel();
  }

}

// CPU affinity/binding for PowerTHERM

// This is necessary because the CPU_COUNT macro is defined only in glibc 2.6 and later,
// and is available on Ion, but not on Plasma.


static auINT32 cpu_count(cpu_set_t *set) {
  auINT32 count = 0;
  ccDOTIMES(cpu, CPU_SETSIZE) {
    if(CPU_ISSET(cpu, set)) count++;
  }
  return count;
}

VOID print_cpus(cpu_set_t *set) {
  msg_print("Procs in CPU mask (size %d):",CPU_SETSIZE);
  ccDOTIMES(cpu, CPU_SETSIZE) {
    if(CPU_ISSET(cpu, set)) {
      msg_print("    %2d",cpu);
    }
  }
}
// This implementation assumes cyclic binding of the CP and SP's.
// Terminology: a logical processor corresponds to a bit in the affinity
// mask. With hyperthreading on, there are two logical processors per
// physical core; with it off, there is one.

// This returns an affinity mask for use by sched_setaffinity, or, if not valid,
// a zero mask. Thus the return value should always be tested before use.

static cpu_set_t make_cpu_mask(SIM_ARGS sim_args) {
  cpu_set_t cpumask;
  CPU_ZERO(&cpumask);

  auINT32 n_pf_ranks = sim_args->n_cpnode_ranks;              // Includes the CP and all SP's on the same node as the CP
  auINT32 n_sockets = sim_args->n_cpnode_sockets;
  auINT32 n_scores = sim_args->n_cpnode_cores_per_socket;    
  auINT32 n_cthreads = sim_args->n_cpnode_threads_per_core;

  if((n_pf_ranks == 0) || ( n_sockets == 0) || (n_scores == 0) || (n_cthreads == 0)) {
    msg_warn("Insufficient information to assign affinity to PowerTHERM processes or threads");
    return cpumask;
  }

  auINT32 n_cores =  n_scores * n_sockets;                  // Total physical cores
  auINT32 n_lprocs =  n_cores * n_cthreads;                 // Total logical processors
  auINT32 n_pf_lprocs = n_pf_ranks * n_cthreads;

  // Since PowerTHERM doesn't have a nice dual-threaded structure like the POwerFLOW SP,
  // we won't assume it's OK to assign two ptherm threads to a single physical core,
  // even with hyperthreading on. Instead we attempt to give each PowerTHERM thread a physical
  // core. If the total number of ptherm threads + PowerFLOW ranks is more than the number of cores on the node,
  // we let ptherm threads compete with POwerFLOW ranks, rather than with each other.

  auINT32 n_pt_lprocs;
  if(cp_info.ptherm_processes == 0) {
    n_pt_lprocs = cp_info.ptherm_threads * n_cthreads;
  } else {
    if(cp_info.ptherm_threads == 0) {
      n_pt_lprocs = cp_info.ptherm_processes * n_cthreads;
    } else {
      n_pt_lprocs = cp_info.ptherm_processes * cp_info.ptherm_threads;
    }
  }
#ifdef DEBUG_PTHERM_AFFINITY
  msg_print("Computing CPU affinity mask for PowerTHERM:");
  msg_print("    n_pf_ranks         %2d",n_pf_ranks);
  msg_print("    n_sockets          %2d",n_sockets);
  msg_print("    n_scores           %2d",n_scores);
  msg_print("    n_cthreads         %2d",n_cthreads);
  msg_print("    n_cores            %2d",n_cores);
  msg_print("    n_lprocs           %2d",n_lprocs);
  msg_print("    ptherm_processes   %2d",cp_info.ptherm_processes);
  msg_print("    ptherm_threads     %2d",cp_info.ptherm_threads);
  msg_print("    n_pt_lprocs        %2d",n_pt_lprocs);
#endif
  auINT32 *next_core = xnew auINT32[n_sockets];    // Next core to use, by socket
  auINT32 *cycproc = xnew  auINT32[n_lprocs];     // cyclic sequence of logical processor numbers
  
  // Initialize the per-socket core numbers
  auINT32 core = 0;
  ccDOTIMES(sock, n_sockets) {
    next_core[sock] = core;
    core += n_scores;
  }

  // Generate the cyclic sequence

  auINT32 socket = 0;
  auINT32 lproc = 0;
  ccDOTIMES(cn, n_cores) {
    ccDOTIMES(cthread, n_cthreads) {
      cycproc[lproc] = next_core[socket] + cthread * n_cores;
      lproc++;
    }
    next_core[socket]++;
    socket++;
    if(socket == (n_sockets)) socket = 0;
  }

#ifdef DEBUG_PTHERM_AFFINITY
#if 0
  msg_print("Cyclic sequence of lprocs:");
  ccDOTIMES(lp, n_lprocs) {
    msg_print("    %2d",cycproc[lp]);
  } 
#endif

  msg_print("Logical processors assigned to PowerFLOW:");
  ccDOTIMES(pt, n_pf_ranks * n_cthreads) {
    msg_print("    %2d",cycproc[pt%n_lprocs]);
  } 
#endif

  // Assign logical processors to PowerTHERM threads

  // We want the ptherm logical processor assignments to start where the pflow ranks left off

  auINT32 pt_lproc = n_pf_lprocs;
  ccDOTIMES(pt_thread, n_pt_lprocs) {
    // If all logical processors are occupied, no need to proceed since cpumask is completely filled
    if (cpu_count(&cpumask) == n_lprocs)
      break;

    while(CPU_ISSET(cycproc[pt_lproc%n_lprocs], &cpumask)) {
      pt_lproc = (pt_lproc == (n_lprocs - 1)) ? 0 : pt_lproc + 1;  // Skip lprocs that are already set
    }
    CPU_SET(cycproc[pt_lproc%n_lprocs], &cpumask);
    pt_lproc = (pt_lproc == (n_lprocs - 1)) ? 0 : pt_lproc + 1;
  }

#ifdef DEBUG_PTHERM_AFFINITY
  msg_print("CPU mask count is %d",cpu_count(&cpumask));
  print_cpus(&cpumask);
#endif

  delete[] next_core;
  delete[] cycproc;

  return cpumask;
}



VOID sCP_CDI_READER::init_surface_coupling(SIM_ARGS sim_args, const char *pf_path) 
{
  // startup the coupling daemon if required 
  if (cTHIRD_PARTY_INTERFACE::IsConnectToPortDisabled()) {
    // Powertherm not invoked with connectonport option (4.2c and prior behavior)
    cSTRING message = cTHIRD_PARTY_INTERFACE::InitializeCouplingDaemonInterface(pf_path);
    if (message) {
      msg_error("%s", message);
    }
    return;
  }

  // peek at the cdi file, and initialize coupling models to launch PowerTHERM immediately 
  // with the connect on port option
  open_file(sim_args->cdi_filename);

  ccCDI_DO_INNER_CHUNKS(case_child, "case", m_cdi_info) {
    asINT32 type = cdi_get_type(m_cdi_info);

    if (type != CDI_CHUNK_TYPE_SCPL)
      continue;

    m_is_scpl_present = true;
    BOOLEAN is_full_checkpoint_restore = (sim_args->resume_filename && !g_seed_ctl.is_mme_checkpoint());
    BOOLEAN is_mme_checkpoint_restore = g_seed_ctl.is_mme_checkpoint();
    read_scpl_chunk((is_full_checkpoint_restore || is_mme_checkpoint_restore), is_full_checkpoint_restore);
    break;
  }

  close_file();

  std::vector<bool> license_warning_issued(cp_info.n_surface_couplings);
  std::fill(license_warning_issued.begin(), license_warning_issued.end(), false);

  if (cp_info.n_surface_couplings <= 0)
    return;

  if (cTHIRD_PARTY_INTERFACE::InitPort() != TPI_SUCCESS) {
    msg_error("Failed to initialize the command connection for surface coupling");
  }

  cpu_set_t cpumask = make_cpu_mask(sim_args);
  ccDOTIMES(model_ix, cp_info.n_surface_couplings) {
    cTHIRD_PARTY_INTERFACE *tpi = cp_info.surface_couplings[model_ix].tpi;
    // don't set afffinity if launching ptherm on a remote host, or if
    // make_cpu_mask failed
    if((cpu_count(&cpumask) != 0) && (cp_info.ptherm_remote_host == NULL)) {
      tpi->SetCPUMask(cpumask);
      tpi->EnableAffinity(TRUE);
    } else {
      tpi->EnableAffinity(FALSE);
    }
    eTPI_STATUS status = tpi->LaunchClient();
    eTPI_JOB_STATUS job_status = tpi->GetJobStatus(true);
    CDI_CMDL cmdl = cp_info.surface_couplings[model_ix].get_cmdl();
    if (status != TPI_SUCCESS || job_status == TPI_JOB_COMPLETED_NOLICENSE) {
      dump_tpi_messages(tpi, cmdl->model_filename);
      if (job_status == TPI_JOB_COMPLETED_NOLICENSE) {
        if (cp_info.ptherm_wait_for_license) {
          // retry launching the client, and running the job again
          // also check for license availability
          const int SECONDS_BETWEEN_LICENSE_CHECKS = 10;
          BOOLEAN license_warning_issued = FALSE;

          while (tpi->GetJobStatus(true) == TPI_JOB_COMPLETED_NOLICENSE) {
            if (!license_warning_issued) { 
              msg_warn("Cannot obtain a license for the %s job for model \"%s\". Trying again...",
                       cmdl->model_type, cmdl->model_filename);
              license_warning_issued = TRUE;
            }
            platform_sleep_seconds(SECONDS_BETWEEN_LICENSE_CHECKS);
            status = tpi->LaunchClient();
          }
          if (status != TPI_SUCCESS) {
            dump_tpi_messages(tpi, cmdl->model_filename);
            msg_error("Failed to launch %s job (model \"%s\"). Check the log file for details.", 
                      cmdl->model_type,cmdl->model_filename);
          }
          if (license_warning_issued) {
            msg_print("Obtained a license for the %s job for model \"%s\".",
                      cmdl->model_type, cmdl->model_filename);
            tpi->ClearMessages(); // clear warning messages
          }
        } // wait for license
        else {
          msg_error("Unable to obtain licenses to run %s for model \"%s\". Please check the %s log file for details",
                    cmdl->model_type, cmdl->model_filename, cmdl->model_type);
        }
      }
      else {
        msg_error("Failed to launch %s job (model \"%s\"). Check the log file for details.", 
                  cmdl->model_type,cmdl->model_filename);
      }
    }
  }

}

#endif // SURF_COUP
 


//----------------------------------------------------------------------------
// write_phys_desc_header
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::write_phys_desc_header_to_buffer(int tag_id)
{
  LGI_PHYSICS_DESCRIPTORS_HEADER header;
  header.tag.id               = tag_id;
  header.tag.length           = 0; // Filled in below
  header.n_physics_descs      = 0;  // Filled in below 
  header.flags                = 0;

  if(tag_id == LGI_FLUID_PHYSICS_DESCRIPTORS_TAG) {
    if (m_user_specified_dns_nu_over_t)
      header.flags |= LGI_USER_SPECIFIED_DNS_NU_OVER_T;
  }

  m_pds.write(&header, sizeof(header));
}

//----------------------------------------------------------------------------
// write_phys_desc_buffer_to_sps
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::write_phys_desc_buffer_to_sps(int n_descriptors)
{
  LGI_PHYSICS_DESCRIPTORS_HEADER *h = (LGI_PHYSICS_DESCRIPTORS_HEADER *)m_pds.buffer;
  h->tag.length       = lgi_pad_and_encode_record_length(m_pds.size);
  h->n_physics_descs  = n_descriptors;
  
  write_record_to_all_sps(m_pds.buffer);
}

//----------------------------------------------------------------------------
// read_global_reference_frame
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_global_reference_frame()
{ 
  if (!m_is_nirf_present)
    return;

  die_if_gpu(true, "Global Reference Frames",true);

  enter_cdi_chunk(CDI_CHUNK_TYPE_NIRF);
  
  m_pds.size =0;
  read_global_physics_chunk("global non-inertial frame", LGI_NON_INERTIAL_FRAME_TAG,
                            cdi_lookup_physics(CDI_PHYS_TYPE_NON_INERTIAL_FRAME), TRUE, FALSE);
  cp_info.is_global_ref_frame = TRUE;
  exit_cdi_chunk();

}

//----------------------------------------------------------------------------
// read_gravity_and_buoyancy
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_gravity_and_buoyancy()
{ 
  if (!m_is_grav_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_GRAV);

  die_if_gpu(true, "Gravity and Buoyancy", true);

  m_pds.size =0;
  read_global_physics_chunk("gravity and buoyancy", LGI_GRAVITY_BUOYANCY_TAG,
                            cdi_lookup_physics(CDI_PHYS_TYPE_GRAVITY_BUOYANCY), TRUE, FALSE);
  exit_cdi_chunk();
}

//----------------------------------------------------------------------------
// read_local_ref_frames
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_local_reference_frames()
{ 
  if (!m_is_lrfs_present)
    return;

  enter_cdi_chunk(CDI_CHUNK_TYPE_LRFS);

  asINT32 n_ref_frames = 0;
  cp_info.n_rotational_dynamics_descs = 0;
  asINT32 max_lrfs = cio_get_count(m_cdi_info->cio_info);

  if (max_lrfs == 0) {
    exit_cdi_chunk();
    return;
  }

  cp_info.sri_lrfs.reserve(max_lrfs);
  cp_info.sri_lrf_polyline_vertices.reserve(max_lrfs);
  CDI_LRF *cdi_lrfs = cnew CDI_LRF [max_lrfs];

  ccCDI_DO_INNER_CHUNKS(j, "lrfs", m_cdi_info) {
    CIO_CCCC chunk_type = cdi_get_type(m_cdi_info);
    if (chunk_type == CDI_CHUNK_TYPE_SLRF
        || chunk_type == CDI_CHUNK_TYPE_MLRF) {
      CDI_LRF cdi_lrf = cdi_read_lrf(m_cdi_info, true);
      sSRI_LRF sri_lrf;
      sri_lrf.name = cdi_lrf->name;
      sri_lrf.n_polyline_vertices = cdi_lrf->pnts.num_points;
      sri_lrf.type = cdi_lrf->lrf_type == CDI_SLIDING_LRF ? SRI_LRF_MOVING : SRI_LRF_STATIONARY;
      die_if_gpu(sri_lrf.type == SRI_LRF_STATIONARY, "Stationary Mesh (MRF)", false);
      sri_lrf.parent_index = -1; // Populated from LGI_CONTAINING
      sri_lrf.boundary_face_index = -1;
      sri_lrf.boundary_type = SRI_LRF_NOT_WALL_BOUNDED; // not relevant to PowerVIZ
      cp_info.sri_lrfs.push_back(sri_lrf);

      cp_info.sri_lrf_polyline_vertices.push_back(cdi_lrf->pnts.points);

      cdi_lrfs[n_ref_frames] = cdi_lrf;
      n_ref_frames++;

      if (cdi_lrf->is_velocity_via_torque) {
        cp_info.n_rotational_dynamics_descs++;
      }
    }
  }

  cp_info.sri_lrf_has_constant_angular_vel_flags = cnew cBOOLEAN [n_ref_frames];
  cp_info.lrf_n_fluid_like_voxels = cnew sriPOINT [n_ref_frames];
  cp_info.rotational_dynamics_descs = (cp_info.n_rotational_dynamics_descs > 0)? cnew sCP_ROTATIONAL_DYNAMICS_DESC[cp_info.n_rotational_dynamics_descs] : NULL;
  asINT32 rotdyn_desc_index = 0;
  CP_ROTATIONAL_DYNAMICS_DESC rotdyn_desc = cp_info.rotational_dynamics_descs;
  dFLOAT cdi_start_time = -1, cdi_end_time = -1;

  m_pds.size = 0;
  write_phys_desc_header_to_buffer(LGI_LRF_PHYSICS_DESCRIPTORS_TAG);
  ccDOTIMES (i, n_ref_frames) {
    LGI_PHYSICS_DESCRIPTOR physics_desc;
    CDI_LRF cdi_lrf = cdi_lrfs[i];
    CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc = cdi_lookup_physics(CDI_PHYS_TYPE_LOCAL_REF_FRAME);
    physics_desc.tag.id               = LGI_LRF_PHYSICS_DESCRIPTORS_TAG;
    physics_desc.tag.length           = 0; // Filled in below
    physics_desc.phys_type_desc_index = phys_type_desc->cdi_physics_type;
    physics_desc.name_length          = 0; // Name is optional
    //physics_desc.n_parameters         = cdi_lrf->n_parameters;
    physics_desc.n_initial_conditions = 0;
    physics_desc.data_table_length    = 0;
    physics_desc.coupling_model_index = -1;
    physics_desc.n_uds_parameters     = 0;
    physics_desc.n_layers             = -1;
    physics_desc.n_uds_initial_conditions = 0;

    if (cp_info.is_transonic_mach_regime)
      physics_desc.n_parameters         = cdi_lrf->n_parameters + 1;//add has_transonic_flow
    else
      physics_desc.n_parameters         = cdi_lrf->n_parameters;

    if (cdi_lrf->n_initial_conditions != 0)
      msg_internal_error("Local reference frame physics descriptors are not allowed to have initial conditions");

    physics_desc.name_length = strlen(cdi_lrf->name) + 1;
    m_pds.write(&physics_desc, sizeof(physics_desc));

    if (physics_desc.name_length > 0)
      m_pds.write((VOID *)cdi_lrf->name, physics_desc.name_length);

    ccDOTIMES(j, cdi_lrf->n_parameters) {
      LGI_PHYSICS_VARIABLE var;
      sCDI_PHYSICS_VARIABLE *param = &cdi_lrf->parameters[j];
      var.value = param->value;
      var.name_length = param->name == NULL ? 0 : strlen(param->name) + 1;

      m_pds.write(&var, sizeof(var));

      if (param->name != NULL) {
        m_pds.write((VOID *)param->name, var.name_length);
      }

      if (cdi_lrf->is_velocity_via_torque && rotdyn_desc != NULL) {
        asINT32 var_id = cdi_lrf->phys_type_desc->continuous_dp_var[j]->id;
        switch(var_id) {
          case CDI_VAR_ID_RESISTIVE_TORQUE:
            if (var.name_length > 0) {
              rotdyn_desc->m_is_resistive_torque_time_varying = TRUE;
              rotdyn_desc->m_resistive_torque = new dFLOAT[NUM_TIME_VARYING_TORQUE_VALUES];
              rotdyn_desc->m_resistive_torque_buffer = new dFLOAT[NUM_TIME_VARYING_TORQUE_VALUES];
            } else {
              rotdyn_desc->m_resistive_torque = new dFLOAT[1];
              rotdyn_desc->m_resistive_torque[0] = var.value;
            }
            break;
          case CDI_VAR_ID_EXTERNAL_TORQUE:
            if (var.name_length > 0) {
              rotdyn_desc->m_is_external_torque_time_varying = TRUE;
              rotdyn_desc->m_external_torque = new dFLOAT[NUM_TIME_VARYING_TORQUE_VALUES];
              rotdyn_desc->m_external_torque_buffer = new dFLOAT[NUM_TIME_VARYING_TORQUE_VALUES];
            } else {
              rotdyn_desc->m_external_torque = new dFLOAT[1];
              rotdyn_desc->m_external_torque[0] = var.value;
            }
            break;
          case CDI_VAR_ID_START_TIME: rotdyn_desc->m_cdi_start_time = var.value; break;
          case CDI_VAR_ID_END_TIME: rotdyn_desc->m_cdi_end_time = var.value; break;
          case CDI_VAR_ID_TIME_ACCURATE: rotdyn_desc->m_is_time_accurate = (cBOOLEAN)((int)var.value); break;
          case CDI_VAR_ID_MOMENT_OF_INERTIA: rotdyn_desc->m_moment_of_inertia = var.value; break;
          case CDI_VAR_ID_MEAS_WINDOW_INDEX: {
            sINT32 meas_window_index = -1;
            ccDOTIMES(k, cp_info.n_meas_windows()) {
              if (cp_info.meas_windows[k] != NULL
                  && (cp_info.meas_windows[k]->cdi_meas_window == &cp_info.cdi_meas_windows[(int)var.value])
                  && (cp_info.meas_windows[k]->sri_file_type == SRI_COMPOSITE_SURFACE_TYPE)) {
                meas_window_index = k;
                break;
              }
            }
            rotdyn_desc->m_window = cp_info.meas_windows[meas_window_index];
            break;
          }
          case CDI_VAR_ID_INITIAL_ANG_VEL: rotdyn_desc->m_initial_omega = var.value; break;
          default: break;
        }
      }
    } // for n_parameters

    if (cp_info.is_transonic_mach_regime) {
      LGI_PHYSICS_VARIABLE var;
      var.value = cdi_lrf->has_transonic_flow ? 1.0 : -1.0;
      var.name_length = 0;

      m_pds.write(&var, sizeof(var));
    }

    if (cdi_lrf->is_velocity_via_torque && rotdyn_desc != NULL) {
      rotdyn_desc->m_lrf_index           = i;
      rotdyn_desc->m_max_radius_lrf      = 0.0;
      rotdyn_desc->m_omega               = rotdyn_desc->m_initial_omega;

      rotdyn_desc->m_window->rotational_dynamics_index = rotdyn_desc_index;

      vcopy(rotdyn_desc->m_axis_origin, cdi_lrf->cyl_begin);
      vsub(rotdyn_desc->m_axis_direction, cdi_lrf->cyl_end, cdi_lrf->cyl_begin);
      rotdyn_desc->m_lrf_height = sqrt(vdot(rotdyn_desc->m_axis_direction, rotdyn_desc->m_axis_direction));
      vunitize(rotdyn_desc->m_axis_direction);

      rotdyn_desc->m_mpi_requests = xnew MPI_Request[total_sps];
      ccDOTIMES(sp, total_sps) {
        rotdyn_desc->m_mpi_requests[sp] = MPI_REQUEST_NULL;
      }

      int tag = make_mpi_tag<eMPI_MSG::ROTDYN>(rotdyn_desc_index);
      if (rotdyn_desc->m_is_resistive_torque_time_varying) {
        xMPI_Irecv(rotdyn_desc->m_resistive_torque_buffer,
                   NUM_TIME_VARYING_TORQUE_VALUES,
                   eMPI_dFLOAT, 0, tag, eMPI_sp_cp_comm,
                   &(rotdyn_desc->m_resistive_torque_mpi_request));
      }
      if (rotdyn_desc->m_is_external_torque_time_varying) {
        xMPI_Irecv(rotdyn_desc->m_external_torque_buffer,
                   NUM_TIME_VARYING_TORQUE_VALUES,
                   eMPI_dFLOAT, 0, tag, eMPI_sp_cp_comm,
                   &(rotdyn_desc->m_external_torque_mpi_request));
      }

      rotdyn_desc->read_user_inputs();

      rotdyn_desc_index++;
      rotdyn_desc++;
    }
  } // for n_ref_frames

  delete[] cdi_lrfs;

  write_phys_desc_buffer_to_sps(n_ref_frames);

  exit_cdi_chunk();

}

//----------------------------------------------------------------------------
// read_seed_from_meas_descs
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_seed_from_meas_descs()
{
  cp_info.tbs_info.is_tbs = FALSE;
  if (!m_is_bsrs_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_BSRS);
  cp_info.seed_from_meas_desc_index_from_face_index = new asINT32[cp_info.n_sri_faces];
  cp_info.seed_from_meas_mask_from_face_index = new auINT32[cp_info.n_sri_faces];
  cp_info.is_face_tbs = new cBOOLEAN[cp_info.n_sri_faces];
  ccDOTIMES(i, cp_info.n_sri_faces) {
    cp_info.seed_from_meas_desc_index_from_face_index[i] = -1;
    cp_info.seed_from_meas_mask_from_face_index[i] = 0;
    cp_info.is_face_tbs[i] = FALSE;
  }

  // throw a warning if the user specifies a part name in the command line option -sampled_face_meas_bc
  // that does not exist in the CDI file
  for(std::map<std::string, std::string>::iterator iter = cp_info.seed_from_meas_location_map.begin();
      iter != cp_info.seed_from_meas_location_map.end();
      ++iter) {
    cBOOLEAN found_part = FALSE;
    ccDOTIMES(i, cp_info.n_sri_faces) {
      if (iter->first == std::string(cp_info.sri_faces[i].name)) {
        found_part = TRUE;
        break;
      }
    }
    if (!found_part) {
      msg_warn("Part \"%s\" specified via the command line option -sampled_face_meas_bc "
               "does not exist in the CDI file.", iter->first.c_str());
    }
  }

  cp_info.n_seed_from_meas_descs = 0;
  cp_info.n_transient_seed_from_meas_descs = 0;
  asINT32  n_chunks = cio_get_count(m_cdi_info->cio_info);
  cp_info.seed_from_meas_descs.resize(n_chunks);
//  SEED_FROM_MEAS_DESC seed_from_meas_desc = cp_info.seed_from_meas_descs;
  CDI_BSRG bsrg;
  ccCDI_DO_INNER_CHUNKS(j, "bsrs", m_cdi_info) {
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_BSRG) {
      bsrg = cdi_read_bsrg(m_cdi_info);
      SEED_FROM_MEAS_DESC seed_from_meas_desc;
      if(bsrg->transient_boundary_seeding) {
        seed_from_meas_desc = new sTRANSIENT_BOUNDARY_SEEDING;
        cp_info.n_transient_seed_from_meas_descs++;
        ccDOTIMES(i, 3)
          static_cast<TRANSIENT_BOUNDARY_SEEDING>(seed_from_meas_desc)->m_surfel_translation_coord[i] = bsrg->coord_offset[i];
      } else
        seed_from_meas_desc = new sSEED_FROM_MEAS_DESC;

      seed_from_meas_desc->m_cdi_file_id = bsrg->cdi_file_id;
      seed_from_meas_desc->m_facet_id_offset = bsrg->facet_offset;
      seed_from_meas_desc->m_transient_boundary_seeding = bsrg->transient_boundary_seeding;
      seed_from_meas_desc->m_is_octree_map = !bsrg->is_merged_to_input;
      if(!cp_info.tbs_info.is_tbs)
        cp_info.tbs_info.is_tbs = bsrg->transient_boundary_seeding;
      seed_from_meas_desc->m_params.seed_via_mks = bsrg->seed_via_mks;

      cBOOLEAN is_filename_set = FALSE;
      ccDOTIMES(i, cp_info.n_sri_faces) {
        if (cp_info.sri_faces[i].parent_index == bsrg->region_index) {
          // create mapping from face index to sead_from_meas descriptor index
          cp_info.seed_from_meas_desc_index_from_face_index[i] = cp_info.n_seed_from_meas_descs;

          if (!is_filename_set) {
            // store the full path along with the filename
            std::string part_name(cp_info.sri_faces[i].name);
            std::string folder = cp_info.seed_from_meas_location_map[part_name];
            if (folder == "") // if a key is missing, the default constructor for std::string is called
              folder = cp_info.default_seed_from_meas_location;
            
            seed_from_meas_desc->m_sampled_face_meas_filename = folder
                                                                + "/"
                                                                + std::string(bsrg->mea_name)
                                                                + ".snc";
            is_filename_set = TRUE;
          }
        }
      }
      cdi_destroy_bsrg(bsrg);
      cp_info.seed_from_meas_descs[cp_info.n_seed_from_meas_descs] = seed_from_meas_desc;
      cp_info.n_seed_from_meas_descs++;
//      seed_from_meas_desc++;
    }
  }
  exit_cdi_chunk();
}

inline BOOLEAN is_inlet_or_outlet(asINT32 face_index) {
  //enough to check only front physics descriptors for inlets & outlets
  asINT32 si = cp_info.front_flow_surface_phys_desc_index_from_face_index[face_index];
  if (si >= 0) {
    asINT32 cdi_phys_type = cp_info.cdi_phys_type_from_flow_surface_physics_desc_index[si];
    return !cdi_phys_type_is_wall(cdi_phys_type);
  } else {
    return FALSE;
  }
}

inline std::vector<asINT32> get_only_inlet_outlet_face_ids(const std::vector<asINT32>& face_indices) {
  std::vector<asINT32> inlet_outlets;
  std::copy_if(face_indices.begin(), face_indices.end(),
               std::back_inserter(inlet_outlets), is_inlet_or_outlet);
  return inlet_outlets;
}

static void set_entity_type_and_face_ids_from_entity_name(const cSTRING entity_name,
                                                          const PHYSICS_INDEX_TO_ENTITY_ID_MAP& surface_physics_index_to_face_ids,
                                                          std::vector<asINT32>& face_ids,
                                                          sBSEED_FACE_CHECK::ENTITY_TYPE& type) {
  type = cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face;
  cCDI_GEOMETRY_REF geomRef(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
  if (cdi_get_geometry_by_path(cp_info.partitions(), entity_name, &geomRef)){
    type = geomRef.GeometrySelectionType();
    auto face_indices = geomRef.ExpandSelection(cp_info.partitions(),
                                                cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
      
    std::sort(face_indices.begin(), face_indices.end());
    face_ids = face_indices;
  }
}

static VOID warn_if_inconsistencies_found_in_bseed_opts(std::vector<sBSEED_FACE_CHECK>& bseed_face_checks,
                                                        std::vector<sBSEED_FACE_CHECK>& bseed_var_face_checks) {

  asINT32 n_bseed_faces = bseed_face_checks.size();
  asINT32 n_bseed_var_specs = bseed_var_face_checks.size();
  // Check the list of boundary faces mentioned in the --bseed_include_faces and --bseed_exclude_faces options
  ccDOTIMES(i, n_bseed_faces) {
    std::string entity_prefix = bseed_face_checks[i].entity_type_name();
    if (bseed_face_checks[i].found == FALSE) {
      msg_warn("%s \"%s\" (included with --%s option) does not exist.",
               entity_prefix.c_str(),
               cp_info.bseed_faces[i].c_str(),
               cp_info.is_bseed_faces_inclusion_list ? "bseed_include_faces" : "bseed_exclude_faces"); 
    }
    else if (bseed_face_checks[i].is_wall == TRUE) {
      msg_error("%s \"%s\" (included with --%s option) %s. Boundary seeding does not apply to walls.",
                entity_prefix.c_str(),
                cp_info.bseed_faces[i].c_str(),
                cp_info.is_bseed_faces_inclusion_list ? "bseed_include_faces" : "bseed_exclude_faces",
                bseed_face_checks[i].is_segment_or_part()? "contains only walls" : "is a wall");
    }
  }	

  // Check the boundary faces mentioned in the --bseed_include_vars and --bseed_exclude_vars options
  if (g_seed_ctl.smart_seed_boundaries()) {
    g_seed_ctl.check_boundary_face_bseed_vars(bseed_face_checks, bseed_var_face_checks);
  }
}

std::vector<asINT32> sCP_CDI_READER::assign_bseed_specs_by_face_id(const PHYSICS_INDEX_TO_ENTITY_ID_MAP& surface_physics_index_to_face_ids) {

  std::vector<asINT32> face_ids_to_bseed_var_spec_index(cp_info.n_sri_faces, -1);
  if (!g_seed_ctl.smart_seed_boundaries()) { return face_ids_to_bseed_var_spec_index; }
  
  asINT32 n_bseed_faces = cp_info.bseed_faces.size();   // get number of bseed faces
  std::vector<sBSEED_FACE_CHECK> bseed_face_checks(n_bseed_faces); // array for error checking bseed faces  
  asINT32 n_bseed_var_specs = g_seed_ctl.n_bseed_var_specs(); //g_seed_ctl.get_seed_controller(0)->n_boundary_seed_var_specs();

  std::vector<sBSEED_FACE_CHECK> bseed_var_face_checks(n_bseed_var_specs);

  asINT32 n_descriptors = surface_physics_index_to_face_ids.size();

  //Its either a bseed_include_list or a bseed_exclude_list but not both
  BOOLEAN is_bseed_exclude_or_empty_bseed_include = cp_info.is_bseed_faces_inclusion_list == FALSE;  

  // Stage - 1: BSEED FACES
  // Mark if user specified faces are subject to boundary seeding or not
  std::unordered_set<asINT32> bseed_face_ids;
  for (int i = 0; i < cp_info.bseed_faces.size(); i++) {
    const auto entity_name = cp_info.bseed_faces[i].c_str();
    std::vector<asINT32> matched_ids;
    sBSEED_FACE_CHECK::ENTITY_TYPE entity_type;
    set_entity_type_and_face_ids_from_entity_name(entity_name, surface_physics_index_to_face_ids,
                                                  matched_ids, entity_type);

    BOOLEAN is_entity_subject_to_boundary_seeding = is_bseed_exclude_or_empty_bseed_include;
    if (!matched_ids.empty()) {
      is_entity_subject_to_boundary_seeding = is_entity_subject_to_boundary_seeding ^ 1;
      bseed_face_checks[i].found = TRUE;
      bseed_face_checks[i].entity_type = entity_type;
      bseed_face_checks[i].is_wall = \
        std::all_of(matched_ids.begin(),
                    matched_ids.end(),
                    [] (asINT32 fi) { return !is_inlet_or_outlet(fi);});

      asINT32 boundary_seed_var_spec_index = is_entity_subject_to_boundary_seeding? 0 : -1;
      std::vector<asINT32> inlet_outlets = get_only_inlet_outlet_face_ids(matched_ids);
      for (asINT32 face_id : inlet_outlets) {
        bseed_face_ids.insert(face_id);
        face_ids_to_bseed_var_spec_index[face_id] = boundary_seed_var_spec_index;
      }
    } else {
      bseed_face_checks[i].entity_type = cdi_get_geometry_type_from_entity_name(entity_name);
    }
  }

  //Stage - 2
  //Go through all faces across all surface physics descriptors if the default is to seed
  //all faces, i.e, the user hasn't provided a bseed_inclusion_list.
  //Only consider faces not dealt in previous step (bseed_faces) and that are associated
  //with inlet-outlet descriptors
  if (is_bseed_exclude_or_empty_bseed_include) {
    for (int si = 0; si < n_descriptors; si++) {
      const auto& si_faces = surface_physics_index_to_face_ids[si];
      asINT32 cdi_phys_type = cp_info.cdi_phys_type_from_flow_surface_physics_desc_index[si];
      if (!cdi_phys_type_is_wall(cdi_phys_type)) {
        for (asINT32 si_face_index : si_faces) {
          if (bseed_face_ids.find(si_face_index) == bseed_face_ids.end()) {
            face_ids_to_bseed_var_spec_index[si_face_index] = 0;
          }
        }
      }
    }
  }
  
  // Stage 3 : BSEED_VAR_FACE_CHECKS
  // Final override with BSEED_VAR_FACES
  // All faces should have been assigned either 0 or -1 for spec index in previous two steps
  // This is a final override with a specific seed var spec if applicable
  //for (asINT32 nth_spec = 1; nth_spec < g_smart_seed_ctl.n_boundary_seed_var_specs(); nth_spec++) {
  for (asINT32 nth_spec = 1; nth_spec < g_seed_ctl.get_seed_controller(0)->n_boundary_seed_var_specs(); nth_spec++) {
    cSTRING entity_name = g_seed_ctl.get_seed_controller(0)->get_boundary_seed_var_spec(nth_spec).entity_name.c_str();
    std::vector<asINT32> matched_ids;
    sBSEED_FACE_CHECK::ENTITY_TYPE entity_type;
    set_entity_type_and_face_ids_from_entity_name(entity_name,
                                                  surface_physics_index_to_face_ids,
                                                  matched_ids, entity_type);
    if (!matched_ids.empty()) {
      bseed_var_face_checks[nth_spec].found = TRUE;
      bseed_var_face_checks[nth_spec].entity_type = entity_type;
      bseed_var_face_checks[nth_spec].is_wall  = std::all_of(matched_ids.begin(),
                                                             matched_ids.end(),
                                                             [] (asINT32 fi) { return !is_inlet_or_outlet(fi);});
      
      std::vector<asINT32> inlet_outlets = get_only_inlet_outlet_face_ids(matched_ids);      
      for (asINT32 face_id : inlet_outlets) {
        //Only if face is subject to boundary seeding as per stage 1 & 2, associate
        //face with a bseed_var_spec. This implies face_ids_to_bseed_var_spec_index[face_id] is 0
        if (face_ids_to_bseed_var_spec_index[face_id] == 0) {
          face_ids_to_bseed_var_spec_index[face_id] = nth_spec;
        }
      }
    } else {
      bseed_var_face_checks[nth_spec].entity_type = cdi_get_geometry_type_from_entity_name(entity_name);
    }
  }
  
  warn_if_inconsistencies_found_in_bseed_opts(bseed_face_checks, bseed_var_face_checks);

  return face_ids_to_bseed_var_spec_index;
}

VOID sCP_CDI_READER::assign_flow_surface_physics_by_face_id(const PHYSICS_INDEX_TO_ENTITY_ID_MAP& surface_physics_index_to_face_ids) {
  std::vector<asINT32> face_ids_to_bseed_var_spec_index = assign_bseed_specs_by_face_id(surface_physics_index_to_face_ids);

  LGI_FACE_ID_TO_SURFACE_PHYSICS_HEADER h;
  h.tag.id = LGI_FACE_ID_TO_SURFACE_PHYSICS_TAG;
  h.tag.length = 0; // SPs will infer length
  h.n_faces = cp_info.n_sri_faces;
  write_header_to_all_sps(h);
  for (int i = 0; i < h.n_faces; i++) {
    LGI_FACE_ID_TO_SURFACE_PHYSICS lgi_data;
    lgi_data.boundary_seed_var_spec_index = face_ids_to_bseed_var_spec_index[i];
    write_to_all_sps(lgi_data);
  }
}

//----------------------------------------------------------------------------
// read_flow_surface_physics_descs
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_flow_surface_physics_descriptors()
{ 
  if (!m_is_srpt_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_SRPT);
  
  m_pds.size = 0;

  if (cio_get_count(m_cdi_info->cio_info) > 0) {
    write_phys_desc_header_to_buffer(LGI_FLOW_SURFACE_PHYSICS_DESCRIPTORS_TAG);

    asINT32 n_descriptors = 0;
    BOOLEAN is_flow_surface = TRUE;

    PHYSICS_INDEX_TO_ENTITY_ID_MAP surface_physics_index_to_face_ids(build_surface_physics_index_to_face_ids_map(cp_info.front_flow_surface_phys_desc_index_from_face_index,
                                                                                                                 cp_info.back_flow_surface_phys_desc_index_from_face_index));
    cp_info.cdi_phys_type_from_flow_surface_physics_desc_index = std::vector<sINT32>(surface_physics_index_to_face_ids.size(), -1);

    std::vector<asINT32> empty;

    // For old CDI files, thermal contact BCs end up in the srpt chunk, but no faces
    // refer to them, because they are applied on a surfel basis, overriding
    // the face specification. 
    auto get_face_indices = [&](asINT32 n) -> const std::vector<asINT32>& {
      if (n < surface_physics_index_to_face_ids.size()) {
        return surface_physics_index_to_face_ids[n];
      } else {
        return empty;
      }
    };
    
    ccCDI_DO_INNER_CHUNKS(i, "srpt", m_cdi_info) {
      if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHYS) {
        const auto& face_indices = get_face_indices(n_descriptors);
        read_physics_chunk(is_flow_surface, FALSE, n_descriptors, face_indices);
        n_descriptors++;
      } else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_SRPH) { //Accomodates Partitions related change
        ccCDI_DO_INNER_CHUNKS(i, "srph", m_cdi_info) {
          if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHYS) {
            const auto& face_indices = get_face_indices(n_descriptors);
            read_physics_chunk(is_flow_surface, FALSE, n_descriptors, face_indices);
            n_descriptors++;
          }
        }
      }
    }
    
    write_phys_desc_buffer_to_sps(n_descriptors);
    
    assign_flow_surface_physics_by_face_id(surface_physics_index_to_face_ids);

  }
  exit_cdi_chunk();
}

//----------------------------------------------------------------------------
// read_thermal_surface_physics_descs
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_thermal_surface_physics_descriptors()
{ 
  if (!m_is_thpt_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_THPT);
  
  m_pds.size = 0;

  if (cio_get_count(m_cdi_info->cio_info) > 0) {
    write_phys_desc_header_to_buffer(LGI_THERMAL_SURFACE_PHYSICS_DESCRIPTORS_TAG);

    asINT32 n_descriptors = 0;
    BOOLEAN is_thermal_surface = TRUE;

    PHYSICS_INDEX_TO_ENTITY_ID_MAP surface_physics_index_to_face_ids(build_surface_physics_index_to_face_ids_map(cp_info.front_thermal_surface_phys_desc_index_from_face_index,
                                                                                                                 cp_info.back_thermal_surface_phys_desc_index_from_face_index));
    ///////cp_info.cdi_phys_type_from_surface_physics_desc_index = std::vector<sINT32>(surface_physics_index_to_face_ids.size(), -1);

    std::vector<asINT32> empty;

    // For new CDI files, thermal contact BCs end up in the thpt chunk, but no faces
    // refer to them, because they are applied on a surfel basis, overriding
    // the face specification. 
    auto get_face_indices = [&](asINT32 n) -> const std::vector<asINT32>& {
      if (n < surface_physics_index_to_face_ids.size()) {
        return surface_physics_index_to_face_ids[n];
      } else {
        return empty;
      }
    };
    
    ccCDI_DO_INNER_CHUNKS(i, "thpt", m_cdi_info) {
      if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHYS) {
        const auto& face_indices = get_face_indices(n_descriptors);
        read_physics_chunk(FALSE, is_thermal_surface, n_descriptors, face_indices);
        n_descriptors++;
      } else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_THPH) { //Accomodates Partitions related change
        ccCDI_DO_INNER_CHUNKS(i, "thph", m_cdi_info) {
          if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHYS) {
            const auto& face_indices = get_face_indices(n_descriptors);
            read_physics_chunk(FALSE, is_thermal_surface, n_descriptors, face_indices);
            n_descriptors++;
          }
        }
      }
    }
    
    write_phys_desc_buffer_to_sps(n_descriptors);
  }
  exit_cdi_chunk();
}

//----------------------------------------------------------------------------
// read_thermal_contact_table
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_thermal_contact_table() {
  // Reads the thermal contact table, needed on the simulator side to 
  // - build a map of contact averaged to faces
  if (!m_is_stct_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_STCT);

  cCDI_THERMAL_CONTACTS tc;
  tc.ReadFromCDI(m_cdi_info);
  tc.BuildContactTable(cp_info.partitions());

  //Vector containing for each averaged contact a vector of integers providing:
  // - [0]      thermal physics descriptor for this contact
  // - [1]      number of faces involved
  // - [2:end]  each individual face id
  std::vector<std::vector<asINT32>> averaged_contacts;
  averaged_contacts.reserve(tc.GetNumContactRegions());
  
  uINT64 length = 0;
  std::vector<cdiINT32> face_indices_primary, face_indices_secondary;
  auto contact_regions = tc.GetContactRegions();
  for (cdiINT32 icontact = 0; icontact < contact_regions.size(); icontact++) {
    if (tc.IsHeatUniform(icontact) &&
        tc.GetContactEntireFaceGeometryIndices(icontact, face_indices_primary, face_indices_secondary)) {
      const CDI_STCR &stcr = contact_regions[icontact]; 
      asINT32 num_faces = face_indices_primary.size() + face_indices_secondary.size();
      auto& new_contact = averaged_contacts.emplace_back(num_faces + 2);
      new_contact.at(0) = stcr->surf_props_index;
      new_contact.at(1) = num_faces;
      auto it = new_contact.begin()+2;
      it = std::copy(face_indices_primary.begin(), face_indices_primary.end(), it);
      std::copy(face_indices_secondary.begin(), face_indices_secondary.end(), it);
      length += new_contact.size() * sizeof(asINT32);
    }
  }
  cp_info.num_averaged_contacts = averaged_contacts.size();
  
  //Time to send info to the SPs
  cDGF_AVERAGED_CONTACTS_HEADER header;
  header.tag.id = DGF_AVERAGED_CONTACTS_HEADER_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(header) + length);
  header.num_averaged_contacts = cp_info.num_averaged_contacts;
  write_header_to_all_sps(header);
  for (auto &contact : averaged_contacts) {
    write_to_all_sps((VOID *)contact.data(), contact.size() * sizeof(asINT32));
  }

  exit_cdi_chunk();
}


sINT32 *g_phys_desc_hxch_index, *g_phys_desc_cdsr_index;
static VOID init_hxch_cdsr_to_phys_index_map(asINT32 n_phys_descs)
{
  g_phys_desc_hxch_index = xnew sINT32[n_phys_descs];
  g_phys_desc_cdsr_index = xnew sINT32[n_phys_descs];
  ccDOTIMES(i, n_phys_descs) {
    g_phys_desc_hxch_index[i] = -1;
    g_phys_desc_cdsr_index[i] = -1;
  }
  ccDOTIMES(i, cp_info.n_hxchs) {
    asINT32 medium_index = cp_info.cdi_hxchs[i]->medium_index;
    if (medium_index < 0 || medium_index >= n_phys_descs)
      msg_internal_error("Invalid porous medium index %d in CDI file for heat exchanger \"%s\"", medium_index, cp_info.cdi_hxchs[i]->name);
    g_phys_desc_hxch_index[cp_info.cdi_hxchs[i]->medium_index] = i;
    g_phys_desc_hxch_index[cp_info.cdi_hxchs[i]->adiabatic_index] = i;
  }
  ccDOTIMES(i, cp_info.n_cdsrs) {
    asINT32 medium_index = cp_info.cdi_cdsrs[i]->medium_index;
    if (medium_index < 0 || medium_index >= n_phys_descs)
      msg_internal_error("Invalid porous medium index %d in CDI file for condenser \"%s\"", medium_index, cp_info.cdi_cdsrs[i]->name);
    g_phys_desc_cdsr_index[cp_info.cdi_cdsrs[i]->medium_index] = i;
    g_phys_desc_cdsr_index[cp_info.cdi_cdsrs[i]->adiabatic_index] = i;
  }
}

static asINT32 curved_hx_meas_window_index(asINT32 phys_desc_index)
{
  if (g_phys_desc_hxch_index[phys_desc_index] >= 0) {
    CDI_HXCH hxch = cp_info.cdi_hxchs[g_phys_desc_hxch_index[phys_desc_index]];
    return cp_info.cdi_meas_windows[hxch->inlet_meas_index].one_window; 
  }
  else if (g_phys_desc_cdsr_index[phys_desc_index] >= 0) {
    CDI_CDSR cdsr = cp_info.cdi_cdsrs[g_phys_desc_cdsr_index[phys_desc_index]];
    return cp_info.cdi_meas_windows[cdsr->inlet_meas_index].one_window; 
  }
  else
    return -1;
}

static asINT32 get_parent_part_index(asINT32 face_index, SRI_FACE faces, 
                                     asINT32 n_faces, asINT32 n_parts) 
{
  asINT32 parent_index = faces[face_index].parent_index;
  if (faces[face_index].is_parent_a_face == FALSE) {
    if (parent_index >= n_parts)
      msg_error("Face index %d has an invalid parent part index %d.", face_index, parent_index);
    return parent_index;
  }
  else {
    if (parent_index >= n_faces)
      msg_error("Face index %d has an invalid parent face index %d.", face_index, parent_index);
    return get_parent_part_index(parent_index, faces, n_faces, n_parts);
  }
}

// Before deforming tires were implemented, the deforming tire cvdp chunk was treated
// as a boolean that was always 0. Now we treat it as an index. If the index is
// 0, we need to check to see if we actually have tires to get the correct type.
static SRI_MOVING_FACE_TYPE get_moving_face_type_from_deforming_tire_index(asINT32 index) {
  if ( cp_info.is_deforming_tire_index(index) ) {
    return SRI_DEFORMING_TIRE_MOVING_FACE;
  }
  else {
    return SRI_RIGID_BODY_MOVING_FACE;
  }
}

static VOID add_part_moving_faces(asINT32 part_index, asINT32 moving_xform_index, asINT32 deforming_tire_index)
{
  ccDOTIMES(i, cp_info.n_sri_faces) {
    if (cp_info.face_to_parent_part_index_map[i] == part_index) {
      asINT32 face_index = cp_info.sri_faces[i].cdi_id;
      sSRI_MOVING_FACE moving_face;
      cp_info.sri_faces[i].deforming_tire_index = cp_info.update_deforming_tire_index(deforming_tire_index);
      moving_face.parent_face_index = face_index;
      moving_face.type = get_moving_face_type_from_deforming_tire_index(deforming_tire_index);
      moving_face.deforming_tire_index = cp_info.update_deforming_tire_index(deforming_tire_index);
      moving_face.n_surfels  = 0;
      moving_face.moving_xform_index = moving_xform_index;
      cp_info.sri_movbs.push_back(moving_face);
    }
  }
}

static VOID add_moving_face_xform(dFLOAT axis_origin[3],
                                  dFLOAT axis_direction[3])
{
  sSRI_MOVING_FACE_DESCRIPTOR movb_xform;
  vcopy(movb_xform.axis_origin, axis_origin);
  vcopy(movb_xform.axis_direction, axis_direction);
  cp_info.sri_movb_xforms.push_back(movb_xform);
}

//----------------------------------------------------------------------------
// read_movb_chunk 
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_movb_chunk_phys_subchunk(asINT32& deforming_tire_index)
{
  asINT32 n_inner_chunks = cio_get_count(m_cdi_info->cio_info);

  sVARIABLE parameters     [CDI_MAX_CONTINUOUS_DP];
  cBOOLEAN  parameter_found[CDI_MAX_CONTINUOUS_DP];

  sVARIABLE initial_conditions     [CDI_MAX_INITIAL_CONDITIONS];
  cBOOLEAN  initial_condition_found[CDI_MAX_INITIAL_CONDITIONS];

  BOOLEAN found_some_eqn = FALSE;

  cSTRING physics_desc_name = read_name();

  CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc;
  CDI_PTYP ptyp;
  LGI_PHYSICS_DESCRIPTOR physics_desc;

  BOOLEAN is_old_tfloat_type      = FALSE;
  BOOLEAN is_old_turb_type        = FALSE;
  BOOLEAN found_turb_via          = FALSE;
  BOOLEAN phys_descriptor_written = FALSE;

  // for surface coupling
  physics_desc.coupling_model_index = -1;
  physics_desc.init_pf_bc_coupling_p = 0;

  CDI_WITH_INNER_CHUNK(m_cdi_info) {
    if (cdi_get_type(m_cdi_info) != CDI_CHUNK_TYPE_PTYP) {
      msg_internal_error("Expected PTYP as second sub-chunk of CDI PHYS chunk.");
    } 
    else {
      ptyp = cdi_read_ptyp(m_cdi_info);
      phys_type_desc = cdi_lookup_physics(ptyp->type);
      if (phys_type_desc == NULL) {
        msg_internal_error("Unknown CDI physics type: %d.", ptyp->type);
      }
      else {
        physics_desc.phys_type_desc_index = ptyp->type;
        physics_desc.name_length          = strlen(physics_desc_name) + 1;
        physics_desc.n_parameters         = phys_type_desc->n_continuous_dp;
        physics_desc.n_initial_conditions = phys_type_desc->n_initial_conditions;
        physics_desc.data_table_length    = 0;
	physics_desc.n_uds_parameters     = 0;
        physics_desc.n_layers             = -1;
	physics_desc.n_uds_initial_conditions = 0; 

        ccDOTIMES(i, phys_type_desc->n_continuous_dp) {
          parameter_found[i] = FALSE;
        }
        ccDOTIMES(j, phys_type_desc->n_initial_conditions) {
          initial_condition_found[j] = FALSE;
        }
      }
    }
  }

  BOOLEAN data_table_found = FALSE;
  dFLOAT axis_origin[3], axis_direction[3];
  ccDOTIMES(m, n_inner_chunks - 2) {
    CDI_WITH_INNER_CHUNK(m_cdi_info) {
      if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQNS) {
        if (data_table_found)
          msg_internal_error("Only one EQNS chunk allowed per PHYS chunk of CDI file.");
        data_table_found = TRUE;

        if (phys_descriptor_written)
          msg_internal_error("EQNS chunk of CDI PHYS chunk must appear before DPRM or IVAL chunks.");

        asINT32 data_table_length = cdi_eqns_read_length(m_cdi_info);
        if (data_table_length < 0)
          msg_internal_error("Failure reading PHYS chunk data table length from CDI file.");

        physics_desc.data_table_length = data_table_length;

        m_pds.write(&physics_desc, sizeof(physics_desc));
        m_pds.write((VOID *)physics_desc_name, physics_desc.name_length);
        phys_descriptor_written = TRUE;

        const int BUFSIZE = 1024;
        char buf[BUFSIZE];
        asINT32 n_left = data_table_length;

        while (n_left > 0) {
          asINT32 n = MIN(n_left, BUFSIZE);
          if (cdi_eqns_read_chars(m_cdi_info, buf, n) != n)
            msg_internal_error("Failure reading PHYS chunk data table contents from CDI file.");
          m_pds.write((VOID *)buf, n);
          n_left -= n;
        }
      }
      else {
        if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_DPRM) {
          asINT32 n_parameter_chunks = cio_get_count(m_cdi_info->cio_info);
          asINT32 n_good_parameters_read = 0;

          if (n_parameter_chunks != ptyp->n_continuous)
            msg_warn("1 Parameter count (%d) in PTYP sub-chunk of PHYS chunk \"%s\" should be %d.",
                     ptyp->n_continuous, physics_desc_name, n_parameter_chunks);

          ccDOTIMES(i, n_parameter_chunks) {
            CDI_WITH_INNER_CHUNK(m_cdi_info) {
              if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_CVDP) {
                CDI_CVDP cvdp = cdi_read_cvdp(m_cdi_info);
                if (cvdp == NULL)
                  msg_internal_error("Missing CVDP chunk in DPRM chunk of CDI file.");
                else {
                  sINT32 type = cvdp->type;
                  asINT32 index = phys_type_desc->continuous_dp_index(type);
                  if (index < 0) {
                    msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                             cvdp->type, physics_desc_name);
                  } else {
                    parameters[index].value = cvdp->value;
                    parameters[index].name = NULL;
                    parameter_found[index] = TRUE;
                    n_good_parameters_read++;
                    switch (type) {
                      case CDI_VAR_ID_UNIT_VEC_X:
                        axis_direction[0] = cvdp->value;
                        break;
                      case CDI_VAR_ID_UNIT_VEC_Y:
                        axis_direction[1] = cvdp->value;
                        break;
                      case CDI_VAR_ID_UNIT_VEC_Z:
                        axis_direction[2] = cvdp->value;
                        break;
                      case CDI_VAR_ID_POINT_X:
                        axis_origin[0] = cvdp->value;
                        break;
                      case CDI_VAR_ID_POINT_Y:
                        axis_origin[1] = cvdp->value;
                        break;
                      case CDI_VAR_ID_POINT_Z:
                        axis_origin[2] = cvdp->value;
                        break;
                      case CDI_VAR_ID_DEFORMING_TIRE:
                        deforming_tire_index = cvdp->value;
                        break;
                      default:
                        break;
                    }
                  }
                }
              }
              else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQDP) {
                CDI_EQDP eqdp = cdi_read_eqdp(m_cdi_info);
                if (eqdp == NULL)
                  msg_internal_error("Missing EQDP chunk in DPRM chunk of CDI file.");
                else {
                  sINT32 type = eqdp->type;
                  asINT32 index = phys_type_desc->continuous_dp_index(type);
                  found_some_eqn = TRUE;
                  if (index < 0) {
                    msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                             eqdp->type, physics_desc_name);
                  } else {
                    parameters[index].value = 0;
                    parameters[index].name = eqdp->var_name;
                    parameter_found[index] = TRUE;
                    n_good_parameters_read++;
                  }
                }
              }
              else {
                msg_internal_error("In CDI file, unexpected sub-chunk of DPRM chunk (see PHYS chunk \"%s\")", physics_desc_name);
              }
            }
          }

        }
        else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_IVAL) {
          asINT32 n_ic_chunks = cio_get_count(m_cdi_info->cio_info);
          asINT32 n_good_ics_read = 0;

          if (n_ic_chunks != ptyp->n_initial)
            msg_warn("Initial condition count (%d) in PTYP sub-chunk of PHYS chunk \"%s\" should be %d.",
                     ptyp->n_initial, physics_desc_name, n_ic_chunks);

          ccDOTIMES(i, n_ic_chunks) {
            CDI_WITH_INNER_CHUNK(m_cdi_info) {
              if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_CVDP) {
                CDI_CVDP cvdp = cdi_read_cvdp(m_cdi_info);
                if (cvdp == NULL)
                  msg_internal_error("Missing CVDP chunk in IVAL chunk of CDI file.");
                else {
                  sINT32 type = cvdp->type;
                  asINT32 index = phys_type_desc->initial_condition_index(type);
                  if (index < 0) {
                    msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                             cvdp->type, physics_desc_name);
                  } else {
                    initial_conditions[index].value = cvdp->value;
                    initial_conditions[index].name = NULL;
                    initial_condition_found[index] = TRUE;
                    n_good_ics_read++;
                  }
                }
              }
              else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQDP) {
                CDI_EQDP eqdp = cdi_read_eqdp(m_cdi_info);
                if (eqdp == NULL)
                  msg_internal_error("Missing EQDP chunk in IVAL chunk of CDI file.");
                else {
                  sINT32 type = eqdp->type;
                  asINT32 index = phys_type_desc->initial_condition_index(type);
                  found_some_eqn = TRUE;
                  if (index < 0) {
                    msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                             eqdp->type, physics_desc_name);
                  } else {
                    initial_conditions[index].value = 0;
                    initial_conditions[index].name = eqdp->var_name;
                    initial_condition_found[index] = TRUE;
                    n_good_ics_read++;
                  }
                }
              }
              else {
                msg_warn("In CDI file, unexpected sub-chunk of IVAL chunk (see PHYS chunk \"%s\")", physics_desc_name);
              }
            }
          }
        } 
        else {
          msg_warn("In CDI file, unexpected sub-chunk of PHYS chunk \"%s\"", physics_desc_name);
        }
      }
    }
  }

  add_moving_face_xform(axis_origin, axis_direction);

  if (!phys_descriptor_written) {
    m_pds.write(&physics_desc, sizeof(physics_desc));
    m_pds.write((VOID *)physics_desc_name, physics_desc.name_length);
    phys_descriptor_written = TRUE;
  }

  ccDOTIMES(j, phys_type_desc->n_continuous_dp) {
    if (!parameter_found[j]) {
      asINT32 var_id = phys_type_desc->continuous_dp_var[j]->id;
      switch (var_id) {
        case CDI_VAR_ID_DEFORMING_TIRE:
          parameters[j].value = -1;
          deforming_tire_index = -1;
          break;

        default:
      msg_internal_error("Missing parameter of type %d from DPRM sub-chunk of PHYS chunk \"%s\".",
                         var_id, physics_desc_name);
      }
    }
  }

  ccDOTIMES(k, phys_type_desc->n_initial_conditions) {
    asINT32 var_id = phys_type_desc->initial_condition_var[k]->id;
    if (!initial_condition_found[k]) {
      msg_internal_error("Missing initial condition of type %d from IVAL sub-chunk of PHYS chunk \"%s\".",
                         var_id, physics_desc_name);
    }
  }

  ccDOTIMES(i, phys_type_desc->n_continuous_dp) {
    LGI_PHYSICS_VARIABLE var;
    var.value = parameters[i].value;
    var.name_length = parameters[i].name == NULL ? 0 : strlen(parameters[i].name) + 1;
    m_pds.write(&var, sizeof(var));
    if (parameters[i].name != NULL) {
      m_pds.write((VOID *)parameters[i].name, var.name_length);
    }
  }

  ccDOTIMES(n, phys_type_desc->n_initial_conditions) {
    LGI_PHYSICS_VARIABLE var;
    var.value = initial_conditions[n].value;
    var.name_length = initial_conditions[n].name == NULL ? 0 : strlen(initial_conditions[n].name) + 1;
    m_pds.write(&var, sizeof(var));     
    if (initial_conditions[n].name != NULL) {
      m_pds.write((VOID *)initial_conditions[n].name, var.name_length);
    }
  }

}

//----------------------------------------------------------------------------
// read_fluid_physics_descs
//----------------------------------------------------------------------------

VOID sCP_CDI_READER::read_flud_chunk()
{
  asINT32 max_fluid_phys_descs = cio_get_count(m_cdi_info->cio_info);
  if (max_fluid_phys_descs > 0) {
    cp_info.cdi_phys_type_from_fluid_physics_desc_index = xnew sINT32 [max_fluid_phys_descs];
    write_phys_desc_header_to_buffer(LGI_FLUID_PHYSICS_DESCRIPTORS_TAG);

    asINT32 n_descriptors = 0;
    BOOLEAN is_surface = FALSE;
    init_hxch_cdsr_to_phys_index_map(max_fluid_phys_descs);
    PHYSICS_INDEX_TO_ENTITY_ID_MAP fluid_physics_index_to_region_ids(build_fluid_physics_index_to_region_ids_map());
    ccCDI_DO_INNER_CHUNKS(i, "flud", m_cdi_info) {
      if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHRG) {
        std::vector<asINT32> region_indices;
        ccCDI_DO_INNER_CHUNKS(j, "phrg", m_cdi_info) {
          if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHYS) {
            region_indices = fluid_physics_index_to_region_ids[n_descriptors];
            cp_info.cdi_phys_type_from_fluid_physics_desc_index[n_descriptors] 
              = read_physics_chunk(is_surface, is_surface, n_descriptors, region_indices);
            n_descriptors++;
          } 
        } 
      }
    }
    write_phys_desc_buffer_to_sps(n_descriptors);
  }
}

VOID sCP_CDI_READER::read_hcsd_chunk()
{

  asINT32 max_hcsd_phys_descs = cio_get_count(m_cdi_info->cio_info);
  if (max_hcsd_phys_descs > 0) {

    cp_info.cdi_phys_type_from_fluid_physics_desc_index = xnew sINT32 [max_hcsd_phys_descs];
    write_phys_desc_header_to_buffer(LGI_HCSD_PHYSICS_DESCRIPTORS_TAG);

    asINT32 n_descriptors = 0;
    BOOLEAN is_surface = FALSE;
    asINT32 fluid_desc_index = 0;
    PHYSICS_INDEX_TO_ENTITY_ID_MAP fluid_physics_index_to_region_ids(build_fluid_physics_index_to_region_ids_map());
    ccCDI_DO_INNER_CHUNKS(j, "hcsd", m_cdi_info) {
      if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHRG) {
        ccCDI_DO_INNER_CHUNKS(k, "phrg", m_cdi_info) {
          if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHYS) {
            const auto& region_indices = fluid_physics_index_to_region_ids[n_descriptors];
            fluid_desc_index = read_physics_chunk(is_surface, is_surface, n_descriptors, region_indices);
            if(fluid_desc_index != CDI_PHYS_TYPE_SOLID_INSULATOR) { //Skipping solid insulator
              cp_info.cdi_phys_type_from_fluid_physics_desc_index[n_descriptors] = fluid_desc_index;
              n_descriptors++;
            }
          }
        }
      }
    }
    write_phys_desc_buffer_to_sps(n_descriptors);
  }
}

VOID sCP_CDI_READER::read_shell_config_chunk()
{
  asINT32 max_hcsl_phys_descs = cio_get_count(m_cdi_info->cio_info);
  if (max_hcsl_phys_descs > 0) {

    cSTRING shell_config_name;
    shell_config_name = read_name();
    LGI_PHYSICS_DESCRIPTOR physics_desc;
    physics_desc.coupling_model_index = -1;
    physics_desc.init_pf_bc_coupling_p = 0;
    physics_desc.name_length = strlen(shell_config_name) + 1;
    // Array of total number of dynamics parameters and initial condition
    // in all the layers one shell configuration.
    sVARIABLE parameters[SHELL_CONFIG_MAX_CONTINUOUS_DP];
    sVARIABLE initial_conditions[SHELL_CONFIG_MAX_INITIAL_CONDITIONS];
    sVARIABLE *parameter = &parameters[0];
    sVARIABLE *initial_condition = &initial_conditions[0];
    CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc = cdi_lookup_physics(CDI_PHYS_TYPE_CONDUCTION_LAYER);

    asINT32 shell_config_n_layers = 0;
    ccCDI_DO_INNER_CHUNKS(j, "hcsh", m_cdi_info) {
      if(cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_INT_) {
        cdi_read_asINT32(m_cdi_info, &shell_config_n_layers, 1);
        physics_desc.n_layers             = shell_config_n_layers;
        physics_desc.phys_type_desc_index = CDI_PHYS_TYPE_CONDUCTION_LAYER;
        physics_desc.n_parameters         = physics_desc.n_layers * phys_type_desc->n_continuous_dp;
        physics_desc.n_initial_conditions = physics_desc.n_layers * phys_type_desc->n_initial_conditions;
        physics_desc.data_table_length    = 0;
        m_pds.write(&physics_desc, sizeof(physics_desc));
        m_pds.write((VOID *)shell_config_name, physics_desc.name_length);
      } else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_HCSL) {
        ccCDI_DO_INNER_CHUNKS(k, "hcsl", m_cdi_info) {
          if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHYS) {
            read_shell_layer_chunk(parameter, initial_condition, shell_config_name);
            parameter += phys_type_desc->n_continuous_dp;
            initial_condition += phys_type_desc->n_initial_conditions;
          }
        }
      }
    }
    write_shell_config_params_and_initial_condition(physics_desc, parameters, initial_conditions);
  }
}

VOID sCP_CDI_READER::read_movb_chunk()
{
  asINT32 max_movb_phys_descs = cio_get_count(m_cdi_info->cio_info);
  die_if_gpu(max_movb_phys_descs, "Moving boundary conditions", true);
  if (max_movb_phys_descs > 0) {    
    write_phys_desc_header_to_buffer(LGI_MOVB_PHYSICS_DESCRIPTORS_TAG);
    cp_info.part_to_movb_phys_desc_index_map = xnew asINT32[cp_info.n_sri_parts];

    ccDOTIMES(i, cp_info.n_sri_parts) {
      cp_info.part_to_movb_phys_desc_index_map[i] = -1;
    }

    asINT32 n_descriptors = 0;
    ccCDI_DO_INNER_CHUNKS(i, "movb", m_cdi_info) {
      if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHRG) {
        CDI_RGNS rgns = nullptr;
        ccCDI_DO_INNER_CHUNKS(j, "phrg", m_cdi_info) {
          if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_RGNS) {
            rgns = cdi_read_rgns( m_cdi_info );
          }
          else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PHYS) {

            if (NULL == rgns) {
              msg_error("Movb CDI property not applied to any regions.");
            }

            asINT32 deforming_tire_index = -1;
            read_movb_chunk_phys_subchunk(deforming_tire_index);

            ccDOTIMES(i,rgns->n_region) {
              asINT32 part_index = rgns->region[i];
              cp_info.part_to_movb_phys_desc_index_map[part_index] = n_descriptors;
              add_part_moving_faces(part_index, n_descriptors, deforming_tire_index);
            }

            n_descriptors++;
          } 
        } 
      }
    }
    write_phys_desc_buffer_to_sps(n_descriptors);
  }
}

VOID sCP_CDI_READER::read_fluid_physics_descriptors()
{ 
  if (!m_is_pcfg_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_PCFG);
  

  // For looking up the movb physics descriptor index from the bsurfel face index
  ccDOTIMES(i, cp_info.n_sri_faces) {
    cp_info.face_to_parent_part_index_map[i] = get_parent_part_index(i, cp_info.sri_faces, cp_info.n_sri_faces, cp_info.n_sri_parts);
  }

  ccCDI_DO_INNER_CHUNKS(j, "pcfg", m_cdi_info) {
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_FLUD) {
      m_pds.size = 0;
      read_flud_chunk();
    } else if(cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_HCSD) {
      m_pds.size = 0;
      read_hcsd_chunk();
    } else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_MOVB) {
      m_pds.size = 0;
      read_movb_chunk();
    }
  }

  exit_cdi_chunk();
  delete[] g_phys_desc_hxch_index;
  delete[] g_phys_desc_cdsr_index;

  // m_found_seed_var_spec_physics_desc values are set in read_physics_chunk
  // for each seed_var_spec
  g_seed_ctl.check_seed_var_spec_physics_desc();

}

//----------------------------------------------------------------------------
// read_shell_config_physics_descriptors
//
// Read the shell configuration laid out in the CDI file. Each configuration
// has number of layers which are read and sent as one shell configuration physics
// descriptor to SPs.
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_shell_config_physics_descriptors()
{
  if (!m_is_hcss_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_HCSS);
  bool write_shell_config_phys_descs = FALSE;
  asINT32 n_shell_configs = 0;
  ccCDI_DO_INNER_CHUNKS(j, "hcss", m_cdi_info) {
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_HCSH) {
      //  write the header of descriptor once.
      if(j < 1) {
        m_pds.size = 0;
        write_phys_desc_header_to_buffer(LGI_SHELL_CONFIG_PHYSICS_DESCRIPTORS_TAG);
        write_shell_config_phys_descs = TRUE;
      }
      read_shell_config_chunk();
      n_shell_configs++;
    }
  }
  if(write_shell_config_phys_descs) {
    write_phys_desc_buffer_to_sps(n_shell_configs);
  }
  exit_cdi_chunk();
}
//----------------------------------------------------------------------------
// read_bfpr
//
// This function is the same as read_global_physics_chunk, except that it
// obtains the information from a CDI_BFPR struct instead of reading directly
// from m_cdi_info. The parameter is_name_present has also been removed because
// the body force parameters do not have this field.
//
// The BODF chunk spec was changed in v4.3 of CDI to allow the
// user to specify multiple body force definitions by region. To maintain
// backwards-compatibility, we created a CDI_BODF struct which is populated
// differently in CDI based on the version number, and this struct is in turn
// read by m_cdi_reader.
//
// IMPORTANT: If any changes are made to this function, the programmer must
// check if corresponding changes need to be made to the function
// read_global_physics_chunk as well.
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_bfpr(CDI_BFPR bfpr, cSTRING physics_desc_name, 
                               CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc)
{
  LGI_PHYSICS_DESCRIPTOR physics_desc;

  physics_desc.tag.length           = 0; // Filled in below
  physics_desc.phys_type_desc_index = phys_type_desc->cdi_physics_type;
  physics_desc.name_length          = 0; // Name is optional
  physics_desc.n_parameters         = phys_type_desc->n_continuous_dp;
  physics_desc.n_initial_conditions = 0;
  physics_desc.data_table_length    = 0;
  physics_desc.n_uds_parameters     = 0;
  physics_desc.n_layers             = -1;
  physics_desc.n_uds_initial_conditions = 0;

  if (phys_type_desc->n_initial_conditions != 0)
    msg_internal_error("Body force descriptors are not allowed to have initial conditions");

  m_pds.write(&physics_desc, sizeof(physics_desc));

  sVARIABLE parameters[CDI_MAX_CONTINUOUS_DP];
  cBOOLEAN parameter_found[CDI_MAX_CONTINUOUS_DP];

  ccDOTIMES(j, phys_type_desc->n_continuous_dp) {
    parameter_found[j] = FALSE;
  }

  ccDOTIMES(i, bfpr->n_cvdp) {
    CDI_CVDP cvdp = bfpr->cvdp[i];
    if (cvdp == NULL)
      msg_internal_error("Missing CVDP chunk in %s chunk of CDI file.", physics_desc_name);
    else {
      asINT32 index = phys_type_desc->continuous_dp_index(cvdp->type);

      if (index < 0) {
        msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
            cvdp->type, physics_desc_name);
      } else {
        parameters[index].value = cvdp->value;
        parameters[index].name = NULL;
        parameter_found[index] = TRUE;
      }
    }
  }
  ccDOTIMES(i, bfpr->n_eqdp) {
    CDI_EQDP eqdp = bfpr->eqdp[i];
    if (eqdp == NULL)
      msg_internal_error("Missing EQDP chunk in %s chunk of CDI file.", physics_desc_name);
    else {
      asINT32 index = phys_type_desc->continuous_dp_index(eqdp->type);

      if (index < 0) {
        msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
            eqdp->type, physics_desc_name);
      } else {
        parameters[index].value = 0;
        parameters[index].name = eqdp->var_name;
        parameter_found[index] = TRUE;
      }
    }
  }

  {
    ccDOTIMES(i, phys_type_desc->n_continuous_dp) {
      if (!parameter_found[i]) {
        asINT32 var_id = phys_type_desc->continuous_dp_var[i]->id;

        msg_internal_error("Missing parameter of type %d from %s chunk of CDI file.",
            var_id, physics_desc_name);
      }

      LGI_PHYSICS_VARIABLE var;
      var.value = parameters[i].value;
      var.name_length = parameters[i].name == NULL ? 0 : strlen(parameters[i].name) + 1;

      m_pds.write(&var, sizeof(var));

      if (parameters[i].name != NULL) {
        m_pds.write((VOID *)parameters[i].name, var.name_length);
      }
    }
  }
}

//----------------------------------------------------------------------------
// read_body_force_descriptors
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_body_force_descriptors()
{
  if (!m_is_bodf_present)
    return;

  die_if_gpu(true, "Body Forces", true);

  enter_cdi_chunk(CDI_CHUNK_TYPE_BODF);

  m_pds.size = 0;
  CDI_BODF bodf = cdi_read_bodf(m_cdi_info);
  write_phys_desc_header_to_buffer(LGI_BODY_FORCE_DESCRIPTORS_TAG);
  ccDOTIMES(j, bodf->n_bfdf) {
    CDI_RGNS rgns = bodf->bfdf[j].rgns;
    m_pds.write(&rgns->n_region, sizeof(rgns->n_region));
    m_pds.write(rgns->region, rgns->n_region*sizeof(cdiINT32));

    CDI_BFPR bfpr = bodf->bfdf[j].bfpr;
    read_bfpr(bfpr, "body force descriptor", cdi_lookup_physics(CDI_PHYS_TYPE_BODY_FORCE));
  }
  write_phys_desc_buffer_to_sps(bodf->n_bfdf);
  cdi_destroy_bodf(bodf);
  exit_cdi_chunk();
}

//----------------------------------------------------------------------------
// read_name
//----------------------------------------------------------------------------
cSTRING sCP_CDI_READER::read_name()
{
  cSTRING name = "Unknown";

  CDI_WITH_INNER_CHUNK(m_cdi_info) 
  {
    if (cdi_get_type(m_cdi_info) != CDI_CHUNK_TYPE_NAME) {
      msg_internal_error("Expected NAME as first sub-chunk of CDI PHYS chunk.");
    }
    else {
      CDI_NAME cdi_name = cdi_read_name(m_cdi_info);

      if (cdi_name->name != NULL) 
        name = cdi_name->name;
    }
  }

  return name;
}

//----------------------------------------------------------------------------
// read_global_physics_chunk
//
// IMPORTANT: If any changes are made to this function, the programmer must
// check if corresponding changes need to be made to the function read_bfpr as
// well. Read the comments above the read_bfpr definition for more info.
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_global_physics_chunk(cSTRING physics_desc_name, asINT32 lgi_record_tag, 
                                               CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc, BOOLEAN is_header, BOOLEAN is_name_present)
{
  LGI_PHYSICS_DESCRIPTOR physics_desc;

  physics_desc.tag.id               = lgi_record_tag;
  physics_desc.tag.length           = 0; // Filled in below
  physics_desc.phys_type_desc_index = phys_type_desc->cdi_physics_type;
  physics_desc.name_length          = 0; // Name is optional
  physics_desc.n_parameters         = phys_type_desc->n_continuous_dp;
  physics_desc.n_initial_conditions = 0;
  physics_desc.data_table_length    = 0;
  physics_desc.n_uds_parameters     = 0;
  physics_desc.n_layers             = -1;
  physics_desc.n_uds_initial_conditions = 0;

  if (phys_type_desc->n_initial_conditions != 0)
    msg_internal_error("Global physics descriptors are not allowed to have initial conditions");

  asINT32 n_chunks = cio_get_count(m_cdi_info->cio_info);
  // Name in CDI file is optional, but if present must be the first chunk
  if (is_name_present) {
    n_chunks--;
    CDI_WITH_INNER_CHUNK(m_cdi_info) {
      if (cdi_get_type(m_cdi_info) != CDI_CHUNK_TYPE_NAME) {
        msg_internal_error("Expected NAME as first sub-chunk of CDI PHYS chunk (%s).", physics_desc_name);
      } else {
        CDI_NAME cdi_name = cdi_read_name(m_cdi_info);
        if (cdi_name->name != NULL) {
          physics_desc.name_length = strlen(cdi_name->name) + 1;
        }
        cdi_destroy_name(cdi_name);
      }
    }
  }

  m_pds.write(&physics_desc, sizeof(physics_desc));
  if (physics_desc.name_length > 0)
    m_pds.write((VOID *)physics_desc_name, physics_desc.name_length);

  sVARIABLE parameters[CDI_MAX_CONTINUOUS_DP];
  cBOOLEAN parameter_found[CDI_MAX_CONTINUOUS_DP];

  ccDOTIMES(j, phys_type_desc->n_continuous_dp) {
    parameter_found[j] = FALSE;
  }

  ccDOTIMES(i, n_chunks) {
    CDI_WITH_INNER_CHUNK(m_cdi_info) {
      if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_CVDP) {
        CDI_CVDP cvdp = cdi_read_cvdp(m_cdi_info);
        if (cvdp == NULL)
          msg_internal_error("Missing CVDP chunk in %s chunk of CDI file.", physics_desc_name);
        else {
          asINT32 index = phys_type_desc->continuous_dp_index(cvdp->type);

          if (index < 0) {
            msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                cvdp->type, physics_desc_name);
          } else {
            parameters[index].value = cvdp->value;
            parameters[index].name = NULL;
            parameter_found[index] = TRUE;
          }
        }
      }
      else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQDP) {
        CDI_EQDP eqdp = cdi_read_eqdp(m_cdi_info);
        if (eqdp == NULL)
          msg_internal_error("Missing EQDP chunk in %s chunk of CDI file.", physics_desc_name);
        else {
          asINT32 index = phys_type_desc->continuous_dp_index(eqdp->type);

          if (index < 0) {
            msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                eqdp->type, physics_desc_name);
          } else {
            parameters[index].value = 0;
            parameters[index].name = eqdp->var_name;
            parameter_found[index] = TRUE;
          }
        }
      }
      else if ((!cdi_is_valid_geometry_chunk(m_cdi_info))
          && (cdi_get_type(m_cdi_info) != CDI_CHUNK_TYPE_CYLS)) {
        msg_internal_error("Unexpected sub-chunk of %s chunk in CDI file", physics_desc_name);
      }
    }
  }

  {
    ccDOTIMES(i, phys_type_desc->n_continuous_dp) {
      if (!parameter_found[i]) {
        asINT32 var_id = phys_type_desc->continuous_dp_var[i]->id;

        msg_internal_error("Missing parameter of type %d from %s chunk of CDI file.",
            var_id, physics_desc_name);
      }

      LGI_PHYSICS_VARIABLE var;
      var.value = parameters[i].value;
      var.name_length = parameters[i].name == NULL ? 0 : strlen(parameters[i].name) + 1;

      m_pds.write(&var, sizeof(var));

      if (parameters[i].name != NULL) {
        m_pds.write((VOID *)parameters[i].name, var.name_length);
      }
    }
  }

  if (is_header) {
    LGI_PHYSICS_DESCRIPTOR *h = (LGI_PHYSICS_DESCRIPTOR *)m_pds.buffer;
    h->tag.length = lgi_pad_and_encode_record_length(m_pds.size);
    write_record_to_all_sps(m_pds.buffer);
  }
}

//----------------------------------------------------------------------------
// read_phrg_chunk // read_physics_chunk
//----------------------------------------------------------------------------
asINT32 sCP_CDI_READER::read_physics_chunk(BOOLEAN is_flow_surface_p, BOOLEAN is_thermal_surface_p, asINT32 phys_desc_index,
                                           const std::vector<asINT32>& part_or_face_indices)
{ 
  const dFLOAT default_porous_media_nu_over_T = 0.05;

  asINT32 n_inner_chunks = cio_get_count(m_cdi_info->cio_info);

  sVARIABLE parameters     [CDI_MAX_CONTINUOUS_DP];
  cBOOLEAN  parameter_found[CDI_MAX_CONTINUOUS_DP];

  sVARIABLE initial_conditions     [CDI_MAX_INITIAL_CONDITIONS];
  cBOOLEAN  initial_condition_found[CDI_MAX_INITIAL_CONDITIONS];

  BOOLEAN found_some_eqn = FALSE;

  cSTRING physics_desc_name = read_name();

  CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc;
  CDI_PHYS_TYPE_DESCRIPTOR old_phys_type_desc;
  CDI_PTYP ptyp;
  LGI_PHYSICS_DESCRIPTOR physics_desc;
  asINT32 old_cdi_physics_type;

  BOOLEAN is_old_tfloat_type      = FALSE;
  BOOLEAN is_old_turb_type        = FALSE;
  BOOLEAN found_turb_via          = FALSE;
  BOOLEAN phys_descriptor_written = FALSE;

  // for surface coupling
  physics_desc.coupling_model_index = -1;
  physics_desc.init_pf_bc_coupling_p = 0;
  physics_desc.n_layers             = -1;

#if SURF_COUP
  // Separate thermal physics chunk was introduced in CDI version 9.5
  BOOLEAN coup_surf_check = CDI_INFO_VERSION_AT_LEAST(m_cdi_info, 9, 5)? is_thermal_surface_p : is_flow_surface_p;
  if (coup_surf_check) {
    for (asINT32 face : part_or_face_indices) {
      asINT32 cfmi = coupling_face_model_index(face);
      if (physics_desc.coupling_model_index == -1) {
        physics_desc.coupling_model_index = cfmi;
      } else if (physics_desc.coupling_model_index != cfmi) {
        msg_internal_error("The coupling model index for physics descriptor %s has already been set to %d, "
                           "but is now being overriden with %d, by face ID %d",
                           physics_desc_name, physics_desc.coupling_model_index, cfmi, face);
      }
      if (physics_desc.coupling_model_index >= 0) { // powerflow coupling wall (cplw)
        physics_desc.init_pf_bc_coupling_p = is_bc_init_from_coupling_model(physics_desc.coupling_model_index,face);
        // if even one of the CPLW pf bcs needs to read its initial conditions
        // from the coupling model, set the coupling model flag to TRUE
        if (physics_desc.init_pf_bc_coupling_p) {
          (cp_info.surface_couplings+physics_desc.coupling_model_index)->init_pf_bc_coupling_p = TRUE;
        }
      }
    }
  }
#endif // SURF_COUP
  
  if (g_seed_ctl.is_smart_seed() && !is_flow_surface_p && !is_thermal_surface_p) {
    g_seed_ctl.detect_fluid_physics_desc_for_seeded_regions(physics_desc_name, part_or_face_indices);
  }
 
 

  BOOLEAN is_stagnation_pressure_bc = FALSE;
  CDI_WITH_INNER_CHUNK(m_cdi_info) {
    if (cdi_get_type(m_cdi_info) != CDI_CHUNK_TYPE_PTYP) {
      msg_internal_error("Expected PTYP as second sub-chunk of CDI PHYS chunk.");
    } else {
      ptyp = cdi_read_ptyp(m_cdi_info);
      if(ptyp->type == CDI_PHYS_TYPE_SOLID_INSULATOR) {
	exit_cdi_chunk();
	return CDI_PHYS_TYPE_SOLID_INSULATOR;
      }
      old_cdi_physics_type = ptyp->type;
      old_phys_type_desc = cdi_lookup_physics(ptyp->type);
      convert_phys_type(ptyp, &is_old_tfloat_type, &is_old_turb_type);
//      if(ptyp->type == CDI_PHYS_TYPE_SOLID_CONDUCTOR)
//    	  phys_type_desc = cdi_lookup_physics(CDI_PHYS_TYPE_SOLID_CONDUCTOR);
//      else
      phys_type_desc = cdi_lookup_physics(ptyp->type);
      
      if (ptyp->type == CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR||
          ptyp->type == CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR ||
          ptyp->type == CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR||
          ptyp->type == CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR ) {
        is_stagnation_pressure_bc = TRUE;
      }
      
      if (old_phys_type_desc == NULL) {
        msg_internal_error("Unknown CDI physics type: %d.", old_cdi_physics_type);
      }
      else if (phys_type_desc == NULL) {
        msg_internal_error("Unknown CDI physics type: %d (converted from %d).", ptyp->type, old_cdi_physics_type);
      }
      else {
        physics_desc.phys_type_desc_index = ptyp->type;
        physics_desc.name_length          = strlen(physics_desc_name) + 1;
        physics_desc.n_parameters         = phys_type_desc->n_continuous_dp;
        physics_desc.n_initial_conditions = phys_type_desc->n_initial_conditions;
        physics_desc.data_table_length    = 0;

        ccDOTIMES(i, phys_type_desc->n_continuous_dp) {
          parameter_found[i] = FALSE;
        }
        ccDOTIMES(j, phys_type_desc->n_initial_conditions) {
          initial_condition_found[j] = FALSE;
        }
      }
    }
  }  

  //For UDS  
  asINT32    n_scalars = cp_info.n_scalars;
  CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = NULL;
  sVARIABLE* uds_parameters = NULL;
  sVARIABLE* uds_initial_conditions = NULL;
  cBOOLEAN*  uds_parameters_found = NULL;
  cBOOLEAN*  uds_initial_conditions_found = NULL;
  asINT32    n_uds_parameters = 0;
  asINT32    n_uds_initial_conditions = 0;
  cBOOLEAN   found_udst_chunk = FALSE;
  cBOOLEAN   found_uds_physics_type = FALSE;
  
  cCDI_USER_DEFINED_VARS* cdi_uds_vars =  new cCDI_USER_DEFINED_VARS(n_scalars);  
  if (cp_info.is_uds_transport && !cp_info.is_5g_sim) { //No PowerCase support for 5G yet, so 5G should not go into it
    uds_phys_type_desc = find_uds_physics(ptyp->type);
    if (uds_phys_type_desc == NULL) {
      msg_warn("Not find uds physics type for cdi physics type %d in CDI physics descriptor \"%s\".", 
                ptyp->type, physics_desc_name);
    } else {
      found_uds_physics_type = TRUE;         
      n_uds_parameters         = n_scalars * uds_phys_type_desc->n_parameters;
      n_uds_initial_conditions = n_scalars * uds_phys_type_desc->n_initial_conditions;
    
      uds_parameters = new sVARIABLE[n_uds_parameters];
      uds_parameters_found = new cBOOLEAN[n_uds_parameters];
      uds_initial_conditions = new sVARIABLE[n_uds_initial_conditions];
      uds_initial_conditions_found = new cBOOLEAN[n_uds_initial_conditions];
    
      ccDOTIMES(i, n_uds_parameters) {
	uds_parameters_found[i] = FALSE;
      }
      ccDOTIMES(j, n_uds_initial_conditions) {
	uds_initial_conditions_found[j] = FALSE;
      }
    }
  }
  physics_desc.n_uds_parameters         = n_uds_parameters;
  physics_desc.n_uds_initial_conditions = n_uds_initial_conditions;
  
  BOOLEAN data_table_found = FALSE;
  ccDOTIMES(m, n_inner_chunks - 2) {
    CDI_WITH_INNER_CHUNK(m_cdi_info) {
      if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQNS) {
        if (data_table_found)
          msg_internal_error("Only one EQNS chunk allowed per PHYS chunk of CDI file.");
        data_table_found = TRUE;

        if (phys_descriptor_written)
          msg_internal_error("EQNS chunk of CDI PHYS chunk must appear before DPRM or IVAL chunks.");

        asINT32 data_table_length = cdi_eqns_read_length(m_cdi_info);
        if (data_table_length < 0)
          msg_internal_error("Failure reading PHYS chunk data table length from CDI file.");

        physics_desc.data_table_length = data_table_length;

        m_pds.write(&physics_desc, sizeof(physics_desc));
        m_pds.write((VOID *)physics_desc_name, physics_desc.name_length);
        phys_descriptor_written = TRUE;

        const int BUFSIZE = 1024;
        char buf[BUFSIZE];
        asINT32 n_left = data_table_length;

        while (n_left > 0) {
          asINT32 n = MIN(n_left, BUFSIZE);
          if (cdi_eqns_read_chars(m_cdi_info, buf, n) != n)
            msg_internal_error("Failure reading PHYS chunk data table contents from CDI file.");
          m_pds.write((VOID *)buf, n);
          n_left -= n;
        }
      }
      else {
        if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_DPRM) {
          asINT32 n_parameter_chunks = cio_get_count(m_cdi_info->cio_info);
          asINT32 n_good_parameters_read = 0;

          if (n_parameter_chunks != ptyp->n_continuous) 
            msg_warn("2 Parameter count (%d) in PTYP sub-chunk of PHYS chunk \"%s\" should be %d.",
		     ptyp->n_continuous, physics_desc_name, n_parameter_chunks);

          // determine the face index if there is any boundary seeding via sample surf meas file
          asINT32 seed_from_meas_desc_index = -1;
          asINT32 face_index = -1;
          if (cp_info.n_seed_from_meas_descs > 0) {
            ccDOTIMES(i, cp_info.n_sri_faces) {
              sSRI_FACE& face = cp_info.sri_faces[i];
              BOOLEAN is_front = face.is_front; //true if face is FrontOnly or FrontAndBack
              BOOLEAN is_back = !cp_info.face_index_is_front_only[i]; //must be BackOnly or FrontAndBack
              if ((is_front && (cp_info.front_flow_surface_phys_desc_index_from_face_index[i] == phys_desc_index)) ||
                  (is_back && (cp_info.back_flow_surface_phys_desc_index_from_face_index[i] == phys_desc_index))) {
                seed_from_meas_desc_index = cp_info.seed_from_meas_desc_index_from_face_index[i];
                face_index = i;
              }
            }
          }
          auINT32 seed_from_meas_mask = 0;
          cBOOLEAN import_fluctuations = FALSE;
          cp_info.is_rwnc_seeded = FALSE;

          ccDOTIMES(i, n_parameter_chunks) {
            CDI_WITH_INNER_CHUNK(m_cdi_info) {
              if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_CVDP) {
                CDI_CVDP cvdp = cdi_read_cvdp(m_cdi_info);
                if (cvdp == NULL)
                  msg_internal_error("Missing CVDP chunk in DPRM chunk of CDI file.");
                else {
                  asINT32 index = phys_type_desc->continuous_dp_index(cvdp->type);
                  if (index < 0)
                    index = old_phys_type_desc->continuous_dp_index(cvdp->type);

                  if (cvdp->type == CDI_VAR_ID_TURB_VIA)
                    found_turb_via = TRUE;

                  if (cvdp->type == CDI_VAR_ID_TABULAR_DATA_ID) {
                    if (cvdp->value >= 0)
                      cp_info.num_turb_tables++;
                  }

                  if (seed_from_meas_desc_index >= 0 && cvdp->type == CDI_VAR_ID_MEAS_FRAME_NUM) {
                    cp_info.seed_from_meas_descs[seed_from_meas_desc_index]->m_meas_frame_num = cvdp->value;
                  }
                  if (seed_from_meas_desc_index >= 0 && cvdp->type == CDI_VAR_ID_MEAS_START_FRAME_NUM) {
                    cp_info.seed_from_meas_descs[seed_from_meas_desc_index]->m_meas_start_frame_num = cvdp->value;
                    if(cvdp->value >= 0)
                      cp_info.is_face_tbs[face_index] = TRUE;
                  }


                  if(cvdp->type == CDI_VAR_ID_IMPORT_FLUCTUATIONS && cvdp->value > 0)
                    import_fluctuations = TRUE;

                  if (cvdp->type == CDI_VAR_ID_NU_ON_T) {
                    BOOLEAN issue_warning = FALSE;
                    switch (ptyp->type) {
                      case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID:
                      case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_FIXED_TEMP:
                      case CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_THERMAL:
                      case CDI_PHYS_TYPE_LES_INLINE_FAN:
                      case CDI_PHYS_TYPE_INLINE_FAN:
                      case CDI_PHYS_TYPE_LES_INLINE_TABLE_FAN:
                      case CDI_PHYS_TYPE_INLINE_TABLE_FAN:
                        // Catch situations where it is set to an equation, where cvdp->value would evaluate to 0.0
                        issue_warning = (cvdp->value >= 0.0 && cvdp->value != default_porous_media_nu_over_T); 
                        break;

                      case CDI_PHYS_TYPE_LES_VV_FLUID:
                      case CDI_PHYS_TYPE_NEW_LES_VV_FLUID:
                        // Catch situations where it is set to an equation, where cvdp->value would evaluate to 0.0
                        issue_warning = (cvdp->value >= 0.0); 
                        break;

                      default:
                        break;
                    }
                    if (issue_warning) {
                      msg_warn("Fluid region \"%s\" has a non-default value of nu/T.", physics_desc_name);
                    }
                  }


                  if (index < 0) {
                    // msg_warn("Illegal DPRM CVDP %d for CDI PHYS chunk \"%s\".", cvdp->type, physics_desc_name);
                    if (complain_about_unrecognized_physics_parameter(old_cdi_physics_type, cvdp->type))
                      msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                          cvdp->type, physics_desc_name);
                  }
                  else {
                    // overwrite the value of the inlet meas window index with
                    // the CP meas window index rather than CDI meas window
                    // index
                    if (cvdp->type == CDI_VAR_ID_CURVED_HX_MEAS_WINDOW_INDEX) {
                      parameters[index].value = curved_hx_meas_window_index(phys_desc_index);
                      if (parameters[index].value < 0)
                        msg_internal_error("Unable to find meas window index for curved heat exchanger \"%s\"", physics_desc_name);
                    }
                    else {
                      parameters[index].value = cvdp->value;
                    }                  
                    
                    parameters[index].name = NULL;
                    parameter_found[index] = TRUE;

                    n_good_parameters_read++;
                  }
                }
              }
              else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQDP) {
                CDI_EQDP eqdp = cdi_read_eqdp(m_cdi_info);
                if (eqdp == NULL)
                  msg_internal_error("Missing EQDP chunk in DPRM chunk of CDI file.");
                else {
                  asINT32 index = phys_type_desc->continuous_dp_index(eqdp->type);
                  if (index < 0)
                    index = old_phys_type_desc->continuous_dp_index(eqdp->type);

                  if (eqdp->type == CDI_VAR_ID_TURB_VIA)
                    found_turb_via = TRUE;

                  found_some_eqn = TRUE;


                  if (index < 0) {
                    // msg_warn("Illegal DPRM EQDP %d for CDI PHYS chunk \"%s\".", eqdp->type, physics_desc_name);
                    if (complain_about_unrecognized_physics_parameter(old_cdi_physics_type, eqdp->type))
                      msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                               eqdp->type, physics_desc_name);
                  } else {
                    parameters[index].value = 0;
                    parameters[index].name = eqdp->var_name;
                    parameter_found[index] = TRUE;
                    n_good_parameters_read++;
                  }
                  
                  if (CDI_VAR_ID_NU_ON_T == eqdp->type) {
                      msg_warn("Fluid region \"%s\" has a non-default value of nu/T.", physics_desc_name);
                  }
                }
              }
              else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_BSDP) {
                // TODOanush: Throw error if seed_from_meas_desc_index == -1 here? This might never actually happen
                CDI_BSDP bsdp = cdi_read_bsdp(m_cdi_info);
                if (bsdp == NULL) {
                  msg_internal_error("Missing BSDP chunk in DPRM chunk of CDI file.");
                } else {
                  asINT32 index = phys_type_desc->continuous_dp_index(bsdp->type);
                  if (index < 0) index = old_phys_type_desc->continuous_dp_index(bsdp->type);

                  dFLOAT default_value = bsdp->value;

                  switch(bsdp->type) {
                    case CDI_VAR_ID_PRESSURE:
                    case CDI_VAR_ID_NEW_PRESSURE:
                      if (is_stagnation_pressure_bc) {
                        seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_TOTAL_PRESSURE);
                      } else {
                        seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_PRESSURE);
                      }
                      break;
                    case CDI_VAR_ID_VEL_X:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_XVEL);
                      break;
                    case CDI_VAR_ID_UNIT_VEC_X:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_FLOW_DIR_X);
                      break;
                    case CDI_VAR_ID_VEL_Y:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_YVEL);
                      break;
                    case CDI_VAR_ID_UNIT_VEC_Y:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_FLOW_DIR_Y);
                      break;
                    case CDI_VAR_ID_VEL_Z:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_ZVEL);
                      break;
                    case CDI_VAR_ID_UNIT_VEC_Z:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_FLOW_DIR_Z);
                      break;
                    case CDI_VAR_ID_TEMP:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_TEMP);
                      break;
                    case CDI_VAR_ID_TURB_KE:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY);
                      break;
                    case CDI_VAR_ID_TURB_DISSIPATION:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION);
                      break;
                    case CDI_VAR_ID_MASS_FLUX_X:
                    case CDI_VAR_ID_NEW_MASS_FLUX_X:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX);
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_XVEL);
                      break;
                    case CDI_VAR_ID_MASS_FLUX_Y:
                    case CDI_VAR_ID_NEW_MASS_FLUX_Y:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX);
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_YVEL);
                      break;
                    case CDI_VAR_ID_MASS_FLUX_Z:
                    case CDI_VAR_ID_NEW_MASS_FLUX_Z:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX);
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_ZVEL);
                      break;
                    case CDI_VAR_ID_TURB_INTENSITY:
                    case CDI_VAR_ID_TURB_LENGTH_SCALE:
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY);
                      seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION);
                      break;
                    case CDI_VAR_ID_MASS_FLOW_RATE:
                      msg_internal_error("Boundary seeding is not supported for the Mass Flow boundary condition "
                                         "used in CDI physics descriptor \"%s\"", physics_desc_name);
                      break;
                    default:
                      msg_internal_error("CDI Variable ID %d is not permitted in a BSDP chunk of CDI physics descriptor \"%s\".",
                                         bsdp->type, physics_desc_name);
                      break;
                  }

                  if (index < 0) {
                    if (complain_about_unrecognized_physics_parameter(old_cdi_physics_type, bsdp->type))
                      msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                               bsdp->type, physics_desc_name);
                  } else {
                    parameters[index].value = default_value;
                    parameters[index].name = NULL;
                    parameter_found[index] = TRUE;
                    n_good_parameters_read++;
                  }                  
                }
              }
              else {
                msg_internal_error("In CDI file, unexpected sub-chunk of DPRM chunk (see PHYS chunk \"%s\")", physics_desc_name);
              }
            }
            if(import_fluctuations) {
              int ts_index = -1;
              cp_info.is_rwnc_seeded = TRUE;
              for(int ts_var = CDI_VAR_ID_TURB_INTENSITY_X; ts_var <= CDI_VAR_ID_TABULAR_DATA_ID; ts_var ++) {
                ts_index = phys_type_desc->continuous_dp_index(ts_var);
                parameter_found[ts_index] = FALSE;
              }
              seed_from_meas_mask = (1 << DGF_BOUNDARY_SEED_VAR_XVEL) | (1 << DGF_BOUNDARY_SEED_VAR_YVEL) | (1 << DGF_BOUNDARY_SEED_VAR_PRESSURE);
              if(parameter_found[phys_type_desc->continuous_dp_index(CDI_VAR_ID_ANG_VEL_Z)])
                seed_from_meas_mask |= (1 << DGF_BOUNDARY_SEED_VAR_ZVEL);
            }
          }

          if (seed_from_meas_desc_index >= 0) {
            if (seed_from_meas_mask) {
              cp_info.seed_from_meas_mask_from_face_index[face_index] = seed_from_meas_mask;
              cp_info.seed_from_meas_descs[seed_from_meas_desc_index]->m_masks_union |= seed_from_meas_mask;
            } else {
              cp_info.seed_from_meas_desc_index_from_face_index[face_index] = -1; // face index is not -1 if seed_from_meas_desc_index is not -1
            }
          }

        }
        else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_IVAL) {
          asINT32 n_ic_chunks = cio_get_count(m_cdi_info->cio_info);
          asINT32 n_good_ics_read = 0;

          if (n_ic_chunks != ptyp->n_initial)
            msg_warn("Initial condition count (%d) in PTYP sub-chunk of PHYS chunk \"%s\" should be %d.",
                     ptyp->n_initial, physics_desc_name, n_ic_chunks);

          ccDOTIMES(i, n_ic_chunks) {
            CDI_WITH_INNER_CHUNK(m_cdi_info) {
              if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_CVDP) {
                CDI_CVDP cvdp = cdi_read_cvdp(m_cdi_info);
                if (cvdp == NULL)
                  msg_internal_error("Missing CVDP chunk in IVAL chunk of CDI file.");
                else {
                  asINT32 index = phys_type_desc->initial_condition_index(cvdp->type);
                  BOOLEAN converted_to_dprm = FALSE;
                  if (index < 0) {
                    // WATER_VAPOR_SOURCE_TERM was changed from IC to DPRM
                    if (cvdp->type == CDI_VAR_ID_WATER_VAPOR_SOURCE_TERM) {
                      index = phys_type_desc->continuous_dp_index(cvdp->type);
                      if (index >= 0) {
                        converted_to_dprm = TRUE;
                        parameters[index].value = cvdp->value;
                        parameters[index].name = NULL;
                        parameter_found[index] = TRUE;
                        //n_good_parameters_read++;
                      }
                    } else {
                      index = old_phys_type_desc->initial_condition_index(cvdp->type);
                    }
                  }

                  if (!converted_to_dprm) {
                    if (cvdp->type == CDI_VAR_ID_TURB_VIA)
                      found_turb_via = TRUE;

                    if (index < 0) {
                      if (complain_about_unrecognized_physics_parameter(old_cdi_physics_type, cvdp->type))
                        msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", cvdp->type, physics_desc_name);
                    }
                    else {
                      initial_conditions[index].value = cvdp->value;
                      initial_conditions[index].name = NULL;
                      initial_condition_found[index] = TRUE;
                      n_good_ics_read++;
                    }
                  }
                }
              }
              else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQDP) {
                CDI_EQDP eqdp = cdi_read_eqdp(m_cdi_info);
                if (eqdp == NULL)
                  msg_internal_error("Missing EQDP chunk in IVAL chunk of CDI file.");
                else {
                  asINT32 index = phys_type_desc->initial_condition_index(eqdp->type);
                  BOOLEAN converted_to_dprm = FALSE;
                  if (index < 0) {
                    // WATER_VAPOR_SOURCE_TERM was changed from IC to DPRM
                    if (eqdp->type == CDI_VAR_ID_WATER_VAPOR_SOURCE_TERM) {
                      index = phys_type_desc->continuous_dp_index(eqdp->type);
                      if (index >= 0) {
                        converted_to_dprm = TRUE;
                        found_some_eqn = TRUE;
                        parameters[index].value = 0;
                        parameters[index].name = eqdp->var_name;
                        parameter_found[index] = TRUE;
                        //n_good_parameters_read++;
                      }
                    } else {
                      index = old_phys_type_desc->initial_condition_index(eqdp->type);
                    }
                  }
                    
                  if (!converted_to_dprm) {
                    if (eqdp->type == CDI_VAR_ID_TURB_VIA)
                      found_turb_via = TRUE;

                    found_some_eqn = TRUE;

                    if (index < 0) {
                      if (complain_about_unrecognized_physics_parameter(old_cdi_physics_type, eqdp->type))
                        msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", eqdp->type, physics_desc_name);
                    }
                    else {
                      initial_conditions[index].value = 0;
                      initial_conditions[index].name = eqdp->var_name;
                      initial_condition_found[index] = TRUE;
                      n_good_ics_read++;
                    }
                  }
                }
              }
              else {
                msg_warn("In CDI file, unexpected sub-chunk of IVAL chunk (see PHYS chunk \"%s\")", physics_desc_name);
              }
            }
          }
        }
	else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_UDST) {
	  if (!found_uds_physics_type)
	    msg_internal_error("cdi phys type (%d) in PHYS chunk \"%s\" does not have uds phys type, but has UDST sub-chunk", ptyp->type, physics_desc_name);
	  
	  asINT32 n_uds_chunks = cio_get_count(m_cdi_info->cio_info);

	  asINT32 n_uds_parameters_per_uds = uds_phys_type_desc->n_parameters;
	  asINT32 n_uds_initial_conditions_per_uds = uds_phys_type_desc->n_initial_conditions;
	  asINT32 n_total_uds_vars = n_uds_parameters + n_uds_initial_conditions;

	  found_udst_chunk = TRUE;
	
	  ccDOTIMES(i, n_uds_chunks) {
            CDI_WITH_INNER_CHUNK(m_cdi_info) {
              if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_CVDP) {
                CDI_CVDP cvdp = cdi_read_cvdp(m_cdi_info);
                if (cvdp == NULL)
                  msg_internal_error("Missing CVDP chunk in UDST chunk of CDI file.");                

		CDI_VAR_ID_UDS_OFFSET offset;
		asINT32 nth_uds;
		if (cdi_uds_vars->GetScalarTypeAndIndex(cvdp->type, &nth_uds, &offset)) {
		  asINT32 offset_var = (asINT32)offset;
		  asINT32 prm_index = uds_phys_type_desc->parameter_index(offset_var);
		  asINT32 inc_index = uds_phys_type_desc->initial_condition_index(offset_var);
		  
		  if (prm_index < 0 && inc_index < 0)
		    msg_internal_error("Invalid uds parameter index (%d) or uds initial condition index (%d) for cvdp type (%d).", prm_index, inc_index, cvdp->type);
		  
		  if (prm_index >= 0) {
		    asINT32 index_in_array = prm_index +  n_uds_parameters_per_uds * nth_uds;  //[nth_uds][prm_index]
		    uds_parameters[index_in_array].value = cvdp->value;
		    uds_parameters[index_in_array].name = NULL;
		    uds_parameters_found[index_in_array] = TRUE;		      
		  } else { //inc_index >= 0
		    asINT32 index_in_array = inc_index + n_uds_initial_conditions_per_uds * nth_uds;  //[nth_uds][inc_index]
		    uds_initial_conditions[index_in_array].value = cvdp->value;
		    uds_initial_conditions[index_in_array].name = NULL;
		    uds_initial_conditions_found[index_in_array] = TRUE;		      
		  }
		} else {
		  asINT32 offset_var = (asINT32)offset;
		  msg_internal_error("cvdp (%d) in UDST sub_chunk of PHYS chunk \"%s\" has invalid nth_uds (%d) or offset_var (%d).\n", cvdp->type, physics_desc_name, nth_uds, offset_var);
		}
	      } else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQDP) {
		CDI_EQDP eqdp = cdi_read_eqdp(m_cdi_info);
                if (eqdp == NULL)
                  msg_internal_error("Missing EQDP chunk in UDST chunk of CDI file.");

		CDI_VAR_ID_UDS_OFFSET offset;
		asINT32 nth_uds;
		
		if (cdi_uds_vars->GetScalarTypeAndIndex(eqdp->type, &nth_uds, &offset)) {
		  asINT32 offset_var = (asINT32)offset;
		  asINT32 prm_index = uds_phys_type_desc->parameter_index(offset_var);
		  asINT32 inc_index = uds_phys_type_desc->initial_condition_index(offset_var);
		  
		  if (prm_index < 0 && inc_index < 0)
		    msg_internal_error("Invalid uds parameter index (%d) or uds initial condition index (%d) for eqdp type (%d).", prm_index, inc_index, eqdp->type);

		  found_some_eqn = TRUE;

		  if (prm_index >= 0) {
		    asINT32 index_in_array = prm_index + n_uds_parameters_per_uds * nth_uds;  //[nth_uds][prm_index]
		    uds_parameters[index_in_array].value = 0;
		    uds_parameters[index_in_array].name = eqdp->var_name;
		    uds_parameters_found[index_in_array] = TRUE;		      
		  } else { //inc_index >= 0
		    asINT32 index_in_array = inc_index + n_uds_initial_conditions_per_uds * nth_uds;  //[nth_uds][inc_index]
		    uds_initial_conditions[index_in_array].value = 0;
		    uds_initial_conditions[index_in_array].name = eqdp->var_name;
		    uds_initial_conditions_found[index_in_array] = TRUE;		      
		  }		    		  
		} else{
		  asINT32 offset_var = (asINT32)offset;
		  msg_internal_error("eqdp (%d) in UDST sub_chunk of PHYS chunk \"%s\" has invalid nth_uds (%d) or offset_var (%d)\n", eqdp->type, physics_desc_name, nth_uds, offset_var);
		}
	      } else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_BSDP) {
		msg_internal_error("Not support uds boundary seeding.");
	      } else {
                msg_warn("In CDI file, unexpected sub-chunk of UDST chunk (see PHYS chunk \"%s\")", physics_desc_name);
              }
	      
	    }
	  }	    
	}
        else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_FLST) {
          /* only discretizer needs this info for the curved porous medium, so
           * skip here */
          CDI_FLST flst = cdi_read_flst(m_cdi_info);
          if (flst == NULL)
            msg_internal_error("Missing FLST chunk in PHYS chunk of CDI file.");
        }
        else {
          msg_warn("In CDI file, unexpected sub-chunk of PHYS chunk \"%s\"", physics_desc_name);
        }
      }
    }
  }

  if (!phys_descriptor_written) {
    m_pds.write(&physics_desc, sizeof(physics_desc));
    m_pds.write((VOID *)physics_desc_name, physics_desc.name_length);
    phys_descriptor_written = TRUE;
  }

  convert_physics_chunk_vars(old_cdi_physics_type, parameters, initial_conditions,found_some_eqn);

  ccDOTIMES(j, phys_type_desc->n_continuous_dp) {
    if (!parameter_found[j]) {
      asINT32 var_id = phys_type_desc->continuous_dp_var[j]->id;

#define SPVB(x)  { parameters[j].value = (x); parameters[j].name = NULL; issue_error = FALSE; break; }
#define SPV(x)  { parameters[j].value = (x); parameters[j].name = NULL; issue_error = FALSE; }

      BOOLEAN issue_error = TRUE;
      switch(var_id) {
      case CDI_VAR_ID_COORD_SYSTEM:
        SPVB(-1);       // Global coord system
      case CDI_VAR_ID_POINT_X:
      case CDI_VAR_ID_POINT_Y:
      case CDI_VAR_ID_POINT_Z:
      case CDI_VAR_ID_RAXIS_X:
      case CDI_VAR_ID_RAXIS_Y:
      case CDI_VAR_ID_RAXIS_Z:
      case CDI_VAR_ID_GAXIS_X:
      case CDI_VAR_ID_GAXIS_Y:
      case CDI_VAR_ID_GAXIS_Z:
      case CDI_VAR_ID_CURVED_POROUS_MEDIUM:
        SPVB(0);
      case CDI_VAR_ID_SURFACE_ROUGHNESS:
        SPVB(0);        // Surface roughness char height
      case CDI_VAR_ID_SURFACE_CHAR_LEN:
        SPVB(-1);       // Wall model char length (default is global char len)
      case CDI_VAR_ID_RESPONSE_TIME:
        SPVB(-1);       // Inlet/outlet response time
      case CDI_VAR_ID_DIST_TO_REFL_SURFACE:
        SPVB(-1);       // Distance to reflecting surface
      case CDI_VAR_ID_REF_FRAME:
        SPVB(CDI_REF_FRAME_GLOBAL);     // Global body-fixed reference frame
      case CDI_VAR_ID_DENSITY_T0:
        SPVB(-1);       // Density at t=0
      case CDI_VAR_ID_TEMP:
        if (is_old_tfloat_type)
          SPV(-1);      // Floating temmperature 
        break;
      case CDI_VAR_ID_NU_ON_T:
        if (old_cdi_physics_type == CDI_PHYS_TYPE_FV_FLUID)
          SPV(0.5);     // nu/T
        break;
      case CDI_VAR_ID_VISCOUS_DAMPING_FACTOR:
          SPV(-1.0);     // use specified nu/T from pre viscous damping factor CDI files
        break;
      case CDI_VAR_ID_TURB_VIA:
        if (m_accept_old_turb_cdi)
          SPV(CDI_TURB_VIA_INTENSITY);
        break;
      case CDI_VAR_ID_TURB_INTENSITY:
        if (found_turb_via || m_accept_old_turb_cdi)
          SPV(0.01);    // 1%
        break;
      case CDI_VAR_ID_TURB_LENGTH_SCALE:
        if (found_turb_via || m_accept_old_turb_cdi)
          SPV(1);
        break;
      case CDI_VAR_ID_TURB_KE:
        if (found_turb_via || m_accept_old_turb_cdi)
          SPV(0);
        break;
      case CDI_VAR_ID_TURB_DISSIPATION: 
        if (found_turb_via || m_accept_old_turb_cdi)
          SPV(0);
        break;
      case CDI_VAR_ID_RESISTANCE_REF_TEMP:
        SPVB(-1);       // Thermal PM resistance reference temperature
      case CDI_VAR_ID_USE_DYN_HEAT_RATE:
        SPVB(0);        // Thermal PM dynamic heating rate
      case CDI_VAR_ID_DYN_HEAT_RATE_COEFF0:
        SPVB(1);        // Thermal PM dynamic heating rate coefficient 0
      case CDI_VAR_ID_DYN_HEAT_RATE_COEFF1:
        SPVB(0);        // Thermal PM dynamic heating rate coefficient 1
      case CDI_VAR_ID_DYN_HEAT_RATE_COEFF2:
        SPVB(0);        // Thermal PM dynamic heating rate coefficient 2
      case CDI_VAR_ID_SURFACE_MATERIAL:
        SPVB(-1); 
      case CDI_VAR_ID_VEL_SLIP_TYPE:
        SPVB(1);
      case  CDI_VAR_ID_APM_TORTUOSITY:
        SPVB(1);
      case CDI_VAR_ID_BOUNDARY_LAYER_TYPE:
        SPVB(-1);
      case CDI_VAR_ID_FAR_FIELD_BC:
        SPVB(0);
      case CDI_VAR_ID_CONDENSABLE_SURFACE:
      case CDI_VAR_ID_FILM_THICKNESS:
      case CDI_VAR_ID_WATER_MASS_FRACTION:
      case CDI_VAR_ID_RELATIVE_HUMIDITY:
        SPVB(-1);
      case CDI_VAR_ID_TURB_INTENSITY_X:
        SPVB(0);
      case CDI_VAR_ID_TURB_INTENSITY_Y:
        SPVB(0);
      case CDI_VAR_ID_TURB_INTENSITY_Z:
        SPVB(0);
      case CDI_VAR_ID_TURB_LEN_SCALE_X:
        SPVB(0);
      case CDI_VAR_ID_TURB_LEN_SCALE_Y:
        SPVB(0);
      case CDI_VAR_ID_TURB_LEN_SCALE_Z:
        SPVB(0);
      case CDI_VAR_ID_TURB_FREQ_MIN:
        SPVB(0);
      case CDI_VAR_ID_TURB_FREQ_MAX:
        SPVB(0);
      case CDI_VAR_ID_TURB_DELTA_T:
        SPVB(0);
      case CDI_VAR_ID_TABULAR_DATA_ID:
        SPVB(-1);
      case CDI_VAR_ID_INLET_AREA:
        SPVB(-1);
      case CDI_VAR_ID_WATER_VAPOR_SOURCE_TERM:
        SPVB(-1);
      case CDI_VAR_ID_USE_REFL_DAMPING:
        SPVB(-1);
      case CDI_VAR_ID_APM_USE_ADV_FLUID:
        SPVB(0);
      case CDI_VAR_ID_APM_VISCOUS_RES:
        SPVB(0);
      case CDI_VAR_ID_APM_INERTIAL_RES:
        SPVB(0);
      case CDI_VAR_ID_APM_REACTANCE_COEFF:
        SPVB(0);
      case CDI_VAR_ID_APM_SURFACE_POROSITY:
        SPVB(1);
      case CDI_VAR_ID_MEAS_FRAME_NUM:
        SPVB(-1);
      case CDI_VAR_ID_IS_TOTAL_TEMP:
        SPVB(0);
      case CDI_VAR_ID_IS_SUPERSONIC_INLET:
        SPVB(0)
      case CDI_VAR_ID_SS_INLET_PRESSURE:
        SPVB(-1)
      case CDI_VAR_ID_REVERSE_MASS_FLOW:
        SPVB(0)
      case CDI_VAR_ID_ROTATION_VIA_REF_FRAME:
        SPVB(0)  
      case CDI_VAR_ID_SURFACE_ACOUSTIC_ABSORB:
        SPVB(0);  
      case CDI_VAR_ID_VEL_N:
        SPVB(0);
      case CDI_VAR_ID_IS_VEL_N:
        SPVB(0);
      case CDI_VAR_ID_MEAS_START_FRAME_NUM:
        SPVB(-1);
      case CDI_VAR_ID_DEFORMING_TIRE:
        SPVB(-1);
      case CDI_VAR_ID_IMPORT_FLUCTUATIONS:
        SPVB(-1);
      case CDI_VAR_ID_SOLID_MATERIAL:
        SPVB(-1);
      case CDI_VAR_ID_SHELL_CONFIGURATION:
        SPVB(-1);
      case CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT:
        SPVB(-1);
      case CDI_VAR_ID_SPECIFY_HEAT_VIA_POWER_DENSITY:
        SPVB(1);
      case CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT_IN_POWER:
        SPVB(-1);
      case CDI_VAR_ID_PM_LEAKAGE_RESOLUTION:
	SPVB(-1);
      case CDI_VAR_ID_HEAT_FLUX_2:
        SPVB(0);
      case CDI_VAR_ID_SHELL_LAYER_THICKNESS:
        SPVB(-1);
      case CDI_VAR_ID_SOLID_COMPUTE_RADIATION:
        SPVB(0);
      case CDI_VAR_ID_RADIATION_SURFACE_COND:
        SPVB(-1);
      case CDI_VAR_ID_ANISOTROPY_USE_PART_AXIS:
        SPVB(0);
      case CDI_VAR_ID_MIN_DESIGN_TEMP:
      case CDI_VAR_ID_MAX_DESIGN_TEMP:
        SPVB(0);
      case CDI_VAR_ID_RADIATION_TEMP:
        SPVB(-1)
      }

      if (issue_error)
        msg_internal_error("Expected CDI_VAR_ID parameter of type %d (look in CDI/cdi_readwrite.h) for physics descriptor \"%s\" was not provided in the CDI file, and no default value was found. "
                           "Most likely the physics descriptor in CDI was modified and a default value needs to be provided in CP/%s(). "
                           "You might need to modify the associated PARAMETERS class in SIMENG to match as well.", var_id, physics_desc_name, __FUNCTION__);
#undef SPVB
#undef SPV
    }
    else {
      if (m_cdi_info->major_version <= 3 && m_cdi_info->minor_version <= 5 && 
          (CDI_VAR_ID_POROUS_Q0 == phys_type_desc->continuous_dp_var[j]->id)) {
        parameters[j].value *= 1.0667; // scale old constant heat input values by 6.67%
      }
    }
  }

  ccDOTIMES(k, phys_type_desc->n_initial_conditions) {
    asINT32 var_id = phys_type_desc->initial_condition_var[k]->id;

    if (!initial_condition_found[k]) {
      if (var_id == CDI_VAR_ID_COORD_SYSTEM) {
        initial_conditions[k].value = -1;       // Global coord system
        initial_conditions[k].name = NULL;
      }
      else if (is_old_tfloat_type && (var_id == CDI_VAR_ID_TEMP)) {
        initial_conditions[k].value = -1;       // Floating temmperature 
        initial_conditions[k].name = NULL;
      }
      else if (m_accept_old_turb_cdi 
               && (var_id == CDI_VAR_ID_TURB_VIA)) {
        initial_conditions[k].value = CDI_TURB_VIA_INTENSITY;
        initial_conditions[k].name = NULL;
      }
      else if ((var_id == CDI_VAR_ID_TURB_INTENSITY) 
               && (found_turb_via || m_accept_old_turb_cdi)) {
        initial_conditions[k].value = 0.01;     // 1%
        initial_conditions[k].name = NULL;
      }
      else if ((var_id == CDI_VAR_ID_TURB_LENGTH_SCALE) 
               && (found_turb_via || m_accept_old_turb_cdi)) {
        initial_conditions[k].value = 1;
        initial_conditions[k].name = NULL;
      }
      else if ((var_id == CDI_VAR_ID_TURB_KE) 
               && (found_turb_via || m_accept_old_turb_cdi)) {
        initial_conditions[k].value = 0;
        initial_conditions[k].name = NULL;
      }
      else if ((var_id == CDI_VAR_ID_TURB_DISSIPATION) 
               && (found_turb_via || m_accept_old_turb_cdi)) {
        initial_conditions[k].value = 0;
        initial_conditions[k].name = NULL;
      }
      //else if (var_id == CDI_VAR_ID_WATER_VAPOR_SOURCE_TERM) {
      //  initial_conditions[k].value = -1;       
      //  initial_conditions[k].name = NULL;
      //}
      else if (var_id == CDI_VAR_ID_WATER_MASS_FRACTION) {
        initial_conditions[k].value = -1;       
        initial_conditions[k].name = NULL;
      }
      else if (var_id == CDI_VAR_ID_RELATIVE_HUMIDITY) {
        initial_conditions[k].value = -1;       
        initial_conditions[k].name = NULL;
      }
      else {
        msg_internal_error("Missing initial condition of type %d from IVAL sub-chunk of PHYS chunk \"%s\".",
            var_id, physics_desc_name);
      }
    }
  }

  //UDS
  if (found_uds_physics_type)  {
    ccDOTIMES(jj, n_uds_parameters) {
      if (!uds_parameters_found[jj]) {
	asINT32 index = jj % uds_phys_type_desc->n_parameters;
	asINT32 nth_uds = (jj - index) / uds_phys_type_desc->n_parameters;
	asINT32 var_id = uds_phys_type_desc->parameter_var[index]->id;
	if (var_id == CDI_VAR_ID_UDS_BC_INLET_CONTENT_VIA) {
	  uds_parameters[jj].value = -1;
	  uds_parameters[jj].name = NULL;
	} else if (var_id == CDI_VAR_ID_UDS_BC_INLET_WALL_VALUE) {
	  uds_parameters[jj].value = (dFLOAT)(-SFLOAT_MAX * 0.1);  //Will compare with SURFACE_Q_LOWER_BOUND ((sdFLOAT)(-SFLOAT_MAX * 0.01)) in sp to tell this is an outlet
	  uds_parameters[jj].name = NULL;
	} else if (var_id == CDI_VAR_ID_UDS_BC_INLET_NO_SCALAR_DIFFUSION) {
	  uds_parameters[jj].value = 1;
	  uds_parameters[jj].name = NULL;
	} else if (var_id == CDI_VAR_ID_UDS_BC_INLET_MEAN_VALUE) {
	  uds_parameters[jj].value = 0;
	  uds_parameters[jj].name = NULL;
	} else if (var_id == CDI_VAR_ID_UDS_BC_OUTLET_REVERSE_FLOW_COMPOSITION) {
	  uds_parameters[jj].value = 0;
	  uds_parameters[jj].name = NULL;
	} else if (var_id == CDI_VAR_ID_UDS_BC_WALL_SCALAR_FLUX) { //wall scalar_value_bc
	  uds_parameters[jj].value = 0;
	  uds_parameters[jj].name = NULL;
	} else { //just for debug
	  msg_internal_error("Missing parameter with index (%d) and nth_uds (%d) in uds physics type (%d) of PHYS chunk \"%s\".", index, nth_uds, uds_phys_type_desc->uds_physics_type,  physics_desc_name);
	}	  
      }		
    }

    ccDOTIMES(kk, n_uds_initial_conditions) {
      asINT32 index = kk % uds_phys_type_desc->n_initial_conditions;
      asINT32 nth_uds = (kk - index) / uds_phys_type_desc->n_initial_conditions;
      //asINT32 var_id = uds_phys_type_desc->initial_condition_var[index]->id;

      if (!uds_initial_conditions_found[kk]) {
	msg_internal_error("Missing initial condition with index (%d) and nth_uds (%d) in uds physics type (%d) of PHYS chunk \"%s\".", index, nth_uds, uds_phys_type_desc->uds_physics_type, physics_desc_name);
      }
    } 
  }

  ccDOTIMES(i, phys_type_desc->n_continuous_dp) {
    LGI_PHYSICS_VARIABLE var;
    var.value = parameters[i].value;
    var.name_length = parameters[i].name == NULL ? 0 : strlen(parameters[i].name) + 1;

    m_pds.write(&var, sizeof(var));

    if (parameters[i].name != NULL) {
      m_pds.write((VOID *)parameters[i].name, var.name_length);

    }
  }

  ccDOTIMES(n, phys_type_desc->n_initial_conditions) {
    LGI_PHYSICS_VARIABLE var;
    var.value = initial_conditions[n].value;
    var.name_length = initial_conditions[n].name == NULL ? 0 : strlen(initial_conditions[n].name) + 1;

    m_pds.write(&var, sizeof(var));     

    if (initial_conditions[n].name != NULL) {
      m_pds.write((VOID *)initial_conditions[n].name, var.name_length);
    }
  }

  //UDS
  if (found_uds_physics_type)  {    
    ccDOTIMES(ii, n_uds_parameters) {
      LGI_PHYSICS_VARIABLE var;
      var.value = uds_parameters[ii].value;
      var.name_length = uds_parameters[ii].name == NULL ? 0 : strlen(uds_parameters[ii].name) + 1;
      
      m_pds.write(&var, sizeof(var));
      
      if (uds_parameters[ii].name != NULL) {
	m_pds.write((VOID *)uds_parameters[ii].name, var.name_length);      
      }
    }

    ccDOTIMES(nn, n_uds_initial_conditions) {
      LGI_PHYSICS_VARIABLE var;
      var.value = uds_initial_conditions[nn].value;
      var.name_length = uds_initial_conditions[nn].name == NULL ? 0 : strlen(uds_initial_conditions[nn].name) + 1;
      
      m_pds.write(&var, sizeof(var));     
      
      if (uds_initial_conditions[nn].name != NULL) {
	m_pds.write((VOID *)uds_initial_conditions[nn].name, var.name_length);
      }
    }
  }

  if (is_flow_surface_p && phys_desc_index < cp_info.cdi_phys_type_from_flow_surface_physics_desc_index.size()) {
    cp_info.cdi_phys_type_from_flow_surface_physics_desc_index[phys_desc_index] = physics_desc.phys_type_desc_index;
  }

  delete cdi_uds_vars;
  if (found_uds_physics_type) {
    delete[] uds_parameters;
    delete[] uds_parameters_found;
    delete[] uds_initial_conditions;
    delete[] uds_initial_conditions_found;
  }

  return physics_desc.phys_type_desc_index;
}
//----------------------------------------------------------------------------
// read_shell_phys_chunk
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_shell_layer_chunk(sVARIABLE *parameter, sVARIABLE *initial_condition, cSTRING &phys_desc_name)
{
  asINT32 n_inner_chunks = cio_get_count(m_cdi_info->cio_info);

  cBOOLEAN  parameter_found[CDI_MAX_CONTINUOUS_DP];

  cBOOLEAN  initial_condition_found[CDI_MAX_INITIAL_CONDITIONS];

  BOOLEAN found_some_eqn = FALSE;

  CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc;
  CDI_PTYP ptyp;
  cSTRING layer_name = read_name();
  BOOLEAN is_old_tfloat_type      = FALSE;
  BOOLEAN is_old_turb_type        = FALSE;
  BOOLEAN found_turb_via          = FALSE;


  //  BOOLEAN is_stagnation_pressure_bc = FALSE;
  CDI_WITH_INNER_CHUNK(m_cdi_info) {
    if (cdi_get_type(m_cdi_info) != CDI_CHUNK_TYPE_PTYP) {
      msg_internal_error("Expected PTYP as second sub-chunk of CDI PHYS chunk.");
    } else {
      ptyp = cdi_read_ptyp(m_cdi_info);
      if(ptyp->type != CDI_PHYS_TYPE_CONDUCTION_LAYER) {
        msg_internal_error("Expected a physics type of CONDUCTION_LAYER for shell configuration %s", phys_desc_name);
      }
//  Hacking it for now till the ptyp was set in CDI in powercase
      ptyp->type = CDI_PHYS_TYPE_CONDUCTION_LAYER;
      phys_type_desc = cdi_lookup_physics(ptyp->type);
      // Moving the pointer in the parameters array to store the subsequent layer parameters and
      // initial conditions at the correct location in the array.
      ccDOTIMES(i, phys_type_desc->n_continuous_dp) {
        parameter_found[i] = FALSE;
      }
      ccDOTIMES(j, phys_type_desc->n_initial_conditions) {
        initial_condition_found[j] = FALSE;
      }
    }
  }

  BOOLEAN data_table_found = FALSE;
  ccDOTIMES(m, n_inner_chunks - 2) {
    CDI_WITH_INNER_CHUNK(m_cdi_info) {
      if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQNS) {
        //        if (data_table_found)
          msg_internal_error("Equations not supported yet for shell configurations.");
          //        data_table_found = TRUE;

          //        if (phys_descriptor_written)
          //          msg_internal_error("EQNS chunk of CDI PHYS chunk must appear before DPRM or IVAL chunks.");
          //
          //        asINT32 data_table_length = cdi_eqns_read_length(m_cdi_info);
          //        if (data_table_length < 0)
          //          msg_internal_error("Failure reading PHYS chunk data table length from CDI file.");
          //
          //        physics_desc.data_table_length = data_table_length;
          //
          //        m_pds.write(&physics_desc, sizeof(physics_desc));
          //        m_pds.write((VOID *)physics_desc_name, physics_desc.name_length);
          //        phys_descriptor_written = TRUE;
          //
          //        const int BUFSIZE = 1024;
          //        char buf[BUFSIZE];
          //        asINT32 n_left = data_table_length;
          //
          //        while (n_left > 0) {
          //          asINT32 n = MIN(n_left, BUFSIZE);
          //          if (cdi_eqns_read_chars(m_cdi_info, buf, n) != n)
          //            msg_internal_error("Failure reading PHYS chunk data table contents from CDI file.");
          //          m_pds.write((VOID *)buf, n);
          //          n_left -= n;
          //        }
      }
      else {
        if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_DPRM) {
          asINT32 n_parameter_chunks = cio_get_count(m_cdi_info->cio_info);
          asINT32 n_good_parameters_read = 0;

          if (n_parameter_chunks != ptyp->n_continuous)
            msg_warn("3 Parameter count (%d) in PTYP sub-chunk of PHYS chunk \"%s\" should be %d.",
		     ptyp->n_continuous, phys_desc_name, n_parameter_chunks);


          ccDOTIMES(i, n_parameter_chunks) {
            CDI_WITH_INNER_CHUNK(m_cdi_info) {
              if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_CVDP) {
                CDI_CVDP cvdp = cdi_read_cvdp(m_cdi_info);
                if (cvdp == NULL)
                  msg_internal_error("Missing CVDP chunk in DPRM chunk of CDI file.");
                else {
                  asINT32 index = phys_type_desc->continuous_dp_index(cvdp->type);

                  if (index < 0) {
                      msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".",
                          cvdp->type, phys_desc_name);
                  }
                  else {

                    parameter[index].value = cvdp->value;
                    parameter[index].name = NULL;
                    parameter_found[index] = TRUE;

                    n_good_parameters_read++;
                  }
                }
              }
              else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQDP) {
                CDI_EQDP eqdp = cdi_read_eqdp(m_cdi_info);
                if (eqdp == NULL)
                  msg_internal_error("Missing EQDP chunk in DPRM chunk of CDI file.");
                else {
                  sINT32 type = eqdp->type;
                  asINT32 index = phys_type_desc->continuous_dp_index(type);
                  found_some_eqn = TRUE;
                  if (index < 0) {
                    msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                             eqdp->type, phys_desc_name);
                  } else {
                    parameter[index].value = 0;
                    parameter[index].name = eqdp->var_name;
                    parameter_found[index] = TRUE;
                    n_good_parameters_read++;
                  }
                }
              }
              else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_BSDP) {
                msg_internal_error("Boundary seeding not supported yet for shell configurations.");
              }
            }
            }
        }else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_IVAL) {
                asINT32 n_ic_chunks = cio_get_count(m_cdi_info->cio_info);
            asINT32 n_good_ics_read = 0;

            if (n_ic_chunks != ptyp->n_initial)
              msg_warn("Initial condition count (%d) in PTYP sub-chunk of PHYS chunk \"%s\" should be %d.",
                  ptyp->n_initial, phys_desc_name, n_ic_chunks);

            ccDOTIMES(i, n_ic_chunks) {
              CDI_WITH_INNER_CHUNK(m_cdi_info) {
                if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_CVDP) {
                  CDI_CVDP cvdp = cdi_read_cvdp(m_cdi_info);
                  if (cvdp == NULL)
                    msg_internal_error("Missing CVDP chunk in IVAL chunk of CDI file.");
                  else {
                    asINT32 index = phys_type_desc->initial_condition_index(cvdp->type);
                    if (index < 0) {
                        msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", cvdp->type, phys_desc_name);
                    }
                    else {
                      initial_condition[index].value = cvdp->value;
                      initial_condition[index].name = NULL;
                      initial_condition_found[index] = TRUE;
                      n_good_ics_read++;
                    }
                  }
                }
                else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_EQDP) {
                  CDI_EQDP eqdp = cdi_read_eqdp(m_cdi_info);
                  if (eqdp == NULL)
                    msg_internal_error("Missing EQDP chunk in IVAL chunk of CDI file.");
                  else {
                    sINT32 type = eqdp->type;
                    asINT32 index = phys_type_desc->initial_condition_index(type);
                    found_some_eqn = TRUE;
                    if (index < 0) {
                      msg_warn("Unrecognized parameter of type %d in CDI physics descriptor \"%s\".", 
                               eqdp->type, phys_desc_name);
                    } else {
                      initial_condition[index].value = 0;
                      initial_condition[index].name = eqdp->var_name;
                      initial_condition_found[index] = TRUE;
                      n_good_ics_read++;
                    }
                  }
                }
                else {
                  msg_warn("In CDI file, unexpected sub-chunk of IVAL chunk (see PHYS chunk \"%s\")", phys_desc_name);
                }
              }
            }
          }
        else {
          msg_warn("In CDI file, unexpected sub-chunk of PHYS chunk \"%s\"", phys_desc_name);
        }
      }
    }
  }



  ccDOTIMES(j, phys_type_desc->n_continuous_dp) {
    if (!parameter_found[j]) {
      asINT32 var_id = phys_type_desc->continuous_dp_var[j]->id;

#define SPVB(x)  { parameter[j].value = (x); parameter[j].name = NULL; issue_error = FALSE; break; }
#define SPV(x)  { parameter[j].value = (x); parameter[j].name = NULL; issue_error = FALSE; }

      BOOLEAN issue_error = TRUE;
      switch(var_id) {
      case CDI_VAR_ID_COORD_SYSTEM:
        SPVB(-1);       // Global coord system
      case CDI_VAR_ID_SOLID_MATERIAL:
        SPVB(-1);
      case CDI_VAR_ID_SHELL_LAYER_THICKNESS:
        SPVB(0);
      case CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT:
        SPVB(-1);
      case CDI_VAR_ID_SPECIFY_HEAT_VIA_POWER_DENSITY:
        SPVB(1);
      case CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT_IN_POWER:
        SPVB(-1);
      case CDI_VAR_ID_ANISOTROPY_USE_PART_AXIS:
        SPVB(0);
      case CDI_VAR_ID_MIN_DESIGN_TEMP:
      case CDI_VAR_ID_MAX_DESIGN_TEMP:
        SPVB(0);
      case CDI_VAR_ID_GAXIS_X:
      case CDI_VAR_ID_GAXIS_Y:
      case CDI_VAR_ID_GAXIS_Z:
        SPVB(0);
      }

      if (issue_error)
        msg_internal_error("Expected CDI_VAR_ID parameter of type %d (look in CDI/cdi_readwrite.h) for physics descriptor \"%s\" was not provided in the CDI file, and no default value was found. "
                           "Most likely the physics descriptor in CDI was modified and a default value needs to be provided in CP/%s(). "
                           "You might need to modify the associated PARAMETERS class in SIMENG to match as well.", var_id, phys_desc_name, __FUNCTION__);
#undef SPVB
#undef SPV
    }
    else {
      if (m_cdi_info->major_version <= 3 && m_cdi_info->minor_version <= 5 &&
          (CDI_VAR_ID_POROUS_Q0 == phys_type_desc->continuous_dp_var[j]->id)) {
        parameter[j].value *= 1.0667; // scale old constant heat input values by 6.67%
      }
    }
  }

  ccDOTIMES(k, phys_type_desc->n_initial_conditions) {
    asINT32 var_id = phys_type_desc->initial_condition_var[k]->id;

    if (!initial_condition_found[k]) {

      if (is_old_tfloat_type && (var_id == CDI_VAR_ID_TEMP)) {
        initial_condition[k].value = -1;       // Floating temmperature
        initial_condition[k].name = NULL;
      }
      else {
        msg_internal_error("Missing initial condition of type %d from IVAL sub-chunk of PHYS chunk \"%s\".",
            var_id, phys_desc_name);
      }
    }
  }
}

// Write the array of parameters and initial conditions in all the layers
// of one shell configuration.
VOID sCP_CDI_READER::write_shell_config_params_and_initial_condition (LGI_PHYSICS_DESCRIPTOR physics_desc, sVARIABLE parameters[], sVARIABLE initial_conditions[])
{
  ccDOTIMES(i, physics_desc.n_parameters) {
    LGI_PHYSICS_VARIABLE var;
    var.value = parameters[i].value;
    var.name_length = parameters[i].name == NULL ? 0 : strlen(parameters[i].name) + 1;

    m_pds.write(&var, sizeof(var));

    if (parameters[i].name != NULL) {
      m_pds.write((VOID *)parameters[i].name, var.name_length);

    }
  }

  ccDOTIMES(n, physics_desc.n_initial_conditions) {
    LGI_PHYSICS_VARIABLE var;
    var.value = initial_conditions[n].value;
    var.name_length = initial_conditions[n].name == NULL ? 0 : strlen(initial_conditions[n].name) + 1;

    m_pds.write(&var, sizeof(var));

    if (initial_conditions[n].name != NULL) {
      m_pds.write((VOID *)initial_conditions[n].name, var.name_length);
    }
  }
}

// convert_physics_chunk_vars
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::convert_physics_chunk_vars(asINT32 cdi_physics_type, sVARIABLE *parameters, 
                                                sVARIABLE *initial_conditions,BOOLEAN found_some_eqn)
{
  BOOLEAN barf_on_eqn = FALSE;

  switch(cdi_physics_type) {
  case CDI_PHYS_TYPE_FV_FLUID:
  case CDI_PHYS_TYPE_VV_FLUID:
  case CDI_PHYS_TYPE_LES_VV_FLUID: 
  {
    barf_on_eqn = found_some_eqn;

    // Convert momentum to velocity
    initial_conditions[1].value /= initial_conditions[0].value;
    initial_conditions[2].value /= initial_conditions[0].value;
    initial_conditions[3].value /= initial_conditions[0].value;

    dFLOAT vmag_sqrd = (initial_conditions[1].value * initial_conditions[1].value
        + initial_conditions[2].value * initial_conditions[2].value
        + initial_conditions[3].value * initial_conditions[3].value);

    // Convert energy to temperature
    initial_conditions[4].value = ((initial_conditions[4].value - 0.5 * initial_conditions[0].value * vmag_sqrd) 
        / (2.0 * initial_conditions[0].value));

    // Convert density to pressure
    initial_conditions[0].value = initial_conditions[0].value * initial_conditions[4].value / DENSITY_CONVERSION_FACTOR;

    break;
  }
  case CDI_PHYS_TYPE_SOURCE: 
  {
    barf_on_eqn = found_some_eqn;

    // Convert momentum to velocity
    parameters[1].value /= parameters[0].value;
    parameters[2].value /= parameters[0].value;
    parameters[3].value /= parameters[0].value;

    dFLOAT vmag_sqrd = (parameters[1].value * parameters[1].value
        + parameters[2].value * parameters[2].value
        + parameters[3].value * parameters[3].value);

    // Convert energy to temperature
    parameters[4].value = ((parameters[4].value - 0.5 * parameters[0].value * vmag_sqrd) 
        / (2.0 * parameters[0].value));

    // Convert density to pressure
    parameters[0].value = parameters[0].value *  DENSITY_CONVERSION_FACTOR * parameters[4].value;

    break;
  }
  case CDI_PHYS_TYPE_MASS_FLUX:
    barf_on_eqn = found_some_eqn;
    parameters[0].value /=  DENSITY_CONVERSION_FACTOR;
    parameters[1].value /=  DENSITY_CONVERSION_FACTOR;
    parameters[2].value /=  DENSITY_CONVERSION_FACTOR;
    break;

  case CDI_PHYS_TYPE_STATIC_PRESSURE_FIXED_DIR:
  case CDI_PHYS_TYPE_STATIC_PRESSURE_FREE_DIR:
  case CDI_PHYS_TYPE_STAG_PRESSURE_FIXED_DIR:
  case CDI_PHYS_TYPE_STAG_PRESSURE_FREE_DIR:
    barf_on_eqn = found_some_eqn;
    parameters[0].value /=  DENSITY_CONVERSION_FACTOR;
    break;

  default:
    break;
  }

  if (barf_on_eqn) {
    msg_error("The CDI file for this case includes an old physics descriptor of"
        " CDI physics type %d with equations. Using equations with this physics"
        " type is no longer supported.",
        cdi_physics_type);
  }

}

//----------------------------------------------------------------------------
// convert_phys_type if necessary
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::convert_phys_type(CDI_PTYP ptyp, BOOLEAN *is_old_tfloat, BOOLEAN *is_old_turb)
{
  *is_old_tfloat = FALSE;
  *is_old_turb = FALSE;

  switch (ptyp->type) {
  case CDI_PHYS_TYPE_FV_FLUID:
  case CDI_PHYS_TYPE_VV_FLUID:
    ptyp->type = CDI_PHYS_TYPE_NEW_VV_FLUID;
    break;
  case CDI_PHYS_TYPE_LES_VV_FLUID:
    ptyp->type = CDI_PHYS_TYPE_NEW_LES_VV_FLUID;
    break;
  case CDI_PHYS_TYPE_SOURCE:
    ptyp->type = CDI_PHYS_TYPE_NEW_SOURCE;
    break;
  case CDI_PHYS_TYPE_MASS_FLUX:
    ptyp->type = CDI_PHYS_TYPE_NEW_MASS_FLUX;
    break;
  case CDI_PHYS_TYPE_STATIC_PRESSURE_FIXED_DIR:
    ptyp->type = CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR;
    break;
  case CDI_PHYS_TYPE_STATIC_PRESSURE_FREE_DIR:
    ptyp->type = CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR;
    break;
  case CDI_PHYS_TYPE_STAG_PRESSURE_FIXED_DIR:
    ptyp->type = CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR;
    break;
  case CDI_PHYS_TYPE_STAG_PRESSURE_FREE_DIR:
    ptyp->type = CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR;
    break;
  }

#define PTYP_TFLOAT_CASE(x)     case x ## _TFLOAT: ptyp->type = x; *is_old_tfloat = TRUE; break

  // drop TFLOAT
  switch (ptyp->type) {
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_NEW_MASS_FLUX);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_MASS_FLOW);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_SOURCE_SURFEL);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_FIXED_VEL);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_LES_SOURCE);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_LES_MASS_FLUX);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_LES_MASS_FLOW);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_LES_FIXED_VEL);
  PTYP_TFLOAT_CASE(CDI_PHYS_TYPE_LES_SOURCE_SURFEL);

  default:
    break;
  }

#define PTYPE_LES_CASE(x, y)    case x: ptyp->type = y; *is_old_turb = TRUE; break

  if (cp_info.is_turb) {
    switch(ptyp->type) {
    PTYPE_LES_CASE(CDI_PHYS_TYPE_NEW_VV_FLUID, CDI_PHYS_TYPE_NEW_LES_VV_FLUID);
    PTYPE_LES_CASE(CDI_PHYS_TYPE_NEW_SOURCE, CDI_PHYS_TYPE_LES_SOURCE);                       
    PTYPE_LES_CASE(CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR, CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR); 
    PTYPE_LES_CASE(CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR, CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR);   
    PTYPE_LES_CASE(CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR, CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR);     
    PTYPE_LES_CASE(CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR, CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR);       
    PTYPE_LES_CASE(CDI_PHYS_TYPE_NEW_MASS_FLUX, CDI_PHYS_TYPE_LES_MASS_FLUX);         

    PTYPE_LES_CASE(CDI_PHYS_TYPE_FIXED_VEL, CDI_PHYS_TYPE_LES_FIXED_VEL);                     
    PTYPE_LES_CASE(CDI_PHYS_TYPE_SOURCE_SURFEL, CDI_PHYS_TYPE_LES_SOURCE_SURFEL);                     
    PTYPE_LES_CASE(CDI_PHYS_TYPE_POROUS_VV_FLUID, CDI_PHYS_TYPE_POROUS_LES_VV_FLUID);         
    PTYPE_LES_CASE(CDI_PHYS_TYPE_POROUS_VV_FLUID_FIXED_TEMP, CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_FIXED_TEMP);    
    PTYPE_LES_CASE(CDI_PHYS_TYPE_POROUS_VV_FLUID_THERMAL, CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_THERMAL);       
    PTYPE_LES_CASE(CDI_PHYS_TYPE_INLINE_FAN, CDI_PHYS_TYPE_LES_INLINE_FAN);           
    default:
      break;
    }
  }

#undef PTYPE_LES_CASE
#undef PTYP_TFLOAT_CASE
}

//----------------------------------------------------------------------------
// complain_about_unrecognized_physics_parameter
//----------------------------------------------------------------------------
BOOLEAN sCP_CDI_READER::complain_about_unrecognized_physics_parameter(asINT32 physics_desc_type, asINT32 param_type)
{
  // Old CDI files will have the shell config variable so it will be left in the wall surface physics
  // descriptors defined in cdi_physics.cc.
#if 0
  if (param_type == CDI_VAR_ID_SHELL_CONFIGURATION)
    // Before the dawn of separate tables in CDI files for flow and thermal BCs, the shell config variable
    // appeared in all wall types (SLIP95, TRUE_NOSLIP, etc.). Ideally we would check that physics_desc_type
    // was one of those types here, but given the list of types is long, better to simply not check.
    return FALSE;
#endif

  // We removed the WALL_HEAT_FLUX parameter from the FIXED_HEAT_FLUX physics descriptor
  if (param_type == CDI_VAR_ID_WALL_HEAT_FLUX
      && physics_desc_type == CDI_PHYS_TYPE_FIXED_HEAT_FLUX)
    return FALSE;

  // We removed the REF_FRAME parameter from all the thermal physics descriptors
  if (param_type == CDI_VAR_ID_REF_FRAME) {
    switch(physics_desc_type) {
    case CDI_PHYS_TYPE_ADIABATIC:
    case CDI_PHYS_TYPE_FIXED_TEMP:
    case CDI_PHYS_TYPE_FIXED_HEAT_FLUX:
    case CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP:
    case CDI_PHYS_TYPE_COUPLED_THERMAL:
      return FALSE;
    default:
      break;
    }
  }   

  if ((physics_desc_type == CDI_PHYS_TYPE_INLINE_FAN)
      || (physics_desc_type == CDI_PHYS_TYPE_LES_INLINE_FAN)) {
    // Fan origin, raxis, and gaxis are now derived from csys (if present)
    switch (param_type) {
    case CDI_VAR_ID_POINT_X:    // origin
    case CDI_VAR_ID_POINT_Y:
    case CDI_VAR_ID_POINT_Z:
    case CDI_VAR_ID_RAXIS_X:    // raxis
    case CDI_VAR_ID_RAXIS_Y:
    case CDI_VAR_ID_RAXIS_Z:
    case CDI_VAR_ID_GAXIS_X:    // gaxis
    case CDI_VAR_ID_GAXIS_Y:
    case CDI_VAR_ID_GAXIS_Z:
      return FALSE;
    }
  }

  if ((param_type == CDI_VAR_ID_HYDRAULIC_DIAMETER) 
      && ((physics_desc_type == CDI_PHYS_TYPE_NEW_LES_VV_FLUID)
          || (physics_desc_type == CDI_PHYS_TYPE_LES_VV_FLUID))
          && cp_info.is_turb 
          && (m_cdi_info->major_version < 2))
    return FALSE;

  return TRUE;
}


asINT32 sCP_CDI_READER::read_accretion_chunk()
{
  BOOLEAN ok = TRUE;
  if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_ACCR) {
    cp_info.is_accretion_simulation = true;
    cp_particle_sim.m_ice_accretion_parameters = xnew sCDI_ACCR;
    cp_particle_sim.m_ice_accretion_parameters->cdi_read(m_cdi_info);
    
    
    //Forward some of the parameters to the SPs.
    asINT32 n_faces = cp_particle_sim.m_ice_accretion_parameters->m_surface_ids.size();
    LGI_ICE_ACCRETION_HEADER header;
    header.tag.id = LGI_ICE_ACCRETION_TAG;
    header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_ICE_ACCRETION_HEADER) 
                                                         + sizeof(sLGI_ICE_ACCRETION_REC) + n_faces * sizeof(int));
    header.num_surface_ids = n_faces;
    write_header_to_all_sps(header);
    
    sLGI_ICE_ACCRETION_REC record;
    record.accretion_material_surface_density = cp_particle_sim.m_ice_accretion_parameters->m_accretion_material_density.value;
    record.accretion_acceleration_factor = cp_particle_sim.m_ice_accretion_parameters->m_accretion_acceleration_factor.value;
    record.accretion_material_index = cp_particle_sim.m_ice_accretion_parameters->m_material_index;
    write_to_all_sps(record);
    ccDOTIMES(surface_index, cp_particle_sim.m_ice_accretion_parameters->m_surface_ids.size() ) {
      int surface_id = cp_particle_sim.m_ice_accretion_parameters->m_surface_ids[surface_index];
      write_to_all_sps(&surface_id, sizeof(int));
    }
    
      //asINT32 accretion_window_index = cp_particle_sim.m_ice_accretion_parameters->m_meas_window_index;
      //const char* accretion_window_name = cp_info.cdi_meas_windows[accretion_window_index].name;
      //msg_print("Storing accretion results in window \"%s\".", accretion_window_name);
      
  }
  return !ok;
}

VOID sCP_CDI_READER::set_thermal_particle_solver_status() {
  //Remove once CDI support available.
  cp_info.is_thermal_particle_solver = FALSE;
  if(g_use_particle_evaporation_model)
    cp_info.is_thermal_particle_solver = TRUE;
}

VOID sCP_CDI_READER::set_film_solver_status() {
  //Decide wether to enable the film solver.  If there are liquid
  //materials defined that aren't being used for ice accretion,
  //then the film solver is needed.
  
  //This can't be called until after the accretion chunk and particle
  //material chunks have been read.

  ccDOTIMES(material_index, cp_particle_sim.cdi_particle_materials()->size()) {
    sCDI_PRMT prmt = cp_particle_sim.cdi_particle_materials()->at(material_index);
    BOOLEAN material_is_liquid = ((int)prmt.type.value) == PARTICLE_MATERIAL_LIQUID;
    BOOLEAN material_is_not_for_accretion = true;
    if(cp_info.is_accretion_simulation) {
      if(material_index == cp_particle_sim.m_ice_accretion_parameters->m_material_index)
        material_is_not_for_accretion = false;
    }
    BOOLEAN material_is_for_film_solver =  
      material_is_liquid  && 
      (material_is_not_for_accretion || g_do_accretion_filtering_with_film_stencil);
    cp_info.is_film_solver |= material_is_for_film_solver; //Enable the film sovler depending on if any particle materials require it.
  }
}

VOID sCP_CDI_READER::enable_particle_solver_features() {
  set_film_solver_status();
  set_thermal_particle_solver_status();
}


//----------------------------------------------------------------------------
// read particle tracking chunk
//----------------------------------------------------------------------------
asINT32 sCP_CDI_READER::read_particle_tracking_chunk()
{
  BOOLEAN ok = TRUE;
  if(m_is_trac_present) {
    enter_cdi_chunk(CDI_CHUNK_TYPE_TRAC);
    ccCDI_DO_INNER_CHUNKS(j, "trac", m_cdi_info) {
      CIO_CCCC chunk_type = cdi_get_type(m_cdi_info);
      CIO_CCCC wipr_type = CDI_CHUNK_TYPE_WIPR;
      switch(chunk_type) {
      case CDI_CHUNK_TYPE_PGLB:
        read_particle_globals();
        break;
      case CDI_CHUNK_TYPE_PRMS:
        read_particle_materials();
        break;
      case CDI_CHUNK_TYPE_ECGS:
        read_particle_emitter_configurations();
        break;
      case CDI_CHUNK_TYPE_PEMC:
        read_particle_emitters();
        break;
      case CDI_CHUNK_TYPE_SRMS:  //surface material
        read_particle_surface_properties();
        break;
      case CDI_CHUNK_TYPE_SCRS:
        read_particle_screens();
        break;
      case CDI_CHUNK_TYPE_WIPS:
        read_simple_wiper_models();
        break;
   case CDI_CHUNK_TYPE_ACCR:
        read_accretion_chunk();
        break;
      default:
        msg_error("unrecognized particle tracking chunk.\n");fflush(stdout);
      }
    }
    exit_cdi_chunk();
    enable_particle_solver_features();
  } 
  return !ok;
}

//Store the face IDs in the following vector when surface emitter records are read from the CDI file.  They will 
//be used later when parsing the relevant geometry information from the CDI file.
std::vector<int> g_surf_emitter_geom_ids;

typedef struct sCDI_SURFACE_EMITTER_FACE_GEOMETRY{
  std::string name;
  asINT32 face_index;       //there should only be one
  //sCDI_CVDP csys;         //always lattice
  //asINT32 num_facets;
  std::list< std::vector<asINT32> > facets;  // list of facets
}*CDI_SURFACE_EMITTER_FACE_GEOMETRY;



//This function reads the relevant CDI data needed to obtain surface emitter geometry.
asINT32 sCP_CDI_READER::read_geometry_data_for_emitters()
{
  BOOLEAN ok = TRUE;
  asINT32  n_cdi_regions;

  //Count the number of regions defined in the CDI file and store the names for later use.
  //The names are needed to resolve the names of sCDI_BOX_ objects that only contain a region index and not an 
  //explicit name.
  asINT32 region_index = 0;
  enter_cdi_chunk(CDI_CHUNK_TYPE_RGDF);
  ccCDI_DO_INNER_CHUNKS(i, "rgdf", m_cdi_info) {
    if(cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_RGCT) {
      sCDI_RGCT rgct;
      cdi_read_case_region_count(m_cdi_info, &rgct);
      n_cdi_regions = rgct.n_regions;
      g_cdi_region_names.resize(n_cdi_regions);
    } else if(cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_RGND) {
      sCDI_RGND rgnd;
      cdi_read_rgnd(m_cdi_info, &rgnd);
      g_cdi_region_names[region_index] = rgnd.name;
      region_index++;
    }
  }
  exit_cdi_chunk();

  //If this case has surface emitters, also read the geometry data for the faces they use.
  if(!m_is_vrtx_present)
    return(!ok);
  
  if(g_surf_emitter_geom_ids.size() == 0)
    return(!ok);

  std::vector<sCDI_SURFACE_EMITTER_FACE_GEOMETRY> surface_emitter_faces;
 
  //Store the CDI's vrtx chunk's data
  enter_cdi_chunk(CDI_CHUNK_TYPE_VRTX);
  CDI_VRTX cdi_vertices = cdi_read_vrtx(m_cdi_info);
  exit_cdi_chunk();

  //----Compile a vector containing the geometry for each face referenced by any surface emitters----

  //First count the number of distinct faces and create a map from the face id referenced by the surface emitter 
  //to an index into the surface_emitter_faces vector.
  asINT32 num_unique_faces = 0;
  std::map<asINT32, asINT32> face_index_position; //Map a face index to an array index.
  ccDOTIMES(i, g_surf_emitter_geom_ids.size()) { //For each face referenced by the surface emitter,
    cdiINT32 index = g_surf_emitter_geom_ids[i];  //add the face index to the map if it isn't already included.
    if(face_index_position.find(index) == face_index_position.end()) {
      face_index_position[index] = num_unique_faces;
      ++num_unique_faces;
    }
  }

  //Allocate space for the vector of face gemoetries.
  surface_emitter_faces.resize(num_unique_faces);

   //And then read the region chunks from the CDI stream and store the relevant geometry data into surface_emitter_faces.
  //Note: A regn chunk contains the following:
  //  ftab
  //    face
  //  body
  //    shll
  //      fact
  ccDOTIMES(region, n_cdi_regions) {
    cdiINT32 index = -1;  //face index in the regn chunk
    enter_cdi_chunk(CDI_CHUNK_TYPE_REGN);
    asINT32 face_count = 0;  //Count the number of face chunks used for surface emitters in a regn chunk.
    ccCDI_DO_INNER_CHUNKS(j, "regn", m_cdi_info) {
      if(cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_FTAB) {
        ccCDI_DO_INNER_CHUNKS(i, "ftab", m_cdi_info) {
          if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_FACE) {
            if (cdi_get_major_version(m_cdi_info) < 3)
              msg_error("Surface emitter is not supported in version < 3.");
            sCDI_FACE face;
            cdi_read_face(m_cdi_info, &face);
            index = face.index;
            
            if(face_index_position.find(index) != face_index_position.end()) {
              asINT32 pos = face_index_position[index];
              surface_emitter_faces[pos].face_index = index;
              surface_emitter_faces[pos].name = face.name;
              ++face_count;
            }
            cdi_empty_face(&face);
          } //if face
        } //chunk ftab
      } //if ftab
      else if(cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_BODY) {
        ccCDI_DO_INNER_CHUNKS(k, "body", m_cdi_info) {
          if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_SHLL) {
            ccCDI_DO_INNER_CHUNKS(l, "shll", m_cdi_info) {
              if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_FACT && face_count >= 1) {
                sCDI_FACT_HEADER facet_header;
                cdi_read_facet_header(m_cdi_info, &facet_header);

                ccDOTIMES(cdi_facet_index, facet_header.n_facets) {
                  sCDI_FACT cdi_facet;
                  cdi_read_facet(m_cdi_info, &cdi_facet);
                  asINT32 num_vertices_in_facet = cdi_facet.n_vertices;
                  std::map<asINT32, asINT32> :: iterator it;
                  if(cdi_facet.front_face >= 0)
                  {
                    it = face_index_position.find(cdi_facet.front_face);

                    if(it != face_index_position.end())
		    {
                      asINT32 pos = it->second;
                      surface_emitter_faces[pos].facets.push_back(std::vector<asINT32>(cdi_facet.vertices, cdi_facet.vertices + num_vertices_in_facet));
		    }
                  }
                  if(cdi_facet.back_face >= 0 && cdi_facet.back_face != cdi_facet.front_face)
                  {
                    it = face_index_position.find(cdi_facet.back_face);

                    if(it != face_index_position.end())
		    {
                      asINT32 pos = it->second;
                      surface_emitter_faces[pos].facets.push_back(std::vector<asINT32>(cdi_facet.vertices, cdi_facet.vertices + num_vertices_in_facet));
                    }
                  }
                } //do n_facets
              } //if fact
            } //chunk shll
          } //if shll
        } //chunk body
      } //if body
      
    } //chunk regn
    
    exit_cdi_chunk();  //exit one regn
  } //regn
  
  //------Now write the data for relevant faces to the LGI stream------.
  
  //The LGI format used is:
  //  header
  //  header of face 1
  //  name of face 1
  //  # vertices   vertex 1   vertex 2 ...  vertex n
  //  ....
  //  header of face 2
  //  name of face 2
  //  # vertices   vertex 1   vertex 2 ...  vertex n
  //  ....
  //  ....
  //  # header of face m
  //  name of face m
  //  # vertices   vertex 1   vertex 2 ...  vertex n
  //  ....

  asINT32 max_n_vertices = 0;
  uINT64 length = 0;

  ccDOTIMES(face_index, surface_emitter_faces.size()) {
    if(surface_emitter_faces[face_index].facets.size() == 0)
      msg_error("No facets found for a face.");
    length += sizeof(LGI_SURFACE_EMITTER_GEOMETRY_RECORD) + sizeof(char) * surface_emitter_faces[face_index].name.size();
    for(std::list<std::vector<asINT32> >::iterator it = surface_emitter_faces[face_index].facets.begin(); 
        it != surface_emitter_faces[face_index].facets.end();
        ++it) {
      asINT32 num_facet_vertices = it->size();
      length += 3 * num_facet_vertices * sizeof(dFLOAT) + sizeof(asINT32);
      max_n_vertices = std::max(num_facet_vertices, max_n_vertices);
    }
  }
  
  dFLOAT *vertices = cnew dFLOAT[3 * max_n_vertices];
  LGI_SURFACE_EMITTER_GEOMETRY_HEADER header;
  header.tag.id = LGI_SURFACE_EMITTER_GEOMETRY_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_SURFACE_EMITTER_GEOMETRY_HEADER) + length);
  header.n_faces = surface_emitter_faces.size();
  if(header.n_faces > 0) {
    write_header_to_all_sps(header);

    ccDOTIMES(face_index, header.n_faces) {
      LGI_SURFACE_EMITTER_GEOMETRY_RECORD record;
      record.name_length = surface_emitter_faces[face_index].name.size();
      record.face_index = surface_emitter_faces[face_index].face_index;
      record.csys_id = 0; //vertices are always in the lattice coordinate system and in lattice length units.
      record.num_facets = surface_emitter_faces[face_index].facets.size();
      write_to_all_sps(&record, sizeof(LGI_SURFACE_EMITTER_GEOMETRY_RECORD));
      write_to_all_sps((char*)surface_emitter_faces[face_index].name.c_str(), sizeof(char) * surface_emitter_faces[face_index].name.size());

      for(std::list<std::vector<asINT32> >::iterator it = surface_emitter_faces[face_index].facets.begin(); 
          it != surface_emitter_faces[face_index].facets.end();
          ++it) {
        asINT32 num_vertices = it->size();
        write_to_all_sps(num_vertices);
        ccDOTIMES(i, num_vertices) {
          asINT32 id = (*it)[i];
          vertices[3 * i] = cdi_vertices->coord[3 * id];
          vertices[3 * i + 1] = cdi_vertices->coord[3 * id + 1];
          vertices[3 * i + 2] = cdi_vertices->coord[3 * id + 2];
        }
        write_to_all_sps(vertices, 3 * num_vertices * sizeof(dFLOAT));
      } //facets
    } //n_faces
  } //if n_faces > 0

  delete [] vertices;
  cdi_destroy_vrtx(cdi_vertices);
  return(!ok);
}


//----------------------------------------------------------------------------
// read_pglb - particle globals
//----------------------------------------------------------------------------
asINT32 sCP_CDI_READER::read_particle_globals()
{
  BOOLEAN ok = TRUE;
  sCDI_PGLB pglb;
  cdi_read_pglb(m_cdi_info,&pglb); 

  *(cp_particle_sim.cdi_particle_global_parameters()) = pglb; //Store a copy of the PGLB chunk so 
                                                      //it can be used to populate a PM{P,R} file.
  LGI_PARTICLE_GLOB_REC record; //populate an LGI record for transmission to the SPs

  if(pglb.dispersion_box.csys.type == CDI_VAR_ID_COORD_SYSTEM) {
    record.dispersion_box.csys_index = pglb.dispersion_box.csys.value;
  } else {
    msg_error("Wrong variable type found in cdi file for dispersion box csys.");
  }
  ccDOTIMES(corner_num,2) {
    ccDOTIMES(axis,3) {
      record.dispersion_box.corners[corner_num][axis] = pglb.dispersion_box.bbox.coord[axis][corner_num]; //lgi record keeps the transpose of the cdi record
    }
  }
  record.dispersion_box.name_length = 0;

  record.gravity_csys_id = pglb.gravity_csys.value;
  record.gravity[0] = pglb.gravity[0].value * cp_particle_sim.lattice_time_correction() * cp_particle_sim.lattice_time_correction();
  record.gravity[1] = pglb.gravity[1].value * cp_particle_sim.lattice_time_correction() * cp_particle_sim.lattice_time_correction();
  record.gravity[2] = pglb.gravity[2].value * cp_particle_sim.lattice_time_correction() * cp_particle_sim.lattice_time_correction();
  record.coupled_momentum_solver = pglb.coupled_momentum_solver.value;
  //set some hidden model parameters
#if 1
  cSTRING env_string;

  //record.reentrainment_diameter = -1;  //Disables modification to re-entrained diameters
  record.reentrainment_diameter = 1.5e-4;  //.15 mm
  env_string = getenv("EXA_REENTRAINMENT_DIAMETER");
  if(env_string != NULL) {
    record.reentrainment_diameter = atof(env_string);
    msg_warn("Overriding reentrainment diameter to %g(m)",record.reentrainment_diameter);
  }
  record.reentrainment_diameter /= cdi_data.meters_per_cell;   //Convert to LU

  record.reentrainment_diameter_range = 5e-5;  // +/- .05mm
  env_string = getenv("EXA_REENTRAINMENT_DIAMETER_RANGE");
  if(env_string != NULL) {
    record.reentrainment_diameter_range = atof(env_string);
    msg_warn("Overriding reentrainment diameter range to %g(m)",record.reentrainment_diameter_range);
  }
  record.reentrainment_diameter_range /= cdi_data.meters_per_cell;   //Convert to LU.

  record.turb_kinetic_energy_influence = 0.0; //Don't generate air velocity fluctuations from local turb KE by default.
  env_string = getenv("EXA_TURB_KE_INFLUENCE");
  if(env_string != NULL) {
    record.turb_kinetic_energy_influence = atof(env_string);
    msg_warn("Overriding turbulent kinetic energy influence to %g",record.turb_kinetic_energy_influence);
  }

  record.surface_tension_factor_at_splash = 1.0; //Modify surface tension at splash by this factor (no effect at default).
  env_string = getenv("EXA_SPLASH_SURFACE_TENSION_FACTOR");
  if(env_string != NULL) {
    record.surface_tension_factor_at_splash = atof(env_string);
    msg_warn("Overriding surface tension at splash factor to %g",record.surface_tension_factor_at_splash);
  }

  record.shear_stress_factor_for_film = 1.6; //Modify shear stress from air acting on film by this factor.
  env_string = getenv("EXA_FILM_SHEAR_STRESS_FACTOR");
  if(env_string != NULL) {
    record.shear_stress_factor_for_film = atof(env_string);
    msg_warn("Overriding shear stress factor for film to %g",record.shear_stress_factor_for_film);
  }

  record.max_radius_of_film_stencil = 5e-3;  //This was changed to the below 5/18/15 and restored 6/10/15.
  //record.max_radius_of_film_stencil = 2.5e-3; 
  record.max_radius_of_film_stencil /= cdi_data.meters_per_cell; //convert to LU
  env_string = getenv("EXA_MAX_FILM_STENCIL_RADIUS");
  if(env_string != NULL) {
    record.max_radius_of_film_stencil = atof(env_string) / cdi_data.meters_per_cell;
    msg_warn("Overriding default film stencil radius of 5e-3(m) %g(LU) to %g(m) %g(LU)",
             5e-3 / cdi_data.meters_per_cell,
             record.max_radius_of_film_stencil * cdi_data.meters_per_cell,
             record.max_radius_of_film_stencil);
  } else {
    //Otherwise, check that the default value isn't too small in lattice units for at least the finest scale.
    if(record.max_radius_of_film_stencil < 2.0 ) {
      record.max_radius_of_film_stencil = 2.0;
      if(!sim_args.disable_particle_modeling) {
        msg_warn("Resolution may be too coarse for accurate film dynamics in at least"
                 " the finest VR.\nIncreasing film stencil radius to %g(LU), %g(m).\n", 
                 record.max_radius_of_film_stencil,
                 record.max_radius_of_film_stencil * cdi_data.meters_per_cell);
      }
    } 
  }
#if 0
  msg_warn("Using a film solver stencil radius of %g(m) %g(LU).",
           record.max_radius_of_film_stencil * cdi_data.meters_per_cell,
           record.max_radius_of_film_stencil);
#endif

  //The default is no surface tension model.
  record.advancing_contact_angle = -1;
  record.receding_contact_angle = -1;
  record.film_surface_tension_model_enabled = FALSE;

  //Turn on the model if the user has set the proper environment variable.
  env_string = getenv("EXA_FILM_SURFACE_TENSION_ENABLED");
  if(env_string != NULL) {

    if(strncmp(env_string,"1",2) == 0) {  //The variable must be set to "1" to be enabled
      //if enabled, these are the default angles
      record.advancing_contact_angle = 15;
      record.receding_contact_angle = 0;
      record.film_surface_tension_model_enabled = TRUE;

      //check if the defaults should be overridden
      env_string = getenv("EXA_ADVANCING_ANGLE");
      if(env_string != NULL) {
        msg_warn("Overriding default advancing angle of %g",record.advancing_contact_angle);
        record.advancing_contact_angle = atof(env_string);
      }
      env_string = getenv("EXA_RECEDING_ANGLE");
      if(env_string != NULL) {
        msg_warn("Overriding default receding angle of %g",record.receding_contact_angle);
        record.receding_contact_angle = atof(env_string);
      }
      msg_warn("Surface tension model enabled for film solver. Using advancing contact angle of %g degrees and a receding contact angle of %g degrees.", record.advancing_contact_angle, record.receding_contact_angle);
    }

  }
#endif

  LGI_PARTICLE_GLOB_HEADER header;
  header.tag.id = LGI_PARTICLE_GLOB_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_PARTICLE_GLOB_HEADER)
                                                       + sizeof(LGI_PARTICLE_GLOB_REC));


  write_header_to_all_sps(header);
  write_to_all_sps(record);
  return(!ok);
}


//----------------------------------------------------------------------------
// read_prmt - particle materials
//----------------------------------------------------------------------------
asINT32 sCP_CDI_READER::read_particle_materials()
{
  BOOLEAN ok = TRUE;
  std::vector<LGI_PARTICLE_MATERIAL_REC> material_records;
  std::vector<std::string> material_names;

  asINT32 total_name_length = 0;

  ccCDI_DO_INNER_CHUNKS(i, "prms", m_cdi_info) {
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PRMT) {
      sCDI_PRMT prmt;
      cdi_read_prmt(m_cdi_info,&prmt);
      cp_particle_sim.cdi_particle_materials()->push_back(prmt); //Save the cdi data for later.
    
      LGI_PARTICLE_MATERIAL_REC record;
      const char*name = prmt.name.c_str();
      record.name_length = strlen(name);  //Don't send the terminating null character.
      total_name_length += record.name_length;
      record.material_type = prmt.type.value;

      sCDI_PARM parm1, parm2;
      CDI_DISTRIBUTION_TYPE dist;
      dist = (CDI_DISTRIBUTION_TYPE)(int)prmt.density_info.GetDistributionType().value;
      prmt.density_info.GetParameterValues(&parm1, &parm2);

      record.density_distribution = cdi_to_lgi_distribution(dist);
      record.mean_density = parm1.value;
      record.density_range = parm2.value;
    
      //Convert LU kinematic viscosity from units of LatticeDensity*LatticeLength^2/LatticeTimeInc to units of LatticeDensity*LatticeLength^2/LatticeTime.
      record.dynamic_viscosity = prmt.viscosity.value * cp_particle_sim.lattice_time_correction();

      //Convert LU surface tension from units of LatticeMass/LatticeTimeInc^2 (LatticeTorquePerArea) to LatticeMass/LatticeTime^2
      record.surface_tension = prmt.surface_tension.value * cp_particle_sim.lattice_time_correction() * cp_particle_sim.lattice_time_correction();

      record.breakup_allowed = prmt.breakup_model.value == 1 ;
      material_records.push_back(record);
      material_names.push_back(prmt.name);
    }
  }
  LGI_PARTICLE_MATERIAL_HEADER header;
  header.tag.id = LGI_PARTICLE_MATERIAL_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_PARTICLE_MATERIAL_HEADER) 
                                                       + sizeof(LGI_PARTICLE_MATERIAL_REC) * material_records.size()
                                                       + total_name_length * sizeof(char));
  header.n_materials = material_records.size();
  if(header.n_materials>0) {
    write_header_to_all_sps(header);
    ccDOTIMES(i, header.n_materials) {
      write_to_all_sps(material_records[i]);
      write_to_all_sps((char*)material_names[i].c_str(), sizeof(char) * material_records[i].name_length);
    }
  }
  return(!ok);
}

//----------------------------------------------------------------------------
// read_scmt - conduction solid materials
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_conduction_solid_materials()
{
  if(!m_is_scms_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_SCMS);
  std::vector<std::string> material_names;

  LGI_CONDUCTION_SOLID_MATERIAL_HEADER header;
  header.tag.id = LGI_CONDUCTION_SOLID_MATERIAL_TAG;
  header.tag.length = 0;
  header.n_materials = 0;

  m_pds.size = 0;
  m_pds.write(&header, sizeof(header));

  asINT32 n_materials = 0;
  ccCDI_DO_INNER_CHUNKS(i, "scms", m_cdi_info) {
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_SCMT) {
      sCDI_SCMT scmt;
      cdi_read_scmt(m_cdi_info,&scmt);
      cp_particle_sim.cdi_conduction_solid_materials()->push_back(scmt); //Save the cdi data for later.

      const char*name = scmt.name.c_str();
      asINT32 name_length = strlen(name) + 1;
      m_pds.write(&name_length, sizeof(asINT32));  
      if (name_length > 1) {
        m_pds.write((VOID *)name, strlen(name)+1);  // Send the terminating null character.
      }

      auto write_lgi_lambda = [name] (auto parm, auto propName, auto *buf) {
        LGI_PHYSICS_VARIABLE var;
        var.value = parm.value;
        if (parm.isEqVariable && parm.variableName.empty()) {
          msg_internal_error("Material property \"%s\":\"%s\" is incorrectly set to an equation", name, propName); 
        } else if (parm.isDataCurve && parm.variableName.empty()) {
          msg_internal_error("Material property \"%s\":\"%s\" is incorrectly set to a data curve", name, propName);
        }
        var.name_length = parm.variableName.empty() ? 1 : parm.variableName.length() +1; // sending terminating null character
        if(parm.isDataCurve)
          var.type = 2;
        else if(parm.isEqVariable)
          var.type = 1;
        buf->write(&var, sizeof(var));
        if (!parm.variableName.empty()) {
          buf->write((VOID *)parm.variableName.c_str(), var.name_length);
        }
      };
      write_lgi_lambda(scmt.density, "density", &m_pds);
      write_lgi_lambda(scmt.specific_heat, "specific heat", &m_pds);
      for (auto axis = 0; axis < 3; axis++) {
        write_lgi_lambda(scmt.therm_conductivity[axis], "thermal conductivity", &m_pds);
      }
      BOOLEAN is_isotropy = TRUE;
      CONDUCTION_ANISOTROPY_TYPE anisotropy_type = CONDUCTION_ANISOTROPY_TYPE::INVALID_ANISOTROPY;
      if (scmt.material_type == eCDI_SOLID_MATERIAL_TYPE::Enum::AnisotropicVolumetric) {
        is_isotropy = FALSE;
        if (scmt.anisotropy_type == eCDI_SOLID_ANISOTROPY_TYPE::Enum::Cylindrical) {
          anisotropy_type = CYLINDRICAL_ANISOTROPY; 
        } else if (scmt.anisotropy_type == eCDI_SOLID_ANISOTROPY_TYPE::Enum::Cartesian) {
          anisotropy_type = CARTESIAN_ANISOTROPY;
        }
      }
      m_pds.write(&is_isotropy, sizeof(is_isotropy));
      m_pds.write(&anisotropy_type, sizeof(anisotropy_type));
      n_materials++;

      material_names.push_back(scmt.name);
    }
  }

  if(n_materials > 0) {
    LGI_CONDUCTION_SOLID_MATERIAL_HEADER *h = reinterpret_cast<LGI_CONDUCTION_SOLID_MATERIAL_HEADER *>(m_pds.buffer);
    h->tag.length = lgi_pad_and_encode_record_length(m_pds.size);
    h->n_materials = n_materials; 
    write_record_to_all_sps(m_pds.buffer);
  }

  exit_cdi_chunk();
}

//----------------------------------------------------------------------------
// read_paxs - conduction anisotropy part axis
//----------------------------------------------------------------------------
VOID sCP_CDI_READER::read_anisotropic_part_axis()
{
  if(!m_is_paxl_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_PAXL);
  std::vector<LGI_PART_AXIS> part_axes;

  ccCDI_DO_INNER_CHUNKS(i, "paxl", m_cdi_info) {
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PAXS) {
      sCDI_PAXS paxs;
      cdi_read_paxs(m_cdi_info,&paxs);

      LGI_PART_AXIS part_axis;

      part_axis.part_index = paxs.part_index;

      ccDOTIMES(i, 3) {
	part_axis.axis_dir[i] = paxs.axis_dir[i];
      }

      part_axes.push_back(part_axis);
    }
  }
  LGI_PART_AXIS_HEADER header;
  header.tag.id = LGI_PART_AXIS_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_PART_AXIS_HEADER)
                                                       + sizeof(LGI_PART_AXIS) * part_axes.size());
  header.n_axes = part_axes.size();
  if(header.n_axes>0) {
    write_header_to_all_sps(header);
    ccDOTIMES(i, header.n_axes) {
      write_to_all_sps(part_axes[i]);
    }
  }
  exit_cdi_chunk();
}

VOID sCP_CDI_READER::read_radiation_surface_conditions()
{
  if(!m_is_rdsl_present)
    return;
  enter_cdi_chunk(CDI_CHUNK_TYPE_RDSL);
  std::vector<std::string> surface_condition_names;

  LGI_RADIATION_SURFACE_CONDITION_HEADER header;
  header.tag.id = LGI_RADIATION_SURFACE_CONDITION_TAG;
  header.tag.length = 0;
  header.n_surface_conditions = 0;
  m_pds.size = 0;
  m_pds.write(&header, sizeof(header));

  asINT32 n_surface_conditions = 0;
  ccCDI_DO_INNER_CHUNKS(i, "rdsc", m_cdi_info) {
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_RDSC) {
      sCDI_RDSC rdsc;
      cdi_read_rdsc(m_cdi_info,&rdsc);
      CDI_PARM em = &(rdsc.emissivity);

      const char*name = rdsc.name.c_str();
      asINT32 name_length = strlen(name) + 1;
      m_pds.write(&name_length, sizeof(asINT32));  
      if (name_length > 1) {
        m_pds.write((VOID *)name, strlen(name)+1);  // Send the terminating null character.
      }

      LGI_PHYSICS_VARIABLE var;
      var.value = em->value;
      if (em->isEqVariable && em->variableName.empty()) {
        msg_internal_error("Emissivity value for radiation condition \"%s\" is incorrectly set to an equation", name); 
      }
      var.name_length = em->variableName.empty() ? 0 : em->variableName.length(); // not sending terminating null character
      m_pds.write(&var, sizeof(var));
      if (!em->variableName.empty()) {
        m_pds.write((VOID *)em->variableName.c_str(), var.name_length);
      }
      n_surface_conditions++;

    }
  }

  if (n_surface_conditions > 0) {
    LGI_RADIATION_SURFACE_CONDITION_HEADER *h = reinterpret_cast<LGI_RADIATION_SURFACE_CONDITION_HEADER*>(m_pds.buffer);
    h->tag.length = lgi_pad_and_encode_record_length(m_pds.size);
    h->n_surface_conditions = n_surface_conditions; 
    write_record_to_all_sps(m_pds.buffer);
  }

  exit_cdi_chunk();

}

//----------------------------------------------------------------------------
// read particle emitters and particle emitter configurations
//----------------------------------------------------------------------------

asINT32 sCP_CDI_READER::read_particle_emitter_configurations() 
{
  
  //Read everything from the cdi file.
  cCDI_EMITTER_CONFIGURATIONS cdi_emitter_configurations;
  cdi_emitter_configurations.ReadFromCDI(m_cdi_info);  
  
  //Get the the vector of all configurations
  std::vector<const sCDI_EMITTER_CONFIG_BASE*> cdi_emitter_configs = 
    cdi_emitter_configurations.GetAllEmittersConfigs();
  
  //Make a vector of non-constant copies so the values could be
  //modified if needed (e.g. for backwards compatibility, the rain
  //emiter config needs to be modifed before being written to a PRI
  //file header).

  std::vector<sCDI_EMITTER_CONFIG_BASE*> emitter_configs;
  ccDOTIMES(config_index , cdi_emitter_configs.size()) {
    const sCDI_EMITTER_CONFIG_BASE* cdi_emitter_config = cdi_emitter_configs[config_index];
    switch(cdi_emitter_config->GetType()) {
    case cCDI_EMITTER_CONFIGURATIONS::NOZZLE:
      {
        sCDI_NOZZLE_EMITTER_CONFIG* emitter_config = xnew sCDI_NOZZLE_EMITTER_CONFIG;
        *emitter_config = *((const sCDI_NOZZLE_EMITTER_CONFIG*)cdi_emitter_config);
        emitter_configs.push_back(emitter_config);
      }
        break;
    case cCDI_EMITTER_CONFIGURATIONS::RAIN:
      {
        sCDI_RAIN_EMITTER_CONFIG* emitter_config = xnew sCDI_RAIN_EMITTER_CONFIG;
        *emitter_config = *(const sCDI_RAIN_EMITTER_CONFIG*)cdi_emitter_config;
        emitter_configs.push_back(emitter_config);
      }
      break;
    case cCDI_EMITTER_CONFIGURATIONS::TIRE:
      {
        sCDI_TIRE_EMITTER_CONFIG* emitter_config = xnew sCDI_TIRE_EMITTER_CONFIG;
        *emitter_config = *(const sCDI_TIRE_EMITTER_CONFIG*)cdi_emitter_config;
        emitter_configs.push_back(emitter_config);
      }
      break;
    default:
      msg_error("Found unknown emitter configuration type found.");
    };
  }


  //The total size of these lgi emitter configuration records need to be encoded in the lgi header.  It is computed here.
  //make one pass and compute the size (which can vary due to the variable name strings)
  size_t num_total_bytes = 0;
  ccDOTIMES(config_index, emitter_configs.size()) {
    sCDI_EMITTER_CONFIG_BASE* emitter_config = emitter_configs[config_index];
    switch(emitter_config->GetType()) {
    case cCDI_EMITTER_CONFIGURATIONS::NOZZLE:
      {
        sCDI_NOZZLE_EMITTER_CONFIG* nozzle_config = (sCDI_NOZZLE_EMITTER_CONFIG*)emitter_config;
        sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER record(cdi_data, nozzle_config);
        sLGI_NOZZLE_CONFIGURATION_REC_WRITER nozzle_record(nozzle_config);
        num_total_bytes += record.num_total_bytes();
        num_total_bytes += nozzle_record.num_total_bytes();
      }
      break;
    case cCDI_EMITTER_CONFIGURATIONS::RAIN:
      {
        sCDI_RAIN_EMITTER_CONFIG* rain_config = (sCDI_RAIN_EMITTER_CONFIG*)emitter_config;
        sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER record(cdi_data, rain_config);
        sLGI_NOZZLE_CONFIGURATION_REC_WRITER nozzle_record(rain_config);
        num_total_bytes += record.num_total_bytes();
        num_total_bytes += nozzle_record.num_total_bytes();
      }
      break;
    case cCDI_EMITTER_CONFIGURATIONS::TIRE:
      {
        sCDI_TIRE_EMITTER_CONFIG* tire_config = (sCDI_TIRE_EMITTER_CONFIG*)emitter_config;
        std::vector<sCDI_TIRE_NOZZLE_PROPS> tire_nozzles = tire_config->GetTireNozzles();
        sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER record(cdi_data, tire_config);
        num_total_bytes += record.num_total_bytes();
        ccDOTIMES(tire_nozzle_index, tire_nozzles.size()) {
          sLGI_NOZZLE_CONFIGURATION_REC_WRITER nozzle_record(&tire_nozzles[tire_nozzle_index]);
          num_total_bytes += nozzle_record.num_total_bytes();
        }
      }
      break;
    default:
      msg_error("Found unknown emitter configuration type found.");
    };
  }

  LGI_PARTICLE_EMITTER_CONFIGURATION_HEADER header;
  header.tag.id = LGI_PARTICLE_EMITTER_CONFIGURATION_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_PARTICLE_EMITTER_CONFIGURATION_HEADER) + num_total_bytes);
  header.n_configurations = emitter_configs.size();                                                       
  write_header_to_all_sps(header);

  //Make a second pass through the emitter configuration cdi records and write them to the LGI stream.
  ccDOTIMES(config_index, header.n_configurations) {
    sCDI_EMITTER_CONFIG_BASE* emitter_config = emitter_configs[config_index];
    switch(emitter_config->GetType()) {
    case cCDI_EMITTER_CONFIGURATIONS::NOZZLE:
      {
        sCDI_NOZZLE_EMITTER_CONFIG* nozzle_config = (sCDI_NOZZLE_EMITTER_CONFIG*)emitter_config;
        sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER record(cdi_data, nozzle_config);
        sLGI_NOZZLE_CONFIGURATION_REC_WRITER nozzle_record(nozzle_config);
        record.write(nozzle_config); //Write the emitter config record and variable names
        nozzle_record.write(nozzle_config); //Write the nozzle config data and variable names
      }
      break;
    case cCDI_EMITTER_CONFIGURATIONS::RAIN:
      {
        sCDI_RAIN_EMITTER_CONFIG* rain_config = (sCDI_RAIN_EMITTER_CONFIG*)emitter_config;
        sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER record(cdi_data, rain_config);
        sLGI_NOZZLE_CONFIGURATION_REC_WRITER nozzle_record(rain_config);
        record.write(rain_config); //Write the emitter config record and variable names
        nozzle_record.write(rain_config); //Write the nozzle config data and variable names
      }
      break;
    case cCDI_EMITTER_CONFIGURATIONS::TIRE:
      {
        sCDI_TIRE_EMITTER_CONFIG* tire_config = (sCDI_TIRE_EMITTER_CONFIG*)emitter_config;
        std::vector<sCDI_TIRE_NOZZLE_PROPS> tire_nozzles = tire_config->GetTireNozzles();
        sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER record(cdi_data, tire_config);
        record.write(tire_config);  //Write the emitter config record and variable names
        ccDOTIMES(tire_nozzle_index, tire_nozzles.size()) {
          sLGI_NOZZLE_CONFIGURATION_REC_WRITER nozzle_record(&tire_nozzles[tire_nozzle_index]);
          nozzle_record.write(tire_config, &tire_nozzles[tire_nozzle_index]); //Write each nozzle config records and asscoiated variable names
        }
      }
      break;
    default:
      msg_error("Found unknown emitter configuration type found.");
    };
  }

  //Lastly, populate cp_particle_sim.cdi_emitter_configurations() with
  //the modified records so it can be used when populating a PMR file
  //header using ConvertToPRI.
  ccDOTIMES(config_index, emitter_configs.size()) {
    cp_particle_sim.cdi_emitter_configurations()->AddConfiguration(emitter_configs[config_index]);
  }

  return(0);
}

static PRI::EMITTER_TYPE convert_emitter_type_from_cdi_to_pri(cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE cdi_type)
{
  switch(cdi_type)
    {
    case cCDI_PARTICLE_EMITTERS::EMITTER_UNKNOWN:
      return PRI::EMITTER_TYPE_UNDEFINED;
    case cCDI_PARTICLE_EMITTERS::EMITTER_SURFACE:
      return PRI::EMITTER_TYPE_SURFACE;
    case cCDI_PARTICLE_EMITTERS::EMITTER_VOLUME:
      return PRI::EMITTER_TYPE_VOLUME;
    case cCDI_PARTICLE_EMITTERS::EMITTER_POINT:
      return PRI::EMITTER_TYPE_POINT;
    case cCDI_PARTICLE_EMITTERS::EMITTER_TIRE:
      return PRI::EMITTER_TYPE_TIRE;
    case cCDI_PARTICLE_EMITTERS::EMITTER_RAIN:
      return PRI::EMITTER_TYPE_RAIN;
    default:
      msg_internal_error("unknown emitter type %d", cdi_type);
    }
  return(PRI::EMITTER_TYPE_UNDEFINED);
}
asINT32 sCP_CDI_READER::read_particle_emitters() {
  //Stores all emitter data for later incase it needs to be added to a pmr file 
  //if there are any trajectory windows defined in this case
  cp_particle_sim.cdi_particle_emitters()->ReadFromCDI(m_cdi_info); 


  const std::vector<const sCDI_SURFACE_EMITTER*> &cdi_surface_emitters = cp_particle_sim.cdi_particle_emitters()->GetSurfaceEmitters();
  const std::vector<const sCDI_VOLUME_EMITTER*> &cdi_volume_emitters = cp_particle_sim.cdi_particle_emitters()->GetVolumeEmitters(); 
  const std::vector<const sCDI_POINT_EMITTER*> &cdi_point_emitters = cp_particle_sim.cdi_particle_emitters()->GetPointEmitters();
  const std::vector<const sCDI_TIRE_EMITTER*> &cdi_tire_emitters = cp_particle_sim.cdi_particle_emitters()->GetTireEmitters();
  const std::vector<const sCDI_RAIN_EMITTER*> &cdi_rain_emitters = cp_particle_sim.cdi_particle_emitters()->GetRainEmitters(); 
  const std::vector<const sCDI_PARTICLE_EMITTER_BASE*> &cdi_emitters = cp_particle_sim.cdi_particle_emitters()->GetAllEmitters();
  asINT32 total_cdi_emitters = cdi_emitters.size();
  g_cdi_to_pri_emitter_ids.resize(total_cdi_emitters);
  g_simulator_to_cdi_emitter_ids.resize(total_cdi_emitters);
  // Ccompute the PRI emitter IDs and populate the g_cdi_to_pri_emitter_ids vector.
  asINT32 sim_emitter_index = 0;

  ccDOTIMES(surface_emitter_index, cdi_surface_emitters.size()) {
    //find the index of this surface emitter in the emitters vector (the vector of all emitters types )
    const sCDI_SURFACE_EMITTER* cdi_emitter = cdi_surface_emitters[surface_emitter_index];
    auto face_list = ((sCDI_SURFACE_EMITTER*)cdi_emitter)->geom_selection.ExpandSelection(cp_info.partitions());
    //record which surface is used so that read_geometry_for_surface_emitters knows which surface needs to be written to lgi
    //extern std::vector<int> g_surf_emitter_geom_ids;
    g_surf_emitter_geom_ids.insert(g_surf_emitter_geom_ids.end(), face_list.begin(), face_list.end());
    
    ccDOTIMES(cdi_emitters_index, total_cdi_emitters) {
      if(cdi_surface_emitters[surface_emitter_index] == cdi_emitters[cdi_emitters_index]) {
        g_simulator_to_cdi_emitter_ids[sim_emitter_index++] = cdi_emitters_index;
        g_cdi_to_pri_emitter_ids[cdi_emitters_index] = surface_emitter_index;
        break;
      }
    }
  }

  ccDOTIMES(volume_emitter_index, cdi_volume_emitters.size()) {
    //find the index of this volume emitter in the emitters vector (the vector of all emitters types )
    ccDOTIMES(cdi_emitters_index, total_cdi_emitters) {
      if(cdi_volume_emitters[volume_emitter_index] == cdi_emitters[cdi_emitters_index]) {
        g_simulator_to_cdi_emitter_ids[sim_emitter_index++] = cdi_emitters_index;
        g_cdi_to_pri_emitter_ids[cdi_emitters_index] = volume_emitter_index;
        break;
      }
    }
  }

  ccDOTIMES(point_emitter_index, cdi_point_emitters.size()) {
    //find the index of this point emitter in the emitters vector (the vector of all emitters types )
    ccDOTIMES(cdi_emitters_index, total_cdi_emitters) {
      if(cdi_point_emitters[point_emitter_index] == cdi_emitters[cdi_emitters_index]) {
        g_simulator_to_cdi_emitter_ids[sim_emitter_index++] = cdi_emitters_index;
        g_cdi_to_pri_emitter_ids[cdi_emitters_index] = point_emitter_index;
        break;
      }
    }
  }

  ccDOTIMES(tire_emitter_index, cdi_tire_emitters.size()) {
    //find the index of this tire emitter in the emitters vector (the vector of all emitters types )
    ccDOTIMES(cdi_emitters_index, total_cdi_emitters) {
      if(cdi_tire_emitters[tire_emitter_index] == cdi_emitters[cdi_emitters_index]) {
        g_simulator_to_cdi_emitter_ids[sim_emitter_index++] = cdi_emitters_index;
        g_cdi_to_pri_emitter_ids[cdi_emitters_index] = tire_emitter_index;
        break;
      }
    }
  }

  ccDOTIMES(rain_emitter_index, cdi_rain_emitters.size()) {
    //find the index of this rain emitter in the emitters vector (the vector of all emitters types )
    ccDOTIMES(cdi_emitters_index, total_cdi_emitters) {
      if(cdi_rain_emitters[rain_emitter_index] == cdi_emitters[cdi_emitters_index]) {
        g_simulator_to_cdi_emitter_ids[sim_emitter_index++] = cdi_emitters_index;
        g_cdi_to_pri_emitter_ids[cdi_emitters_index] = rain_emitter_index;
        break;
      }
    }
  }

  g_simulator_to_pri_emitter_ids.resize(g_simulator_to_cdi_emitter_ids.size());
  ccDOTIMES(i, g_simulator_to_pri_emitter_ids.size())
    g_simulator_to_pri_emitter_ids[i] = g_cdi_to_pri_emitter_ids[ g_simulator_to_cdi_emitter_ids[i] ];

  g_simulator_emitter_id_to_pri_emitter_types.resize(g_simulator_to_pri_emitter_ids.size());
  ccDOTIMES(i, g_simulator_emitter_id_to_pri_emitter_types.size()) {
    asINT32 cdi_emitter_id = g_simulator_to_cdi_emitter_ids[i];
    cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE cdi_emitter_type = cdi_emitters[cdi_emitter_id]->GetType();
    g_simulator_emitter_id_to_pri_emitter_types[i] = convert_emitter_type_from_cdi_to_pri(cdi_emitter_type);
  } 

  //A ficticious emitter id is used by PRI to represent the emitter id for particles that 
  //have merged.  This is necessecary since the particles that merged may have come from different emitters.
  //PRI expects the merged emitters to have an id of 0 and the simulator uses the last emitter id in its space of ids.
  g_cdi_to_pri_emitter_ids.push_back(0);
  g_simulator_to_pri_emitter_ids.push_back(0);
  g_simulator_to_cdi_emitter_ids.push_back(sim_emitter_index++);
  g_simulator_emitter_id_to_pri_emitter_types.push_back(PRI::EMITTER_TYPE_MERGED);

  if(sim_emitter_index != 1+ total_cdi_emitters) //then somewhere above, an emitter of a particular type could not be found in CDI's vector of all emitters
    msg_error("Inconsistend set of emitters found in CDI data.");

  return 0;
}

asINT32 sCP_CDI_READER::send_particle_emitters() {
  
  if(!m_is_trac_present) 
    return TRUE;

  size_t num_total_bytes = 0;
  const std::vector<const sCDI_SURFACE_EMITTER*> &cdi_surface_emitters = cp_particle_sim.cdi_particle_emitters()->GetSurfaceEmitters();
  const std::vector<const sCDI_VOLUME_EMITTER*> &cdi_volume_emitters = cp_particle_sim.cdi_particle_emitters()->GetVolumeEmitters(); 
  const std::vector<const sCDI_POINT_EMITTER*> &cdi_point_emitters = cp_particle_sim.cdi_particle_emitters()->GetPointEmitters();
  const std::vector<const sCDI_TIRE_EMITTER*> &cdi_tire_emitters = cp_particle_sim.cdi_particle_emitters()->GetTireEmitters();
  const std::vector<const sCDI_RAIN_EMITTER*> &cdi_rain_emitters = cp_particle_sim.cdi_particle_emitters()->GetRainEmitters(); 
  const std::vector<const sCDI_PARTICLE_EMITTER_BASE*> &cdi_emitters = cp_particle_sim.cdi_particle_emitters()->GetAllEmitters();

  asINT32 total_cdi_emitters = cdi_emitters.size();
 
  //Make one pass over all emitter types to compute the lgi record 
  //sizes (including variable name strings) that will be encoded in the header.
  ccDOTIMES(surface_emitter_index, cdi_surface_emitters.size()) {
    sLGI_SURFACE_EMITTER_REC_WRITER lgi_emitter(cdi_surface_emitters[surface_emitter_index]);
    num_total_bytes += lgi_emitter.num_total_bytes();
  }
  
  ccDOTIMES(volume_emitter_index, cdi_volume_emitters.size()) {
    sLGI_VOLUME_EMITTER_REC_WRITER lgi_emitter(cdi_volume_emitters[volume_emitter_index]);
    num_total_bytes += lgi_emitter.num_total_bytes();
  }
  
  ccDOTIMES(point_emitter_index, cdi_point_emitters.size()) {
    sLGI_POINT_EMITTER_REC_WRITER lgi_emitter(cdi_point_emitters[point_emitter_index]);
    num_total_bytes += lgi_emitter.num_total_bytes();
  }
  
  ccDOTIMES(tire_emitter_index, cdi_tire_emitters.size()) {
    sLGI_TIRE_EMITTER_REC_WRITER lgi_emitter(cdi_tire_emitters[tire_emitter_index]);
    num_total_bytes += lgi_emitter.num_total_bytes();
  }
  
  ccDOTIMES(rain_emitter_index, cdi_rain_emitters.size()) {
    sLGI_RAIN_EMITTER_REC_WRITER lgi_emitter(cdi_rain_emitters[rain_emitter_index]);
    num_total_bytes += lgi_emitter.num_total_bytes();
  }
  
  
  //Encode the length into the header and make a second pass over all emitters while writing to the LGI stream.
  //Make one pass over all emitter types to compute the lgi record sizes (including variable name strings) that will be encoded in the header.
  
  LGI_PARTICLE_EMITTER_HEADER header;
  header.tag.id = LGI_PARTICLE_EMITTER_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_PARTICLE_EMITTER_HEADER) + num_total_bytes);
  header.num_surface_emitters = cp_particle_sim.cdi_particle_emitters()->GetSurfaceEmitters().size();
  header.num_volume_emitters = cp_particle_sim.cdi_particle_emitters()->GetVolumeEmitters().size();
  header.num_point_emitters = cp_particle_sim.cdi_particle_emitters()->GetPointEmitters().size();
  header.num_tire_emitters = cp_particle_sim.cdi_particle_emitters()->GetTireEmitters().size();
  header.num_rain_emitters = cp_particle_sim.cdi_particle_emitters()->GetRainEmitters().size();
  write_header_to_all_sps(header);
  
  ccDOTIMES(emitter_index, cdi_surface_emitters.size()) {
    sLGI_SURFACE_EMITTER_REC_WRITER lgi_emitter(cdi_surface_emitters[emitter_index]);
    lgi_emitter.write(cdi_surface_emitters[emitter_index]);
  }
  
  ccDOTIMES(emitter_index, cdi_volume_emitters.size()) {
    sLGI_VOLUME_EMITTER_REC_WRITER lgi_emitter(cdi_volume_emitters[emitter_index]);
    lgi_emitter.write(cdi_volume_emitters[emitter_index]);
  }

  ccDOTIMES(emitter_index, cdi_point_emitters.size()) {
    sLGI_POINT_EMITTER_REC_WRITER lgi_emitter(cdi_point_emitters[emitter_index]);
    lgi_emitter.write(cdi_point_emitters[emitter_index]);
  }

  ccDOTIMES(emitter_index, cdi_tire_emitters.size()) {
    sLGI_TIRE_EMITTER_REC_WRITER lgi_emitter(cdi_tire_emitters[emitter_index]);
    lgi_emitter.write(cdi_tire_emitters[emitter_index]);
  }
  
  ccDOTIMES(emitter_index, cdi_rain_emitters.size()) {
    sLGI_RAIN_EMITTER_REC_WRITER lgi_emitter(cdi_rain_emitters[emitter_index]);
    lgi_emitter.write(cdi_rain_emitters[emitter_index]);
  }
  return 0;
}


//----------------------------------------------------------------------------
// read_srmi - surface particle material interaction
//----------------------------------------------------------------------------
asINT32 sCP_CDI_READER::read_particle_surface_properties() {
  BOOLEAN ok = TRUE;
  std::vector<LGI_PARTICLE_SURFACE_MATERIAL_REC> surface_interaction_records;
  std::vector<std::string> surface_type_names;
  asINT32 num_surface_types = 0;
  asINT32 total_variable_size = 0;
  ccCDI_DO_INNER_CHUNKS(i, "srms", m_cdi_info) {
    std::string prop_name;
    sCDI_PARM reentrainment_allowed;
    sCDI_PARM reentrainment_length;
    sCDI_PARM disable_film_solver;

    BOOLEAN read_reentrainment_enabled_flag = FALSE;
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_SRFM) {
      ccCDI_DO_INNER_CHUNKS(index, "srfm", m_cdi_info) {

        if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_NAME) {
          CDI_NAME surface_material_name = cdi_read_name(m_cdi_info);
          prop_name = surface_material_name->name;
          EXA_FREE(surface_material_name);
        } else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_PARM) {
          if(read_reentrainment_enabled_flag == FALSE) {
            //cdi_inner_chunk_read_parm( index,chunkname,m_cdi_info, &reentrainment_allowed);
            cdi_read_parm(m_cdi_info, &reentrainment_allowed);
            read_reentrainment_enabled_flag = TRUE;
          } else {
            //cdi_inner_chunk_read_parm( index,chunkname,m_cdi_info, &reentrainment_length);
            if (m_cdi_info->major_version < 8 || (m_cdi_info->major_version == 8 && m_cdi_info->minor_version < 2)) {
              cdi_read_parm( m_cdi_info, &reentrainment_length);
            } else {
                reentrainment_length.value = -1; // -1 was read for older files (pre 8.2)
                cdi_read_parm( m_cdi_info, &disable_film_solver);
            }
          }
        } else if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_SRMI) {
          LGI_PARTICLE_SURFACE_MATERIAL_REC record;
          sCDI_SRMI srmi;
          cdi_read_srmi(m_cdi_info, &srmi);

          //Stash a copy on the CP incase it is needed for a PMR file.
          cp_particle_sim.cdi_surface_interaction_parameters()->push_back(srmi);
          cp_particle_sim.cdi_surface_interaction_names()->push_back(prop_name);

          //Forward the record on to the SPs.
          record.name_length = prop_name.length();
          record.particle_material_id       = srmi.particle_material.value;
          record.surface_material_id        = num_surface_types;
          record.reentrainment_allowed      = reentrainment_allowed.value != 0.0;
          record.reentrainment_length       = reentrainment_length.value;
          record.disable_film_solver        = disable_film_solver.value > 0.0;
          record.splash_model_enabled       = srmi.splash_model.value != 0.0;
          record.reflect_min_momentum       = srmi.reflect_min_momentum.value;
          record.reflect_min_normal_vel     = srmi.reflect_min_normal_vel.value;
          record.reflect_min_angle          = srmi.reflect_min_angle.value;
          //record.reflection_enabled         = !( record.reflect_min_angle == 0.0  && record.reflect_min_normal_vel == 0.0 && record.reflect_min_momentum == 0.0 );
          record.reflection_enabled         = srmi.enable_reflection.value == 1.0;
          record.normal_rest_coeff          = srmi.normal_rest_coeff.value;
          record.tang_restitution_coeff     = srmi.tang_restitution_coeff.value;
          record.scatter_angle_distribution = cdi_to_lgi_distribution(srmi.scatter_angle_distribution);
          record.scatter_angle_range        = srmi.scatter_angle_range.value;
          surface_type_names.push_back(prop_name);
          surface_interaction_records.push_back(record);
          total_variable_size += record.name_length * sizeof(char);
        }
      }
      num_surface_types++;
    }
  }
  LGI_PARTICLE_SURFACE_MATERIAL_HEADER header;
  header.tag.id = LGI_PARTICLE_SURFACE_MATERIAL_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_PARTICLE_SURFACE_MATERIAL_HEADER)
                                                       + sizeof(LGI_PARTICLE_SURFACE_MATERIAL_REC)*surface_interaction_records.size()
                                                       + total_variable_size);
  header.n_surface_materials = surface_interaction_records.size();
  if(header.n_surface_materials>0) {
    write_header_to_all_sps(header);
    ccDOTIMES(surface_prop_num,header.n_surface_materials) {
      write_to_all_sps(surface_interaction_records[surface_prop_num]);
      write_to_all_sps((char*)surface_type_names[surface_prop_num].c_str(),sizeof(char)*surface_interaction_records[surface_prop_num].name_length);
    }
  }
  return(!ok);
}

//----------------------------------------------------------------------------
// read_scrn - particle screens
//----------------------------------------------------------------------------
asINT32 sCP_CDI_READER::read_particle_screens() {
  BOOLEAN ok = TRUE;

  asINT32 total_variable_size = 0;
  std::vector<LGI_PARTICLE_SCREEN_REC> particle_screen_records;
  std::vector<std::string> screen_names;
  std::vector<std::vector<int> > face_lists;

  ccCDI_DO_INNER_CHUNKS(i, "scrs", m_cdi_info) {
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_SCRN) {
      sCDI_SCRN scrn;
      cdi_read_scrn(m_cdi_info, &scrn);
      cp_particle_sim.cdi_particle_screens()->push_back(scrn); //Keep a copy incase it's needed to populate pmr file parameters.
      
      LGI_PARTICLE_SCREEN_REC record;

      record.name_length = scrn.name.length();
      auto scrn_face_list = scrn.geom_selection.ExpandSelection(cp_info.partitions());
      record.num_faces = scrn_face_list.size();
      record.opening_size = scrn.opening_size.value;
      record.pass_thru_fraction = scrn.pass_thru_fraction.value;
      record.surface_material = scrn.surface_material.value;
      record.measured_fraction = scrn.measured_fraction.value;

      particle_screen_records.push_back(record);
      screen_names.push_back(scrn.name);
      face_lists.push_back(scrn_face_list);

      total_variable_size += record.name_length * sizeof(char) + scrn_face_list.size() * sizeof(int);
    }
  }

  LGI_PARTICLE_SCREEN_HEADER header;
  header.tag.id = LGI_PARTICLE_SCREENS_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_PARTICLE_SCREEN_HEADER)
                                                     + sizeof(LGI_PARTICLE_SCREEN_REC)*particle_screen_records.size()
                                                     + total_variable_size);
  header.n_screens = particle_screen_records.size();

  if(header.n_screens>0) {
    write_header_to_all_sps(header);
    ccDOTIMES(particel_screen_num, header.n_screens) {
      write_to_all_sps(particle_screen_records[particel_screen_num]);
      write_to_all_sps((char*)screen_names[particel_screen_num].c_str(), sizeof(char)*particle_screen_records[particel_screen_num].name_length);
      write_to_all_sps(face_lists[particel_screen_num].data(), sizeof(int) * face_lists[particel_screen_num].size());
    }
  }

  return(!ok);
}


LGI_VIRTUAL_WIPER_REC::eWIPER_START_MODE map_cdi_wiper_start_mode_to_lgi(eCDI_MEAS_START_TIME_VIA::Enum cdi_start_mode) {
  switch(cdi_start_mode) {
  case eCDI_MEAS_START_TIME_VIA::AfterInitialTransient: 
    return LGI_VIRTUAL_WIPER_REC::START_AFTER_INITIAL_TRANSIENT ;
  case eCDI_MEAS_START_TIME_VIA::RelativeToEmitter: 
    return LGI_VIRTUAL_WIPER_REC::START_AFTER_EMITTER ;
  case eCDI_MEAS_START_TIME_VIA::StartTime: 
    return LGI_VIRTUAL_WIPER_REC::START_AT_TIME;
  default:
    msg_error("A virtual wiper was defined with an unsupported mode for its start time specification.");
  }
}

asINT32 sCP_CDI_READER::read_simple_wiper_models() {
  BOOLEAN ok = TRUE;

  //Check if the user want's to override the wiper start delay
  BOOLEAN force_start = FALSE;
  asINT32 force_start_time;
  cSTRING wiper_start_delay_time = getenv("EXA_WIPER_START_DELAY");
  if(wiper_start_delay_time != NULL) {
    force_start = TRUE;
    force_start_time = atol(wiper_start_delay_time);
    msg_print("Setting all wiper start times to %d timesteps", force_start_time);
  }

  //Read each wiper descriptor from the cdi file and store the data for later
  ccCDI_DO_INNER_CHUNKS(i, "wipr", m_cdi_info) {
    sCDI_WIPR cdi_wiper;
    cdi_read_wipr(m_cdi_info, &cdi_wiper);
    
    if(force_start) {
      cdi_wiper.start_via = eCDI_MEAS_START_TIME_VIA::StartTime;
      cdi_wiper.initial_delay.value = force_start_time;
    }

    cp_particle_sim.m_cdi_simple_wiper_models.push_back(cdi_wiper);
  }
 return !ok;
}

asINT32 sCP_CDI_READER::send_wipers() {

  if(!m_is_trac_present) 
    return TRUE;

  BOOLEAN ok = TRUE;
  //Translate each cdi wiper descriptor into an LGI struct and then send to the SPs

  asINT32 total_variable_size = 0;
  asINT32 num_cdi_wipers = cp_particle_sim.m_cdi_simple_wiper_models.size();
  ccDOTIMES(cdi_wiper_index, num_cdi_wipers) {
    sCDI_WIPR& wipr = cp_particle_sim.m_cdi_simple_wiper_models[cdi_wiper_index];
    auto wiper_blade = wipr.wiper_blade_geoms.ExpandSelection(cp_info.partitions());
    auto wiped_surface = wipr.wiped_surface_geoms.ExpandSelection(cp_info.partitions());
    total_variable_size += wipr.name.length() * sizeof(char);
    total_variable_size += wiper_blade.size() * sizeof(cdiINT32);
    total_variable_size += wiped_surface.size() * sizeof(cdiINT32);
    total_variable_size += wipr.monitors.size() * sizeof(cdiINT32);
  }

  LGI_VIRTUAL_WIPER_HEADER header;
  header.tag.id = LGI_VIRTUAL_WIPER_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_VIRTUAL_WIPER_HEADER)
                                                       + sizeof(LGI_VIRTUAL_WIPER_REC) * cp_particle_sim.m_cdi_simple_wiper_models.size()
                                                       + total_variable_size);
  header.n_wipers = num_cdi_wipers;

  write_header_to_all_sps(header);
  
  ccDOTIMES(wiper_index, cp_particle_sim.m_cdi_simple_wiper_models.size()) {
    sCDI_WIPR &wipr = cp_particle_sim.m_cdi_simple_wiper_models[wiper_index];
    LGI_VIRTUAL_WIPER_REC record;
    std::string wiper_name = wipr.name;
    std::vector<asINT32> wiped_surfaces = wipr.wiped_surface_geoms.ExpandSelection(cp_info.partitions());
    std::vector<asINT32> wiper_parts = wipr.wiper_blade_geoms.ExpandSelection(cp_info.partitions());

    record.name_length = wiper_name.length();
    record.num_wiped_surfaces = wiped_surfaces.size();
    record.num_wiper_blade_parts = wiper_parts.size();

    record.id = wiper_index;
    vcopy(record.pivot_point, wipr.wiper_axis_origin);
    vcopy(record.rotation_axis, wipr.wiper_axis_dir);
    vcopy(record.blade_edge_1, wipr.inner_endpoint);
    vcopy(record.blade_edge_2, wipr.outer_endpoint);
    record.import_angle = wipr.import_angle.value;
    record.initial_angle = wipr.start_angle.value;
    record.final_angle = wipr.end_angle.value;
    if(wipr.stroke_duration.value < 1)
      msg_error("Wiper \"%s\" has a stroke period of less than one timestep.\n", wiper_name.c_str());
    record.stroke_period = wipr.stroke_duration.value;
    record.delay_period = wipr.delay.value;

    //For CDI  versions older than 7.22, these two fields have assumed values that differ from those returned by the CDI API.
    if( cdi_data.major_version < 7 ||
        (cdi_data.major_version  == 7 && cdi_data.minor_version < 22 )) {
      record.start_mode = LGI_VIRTUAL_WIPER_REC::START_AT_TIME;
      record.num_strokes = -1;
    } else {
      record.start_mode = map_cdi_wiper_start_mode_to_lgi(wipr.start_via);
      record.num_strokes = wipr.num_strokes.value;
    }
    record.num_monitors = wipr.monitors.size();
    record.emitter_index = wipr.emitter_index;
    record.emitter_delay = wipr.emitter_delay.value;
    record.initial_delay = wipr.initial_delay.value;
    record.rotate_wiper_with_arm = wipr.wiper_type != eCDI_WIPER_TYPE::Pantograph;

    write_to_all_sps(record);
    write_to_all_sps(wipr.name);
    write_to_all_sps(wiped_surfaces.data(), sizeof(asINT32) * record.num_wiped_surfaces);
    write_to_all_sps(wiper_parts.data(), sizeof(asINT32) * record.num_wiper_blade_parts);
    write_to_all_sps(wipr.monitors.data(), sizeof(asINT32) * record.num_monitors);
  }
  return !ok;
}

void sCP_CDI_READER::read_uddl_chunk() {
  if(!m_is_uddl_present)
    return;
  cCDI_UDDL cdi_uddl_chunk;
  enter_cdi_chunk(CDI_CHUNK_TYPE_UDDL);
  cdi_uddl_chunk.ReadFromCDI(m_cdi_info);
  cp_info.m_dep_properties_info = cdi_uddl_chunk;
//  cp_info.dep_properties().ReadFromCDI(m_cdi_info);
  exit_cdi_chunk();
}

void sCP_CDI_READER::send_data_curves() {

  if(!m_is_uddl_present)
    return;

  asINT32 total_variable_size = 0;
  asINT32 num_curves = cp_info.dep_properties().GetDataCurves().size();
  asINT32 num_ydata = 0;
  ccDOTIMES(curve_index, num_curves) {
    cCDI_UDDS udds = cp_info.dep_properties().GetDataCurves()[curve_index];
    total_variable_size += udds.GetName().length() * sizeof(char);
    total_variable_size += udds.GetXCol().GetData().size() * sizeof(double);
    num_ydata += udds.GetAllYCols().size();
    ccDOTIMES(ydata_index, udds.GetAllYCols().size()) {
      total_variable_size += udds.GetAllYCols()[ydata_index].GetName().length() * sizeof(char);
      total_variable_size += udds.GetAllYCols()[ydata_index].GetData().size() * sizeof(double);
    }
  }
  LGI_DATA_CURVES_HEADER header;
  header.tag.id = LGI_DATA_CURVES_TAG;
  header.tag.length = lgi_pad_and_encode_record_length(sizeof(LGI_DATA_CURVES_HEADER)
                                                       + sizeof(LGI_DATA_CURVES_REC) * num_curves
                                                       + sizeof(LGI_YDATA_REC) * num_ydata
                                                       + total_variable_size);
  header.n_data_curves = num_curves;
  write_header_to_all_sps(header);
  ccDOTIMES(curve_index, num_curves) {
    cCDI_UDDS udds = cp_info.dep_properties().GetDataCurves()[curve_index];
    LGI_DATA_CURVES_REC record;
    std::string curve_name = udds.GetName();
    std::vector<double> x_data = udds.GetXCol().GetData();
    record.name_length = curve_name.length();
    record.num_data_points = udds.GetXCol().GetData().size();
    record.num_ydata = udds.GetAllYCols().size();
    write_to_all_sps(record);
    write_to_all_sps(curve_name);
    write_to_all_sps(x_data.data(), sizeof(double) * record.num_data_points);
    ccDOTIMES(ydata_index, udds.GetAllYCols().size()) {
      LGI_YDATA_REC yrecord;
      yrecord.name_length = udds.GetAllYCols()[ydata_index].GetName().length();
      yrecord.num_ydata_points = udds.GetAllYCols()[ydata_index].GetData().size();
      std::string y_name = udds.GetAllYCols()[ydata_index].GetName();
      std::vector<double> y_data = udds.GetAllYCols()[ydata_index].GetData();
      write_to_all_sps(yrecord);
      write_to_all_sps(y_name);
      write_to_all_sps(y_data.data(), sizeof(double) * yrecord.num_ydata_points);
    }
  }
}

VOID sCP_CDI_READER::read_scas_chunk()
{
  if (!m_is_scas_present)
    return;

  LGI_SCAS_HEADER header;
  header.tag.id = LGI_SCAS_TAG;
  header.tag.length = 0;
  header.n_assignments = 0;

  m_pds.size = 0;
  m_pds.write(&header, sizeof(header));

  asINT32 n_assignments = 0;

  enter_cdi_chunk(CDI_CHUNK_TYPE_SCAS);
  ccCDI_DO_INNER_CHUNKS(i, "scas", m_cdi_info)
  {
    if (cdi_get_type(m_cdi_info) == CDI_CHUNK_TYPE_SCMA)
    {
      sCDI_SCMA scma;
      cdi_read_scma(m_cdi_info, &scma);

      for (auto region_id : scma.regions)
      {
        LGI_SCAS_ASSIGNMENT assignment;
        assignment.region_id = region_id;
        assignment.material_index = scma.material;

        m_pds.write(&assignment, sizeof(assignment));
        n_assignments++;
      }
    }
  }

  if (n_assignments > 0)
  {
    LGI_SCAS_HEADER *h = reinterpret_cast<LGI_SCAS_HEADER *>(m_pds.buffer);
    h->tag.length = lgi_pad_and_encode_record_length(m_pds.size);
    h->n_assignments = n_assignments;
    write_record_to_all_sps(m_pds.buffer);
  }

  exit_cdi_chunk();
}

