# .----------------.  .----------------.  .-----------------. .----------------.  .----------------.  .----------------.  .----------------. 
# | .--------------. || .--------------. || .--------------. || .--------------. || .--------------. || .--------------. || .--------------. |
# | |    ______    | || |  _________   | || | ____  _____  | || |  _________   | || |  _______     | || |     _____    | || |     ______   | |
# | |  .' ___  |   | || | |_   ___  |  | || ||_   \|_   _| | || | |_   ___  |  | || | |_   __ \    | || |    |_   _|   | || |   .' ___  |  | |
# | | / .'   \_|   | || |   | |_  \_|  | || |  |   \ | |   | || |   | |_  \_|  | || |   | |__) |   | || |      | |     | || |  / .'   \_|  | |
# | | | |    ____  | || |   |  _|  _   | || |  | |\ \| |   | || |   |  _|  _   | || |   |  __ /    | || |      | |     | || |  | |         | |
# | | \ `.___]  _| | || |  _| |___/ |  | || | _| |_\   |_  | || |  _| |___/ |  | || |  _| |  \ \_  | || |     _| |_    | || |  \ `.___.'\  | |
# | |  `._____.'   | || | |_________|  | || ||_____|\____| | || | |_________|  | || | |____| |___| | || |    |_____|   | || |   `._____.'  | |
# | |              | || |              | || |              | || |              | || |              | || |              | || |              | |
# | '--------------' || '--------------' || '--------------' || '--------------' || '--------------' || '--------------' || '--------------' |
#  '----------------'  '----------------'  '----------------'  '----------------'  '----------------'  '----------------'  '----------------' 

define_target generic
  host fusionsim
  rpc ssh
  env PATH "/opt/doxygen-1.9.7/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin"
  command emake
end

# generic_postbuild:
# See end of the file

# .----------------.  .----------------.  .-----------------. .----------------.  .----------------.  .----------------.  .----------------. 
# | .--------------. || .--------------. || .--------------. || .--------------. || .--------------. || .--------------. || .--------------. |
# | | _____  _____ | || |     _____    | || | ____  _____  | || |  ________    | || |     ____     | || | _____  _____ | || |    _______   | |
# | ||_   _||_   _|| || |    |_   _|   | || ||_   \|_   _| | || | |_   ___ `.  | || |   .'    `.   | || ||_   _||_   _|| || |   /  ___  |  | |
# | |  | | /\ | |  | || |      | |     | || |  |   \ | |   | || |   | |   `. \ | || |  /  .--.  \  | || |  | | /\ | |  | || |  |  (__ \_|  | |
# | |  | |/  \| |  | || |      | |     | || |  | |\ \| |   | || |   | |    | | | || |  | |    | |  | || |  | |/  \| |  | || |   '.___`-.   | |
# | |  |   /\   |  | || |     _| |_    | || | _| |_\   |_  | || |  _| |___.' / | || |  \  `--'  /  | || |  |   /\   |  | || |  |`\____) |  | |
# | |  |__/  \__|  | || |    |_____|   | || ||_____|\____| | || | |________.'  | || |   `.____.'   | || |  |__/  \__|  | || |  |_______.'  | |
# | |              | || |              | || |              | || |              | || |              | || |              | || |              | |
# | '--------------' || '--------------' || '--------------' || '--------------' || '--------------' || '--------------' || '--------------' |
#  '----------------'  '----------------'  '----------------'  '----------------'  '----------------'  '----------------'  '----------------' 


# The target amd64_vs2017_* will cover all ABI compatible versions.
#
define_target amd64_vs2017_md
  host stoneham
  env NT_BUILD_SERVER stoneham
  env MSVC_OP_DEBUG 0
  env CYGWIN winsymlinks
  env PATH "/usr/local/bin:/usr/local/MSVSx86_2019/Common7/IDE:/usr/local/MSVSx86_2019/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64:/usr/local/MSVSx86_2019/Common7/Tools:/cygdrive/c/Windows/Microsoft.NET/Framework/v4.0.30319:/usr/local/WINKIT_10/bin/10.0.19041.0/x64:/usr/bin:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/x/usr/exa/bin:/cygdrive/x/usr/exa/platforms/cygwin/bin:/usr/lib/lapack"
  env EXA_CLEAN_HOST fusionsim
  env EXA_CLEAN_PATH "/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin"
  env EXA_DEPEND_HOST fusionsim
  env EXA_DEPEND_PATH "/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin"
  command vemake
  shell bash
  shellopt -l
  rpc ssh
end

define_target amd64_vs2013_mt
  host melrose
  env NT_BUILD_SERVER melrose
  # env MSVC_OP_DEBUG 1
  env CYGWIN winsymlinks
  env PATH "/usr/local/bin:/usr/local/MSVSx86_12.0/Common7/IDE/CommonExtensions/Microsoft/TestWindow:/usr/local/MSVSx86_12.0/VSTSDB/Deploy:/usr/local/MSVSx86_12.0/Common7/IDE:/usr/local/MSVSx86_12.0/VC/bin/x86_amd64:/usr/local/MSVSx86_12.0/VC/BIN:/usr/local/MSVSx86_12.0/Common7/Tools:/cygdrive/c/Windows/Microsoft.NET/Framework/v4.0.30319:/cygdrive/c/Windows/Microsoft.NET/Framework/v3.5:/usr/local/MSVSx86_12.0/VC/VCPackages:/usr/local/WINKIT_8.0/bin/x86:/usr/local/MSx86_SDKs/Windows/v7.0A/bin:/usr/bin:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/cygwin/bin:/cygdrive/x/usr/exa/bin:/cygdrive/x/usr/exa/platforms/cygwin/bin:/cygdrive/c/Windows/SUA/common:/cygdrive/c/Windows/SUA/usr/lib:/cygdrive/c/Windows/SUA/usr/lpp/mmfs/win:/usr/lib/lapack"
  env EXA_CLEAN_HOST fusionsim
  env EXA_CLEAN_PATH "/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin"
  env EXA_DEPEND_HOST fusionsim
  env EXA_DEPEND_PATH "/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin"
  command vemake
  shell bash
  shellopt -l
  rpc ssh
end

define_target amd64_vs2017_mt
  host stoneham
  env NT_BUILD_SERVER stoneham
  env MSVC_OP_DEBUG 0
  env CYGWIN winsymlinks
  env PATH "/usr/local/bin:/usr/local/MSVSx86_2019/Common7/IDE:/usr/local/MSVSx86_2019/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64:/usr/local/MSVSx86_2019/Common7/Tools:/cygdrive/c/Windows/Microsoft.NET/Framework/v4.0.30319:/usr/local/WINKIT_10/bin/10.0.19041.0/x64:/usr/bin:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/x/usr/exa/bin:/cygdrive/x/usr/exa/platforms/cygwin/bin:/usr/lib/lapack"
  env EXA_CLEAN_HOST fusionsim
  env EXA_CLEAN_PATH "/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin"
  env EXA_DEPEND_HOST fusionsim
  env EXA_DEPEND_PATH "/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin"
  command vemake
  shell bash
  shellopt -l
  rpc ssh
end

define_target amd64_vs2022_md
  host stoneham
  env NT_BUILD_SERVER stoneham
  env MSVC_OP_DEBUG 0
  env CYGWIN winsymlinks
  env PATH "/usr/local/bin:/usr/local/MSVSx86_2022/Common7/IDE:/usr/local/MSVSx86_2022/VC/Tools/MSVC/14.34.31933/bin/Hostx64/x64:/usr/local/MSVSx86_2022/Common7/Tools:/cygdrive/c/Windows/Microsoft.NET/Framework/v4.0.30319:/usr/local/WINKIT_10/bin/10.0.19041.0/x64:/usr/bin:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/x/usr/exa/bin:/cygdrive/x/usr/exa/platforms/cygwin/bin:/usr/lib/lapack"
  env EXA_CLEAN_HOST fusionsim
  env EXA_CLEAN_PATH "/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin"
  env EXA_DEPEND_HOST fusionsim
  env EXA_DEPEND_PATH "/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin"
  command vemake
  shell bash
  shellopt -l
  rpc ssh
end

define_target amd64_vs2022_mt
  host stoneham
  env NT_BUILD_SERVER stoneham
  env MSVC_OP_DEBUG 0
  env CYGWIN winsymlinks
  env PATH "/usr/local/bin:/usr/local/MSVSx86_2022/Common7/IDE:/usr/local/MSVSx86_2022/VC/Tools/MSVC/14.34.31933/bin/Hostx64/x64:/usr/local/MSVSx86_2022/Common7/Tools:/cygdrive/c/Windows/Microsoft.NET/Framework/v4.0.30319:/usr/local/WINKIT_10/bin/10.0.19041.0/x64:/usr/bin:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/x/usr/exa/bin:/cygdrive/x/usr/exa/platforms/cygwin/bin:/usr/lib/lapack"
  env EXA_CLEAN_HOST fusionsim
  env EXA_CLEAN_PATH "/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin"
  env EXA_DEPEND_HOST fusionsim
  env EXA_DEPEND_PATH "/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin"
  command vemake
  shell bash
  shellopt -l
  rpc ssh
end


#.----------------.  .----------------.  .-----------------. .----------------.  .----------------. 
#| .--------------. || .--------------. || .--------------. || .--------------. || .--------------. |
#| |   _____      | || |     _____    | || | ____  _____  | || | _____  _____ | || |  ____  ____  | |
#| |  |_   _|     | || |    |_   _|   | || ||_   \|_   _| | || ||_   _||_   _|| || | |_  _||_  _| | |
#| |    | |       | || |      | |     | || |  |   \ | |   | || |  | |    | |  | || |   \ \  / /   | |
#| |    | |   _   | || |      | |     | || |  | |\ \| |   | || |  | '    ' |  | || |    > `' <    | |
#| |   _| |__/ |  | || |     _| |_    | || | _| |_\   |_  | || |   \ `--' /   | || |  _/ /'`\ \_  | |
#| |  |________|  | || |    |_____|   | || ||_____|\____| | || |    `.__.'    | || | |____||____| | |
#| |              | || |              | || |              | || |              | || |              | |
#| '--------------' || '--------------' || '--------------' || '--------------' || '--------------' |
# '----------------'  '----------------'  '----------------'  '----------------'  '----------------' 

# Build targets for GCC with RH8
#
# How the gcc11 targets differ from the gcc9 targets:
# * We're pointing at the DS standard tool set in /opt/rh/gcc-toolset-11 to get the new compiler,
#   instead of our own local build.
# * This toolset also includes a slightly newer binutils (ld reports 2.36.1-2.el8) compared to our
#   former build (2.35.1.  So a separate path element for binutils has been dropped.
# * For the moment, presuming that nothing changes for purpose of the QT "moc" utility.
#
define_target amd64_gcc11
  host photon
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/rh/gcc-toolset-11/root/usr/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc11_omp
  host photon
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/rh/gcc-toolset-11/root/usr/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc11_pic
  host photon
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/rh/gcc-toolset-11/root/usr/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

# This is the permanent debug target setting the option DEBUG=1
define_target amd64_gcc11_debug
  host photon
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/rh/gcc-toolset-11/root/usr/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

# This is the permanent debug target setting the option DEBUG=1
define_target amd64_gcc11_pic_debug
  host photon
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/rh/gcc-toolset-11/root/usr/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end


# In the new targets gcc is compiled so that LD_LIBRARY_PATH is not needed to run the compiler or the tools
# Also in PATH all non existing directories were removed
# The combined target for amd64_gcc9 of compilers being compatible with gcc 8.3-9.2 ABI (gcc,clang,icc)

define_target amd64_gcc9
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_omp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end


# Target for near-term RH 8 requirements, as of 3/14/24.  The team isn't ready to move to RH 8 in general,
# so we need to leave amd64_gcc9 and amd64_gcc9_pic where they are (RH 7 and gcc 9).  A second stage move,
# perhaps Aug/Sep time frame or early next year - will change amd64_gcc9 and amd64_gcc9_pic to move to
# RH 8 and a GCC 11.  After that occurs?  This target should be retired.
define_target amd64_gcc9_rh8
  host photon
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.35.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_pic
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

# This is the permanent debug target setting the option DEBUG=1
define_target amd64_gcc9_debug
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

# This is the permanent debug target setting the option DEBUG=1
define_target amd64_gcc9_pic_debug
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end



# BEGIN: This Linux targets have been resurrected to satisfy dist_build!

# The combined intel / gcc4.9.2 target for amd64 (unaligned)
# define_target amd64_linux_na
#   host ion
#   rpc ssh
#   env PATH "/opt/binutils-2.27/bin:/usr/exa/bin:/usr/exaplat/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:/opt/intel/cce/10.1.015/bin"
#   command emake
# end
# END: This Linux targets have been resurrected to satisfy dist_build!

# The combined intel / gcc4.3.2 target for amd64 (aligned)
# define_target amd64_linux
#   host ion
#   rpc ssh
#   env PATH "/opt/binutils-2.27/bin:/opt/gcc-4.3.2/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
#   env LD_LIBRARY_PATH "/fa/sw/llvm/5.0.1-01/gcc-5.1.0/lib64"
#   command emake
# end




#.----------------.  .----------------.           .----------------.  .----------------.  .----------------.  .-----------------. .----------------. 
#| .--------------. || .--------------. |         | .--------------. || .--------------. || .--------------. || .--------------. || .--------------. |
#| | ____    ____ | || |   ______     | |         | |   _____      | || |     _____    | || | _____  _____ | || | ____  _____  | || |  ____  ____  | |
#| ||_   \  /   _|| || |  |_   __ \   | |         | |  |_   _|     | || |    |_   _|   | || ||_   _||_   _|| || ||_   \|_   _| | || | |_  _||_  _| | |
#| |  |   \/   |  | || |    | |__) |  | |         | |    | |       | || |      | |     | || |  | |    | |  | || |  |   \ | |   | || |   \ \  / /   | |
#| |  | |\  /| |  | || |    |  ___/   | |         | |    | |   _   | || |      | |     | || |  | '    ' |  | || |  | |\ \| |   | || |    > `' <    | |
#| | _| |_\/_| |_ | || |   _| |_      | |         | |   _| |__/ |  | || |     _| |_    | || |   \ `--' /   | || | _| |_\   |_  | || |  _/ /'`\ \_  | |
#| ||_____||_____|| || |  |_____|     | |         | |  |________|  | || |    |_____|   | || |    `.__.'    | || ||_____|\____| | || | |____||____| | |
#| |              | || |              | |         | |              | || |              | || |              | || |              | || |              | |
#| '--------------' || '--------------' |         | '--------------' || '--------------' || '--------------' || '--------------' || '--------------' |
# '----------------'  '----------------'           '----------------'  '----------------'  '----------------'  '----------------'  '----------------' 

# The amd64_gcc9_pic target for MPI.
define_target amd64_gcc9_pic_hpmpi
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

#==============================================================#

# The combined gcc9 target for amd64 (aligned) - variant for "HPMPI"
define_target amd64_gcc9_hpmpi
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_omp_hpmpi
  host fusionsim
  rpc ssh
  env PATH "/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_hpmpi_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_nvcc
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_hpmpi_avx_nvcc
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_openmpi_avx_nvcc
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

# The combined intel / gcc4.3.2 target for amd64 (aligned) - variant for "HPMPI"
# amd64_gcc92_64_hpmpi -> amd64_gcc9_hpmpi
define_target amd64_gcc9_hpmpi_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_hpmpi_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

# The combined intel / gcc4.3.2 target for amd64 (aligned) - variant for "HPMPI"
# amd64_gcc92_64_hpmpi -> amd64_gcc9_hpmpi
define_target amd64_gcc9_hpmpi_d39
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_hpmpi_d39_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_hpmpi_d39_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_hpmpi_d39_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

# The combined intel / gcc4.3.2 target for amd64 (aligned) - variant for "HPMPI"
# amd64_gcc92_64_hpmpi -> amd64_gcc9_hpmpi
define_target amd64_gcc9_hpmpi_5g
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_hpmpi_5g_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_hpmpi_5g_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_hpmpi_5g_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

# The combined intel / gcc4.3.2 target for amd64 (aligned) - variant for openmpi
define_target amd64_gcc9_openmpi
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_d39
  host fusionsim
  rpc ssh
  env PATH "/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_d39_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_d39_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_d39_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_5g
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_5g_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_5g_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

define_target amd64_gcc9_openmpi_5g_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64:/fa/sw/mpi_support/053/amd64_linux_openmpi/lib"
  command emake
end

# Intel target for amd64 (non-aligned) - variant for "HPMPI"
define_target amd64_gcc9_na_hpmpi
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  command emake
end

# The combined intel / gcc4.3.2 target for amd64 (aligned) - variant for "SGI MPI"
define_target amd64_gcc9_sgimpi
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_d39
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_d39_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_d39_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_d39_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_5g
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_5g_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_5g_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_sgimpi_5g_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end


# The combined intel / gcc4.3.2 target for amd64 (aligned) - variant for "Intel MPI"
define_target amd64_gcc9_impi
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end

# The combined intel / gcc4.3.2 target for amd64 (aligned) - variant for "Intel MPI"
define_target amd64_gcc9_impi_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_impi_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_impi_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end

# The combined intel / gcc4.3.2 target for amd64 (aligned) - variant for "Intel MPI"
define_target amd64_gcc9_impi_d39
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_impi_d39_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_impi_d39_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end


define_target amd64_gcc9_impi_d39_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end

# The combined intel / gcc4.3.2 target for amd64 (aligned) - variant for "Intel MPI"
define_target amd64_gcc9_impi_5g
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_impi_5g_dp
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc9_impi_5g_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end


define_target amd64_gcc9_impi_5g_dp_avx
  host fusionsim
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/binutils-2.33.1/bin:/opt/gcc-9.2.0/bin:/usr/exaplat/bin:/usr/exa/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:"
	env LD_LIBRARY_PATH "/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/lib:/opt/intel/compilers_and_libraries_2019.5.281/linux/mpi/intel64/libfabric/lib:/opt/gcc-9.2.0/lib64"
  command emake
end


#==============================================================#
# MPI targets for amd64_gcc11

# The combined gcc11 target for amd64 - variant for "HPMPI"
define_target amd64_gcc11_hpmpi
  host photon
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/rh/gcc-toolset-11/root/usr/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc11_omp_hpmpi
  host photon
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/rh/gcc-toolset-11/root/usr/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

define_target amd64_gcc11_impi
  host photon
  rpc ssh
  env PATH "/opt/cmake-3.27.7/bin:/opt/rh/gcc-toolset-11/root/usr/bin:/usr/exaplat/bin:/usr/exa/bin:/bin:/usr/bin:/usr/sbin"
  # LD_LIBRARY_PATH required for moc
  env LD_LIBRARY_PATH "/opt/gcc-9.2.0/lib64"
  command emake
end

# .----------------.  .----------------.  .-----------------. .----------------.  .----------------.  .----------------.  .----------------. 
# | .--------------. || .--------------. || .--------------. || .--------------. || .--------------. || .--------------. || .--------------. |
# | |    ______    | || |  _________   | || | ____  _____  | || |  _________   | || |  _______     | || |     _____    | || |     ______   | |
# | |  .' ___  |   | || | |_   ___  |  | || ||_   \|_   _| | || | |_   ___  |  | || | |_   __ \    | || |    |_   _|   | || |   .' ___  |  | |
# | | / .'   \_|   | || |   | |_  \_|  | || |  |   \ | |   | || |   | |_  \_|  | || |   | |__) |   | || |      | |     | || |  / .'   \_|  | |
# | | | |    ____  | || |   |  _|  _   | || |  | |\ \| |   | || |   |  _|  _   | || |   |  __ /    | || |      | |     | || |  | |         | |
# | | \ `.___]  _| | || |  _| |___/ |  | || | _| |_\   |_  | || |  _| |___/ |  | || |  _| |  \ \_  | || |     _| |_    | || |  \ `.___.'\  | |
# | |  `._____.'   | || | |_________|  | || ||_____|\____| | || | |_________|  | || | |____| |___| | || |    |_____|   | || |   `._____.'  | |
# | |              | || |              | || |              | || |              | || |              | || |              | || |              | |
# | '--------------' || '--------------' || '--------------' || '--------------' || '--------------' || '--------------' || '--------------' |
#  '----------------'  '----------------'  '----------------'  '----------------'  '----------------'  '----------------'  '----------------' 

# generic_postbuild:
# A generic target that will be run at the very end after all compiles.
# This can be used e.g. to create links after compile (links cannot be
# created correctly on Windows / via Samba), or e.g. to automatically
# create the export.mak file at the end.
#
# Please note: This does not work reliable if using the vmake -J option!
#
# The order to build multiple targets per component is determined by the order of this file, so generic_postbuild needs to be the last entry.
define_target generic_postbuild
  host fusionsim
  rpc ssh
  env PATH "/opt/binutils-2.27/bin:/usr/exa/bin:/usr/exaplat/bin:/usr/local/exa/bin:/usr/local/bin:/bin:/usr/bin:/usr/bin/X11:/usr/bsd:/usr/sbin:/opt/intel/cce/10.1.015/bin"
  command emake
end


