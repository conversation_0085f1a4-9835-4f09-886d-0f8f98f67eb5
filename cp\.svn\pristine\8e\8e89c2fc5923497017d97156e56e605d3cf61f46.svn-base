/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Interface to Write LGI files
 *
 * Jim Salem, Exa Corporation 
 * Created Fri Jan 21 1994
 *--------------------------------------------------------------------------*/

#ifndef __WRITE_CP_LGI_H
#define __WRITE_CP_LGI_H

#include "common.h"


// TOC support. This is borrowed and adapted from DISC.

enum eCP_CKPT_FILE_POS {
  CKPT_TOC_POS,
  CKPT_CKPT_INFO_POS,
  CKPT_AUDIT_TRAIL_POS,
  CKPT_FILE_PARENTAGE_POS,
  CKPT_RANDOM_SEED_POS,
  CKPT_EQN_RANDOM_SEED_POS,
  CKPT_TURB_SYNTH_POS,
  CKPT_RADIATION_TM_POS,
  CKPT_CDI_MEAS_WINDOW_POS,
  CKPT_UBLK_SHOB_STATE_POS,
  CKPT_SURFEL_SHOB_STATE_POS,
  CKPT_MEAS_WINDOW_POS,
  CKPT_NIRF_POS,
  CKPT_LRF_POS,
  CKPT_MOVB_POS,
  CKPT_EMITTER_POS,
  CKPT_THERMAL_ACCEL_POS,
  CKPT_FAN_POS,
  CKPT_AVERAGED_CONTACT_POS,
  CKPT_ROTDYN_POS,
  CKPT_MONITORS_POS,
  CKPT_PT_PF_RATIO_POS
};
typedef tENUMERATION<eCP_CKPT_FILE_POS, uINT16> CP_CKPT_FILE_POS;

// Keep this at last CP_CKPT_FILE_POS + 1
const uINT32 N_CKPT_FILE_POS = (CKPT_PT_PF_RATIO_POS + 1);


// File position accessors
//


extern sLGI_POS g_output_ckpt_file_pos[N_CKPT_FILE_POS];
extern sLGI_POS g_input_ckpt_file_pos[N_CKPT_FILE_POS];

/* Writes the current simulation state to a CP format LGI file */
/* Returns TRUE if successful or FALSE if an output error occurred */
BOOLEAN write_full_ckpt(cSTRING filename, TIMESTEP ckpt_timestep, BOOLEAN precious, cSTRING cp_status);

#endif /* __WRITE_CP_LGI_H */

