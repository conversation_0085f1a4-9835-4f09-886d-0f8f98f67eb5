/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#include "timestep_subcycling.h"
#include "common.h"
#include "cp_lattice.h"
#include "cp_info.h"

//----------------------------------------------------------------------------
// create_initial_solver_mask
//----------------------------------------------------------------------------
VOID create_initial_solver_mask()
{
  cp_info.init_solver_mask      = LB_ACTIVE;
  if (cp_info.is_heat_transfer) cp_info.init_solver_mask |= T_PDE_ACTIVE;
  if (cp_info.is_turb)          cp_info.init_solver_mask |= KE_PDE_ACTIVE;
  if (cp_info.is_water_vapor_transport || cp_info.is_uds_transport)        cp_info.init_solver_mask |= UDS_PDE_ACTIVE;
  if (cp_info.is_conduction)    cp_info.init_solver_mask |= CONDUCTION_PDE_ACTIVE;
  if (cp_info.is_particle_solver) cp_info.init_solver_mask |=PARTICLE_ACTIVE;

}

//----------------------------------------------------------------------------
// round_down_time
//----------------------------------------------------------------------------
TIMESTEP round_down_time(dFLOAT t, dFLOAT ct)
{
  return ct * floor(t / ct);
}

//----------------------------------------------------------------------------
// round_up_time
// Round up a signed int to a multiple of a given integer. 
// If the result will overflow a signed int, round down instead.
//----------------------------------------------------------------------------
TIMESTEP round_up_time(dFLOAT t, dFLOAT ct)
{
  const TIMESTEP LARGEST_TIMESTEP = 0x7fffffff;
  dFLOAT rounded_up = ct * ceil(t / ct);

  if (rounded_up <= (dFLOAT)LARGEST_TIMESTEP) 
    return rounded_up;
  else 
    return round_down_time(t,ct);
}  

//----------------------------------------------------------------------------
// assign_solver_timesteps
// TODO: rewrite this code to support new solvers
//----------------------------------------------------------------------------
VOID maybe_adjust_realms_ts_ratio(asINT32 &n_lb_base_steps, asINT32 &n_conduction_base_steps) {
  if (n_conduction_base_steps > n_lb_base_steps) {
    //conduction supercycled
    asINT32 time_step_ratio = n_conduction_base_steps / n_lb_base_steps;
    if (time_step_ratio % 2 == 0) {
      time_step_ratio--;
      n_conduction_base_steps = time_step_ratio * n_lb_base_steps;
      msg_print("Conduction/Flow time step ratio adjusted to be odd (%d)", time_step_ratio);
    }
  } else if (n_conduction_base_steps > n_lb_base_steps) { 
    //flow supercycled
    asINT32 time_step_ratio = n_lb_base_steps / n_conduction_base_steps;
    if (time_step_ratio % 2 == 0) {
      time_step_ratio--;
      n_lb_base_steps = time_step_ratio * n_conduction_base_steps;
      msg_print("Flow/Conduction time step ratio adjusted to be odd (%d)", time_step_ratio);
    }
  }
}

VOID assign_solver_timesteps()
{
  asINT32 n_lb_base_steps = sim_args.n_lb_base_steps;
  asINT32 n_t_base_steps  = sim_args.n_t_base_steps;
  asINT32 n_ke_base_steps = sim_args.n_ke_base_steps;
  asINT32 n_uds_base_steps = sim_args.n_uds_base_steps;
  asINT32 n_conduction_base_steps = sim_args.n_conduction_base_steps;
  asINT32 n_radiation_base_steps = sim_args.n_radiation_base_steps;
  asINT32 n_particle_base_steps = n_lb_base_steps; //Assume for now particle modeling base steps is the same as the lb solver.

  if(n_lb_base_steps < 1) 
    msg_error("n_lb_base_steps(%d) must be a positive integer \n",n_lb_base_steps);
  if(n_t_base_steps  < 1 && cp_info.is_heat_transfer) 
    msg_error("n_t_base_steps(%d) must be a positive integer in a heat transfer case\n",n_t_base_steps);
  if(n_ke_base_steps < 1 && cp_info.is_turb) 
    msg_error("n_ke_base_steps(%d) must be a positive integer in a turbulent case\n",n_ke_base_steps);
  if(n_uds_base_steps < 1 && (cp_info.is_water_vapor_transport || cp_info.is_uds_transport)) 
    msg_error("n_uds_base_steps(%d) must be a positive integer in a defrost model case\n",n_uds_base_steps);
  if(n_conduction_base_steps < 1 && cp_info.is_conduction)
    msg_error("n_conduction_base_steps(%d) must be a positive integer in a conduction case\n",n_conduction_base_steps);
  if(n_radiation_base_steps < 1 && cp_info.is_radiation)
    msg_error("n_radiation_base_steps(%d) must be a positive integer in a radiation case\n",n_radiation_base_steps);
  if(n_particle_base_steps < 1 && cp_info.is_particle_solver) 
    msg_error("n_particle_base_steps(%d) must be a positive integer in a particle modeling case\n", n_particle_base_steps);

  //Enforce odd time-step ratio between realms to keep the alignment between even/odd time steps
  maybe_adjust_realms_ts_ratio(n_lb_base_steps, n_conduction_base_steps);
  
  if (sim_args.gpu) {
    if (n_lb_base_steps != 1 || n_t_base_steps != 1 || n_ke_base_steps != 1 || n_uds_base_steps != 1 || n_particle_base_steps != 1) {
      msg_warn("Supercycling of the turbulence solver is not supported on GPU and will be ignored");
      n_lb_base_steps = 1;
      n_t_base_steps  = 1;
      n_ke_base_steps = 1;
      n_uds_base_steps = 1;
      n_particle_base_steps = 1; 
    }
  }

  //asINT32 n_gcd = gcd(n_lb_base_steps, n_t_base_steps, n_ke_base_steps, n_uds_base_steps, n_conduction_base_steps, n_particle_base_steps);
  n_uds_base_steps = n_lb_base_steps; // For now, assume scalars have the same base step as lb

 if (cp_info.is_turb && cp_info.is_heat_transfer && cp_info.is_conduction) {
    asINT32 n_gcd = gcd(n_lb_base_steps, n_t_base_steps, n_ke_base_steps, n_conduction_base_steps);
    if(n_gcd > 1) {
      n_lb_base_steps /= n_gcd;
      n_t_base_steps  /= n_gcd;
      n_ke_base_steps /= n_gcd;
      n_uds_base_steps /= n_gcd;
      n_particle_base_steps /= n_gcd;
      n_conduction_base_steps /= n_gcd;
    }
  } else if (cp_info.is_turb && cp_info.is_heat_transfer) {  //not cond
    asINT32 n_gcd = gcd(n_lb_base_steps, n_t_base_steps, n_ke_base_steps);
    if(n_gcd > 1) {
      n_lb_base_steps /= n_gcd;
      n_t_base_steps  /= n_gcd;
      n_ke_base_steps /= n_gcd;
      n_uds_base_steps /= n_gcd;
      n_particle_base_steps /= n_gcd;
    }
    n_conduction_base_steps = 1;
  } else if (cp_info.is_heat_transfer && cp_info.is_conduction) {//thermal and conduction and DNS case
    asINT32 n_gcd = gcd(n_lb_base_steps, n_t_base_steps, n_conduction_base_steps);
    if(n_gcd > 1) {
      n_lb_base_steps /= n_gcd;
      n_particle_base_steps /= n_gcd;
      n_uds_base_steps /= n_gcd;
      n_t_base_steps  /= n_gcd;
      n_conduction_base_steps /= n_gcd;
    }
    n_ke_base_steps = 1;
  } else if (cp_info.is_turb) {  //isothermal and turb case, cannot be cond because cond must be with heat_transfer
    asINT32 n_gcd = gcd(n_lb_base_steps, n_ke_base_steps);
    if(n_gcd > 1) {
      n_lb_base_steps /= n_gcd;
      n_ke_base_steps /= n_gcd;
      n_particle_base_steps /= n_gcd;
      n_uds_base_steps /= n_gcd;
    }
    //msg_print("This is an isothermal turbulent case: \n");
    //msg_print("n_lb_base_steps=%d, n_ke_base_steps=%d \n", n_lb_base_steps,n_ke_base_steps);
    n_t_base_steps = 1; //no sense, just to facilitate the later lcm calculation
    n_conduction_base_steps = 1;
  } else if (cp_info.is_heat_transfer) { //thermal and DNS case, not cond
    asINT32 n_gcd = gcd(n_lb_base_steps, n_t_base_steps);
    if(n_gcd > 1) {
      n_lb_base_steps /= n_gcd;
      n_t_base_steps  /= n_gcd;
      n_particle_base_steps /= n_gcd;
      n_uds_base_steps /= n_gcd;
    }
    //msg_print("This is a thermal DNS case: \n");
    //msg_print("n_lb_base_steps=%d, n_t_base_steps=%d \n", n_lb_base_steps, n_t_base_steps);
    n_ke_base_steps = 1; //no sense, just to facilitate the later lcm calculation
    n_conduction_base_steps = 1;
  } else { //isothermal and DNS case
    n_lb_base_steps = 1;
    //msg_print("This is an isothermal DNS case: \n");
    //msg_print("n_lb_base_steps=%d \n", n_lb_base_steps);

    n_t_base_steps = 1; //no sense, just to facilitate the later lcm calculation
    n_ke_base_steps = 1; //no sense, just to facilitate the later lcm calculation 
    n_particle_base_steps = 1;
    n_uds_base_steps =1;
    n_conduction_base_steps = 1;
  } //msg_print("Base steps lb=%d, t=%d, ke=%d, uds=%d, conduction=%d\n", n_lb_base_steps,
  //n_t_base_steps,n_ke_base_steps,n_uds_base_steps,n_conduction_base_steps); 

  cp_info.n_lb_base_steps   = n_lb_base_steps; 
  cp_info.n_t_base_steps    = n_t_base_steps;
  cp_info.n_ke_base_steps   = n_ke_base_steps;
  cp_info.n_uds_base_steps  = n_uds_base_steps;
  cp_info.n_conduction_base_steps  = n_conduction_base_steps;
  cp_info.n_radiation_base_steps = n_radiation_base_steps;
  cp_info.n_particle_base_steps = n_particle_base_steps;
  cp_info.n_user_base_steps = n_lb_base_steps; //user's timer
  cp_info.n_lattice_base_steps = n_lb_base_steps; //base steps per lattice time
  cp_info.restart_base_time = cp_info.time * cp_info.n_user_base_steps; //should be zero since cp_info.start_time=0
}

//----------------------------------------------------------------------------
// get_lighthill_solver_start_time
//----------------------------------------------------------------------------
VOID get_lighthill_solver_start_time()
{
  asINT32 acous_start_time = sim_args.acous_start_time;
  if(acous_start_time >= 0) { //round up acous_start_time to the sync timestep
    if(cp_info.is_heat_transfer) {
      asINT32 coarse_steps = 1<<(cp_info.num_scales -1);
      dFLOAT  acous_start_base_time = acous_start_time * cp_info.n_user_base_steps;  //in base steps

      if(cp_info.is_turb) { //thermal, turbulent case
        dFLOAT lcm_steps = coarse_steps * lcm(cp_info.n_lb_base_steps,
                                              cp_info.n_t_base_steps,
                                              cp_info.n_ke_base_steps,
                                              cp_info.n_uds_base_steps,
                                              cp_info.n_conduction_base_steps); //in base steps
        acous_start_time = round_up_time(acous_start_base_time, lcm_steps) / cp_info.n_user_base_steps; //in user's timestep
      } else {              //thermal, DNS case
        dFLOAT lcm_steps = coarse_steps * lcm(cp_info.n_lb_base_steps, cp_info.n_t_base_steps); //in base steps
        acous_start_time = round_up_time(acous_start_base_time, lcm_steps) / cp_info.n_user_base_steps; //in user's timestep
      }  
    } else
      msg_internal_error("Lighthill switch (-acous_start_time) does not support isothermal case! \n"); 
  }

  cp_info.acous_start_time = acous_start_time;
}

//----------------------------------------------------------------------------
// assign_momentum_freeze_params
//----------------------------------------------------------------------------
VOID assign_momentum_freeze_params()
{
  if (cp_info.freeze_momentum_field) {
    if (cp_info.is_high_subsonic_mach_regime)
      msg_error("Cannot apply velocity freeze solver on high_subsonic_mach_regime.\n");
    if (!cp_info.is_heat_transfer)
      msg_error("Cannot apply velocity freeze solver on isothermal case.\n");

    if (cp_info.n_lb_base_steps != 1 || cp_info.n_ke_base_steps != 1
        || cp_info.n_t_base_steps != 1 || cp_info.n_uds_base_steps != 1)
      msg_error("Cannot apply velocity freeze solver on subcycling. \n");

    if (cp_info.lattice_type != STP_LATTICE_D19)
      msg_error("Cannot apply velocity freeze solver on lattice type %d. \n", cp_info.lattice_type);
  }

  if (cp_info.freeze_momentum_field) {
    asINT32 ct = 1<<(cp_info.num_scales -1); //coarse_steps

    asINT32 freeze_start_time = MOMENTUM_FREEZE_START_COEFF * ct;
    asINT32 freeze_start_time_threshold = MAX(VR_SYNC_COEFF * ct, MAXIMUM_FREEZE_START_TIME);

    if (freeze_start_time > freeze_start_time_threshold) {
      freeze_start_time = round_down_time((dFLOAT)freeze_start_time_threshold,(dFLOAT)ct); //in user's timestep
    }
    cp_info.momentum_freeze_start_time = freeze_start_time;
  } else {
    cp_info.momentum_freeze_start_time = -1;
  }
}

//----------------------------------------------------------------------------
// class to provide realms periodicity to handle time phases
//----------------------------------------------------------------------------

sLCM_STEPS_REALMS::sLCM_STEPS_REALMS() {
  //internal scale with respect to the smallest span needed to respect the ratios between the periodicity of each solver
  //
  //1st STEP: 
  //Compute the reference scale of each realm, noting that:
  //- when multiple solvers are involved in a realm, the scale should be applicable to all of them, i.e.
  //  solver_base_steps/solver_base_steps_ref is equal and a valid integer for all solvers
  //- only one realm is accelerated (the one with the larger base steps), so the realm not accelerated is unscaled, i.e 
  //  m_<realm-subcyled>_scale_ref = 1 and <realm-subcycled>_base_steps = <realm-subcycled>_ref
  //- when can modify the pace at which a realm is processed during the simulation by adjusting its scale with respect
  //  to the reference value (accelerate if realm_scale < realm_scale_ref)
  //- the realm being accelerated cannot be processed at a faster pace than the unscaled realm, which implies that 
  //  realm_base_steps_ref >= other_realm_base_steps
  //- we enforce that the time-step ratio between realms is odd, which we hold by enforcing that
  //  <realm_supercycled>_ref / <realm-subcycled>_ref is >=1 and odd 
  if (cp_info.n_conduction_base_steps > cp_info.n_lb_base_steps) { //conduction supercycled
    m_flow_scale_ref = 1; 
    m_cond_scale_ref = cp_info.n_conduction_base_steps;
  } else if (cp_info.n_conduction_base_steps == cp_info.n_lb_base_steps) { //tightly coupled
    m_flow_scale_ref = 1;
    m_cond_scale_ref = 1;
  } else { // flow supercycled
    // The gcd of all flow-related solvers is the maximum possible value we can use as reference scale
    m_flow_scale_ref = gcd(cp_info.n_lb_base_steps, cp_info.n_t_base_steps, cp_info.n_ke_base_steps, cp_info.n_uds_base_steps);
    /* However, the condition above is not sufficient to satisfy all requirements.
    Noting that the user time-step ratio between flow and condution is odd, we establish the following relations
    \begin{align}
    odd_ratio = flow_base_steps / cond_base_steps\\
    cond_base_steps = even_cond * odd_cond \\
    flow_base_steps = even_flow * odd_flow = even_ref_flow * odd_ref_flow * even_flow_gcd * odd_flow_gcd \\
    even_ref_flow * odd_ref_flow * even_flow_gcd * odd_flow_gcd = even_cond * odd_cond * odd_ratio \\
    \end{align}
    
    First observation we do is that if we want to preserve and odd ratio between lb_ref and cond_ref, we need to include
    all even terms in lb_ref, not on the scale_ref. Furthermore, the ratio should be a valid integer. To enforce this:
    \begin{align}
    \lambda = gcd(odd_flow_gcd, odd_ratio) \\
    odd_flow_gcd = \lambda * odd_flow_gcd_base \\
    odd_ratio = \lambda * odd_ratio_base \\
    \end{align}
    leading to
    \begin{align}
    even_ref_flow*odd_ref_flow*even_flow_gcd*odd_flow_gcd_base * \lambda = even_cond*odd_cond*odd_ratio_base * \lambda
    \end{align}

    This provides an insight into which terms should be assigned to lb_ref and which ones to flow_scale_ref:
    \begin{align}
    lb_ref = even_ref_flow * odd_ref_flow * even_flow_gcd * odd_flow_gcd_base
    lb_scale_ref = \lambda
    \end{align}
    such that
    \begin{align}
    lb_ref / cond_base_steps \\
    = even_ref_flow * even_flow_gcd * odd_ref_flow  * odd_flow_gcd_base / (even_cond * odd_cond) \\
    = (even_ref_flow * even_flow_gcd) * odd_ref_flow  * odd_flow_gcd_base * odd_ratio /  (even_flow * odd_flow) \\
    = even_flow * (odd_ref_flow * odd_flow_gcd) * odd_ratio / (even_flow * odd_flow * \lambda)
    = even_flow * odd_flow * odd_ratio / (even_flow * odd_flow * \lambda)
    = odd_ratio / \lambda
    = odd_ratio_base
    \end{align}
    
    Therefore, in order to determine the actual scale, we need to remove all even components out of flow_gcd and then
    get the gcd between odd_flow_gcd and odd_ratio
    */
    while (m_flow_scale_ref % 2 == 0) m_flow_scale_ref /= 2; //remove even components, that should be part of m_flow_ref to respect odd ratio
    m_flow_scale_ref = gcd(m_flow_scale_ref, cp_info.n_lb_base_steps);
    m_cond_scale_ref = 1;
  }
  //Respects periodicity of even/odd surfels by coarsen the coupled coarsest scale, so keeps the alignment between
  //even and odd timesteps between realms
  m_coupled_scales_factor = (cp_info.coarsest_coupled_scale > 0) 
                            ? (1 << (cp_info.num_scales - cp_info.coarsest_coupled_scale))
                            : (1 << (cp_info.num_scales - 1));
  //Yielding the following realm reference periodicities
  m_lb_ref = cp_info.n_lb_base_steps * m_coupled_scales_factor / m_flow_scale_ref;
  m_flow_ref = lcm(cp_info.n_lb_base_steps, cp_info.n_t_base_steps, cp_info.n_ke_base_steps, cp_info.n_uds_base_steps)
               * m_coupled_scales_factor / m_flow_scale_ref;
  m_cond_ref = cp_info.n_conduction_base_steps * m_coupled_scales_factor / m_cond_scale_ref;
  //Finally, initializes the scales to the reference values, i.e. initialized to a cond_over_flow ratio equal to 1
  m_flow_scale = m_flow_scale_ref;
  m_cond_scale = m_cond_scale_ref;
}

VOID sLCM_STEPS_REALMS::set_ratio(double& cond_over_flow_time_ratio, bool bound_by_subcycled) {
  if (std::abs(cond_over_flow_time_ratio - 1.0) <= 1.0e-06) { //no time acceleration, set scales to reference value
    m_flow_scale = m_flow_scale_ref;
    m_cond_scale = m_cond_scale_ref;
    return;
  }
  //Initializes periodicity of each realm with reference values, needed as first step to determine if we are
  //accelerating flow or conduction.
  //
  //Note that:
  // - the timestep ratio is the inverse of time ratio, i.e. a ratio larger than one implies that time in conduction
  //   advances faster than in flow so traverse less flow timesteps for a given conduction timestep. In other words, for
  //   a given flow time span, we traverse a larger number of conduction timesteps if we are accelerating conduction
  // - requested cond_over_flow_time_ratio is adjusted to ensure an odd timestep ratio between the new sub and
  //   supercycled solver, 
  // - the rounding done preservers the intention of the user, i.e. rounding should allow only higher
  //   acceleration/slowing down values)
  TIMESTEP n_flow_base_steps = m_flow_scale_ref * m_flow_ref;
  TIMESTEP n_cond_base_steps = m_cond_scale_ref * m_cond_ref;
  if ((n_flow_base_steps < n_cond_base_steps) || 
      (n_flow_base_steps == n_cond_base_steps && cond_over_flow_time_ratio < 1.0)) {
    //conduction supercycled
    m_flow_scale = m_flow_scale_ref; //unscaled (should be 1)
    TIMESTEP ref_ratio = n_cond_base_steps / n_flow_base_steps;
    double scale = (double)ref_ratio / cond_over_flow_time_ratio;
    if (scale < 1.0) { //ratio accelerates solver so much that it turns it into subcycled
      if (bound_by_subcycled) { //adjusts scale so solvers become tightly coupled
        scale = 1.0;
      } else {
        scale = 1.0 / (std::ceil((1.0/scale + 1.0)/2.0) * 2.0 - 1.0);
      }
    } else {
      if (cond_over_flow_time_ratio >= 1.0) { //ratio accelerates solver, needs to round down
        scale = std::floor((scale + 1.0)/2.0) * 2.0 - 1.0;
      } else { //ratio slows down, needs to round up
        scale = std::ceil((scale + 1.0)/2.0) * 2.0 - 1.0;
      }
    }
    m_cond_scale = std::round(scale * (double)n_flow_base_steps) / m_cond_ref;
    //Updates the ratio with the correct value used
    cond_over_flow_time_ratio = (double)m_cond_scale_ref / (double)m_cond_scale;
  } else {
    //flow supercycled: accelerated with the inverse of the ratio
    m_cond_scale = m_cond_scale_ref; //unscaled (should be 1)
    TIMESTEP ref_ratio = n_flow_base_steps / n_cond_base_steps;
    double scale = (double)ref_ratio * cond_over_flow_time_ratio;
    if (scale < 1.0) { //ratio accelerates solver so much that it turns it into subcycled
      if (bound_by_subcycled) { //adjusts scale so solvers become tightly coupled
        scale = 1.0;
      } else {
        scale = 1.0 / (std::ceil((1.0/scale + 1.0)/2.0) * 2.0 - 1.0);
      }
    } else {
      if (cond_over_flow_time_ratio <= 1.0) { //ratio accelerates solver, needs to round down
        scale = std::floor((scale + 1.0)/2.0) * 2.0 - 1.0;
      } else { //ratio slows down, needs to round up
        scale = std::ceil((scale + 1.0)/2.0) * 2.0 - 1.0;
      }
    }
    m_flow_scale = std::round(scale * (double)n_cond_base_steps) / m_flow_ref;
    //Updates the ratio with the correct value used
    cond_over_flow_time_ratio = (double)m_flow_scale / (double)m_flow_scale_ref;
  }
}

