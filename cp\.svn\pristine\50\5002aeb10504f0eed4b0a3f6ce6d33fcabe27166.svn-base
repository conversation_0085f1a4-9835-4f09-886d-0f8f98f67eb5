/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#ifndef __TABLE_H
#define __TABLE_H

#include "common.h"

struct sCDI_MEAS_WINDOW; // forward reference

// Number of iterations for Laplacian smoothing of missing table data
#define DEFAULT_VTABLE_ITERATIONS 10

enum TABLE_LOCATION {
  TABLE_LOCATION_INVALID = -1,
  TABLE_LOCATION_SEARCH_PATH = 0,
  TABLE_LOCATION_CDI_RELATIVE = 1,
  TABLE_LOCATION_CDI_ABSOLUTE = 2,
  TABLE_LOCATION_RUNDIR = 3,
  TABLE_LOCATION_TABLE_SUBDIR = 4
};

typedef struct sTABLE_DESC {
  STRING name;				// Name assigned to table in PowerCASE
  sINT32 table_index;                   // Table index into cp_info.table_descs for ordering messages
  STRING filename;                   	// Next file to read (initial name provided in PowerCASE)
  STRING abs_filename;                  // Absolute pathname for next file to read (initial name provided in PowerCASE)
  STRING index;				// The last integer in filename, including leading zeros
  sINT32 cdi_flags;                     // table flags
  cBOOLEAN read_during_sim;             // Re-read the table during simulation
  TIMESTEP first_interval;
  TIMESTEP period;        		// Period after the first interval
  TIMESTEP end_time;
  TIMESTEP next_periodic_read;		// Initialized to first_interval, and incremented by period
  WALLCLOCK_TIME_SECS last_file_check_time;

  sINT32 n_variables;			// The number of variables in the table
  UNITS_UNIT *variable_units;		// The units of each variable

  sCDI_MEAS_WINDOW *cdi_meas_window;	// Non-NULL if table read tied to meas window write
  sINT32 first_frame;			// First meas frame to read after
  sINT32 num_meas_frames_between_reads;
  cSTRING cmd_string;			// Command to issue after meas frame is written (before table read)
  cBOOLEAN has_cmd_been_issued;		// If TRUE, command has been issued so table can be read

  sINT32 n_tables_read;		// How many times has table been read from file (not counting CDI file)

  // This is the global index among all hxs including condenser, heat exchanger and amesim heat exchanger.
  sINT32 hx_index;

  sTABLE_DESC() {hx_index = -1;}

} *TABLE_DESC;

typedef struct sTABLE_QUEUE_ENTRY {
  TABLE_DESC 			table_desc;
  cBOOLEAN 			waiting_msg_issued;
  // Store a read time here distinct from the table desc read time to allow for
  // async requests via exasignal.
  TIMESTEP			read_time;
  // If vtable is not NULL, the table has been read by the CP, and the CP is simply
  // waiting to transfer the table to all SPs.
  EXPRLANG_VALUE 		vtable;
  // next process to test
  STP_PROC			next_sp_to_test;
  struct sTABLE_QUEUE_ENTRY	*next;
} *TABLE_QUEUE_ENTRY;
  


typedef struct sCP_TABLE_COMM_DESC {
  enum { N_REQUESTS = 3 };
  sCP_TABLE_COMM_DESC() {
    for(int i=0; i<N_REQUESTS; i++) {
      mpi_requests[i] = MPI_REQUEST_NULL;
    }
  }

  // These send requests are used to track the progress of the 3 chunks
  // of a table sent to an SP
  MPI_Request    mpi_requests[N_REQUESTS];
} *CP_TABLE_COMM_DESC;

BOOLEAN is_table_file_present(cSTRING table_filename, 
    			      cSTRING abs_table_filename, 
			      char table_location[PLATFORM_MAXPATHLEN]);
FILE *open_table_file(cSTRING table_filename, 
     		      cSTRING abs_table_filename, 
		      char table_location[PLATFORM_MAXPATHLEN]);

BOOLEAN does_table_filename_include_digit(cSTRING name);

VOID increment_table_filename(STRING name, asINT32 inc, STRING index_return);

VOID maybe_read_tables();
VOID maybe_wait_for_tables_at_end();

VOID initialize_table_queue();


#endif /* __TABLE_H */
