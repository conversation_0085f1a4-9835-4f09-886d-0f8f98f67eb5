/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#include "cdi_common.h"

CIO_CCCC	cdi_get_type(CDI_INFO cdi_info) {
  return(cio_get_type(cdi_info->cio_info));
}

cSTRING		cdi_get_filename(CDI_INFO cdi_info) {
  return(cio_filename(cdi_info->cio_info));
}

asINT32		cdi_get_major_version(CDI_INFO cdi_info) {
  return(cdi_info->major_version);
}

asINT32		cdi_get_minor_version(CDI_INFO cdi_info) {
  return(cdi_info->minor_version);
}

cSTRING		cdi_get_error_state_string(asINT32 errval) {
  switch(errval) {
    case CDI_ERR_OK:				return("no error on operation");
    case CDI_ERR_OPEN_READ_FAILED:		return("can find file to open for read");
    case CDI_ERR_OPEN_READ_NOT_CDI:		return("file not CDI format");
    case CDI_ERR_OPEN_READ_CDI_INCOMPLETE:	return("file was not completed");
    case CDI_ERR_OPEN_READ_ADVANCED_VERSION:	return("file version beyond known limits");
    case CDI_ERR_OPEN_READ_CDI_DAMAGED:		return("file appears damaged");
    case CDI_ERR_OPEN_WRITE_FAILED:		return("can not open file for write");
    case CDI_ERR_OPEN_WRITE_UNK_VERSION:	return("can not open version beyond known limits");
    case CDI_ERR_BAD_STRUCT:	                return("file read failed because of unexpected structure");

    default: {
      static char buf[256];
      sprintf(buf, "error value %d not recognized", errval);
      return(buf);
    }
  }
}

