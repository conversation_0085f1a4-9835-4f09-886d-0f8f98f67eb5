/* ~~~CO<PERSON>Y<PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Unpublished Work Copyright (C) 1995, 1994 Exa Corporation.            ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129                                                      ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#include "cdi_common.h"
#include "lexer.h"

static asINT32 lineno;

static VOID update_lineno (cSTRING text)
{
  const CHARACTER *next_char = text;
  while (*next_char != '\0') {
    if (*next_char == '\n')
      lineno++;
    next_char++;
  }
  return;
}

VOID lexer_set_lineno (asINT32 new_lineno)
{
  lineno = new_lineno;
  return;
}

asINT32 lexer_get_lineno (void)
{
  return(lineno);
}

/*** This function must be provided to the lexer. */
extern "C" int cdi_yywrap (void)
{
  return(TRUE);
}

extern "C" {
#include LEX_YY_C
}

idFLOAT lexer_parse_dfloat (void)
{
  if (cdi_yylex() != LEXER_TOKEN_IS_NUMBER) {
    msg_error("Error at line %d: expected a floating point number", lexer_get_lineno());
  }

  {
    double result;
    sscanf(cdi_yytext, " %lf", &result);

    return((idFLOAT)result);
  }
}

static BOOLEAN number_is_int (cSTRING number)
{
  /** If we only have the chars +, -, or a digit, it's an int. */
  /** The definition of strspn(s, set) says that if every character in s is found in set,
   ** strspn returns the length of s.  Hence the predicate below.
   */
  return(strspn(number, "+-0123456789") == strlen(number));
}

asINT32 lexer_parse_int (void)
{
  if (cdi_yylex() != LEXER_TOKEN_IS_NUMBER) {
    msg_error("Error at line %d: expected an integer", lexer_get_lineno());
  }

  if (!(number_is_int(cdi_yytext))) {
    msg_error("Error at line %d: expected an integer", lexer_get_lineno());
  }

  {
    int result;
    sscanf(cdi_yytext, " %d", &result);

    return((asINT32)result);
  }
}

BOOLEAN lexer_parse_int_or_rbrace(asINT32* value)
{
  BOOLEAN retval = FALSE;

  LEXER_TOKEN_TYPE type = cdi_yylex();
  if (type ==  LEXER_TOKEN_IS_RBRACE)
    retval = FALSE;
  else if (type == LEXER_TOKEN_IS_NUMBER) {
    if (!(number_is_int(cdi_yytext)))
      msg_error("Error at line %d: expected an integer", lexer_get_lineno());
    else {
      sscanf(cdi_yytext, " %d", value);
      retval = TRUE;
    }
  }
  else
    msg_error("Error at line %d: expected an integer", lexer_get_lineno());

  return retval;
}

asINT64 lexer_parse_int64 (void)
{
  if (cdi_yylex() != LEXER_TOKEN_IS_NUMBER) {
    msg_error("Error at line %d: expected an integer", lexer_get_lineno());
  }

  if (!(number_is_int(cdi_yytext))) {
    msg_error("Error at line %d: expected an integer", lexer_get_lineno());
  }

  {
    asINT64 result;
    sscanf(cdi_yytext, " %" SCALAR_PRINTF_INT64 "d", &result);

    return((asINT64)result);
  }
}


static VOID copy_and_unquotify_unslashify (STRING dest, cSTRING src)
{
  CHARACTER *next_dest_char = dest;
  const CHARACTER *next_src_char = src + 1; /* +1 to skip the leading quote */

  /** The second clause of the while predicate is there just for safety; the src
   ** string should always end with a quote.
   */
  while (*next_src_char != '"' && *next_src_char != '\0') {
    if (*next_src_char == '\\')
      next_src_char++;
    *next_dest_char = *next_src_char;
    next_dest_char++;
    next_src_char++;
  }
  *next_dest_char = '\0';
  return;
}

STRING lexer_parse_string (void)
{
  if (cdi_yylex() != LEXER_TOKEN_IS_STRING) {
    msg_error("Error at line %d: expected a string", lexer_get_lineno());
  }

  {
    STRING result = (STRING)exa_malloc(strlen(cdi_yytext)+1, "parse_string", "a new string");

    copy_and_unquotify_unslashify(result, cdi_yytext);

    return result ;
  }
}


std::string lexer_parse_std_string()
{
  STRING cstr = lexer_parse_string();
  std::string result = cstr;
  exa_free(cstr);
  return result;
}

#if 0
static BOOLEAN has_slashes(cSTRING src)
{
  while (*src != '"' && *src != '\0') {
    if (*src == '\\')
      return TRUE;
    src++;
  }
  return FALSE;
}

// Avoid copies, but terminates string early
std::string lexer_parse_std_string()
{
  std::string result;

  if (cdi_yylex() != LEXER_TOKEN_IS_STRING) {
    msg_error("Error at line %d: expected a string", lexer_get_lineno());
  }
  
  if (has_slashes(cdi_yytext)) {
    STRING cstr = (STRING)exa_malloc(strlen(cdi_yytext)+1, "parse_string", "a new string");
    copy_and_unquotify_unslashify(cstr, cdi_yytext);
    result = cstr;
    exa_free(cstr);
  } else {
    // Avoid call to lexer_parse_string, which makes a copy,
    // because the strings produced by encryption are very large
    // Skip begin and end quotes
    result = std::string(cdi_yytext[1],strlen(cdi_yytext)-2);
  }
  return result;
}
#endif
VOID lexer_parse_lbrace (void)
{
  if (cdi_yylex() != LEXER_TOKEN_IS_LBRACE) {
    msg_error("Error at line %d: expected a left brace", lexer_get_lineno());
  }

  return;
}

VOID lexer_parse_rbrace (void)
{
  if (cdi_yylex() != LEXER_TOKEN_IS_RBRACE) {
    msg_error("Error at line %d: expected a right brace", lexer_get_lineno());
  }

  return;
}

BOOLEAN lexer_parse_n_char_id_or_rbrace (STRING result, asINT32 n_chars)
{
  LEXER_TOKEN_TYPE type = cdi_yylex();

  if (type == LEXER_TOKEN_IS_RBRACE)
    return(FALSE);
  else if (type == LEXER_TOKEN_IS_ID &&
	   (asINT32)strlen(cdi_yytext) == n_chars) {
    strcpy(result, cdi_yytext);
    return(TRUE);
  }
  else {
    printf("Lexer type is: %d\n", type);
    msg_error("Error at line %d: expected a %d-character identifier or a right brace",
	      lexer_get_lineno(), n_chars);
    /** Msg_error won't actually return; the following statement is just to avoid a 
     ** warning from the C compiler.
     */
    return(FALSE);
  }
}

BOOLEAN lexer_parse_string_or_rbrace (STRING* result)
{
  LEXER_TOKEN_TYPE type = cdi_yylex();
  if (type == LEXER_TOKEN_IS_RBRACE)
    return FALSE;
  else if (type == LEXER_TOKEN_IS_STRING) {
    *result = (STRING)exa_malloc(strlen(cdi_yytext)+1, "parse_string", "a new string");
    copy_and_unquotify_unslashify(*result, cdi_yytext);
    return TRUE;
  }
  else {
    msg_error("Error at line %d: expected a string or a right brace", lexer_get_lineno());
    /** Msg_error won't actually return; the following statement is just to avoid a 
     ** warning from the compiler.
     */
    return FALSE;
  }
}

BOOLEAN lexer_parse_dfloat_or_rbrace (idFLOAT &result)
{
  LEXER_TOKEN_TYPE type = cdi_yylex();
  if (type == LEXER_TOKEN_IS_RBRACE) {
    return FALSE;
  } else if (type == LEXER_TOKEN_IS_NUMBER) {
    sscanf(cdi_yytext, " %lf", &result);
    return TRUE;
  } else {
    msg_error("Error at line %d: expected a floating point number or a right brace",
              lexer_get_lineno());
    /** Msg_error won't actually return; the following statement is just to avoid a 
     ** warning from the compiler.
     */
    return FALSE;
  }
}

VOID lexer_parse_specific_id (CIO_CCCC desired_chunk)
{
  if (cdi_yylex() == LEXER_TOKEN_IS_ID &&
      (strlen(cdi_yytext) == 4) &&
      (CIO_MAKECCCC(cdi_yytext) == desired_chunk)) {
    return;
  } else {
    char *str = (char *) &desired_chunk;
    msg_error("Error at line %d: expected `%c%c%c%c' (got `%s')\n",
	      lexer_get_lineno(),
	      str[SCALAR_UINT32_BYTE(0)],
	      str[SCALAR_UINT32_BYTE(1)],
	      str[SCALAR_UINT32_BYTE(2)],
	      str[SCALAR_UINT32_BYTE(3)],
	      cdi_yytext);
  }
  return;
}

VOID lexer_set_scan_string(STRING buffer)
{
  cdi_yy_scan_string(buffer);
}
