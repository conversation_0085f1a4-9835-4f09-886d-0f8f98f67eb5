/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2022, 1994-2021 Dassault Systemes Simulia Corp.         ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Simulia Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Simulia Corp.                                                         ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#ifndef HEAT_EXCHANGERS_H
#define HEAT_EXCHANGERS_H

#include "common.h"

VOID initialize_heat_exchanger_and_condenser_data();
VOID free_heat_exchanger_and_condenser_storage();
VOID write_heat_exchanger_coupling(BOOLEAN is_from_ckpt);
VOID process_any_heat_exchangers_and_condensers(BOOLEAN is_from_ckpt);
VOID sync_series_heat_exchanger_meas_windows();
VOID sync_condenser_meas_windows();
VOID process_hx_monitors(asINT32 hx_index, 
                         TIMESTEP signal_time, 
                         TIMESTEP next_signal_time);
VOID remove_future_lines_from_powercool_results_file(cSTRING results_filename);
VOID convert_hxch_name_slash_to_underscore(STRING old_name, STRING new_name);
VOID convert_hxch_part_name_to_base_assembly_name(char*& old_name);

#endif 

