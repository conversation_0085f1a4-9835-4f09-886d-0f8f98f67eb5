# ~~~COPYWRITE~~~+ boxcomment("fx1.copyright", "78")
##############################################################################
### Copyright (C) 2000 Exa Corporation.                                    ###
### All Rights Reserved.                                                   ###
### US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                     ###
###        5,640,335; 5,848,260; 5,910,902; 5,953,239                      ###
### UK FR DE Pat 0 538 415                                                 ###
###                                                                        ###
### This computer program is the property of Exa Corporation and contains  ###
### its confidential trade secrets.  Use, examination, copying, transfer   ###
### and disclosure to others, in whole or in part, are prohibited except   ###
### with the express prior written consent of Exa Corporation.             ###
###                                                                        ###
### RESTRICTED RIGHTS LEGEND                                               ###
###                                                                        ###
### Use, duplication, or disclosure is subject to restrictions stated in   ###
### Contract No. DABT63-93-C-0010 with Exa Corporation.                    ###
##############################################################################
# ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78")

override TARGET_LINK_OPTS+=Advapi32.lib

%.RES:	../generic.rc
	rc /fo $@ /d APPNAME=$* /d CREATION_YEAR=2005 /d CURRENT_YEAR=$(shell date +%Y) ../generic.rc

TARGET_OBJECTS = cdi_tools.RES

TARGET_BUILDS=dump_cdi undump_cdi dump_cdi_do_not_release undump_cdi_do_not_release

include ../master.mak
# DO NOT DELETE THIS LINE -- make depend depends on it.
