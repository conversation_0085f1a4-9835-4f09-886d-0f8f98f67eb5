/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#ifndef _CDI_GET_H
#define _CDI_GET_H

#include CIO_H

#define CDI_READ_MODE	CIO_READ_MODE
#define CDI_WRITE_MODE	CIO_WRITE_MODE

#include <set>

CDI_INFO	cdi_open_for_read(const char *filename, cdiINT32 *user_err_ptr);
CDI_INFO	cdi_open_for_write(const char *filename,
				   cdiINT32 major_version, cdiINT32 minor_version,
				   cdiINT32 *user_err_ptr,
                                   BOOLEAN use_large_file_offsets=FALSE);
void		cdi_close(CDI_INFO cdi);
asINT32         cdi_rewind(CDI_INFO cdi_info);

cdiINT32	cdi_get_origin(const char *filename, double origin[3]);
double		cdi_get_meters_per_cell(const char *filename);
cdiINT32	cdi_get_n_scales (const char *filename);

int             cdi_get_solver_version(asINT32 simv_flags);

std::string     cdi_get_pflow_compatibility_string(cdiINT32 major_version,
                                                   cdiINT32 minor_version,
                                                   asINT32 simv_flags);

/**
 *   cdi_find_and_read_ptge
 *
 *   Reads PTGE chunk if present in file
 *   On first read, will create a CIPHER
 *   On second read, will not as CIPHER already exists
 *
 *   rewind: if true, rewind is called before to start search from top of file
 *           and after to reposition parser to top of file
 **/
asINT32         cdi_find_and_read_ptge(CDI_INFO cdi_info, bool rewind);

/**
 *   Scans for given types and if found add them to the types_found set
 *   Does not recurse
 *   If start position is the top of the CDI file (the "case" chunk)
 *   this will return all the top level chunks in the case
 */                                                   
void cdi_find_chunks(CDI_INFO cdi_info, 
                     std::set<CIO_CCCC> &types_to_find, std::set<CIO_CCCC> &types_found);


asINT32 cdi_descend_in_chunk(CDI_INFO cdi_info, CIO_CCCC type);
asINT32 cdi_ascend_out(CDI_INFO cdi_info);

#endif /* !_CDI_GET_H */



