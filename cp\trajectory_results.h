/* ~~~COPY<PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#ifndef _TRAJECTORY_RESULTS_H
#define _TRAJECTORY_RESULTS_H

//The templates in this file, in conjunction with complementary code in simeng, define a class that
//facillitates moving three types of trajectory data from the SP to the CP so that the data may be
//written to a measurement file.  Trajectory vertices, trajectory hitpoints, and instances of new
//parcel creation (refered to as trajectory startpoints) are the three types this is used for.

#include PHYSTYPES_H
#include CP_H

#include <string>
#include <typeinfo>
#include "common.h"
#include "cp_info.h"
#include "trajectory_window.h"

#define CONSISTENCY_CHECK 1

extern sINT32 g_total_pri_emitters;                       // includes PRI's special "merge" emitter
extern volatile asINT32 g_num_unprocessed_startpoints;

extern std::vector<sINT32> g_window_to_trajectory_window; // maps from meas window index to trajectory window index
extern std::vector<CP_TRAJECTORY_WINDOW> g_trajectory_windows;

#if TRAJECTORY_DATA_MSG_INCLUDES_HEADER_ELEMENT
const asINT32 FIRST_NON_HEADER_INDEX = 1;
#else
const asINT32 FIRST_NON_HEADER_INDEX = 0;
#endif

extern std::vector<TRAJECTORY_ID> g_n_parcels_first_measured_on_sp; // g_n_parcels_first_measured_on_sp[n_sps][n_windows][n_emitters]

inline TRAJECTORY_ID &n_parcels_first_measured_on_sp(asINT32 sp, asINT32 window, asINT32 emitter)
{ return g_n_parcels_first_measured_on_sp[(sp * g_trajectory_windows.size() + window) * g_total_pri_emitters + emitter]; }


typedef class sCP_TRAJECTORY_ID_MAP {
 private:
  bool m_remap_in_progress;
  TIMESTEP m_current_start_timestep;             // start of current interval for which IDs are remapped
  std::vector<TRAJECTORY_ID> m_base_global_ids;  // m_base_global_ids[t][sp][window][emitter]
  std::vector<TRAJECTORY_ID> m_next_available_global_index;   // m_next_available_global_index[window][emitter]
  std::vector<TRAJECTORY_ID> m_send_buffer;

 public:
  TIMESTEP current_start_timestep() { return m_current_start_timestep; }

  TRAJECTORY_ID &base_global_id(TIMESTEP t, asINT32 sp, asINT32 window, asINT32 emitter)
    { return m_base_global_ids[ (((t - m_current_start_timestep) * total_sps + sp) * g_trajectory_windows.size() + window)
                                * g_total_pri_emitters + emitter ]; }

  TRAJECTORY_ID &next_available_global_index(asINT32 window, asINT32 emitter)
    { return m_next_available_global_index[ window * g_total_pri_emitters + emitter ]; }

  VOID initialize() {
    m_remap_in_progress = (cp_info.restart_time % TRAJECTORY_ID_MAP_SEND_PERIOD > 0
                           && cp_info.restart_time % TRAJECTORY_ID_MAP_SEND_PERIOD <= TRAJECTORY_ID_MAP_RECV_DELAY);

    m_current_start_timestep = ((cp_info.restart_time - TRAJECTORY_ID_MAP_RECV_DELAY)
                                / TRAJECTORY_ID_MAP_SEND_PERIOD * TRAJECTORY_ID_MAP_SEND_PERIOD);
    m_next_available_global_index.resize(g_trajectory_windows.size() * g_total_pri_emitters, 0);

    m_base_global_ids.resize((TRAJECTORY_ID_MAP_SEND_PERIOD + TRAJECTORY_ID_MAP_RECV_DELAY)
                             * total_sps * g_trajectory_windows.size() * g_total_pri_emitters, 0);
    m_send_buffer.resize(TRAJECTORY_ID_MAP_SEND_PERIOD * total_sps * g_trajectory_windows.size() * g_total_pri_emitters, 0);
  }

  BOOLEAN maybe_send_base_global_ids_to_sps(BOOLEAN force = FALSE); // force is set to true in some circumstances on
  //ckpt restart
  BOOLEAN maybe_discard_sent_base_global_ids();
  BOOLEAN remap_in_progress() {return m_remap_in_progress;}
  VOID write_ckpt();
  VOID write_ckpt(sCKPT_BUFFER& buff);
  size_t ckpt_len();
  VOID read_ckpt(LGI_STREAM ckpt_lgi_stream, asINT32 total_ckpt_sps);
  VOID read_ckpt(sCKPT_BUFFER& ckpt_buff, asINT32 total_ckpt_sps);
  TRAJECTORY_ID base_global_id_size() const {return m_base_global_ids.size();}
  TRAJECTORY_ID base_global_id_at(size_t pos) const
  { 
    assert(pos < m_base_global_ids.size());
    return m_base_global_ids.at(pos);
  }
  TRAJECTORY_ID next_available_global_index_size() const {return m_next_available_global_index.size();}
  TRAJECTORY_ID next_available_global_index_at(size_t pos) const
  {
    assert(pos < m_next_available_global_index.size());
    return m_next_available_global_index.at(pos);
  }
} *CP_TRAJECTORY_ID_MAP;

extern std::vector<sINT32> g_window_emitter_children_cumulative_counts;
extern std::vector<sINT32> g_window_emitter_parent_cumulative_counts;
//extern std::vector<sINT32> g_window_emitter_states_cumulative_counts;

extern sCP_TRAJECTORY_ID_MAP g_trajectory_id_map;
BOOLEAN trajectory_data_is_waiting_for_results_thread();

//extern pthread_mutex_t  g_trajectory_manager_exclude;

typedef class sCP_TRAJECTORY_MANAGER_ORDER {
 private:
  asINT32 m_data_type_priority;
 public:

  volatile BOOLEAN m_buffer_ready;
  volatile TIMESTEP m_buffered_timestep;
  volatile TIMESTEP m_max_buffered_timestep;
  asINT32 m_completed_timestep;
  sCP_TRAJECTORY_MANAGER_ORDER() {
    m_buffered_timestep = 0;
    m_max_buffered_timestep = -1;
    m_completed_timestep = -1;
    m_buffer_ready = FALSE;
  }

  virtual TIMESTEP compute_timestep_range() = 0;
  TIMESTEP max_buffered_timestep() const {return m_max_buffered_timestep; }

  BOOLEAN buffer_ready() const { return m_buffer_ready; }
  TIMESTEP buffered_timestep() const { return m_buffered_timestep; }
  TIMESTEP completed_timestep() const { return m_completed_timestep; }
  asINT32 priority() const { return m_data_type_priority;}
  virtual asINT32 receive_counts(asINT32 sp) = 0;
  VOID set_data_type_priority(asINT32 priority) { m_data_type_priority = priority;}
  virtual VOID write_receive_buffers_to_pri() = 0;

  bool operator < (sCP_TRAJECTORY_MANAGER_ORDER const &other_manager) const {
    //Define the order in which two manager's buffers must be written to file.

    if( this->max_buffered_timestep() < other_manager.max_buffered_timestep())
      return TRUE; //This manager must go before the other.

    if(this->max_buffered_timestep() > other_manager.max_buffered_timestep())
      return FALSE;  //The other manager's buffer must be processed before this manager's buffer

    //If the buffers are for the same timestep, then the datatype with higher priority must go first
    if(this->priority() > other_manager.priority())
      return TRUE; //This must go before the other.

    return FALSE; //The other manager must go before this one.
  }
}*CP_TRAJECTORY_MANAGER_ORDER;

template <typename DATA_TYPE>
class sCP_TRAJECTORY_MANAGER : public sCP_TRAJECTORY_MANAGER_ORDER
{
 private:

  TIMESTEP m_timestep;
  volatile sINT64 m_total_received;  // for a timestep
  sINT32 m_next_sp_to_probe;
  sINT32 m_next_sp_to_complete_recv;

  std::vector<sINT32> m_receive_counts;
  std::vector<DATA_TYPE *> m_receive_buffers;
  std::vector<MPI_Request> m_receive_requests;

  // The next 3 vectors are element counts that are filled in every timestep after all data is received from the SPs.
  std::vector<sINT32> m_per_window_per_emitter_element_counts;  // m_per_window_per_emitter_element_counts[n_windows][n_emitters]
  sINT32 &per_window_per_emitter_element_count(asINT32 window, asINT32 emitter)
    { return m_per_window_per_emitter_element_counts[g_total_pri_emitters * window + emitter]; }

  std::vector<sINT32> m_window_emitter_cumulative_counts;
  sINT32 &window_emitter_cumulative_count(asINT32 window, asINT32 emitter)
    { return m_window_emitter_cumulative_counts[g_total_pri_emitters * window + emitter]; }

  std::vector<sINT32> m_window_emitter_start_indices;
  sINT32 &window_emitter_start_index(asINT32 window, asINT32 emitter)
    { return m_window_emitter_start_indices[g_total_pri_emitters * window + emitter]; }

  const char* data_type_name() {
    static std::string name = typeid(DATA_TYPE).name();
    return name.c_str();
  }

  // This vector is used to keep track of how many children table entries have already been written to the file on a per emitter, per window basis.
  //std::vector<sINT32> m_window_emitter_children_cumulative_counts;
  sINT32 &window_emitter_children_cumulative_counts( asINT32 window, asINT32 emitter)
    {return g_window_emitter_children_cumulative_counts[g_total_pri_emitters * window + emitter]; }

  // This vector is used to keep track of how many parent table entries have already been written to the file on a per emitter, per window basis.
  //std::vector<sINT32> m_window_emitter_parent_cumulative_counts;
  sINT32 &window_emitter_parent_cumulative_counts( asINT32 window, asINT32 emitter)
    {return g_window_emitter_parent_cumulative_counts[g_total_pri_emitters * window + emitter]; }

  // This vector is used to keep track of how many state table entries have already been written to the file on a per emitter, per window basis.
  //std::vector<sINT32> m_window_emitter_states_cumulative_counts;
  //  sINT32 &window_emitter_states_cumulative_counts( asINT32 window, asINT32 emitter)
  //    {return g_window_emitter_states_cumulative_counts[g_total_pri_emitters * window + emitter]; }


  VOID count_parcels_per_window_and_emitter();
  VOID update_local_to_global_parcel_id_map()  { }; // this is overridden for sTRAJECTORY_STARTPOINT
  std::size_t parcel_global_id(DATA_TYPE &element, cSTRING record_type);

 public:

  sCP_TRAJECTORY_MANAGER() {
    m_total_received = 0;
    m_next_sp_to_probe = 0;
    m_next_sp_to_complete_recv = 0;

    asINT32 priority = -1;
    if (typeid(DATA_TYPE) == typeid(sTRAJECTORY_STARTPOINT))
      priority = 2;
    if (typeid(DATA_TYPE) == typeid(sTRAJECTORY_VERTEX))
      priority = 1;
    if (typeid(DATA_TYPE) == typeid(sTRAJECTORY_HITPOINT))
      priority = 0;
    if(priority == -1)
      msg_error("Unknown trajectory measurement datatype referenced.\n");
    set_data_type_priority(priority);
  }

  ~sCP_TRAJECTORY_MANAGER() { }

  TIMESTEP next_timestep_to_output() { return m_timestep; }
  TIMESTEP compute_timestep_range();

  VOID initialize()
  {
    m_timestep = cp_info.restart_time;


    m_per_window_per_emitter_element_counts.resize(g_trajectory_windows.size() * g_total_pri_emitters, 0);
    m_window_emitter_cumulative_counts.resize(g_trajectory_windows.size() * g_total_pri_emitters, 0);
    m_window_emitter_start_indices.resize(g_trajectory_windows.size() * g_total_pri_emitters, 0);
    g_window_emitter_children_cumulative_counts.resize(g_trajectory_windows.size() * g_total_pri_emitters, 0);
    g_window_emitter_parent_cumulative_counts.resize(g_trajectory_windows.size() * g_total_pri_emitters, 0);
    //    g_window_emitter_states_cumulative_counts.resize(g_trajectory_windows.size() * g_total_pri_emitters, 0);

    m_receive_counts.resize(total_sps, 0);
    m_receive_buffers.resize(total_sps, NULL);
    m_receive_requests.resize(total_sps, MPI_REQUEST_NULL);
    // Can't initialze MPI data types in the class constructor since there is no guarantee MPI_Init has been called yet.
    DATA_TYPE::define_mpi_type();
  }

  // This method is used to ensure that startpoints for a timestep are processed before
  // vertices and hitpoints. This is necessary because the local_to_global_parcel_id_map is
  // updated as the result of processing the startpoints, and then used when processing the
  // the vertices and hitpoints.
  bool ready_to_write_to_pri(); //After fixing PR43284, this function name is a poor choice.
  VOID maybe_receive_sp_data();
  VOID write_receive_buffers_to_pri(); //This is not thead safe, only use from the IO thread.
  asINT32 receive_counts(asINT32 sp) {return m_receive_counts[sp];}
  VOID assign_all_global_ids();
};

template < typename DATA_TYPE >
inline VOID sCP_TRAJECTORY_MANAGER < DATA_TYPE >::maybe_receive_sp_data()
{

  //First check that write_sri_results_thread is done processing previously received results.
  //This is needed as a solution to PR43284 which was caused by a thread saftey issue in the HDF5 lib.
  //This function will now defer receiving more data from SPs until the write_sri_results_thread has called
  //write_receive_buffers_to_pri() which should empty and deallocate previously buffered messages.

  if(m_buffer_ready) {
    return; //flag is set false by the results thread after previous data was passed to PRI.
  }

  //Don't receive any data from outside the map time range until other managers can catch up and remapping can complete.
  TIMESTEP remap_done_time = g_trajectory_id_map.current_start_timestep() +
    TRAJECTORY_ID_MAP_SEND_PERIOD + TRAJECTORY_ID_MAP_RECV_DELAY;
  if(m_timestep >= remap_done_time)
    return;

  // Probe for messages from all SPs and post a receive for each once its "message envelope" is
  // available, which includes the message size.
  ccDO_FROM_BELOW(sp, m_next_sp_to_probe, total_sps) {
    // Check for message from next SP
    int flag;
    MPI_Status status;
    MPI_Iprobe(sp, DATA_TYPE::mpi_tag(), eMPI_sp_cp_comm, &flag, &status);

    // If not available, exit loop
    if (!flag)
      break;
    m_next_sp_to_probe++;

    // Extract the actual message size
    int actual_count;
    MPI_Get_count(&status, DATA_TYPE::m_eMPI_data_type, &actual_count);
    m_total_received += actual_count - FIRST_NON_HEADER_INDEX; // don't count the header element
    m_receive_counts[sp] = actual_count;

    // Allocate a receive buffer to accomodate message
    if (actual_count > 0)
      m_receive_buffers[sp] = xnew DATA_TYPE[actual_count];

    // Post receive
    MPI_Irecv(m_receive_buffers[sp], actual_count, DATA_TYPE::m_eMPI_data_type, sp, DATA_TYPE::mpi_tag(),
              eMPI_sp_cp_comm, &m_receive_requests[sp]);
  }

  // Complete all the receives
  ccDO_FROM_BELOW(sp, m_next_sp_to_complete_recv, m_next_sp_to_probe) {
    int receive_flag;
    MPI_Status receive_status;
    MPI_Test(&m_receive_requests[sp], &receive_flag, &receive_status);

    if (!receive_flag)
      break;
    m_next_sp_to_complete_recv++;
  }

  // Return if all receives have not been completed
  if (m_next_sp_to_complete_recv != total_sps)
    return;

  //if (!ready_to_write_to_pri())
  //  return;

  // If we reached this point, a message from every SP has been received

  if (m_total_received > 0) {

    m_max_buffered_timestep = compute_timestep_range();
#if 0
    if (typeid(DATA_TYPE) == typeid(sTRAJECTORY_STARTPOINT)) {
      msg_print("CP: startpoint data received from SP at timestep %d ( %d elements, max_timestep %d ).\n", m_timestep, m_total_received, m_max_buffered_timestep);
    }

    if (typeid(DATA_TYPE) == typeid(sTRAJECTORY_HITPOINT)) {
      msg_print("CP: hitpoint data received from SP at timestep %d ( %d elements, max_timestep %d ).\n", m_timestep, m_total_received, m_max_buffered_timestep);
    }

    if (typeid(DATA_TYPE) == typeid(sTRAJECTORY_VERTEX)) {
      msg_print("CP: vertex data received from SP at timestep %d ( %d elements, max_timestep %d ).\n", m_timestep, m_total_received, m_max_buffered_timestep);
    }
#endif


    count_parcels_per_window_and_emitter();
    update_local_to_global_parcel_id_map(); // only does something for startpoints
    //assign_all_global_ids(); moved to result thread

    //For PR43284, this was moved to the write_sri_results_thread which is expected to consume the receive buffers and set them to NULL when complete.
    //write_receive_buffers_to_pri();

    if (typeid(DATA_TYPE) == typeid(sTRAJECTORY_STARTPOINT)) {
      g_num_unprocessed_startpoints = m_total_received; //This is needed to prevent vertices and hitpoints being passed to PRI before their corresponding startpoints (PR43284).
    }

    //Set a flag to tell the results thread there is data to write.
    m_buffered_timestep = m_timestep;
    m_buffer_ready = TRUE;

  } else {
    m_completed_timestep = m_timestep;
    m_buffered_timestep = m_timestep; //the buffer is empty, but it's for this timestep.
    m_max_buffered_timestep = m_buffered_timestep;
  }

  // Prepare for next timestep
  m_next_sp_to_probe = 0;
  m_next_sp_to_complete_recv = 0;
  m_timestep++;
}

template < typename DATA_TYPE >
inline TIMESTEP sCP_TRAJECTORY_MANAGER < DATA_TYPE >::compute_timestep_range()
{
  asINT32 max_timestep = -1;
  ccDOTIMES(sp, total_sps) {
    DATA_TYPE *recv_buffer = m_receive_buffers[sp];
    ccDO_FROM_BELOW(i, FIRST_NON_HEADER_INDEX, m_receive_counts[sp]) {
      DATA_TYPE &element = recv_buffer[i];
      if(element.timestep > max_timestep)
        max_timestep = element.timestep;
    }
  }
  return max_timestep;
}

template < typename DATA_TYPE >
inline VOID sCP_TRAJECTORY_MANAGER < DATA_TYPE >::count_parcels_per_window_and_emitter()
{
  asINT32 n_emitters = g_total_pri_emitters;

  //Set all counts to 0.
  m_per_window_per_emitter_element_counts.assign(m_per_window_per_emitter_element_counts.size(), 0);

  // Tally the element count per emitter and window
  ccDOTIMES(sp, total_sps) {
    DATA_TYPE *recv_buffer = m_receive_buffers[sp];
    ccDO_FROM_BELOW(i, FIRST_NON_HEADER_INDEX, m_receive_counts[sp]) {  // Skip the header element
      DATA_TYPE &element = recv_buffer[i];
      asINT32 orig_window_index = element.window_index;

#if CONSISTENCY_CHECK
      asINT32 first_measured_sp = element.sp_index;
      if (orig_window_index < 0 || orig_window_index >= g_window_to_trajectory_window.size())
        msg_internal_error("Trajectory original window index (%d) out of range for SP %d at timestep %d. First measured timestep=%d, First measured SP=%d, Emitter ID=%d, Local ID=%d",
                           orig_window_index, sp, m_timestep, element.ts_index, first_measured_sp, element.emitter_id, element.order_in_ts);
#endif

      element.window_index = g_window_to_trajectory_window[orig_window_index];
#if CONSISTENCY_CHECK
      if (element.window_index < 0 || element.window_index >= g_trajectory_windows.size())
        msg_internal_error("Trajectory window index out of range (%d --> %d) for SP %d at timestep %d. First measured timestep=%d, First measured SP=%d, Emitter ID=%d, Local ID=%d",
                           orig_window_index, element.window_index, sp, m_timestep, element.ts_index, first_measured_sp, element.emitter_id, element.order_in_ts);
      if (element.emitter_id < 0 || element.emitter_id >= g_total_pri_emitters)
        msg_internal_error("Trajectory window emitter index (%d) out of range for SP %d at timestep %d. First measured timestep=%d, First measured SP=%d, Local ID=%d",
                           element.emitter_id, sp, m_timestep, element.ts_index, first_measured_sp, element.order_in_ts);
#if 0 /* This misbehaves if the simulation is resumed on a smaller number of SPs */
      if (first_measured_sp < 0 || first_measured_sp >= total_sps)
        msg_internal_error("Trajectory window first measured SP (%d) out of range for SP %d at timestep %d. First measured timestep=%d, Emitter ID=%d, Local ID=%d",
                           first_measured_sp, sp, m_timestep, element.ts_index, element.emitter_id, element.order_in_ts);
#endif
#endif
      ++per_window_per_emitter_element_count(element.window_index, element.emitter_id);
    }
  }
  // Convert the element counts to cumulative form, which are the initial offset in a vector
  // that will house all the elements
  sINT32 cumulative_count = 0;
  ccDOTIMES(i, m_window_emitter_cumulative_counts.size()) {
    m_window_emitter_cumulative_counts[i] = cumulative_count;
    cumulative_count += m_per_window_per_emitter_element_counts[i];
  }
}

#if 0
extern asINT32 debug_buffer_size;
extern asINT32 debug_buffer_capacity;
extern asINT32 debug_buffer_timestep;
extern sTRAJECTORY_STARTPOINT* debug_buffer;
#endif

template <>
inline VOID sCP_TRAJECTORY_MANAGER<sTRAJECTORY_STARTPOINT>::update_local_to_global_parcel_id_map()
{
  g_n_parcels_first_measured_on_sp.assign(g_n_parcels_first_measured_on_sp.size(), 0); // clear counters


  //msg_print("CP: updating local to global map with new startpoints from timesteps %d to %d.\n", m_timestep, m_max_buffered_timestep);


  // Count the number of parcels born on each SP for each emitter
  ccDO_FROM_BELOW(timestep, m_timestep, m_max_buffered_timestep + 1) {
    ccDOTIMES(sp, total_sps) {
      sTRAJECTORY_STARTPOINT *recv_buffer = m_receive_buffers[sp];

#if 0
      if(m_receive_counts[sp]) {
        if(debug_buffer_capacity < m_receive_counts[sp]  ) {
          if(debug_buffer)
            delete [] debug_buffer;
          debug_buffer_capacity = std::max( 2 * debug_buffer_capacity, m_receive_counts[sp]);
          debug_buffer  = new sTRAJECTORY_STARTPOINT[ debug_buffer_capacity];

        }

        memcpy(debug_buffer, recv_buffer, m_receive_counts[sp]);
        debug_buffer_size = m_receive_counts[sp];
        debug_buffer_timestep = m_timestep;
      }
#endif

      ccDO_FROM_BELOW(receive_buffer_index, FIRST_NON_HEADER_INDEX, m_receive_counts[sp]) {  // Skip the header element
        sTRAJECTORY_STARTPOINT &element = recv_buffer[receive_buffer_index];
        if(element.timestep == timestep) {
          asINT32 first_measured_sp = element.sp_index;
          asINT32 window = element.window_index;
          asINT32 emitter = element.emitter_id;
          n_parcels_first_measured_on_sp(first_measured_sp, window, emitter)++;
          receive_buffer_index += element.num_subrecords; //Skip over records used to encode the parent information.
        }
      }
    }


    // Update translation table from local to global IDs
    ccDOTIMES(window, g_trajectory_windows.size()) {
      ccDOTIMES(emitter, g_total_pri_emitters) {
        ccDOTIMES(sp, total_sps) { // must be inner loop
          g_trajectory_id_map.base_global_id(timestep, sp, window, emitter) = g_trajectory_id_map.next_available_global_index(window, emitter);
          if(n_parcels_first_measured_on_sp(sp, window, emitter) > 0) {
            g_trajectory_id_map.next_available_global_index(window, emitter) += n_parcels_first_measured_on_sp(sp, window, emitter);
          }
        }
      }
    }
  }
}

template < typename DATA_TYPE >
inline std::size_t sCP_TRAJECTORY_MANAGER < DATA_TYPE >::parcel_global_id(DATA_TYPE &element, cSTRING record_type)
{
  TIMESTEP first_measured_ts = element.ts_index;
  if (first_measured_ts >= 0) {
    asINT32 first_measured_sp = element.sp_index;
    asINT32 emitter = element.emitter_id;
    asINT32 window = element.window_index;

#if CONSISTENCY_CHECK
    if (first_measured_ts < g_trajectory_id_map.current_start_timestep()
        || first_measured_ts >= (g_trajectory_id_map.current_start_timestep() + TRAJECTORY_ID_MAP_SEND_PERIOD + TRAJECTORY_ID_MAP_RECV_DELAY))
      msg_internal_error("Trajectory window first measured timestep (%d) out of range at timestep %d."
                         " First measured SP=%d, Window=%d, Emitter ID=%d, Local ID=%d (%s record)",
                         first_measured_ts, m_timestep, first_measured_sp, window, emitter, element.order_in_ts, record_type);
#endif
    TRAJECTORY_ID base_id = g_trajectory_id_map.base_global_id(first_measured_ts, first_measured_sp, window, emitter);
    if (base_id < 0)
      msg_internal_error("Invalid base global ID %d for local parcel ID at timestep %d."
                         " First measured timestep=%d, First measured SP=%d, Window=%d, Emitter ID=%d, Local ID=%d (%s record), element_ts %d",
                         base_id, m_timestep, first_measured_ts, first_measured_sp, window, emitter, element.order_in_ts, record_type, element.timestep);
#if CONSISTENCY_CHECK
    asINT32 next_sp = first_measured_sp + 1;
    TRAJECTORY_ID next_base_id;
    if (next_sp < total_sps)
      next_base_id = g_trajectory_id_map.base_global_id(first_measured_ts, next_sp, window, emitter);
    else {
      TIMESTEP next_ts = first_measured_ts + 1;
      if (next_ts < (g_trajectory_id_map.current_start_timestep() + TRAJECTORY_ID_MAP_SEND_PERIOD + TRAJECTORY_ID_MAP_RECV_DELAY))
        next_base_id = g_trajectory_id_map.base_global_id(next_ts, 0, window, emitter);
      else
        next_base_id = g_trajectory_id_map.next_available_global_index(window, emitter);
    }

    //if (element.order_in_ts < 0 || element.order_in_ts >= next_base_id)
    //  msg_internal_error("Local parcel ID (%d) out of range at timestep %d."
    //                     " First measured timestep=%d, First measured SP=%d, Window=%d, Emitter ID=%d (%s record)",
    //                     element.order_in_ts, m_timestep, first_measured_ts, first_measured_sp, window, emitter, record_type);
#endif

    element.order_in_ts += base_id; //The element is modified as part of the fix for PR43284 so that subsequent calls from the IO thread don't need to access the ID map.
    element.ts_index = -1;
    return element.order_in_ts;
  } else {
    // already converted to global ID
    return element.order_in_ts;
  }
}

template < typename DATA_TYPE >
inline VOID sCP_TRAJECTORY_MANAGER < DATA_TYPE >::assign_all_global_ids()
{
  //Assign each element in the receive buffer a global ID so that the ID map is not modified by the IO thread as it may be in the process of updating at the time.
  ccDOTIMES(sp, total_sps) {
    DATA_TYPE *recv_buffer = m_receive_buffers[sp];
    ccDO_FROM_BELOW(receive_buffer_index, FIRST_NON_HEADER_INDEX, m_receive_counts[sp]) {  // Skip the header element
      DATA_TYPE &element = recv_buffer[receive_buffer_index];
      std::size_t parcel_id = parcel_global_id(element, typeid(DATA_TYPE).name()); //parcel_global_id modifies the element so subsequent calls don't need the ID map.
    }
  }
}

template <  >
inline VOID sCP_TRAJECTORY_MANAGER < sTRAJECTORY_STARTPOINT >::assign_all_global_ids()
{
  //This method is specialized for startpoints because if the element is a startpoint for a merge event and has
  //subrecords, the emitter ID of the subrecords needs to be set to the reference emitter id before the global id is
  //assigned.  This can't be done upstream because the trajectory manager needs to sort incomming data by emitter id and
  // keep corresponding subrecords next to each other.
  ccDOTIMES(sp, total_sps) {
    sTRAJECTORY_STARTPOINT *recv_buffer = m_receive_buffers[sp];
    asINT32 remaining_merge_subrecords = 0;
    ccDO_FROM_BELOW(receive_buffer_index, FIRST_NON_HEADER_INDEX, m_receive_counts[sp]) {  // Skip the header element
      sTRAJECTORY_STARTPOINT &element = recv_buffer[receive_buffer_index];

      if(remaining_merge_subrecords > 0) {
        element.emitter_id = element.reference_emitter_id; //change the emitter type for all merge events to the actual emitter id (and not the parent's)
        remaining_merge_subrecords--;
      }

      if(element.event_type == EVENT_MERGE && remaining_merge_subrecords == 0) {
        remaining_merge_subrecords = element.num_subrecords; //Trap merge events so the subsequent records can have their emitter IDs set properly.
      }

      std::size_t parcel_id = parcel_global_id(element, typeid(sTRAJECTORY_STARTPOINT).name()); //parcel_global_id modifies the element so subsequent calls don't need the ID map.
    }
  }
}



// Write_receive_buffers_to_pri is specialized for every data type.
template <>
inline VOID sCP_TRAJECTORY_MANAGER<sTRAJECTORY_VERTEX>::write_receive_buffers_to_pri()
{

  if ( !m_buffer_ready)
    return;
#if 0
  if(g_num_unprocessed_startpoints)
    return;  //Never attempt to write vertices to a file while there are outstanding startpoints that haven't been added yet.
#endif

  //msg_print("CP: writing vertex data at timestep %d with %d records (max buffered timestep %d).\n", m_buffered_timestep, m_total_received, m_max_buffered_timestep);



  assign_all_global_ids();


  PRI::cPARTICLE_TRACE_VERTEX *pri_vertices = xnew PRI::cPARTICLE_TRACE_VERTEX[ m_total_received ];

  //Added to support new PRI scheme for updating particle states.
  std::vector<PRI::cPARTICLE_UPDATE>* pri_state_updates = cnew std::vector<PRI::cPARTICLE_UPDATE>[g_trajectory_windows.size() * g_total_pri_emitters ]; //accumulate state updates into seperate vectors for each window and emitter.
  ccDOTIMES(state_update_block, g_trajectory_windows.size() * g_total_pri_emitters ) {
    pri_state_updates[state_update_block].reserve(m_total_received); //This is overkill but easy to implement.
  }

  //PRI::cPARTICLE_UPDATE *pri_state_updates = xnew PRI::cPARTICLE_UPDATE[ m_total_received ];
  //std::size_t* pri_state_block_lengths = cnew std::size_t[g_trajectory_windows.size() * g_total_pri_emitters]; //this is fixed size so should be made a member.
  //std::size_t  pri_state_count = 0;

  memcpy(m_window_emitter_start_indices.data(), m_window_emitter_cumulative_counts.data(), sizeof(sINT32) * g_trajectory_windows.size() * g_total_pri_emitters);

  // Copy the receive buffers into pri_vertices sorted by emitter, window index, and SP. Sorting by
  // SP is only necessary to ensure a repeatable order in the PRI file. A bucket sort is used.
  ccDOTIMES(sp, total_sps) {
    sTRAJECTORY_VERTEX *recv_buffer = m_receive_buffers[sp];

    ccDO_FROM_BELOW(receive_buffer_index, FIRST_NON_HEADER_INDEX, m_receive_counts[sp]) {  // Skip the header element

      sTRAJECTORY_VERTEX &element = recv_buffer[receive_buffer_index];

      asINT32 index = window_emitter_start_index(element.window_index, element.emitter_id)++;
      if(element.event_type == EVENT_EMITTED) {
        //If the particle is just emitted, this vertex is the position on this timestep, otherwise its the vertex at the
        // beginning of the next timestep.
        pri_vertices[index].SetTimeStep(m_buffered_timestep);
      } else {
        //All the emitted events must be ordered first naturally so as to not break the chronological order of the vertex records.
        pri_vertices[index].SetTimeStep(m_buffered_timestep + 1);
      }

      //Enable trajectory measurments for thermal particles.
#if 0
      pri_vertices[index].SetDiameter(element.diameter);
      pri_vertices[index].SetTemperature(element.temperature);
#endif
      PRI::VECTOR3F position_vector( element.x[0], element.x[1], element.x[2] );
      pri_vertices[index].SetPosition(position_vector);
      PRI::VECTOR3F velocity_vector( element.v[0], element.v[1], element.v[2] );
      pri_vertices[index].SetVelocity(velocity_vector);
      std::size_t parcel_id = parcel_global_id(element, "vertex");
      pri_vertices[index].SetParticleIndex(parcel_id);

      //If the event type is EVENT_EXITED_WINDOW, this is the last vertex of the trace because the particle has left the window.
      //If it reenters, it will be treated as a different particle with a new global ID.
      if(element.event_type == EVENT_EXITED_WINDOW) {

        ////std::size_t parcel_id = parcel_global_id(element);
        PRI::EMITTER_TYPE emitter_type = g_simulator_emitter_id_to_pri_emitter_types[ element.emitter_id ];
        asINT32 pri_emitter_id = g_simulator_to_pri_emitter_ids[ element.emitter_id ];
        CP_TRAJECTORY_WINDOW window = g_trajectory_windows[element.window_index];

        PRI::cPARTICLE_UPDATE state_update;
        state_update.SetParticleIndex(parcel_id);
        state_update.SetParticleState(PRI:: PARTICLE_STATE_EXITED_WINDOW);
        //state_update.SetChildIndices(); //traces that end becasue the parcel left the window do not have any children.
        state_update.SetHitSurfaceIndex(-1);
        state_update.SetHitSurfaceType(PRI::HIT_POINT_SURFACE_TYPE_UNDEFINED);
        state_update.SetDeathTimeStep(m_buffered_timestep + 1);  //plus one because were dealing with the state computed for the beginning of the next timestep.
        pri_state_updates[g_total_pri_emitters * element.window_index + element.emitter_id].push_back(state_update);
      }
    }

    if (m_receive_counts[sp] > 0) {
      // Free receive buffer
      delete[] recv_buffer;
      m_receive_buffers[sp] = NULL;
    }
  }

  ccDOTIMES(window_index, g_trajectory_windows.size()) {
    CP_TRAJECTORY_WINDOW window = g_trajectory_windows[window_index];
    ccDOTIMES(emitter_index, g_total_pri_emitters) {
      asINT32 n_vertices = per_window_per_emitter_element_count(window_index, emitter_index);
      if (n_vertices > 0) {
        //Get the starting index to the block of pri_vertices for this window and this emitter.
        sINT32 start_vertex = window_emitter_cumulative_count(window_index, emitter_index);
        PRI::EMITTER_TYPE emitter_type = g_simulator_emitter_id_to_pri_emitter_types[emitter_index];
        asINT32 pri_emitter_id = g_simulator_to_pri_emitter_ids[emitter_index];

        // Write the block of vertices for this window and emitter to the trajectory file.
        window->pmr_file().WriteParticleTraceVertices(pri_vertices + start_vertex, n_vertices, emitter_type, pri_emitter_id, m_buffered_timestep);

        // If there are any particle states that need updating, add them to the trajectory file.
        asINT32 num_state_updates = pri_state_updates[g_total_pri_emitters * window_index + emitter_index].size();
        if( num_state_updates > 0) {
          window->pmr_file().WriteParticleUpdates(pri_state_updates[g_total_pri_emitters * window_index + emitter_index],
                                                  emitter_type, pri_emitter_id);
        }
      }
    }
  }
  //msg_print("Wrote %d vertices from timestep %d.\n", m_total_received, m_buffered_timestep);
  delete[] pri_vertices;
  delete[] pri_state_updates;
  m_total_received = 0;
  m_buffer_ready = FALSE;
  m_completed_timestep = m_buffered_timestep;
}


template <>
inline VOID sCP_TRAJECTORY_MANAGER<sTRAJECTORY_HITPOINT>::write_receive_buffers_to_pri()
{

  if ( !m_buffer_ready)
    return;

  //msg_print("CP: hitpoint data ready for PRI processed at timestep %d.\n", m_buffered_timestep);
#if 0
  if(g_num_unprocessed_startpoints) {
    return; //Never attempt to write hitpoints to a file while there are outstanding startpoints that haven't been added yet.
  }
#endif

  assign_all_global_ids();

  //Allocate an array of PRI hitpoint objects that will be populated by some of the simualtor hitpoint records in
  //the receive buffer.
  PRI::cPARTICLE_HIT_POINT *pri_hitpoints = xnew PRI::cPARTICLE_HIT_POINT[ m_total_received ];
  //Not all of the above elements will be needed since some of the hitpoint messages received from the SP may be control records used to
  //encode the ID's of a parcel's children.  Control records are detected when the num_subrecords field is >= 0 and following the control records, there
  //may be an arbitrary number of "subrecords". The parel ID information in the subrecords contain the IDs of the children.

  //Added to support new PRI scheme for updating particle states and child indices.
  std::vector<PRI::cPARTICLE_UPDATE>* pri_state_updates = cnew std::vector<PRI::cPARTICLE_UPDATE>[g_trajectory_windows.size() * g_total_pri_emitters ]; //accumulate state updates into seperate vectors for each window and emitter.
  ccDOTIMES(state_update_block, g_trajectory_windows.size() * g_total_pri_emitters ) {
    pri_state_updates[state_update_block].reserve(m_total_received); //This is overkill but easy to implement.
  }

  std::vector<PRI::cPARTICLE_CHILD>* pri_children = cnew std::vector<PRI::cPARTICLE_CHILD>[g_trajectory_windows.size() * g_total_pri_emitters ]; //accumulate children updates into seperate vectors for each window and emitter.
  ccDOTIMES(state_children_block, g_trajectory_windows.size() * g_total_pri_emitters ) {
    pri_children[state_children_block].reserve(m_total_received); //This is overkill but easy to implement.
  }

  memcpy(m_window_emitter_start_indices.data(), m_window_emitter_cumulative_counts.data(), sizeof(sINT32) * g_trajectory_windows.size() * g_total_pri_emitters);


  // Copy the receive buffers into pri_hitpoints sorted by emitter, window index, and SP. Sorting by
  // SP is only necessary to ensure a repeatable order in the PRI file. A bucket sort is used.
  ccDOTIMES(sp, total_sps) {
    sTRAJECTORY_HITPOINT *recv_buffer = m_receive_buffers[sp];

    //Go through the receive buffer and create pri hitpoint objects and parse the split events.
    ccDO_FROM_BELOW(receive_buffer_index, FIRST_NON_HEADER_INDEX, m_receive_counts[sp]) {  // Skip the header element
      sTRAJECTORY_HITPOINT &element = recv_buffer[receive_buffer_index];
      std::size_t parcel_id = parcel_global_id(element, "hit point");
      PRI::EMITTER_TYPE emitter_type = g_simulator_emitter_id_to_pri_emitter_types[ element.emitter_id ];
      asINT32 pri_emitter_id = g_simulator_to_pri_emitter_ids[ element.emitter_id ];
      CP_TRAJECTORY_WINDOW window = g_trajectory_windows[element.window_index];

      asINT32 num_subrecords = element.num_subrecords;
      asINT32 first_child_index = 0;
      asINT32 last_child_index = 0;

      //Determine the correct PRI hitpoint type, surface type, and state given the simulator's event type:
      PRI::HIT_POINT_SURFACE_TYPE pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_UNDEFINED;
      PRI::HIT_POINT_TYPE pri_hitpoint_type = PRI::HIT_POINT_TYPE_UNDEFINED;
      PRI::PARTICLE_STATE pri_parent_particle_state = PRI::PARTICLE_STATE_UNDEFINED;
      asINT32 hitpoint_surface_index = element.surface_index;
      switch(element.event_type) {
      case EVENT_ACCUMULATE_REFLECTION_COUNT_EXCEEDED:  //These event types don't have corresponding PRI::HIT_POINT_TYPES yet so they get treated as LOW_MOMENTUM.
      case EVENT_ACCUMULATE_MASSLESS_TRACER:
      case EVENT_ACCUMULATE_ON_INLET_OR_OUTLET:
      case EVENT_ACCUMULATE_SPLASH_OFF:
      case EVENT_ACCUMULATE_REFLECTION_OFF:
      case EVENT_ACCUMULATE_LOW_MOMENTUM:
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_ACCUMULATED_LOW_MOMENTUM;
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_FACE;
        pri_parent_particle_state = PRI::PARTICLE_STATE_HIT_SURFACE;
        break;
      case EVENT_ACCUMULATE_LOW_VNORM:
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_ACCUMULATED_LOW_NORMAL_VELOCITY;
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_FACE;
        pri_parent_particle_state = PRI::PARTICLE_STATE_HIT_SURFACE;
        break;
      case EVENT_ACCUMULATE_LOW_ANGLE:
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_ACCUMULATED_ANGLE_TOO_SMALL;
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_FACE;
        pri_parent_particle_state = PRI::PARTICLE_STATE_HIT_SURFACE;
        break;
      case EVENT_REFLECT:
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_REFLECTION;
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_FACE;
        pri_parent_particle_state = PRI::PARTICLE_STATE_HIT_SURFACE;
        break;
      case EVENT_SPLASH:
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_SPLASH;
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_FACE;
        pri_parent_particle_state = PRI::PARTICLE_STATE_SPLASHED;
        break;

      case EVENT_SCREEN_ACCUMULATE_REFLECTION_COUNT_EXCEEDED: //These event types don't have corresponding PRI::HIT_POINT_TYPES yet so they get treated as LOW_MOMENTUM
      case EVENT_SCREEN_ACCUMULATE_SPLASH_OFF:
      case EVENT_SCREEN_ACCUMULATE_REFLECTION_OFF:
      case EVENT_SCREEN_ACCUMULATE_LOW_MOMENTUM:
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_SCREEN;
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_ACCUMULATED_LOW_MOMENTUM;
        pri_parent_particle_state = PRI::PARTICLE_STATE_HIT_SURFACE;
        hitpoint_surface_index = cp_particle_sim.face_index_to_screen_index_map()->at(element.surface_index);
        break;
      case EVENT_SCREEN_ACCUMULATE_LOW_VNORM:
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_SCREEN;
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_ACCUMULATED_LOW_NORMAL_VELOCITY;
        pri_parent_particle_state = PRI::PARTICLE_STATE_HIT_SURFACE;
        hitpoint_surface_index = cp_particle_sim.face_index_to_screen_index_map()->at(element.surface_index);
        break;
      case EVENT_SCREEN_ACCUMULATE_LOW_ANGLE:
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_SCREEN;
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_ACCUMULATED_ANGLE_TOO_SMALL;
        pri_parent_particle_state = PRI::PARTICLE_STATE_HIT_SURFACE;
        hitpoint_surface_index = cp_particle_sim.face_index_to_screen_index_map()->at(element.surface_index);
        break;
      case EVENT_SCREEN_REFLECT:
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_SCREEN;
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_REFLECTION;
        pri_parent_particle_state = PRI::PARTICLE_STATE_HIT_SURFACE;
        hitpoint_surface_index = cp_particle_sim.face_index_to_screen_index_map()->at(element.surface_index);
        break;
      case EVENT_SCREEN_SPLASH:
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_SCREEN;
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_SPLASH;
        pri_parent_particle_state = PRI::PARTICLE_STATE_SPLASHED;
        hitpoint_surface_index = cp_particle_sim.face_index_to_screen_index_map()->at(element.surface_index);
        break;
      case EVENT_SCREEN_PASS_THROUGH:
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_SCREEN;
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_PASS_THROUGH;
        pri_parent_particle_state = PRI::PARTICLE_STATE_HIT_SURFACE;
        //Modify the surface index to be the screen index.
        hitpoint_surface_index = cp_particle_sim.face_index_to_screen_index_map()->at(element.surface_index);
        break;
      case EVENT_MERGE:
        msg_error("Unexpected MERGE event encountered in hitpoint stream.\n"); //Merges should be processed in the startpoint stream
        break;
      case EVENT_SPLIT:
        pri_parent_particle_state = PRI::PARTICLE_STATE_UNMERGED;
        hitpoint_surface_index = -1; //fix for PR41452.
        break;
      case EVENT_BREAKUP:
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_UNDEFINED;
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_PASS_THROUGH;
        hitpoint_surface_index = -1;
        pri_parent_particle_state = PRI::PARTICLE_STATE_BROKEUP;
        break;
      case EVENT_EXITED_WINDOW:
         //This will occur here for particles that go around peridic
         //bc's and have a child trace to connect to. Otherwise the
         //state update for a parcel exiting a window is handled by
         //the vertex manager.
        pri_surface_type = PRI::HIT_POINT_SURFACE_TYPE_UNDEFINED;
        pri_hitpoint_type = PRI::HIT_POINT_TYPE_PASS_THROUGH;
        hitpoint_surface_index = -1;  //would prefer to put the simvol index here.
        pri_parent_particle_state = PRI::PARTICLE_STATE_EXITED_WINDOW;
        break;
      default:
        msg_error("Unexpected event %d encountered in hitpoint stream.\n", element.event_type);
        break;
      }

      //Maybe write a hitpoint to the PMR file depdnding on the event type.
      switch(element.event_type) {
      case EVENT_SPLIT:
      case EVENT_BREAKUP:
      case EVENT_EXITED_WINDOW:
        //Split and Breakup events don't need hitpoints written to the
        //PMR file but they do require children info and states
        //updates encoded in the hitpoint record sent from the SP.
        //Hitpoints with exited_window event type are due to particles
        //wrapping around a periodic BC connecting to a child trace
        //and also don't require a hitpoint in the pmr file.
        per_window_per_emitter_element_count(element.window_index, element.emitter_id)--;
        break;
      default:
        asINT32 index = window_emitter_start_index(element.window_index, element.emitter_id)++;
        pri_hitpoints[index].SetTimeStep(element.time_of_impact);
        PRI::VECTOR3F position_vector( element.x[0], element.x[1], element.x[2]);
        pri_hitpoints[index].SetPosition(position_vector);
        pri_hitpoints[index].SetParticleIndex(parcel_id);
        pri_hitpoints[index].SetSurfaceIndex(hitpoint_surface_index);
        pri_hitpoints[index].SetSurfaceType(pri_surface_type);
        PRI::VECTOR3F impact_velocity( element.impact_velocity[0], element.impact_velocity[1], element.impact_velocity[2]);
        pri_hitpoints[index].SetImpactVelocity(impact_velocity);
        PRI::VECTOR3F normal_vector( element.normal[0], element.normal[1], element.normal[2]);
        pri_hitpoints[index].SetNormal(normal_vector);
        pri_hitpoints[index].SetType(pri_hitpoint_type); //The hit point surface type lives in the update and is set below.
      }

      if(num_subrecords > 0) {//Consume any subrecords which hold children parcel IDs.
        asINT32 first_subrec_index = receive_buffer_index + 1;
        asINT32 last_subrec_index = first_subrec_index + num_subrecords;
        first_child_index = window_emitter_children_cumulative_counts(element.window_index, element.emitter_id);
        ccDO_FROM_BELOW(subrec_index, first_subrec_index, last_subrec_index) {

          PRI::cPARTICLE_CHILD pri_child;
          recv_buffer[subrec_index].emitter_id = recv_buffer[subrec_index].reference_emitter_id; //The parcel referred to in the subrecord keeps it's emitter id in the reference_emitter_id field.  the emitter_id is set to the parent's emitter id so that it is sorted into the correct order.
          std::size_t child_id = parcel_global_id(recv_buffer[subrec_index], "hit point child");
          pri_child.SetIndex(child_id);
          pri_children[g_total_pri_emitters * element.window_index + element.emitter_id].push_back(pri_child);

        }
        window_emitter_children_cumulative_counts(element.window_index, element.emitter_id) += num_subrecords;
        last_child_index = window_emitter_children_cumulative_counts(element.window_index, element.emitter_id);

        //PR41929: Account for the existing records that may be in the PMR file after resuming from checkpoint.
        first_child_index += window->m_child_indices_offset[element.emitter_id];
        last_child_index += window->m_child_indices_offset[element.emitter_id];

        //Exclude any subrecords from the counters used to manage the block positions.
        per_window_per_emitter_element_count(element.window_index, element.emitter_id) -= num_subrecords;

        //If this was a split event and no hitpoint is needed, exclude the control record from the hitpoint count.
        ////if(element.event_type == EVENT_SPLIT)    //MERGE events also do not have hitpoints but they are handled by the startpoint manager.
        ////  per_window_per_emitter_element_count(element.window_index, element.emitter_id)--;

        //Advance the outer loop past the subrecords and
        receive_buffer_index += element.num_subrecords;
      }

      //Populate the next state update element.
      PRI::cPARTICLE_UPDATE state_update;
      state_update.SetParticleIndex(parcel_id);
      state_update.SetParticleState(pri_parent_particle_state);
      if(first_child_index < last_child_index) //Set the children indices if any children were compiled from subrecords.
        state_update.SetChildIndices(PRI::VECTOR2_INDEX(first_child_index, last_child_index));
      state_update.SetHitSurfaceIndex(hitpoint_surface_index);
      state_update.SetHitSurfaceType(pri_surface_type);
      state_update.SetDeathTimeStep(m_buffered_timestep + 1); //plus one because were dealing with the state computed for the beginning of the next timestep.
      pri_state_updates[g_total_pri_emitters * element.window_index + element.emitter_id].push_back(state_update);


    }

    // Free the receive buffer for this sp.
    if (m_receive_counts[sp] > 0) {
      delete[] recv_buffer;
      m_receive_buffers[sp] = NULL;
    }
  }

  // Go through the array of PRI hitpoints compiled above and pass the blocks of hitpoints from the same window and
  //emitter to the PRI interface.
  ccDOTIMES(window_index, g_trajectory_windows.size()) {
    CP_TRAJECTORY_WINDOW window = g_trajectory_windows[window_index];
    ccDOTIMES(emitter_index, g_total_pri_emitters) {

      PRI::EMITTER_TYPE emitter_type = g_simulator_emitter_id_to_pri_emitter_types[emitter_index];
      asINT32 pri_emitter_id = g_simulator_to_pri_emitter_ids[emitter_index];

      asINT32 num_hitpoints = per_window_per_emitter_element_count(window_index, emitter_index);
      //Write a block of hitpoints if there were any
      if (num_hitpoints > 0) {
        sINT32 start_index = window_emitter_cumulative_count(window_index, emitter_index);
        window->pmr_file().WriteParticleHitPoints(pri_hitpoints + start_index, num_hitpoints,
                                                  emitter_type, pri_emitter_id, m_buffered_timestep);

      }

      // Write a block of child table entries if there were any
      asINT32 num_children_records = pri_children[g_total_pri_emitters * window_index + emitter_index].size();
      if( num_children_records > 0) {
        window->pmr_file().WriteParticleChildren(pri_children[g_total_pri_emitters * window_index + emitter_index],
                                                 emitter_type, pri_emitter_id);
      }

      // Also write a block of state updates if there were any.
      asINT32 num_state_updates = pri_state_updates[g_total_pri_emitters * window_index + emitter_index].size();
      if( num_state_updates > 0) {
        window->pmr_file().WriteParticleUpdates(pri_state_updates[g_total_pri_emitters * window_index + emitter_index],
                                                emitter_type, pri_emitter_id);
      }
    }
  }
  //msg_print("Wrote %d hitpoints from timestep %d.\n", m_total_received, m_buffered_timestep);
  delete[] pri_hitpoints;
  delete[] pri_state_updates;
  delete[] pri_children;
  m_total_received = 0;
  m_buffer_ready = FALSE;
  m_completed_timestep = m_buffered_timestep;
  //msg_print("Wrote hitpoints.\n");
}



template <>
inline VOID sCP_TRAJECTORY_MANAGER<sTRAJECTORY_STARTPOINT>::write_receive_buffers_to_pri()
{

  if (!m_buffer_ready)
    return;


  //msg_print("CP: writing startpoint data at timestep %d with %d records (max buffered timestep %d).\n", m_buffered_timestep, m_total_received,m_max_buffered_timestep);

  assign_all_global_ids();



  //Allocate an array of PRI::cPARTICLE objects.
  PRI::cPARTICLE *pri_particles = xnew PRI::cPARTICLE[ m_total_received ];  //Not all of the above elements will be used because some elements in the recv buffer may be used to record multiple parents

  //Allocate buffers for any state updates and parent information that may be needed while dealing with merged parcel.
  //std::vector<PRI::cPARTICLE_UPDATE>* pri_state_updates = cnew std::vector<PRI::cPARTICLE_UPDATE>[g_trajectory_windows.size() * g_total_pri_emitters ]; //accumulate state updates into seperate vectors for each window and emitter.
  //ccDOTIMES(state_update_block, g_trajectory_windows.size() * g_total_pri_emitters ) {
  //  pri_state_updates[state_update_block].reserve(m_total_received); //This is overkill but easy to implement.
  //}

  std::vector<PRI::cPARTICLE_PARENT>* pri_parents = cnew std::vector<PRI::cPARTICLE_PARENT>[g_trajectory_windows.size() * g_total_pri_emitters ]; //accumulate children updates into seperate vectors for each window and emitter.
  ccDOTIMES(state_parent_block, g_trajectory_windows.size() * g_total_pri_emitters ) {
    pri_parents[state_parent_block].reserve(m_total_received); //This is overkill but easy to implement.
  }


  memcpy(m_window_emitter_start_indices.data(), m_window_emitter_cumulative_counts.data(),
         sizeof(sINT32) * g_trajectory_windows.size() * g_total_pri_emitters);
  // Copy the receive buffers into pri_particles sorted by emitter, window index, and SP. Sorting by
  // SP is only necessary to ensure a repeatable order in the PRI file. A bucket sort is used.

  ccDOTIMES(sp, total_sps) {
    sTRAJECTORY_STARTPOINT *recv_buffer = m_receive_buffers[sp];

    ccDO_FROM_BELOW(receive_buffer_index, FIRST_NON_HEADER_INDEX, m_receive_counts[sp]) {
      sTRAJECTORY_STARTPOINT &element = recv_buffer[receive_buffer_index];
      PRI::EMITTER_TYPE emitter_type = g_simulator_emitter_id_to_pri_emitter_types[ element.emitter_id ];
      asINT32 pri_emitter_id = g_simulator_to_pri_emitter_ids[ element.emitter_id ];
      CP_TRAJECTORY_WINDOW window = g_trajectory_windows[element.window_index];
      //This startpoint record may have zero or more following subrecords encoding the ID(s) of the particle's parent(s).
      //If the event type is MERGE, then there can be any number of parents (possibly from different emitters).
      //Particles coming from an emitter (event_type EVENT_EMITTER) will have no parents.
      //Particles coming from a SPLASH can have only one parent and it must be from the same emitter.

      asINT32 num_subrecords = element.num_subrecords;
      if(num_subrecords > 1 && element.event_type != EVENT_MERGE) {
        msg_internal_error("Expected only zero or one parents for a merge event but received more.\n");
      }

      asINT32 index = window_emitter_start_index(element.window_index, element.emitter_id)++;
      pri_particles[index].SetSize(element.diameter);
      pri_particles[index].SetDensity(element.density);
      pri_particles[index].SetEmitterType(emitter_type);
      pri_particles[index].SetEmitterIndex(pri_emitter_id);
      pri_particles[index].SetBirthTimeStep(element.birth_time);
      pri_particles[index].SetFirstVertexTimeStep(m_buffered_timestep);
      pri_particles[index].SetParticleSource(convert_particle_source(element.event_type));

      if(num_subrecords > 0) { //then there exists some new parent information...

        asINT32 first_subrec_index = receive_buffer_index + 1;
        asINT32 last_subrec_index = first_subrec_index + num_subrecords;
        asINT32 first_parent_index_offset = window_emitter_parent_cumulative_counts(element.window_index, element.emitter_id);

        ccDO_FROM_BELOW(subrec_index, first_subrec_index, last_subrec_index) {

          asINT32 parent_particle_simulator_emitter_id = recv_buffer[subrec_index].reference_emitter_id;  //Reference emitter id and emitter id can differ for MERGE events.
          PRI::EMITTER_TYPE parent_emitter_type = g_simulator_emitter_id_to_pri_emitter_types[ parent_particle_simulator_emitter_id ];
          asINT32 parent_particle_pri_emitter_id = g_simulator_to_pri_emitter_ids[ parent_particle_simulator_emitter_id ];

          //Set the emitter ID for this subrecord with the reference emitter ID before calling parcel_global_id(). This only makes a difference for MERGE subrecords
          //because the emitter id of the parent may be different than the child. The emitter_id field of the startpoint subrecord must always use the emitter
          //id of the child so that the subrecords are not scattered after sorting the receive buffer by emitter id.

          recv_buffer[subrec_index].emitter_id = recv_buffer[subrec_index].reference_emitter_id;
          std::size_t parent_parcel_id = parcel_global_id(recv_buffer[subrec_index], "startpoint parent");  //get the parcel id of the parent.

          //Populate the next PRI parent object (for the child parcel's emitter's parent table) using the above items.
          //All these parent elements will be written to PRM in blocks (blocks organized by emitter id and window)
          PRI::cPARTICLE_PARENT pri_parent;
          pri_parent.SetEmitterType(parent_emitter_type);
          pri_parent.SetEmitterIndex(parent_particle_pri_emitter_id);
          pri_parent.SetParticleIndex(parent_parcel_id);
          pri_parents[g_total_pri_emitters * element.window_index + element.emitter_id].push_back(pri_parent);

          //If this parent is from a merge event, then it died without a hitpoint being measured on the SP for the parents therefore its state and children references need to be updated here.
          if(recv_buffer[subrec_index].event_type == EVENT_MERGE) {

            //The states of the parents would not necessecarily end up sorted in blocks since they can come from different emitters.
            //Therefor for simplicity, the states and children IDs are added to PMR files one at a time here instead of blockwise.

            //Create child table entries that will be linked to the parent parcel.
            std::size_t child_parcel_id = parcel_global_id(element, "startpoint parent child");
            PRI::cPARTICLE_CHILD pri_parent_parcel_child;
            pri_parent_parcel_child.SetIndex(child_parcel_id);  //PRI has to assume this child id is for the same emitter unless the state is MERGED in which case it assumes that the ID referes to the MERGED emitter cPARTICLE table.

            PRI::cPARTICLE_UPDATE parent_state;
            parent_state.SetParticleIndex(parent_parcel_id);
            parent_state.SetParticleState(PRI::PARTICLE_STATE_MERGED);
            parent_state.SetHitSurfaceIndex(-1);
            parent_state.SetHitSurfaceType(PRI::HIT_POINT_SURFACE_TYPE_UNDEFINED);
            parent_state.SetDeathTimeStep(m_buffered_timestep + 1);  //plus one because were dealing with the state computed for the beginning of the next timestep.
            std::size_t next_child_index_offset = window_emitter_children_cumulative_counts(element.window_index, parent_particle_simulator_emitter_id);

            //PR41929, include offset due to a resumed file.
            next_child_index_offset += window->m_child_indices_offset[parent_particle_simulator_emitter_id];

            parent_state.SetChildIndices(PRI::VECTOR2_INDEX(next_child_index_offset, next_child_index_offset + 1));

            //Add the new entry to the child table of the parent parcel's emitter
            window->pmr_file().WriteParticleChildren(&pri_parent_parcel_child,
                                                     1,                                                                                              //std::size_t arraySize,
                                                     parent_emitter_type, parent_particle_pri_emitter_id);

            window_emitter_children_cumulative_counts(element.window_index, parent_particle_simulator_emitter_id)++;

            //Add one new state update entry to the update table for the parent parcel.
            window->pmr_file().WriteParticleUpdates(&parent_state,
                                                    1,
                                                    parent_emitter_type, parent_particle_pri_emitter_id);

            //window_emitter_states_cumulative_counts(element.window_index, parent_particle_simulator_emitter_id)++;

          }
        }
        window_emitter_parent_cumulative_counts(element.window_index, element.emitter_id) += num_subrecords;
        asINT32 last_parent_index_offset = window_emitter_parent_cumulative_counts(element.window_index, element.emitter_id);

        //Ensure that the pmr file has been opened so that m_parent_indices_offset is sized correctly (PR42255).
        window->pmr_file();

        //PR41929, include offset due to a resumed file.
        first_parent_index_offset += window->m_parent_indices_offset[element.emitter_id];
        last_parent_index_offset  += window->m_parent_indices_offset[element.emitter_id];


        //Set the parent indices of this parcel
        pri_particles[index].SetParentIndices(PRI::VECTOR2_INDEX(first_parent_index_offset, last_parent_index_offset));

        //Advance the outer loop past the consumed subrecords and exclude them from the counters used to manage the block positions.
        per_window_per_emitter_element_count(element.window_index, element.emitter_id) -= num_subrecords;  //Subtract the n subrecords from the count of actual startpoints
        receive_buffer_index += num_subrecords;
      }
    }

    if (m_receive_counts[sp] > 0) {
      // Free the receive buffer for this sp,
      delete[] recv_buffer;
      m_receive_buffers[sp] = NULL;
    }
  }

  // Go through the array of cPARTICLE objects compiled above and pass the blocks of objects from the same window and
  //emitter to the PRI interface.
  ccDOTIMES(window_index, g_trajectory_windows.size()) {
    CP_TRAJECTORY_WINDOW window = g_trajectory_windows[window_index];
    ccDOTIMES(emitter_index, g_total_pri_emitters) {

      PRI::EMITTER_TYPE emitter_type = g_simulator_emitter_id_to_pri_emitter_types[emitter_index];
      asINT32 pri_emitter_id = g_simulator_to_pri_emitter_ids[emitter_index];

      asINT32 num_startpoints = per_window_per_emitter_element_count(window_index, emitter_index);
      if (num_startpoints > 0) {
        sINT32 start_index = window_emitter_cumulative_count(window_index, emitter_index);
        // Pass the block of startpoints to the PRI interface.
        window->pmr_file().WriteParticles(pri_particles + start_index, num_startpoints, emitter_type, pri_emitter_id);
      }

      asINT32 num_parent_records = pri_parents[g_total_pri_emitters * window_index + emitter_index].size();
      if(num_parent_records > 0) {
        window->pmr_file().WriteParticleParents(pri_parents[g_total_pri_emitters * window_index + emitter_index],
                                                emitter_type, pri_emitter_id);
      }
    }
  }

  //msg_print("Wrote %d startpoints from timestep %d.\n", m_total_received, m_buffered_timestep);
  delete[] pri_particles;
  delete[] pri_parents;
  g_num_unprocessed_startpoints = 0;
  m_total_received = 0;
  m_buffer_ready = FALSE;
  m_completed_timestep = m_buffered_timestep;

}


//Use the templates above to define a manager for each of the three data types
typedef class sCP_TRAJECTORY_MANAGER<sTRAJECTORY_VERTEX>     sCP_TRAJECTORY_VERTEX_MANAGER,     *CP_TRAJECTORY_VERTEX_MANAGER;
typedef class sCP_TRAJECTORY_MANAGER<sTRAJECTORY_HITPOINT>   sCP_TRAJECTORY_HITPOINT_MANAGER,   *CP_TRAJECTORY_HITPOINT_MANAGER;
typedef class sCP_TRAJECTORY_MANAGER<sTRAJECTORY_STARTPOINT> sCP_TRAJECTORY_STARTPOINT_MANAGER, *CP_TRAJECTORY_STARTPOINT_MANAGER;

extern sCP_TRAJECTORY_VERTEX_MANAGER     cp_trajectory_vertex_manager;
extern sCP_TRAJECTORY_HITPOINT_MANAGER   cp_trajectory_hitpoint_manager;
extern sCP_TRAJECTORY_STARTPOINT_MANAGER cp_trajectory_startpoint_manager;

// This is for vertices and hitpoints.
template < typename DATA_TYPE >
bool sCP_TRAJECTORY_MANAGER < DATA_TYPE >::ready_to_write_to_pri()
{ return cp_trajectory_startpoint_manager.next_timestep_to_output() > this->next_timestep_to_output(); }

template <>
inline bool sCP_TRAJECTORY_MANAGER<sTRAJECTORY_STARTPOINT>::ready_to_write_to_pri()
{ return (cp_trajectory_vertex_manager.next_timestep_to_output() == this->next_timestep_to_output()
          && cp_trajectory_hitpoint_manager.next_timestep_to_output() == this->next_timestep_to_output());
}

VOID initialize_trajectory_meas_data_comm();
VOID synchronize_trajectory_window_data(TIMESTEP end_timestep);
VOID maybe_receive_sp_trajectory_data();
VOID write_pri_result_buffers();
BOOLEAN trajectory_data_is_waiting_for_results_thread();
VOID wait_for_IO_thread_to_process_trajectory_window_data();
VOID receive_all_remaining_trajectory_window_measurements();

#endif

