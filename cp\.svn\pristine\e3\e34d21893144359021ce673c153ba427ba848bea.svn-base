/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Job Control Support (runs on the CP)
 *
 * Jim Salem, Exa Corporation 
 * Created Mon Jun 20 1994
 *--------------------------------------------------------------------------*/

#include "common.h"
#include "exa_sim.h"
#include "jobctl_status.h"
#include "cp_lattice.h"
#include "cp_info.h"


/*--------------------------------------------------------------------------*
 * Writing memory usage status to stdout
 *--------------------------------------------------------------------------*/

BOOLEAN cp_jobctl_memory_status_requested()
{
  static asINT32 memory_status_report_initialized = 0;

  if (memory_status_report_initialized == 0) {
    cSTRING ename = platform_getenv("EXA_CP_INIT_MEM_PROFILE");
    if (ename && (
	(platform_strcasecmp(ename, "yes") == 0) ||
	(platform_strcasecmp(ename, "true") == 0) ||
	(platform_strcasecmp(ename, "enable") == 0) ||
	(platform_strcasecmp(ename, "1") == 0))) {
      memory_status_report_initialized = 1;
    } else {
      memory_status_report_initialized = 2;
    }
  }

  return(memory_status_report_initialized == 1);
}

/* Variant of jobctl_output_status, that only emits to stderr if
 * the memory footprint of the process has changed.  Keeps track
 * of the "prior" status message so that the last status before
 * the memory size change can be emitted as well */
VOID cp_jobctl_output_memory_status(cSTRING status_string)
{
  /* If we're reporting memory consumption... */
  if (cp_jobctl_memory_status_requested()) {
    static auINT64 last_total_size = 0;
    static auINT64 last_resident_size = 0;
    static CHARACTER last_memory_status_string[512] = "";

    auINT64 total_size = 0;
    auINT64 resident_size = 0;
    platform_process_sizes(-1, &total_size, &resident_size);

    /* If memory size has changed since last time through... */
    if ((total_size != last_total_size) ||
	(resident_size != last_resident_size)) {
      char res_buf[32];
      char total_buf[32];

      /* If we have a retained status, dump that now so that the log
       * file will show what we were doing immediately before memory
       * jumped and immediately after */
      if (last_memory_status_string[0]) {
	fprintf(stderr, "%s\n", last_memory_status_string);
	last_memory_status_string[0] = (char) 0x00;
      }

      /* If the total size has grown... */
      if (total_size >= last_total_size) {
	if (total_size != last_total_size) {
	  sprintf(total_buf, " (%c%lu)", '+', (unsigned long) (total_size - last_total_size));
	} else {
	  strcpy(total_buf, "");
	}
      } else {
	sprintf(total_buf, " (%c%lu)", '-', (unsigned long) (last_total_size - total_size));
      }

      /* If the resident size has grown... */
      if (resident_size >= last_resident_size) {
	if (resident_size != last_resident_size) {
	  sprintf(res_buf, " (%c%lu)", '+', (unsigned long) (resident_size - last_resident_size));
	} else {
	  strcpy(res_buf, "");
	}
      } else {
	sprintf(res_buf, " (%c%lu)", '-', (unsigned long) (last_resident_size - resident_size));
      }

      fprintf(stderr, "Status (%luK%s, %luK%s): %s\n",
	(unsigned long) total_size, total_buf, (unsigned long) resident_size,
	res_buf, status_string); fflush(stderr);

      /* Retain the current total and resident sizes so that we can
       * tell if they change */
      last_total_size = total_size;
      last_resident_size = resident_size;
    } else {
    /* Else, memory size hasn't changed, update and retain the status
     * message in case the next status shows a change */
      sprintf(last_memory_status_string, "Status (%luK, %luK): %s",
	(unsigned long) total_size, (unsigned long) resident_size,
	status_string);
    }
  }
}


/*--------------------------------------------------------------------------*
 * Writing the job control file
 *--------------------------------------------------------------------------*/

/* Writes a status message to a file */
VOID cp_jobctl_output_status(cSTRING status_string)
{
  jobctl_server_set_phase(status_string, 0.0, 0.0);
  /* Take this opportunity to write memory status */
  cp_jobctl_output_memory_status(status_string);
  return;
}

int cp_jobctl_timestep_status_string(SIM_STATUS status, SIM_STATUS flow, SIM_STATUS cond, char status_string[])
{
  if (cp_info.is_flow && cp_info.is_conduction) {
    return sprintf(status_string, "Flow Timestep %ld Conduction Timestep %ld", (long) sim_status_timestep(flow), (long) sim_status_timestep(cond));
  } else {
    return sprintf(status_string, "Timestep %ld", (long) sim_status_timestep(status));
  }
}

/* Returns a statues message based on cp_info.cp_status */
cSTRING cp_jobctl_status_string(VOID)
{
  /* Status string */
  static CHARACTER status_string[256];
  memset(status_string, (char) '\0', sizeof(status_string));

  if (!cp_info_has_been_initialized_p)
    sprintf(status_string, "Initialization");
  else {
    sSIM_SOLVER_STATUS cp_status = cp_info.cp_status;
    switch (cp_status.status) {
    case SIM_STATUS_UNKNOWN:
      sprintf(status_string, "Initialization");
      break;
    case SIM_STATUS_INIT:
      sprintf(status_string, "Initialization");
      break;
    case SIM_STATUS_DONE:
      sprintf(status_string, "Finishing Up");
      break;
    default:
      cp_jobctl_timestep_status_string(cp_status.status,
                                       cp_status.flow_status,
                                       cp_status.cond_status,
                                       status_string);
   }
  }
  return status_string;
}

/* Outputs the current status */
VOID cp_jobctl_output_current_status(VOID)
{
  cp_jobctl_output_status(cp_jobctl_status_string());
}

