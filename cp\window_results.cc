/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 *
 * Write measurement files
 *
 *--------------------------------------------------------------------------*/


#include "common.h"
#include LGI_H
#include "window.h"
#include "window_results.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "errbuf.h"
#include "monitor.h"

#include "particle_sim_cp.h"
VOID modify_particle_var_descriptor(CP_MEAS_WINDOW window, asINT32 var_index, SRI_VARIABLE_DESC descriptor);

//5G
VOID get_5g_var_desc(sriINT id,
		     SRI_CUSTOM_VAR_ID_HELPER cvid_helper,
		     SRI_VARIABLE_DESC desc)
{
  SRI_VARIABLE_TYPE var_type = cvid_helper->get_5g_var_type(id); 
  desc->unit_class_name = sri_variable_unit_class_name(var_type);
  desc->lattice_unit_name = sri_variable_lattice_unit_name(var_type);

  desc->tiny_name = strsave(cvid_helper->variable_5g_tiny_name((SRI_VARIABLE_TYPE)id, cp_info.s_fluid_components_5g));
  desc->short_name = strsave(cvid_helper->variable_5g_short_name((SRI_VARIABLE_TYPE)id, cp_info.s_fluid_components_5g));
  desc->long_name = strsave(cvid_helper->variable_5g_long_name((SRI_VARIABLE_TYPE)id, cp_info.s_fluid_components_5g));
}

//UDS
VOID get_uds_var_desc(sriINT id,
		      SRI_CUSTOM_VAR_ID_HELPER cvid_helper,
		      SRI_VARIABLE_DESC desc,
		      BOOLEAN need_units)
{
  SRI_VARIABLE_TYPE var_type = cvid_helper->get_uds_var_base_type(id);  //base type
  asINT32 nth_uds = cvid_helper->get_uds_scalar_for_var_id(id);

  if (need_units) {
    if ((var_type == SRI_VARIABLE_UDS_SCALAR_VALUE) && (nth_uds>=0)) {
      desc->unit_class_name = cp_info.s_scalar_materials[nth_uds].unit_class;
      UNITS_UNIT lattice_unit;
      units_unit_class_expr_string_to_unit(cp_info.units_db, "lattice", desc->unit_class_name, &lattice_unit);
      desc->lattice_unit_name = units_unit_name(lattice_unit);
    } else {
      desc->unit_class_name = sri_variable_unit_class_name(var_type);
      desc->lattice_unit_name = sri_variable_lattice_unit_name(var_type);
    }
  }

  desc->tiny_name = strsave(cvid_helper->variable_uds_tiny_name((SRI_VARIABLE_TYPE)id, cp_info.s_scalar_materials));
  desc->short_name = strsave(cvid_helper->variable_uds_short_name((SRI_VARIABLE_TYPE)id, cp_info.s_scalar_materials));
  desc->long_name = strsave(cvid_helper->variable_uds_long_name((SRI_VARIABLE_TYPE)id, cp_info.s_scalar_materials));
}

VOID initialize_sri_file_params_grf_info(SRI_FILE_PARAMS sri_file_params, BOOLEAN is_probe)
{
  sri_file_params->has_grf = cp_info.is_global_ref_frame;
  vcopy(sri_file_params->grf_ref_point, cp_info.global_nirf_info.ref_point);
  sri_file_params->grf_has_constant_angular_vel = is_probe || cp_info.global_nirf_info.is_angular_vel_constant;
  vcopy(sri_file_params->grf_constant_angular_vel, cp_info.global_nirf_info.angular_vel);
  vcopy(sri_file_params->grf_constant_axis_origin, cp_info.global_nirf_info.point);
  sri_file_params->grf_initial_angular_rotation = cp_info.global_nirf_info.angle_rotated;
  sri_file_params->grf_has_constant_ref_point_vel = is_probe || cp_info.global_nirf_info.is_ref_point_vel_constant;
  vzero(sri_file_params->grf_initial_translation);
  vcopy(sri_file_params->grf_constant_ref_point_vel, cp_info.global_nirf_info.ref_point_vel);
}

static inline SRI_VARIABLE_TYPE filter_var_type (BOOLEAN is_development, SRI_VARIABLE_TYPE var_type)
{
  if (!is_development) {
    return var_type;
  } else {
    switch (var_type) {
    case SRI_VARIABLE_XFORCE: return SRI_VARIABLE_XFORCE_TRUE;
    case SRI_VARIABLE_YFORCE: return SRI_VARIABLE_YFORCE_TRUE;
    case SRI_VARIABLE_ZFORCE: return SRI_VARIABLE_ZFORCE_TRUE;
    case SRI_VARIABLE_XTORQUE: return SRI_VARIABLE_XTORQUE_TRUE;
    case SRI_VARIABLE_YTORQUE: return SRI_VARIABLE_YTORQUE_TRUE;
    case SRI_VARIABLE_ZTORQUE: return SRI_VARIABLE_ZTORQUE_TRUE;
    case SRI_VARIABLE_FLUID_XFORCE: return SRI_VARIABLE_FLUID_XFORCE_TRUE;
    case SRI_VARIABLE_FLUID_YFORCE: return SRI_VARIABLE_FLUID_YFORCE_TRUE;
    case SRI_VARIABLE_FLUID_ZFORCE: return SRI_VARIABLE_FLUID_ZFORCE_TRUE;
    case SRI_VARIABLE_FLUID_XTORQUE: return SRI_VARIABLE_FLUID_XTORQUE_TRUE;
    case SRI_VARIABLE_FLUID_YTORQUE: return SRI_VARIABLE_FLUID_YTORQUE_TRUE;
    case SRI_VARIABLE_FLUID_ZTORQUE: return SRI_VARIABLE_FLUID_ZTORQUE_TRUE;
    default: return var_type;
    }
  }
}

VOID set_experimental_var_descriptors(SRI_VARIABLE_DESC descriptor) {
  eEXPERIMENTAL_VARIABLE_TYPES offset =
    (eEXPERIMENTAL_VARIABLE_TYPES)
    (descriptor->var_type - cp_info.cvid_helper->get_first_unassigned_var_id());
  switch(offset) {
  case eEXPERIMENTAL_VARIABLE_TYPES::CONDUCTION_PASSTHROUGH:
    descriptor->unit_class_name = "PowerDensity";
    descriptor->lattice_unit_name = "LatticePowerDensity";
    descriptor->tiny_name = "cppd";
    descriptor->short_name = "conduction solver passthrough";
    descriptor->long_name = "Conduction Solver Pass Through Power Density";
    descriptor->mean_particle_count_variable_type = SRI_VARIABLE_INVALID;
    break;
  case eEXPERIMENTAL_VARIABLE_TYPES::TANGENTIAL_GRAD:
    descriptor->unit_class_name = "Temperature";
    descriptor->lattice_unit_name = "LatticeTemperature";
    descriptor->tiny_name = "tang";
    descriptor->short_name = "tangential gradient";
    descriptor->long_name = "Tangential gradient magnitude";
    descriptor->mean_particle_count_variable_type = SRI_VARIABLE_INVALID;
    break;
  default:
    msg_error("Unimplmented experimental var type encountered.");
  }
}

static VOID initialize_sri_file_params(SRI_FILE_PARAMS sri_file_params, 
                                       CP_MEAS_WINDOW window)
{
  sri_file_params->n_params = cp_info.sri_params.size();
  sri_file_params->params = &cp_info.sri_params[0];
  sri_file_params->n_translations = cp_info.n_sri_translations;
  sri_file_params->translations = cp_info.sri_translations;
  sri_file_params->n_vectors = 0;
  sri_file_params->vectors = NULL;
  sri_file_params->n_assemblies = cp_info.n_sri_assemblies;
  sri_file_params->assemblies = cp_info.sri_assemblies;
  sri_file_params->n_parts = cp_info.n_sri_parts;
  sri_file_params->parts = cp_info.sri_parts;
  sri_file_params->n_faces = cp_info.n_sri_faces;
  sri_file_params->n_phases = cp_info.n_sri_solver_phases;
  sri_file_params->time_coupling_phases = cp_info.sri_solver_phases;
  sri_file_params->faces = cp_info.sri_faces;

  std::vector<bool> processed_faces(cp_info.n_sri_faces, false);

  ccDOTIMES(face, cp_info.n_sri_faces) {

    if (processed_faces[face]) {
      // If a back face comes after the front face, it has already
      // been setup, so we don't want to do it again
      continue;
    }

    bool face_is_front = cp_info.sri_faces[face].is_front;
    asINT32 opp_face = cp_info.sri_faces[face].opposite_face;

    bool face_is_split = opp_face >= 0 && opp_face != face;

    bool is_surface_window = window->meas_window_type == LGI_SURFACE_WINDOW;
    bool is_shell_window = window->meas_window_type == LGI_SHELL_WINDOW;
    bool is_surface_or_shell_window = is_surface_window || is_shell_window;

    // Set these up for this face. This will touch front & back faces
    // For back faces, in the rare instance someone is measuring only a back face,
    // this will set everything up appropriately
    sri_file_params->faces[face].n_surfels = window->n_surfels[face];
    sri_file_params->faces[face].first_surfel = window->first_surfel[face];
    sri_file_params->faces[face].first_point = window->first_point[face];
    sri_file_params->faces[face].n_surfels_with_two_meas = window->n_surfels_with_two_meas[face];

    if (face_is_front) {
      // Do the front face parameters first, which are correct in the window->*[] arrays
      if (is_surface_or_shell_window && face_is_split && window->n_surfels[face] > 0) {

        // This face is split into front/back, we are currently processing the
        // front, so set the parameters for the back face now. The parameters
        // in window->*[] arrays are only set correctly for the front face, so
        // we have to do this here.
        //
        // If the back face came first, we will override whatever was put in there already

        sriPOINT first_point = sri_file_params->faces[face].first_point;
        sriPOINT n_surfels  = sri_file_params->faces[face].n_surfels;
        if (is_surface_window) {
          // For surface windows, we set n_surfels on the back face to n_surfels_with_two_meas
          // and update first_point on the back side, as it is currently incorrect
          n_surfels  = sri_file_params->faces[face].n_surfels_with_two_meas;
          first_point += sri_file_params->faces[face].n_surfels;
        }

        sri_file_params->faces[opp_face].n_surfels = n_surfels;
        sri_file_params->faces[opp_face].first_point = first_point;
        sri_file_params->faces[opp_face].first_surfel = sri_file_params->faces[face].first_surfel;
        sri_file_params->faces[opp_face].n_surfels_with_two_meas = window->n_surfels_with_two_meas[face];

        // the back face has been processed, and we don't want to do it again when we come to it in the loop
        processed_faces[opp_face] = true;
      }
    }
    processed_faces[face] = true;
  }

  sri_file_params->n_csys = cp_info.sri_n_csys;
  sri_file_params->all_csys = cp_info.sri_all_csys;

  sri_file_params->n_user_defined_units = cp_info.condensed_units_db->user_defined_units.size();
  sri_file_params->user_defined_units = &cp_info.condensed_units_db->user_defined_units[0];

  sri_file_params->vehicle_defn = cp_info.sri_vehicle_defn;

  sri_file_params->sim_ht_type = cp_info.sim_ht_type;
  sri_file_params->is_sampling_meas_type = (window->meas_window_type == LGI_SAMPLING_SURFACE_WINDOW || window->meas_window_type == LGI_SAMPLING_SHELL_WINDOW);
  sri_file_params->is_lattice_temperature_constant = cp_info.is_lattice_temperature_constant;
  if (sri_file_params->is_lattice_temperature_constant)
    sri_file_params->constant_lattice_temperature_value = cp_info.constant_lattice_temperature_value;
  else
    sri_file_params->constant_lattice_temperature_value = 0.0;

  sri_file_params->is_output_in_local_csys = window->is_output_in_local_csys;
  sri_file_params->is_subsonic = !cp_info.is_high_subsonic_mach_regime && !cp_info.is_transonic_mach_regime;

  sri_file_params->is_density_constant = cp_info.is_density_constant;
  if (sri_file_params->is_density_constant)
    sri_file_params->constant_density_value = cdi_data.char_density;
  else
    sri_file_params->constant_density_value = 0.0;

  sri_file_params->audit_trail = (sriCHAR*)audit_universal_rep(cp_info.audit_trail);
  sri_file_params->n_dims = cp_info.n_dims;
  sri_file_params->n_points = window->n_meas_cells;

  window->base_frame = window->prev_nsets_written;
  sri_file_params->base_frame = window->base_frame;

  initialize_sri_file_params_grf_info(sri_file_params, window->is_probe);

  sri_file_params->n_lrfs = cp_info.n_lrfs();
  sri_file_params->lrfs = &cp_info.sri_lrfs[0];

  if (cp_info.n_lrfs() > 0) {
    ccDOTIMES(i, cp_info.n_lrfs()) {
      cp_info.sri_lrfs[i].n_points = 0;
    }
    BOOLEAN isSurfaceOrShell = (!window->is_composite && !window->is_development) && ((window->meas_window_type == LGI_SURFACE_WINDOW) || (window->meas_window_type == LGI_SHELL_WINDOW));
    asINT32 meas_cells = window->n_meas_cells;

    if (isSurfaceOrShell) {
      if (auto* surface_window = dynamic_cast<sCP_SURFACE_MEAS_WINDOW_SFLOAT*>(window)) {
        meas_cells = surface_window->m_n_surfels;
      } else if (auto* surface_window = dynamic_cast<sCP_SURFACE_MEAS_WINDOW_DFLOAT*>(window)) {
        meas_cells = surface_window->m_n_surfels;
      } else {
        msg_internal_error("Unable to perform dynamic cast");
      }
    }

    ccDOTIMES(i, meas_cells) {
      asINT32 ref_frame = window->m_ref_frame_indices.at(i);
      if (ref_frame >= 0) {
        if (ref_frame >= cp_info.n_lrfs())
          msg_internal_error("Invalid reference frame index (%d) for cell %d of window %d",
                               ref_frame, i, window->index);
        cp_info.sri_lrfs[ref_frame].n_points ++;
      }
    }
  }

  sri_file_params->n_mbcs = cp_info.n_mbcs();
  sri_file_params->mbcs = &cp_info.sri_mbcs[0];

  if ( window->meas_window_type == LGI_SURFACE_WINDOW ) {
    sri_file_params->n_deforming_tires = cp_info.n_dtires;
    sri_file_params->deforming_tires = cp_info.sri_dtires;
  }
  else {
    sri_file_params->n_deforming_tires = 0;
    sri_file_params->deforming_tires = nullptr;
  }

  // parentage record
  sri_file_params->ptge_rec.case_file_id = cp_info.ptge.case_file_id;
  sri_file_params->ptge_rec.case_geometry_id = cp_info.ptge.case_geometry_id;
  sri_file_params->ptge_rec.cdi_file_id = cp_info.ptge.cdi_file_id;
  sri_file_params->ptge_rec.lgi_file_id = cp_info.ptge.lgi_file_id;
  sri_file_params->ptge_rec.decomp_lgi_file_id = cp_info.ptge.decomp_lgi_file_id;
  sri_file_params->ptge_rec.decomp_nprocs = cp_info.ptge.decomp_n_procs;
  sri_file_params->ptge_rec.solver_version = strtol(cp_info.solver_version, NULL, 10);
  try {
    sri_file_params->ptge_rec.cp_version = std::stoi(CP_VERSION);
    if(sri_file_params->ptge_rec.cp_version == 0)
      throw std::out_of_range("Zero is not a valid CP version.");
  }
  catch (...) {
    msg_internal_error("Could not determine the CP version from string %s.", CP_VERSION);
  }

  
  sri_file_params->is_std_meas_mme = cp_info.is_std_meas_mme_p;

  ccDOTIMES(i, 3) {
    sri_file_params->min_bound[i] = window->min_bound[i];
    sri_file_params->max_bound[i] = window->max_bound[i];
  }

  sri_file_params->n_scales = cp_info.num_scales;

  if (cp_info.is_5g_sim || cp_info.is_uds_transport)
    sri_file_params->custom_var_id_helper = cp_info.cvid_helper;
  
  // for new measurement files a measurement window scale of 0 represents the
  // finest scale, in the LGI file it has the reverse meaning, so modify below
  sri_file_params->meas_win_scale = sim_scale_to_sri_scale(window->meas_cell_scale);
  sri_file_params->single_voxel_meas_p = window->is_per_voxel;
  sri_file_params->n_variables = window->n_variables;
  sri_file_params->variables = cnew sSRI_VARIABLE_DESC [sri_file_params->n_variables];
  SRI_VARIABLE_DESC var_desc = sri_file_params->variables;
  ccDOTIMES(var, sri_file_params->n_variables) {
    var_desc->var_double  = window->is_meas_vars_output_dp;
    var_desc->var_type = filter_var_type(window->is_development, window->var_types[var]);

    var_desc->mean_particle_count_variable_type = SRI_VARIABLE_INVALID;
    if(window->population_var_indices[var] != -1)
      var_desc->mean_particle_count_variable_type = (SRI_VARIABLE_TYPE)window->var_types[window->population_var_indices[var]];

    if (var_desc->var_type < SRI_VARIABLE_FIRST_NON_PREDEFINED) {
      var_desc->tiny_name = sri_variable_type_to_tiny_name(var_desc->var_type);
      var_desc->short_name = sri_variable_type_to_short_name(var_desc->var_type);
      var_desc->long_name = sri_variable_type_to_long_name(var_desc->var_type);
      var_desc->unit_class_name = sri_variable_unit_class_name(var_desc->var_type);
      var_desc->lattice_unit_name = sri_variable_lattice_unit_name(var_desc->var_type);

      modify_particle_var_descriptor(window, var, var_desc); //Do something special for particle modeling variables with multiple compoents when per-emitter or per-material particle measurements.

    } else if (cp_info.is_5g_sim && cp_info.cvid_helper->is_5g_var_id(var_desc->var_type)) { //5G UDS will go here as well
      get_5g_var_desc(var_desc->var_type,
		      cp_info.cvid_helper,
		      var_desc);
    } else if(cp_info.is_particle_solver && cp_info.cvid_helper->is_pm_var_id(var_desc->var_type)) {
      cp_particle_sim.set_experimental_particle_var_descriptors(var_desc);
    } else if (cp_info.is_uds_transport && cp_info.cvid_helper->is_uds_var_id(var_desc->var_type)) {
      get_uds_var_desc(var_desc->var_type,
		       cp_info.cvid_helper,
		       var_desc);
    } else if(var_desc->var_type >= cp_info.cvid_helper->get_first_unassigned_var_id())
      set_experimental_var_descriptors(var_desc);
    else
      msg_internal_error("Unsupported user defined variable type %d encountered.", var_desc->var_type);
    
    var_desc++;
  }
  sri_file_params->n_fluid_components = cp_info.num_fluid_components;
  sri_file_params->fluid_components = cp_info.s_fluid_components_5g;

  if (cp_info.is_uds_transport && !cp_info.is_5g_sim) {
    sri_file_params->n_scalars = cp_info.n_scalars;
    sri_file_params->scalars = cp_info.s_scalar_materials;
  }

  sri_file_params->n_moving_face_xforms = cp_info.n_movb_xforms();
  sri_file_params->n_moving_faces = cp_info.n_movbs();
  if (sri_file_params->n_moving_faces > 0) {
    sri_file_params->moving_faces = cp_info.sri_movbs.data();
  }

  if (sri_file_params->n_moving_face_xforms > 0) {
    sri_file_params->moving_face_xforms = cp_info.sri_movb_xforms.data();
  }
  
  if (cp_info.n_dtires > 0 && window->n_moving_meas_cells > 0) {
    sri_file_params->first_moving_meas_surfel_index = window->n_stationary_meas_cells;
  }

  // Check if compression is asked for
  sri_file_params->m_enable_compression = (window->cdi_meas_window->mstp.standard_mask & CDI_MEAS_OPT_COMPRESS) > 0;

  if (cp_info.meas_compression == ENABLE_COMPRESSION)
    sri_file_params->m_enable_compression  = TRUE;
  else if (cp_info.meas_compression == DISABLE_COMPRESSION)
    sri_file_params->m_enable_compression = FALSE;

  sri_file_params->encryption_struct = cp_info.encryption_struct.get_sri_encryption_struct();

  //Only bother writing partitions information if the CDI file has that information
  //and if the measurement window itself has a GMRF or GEOS chunk associated with it
  //Some measurement windows such as Probes do not have hierarchy
  if (!cp_info.m_partitions_info.is_pre_heirarchical_partitions_cdi() &&
      window->cdi_meas_window->m_geom_selection) {
    sri_file_params->m_cdi_partitions = &cp_info.partitions();
    sri_file_params->m_cdi_geom_selection = window->cdi_meas_window->m_geom_selection;
  }
}

// this uses cp_info, as sri_file_params has pointers
// to this data
static VOID mark_all_lrf_mbc_and_movb_constant_vel()
{
  ccDOTIMES(i, cp_info.n_lrfs()) {
    cp_info.sri_lrfs[i].has_constant_angular_vel = TRUE;
  }
  ccDOTIMES(i, cp_info.n_mbcs()) {
    cp_info.sri_mbcs[i].has_constant_velocity = TRUE;
  }
  ccDOTIMES(i, cp_info.n_movb_xforms()) {
    cp_info.sri_movb_xforms[i].has_constant_velocity = TRUE;
  }
}

static VOID reset_all_lrf_mbc_and_movb_constant_vel()
{
  ccDOTIMES(i, cp_info.n_lrfs()) {
    cp_info.sri_lrfs[i].has_constant_angular_vel = cp_info.sri_lrf_has_constant_angular_vel_flags[i];
  }
  ccDOTIMES(i, cp_info.n_mbcs()) {
    cp_info.sri_mbcs[i].has_constant_velocity = cp_info.sri_mbc_has_constant_vel_flags[i];
  }
  ccDOTIMES(i, cp_info.n_movb_xforms()) {
    cp_info.sri_movb_xforms[i].has_constant_velocity = cp_info.sri_movb_xform_has_constant_velocity_flags[i];
  }
}

// Create parent directories if neccessary. Here file_name is an absolute path name starting with "/"
static void maybe_create_parent_directories(const char* file_name)
{
  //SRI_STATUS result_status = SRI_SUCCESS;
  char *name = strsave(file_name);  // Make a local copy
  if (name[0] != '/')
    msg_internal_error("Filename \"%s\"is not an absolute path!", name);
  
  char *ch = name + 1; // start from the second char
  while ((ch = strchr(ch, '/'))) {
    name[ch - name] = '\0';
    if (platform_file_present(name)) {
      if (!platform_file_is_dir(name)) {
        msg_error("Unable to create directory %s since a file with the same name exists.", name);
      }
    } else {
      if (!platform_createdir(name))
        msg_error("Unable to create directory %s, please check permissions in the parent directory.", name);
    }
    name[ch - name] = '/'; // restore the original name
    ch++;
  }
  delete[] name;
}

static VOID maybe_delete_nontemp_meas_file(STRING filename)
{
  // If creating a .tmp meas file, delete the regular meas file (which may exist due to previous runs)
  // to avoid confusion.
  if (strcmp(filename + strlen(filename) - 4, ".tmp") == 0) {
    STRING regular_filename = strsave(filename);
    regular_filename[strlen(filename) - 4] = '\0';
    if (platform_file_present(regular_filename)) {
      if (!platform_remove_file(regular_filename))
        msg_warn("Unable to delete the non-temp meas file %s, please check the permissions.", regular_filename);
    }
    delete[] regular_filename;
  }
}

template <typename MEAS_FLOAT_TYPE>
SRI_STATUS tCP_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>::open_sri_file(SRI_FILE *sri_file_return)
{
  //sSRI_FLUID_FILE_PARAMS sri_file_params;
  // Since sri_file_params may be used even after calling sri_open_file_for_write(), 
  // e.g., characteristic numbers sri_file_params->params could be used by monitors 
  // to derive some variables, we allocate it on the heap instead of on the stack.
  // This change is made for all CP meas window types below.
  SRI_FLUID_FILE_PARAMS persistent_sri_file_params = cnew sSRI_FLUID_FILE_PARAMS;
  sSRI_FLUID_FILE_PARAMS &sri_file_params = *persistent_sri_file_params;
  sri_file_params.are_special_fluid_volumes_present = FALSE;
  sri_file_params.n_nw_meas_cells   = n_nw_meas_cells();
  sri_file_params.n_nw_facet_sides  = m_n_nw_facet_sides;
  sri_file_params.n_nw_neighbors    = m_n_nw_neighbors;
  sri_file_params.n_nw_norm_meas_cells   = 0; //new
  
  initialize_sri_file_params(&sri_file_params, this);

  if (this->cdi_meas_window->start_time_via_monitors_p && this->cdi_meas_window->cyclic_buffer_size > 0) {
    sri_file_params.cyclic_buffer_size = this->cdi_meas_window->cyclic_buffer_size;
  }
  sri_file_params.cdi_window_id = this->cdi_window_index;
  if(!sri_file_params.is_sampling_meas_type && this->cdi_meas_window->fluid_meas_window_index >= 0 && this->cdi_meas_window->solid_meas_window_index >= 0)
    sri_file_params.file_has_a_mate = TRUE;
  else
    sri_file_params.file_has_a_mate = FALSE;
  if(this->meas_window_type == LGI_VOLUME_WINDOW)
    sri_file_params.is_solid_file = TRUE;
  else
    sri_file_params.is_solid_file = FALSE;
  sri_file_params.is_cond_sim = cp_info.is_conduction;
  sri_file_params.is_flow_sim = cp_info.is_flow;
  if (this->is_probe)
    mark_all_lrf_mbc_and_movb_constant_vel();
  STRING filename = this->compose_output_pathname();
  maybe_create_parent_directories(filename);
  maybe_delete_nontemp_meas_file(filename);
  sriBOOL probe_contains_fully_qualified_names = this->is_probe && !cdi_data.is_pre_partitions_cdi();
  SRI_STATUS status = sri_open_file_for_write(&sri_file_params, filename, 
                                              TRUE, this->file_per_frame_p,
                                              SRI_FLUID_TYPE, sri_file_return, 0, probe_contains_fully_qualified_names);
  delete[] filename;
  if (this->is_probe)
    reset_all_lrf_mbc_and_movb_constant_vel();
  return status;
}

template <typename MEAS_FLOAT_TYPE>
SRI_STATUS tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::open_sri_file(SRI_FILE *sri_file_return)
{
  //sSRI_SURFACE_FILE_PARAMS sri_file_params;
  SRI_SURFACE_FILE_PARAMS persistent_sri_file_params = cnew sSRI_SURFACE_FILE_PARAMS;
  sSRI_SURFACE_FILE_PARAMS &sri_file_params = *persistent_sri_file_params;
  initialize_sri_file_params(&sri_file_params, this);

  sri_file_params.n_vertices = m_n_vertices;
  sri_file_params.n_vertex_refs = m_n_vertex_refs;
  sri_file_params.n_surfel_blocks = this->surfel_block_map.size();
  sri_file_params.is_mean_vel_added = this->cdi_meas_window->is_rwnc_mean_vel_added;
  sri_file_params.n_surfels = m_n_surfels;
  sri_file_params.cdi_window_id = this->cdi_window_index;
  if(!sri_file_params.is_sampling_meas_type && this->cdi_meas_window->surface_meas_window_index >= 0 && this->cdi_meas_window->shell_meas_window_index >= 0)
    sri_file_params.file_has_a_mate = TRUE;
  else
    sri_file_params.file_has_a_mate = FALSE;

  if(this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW)
    sri_file_params.is_solid_file = TRUE;
  else
    sri_file_params.is_solid_file = FALSE;
  sri_file_params.is_cond_sim = cp_info.is_conduction;
  sri_file_params.is_flow_sim = cp_info.is_flow;
  SRI_MOVING_FACE moving_face = sri_file_params.moving_faces;
  ccDOTIMES(i, sri_file_params.n_moving_faces) {
    moving_face->n_surfels = this->m_moving_face_n_surfels[i];
    moving_face++;
  }

  if (m_facet_ids)
    sri_file_params.declare_facet_ids = TRUE;

  if (this->cdi_meas_window->start_time_via_monitors_p && this->cdi_meas_window->cyclic_buffer_size > 0) {
    sri_file_params.cyclic_buffer_size = this->cdi_meas_window->cyclic_buffer_size;
  }

  if (this->is_probe)
    mark_all_lrf_mbc_and_movb_constant_vel();
  STRING filename   = this->compose_output_pathname();
  maybe_create_parent_directories(filename);
  maybe_delete_nontemp_meas_file(filename);
  sriBOOL probe_contains_fully_qualified_names = this->is_probe && !cdi_data.is_pre_partitions_cdi();
  SRI_STATUS status = sri_open_file_for_write(&sri_file_params, filename, 
                                              TRUE, this->file_per_frame_p,
                                              SRI_SURFACE_TYPE, sri_file_return, 0, probe_contains_fully_qualified_names);
  delete[] filename;
  if (this->is_probe)
    reset_all_lrf_mbc_and_movb_constant_vel();
  return status;
}

template <typename MEAS_FLOAT_TYPE>
SRI_STATUS tCP_COMPOSITE_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::open_sri_file(SRI_FILE *sri_file_return)
{
  SRI_COMPOSITE_FILE_PARAMS persistent_sri_file_params = cnew sSRI_COMPOSITE_FILE_PARAMS;
  sSRI_COMPOSITE_FILE_PARAMS &sri_file_params = *persistent_sri_file_params;
  initialize_sri_file_params(&sri_file_params, this);
  
  if(this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW) //PR55174
    sri_file_params.is_solid_file = TRUE;
  
  if(this->is_composite) {
    vcopy(sri_file_params.reference_point, this->cdi_meas_window->reference_point);
    sri_file_params.is_reference_point_present = TRUE;
  }
  else
    sri_file_params.is_reference_point_present = FALSE;

  if (this->cdi_meas_window->start_time_via_monitors_p && this->cdi_meas_window->cyclic_buffer_size > 0) {
    sri_file_params.cyclic_buffer_size = this->cdi_meas_window->cyclic_buffer_size;
  }
  sri_file_params.cdi_window_id = this->cdi_window_index;
  if(!sri_file_params.is_sampling_meas_type && this->cdi_meas_window->surface_meas_window_index >= 0 && this->cdi_meas_window->shell_meas_window_index >= 0)
    sri_file_params.file_has_a_mate = TRUE;
  else
    sri_file_params.file_has_a_mate = FALSE;

  if(this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW)
    sri_file_params.is_solid_file = TRUE;
  else
    sri_file_params.is_solid_file = FALSE;
 
  SRI_MOVING_FACE moving_face = sri_file_params.moving_faces;
  ccDOTIMES(i, sri_file_params.n_moving_faces) {
    moving_face->n_surfels = this->m_moving_face_n_surfels[i];
    moving_face++;
  }
  sri_file_params.is_cond_sim = cp_info.is_conduction;
  sri_file_params.is_flow_sim = cp_info.is_flow;
  if (this->is_probe)
    mark_all_lrf_mbc_and_movb_constant_vel();
  STRING filename   = this->compose_output_pathname();
  maybe_create_parent_directories(filename);
  maybe_delete_nontemp_meas_file(filename);  
  sriBOOL probe_contains_fully_qualified_names = this->is_probe && !cdi_data.is_pre_partitions_cdi();
  SRI_STATUS status = sri_open_file_for_write(&sri_file_params, filename, 
                                              TRUE, this->file_per_frame_p,
                                              SRI_COMPOSITE_SURFACE_TYPE, sri_file_return, 0, probe_contains_fully_qualified_names);
  delete[] filename;
  if (this->is_probe)
    reset_all_lrf_mbc_and_movb_constant_vel();
  return status;
}

template <typename MEAS_FLOAT_TYPE>
SRI_STATUS tCP_COMPOSITE_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>::open_sri_file(SRI_FILE *sri_file_return)
{
  SRI_COMPOSITE_FLUID_FILE_PARAMS persistent_sri_file_params = cnew sSRI_COMPOSITE_FLUID_FILE_PARAMS;
  sSRI_COMPOSITE_FLUID_FILE_PARAMS &sri_file_params = *persistent_sri_file_params;
  initialize_sri_file_params(&sri_file_params, this);

  if(this->is_composite) {
    vcopy(sri_file_params.reference_point, this->cdi_meas_window->reference_point);
    sri_file_params.is_reference_point_present = TRUE;
  }
  else
    sri_file_params.is_reference_point_present = FALSE;
 
  if (this->cdi_meas_window->start_time_via_monitors_p && this->cdi_meas_window->cyclic_buffer_size > 0) {
    sri_file_params.cyclic_buffer_size = this->cdi_meas_window->cyclic_buffer_size;
  }
  sri_file_params.is_cond_sim = cp_info.is_conduction;
  sri_file_params.is_flow_sim = cp_info.is_flow;
  sri_file_params.cdi_window_id = this->cdi_window_index;
  if(!sri_file_params.is_sampling_meas_type && this->cdi_meas_window->fluid_meas_window_index >= 0 && this->cdi_meas_window->solid_meas_window_index >= 0)
    sri_file_params.file_has_a_mate = TRUE;
  else
    sri_file_params.file_has_a_mate = FALSE;
  if(this->meas_window_type == LGI_VOLUME_WINDOW)
    sri_file_params.is_solid_file = TRUE;
  else
    sri_file_params.is_solid_file = FALSE;

  if (this->is_probe)
    mark_all_lrf_mbc_and_movb_constant_vel();
  STRING filename   = this->compose_output_pathname();
  maybe_create_parent_directories(filename);
  maybe_delete_nontemp_meas_file(filename);  
  sriBOOL probe_contains_fully_qualified_names = this->is_probe && !cdi_data.is_pre_partitions_cdi();
  SRI_STATUS status = sri_open_file_for_write(&sri_file_params, filename, 
                                              TRUE, this->file_per_frame_p,
                                              SRI_COMPOSITE_FLUID_TYPE, sri_file_return, 0, probe_contains_fully_qualified_names);
  delete[] filename;
  if (this->is_probe)
    reset_all_lrf_mbc_and_movb_constant_vel();
  return status;
}

template <typename MEAS_FLOAT_TYPE>
SRI_STATUS tCP_SURFACE_DEV_MEAS_WINDOW<MEAS_FLOAT_TYPE>::open_sri_file(SRI_FILE *sri_file_return)
{
  SRI_SURFACE_DEV_FILE_PARAMS persistent_sri_file_params = cnew sSRI_SURFACE_DEV_FILE_PARAMS;
  sSRI_SURFACE_DEV_FILE_PARAMS &sri_file_params = *persistent_sri_file_params;
  initialize_sri_file_params(&sri_file_params, this);

  ccDOTIMES(axis,cp_info.n_dims) {
    vcopy(sri_file_params.start_point[axis],this->m_start_pt[axis]);
    vcopy(sri_file_params.end_point[axis],this->m_end_pt[axis]);
    sri_file_params.total_segments[axis] = this->m_num_segments[axis];
    sri_file_params.face_first_segment[axis] = this->m_face_first_segment[axis];
    sri_file_params.face_n_segments[axis] = this->m_face_n_segments[axis];
  }

  sri_file_params.csys = cdi_csys_to_sri_csys(this->cdi_meas_window->icsys);
  sri_file_params.lrf  = this->cdi_meas_window->ilrf;
  sri_file_params.is_cond_sim = cp_info.is_conduction;
  sri_file_params.is_flow_sim = cp_info.is_flow;
  sri_file_params.cdi_window_id = this->cdi_window_index;
  if(!sri_file_params.is_sampling_meas_type && this->cdi_meas_window->surface_meas_window_index >= 0 && this->cdi_meas_window->shell_meas_window_index >= 0)
    sri_file_params.file_has_a_mate = TRUE;
  else
    sri_file_params.file_has_a_mate = FALSE;

  if(this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW)
    sri_file_params.is_solid_file = TRUE;
  else
    sri_file_params.is_solid_file = FALSE;
 
  vcopy(sri_file_params.reference_point, this->cdi_meas_window->reference_point);
  sri_file_params.is_reference_point_present = TRUE;

  if (this->cdi_meas_window->start_time_via_monitors_p && this->cdi_meas_window->cyclic_buffer_size > 0) {
    sri_file_params.cyclic_buffer_size = this->cdi_meas_window->cyclic_buffer_size;
  }

  STRING filename   = this->compose_output_pathname();
  maybe_create_parent_directories(filename);
  maybe_delete_nontemp_meas_file(filename);  
  sriBOOL probe_contains_fully_qualified_names = this->is_probe && !cdi_data.is_pre_partitions_cdi();
  SRI_STATUS status = sri_open_file_for_write(&sri_file_params, filename, 
                                              TRUE, this->file_per_frame_p,
                                              SRI_SURFACE_DEV_TYPE, sri_file_return, 0, probe_contains_fully_qualified_names);
  delete[] filename;
  return status;
}

template <typename MEAS_FLOAT_TYPE>
SRI_STATUS tCP_FLUID_DEV_MEAS_WINDOW<MEAS_FLOAT_TYPE>::open_sri_file(SRI_FILE *sri_file_return)
{
  SRI_FLUID_DEV_FILE_PARAMS persistent_sri_file_params = cnew sSRI_FLUID_DEV_FILE_PARAMS;
  sSRI_FLUID_DEV_FILE_PARAMS &sri_file_params = *persistent_sri_file_params;

  initialize_sri_file_params(&sri_file_params, this);

  ccDOTIMES(axis,cp_info.n_dims) {
    vcopy(sri_file_params.start_point[axis],this->m_start_pt[axis]);
    vcopy(sri_file_params.end_point[axis],this->m_end_pt[axis]);
    sri_file_params.total_segments[axis] = this->m_num_segments[axis];
    sri_file_params.part_first_segment[axis] = this->m_part_first_segment[axis];
    sri_file_params.part_n_segments[axis] = this->m_part_n_segments[axis];
  }
 
  sri_file_params.csys = cdi_csys_to_sri_csys(this->cdi_meas_window->icsys);
  sri_file_params.lrf  = this->cdi_meas_window->ilrf;
  vcopy(sri_file_params.reference_point, this->cdi_meas_window->reference_point);
  sri_file_params.is_reference_point_present = TRUE;
  sri_file_params.cdi_window_id = this->cdi_window_index;
  if(!sri_file_params.is_sampling_meas_type && this->cdi_meas_window->fluid_meas_window_index >= 0 && this->cdi_meas_window->solid_meas_window_index >= 0)
    sri_file_params.file_has_a_mate = TRUE;
  else
    sri_file_params.file_has_a_mate = FALSE;
  if(this->meas_window_type == LGI_VOLUME_WINDOW)
    sri_file_params.is_solid_file = TRUE;
  else
    sri_file_params.is_solid_file = FALSE;

  // If cyclic buffer size == 0, do not use cyclic mode
  if (this->cdi_meas_window->start_time_via_monitors_p && this->cdi_meas_window->cyclic_buffer_size > 0) {
    sri_file_params.cyclic_buffer_size = this->cdi_meas_window->cyclic_buffer_size;
  }
  sri_file_params.is_cond_sim = cp_info.is_conduction;
  sri_file_params.is_flow_sim = cp_info.is_flow;
  STRING filename   = this->compose_output_pathname();
  maybe_create_parent_directories(filename);
  maybe_delete_nontemp_meas_file(filename);  
  sriBOOL probe_contains_fully_qualified_names = this->is_probe && !cdi_data.is_pre_partitions_cdi();
  SRI_STATUS status = sri_open_file_for_write(&sri_file_params, filename, 
                                              TRUE, this->file_per_frame_p,
                                              SRI_FLUID_DEV_TYPE, sri_file_return, 0, probe_contains_fully_qualified_names);
  delete[] filename;
  return status;
}

SRI_STATUS write_sri_meas_file_polyline_vertices(SRI_FILE sri_file)
{
  SRI_STATUS status = SRI_SUCCESS;
  ccDOTIMES(i, cp_info.n_lrfs()) {
    sriDOUBLE *polyline_vertices = cp_info.sri_lrf_polyline_vertices[i];
    status = sri_file->write_lrf_polyline_vertices(polyline_vertices, i, TRUE);
    if (status != SRI_SUCCESS)
      break;
  }
  return status;
}
  

static SRI_STATUS write_sri_meas_fluid_volumes(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE)window->sri_file;
  SRI_STATUS status = sri_file->write_fluid_volumes(window->m_volumes, window->n_meas_cells, 0, TRUE);
  return status;
}

static SRI_STATUS write_sri_meas_cell_coords(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_meas_cell_coords(window->m_coords, window->n_meas_cells, 0, TRUE);
  return status;
}

static SRI_STATUS write_sri_ref_frame_indices(CP_MEAS_WINDOW window)
{
  SRI_FILE sri_file = window->sri_file;
  SRI_STATUS status = sri_file->write_ref_frame_indices(window->m_ref_frame_indices.data(), window->n_meas_cells, 0, TRUE);
  return status;
}

static SRI_STATUS write_sri_surface_ref_frame_indices(CP_SURFACE_MEAS_WINDOW window)
{
  SRI_SURFACE_FILE sri_file = (SRI_SURFACE_FILE)window->sri_file;
  SRI_STATUS status = sri_file->write_ref_frame_indices(window->m_ref_frame_indices.data(), window->m_n_surfels, 0, TRUE);
  return status;
}

static SRI_STATUS write_sri_voxel_scales(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_voxel_scales(window->m_scales, 
                                                   window->n_meas_cells, 0, TRUE);
  return status;
}

static SRI_STATUS write_sri_meas_cell_part_indices(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_part_indices(window->m_part_indices, 
                                                   window->n_meas_cells, 0, TRUE);
  return status;
}

static SRI_STATUS write_sri_nw_meas_cell_indices(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_nw_meas_cell_indices(&window->m_nw_meas_cell_indices[0], 
							   window->n_nw_meas_cells(), 
							   0, TRUE);
  return status;
}

static SRI_STATUS write_sri_nw_centroids(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_nw_centroids(&window->m_nw_centroids[0], 
						   window->n_nw_meas_cells(), 
						   0, TRUE);
  return status;
}

#if LGI_COMPLETE_NEAR_WALL_MEAS_CELL_DATA // de-activated near wall meas cell info
static SRI_STATUS write_sri_nw_face_neighbors_masks(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_nw_face_neighbors_masks(window->m_nw_face_neighbors_masks, 
							      window->n_nw_meas_cells, 
							      0,
							      TRUE);
  return status;
}

static SRI_STATUS write_sri_nw_facet_sides(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_nw_facet_sides(window->m_nw_facet_sides, 
						     window->m_n_nw_facet_sides, 
						     0, TRUE);
  return status;
}

static SRI_STATUS write_sri_nw_first_facet_sides(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_nw_first_facet_sides(window->m_nw_first_facet_sides, 
							   window->m_n_nw_meas_cells, 
							   0, TRUE);
  return status;
}

static SRI_STATUS write_sri_nw_neighbors(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_nw_neighbors(window->m_nw_neighbors, 
						   window->m_n_nw_neighbors, 
						   0, TRUE);
  return status;
}

static SRI_STATUS write_sri_nw_neighbor_faces(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_nw_neighbor_faces(window->m_nw_neighbor_faces, 
							window->m_n_nw_neighbors, 
							0, TRUE);
  return status;
}

static SRI_STATUS write_sri_nw_first_neighbors(CP_FLUID_MEAS_WINDOW window)
{
  SRI_FLUID_FILE sri_file = (SRI_FLUID_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_nw_first_neighbors(window->m_nw_first_neighbors, 
							 window->m_n_nw_meas_cells, 
							 0, TRUE);
  return status;

}
#endif // de-activated near wall meas cell info

static SRI_STATUS write_sri_surfel_scales(CP_SURFACE_MEAS_WINDOW window)
{
  SRI_SURFACE_FILE sri_file = (SRI_SURFACE_FILE) window->sri_file;
  SRI_STATUS status = sri_file->write_surfel_scales(window->m_scales, 
                                                    window->m_n_surfels, 0, TRUE);
  return status;
}

static SRI_STATUS write_sri_face_indices(CP_SURFACE_MEAS_WINDOW window)
{
  SRI_SURFACE_FILE sri_file = (SRI_SURFACE_FILE)window->sri_file;
  SRI_STATUS status = sri_file->write_face_indices(window->m_faces, window->m_n_surfels,
                                                   0, TRUE);
  return status;

}

static SRI_STATUS write_sri_surface_info(CP_SURFACE_MEAS_WINDOW window)
{
  SRI_SURFACE_FILE sri_file = (SRI_SURFACE_FILE)window->sri_file;

  SRI_STATUS status = 0;

  if (status == 0 && window->surfel_block_map.size() > 0) {
    sriINT *block_data = reinterpret_cast<sriINT*>(&window->surfel_block_map[0]);
    status = sri_file->write_surfel_block_map(&window->surfel_block_map[0], window->surfel_block_map.size(), 0, TRUE);
  }
  // facet IDs are only written to sampling surface meas files (in support of PowerCOOL)
  if (window->m_facet_ids && status == 0)
    status = sri_file->write_facet_ids(window->m_facet_ids, window->m_n_surfels, 0, TRUE);

  if (status == 0)
    status = sri_file->write_areas(window->m_surfel_areas, window->m_n_surfels, 0, TRUE);

  if (status == 0)
    status = sri_file->write_normals(window->m_surfel_normals, window->m_n_surfels, 0, TRUE);

  if (status == 0)
    status = sri_file->write_first_vertex_refs(window->m_first_vertex_refs, window->m_n_surfels, 0, TRUE);



  // Write coords and vertex_refs. The loop below converts the vertex_refs from global
  // vertex indices to local vertex indices (i.e. indices for the reduced set of vertices 
  // that are actually written to this meas file).
  // 
  // Note that we change the contents of window->m_vertex_refs on the assumption that the 
  // original contents are no longer needed.
  sMEAS_WINDOW_VERTEX_MAP &vertex_map = g_meas_window_vertex_map; // just a vector of indices, one per surfel vertex

  // Clear vertex_map, which maps global vertex indices to local vertex indices.
  // If the meas window refers to a large percentage of the vertices in the case,
  // we just clear the entire vertex_map.
  if (window->m_n_vertex_refs < 0.2f * cp_info.vertex_array.size()) {
    ccDOTIMES(i, window->m_n_vertex_refs) {
      sriINT global_vertex_index = window->m_vertex_refs[i];
      vertex_map[ global_vertex_index ] = -1;
    }
  } else {
    vertex_map.clear();   // set all entries to -1
  }

  asINT32 n_dims = cp_info.n_dims;

  sriINT local_vertex_index       = 0;
  sriINT coord_index              = 0;
  sriINT coord_chunk_index        = 0;
  const asINT32 COORDS_CHUNK_SIZE = 4096;
  sriFLOAT coords[3 * COORDS_CHUNK_SIZE];
  ccDOTIMES(i, window->m_n_vertex_refs) {
    sriINT global_vertex_index = window->m_vertex_refs[i];
    if (vertex_map[ global_vertex_index ] < 0) {
      sVPoint *vertex = &cp_info.vertex_array[global_vertex_index];
      coords[ n_dims * coord_chunk_index + 0] = vertex->pcoord[0];
      coords[ n_dims * coord_chunk_index + 1] = vertex->pcoord[1];
      if (n_dims == 3)
        coords[ n_dims * coord_chunk_index + 2] = vertex->pcoord[2];
      coord_chunk_index++;

      vertex_map[ global_vertex_index ] = local_vertex_index++;

      if (coord_chunk_index == COORDS_CHUNK_SIZE) {
        status = sri_file->write_vertex_coords(coords, coord_chunk_index, coord_index, TRUE);
        if (status != SRI_SUCCESS)
          break;
        coord_index += coord_chunk_index;
        coord_chunk_index = 0;
      }
    }

    window->m_vertex_refs[i] = vertex_map[ global_vertex_index ];
  }

  if (status == SRI_SUCCESS && coord_chunk_index > 0)
    status = sri_file->write_vertex_coords(coords, coord_chunk_index, coord_index, TRUE);

  if (status == SRI_SUCCESS)
    status = sri_file->write_vertex_refs(window->m_vertex_refs, window->m_n_vertex_refs, 0, TRUE);

  return status;
}

SRI_STATUS write_sri_meas_file_grf_info(SRI_FILE sri_file, GRF_MEAS_FRAME_SP_TO_CP_MSG grf_info)
{
  SRI_STATUS status = SRI_SUCCESS;

#define sri_cmd(cmd) status = (cmd); if (status != SRI_SUCCESS) return status

  // The conditions under which we write global ref frame info (ref point vel & translation,
  // angular vel, effective axis, and angular rotation) must be in sync with what the SRI
  // library expects.
  if (!cp_info.global_nirf_info.is_ref_point_vel_constant)
    sri_cmd(sri_file->write_grf_ref_point_velocity(grf_info->ref_point_velocity, TRUE));

  if (!cp_info.global_nirf_info.is_ref_point_vel_constant        // !ref-point vel constant
      || !cp_info.global_nirf_info.is_angular_vel_constant)      // || !angular vel constant
    sri_cmd(sri_file->write_grf_translation(grf_info->translation, TRUE));

  if (!cp_info.global_nirf_info.is_angular_vel_constant          // !angular vel constant
      || (!cp_info.global_nirf_info.is_ref_point_vel_constant    // || (!ref-point vel constant
          && (cp_info.global_nirf_info.angular_vel[0] != 0       //     && non-zero angular vel)
              || cp_info.global_nirf_info.angular_vel[1] != 0
              || cp_info.global_nirf_info.angular_vel[2] != 0))) {
    sri_cmd(sri_file->write_grf_effective_axis(grf_info->point, grf_info->axis, TRUE));
    sri_cmd(sri_file->write_ref_frame_angular_rotation(grf_info->angle_rotated, SRI_GLOBAL_REF_FRAME_INDEX, TRUE));    
  }

  if (!cp_info.global_nirf_info.is_angular_vel_constant)
    sri_cmd(sri_file->write_grf_angular_vel(grf_info->angular_vel, TRUE));

#undef sri_cmd

  return status;
}

template <typename MEAS_FLOAT_TYPE>
SRI_STATUS tCP_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>::write_sri_header()
{

  CP_FLUID_MEAS_WINDOW window = (CP_FLUID_MEAS_WINDOW) (this);
  SRI_STATUS status = write_sri_meas_fluid_volumes(window);
  if (status == SRI_SUCCESS)
    status = write_sri_voxel_scales(window);
  if (status == SRI_SUCCESS)
    status = write_sri_meas_cell_part_indices(window);
  if (status == SRI_SUCCESS)
    status = write_sri_meas_cell_coords(window);
  if (status == SRI_SUCCESS && cp_info.n_lrfs() > 0)
    status = write_sri_ref_frame_indices(window);
  if (status == SRI_SUCCESS)
    status = write_sri_nw_meas_cell_indices(window);
  if (status == SRI_SUCCESS)
    status = write_sri_nw_centroids(window);
#if LGI_COMPLETE_NEAR_WALL_MEAS_CELL_DATA
  if (status == SRI_SUCCESS)
    status = write_sri_nw_face_neighbors_masks(window);
  if (status == SRI_SUCCESS)
    status = write_sri_nw_facet_sides(window);
  if (status == SRI_SUCCESS)
    status = write_sri_nw_first_facet_sides(window);
  if (status == SRI_SUCCESS)
    status = write_sri_nw_neighbors(window);
  if (status == SRI_SUCCESS)
    status = write_sri_nw_neighbor_faces(window);
  if (status == SRI_SUCCESS)
    status = write_sri_nw_first_neighbors(window);
#endif
  return status;
}

static SRI_STATUS write_sri_ref_frame_state(CP_MEAS_WINDOW window)
{
  SRI_STATUS status = SRI_SUCCESS;
  SRI_FILE sri_file = window->sri_file;

#define sri_cmd(cmd) status = (cmd); if (status != SRI_SUCCESS) return status
  
  sriLRF_INDEX *time_varying_lrf_indices = &cp_info.time_varying_lrf_indices[0];
  ccDOTIMES(i, cp_info.n_time_varying_lrfs()) {
    LRF_MEAS_FRAME_SP_TO_CP_MSG lrf = window->lrf_return_buffer(i);
    asINT32 index = *time_varying_lrf_indices++;
    sri_cmd(sri_file->write_ref_frame_angular_rotation(lrf->angle_rotated, index, TRUE));
    sri_cmd(sri_file->write_ref_frame_n_revolutions(lrf->n_revolutions, index, TRUE));
    sri_cmd(sri_file->write_lrf_angular_vel_mag(lrf->angular_vel_mag, index, TRUE));
    if (window->rotational_dynamics_index > -1) {
      sri_cmd(sri_file->write_lrf_moment_of_inertia(cp_info.rotational_dynamics_descs[window->rotational_dynamics_index].m_moment_of_inertia, index, TRUE));
    }
  }
  
  if (cp_info.is_global_ref_frame) {
    // Process the global ref frame
    GRF_MEAS_FRAME_SP_TO_CP_MSG grf_info = window->grf_return_buffer();
    sri_cmd(write_sri_meas_file_grf_info(sri_file, grf_info));
  }

#undef sri_cmd

  return status;
}

static SRI_STATUS write_sri_mbc_state(CP_MEAS_WINDOW window)
{
  SRI_STATUS status = SRI_SUCCESS;
  SRI_FILE sri_file = (SRI_FILE)window->sri_file;

#define sri_cmd(cmd) status = (cmd); if (status != SRI_SUCCESS) return status
  
  sriMBC_INDEX *time_varying_mbc_indices = &cp_info.time_varying_mbc_indices[0];
  ccDOTIMES(i, cp_info.n_time_varying_mbcs()) {
    MBC_MEAS_FRAME_SP_TO_CP_MSG mbc = window->mbc_return_buffer(i);
    asINT32 index = *time_varying_mbc_indices++;
    if (cp_info.sri_mbcs[index].type == SRI_MBC_ROTATING) {
      sri_cmd(sri_file->write_mbc_angular_vel_mag(mbc->angular_vel_mag, index, TRUE));
    } else {
      sri_cmd(sri_file->write_mbc_linear_vel(mbc->linear_vel, index, TRUE));
    }
  }

  return status;
}

static SRI_STATUS write_sri_moving_xforms_state(CP_MEAS_WINDOW window)
{
  SRI_STATUS status = SRI_SUCCESS;
  SRI_FILE sri_file = (SRI_FILE)window->sri_file;

#define sri_cmd(cmd) status = (cmd); if (status != SRI_SUCCESS) return status

  ccDOTIMES(i, cp_info.n_movb_xforms()) {
    MOVB_MEAS_FRAME_SP_TO_CP_MSG movb_msg = window->movb_return_buffer(i);
    sSRI_XFORM sri_xform;
    sri_xform = movb_msg->xform;
    sri_cmd(sri_file->write_moving_face_xform(&sri_xform, i, TRUE));
  }

  return status;
}

template <typename MEAS_FLOAT_TYPE>
SRI_STATUS tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::write_sri_header()
{

  CP_SURFACE_MEAS_WINDOW window = (CP_SURFACE_MEAS_WINDOW)(this);
  SRI_STATUS status = write_sri_surfel_scales(window);
  if (status == SRI_SUCCESS && cp_info.n_lrfs() > 0)
    status = write_sri_surface_ref_frame_indices(window);
  if (status == SRI_SUCCESS)
    status = write_sri_face_indices(window);
  if (status == SRI_SUCCESS)
    status = write_sri_surface_info(window);
  return status;
}

template <typename MEAS_FLOAT_TYPE>
SRI_STATUS tCP_COMPOSITE_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::write_sri_header()
{
  SRI_COMPOSITE_SURFACE_FILE sfile = (SRI_COMPOSITE_SURFACE_FILE)this->sri_file;
  SRI_STATUS status = sfile->write_face_indices(m_faces, this->n_meas_cells, 0, TRUE);

  if (status == SRI_SUCCESS)
    status = sfile->write_face_areas(m_face_areas, this->n_meas_cells, 0, TRUE);
  if (status == SRI_SUCCESS)
    status = sfile->write_projected_face_areas(m_projected_areas, this->n_meas_cells, 0, TRUE);
  if (status == SRI_SUCCESS && cp_info.n_lrfs() > 0)
    status = write_sri_ref_frame_indices(this);
  return status;
}

template <typename MEAS_FLOAT_TYPE>
SRI_STATUS tCP_COMPOSITE_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>::write_sri_header()
{
  SRI_COMPOSITE_FLUID_FILE sfile = (SRI_COMPOSITE_FLUID_FILE)this->sri_file;

  SRI_STATUS status = sfile->write_part_indices(m_part_indices, this->n_meas_cells, 
                                                0, TRUE);

  if (status == SRI_SUCCESS)
    status = sfile->write_part_volumes(m_part_volumes, this->n_meas_cells, 0, TRUE);

  if (status == SRI_SUCCESS && cp_info.n_lrfs() > 0)
    status = write_sri_ref_frame_indices(this);

  return status;
}

VOID sCDI_MEAS_WINDOW::insert_post_meas_cmd_in_queue(POST_MEAS_CMD entry)
{
  POST_MEAS_CMD queue = cmd_queue;
  if ((queue == NULL)
      || (entry->issue_frame < queue->issue_frame)) {
    cmd_queue = entry;
    entry->next = queue;
  } else {
    POST_MEAS_CMD before = queue;
    POST_MEAS_CMD after = before->next;
    while (after != NULL) {
      if (after->issue_frame > entry->issue_frame)
	break;
      before = after;
      after = before->next;
    }
    before->next = entry;
    entry->next = after;
  }
}

VOID sCP_MEAS_WINDOW::sync(WALLCLOCK_TIME_SECS time_secs)
{
  sri_file->sync_file();
  n_bytes_since_last_sync = 0;
  m_time_of_last_sync = time_secs;
}

static VOID maybe_issue_post_meas_cmds(CP_MEAS_WINDOW window, WALLCLOCK_TIME_SECS time_secs,
                                       TIMESTEP window_end_time)
{
  CDI_MEAS_WINDOW cdi_meas_win = window->cdi_meas_window;
  POST_MEAS_CMD cmd = cdi_meas_win->cmd_queue;

  // Force the meas window data to disk before the cmd is issued
  if (cmd && (window->prev_nsets_written == cmd->issue_frame))
    window->sync(time_secs);

  cdi_meas_win->n_meas_files_written ++;

  if (cdi_meas_win->n_meas_files_written < cdi_meas_win->n_meas_files)
    return;

  cdi_meas_win->n_meas_files_written = 0;
  
  while (cmd && (window->prev_nsets_written == cmd->issue_frame)) {
    char fullcmd[16 * 1024];
    // Also note that "." is added to the front of PATH.
    if (cmd->table_desc == NULL) {
      sprintf(fullcmd, 
	      "PATH=.:$PATH;"
	      " EXA_MEAS_WINDOW=\"%s\"; EXA_MEAS_FRAME=%d; export PATH EXA_MEAS_WINDOW EXA_MEAS_FRAME ; %s",
	      cdi_meas_win->name, cmd->issue_frame, cmd->cmd_string);

    } else {
      char table_location[PLATFORM_MAXPATHLEN];
      if (is_table_file_present(cmd->table_desc->filename, NULL, table_location))
	msg_warn("Table file \"%s\" (table \"%s\") exists before associated shell command"
		 " has been issued at timestep %d. Deleting the file before executing the shell command.",
		 table_location, cmd->table_desc->name, (int)window_end_time);

      sprintf(fullcmd, 
	      "rm -f \"%s\";"
	      "PATH=.:$PATH;"
	      " EXA_MEAS_WINDOW=\"%s\"; EXA_MEAS_FRAME=%d;"
	      " EXA_TABLE=\"%s\"; EXA_TABLE_FILENAME=\"%s\"; EXA_TABLE_INDEX=%s;"
	      " export PATH EXA_MEAS_WINDOW EXA_MEAS_FRAME EXA_TABLE EXA_TABLE_FILENAME EXA_TABLE_INDEX; %s",
	      cmd->table_desc->filename,
	      cdi_meas_win->name, cmd->issue_frame - window->base_frame, 
	      cmd->table_desc->name, cmd->table_desc->filename, cmd->table_desc->index,
	      cmd->cmd_string);

      cmd->table_desc->has_cmd_been_issued = TRUE;
    }

    cTHIRD_PARTY_INTERFACE::AsyncSystemCmd(fullcmd);

    cdi_meas_win->cmd_queue = cmd->next;
    if (cmd->period > 0) {
      cmd->issue_frame += cmd->period;
      cdi_meas_win->insert_post_meas_cmd_in_queue(cmd);
    }
    cmd = cdi_meas_win->cmd_queue;
  }
}

static SRI_STATUS create_new_sri_measurement_file(CP_MEAS_WINDOW window)
{

  SRI_STATUS status = window->open_sri_file(&window->sri_file);
  if (status != SRI_SUCCESS) {
    msg_error("Unable to open measurement file \"%s\": %s.",
              window->output_filename, sri_error_string(status));
  }
  
  status = window->write_sri_header();

  if (status == SRI_SUCCESS)
    status = write_sri_meas_file_polyline_vertices(window->sri_file);

  return status;
}

VOID sCP_MEAS_WINDOW::open_sri_file_for_resume()
{
  // File isn't open because we have just restarted from a checkpoint
  STRING filename = compose_output_pathname();
  SRI_STATUS status = sri_open_file_for_append(filename, NULL, &sri_file);
  delete filename;

  if (status != SRI_SUCCESS) { 
    // Measurement file is inaccessible. User was already warned at startup.
    create_new_sri_measurement_file(this);
  } else {
    base_frame = sri_file->file_params()->base_frame;
  }
    
  if (cp_info.is_full_checkpoint_restore) 
    sri_file->add_full_restart(cp_info.restart_time, TRUE);
  else
    sri_file->add_mme_restart(cp_info.restart_time, TRUE);
}

template <typename MEAS_FLOAT_TYPE> 
VOID tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::process_flow_monitors()
{
  process_flow_monitors_internal<MEAS_FLOAT_TYPE>();
}

template <typename MEAS_FLOAT_TYPE> 
VOID tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::process_flow_monitors()
{
  process_flow_monitors_internal<MEAS_FLOAT_TYPE>();
}

template <typename MEAS_FLOAT_TYPE> 
VOID tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::process_solid_monitors()
{
  process_solid_monitors_internal<MEAS_FLOAT_TYPE>();
}

template <typename MEAS_FLOAT_TYPE> 
VOID tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::process_solid_monitors()
{
  process_solid_monitors_internal<MEAS_FLOAT_TYPE>();
}



template <typename MEAS_FLOAT_TYPE> 
// Support both composite (reduction) and probe (no reduction) meas windows
VOID sCP_MEAS_WINDOW::process_flow_monitors_internal()
{
  if (m_flow_monitors.size() <= 0)
    return;
    
  MEAS_FLOAT_TYPE ref_point[3] = {0, 0, 0}; // Used for moment type variables
  MEAS_FLOAT_TYPE data = 0.0;

  //Get reference point from CDI meas windows. Used for moment type variables
  ref_point[0] = cdi_meas_window->reference_point[0];    
  ref_point[1] = cdi_meas_window->reference_point[1];
  ref_point[2] = cdi_meas_window->reference_point[2];

  // Get scale factors for all meas cells
  sriFLOAT scale_factors[n_meas_cells];
  sriFLOAT* meas_cell_scale_factors = cell_scale_factors();
  // cell_scale_factors returns m_part_volumes[] for composite fluid meas
  // and m_face_areas[] for composite surface meas. The values are actually
  // the inverse scale factors, need to invert to get the real numbers
  ccDOTIMES(cell, n_meas_cells)
    scale_factors[cell] = 1.0 / meas_cell_scale_factors[cell]; 

  // Change monitor->m_ref_point[] to MEAS_FLOAT_TYPE as it will be used
  // by the template function transform_moment_to_csys_ref_point();
  MEAS_FLOAT_TYPE monitor_ref_point[3] = {0, 0, 0};
 
  // process all flow monitors which use variables measured by this meas window
  ccDOTIMES(i, m_flow_monitors.size()) {
    
    FLOW_MONITOR monitor = cp_info.flow_monitors[m_flow_monitors[i]];

    monitor_ref_point[0] = monitor->m_ref_point[0];
    monitor_ref_point[1] = monitor->m_ref_point[1];
    monitor_ref_point[2] = monitor->m_ref_point[2];

    // Pick the appropriate sri_var_type from the monitor
    SRI_VARIABLE_TYPE var_type = SRI_VARIABLE_INVALID;

    // Handle UDS variables first
    if (monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::UDS) {
      var_type = monitor->m_uds_var_type;
      // UDS variables are measured in fluid windows
      data = sri_file->sri_integrate_var<MEAS_FLOAT_TYPE>(monitor_ref_point,
                                                          monitor->m_csys_index,
                                                          scale_factors,
                                                          m_ref_frame_indices.data(),
                                                          ref_point,
                                                          monitor->m_is_meas_cell_in_monitor,
                                                          var_type);
    } else if (meas_window_type == LGI_FLUID_WINDOW || meas_window_type == LGI_POROUS_WINDOW) {
      var_type = monitor->m_fluid_var_type;
      data = sri_file->sri_integrate_var<MEAS_FLOAT_TYPE>(monitor_ref_point,
                                                          monitor->m_csys_index,
                                                          scale_factors,
                                                          m_ref_frame_indices.data(),
                                                          ref_point,
                                                          monitor->m_is_meas_cell_in_monitor,
                                                          var_type);
    } else { // meas_window_type = LGI_SURFACE_WINDOW or LGI_SAMPLING_SURFACE_WINDOW
      var_type = monitor->m_surface_var_type;
      data = sri_file->sri_integrate_var<MEAS_FLOAT_TYPE>(monitor_ref_point,
                                                          monitor->m_csys_index,
                                                          scale_factors,
                                                          m_ref_frame_indices.data(),
                                                          ref_point,
                                                          monitor->m_is_meas_surfel_in_monitor,
                                                          var_type);
    }
/*
     Both forces and moments are required for moment-type varibles (either 
     SRI_MOMENT or SRI_SCALAR_ARRAY, where SRI_MOMENT variables are:
     XTORQUE, YTORQUE, ZTORQUE, 
     XTORQUE_TRUE, YTORQUE_TRUE, ZTORQUE_TRUE,
     FLUID_XTORQUE, FLUID_YTORQUE, FLUID_ZTORQUE, 
     FLUID_XTORQUE_TRUE, FLUID_YTORQUE_TRUE, FLUID_ZTORQUE_TRUE,
     
     SRI_SCALAR_ARRAY variables are:
     SURFACE_ROLL, SURFACE_YAW, SURFACE_PITCH, 
     SURFACE_FRONT_LIFT, SURFACE_REAR_LIFT)
     FLUID_ROLL, FLUID_YAW, FLUID_PITCH, 
     FLUID_FRONT_LIFT, FLUID_REAR_LIFT)

     Note that drag, lift and side force are derivable, thus can be obtained
     by using get_variable_from_sri() directly.
*/
    // If the monitor is using variable from surface+porous(fan), fill the corresponding data
    if (monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::SurfacePlusFanAndPorous 
        && monitor->m_n_cp_meas_windows_written < 2) { // n_cp_meas_windows_written = 0 or 1
      if (meas_window_type == LGI_POROUS_WINDOW) {// Fan or porous
        monitor->m_porous_data = data;
      } else if (meas_window_type == LGI_SURFACE_WINDOW || meas_window_type == LGI_SAMPLING_SURFACE_WINDOW) {
        monitor->m_surface_data = data;
      } else  // Should never happen
        msg_internal_error("Meas Window Type %s does not match flow monitor \"%s\" variable source %d",
                           lgi_meas_window_type_to_name(meas_window_type), 
                           monitor->m_name, monitor->m_var_source);
      
      monitor->m_n_cp_meas_windows_written++;
      if (monitor->m_n_cp_meas_windows_written < 2)  // average of surface and porous(fan) data not available yet
        continue;
    }

    // Note that we use the meas window output timestep here as the signal timestep
    // so that it is consistent with heat exchanger monitors and PowerTHERM monitors.
    // The measurement timestep is the middle of meas window, which diffs from the 
    // signal timestep by half period.
  
    //TIMESTEP signal_timestep = m_output_timestep - 0.5 * cdi_meas_window->time_desc.period;
    TIMESTEP signal_timestep = m_output_timestep;

    if (monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::SurfacePlusFanAndPorous) {
      data = monitor->m_porous_data + monitor->m_surface_data;
      monitor->m_n_cp_meas_windows_written = 0;
    }
    monitor->append_and_analyze_signal(data, signal_timestep, m_next_update_time.output_time);
  }
}

template <typename MEAS_FLOAT_TYPE> 
// Support both composite (reduction) and probe (no reduction) meas windows
VOID sCP_MEAS_WINDOW::process_solid_monitors_internal()
{
  if (m_solid_monitors.size() <= 0)
    return;
    
  MEAS_FLOAT_TYPE ref_point[3] = {0, 0, 0}; // Used for moment type variables
  MEAS_FLOAT_TYPE data = 0.0;

  //Get reference point from CDI meas windows. Used for moment type variables
  ref_point[0] = cdi_meas_window->reference_point[0];    
  ref_point[1] = cdi_meas_window->reference_point[1];
  ref_point[2] = cdi_meas_window->reference_point[2];

  // Get scale factors for all meas cells
  sriFLOAT scale_factors[n_meas_cells];
  sriFLOAT* meas_cell_scale_factors = cell_scale_factors();
  // cell_scale_factors returns m_part_volumes[] for composite solid meas
  // and m_face_areas[] for composite shell meas. The values are actually
  // the inverse scale factors, need to invert to get the real numbers
  ccDOTIMES(cell, n_meas_cells)
    scale_factors[cell] = 1.0 / meas_cell_scale_factors[cell]; 

  // Change monitor->m_ref_point[] to MEAS_FLOAT_TYPE as it will be used
  // by the template function transform_moment_to_csys_ref_point();
  MEAS_FLOAT_TYPE monitor_ref_point[3] = {0, 0, 0};
 
  // process all solid monitors which use variables measured by this meas window
  ccDOTIMES(i, m_solid_monitors.size()) {
    
    SOLID_MONITOR monitor = cp_info.solid_monitors[m_solid_monitors[i]];

    monitor_ref_point[0] = monitor->m_ref_point[0];
    monitor_ref_point[1] = monitor->m_ref_point[1];
    monitor_ref_point[2] = monitor->m_ref_point[2];

    // Pick the appropriate sri_var_type from the monitor
    SRI_VARIABLE_TYPE var_type = SRI_VARIABLE_INVALID;
    
    if (meas_window_type == LGI_VOLUME_WINDOW) {
      var_type = monitor->m_solid_var_type;
      data = sri_file->sri_integrate_var<MEAS_FLOAT_TYPE>(monitor_ref_point,
                                                          monitor->m_csys_index,
                                                          scale_factors,
                                                          m_ref_frame_indices.data(),
                                                          ref_point,
                                                          monitor->m_is_meas_cell_in_monitor,
                                                          var_type);
    } else { // meas_window_type = LGI_SHELL_WINDOW or LGI_SAMPLING_SHELL_WINDOW
      var_type = monitor->m_shell_var_type;
      data = sri_file->sri_integrate_var<MEAS_FLOAT_TYPE>(monitor_ref_point,
                                                          monitor->m_csys_index,
                                                          scale_factors,
                                                          m_ref_frame_indices.data(),
                                                          ref_point,
                                                          monitor->m_is_meas_surfel_in_monitor,
                                                          var_type);
    } 
    // If the monitor is using variable from shell+solid, fill the corresponding data
    if (monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::ShellPlusSolid 
        && monitor->m_n_cp_meas_windows_written < 2) { // n_cp_meas_windows_written = 0 or 1
      if (meas_window_type == LGI_VOLUME_WINDOW) {// Solid
        monitor->m_solid_data = data;
      } else if (meas_window_type == LGI_SHELL_WINDOW || meas_window_type == LGI_SAMPLING_SHELL_WINDOW) {
        monitor->m_shell_data = data;
      } else  // Should never happen
        msg_internal_error("Meas Window Type %s does not match solid monitor \"%s\" variable source %d",
                           lgi_meas_window_type_to_name(meas_window_type), 
                           monitor->m_name, monitor->m_var_source);
      
      monitor->m_n_cp_meas_windows_written++;
      if (monitor->m_n_cp_meas_windows_written < 2)  
        continue;
    }

    TIMESTEP signal_timestep = m_output_timestep;

    if (monitor->m_var_source == eCDI_MNTR_FLOW_VAR_SRC::ShellPlusSolid) {
      data = monitor->m_solid_data + monitor->m_shell_data;
      monitor->m_n_cp_meas_windows_written = 0;
    }
    monitor->append_and_analyze_signal(data, signal_timestep, m_next_update_time.output_time);
  }
}

VOID sCP_MEAS_WINDOW::write_sri_results(WALLCLOCK_TIME_SECS time_secs)
{
  sriINT window_start_time  = m_clear_timestep;   /* in realm steps */
  sriINT window_end_time    = m_output_timestep;  /* in realm steps */

  // It is possible that the meas window writing event is in the queue while it already passed the end 
  // time (e.g. when the meas window is specified by using (start_by_monitor + duration)). In that case 
  // we should ignore this writing event. See PR40199.
  // We don't need to worry about heat exchanger meas windows which have end_time set to be -1 because 
  // heat exchanger meas windows are never started by monitors.
  BOOLEAN is_cond_realm = this->is_cond_window();
  TIMESTEP cdi_meas_window_end_timestep = (is_cond_realm)
                                           ? cp_info.convert_to_ts_cond(cdi_meas_window->end_time)
                                           : cp_info.convert_to_ts_flow(cdi_meas_window->end_time);
  if (cdi_meas_window->start_time_via_monitors_p  && window_start_time > cdi_meas_window_end_timestep) {
    return;
  }

  // prev_nsets_written can be greater than 0 when restarting from checkpoint
  if (prev_nsets_written == 0) {
    create_new_sri_measurement_file(this);
  }
  else if (sri_file == NULL) {
    // File isn't open because we have just restarted from a checkpoint
    open_sri_file_for_resume();
  }

  // If this meas window is started via monitors, it is possible that the meas window is not really 
  // started when all the monitors reach the end of initial transient. For example, if there is only
  // one monitor controlling the meas window, and the monitor EIT and EITDT are in the same or two 
  // adjacent frames of the meas window started. Should check if we need to start the meas window here.

  if (first_set_to_write_in_file == (prev_nsets_written - base_frame)) {
    // Start the measurement window, rename .fnc(snc).tmp measurement file to .fnc(snc)
    sri_file->turn_off_cyclic_mode(TRUE, first_set_to_write_in_file, first_set_to_write_in_file, first_set_to_write_in_file); // stop cyclic mode and set the first retained frame in buffer
    
    // If using file_per_frame_p, the header file will be renamed 
    STRING new_output_filename = strsave(output_filename);
    // It is possible that ".tmp" has already been removed from the filename, which could happen when resuming from ckpt (original run passed
    // the end of initial transient while the ckpt timestep is before that). In that case do not change the filename
    if (strcmp(output_filename + strlen(output_filename) - 4, ".tmp") == 0) {
      new_output_filename[strlen(new_output_filename) - 4] = '\0'; // Remove ".tmp" in the end
        if (-1 == rename(output_filename, new_output_filename)) {
          msg_warn("Could not rename \"%s\" to \"%s\": %s",
                    output_filename, new_output_filename, strerror((errno)));
        }
      delete[] new_output_filename;
    }
  }


#if COPY_TMP_MEAS_FILES_AT_CKPT
  // If the meas window is started via monitors, do not write data if :
  //    * The meas window has not reached tmp_start_time
  //    * the buffer length is 0 (no need to store temporary frames)
  sri_file->set_meas_frame(prev_nsets_written - base_frame);
  if (sri_file->meas_frame() >= 0) {
#else


  if (cdi_meas_window->meas_started_p) {
    //sri_file->m_cyclic_buffer_size = -1;
    if (sri_file->first_retained_frame() < 0)
      sri_file->reset_first_retained_frame();   // The meas window has started but somehow the first retained frame is still -1.
                                                // This could happen when resuming from ckpt while the user deleted the meas file
                                                // Should set the first retained frame to be 0 since we write from the beginning of the buffer zone.
  }

  // If the meas window is started via monitors, do not write data if :
  //    * The meas window has not reached tmp_start_time
  //    * the buffer length is 0 (no need to store temporary frames) 
  //    * Restoring from ckpt, and it has not reached the old first retained frame
  if ((prev_nsets_written - base_frame) >= sri_file->first_retained_frame()) {

    // If resuming from ckpt while the meas window is not started (stilling writing to .tmp 
    // meas file), find the first frame to be written to file after the ckpt. It will to 
    // used to find the earliest good frame in the cyclic buffer later when the meas window
    // is started (monitor end of initial transient is found).

    // If resuming from ckpt and it is writing the frame (old_first_retained_frame + cyclic_buffer_size) 
    // while the new first retained frame has yet to be determined, turn on cyclic mode and reset
    // n_empty_cyclic_buffer_slots_in_file to be cyclic_buffer_size.
    BOOLEAN is_full_checkpoint_restore = (sim_args.resume_filename && !g_seed_ctl.is_mme_checkpoint());
    BOOLEAN is_mme_checkpoint_restore = g_seed_ctl.is_mme_checkpoint();

    if (cdi_meas_window->start_time_via_monitors_p &&
        !cdi_meas_window->meas_started_p &&
        (is_full_checkpoint_restore || is_mme_checkpoint_restore)) {
      if (first_frame_after_ckpt == -1) {
        first_frame_after_ckpt = prev_nsets_written - base_frame;
#if DEBUG_MONITOR
        msg_print("First set written to cyclic buffer after ckpt is %d, base_frame = %d", first_frame_after_ckpt, base_frame);
#endif
      }
     
      if (sri_file->first_retained_frame() >= 0) {
        if ((prev_nsets_written - base_frame) == (sri_file->first_retained_frame() + sri_file->cyclic_buffer_size())) {
          // We ignore the status returned here because we set "try_until_success" to be TRUE
          sri_file->reset_buffer_info(TRUE);
        }
      }
    }

    sri_file->set_meas_frame(prev_nsets_written - base_frame);
#endif
    sri_file->write_meas_frame_time(window_start_time, window_end_time, TRUE);
  
    SRI_STATUS status = SRI_SUCCESS;
    if (!is_probe) {
      status = write_sri_ref_frame_state(this);
      if (status != SRI_SUCCESS)
        msg_internal_error("%s: Failed to write reference frame state: %s", 
                           output_filename, sri_file->error_string(status));
  
      status = write_sri_mbc_state(this);
      if (status != SRI_SUCCESS)
        msg_internal_error("%s: Failed to write moving BC state: %s", 
                           output_filename, sri_file->error_string(status));

      status = write_sri_moving_xforms_state(this);
      if (status != SRI_SUCCESS)
	msg_internal_error("%s: Failed to write moving face xform state: %s", 
			   output_filename, sri_file->error_string(status));
      }
  
    if (coupling_window_p && sri_file_type != SRI_SURFACE_TYPE) {
      msg_internal_error("Coupling measurement windows can only be of surface measurement type");
    }
  
    // precompute some measurement scaling the first time this function is called
    precompute_cell_scale_factors();
    TIMESTEP lcm_solver_steps = (is_cond_realm) ? cdi_meas_window->lcm_cond_tsteps : cdi_meas_window->lcm_flow_tsteps;

    sriFLOAT time_scale = static_cast<sriFLOAT>(lcm_solver_steps) / static_cast<sriFLOAT>(window_end_time - window_start_time);
  
    bool some_std_dev_var = FALSE;
    ccDOTIMES(i, n_variables) {
      switch (var_types[i]) {
      case SRI_VARIABLE_STD_DEV_XVEL:
      case SRI_VARIABLE_STD_DEV_YVEL:
      case SRI_VARIABLE_STD_DEV_ZVEL:
      case SRI_VARIABLE_STD_DEV_VEL_MAG:
      case SRI_VARIABLE_STD_DEV_PRESSURE:
      case SRI_VARIABLE_STD_DEV_TEMP:
      case SRI_VARIABLE_STD_DEV_DENSITY:
        some_std_dev_var = TRUE;
        break;
      default:
        break;
      }
    }
  
    write_sri_variables(some_std_dev_var, time_scale);
  
    n_bytes_since_last_sync += n_bytes_per_frame;
  
    const asINT32 N_BYTES_BEFORE_SYNC = 8 * 1024;
  
    // Call nc_sync on the first frame so that the user has something to
    // look at and also because the visualizer just hates to see a .nc
    // file with a header and no complete set of measurements.
    if ((cdi_meas_window->flush_every_frame) 
        || (prev_nsets_written == 0)
        || (n_bytes_since_last_sync >= N_BYTES_BEFORE_SYNC)) {
      sync(time_secs);
    }
  }
 
#if DEBUG_MONITOR_CKPT && !COPY_TMP_MEAS_FILES_AT_CKPT
  if ((prev_nsets_written - base_frame) < sri_file->first_retained_frame()) {
    msg_print("Current sim frame %d < old first retained frame %d in sri file",
              prev_nsets_written - base_frame, sri_file->first_retained_frame());
  }
#endif

  maybe_issue_post_meas_cmds(this, time_secs, window_end_time);

  // Process autostop flow monitors only for composite and probe meas windows
  if (is_composite || is_probe) {
    process_flow_monitors();
    process_solid_monitors();
  }

  sri_file->close_current_frame();
  /* Update results count */
  ++ prev_nsets_written;
  }


static inline dFLOAT maybe_timestep_to_lattice_time_inc_scale_factor(SRI_VARIABLE_TYPE var_type, sINT32  lcm_solver_steps)  {
  // Modify the scale factor for the following particle solver
  // related var types to rescale from LatticeTime to
  // LatticeTimeInc.  Also, for subcycling, some particle modeling
  // quantities have to be sampled every particle timestep, not just
  // timesteps where all solvers are active. The lcm_solver_steps
  // factor in base_time is accounted for here for selcect var types.
  
  if(var_type >= SRI_VARIABLE_FIRST_USER_VARIABLE) {
    if(!cp_info.cvid_helper->is_pm_var_id(var_type))
      return 1.0;
    
    eSRI_VARIABLE_PM_OFFSET pm_var_offset = cp_info.cvid_helper->get_pm_var_type(var_type);
    var_type = cp_info.cvid_helper->pm_offset_to_sri_type(pm_var_offset); 
  }
  
  switch(var_type) { 
  case SRI_VARIABLE_PRTCL_MASS_FLUX: 
  case SRI_VARIABLE_PRTCL_MEAN_XVEL:
  case SRI_VARIABLE_PRTCL_MEAN_YVEL:
  case SRI_VARIABLE_PRTCL_MEAN_ZVEL:
  case SRI_VARIABLE_PRTCL_MEAN_VEL_MAG:
  case SRI_VARIABLE_FILM_XVEL:
  case SRI_VARIABLE_FILM_YVEL:
  case SRI_VARIABLE_FILM_ZVEL:
  case SRI_VARIABLE_FILM_VEL_MAG:
    return cp_particle_sim.one_over_lattice_time_correction();
  case SRI_VARIABLE_FILM_STRESS:
  case SRI_VARIABLE_PRTCL_XFORCE:
  case SRI_VARIABLE_PRTCL_YFORCE:
  case SRI_VARIABLE_PRTCL_ZFORCE:
    return cp_particle_sim.one_over_lattice_time_correction() * cp_particle_sim.one_over_lattice_time_correction(); 
    //The following quantites are sampled every active particle solver
    //timestep and not every LCM of all solver base times.  The LCM
    //factor in the window's base_time is canceled here.
  case SRI_VARIABLE_PRTCL_XIMPULSE:
  case SRI_VARIABLE_PRTCL_YIMPULSE:
  case SRI_VARIABLE_PRTCL_ZIMPULSE:
  case SRI_VARIABLE_PRTCL_MASS_RATE_OUTBOUND:
  case SRI_VARIABLE_PRTCL_MASS_RATE_INBOUND:
  case SRI_VARIABLE_PRTCL_RATE_INBOUND:
  case SRI_VARIABLE_PRTCL_RATE_OUTBOUND:
    return 1.0 / lcm_solver_steps; 
    //However some of these, while also sampled every active particle
    //timestep, are normalized by particle counts stored in
    //RATE_INBOUND and RATE_OUTBOUND meas cells (to obtain averages)
    //so the lcm_solver steps factor would cancel and isn't needed here.
  case SRI_VARIABLE_PRTCL_MEAN_XVEL_OUTBOUND:
  case SRI_VARIABLE_PRTCL_MEAN_YVEL_OUTBOUND:
  case SRI_VARIABLE_PRTCL_MEAN_ZVEL_OUTBOUND:
  case SRI_VARIABLE_PRTCL_MEAN_XVEL_INBOUND:
  case SRI_VARIABLE_PRTCL_MEAN_YVEL_INBOUND:
  case SRI_VARIABLE_PRTCL_MEAN_ZVEL_INBOUND:
    return cp_particle_sim.one_over_lattice_time_correction(); 
  case SRI_VARIABLE_PRTCL_MEAN_DIAM_INBOUND:
  case SRI_VARIABLE_PRTCL_MEAN_DIAM_OUTBOUND:
  case SRI_VARIABLE_PRTCL_MEAN_DENS_INBOUND:
  case SRI_VARIABLE_PRTCL_MEAN_DENS_OUTBOUND:
  default:
    return 1.0;
  }
}
 
const asINT32 CELL_CHUNK_SIZE = 16 * 1024; // number of cells processed in a whack

template <typename MEAS_FLOAT_TYPE>
VOID tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::write_sri_variables(BOOLEAN some_std_dev_var, sriFLOAT base_time_scale)
{
  sriFLOAT *meas_cell_scale_factors = cell_scale_factors();
  asINT32 n_total_vars = n_variables;
  uSP_MEAS_CELL_REF_OR_LIST *sp_mcell_refs = sp_meas_cell_refs();

  //5g, only used in fluid composite window
  ////sriFLOAT meas_window_scale_factor_5g = cell_scale_factor_5g();
  ////sriFLOAT scale_factor_5g_tmp = meas_window_scale_factor_5g * time_scale;
  
  TIMESTEP lcm_solver_steps = (is_cond_window()) ? cdi_meas_window->lcm_cond_tsteps : cdi_meas_window->lcm_flow_tsteps;
  ccDOTIMES(var_index, n_total_vars) {
    asINT32 population_var_index = population_var_indices[var_index];
    SRI_VARIABLE_TYPE var_type = var_types[var_index];
    sriFLOAT timestep_rescale = maybe_timestep_to_lattice_time_inc_scale_factor(var_type, lcm_solver_steps);
    sriFLOAT time_scale = timestep_rescale * base_time_scale;

    ccDO_FROM_BELOW_BY(first_cell, 0, n_stationary_meas_cells, CELL_CHUNK_SIZE) { 
      sriPOINT n_cells = SRI_MIN(CELL_CHUNK_SIZE, n_stationary_meas_cells - first_cell);
      MEAS_FLOAT_TYPE meas_variable[CELL_CHUNK_SIZE];
      ccDOTIMES(j, n_cells) {
        asINT32 meas_cell_index = first_cell + j;
        asINT32 meas_surfel_index = meas_cell_index;
        if(this->sri_file_type == SRI_SURFACE_TYPE)
            meas_surfel_index = cp_info.meas_surfel_index_per_window.at(this->index).at(meas_cell_index);
            
        uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &sp_mcell_refs[meas_cell_index];
        ////sriFLOAT scale_factor = meas_cell_scale_factors[meas_cell_index] * time_scale;
	////sriFLOAT scale_factor_5g = scale_factor_5g_tmp * meas_cell_scale_factors[meas_cell_index];

        if (ref_or_list->is_list()) {
          // Add contributions from all SPs together (a.k.a. the compositing operation).
          // Perform the operation in double precision to ensure accuracy.

          sSP_MEAS_CELL_REF_LIST_ELT *list     = ref_or_list->list();
          sSP_MEAS_CELL_REF          first_ref = list->m_ref;
          MEAS_FLOAT_TYPE  *first_sp_meas_cell = first_ref.sp_meas_cell(all_sp_meas_cells());
          dFLOAT var = first_sp_meas_cell[var_index];
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
          dFLOAT pop;

          if (population_var_index >= 0)
            pop = first_sp_meas_cell[population_var_index];
//#endif
                  
          list = list->m_next; // jump to second ref
          while (list != NULL) {
            sSP_MEAS_CELL_REF ref           = list->m_ref;
            MEAS_FLOAT_TYPE   *sp_meas_cell = ref.sp_meas_cell(all_sp_meas_cells());
            // For defrost time, if contribution from any SP is +inf, the final result
            // should be +inf
            if (var_type == SRI_VARIABLE_DEFROST_TIME)
              var = MAX(var, sp_meas_cell[var_index]); 
            else
              var += sp_meas_cell[var_index];
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
            if (population_var_index >= 0)
              pop += sp_meas_cell[population_var_index];
//#endif
            list = list->m_next;
          }

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
          if (population_var_index >= 0) {
            if (pop > 0)
              meas_variable[j] = timestep_rescale * var / pop;
            else
              meas_variable[j] = 0;
          } else
//#endif
            meas_variable[j] = 
              scale_variable(var, meas_cell_scale_factors[meas_surfel_index], time_scale,
                             var_type, some_std_dev_var);
        }
        else {
          sSP_MEAS_CELL_REF ref           = ref_or_list->ref();
          MEAS_FLOAT_TYPE   *sp_meas_cell = ref.sp_meas_cell(all_sp_meas_cells());
          MEAS_FLOAT_TYPE   var           = sp_meas_cell[var_index];
   
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
          if (population_var_index >= 0) {
            if (sp_meas_cell[ population_var_index ] > 0)
              meas_variable[j] = timestep_rescale * var / sp_meas_cell[ population_var_index ];
            else
              meas_variable[j] = 0;
          } else
//#endif
            meas_variable[j] = 
              scale_variable(var, meas_cell_scale_factors[meas_surfel_index],
                             time_scale, var_type, some_std_dev_var);
        }
      }
         
      // We ignore the status returned here because we set "try_until_success" TRUE
      sri_file->write_variable(var_index, meas_variable, n_cells, first_cell, TRUE);
    }
  }

  // Now write the moving meas cells, if any
  sriFLOAT time_scale = base_time_scale;
  ccDOTIMES(var_index, n_total_vars) {
    ccDO_FROM_BELOW_BY(first_cell, n_stationary_meas_cells, n_meas_cells, CELL_CHUNK_SIZE) {
      sriPOINT n_cells = SRI_MIN(CELL_CHUNK_SIZE, n_meas_cells - first_cell);
      MEAS_FLOAT_TYPE meas_variable[CELL_CHUNK_SIZE];
      ccDOTIMES(j, n_cells) {
        asINT32 meas_cell_index = first_cell + j;
        asINT32 meas_surfel_index = cp_info.meas_surfel_index_per_window.at(this->index).at(meas_cell_index);
        ////sriFLOAT scale_factor = meas_cell_scale_factors[meas_cell_index] * time_scale;
        ////sriFLOAT scale_factor_5g = scale_factor_5g_tmp * meas_cell_scale_factors[meas_cell_index];
        MEAS_FLOAT_TYPE *moving_meas_cell = all_moving_meas_cells() + (meas_cell_index  - n_stationary_meas_cells) + 
                                            var_index * n_moving_meas_cells;
        meas_variable[j] = scale_variable(*moving_meas_cell, meas_cell_scale_factors[meas_surfel_index], time_scale,
                                          var_types[var_index], some_std_dev_var);
      }
          
      // We ignore the status returned here because we set "try_until_success" TRUE
      sri_file->write_variable(var_index, meas_variable, n_cells, first_cell, TRUE);
    }
  }
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::write_sri_variables(BOOLEAN some_std_dev_var, sriFLOAT base_time_scale)
{
  // cell_scale_factors() returns an array whose values are the inverse of the 
  // total surface areas of the faces that constitute the composite measurement 
  // window.
  // We scale the measurement variables on each face by these values, which 
  // means that the numbers written to the composite measurement files are 
  // actually the Force/Torque/Other divided by the corresponding face area.
  //
  // NOTE: Although cell_scale_factors() returns the array named m_face_areas, 
  //       the array contains the inverse of the face areas because we set 
  //       m_face_areas = 1.0/m_face_areas in the function 
  //       precompute_cell_scale_factors (called in write_sri_results).
  sriFLOAT *meas_cell_scale_factors = cell_scale_factors();
  asINT32 n_total_vars = n_variables;

  //5g, only used in fluid composite window
  sriFLOAT meas_window_scale_factor_5g = cell_scale_factor_5g();
  sriFLOAT scale_factor_5g_tmp = meas_window_scale_factor_5g * base_time_scale;

  STP_MEAS_CELL_INDEX *end_meas_cell_indices0 = m_child_global_meas_cell_indices[0] + m_n_child_meas_cells[0];
  STP_MEAS_CELL_INDEX *end_meas_cell_indices1 = m_child_global_meas_cell_indices[1] + m_n_child_meas_cells[1];

  sriFLOAT z_factor_for_porosity = meas_window_scale_factor_5g;
  TIMESTEP lcm_solver_steps = (is_cond_window()) ? cdi_meas_window->lcm_cond_tsteps : cdi_meas_window->lcm_flow_tsteps;

  ccDOTIMES(k, n_total_vars) {
    asINT32 population_var_index = population_var_indices[k];
    SRI_VARIABLE_TYPE var_type = var_types[k];
    sriFLOAT timestep_rescale = maybe_timestep_to_lattice_time_inc_scale_factor(var_type, lcm_solver_steps);
    sriFLOAT time_scale = timestep_rescale * base_time_scale;

    // reduction of meas cells across 2 child SPs
    REDUCTION_MEAS_CELL_VAR *cells0 = m_child_meas_cells[0] + k;
    REDUCTION_MEAS_CELL_VAR *cells1 = m_child_meas_cells[1] + k;

    STP_MEAS_CELL_INDEX *meas_cell_indices0 = m_child_global_meas_cell_indices[0];
    STP_MEAS_CELL_INDEX *meas_cell_indices1 = m_child_global_meas_cell_indices[1];

    m_meas_variable_sums[k] = 0;

    ccDO_FROM_BELOW_BY(first_cell, 0, n_stationary_meas_cells, CELL_CHUNK_SIZE) {
      sriPOINT n_cells = SRI_MIN(CELL_CHUNK_SIZE, n_stationary_meas_cells - first_cell);
      MEAS_FLOAT_TYPE meas_variable[CELL_CHUNK_SIZE];
      MEAS_FLOAT_TYPE var;
      STP_MEAS_CELL_INDEX j = 0;
     // loop advancing the 2 vectors of meas cell indices
      for ( ; j < n_cells; j++) {
        if (meas_cell_indices1 >= end_meas_cell_indices1) {
          // loop advancing the one remaining vector of meas cell indices
          for ( ; j < n_cells; j++) {
            if (meas_cell_indices0 >= end_meas_cell_indices0)
              break;
            STP_MEAS_CELL_INDEX meas_cell_index = first_cell + j;
            asINT32 meas_surfel_index = meas_cell_index;
            if((this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW) && this->is_composite)
              meas_surfel_index = cp_info.meas_surfel_index_per_window.at(this->index).at(meas_cell_index);
            if (*meas_cell_indices0 == meas_cell_index) {
              sriFLOAT spatial_avg_factor = 1.0;
              if (meas_cell_scale_factors) {
                spatial_avg_factor = meas_cell_scale_factors[meas_surfel_index];
              }
              var = *cells0;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
              if (population_var_index >= 0) {
                if (cells0[ population_var_index - k ] > 0) // cells0 points at k'th var
                  meas_variable[j] = timestep_rescale * var / cells0[ population_var_index - k ];
                else
                  meas_variable[j] = 0;
              } else
//#endif
                meas_variable[j] = 
                  scale_variable(var, spatial_avg_factor, time_scale, var_type,
                                 z_factor_for_porosity);
              cells0 += n_total_vars;
              meas_cell_indices0++;
	      if (cp_info.is_5g_sim && meas_cell_scale_factors) {
		/**NOTE: Global volume must be the first var in 5G fluid composite or development file, because its inverse is used as "spatial_avg_factor" to scale other ddp vars. **/
                meas_cell_scale_factors[meas_surfel_index] = spatial_avg_factor;
              }
            } else
              meas_variable[j] = 0; // dev meas windows may have empty meas cells
          }
          break;
        }
        if (meas_cell_indices0 >= end_meas_cell_indices0) {
          // loop advancing the one remaining vector of meas cell indices
          for ( ; j < n_cells; j++) {
            if (meas_cell_indices1 >= end_meas_cell_indices1)
              break;
            STP_MEAS_CELL_INDEX meas_cell_index = first_cell + j;
            asINT32 meas_surfel_index = meas_cell_index;
            if((this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW) && this->is_composite)
              meas_surfel_index = cp_info.meas_surfel_index_per_window.at(this->index).at(meas_cell_index);
            if (*meas_cell_indices1 == meas_cell_index) {
              sriFLOAT spatial_avg_factor = 1.0;
              if (meas_cell_scale_factors) {
                spatial_avg_factor = meas_cell_scale_factors[meas_surfel_index];
              }
              var = *cells1;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
              if (population_var_index >= 0) {
                if (cells1[ population_var_index - k ] > 0) // cells1 points at k'th var
                  meas_variable[j] = timestep_rescale * var / cells1[ population_var_index - k ];
                else
                  meas_variable[j] = 0;
              } else
//#endif
                meas_variable[j] = 
                  scale_variable(var, spatial_avg_factor, time_scale, var_type,
                                 z_factor_for_porosity);
              cells1 += n_total_vars;
              meas_cell_indices1++;
	      if (cp_info.is_5g_sim && meas_cell_scale_factors) {
		/**NOTE: Global volume must be the first var in 5G fluid composite or development file, because its inverse is used as "spatial_avg_factor" to scale other ddp vars. **/
                meas_cell_scale_factors[meas_surfel_index] = spatial_avg_factor;
              }
            } else
              meas_variable[j] = 0; // dev meas windows may have empty meas cells
          }
          break;
        }
        STP_MEAS_CELL_INDEX meas_cell_index = first_cell + j;
        asINT32 meas_surfel_index = meas_cell_index;
        if((this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW) && this->is_composite)
          meas_surfel_index = cp_info.meas_surfel_index_per_window.at(this->index).at(meas_cell_index);
        STP_MEAS_CELL_INDEX min = MIN(*meas_cell_indices0, *meas_cell_indices1);
        if (min != meas_cell_index) {
          meas_variable[j] = 0; // dev meas windows may have empty meas cells
        } else {
          sriFLOAT spatial_avg_factor = 1.0;
          if (meas_cell_scale_factors) {
            spatial_avg_factor = meas_cell_scale_factors[meas_surfel_index];
          }
          MEAS_FLOAT_TYPE pop;
          if (*meas_cell_indices0 == meas_cell_index) {
            if (*meas_cell_indices1 == meas_cell_index) {
              var = *cells0 + *cells1;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
              if (population_var_index >= 0) // cells0 and cells1 point at k'th var
                pop = cells0[ population_var_index - k ] + cells1[ population_var_index - k ];
//#endif
              cells0 += n_total_vars;
              cells1 += n_total_vars;
              meas_cell_indices0++;
              meas_cell_indices1++;
            } else {
              var = *cells0;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
              if (population_var_index >= 0)
                pop = cells0[ population_var_index - k ]; // cells0 points at k'th var
//#endif
              cells0 += n_total_vars;
              meas_cell_indices0++;
            }
          } else {
            var = *cells1;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
            if (population_var_index >= 0)
              pop = cells1[ population_var_index - k ]; // cells1 points at k'th var
//#endif
            cells1 += n_total_vars;
            meas_cell_indices1++;
          }
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
          if (population_var_index >= 0) {
            if (pop > 0)
              meas_variable[j] = timestep_rescale * var / pop;
            else
              meas_variable[j] = 0;
          } else {
//#endif
            meas_variable[j] = 
              scale_variable(var, spatial_avg_factor, time_scale, var_type,
                             z_factor_for_porosity);
	    if (cp_info.is_5g_sim && meas_cell_scale_factors) {
	      /**NOTE: Global volume must be the first var in 5G fluid composite or development file, because its inverse is used as "spatial_avg_factor" to scale other ddp vars. **/
              meas_cell_scale_factors[meas_surfel_index] = spatial_avg_factor;
            }
	  }
	}
      }
      for ( ; j < n_cells; j++)
        meas_variable[j] = 0; // dev meas windows may have empty meas cells

      // calculate the sum of the meas variable over all the faces (measurement cells)
      if(rotational_dynamics_index >= 0 && meas_cell_scale_factors) {
        for (j=0; j < n_cells; j++) {
          // As explained by the comment in the beginning of this function, 
          // meas_variable contains the value of the composite measurement 
          // scaled by meas_cell_scale_factors[first_cell+j]. Hence, we need to 
          // divide by this factor to obtain the true value of the measurement.
          asINT32 meas_cell_index = first_cell + j;
          asINT32 meas_surfel_index = meas_cell_index;
          if((this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW) && this->is_composite)
            meas_surfel_index = cp_info.meas_surfel_index_per_window.at(this->index).at(meas_cell_index);
          m_meas_variable_sums[k] += meas_variable[j]/meas_cell_scale_factors[meas_surfel_index];
        }
      }

      // We ignore the status returned here because we set "try_until_success" TRUE
      sri_file->write_variable(k, meas_variable, n_cells, first_cell, TRUE);

    } // end of loop over all measurement cells

    // Now loop over the moving meas cells, if any
    ccDO_FROM_BELOW_BY(first_cell, n_stationary_meas_cells, n_meas_cells, CELL_CHUNK_SIZE) {
      sriPOINT n_cells = SRI_MIN(CELL_CHUNK_SIZE, n_meas_cells - first_cell);
      MEAS_FLOAT_TYPE meas_variable[CELL_CHUNK_SIZE];
      ccDOTIMES(j, n_cells) {
        asINT32 meas_cell_index = first_cell + j;

        sriFLOAT spatial_avg_factor = 1.0;
        if (meas_cell_scale_factors) {
          spatial_avg_factor = meas_cell_scale_factors[meas_cell_index];
        }
        MEAS_FLOAT_TYPE *moving_meas_cell = all_moving_meas_cells() + (meas_cell_index  - n_stationary_meas_cells) + 
                                            k*n_moving_meas_cells;
        meas_variable[j] = scale_variable(*moving_meas_cell, spatial_avg_factor, time_scale, var_types[k], 
                                          z_factor_for_porosity);
      }
          
      // We ignore the status returned here because we set "try_until_success" TRUE
      sri_file->write_variable(k, meas_variable, n_cells, first_cell, TRUE);
    }

  } // end of loop over all variables

  if (rotational_dynamics_index >= 0) {
    cp_info.rotational_dynamics_descs[rotational_dynamics_index].do_rotational_dynamics();
  }
}

template struct tCP_REDUCTION_MEAS_WINDOW<sriFLOAT>;
template struct tCP_REDUCTION_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_NO_REDUCTION_MEAS_WINDOW<sriFLOAT>;
template struct tCP_NO_REDUCTION_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_FLUID_MEAS_WINDOW<sriFLOAT>;
template struct tCP_FLUID_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_SURFACE_MEAS_WINDOW<sriFLOAT>;
template struct tCP_SURFACE_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_COMPOSITE_FLUID_MEAS_WINDOW<sriFLOAT>;
template struct tCP_COMPOSITE_FLUID_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_COMPOSITE_SURFACE_MEAS_WINDOW<sriFLOAT>;
template struct tCP_COMPOSITE_SURFACE_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_FLUID_DEV_MEAS_WINDOW<sriFLOAT>;
template struct tCP_SURFACE_DEV_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_SURFACE_DEV_MEAS_WINDOW<sriFLOAT>;
template struct tCP_FLUID_DEV_MEAS_WINDOW<sriDOUBLE>;
