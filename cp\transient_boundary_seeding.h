/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Transient boundary seeding class
 *
 * Mohit Jain, Exa Corporation, Dassault Systemes Simulia Corp.
 * Created Thu Jun 20, 2019
 *--------------------------------------------------------------------------*/
#ifndef TRANSIENT_BOUNDARY_SEEDING_H
#define TRANSIENT_BOUNDARY_SEEDING_H
#include "common.h"
#include "cp_seed_from_meas.h"


struct sCP_TBS_INFO_PER_SP {
  auINT32 n_tbs_surfels;
  auINT32 n_total_vars;
  sCP_TBS_INFO_PER_SP() {
    n_tbs_surfels = 0;
    n_total_vars = 0;
  }
};

typedef struct sTRANSIENT_BOUNDARY_SEEDING: public sSEED_FROM_MEAS_DESC {
  auINT32 m_n_frames_per_iter;
  auINT32 m_n_frames_sent;
  auINT32 m_send_buffer_size;
  std::map<auINT32, int> m_n_var_mask_map;
  sFLOAT **m_send_buffer;
  cBOOLEAN m_reset_meas_file_buffer;
  MPI_Request *m_tbs_send_requests;
  std::unordered_map<asINT32, std::vector<sTBS_SURFEL_INFO>> m_transient_boundary_seeding_map;
  std::vector<sCP_TBS_INFO_PER_SP> m_tbs_info;
  sFLOAT m_surfel_translation_coord[3];

  sTRANSIENT_BOUNDARY_SEEDING() {
    m_n_frames_per_iter = NUM_FRAMES;
    m_n_frames_sent = 0;
    m_n_total_frames_sent = 0;
    m_send_buffer_size = 0;
    m_reset_meas_file_buffer = FALSE;
    ccDOTIMES(i, 3)
      m_surfel_translation_coord[i] = 0.0F;
  }

  VOID insert_value_in_n_var_mask_map(auINT32 var_mask);
  inline auINT32 tbs_data_index(auINT32 var_index, STP_PROC home_sp, auINT32 var_mask);
  VOID increment_tbs_info(auINT32 var_mask, STP_PROC home_sp);
  VOID increment_variable_mask_map(auINT32 var);
  VOID allocate_send_requests();
  VOID reset_variable_mask_map();
  VOID reset_n_var_mask_map();
  VOID allocate_send_buffer();
  VOID read_transient_boundary_seeding_data(auINT32 var);
  VOID set_n_frames_per_iter();
  VOID set_buffer_size();
  VOID transient_seed_surfel_comm(asINT32 imeas);
  VOID set_meas_cell_index_from_map(cDGF_SURFEL_DESC &surfel_desc, asINT32 &meas_cell_index);
  VOID fill_transient_boundary_seeding_map(cDGF_SURFEL_DESC &surfel_desc, STP_PROC home_sp, cDGF_TBS &tbs);
  VOID tbs_finalize();
} *TRANSIENT_BOUNDARY_SEEDING;


VOID send_transient_seed_data();
VOID complete_tbs_send_req();
VOID init_transient_boundary_seeding_comm();
VOID finalize_tbs_requests();
#endif
