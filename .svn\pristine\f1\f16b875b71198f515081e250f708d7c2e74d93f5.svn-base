/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */

#include "cdi_common.h"

#include GEOM_COMMON_H


// Instantiations of chunk parsers
template class tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_PSDF, cCDI_GEOMETRY_READER>;
template class tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_RGDF, cCDI_GEOMETRY_READER>;
template class tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_MEAS, cCDI_GEOMETRY_READER>;
template class tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_FLUD, cCDI_GEOMETRY_READER>;
template class tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_SOLD, cCDI_GEOMETRY_READER>;
template class tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_MOVB, cCDI_GEOMETRY_READER>;


// This class automatically parses chunks with geometry information: PSDF, RGDF, and MEAS chunks
// and subordinates.  If you need to handle those chunks yourself, derive from cCDI_READER
// instead, taking note of what cCDI_GEOMETRY_READER needs to do.
cCDI_GEOMETRY_READER::cCDI_GEOMETRY_READER(cCDI_CHUNK_PARSER_MAP&& parsers,
                                           cCDI_PARTITIONS& cdiPartitions,
                                           cGEOM_COMMON_ENTITY_LIST& entities,
                                           CDI_ENTITY_FILTER_FUNCTION regionFilterFunc,
                                           CDI_ENTITY_FILTER_FUNCTION faceFilterFunc)
    : cCDI_READER(std::move(parsers))
    , m_cdiPartitions(cdiPartitions)
    , m_entities(entities)
    , m_regionFilterFunc(regionFilterFunc)
    , m_faceFilterFunc(faceFilterFunc)
{
  m_parsers.insert(tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_PSDF, cCDI_GEOMETRY_READER>::Create(this));
  m_parsers.insert(tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_RGDF, cCDI_GEOMETRY_READER>::Create(this)),
  m_parsers.insert(tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_MEAS, cCDI_GEOMETRY_READER>::Create(this));
  m_parsers.insert(tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_FLUD, cCDI_GEOMETRY_READER>::Create(this));
  m_parsers.insert(tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_SOLD, cCDI_GEOMETRY_READER>::Create(this));
  m_parsers.insert(tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_MOVB, cCDI_GEOMETRY_READER>::Create(this));
  m_parsers.insert({ CDI_CHUNK_TYPE_PCFG, new cCDI_DESCEND_PARSER(this) });
  entities.clear();
}

// Process a CDI file, call PostProcess at the end, and close it.
// Note: This function needs to exist so that derived classes like cSPEC_CDI_READER can call
// it, resulting in the second overload of cCDI_GEOMETRY_READER::Parse being called when this
// one invokes cCDI_READER::Parse.
bool cCDI_GEOMETRY_READER::Parse(const std::string& cdiFile)
{
  return cCDI_READER::Parse(cdiFile);
}

void cCDI_GEOMETRY_READER::Parse(CDI_INFO ci, cCDI_CHUNK_PARSER* parent)
{
  cCDI_READER::Parse(ci, parent);
  // PR 50586 - handle old format with no PSDF or RGDF
  if (!m_foundPSDF) {
    cdi_rewind(ci);
    if (m_cdiPartitions.ReadRegionsAndFacesFromCDI(ci)) {
      m_foundPSDF = true;
    }
  }
}

void cCDI_GEOMETRY_READER::PostProcess()
{
  cCDI_GEOMETRY_GENERATOR(m_cdiPartitions,
                          m_regionsMeasured,
                          m_facesMeasured,
                          m_solidRegions,
                          m_fluidRegions,
                          m_regionFilterFunc,
                          m_faceFilterFunc,
                          m_entities)();
}


template <>
bool tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_PSDF, cCDI_GEOMETRY_READER>::parse(CDI_INFO ci)
{
  cCDI_GEOMETRY_READER* reader = dynamic_cast<cCDI_GEOMETRY_READER*>(m_reader);
  assert(reader);
  if (reader->m_foundPSDF)
    return false;

  reader->m_foundPSDF = true;
  reader->m_cdiPartitions.ReadFromCDI(ci);
  reader->m_cdiPartitions.ReadRegionsAndFacesFromCDI(ci);
  return true;
}


template <>
bool tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_RGDF, cCDI_GEOMETRY_READER>::parse(CDI_INFO ci)
{
  cCDI_GEOMETRY_READER* reader = dynamic_cast<cCDI_GEOMETRY_READER*>(m_reader);
  assert(reader);
  if (reader->m_foundPSDF)
    return true;

  // Old formats may not have had a PSDF chunk, so if we reach RGDF without
  // finding one, we need to call ReadRegionsAndFacesFromCDI.  However, we have
  // to back out of the RGDF chunk first, and then get past it after calling
  // ReadRegionsAndFacesFromCDI, since the latter preserves the file posiiton.
  // (The extra seeking makes this a little slower, but we're only seeking
  // short distances, and this is only for backward compatibility.)
  CIO_INFO cio = ci->cio_info;
  cio_ascend(cio);  // up out of RGDF
  cdi_rewind(ci);   // rewind to PTGE

  if (reader->m_cdiPartitions.ReadRegionsAndFacesFromCDI(ci)) {
    reader->m_foundPSDF = true;
  }

  // ReadRegionsAndFacesFromCDI preserves the file position, so we are
  // still at the PTGE.  Scan forward to get past the RGDF chunk.
  ccCDI_DO_INNER_CHUNKS(i, "project", ci) {
    CIO_CCCC chunkType1 = cdi_get_type(ci);
    if (chunkType1 == ChunkType()) {
      break;
    }
  }
  return reader->m_foundPSDF;
}


template <>
bool tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_MEAS, cCDI_GEOMETRY_READER>::parse(CDI_INFO ci)
{
  cCDI_GEOMETRY_READER* reader = dynamic_cast<cCDI_GEOMETRY_READER*>(m_reader);
  assert(reader);
  if (!reader->m_foundPSDF)
    return false;

  // Iterate over MEAS chunk, looking at all MESR chunks
  ccCDI_DO_INNER_CHUNKS(i, "meas", ci) {
    CIO_CCCC chunkType1 = cdi_get_type(ci);
    if (chunkType1 != CDI_CHUNK_TYPE_MESR)
      continue;

    // If the MESR has a face list, remember the face indexes.
    // If it has a region list, remember the region indexes.
    CDI_MESR mesr = cdi_read_mesr(ci, /*is_2d=*/true, /*is_heat_xfer=*/false);
    auto selectionTree = mesr->geom_selection;
    if (selectionTree) {
      cCDI_GEOMETRY_REF::eGEOMETRY_TYPE measurementType = selectionTree->GeometrySelectionType();
      switch (measurementType) {
      case cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face: {
        // effective selections are face indices
        std::vector<cdiINT32> selections = selectionTree->ExpandSelection(reader->m_cdiPartitions);
        reader->m_facesMeasured.insert(selections.begin(), selections.end());
        break;
      }
      case cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part: {
        // effective selections are region indices
        std::vector<cdiINT32> selections = selectionTree->ExpandSelection(reader->m_cdiPartitions);
        reader->m_regionsMeasured.insert(selections.begin(), selections.end());
        break;
      }
      // GeometrySelectionType cannot currently be anything else
      case cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart:
      case cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment:
      default:
        assert(false);
        break;
      }
    }
    cdi_destroy_mesr(mesr);
  }
  return true;
}


template <>
bool tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_FLUD, cCDI_GEOMETRY_READER>::parse(CDI_INFO ci)
{
  bool readSomething = false;
  cCDI_GEOMETRY_READER* reader = dynamic_cast<cCDI_GEOMETRY_READER*>(m_reader);
  assert(reader);

  // Fetch the region list from child PHRG chunks. These are fluid regions.
  ccCDI_DO_INNER_CHUNKS(i0, "flud", ci) {
    CIO_CCCC chunkType0 = cdi_get_type(ci);
    if (chunkType0 != CDI_CHUNK_TYPE_PHRG)
      continue;

    // Read regions lists from inner RGNS chunks
    ccCDI_DO_INNER_CHUNKS(i1, "phrg", ci) {
      CIO_CCCC chunkType1 = cdi_get_type(ci);
      if (chunkType1 != CDI_CHUNK_TYPE_RGNS)
        continue;

      CDI_RGNS rgns = cdi_read_rgns(ci);
      reader->m_fluidRegions.insert(rgns->region, rgns->region + rgns->n_region);
      cdi_destroy_rgns(rgns);
      readSomething = true;
    }
  }
  return readSomething;
}


template <>
bool tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_SOLD, cCDI_GEOMETRY_READER>::parse(CDI_INFO ci)
{
  bool readSomething = false;
  cCDI_GEOMETRY_READER* reader = dynamic_cast<cCDI_GEOMETRY_READER*>(m_reader);
  assert(reader);

  // Fetch the region list from child PHRG chunks. These are solid regions.
  ccCDI_DO_INNER_CHUNKS(i0, "sold", ci) {
    CIO_CCCC chunkType0 = cdi_get_type(ci);
    if (chunkType0 != CDI_CHUNK_TYPE_PHRG)
      continue;

    // Read regions lists from inner RGNS chunks
    ccCDI_DO_INNER_CHUNKS(i1, "phrg", ci) {
      CIO_CCCC chunkType1 = cdi_get_type(ci);
      if (chunkType1 != CDI_CHUNK_TYPE_RGNS)
        continue;

      CDI_RGNS rgns = cdi_read_rgns(ci);
      reader->m_solidRegions.insert(rgns->region, rgns->region + rgns->n_region);
      cdi_destroy_rgns(rgns);
      readSomething = true;
    }
  }
  return readSomething;
}


template <>
bool tCDI_CHUNK_PARSER<CDI_CHUNK_TYPE_MOVB, cCDI_GEOMETRY_READER>::parse(CDI_INFO ci)
{
  bool readSomething = false;
  cCDI_GEOMETRY_READER* reader = dynamic_cast<cCDI_GEOMETRY_READER*>(m_reader);
  assert(reader);

  // Fetch the region list from child PHRG chunks. These are moving boundary regions,
  // but we can just lump them in with the solid regions.
  ccCDI_DO_INNER_CHUNKS(i0, "movb", ci) {
    CIO_CCCC chunkType0 = cdi_get_type(ci);
    if (chunkType0 != CDI_CHUNK_TYPE_PHRG)
      continue;

    // Read regions lists from inner RGNS chunks
    ccCDI_DO_INNER_CHUNKS(i1, "phrg", ci) {
      CIO_CCCC chunkType1 = cdi_get_type(ci);
      if (chunkType1 != CDI_CHUNK_TYPE_RGNS)
        continue;

      CDI_RGNS rgns = cdi_read_rgns(ci);
      reader->m_solidRegions.insert(rgns->region, rgns->region + rgns->n_region);
      cdi_destroy_rgns(rgns);
      readSomething = true;
    }
  }
  return readSomething;
}
