# ~~~COPYWRITE~~~+ boxcomment("cp.copyright", "78")
##############################################################################
### PowerFLOW Simulator Control Process                                    ###
###                                                                        ###
### Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.         ###
### All Rights Reserved.                                                   ###
### US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                     ###
###        5,640,335; 5,848,260; 5,910,902; 5,953,239;                     ###
###        6,089,744; 7,558,714                                            ###
### UK FR DE Pat 0 538 415                                                 ###
###                                                                        ###
### This computer program is the property of Dassault Systemes Americas Corp. ###
### and contains its confidential trade secrets.  Use, examination, copying, ###
### transfer and disclosure to others, in whole or in part, are prohibited ###
### except with the express prior written consent of Dassault Systemes     ###
### Americas Corp.                                                         ###
##############################################################################
# ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78")

#--------------------------------------------------
# Included files and libraries
#--------------------------------------------------

INLINE_DEPS=1
include $R/registry.mak

VMAKE_WARNLEVEL=low

ifeq ($(T),amd64_linux_na)
# Use gcc9 in compatibility mode to compile gcc 4.9 builds (required to be able to link against DSLS which requires at least gcc 8+)
include $(MAKEINCLUDE_DIR)/amd64_gcc9/makeinclude.mak
else
include $(MAKEINCLUDE_DIR)/$(T)/makeinclude.mak
endif

include $(MAKEINCLUDE_DIR)/simulator_flags.mak

#The following disables this warning: "warning The entire set of C++ language bindings have been deprecated in mpi 2.2"
#CC_OPT += -Wno-deprecated

CC_OPT += -Wno-deprecated
#Prevent any optimizations when compiling in debug mode
CC_DBG += -Wno-deprecated

USER_CCFLAGS += -Wno-parentheses

# Address sanitizer
# USER_CCFLAGS+=-fsanitize=thread -fno-omit-frame-pointer -g
# USER_CCFLAGS+=-fsanitize=address -fno-omit-frame-pointer -fsanitize=undefined -g
# USER_CCFLAGS += -Og #-fsanitize=address -fno-omit-frame-pointer
# USER_CCFLAGS+=-g -O0 -fsanitize=address -fno-omit-frame-pointer

#Deactivate asserts in Release mode
ifndef DEBUG
	CCFLAGS += -DNDEBUG
endif

#Introduce commit hash to identify builds
#COMMIT_ID = $(shell cd $R/components && git log -1 --pretty=format:"%h")
#USER_CCFLAGS += -DCOMMIT_ID=\"${COMMIT_ID}\"

include $(PLATFORM_DIR)/export.mak
include $(SCALAR_DIR)/export.mak
include $(MSGERR_DIR)/export.mak
include $(MALLOC_DIR)/export.mak
include $(LOOP_DIR)/export.mak
include $(DEBUG_DIR)/export.mak
include $(SORT_DIR)/export.mak
include $(AUDIT_DIR)/export.mak
include $(JOBCTL_DIR)/export.mak
include $(TRIO_DIR)/export.mak
include $(ESTRING_DIR)/export.mak
include $(EXALIC_DIR)/export.mak
include $(REPRISE_DIR)/export.mak
include $(DSLS_DIR)/export.mak
include $(EXATIME_DIR)/export.mak
include $(PHYSTYPES_DIR)/export.mak
include $(CIO_DIR)/export.mak
include $(XRAND_DIR)/export.mak
include $(CIPHER_DIR)/export.mak
include $(CDI_DIR)/export.mak
include $(LGI_DIR)/export.mak
include $(VHASH_DIR)/export.mak
include $(UNITS_DIR)/export.mak
include $(EXPRLANG_DIR)/export.mak
include $(G3_DIR)/export.mak
include $(G2_DIR)/export.mak
include $(G1_DIR)/export.mak
include $(XARRAY_DIR)/export.mak
include $(EARRAY_DIR)/export.mak
include $(FOREST_DIR)/export.mak
include $(SMEM_DIR)/export.mak
include $(VMEM_DIR)/export.mak
include $(VMEM_VECTOR_DIR)/export.mak
include $(TEARRAY_DIR)/export.mak
include $(THARRAY_DIR)/export.mak
include $(TXARRAY_DIR)/export.mak
include $(GRID_DIR)/export.mak
include $(AME_MODEL_DIR)/export.mak
include $(XNEW_DIR)/export.mak
include $(CCUTILS_DIR)/export.mak
include $(TRIO_DIR)/export.mak
include $(HDF5_DIR)/export.mak
include $(ZLIB_DIR)/export.mak
include $(SRI_DIR)/export.mak
include $(NETCDF_DIR)/export.mak
include $(JPEG_DIR)/export.mak
include $(PRI_DIR)/export.mak
include $(FILE_ADAPTER_DIR)/export.mak
include $(OPENSSL98_DIR)/export.mak
include $(MSA_DIR)/export.mak
include $(SIMUTILS_DIR)/export.mak
include $(FMT_DIR)/export.mak
include $(TIRE_DIR)/export.mak
include $(DIERCKX_DIR)/export.mak
include $(SIMSIZES_DIR)/export.mak
include $(PARALLEL_IO_DIR)/export.mak

# Surface coupling not supported for Alpha
ifneq (true,$(DISABLE_SURF_COUP))
    include $(THASH_DIR)/export.mak
    include $(TDFIO_DIR)/export.mak
    include $(TPI_DIR)/export.mak
    include $(MIO_DIR)/export.mak
    include $(MIO_BREP_INSTANCES_DIR)/export.mak
    include $(BREP_DIR)/export.mak
    include $(BAGS_DIR)/export.mak
    include $(PQ_DIR)/export.mak
    include $(NERO_DIR)/export.mak
    include $(NERO_CGAL_DIR)/export.mak
    include $(INTERP_DIR)/export.mak
    include $(BG_DIR)/export.mak
    include $(POOLS_DIR)/export.mak
    include $(BITMAP_DIR)/export.mak
    include $(FGEOM_DIR)/export.mak
    include $(DLIST_DIR)/export.mak
endif

include $(CP_SP_LIB_DIR)/export.mak
include $(PF_COMM_DIR)/export.mak
include $(PF_LOG_DIR)/export.mak

include $(CP_DIR)/export.mak
include $(RADIO_DIR)/export.mak
# include $(METIS_DIR)/export.mak

ifdef SW_CONFIG
CONFIG_DFLAGS	=-DSW_CONFIG
else
CONFIG_DFLAGS	=-DASIC_CONFIG
endif


ifneq (true,$(DISABLE_SURF_COUP))
COUP_D_FLAGS = -DSURF_COUP $(TPI_D) $(TPI_INSTANCES_D) $(MIO_D) $(MIO_BREP_INSTANCES_D) $(BREP_D) $(BAGS_D) $(PQ_D) $(NERO_D) $(NERO_CGAL_D) $(INTERP_D) \
	       $(BG_D) $(TDFIO_D) $(FGEOM_D) $(POOLS_D) $(BITMAP_D) $(DLIST_D) \
	       $(VHASH_D) $(THASH_D)
else
COUP_D_FLAGS =
endif

D_FLAGS = $(RADIO_DECOMP_D) $(RADIO_D) $(FMT_D) $(PLATFORM_D) $(SCALAR_D) $(MSGERR_D) $(LOOP_D) $(LOOP_RANGE_D) $(AUDIT_D) $(JOBCTL_SERVER_D) $(ESTRING_D) \
	$(MALLOC_D) $(DEBUG_D) $(CIO_D) $(XRAND_D) $(CIPHER_D) $(CDI_D) $(CDI_FIX_PARALLEL_DEV_D) $(LGI_D) $(GUILIC_D) $(EXALIC_D) $(EXATIME_D) \
	$(TEARRAY_D) $(THARRAY_D) $(TXARRAY_D) $(XARRAY_D) $(EARRAY_D) $(UNITS_D) $(G3_D) $(G2_D) \
	$(G1_D) $(COUP_D_FLAGS) $(VMEM_D) $(VMEM_VECTOR_D) $(SMEM_D) $(GRID_D) \
	$(SORT_D) $(FOREST_D) $(EXPRLANG_D) \
	$(CP_D) $(DISC_INTERCOMM_D) $(SP_D) $(NETCDF_D) $(SRI_D) $(PHYSTYPES_D) $(AME_MODEL_D) \
	$(XNEW_D) $(CCUTILS_D) $(TRIO_D) $(FILE_ADAPTER_D) $(MSA_D)\
	$(STL_D) $(PF_LOG_D) -DLICENSE_ENABLED \
	$(CONFIG_DFLAGS) -DCP_VERSION=\"$(COMPONENT_VERSION)\" \
	-DHOST_SP_BIN_DIR=\"$T\" \
	-DBUILD_IB=1 \
	-DSIMENG_DIR=\"$(SIMENG_DIR)\" \
	-DPHYSICS_VERSION=\"$(PHYSICS_VERSION)\" \
	-DSIMENG_VERSION=\"$(SIMENG_VERSION)\" \
	-DSOLVER_VERSION=$(SOLVER_VERSION) \
	-DPRODUCT_VERSION=\"$(PRODUCT_VERSION)\" $(TARGET_D_FLAGS) \
        $(PARTICLE_CP_SP_LIB_D) $(PRI_D) $(HDF5_D) $(SIMUTILS_D) $(TIRE_D) $(DIERCKX_D) \
	$(SIMSIZES_SHARED_D)

I_FLAGS =	$(HDF5_I) $(PARALLEL_IO_I)
ifdef SGI_MPI
L_FLAGS_GLIBC = -L/opt/glibc-2.28/lib
else
L_FLAGS_GLIBC =
endif
ifneq (,$(findstring x86_linux2_na_rh8,$T))
USER_CCFLAGS+=-Wno-deprecated
endif

#--------------------------------------------------
# Library definitions
#--------------------------------------------------

ifneq (true,$(DISABLE_SURF_COUP))
COUP_LIBS = $(TPI_A) $(INTERP_A) $(MIO_BREP_INSTANCES_A) $(MIO_A) $(BAGS_A) \
	    $(NERO_CGAL_A) $(BREP_A) $(POOLS_A) $(BITMAP_A) $(BG_A) \
	    $(FGEOM_A) $(DLIST_A) \
	    $(THASH_A) 
else
COUP_LIBS =
endif

GENERIC_LIBS = $(SRI_A) $(CDI_A) $(CIPHER_A) $(XRAND_A) $(CIO_A) \
	       $(LGI_A) $(EXPRLANG_A) $(UNITS_A) $(FOREST_A) $(SRI_A) $(EXATIME_A) \
	       $(COUP_LIBS) $(SIMUTILS_A) $(VHASH_A) $(THARRAY_A) $(VMEM_A) \
	       $(VMEM_VECTOR_A) $(XARRAY_A) $(EARRAY_A) $(SORT_A) $(DEBUG_A) \
	       $(ESTRING_A) $(MALLOC_A) $(SCALAR_A) \
	       $(CCUTILS_A) $(PLATFORM_A) $(MSGERR_A) $(G3_A) $(G2_A) $(G1_A) \
	       $(XNEW_A) $(TRIO_A) $(EXALIC_A) $(MSA_A) $(PRI_A) $(FMT_A) $(SMEM_A) $(TIRE_A) $(DIERCKX_A)

# Libraries needed by the CP 
CP_LIBS = $(RADIO_DECOMP_A) $(RADIO_A) $(METIS_A) $(AUDIT_A) $(JOBCTL_SERVER_A) $(AME_MODEL_A) $(GENERIC_LIBS) $(PARALLEL_IO_A)

# Libraries needed by the exa_sim_comm target
COMM_OBJS = license.o license_aux.o 
COMM_LIBS = $(COMM_OBJS) $(EXALIC_A) $(JOBCTL_SERVER_A) $(CCUTILS_A) $(TRIO_A) $(PLATFORM_A) $(MSGERR_A) $(MALLOC_A)
COMM_LINK_OPTS = $(EXALIC_LINKOPTS) 
#RLMCOMM_LIBS = $(COMM_OBJS) $(RLMEXALIC_A) $(JOBCTL_SERVER_A) $(CCUTILS_A) $(TRIO_A) $(PLATFORM_A) $(MSGERR_A) $(MALLOC_A)
#COMM_LINK_OPTS = -lm -pthread

#in this Makefile CP_A below refers to libcp.a in the CP_SP_LIB component
ifneq (true,$(DISABLE_SURF_COUP))

ifdef USE_CLANG
CP_LINKOPTS = $(CP_A) $(CP_LIBS) $(TDFIO_SO) $(NETCDF_SO) $(HDF5_SO) $(ZLIB_SO) $(JPEG_SO) $(TARGET_LIBS) -lm -lpthread -lrt -ldl -lirc -lsvml 
else
CP_LINKOPTS = $(CP_A) $(CP_LIBS) $(TDFIO_SO) $(NETCDF_SO) $(HDF5_SO) $(ZLIB_SO) $(JPEG_SO) $(TARGET_LIBS) -lm -lpthread -lrt -ldl
endif

else
ifdef USE_CLANG
CP_LINKOPTS = $(CP_A) $(CP_LIBS) $(NETCDF_SO) $(HDF5_SO) $(ZLIB_SO) $(JPEG_SO) $(TARGET_LIBS) -lm -lpthread -lrt -lirc -lsvml 
else
CP_LINKOPTS = $(CP_A) $(CP_LIBS) $(NETCDF_SO) $(HDF5_SO) $(ZLIB_SO) $(JPEG_SO) $(TARGET_LIBS) -lm -lpthread -lrt
endif

endif


#--------------------------------------------------
# Configuration specific definitions
#--------------------------------------------------

#--------------------------------------------------
# File definitions
#--------------------------------------------------

COPYRIGHT_FILE = cp.copyright
CREATION_YEAR = 1994

ifneq (true,$(DISABLE_SURF_COUP))
COUP_INC_FILES = surface_coupling.h coupling_model.h 
COUP_CC_FILES = surface_coupling.cc coupling_model.cc 
else
COUP_INC_FILES =
COUP_CC_FILES =
endif

INC_FILES = common.h parse_args.h window.h cp_lattice.h cp_info.h jobctl.h jobctl_status.h \
	    exa_sim.h license_guts.h license_aux.h errbuf.h \
	    cp_stream_manager.h cp_cdi_reader.h ublk_octree.h cp_dgf_reader.h \
	    timestep_subcycling.h seed.h full_ckpt.h mme_ckpt.h sim_tree.h \
	    checkpoint_control.h monitor.h table.h heat_exhangers.h window_results.h rotational_dynamics.h $(COUP_INC_FILES) \
	    particle_sim_cp.h trajectory_results.h trajectory_window.h write_pm_lgi_records.h cp_seed_from_meas.h \
	    dsm_reader.h transient_boundary_seeding.h cp_nero.h exa_sim_parse_cp.h text_table.h


SIMENG_LIB_FILES = common.cc parse_args.cc window.cc cp_lattice.cc cp_info.cc jobctl.cc jobctl_status.cc \
		   cp_stream_manager.cc cp_cdi_reader.cc cp_dgf_reader.cc \
		   timestep_subcycling.cc seed.cc full_ckpt.cc mme_ckpt.cc license_aux.cc sim_tree.cc \
		   checkpoint_control.cc exa_sim_shared.cc errbuf.cc table.cc \
		   heat_exchangers.cc window_results.cc monitor.cc \
		   rotational_dynamics.cc $(COUP_CC_FILES) \
		   particle_sim_cp.cc trajectory_results.cc trajectory_window.cc \
		   write_pm_lgi_records.cc cp_seed_from_meas.cc \
		   dsm_reader.cc transient_boundary_seeding.cc cp_nero.cc exa_sim_parse_cp.cc porous_rock.cc text_table.cc

# Programs to be compiled for the CP 
CP_PROGRAMS = exa_sim.cc license.cc

SRC_FILES = master.mak export.mak $(INC_FILES) \
	    $(SIMENG_LIB_FILES) $(CP_PROGRAMS)

#--------------------------------------------------
# Derived variable definitions
#--------------------------------------------------

SIMENG_LIB_OBJ_FILES = $(patsubst %.cc,%.o,$(SIMENG_LIB_FILES))

OTHER_OBJ_FILES = exa_sim.o license.o

#--------------------------------------------------
#
#---------------------------------------------------
ifeq ($T,amd64_gcc9_hpmpi)
CP_SIMSIZES_LIB = cp_simsizes_lib.a
else
CP_SIMSIZES_LIB = 
endif

cp_simsizes_lib.a : cp_sizes.o

I_FLAGS_NOMPI = $(HDF5_NOMPI_I)
cp_sizes.o : ../cp_sizes.cc
	   $(CCC_WITHOUT_MPI) $(EXA_BUILD_COMPILER_FLAGS) $(EXA_BUILD_TARGET_SYMBOLS) $(D_FLAGS) $(I_FLAGS_NOMPI) -c ../cp_sizes.cc -o cp_sizes.o

#--------------------------------------------------
# Compilation Info
#--------------------------------------------------

all: $(CP_BIN_FILES) $(CP_SIMSIZES_LIB)

clean:	save_only_makefile

# Library compile
$(SIMENG_CP_A): $(SIMENG_LIB_OBJ_FILES)

# Program library dependencies
# $(CP_BIN_FILES) : $(SIMENG_CP_A)

# Program compiles

exa_sim_comm: $(COMM_LIBS)
	$(LINK.cc) -o exa_sim_comm $(COMM_LIBS)  $(COMM_LINK_OPTS)

ifneq (,$(findstring amd64_linux_crayxd1, $T))

exa_sim: exa_sim.o $(CP_A) $(CP_LIBS) $(SIMENG_CP_A)
	$(CCC) -L$(MPICH_DIR)/amd64_linux2_64_crayxd1/lib -L$(MPICH_DIR)/amd64_linux2_64_crayxd1/lib/shared -L$(MPICH_DIR)/amd64_linux2_64_crayxd1/craylib $(ccLDFLAGS) -o exa_sim exa_sim.o $(SIMENG_CP_A) $(CP_LINKOPTS)


else

ifneq (,$(findstring amd64_linux, $T))

exa_sim: exa_sim.o $(CP_A) $(CP_LIBS) $(SIMENG_CP_A)
	$(CCC) $(USER_CCFLAGS) $(ccLDFLAGS) -o exa_sim exa_sim.o $ $(SIMENG_CP_A) $(CP_LINKOPTS)

else

exa_sim: exa_sim.o $(CP_A) $(CP_LIBS) $(SIMENG_CP_A)
	$(LINK.cc) $(USER_CCFLAGS) $(L_FLAGS_GLIBC) $(ccLDFLAGS) -o exa_sim exa_sim.o $(SIMENG_CP_A) $(CP_LINKOPTS)


endif
endif

#------------------------------------------------------------------------------
# Special dependencies
#------------------------------------------------------------------------------

# This must be recompiled when the registry changes to get the correct
# default binary directory.
exa_sim.o: $(R)/registry.mak

# This must be recompiled when the registry changes to get the correct 
# version numbers.
exa_sim_shared.o: $(R)/registry.mak

# This must be recompiled when the registry changes to get the correct 
# version numbers.
#read_cp_lgi.o: $(R)/registry.mak

#------------------------------------------------------------------------------
# Testing
#------------------------------------------------------------------------------

