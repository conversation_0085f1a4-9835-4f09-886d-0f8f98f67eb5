<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="cdt.managedbuild.toolchain.gnu.base.**********.137121862">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.toolchain.gnu.base.**********.137121862" moduleId="org.eclipse.cdt.core.settings" name="amd64_linux_na">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildProperties="" description="" id="cdt.managedbuild.toolchain.gnu.base.**********.137121862" name="amd64_linux_na" parent="org.eclipse.cdt.build.core.emptycfg">
					<folderInfo id="cdt.managedbuild.toolchain.gnu.base.**********.137121862." name="/" resourcePath="">
						<toolChain id="cdt.managedbuild.toolchain.gnu.base.2018679343" name="Linux GCC" superClass="cdt.managedbuild.toolchain.gnu.base">
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.target.gnu.platform.base.1341660174" name="Debug Platform" osList="linux,hpux,aix,qnx" superClass="cdt.managedbuild.target.gnu.platform.base"/>
							<builder arguments="VERBOSE=1" buildPath="${workspace_loc:/cdi}/amd64_linux_na" command="make" id="cdt.managedbuild.target.gnu.builder.base.1078585749" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="cdt.managedbuild.target.gnu.builder.base"/>
							<tool id="cdt.managedbuild.tool.gnu.archiver.base.462705807" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.compiler.base.735329311" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.base">
								<option id="gnu.cpp.compiler.option.include.paths.865021735" name="Include paths (-I)" superClass="gnu.cpp.compiler.option.include.paths"/>
								<option id="gnu.cpp.compiler.option.preprocessor.def.610594797" name="Defined symbols (-D)" superClass="gnu.cpp.compiler.option.preprocessor.def" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="AUDIT_H=&quot;/home/<USER>/Workspace/audit/audit.h&quot;"/>
									<listOptionValue builtIn="false" value="CCUTILS_H=&quot;/home/<USER>/Workspace/ccutils/ccutils.h&quot;"/>
									<listOptionValue builtIn="false" value="CDI_H=&quot;/home/<USER>/Workspace/cdi/cdi_export.h&quot;"/>
									<listOptionValue builtIn="false" value="CIO_H=&quot;/home/<USER>/Workspace/cio/cio.h&quot;"/>
									<listOptionValue builtIn="false" value="DEBUG_H=&quot;/home/<USER>/Workspace/debug/debug.h&quot;"/>
									<listOptionValue builtIn="false" value="EARRAY_H=&quot;/home/<USER>/Workspace/earray/earray.h&quot;"/>
									<listOptionValue builtIn="false" value="ESTRING_H=&quot;/home/<USER>/Workspace/estring/estring.h&quot;"/>
									<listOptionValue builtIn="false" value="LOOP_H=&quot;/home/<USER>/Workspace/loop/loop.h&quot;"/>
									<listOptionValue builtIn="false" value="MALLOC_H=&quot;/home/<USER>/Workspace/malloc/malloc.h&quot;"/>
									<listOptionValue builtIn="false" value="MSGERR_H=&quot;/home/<USER>/Workspace/msgerr/msgerr.h&quot;"/>
									<listOptionValue builtIn="false" value="NETCDF_H=&quot;/home/<USER>/Workspace/netcdf/amd64_linux_na/netcdf.h&quot;"/>
									<listOptionValue builtIn="false" value="PLATFORM_H=&quot;/home/<USER>/Workspace/platform/platform.h&quot;"/>
									<listOptionValue builtIn="false" value="SCALAR_H=&quot;/home/<USER>/Workspace/scalar/scalar.h&quot;"/>
									<listOptionValue builtIn="false" value="SRI_H=&quot;/home/<USER>/Workspace/sri/export.h&quot;"/>
									<listOptionValue builtIn="false" value="UNITS_H=&quot;/home/<USER>/Workspace/units/units.h&quot;"/>
									<listOptionValue builtIn="false" value="VHASH_H=&quot;/home/<USER>/Workspace/vhash/vhash.h&quot;"/>
									<listOptionValue builtIn="false" value="__AMD64_LINUX2_64_NA__=1"/>
									<listOptionValue builtIn="false" value="__AMD64_LINUX_NA__=1"/>
									<listOptionValue builtIn="false" value="__LINUX__=1"/>
									<listOptionValue builtIn="false" value="ARG_HELPER_H=&quot;/home/<USER>/Workspace/arg_helper/cARG_HELPER.h&quot;"/>
									<listOptionValue builtIn="false" value="TRIO_H=&quot;/home/<USER>/Workspace/trio/exa_trio.h&quot;"/>
									<listOptionValue builtIn="false" value="PHYSTYPES_H=&quot;/home/<USER>/Workspace/phystypes/export.h&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.805396112" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.compiler.base.2037511823" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base">
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.74046381" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.linker.base.787234696" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.base.1623556527" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.base">
								<inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.198836978" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.assembler.base.2068368779" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.base">
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.**********" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="cdi.null.664387598" name="cdi"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Default">
			<resource resourceType="PROJECT" workspacePath="/cdi"/>
		</configuration>
		<configuration configurationName="amd64_linux_na">
			<resource resourceType="PROJECT" workspacePath="/cdi"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********.137121862;cdt.managedbuild.toolchain.gnu.base.**********.137121862.;cdt.managedbuild.tool.gnu.cpp.compiler.base.735329311;cdt.managedbuild.tool.gnu.cpp.compiler.input.805396112">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********.137121862;cdt.managedbuild.toolchain.gnu.base.**********.137121862.;cdt.managedbuild.tool.gnu.c.compiler.base.2037511823;cdt.managedbuild.tool.gnu.c.compiler.input.74046381">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
</cproject>
