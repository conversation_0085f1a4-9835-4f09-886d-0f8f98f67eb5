/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("cpc.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("cpc.copyright", "78") */ 

#ifndef CDI_LSR_QUADRATIC_SOLVER
#define CDI_LSR_QUADRATIC_SOLVER

#include SCALAR_H

#include <list>
#include <vector>

class cLSR_QUADRATIC_SOLVER
{

public:

  cLSR_QUADRATIC_SOLVER();
  ~cLSR_QUADRATIC_SOLVER() {}
  VOID AddPoint(dFLOAT x, dFLOAT y);
  VOID Solve(bool pureQuadraticFit = false);
  dFLOAT GetA() const { return m_aTerm; }
  dFLOAT GetB() const { return m_bTerm; }
  dFLOAT CalculateRSquare() const;
  dFLOAT CalculateStandardDeviation() const;
  dFLOAT CalculateAverageDeviation() const;
  dFLOAT CalculateY(dFLOAT x) const;
  
private:

  cLSR_QUADRATIC_SOLVER(const cLSR_QUADRATIC_SOLVER&);
  cLSR_QUADRATIC_SOLVER& operator=(const cLSR_QUADRATIC_SOLVER&);
  dFLOAT CalculateSum(int xPower, int yPower) const;

  static const size_t X = 0;
  static const size_t Y = 1;

  typedef std::vector<dFLOAT> cPOINT;
  typedef std::list<cPOINT> cPOINT_CONTAINER;
  cPOINT_CONTAINER m_points;

  dFLOAT m_aTerm;
  dFLOAT m_bTerm;

};

#endif
