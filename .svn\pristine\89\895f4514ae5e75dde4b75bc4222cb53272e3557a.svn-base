/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#include <string>
#include PLATFORM_H
#include "cdi_common.h"
#include "cdi_internal.h"
#include PHYSTYPES_H
#include CIPHER_H
#include <iostream>
#include <limits>
#include <memory>
#include<stack>
#include<iterator>

#include "pri_support.h"
#include "cdi_io.h"
#include "cdi_encrypted_io.h"
#include "cdi_fix_parallel_dev.h"

/****************************************************************\
|
| Function names:cdi_push, cdi_pop
|
| Purpose: handles cio_push and cio_pop more conveniently
|
\****************************************************************/

VOID cdi_push(
               CDI_INFO cdi_info,        /* the info structure */
               CIO_CCCC        cccc                /* Chunk type */
               )
{
  cio_push(cdi_info->cio_info, cccc);
}

VOID cdi_pop(
             CDI_INFO cdi_info /* the info structure */
             )
{
  cio_pop(cdi_info->cio_info);
}


/****************************************************************\
|
| Function name:cdi_write_ptge
|
| Purpose:writes file parentage
|
\****************************************************************/
VOID
cdi_write_ptge(
               CDI_INFO cdi_info, /* the info structure */
               CDI_PTGE ptge /* the ptge to write */
               )
{
  ptge->cccc = CDI_CHUNK_TYPE_PTGE;

  ASSERT_VALID_CDI_INFO(cdi_info);

  cdi_push(cdi_info, CDI_CHUNK_TYPE_PTGE);
  cdi_write_sINT64(cdi_info, (sINT64 *) &ptge->case_file_id, 1);
  cdi_write_sINT64(cdi_info, (sINT64 *) &ptge->case_geometry_id, 1);
  cdi_write_sINT64(cdi_info, (sINT64 *) &ptge->cdi_file_id, 1);
  cdi_write_cdichars(cdi_info, ptge->powercase_version, ptge->powercase_version_n_chars);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 0)) {
    cdi_write_sINT64(cdi_info, (sINT64 *) &ptge->encryption_version_id, 1);
    // Don't create cipher more than once
    cdi_create_cipher(cdi_info, ptge->cdi_file_id, ptge->encryption_version_id);
  }

  // Turn off the encryption during write, the chunks that need it will toggle it on
    cdi_set_encryption_off(cdi_info);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_ptge
|
| Purpose:reads and returns file parentage
|  This chunk will only be read once as it creates the cipher
|  call cdi_read_ptge_on_read to make sure everything is in sync
|  or look at the implementation if you wish to call
|  cdi_read_ptge direcly
|
\****************************************************************/
CDI_PTGE
cdi_read_ptge(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_PTGE ptge = EXA_CALLOC_STRUCT(CDI_PTGE);

  ASSERT_VALID_CDI_INFO(cdi_info);

  ptge->cccc = CDI_CHUNK_TYPE_PTGE;

  cdi_read_sINT64(cdi_info, (sINT64 *) &ptge->case_file_id, 1);
  cdi_read_sINT64(cdi_info, (sINT64 *) &ptge->case_geometry_id, 1);
  cdi_read_sINT64(cdi_info, (sINT64 *) &ptge->cdi_file_id, 1);
  ptge->powercase_version = cdi_read_cdichars(cdi_info, NULL, &ptge->powercase_version_n_chars);

  sINT64 encryption_version = 0;
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 0)) {
    cdi_read_sINT64(cdi_info, &encryption_version, 1);
  }
  ptge->encryption_version_id = encryption_version;

  // PTGE should only be read once, skip cipher creation if done already
  if (cdi_info->encryption_on == -1 ) {
    if (encryption_version != 0) {
      cdi_create_cipher(cdi_info,  ptge->cdi_file_id, encryption_version);
    } else {
      cdi_info->cipher = NULL;
    }
  } else {
    // The cipher was already created if needed
    assert(encryption_version == 0 || cdi_info->cipher != NULL);
    ;
  }
  
  // Turn off the encryption during read, the chunks that need it will toggle it on
  cdi_set_encryption_off(cdi_info);

  return(ptge);
}
/****************************************************************\
|
| Function name:cdi_destroy_ptge
|
| Purpose:destroys file parentage
|
\****************************************************************/
VOID
cdi_destroy_ptge(
                 CDI_PTGE ptge        /* the ptge to destroy */
                 )
{
  exa_free(ptge);
}

/****************************************************************\
|
| Function name:cdi_write_bbox
|
| Purpose:writes bounding box
|
\****************************************************************/
VOID
cdi_write_bbox(
               CDI_INFO cdi_info, /* the info structure */
               CDI_BBOX bbox /* the bounding box to write */
               )
{
  bbox->cccc = CDI_CHUNK_TYPE_BBOX;

  ASSERT_VALID_CDI_INFO(cdi_info);

  cdi_push(cdi_info, CDI_CHUNK_TYPE_BBOX);
  cdi_write_idFLOAT(cdi_info, (idFLOAT *) &bbox->coord, 6);
  cdi_pop(cdi_info);
}





/****************************************************************\
|
| Function name:cdi_read_bbox
|
| Purpose:reads and returns a bounding box
|
\****************************************************************/
CDI_BBOX
cdi_read_bbox(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_BBOX bbox = EXA_CALLOC_STRUCT(CDI_BBOX);

  ASSERT_VALID_CDI_INFO(cdi_info);

  bbox->cccc = CDI_CHUNK_TYPE_BBOX;
  cdi_read_idFLOAT(cdi_info, (idFLOAT *) &bbox->coord, 6);

  return(bbox);
}

/****************************************************************\
|
| Function name:cdi_destroy_bbox
|
| Purpose:destroys a bounding box
|
\****************************************************************/
VOID
cdi_destroy_bbox(
                 CDI_BBOX bbox        /* the bbox to destroy */
                 )
{
  exa_free(bbox);
}

/****************************************************************\
|
| Function name:cdi_write_cmnt
|
| Purpose:writes comment
|
\****************************************************************/
VOID
cdi_write_cmnt(
               CDI_INFO cdi_info, /* the info structure */
               CDI_CMNT cmnt /* the cmnt structure */
               )
{
  ASSERT_VALID_CDI_INFO(cdi_info);

  cmnt->cccc = CDI_CHUNK_TYPE_CMNT;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_CMNT);

  cdi_write_cdichars(cdi_info, cmnt->title, cmnt->n_title_char);
  cdi_write_cdichars(cdi_info, cmnt->value, cmnt->n_value_char);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_cmnt
|
| Purpose:reads comment
|
\****************************************************************/
CDI_CMNT
cdi_read_cmnt(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_CMNT cmnt = EXA_CALLOC_STRUCT(CDI_CMNT);

  ASSERT_VALID_CDI_INFO(cdi_info);

  cmnt->cccc = CDI_CHUNK_TYPE_CMNT;

  cmnt->title = cdi_read_cdichars(cdi_info, NULL, &cmnt->n_title_char);
  cmnt->value = cdi_read_cdichars(cdi_info, NULL, &cmnt->n_value_char);

  return(cmnt);
}

/****************************************************************\
|
| Function name:cdi_destroy_cmnt
|
| Purpose:destroys a comment
|
\****************************************************************/
VOID
cdi_destroy_cmnt(
                 CDI_CMNT cmnt        /* the cmnt to destroy */
                 )
{
  exa_free(cmnt->title);
  exa_free(cmnt->value);
  exa_free(cmnt);
}

void sCDI_PWBY::WriteToCDI(sCDI_INFO* pCDI_Info) const
{
  cdi_push(pCDI_Info, GetChunkType());
  cdi_write_bool(pCDI_Info, m_isAPowerByFile);
  cdi_write_strg(pCDI_Info, m_itemName);
  cdi_write_strg(pCDI_Info, m_itemId);
  cdi_pop(pCDI_Info);
}

void sCDI_PWBY::ReadFromCDI(sCDI_INFO* pCDI_info)
{
  cdiINT32 index=0;
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);
  cdi_inner_chunk_read_bool(index, chunkName, pCDI_info, &m_isAPowerByFile);
  cdi_inner_chunk_read_strg(index, chunkName, pCDI_info, &m_itemName);
  cdi_inner_chunk_read_strg(index, chunkName, pCDI_info, &m_itemId);
}

/****************************************************************\
|
| Function name:cdi_write_audt
|
| Purpose:writes audit trail
|
\****************************************************************/
VOID
cdi_write_audt(
               CDI_INFO cdi_info, /* the info structure */
               CDI_AUDT audt /* the audt structure */
               )
{
  ASSERT_VALID_CDI_INFO(cdi_info);

  audt->cccc = CDI_CHUNK_TYPE_AUDT;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_AUDT);

  cdi_write_cdichars(cdi_info, audt->audit_ur, audt->n_audit_ur_char);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_audt
|
| Purpose:reads audit trail
|
\****************************************************************/
CDI_AUDT
cdi_read_audt(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_AUDT audt= EXA_CALLOC_STRUCT(CDI_AUDT);

  ASSERT_VALID_CDI_INFO(cdi_info);

  audt->cccc = CDI_CHUNK_TYPE_AUDT;

  audt->audit_ur = cdi_read_cdichars(cdi_info, NULL, &audt->n_audit_ur_char);

  return(audt);
}

/****************************************************************\
|
| Function name:cdi_destroy_audt
|
| Purpose:destroys a comment
|
\****************************************************************/
VOID
cdi_destroy_audt(
                 CDI_AUDT audt        /* the audt to destroy */
                 )
{
  exa_free(audt->audit_ur);
  exa_free(audt);
}

/****************************************************************\
|
| Function name:cdi_write_undb
|
| Purpose:writes units_db
|
\****************************************************************/
VOID
cdi_write_undb(
               CDI_INFO cdi_info, /* the info structure */
               CDI_UNDB undb /* the undb structure */
               )
{
  undb->cccc = CDI_CHUNK_TYPE_UNDB;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_UNDB);

  cdi_write_cdichars(cdi_info, undb->units_db_str, undb->units_db_str_length);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_undb
|
| Purpose:reads units_db
|
\****************************************************************/
CDI_UNDB
cdi_read_undb(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_UNDB undb = EXA_CALLOC_STRUCT(CDI_UNDB);

  ASSERT_VALID_CDI_INFO(cdi_info);

  undb->cccc = CDI_CHUNK_TYPE_UNDB;

  undb->units_db_str = cdi_read_cdichars(cdi_info, NULL, &undb->units_db_str_length);

  return(undb);
}

asINT32 cdi_undb_read_length(CDI_INFO cdi_info)
{
  asINT32 length;
  asINT32 n_words = cdi_read_asINT32(cdi_info, &length, 1);

  return n_words == 1 ? length : -1;
}

asINT32 cdi_undb_read_chars(CDI_INFO cdi_info, VOID *buf, asINT32 n_chars)
{
  asINT32 n_read = cdi_read_chars(cdi_info, (CHARACTER *)buf, n_chars);

  return n_read;
}

static void skip_whitespace(STRING &s)
{
  size_t n_whitespace = strspn(s, " \n\t");
  s += n_whitespace;
}

static void skip_non_whitespace(STRING &s)
{
  size_t n_non_whitespace = strcspn(s, " \n\t\0"); // note inclusion of '\0' here
  s += n_non_whitespace;
}

static void delimit_next_token(STRING &s, STRING &token)
{
  skip_whitespace(s);
  token = s;
  skip_non_whitespace(s);
  *s++ = '\0';
}

static void skip_line(STRING &s)
{
  while (1) {
    if (*s == '\n') {
      s++;
      break;
    }
    if (*s == '\0')
      break;
    s++;
  }
}

// This can be called to read a UNDB chunk
CDI_CONDENSED_UNITS_DB cdi_read_condensed_units_db(CDI_INFO cdi_info)
{
  asINT32 units_db_length = cdi_undb_read_length(cdi_info);
  if (units_db_length < 0)
    return NULL;
  
  STRING units_db_str = new char [ units_db_length + 1];
  if (cdi_undb_read_chars(cdi_info, units_db_str, units_db_length) != units_db_length) {
    delete[] units_db_str;
    return NULL;
  }
  units_db_str[units_db_length] = '\0';

  // Only user-defined units added to unit classes are currently registered in CDB.
  // In the future, there should be an explicit indication in the CDI file of units
  // added/deleted to/from unit classes by the user.
  CDI_CONDENSED_UNITS_DB cdb = new sCDI_CONDENSED_UNITS_DB;

  STRING u   = units_db_str;
  STRING end = u + units_db_length;
  STRING token;
  while (u < end) {
    const asINT32 CHANGEABLE_LEN  = 11; // strlen("$changeable")
    const asINT32 USERDEFINED_LEN = 12; // strlen("$userdefined")
    const asINT32 CLASS_LEN       = 6;  // strlen("$class")
    if (strncmp(u, "$changeable", CHANGEABLE_LEN) == 0) {
      cSU_CHANGEABLE_UNIT unit;
      u += CHANGEABLE_LEN + 1;
      delimit_next_token(u, token);
      unit.name = std::string(token);
      delimit_next_token(u, token);
      unit.scale = atof(token);
      delimit_next_token(u, token);
      unit.offset = atof(token);
      skip_whitespace(u);
      cdb->changeable_units.push_back(unit);
    }
    else if (strncmp(u, "$userdefined", USERDEFINED_LEN) == 0) {
      sCDI_USER_DEFINED_UNIT unit;
      u += USERDEFINED_LEN + 1;
      delimit_next_token(u, token);
      unit.name = std::string(token);
      delimit_next_token(u, token); // skip "S"
      skip_whitespace(u);
      token = u;
      while (*u != '\n' && *u != '\0')
        u++;
      *u++ = '\0';
      unit.definition = std::string(token);
      cdb->user_defined_units.push_back(unit);
    }
    else if (strncmp(u, "$class", CLASS_LEN) == 0) {
      u += CLASS_LEN + 1;
      STRING unit_class;
      delimit_next_token(u, unit_class);
      skip_whitespace(u);
      STRING unit = u;
      while (*u != '\n' && *u != '\0')
        u++;
      *u++ = '\0';
      // There is an assumption here that the class definition lines follow the
      // user-defined unit lines. This must be true because all units must be
      // registered in the units database before they can be assigned to classes.
      ccDOTIMES(i, cdb->user_defined_units.size()) {
        if (strcmp(cdb->user_defined_units[i].name.c_str(), unit) == 0) {
          sCDI_UNIT_CLASS_ENTRY uce;
          uce.unit_class = std::string(unit_class);
          uce.unit = std::string(unit);
          cdb->unit_class_additions.push_back(uce);
          break;
        }
      }
    }
    else {
      skip_line(u);
    }
  }
  delete[] units_db_str;
  return cdb;
}

VOID sCDI_CONDENSED_UNITS_DB::update_units_db(UNITS_DB db)
{
  ccDOTIMES(i, changeable_units.size()) {
    UNITS_STATUS ustatus = units_set_changeable_unit(db, changeable_units[i].name.c_str(),
                                                     changeable_units[i].scale, changeable_units[i].offset);
    // If a unit is no longer changeable (due to a change in units.dat) do not issue an error
    if (ustatus != UNITS_STATUS_OK && ustatus != UNITS_STATUS_NOT_CHANGEABLE)
      msg_internal_error("CDI file contains invalid definition of unit \"%s\": %s", 
                         changeable_units[i].name.c_str(), units_error_string(ustatus));
  }

  ccDOTIMES(i, user_defined_units.size()) {
    UNITS_STATUS ustatus = units_define_unit(db, user_defined_units[i].name.c_str(), 
                                             user_defined_units[i].definition.c_str());
    if (ustatus != UNITS_STATUS_OK)
      msg_warn("Unable to add unit \"%s\" to units database: %s", 
               user_defined_units[i].name.c_str(), units_error_string(ustatus));
  }

  ccDOTIMES(i, unit_class_additions.size()) {
    UNITS_UNIT unit;
    UNITS_STATUS ustatus = units_parse_unit(db, unit_class_additions[i].unit.c_str(), &unit);
    if (ustatus == UNITS_STATUS_OK)
      ustatus = units_add_unit_to_class(db, unit, unit_class_additions[i].unit_class.c_str());
    if (ustatus != UNITS_STATUS_OK)
      msg_warn("Unable to add unit \"%s\" to unit class %s: %s", 
               unit_class_additions[i].unit.c_str(), 
               unit_class_additions[i].unit_class.c_str(),
               units_error_string(ustatus));
  }

  ccDOTIMES(i, unit_class_deletions.size()) {
    UNITS_UNIT unit;
    UNITS_STATUS ustatus = units_parse_unit(db, unit_class_deletions[i].unit.c_str(), &unit);
    if (ustatus == UNITS_STATUS_OK)
      ustatus = units_remove_unit_from_class(db, unit, unit_class_deletions[i].unit_class.c_str());
    if (ustatus != UNITS_STATUS_OK)
      msg_warn("Unable to remove unit \"%s\" from unit class %s: %s", 
               unit_class_deletions[i].unit.c_str(), 
               unit_class_deletions[i].unit_class.c_str(),
               units_error_string(ustatus));
  }
}

static VOID find_changeable_unit(CDI_CONDENSED_UNITS_DB cdb, cSTRING name, dFLOAT &scale, dFLOAT &offset)
{
  ccDOTIMES(i, cdb->changeable_units.size()) {
    if (strcmp(cdb->changeable_units[i].name.c_str(), name) == 0) {
      scale = cdb->changeable_units[i].scale;
      offset = cdb->changeable_units[i].offset;
      return;
    }
  }
  scale = 1;
  offset = 0;
}

bool cCDI_GLOB_BUNDLE::are_real_and_lattice_gamma_equal()
{ return fabs((gamma_mks - gamma_lattice) / gamma_mks) < 1e-6; }

bool cCDI_GLOB_BUNDLE::are_real_and_sim_mach_number_equal()
{ return fabs((mach_num_real - mach_num_sim) / mach_num_real) < 1e-6; }

bool cCDI_GLOB_BUNDLE::read_glob_chunk_into_bundle(CDI_INFO cdi_info, bool is_5g_sim, CDI_CONDENSED_UNITS_DB cdb)
{
  cCDI_GLOB_BUNDLE &bundle = *this;
  const dFLOAT CP_AIR = 1004.0;            // J/kg/K (assumed to be air)
  //const dFLOAT GAS_CONSTANT_AIR = 287.0;   // Gas constant for air, in m^2/(s^2*K)
  const dFLOAT OLD_LATTICE_GAMMA = 1.5;

  // Ancient default lattice values for the char params. This mimics the values 
  // in SRI_FILE:set_dimless_unit_translations()
  bundle.char_length_lattice   = 1;
  bundle.char_temp_lattice     = 0.42;
  bundle.char_vel_lattice      = 0.25;
  bundle.char_density_lattice  = 0.2;
  bundle.char_specific_heat_mks = CP_AIR;
  bundle.fluid_prandtl_num = 1;

  //MMD_EUTECTIC
  bundle.specific_heat_solid = 1.56;
  bundle.melting_temp = -11.0;
  bundle.boiling_temp = 100;
  bundle.latent_heat_of_fusion = 152.9;
  bundle.thermal_conductivity_solid = 0.75;
  bundle.thermal_conductivity_liquid = 0.57;
  bundle.density_solid = 1090;

  bundle.reynolds_number = 1;
  bundle.gas_constant_lattice = 1;
  bundle.gas_constant_mks = GAS_CONSTANT_AIR;
  bundle.gamma_mks = CP_AIR / (CP_AIR - GAS_CONSTANT_AIR);
  bundle.gamma_lattice = OLD_LATTICE_GAMMA;

  //kEps_SuperCycle
  bundle.bsuper_cycle_turbulence_solver = 0;
  bundle.super_cycling_factor = 2;

  //H2EoS
  bundle.h2EoSBeta_mks = 0.007691; //SI-MKS Unit

  //Radiation
  bundle.radiation_background_temp = bundle.char_temp_lattice;
  bundle.radiation_viewfactor_rays = 5000;
  bundle.radiation_update_ratio = 1.0;
  bundle.num_radiation_faces = 0.0;

  bool char_area_found = false;
  bool mach_sim_found = false;
  bool mach_real_found = false;

  // Read the lattice values of various char params
  ccCDI_DO_INNER_CHUNKS(i, "glob", cdi_info) {
    switch(cdi_get_type(cdi_info)) {
    default:
      break;
    case CDI_CHUNK_TYPE_CPRP: 
      {
        CDI_CPRP cprp = cdi_read_cprp(cdi_info);
        char* name = cprp->char_prop_name;
        if (strcmp(name, CDI_CASE_CHAR_LENGTH_NAME) == 0) {
          bundle.char_length_lattice = cprp->value;
        } else if (strcmp(name, CDI_CASE_CHAR_AREA_NAME) == 0) {
          bundle.char_area_lattice = cprp->value;
          char_area_found = TRUE;
        } else if (strcmp(name, CDI_CASE_CHAR_SPEED_NAME) == 0) {
          bundle.char_vel_lattice = cprp->value;
        } else if (strcmp(name, CDI_CASE_CHAR_TEMPERATURE_NAME) == 0) {
          bundle.char_temp_lattice = cprp->value;
        } else if (strcmp(name, CDI_CASE_SPECIFIC_HEAT_NAME) == 0) {
          bundle.char_specific_heat_mks = cprp->value;
        } else if (strcmp(name, CDI_CASE_CHAR_DENSITY_NAME) == 0) {
          if (is_5g_sim)
            bundle.char_density_lattice = cprp->value; // char_density is not scaled by DENSITY_CONVERSION_FACTOR in 5G cdi file
          else
            bundle.char_density_lattice = cprp->value / DENSITY_CONVERSION_FACTOR;
        } else if (strcmp(name, CDI_CASE_REYNOLDS_NUMBER_NAME) == 0) {
          bundle.reynolds_number = cprp->value;
        } else if (strcmp(name, CDI_CASE_LATTICE_GAS_CONSTANT_NAME) == 0) {
          bundle.gas_constant_lattice = cprp->value;
        } else if (strcmp(name, CDI_CASE_FLUID_PRANDTL_NUMBER_NAME) == 0) {
          bundle.fluid_prandtl_num = cprp->value;
        } else if (strcmp(name, CDI_CASE_SPECIFIC_HEAT_RATIO_NAME) == 0) {
          bundle.gamma_mks = cprp->value;
        } else if (strcmp(name, CDI_CASE_LATTICE_SPECIFIC_HEAT_RATIO_NAME) == 0) {
          bundle.gamma_lattice = cprp->value;
        } else if (strcmp(name, CDI_CASE_GAS_CONSTANT_NAME) == 0) {
          bundle.gas_constant_mks = cprp->value;
        } else if (strcmp(name, CDI_CASE_REAL_MACH_NUM) == 0) {
          bundle.mach_num_real = cprp->value;
          mach_real_found = true;
        } else if (strcmp(name, CDI_CASE_SIMULATED_MACH_NUM) == 0) {
          bundle.mach_num_sim = cprp->value;
          mach_sim_found = true;
        } else if (strcmp(name, CDI_CASE_SPECIFIC_HEAT_SOLID_NAME) == 0) {//MMD_EUTECTIC
          bundle.specific_heat_solid = cprp->value;
        } else if (strcmp(name, CDI_CASE_MELTING_TEMP_NAME) == 0) {
          bundle.melting_temp = cprp->value;
        } else if (strcmp(name, CDI_CASE_BOILING_TEMP_NAME) == 0) {
          bundle.boiling_temp = cprp->value;
        } else if (strcmp(name, CDI_CASE_LATENT_HEAT_OF_FUSION_NAME) == 0) {
          bundle.latent_heat_of_fusion = cprp->value;
        } else if (strcmp(name, CDI_CASE_THERMAL_CONDUCTIVITY_SOLID_NAME) == 0) {
          bundle.thermal_conductivity_solid = cprp->value;
        } else if (strcmp(name, CDI_CASE_THERMAL_CONDUCTIVITY_LIQUID_NAME) == 0) {
          bundle.thermal_conductivity_liquid = cprp->value;
        } else if (strcmp(name, CDI_CASE_DENSITY_SOLID_NAME) == 0) {
          bundle.density_solid = cprp->value;
        }  else if (strcmp(name, CDI_CASE_H2EOS_BETA_NAME) == 0){ //Abel-Nobel EoS Constant
          bundle.h2EoSBeta_mks = cprp->value;
        } else if (strcmp(name, CDI_CASE_RADIATION_BACKGROUND_TEMP) == 0) {
          bundle.radiation_background_temp = cprp->value;
        } else if (strcmp(name, CDI_CASE_RADIATION_VIEWFACTOR_RAYS) == 0) {
          bundle.radiation_viewfactor_rays = cprp->value;
        } else if (strcmp(name, CDI_CASE_RADIATION_UPDATE_RATIO) == 0) {
          bundle.radiation_update_ratio = cprp->value;
        } else if (strcmp(name, CDI_CASE_NUM_RADIATION_FACES) == 0) {
          bundle.num_radiation_faces = cprp->value;
        }

        cdi_destroy_cprp(cprp);
      }
      break;
    case CDI_CHUNK_TYPE_KESC://kEps_SuperCycle
      {
        sCDI_KESC kesc = cdi_read_kesc(cdi_info);
        bundle.bsuper_cycle_turbulence_solver = kesc.bKesc_activated;
        bundle.super_cycling_factor = kesc.super_cycling_factor;
      }
    }
  }

  bundle.char_static_pressure_lattice = bundle.char_density_lattice * bundle.char_temp_lattice * gas_constant_lattice;

  if (!char_area_found)
    bundle.char_area_lattice = bundle.char_length_lattice * bundle.char_length_lattice;

  if (!mach_real_found) {
    dFLOAT scale, offset;
    find_changeable_unit(cdb, "LatticeTemperature", scale, offset);
    dFLOAT char_temp_mks = bundle.char_temp_lattice * scale;
    dFLOAT sound_speed_real = sqrt(bundle.gas_constant_mks * bundle.gamma_mks * char_temp_mks);
    find_changeable_unit(cdb, "LatticeVelocity", scale, offset);
    dFLOAT char_vel_mks = bundle.char_vel_lattice * scale;
    bundle.mach_num_real = char_vel_mks / sound_speed_real;
  }

  if (!mach_sim_found) {
    dFLOAT sound_speed_sim = sqrt(bundle.gas_constant_lattice * bundle.gamma_lattice * bundle.char_temp_lattice);
    bundle.mach_num_sim = bundle.char_vel_lattice / sound_speed_sim;
  }

  return true; // success
}

// This adds dimless unit definitions to the provided condensed units database.
// It is only necessary to call this function for CDI version 4.16 and prior.
// For 4.17 and later, the dimless unit definitions are present in the
// units database. Returns true on success.
bool cCDI_GLOB_BUNDLE::define_dimless_units(CDI_CONDENSED_UNITS_DB cdb)
{
  cSU_DIMLESS_UNIT_INPUTS inputs;

  // Convert the char params from lattice to MKS units
  dFLOAT scale;
  dFLOAT offset;
  find_changeable_unit(cdb, "LatticeLength", scale, offset);
  inputs.charLengthMKS = char_length_lattice * scale;
  inputs.charAreaMKS = char_area_lattice * scale * scale;

  find_changeable_unit(cdb, "LatticeTemperature", scale, offset);
  inputs.charTempMKS = char_temp_lattice * scale;

  find_changeable_unit(cdb, "LatticeDensity", scale, offset);
  inputs.charDensityMKS = char_density_lattice * scale;
  dFLOAT dynPressureScale = scale;

  dFLOAT vel_scale; // char_vel_mks / char_vel_lat  
  find_changeable_unit(cdb, "LatticeVelocity", vel_scale, offset);
  inputs.charVelMKS = char_vel_lattice * vel_scale;
  dynPressureScale *= vel_scale * vel_scale;

  find_changeable_unit(cdb, "LatticeStaticPressure", scale, offset);
  inputs.charStaticPressureMKS = char_static_pressure_lattice * dynPressureScale;
  inputs.charStaticPressureMKS += dynPressureScale * offset;

  inputs.charViscosityMKS = inputs.charVelMKS * inputs.charLengthMKS / reynolds_number;
  inputs.fluidPrandtlNum = fluid_prandtl_num;
  inputs.charSpecificHeatMKS = char_specific_heat_mks;
  
  inputs.ComputeDimlessUnitDefinitions(cdb->changeable_units);

  return true; // success
}



/****************************************************************\
|
| Function name:cdi_destroy_undb
|
| Purpose:destroys an units_db
|
\****************************************************************/
VOID
cdi_destroy_undb(
                 CDI_UNDB undb        /* the undb to destroy */
                 )
{
  exa_free(undb->units_db_str);
  exa_free(undb);
}

/****************************************************************\
|
| Function name:cdi_write_eqns
|
| Purpose:writes equations
|
\****************************************************************/
VOID
cdi_write_eqns(
               CDI_INFO cdi_info, /* the info structure */
               CDI_EQNS eqns /* the eqns structure */
               )
{
  eqns->cccc = CDI_CHUNK_TYPE_EQNS;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_EQNS);

  cdi_write_cdichars(cdi_info, eqns->equations, eqns->equations_length);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23))
      cdi_write_asINT32(cdi_info, &eqns->pthermTimeAvailable, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_eqns
|
| Purpose:reads equations
|
\****************************************************************/
CDI_EQNS
cdi_read_eqns(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_EQNS eqns = EXA_CALLOC_STRUCT(CDI_EQNS);

  eqns->cccc = CDI_CHUNK_TYPE_EQNS;
      
  eqns->equations = cdi_read_cdichars(cdi_info, NULL, &eqns->equations_length);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23))
      cdi_read_asINT32(cdi_info, &eqns->pthermTimeAvailable, 1);
  else 
      eqns->pthermTimeAvailable  = FALSE;

  return(eqns);
}

asINT32 cdi_eqns_read_length(CDI_INFO cdi_info)
{
  asINT32 length;
  asINT32 n_words = cdi_read_asINT32(cdi_info, &length, 1);

  return n_words == 1 ? length : -1;
}

asINT32 cdi_eqns_read_chars(CDI_INFO cdi_info, VOID *buf, asINT32 n_chars)
{
  asINT32 n_read = cdi_read_chars(cdi_info, (CHARACTER *)buf, n_chars);

  return n_read;
}

/****************************************************************\
|
| Function name:cdi_destroy_eqns
|
| Purpose:destroys an equations
|
\****************************************************************/
VOID
cdi_destroy_eqns(
                 CDI_EQNS eqns        /* the eqns to destroy */
                 )
{
  exa_free(eqns->equations);
  exa_free(eqns);
}

/****************************************************************\
|
| Function name:cdi_write_gtbl
|
| Purpose:writes a grid-table
|
\****************************************************************/
VOID
cdi_write_gtbl(CDI_INFO cdi_info,
               CDI_GTBL gtbl)
{
  gtbl->cccc = CDI_CHUNK_TYPE_GTBL;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_GTBL);

  cdi_write_cdichars(cdi_info, gtbl->name, strlen(gtbl->name));
  cdi_write_cdichars(cdi_info, gtbl->filename, strlen(gtbl->filename));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 4))
    cdi_write_cdichars(cdi_info, gtbl->absolute_filename, strlen(gtbl->absolute_filename));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 1))
    cdi_write_asINT32(cdi_info, &gtbl->flags, 1);
  cdi_write_asINT32(cdi_info, &gtbl->coord_sys, 1);
  cdi_write_asINT32(cdi_info, &gtbl->read_during_sim, 1);
  cdi_write_asINT32(cdi_info, &gtbl->read_after_meas, 1);
  cdi_write_asINT32(cdi_info, &gtbl->meas_window_index, 1);
  cdi_write_asINT32(cdi_info, &gtbl->first_meas_frame_to_read_after, 1);
  cdi_write_asINT32(cdi_info, &gtbl->num_meas_frames_between_reads, 1);
  cdi_write_cdichars(cdi_info, gtbl->command_to_run_before_read, 
                     strlen(gtbl->command_to_run_before_read));
  cdi_write_asINT32(cdi_info, &gtbl->first_interval, 1);
  cdi_write_asINT32(cdi_info, &gtbl->subsequent_intervals, 1);
  cdi_write_asINT32(cdi_info, &gtbl->end_time, 1);
  cdi_write_cdichars(cdi_info, gtbl->table_string, strlen(gtbl->table_string));

  cdi_pop(cdi_info);
  
}

/****************************************************************\
|
| Function name:cdi_read_gtbl
|
| Purpose:reads a grid-table
|
\****************************************************************/
CDI_GTBL
cdi_read_gtbl(CDI_INFO cdi_info)
{
  asINT32 tempInt;
  CDI_GTBL gtbl = EXA_CALLOC_STRUCT(CDI_GTBL);

  gtbl->cccc = CDI_CHUNK_TYPE_GTBL;

  gtbl->name = cdi_read_cdichars(cdi_info, NULL, &tempInt);
  gtbl->filename = cdi_read_cdichars(cdi_info, NULL, &tempInt);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 4))
    gtbl->absolute_filename = cdi_read_cdichars(cdi_info, NULL, &tempInt);
  else
    gtbl->absolute_filename = EXA_STRDUP("");
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 1))
    cdi_read_asINT32(cdi_info, &gtbl->flags, 1);
  else
    gtbl->flags = 0;
  cdi_read_asINT32(cdi_info, &gtbl->coord_sys, 1);
  cdi_read_asINT32(cdi_info, &gtbl->read_during_sim, 1);
  cdi_read_asINT32(cdi_info, &gtbl->read_after_meas, 1);
  cdi_read_asINT32(cdi_info, &gtbl->meas_window_index, 1);
  cdi_read_asINT32(cdi_info, &gtbl->first_meas_frame_to_read_after, 1);
  cdi_read_asINT32(cdi_info, &gtbl->num_meas_frames_between_reads, 1);
  gtbl->command_to_run_before_read = cdi_read_cdichars(cdi_info, NULL, &tempInt);
  cdi_read_asINT32(cdi_info, &gtbl->first_interval, 1);
  cdi_read_asINT32(cdi_info, &gtbl->subsequent_intervals, 1);
  cdi_read_asINT32(cdi_info, &gtbl->end_time, 1);
  gtbl->table_string = cdi_read_cdichars(cdi_info, NULL, &tempInt);
  gtbl->table_length = tempInt;

  return(gtbl);
}

/****************************************************************\
|
| Function name:cdi_destroy_gtbl
|
| Purpose:destroys a grid-table
|
\****************************************************************/
VOID
cdi_destroy_gtbl(CDI_GTBL gtbl)
{
  exa_free(gtbl->name);
  exa_free(gtbl->filename);
  exa_free(gtbl->absolute_filename);
  exa_free(gtbl->command_to_run_before_read);
  exa_free(gtbl->table_string);
  exa_free(gtbl);
}

/****************************************************************\
|
| Function name:cdi_write_tabl
|
| Purpose:writes a table
|
\****************************************************************/
VOID
cdi_write_tabl(
               CDI_INFO cdi_info, /* the info structure */
               cCDI_TABL tabl /* the tabl structure */
               )
{
  tabl->cccc = CDI_CHUNK_TYPE_TABL;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_TABL);

  cdi_write_asINT32(cdi_info, &tabl->num_tables, 1);

  DOTIMES(i, tabl->num_tables, {
    cdi_write_cdichars(cdi_info, tabl->tables[i].name, strlen(tabl->tables[i].name));
    cdi_write_cdichars(cdi_info, tabl->tables[i].table_string, strlen(tabl->tables[i].table_string));
    cdi_write_asINT32(cdi_info, &tabl->tables[i].coord_sys, 1);
  });

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_tabl
|
| Purpose:reads tables
|
\****************************************************************/
cCDI_TABL
cdi_read_tabl(
              CDI_INFO cdi_info /* the info structure */
              )
{
  asINT32 tempInt;
  cCDI_TABL tabl = EXA_CALLOC_STRUCT(cCDI_TABL);

  tabl->cccc = CDI_CHUNK_TYPE_TABL;

  cdi_read_asINT32(cdi_info, &tabl->num_tables, 1);

  tabl->tables = EXA_CALLOC_ARRAY(sCDI_GTBL, tabl->num_tables);

  DOTIMES(i, tabl->num_tables, {
    tabl->tables[i].name = cdi_read_cdichars(cdi_info, NULL, &tempInt);
    tabl->tables[i].table_string = cdi_read_cdichars(cdi_info, NULL, &tempInt);
    tabl->tables[i].table_length = tempInt;
    cdi_read_asINT32(cdi_info, &tabl->tables[i].coord_sys, 1);
  });

  return(tabl);
}

/****************************************************************\
|
| Function name:cdi_destroy_tabl
|
| Purpose:destroys a table
|
\****************************************************************/
VOID
cdi_destroy_tabl(
                 cCDI_TABL tabl        /* the table to destroy */
                 )
{
  DOTIMES(i, tabl->num_tables, {
    exa_free(tabl->tables[i].name);
    exa_free(tabl->tables[i].table_string);
  });
  exa_free(tabl->tables);
  exa_free(tabl);
}

/****************************************************************\
|
| Function name:cdi_write_csys
|
| Purpose:writes coordinate systems
|
\****************************************************************/
VOID
cdi_write_csys(
               CDI_INFO cdi_info, /* the info structure */
               cCDI_CSYS csys /* the csys structure */
               )
{
  csys->cccc = CDI_CHUNK_TYPE_CSYS;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_CSYS);

  cdi_write_asINT32(cdi_info, &csys->num_coord_systems, 1);

  DOTIMES(i, csys->num_coord_systems, {
    cdi_write_cdistring(cdi_info, csys->coord_systems[i].name);
    cdi_write_idFLOAT(cdi_info, (idFLOAT *) &csys->coord_systems[i].g_to_l_xform, 16);
    cdi_write_idFLOAT(cdi_info, (idFLOAT *) &csys->coord_systems[i].l_to_g_xform, 16);
  });

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_csys
|
| Purpose:reads coordinate systems
|
\****************************************************************/
cCDI_CSYS
cdi_read_csys(
              CDI_INFO cdi_info /* the info structure */
              )
{
  cCDI_CSYS csys = EXA_CALLOC_STRUCT(cCDI_CSYS);

  csys->cccc = CDI_CHUNK_TYPE_CSYS;

  cdi_read_asINT32(cdi_info, &csys->num_coord_systems, 1);

  csys->coord_systems = EXA_CALLOC_ARRAY(sCDI_CSYS, csys->num_coord_systems);

  DOTIMES(i, csys->num_coord_systems, {
    cdi_read_cdistring(cdi_info, &(csys->coord_systems[i].name));
    cdi_read_idFLOAT(cdi_info, (idFLOAT *) &csys->coord_systems[i].g_to_l_xform, 16);
    cdi_read_idFLOAT(cdi_info, (idFLOAT *) &csys->coord_systems[i].l_to_g_xform, 16);
  });

  return(csys);
}

/****************************************************************\
|
| Function name:cdi_read_cyl
|
| Purpose:reads cylinder
|
\****************************************************************/
void
cdi_read_cyl(
             CDI_INFO cdi_info, /* the info structure */
             double& x1, double& y1, double& z1, 
             double& x2, double& y2, double& z2, 
             double& r1, double& r2
             )
{
  switch(cio_get_type(cdi_info->cio_info)) {
  case CDI_CHUNK_TYPE_CYLR: 
    asINT32 count = cio_get_count(cdi_info->cio_info);
    ccDOTIMES(i, count) {
      cio_descend(cdi_info->cio_info);
      
      switch(cio_get_type(cdi_info->cio_info)) {
      case CDI_CHUNK_TYPE_CVDP:
        CDI_CVDP nchunk = cdi_read_cvdp(cdi_info);
        switch(nchunk->type) {
        case CDI_VAR_ID_CYL_BEGIN_X:  x1 = nchunk->value; break;
        case CDI_VAR_ID_CYL_BEGIN_Y:  y1 = nchunk->value; break;
        case CDI_VAR_ID_CYL_BEGIN_Z:  z1 = nchunk->value; break;
        case CDI_VAR_ID_CYL_END_X:    x2 = nchunk->value; break;
        case CDI_VAR_ID_CYL_END_Y:    y2 = nchunk->value; break;
        case CDI_VAR_ID_CYL_END_Z:    z2 = nchunk->value; break;
        case CDI_VAR_ID_BEGIN_RADIUS: r1 = nchunk->value; break;
        case CDI_VAR_ID_END_RADIUS:   r2 = nchunk->value; break;
        default: break;
        }
        EXA_FREE(nchunk);
      }
      cio_ascend(cdi_info->cio_info);
    }
  }
}


/****************************************************************        \
|
| Function name:cdi_read_lrf
|
| Purpose:reads reference frame
|
\****************************************************************/
CDI_LRF
cdi_read_lrf(
             CDI_INFO cdi_info, /* the info structure */
             bool warn_about_unexpected_params
             )
{
  CDI_LRF lrf = EXA_CALLOC_STRUCT(CDI_LRF); 
  lrf->half_plane_dir[0]= 0;
  lrf->half_plane_dir[1]= 0;
  lrf->half_plane_dir[2]= 0;
  lrf->has_transonic_flow = FALSE;
  lrf->is_velocity_via_torque = FALSE;
  
  lrf->name = EXA_STRDUP("Unknown local ref frame");
  BOOLEAN found_name = FALSE;
  
  lrf->phys_type_desc = cdi_lookup_physics(CDI_PHYS_TYPE_LOCAL_REF_FRAME);
  lrf->n_parameters = lrf->phys_type_desc->n_continuous_dp;
  lrf->n_initial_conditions = 0;
  lrf->parameters = EXA_CALLOC_ARRAY(sCDI_PHYSICS_VARIABLE, lrf->n_parameters);
  lrf->initial_conditions = NULL;
  
  cBOOLEAN parameter_found[CDI_MAX_CONTINUOUS_DP];

  ccDOTIMES(j, lrf->n_parameters) {
    parameter_found[j] = FALSE;
  }

  { // initialize to account for old CDI files that lack this parameter
    asINT32 index = lrf->phys_type_desc->continuous_dp_index (CDI_VAR_ID_MRF_TYPE);
    parameter_found[index] = TRUE;
    lrf->parameters[index].value = CDI_STATIC_LRF;
    lrf->parameters[index].name  = NULL;
  }

  asINT32 count = cio_get_count(cdi_info->cio_info);
  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);
    
    switch(cio_get_type(cdi_info->cio_info)) {
    case CDI_CHUNK_TYPE_NAME: {
      CDI_NAME nameChunk = cdi_read_name(cdi_info);
      lrf->name = nameChunk->name;
      EXA_FREE(nameChunk);
      found_name = TRUE;
      break;
    }
    case CDI_CHUNK_TYPE_EQDP: {
      CDI_EQDP eqChunk = cdi_read_eqdp(cdi_info);
      asINT32 index = lrf->phys_type_desc->continuous_dp_index (eqChunk->type);
      if (index < 0) {
        if (warn_about_unexpected_params)
          msg_warn("Invalid EQDP %d for CDI LRF chunk \"%s\".", eqChunk->type, lrf->name);
      } else {
        parameter_found[index]  = TRUE;
        lrf->parameters[index].value = 0;
        lrf->parameters[index].name  = eqChunk->var_name;
      }
      EXA_FREE(eqChunk);
      break;
    }
    case CDI_CHUNK_TYPE_CVDP: {
      CDI_CVDP cvChunk = cdi_read_cvdp(cdi_info);
      asINT32 index = lrf->phys_type_desc->continuous_dp_index (cvChunk->type);
      if (index < 0) {
        if (warn_about_unexpected_params && cvChunk->type != CDI_VAR_ID_CONTAIN_TRANSONIC_FLOW)
          msg_warn("Invalid CVDP %d for CDI LRF chunk \"%s\".", cvChunk->type, lrf->name);
      } else {
        parameter_found[index]  = TRUE;
        lrf->parameters[index].value = cvChunk->value;
        lrf->parameters[index].name  = NULL;
      }

      switch(cvChunk->type) {
      case CDI_VAR_ID_CYL_BEGIN_X: lrf->cyl_begin[0] = cvChunk->value; break;
      case CDI_VAR_ID_CYL_BEGIN_Y: lrf->cyl_begin[1] = cvChunk->value; break;
      case CDI_VAR_ID_CYL_BEGIN_Z: lrf->cyl_begin[2] = cvChunk->value; break;
      case CDI_VAR_ID_CYL_END_X:   lrf->cyl_end[0]   = cvChunk->value; break;
      case CDI_VAR_ID_CYL_END_Y:   lrf->cyl_end[1]   = cvChunk->value; break;
      case CDI_VAR_ID_CYL_END_Z:   lrf->cyl_end[2]   = cvChunk->value; break;
      case CDI_VAR_ID_MRF_TYPE:    lrf->lrf_type = (CDI_LRF_TYPE) ( (int) cvChunk->value );  break;
      case CDI_VAR_ID_CONTAIN_TRANSONIC_FLOW:
           lrf->has_transonic_flow     = (cBOOLEAN)((int)cvChunk->value); break;
      case CDI_VAR_ID_VELOCITY_VIA_TORQUE:
           lrf->is_velocity_via_torque = (cBOOLEAN)((int)cvChunk->value); break;
      default:         break;
      }
      EXA_FREE(cvChunk);
      
      break;
    }
    case CDI_CHUNK_TYPE_CYLS: {
      asINT32 numCyls = cio_get_count(cdi_info->cio_info); // the number of cylinders
      CDI_PNTS pnts = EXA_CALLOC_STRUCT(CDI_PNTS); 
      pnts->cccc = CDI_CHUNK_TYPE_PNTS;
      pnts->dim = 2;
      pnts->num_points = numCyls * 2 + 2;
      if ( pnts->num_points == 0 || pnts->dim <= 0 )
        pnts->points = NULL;
      else {
        pnts->points = EXA_MALLOC_ARRAY(double, pnts->num_points*pnts->dim);
        
        ccDOTIMES(i, numCyls) {
          double x1, y1, z1, x2, y2, z2, r1, r2, d1, d2, dx, dy, dz;
          cio_descend(cdi_info->cio_info);
          cdi_read_cyl( cdi_info, x1, y1, z1, x2, y2, z2, r1, r2 );
          cio_ascend(cdi_info->cio_info);
          
          dx = x1 - lrf->cyl_begin[0];
          dy = y1 - lrf->cyl_begin[1];
          dz = z1 - lrf->cyl_begin[2];
          d1 = sqrt( dx * dx + dy * dy + dz * dz );
          dx = x2 - lrf->cyl_begin[0];
          dy = y2 - lrf->cyl_begin[1];
          dz = z2 - lrf->cyl_begin[2];
          d2 = sqrt( dx * dx + dy * dy + dz * dz );
          
          // If it's the first or last point, add the vertices on the axis
          // If it's the first point also set the half plane direrction
          if ( i == 0 ) {
            
            if ( dx == 0 && dz == 0 ) {
              // If the axis of the cylinder is [0, ?, 0], then set the half plane dir to be [1 0 0]
              lrf->half_plane_dir[0] = 1.0;
            }
            else {
              // else set the half plane dir to be [0 1 0]
              lrf->half_plane_dir[1] = 1.0;
            }
            
            pnts->points[0] = d1;
            pnts->points[1] = 0;
          }
          if ( i == numCyls - 1 ) {
            pnts->points[pnts->num_points * 2 - 2] = d2;
            pnts->points[pnts->num_points * 2 - 1] = 0;
          }
          
          int index = i * 2 + 1;
          pnts->points[index * 2    ] = d1;
          pnts->points[index * 2 + 1] = r1;
          index++;
          pnts->points[index * 2    ] = d2;
          pnts->points[index * 2 + 1] = r2;
        }
      }
      lrf->pnts = *pnts;
      break;
    }
    case CDI_CHUNK_TYPE_VREV: {
      asINT32 vCount = cio_get_count(cdi_info->cio_info); 
      CDI_PNTS pnts;

      ccDOTIMES(i, vCount) {
        cio_descend(cdi_info->cio_info);
        switch(cio_get_type(cdi_info->cio_info)) {
          
        case CDI_CHUNK_TYPE_PNTS:
          pnts = cdi_read_pnts(cdi_info);
          break;
          
        case CDI_CHUNK_TYPE_CVDP: 
          CDI_CVDP nchunk = cdi_read_cvdp(cdi_info);
          switch(nchunk->type) {
          case CDI_VAR_ID_PLANE_VEC_X: lrf->half_plane_dir[0] = nchunk->value; break;
          case CDI_VAR_ID_PLANE_VEC_Y: lrf->half_plane_dir[1] = nchunk->value; break;
          case CDI_VAR_ID_PLANE_VEC_Z: lrf->half_plane_dir[2] = nchunk->value; break;
          default: break;
          }
          EXA_FREE(nchunk);
          break;
          
        }
        
        cio_ascend(cdi_info->cio_info);
      }
      
      lrf->pnts = *pnts;
      break;
    }
    case CDI_CHUNK_TYPE_NULL:
      cdi_read_null(cdi_info);
      break;
      
    default: 
      break;
    }
    
    cio_ascend(cdi_info->cio_info);
  }

  if (!found_name)
    msg_internal_error("Expected NAME as first sub-chunk of CDI LRF chunk.");

  ccDOTIMES(j, lrf->n_parameters) {
    asINT32 var_id = lrf->phys_type_desc->continuous_dp_var[j]->id;
    // CDI v4.8 added extra fields to the lrfs chunk
    // If an older CDI file is provided, then we should not expect these fields to be present
    if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 8)) {
      switch(var_id) {
        case CDI_VAR_ID_VELOCITY_VIA_TORQUE:
        case CDI_VAR_ID_RESISTIVE_TORQUE:
        case CDI_VAR_ID_EXTERNAL_TORQUE:
        case CDI_VAR_ID_START_TIME:
        case CDI_VAR_ID_END_TIME:
        case CDI_VAR_ID_TIME_ACCURATE:
        case CDI_VAR_ID_MOMENT_OF_INERTIA:
        case CDI_VAR_ID_MEAS_WINDOW_INDEX:
        case CDI_VAR_ID_INITIAL_ANG_VEL:
          parameter_found[j] = TRUE;
      }
    }
    if (!parameter_found[j]) {
      asINT32 var_id = lrf->phys_type_desc->continuous_dp_var[j]->id;
      
      msg_internal_error("Missing parameter of type %d in CDI LRF chunk \"%s\".",
                         var_id, lrf->name);
    }
  }
  
  return( lrf );
}



/****************************************************************        \
|
| Function name:cdi_destroy_csys
|
| Purpose:destroys coordinate systems
|
\****************************************************************/
VOID
cdi_destroy_csys(
                 cCDI_CSYS csys /* the csys to destroy */
                 )
{
  DOTIMES(i, csys->num_coord_systems, {
    exa_free(csys->coord_systems[i].name);
  });
  exa_free(csys->coord_systems);
  exa_free(csys);
}


static VOID destroy_physics_desc_contents(CDI_PHYSICS_DESCRIPTOR pd)
{
  // need to free the strings also 
  exa_free(pd->name);

  ccDOTIMES(i, pd->n_parameters) {
    if (pd->parameters[i].name)
      exa_free(pd->parameters[i].name);
  }

  ccDOTIMES(i, pd->n_initial_conditions) {
    if (pd->initial_conditions[i].name)
      exa_free(pd->initial_conditions[i].name);
  }

  exa_free(pd->parameters);
  exa_free(pd->initial_conditions);
}

/****************************************************************\
|
| Function name:cdi_destroy_lrf
|
| Purpose:destroys reference frame
|
\****************************************************************/
VOID
cdi_destroy_lrf(
                CDI_LRF lrf /* the ref frame to destroy */
                )
{
  destroy_physics_desc_contents(lrf);
  exa_free(lrf);
}

/****************************************************************\
|
| Function name:cdi_write_rgpn, cdi_write_rgpn_encoded64
|
| Purpose:writes rgpn (region protection)
|
\****************************************************************/
VOID
cdi_write_rgpn(
               CDI_INFO cdi_info,
               CDI_RGPN rgpn
               )
{
  rgpn->cccc = CDI_CHUNK_TYPE_RGPN;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_RGPN);

  cdi_set_encryption_on(cdi_info);

  auINT32 numRegions = rgpn->regionIds.size();
  cdi_write_auINT32(cdi_info, &numRegions, 1);

  auINT32 *regionPairs = new auINT32[numRegions*2];

  ccDOTIMES(i, numRegions) {
    regionPairs[i] = rgpn->regionIds[i];
    regionPairs[numRegions+i] = rgpn->protectionIds[i];
  }

  cdi_write_auINT32(cdi_info, regionPairs, 2*numRegions);

  delete[] regionPairs;

  cdi_set_encryption_off(cdi_info);
  cdi_pop(cdi_info);
}


/****************************************************************\
|
| Function name:cdi_read_rgpn
|
| Purpose:reads rgpn (region protection)
|
\****************************************************************/
CDI_RGPN
cdi_read_rgpn(
              CDI_INFO cdi_info
              )
{
  cdi_set_encryption_on(cdi_info);

  CDI_RGPN rgpn = new sCDI_RGPN; 
  rgpn->cccc = CDI_CHUNK_TYPE_RGPN;
  
  cdiUINT32 numRegions = 0;
  cdi_read_auINT32(cdi_info, &numRegions, 1);
  rgpn->n_regions = numRegions;

  cdiUINT32 *regionPairs = new cdiUINT32[numRegions*2];
  cdi_read_auINT32(cdi_info, regionPairs, 2*numRegions);

  // The region array should be [0, 1, 2, ...]
  // Use it to verify that the file data was not altered, if not encrypt all
  ccDOTIMES(i, numRegions) {
    if(regionPairs[i] != (auINT32) i) {
      for (size_t j=0; j < numRegions; ++j)
        regionPairs[2*numRegions+1] = 1;
      msg_warn("Invalid RGPN data, all the vertices of the regions will be encrypted.\n");
      break;
    }

  }

  ccDOTIMES(i, numRegions) {
    rgpn->regionIds.push_back(regionPairs[i]);
    rgpn->protectionIds.push_back(regionPairs[numRegions+i]);
  }

  delete[] regionPairs;

  cdi_set_encryption_off(cdi_info);

  return(rgpn);
}

/****************************************************************\
|
| Function name:cdi_destroy_rgpn
|
| Purpose:destroys a rgpn
|
\****************************************************************/
VOID
cdi_destroy_rgpn(
                 CDI_RGPN rgpn
                 )
{
  delete rgpn;
}

/****************************************************************        \
|
| Function name:cdi_write_vrtx, cdi_write_vrtx_encoded64
|
| Purpose:writes vertices
|
\****************************************************************/
VOID
cdi_write_vrtx(
               CDI_INFO cdi_info, /* the info structure */
               CDI_VRTX vrtx        /* the vrtx structure */
               )
{   
  vrtx->cccc = CDI_CHUNK_TYPE_VRTX;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_VRTX);

  cdi_set_encryption_on(cdi_info);
  cdi_write_asINT32(cdi_info, &vrtx->n_vertex, 1);
  cdi_write_idFLOAT(cdi_info, vrtx->coord, 3 * vrtx->n_vertex);
  cdi_set_encryption_off(cdi_info);

  cdi_pop(cdi_info);

}


/****************************************************************\
|
| Function name:cdi_read_vrtx
|
| Purpose:reads vertices
|
\****************************************************************/
CDI_VRTX
cdi_read_vrtx(
              CDI_INFO cdi_info        /* the info structure */
              )
{
  cdi_set_encryption_on(cdi_info);      

  CDI_VRTX vrtx = EXA_CALLOC_STRUCT(CDI_VRTX);

  vrtx->cccc = CDI_CHUNK_TYPE_VRTX;
      
  cdi_read_asINT32(cdi_info, &vrtx->n_vertex, 1);
  vrtx->coord = NULL;

  vrtx->coord = EXA_CALLOC_ARRAY(idFLOAT, 3 * vrtx->n_vertex);
  cdi_read_idFLOAT(cdi_info, vrtx->coord, 3 * vrtx->n_vertex);

  cdi_set_encryption_off(cdi_info);

  return(vrtx);
}

/****************************************************************\
|
| Function name:cdi_destroy_vrtx
|
| Purpose:destroys a vertex 
|
\****************************************************************/
VOID
cdi_destroy_vrtx(
                 CDI_VRTX vrtx        /* the vrtx to destroy */
                 )
{
  exa_free(vrtx->coord);
  exa_free(vrtx);
}

/****************************************************************\
|
| Function name:cdi_write_fact
|
| Purpose:writes facets
|
\****************************************************************/
VOID
cdi_write_fact(
               CDI_INFO cdi_info, /* the info structure */
               cCDI_FACT fact        /* the fact structure */
               )
{   
  fact->cccc = CDI_CHUNK_TYPE_FACT;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_FACT);

  cdi_write_asINT32(cdi_info, &fact->n_facet, 1);

  DOTIMES(i, fact->n_facet, {
    cdi_write_asINT32(cdi_info, &fact->facet[i].face, 1);
    cdi_write_asINT32(cdi_info, fact->facet[i].edgehalf, 3);
    cdi_write_idFLOAT(cdi_info, fact->facet[i].normal, 3);
  });

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_fact
|
| Purpose:reads facets
|
\****************************************************************/
cCDI_FACT
cdi_read_fact(
              CDI_INFO cdi_info        /* the info structure */
              )
{   
  cCDI_FACT fact = EXA_CALLOC_STRUCT(cCDI_FACT); /* the fact structure */

  fact->cccc = CDI_CHUNK_TYPE_FACT;
      
  cdi_read_asINT32(cdi_info, &fact->n_facet, 1);

  fact->facet = EXA_CALLOC_ARRAY(sCDI_FACT_OLD, fact->n_facet);
      
  DOTIMES(i, fact->n_facet, {
    cdi_read_asINT32(cdi_info, &fact->facet[i].face, 1);
    cdi_read_asINT32(cdi_info, fact->facet[i].edgehalf, 3);
    cdi_read_idFLOAT(cdi_info, fact->facet[i].normal, 3);
  });

  return(fact);      
}

/****************************************************************\
|
| Function name:cdi_destroy_fact
|
| Purpose:destroys a facet 
|
\****************************************************************/
VOID
cdi_destroy_fact(
                 cCDI_FACT fact        /* the fact to destroy */
                 )
{
  exa_free(fact->facet);
  exa_free(fact);
}

/****************************************************************\
|
| Function name:cdi_write_edge
|
| Purpose:writes edges
|
\****************************************************************/
VOID
cdi_write_edge(
               CDI_INFO cdi_info, /* the info structure */
               cCDI_EDGE edge        /* the edge structure */
               )
{   
  edge->cccc = CDI_CHUNK_TYPE_EDGE;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_EDGE);

  cdi_write_asINT32(cdi_info, &edge->n_edgehalf, 1);

  DOTIMES(i, edge->n_edgehalf, {
    cdi_write_asINT32(cdi_info, &edge->edgehalf[i].conj_facet, 1);
    cdi_write_asINT32(cdi_info, &edge->edgehalf[i].conj_edgehalf, 1);
    cdi_write_asINT32(cdi_info, &edge->edgehalf[i].head_vert, 1);
    cdi_write_asINT32(cdi_info, &edge->edgehalf[i].tail_vert, 1);
  });
  
  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_edge
|
| Purpose:reads edges
|
\****************************************************************/
cCDI_EDGE
cdi_read_edge(
              CDI_INFO cdi_info        /* the info structure */
              )
{   
  cCDI_EDGE edge = EXA_CALLOC_STRUCT(cCDI_EDGE); /* the edge structure */

  edge->cccc = CDI_CHUNK_TYPE_EDGE;

  cdi_read_asINT32(cdi_info, &edge->n_edgehalf, 1);

  edge->edgehalf = EXA_CALLOC_ARRAY(sCDI_EDGE, edge->n_edgehalf);

  DOTIMES(i, edge->n_edgehalf, {
    cdi_read_asINT32(cdi_info, &edge->edgehalf[i].conj_facet, 1);
    cdi_read_asINT32(cdi_info, &edge->edgehalf[i].conj_edgehalf, 1);
    cdi_read_asINT32(cdi_info, &edge->edgehalf[i].head_vert, 1);
    cdi_read_asINT32(cdi_info, &edge->edgehalf[i].tail_vert, 1);
  });

  return(edge);
}

/****************************************************************\
|
| Function name:cdi_destroy_edge
|
| Purpose:destroys a edge 
|
\****************************************************************/
VOID
cdi_destroy_edge(
                 cCDI_EDGE edge        /* the edge to destroy */
                 )
{
  exa_free(edge->edgehalf);
  exa_free(edge);
}

/****************************************************************\
|
| Function name:cdi_write_face
|
| Purpose:writes face
|
\****************************************************************/
VOID
cdi_write_face_old(
               CDI_INFO cdi_info, /* the info structure */
               CDI_FACE_OLD face /* the face structure */
               )
{
  ASSERT_VALID_CDI_INFO(cdi_info);

  face->cccc = CDI_CHUNK_TYPE_FACE;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_FACE);

  if(cdi_get_major_version(cdi_info) >= 1) {
      if(face->name==NULL)
          cdi_write_cdistring(cdi_info, "");
      else
          cdi_write_cdistring(cdi_info, face->name);

      assert(face->index>=0);
      cdi_write_asINT32(cdi_info, &face->index, 1);

      if(face->color==NULL)
          cdi_write_cdistring(cdi_info, (STRING)"");
      else
          cdi_write_cdistring(cdi_info, face->color);
  }

  cdi_write_asINT32(cdi_info, &face->n_facet, 1);
  cdi_write_asINT32(cdi_info, face->facet, face->n_facet);
  cdi_write_asINT32(cdi_info, &face->prop, 1);

  cdi_pop(cdi_info);
}


/****************************************************************\
|
| Function name:cdi_read_face
|
| Purpose:reads face
|
\****************************************************************/
CDI_FACE_OLD cdi_read_face_old(CDI_INFO cdi_info) /* the info structure */
{
  CDI_FACE_OLD face = EXA_CALLOC_STRUCT(CDI_FACE_OLD); /* the face structure */
  ASSERT_VALID_CDI_INFO(cdi_info);

  face->cccc = CDI_CHUNK_TYPE_FACE;

  if(cdi_get_major_version(cdi_info) < 1) {
    face->name = NULL;
    face->index = -1;
    face->color = NULL;
  } else {
    cdi_read_cdistring(cdi_info, &(face->name));
    cdi_read_asINT32(cdi_info, &face->index, 1);
    cdi_read_cdistring(cdi_info, &(face->color));
  }

  cdi_read_asINT32(cdi_info, &face->n_facet, 1);

  if(face->n_facet>0) {
    face->facet = EXA_CALLOC_ARRAY(asINT32, face->n_facet);
    cdi_read_asINT32(cdi_info, face->facet, face->n_facet);
  } else {
    face->facet = NULL;
  }

  cdi_read_asINT32(cdi_info, &face->prop, 1);

  return(face);
}

/****************************************************************\
|
| Function name:cdi_destroy_face
|
| Purpose:destroys a face 
|
\****************************************************************/
VOID
cdi_destroy_face_old(
                 CDI_FACE_OLD face        /* the face to destroy */
                 )
{
  exa_free((char *) face->name);
  exa_free((char *) face->color);
  exa_free((char *) face->facet);
  exa_free((char *) face);
}

/****************************************************************\
|
| Function name:cdi_write_null
|
| Purpose:writes null
|
\****************************************************************/
VOID
cdi_write_null(
               CDI_INFO cdi_info, /* the info structure */
               CDI_NULL null /* the null structure */
               )
{
  null->cccc = CDI_CHUNK_TYPE_NULL;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_NULL);

  cdi_write_cdichars(cdi_info, null->null_char, null->n_null_char);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_null
|
| Purpose:reads null
|
\****************************************************************/
CDI_NULL
cdi_read_null(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_NULL null = EXA_CALLOC_STRUCT(CDI_NULL);

  null->cccc = CDI_CHUNK_TYPE_NULL;

  null->null_char = cdi_read_cdichars(cdi_info, NULL, &null->n_null_char);

  return(null);
}

/****************************************************************\
|
| Function name:cdi_destroy_null
|
| Purpose:destroys a null
|
\****************************************************************/
VOID
cdi_destroy_null(
                 CDI_NULL null        /* the null to destroy */
                 )
{
  exa_free(null->null_char);
  exa_free(null);
}

/****************************************************************\
|
| Function name:cdi_write_name
|
| Purpose:writes name
|
\****************************************************************/
VOID
cdi_write_name(
               CDI_INFO cdi_info, /* the info structure */
               CDI_NAME name /* the name structure */
               )
{
  name->cccc = CDI_CHUNK_TYPE_NAME;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_NAME);

  cdi_write_cdichars(cdi_info, name->name, name->n_char);

  cdi_pop(cdi_info);
}
/****************************************************************\
|
| Function name:cdi_read_name
|
| Purpose:reads name
|
\****************************************************************/
CDI_NAME
cdi_read_name(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_NAME name = EXA_CALLOC_STRUCT(CDI_NAME);

  name->cccc = CDI_CHUNK_TYPE_NAME;
  name->name = cdi_read_cdichars(cdi_info, NULL, &name->n_char);

  return(name);
}

/****************************************************************\
|
| Function name:cdi_destroy_name
|
| Purpose:destroys a name
|
\****************************************************************/
VOID
cdi_destroy_name(
                 CDI_NAME name        /* the name to destroy */
                 )
{
  exa_free(name->name);
  exa_free(name);
}


/****************************************************************\
|
| Function name:cdi_write_offs
|
| Purpose:writes offs (offset chunk)
|
\****************************************************************/
VOID
cdi_write_offs(
               CDI_INFO cdi_info, /* the info structure */
               CDI_OFFS offs /* the offs structure */
               )
{
  offs->cccc = CDI_CHUNK_TYPE_OFFS;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_OFFS);

  cdi_write_asINT32(cdi_info, &offs->offset, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_offs
|
| Purpose:reads offs (offset chunk)
|
\****************************************************************/
CDI_OFFS
cdi_read_offs(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_OFFS offs = EXA_CALLOC_STRUCT(CDI_OFFS);

  offs->cccc = CDI_CHUNK_TYPE_OFFS;
      
  cdi_read_asINT32(cdi_info, &offs->offset, 1);

  return(offs);
}

/****************************************************************\
|
| Function name:cdi_destroy_offs
|
| Purpose:destroys an offs (offset chunk)
|
\****************************************************************/
VOID
cdi_destroy_offs(
                 CDI_OFFS offs        /* the offs to destroy */
                 )
{
  exa_free(offs);
}

/****************************************************************\
|
| Function name:cdi_write_prgn
|
| Purpose:writes prgn (parametric chunk)
|
\****************************************************************/
VOID
cdi_write_prgn(
               CDI_INFO cdi_info, /* the info structure */
               CDI_PRGN prgn /* the prgn structure */
               )
{
  prgn->cccc = CDI_CHUNK_TYPE_PRGN;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_PRGN);

  cdi_write_asINT32(cdi_info, &prgn->tire_index, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_prgn
|
| Purpose:reads prgn (parametric chunk)
|
\****************************************************************/
CDI_PRGN
cdi_read_prgn(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_PRGN prgn = EXA_CALLOC_STRUCT(CDI_PRGN);

  prgn->cccc = CDI_CHUNK_TYPE_PRGN;

  cdi_read_asINT32(cdi_info, &prgn->tire_index, 1);

  return(prgn);
}

/****************************************************************\
|
| Function name:cdi_destroy_prgn
|
| Purpose:destroys a prgn (parametric chunk)
|
\****************************************************************/
VOID
cdi_destroy_prgn(
                 CDI_PRGN prgn        /* the prgn to destroy */
                 )
{
  exa_free(prgn);
}

/****************************************************************\
|
| Function name:cdi_write_body
|
| Purpose:writes body
|
\****************************************************************/
VOID
cdi_write_body(
               CDI_INFO cdi_info /* the info structure */
               )
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_BODY);
}

/****************************************************************\
|
| Function name:cdi_write_shll
|
| Purpose:writes shll
|
\****************************************************************/
VOID
cdi_write_shll(
               CDI_INFO cdi_info /* the info structure */
               )
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_SHLL);
}

/****************************************************************\
|
| Function name:cdi_write_mesr
|
| Purpose:writes mesr (measurement chunk)
|
\****************************************************************/
VOID
cdi_write_mesr(
               CDI_INFO cdi_info, /* the info structure */
               CDI_MESR mesr /* the mesr structure */
               )
{
  mesr->cccc = CDI_CHUNK_TYPE_MESR;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_MESR);

  // Old mesr chunks can no longer be written. They can be processed by
  // dump/undump, but those tools should directly write/read the subchunks of
  // MESR.
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 8))
    msg_error("cdi_write_mesr() cannot write chunks older than version 3.8\n");

  // Write out whichever chunks have been accumulated
  if (mesr->file_name)    cdi_write_name(cdi_info, mesr->file_name);
  if (mesr->rgn_list)     cdi_write_rgns(cdi_info, mesr->rgn_list);
  if (mesr->probe)        cdi_write_prbe(cdi_info, mesr->probe);
  if (mesr->prtclTrajec)  cdi_write_prtj(cdi_info, mesr->prtclTrajec);
  if (mesr->face_list)    cdi_write_flst(cdi_info, mesr->face_list);
  if (mesr->params)       cdi_write_mprm(cdi_info, mesr->params);
  if (mesr->face_meas)    cdi_write_mfac(cdi_info, mesr->face_meas);
  if (mesr->options)      cdi_write_mstp(cdi_info, mesr->options);
  if (mesr->rp)           cdi_write_mref(cdi_info, mesr->rp);
  if (mesr->force_filter) cdi_write_mflt(cdi_info, mesr->force_filter);
  if (mesr->dev_win)      cdi_write_mdev(cdi_info, mesr->dev_win);
  if (mesr->geom_selection) {
    WITH_CDI_CHUNK(cdi_info, mesr->geom_selection->GetChunkType()) {
      mesr->geom_selection->WriteToCDI(cdi_info);
    }
  }

  cdi_pop(cdi_info);
}


// This method tries to determine the solver version of our PowerFLOW
// distribution. It's important to remember that some clients of CDI are not
// currently shipped as part of PowerFLOW so no such version exists. In that
// case, we report the solver version as -1.
static asINT32 get_solver_version() {
  static bool solver_version_gotten = false;
  static asINT32 solver_version = -1;

  if (!solver_version_gotten) {
    solver_version_gotten = true;
    char* solver_version_str = getenv("EXA_POWERFLOW_SOLVER_VERSION");
    if (solver_version_str != 0)
      sscanf(solver_version_str, "%d", &solver_version);
  }
  return solver_version;
}


//////////////////////////////////////////////////////////
// sCDI_BFPR methods
//////////////////////////////////////////////////////////

BOOLEAN
sCDI_BFPR::HasVariable(cdiINT32 varType) const 
{
  ccDOTIMES(i, n_cvdp) {
    if (cvdp[i]->type == varType) 
      return true; 
  }
  ccDOTIMES(i, n_eqdp) {
    if (eqdp[i]->type == varType) 
      return true; 
  }
  return false;
}

BOOLEAN 
sCDI_BFPR::IsSetToEqVar(cdiINT32 varType) const 
{
  ccDOTIMES(i, n_eqdp) {
    if (eqdp[i]->type == varType) 
      return true; 
  }
  return false;
}    

dFLOAT 
sCDI_BFPR::GetConstValue(cdiINT32 varType) const 
{
  ccDOTIMES(i, n_cvdp) {
    if (cvdp[i]->type == varType) 
      return cvdp[i]->value; 
  }
  return -1;
}

cSTRING 
sCDI_BFPR::GetEqVarName(cdiINT32 varType) const 
{
  ccDOTIMES(i, n_eqdp) {
    if (eqdp[i]->type == varType) 
      return eqdp[i]->var_name; 
  }
  return NULL;
}


//////////////////////////////////////////////////////////
// sCDI_BODF methods
//////////////////////////////////////////////////////////

sCDI_BFPR* 
sCDI_BODF::GetBfpr(cdiINT32 regionIndex) const
{
  ccDOTIMES(i, n_bfdf) {
    ccDOTIMES(j, bfdf[i].rgns->n_region) {
      if (bfdf[i].rgns->region[j] == regionIndex)
        return bfdf[i].bfpr;
    }
  }
  return NULL;
}



/****************************************************************\
|
| Function name:cdi_read_bfpr
|
| Purpose:reads bfpr (body force parameters chunk)
|
\****************************************************************/
CDI_BFPR cdi_read_bfpr(CDI_INFO cdi_info)
{
  CDI_BFPR bfpr = EXA_CALLOC_STRUCT(CDI_BFPR);
  bfpr->cccc   = CDI_CHUNK_TYPE_BFPR;
  bfpr->n_cvdp = 0;
  bfpr->n_eqdp = 0;
  asINT32 n_chunks = cio_get_count(cdi_info->cio_info);
  bfpr->cvdp = EXA_CALLOC_ARRAY(CDI_CVDP, n_chunks);
  bfpr->eqdp = EXA_CALLOC_ARRAY(CDI_EQDP, n_chunks);
  ccDOTIMES(i, n_chunks) {
    CDI_WITH_INNER_CHUNK(cdi_info) {
      if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_CVDP) {
        bfpr->cvdp[bfpr->n_cvdp] = cdi_read_cvdp(cdi_info);
        bfpr->n_cvdp++;
      }
      else if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_EQDP) {
        bfpr->eqdp[bfpr->n_eqdp] = cdi_read_eqdp(cdi_info);
        bfpr->n_eqdp++;
      }
      else if ((cdi_get_type(cdi_info) != CDI_CHUNK_TYPE_RGNS)
          && (cdi_get_type(cdi_info) != CDI_CHUNK_TYPE_CYLS)) {
        msg_internal_error("Unexpected sub-chunk of BFPR chunk in CDI file");
      }
    }
  }
  return bfpr;
}

/****************************************************************\
|
| Function name: cdi_destroy_bfpr
|
| Purpose: destroys bfpr (body force parameters) struct
|
\****************************************************************/
void cdi_destroy_bfpr(CDI_BFPR bfpr)
{
  ccDOTIMES(i, bfpr->n_cvdp) {
    cdi_destroy_cvdp(bfpr->cvdp[i]);
  }
  exa_free(bfpr->cvdp);
  ccDOTIMES(i, bfpr->n_eqdp) {
    cdi_destroy_eqdp(bfpr->eqdp[i]);
  }
  exa_free(bfpr->eqdp);
  exa_free(bfpr);
}

/****************************************************************\
|
| Function name:cdi_read_bodf
|
| Purpose:reads bodf (body force chunk)
|
\****************************************************************/
CDI_BODF cdi_read_bodf (CDI_INFO cdi_info)
{
  CDI_BODF bodf = EXA_CALLOC_STRUCT(CDI_BODF);

  bodf->cccc = CDI_CHUNK_TYPE_BODF;
  // if CDI v4.4 or later
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 4)) {
    bodf->n_bfdf = 0;
    asINT32 n_chunks = cio_get_count(cdi_info->cio_info);
    bodf->bfdf = EXA_CALLOC_ARRAY(sCDI_BFDF, n_chunks);
    ccCDI_DO_INNER_CHUNKS(j, "bodf", cdi_info) {
      if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_BFDF) {
        ccCDI_DO_INNER_CHUNKS(i, "bfdf", cdi_info) {
          if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_RGNS) {
            bodf->bfdf[bodf->n_bfdf].rgns = NULL;
            bodf->bfdf[bodf->n_bfdf].rgns = cdi_read_rgns(cdi_info);
          }
          else
          if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_BFPR) {
            bodf->bfdf[bodf->n_bfdf].bfpr = NULL;
            bodf->bfdf[bodf->n_bfdf].bfpr = cdi_read_bfpr(cdi_info);
          }
          else {
            msg_internal_error("Unexpected sub-chunk of BFDF chunk in CDI file");
          }
        }
        bodf->n_bfdf++;
      }
      else {
        msg_internal_error("Unexpected sub-chunk of BODF chunk in CDI file");
      }
    }
  }
  // CDI v4.3 and older
  else {
    bodf->n_bfdf = 1;
    bodf->bfdf   = EXA_CALLOC_ARRAY(sCDI_BFDF, 1);
    bodf->bfdf[0].rgns = EXA_CALLOC_STRUCT(CDI_RGNS);
    bodf->bfdf[0].rgns->cccc = CDI_CHUNK_TYPE_RGNS;
    bodf->bfdf[0].rgns->n_region = 0;
    // the BODF chunk in CDI v4.3 and below resembles a BFPR chunk in the newer versions
    // hence, we use the same function cdi_read_bfpr to read the subchunks
    bodf->bfdf[0].bfpr = cdi_read_bfpr(cdi_info);
  }

  return bodf;
}

/****************************************************************\
|
| Function name: cdi_destroy_bodf
|
| Purpose: destroys bodf (body force chunk)
|
\****************************************************************/
void cdi_destroy_bodf (CDI_BODF bodf)
{
  ccDOTIMES(i, bodf->n_bfdf) {
    cdi_destroy_rgns(bodf->bfdf[i].rgns);
    cdi_destroy_bfpr(bodf->bfdf[i].bfpr);
  }
  exa_free(bodf->bfdf);
  exa_free(bodf);
}

/****************************************************************\
|
| Function name:cdi_correct_mesr
|
| Purpose: Updates older cdi version options for measurement chunk
|
\****************************************************************/
void cdi_correct_mesr (CDI_MESR mesr,
                       cdiINT32 major_version,
                       cdiINT32 minor_version,
                       cdiBOOLEAN is_fluidProbe,
                       cdiBOOLEAN is_2d,
                       cdiBOOLEAN is_heat_xfer)
{
  // In 4.X isothermal cases, we allow the user to measure HTC (Near Wall Temp),
  // in 5.X isothermal cases, we allow the user to measure HTC (Char Temp). This
  // bit of logic below allows us ensure that 4.X isothermal CDI files will be
  // properly processed in 5.X and vice versa.
  asINT32 solver_version = get_solver_version();

  if (!is_heat_xfer) {
    if (solver_version == 5) {
      if (mesr->options->standard_mask & CDI_MEAS_OPT_HTC_NEAR_WALL_TEMP) {
        mesr->options->standard_mask &= ~((cdiINT32)CDI_MEAS_OPT_HTC_NEAR_WALL_TEMP);
        mesr->options->standard_mask |= CDI_MEAS_OPT_HTC_CHAR_TEMP;
      }
      mesr->options->custom_surface_options.replace_var_if_present(SRI_VARIABLE_HTC_NEAR_WALL_TEMP,
                                                                   SRI_VARIABLE_HTC_CHAR_TEMP);
    }
    else if (solver_version == 4) {
      if (mesr->options->standard_mask & CDI_MEAS_OPT_HTC_CHAR_TEMP) {
        mesr->options->standard_mask &= ~((cdiINT32)CDI_MEAS_OPT_HTC_CHAR_TEMP);
        mesr->options->standard_mask |= CDI_MEAS_OPT_HTC_NEAR_WALL_TEMP;
      }
      mesr->options->custom_surface_options.replace_var_if_present(SRI_VARIABLE_HTC_CHAR_TEMP,
                                                                   SRI_VARIABLE_HTC_NEAR_WALL_TEMP);
    }
  }

  // for older versions of cdi file, set the bit such that the output for a
  // standard measurement windows is in local reference frame and in global 
  // reference frame for a composite measurement window
  if (!CDI_VERSION_AT_LEAST(major_version, minor_version, 3, 17)) {
    BOOLEAN is_composite = cdi_meas_type_composite_meas(mesr->options->meas_type);
    if (!is_composite)
      mesr->options->standard_mask |= CDI_MEAS_OPT_LOCAL_CSYS;
  }

  // If this is not an old CDI file, we're done
  if (CDI_VERSION_AT_LEAST(major_version, minor_version, 3, 8))
    return;

  // Otherwise, we need to convert some of the old semantics to new.
  cdiINT32 options_mask = mesr->options->standard_mask;

  // Establish the MEASTYPE for old CDI files based on the contents
  // of the MESR chunk.
  if (mesr->probe != NULL) {
    mesr->options->meas_type = CDI_MEASTYPE_PROBE;
    if (is_fluidProbe) 
      options_mask &= ~((cdiINT32)CDI_MEAS_OPT_STANDARD_SURFACE);
    else 
      options_mask &= ~((cdiINT32)CDI_MEAS_OPT_STANDARD_FLUID);
  }
  else if (mesr->rgn_list != NULL) {
    if (options_mask & CDI_MEAS_OPT_COMPOSITE_FORCE_OBSOLETE ||
        options_mask & CDI_MEAS_OPT_COMPOSITE_MOMENTS_OBSOLETE)
      mesr->options->meas_type = CDI_MEASTYPE_COMPOSITE_REGION;
    else
      mesr->options->meas_type = CDI_MEASTYPE_REGION;
  }
  else {
    if (options_mask & CDI_MEAS_OPT_COMPOSITE_FORCE_OBSOLETE ||
        options_mask & CDI_MEAS_OPT_COMPOSITE_MOMENTS_OBSOLETE)
      mesr->options->meas_type = CDI_MEASTYPE_COMPOSITE_FACE;
    else if (mesr->face_meas != NULL) {
      mesr->options->meas_type = CDI_MEASTYPE_SAMPLED_FACE;
      if (options_mask & CDI_MEAS_OPT_STANDARD_SURFACE) {
        options_mask &= ~((cdiINT32)CDI_MEAS_OPT_STANDARD_SURFACE);
        options_mask |= CDI_MEAS_OPT_STANDARD_FLUID;
      } else {
        options_mask |= CDI_MEAS_OPT_CUSTOM_FLUID;
      }
    }
    else
      mesr->options->meas_type = CDI_MEASTYPE_FACE;
  }
  
  // If old-style composite, enable appropriate surface measurements
  if (mesr->force_filter != NULL) {
    options_mask |= CDI_MEAS_OPT_CUSTOM_SURFACE;
    mesr->options->custom_surface_options.add_var(SRI_VARIABLE_N_SCREENED_SURFELS);
    mesr->options->custom_surface_options.add_var(SRI_VARIABLE_SCREENED_AREA);
  }
  if (options_mask & CDI_MEAS_OPT_COMPOSITE_FORCE_OBSOLETE) {
    options_mask &= ~((cdiINT32)CDI_MEAS_OPT_COMPOSITE_FORCE_OBSOLETE);
    options_mask |= CDI_MEAS_OPT_CUSTOM_SURFACE;
    mesr->options->custom_surface_options.add_var(SRI_VARIABLE_XFORCE);
    mesr->options->custom_surface_options.add_var(SRI_VARIABLE_YFORCE);
    if (!is_2d)
      mesr->options->custom_surface_options.add_var(SRI_VARIABLE_ZFORCE);
  }
  if (options_mask & CDI_MEAS_OPT_COMPOSITE_MOMENTS_OBSOLETE) {
    options_mask &= ~((cdiINT32)CDI_MEAS_OPT_COMPOSITE_MOMENTS_OBSOLETE);
    options_mask |= CDI_MEAS_OPT_CUSTOM_SURFACE;
    // It seems counter-intuitive to turn off X and Y in 2D, but torque only
    // occurs in the Z direction in 2D.
    mesr->options->custom_surface_options.add_var(SRI_VARIABLE_XFORCE);
    mesr->options->custom_surface_options.add_var(SRI_VARIABLE_YFORCE);
    if (!is_2d) {
      mesr->options->custom_surface_options.add_var(SRI_VARIABLE_XTORQUE);
      mesr->options->custom_surface_options.add_var(SRI_VARIABLE_YTORQUE);
      mesr->options->custom_surface_options.add_var(SRI_VARIABLE_ZFORCE);
    }
    mesr->options->custom_surface_options.add_var(SRI_VARIABLE_ZTORQUE);
  }
  
  // Convert old pressure & vel_mag options to the new custom option
  // flavor.
  if (options_mask & CDI_MEAS_OPT_FLUID_PRESSURE_OBSOLETE) { 
    options_mask &= ~((cdiINT32)CDI_MEAS_OPT_FLUID_PRESSURE_OBSOLETE);
    options_mask |= CDI_MEAS_OPT_CUSTOM_FLUID;
    mesr->options->custom_fluid_options.add_var(SRI_VARIABLE_PRESSURE);
  }
  if (options_mask & CDI_MEAS_OPT_FLUID_VEL_MAG_OBSOLETE) { 
    options_mask &= ~((cdiINT32)CDI_MEAS_OPT_FLUID_VEL_MAG_OBSOLETE);
    options_mask |= CDI_MEAS_OPT_CUSTOM_FLUID;
    mesr->options->custom_fluid_options.add_var(SRI_VARIABLE_VEL_MAG);
  }
  if (options_mask & CDI_MEAS_OPT_SURFACE_PRESSURE_OBSOLETE) { 
    options_mask &= ~((cdiINT32)CDI_MEAS_OPT_SURFACE_PRESSURE_OBSOLETE);
    options_mask |= CDI_MEAS_OPT_CUSTOM_SURFACE;
    mesr->options->custom_surface_options.add_var(SRI_VARIABLE_PRESSURE);
  }
  if (options_mask & CDI_MEAS_OPT_SURFACE_VEL_MAG_OBSOLETE) { 
    options_mask &= ~((cdiINT32)CDI_MEAS_OPT_SURFACE_VEL_MAG_OBSOLETE);
    options_mask |= CDI_MEAS_OPT_CUSTOM_SURFACE;
    mesr->options->custom_surface_options.add_var(SRI_VARIABLE_VEL_MAG);
  }
  //  
  mesr->options->standard_mask = options_mask;
}

/****************************************************************\
|
| Function name:cdi_read_mesr
|
| Purpose:reads mesr (measurement chunk)
|
\****************************************************************/
CDI_MESR cdi_get_mesr (CDI_INFO cdi_info,
                       cdiBOOLEAN is_2d,
                       cdiBOOLEAN is_heat_xfer,
                       cdiBOOLEAN *p_is_fluidProbe)
{
  cdiBOOLEAN is_fluidProbe = FALSE;

  CDI_MESR mesr = EXA_CALLOC_STRUCT(CDI_MESR);
  mesr->cccc = CDI_CHUNK_TYPE_MESR;

  ccCDI_DO_INNER_CHUNKS(i, "mesr", cdi_info) {
    CIO_CCCC chunkType = cdi_get_type(cdi_info);
    if (chunkType == CDI_CHUNK_TYPE_RGNS) {
      //mesr->rgn_list = cdi_read_rgns(cdi_info);
      CDI_RGNS rgns = cdi_read_rgns(cdi_info);
      mesr->rgn_list = rgns;
      // This is an old case. Eventually get rid of rgn_list data member
      mesr->geom_selection = new cCDI_GEOM_SELECTION_TREE(0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
      mesr->geom_selection->m_selections.rgn_list.assign(rgns->region, rgns->region + rgns->n_region);
    }
    else if (chunkType == CDI_CHUNK_TYPE_FLST) {
      //mesr->face_list = cdi_read_flst(cdi_info);
      CDI_FLST flst = cdi_read_flst(cdi_info);
      mesr->face_list = flst;
      // This is an old case. Eventually get rid of rgn_list data member
      mesr->geom_selection = new cCDI_GEOM_SELECTION_TREE(0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
      mesr->geom_selection->m_selections.face_list.assign(flst->face, flst->face + flst->n_face);
    }
    else if (chunkType == CDI_CHUNK_TYPE_PRBE)
      mesr->probe = cdi_read_prbe(cdi_info);
    else if (chunkType == CDI_CHUNK_TYPE_PRTJ)
      mesr->prtclTrajec = cdi_read_prtj(cdi_info);
    else if (chunkType == CDI_CHUNK_TYPE_MPRM)
      mesr->params = cdi_read_mprm(cdi_info);
    else if (chunkType == CDI_CHUNK_TYPE_MSTP)
      mesr->options = cdi_read_mstp(cdi_info);
    else if (chunkType == CDI_CHUNK_TYPE_MFLT)
      mesr->force_filter = cdi_read_mflt(cdi_info);
    else if (chunkType == CDI_CHUNK_TYPE_MREF)
      mesr->rp = cdi_read_mref(cdi_info);
    else if (chunkType == CDI_CHUNK_TYPE_MFAC)
      mesr->face_meas = cdi_read_mfac(cdi_info);
    else if (chunkType == CDI_CHUNK_TYPE_NAME)
      mesr->file_name = cdi_read_name(cdi_info);
    else if (chunkType == CDI_CHUNK_TYPE_MDEV)
      mesr->dev_win = cdi_read_mdev(cdi_info);
    else if (chunkType == CDI_CHUNK_TYPE_MENT) {
      CDI_MENT ment = cdi_read_ment(cdi_info);
      is_fluidProbe = (ment->options == CDI_MENT_FLUID);
      cdi_destroy_ment(ment);
    }
    else if (chunkType == CDI_CHUNK_TYPE_MSTT) {
      ccCDI_DO_INNER_CHUNKS(j, "mstt", cdi_info) {
        CIO_CCCC chunkType = cdi_get_type(cdi_info);
        if (chunkType == CDI_CHUNK_TYPE_MPRM)
          mesr->params = cdi_read_mprm(cdi_info);
        else if (chunkType == CDI_CHUNK_TYPE_MSTP)
          mesr->options = cdi_read_mstp(cdi_info);
        else if (chunkType == CDI_CHUNK_TYPE_MFLT)
          mesr->force_filter = cdi_read_mflt(cdi_info);
        else if (chunkType == CDI_CHUNK_TYPE_MREF)
          mesr->rp = cdi_read_mref(cdi_info);
        else if (chunkType == CDI_CHUNK_TYPE_MFAC)
          mesr->face_meas = cdi_read_mfac(cdi_info);
        else if (chunkType == CDI_CHUNK_TYPE_NAME)
          mesr->file_name = cdi_read_name(cdi_info);
      }
    }
    else if (chunkType == cCDI_GEOM_SELECTION_TREE::GetGeometrySelectionChunkType())
      mesr->geom_selection = cdi_read_geos(cdi_info);
  }

  if (p_is_fluidProbe)
    *p_is_fluidProbe = is_fluidProbe;

  return(mesr);
}

/****************************************************************\
|
| Function name:cdi_read_mesr
|
| Purpose:reads mesr (measurement chunk)
|
\****************************************************************/
CDI_MESR cdi_read_mesr (CDI_INFO cdi_info,
                        cdiBOOLEAN is_2d,
                        cdiBOOLEAN is_heat_xfer)
{
  cdiBOOLEAN is_fluidProbe = FALSE;

  CDI_MESR mesr = cdi_get_mesr(cdi_info, is_2d, is_heat_xfer, &is_fluidProbe);

  // version-dependent update of older cdi files
  cdi_correct_mesr(mesr, cdi_info->major_version, cdi_info->minor_version,
                   is_fluidProbe, is_2d, is_heat_xfer);

  return mesr;
}

/****************************************************************\
|
| Function name:cdi_destroy_mesr
|
| Purpose:destroys a mesr 
|
\****************************************************************/
VOID 
cdi_destroy_mesr(CDI_MESR mesr)
{
  // Free the subchunks. Not all of these will be established, but that's OK,
  // because exa_free does nothing to NULL pointers.
  exa_free(mesr->file_name);
  exa_free(mesr->rgn_list);
  exa_free(mesr->probe);
  delete mesr->prtclTrajec;
  exa_free(mesr->face_list);
  delete mesr->params;
  delete mesr->options;
  exa_free(mesr->force_filter);
  exa_free(mesr->rp);
  exa_free(mesr->face_meas);
  if (mesr->geom_selection) delete mesr->geom_selection;

  // Free the mesr chunk itself
  exa_free(mesr);
}

/****************************************************************\
|
| Function name:cdi_write_mpsg
|
| Purpose:write a mpsg
|
\****************************************************************/
void
cdi_write_mpsg(
               CDI_INFO cdi_info,
               CDI_MPSG mpsg
               )
{
  mpsg->cccc = CDI_CHUNK_TYPE_MPSG;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_MPSG);

  cdi_write_cdistring(cdi_info, mpsg->name.c_str());

  cdi_pop(cdi_info);
}


void 
cdi_read_mpsg(
              CDI_INFO cdi_info,
              CDI_MPSG mpsg
              )
{
  mpsg->cccc = CDI_CHUNK_TYPE_MPSG;

  cdi_read_stdstring(cdi_info, mpsg->name);
}

/****************************************************************\
|
| Function name:cdi_write_mprm
|
| Purpose:writes mprm (measurement parameters)
|
\****************************************************************/
VOID
cdi_write_mprm(
               CDI_INFO cdi_info, /* the info structure */
               CDI_MPRM mprm /* the mprm structure */
               )
{
  mprm->cccc = CDI_CHUNK_TYPE_MPRM;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_MPRM);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 12)) {
    asINT32 start_via = (asINT32)mprm->start_via;
    cdi_write_asINT32(cdi_info, &start_via, 1);
    asINT32 monitorSize = mprm->monitors.size();
    cdi_write_asINT32(cdi_info, &monitorSize, 1);
    ccDOTIMES(i, mprm->monitors.size())
      cdi_write_asINT32(cdi_info, &mprm->monitors[i], 1);
  }
  cdi_write_asINT32(cdi_info, &mprm->start_time, 1);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 12)) {
    asINT32 end_via = (asINT32)mprm->end_via;
    cdi_write_asINT32(cdi_info, &end_via, 1);
    cdi_write_asINT32(cdi_info, &mprm->duration, 1);
  }
  cdi_write_asINT32(cdi_info, &mprm->end_time, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 1))
    cdi_write_asINT32(cdi_info, &mprm->num_frames, 1);
  cdi_write_asINT32(cdi_info, &mprm->period, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 22))
    cdi_write_asINT32(cdi_info, &mprm->period_sync_group_index, 1);
  cdi_write_asINT32(cdi_info, &mprm->average_interval, 1);
  cdi_write_asINT32(cdi_info, &mprm->spacing, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 1)) {
    asINT32 time_solver = (asINT32)mprm->time_rel_solver;
    cdi_write_asINT32(cdi_info, &time_solver, 1);
  }

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_mprm
|
| Purpose:reads mprm
|
\****************************************************************/
CDI_MPRM
cdi_read_mprm(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_MPRM mprm = new sCDI_MPRM; /* the mprm structure */

  mprm->cccc = CDI_CHUNK_TYPE_MPRM;
  
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 12)) {
    cdiINT32 start_via;
    cdi_read_asINT32(cdi_info, &start_via, 1);
    mprm->start_via = static_cast<eCDI_MEAS_START_TIME_VIA::Enum>(start_via);
    cdiINT32 numMonitors = 0;
    cdiINT32 monitorIndex = -1;
    cdi_read_asINT32(cdi_info, &numMonitors, 1);
    ccDOTIMES(i, numMonitors) {
      cdi_read_asINT32(cdi_info, &monitorIndex, 1);
      mprm->monitors.push_back(monitorIndex);
    }
  } else {
    mprm->start_via = eCDI_MEAS_START_TIME_VIA::StartTime;
  }
  cdi_read_asINT32(cdi_info, &mprm->start_time, 1);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 12)) {
    cdiINT32 end_via;
    cdi_read_asINT32(cdi_info, &end_via, 1);
    mprm->end_via = static_cast<eCDI_MEAS_END_TIME_VIA::Enum>(end_via);
    cdi_read_asINT32(cdi_info, &mprm->duration, 1);
  } else {
    mprm->end_via = eCDI_MEAS_END_TIME_VIA::EndTime;
  }

  cdi_read_asINT32(cdi_info, &mprm->end_time, 1);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 1))
    cdi_read_asINT32(cdi_info, &mprm->num_frames, 1);
  else
    mprm->num_frames = -1;

  cdi_read_asINT32(cdi_info, &mprm->period, 1);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 22))
    cdi_read_asINT32(cdi_info, &mprm->period_sync_group_index, 1);
  else
    mprm->period_sync_group_index = -1;

  cdi_read_asINT32(cdi_info, &mprm->average_interval, 1);
  cdi_read_asINT32(cdi_info, &mprm->spacing, 1);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 1)) {
    cdiINT32 time_solver;
    cdi_read_asINT32(cdi_info, &time_solver, 1);
    mprm->time_rel_solver = static_cast<eCDI_COUPLED_SOLVER::Enum>(time_solver);
  }

  return(mprm);
}

/****************************************************************\
|
| Function name:cdi_destroy_mprm
|
| Purpose:destroys a mprm 
|
\****************************************************************/
VOID
cdi_destroy_mprm(
                 CDI_MPRM mprm        /* the mprm to destroy */
                 )
{
  delete mprm;
}

/****************************************************************\
|
| Function name:cdi_write_mflt
|
| Purpose:writes mflt (measurement parameters)
|
\****************************************************************/
VOID
cdi_write_mflt(
               CDI_INFO cdi_info, /* the info structure */
               CDI_MFLT mflt /* the mflt structure */
               )
{
  mflt->cccc = CDI_CHUNK_TYPE_MFLT;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_MFLT);

  cdi_write_idFLOAT(cdi_info, &mflt->min_pressure, 1);
  cdi_write_idFLOAT(cdi_info, &mflt->max_pressure, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_mflt
|
| Purpose:reads mflt
|
\****************************************************************/
CDI_MFLT
cdi_read_mflt(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_MFLT mflt = EXA_CALLOC_STRUCT(CDI_MFLT); /* the mflt structure */

  mflt->cccc = CDI_CHUNK_TYPE_MFLT;
  
  cdi_read_idFLOAT(cdi_info, &mflt->min_pressure, 1);
  cdi_read_idFLOAT(cdi_info, &mflt->max_pressure, 1);

  return(mflt);
}

/****************************************************************\
|
| Function name:cdi_destroy_mflt
|
| Purpose:destroys a mflt 
|
\****************************************************************/
VOID
cdi_destroy_mflt(
                 CDI_MFLT mflt        /* the mflt to destroy */
                 )
{
  exa_free(mflt);
}

/****************************************************************\
|
| Function name:cdi_write_mfac
|
| Purpose:writes mfac (meas face options)
|
\****************************************************************/
VOID
cdi_write_mfac(
               CDI_INFO cdi_info, /* the info structure */
               CDI_MFAC mfac /* the mfac structure */
               )
{
  mfac->cccc = CDI_CHUNK_TYPE_MFAC;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_MFAC);

  cdi_write_asINT32(cdi_info, (asINT32 *)&mfac->grouping, 1);
  cdi_write_asINT32(cdi_info, &mfac->merge_to_input, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_mfac
|
| Purpose:reads mfac
|
\****************************************************************/
CDI_MFAC
cdi_read_mfac(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_MFAC mfac = EXA_CALLOC_STRUCT(CDI_MFAC); /* the mfac structure */

  mfac->cccc = CDI_CHUNK_TYPE_MFAC;
  
  cdi_read_asINT32(cdi_info, (asINT32 *)&mfac->grouping, 1);
  cdi_read_asINT32(cdi_info, &mfac->merge_to_input, 1);

  return(mfac);
}

/****************************************************************\
|
| Function name:cdi_destroy_mfac
|
| Purpose:destroys a mfac 
|
\****************************************************************/
VOID
cdi_destroy_mfac(
                 CDI_MFAC mfac        /* the mfac to destroy */
                 )
{
  exa_free(mfac);
}

/****************************************************************\
|
| Function name:cdi_write_mref
|
| Purpose:writes mref (measurement parameters)
|
\****************************************************************/
VOID
cdi_write_mref(
               CDI_INFO cdi_info, /* the info structure */
               CDI_MREF mref /* the mref structure */
               )
{
  mref->cccc = CDI_CHUNK_TYPE_MREF;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_MREF);

  cdi_write_idFLOAT(cdi_info, &mref->reference_point[0], 1);
  cdi_write_idFLOAT(cdi_info, &mref->reference_point[1], 1);
  cdi_write_idFLOAT(cdi_info, &mref->reference_point[2], 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_mref
|
| Purpose:reads mref
|
\****************************************************************/
CDI_MREF
cdi_read_mref(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_MREF mref = EXA_CALLOC_STRUCT(CDI_MREF); /* the mref structure */

  mref->cccc = CDI_CHUNK_TYPE_MREF;
  
  cdi_read_idFLOAT(cdi_info, &mref->reference_point[0], 1);
  cdi_read_idFLOAT(cdi_info, &mref->reference_point[1], 1);
  cdi_read_idFLOAT(cdi_info, &mref->reference_point[2], 1);

  return(mref);
}

/****************************************************************\
|
| Function name:cdi_destroy_mref
|
| Purpose:destroys a mref 
|
\****************************************************************/
VOID
cdi_destroy_mref(
                 CDI_MREF mref        /* the mref to destroy */
                 )
{
  exa_free(mref);
}

void
cCDI_BASE_VAR_OPTIONS::SetOption(const sCDI_VAR_OPTION& opt)
{ 
  std::set<sCDI_VAR_OPTION> allOptions = GetAllOptions();
  if (allOptions.find(opt) != allOptions.end()) {
    m_options.insert(opt);
  }
}

bool
cCDI_BASE_VAR_OPTIONS::GetVarOptionByName(const char* varName, sCDI_VAR_OPTION& opt) const
{
  std::set<sCDI_VAR_OPTION> allOptions = GetAllOptions();
  auto foundOpt = std::find_if(allOptions.begin(), allOptions.end(),
    [varName](const sCDI_VAR_OPTION& varOpt) { return strcmp(varName, varOpt.name) == 0; });
  if (foundOpt != allOptions.end()) {
    opt = *foundOpt;
  }
  return foundOpt != allOptions.end();
}

bool
cCDI_BASE_VAR_OPTIONS::WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_write_one_asINT32(cdi_info, m_options.size());
  for (sCDI_VAR_OPTION opt : m_options) {
    cdi_write_cdichars(cdi_info, opt.name, strlen(opt.name));
  }
  return true;
}

bool
cCDI_BASE_VAR_OPTIONS::ReadFromCDI(CDI_INFO cdi_info)
{
  asINT32 num_options = cdi_read_one_asINT32(cdi_info);
  if (num_options < 0)
    return false;

  sCDI_VAR_OPTION readOption;
  for (size_t i = 0; i < num_options; ++i) {
    int unusedInt;
    STRING optName = cdi_read_cdichars(cdi_info, NULL, &unusedInt);
    if (GetVarOptionByName(optName, readOption))
      SetOption(readOption);
  }
  return true;
}

/****************************************************************\
|
| Function name:cdi_write_mstp
|
| Purpose:writes mstp (measurement options)
|
\****************************************************************/
VOID
cdi_write_mstp(
               CDI_INFO cdi_info, /* the info structure */
               CDI_MSTP mstp /* the mstp structure */
               )
{
  mstp->cccc = CDI_CHUNK_TYPE_MSTP;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_MSTP);

  cdi_write_one_asINT32(cdi_info, mstp->standard_mask);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 8)) {
    // Meas type
    cdi_write_one_asINT32(cdi_info, (asINT32)mstp->meas_type);
    
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 1) || 
        (cdi_info->major_version == 3 && cdi_info->minor_version == 24)) {
      
      cdi_write_one_auINT32(cdi_info,mstp->custom_fluid_options.num_vars());
      cdi_write_one_auINT32(cdi_info,mstp->custom_fluid_particle_options.num_vars());
      cdi_write_one_auINT32(cdi_info,mstp->custom_surface_options.num_vars());
      cdi_write_one_auINT32(cdi_info,mstp->custom_surface_particle_options.num_vars());
      cdi_write_one_auINT32(cdi_info,mstp->custom_porous_options.num_vars());
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 0)) {
        cdi_write_one_auINT32(cdi_info, mstp->custom_solid_volume_options.num_vars());
        cdi_write_one_auINT32(cdi_info, mstp->custom_shell_options.num_vars());
      }
      
      mstp->custom_fluid_options.sort_vars();
      ccDOTIMES(ivar,mstp->custom_fluid_options.num_vars()) 
        cdi_write_one_auINT32(cdi_info,mstp->custom_fluid_options.var(ivar));

      mstp->custom_fluid_particle_options.sort_vars();
      ccDOTIMES(ivar,mstp->custom_fluid_particle_options.num_vars()) 
        cdi_write_one_auINT32(cdi_info,mstp->custom_fluid_particle_options.var(ivar));

      mstp->custom_surface_options.sort_vars();
      ccDOTIMES(ivar,mstp->custom_surface_options.num_vars()) 
        cdi_write_one_auINT32(cdi_info,mstp->custom_surface_options.var(ivar));
     
      mstp->custom_surface_particle_options.sort_vars();
      ccDOTIMES(ivar,mstp->custom_surface_particle_options.num_vars()) 
        cdi_write_one_auINT32(cdi_info,mstp->custom_surface_particle_options.var(ivar));
      
      mstp->custom_porous_options.sort_vars();
      ccDOTIMES(ivar,mstp->custom_porous_options.num_vars()) 
        cdi_write_one_auINT32(cdi_info,mstp->custom_porous_options.var(ivar));

      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 0)) {
        mstp->custom_solid_volume_options.sort_vars();
        ccDOTIMES(ivar, mstp->custom_solid_volume_options.num_vars())
          cdi_write_one_auINT32(cdi_info, mstp->custom_solid_volume_options.var(ivar));

        mstp->custom_shell_options.sort_vars();
        ccDOTIMES(ivar, mstp->custom_shell_options.num_vars())
          cdi_write_one_auINT32(cdi_info, mstp->custom_shell_options.var(ivar));
      }
      
    } else {
      // Custom meas variable masks...
      int numBitmasks = cdi_meas_vars_num_bitmasks(cdi_info);

      mstp->custom_fluid_options.convert_vars_to_bit_mask();
      ccDOTIMES(i, numBitmasks)
        cdi_write_one_auINT32(cdi_info, mstp->custom_fluid_options.m_bitmasksObsolete[i]);

      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 21)) {
        mstp->custom_fluid_particle_options.convert_vars_to_bit_mask();
        ccDOTIMES(i, numBitmasks)
          cdi_write_one_auINT32(cdi_info, mstp->custom_fluid_particle_options.m_bitmasksObsolete[i]);
      }
      mstp->custom_surface_options.convert_vars_to_bit_mask();
      ccDOTIMES(i, numBitmasks)
        cdi_write_one_auINT32(cdi_info, mstp->custom_surface_options.m_bitmasksObsolete[i]);
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 21)) {
        mstp->custom_surface_particle_options.convert_vars_to_bit_mask();
        ccDOTIMES(i, numBitmasks)
          cdi_write_one_auINT32(cdi_info, mstp->custom_surface_particle_options.m_bitmasksObsolete[i]);
      }
      mstp->custom_porous_options.convert_vars_to_bit_mask();
      ccDOTIMES(i, numBitmasks)
        cdi_write_one_auINT32(cdi_info, mstp->custom_porous_options.m_bitmasksObsolete[i]);
    }
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
    mstp->extended_std_var_options.WriteToCDI(cdi_info);
  }

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_mstp
|
| Purpose:reads mstp
|
\****************************************************************/
CDI_MSTP
cdi_read_mstp(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_MSTP mstp = new sCDI_MSTP; 

  mstp->cccc = CDI_CHUNK_TYPE_MSTP;
  
  mstp->standard_mask = cdi_read_one_asINT32(cdi_info);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 8)) {
    // Meas type
    mstp->meas_type = (CDI_MEASTYPE)cdi_read_one_asINT32(cdi_info);

     if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 1) ||
         (cdi_info->major_version == 3 && cdi_info->minor_version == 24)) {
     
       cdiINT32 n_custom_fluid_vars =  cdi_read_one_auINT32(cdi_info);
       cdiINT32 n_custom_fluid_particle_vars =  cdi_read_one_auINT32(cdi_info);
       cdiINT32 n_custom_surface_vars =  cdi_read_one_auINT32(cdi_info);
       cdiINT32 n_custom_surface_particle_vars =  cdi_read_one_auINT32(cdi_info);
       cdiINT32 n_custom_porous_vars =  cdi_read_one_auINT32(cdi_info);
       cdiINT32 n_custom_volume_vars = 0;
       cdiINT32 n_custom_shell_vars = 0;
       if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 0)) {
         n_custom_volume_vars = cdi_read_one_auINT32(cdi_info);
         n_custom_shell_vars = cdi_read_one_auINT32(cdi_info);
       }

       ccDOTIMES(ivar,n_custom_fluid_vars) {
         sriINT var = (sriINT) cdi_read_one_auINT32(cdi_info);
         mstp->custom_fluid_options.add_var(var);
       }
       ccDOTIMES(ivar,n_custom_fluid_particle_vars) {
         sriINT var = (sriINT) cdi_read_one_auINT32(cdi_info);
         mstp->custom_fluid_particle_options.add_var(var);
       }
       ccDOTIMES(ivar,n_custom_surface_vars) {
         sriINT var = (sriINT) cdi_read_one_auINT32(cdi_info);
         mstp->custom_surface_options.add_var(var);
       }
       ccDOTIMES(ivar,n_custom_surface_particle_vars) {
         sriINT var = (sriINT) cdi_read_one_auINT32(cdi_info);
         mstp->custom_surface_particle_options.add_var(var);
       }
       ccDOTIMES(ivar,n_custom_porous_vars) {
         sriINT var = (sriINT) cdi_read_one_auINT32(cdi_info);
         mstp->custom_porous_options.add_var(var);
       }

       if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 0)) {
         ccDOTIMES(ivar, n_custom_volume_vars) {
           sriINT var = (sriINT)cdi_read_one_auINT32(cdi_info);
             mstp->custom_solid_volume_options.add_var(var);
         }

         ccDOTIMES(ivar, n_custom_shell_vars) {
           sriINT var = (sriINT)cdi_read_one_auINT32(cdi_info);
             mstp->custom_shell_options.add_var(var);
         }
       }
       
     } else {

    // Custom meas variable masks...

    // number of 32-bit masks needed to capture the set of available custom
    // measurement variables. May change in the future if variables are added.
    int numBitmasks = cdi_meas_vars_num_bitmasks(cdi_info);

    ccDOTIMES(i, numBitmasks)
      mstp->custom_fluid_options.m_bitmasksObsolete[i] = cdi_read_one_auINT32(cdi_info);
    mstp->custom_fluid_options.convert_bit_mask_to_vars();

    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 21)) {
      ccDOTIMES(i, numBitmasks)
        mstp->custom_fluid_particle_options.m_bitmasksObsolete[i] = cdi_read_one_auINT32(cdi_info);
      mstp->custom_fluid_particle_options.convert_bit_mask_to_vars();
    }
    ccDOTIMES(i, numBitmasks)
      mstp->custom_surface_options.m_bitmasksObsolete[i] = cdi_read_one_auINT32(cdi_info);
    mstp->custom_surface_options.convert_bit_mask_to_vars();
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 21)) {
      ccDOTIMES(i, numBitmasks)
        mstp->custom_surface_particle_options.m_bitmasksObsolete[i] = cdi_read_one_auINT32(cdi_info);
      mstp->custom_surface_particle_options.convert_bit_mask_to_vars();
    }
    ccDOTIMES(i, numBitmasks)
      mstp->custom_porous_options.m_bitmasksObsolete[i] = cdi_read_one_auINT32(cdi_info);
    mstp->custom_porous_options.convert_bit_mask_to_vars();
     }
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
    mstp->extended_std_var_options.ReadFromCDI(cdi_info);
  }

  return(mstp);
}

/****************************************************************\
|
| Function name:cdi_destroy_mstp
|
| Purpose:destroys a mstp 
|
\****************************************************************/
VOID
cdi_destroy_mstp(
                 CDI_MSTP mstp        /* the mstp to destroy */
                 )
{
  delete mstp;
}

/****************************************************************\
|
| Function name:cdi_write_ptyp
|
| Purpose:writes ptyp (physics type)
|
\****************************************************************/
VOID
cdi_write_ptyp(
               CDI_INFO cdi_info, /* the info structure */
               CDI_PTYP ptyp /* the ptyp structure */
               )
{
  ptyp->cccc = CDI_CHUNK_TYPE_PTYP;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_PTYP);

  cdi_write_asINT32(cdi_info, &ptyp->type, 1);
  cdi_write_asINT32(cdi_info, &ptyp->n_integer, 1);
  cdi_write_asINT32(cdi_info, &ptyp->n_continuous, 1);
  cdi_write_asINT32(cdi_info, &ptyp->n_initial, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_ptyp
|
| Purpose:reads ptyp
|
\****************************************************************/
CDI_PTYP
cdi_read_ptyp(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_PTYP ptyp = EXA_CALLOC_STRUCT(CDI_PTYP); /* the ptyp structure */

  ptyp->cccc = CDI_CHUNK_TYPE_PTYP;
  
  cdi_read_asINT32(cdi_info, &ptyp->type, 1);
  cdi_read_asINT32(cdi_info, &ptyp->n_integer, 1);
  cdi_read_asINT32(cdi_info, &ptyp->n_continuous, 1);
  cdi_read_asINT32(cdi_info, &ptyp->n_initial, 1);

  return(ptyp);
}

/****************************************************************\
|
| Function name:cdi_destroy_ptyp
|
| Purpose:destroys a ptyp 
|
\****************************************************************/
VOID
cdi_destroy_ptyp(
                 CDI_PTYP ptyp        /* the ptyp to destroy */
                 )
{
  exa_free(ptyp);
}


//////////////////////////////////////////////////////////
// sCDI_UDST methods
//////////////////////////////////////////////////////////

BOOLEAN sCDI_UDST::HasVariable(cdiINT32 varType) const
{
  ccDOTIMES(i, n_cvdp) {
    if (cvdp[i]->type == varType)
      return true;
  }
  ccDOTIMES(i, n_eqdp) {
    if (eqdp[i]->type == varType)
      return true;
  }
  return false;
}

BOOLEAN sCDI_UDST::IsSetToEqVar(cdiINT32 varType) const
{
  ccDOTIMES(i, n_eqdp) {
    if (eqdp[i]->type == varType)
      return true;
  }
  return false;
}

dFLOAT sCDI_UDST::GetConstValue(cdiINT32 varType) const
{
  ccDOTIMES(i, n_cvdp) {
    if (cvdp[i]->type == varType)
      return cvdp[i]->value;
  }
  return -1;
}

cSTRING sCDI_UDST::GetEqVarName(cdiINT32 varType) const
{
  ccDOTIMES(i, n_eqdp) {
    if (eqdp[i]->type == varType)
      return eqdp[i]->var_name;
  }
  return NULL;
}

/****************************************************************\
|
| Function name:cdi_read_udst
|
| Purpose:reads udst (user-defined scalar transport chunk)
|
\****************************************************************/
sCDI_UDST cdi_read_udst(CDI_INFO cdi_info)
{
  sCDI_UDST udst;
  udst.cccc = CDI_CHUNK_TYPE_UDST;
  udst.n_cvdp = 0;
  udst.n_eqdp = 0;
  if (!cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info)) {
    return udst;
  }

  bool found = false;
  cdiINT32 index = 0;

  ccCDI_DO_INNER_CHUNK(index, "udst", cdi_info) {
    if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_UDST) {
      found = true;
      asINT32 count = cio_get_count(cdi_info->cio_info);
      udst.cvdp = EXA_CALLOC_ARRAY(CDI_CVDP, count);
      udst.eqdp = EXA_CALLOC_ARRAY(CDI_EQDP, count);
      ccCDI_DO_INNER_CHUNKS(j, "udst", cdi_info) {
        if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_CVDP) {
          udst.cvdp[udst.n_cvdp] = cdi_read_cvdp(cdi_info);
          udst.n_cvdp++;
        }
        else if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_EQDP) {
          udst.eqdp[udst.n_eqdp] = cdi_read_eqdp(cdi_info);
          udst.n_eqdp++;
        }
        else {
          msg_internal_error("Unexpected sub-chunk of UDST chunk in CDI file");
        }
      }
    }
  }

  if(!found)
    cio_rewind(cdi_info->cio_info);

  return udst;
}

/****************************************************************\
|
| Function name: cdi_destroy_udst
|
| Purpose: destroys udst (user-defined scalar transport) struct
|
\****************************************************************/
void cdi_destroy_udst(CDI_UDST udst)
{
  if (udst->n_cvdp > 0) {
    ccDOTIMES(i, udst->n_cvdp) {
      cdi_destroy_cvdp(udst->cvdp[i]);
    }
    exa_free(udst->cvdp);
  }

  if (udst->n_eqdp > 0) {
    ccDOTIMES(i, udst->n_eqdp) {
      cdi_destroy_eqdp(udst->eqdp[i]);
    }
    exa_free(udst->eqdp);
  }
  //exa_free(udst);
}

/****************************************************************\
|
| Function name:cdi_write_hxch
|
| Purpose:writes hxch (heat exchanger)
|
\****************************************************************/
static VOID
write_hxch_base(
                CDI_INFO cdi_info, /* the info structure */
                CDI_HXCH_BASE hxch /* the hxch structure */
                )
{
  cdi_write_cdistring(cdi_info, hxch->name ? hxch->name : "");
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 7))
    cdi_write_cdistring(cdi_info, hxch->part_name);
  cdi_write_asINT32(cdi_info, &hxch->type, 1);
  cdi_write_asINT32(cdi_info, &hxch->tool, 1);
  cdi_write_asINT32(cdi_info, &hxch->flags, 1);
  cdi_write_asINT32(cdi_info, &hxch->table_csys_index, 1);
  cdi_write_asINT32(cdi_info, &hxch->medium_csys_index, 1);
  cdi_write_asINT32(cdi_info, &hxch->inlet_face_index, 1);
  cdi_write_asINT32(cdi_info, &hxch->outlet_face_index, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 20)) {
    cdi_write_asINT32(cdi_info, &hxch->coolant_entry_face_index, 1);
    cdi_write_asINT32(cdi_info, &hxch->top_exchanger_face_index, 1);
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 3)) {
    cdi_write_asINT32(cdi_info, &hxch->inlet_facet_offset, 1);
    cdi_write_asINT32(cdi_info, &hxch->outlet_facet_offset, 1);
  }
  cdi_write_asINT32(cdi_info, &hxch->inlet_meas_index, 1);
  cdi_write_asINT32(cdi_info, &hxch->outlet_meas_index, 1);
  if ((hxch->flags & CDI_HXCH_HAS_HEAT_GEN_MEAS) != 0)
    cdi_write_asINT32(cdi_info, &hxch->heat_gen_meas_index, 1);
  cdi_write_asINT32(cdi_info, &hxch->table_index, 1);
  cdi_write_asINT32(cdi_info, &hxch->adiabatic_index, 1);
  cdi_write_asINT32(cdi_info, &hxch->medium_index, 1);
  cdi_write_asINT32(cdi_info, &hxch->upstream_index, 1);
  cdi_write_asINT32(cdi_info, &hxch->n_stages, 1);
  cdi_write_asINT32(cdi_info, &hxch->n_y_divisions, 1);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 15)) {
    cdi_write_asINT32(cdi_info, hxch->n_pass_divisions, hxch->n_stages);
  }
  else { // Pre version 3.15 CDI files included only a total # divisions
    cdiINT32 totalNumDivisions = hxch->n_pass_divisions[0];
    if (hxch->n_stages == 2) // Pre version 3.15, only 1 or 2 stages were allowed
      totalNumDivisions += hxch->n_pass_divisions[1];
    cdi_write_asINT32(cdi_info, &totalNumDivisions, 1);
  }

  cdi_write_idFLOAT(cdi_info, &hxch->x_len, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->y_len, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->z_len, 1);
}


VOID
cdi_write_hxch(
               CDI_INFO cdi_info, /* the info structure */
               CDI_HXCH hxch /* the hxch structure */
               )
{
  hxch->cccc = CDI_CHUNK_TYPE_HXCH;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_HXCH);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 12))
    cdi_set_encryption_on(cdi_info);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info)) {
    // write values only if we have udst chunks
    if (hxch->udstValues.n_cvdp > 0 || hxch->udstValues.n_eqdp > 0) {
      WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_UDST)
      {
        ccDOTIMES(i, hxch->udstValues.n_cvdp) {
          cdi_write_cvdp(cdi_info, hxch->udstValues.cvdp[i]);
        }

        ccDOTIMES(i, hxch->udstValues.n_eqdp) {
          cdi_write_eqdp(cdi_info, hxch->udstValues.eqdp[i]);
        }
      }
    }
  }

  write_hxch_base(cdi_info, hxch);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 2))
    cdi_write_idFLOAT(cdi_info, &hxch->experiment_area, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->percent_flow, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->mass_flow_rate, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->specific_heat, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->heat_rejection, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->entry_temp, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->experiment_ref_temp, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->min_air_flow, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->max_air_flow, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->kc_coeff, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->kh_coeff, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->alpha_coeff, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->beta_coeff, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->d_coeff, 1);
  cdi_write_cdistring(cdi_info, hxch->data_string ? hxch->data_string : "");
  cdi_write_idFLOAT(cdi_info, &hxch->exp_coolant_sp_heat, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->exp_coolant_viscosity, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->exp_coolant_thermal_conductivity, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->user_coolant_viscosity, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->user_coolant_thermal_conductivity, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->experimental_height, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->experimental_width, 1);
  cdi_write_idFLOAT(cdi_info, &hxch->experimental_depth, 1);
  
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 12))
    cdi_set_encryption_off(cdi_info);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_hxch
|
| Purpose:reads hxch
|
\****************************************************************/

static void cdi_read_hxch_base(
                               CDI_INFO cdi_info, /* the info structure */
                               CDI_HXCH_BASE hxch
                               )
{
  cdi_read_cdistring(cdi_info, &hxch->name);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 7))
    cdi_read_cdistring(cdi_info, &hxch->part_name);
  else
    hxch->part_name = EXA_STRDUP(hxch->name);
  cdi_read_asINT32(cdi_info, &hxch->type, 1);
  cdi_read_asINT32(cdi_info, &hxch->tool, 1);
  cdi_read_asINT32(cdi_info, &hxch->flags, 1);
  cdi_read_asINT32(cdi_info, &hxch->table_csys_index, 1);
  cdi_read_asINT32(cdi_info, &hxch->medium_csys_index, 1);
  cdi_read_asINT32(cdi_info, &hxch->inlet_face_index, 1);
  cdi_read_asINT32(cdi_info, &hxch->outlet_face_index, 1);


  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 20)) {
    cdi_read_asINT32(cdi_info, &hxch->coolant_entry_face_index, 1);
    cdi_read_asINT32(cdi_info, &hxch->top_exchanger_face_index , 1);
  }
  else {
    hxch->coolant_entry_face_index = -1;
    hxch->top_exchanger_face_index = -1;
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 3)) {
    cdi_read_asINT32(cdi_info, &hxch->inlet_facet_offset, 1);
    cdi_read_asINT32(cdi_info, &hxch->outlet_facet_offset, 1);
  }
  else {
    hxch->inlet_facet_offset = 0;
    hxch->outlet_facet_offset = 0;
  }
  cdi_read_asINT32(cdi_info, &hxch->inlet_meas_index, 1);
  cdi_read_asINT32(cdi_info, &hxch->outlet_meas_index, 1);
  if ((hxch->flags & CDI_HXCH_HAS_HEAT_GEN_MEAS) != 0)
    cdi_read_asINT32(cdi_info, &hxch->heat_gen_meas_index, 1);
  else
    hxch->heat_gen_meas_index = -1;
  cdi_read_asINT32(cdi_info, &hxch->table_index, 1);
  cdi_read_asINT32(cdi_info, &hxch->adiabatic_index, 1);
  cdi_read_asINT32(cdi_info, &hxch->medium_index, 1);
  cdi_read_asINT32(cdi_info, &hxch->upstream_index, 1);
  cdi_read_asINT32(cdi_info, &hxch->n_stages, 1);

  hxch->n_pass_divisions = new cdiINT32[hxch->n_stages];
  cdi_read_asINT32(cdi_info, &hxch->n_y_divisions, 1);

  // We used to write out the total number of divisions across the heat
  // exchanger in the non-flow direction. If the HX was U-type, the passes were
  // assumed to have the same number of passes. As of CDI version 3.15, we write
  // out the number of divisions per pass.
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 15)) {
    cdi_read_asINT32(cdi_info, hxch->n_pass_divisions, hxch->n_stages);
  }
  else {
    cdi_read_asINT32(cdi_info, hxch->n_pass_divisions, 1);
    if (hxch->n_stages == 2) { // Pre version 3.15, only 1 or 2 stages were allowed
      cdiINT32 numDivisionsPerPass = (hxch->n_pass_divisions[0] / 2);
      hxch->n_pass_divisions[0] = hxch->n_pass_divisions[1] = numDivisionsPerPass;
    }
  }

  cdi_read_idFLOAT(cdi_info, &hxch->x_len, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->y_len, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->z_len, 1);
}

CDI_HXCH
cdi_read_hxch(
              CDI_INFO cdi_info /* the info structure */
              )
{
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 12))
    cdi_set_encryption_on(cdi_info);

  CDI_HXCH hxch = EXA_CALLOC_STRUCT(CDI_HXCH); /* the hxch structure */

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info)) {
      hxch->udstValues = cdi_read_udst(cdi_info);
  }
  else {
    hxch->udstValues.n_cvdp = 0;
    hxch->udstValues.n_eqdp = 0;
  }

  hxch->cccc = CDI_CHUNK_TYPE_HXCH;

  cdi_read_hxch_base(cdi_info, hxch);
  
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 2)) {
    cdi_read_idFLOAT(cdi_info, &hxch->experiment_area, 1);
  } else {
    hxch->experiment_area = -1;
  }

  cdi_read_idFLOAT(cdi_info, &hxch->percent_flow, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->mass_flow_rate, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->specific_heat, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->heat_rejection, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->entry_temp, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->experiment_ref_temp, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->min_air_flow, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->max_air_flow, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->kc_coeff, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->kh_coeff, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->alpha_coeff, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->beta_coeff, 1);
  cdi_read_idFLOAT(cdi_info, &hxch->d_coeff, 1);
  cdi_read_cdistring(cdi_info, &hxch->data_string);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 12)) {
    cdi_read_idFLOAT(cdi_info, &hxch->exp_coolant_sp_heat, 1);
    cdi_read_idFLOAT(cdi_info, &hxch->exp_coolant_viscosity, 1);
    cdi_read_idFLOAT(cdi_info, &hxch->exp_coolant_thermal_conductivity, 1);
    cdi_read_idFLOAT(cdi_info, &hxch->user_coolant_viscosity, 1);
    cdi_read_idFLOAT(cdi_info, &hxch->user_coolant_thermal_conductivity, 1);
    cdi_read_idFLOAT(cdi_info, &hxch->experimental_height, 1);
    cdi_read_idFLOAT(cdi_info, &hxch->experimental_width, 1);
    cdi_read_idFLOAT(cdi_info, &hxch->experimental_depth, 1);
  } else {
    hxch->exp_coolant_sp_heat = -1;
    hxch->exp_coolant_viscosity = -1;
    hxch->exp_coolant_thermal_conductivity = -1;
    hxch->user_coolant_viscosity = -1;
    hxch->user_coolant_thermal_conductivity = -1;
    hxch->experimental_height = -1;
    hxch->experimental_width = -1;
    hxch->experimental_depth = -1;
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 12))
    cdi_set_encryption_off(cdi_info);

  return(hxch);
}

/****************************************************************\
|
| Function name:cdi_write_amhx
|
| Purpose:writes amhx (AMESim heat exchanger)
|
| There is not a common base class read method for HXCH and AMHX
| since the file format was established before the base class
| and the AMHX chunk were created.
|
\****************************************************************/
VOID
cdi_write_amhx(
               CDI_INFO cdi_info, /* the info structure */
               CDI_AMHX hxch
               )
{
  hxch->cccc = CDI_CHUNK_TYPE_AMHX;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_AMHX);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info)) {
    // write values only if we have udst chunks
    if (hxch->udstValues.n_cvdp > 0 || hxch->udstValues.n_eqdp > 0) {
      WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_UDST)
      {
        ccDOTIMES(i, hxch->udstValues.n_cvdp) {
          cdi_write_cvdp(cdi_info, hxch->udstValues.cvdp[i]);
        }

        ccDOTIMES(i, hxch->udstValues.n_eqdp) {
          cdi_write_eqdp(cdi_info, hxch->udstValues.eqdp[i]);
        }
      }
    }
  }

  write_hxch_base(cdi_info, hxch);

  cdi_write_cdistring(cdi_info, hxch->model_filename ? hxch->model_filename : "");
  cdi_write_cdistring(cdi_info, hxch->absolute_model_filename ? hxch->absolute_model_filename : "");
  cdi_write_cdistring(cdi_info, hxch->amesim_hx_name ? hxch->amesim_hx_name : "");

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_amhx
|
| Purpose:reads amhx
|
\****************************************************************/

CDI_AMHX
cdi_read_amhx(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_AMHX hxch = EXA_CALLOC_STRUCT(CDI_AMHX);

  hxch->cccc = CDI_CHUNK_TYPE_AMHX;

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info)) {
    hxch->udstValues = cdi_read_udst(cdi_info);
  }
  else {
    hxch->udstValues.n_cvdp = 0;
    hxch->udstValues.n_eqdp = 0;
  }

  cdi_read_hxch_base(cdi_info, hxch);
  
  cdi_read_cdistring(cdi_info, &hxch->model_filename);
  cdi_read_cdistring(cdi_info, &hxch->absolute_model_filename);
  cdi_read_cdistring(cdi_info, &hxch->amesim_hx_name);

  return(hxch);
}

/****************************************************************\
|
| Function name:cdi_destroy_hxch
|
| Purpose:destroys a hxch 
|
\****************************************************************/
VOID
cdi_destroy_hxch(
                 CDI_HXCH hxch        /* the hxch to destroy */
                 )
{
  cdi_destroy_udst(&hxch->udstValues);
  exa_free(hxch);
}

/****************************************************************\
|
| Function name:cdi_destroy_amhx
|
| Purpose:destroys a amhx 
|
\****************************************************************/
VOID
cdi_destroy_amhx(
                 CDI_AMHX amhx        /* the hxch to destroy */
                 )
{
  cdi_destroy_udst(&amhx->udstValues);
  exa_free(amhx);
}

/****************************************************************\
|
| Function name:cdi_write_bsrg
|
| Purpose:writes bsrg (boundary seeding region)
|
\****************************************************************/
VOID
cdi_write_bsrg(
               CDI_INFO cdi_info, /* the info structure */
               CDI_BSRG bsrg      /* the bsrg structure */
               )
{
  bsrg->cccc = CDI_CHUNK_TYPE_BSRG;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_BSRG);

  cdi_write_cdichars(cdi_info, bsrg->mea_name, strlen(bsrg->mea_name));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 21)) {
    cdi_write_sINT64(cdi_info, (sINT64 *)&bsrg->cdi_file_id, 1);
  }
  cdi_write_asINT32(cdi_info, &bsrg->region_index, 1);
  cdi_write_asINT32(cdi_info, &bsrg->facet_offset, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 10)) {
    cdi_write_asINT32(cdi_info, &bsrg->spatial_mapping, 1);
    cdi_write_one_asINT32(cdi_info, bsrg->transient_boundary_seeding);
    cdi_write_one_asINT32(cdi_info, bsrg->is_merged_to_input);
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 1)) {
    cdi_write_one_asINT32(cdi_info, bsrg->seed_via_mks);
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 13)) {
    cdi_write_idFLOAT(cdi_info, (idFLOAT *) &bsrg->coord_offset, 3 );
  }
  cdi_pop(cdi_info);
}
/****************************************************************\
|
| Function name:cdi_read_bsrg
|
| Purpose:reads bsrg (boundary seeding region)
|
\****************************************************************/
CDI_BSRG
cdi_read_bsrg(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_BSRG bsrg = EXA_CALLOC_STRUCT(CDI_BSRG); /* the bsrg structure */

  bsrg->cccc = CDI_CHUNK_TYPE_BSRG;

  int unusedInt;
  bsrg->mea_name = cdi_read_cdichars(cdi_info, NULL, &unusedInt);
  sINT64 cdi_file_id = -1;
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 21)) {
    cdi_read_sINT64(cdi_info, (sINT64 *)&cdi_file_id, 1);
  }
  bsrg->cdi_file_id = cdi_file_id;
  cdi_read_asINT32(cdi_info, &bsrg->region_index, 1);
  cdi_read_asINT32(cdi_info, &bsrg->facet_offset, 1);
  asINT32 mapping = -1;
  cBOOLEAN tbs = 0;
  cBOOLEAN is_merged = 0;
  if(CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 10)) {
    cdi_read_asINT32(cdi_info, &mapping, 1);
    tbs = (cdi_read_one_asINT32(cdi_info) != 0);
    is_merged = (cdi_read_one_asINT32(cdi_info) != 0);
  }
  bsrg->spatial_mapping = mapping;
  bsrg->transient_boundary_seeding = tbs;
  bsrg->is_merged_to_input = is_merged;
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 1)) {
     bsrg->seed_via_mks = (cdi_read_one_asINT32(cdi_info) != 0);
  }
  double coord[3] = {0.0F};
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 13)) {
    cdi_read_idFLOAT(cdi_info, (idFLOAT *) &coord, 3 );
  }
  for(int i = 0; i < 3; i++)
    bsrg->coord_offset[i] = coord[i];
  return(bsrg);
}
/****************************************************************\
|
| Function name:cdi_destroy_bsrg
|
| Purpose:destroys a bsrg
|
\****************************************************************/
VOID
cdi_destroy_bsrg(
                 CDI_BSRG bsrg        /* the bsrg to destroy */
                 )
{
  exa_free(bsrg->mea_name);
}


/****************************************************************\
|
| Function name:cdi_write_vpnt
|
| Purpose:writes vpnt (viewpoint)
|
\****************************************************************/
VOID
cdi_write_vpnt(
               CDI_INFO cdi_info, /* the info structure */
               CDI_VPNT vpnt /* the vpnt structure */
               )
{
  vpnt->cccc = CDI_CHUNK_TYPE_VPNT;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_VPNT);

  cdi_write_cdichars(cdi_info, vpnt->name, strlen(vpnt->name));
  cdi_write_asINT32(cdi_info, &vpnt->csys_index, 1);
  cdi_write_cdichars(cdi_info, vpnt->camera_pos_unit, strlen(vpnt->camera_pos_unit));
  cdi_write_idFLOAT(cdi_info, &vpnt->camera_pos[0], 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->camera_pos[1], 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->camera_pos[2], 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->view_dir[0], 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->view_dir[1], 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->view_dir[2], 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->up_dir[0], 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->up_dir[1], 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->up_dir[2], 1);
  cdi_write_asINT32(cdi_info, &vpnt->projection_type, 1);
  cdi_write_cdichars(cdi_info, vpnt->orthographic_fov_unit, strlen(vpnt->orthographic_fov_unit));
  cdi_write_idFLOAT(cdi_info, &vpnt->orthographic_fov, 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->perspective_fov, 1);
  cdi_write_cdichars(cdi_info, vpnt->spin_center_unit, strlen(vpnt->spin_center_unit));
  cdi_write_idFLOAT(cdi_info, &vpnt->spin_center[0], 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->spin_center[1], 1);
  cdi_write_idFLOAT(cdi_info, &vpnt->spin_center[2], 1);

  cdi_pop(cdi_info);
}
/****************************************************************\
|
| Function name:cdi_read_vpnt
|
| Purpose:reads vpnt (viewpoint)
|
\****************************************************************/
CDI_VPNT
cdi_read_vpnt(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_VPNT vpnt = EXA_CALLOC_STRUCT(CDI_VPNT); /* the vpnt structure */

  vpnt->cccc = CDI_CHUNK_TYPE_VPNT;

  int unusedInt;
  vpnt->name = cdi_read_cdichars(cdi_info, NULL, &unusedInt);
  cdi_read_asINT32(cdi_info, &vpnt->csys_index, 1);
  vpnt->camera_pos_unit = cdi_read_cdichars(cdi_info, NULL, &unusedInt);
  cdi_read_idFLOAT(cdi_info, &vpnt->camera_pos[0], 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->camera_pos[1], 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->camera_pos[2], 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->view_dir[0], 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->view_dir[1], 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->view_dir[2], 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->up_dir[0], 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->up_dir[1], 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->up_dir[2], 1);
  cdi_read_asINT32(cdi_info, &vpnt->projection_type, 1);
  vpnt->orthographic_fov_unit = cdi_read_cdichars(cdi_info, NULL, &unusedInt);
  cdi_read_idFLOAT(cdi_info, &vpnt->orthographic_fov, 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->perspective_fov, 1);
  vpnt->spin_center_unit = cdi_read_cdichars(cdi_info, NULL, &unusedInt);
  cdi_read_idFLOAT(cdi_info, &vpnt->spin_center[0], 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->spin_center[1], 1);
  cdi_read_idFLOAT(cdi_info, &vpnt->spin_center[2], 1);

  return(vpnt);
}
/****************************************************************\
|
| Function name:cdi_destroy_vpnt
|
| Purpose:destroys a vpnt
|
\****************************************************************/
VOID
cdi_destroy_vpnt(
                 CDI_VPNT vpnt        /* the vpnt to destroy */
                 )
{
  exa_free(vpnt->name);
  exa_free(vpnt->camera_pos_unit);
  exa_free(vpnt->orthographic_fov_unit);
  exa_free(vpnt->spin_center_unit);
  exa_free(vpnt);
}
/****************************************************************\
|
| Function name:cdi_write_ivdp
|
| Purpose:writes ivdp (integer dynamical parameters)
|
\****************************************************************/
VOID
cdi_write_ivdp(
               CDI_INFO cdi_info, /* the info structure */
               CDI_IVDP ivdp /* the ivdp structure */
               )
{
  ivdp->cccc = CDI_CHUNK_TYPE_IVDP;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_IVDP);

  cdi_write_asINT32(cdi_info, &ivdp->type, 1);
  cdi_write_asINT32(cdi_info, &ivdp->value, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_ivdp
|
| Purpose:reads ivdp
|
\****************************************************************/
CDI_IVDP
cdi_read_ivdp(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_IVDP ivdp = EXA_CALLOC_STRUCT(CDI_IVDP); /* the ivdp structure */

  ivdp->cccc = CDI_CHUNK_TYPE_IVDP;
  
  cdi_read_asINT32(cdi_info, &ivdp->type, 1);
  cdi_read_asINT32(cdi_info, &ivdp->value, 1);

  return(ivdp);
}

/****************************************************************\
|
| Function name:cdi_destroy_ivdp
|
| Purpose:destroys a ivdp 
|
\****************************************************************/
VOID
cdi_destroy_ivdp(
                 CDI_IVDP ivdp        /* the ivdp to destroy */
                 )
{
  exa_free(ivdp);
}

/****************************************************************\
|
| Function name:cdi_write_cvdp
|
| Purpose:writes cvdp (continuous dynamical parameters)
|
\****************************************************************/
VOID
cdi_write_cvdp(
               CDI_INFO cdi_info, /* the info structure */
               CDI_CVDP cvdp /* the cvdp structure */
               )
{
  cdi_set_encryption_on_if_needed(cdi_info, CDI_CHUNK_TYPE_CVDP);

  cvdp->cccc = CDI_CHUNK_TYPE_CVDP;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_CVDP);

  cdi_write_asINT32(cdi_info, &cvdp->type, 1);
  cdi_write_idFLOAT(cdi_info, &cvdp->value, 1);

  cdi_set_encryption_off(cdi_info);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_cvdp
|
| Purpose:reads cvdp
|
\****************************************************************/
CDI_CVDP
cdi_read_cvdp(
              CDI_INFO cdi_info /* the info structure */
              )
{
  cdi_set_encryption_on_if_needed(cdi_info, CDI_CHUNK_TYPE_CVDP);
  
  CDI_CVDP cvdp = EXA_CALLOC_STRUCT(CDI_CVDP); /* the cvdp structure */
  cvdp->cccc = CDI_CHUNK_TYPE_CVDP;

  cdi_read_asINT32(cdi_info, &cvdp->type, 1);
  cdi_read_idFLOAT(cdi_info, &cvdp->value, 1);
  
  cdi_set_encryption_off(cdi_info);
  return(cvdp);
}

/****************************************************************\
|
| Function name:cdi_destroy_cvdp
|
| Purpose:destroys a cvdp 
|
\****************************************************************/
VOID
cdi_destroy_cvdp(
                 CDI_CVDP cvdp        /* the cvdp to destroy */
                 )
{
  exa_free(cvdp);
}


/****************************************************************\
|
| Function name:cdi_write_bsdp
|
| Purpose:writes bsdp (continuous dynamical parameters)
|
\****************************************************************/
VOID
cdi_write_bsdp(
               CDI_INFO cdi_info, /* the info structure */
               CDI_BSDP bsdp /* the bsdp structure */
               )
{
  bsdp->cccc = CDI_CHUNK_TYPE_BSDP;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_BSDP);

  cdi_write_asINT32(cdi_info, &bsdp->type, 1);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 10))
    cdi_write_idFLOAT(cdi_info, &bsdp->value, 1);
  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_bsdp
|
| Purpose:reads bsdp
|
\****************************************************************/
CDI_BSDP
cdi_read_bsdp(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_BSDP bsdp = EXA_CALLOC_STRUCT(CDI_BSDP); /* the bsdp structure */

  bsdp->cccc = CDI_CHUNK_TYPE_BSDP;


  cdi_read_asINT32(cdi_info, &bsdp->type, 1);
  double val = (bsdp->type == CDI_VAR_ID_NEW_PRESSURE 
                || bsdp->type == CDI_VAR_ID_PRESSURE ? 0.02 : 0);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 10))
    cdi_read_idFLOAT(cdi_info, &val, 1);
  bsdp->value = val;
  return(bsdp);
}

/****************************************************************\
|
| Function name:cdi_destroy_bsdp
|
| Purpose:destroys a bsdp
|
\****************************************************************/
VOID
cdi_destroy_bsdp(
                 CDI_BSDP bsdp        /* the bsdp to destroy */
                 )
{
  exa_free(bsdp);
}

/****************************************************************\
|
| Function name:cdi_write_pnts
|
| Purpose:writes pnts (point chunk)
|
\****************************************************************/
VOID
cdi_write_pnts(
               CDI_INFO cdi_info,    /* the info structure */
               sCDI_PNTS* pnts       /* the pnts structure */
               )
{
  pnts->cccc = CDI_CHUNK_TYPE_PNTS;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_PNTS);

  cdi_write_asINT32(cdi_info, &pnts->dim, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 14)) {
    cdi_write_asINT32(cdi_info, &pnts->is_closed, 1);
  }
  cdi_write_asINT32(cdi_info, &pnts->num_points, 1);
  if (pnts->num_points > 0)
    cdi_write_idFLOAT(cdi_info, pnts->points, pnts->num_points*pnts->dim);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_pnts
|
| Purpose:reads pnts
|
\****************************************************************/
CDI_PNTS
cdi_read_pnts(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_PNTS pnts = EXA_CALLOC_STRUCT(CDI_PNTS); /* the pnts structure */

  pnts->cccc = CDI_CHUNK_TYPE_PNTS;
 
  cdi_read_asINT32(cdi_info, &pnts->dim, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 14)) { 
    cdi_read_asINT32(cdi_info, &pnts->is_closed, 1);
  } else {
    pnts->is_closed = -1;
  }
  cdi_read_asINT32(cdi_info, &pnts->num_points, 1);
  if (pnts->num_points == 0 || pnts->dim <= 0)
    pnts->points = NULL;
  else {
    pnts->points = EXA_MALLOC_ARRAY(double, pnts->num_points*pnts->dim);
    cdi_read_idFLOAT(cdi_info, pnts->points, pnts->num_points*pnts->dim);
  }
  return(pnts);
}

/****************************************************************\
|
| Function name:cdi_destroy_pnts
|
| Purpose:destroys a pnts
|
\****************************************************************/
VOID
cdi_destroy_pnts(
                 CDI_PNTS pnts        /* the pnts to destroy */
                 )
{
  if (pnts->points != NULL)
    exa_free(pnts->points);
  exa_free(pnts);
}

//****************************************************************
//
// STL-friendly version of sCDI_PNTS
//
// Writes/reads the same chunk as sCDI_PNTS, but holds the point
// data in a std::vector<>, rather than C-style array.
//
//***************************************************************
void sCDI_VECOF_PNTS::WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_push(cdi_info, GetChunkType());

  // To get around the 'const' problem ...
  cdiINT32 localDim = dim;
  cdi_write_asINT32(cdi_info, &localDim, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 14))
  {
    cdiINT32 localIsClosed = is_closed;
    cdi_write_asINT32(cdi_info, &localIsClosed, 1);
  }

  cdiINT32 num_points = points.size()/dim;
  cdi_write_asINT32(cdi_info, &num_points, 1);
  if (!points.empty())
  {
    double* pointsArr = const_cast<double*>(&points[0]);
    cdi_write_idFLOAT(cdi_info, pointsArr, points.size());
  }

  cdi_pop(cdi_info);
}

void sCDI_VECOF_PNTS::ReadFromCDI(CDI_INFO cdi_info)
{
  cdi_read_asINT32(cdi_info, &dim, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 14)) {
    cdi_read_asINT32(cdi_info, &is_closed, 1);
  }
  else {
    is_closed = -1;
  }
  cdiINT32 num_points;  // Throw away
  cdi_read_asINT32(cdi_info, &num_points, 1);
  if (num_points * dim > 0) {
    double* pointsArr = EXA_MALLOC_ARRAY(double, num_points*dim);
    cdi_read_idFLOAT(cdi_info, pointsArr, num_points*dim);
    points.assign(pointsArr, pointsArr + num_points*dim);
    exa_free(pointsArr);
  }
}


/****************************************************************\
|
| Function name:cdi_write_eqdp
|
| Purpose:writes eqdp (continuous dynamical parameter defined by equation)
|
\****************************************************************/
VOID
cdi_write_eqdp(
               CDI_INFO cdi_info, /* the info structure */
               CDI_EQDP eqdp /* the eqdp structure */
               )
{
  eqdp->cccc = CDI_CHUNK_TYPE_EQDP;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_EQDP);

  cdi_write_asINT32(cdi_info, &eqdp->type, 1);
  cdi_write_cdichars(cdi_info, eqdp->var_name, eqdp->var_name_length);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_eqdp
|
| Purpose:reads eqdp
|
\****************************************************************/
CDI_EQDP
cdi_read_eqdp(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_EQDP eqdp = EXA_CALLOC_STRUCT(CDI_EQDP); /* the eqdp structure */

  eqdp->cccc = CDI_CHUNK_TYPE_EQDP;
  
  cdi_read_asINT32(cdi_info, &eqdp->type, 1);
  eqdp->var_name = cdi_read_cdichars(cdi_info, NULL, &eqdp->var_name_length);

  return(eqdp);
}

/****************************************************************\
|
| Function name:cdi_destroy_eqdp
|
| Purpose:destroys a eqdp 
|
\****************************************************************/
VOID
cdi_destroy_eqdp(
                 CDI_EQDP eqdp        /* the eqdp to destroy */
                 )
{
  exa_free(eqdp);
}

/****************************************************************\
|
| Function name:cdi_write_rgns
|
| Purpose:writes rgns (regions list)
|
\****************************************************************/
void
cdi_write_rgns(CDI_INFO cdi_info, CDI_RGNS rgns)
{   
  rgns->cccc = CDI_CHUNK_TYPE_RGNS;
  cdi_push(cdi_info, CDI_CHUNK_TYPE_RGNS);
  cdi_write_asINT32(cdi_info, &rgns->n_region, 1);
  cdi_write_asINT32(cdi_info, rgns->region, rgns->n_region);
  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_write_rgns
|
| Purpose:writes rgns (regions list) from a std::vector<int>
|
\****************************************************************/
void
cdi_write_rgns(CDI_INFO cdi_info, std::vector<cdiINT32>& rgnVec)
{
  sCDI_RGNS rgns;
  rgns.cccc = CDI_CHUNK_TYPE_RGNS;
  rgns.region = &rgnVec[0];
  rgns.n_region = rgnVec.size();
  //this funct does the push/pop of info
  cdi_write_rgns(cdi_info, &rgns);
}

/****************************************************************\
|
| Function name:cdi_read_rgns
|
| Purpose:reads regions lists
|
\****************************************************************/
CDI_RGNS
cdi_read_rgns(CDI_INFO cdi_info)
{   
  CDI_RGNS rgns = EXA_CALLOC_STRUCT(CDI_RGNS);
  rgns->cccc = CDI_CHUNK_TYPE_RGNS;
  cdi_read_asINT32(cdi_info, &rgns->n_region, 1);
  rgns->region = EXA_CALLOC_ARRAY(asINT32, rgns->n_region);
  cdi_read_asINT32(cdi_info, rgns->region, rgns->n_region);
  return rgns;
}

/****************************************************************\
|
| Function name: cdi_inner_chunk_read_rgns
|
| Purpose:reads regions list into a std::vector<int>
|
\****************************************************************/
void
cdi_inner_chunk_read_rgns(cdiINT32 &index, const char* chunkName, CDI_INFO cdi_info, std::vector<cdiINT32>& rgnVec)
{
  if (cdi_cio_descend_with_error(cdi_info->cio_info, chunkName) == 0) {
    sCDI_RGNS* rgns = cdi_read_rgns(cdi_info);
    rgnVec.assign(rgns->region, rgns->region + rgns->n_region);
    cdi_destroy_rgns(rgns);
  }
  cdi_cio_ascend_with_error(cdi_info->cio_info, chunkName, ++index);
}

/****************************************************************\
|
| Function name:cdi_destroy_rgns
|
| Purpose:destroys a regions list
|
\****************************************************************/
void
cdi_destroy_rgns(CDI_RGNS rgns)
{
  exa_free(rgns->region);
  exa_free(rgns);
}
  
  
/****************************************************************\
|
| Function name:cdi_read_gscl
|
| Purpose:
|
\****************************************************************/

CDI_GSCL cdi_read_gscl (CDI_INFO cdi_info)
{
  CDI_GSCL gscl = new sCDI_GSCL();

  asINT32 count = cio_get_count(cdi_info->cio_info);
  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);

    switch(cio_get_type(cdi_info->cio_info)) {
    case CDI_CHUNK_TYPE_NAME: {
      CDI_NAME nchunk = cdi_read_name(cdi_info);
      gscl -> name = *nchunk;
      EXA_FREE(nchunk);
      break;
    };

    case CDI_CHUNK_TYPE_RGNS: {
      CDI_RGNS rgns = cdi_read_rgns(cdi_info);
      gscl->rgn_list = *rgns;
      gscl->geom_ref.rgn_list.assign(rgns->region, rgns->region + rgns->n_region);
      gscl->geom_ref.GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
      EXA_FREE(rgns);
      break;
    }
    case CDI_CHUNK_TYPE_GMRF: {
      cCDI_GEOMETRY_REF* read_gmrf = cdi_read_gmrf(cdi_info);
      gscl->geom_ref = *read_gmrf;
      cdi_destroy_gmrf(read_gmrf);
      break;
    }

    case CDI_CHUNK_TYPE_GSCC: {
      CDI_GSCC gchunk = cdi_read_gscc(cdi_info);
      gscl -> scale = *gchunk;
      EXA_FREE(gchunk);
      break;
    };

    case CDI_CHUNK_TYPE_NULL: {
      cdi_read_null(cdi_info);
      break;
    };

    default: {
      break;
    }
    }

    cio_ascend(cdi_info->cio_info);
  }

  return(gscl);
}

/****************************************************************\
|
| Function name:cdi_destroy_gscl
|
| Purpose:
|
\****************************************************************/
VOID
cdi_destroy_gscl(CDI_GSCL gscl)
{
  if (gscl->name.name) {exa_free(gscl->name.name);}
  if (gscl->rgn_list.region) {exa_free(gscl->rgn_list.region);}

  delete gscl;
}

/****************************************************************\
|
| Function name:cdi_read_gfds
|
| Purpose:
|
\****************************************************************/
CDI_GFDS cdi_read_gfds (CDI_INFO cdi_info)
{
  CDI_GFDS gfds = EXA_CALLOC_STRUCT(CDI_GFDS);

  asINT32 count = cio_get_count(cdi_info->cio_info);
  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);

    switch(cio_get_type(cdi_info->cio_info)) {
    case CDI_CHUNK_TYPE_NAME: {
      gfds -> name = *(cdi_read_name(cdi_info));
      break;
    }

    case CDI_CHUNK_TYPE_FDLT: {
      gfds -> fd_list = *(cdi_read_fdlt(cdi_info));
      break;
    }

    case CDI_CHUNK_TYPE_GSCC: {
      gfds -> scale = *(cdi_read_gscc(cdi_info));
      break;
    }

    case CDI_CHUNK_TYPE_NULL: {
      cdi_read_null(cdi_info);
      break;
    }

    default: {
      break;
    }
    }

    cio_ascend(cdi_info->cio_info);
  }

  return(gfds);
}


/****************************************************************\
|
| Function name:cdi_write_gscc
|
| Purpose:writes gscc (grid scale value)
|
\****************************************************************/
VOID
cdi_write_gscc(
               CDI_INFO cdi_info, /* the info structure */
               CDI_GSCC gscc /* the gscc structure */
               )
{
  gscc->cccc = CDI_CHUNK_TYPE_GSCC;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_GSCC);

  cdi_write_asINT32(cdi_info, &gscc->value, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_gscc
|
| Purpose:reads gscc
|
\****************************************************************/
CDI_GSCC
cdi_read_gscc(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_GSCC gscc = EXA_CALLOC_STRUCT(CDI_GSCC); /* the gscc structure */

  gscc->cccc = CDI_CHUNK_TYPE_GSCC;
  
  cdi_read_asINT32(cdi_info, &gscc->value, 1);

  return(gscc);
}

/****************************************************************\
|
| Function name:cdi_destroy_gscc
|
| Purpose:destroys a gscc 
|
\****************************************************************/
VOID
cdi_destroy_gscc(
                 CDI_GSCC gscc        /* the gscc to destroy */
                 )
{
  exa_free(gscc);
}

/****************************************************************\
|
| Function name:cdi_write_gsep
|
| Purpose:writes gsep (grid scale separation factor)
|
\****************************************************************/
VOID cdi_write_gsep(CDI_INFO cdi_info, /* the info structure */
                    CDI_GSEP gsep)     /* the gsep structure */
{
  gsep->cccc = CDI_CHUNK_TYPE_GSEP;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_GSEP);

  cdi_write_asINT32(cdi_info, &gsep->scale, 1);
  cdi_write_asINT32(cdi_info, &gsep->separation_factor, 1);

  cdi_pop(cdi_info);
}
/****************************************************************\
|
| Function name:cdi_read_gsep
|
| Purpose:reads gsep
|
\****************************************************************/
CDI_GSEP cdi_read_gsep(CDI_INFO cdi_info) /* the info structure */
{
  CDI_GSEP gsep = EXA_CALLOC_STRUCT(CDI_GSEP); /* the gsep structure */

  gsep->cccc = CDI_CHUNK_TYPE_GSEP;
  
  cdi_read_asINT32(cdi_info, &gsep->scale, 1);
  cdi_read_asINT32(cdi_info, &gsep->separation_factor, 1);

  return gsep;
}

/****************************************************************\
|
| Function name:cdi_destroy_gsep
|
| Purpose:destroys a gsep 
|
\****************************************************************/
VOID cdi_destroy_gsep(CDI_GSEP gsep)        /* the gsep to destroy */
{
  exa_free(gsep);
}

/****************************************************************\
|
| Function name:cdi_write_grdf
|
| Purpose:writes grdf (grid scale default parameters)
|
\****************************************************************/
VOID cdi_write_grdf(CDI_INFO cdi_info,
                    CDI_GRDF grdf)
{
  grdf->cccc = CDI_CHUNK_TYPE_GRDF;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_GRDF);

  cdi_write_asINT32(cdi_info, &grdf->resolution_precedence, 1);
  cdi_write_asINT32(cdi_info, &grdf->separation_factor, 1);

  cdi_pop(cdi_info);
}
/****************************************************************\
|
| Function name:cdi_read_grdf
|
| Purpose: read grid (scale_map) default parameters
|
\****************************************************************/
CDI_GRDF cdi_read_grdf (CDI_INFO cdi_info)
{
  CDI_GRDF grdf = EXA_CALLOC_STRUCT(CDI_GRDF);

  grdf->cccc = CDI_CHUNK_TYPE_GRDF;
  
  cdi_read_asINT32(cdi_info, &grdf->resolution_precedence, 1);
  cdi_read_asINT32(cdi_info, &grdf->separation_factor, 1);

  return grdf;
}
/****************************************************************\
|
| Function name:cdi_destroy_grdf
|
| Purpose:
|
\****************************************************************/
VOID cdi_destroy_grdf(CDI_GRDF grdf)
{
  exa_free(grdf);
}

/****************************************************************\
|
| Function name:cdi_write_prdf
|
| Purpose:writes prdf (case default region precedences)
|
\****************************************************************/
VOID cdi_write_prdf(CDI_INFO cdi_info,
                    CDI_PRDF prdf)
{
  prdf->cccc = CDI_CHUNK_TYPE_PRDF;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_PRDF);

  cdi_write_asINT32(cdi_info, &prdf->default_scale_precedence, 1);
  cdi_write_asINT32(cdi_info, &prdf->default_physics_precedence, 1);

  cdi_pop(cdi_info);
}
/****************************************************************\
|
| Function name:cdi_read_prdf
|
| Purpose: read prdf (case default region precedences)
|
\****************************************************************/
CDI_PRDF cdi_read_prdf (CDI_INFO cdi_info)
{
  CDI_PRDF prdf = EXA_CALLOC_STRUCT(CDI_PRDF);

  prdf->cccc = CDI_CHUNK_TYPE_PRDF;
  
  cdi_read_asINT32(cdi_info, &prdf->default_scale_precedence, 1);
  cdi_read_asINT32(cdi_info, &prdf->default_physics_precedence, 1);

  return prdf;
}
/****************************************************************\
|
| Function name:cdi_destroy_prdf
|
| Purpose:
|
\****************************************************************/
VOID cdi_destroy_prdf(CDI_PRDF prdf)
{
  exa_free(prdf);
}

/****************************************************************\
|
| Function name:cdi_write_prec
|
| Purpose:writes prec (generalized precedence value)
|
\****************************************************************/
VOID cdi_write_prec(CDI_INFO cdi_info,
                    CDI_PREC prec)
{
  prec->cccc = CDI_CHUNK_TYPE_PREC;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_PREC);

  cdi_write_asINT32(cdi_info, &prec->precedence, 1);

  cdi_pop(cdi_info);
}
/****************************************************************\
|
| Function name:cdi_read_prec
|
| Purpose: read prec (generalized precedence value)
|
\****************************************************************/
CDI_PREC cdi_read_prec (CDI_INFO cdi_info)
{
  CDI_PREC prec = EXA_CALLOC_STRUCT(CDI_PREC);

  prec->cccc = CDI_CHUNK_TYPE_PREC;
  
  cdi_read_asINT32(cdi_info, &prec->precedence, 1);

  return prec;
}
/****************************************************************\
|
| Function name:cdi_destroy_prec
|
| Purpose:
|
\****************************************************************/
VOID cdi_destroy_prec(CDI_PREC prec)
{
  exa_free(prec);
}

/****************************************************************\
|
| Function name:cdi_write_simv
|
| Purpose:writes simv (simulation volume)
|
\****************************************************************/
VOID
cdi_write_simv(
               CDI_INFO cdi_info, /* the info structure */
               CDI_SIMV simv /* the simv structure */
               )
{
  simv->cccc = CDI_CHUNK_TYPE_SIMV;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_SIMV);

  // see cdi_read_simv() for why we have to do this
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 0) && !CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 9)) {

    asINT32 split_bit = 26;
    asINT32 shift = 2;

    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9,3)) {
      split_bit = 27;
      shift = 1;
    }

    using FLAG_INT = decltype(simv->flags);
    FLAG_INT flags = simv->flags;
    FLAG_INT high_bits = flags & (~((FLAG_INT(1) << split_bit)-1));
    FLAG_INT low_bits  = flags & (  (FLAG_INT(1) << split_bit)-1);
    high_bits >>= shift; 
    flags = high_bits | low_bits;
    simv->flags = flags;
  }

  cdi_write_idFLOAT(cdi_info, simv->max, 3);
  cdi_write_asINT32(cdi_info, &simv->flags, 1);
  cdi_write_idFLOAT(cdi_info, (idFLOAT *) &simv->xform, 16);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_simv
|
| Purpose:reads simv
|
\****************************************************************/
CDI_SIMV
cdi_read_simv(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_SIMV simv = EXA_CALLOC_STRUCT(CDI_SIMV); /* the simv structure */

  simv->cccc = CDI_CHUNK_TYPE_SIMV;
  
  cdi_read_idFLOAT(cdi_info, simv->max, 3);
  cdi_read_asINT32(cdi_info, &simv->flags, 1);
  cdi_read_idFLOAT(cdi_info, (idFLOAT *) &simv->xform, 16);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 0) && !CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 9)) {
    // Versions between 9.0 and 9.2 had new conduction bits starting at bit 26.
    // In 9.3 they shifted to start at bit 27. In 9.9 they shifted to bit 28.
    // This happened because the bits 26 & 27 got used for 2 different properties
    // between the conduction development branch and the mainline. We decided
    // the mainline bits should take precendence. Hence, we have have to adjust
    // the old conduction cdi files to match the current bit locations.
    asINT32 split_bit = 26;
    asINT32 shift = 2;
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9,3)) {
      split_bit = 27;
      shift = 1;
    }

    using FLAG_INT = decltype(simv->flags);
    FLAG_INT flags = simv->flags;
    FLAG_INT high_bits = flags & (~((FLAG_INT(1) << split_bit)-1));
    FLAG_INT low_bits  = flags & (  (FLAG_INT(1) << split_bit)-1);
    high_bits <<= shift; 
    flags = high_bits | low_bits;
    simv->flags = flags;
  }

  return(simv);
}

/****************************************************************\
|
| Function name:cdi_destroy_simv
|
| Purpose:destroys a simv 
|
\****************************************************************/
VOID
cdi_destroy_simv(
                 CDI_SIMV simv        /* the simv to destroy */
                 )
{
  exa_free(simv);
}

/****************************************************************\
|
| Function name:cdi_write_thma
|
| Purpose:writes thma (thermal acceleration)
|
\****************************************************************/
VOID
cdi_write_thma(
               CDI_INFO cdi_info, /* the info structure */
               CDI_THMA thma /* the thma structure */
               )
{
  thma->cccc = CDI_CHUNK_TYPE_THMA;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_THMA);

  cdi_write_asINT32(cdi_info, &thma->start_time, 1);
  cdi_write_asINT32(cdi_info, &thma->end_time, 1);
  cdi_write_asINT32(cdi_info, &thma->period, 1);
  cdi_write_asINT32(cdi_info, &thma->interval, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_thma
|
| Purpose:reads thma
|
\****************************************************************/
CDI_THMA
cdi_read_thma(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_THMA thma = EXA_CALLOC_STRUCT(CDI_THMA); /* the thma structure */

  thma->cccc = CDI_CHUNK_TYPE_THMA;
  
  cdi_read_asINT32(cdi_info, &thma->start_time, 1);
  cdi_read_asINT32(cdi_info, &thma->end_time, 1);
  cdi_read_asINT32(cdi_info, &thma->period, 1);
  cdi_read_asINT32(cdi_info, &thma->interval, 1);

  return(thma);
}

/****************************************************************\
|
| Function name:cdi_destroy_thma
|
| Purpose:destroys a thma 
|
\****************************************************************/
VOID
cdi_destroy_thma(
                 CDI_THMA thma        /* the thma to destroy */
                 )
{
  exa_free(thma);
}

/****************************************************************\
|
| Function name:cdi_write_csph
|
| Purpose:writes csph (coupling solver phase)
|
\****************************************************************/
VOID
cdi_write_csph( CDI_INFO cdi_info, /* the info structure */
                CDI_CSPH csph /* the csph structure */
)
{
  csph->cccc = CDI_CHUNK_TYPE_CSPH;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_CSPH);

  cdiINT32 tempOpt = csph->time_rel_solver;
  cdi_write_asINT32(cdi_info, &tempOpt, 1);
  cdi_write_asINT32(cdi_info, &csph->start, 1);
  tempOpt = csph->time_coupling;
  cdi_write_asINT32(cdi_info, &tempOpt, 1);
  cdi_write_idFLOAT(cdi_info, &csph->therm_time_ratio, 1);
  cdi_write_asINT32(cdi_info, &csph->flow_duration, 1);
  cdi_write_asINT32(cdi_info, &csph->flow_avg_interval, 1);
  cdi_write_asINT32(cdi_info, &csph->conduction_duration, 1);
  cdi_write_asINT32(cdi_info, &csph->conduction_avg_interval, 1);
  tempOpt = csph->frozen_solver;
  cdi_write_asINT32(cdi_info, &tempOpt, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 7)) {
    // Got rid of radiation_upate_start_time
    if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
      cdiINT32 old_update_start_time = -1;
      cdi_write_asINT32(cdi_info, &old_update_start_time, 1);
    }
    cdi_write_idFLOAT(cdi_info, &csph->radiation_update_ratio, 1);
  }

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_csph
|
| Purpose:reads csph
|
\****************************************************************/
CDI_CSPH
cdi_read_csph( CDI_INFO cdi_info)
{
  CDI_CSPH csph = EXA_CALLOC_STRUCT(CDI_CSPH);

  csph->cccc = CDI_CHUNK_TYPE_CSPH;
  cdiINT32 tempOpt = -1;
  cdi_read_asINT32(cdi_info, &tempOpt, 1);
  csph->time_rel_solver = static_cast<eCDI_COUPLED_SOLVER::Enum>(tempOpt);
  cdi_read_asINT32(cdi_info, &csph->start, 1);
  tempOpt = -1;
  cdi_read_asINT32(cdi_info, &tempOpt, 1);
  csph->time_coupling = static_cast<eCDI_TIME_COUPLING_SCHEME::Enum>(tempOpt);
  cdi_read_idFLOAT(cdi_info, &(csph->therm_time_ratio), 1);
  cdi_read_asINT32(cdi_info, &csph->flow_duration, 1);
  cdi_read_asINT32(cdi_info, &csph->flow_avg_interval, 1);
  cdi_read_asINT32(cdi_info, &csph->conduction_duration, 1);
  cdi_read_asINT32(cdi_info, &csph->conduction_avg_interval, 1);
  tempOpt = -1;
  cdi_read_asINT32(cdi_info, &tempOpt, 1);
  csph->frozen_solver = static_cast<eCDI_COUPLED_SOLVER::Enum>(tempOpt);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 7)) {
    if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
      cdiINT32 old_radiation_update_start_time;
      cdi_read_asINT32(cdi_info, &(old_radiation_update_start_time), 1);
    }
    cdi_read_idFLOAT(cdi_info, &(csph->radiation_update_ratio), 1);
  }

  return(csph);
}

/****************************************************************\
|
| Function name:cdi_destroy_csph
|
| Purpose:destroys a csph
|
\****************************************************************/
VOID
cdi_destroy_csph(CDI_CSPH csph)
{
  exa_free(csph);
}

/****************************************************************\
|
| Function name: cdi_write_symp
|
| Purpose: writes symp (symmetry plane(s))
|
\****************************************************************/
VOID cdi_write_symp (CDI_INFO cdi_info, /* the info structure */
                     CDI_SYMP symp /* the symp structure */ )
{
  symp->cccc = CDI_CHUNK_TYPE_SYMP;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_SYMP);

  cdi_write_asINT32(cdi_info, &symp->axes, 1);

  DOTIMES(j, 3, {cdi_write_asINT32(cdi_info, symp->min + j, 1);});

  DOTIMES(j, 3, {cdi_write_asINT32(cdi_info, symp->max + j, 1);});

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_symp
|
| Purpose:reads symp
|
\****************************************************************/

CDI_SYMP cdi_read_symp (CDI_INFO cdi_info /* the info structure */)
{
  CDI_SYMP symp = EXA_CALLOC_STRUCT(CDI_SYMP); /* the symp structure */

  symp->cccc = CDI_CHUNK_TYPE_SYMP;

  cdi_read_asINT32(cdi_info, &symp->axes, 1);

  DOTIMES(j, 3, {cdi_read_asINT32(cdi_info, symp->min + j, 1);});

  DOTIMES(j, 3, {cdi_read_asINT32(cdi_info, symp->max + j, 1);});

  return(symp);
}

/****************************************************************\
|
| Function name:cdi_destroy_symp
|
| Purpose:destroys a symp 
|
\****************************************************************/
VOID cdi_destroy_symp (CDI_SYMP symp        /* the symp to destroy */)
{
  exa_free(symp);
}

/****************************************************************\
|
| Function name:cdi_write_unit
|
| Purpose:writes unit (lattice units)
|
\****************************************************************/
VOID
cdi_write_unit(
               CDI_INFO cdi_info, /* the info structure */
               CDI_UNIT unit /* the unit structure */
               )
{
  unit->cccc = CDI_CHUNK_TYPE_UNIT;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_UNIT);

  cdi_write_idFLOAT(cdi_info, &unit->kilos_per_particle, 1);
  cdi_write_idFLOAT(cdi_info, &unit->meters_per_cell, 1);
  cdi_write_idFLOAT(cdi_info, &unit->seconds_per_timestep, 1);
  cdi_write_idFLOAT(cdi_info, &unit->kelvins_per_lattice_temp, 1);
  cdi_write_asINT32(cdi_info, &unit->n_user_units, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_unit
|
| Purpose:reads unit
|
\****************************************************************/
CDI_UNIT
cdi_read_unit(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_UNIT unit = EXA_CALLOC_STRUCT(CDI_UNIT); /* the unit structure */

  unit->cccc = CDI_CHUNK_TYPE_UNIT;
  
  cdi_read_idFLOAT(cdi_info, &unit->kilos_per_particle, 1);
  cdi_read_idFLOAT(cdi_info, &unit->meters_per_cell, 1);
  cdi_read_idFLOAT(cdi_info, &unit->seconds_per_timestep, 1);
  cdi_read_idFLOAT(cdi_info, &unit->kelvins_per_lattice_temp, 1);
  cdi_read_asINT32(cdi_info, &unit->n_user_units, 1);

  return(unit);
}

/****************************************************************\
|
| Function name:cdi_destroy_unit
|
| Purpose:destroys a unit 
|
\****************************************************************/
VOID
cdi_destroy_unit(
                 CDI_UNIT unit        /* the unit to destroy */
                 )
{
  exa_free(unit);
}

/****************************************************************\
|
| Function name:cdi_write_uunt
|
| Purpose:writes uunt (lattice uunts)
|
\****************************************************************/
VOID
cdi_write_uunt(
               CDI_INFO cdi_info, /* the info structure */
               CDI_UUNT uunt /* the uunt structure */
               )
{
  uunt->cccc = CDI_CHUNK_TYPE_UUNT;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_UUNT);

  {
    asINT32 unit_type = (CDI_UNIT_TYPES) uunt->unit_type;
    cdi_write_asINT32(cdi_info, &unit_type, 1);
  }
  cdi_write_asINT32(cdi_info, &uunt->mass_exponent, 1);
  cdi_write_asINT32(cdi_info, &uunt->length_exponent, 1);
  cdi_write_asINT32(cdi_info, &uunt->time_exponent, 1);
  cdi_write_asINT32(cdi_info, &uunt->temp_exponent, 1);
  cdi_write_idFLOAT(cdi_info, &uunt->scale, 1);
  cdi_write_idFLOAT(cdi_info, &uunt->offset, 1);
  cdi_write_cdichars(cdi_info, uunt->unit_name, uunt->n_char);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_uunt
|
| Purpose:reads uunt
|
\****************************************************************/
CDI_UUNT
cdi_read_uunt(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_UUNT uunt = EXA_CALLOC_STRUCT(CDI_UUNT); /* the uunt structure */

  uunt->cccc = CDI_CHUNK_TYPE_UUNT;
  
  {
    asINT32 unit_type;
    cdi_read_asINT32(cdi_info, &unit_type, 1);
    uunt->unit_type = (CDI_UNIT_TYPES) unit_type;
  }

  cdi_read_asINT32(cdi_info, &uunt->mass_exponent, 1);
  cdi_read_asINT32(cdi_info, &uunt->length_exponent, 1);
  cdi_read_asINT32(cdi_info, &uunt->time_exponent, 1);
  cdi_read_asINT32(cdi_info, &uunt->temp_exponent, 1);
  cdi_read_idFLOAT(cdi_info, &uunt->scale, 1);
  cdi_read_idFLOAT(cdi_info, &uunt->offset, 1);
  uunt->unit_name = cdi_read_cdichars(cdi_info, NULL, &uunt->n_char);

  return(uunt);
}

/****************************************************************\
|
| Function name:cdi_destroy_uunt
|
| Purpose:destroys a uunt 
|
\****************************************************************/
VOID
cdi_destroy_uunt(
                 CDI_UUNT uunt        /* the uunt to destroy */
                 )
{
  exa_free(uunt->unit_name);
  exa_free(uunt);
}

/****************************************************************\
|
| Function name:cdi_write_cprp
|
| Purpose:writes cprp (Characteristic property)
|
\****************************************************************/
VOID
cdi_write_cprp(
               CDI_INFO cdi_info, /* the info structure */
               CDI_CPRP cprp /* the cprp structure */
               )
{
  cprp->cccc = CDI_CHUNK_TYPE_CPRP;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_CPRP);

  cdi_write_asINT32(cdi_info, &cprp->mass_exponent, 1);
  cdi_write_asINT32(cdi_info, &cprp->length_exponent, 1);
  cdi_write_asINT32(cdi_info, &cprp->time_exponent, 1);
  cdi_write_asINT32(cdi_info, &cprp->temp_exponent, 1);
  cdi_write_cdichars(cdi_info, cprp->char_prop_name, cprp->n_char);
  cdi_write_idFLOAT(cdi_info, &cprp->value, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_cprp
|
| Purpose:reads cprp
|
\****************************************************************/
CDI_CPRP
cdi_read_cprp(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_CPRP cprp = EXA_CALLOC_STRUCT(CDI_CPRP); /* the cprp structure */

  cprp->cccc = CDI_CHUNK_TYPE_CPRP;
  
  cdi_read_asINT32(cdi_info, &cprp->mass_exponent, 1);
  cdi_read_asINT32(cdi_info, &cprp->length_exponent, 1);
  cdi_read_asINT32(cdi_info, &cprp->time_exponent, 1);
  cdi_read_asINT32(cdi_info, &cprp->temp_exponent, 1);
  cprp->char_prop_name = cdi_read_cdichars(cdi_info, NULL, &cprp->n_char);
  cdi_read_idFLOAT(cdi_info, &cprp->value, 1);

  return(cprp);
}

/****************************************************************\
|
| Function name:cdi_destroy_cprp
|
| Purpose:destroys a cprp 
|
\****************************************************************/
VOID
cdi_destroy_cprp(
                 CDI_CPRP cprp        /* the cprp to destroy */
                 )
{
  exa_free(cprp->char_prop_name);
  exa_free(cprp);
}

/****************************************************************\
|
| Function name:cdi_write_ghdr
|
| Purpose:writes ghdr (global header)
|
\****************************************************************/
VOID
cdi_write_ghdr(
               CDI_INFO cdi_info, /* the info structure */
               CDI_GHDR ghdr /* the ghdr structure */
               )
{
  ghdr->cccc = CDI_CHUNK_TYPE_GHDR;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_GHDR);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 11)) { // Total simulation time options
    asINT32 duration_via_as_int = static_cast<asINT32>(ghdr->sim_duration_via);
    cdi_write_asINT32(cdi_info, &duration_via_as_int, 1);
    cdiINT32 numMonitors = ghdr->monitors.size();
    cdi_write_asINT32(cdi_info, &numMonitors, 1);
    if (numMonitors > 0) {
      cdiINT32* monitorsArray = &(ghdr->monitors[0]);
      cdi_write_asINT32(cdi_info, monitorsArray, numMonitors);
    }
    cdi_write_asINT32(cdi_info, &ghdr->n_timesteps_after_init_trans, 1);
  }
  cdi_write_asINT32(cdi_info, &ghdr->n_timesteps, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 1)) {
    cdiINT32 tempOpt = ghdr->timesteps_rel_solver;
    cdi_write_asINT32(cdi_info, &tempOpt, 1);
  }
  cdi_write_asINT32(cdi_info, &ghdr->n_processors, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 10)) { // liquid material type first introduced in 4.10
    cdi_write_asINT32(cdi_info, &ghdr->liquidMaterialType, 1);
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 1)) { // Average mme meas window first introduced in 6.1
    cdi_write_asINT32(cdi_info, &ghdr->has_average_mme_window, 1);
    cdi_write_asINT32(cdi_info, &ghdr->local_vel_freeze, 1);
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 27))
    cdi_write_asINT32(cdi_info, &ghdr->m_temperatureDependentGamma, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 28)) {
    cdiINT32 tempOpt = ghdr->m_coolingAirOpt;
    cdi_write_asINT32(cdi_info, &tempOpt, 1);
  }

  if (ghdr->include_second_block) {
    int freezeMomentumField = ghdr->freeze_momentum_field;
    cdi_write_asINT32(cdi_info, &freezeMomentumField, 1);
    cdi_write_idFLOAT(cdi_info, &ghdr->physical_time_scaling, 1);
  }

  if (ghdr->include_third_block) {
    cdi_write_asINT32(cdi_info, &ghdr->numParticleEmitters, 1);
    cdi_write_asINT32(cdi_info, &ghdr->numParticleMaterials, 1);
    cdi_write_asINT32(cdi_info, &ghdr->numParticleModelingVars, 1);
    cdi_write_asINT32(cdi_info, &ghdr->numUDScalars, 1);
    cdi_write_asINT32(cdi_info, &ghdr->numUDSVars, 1);
    cdi_write_asINT32(cdi_info, &ghdr->num5gFluidComponents, 1);
    cdi_write_asINT32(cdi_info, &ghdr->num5gVars, 1);
  }

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_ghdr
|
| Purpose:reads ghdr
|
\****************************************************************/
CDI_GHDR cdi_read_ghdr(CDI_INFO cdi_info) /* the info structure */
{
  CDI_GHDR ghdr = new sCDI_GHDR();  // Get the default initialization
  ghdr->include_second_block = false;
  ghdr->include_third_block = false;

  uINT64 bytesRead = 0;
  
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 11)) { // Total simulation time options
    cdiINT32 enumInt = -1;
    cdi_read_asINT32(cdi_info, &enumInt, 1, bytesRead);
    ghdr->sim_duration_via = static_cast<eCDI_SIM_DURATION_VIA::Enum>(enumInt);
    cdiINT32 numMonitors = 0;
    cdi_read_asINT32(cdi_info, &numMonitors, 1, bytesRead);
    ghdr->monitors.resize(numMonitors);
    for (int im = 0; im < numMonitors; im++) {
      cdi_read_asINT32(cdi_info, &(ghdr->monitors[im]), 1, bytesRead);
    }
    cdi_read_asINT32(cdi_info, &ghdr->n_timesteps_after_init_trans, 1, bytesRead);
  }

  cdi_read_asINT32(cdi_info, &ghdr->n_timesteps, 1, bytesRead);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 1)) {
    cdiINT32 tempOpt = -1;
    cdi_read_asINT32(cdi_info, &tempOpt, 1, bytesRead);
    ghdr->timesteps_rel_solver = static_cast<eCDI_COUPLED_SOLVER::Enum>(tempOpt);
  }
  cdi_read_asINT32(cdi_info, &ghdr->n_processors, 1, bytesRead);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 10)) { // liquid material type first introduced in 4.10
    cdi_read_asINT32(cdi_info, &ghdr->liquidMaterialType, 1, bytesRead);
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 1)) { // Average mme meas window first introduced in 6.1
    cdi_read_asINT32(cdi_info, &ghdr->has_average_mme_window, 1, bytesRead);
    cdi_read_asINT32(cdi_info, &ghdr->local_vel_freeze, 1, bytesRead);
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 27))
    cdi_read_asINT32(cdi_info, &ghdr->m_temperatureDependentGamma, 1, bytesRead);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 28)) {
    cdiINT32 tempOpt = -1;
    cdi_read_asINT32(cdi_info, &tempOpt, 1, bytesRead);
    ghdr->m_coolingAirOpt = static_cast<eCDI_COOLING_AIR_LEAKAGE_OPTION::Enum>(tempOpt);
  }

  // If we haven't read all the info in the chunk, there must be a second block
  // of parameters. When Momentum Freeze Option was introduced to this chunk we
  // added it here without bumping the CDI version number (so as to not break
  // compatibility).  So if there's more to read, it must be Momentum Freeze
  // Option.
  if (bytesRead < cio_get_size(cdi_info->cio_info)) {
    int freezeMomentumField;
    cdi_read_asINT32(cdi_info, &freezeMomentumField, 1, bytesRead);
    ghdr->freeze_momentum_field = freezeMomentumField;
    cdi_read_idFLOAT(cdi_info, &ghdr->physical_time_scaling, 1, bytesRead);
    ghdr->include_second_block = true;
  }

  // If we haven't read all the info in the chunk, there must be a third block
  // of parameters.
  if (bytesRead < cio_get_size(cdi_info->cio_info)) {
    cdi_read_asINT32(cdi_info, &ghdr->numParticleEmitters, 1, bytesRead);
    cdi_read_asINT32(cdi_info, &ghdr->numParticleMaterials, 1, bytesRead);
    cdi_read_asINT32(cdi_info, &ghdr->numParticleModelingVars, 1, bytesRead);
    cdi_read_asINT32(cdi_info, &ghdr->numUDScalars, 1, bytesRead);
    cdi_read_asINT32(cdi_info, &ghdr->numUDSVars, 1, bytesRead);
    cdi_read_asINT32(cdi_info, &ghdr->num5gFluidComponents, 1, bytesRead);
    cdi_read_asINT32(cdi_info, &ghdr->num5gVars, 1, bytesRead);
    ghdr->include_third_block = true;
  }

  return(ghdr);
}

/****************************************************************\
|
| Function name:cdi_destroy_ghdr
|
| Purpose:destroys a ghdr 
|
\****************************************************************/
VOID cdi_destroy_ghdr(CDI_GHDR ghdr) //the ghdr to destroy
{
  delete ghdr;
}

/****************************************************************\
|
| Function name:cdi_write_cpnt
|
| Purpose:writes cpnt (checkpoint)
|
\****************************************************************/
VOID
cdi_write_cpnt(
               CDI_INFO cdi_info, /* the info structure */
               CDI_CPNT cpnt /* the cpnt structure */
               )
{
  cpnt->cccc = CDI_CHUNK_TYPE_CPNT;
  
  cdi_push(cdi_info, CDI_CHUNK_TYPE_CPNT);

  cdi_write_asINT32(cdi_info, &cpnt->start, 1);
  cdi_write_asINT32(cdi_info, &cpnt->end, 1);
  cdi_write_asINT32(cdi_info, &cpnt->freq, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_cpnt
|
| Purpose:reads cpnt
|
\****************************************************************/
CDI_CPNT
cdi_read_cpnt(
              CDI_INFO cdi_info /* the info structure */
              )
{
  CDI_CPNT cpnt = EXA_CALLOC_STRUCT(CDI_CPNT); /* the cpnt structure */

  cpnt->cccc = CDI_CHUNK_TYPE_CPNT;
  
  cdi_read_asINT32(cdi_info, &cpnt->start, 1);
  cdi_read_asINT32(cdi_info, &cpnt->end, 1);
  cdi_read_asINT32(cdi_info, &cpnt->freq, 1);

  return(cpnt);
}

/****************************************************************\
|
| Function name:cdi_destroy_cpnt
|
| Purpose:destroys a cpnt 
|
\****************************************************************/
VOID
cdi_destroy_cpnt(
                 CDI_CPNT cpnt        /* the cpnt to destroy */
                 )
{
  exa_free(cpnt);
}


/****************************************************************\
|
| Function name:cdi_write_prbe
|
| Purpose:writes prbe (probe definition)
|
\****************************************************************/
VOID
cdi_write_prbe(
               CDI_INFO cdi_info,
               CDI_PRBE prbe
               )
{
    prbe->cccc = CDI_CHUNK_TYPE_PRBE;

    cdi_push(cdi_info, CDI_CHUNK_TYPE_PRBE);

    cdi_write_idFLOAT(cdi_info, &prbe->x, 1);
    cdi_write_idFLOAT(cdi_info, &prbe->y, 1);
    cdi_write_idFLOAT(cdi_info, &prbe->z, 1);
    cdi_write_idFLOAT(cdi_info, &prbe->diam, 1);

    cdi_pop(cdi_info);
}


/****************************************************************\
|
| Function name:cdi_read_prbe
|
| Purpose:reads prbe (probe definition)
|
\****************************************************************/
CDI_PRBE
cdi_read_prbe(
              CDI_INFO cdi_info 
              )
{
    CDI_PRBE prbe = EXA_CALLOC_STRUCT(CDI_PRBE);

    prbe->cccc = CDI_CHUNK_TYPE_PRBE;
    cdi_read_idFLOAT(cdi_info, &prbe->x, 1);
    cdi_read_idFLOAT(cdi_info, &prbe->y, 1);
    cdi_read_idFLOAT(cdi_info, &prbe->z, 1);
    cdi_read_idFLOAT(cdi_info, &prbe->diam, 1);

    return prbe;
}


/****************************************************************\
|
| Function name:cdi_destroy_prbe
|
| Purpose:destroys a prbe (probe definition)
|
\****************************************************************/
VOID
cdi_destroy_prbe(
                 CDI_PRBE prbe
                 )
{
    exa_free(prbe);
}

void cdi_write_prtj(CDI_INFO info, sCDI_PRTJ* prtj)
{
  prtj->cccc = CDI_CHUNK_TYPE_PRTJ;
  cdi_push(info, CDI_CHUNK_TYPE_PRTJ);
  cdi_write_parm(info, &(prtj->trajectories));
  cdi_write_parm(info, &(prtj->recordVel));
  cdi_write_parm(info, &(prtj->dynTrajDecimation));
  cdi_write_parm(info, &(prtj->decimErrortol));
  cdi_write_parm(info, &(prtj->hitPoints));
  cdi_write_parm(info, &(prtj->adherParticles));
  cdi_write_parm(info, &(prtj->recordNormImpulse));
  if (CDI_INFO_VERSION_AT_LEAST(info, 5, 3))
    cdi_write_parm(info, &(prtj->frac_trajectory_recording));
  cdi_pop(info);
}

sCDI_PRTJ* cdi_read_prtj(CDI_INFO info)
{
  //TODO: prtj is deleted later in 'cdi_destroy_mesr',
  //      this needs to be handled in a smart pointer way
  sCDI_PRTJ* prtj = new sCDI_PRTJ();
  cdiINT32 index=0;
  prtj->cccc = CDI_CHUNK_TYPE_PRTJ;
  const char *chunkName = "prtj";
  cdi_inner_chunk_read_parm(index, chunkName, info, &(prtj->trajectories));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(prtj->recordVel));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(prtj->dynTrajDecimation));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(prtj->decimErrortol));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(prtj->hitPoints));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(prtj->adherParticles));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(prtj->recordNormImpulse));
  if (CDI_INFO_VERSION_AT_LEAST(info, 5, 3))
    cdi_inner_chunk_read_parm(index, chunkName, info, &(prtj->frac_trajectory_recording));
  else {
    prtj->frac_trajectory_recording.value = 0.001;
    prtj->frac_trajectory_recording.preferredUnit = "dimensionless";
    prtj->frac_trajectory_recording.defaulted = true;
  }
  return prtj;
}

void cdi_destroy_prtj(sCDI_PRTJ* prtj)
{
  delete prtj;
}


/****************************************************************\
|
| Function name:cdi_write_ment
|
| Purpose:writes ment (measurement entity)
|
\****************************************************************/
VOID
cdi_write_ment(
               CDI_INFO cdi_info,
               CDI_MENT ment
               )
{
    ment->cccc = CDI_CHUNK_TYPE_MENT;

    cdi_push(cdi_info, CDI_CHUNK_TYPE_MENT);

    cdi_write_asINT32(cdi_info, &ment->options, 1);

    cdi_pop(cdi_info);
}


/****************************************************************\
|
| Function name:cdi_read_ment
|
| Purpose:reads ment (measurement entity)
|
\****************************************************************/
CDI_MENT
cdi_read_ment(
              CDI_INFO cdi_info
              )
{
    CDI_MENT ment = EXA_CALLOC_STRUCT(CDI_MENT);

    ment->cccc = CDI_CHUNK_TYPE_MENT;
    cdi_read_asINT32(cdi_info, &ment->options, 1);

    return ment;
}


/****************************************************************\
|
| Function name:cdi_destroy_ment
|
| Purpose:destroys a ment (measurement entity)
|
\****************************************************************/
VOID
cdi_destroy_ment(
                 CDI_MENT ment
                 )
{
    exa_free(ment);
}


/****************************************************************\
|
| Function name:cdi_write_flst
|
| Purpose:writes flst (face list)
|
\****************************************************************/
VOID
cdi_write_flst(CDI_INFO cdi_info, const sCDI_FLST* flst)
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_FLST);
  cdi_write_asINT32(cdi_info, &flst->n_face, 1);
  DOTIMES(j, flst->n_face, {cdi_write_asINT32(cdi_info, flst->face + j, 1);});
  cdi_pop(cdi_info);
}


/****************************************************************\
|
| Function name:cdi_read_flst
|
| Purpose:reads flst (face list)
|
\****************************************************************/
CDI_FLST
cdi_read_flst(CDI_INFO cdi_info)
{
  CDI_FLST flst = EXA_CALLOC_STRUCT(CDI_FLST);
  flst->cccc = CDI_CHUNK_TYPE_FLST;
  cdi_read_asINT32(cdi_info, &flst->n_face, 1);
  flst->face = EXA_CALLOC_ARRAY(asINT32, flst->n_face);
  DOTIMES(j, flst->n_face, {cdi_read_asINT32(cdi_info, flst->face + j, 1);});
  return flst;
}


/****************************************************************\
|
| Function name:cdi_destroy_flst
|
| Purpose:destroys a flst (face list)
|
\****************************************************************/
VOID
cdi_destroy_flst(CDI_FLST flst)
{
  exa_free(flst->face);
  exa_free(flst);
}


static sCDI_FLST*
cdi_create_flst_from_int_vector(const std::vector<cdiINT32>& face_vector)
{
  sCDI_FLST* flst = EXA_CALLOC_STRUCT(CDI_FLST);
  flst->cccc = CDI_CHUNK_TYPE_FLST;
  flst->n_face = face_vector.size();
  flst->face = EXA_CALLOC_ARRAY(asINT32, flst->n_face);
  std::copy(face_vector.begin(), face_vector.end(), flst->face);
  return flst;
}


// cdi_write_flst is used to write a FLST to CDI_INFO from a vector
void
cdi_write_flst(CDI_INFO info, const std::vector<cdiINT32>& values)
{
  sCDI_FLST* flst = cdi_create_flst_from_int_vector(values);
  cdi_write_flst(info, flst);
  cdi_destroy_flst(flst);
}


/****************************************************************\
|
| Function name:cdi_inner_chunk_read_flst and
|               cdi_read_flst_from_vector
| Purpose:read/write operation for FLST in vector form
|
\****************************************************************/
// cdi_inner_chunk_read_flst is used to read a FLST from CDI_INFO to a vector
void
cdi_inner_chunk_read_flst(cdiINT32 &index, const char* chunkName,
                          CDI_INFO info, std::vector<cdiINT32> &values)
{
  if (cdi_cio_descend_with_error(info->cio_info, chunkName) == 0) {
    CDI_FLST flst = cdi_read_flst(info);
    values.assign(flst->face, flst->face + flst->n_face);
    cdi_destroy_flst(flst);
  }
  cdi_cio_ascend_with_error(info->cio_info, chunkName, ++index);
}

/****************************************************************\
|
| Function name:cdi_read_geometry_selection
| 
| Purpose: support to read old and new cdi files into a
|          cCDI_GEOMETRY_REF or cCDI_GEOM_SELECTION_TREE structure
|
\****************************************************************/

bool cdi_is_valid_geometry_chunk(CDI_INFO cdi_info)
{
  CIO_CCCC chunkType = cdi_get_type(cdi_info);
  return (chunkType == CDI_CHUNK_TYPE_RGNS ||
          chunkType == CDI_CHUNK_TYPE_FLST ||
          chunkType == cCDI_GEOMETRY_REF::GetGeometryRefChunkType() ||
          chunkType == cCDI_GEOM_SELECTION_TREE::GetGeometrySelectionChunkType());
}

bool cdi_read_geometry_selection(CDI_INFO cdi_info, cCDI_GEOMETRY_REF &gmrf)
{
  bool wasRead = true;
  CIO_CCCC chunkType = cdi_get_type(cdi_info);
  if (chunkType == CDI_CHUNK_TYPE_RGNS) {
    CDI_RGNS rgns = cdi_read_rgns(cdi_info);
    gmrf.rgn_list.assign(rgns->region, rgns->region + rgns->n_region);
    gmrf.GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
    cdi_destroy_rgns(rgns);
  }
  else if (chunkType == CDI_CHUNK_TYPE_FLST) {
    CDI_FLST flst = cdi_read_flst(cdi_info);
    gmrf.face_list.assign(flst->face, flst->face + flst->n_face);
    gmrf.GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
    cdi_destroy_flst(flst);
  }
  else if (chunkType == cCDI_GEOMETRY_REF::GetGeometryRefChunkType()) {
    cCDI_GEOMETRY_REF* read_gmrf = cdi_read_gmrf(cdi_info);
    gmrf = *read_gmrf;
    cdi_destroy_gmrf(read_gmrf);
  }
  else {
    wasRead = false;
  }
  return wasRead;
}

bool cdi_read_geometry_selection(CDI_INFO cdi_info, cCDI_GEOM_SELECTION_TREE &geos)
{
  bool wasRead = true;
  CIO_CCCC chunkType = cdi_get_type(cdi_info);
  if (chunkType == CDI_CHUNK_TYPE_RGNS) {
    CDI_RGNS rgns = cdi_read_rgns(cdi_info);
    geos.m_selections.rgn_list.assign(rgns->region, rgns->region + rgns->n_region);
    geos.m_selections.GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
    geos.m_exclusions.GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
    cdi_destroy_rgns(rgns);
  }
  else if (chunkType == CDI_CHUNK_TYPE_FLST) {
    CDI_FLST flst = cdi_read_flst(cdi_info);
    geos.m_selections.face_list.assign(flst->face, flst->face + flst->n_face);
    geos.m_selections.GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
    geos.m_exclusions.GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
    cdi_destroy_flst(flst);
  }
  else if (chunkType == cCDI_GEOM_SELECTION_TREE::GetGeometrySelectionChunkType()) {
    cCDI_GEOM_SELECTION_TREE* read_geos = cdi_read_geos(cdi_info);
    geos = *read_geos;
    cdi_destroy_geos(read_geos);
  }
  else {
    wasRead = false;
  }
  return wasRead;
}

std::vector<cdiINT32> cdi_get_effective_selections(CDI_INFO cdi_info, const cCDI_PARTITIONS& partitions)
{
  std::vector<cdiINT32> retVector;
  cCDI_GEOM_SELECTION_TREE geos(0, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
  cCDI_GEOMETRY_REF gmrf(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
  if (cdi_read_geometry_selection(cdi_info, gmrf))
    retVector = gmrf.ExpandSelection(partitions);
  else if (cdi_read_geometry_selection(cdi_info, geos))
    retVector = geos.ExpandSelection(partitions);
  return retVector;
}

/****************************************************************\
|
| Geometry References (cCDI_GEOMETRY_REF)
|
\****************************************************************/

std::vector<cdiINT32>
cCDI_GEOMETRY_REF::ExpandSelection(const cCDI_PARTITIONS& partitions, bool recursive) const
{
  return ExpandSelection(partitions, m_geometrySelectionType, recursive);
}

std::vector<cdiINT32>
cCDI_GEOMETRY_REF::ExpandSelection(const cCDI_PARTITIONS& partitions, eGEOMETRY_TYPE geomSelectionType, bool recursive) const
{
  std::vector<cdiINT32> selections;
  std::stack<cCDI_SEGMENT*> segStack;
  // Need to "reconstruct" any partial parts
  std::map<cdiINT32, cdiINT32> partialPartFaceSumMap;
  for (cCDI_SEGMENT_REF segRef : segment_list) {
    cCDI_SEGMENT* cdiSeg = segRef.GetSegment(partitions);
    const cCDI_PARTITION& selectionPartition = segRef.GetPartition(partitions);
    if (cdiSeg)
      segStack.push(cdiSeg);
    while (!segStack.empty()) {
      cCDI_SEGMENT* curSeg = segStack.top();
      segStack.pop();
      if (geomSelectionType == eGEOMETRY_TYPE::Segment) {
        cdiINT32 segmentIndex = selectionPartition.GetSegmentIndex(*curSeg);
        selections.push_back(segmentIndex);
      }
      else if (geomSelectionType == eGEOMETRY_TYPE::PartialPart) {
        for (cCDI_PARTIAL_PART* partialPart : curSeg->GetPartialParts()) {
          cdiINT32 partialPartIndex = selectionPartition.GetPartialPartIndex(*partialPart);
          selections.push_back(partialPartIndex);
        }
      }
      else if (geomSelectionType == eGEOMETRY_TYPE::Part) {
        std::vector<cdiINT32> parts = curSeg->GetRegionIndices(false);
        selections.insert(selections.end(), parts.begin(), parts.end());
        // Need to check if any partial parts "add up" to a complete part in this reference
        for (cCDI_PARTIAL_PART* partialPart : curSeg->GetPartialParts()) {
          partialPartFaceSumMap[partialPart->GetPartIndex()] += partialPart->GetFaceIndices().size();
        }
      }
      else if (geomSelectionType == eGEOMETRY_TYPE::Face) {
        std::vector<cdiINT32> faces = curSeg->GetFaceIndices(false);
        selections.insert(selections.end(), faces.begin(), faces.end());
      }
      if (recursive) {
        for (cCDI_SEGMENT* child : curSeg->GetChildSegments())
          segStack.push(child);
      }
    }
  }
  if (geomSelectionType == eGEOMETRY_TYPE::Face ||
      geomSelectionType == eGEOMETRY_TYPE::PartialPart ||
      geomSelectionType == eGEOMETRY_TYPE::Part) {
    for (cCDI_PARTIAL_PART_REF partialPartRef : partial_part_list) {
      if (geomSelectionType == eGEOMETRY_TYPE::PartialPart) {
        selections.push_back(partialPartRef.m_partIndex);
      }
      else if (geomSelectionType == eGEOMETRY_TYPE::Face) {
        const cCDI_PARTIAL_PART& partialPart = partialPartRef.GetPartialPart(partitions);
        for (auto faceIndex : partialPart.GetFaceIndices()) {
          selections.push_back(faceIndex);
        }
      }
      else if (geomSelectionType == eGEOMETRY_TYPE::Part) {
        const cCDI_PARTIAL_PART& partialPart = partialPartRef.GetPartialPart(partitions);
        partialPartFaceSumMap[partialPart.GetPartIndex()] += partialPart.GetFaceIndices().size();
      }
    }
  }
  if (geomSelectionType == eGEOMETRY_TYPE::Part ||
      geomSelectionType == eGEOMETRY_TYPE::Face) {
    for (cdiINT32 partIndex : rgn_list) {
      if (geomSelectionType == eGEOMETRY_TYPE::Part) {
        selections.push_back(partIndex);
      }
      else if (geomSelectionType == eGEOMETRY_TYPE::Face) {
        std::vector<cdiINT32> faces = partitions.GetPartFaceList(partIndex);
        selections.insert(selections.end(), faces.begin(), faces.end());
      }
    }
  }
  if (geomSelectionType == eGEOMETRY_TYPE::Face) {
    selections.insert(selections.end(), face_list.begin(), face_list.end());
  }
  // Check partial parts
  if (geomSelectionType == eGEOMETRY_TYPE::Part) {
    for (auto mapItem : partialPartFaceSumMap) {
      cdiINT32 partIndex = mapItem.first;
      if (partitions.GetPartFaceList(partIndex).size() == mapItem.second)
        selections.push_back(partIndex);
    }
  }

  std::sort(selections.begin(), selections.end());
  selections.erase(std::unique(selections.begin(), selections.end()), selections.end());
  // Check for any prohibited selections
  if (prohibited_selections.empty())
    return selections;
  std::vector<cdiINT32> filteredSelections;
  for (cdiINT32 select : selections) {
    if (prohibited_selections.find(select) == prohibited_selections.end())
      filteredSelections.push_back(select);
  }

  return filteredSelections;
}

bool
cCDI_GEOMETRY_REF::ReadFromCDI(CDI_INFO cdi_info)
{
  cdiINT32 index = 0;
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);
  // Type of selection
  cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &m_geometrySelectionType);
  // Face references
  cdi_inner_chunk_read_flst(index, chunkName, cdi_info, face_list);
  // Region/Part references
  cdi_inner_chunk_read_rgns(index, chunkName, cdi_info, rgn_list);
  // Partial Part references
  CDI_WITH_INNER_CHUNK(cdi_info) {
    if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_PPRL) {
      cdi_read_partial_part_reference_list(cdi_info, partial_part_list);
    }
  }
  // Segment references
  CDI_WITH_INNER_CHUNK(cdi_info) {
    if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_SEGL) {
      cdi_read_segment_reference_list(cdi_info, segment_list);
    }
  }
  // Reference Partition selected
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 8, 6))
    cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &m_partitionIndex);

  // Prohibited geometry
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 8, 7)) {
    std::vector<cdiINT32> geomIndices;
    if (m_geometrySelectionType == eGEOMETRY_TYPE::Face)
      cdi_inner_chunk_read_flst(index, chunkName, cdi_info, geomIndices);
    else if (m_geometrySelectionType == eGEOMETRY_TYPE::Part)
      cdi_inner_chunk_read_rgns(index, chunkName, cdi_info, geomIndices);
    for (cdiINT32 geom : geomIndices)
      prohibited_selections.insert(geom);
  }

  return true;
}

bool
cCDI_GEOMETRY_REF::WriteToCDI(CDI_INFO cdi_info) const
{
  // Selection type
  cdi_write_enum(cdi_info, m_geometrySelectionType);
  // Face references
  cdi_write_flst(cdi_info, face_list);
  // Region/Part references
  cdi_write_rgns(cdi_info, const_cast<std::vector<cdiINT32>&>(rgn_list));
  // Partial Part references
  WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_PPRL) {
    cdi_write_partial_part_reference_list(cdi_info, partial_part_list);
  }
  // Segment references
  WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SEGL) {
    cdi_write_segment_reference_list(cdi_info, segment_list);
  }
  // Reference partition
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 8, 6))
    cdi_write_int_(cdi_info, m_partitionIndex);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 8, 7)) {
    std::vector<cdiINT32> geomIndices;
    if (!prohibited_selections.empty()) {
      geomIndices.reserve(prohibited_selections.size());
      for (cdiINT32 prohibit : prohibited_selections)
        geomIndices.push_back(prohibit);
    }
    if (m_geometrySelectionType == eGEOMETRY_TYPE::Face)
      cdi_write_flst(cdi_info, geomIndices);
    else if (m_geometrySelectionType == eGEOMETRY_TYPE::Part)
      cdi_write_rgns(cdi_info, geomIndices);
  }

  return true;
}

cCDI_GEOMETRY_REF* cdi_read_gmrf(CDI_INFO cdi_info)
{
  cCDI_GEOMETRY_REF* gmrf = new cCDI_GEOMETRY_REF(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment);
  if (!gmrf->ReadFromCDI(cdi_info))
    cdi_destroy_gmrf(gmrf);

  return gmrf;
}

void cdi_destroy_gmrf(cCDI_GEOMETRY_REF* gmrf)
{
  delete gmrf;
  gmrf = nullptr;
}

/****************************************************************\
|
| Function name:cdi_get_geometry_type_from_entity_name
| 
| Purpose: return cCDI_GEOMETRY_REF::eGEOMETRY_TYPE from input
|          entity string.
|
\****************************************************************/

constexpr static const char* CDI_FACE_DELIMITER = "::"; // If you change this, keep cGEOM_COMMON_ENTITY::PartFaceDelimiterInFullPath() in sync
constexpr char CDI_SEGMENT_DELIMITER = '/';

cCDI_GEOMETRY_REF::eGEOMETRY_TYPE cdi_get_geometry_type_from_entity_name(const std::string& entity_name) {
  size_t loc_of_last_segment_delim = entity_name.find_last_of(CDI_SEGMENT_DELIMITER);
  size_t last_index = entity_name.length() - 1;
  
  auto token_part_or_face = [] (const std::string& entity_name,
                                size_t start_pos) -> cCDI_GEOMETRY_REF::eGEOMETRY_TYPE
  {
    size_t loc_of_face_delimiter = entity_name.find(CDI_FACE_DELIMITER, start_pos);
    if (loc_of_face_delimiter != std::string::npos) {
      return cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face;
    } else {
      return cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part;
    }
  };
      
  if (loc_of_last_segment_delim != std::string::npos) { //entity_name has a segment_delim
    bool is_terminal_segment_delim = loc_of_last_segment_delim == last_index;
    if (!is_terminal_segment_delim) {
      return token_part_or_face(entity_name, loc_of_last_segment_delim);
    } else {
      return cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment;
    }
  } else { //entity_name has no segment_delim
    return token_part_or_face(entity_name, 0 /*no segment_delim, start from beginning*/);
  }
}

/****************************************************************\
|
| Geometry Selection Tree (cCDI_GEOM_SELECTION_TREE)
|
\****************************************************************/

cCDI_GEOM_SELECTION_TREE::cCDI_GEOM_SELECTION_TREE(cdiINT32 partitionIndex, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geomSelectionType)
  : m_selections(geomSelectionType), m_exclusions(geomSelectionType),
    m_partitionIndex(partitionIndex), m_printMajor(CDI_MAJOR_VERSION), m_printMinor(CDI_MINOR_VERSION) {
  m_selections.PartitionIndex(partitionIndex);
  m_exclusions.PartitionIndex(partitionIndex);
}

bool
cCDI_GEOM_SELECTION_TREE::ReadFromCDI(CDI_INFO cdi_info)
{
  m_printMajor = cdi_get_major_version(cdi_info);
  m_printMinor = cdi_get_minor_version(cdi_info);
  cdiINT32 index = 0;
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);
  // Partition selected
  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &m_partitionIndex);
  // Selected geometries
  CDI_WITH_INNER_CHUNK(cdi_info) {
    if (cdi_get_type(cdi_info) == m_selections.GetChunkType()) {
      m_selections.ReadFromCDI(cdi_info);
    }
  }
  // Excluded geometries
  CDI_WITH_INNER_CHUNK(cdi_info) {
    if (cdi_get_type(cdi_info) == m_exclusions.GetChunkType()) {
      m_exclusions.ReadFromCDI(cdi_info);
    }
  }

  return true;
}

bool
cCDI_GEOM_SELECTION_TREE::WriteToCDI(CDI_INFO cdi_info) const
{
  // Selected partition
  cdi_write_int_(cdi_info, m_partitionIndex);
  // Selected geometries
  WITH_CDI_CHUNK(cdi_info, m_selections.GetChunkType()) {
    m_selections.WriteToCDI(cdi_info);
  }
  // Excluded geometries
  WITH_CDI_CHUNK(cdi_info, m_exclusions.GetChunkType()) {
    m_exclusions.WriteToCDI(cdi_info);
  }
  return true;
}

void
cCDI_GEOM_SELECTION_TREE::PrintToStream(std::ostream &output, int depth) const
{
  cdi_print_geos(*this, output, depth, true, m_printMajor, m_printMinor);
}

bool
cCDI_GEOM_SELECTION_TREE::ReadFromStream(std::istream &input)
{
  return cdi_read_geos(input, this, &m_printMajor, &m_printMinor);
}

std::vector<cdiINT32>
cCDI_GEOM_SELECTION_TREE::ExpandSelection(const cCDI_PARTITIONS& partitions) const
{
  return ExpandSelection(partitions, m_selections.GeometrySelectionType());
}

std::vector<cdiINT32>
cCDI_GEOM_SELECTION_TREE::ExpandSelection(const cCDI_PARTITIONS& partitions, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geomSelectionType) const
{
  std::vector<cdiINT32> selections;
  cCDI_PARTITION* selectionPartition = partitions.GetPartition(m_partitionIndex);
  if (selectionPartition) {
    if (m_selections.segment_list.empty()) {
    // We can just process the selected parts and lower items directly.
      ProcessPartAndLowerLevelSelectionsDirectly(*selectionPartition, geomSelectionType, selections);
    }
    else {
      std::map<cdiINT32, cdiINT32> partialPartFaceSumMap;
      // Systemically recurse through the selection partition's segment tree.
      ExpandSelectionSegment(selectionPartition->GetRoot(), false, geomSelectionType, selections, partialPartFaceSumMap);
      // Check partial parts
      if (geomSelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) {
        for (auto mapItem : partialPartFaceSumMap) {
          cdiINT32 partIndex = mapItem.first;
          if (partitions.GetPartFaceList(partIndex).size() == mapItem.second)
            selections.push_back(partIndex);
        }
      }
    }
  }

  // Check for any prohibited selections
  if (m_selections.prohibited_selections.empty() && m_exclusions.prohibited_selections.empty())
    return selections;
  
  std::vector<cdiINT32> filteredSelections;
  for (cdiINT32 select : selections) {
    if (m_selections.prohibited_selections.find(select) == m_selections.prohibited_selections.end() &&
        m_exclusions.prohibited_selections.find(select) == m_exclusions.prohibited_selections.end()) {
      filteredSelections.push_back(select);
    }
  }

  return filteredSelections;
}


void
cCDI_GEOM_SELECTION_TREE::ProcessPartAndLowerLevelSelectionsDirectly(const cCDI_PARTITION& selectionPartition,
                                                                     cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geomSelectionType,
                                                                     std::vector<cdiINT32> &selections) const
{

  if (geomSelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) {
    selections = m_selections.rgn_list;
    // Check if any partial parts add up to a full part
    std::map<cdiINT32, cdiINT32> partialPartFaceSumMap;
    for (const cCDI_PARTIAL_PART_REF& partialPartRef : m_selections.partial_part_list) {
      cdiINT32 partialPartIndex = partialPartRef.m_partIndex;
      cCDI_PARTIAL_PART* pPartialPart = selectionPartition.GetPartialPart(partialPartIndex);
      partialPartFaceSumMap[pPartialPart->GetPartIndex()] += pPartialPart->GetFaceIndices().size();
    }
    for (auto mapItem : partialPartFaceSumMap) {
      cdiINT32 partIndex = mapItem.first;
      if (selectionPartition.GetOwner().GetPartFaceList(partIndex).size() == mapItem.second)
        selections.push_back(partIndex);
    }
  }
  else if (geomSelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart) {
    for (const cCDI_PARTIAL_PART_REF& partialPartRef : m_selections.partial_part_list)
      selections.push_back(partialPartRef.m_partIndex);
  }
  else if (geomSelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face) {
    for (cdiINT32 partIndex : m_selections.rgn_list) {
      std::vector<cdiINT32> faceList = selectionPartition.GetOwner().GetPartFaceList(partIndex);
      for (auto faceIndex : faceList) {
        if (!IsExcluded(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, faceIndex))
          selections.push_back(faceIndex);
      }
    }

    for (const cCDI_PARTIAL_PART_REF& partialPartRef : m_selections.partial_part_list) {
      cdiINT32 partialPartIndex = partialPartRef.m_partIndex;
      cCDI_PARTIAL_PART* pPartialPart = selectionPartition.GetPartialPart(partialPartIndex);
      for (auto faceIndex : pPartialPart->GetFaceIndices()) {
        if (!IsExcluded(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, faceIndex))
          selections.push_back(faceIndex);
      }
    }

    selections.insert(selections.end(), m_selections.face_list.begin(), m_selections.face_list.end());
  }

}


void
cCDI_GEOM_SELECTION_TREE::ExpandSelectionSegment(const cCDI_SEGMENT& segment, bool isParentSelected, 
  cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geometrySelectionType, std::vector<cdiINT32> &selections,
  std::map<cdiINT32, cdiINT32> &partialPartFaceSumMap) const
{
  const cCDI_PARTITION& selectionPartition = segment.GetPartition();
  cdiINT32 segmentIndex = selectionPartition.GetSegmentIndex(segment);
  bool isSegmentSelected = IsSelected(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment, segmentIndex) ||
    (isParentSelected && !IsExcluded(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment, segmentIndex));
  if (geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment && isSegmentSelected) {
    selections.push_back(segmentIndex);
  }
  if (geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part ||
      geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face) {
    std::vector<cdiINT32> parts = segment.GetRegionIndices(false);
    for (cdiINT32 partIndex : parts) {
      bool isPartSelected = IsSelected(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part, partIndex) ||
        (isSegmentSelected && !IsExcluded(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part, partIndex));
      if (isPartSelected && geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) {
        selections.push_back(partIndex);
      }
      else if (geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face) {
        std::vector<cdiINT32> faceList = selectionPartition.GetOwner().GetPartFaceList(partIndex);
        for (auto faceIndex : faceList) {
          bool isFaceSelected = IsSelected(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, faceIndex) ||
              (isPartSelected && !IsExcluded(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, faceIndex));
          if (isFaceSelected)
            selections.push_back(faceIndex);
        }
      }
    }
  }
  if (geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face ||
      geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart ||
      geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) {
    for (cCDI_PARTIAL_PART* partialPart : segment.GetPartialParts()) {
      cdiINT32 partialPartIndex = selectionPartition.GetPartialPartIndex(*partialPart);
      bool isPartialPartSelected = IsSelected(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart, partialPartIndex) ||
        (isSegmentSelected && !IsExcluded(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart, partialPartIndex));
      if (isPartialPartSelected && geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart) {
        selections.push_back(partialPartIndex);
      }
      else if (geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face) {
        for (auto faceIndex : partialPart->GetFaceIndices()) {
          bool isFaceSelected = IsSelected(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, faceIndex) ||
              (isPartialPartSelected && !IsExcluded(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, faceIndex));
          if (isFaceSelected)
            selections.push_back(faceIndex);
        }
      }
      else if (isPartialPartSelected && geometrySelectionType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) {
        partialPartFaceSumMap[partialPart->GetPartIndex()] += partialPart->GetFaceIndices().size();
      }
    }
  }
  for (cCDI_SEGMENT* childSegment : segment.GetChildSegments()) {
    ExpandSelectionSegment(*childSegment, isSegmentSelected, geometrySelectionType, selections, partialPartFaceSumMap);
  }
}

bool
cCDI_GEOM_SELECTION_TREE::IsSelected(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geometryType, cdiINT32 geomIndex) const
{
  bool isSelected = false;
  if (geometryType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) {
    isSelected = std::find(m_selections.rgn_list.begin(), m_selections.rgn_list.end(), geomIndex) != m_selections.rgn_list.end();
  }
  else if (geometryType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face) {
    isSelected = std::find(m_selections.face_list.begin(), m_selections.face_list.end(), geomIndex) != m_selections.face_list.end();
  }
  else if (geometryType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment) {
    isSelected = std::find_if(m_selections.segment_list.begin(), m_selections.segment_list.end(),
      [&geomIndex](const cCDI_SEGMENT_REF& seg) { return seg.m_segmentIndex == geomIndex; }) != m_selections.segment_list.end();
  }
  else if (geometryType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart) {
    isSelected = std::find_if(m_selections.partial_part_list.begin(), m_selections.partial_part_list.end(),
      [&geomIndex](const cCDI_PARTIAL_PART_REF& ppart) { return ppart.m_partIndex == geomIndex; }) != m_selections.partial_part_list.end();
  }
  return isSelected;
}

bool
cCDI_GEOM_SELECTION_TREE::IsExcluded(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE geometryType, cdiINT32 geomIndex) const
{
  bool isExcluded = false;
  if (geometryType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part) {
    isExcluded = std::find(m_exclusions.rgn_list.begin(), m_exclusions.rgn_list.end(), geomIndex) != m_exclusions.rgn_list.end();
  }
  else if (geometryType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face) {
    isExcluded = std::find(m_exclusions.face_list.begin(), m_exclusions.face_list.end(), geomIndex) != m_exclusions.face_list.end();
  }
  else if (geometryType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment) {
    isExcluded = std::find_if(m_exclusions.segment_list.begin(), m_exclusions.segment_list.end(),
      [&geomIndex](const cCDI_SEGMENT_REF& seg) { return seg.m_segmentIndex == geomIndex; }) != m_exclusions.segment_list.end();
  }
  else if (geometryType == cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::PartialPart) {
    isExcluded = std::find_if(m_exclusions.partial_part_list.begin(), m_exclusions.partial_part_list.end(),
      [&geomIndex](const cCDI_PARTIAL_PART_REF& ppart) { return ppart.m_partIndex == geomIndex; }) != m_exclusions.partial_part_list.end();
  }
  return isExcluded;
}

cCDI_GEOM_SELECTION_TREE* cdi_read_geos(CDI_INFO cdi_info)
{
  cCDI_GEOM_SELECTION_TREE* selTree = new cCDI_GEOM_SELECTION_TREE(-1, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Segment);
  if (!selTree->ReadFromCDI(cdi_info))
    cdi_destroy_geos(selTree);

  return selTree;
}

void cdi_destroy_geos(cCDI_GEOM_SELECTION_TREE* geos)
{
  delete geos;
  geos = nullptr;
}

/****************************************************************\
|
| Function name:cdi_write_facd
|
| Purpose:writes facd (face-distance record )
|
\****************************************************************/
VOID cdi_write_facd(CDI_INFO cdi_info,
                    CDI_FACD facd)
{
  cdi_write_cdichars(cdi_info, facd->face_name, facd->n_face_name_char);
  cdi_write_idFLOAT(cdi_info, &facd->front_offset, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 7)) { // separate front and back offsets first introduced in 3.7
    cdi_write_idFLOAT(cdi_info, &facd->back_offset, 1);
    cdi_write_asINT32(cdi_info, &facd->region_index, 1);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 18)) // selection of offset from front/back/both of facets introduced in 3.18
      cdi_write_asINT32(cdi_info, &facd->facet_side, 1);
  }
}


/****************************************************************\
|
| Function name:cdi_read_facd
|
| Purpose:reads facd (face name and distance )
|
\****************************************************************/
VOID cdi_read_facd(CDI_INFO cdi_info,
                   CDI_FACD facd)
{
  facd->face_name = cdi_read_cdichars(cdi_info, NULL, &facd->n_face_name_char);
  cdi_read_idFLOAT(cdi_info, &facd->front_offset, 1);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 7)) { // separate front and back offsets first introduced in 3.7
    cdi_read_idFLOAT(cdi_info, &facd->back_offset, 1);
    cdi_read_asINT32(cdi_info, &facd->region_index, 1);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 18)) // selection of offset from front/back/both of facets introduced in 3.18
      cdi_read_asINT32(cdi_info, &facd->facet_side, 1);
    else
      facd->facet_side = 0;
  }
  else {
    facd->back_offset = facd->front_offset;
    facd->region_index = -1;
    facd->facet_side = 0;
  }
}


/****************************************************************\
|
| Function name:cdi_destroy_facd
|
| Purpose:destroys a facd (face dist record)
|
\****************************************************************/
VOID cdi_destroy_facd(CDI_FACD facd)
{
  exa_free(facd->face_name);
  exa_free(facd);
}

/****************************************************************\
|
| Function name:cdi_clear_facd
|
| Purpose:clearsa facd (face dist record)
|
\****************************************************************/
VOID cdi_clear_facd(CDI_FACD facd)
{
  exa_free(facd->face_name);
}


/****************************************************************\
|
| Function name:cdi_write_fdlt
|
| Purpose:writes fdlt (facd list)
|
\****************************************************************/
VOID cdi_write_fdlt(CDI_INFO cdi_info,
                    CDI_FDLT fdlt)
{
    fdlt->cccc = CDI_CHUNK_TYPE_FDLT;

    cdi_push(cdi_info, CDI_CHUNK_TYPE_FDLT);

    cdi_write_asINT32(cdi_info, &fdlt->n_facd, 1);
    ccDOTIMES(j, fdlt->n_facd)
      cdi_write_facd(cdi_info, fdlt->facd+j);

    cdi_pop(cdi_info);
}


/****************************************************************\
|
| Function name:cdi_read_fdlt
|
| Purpose:reads fdlt (facd list)
|
\****************************************************************/
CDI_FDLT cdi_read_fdlt(CDI_INFO cdi_info)
{
    CDI_FDLT fdlt = EXA_CALLOC_STRUCT(CDI_FDLT);

    fdlt->cccc = CDI_CHUNK_TYPE_FDLT;

    cdi_read_asINT32(cdi_info, &fdlt->n_facd, 1);
      
    fdlt->facd = EXA_CALLOC_ARRAY(sCDI_FACD, fdlt->n_facd);

    ccDOTIMES(j, fdlt->n_facd)
      cdi_read_facd(cdi_info, fdlt->facd+j);

    return fdlt;
}


/****************************************************************\
|
| Function name:cdi_destroy_fdlt
|
| Purpose:destroys a fdlt (facd list)
|
\****************************************************************/
VOID cdi_destroy_fdlt(CDI_FDLT fdlt)
{
  cdi_clear_fdlt(fdlt);
  exa_free(fdlt);
}


/****************************************************************\
|
| Function name:cdi_clear_fdlt
|
| Purpose:clears a fdlt (facd list)
|
\****************************************************************/
VOID cdi_clear_fdlt(CDI_FDLT fdlt)
{
  if (fdlt != NULL){
    ccDOTIMES(i, fdlt->n_facd)
      cdi_clear_facd(fdlt->facd+i);
    exa_free(fdlt->facd);
  }
}


/****************************************************************\
|
| Function name:cdi_write_farg
|
| Purpose:writes farg (face angle-range record )
|
\****************************************************************/
VOID cdi_write_farg(CDI_INFO cdi_info,
                    sCDI_FARG farg)
{
  cdi_write_enum(cdi_info, farg.type);
  cdi_write_parm(cdi_info, &(farg.min_param));
  cdi_write_parm(cdi_info, &(farg.max_param));
  cdi_write_enum(cdi_info, farg.voxel_size_via);
  cdi_write_dbls(cdi_info, &farg.resolution, 1);
  cdi_write_int_(cdi_info, farg.scale);
  cdi_write_int_(cdi_info, farg.thickness);
} 


/****************************************************************\
|
| Function name:cdi_read_farg
|
| Purpose:reads farg (face angle-range record)
|
\****************************************************************/
sCDI_FARG cdi_read_farg(CDI_INFO cdi_info)
{
  char chunkName[10];
  cio_type_to_string(CDI_CHUNK_TYPE_FARG, chunkName);
  sCDI_FARG farg;
  cdiINT32 index = 0;
  cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &farg.type);
  cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &(farg.min_param));
  cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &(farg.max_param));
  cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &farg.voxel_size_via);
  cdi_inner_chunk_read_dbls(index, chunkName, cdi_info, &farg.resolution);
  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &farg.scale);
  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &farg.thickness);
  return farg;
}


/****************************************************************\
|
| Function name:cdi_write_gfar
|
| Purpose:writes gfar (farg array)
|
\****************************************************************/
VOID cdi_write_gfar(CDI_INFO cdi_info,
                    CDI_GFAR gfar)
{
  gfar->cccc = CDI_CHUNK_TYPE_GFAR;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_GFAR);

  cdi_write_int_(cdi_info, gfar->n_farg);
  ccDOTIMES(j, gfar->n_farg) {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_FARG)
      cdi_write_farg(cdi_info, gfar->farg[j]);
  }

  cdi_pop(cdi_info);
}


/****************************************************************\
|
| Function name:cdi_read_gfar
|
| Purpose:reads gfar (farg array)
|
\****************************************************************/
sCDI_GFAR cdi_read_gfar(CDI_INFO cdi_info)
{
  sCDI_GFAR gfar;

  gfar.cccc = CDI_CHUNK_TYPE_GFAR;

  cdiINT32 index=0;
  CIO_INFO cio = cdi_info->cio_info;
  cdi_inner_chunk_read_int_(index, "gfar", cdi_info, &gfar.n_farg);
  ccDOTIMES(j, gfar.n_farg) {
     cio_descend(cio);
     gfar.farg.push_back(cdi_read_farg(cdi_info));
     cio_ascend(cio);
  }
  return gfar;
}

/****************************************************************\
|
| Function name:cdi_read_gmcv
|
| Purpose: reads a gmcv (mesh curvature VR control) record
|
\****************************************************************/
CDI_GMCV cdi_read_gmcv (CDI_INFO cdi_info)
{
  CDI_GMCV gmcv = new sCDI_GMCV();

  asINT32 count = cio_get_count(cdi_info->cio_info);
  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);

    switch(cio_get_type(cdi_info->cio_info)) {
    case CDI_CHUNK_TYPE_FLST: {
      gmcv->face_list = cdi_read_flst(cdi_info);
      break;
    }

    case CDI_CHUNK_TYPE_GMRF: {
      cCDI_GEOMETRY_REF* read_gmrf = cdi_read_gmrf(cdi_info);
      gmcv->geom_ref = *read_gmrf;
      cdi_destroy_gmrf(read_gmrf);
      break;
    }

    case CDI_CHUNK_TYPE_GEOS: {
      cdi_read_geometry_selection(cdi_info, gmcv->geom_selection);
      break;
    }

    case CDI_CHUNK_TYPE_GFAR: {
      gmcv -> angle_ranges = cdi_read_gfar(cdi_info);
      break;
    }

    default: {
      break;
    }
    }

    cio_ascend(cdi_info->cio_info);
  }

  return(gmcv);
}


/****************************************************************\
|
| Function name:cdi_write_gapd
|
| Purpose:writes gapd (gap VR parameters)
|
\****************************************************************/
VOID cdi_write_gapd(CDI_INFO cdi_info,
                    CDI_GAPD gapd)
{
  gapd->cccc = CDI_CHUNK_TYPE_GAPD;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_GAPD);

  cdi_write_int_(cdi_info, gapd->n_voxels);
  cdi_write_parm(cdi_info, &(gapd->gap_size));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 29))
    cdi_write_int_(cdi_info, gapd->scale);
  cdi_pop(cdi_info);
}


/****************************************************************\
|
| Function name:cdi_read_gapd
|
| Purpose:reads gapd (gap VR parameters)
|
\****************************************************************/
sCDI_GAPD cdi_read_gapd(CDI_INFO cdi_info)
{
  char chunkName[10];
  cio_type_to_string(CDI_CHUNK_TYPE_GAPD, chunkName);
  sCDI_GAPD gapd;
  gapd.scale = 1024; // larger than ever expected scale
  cdiINT32 index = 0;
  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &gapd.n_voxels);
  cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &(gapd.gap_size));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 29))
    cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &gapd.scale);
  return gapd;
}

/****************************************************************\
|
| Function name:cdi_read_ggap
|
| Purpose: reads a ggap (gap VR control) record
|
\****************************************************************/
sCDI_GGAP cdi_read_ggap (CDI_INFO cdi_info)
{
  sCDI_GGAP ggap;
  asINT32 count = cio_get_count(cdi_info->cio_info);
  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);

    switch(cio_get_type(cdi_info->cio_info)) {

    case CDI_CHUNK_TYPE_NAME: {
      ggap.name = *(cdi_read_name(cdi_info));
      break;
    }

    case CDI_CHUNK_TYPE_FLST:
    case CDI_CHUNK_TYPE_GEOS: {
      cdi_read_geometry_selection(cdi_info, ggap.geom_selection);
      break;
    }

    case CDI_CHUNK_TYPE_GAPD: {
      ggap.gapd= cdi_read_gapd(cdi_info);
      break;
    }

    default: {
      break;
    }
    }

    cio_ascend(cdi_info->cio_info);
  }

  return(ggap);
}



/****************************************************************\
|
| Function name:cdi_write_mdev
|
| Purpose:writes mdev
|
\****************************************************************/
VOID cdi_write_mdev(CDI_INFO cdi_info,
                    CDI_MDEV mdev)
{
  mdev->cccc = CDI_CHUNK_TYPE_MDEV;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_MDEV);
    
  cdi_write_idFLOAT(cdi_info, &mdev->seg_size, 1);
  cdi_write_asINT32(cdi_info, &mdev->icsys, 1);
  cdi_write_asINT32(cdi_info, &mdev->ilrf, 1);
 
  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_mdev
|
| Purpose:reads mdev
|
\****************************************************************/
CDI_MDEV cdi_read_mdev(CDI_INFO cdi_info) /* the info structure */
{
  CDI_MDEV mdev = EXA_CALLOC_STRUCT(CDI_MDEV); /* the mref structure */

  mdev->cccc = CDI_CHUNK_TYPE_MDEV;
  
  cdi_read_idFLOAT(cdi_info, &mdev->seg_size, 1);
  cdi_read_asINT32(cdi_info, &mdev->icsys, 1);
  cdi_read_asINT32(cdi_info, &mdev->ilrf, 1);

  return mdev;
}

/****************************************************************\
|
| Function name:cdi_destroy_mdev
|
| Purpose:destroys a mdev
|
\****************************************************************/
VOID cdi_destroy_mdev(CDI_MDEV mdev)        /* the mdev to destroy */
{
  exa_free(mdev);
}

void cdi_read_parm(CDI_INFO cdi_info, CDI_PARM parm)
{
  parm->cccc = CDI_CHUNK_TYPE_PARM;
  
  int isDefaulted;
  cdi_read_asINT32(cdi_info, &(isDefaulted), 1);
  parm->defaulted = (isDefaulted == 1);
  int isVariable;
  cdi_read_asINT32(cdi_info, &(isVariable), 1);
  parm->isEqVariable = (isVariable == 1);
  parm->isDataCurve = (isVariable == 2);
 
  cdi_read_stdstring(cdi_info, parm->variableName);

  cdi_read_idFLOAT(cdi_info, &(parm->value), 1);

  cdi_read_stdstring(cdi_info, parm->preferredUnit);
}


void cdi_write_parm(CDI_INFO cdi_info, const sCDI_PARM* parm)
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_PARM);
  ASSERT_VALID_CDI_INFO(cdi_info);
  int toWrite = (int) parm->defaulted;
  cdi_write_asINT32(cdi_info, &toWrite, 1);
  toWrite = parm->isEqVariable ? 1 : (parm->isDataCurve ? 2 : 0);
  cdi_write_asINT32(cdi_info, &toWrite, 1);
  cdi_write_cdistring(cdi_info, parm->variableName.c_str());
  idFLOAT val = parm->value;
  cdi_write_idFLOAT(cdi_info, &val, 1);
  cdi_write_cdistring(cdi_info, parm->preferredUnit.c_str());
  cdi_pop(cdi_info);
}

void cdi_write_enum(CDI_INFO cdi_info, sINT32 value)
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_ENUM);
  ASSERT_VALID_CDI_INFO(cdi_info);
  cdi_write_asINT32(cdi_info, &value, 1);
  cdi_pop(cdi_info);
}

void cdi_write_int_(CDI_INFO cdi_info, sINT32 value)
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_INT_);
  ASSERT_VALID_CDI_INFO(cdi_info);
  cdi_write_asINT32(cdi_info, &value, 1);
  cdi_pop(cdi_info);
}

void cdi_write_bool(CDI_INFO cdi_info, bool value)
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_BOOL);
  ASSERT_VALID_CDI_INFO(cdi_info);
  sINT32 intValue = value ? 1 : 0;
  cdi_write_asINT32(cdi_info, &intValue, 1);
  cdi_pop(cdi_info);
}

void cdi_write_strg(CDI_INFO cdi_info, const std::string& value)
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_STRG);
  ASSERT_VALID_CDI_INFO(cdi_info);
  cdi_write_stdstring(cdi_info, value);
  cdi_pop(cdi_info);
}

void cdi_write_dble(CDI_INFO cdi_info, const idFLOAT value)
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_DBLE);
  ASSERT_VALID_CDI_INFO(cdi_info);
  cdi_write_idFLOAT(cdi_info, &value, 1);
  cdi_pop(cdi_info);
}

// if values is NULL, we are reading a unknown dbls which is allocated according
// to the current chunk size; otherwise, we are reading from a known structure
void cdi_read_dbls(CDI_INFO cdi_info, idFLOAT* values, cdiINT32 &count)
{
  // read size of dbls
  count = cio_get_size(cdi_info->cio_info) / sizeof(idFLOAT);
  // allocate memory if values is NULL
  if (!values) values = EXA_CALLOC_ARRAY(idFLOAT, count);
  // read all elem of this dbls
  cdi_read_idFLOAT(cdi_info, values, count);
}

// size of dbls is known when we wite it
void cdi_write_dbls(CDI_INFO cdi_info, idFLOAT* values, cdiINT32 count)
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_DBLS);
  ASSERT_VALID_CDI_INFO(cdi_info);

  // write all elem to cdi file 
  cdi_write_idFLOAT(cdi_info, values, count);

  cdi_pop(cdi_info);
}

VOID cdi_destroy_dbls(idFLOAT* dbls)
{
  if (dbls) exa_free(dbls);
}


void cdi_read_wipr(CDI_INFO info, CDI_WIPR wipr)
{
  cdiINT32 index=0;

  // name
  ccCDI_DO_INNER_CHUNK(index, "wipr", info) { 
    CDI_NAME name = cdi_read_name(info);    
    wipr->name = name->name;
    cdi_destroy_name(name);
  }

  // wiper blade and wiped surface
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,3>(info)) {
    cdi_inner_chunk_read_flst(index, "wipr", info, wipr->wiper_blade_geoms.m_selections.face_list);
    cdi_inner_chunk_read_flst(index, "wipr", info, wipr->wiped_surface_geoms.m_selections.face_list);
  }

  // wiper axis 
  cdi_inner_chunk_read_dbls(index, "wipr", info,
      (idFLOAT*)wipr->wiper_axis_origin);
  cdi_inner_chunk_read_dbls(index, "wipr", info,
      (idFLOAT*)wipr->wiper_axis_dir);

  // end points
  cdi_inner_chunk_read_dbls(index, "wipr", info,
      (idFLOAT*)wipr->inner_endpoint);
  cdi_inner_chunk_read_dbls(index, "wipr", info,
      (idFLOAT*)wipr->outer_endpoint);

  // wiper type
  cdi_inner_chunk_read_enum(index, "wipr", info, &(wipr->wiper_type));

  // angles
  cdi_inner_chunk_read_parm(index, "wipr", info, &(wipr->import_angle));
  cdi_inner_chunk_read_parm(index, "wipr", info, &(wipr->start_angle));
  cdi_inner_chunk_read_parm(index, "wipr", info, &(wipr->end_angle));

  // time
  cdi_inner_chunk_read_parm(index, "wipr", info, &(wipr->stroke_duration));
  cdi_inner_chunk_read_parm(index, "wipr", info, &(wipr->delay));

  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 15))
    cdi_inner_chunk_read_parm(index, "wipr", info, &(wipr->initial_delay));

  if (CDI_INFO_VERSION_AT_LEAST(info, 7, 22)) {
    cdi_inner_chunk_read_enum(index, "wipr", info, &(wipr->start_via));
    cdiINT32 numMonitors = 0;
    cdiINT32 monitorIndex = -1;
    cdi_inner_chunk_read_int_(index, "wipr", info, &numMonitors);
    ccDOTIMES(i, numMonitors) {
      cdi_inner_chunk_read_int_(index, "wipr", info, &monitorIndex);
      wipr->monitors.push_back(monitorIndex);
    }
    cdi_inner_chunk_read_int_(index, "wipr", info, &(wipr->emitter_index));
    cdi_inner_chunk_read_parm(index, "wipr", info, &(wipr->emitter_delay));
    cdi_inner_chunk_read_parm(index, "wipr", info, &(wipr->num_strokes));
  }

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(info)) {
    // wiper blade
    CDI_WITH_INNER_CHUNK(info) {
      if (cdi_get_type(info) == CDI_CHUNK_TYPE_GEOS) {
        cCDI_GEOM_SELECTION_TREE* read_geos = cdi_read_geos(info);
        wipr->wiper_blade_geoms = *read_geos;
        cdi_destroy_geos(read_geos);
      }
    }
    // wiped surface
    CDI_WITH_INNER_CHUNK(info) {
      if (cdi_get_type(info) == CDI_CHUNK_TYPE_GEOS) {
        cCDI_GEOM_SELECTION_TREE* read_geos = cdi_read_geos(info);
        wipr->wiped_surface_geoms = *read_geos;
        cdi_destroy_geos(read_geos);
      }
    }
  }

  return;
}

void cdi_write_wipr(CDI_INFO info, CDI_WIPR wipr)
{
  // name
  sCDI_NAME name;
  name.name = (char*) wipr->name.c_str();
  name.n_char = wipr->name.size();
  cdi_write_name(info, &name);

  // wiper blade and wiped surface
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(info)) {
    cdi_write_flst(info, wipr->wiper_blade_geoms.m_selections.face_list);
    cdi_write_flst(info, wipr->wiped_surface_geoms.m_selections.face_list);
  }

  // wiper axis 
  cdi_write_dbls(info, (idFLOAT*)wipr->wiper_axis_origin, 3);
  cdi_write_dbls(info, (idFLOAT*)wipr->wiper_axis_dir, 3);

  // end points
  cdi_write_dbls(info, (idFLOAT*)wipr->inner_endpoint, 3);
  cdi_write_dbls(info, (idFLOAT*)wipr->outer_endpoint, 3);

  // wiper type
  cdi_write_enum(info, wipr->wiper_type);

  // angles
  cdi_write_parm(info, &(wipr->import_angle));
  cdi_write_parm(info, &(wipr->start_angle));
  cdi_write_parm(info, &(wipr->end_angle));

  // time
  cdi_write_parm(info, &(wipr->stroke_duration));
  cdi_write_parm(info, &(wipr->delay));

  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 15))
    cdi_write_parm(info, &(wipr->initial_delay));

  if (CDI_INFO_VERSION_AT_LEAST(info, 7, 22)) {
    cdi_write_enum(info, wipr->start_via);
    cdi_write_int_(info, wipr->monitors.size());
    ccDOTIMES(i, wipr->monitors.size())
      cdi_write_int_(info, wipr->monitors[i]);
    cdi_write_int_(info, wipr->emitter_index);
    cdi_write_parm(info, &(wipr->emitter_delay));
    cdi_write_parm(info, &(wipr->num_strokes));
  }

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(info)) {
    // wiper blade
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_GEOS) {
      wipr->wiper_blade_geoms.WriteToCDI(info);
    }
    // wiped surface
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_GEOS) {
      wipr->wiped_surface_geoms.WriteToCDI(info);
    }
  }
  
  return;
}

void cdi_destroy_wipr(CDI_WIPR wipr) {
  exa_free(wipr);
}

// Read an accreation chunk.
void sCDI_ACCR::cdi_read(CDI_INFO info)
{
  cccc = CDI_CHUNK_TYPE_ACCR;
  cdiINT32 index = 0;
  const char *chunkName = "accr";
  cdi_inner_chunk_read_parm(index, chunkName, info, &m_total_accretion_duration);
  cdi_inner_chunk_read_parm(index, chunkName, info, &m_liquid_water_content);
  cdi_inner_chunk_read_int_(index, chunkName, info, &m_material_index);
  cdi_inner_chunk_read_parm(index, chunkName, info, &m_accretion_material_density);
  cdi_inner_chunk_read_int_(index, chunkName, info, &m_total_num_of_accretion_layers);
  cdi_inner_chunk_read_parm(index, chunkName, info, &m_collection_duration_per_layer);
  cdi_inner_chunk_read_parm(index, chunkName, info, &m_accretion_acceleration_factor);
  cdi_inner_chunk_read_int_(index, chunkName, info, &m_meas_window_index);
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8, 11>(info)) {
    cdi_inner_chunk_read_dble(index, chunkName, info, &m_accretion_scale_factor);
  }
  cdi_inner_chunk_read_flst(index, chunkName, info, m_surface_ids);
}

// Write an accretion chunk
void sCDI_ACCR::cdi_write(CDI_INFO info) const
{
  cdi_write_parm(info, &m_total_accretion_duration);
  cdi_write_parm(info, &m_liquid_water_content);
  cdi_write_int_(info, m_material_index);
  cdi_write_parm(info, &m_accretion_material_density);
  cdi_write_int_(info, m_total_num_of_accretion_layers);
  cdi_write_parm(info, &m_collection_duration_per_layer);
  cdi_write_parm(info, &m_accretion_acceleration_factor);
  cdi_write_int_(info, m_meas_window_index);
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8, 11>(info)) {
    cdi_write_dble(info, m_accretion_scale_factor);
  }
  cdi_write_flst(info, m_surface_ids);
}


// =======================================================================================
// Particle Globals
// =======================================================================================

void cdi_read_pglb(CDI_INFO info, CDI_PGLB pglb)
{
  pglb->cccc = CDI_CHUNK_TYPE_PGLB;
  cdiINT32 index = 0;
  const char *chunkName = "pglb";

  if (cdi_cio_descend_with_error(info->cio_info, chunkName) == 0)
    cdi_read_box_(info, pglb->dispersion_box);
  cdi_cio_ascend_with_error(info->cio_info, chunkName, ++index);
  cdi_inner_chunk_read_parm(index, chunkName, info, &(pglb->gravity_csys));
  cdi_inner_chunk_read_parm(index, chunkName, info, pglb->gravity);
  cdi_inner_chunk_read_parm(index, chunkName, info, pglb->gravity + 1);
  cdi_inner_chunk_read_parm(index, chunkName, info, pglb->gravity + 2);
  cdi_inner_chunk_read_parm(index, chunkName, info, &(pglb->coupled_momentum_solver));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(pglb->default_emitter_start));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(pglb->default_emitter_end));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(pglb->default_max_particle_age));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(pglb->default_max_num_reflections));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(pglb->default_min_particle_velocity));
}

void cdi_write_pglb( CDI_INFO info, CDI_PGLB pglb)
{
  cdi_write_box_(info, &(pglb->dispersion_box));
  cdi_write_parm(info, &(pglb->gravity_csys));
  cdi_write_parm(info, pglb->gravity);
  cdi_write_parm(info, pglb->gravity + 1);
  cdi_write_parm(info, pglb->gravity + 2);
  cdi_write_parm(info, &(pglb->coupled_momentum_solver));
  cdi_write_parm(info, &(pglb->default_emitter_start));
  cdi_write_parm(info, &(pglb->default_emitter_end));
  cdi_write_parm(info, &(pglb->default_max_particle_age));
  cdi_write_parm(info, &(pglb->default_max_num_reflections));
  cdi_write_parm(info, &(pglb->default_min_particle_velocity));
}

//Write global data from a CDI class to a PRI::cGLOBAL_PARAAMETERS class.
void sCDI_PGLB::ConvertToPRI(PRI::cPARAMETERS &pri_parameters) const 
  {

    PRI::cGLOBAL_PARAMETERS pri_globals;
    pri_globals.SetDispersionBoxIndex(0); //need to modify this to supply a meaningful index.
    pri_globals.SetDispersionBoxType(PRI:: GEOMETRY_TYPE_BOX_VIA_CORNERS);
    pri_globals.SetApplyGravity(TRUE); //There is no way to tell if gravity is checked off in PowerCASE GUI so always 
                                       //leave it on even if gravity is zero.
    pri_globals.SetGravityCoordinateSystemIndex(cdi_csys_to_pri_csys((size_t)this->gravity_csys.value));
    pri_globals.SetGravity(PRI::GetCompoundFloat3(this->gravity[0], this->gravity[1], this->gravity[2]));
    pri_globals.SetCoupledParticleSolver(coupled_momentum_solver.value != 0.0);
    pri_globals.SetDefaultEmitterStartTime(default_emitter_start);
    pri_globals.SetDefaultEmitterEndTime(default_emitter_end);
    pri_globals.SetDefaultMaximumParticleAge(default_max_particle_age);
    pri_globals.SetDefaultMinimumParticleVelocity(default_min_particle_velocity);
    pri_globals.SetDefaultMaximumNumberOfReflections((asINT32)default_max_num_reflections.value);
    pri_parameters.GetGlobalParametersList().push_back(pri_globals); //Should be changed to not use a vector in PRI.
    
    
  }
// =======================================================================================
// Particle Materials
// =======================================================================================


void cdi_write_prmt( CDI_INFO info, CDI_PRMT prmt)
{
  sCDI_NAME name;
  name.name = (char*) prmt->name.c_str();
  name.n_char = prmt->name.size();
  //cdi is inconsistent about whether write functions do the push/pop 
  //themselves
  cdi_write_name(info, &name);
  cdi_write_parm(info, &(prmt->type));
  if (prmt->type.value == PARTICLE_MATERIAL_LIQUID) {
    cdi_write_parm(info, &(prmt->viscosity));
    cdi_write_parm(info, &(prmt->surface_tension));
  }
  prmt->density_info.WriteToCDI(info);
  if (prmt->type.value == PARTICLE_MATERIAL_LIQUID) {
    cdi_write_parm(info, &(prmt->breakup_model));
  }
}

void cdi_read_prmt(CDI_INFO info, CDI_PRMT prmt)
{
  prmt->cccc = CDI_CHUNK_TYPE_PRMT;
  cdiINT32 index=0;
  const char *chunkName = "prmt";
  ccCDI_DO_INNER_CHUNK(index, chunkName, info)
  { 
    CDI_NAME name = cdi_read_name(info);    
    prmt->name = name->name;
    cdi_destroy_name(name);
  }
  cdi_inner_chunk_read_parm(index, chunkName, info, &(prmt->type));
  if (prmt->type.value == PARTICLE_MATERIAL_LIQUID) {
    cdi_inner_chunk_read_parm(index, chunkName, info, &(prmt->viscosity));
    cdi_inner_chunk_read_parm(index, chunkName, info, &(prmt->surface_tension));
  }

  prmt->density_info.ReadFromCDI(info, chunkName, &index);

  if (prmt->type.value == PARTICLE_MATERIAL_LIQUID) {
    cdi_inner_chunk_read_parm(index, chunkName, info, &(prmt->breakup_model));
  }
}


void sCDI_PRMT::ConvertToPRI(PRI::cPARTICLE_MATERIAL &pri_material) const {

  pri_material.SetName(std::string(this->name));
  pri_material.SetParticleType(cdi_to_pri_material_type((CDI_PARTICLE_MATERIAL_TYPE)(int)this->type.value));
  pri_material.SetDynamicViscosity(viscosity);
  pri_material.SetSurfaceTension(surface_tension);
  
  CDI_DISTRIBUTION_TYPE distribution = (CDI_DISTRIBUTION_TYPE)(int)this->density_info.GetDistributionType().value;
  sCDI_PARM p1;
  sCDI_PARM p2;
  this->density_info.GetParameterValues(&p1, &p2);
  pri_material.SetDensityDistribution(cdi_to_pri_distribution_type(distribution));
  pri_material.SetDensityDistributionParameter1(p1);
  pri_material.SetDensityDistributionParameter2(p2);
  pri_material.SetEnableBreakupModel(breakup_model.value != 0.0); //I think the comment in CDI/430/cdi_readwrite.h regarding possible values of the sCDI_PRMT::breakup_model is incorrect.
  
}

/****************************************************************\
|
| Function name:cdi_write_wpdt
|
| Purpose:writes wind profile data table
|
\****************************************************************/
VOID
cdi_write_wpdt(
               CDI_INFO cdi_info, /* the info structure */
               CDI_WPDT wpdt        /* the wpdt structure */
               )
{
  wpdt->cccc = CDI_CHUNK_TYPE_WPDT;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_WPDT);

  cdi_write_asINT32(cdi_info, &wpdt->index, 1);
  cdi_write_cdichars(cdi_info, wpdt->filename, strlen(wpdt->filename));
  cdi_write_asINT32(cdi_info, &wpdt->n_timesteps, 1);
  cdi_write_idFLOAT(cdi_info, wpdt->velocity, 3 * wpdt->n_timesteps);

  cdi_pop(cdi_info);
}

/****************************************************************\
|
| Function name:cdi_read_wpdt
|
| Purpose:reads wind profile data table
|
\****************************************************************/
CDI_WPDT
cdi_read_wpdt(
              CDI_INFO cdi_info        /* the info structure */
              )
{
  CDI_WPDT wpdt = EXA_CALLOC_STRUCT(CDI_WPDT);

  wpdt->cccc = CDI_CHUNK_TYPE_WPDT;

  cdi_read_asINT32(cdi_info, &wpdt->index, 1);

  asINT32 tempInt;
  wpdt->filename = cdi_read_cdichars(cdi_info, NULL, &tempInt);

  cdi_read_asINT32(cdi_info, &wpdt->n_timesteps, 1);

  wpdt->velocity = EXA_CALLOC_ARRAY(idFLOAT, 3 * wpdt->n_timesteps);

  cdi_read_idFLOAT(cdi_info, wpdt->velocity, 3 * wpdt->n_timesteps);

  return(wpdt);
}

/****************************************************************\
|
| Function name:cdi_destroy_wpdt
|
| Purpose:destroys a wind profile data table
|
\****************************************************************/
VOID
cdi_destroy_wpdt(
                 CDI_WPDT wpdt        /* the wpdt to destroy */
                 )
{
  exa_free(wpdt->velocity);
  exa_free(wpdt->filename);
}

// =======================================================================================
// Particle Model - Emitter Configurations
// =======================================================================================

cCDI_EMITTER_CONFIGURATIONS::~cCDI_EMITTER_CONFIGURATIONS()
{
  for (size_t ice = 0; ice < m_emitterConfigs.size(); ice++)
    delete m_emitterConfigs[ice];
}

std::vector<const sCDI_NOZZLE_EMITTER_CONFIG*> cCDI_EMITTER_CONFIGURATIONS::GetNozzleEmitterConfigs() const
{
  return GetEmitterConfigsOfType<sCDI_NOZZLE_EMITTER_CONFIG>(*this);
}

std::vector<const sCDI_RAIN_EMITTER_CONFIG*> cCDI_EMITTER_CONFIGURATIONS::GetRainEmitterConfigs() const
{
  return GetEmitterConfigsOfType<sCDI_RAIN_EMITTER_CONFIG>(*this);
}

std::vector<const sCDI_TIRE_EMITTER_CONFIG*> cCDI_EMITTER_CONFIGURATIONS::GetTireEmitterConfigs() const
{
  return GetEmitterConfigsOfType<sCDI_TIRE_EMITTER_CONFIG>(*this);
}

void cCDI_EMITTER_CONFIGURATIONS::WriteToCDI(CDI_INFO info) const
{
  const std::vector<const sCDI_EMITTER_CONFIG_BASE*>& allEmitterConfigPtrs = GetAllEmittersConfigs();
  for (size_t ic = 0; ic < allEmitterConfigPtrs.size(); ic++)
  {
    WITH_CDI_CHUNK(info, allEmitterConfigPtrs[ic]->GetChunkType()) {
      // Not part of the struct data.
      sCDI_PARM type; type.value = allEmitterConfigPtrs[ic]->GetType();
      cdi_write_parm(info, &type);

      allEmitterConfigPtrs[ic]->WriteToCDI(info);
    }
  }
}
void cCDI_EMITTER_CONFIGURATIONS::ReadFromCDI(CDI_INFO cdi_info)
{
  CIO_INFO cio = cdi_info->cio_info;

  asINT32 count = cio_get_count(cio);

  for (int i = 0; i < count; i++)
  {
    cio_descend(cio);
    const asINT32 itemCount = cio_get_count(cio);

    cdiINT32 index = 0;
    const char *chunkName = "ecfg";

    // Read type first!  (not part of the configuration struct data)
    sCDI_PARM configurationType;
    cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &configurationType);

    if (configurationType.value == NOZZLE) {
      std::unique_ptr<sCDI_NOZZLE_EMITTER_CONFIG> pConfig(new sCDI_NOZZLE_EMITTER_CONFIG);
      pConfig->ReadFromCDI(cdi_info, &index);
      AddConfiguration(pConfig.release());
    }
    else if (configurationType.value == RAIN) {
      std::unique_ptr<sCDI_RAIN_EMITTER_CONFIG> pConfig(new sCDI_RAIN_EMITTER_CONFIG);
      pConfig->ReadFromCDI(cdi_info, &index);
      AddConfiguration(pConfig.release());
    }
    else if (configurationType.value == TIRE) {
      std::unique_ptr<sCDI_TIRE_EMITTER_CONFIG> pConfig(new sCDI_TIRE_EMITTER_CONFIG);
      pConfig->ReadFromCDI(cdi_info, itemCount, &index);
      AddConfiguration(pConfig.release());
    }
    else
      assert(false);

    cio_ascend(cio);
  }
}


void cCDI_EMITTER_CONFIGURATIONS::ConvertToPRI(sCDI_INFO &cdi_info,
                                               PRI::cPARAMETERS &pri_parameters,
                                               std::vector<std::size_t> &cdi_to_pri_emitter_config_map) const
{

 
  //PR40128/PR40179:  Emitter configuration indices in a PMR file are relative to the relevant config type while the
  //CDI config indices are not.  The map between the two systems needs to be provided to the ConvertToPRI methods 
  //for for emitters; it's computed below and returned by reference to the caller for later use.
  std::size_t num_emitters_using_nozzle_emitter_configs = 0;
  std::size_t num_emitters_using_rain_emitter_configs = 0;
  std::size_t num_emitters_using_tire_emitter_configs = 0;
  const std::vector<const sCDI_EMITTER_CONFIG_BASE*> cdi_all_emitters = this->GetAllEmittersConfigs();
  ccDOTIMES(cdi_config_index, cdi_all_emitters.size()) {

    const sCDI_EMITTER_CONFIG_BASE* emitter_config = cdi_all_emitters.at(cdi_config_index);
    cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE emitter_config_type = 
      emitter_config->GetType();

    switch(emitter_config_type) {
    case cCDI_EMITTER_CONFIGURATIONS::NOZZLE: 
      cdi_to_pri_emitter_config_map.push_back(num_emitters_using_nozzle_emitter_configs++);
      break;
    case cCDI_EMITTER_CONFIGURATIONS::RAIN:
      cdi_to_pri_emitter_config_map.push_back(num_emitters_using_rain_emitter_configs++);
      break;
    case cCDI_EMITTER_CONFIGURATIONS::TIRE: 
      cdi_to_pri_emitter_config_map.push_back(num_emitters_using_tire_emitter_configs++);
      break;
    default:
      msg_error("Unsupported emitter configuration of type \"%s\" encountered and can't be converted to PRI format.",
                cCDI_EMITTER_CONFIGURATIONS::GetTypeName(emitter_config_type).c_str());
   
    }
  }

  std::vector<const sCDI_NOZZLE_EMITTER_CONFIG*> cdi_nozzle_configs = this->GetNozzleEmitterConfigs();
  std::vector<PRI::cNOZZLE_EMITTER_CONFIGURATION>& pri_nozzle_configs = pri_parameters.GetNozzleEmitterConfigurationList();
  //pri_nozzle_configs.resize(cdi_nozzle_configs.size());
  ccDOTIMES(i, cdi_nozzle_configs.size()) {
    const sCDI_NOZZLE_EMITTER_CONFIG* cdi_config = cdi_nozzle_configs[i];
    PRI::cNOZZLE_EMITTER_CONFIGURATION pri_config;
    cdi_config->ConvertToPRI(cdi_info, pri_config);
    pri_nozzle_configs.push_back(pri_config);
  }

  std::vector<const sCDI_RAIN_EMITTER_CONFIG*> cdi_rain_configs = this->GetRainEmitterConfigs();
  std::vector<PRI::cRAIN_EMITTER_CONFIGURATION>& pri_rain_configs = pri_parameters.GetRainEmitterConfigurationList();
  //pri_rain_configs.resize(cdi_rain_configs.size());
  ccDOTIMES(i, cdi_rain_configs.size()) {
    const sCDI_RAIN_EMITTER_CONFIG* cdi_config = cdi_rain_configs[i];
    PRI::cRAIN_EMITTER_CONFIGURATION pri_config;
    cdi_config->ConvertToPRI(pri_config);
    pri_rain_configs.push_back(pri_config);
  }

  std::vector<const sCDI_TIRE_EMITTER_CONFIG*> cdi_tire_configs = this->GetTireEmitterConfigs();
  std::vector<PRI::cTIRE_EMITTER_CONFIGURATION> &pri_tire_configs = pri_parameters.GetTireEmitterConfigurationList();
  std::vector<PRI::cNOZZLE_PROPERTIES> &pri_arc_station_properties = pri_parameters.GetNozzlePropertiesList();
  std::vector<PRI::cANGLE_NOZZLE_PROP_MAP> &pri_nozzle_station_map = pri_parameters.GetAngleNozzlePropertiesMapList();
  ccDOTIMES(i, cdi_tire_configs.size()) {
    const sCDI_TIRE_EMITTER_CONFIG* cdi_config = cdi_tire_configs[i];
    PRI::cTIRE_EMITTER_CONFIGURATION pri_config;
    cdi_config->ConvertToPRI(pri_config, pri_arc_station_properties, pri_nozzle_station_map);
    pri_tire_configs.push_back(pri_config);
  }
}

void sCDI_EMITTER_CONFIG_BASE::WriteToCDI(CDI_INFO info) const
{
  sCDI_NAME cdiName;
  cdiName.name = (char*)name.c_str();
  cdiName.n_char = name.size();
  cdi_write_name(info, &cdiName);
  cdi_write_parm(info, &material_index);
}

void sCDI_EMITTER_CONFIG_BASE::ReadFromCDI(CDI_INFO info, cdiINT32* pIndex)
{
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  int& localIndexVarForMacro = *pIndex;    // :-(
  ccCDI_DO_INNER_CHUNK(localIndexVarForMacro, chunkName, info)
  {
    sCDI_NAME* pCDIName = cdi_read_name(info);
    name = pCDIName->name;
    cdi_destroy_name(pCDIName);
  }
  cdi_inner_chunk_read_parm(*pIndex, chunkName, info, &material_index);
}

void cCDI_FOUR_PARAMETER_DISTRIBUTION::WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_write_parm(cdi_info, &m_distribution);

  cdi_write_parm(cdi_info, &m_param1);
  if (m_distribution.value != DISTRIBUTION_NONE)
    cdi_write_parm(cdi_info, &m_param2);
  if ((CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31)) && 
	  (m_distribution.value != DISTRIBUTION_NONE && m_distribution.value != DISTRIBUTION_UNIFORM))//PR_48032
  {
    cdi_write_parm(cdi_info, &m_param3);
	cdi_write_parm(cdi_info, &m_param4);
  }
}
void cCDI_FOUR_PARAMETER_DISTRIBUTION::ReadFromCDI(CDI_INFO cdi_info, const char* chunkName, cdiINT32* pIndex)
{
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &m_distribution);

  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &m_param1);
  if (m_distribution.value != DISTRIBUTION_NONE)
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &m_param2);
  if (m_distribution.value != DISTRIBUTION_NONE && m_distribution.value != DISTRIBUTION_UNIFORM)//PR_48032
  {
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31)) { 
	  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &m_param3);
	  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &m_param4);
    }
  }
}

void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::GetNoneDistribution(sCDI_PARM* pMean) const
{
  assert(GetDistributionType().value == DISTRIBUTION_NONE);
  sCDI_PARM dummy;
  GetParameterValues(pMean, &dummy);
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::GetUniformDistribution(sCDI_PARM* pMean, sCDI_PARM* pRange) const
{
  assert(GetDistributionType().value == DISTRIBUTION_UNIFORM);
  GetParameterValues(pMean, pRange);
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::GetGaussianDistribution(sCDI_PARM* pMean, sCDI_PARM* pStdDev) const
{
  assert(GetDistributionType().value == DISTRIBUTION_GAUSSIAN);
  GetParameterValues(pMean, pStdDev);
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::GetGaussianDistribution(sCDI_PARM* pMean, sCDI_PARM* pStdDev, sCDI_PARM* minDia, sCDI_PARM* maxDia) const //PR_48032
{
	assert(GetDistributionType().value == DISTRIBUTION_GAUSSIAN);
	GetParameterValues(pMean, pStdDev, minDia, maxDia);
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::GetGammaDistribution(sCDI_PARM* pMean, sCDI_PARM* pShapeParam) const
{
  assert(GetDistributionType().value == DISTRIBUTION_GAMMA);
  GetParameterValues(pMean, pShapeParam);
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::GetGammaDistribution(sCDI_PARM* pMean, sCDI_PARM* pShapeParam, sCDI_PARM* minDia, sCDI_PARM* maxDia) const //PR_48032
{
	assert(GetDistributionType().value == DISTRIBUTION_GAMMA);
	GetParameterValues(pMean, pShapeParam, minDia, maxDia);
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::SetNoneDistribution(const sCDI_PARM& mean)
{
  SetDistribution(DISTRIBUTION_NONE, mean, sCDI_PARM());
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::SetUniformDistribution(const sCDI_PARM& mean, const sCDI_PARM& range)
{
  SetDistribution(DISTRIBUTION_UNIFORM, mean, range);
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::SetGaussianDistribution(const sCDI_PARM& mean, const sCDI_PARM& stdDev)
{
  SetDistribution(DISTRIBUTION_GAUSSIAN, mean, stdDev);
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::SetGammaDistribution(const sCDI_PARM& mean, const sCDI_PARM& shapeParam)
{
  SetDistribution(DISTRIBUTION_GAMMA, mean, shapeParam);
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::SetGaussianDistribution(const sCDI_PARM& mean, const sCDI_PARM& stdDev, const sCDI_PARM& minDia, const sCDI_PARM& maxDia) //PR_48032
{
  SetDistribution(DISTRIBUTION_GAUSSIAN, mean, stdDev, minDia, maxDia);
}
void cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION::SetGammaDistribution(const sCDI_PARM& mean, const sCDI_PARM& shapeParam, const sCDI_PARM& minDia, const sCDI_PARM& maxDia) //PR_48032
{
  SetDistribution(DISTRIBUTION_GAMMA, mean, shapeParam, minDia, maxDia);
}

void cCDI_PARTICLE_DIAMETER_DISTRIBUTION::GetRosinRammlerDistribution(sCDI_PARM* pCharacteristicValue, sCDI_PARM* pExponent, sCDI_PARM* minDia, sCDI_PARM* maxDia) const //PR_48032
{
  assert(GetDistributionType().value == DISTRIBUTION_ROSIN_RAMMLER);
  GetParameterValues(pCharacteristicValue, pExponent, minDia, maxDia);
}

void cCDI_PARTICLE_DIAMETER_DISTRIBUTION::GetRosinRammlerVolumeFractionDistribution(sCDI_PARM* pCharacteristicValue, sCDI_PARM* pExponent, sCDI_PARM* minDia, sCDI_PARM* maxDia) const //PR_48032
{
	assert(GetDistributionType().value == DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION);
	GetParameterValues(pCharacteristicValue, pExponent, minDia, maxDia);
}

void cCDI_PARTICLE_DIAMETER_DISTRIBUTION::GetLogNormalDistribution(sCDI_PARM* pMean, sCDI_PARM* pStdDev) const
{
  assert(GetDistributionType().value == DISTRIBUTION_LOG_NORMAL);
  GetParameterValues(pMean, pStdDev);
}

void cCDI_PARTICLE_DIAMETER_DISTRIBUTION::GetLogNormalDistribution(sCDI_PARM* pMean, sCDI_PARM* pStdDev, sCDI_PARM* minDia, sCDI_PARM* maxDia) const
{
	assert(GetDistributionType().value == DISTRIBUTION_LOG_NORMAL);
	GetParameterValues(pMean, pStdDev, minDia, maxDia);
}

void cCDI_PARTICLE_DIAMETER_DISTRIBUTION::SetRosinRammlerDistribution(const sCDI_PARM& characteristicValue, const sCDI_PARM& exponent) //PR_48032
{
	SetDistribution(DISTRIBUTION_ROSIN_RAMMLER, characteristicValue, exponent);
}

void cCDI_PARTICLE_DIAMETER_DISTRIBUTION::SetRosinRammlerDistribution(const sCDI_PARM& characteristicValue, const sCDI_PARM& exponent, const sCDI_PARM& minDia, const sCDI_PARM& maxDia) //PR_48032
{
  SetDistribution(DISTRIBUTION_ROSIN_RAMMLER, characteristicValue, exponent, minDia, maxDia);
}

void cCDI_PARTICLE_DIAMETER_DISTRIBUTION::SetRosinRammlerVolumeFractionDistribution(const sCDI_PARM& characteristicValue, const sCDI_PARM& exponent, const sCDI_PARM& minDia, const sCDI_PARM& maxDia) //PR_48032
{
	SetDistribution(DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION, characteristicValue, exponent, minDia, maxDia);
}

void cCDI_PARTICLE_DIAMETER_DISTRIBUTION::SetLogNormalDistribution(const sCDI_PARM& mean, const sCDI_PARM& stdDev, const sCDI_PARM& minDia, const sCDI_PARM& maxDia) //PR_48032
{
  SetDistribution(DISTRIBUTION_LOG_NORMAL, mean, stdDev, minDia, maxDia);
}

void cCDI_PARTICLE_DIAMETER_DISTRIBUTION::SetLogNormalDistribution(const sCDI_PARM& mean, const sCDI_PARM& stdDev) //PR_48032
{
	SetDistribution(DISTRIBUTION_LOG_NORMAL, mean, stdDev);
}

void sCDI_NOZZLE_EMITTER_CONFIG::WriteToCDI(CDI_INFO cdi_info) const
{
  SUPER_EMITTER_CONFIG::WriteToCDI(cdi_info);

  cdi_write_parm(cdi_info, &nozzle_type);
  cdi_write_parm(cdi_info, &angle_distribution);
  switch (cdiINT32(nozzle_type.value))
  {
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_FULL_CONE:
    cdi_write_parm(cdi_info, &cone_half_angle);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && angle_distribution.value == DISTRIBUTION_GAUSSIAN)
      cdi_write_parm(cdi_info, &outer_half_angle_limit);
    break;
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_HOLLOW_CONE:
    cdi_write_parm(cdi_info, &mean_angle);
    cdi_write_parm(cdi_info, &angle_range);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && angle_distribution.value == DISTRIBUTION_GAUSSIAN) {
      cdi_write_parm(cdi_info, &inner_half_angle_limit);
      cdi_write_parm(cdi_info, &outer_half_angle_limit);
    }
    break;
  case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_ELLIPTICAL_CONE:
    cdi_write_parm(cdi_info, &major_half_angle);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && angle_distribution.value == DISTRIBUTION_GAUSSIAN)
      cdi_write_parm(cdi_info, &major_outer_half_angle_limit);
    cdi_write_parm(cdi_info, &minor_half_angle);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && angle_distribution.value == DISTRIBUTION_GAUSSIAN)
      cdi_write_parm(cdi_info, &minor_outer_half_angle_limit);
    break;
  }
  velocity_info.WriteToCDI(cdi_info);
  particle_diam_info.WriteToCDI(cdi_info);
  cdi_write_parm(cdi_info, &emission_rate_type);
  cdi_write_parm(cdi_info, &emission_rate);
}
void sCDI_NOZZLE_EMITTER_CONFIG::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  SUPER_EMITTER_CONFIG::ReadFromCDI(cdi_info, pIndex);

  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &nozzle_type);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &angle_distribution);
  switch (cdiINT32(nozzle_type.value))
  {
  case NOZZLETYPE_FULL_CONE:
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &cone_half_angle);
    outer_half_angle_limit.value = M_PI;
    outer_half_angle_limit.preferredUnit = "deg";
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && angle_distribution.value == DISTRIBUTION_GAUSSIAN)
      cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &outer_half_angle_limit);
    break;
  case NOZZLETYPE_HOLLOW_CONE:
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &mean_angle);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &angle_range);
    outer_half_angle_limit.value = M_PI;
    outer_half_angle_limit.preferredUnit = "deg";
    inner_half_angle_limit.value = 0.0;
    inner_half_angle_limit.preferredUnit = "deg";
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && angle_distribution.value == DISTRIBUTION_GAUSSIAN) {
      cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &inner_half_angle_limit);
      cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &outer_half_angle_limit);
    }
    break;
  case NOZZLETYPE_ELLIPTICAL_CONE:
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &major_half_angle);
    major_outer_half_angle_limit.value = M_PI; 
    major_outer_half_angle_limit.preferredUnit = "deg";
    minor_outer_half_angle_limit.value = M_PI;
    minor_outer_half_angle_limit.preferredUnit = "deg";
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && angle_distribution.value == DISTRIBUTION_GAUSSIAN)
      cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &major_outer_half_angle_limit);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &minor_half_angle);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && angle_distribution.value == DISTRIBUTION_GAUSSIAN)
      cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &minor_outer_half_angle_limit);
    break;
  }
  velocity_info.ReadFromCDI(cdi_info, chunkName, pIndex);
  particle_diam_info.ReadFromCDI(cdi_info, chunkName, pIndex);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emission_rate_type);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emission_rate);
}

void sCDI_NOZZLE_EMITTER_CONFIG::ConvertToPRI(sCDI_INFO &cdi_info,
                                              PRI::cNOZZLE_EMITTER_CONFIGURATION& pri_emitter_config) const {
  set_pri_base_emitter_config_data(pri_emitter_config, this);
  set_pri_rain_emitter_config_data(pri_emitter_config, this); //In PRI, a nozzle emitter configuration uses a rain emitter configuration as a base class.
  set_pri_nozzle_emitter_config_data(cdi_info, pri_emitter_config, this);
}


void sCDI_RAIN_EMITTER_CONFIG::WriteToCDI(CDI_INFO cdi_info) const
{
  SUPER_EMITTER_CONFIG::WriteToCDI(cdi_info);
  particle_diam_info.WriteToCDI(cdi_info);
  cdi_write_parm(cdi_info, &emission_rate_type);
  cdi_write_parm(cdi_info, &emission_rate);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 11)) {
  cdi_write_parm(cdi_info, &wind_x_velocity);
  cdi_write_parm(cdi_info, &wind_y_velocity);
  cdi_write_parm(cdi_info, &wind_z_velocity);
  cdi_write_int_(cdi_info, csysIndex);
  cdi_write_int_(cdi_info, refFrameIndex);
  cdi_write_strg(cdi_info, refFrameName);
  }
}
void sCDI_RAIN_EMITTER_CONFIG::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  SUPER_EMITTER_CONFIG::ReadFromCDI(cdi_info, pIndex);

  particle_diam_info.ReadFromCDI(cdi_info, chunkName, pIndex);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emission_rate_type);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emission_rate);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 11)) {
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &wind_x_velocity);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &wind_y_velocity);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &wind_z_velocity);
  cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &csysIndex);
  cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &refFrameIndex);
  cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &refFrameName);
   }
  else{
   this->wind_x_velocity.value = 0;
   this->wind_y_velocity.value = 0;
   this->wind_z_velocity.value = 0;
   this->csysIndex = -1;
   this->refFrameIndex = -1;
   this->refFrameName ="Global Body-Fixed";
  }
}

void sCDI_RAIN_EMITTER_CONFIG::ConvertToPRI(PRI::cRAIN_EMITTER_CONFIGURATION& pri_emitter_config) const {
  set_pri_base_emitter_config_data(pri_emitter_config, this);
  set_pri_rain_emitter_config_data(pri_emitter_config, this);
  pri_emitter_config.SetWindVelocityCoordinateSystemIndex(cdi_csys_to_pri_csys((size_t)this->csysIndex));
  PRI::cCOMPOUND_FLOAT3  windVelocity(this->wind_x_velocity.value, this->wind_x_velocity.preferredUnit,this->wind_x_velocity.variableName,
                                      this->wind_y_velocity.value, this->wind_y_velocity.preferredUnit,this->wind_y_velocity.variableName,
                                      this->wind_z_velocity.value, this->wind_z_velocity.preferredUnit,this->wind_z_velocity.variableName);
  pri_emitter_config.SetWindVelocity(windVelocity);
  pri_emitter_config.SetReferenceFrame(this->refFrameName);

}

void sCDI_TIRE_NOZZLE_PROPS::WriteToCDI(CDI_INFO cdi_info) const
{
  WITH_CDI_CHUNK(cdi_info, GetChunkType()) {
    cdi_write_parm(cdi_info, &tire_arc_position);
    emission_offset_angle_info.WriteToCDI(cdi_info);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && emission_offset_angle_info.GetDistributionType().value == DISTRIBUTION_GAUSSIAN)
      cdi_write_parm(cdi_info, &outer_half_angle_limit);
    cdi_write_parm(cdi_info, &transverse_stretch_factor);
    velocity_info.WriteToCDI(cdi_info);
    particle_diam_info.WriteToCDI(cdi_info);
  }
}
void sCDI_TIRE_NOZZLE_PROPS::ReadFromCDI(CDI_INFO cdi_info)
{
  cio_descend(cdi_info->cio_info);
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);
  cdiINT32 index = 0;
  cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &tire_arc_position);
  emission_offset_angle_info.ReadFromCDI(cdi_info, chunkName, &index);
  outer_half_angle_limit.value = M_PI;
  outer_half_angle_limit.preferredUnit = "deg";
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && emission_offset_angle_info.GetDistributionType().value == DISTRIBUTION_GAUSSIAN)
    cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &outer_half_angle_limit);
  cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &transverse_stretch_factor);
  velocity_info.ReadFromCDI(cdi_info, chunkName, &index);
  particle_diam_info.ReadFromCDI(cdi_info, chunkName, &index);
  cio_ascend(cdi_info->cio_info);
}

CDI_DISTRIBUTION_TYPE sCDI_TIRE_NOZZLE_PROPS::ConvertToPRI(PRI::cNOZZLE_PROPERTIES &pri_nozzle_property, 
                                          PRI::cANGLE_NOZZLE_PROP_MAP &pri_nozzle_angle_map_element) const{
  //note PRI has the following parameters for each arc station.
  //void SetEmissionOffsetAngle(const cCOMPOUND_FLOAT& emissionOffsetAngle);
  //void SetConeHalfAngle(const cCOMPOUND_FLOAT& coneHalfAngle);
  //void SetHalfAngleStdDev(const cCOMPOUND_FLOAT& halfAngleStdDev);
  //void SetVelocityMagnitudeMean(const cCOMPOUND_FLOAT& velocityMagnitudeMean);
  //void SetVelocityMagnitudeRangeStdDev(const cCOMPOUND_FLOAT& velocityMagnitudeRangeStdDev);
  //void SetVelocityMagnitudeGammaTheta(const cCOMPOUND_FLOAT& velocityMagnitudeGammaTheta);
  //void SetParticleDiameterMean(const cCOMPOUND_FLOAT& particleDiameterMean);
  //void SetParticleDiameterRangeStdDev(const cCOMPOUND_FLOAT& particleDiameterRangeStdDev);
  //void SetParticleDiameterGammaTheta(const cCOMPOUND_FLOAT& particleDiameterGammaTheta);
  //void SetParticleDiameterRosinRammlerP80(const cCOMPOUND_FLOAT& particleDiameterRosinRammlerP80);
  //void SetParticleDiameterRosinRammlerM(const cCOMPOUND_FLOAT& particleDiameterRosinRammlerM);
  //void SetStretchFactor(float stretchFactor);                                                         
  //void SetOuterHalfAngleLimit(const cCOMPOUND_FLOAT& outerHalfAngleLimit);

  CDI_DISTRIBUTION_TYPE diameter_distribution = (CDI_DISTRIBUTION_TYPE)(int)this->particle_diam_info.GetDistributionType().value;
  CDI_DISTRIBUTION_TYPE velocity_distribution = (CDI_DISTRIBUTION_TYPE)(int)this->velocity_info.GetDistributionType().value;
  CDI_DISTRIBUTION_TYPE offset_angle_distribution = (CDI_DISTRIBUTION_TYPE)(int)this->emission_offset_angle_info.GetDistributionType().value;
 
  sCDI_PARM p1, p2;
  this->velocity_info.GetParameterValues(&p1, &p2);
  pri_nozzle_property.SetVelocityMagnitudeDistributionParameter1(p1);
  pri_nozzle_property.SetVelocityMagnitudeDistributionParameter2(p2);   

  switch(velocity_distribution) {
  case DISTRIBUTION_UNIFORM:
  case DISTRIBUTION_GAUSSIAN:
  case DISTRIBUTION_NONE:
    break;
  default:
    msg_error("Unsupported velocity magnitude distribution encountered in tire emitter nozzle property.");
  }
  
  this->particle_diam_info.GetParameterValues(&p1, &p2);
  pri_nozzle_property.SetParticleDiameterDistributionParameter1(p1);                          
  pri_nozzle_property.SetParticleDiameterDistributionParameter2(p2);            
 
  switch(diameter_distribution) {
  case DISTRIBUTION_UNIFORM:
  case DISTRIBUTION_GAUSSIAN:
  case DISTRIBUTION_GAMMA:
  case DISTRIBUTION_ROSIN_RAMMLER:
  case DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION: //PR_48032
  case DISTRIBUTION_LOG_NORMAL:
  case DISTRIBUTION_NONE:
    break;
  default:
    msg_error("Unsupported diameter distribution encountered in tire emitter nozzle property.");
  }
  pri_nozzle_property.SetTransverseStretchFactor(this->transverse_stretch_factor.value);                                                         
  pri_nozzle_angle_map_element.SetAngle(this->tire_arc_position.value);

  emission_offset_angle_info.GetParameterValues(&p1, &p2);
  pri_nozzle_property.SetEmissionOffsetAngle(p1); //the mean
  //pri_nozzle_property.SetConeHalfAngle(p2);   //The standard deviation or range.
  //pri_nozzle_property.SetHalfAngleStdDev(p2); //The standard deviation or range (see PR37835, fix #2).
  switch(offset_angle_distribution) {
  case DISTRIBUTION_UNIFORM:
    pri_nozzle_property.SetConeHalfAngle(p2);   //The standard deviation or range.
    break;
  case DISTRIBUTION_GAUSSIAN:
    pri_nozzle_property.SetHalfAngleStdDev(p2); //(see PR37835, fix #1).
    break;
  case DISTRIBUTION_NONE:
    break;
  default:
    msg_error("Unsupported offset angle distribution encountered in tire emitter nozzle property.");
  }

  pri_nozzle_property.SetOuterHalfAngleLimit(this->outer_half_angle_limit); 

  return(offset_angle_distribution);
}


void sCDI_TIRE_EMITTER_CONFIG::WriteToCDI(CDI_INFO cdi_info) const
{
  SUPER_EMITTER_CONFIG::WriteToCDI(cdi_info);

  cdi_write_parm(cdi_info, &emission_rate_type);
  cdi_write_parm(cdi_info, &emission_rate);
  cdi_write_parm(cdi_info, &spatial_emission_distribution);
  if (spatial_emission_distribution.value != DISTRIBUTION_LINEAR)
    cdi_write_parm(cdi_info, &spatial_emission_distribution_parameter);
  cdi_write_parm(cdi_info, &emission_rate_ratio);

  const std::vector<sCDI_TIRE_NOZZLE_PROPS>& tireNozzles = GetTireNozzles();
  for (size_t inz = 0; inz < tireNozzles.size(); inz++)
    tireNozzles[inz].WriteToCDI(cdi_info);
}
void sCDI_TIRE_EMITTER_CONFIG::ReadFromCDI(CDI_INFO cdi_info, asINT32 itemCount, cdiINT32* pIndex)
{
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  SUPER_EMITTER_CONFIG::ReadFromCDI(cdi_info, pIndex);

  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emission_rate_type);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emission_rate);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &spatial_emission_distribution);
  if (spatial_emission_distribution.value != DISTRIBUTION_LINEAR)
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &spatial_emission_distribution_parameter);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emission_rate_ratio);

  // Infer number of Tire Nozzles by subtracting the number of parameters read
  // so far from the total number of nested items detected in this CDI chunk.
  asINT32 numTireNozzles = itemCount - *pIndex;
  for (int itan = 0; itan < numTireNozzles; itan++)
  {
    sCDI_TIRE_NOZZLE_PROPS tireNozzle;
    tireNozzle.ReadFromCDI(cdi_info);
    AddTireNozzle(tireNozzle);
  }
}

void sCDI_TIRE_EMITTER_CONFIG::ConvertToPRI(PRI::cTIRE_EMITTER_CONFIGURATION &pri_emitter_config,
                                            std::vector<PRI::cNOZZLE_PROPERTIES> &pri_arc_station_properties,
                                            std::vector<PRI::cANGLE_NOZZLE_PROP_MAP> &pri_arc_station_map) const
{
  set_pri_base_emitter_config_data(pri_emitter_config, this);
  set_pri_tire_emitter_config_data(pri_emitter_config, pri_arc_station_properties, pri_arc_station_map, this);
}


void sCDI_TIRE_EMITTER_CONFIG::SetSpatialEmissionLinearDistribution()
{
  spatial_emission_distribution.value = DISTRIBUTION_LINEAR;
}
void sCDI_TIRE_EMITTER_CONFIG::SetSpatialEmissionGaussianDistribution(const sCDI_PARM& stdDev)
{
  spatial_emission_distribution.value = DISTRIBUTION_GAUSSIAN;
  spatial_emission_distribution_parameter = stdDev;
}
void sCDI_TIRE_EMITTER_CONFIG::SetSpatialEmission1MXDistribution(const sCDI_PARM& exponent)
{
  spatial_emission_distribution.value = DISTRIBUTION_1MX_POW_N;
  spatial_emission_distribution_parameter = exponent;
}
void sCDI_TIRE_EMITTER_CONFIG::SetSpatialEmissionHalfCosineDistribution(const sCDI_PARM& exponent)
{
  spatial_emission_distribution.value = DISTRIBUTION_HALF_COSINE;
  spatial_emission_distribution_parameter = exponent;
}

sCDI_PARM sCDI_TIRE_EMITTER_CONFIG::GetSpatialEmissionDistributionParameter() const
{
  return spatial_emission_distribution_parameter;
}

sCDI_PARM sCDI_TIRE_EMITTER_CONFIG::GetSpatialEmissionGaussianStandardDeviation() const
{
  assert(GetSpatialEmissionDistributionType().value == DISTRIBUTION_GAUSSIAN);
  return spatial_emission_distribution_parameter;
}
sCDI_PARM sCDI_TIRE_EMITTER_CONFIG::GetSpatialEmission1MXExponent() const
{
  assert(GetSpatialEmissionDistributionType().value == DISTRIBUTION_1MX_POW_N);
  return spatial_emission_distribution_parameter;
}
sCDI_PARM sCDI_TIRE_EMITTER_CONFIG::GetSpatialEmissionHalfCosineExponent() const
{
  assert(GetSpatialEmissionDistributionType().value == DISTRIBUTION_HALF_COSINE);
  return spatial_emission_distribution_parameter;
}


// =======================================================================================
// Particle Model - Emitters
// =======================================================================================

cCDI_PARTICLE_EMITTERS::~cCDI_PARTICLE_EMITTERS()
{
  for (size_t ie = 0; ie < m_emitters.size(); ie++)
    delete m_emitters[ie];
}

std::vector<const sCDI_SURFACE_EMITTER*> cCDI_PARTICLE_EMITTERS::GetSurfaceEmitters() const
{
  return GetEmittersOfType<sCDI_SURFACE_EMITTER>(*this);
}

std::vector<const sCDI_VOLUME_EMITTER*> cCDI_PARTICLE_EMITTERS::GetVolumeEmitters() const
{
  return GetEmittersOfType<sCDI_VOLUME_EMITTER>(*this);
}

std::vector<const sCDI_POINT_EMITTER*> cCDI_PARTICLE_EMITTERS::GetPointEmitters() const
{
  return GetEmittersOfType<sCDI_POINT_EMITTER>(*this);
}

std::vector<const sCDI_TIRE_EMITTER*> cCDI_PARTICLE_EMITTERS::GetTireEmitters() const
{
  return GetEmittersOfType<sCDI_TIRE_EMITTER>(*this);
}

std::vector<const sCDI_RAIN_EMITTER*> cCDI_PARTICLE_EMITTERS::GetRainEmitters() const
{
  return GetEmittersOfType<sCDI_RAIN_EMITTER>(*this);
}

void cCDI_PARTICLE_EMITTERS::UpdateEmitterTime(const std::vector<asINT32> &real_emitter_start_time, 
                                               const std::vector<asINT32> &real_emitter_end_time) 
{
  assert(m_emitters.size() == real_emitter_start_time.size());
  assert(m_emitters.size() == real_emitter_end_time.size());
  for (size_t emitter_index = 0; emitter_index < m_emitters.size(); emitter_index++) {
    sCDI_PARTICLE_EMITTER_BASE *emitter = const_cast<sCDI_PARTICLE_EMITTER_BASE *>(m_emitters[emitter_index]);
    if (emitter->start_via == eCDI_MEAS_START_TIME_VIA::AfterInitialTransient) {
      emitter->start.value = real_emitter_start_time[emitter_index];
      if (emitter->end_via == eCDI_MEAS_END_TIME_VIA::Duration)
        emitter->end.value = real_emitter_end_time[emitter_index];
    }
  }
}

void cCDI_PARTICLE_EMITTERS::WriteToCDI(CDI_INFO cdi_info) const
{
  const std::vector<const sCDI_PARTICLE_EMITTER_BASE*>& emitterPtrs = GetAllEmitters();
  for (size_t ie = 0; ie < emitterPtrs.size(); ie++)
  {
    WITH_CDI_CHUNK(cdi_info, emitterPtrs[ie]->GetChunkType()) {
      // Not part of the struct data.
      sCDI_PARM type; type.value = emitterPtrs[ie]->GetType();
      cdi_write_parm(cdi_info, &type);

      emitterPtrs[ie]->WriteToCDI(cdi_info);
    }
  }
}

void cCDI_PARTICLE_EMITTERS::ReadFromCDI(CDI_INFO cdi_info)
{
  CIO_INFO cio = cdi_info->cio_info;

  asINT32 count = cio_get_count(cio);

  for (int i = 0; i < count; i++)
  {
    cio_descend(cio);

    cdiINT32 index = 0;
    const char *chunkName = "pemc";

    // Read type first!  (not part of the configuration struct data)
    sCDI_PARM emitterType;
    cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &emitterType);

    std::unique_ptr<sCDI_PARTICLE_EMITTER_BASE> pEmitter;
    switch (cdiINT32(emitterType.value)) {
    case EMITTER_SURFACE:
      pEmitter.reset(new sCDI_SURFACE_EMITTER);
      break;
    case EMITTER_VOLUME:
      pEmitter.reset(new sCDI_VOLUME_EMITTER);
      break;
    case EMITTER_POINT:
      pEmitter.reset(new sCDI_POINT_EMITTER);
      break;
    case EMITTER_TIRE:
      pEmitter.reset(new sCDI_TIRE_EMITTER);
      break;
    case EMITTER_RAIN:
      pEmitter.reset(new sCDI_RAIN_EMITTER);
      break;
    default:
      assert(false);
    }

    pEmitter->ReadFromCDI(cdi_info, &index);
    AddEmitter(pEmitter.release());

    cio_ascend(cio);
  }
}


asINT32 find_entity_geometry_by_name(const std::string &name, 
                                     const std::vector<PRI::cENTITY_GEOMETRY>& entity_geometry_list) {
  asINT32 n_entity_geometries = entity_geometry_list.size();
  ccDOTIMES(entity_index, n_entity_geometries) {
    std::string entity_name = entity_geometry_list[entity_index].GetName();
    if(name == entity_name)
      return entity_index;
  }
  return -1;
}


void cCDI_PARTICLE_EMITTERS::ConvertToPRI(PRI::cPARAMETERS &pri_parameters, 
                                          const std::vector<std::string> &region_names, 
                                          const std::vector<std::string> &sri_face_names, 
                                          const std::vector<std::size_t> &cdi_to_pri_emitter_configuration_map,
                                          const std::vector<asINT32> &real_emitter_start_time,
                                          const std::vector<asINT32> &real_emitter_end_time)
{
  //Copy the emitters contained in this class to the emitters in a PRI parameters class.
  //Also remap the emitter configuration indices and add the proper references from the emitters to the proper 
  //entity geometries.
 
  //  std::vector<PRI::cCOORDINATE_SYSTEM>& csys_list = pri_parameters.GetCoordinateSystemList();
  std::vector<PRI::cENTITY_GEOMETRY>& pri_entity_geometry_list = pri_parameters.GetEntityGeometryList();
  std::vector<PRI::cEMITTER_GEOMETRY_REFERENCE>& pri_geometry_reference_list = 
    pri_parameters.GetEmitterGeometryReferenceList();

  // Update the emitter start and time time for those started via monitors
  // Note that this only works when there is only one emitter in the case and it is controlled by monitors.
  // If there are multiple monitors and some start (via time) first, then the parameters for all emitters
  // will be written to the pri file. Even some monitors start later since monitors reach the end of initial
  // transient, the start time in pri will not be updated since the parameters are already written.
  UpdateEmitterTime(real_emitter_start_time, real_emitter_end_time);

  //Retrieve the vector of CDI surface emitters and write the data to the vector of pri surface emitters.
  std::vector<const sCDI_SURFACE_EMITTER*> cdi_surface_emitters = GetSurfaceEmitters();
  std::vector<PRI::cSURFACE_EMITTER>& pri_surface_emitters = pri_parameters.GetSurfaceEmitterList();
  pri_surface_emitters.resize(cdi_surface_emitters.size());
  ccDOTIMES(emitter_index, cdi_surface_emitters.size()) {
    //Populate the fields of the surface emitter.

    cdi_surface_emitters[emitter_index]->ConvertToPRI(pri_surface_emitters[emitter_index]);
    //Remap the configuration index from the cdi convention to the pri convention (PR40128/PR40179)
    std::size_t cdi_config_index = pri_surface_emitters[emitter_index].GetEmitterConfigurationIndex();
    std::size_t pri_config_index = cdi_to_pri_emitter_configuration_map[cdi_config_index];
    pri_surface_emitters[emitter_index].SetEmitterConfigurationIndex(pri_config_index);

    //For each face, create a geometry reference record that refers to the proper element in the entity geometry table.
    asINT32 n_faces = cdi_surface_emitters[emitter_index]->geom_selection.m_selections.face_list.size();
    asINT32 first_geometry_reference = pri_geometry_reference_list.size();
    asINT32 last_geometry_reference = first_geometry_reference;
    
    ccDOTIMES(face_list_index, n_faces) {
      asINT32 face_index = cdi_surface_emitters[emitter_index]->geom_selection.m_selections.face_list[face_list_index];
      asINT32 entity_geometry_index = find_entity_geometry_by_name(sri_face_names[face_index], pri_entity_geometry_list); 
 
      PRI::cEMITTER_GEOMETRY_REFERENCE pri_geometry_reference; //Create a reference for this emitter's geom to the 
                                                               //entity geometry added above.
      pri_geometry_reference.SetGeometryType(PRI::GEOMETRY_TYPE_ENTITY);
      pri_geometry_reference.SetGeometryIndex(entity_geometry_index); 
      pri_geometry_reference_list.push_back(pri_geometry_reference);
      last_geometry_reference++;
    }
    pri_surface_emitters[emitter_index].SetEmitterGeometryIndices(PRI::VECTOR2_INDEX(first_geometry_reference, 
                                                                                    last_geometry_reference)); 
  }

  //Retrieve the vector of CDI volume emitters and write the data to the vector of pri volume emitters.
  std::vector<const sCDI_VOLUME_EMITTER*> cdi_volume_emitters = GetVolumeEmitters();
  std::vector<PRI::cVOLUME_EMITTER>& pri_volume_emitters = pri_parameters.GetVolumeEmitterList();
  pri_volume_emitters.resize(cdi_volume_emitters.size());
  ccDOTIMES(emitter_index, cdi_volume_emitters.size()) {
    asINT32 n_cylinders = cdi_volume_emitters.at(emitter_index)->m_cylGeometries.size();
    asINT32 n_boxes = cdi_volume_emitters[emitter_index]->m_boxGeometries.size();
    asINT32 first_geometry_reference = pri_geometry_reference_list.size();
    asINT32 last_geometry_reference = first_geometry_reference;

   //For each cylinder, create a PRI geometry reference.    
    ccDOTIMES(cylinder_index, n_cylinders) { 
      const sCDI_CYL_ &cdi_cylinder = cdi_volume_emitters[emitter_index]->m_cylGeometries[cylinder_index];
      asINT32 entity_geometry_index = find_entity_geometry_by_name(cdi_cylinder.name, pri_entity_geometry_list); 
      PRI::cEMITTER_GEOMETRY_REFERENCE pri_geometry_reference; //Create a reference for this emitter's geom to the 
                                                               //entity geometry added above.
      pri_geometry_reference.SetGeometryType(PRI::GEOMETRY_TYPE_ENTITY);
      pri_geometry_reference.SetGeometryIndex(entity_geometry_index); 
      pri_geometry_reference_list.push_back(pri_geometry_reference);
      last_geometry_reference++;
    }
    
    //Repeat the above for boxes.
    ccDOTIMES(box_index, n_boxes) {
      const sCDI_BOX_ &cdi_box = cdi_volume_emitters[emitter_index]->m_boxGeometries[box_index];
      asINT32 region_index = cdi_box.region.regions[0];
      if(region_index >= (asINT32) region_names.size())
        msg_error("Region index of the box used for volume emitter \"%s\" is out of range.\n",
                  cdi_volume_emitters[emitter_index]->name.c_str());
      const std::string &box_name = region_names[region_index];
      asINT32 entity_geometry_index = find_entity_geometry_by_name(box_name, pri_entity_geometry_list); 
      PRI::cEMITTER_GEOMETRY_REFERENCE pri_geometry_reference; //Create a reference for this emitter's geom to the 
                                                               //entity geometry added above.
      pri_geometry_reference.SetGeometryType(PRI::GEOMETRY_TYPE_ENTITY);
      pri_geometry_reference.SetGeometryIndex(entity_geometry_index); 
      pri_geometry_reference_list.push_back(pri_geometry_reference);
      last_geometry_reference++;
    }

    //Convert the cdi emitter_data into the pri_volume_emitters[emitter_index] object.
    cdi_volume_emitters[emitter_index]->ConvertToPRI(pri_volume_emitters[emitter_index]);

    //Remap the configuration index from the cdi convention to the pri convention (PR40128/PR40179)
    std::size_t cdi_config_index = pri_volume_emitters[emitter_index].GetEmitterConfigurationIndex();
    std::size_t pri_config_index = cdi_to_pri_emitter_configuration_map[cdi_config_index];
    pri_volume_emitters[emitter_index].SetEmitterConfigurationIndex(pri_config_index);

    //Set the gometry reference indices range in the newly created PRI emitter
    pri_volume_emitters[emitter_index].SetEmitterGeometryIndices(PRI::VECTOR2_INDEX(first_geometry_reference, 
                                                                                    last_geometry_reference));
  }  

  //Retrieve the vector of CDI point emitters and write the data to the vector of pri point emitters.
  std::vector<const sCDI_POINT_EMITTER*> cdi_point_emitters = GetPointEmitters();
  std::vector<PRI::cPOINT_EMITTER>& pri_point_emitters = pri_parameters.GetPointEmitterList();
  std::vector<PRI::cPOINT>& pri_point_list = pri_parameters.GetPointEmitterPointsList();
  pri_point_emitters.resize(cdi_point_emitters.size());
  ccDOTIMES(emitter_index, cdi_point_emitters.size()) {
    //Create the geometry records and emitter geometry reference list:
    asINT32 n_points = cdi_point_emitters[emitter_index]->points.points.size() / 3;
    asINT32 first_point_reference = pri_point_list.size();
    asINT32 last_point_reference = pri_point_list.size();

    const sCDI_VECOF_PNTS &cdi_points = cdi_point_emitters[emitter_index]->points;
    ccDOTIMES(point_index, n_points) {
      PRI::cPOINT pri_point;
      cdi_points.ConvertToPRI(point_index, pri_point);
      pri_point_list.push_back(pri_point);
      last_point_reference++;
    }

    //Convert the cdi emitter data into the pri_point_emitters[emitter_index] object.
    cdi_point_emitters[emitter_index]->ConvertToPRI(pri_point_emitters[emitter_index]);

    //Remap the configuration index from the cdi convention to the pri convention (PR40128/PR40179)
    std::size_t cdi_config_index = pri_point_emitters[emitter_index].GetEmitterConfigurationIndex();
    std::size_t pri_config_index = cdi_to_pri_emitter_configuration_map[cdi_config_index];
    pri_point_emitters[emitter_index].SetEmitterConfigurationIndex(pri_config_index);

    //Set the gometry reference indices range in the newly created PRI emitter
    //These indices should reference the vector of PRI points and not the geometry reference vector
    pri_point_emitters[emitter_index].SetPointIndices(PRI::VECTOR2_INDEX(first_point_reference, 
                                                                         last_point_reference));
  } 
 
  //Retrieve the vector of CDI tire emitters and write the data to the vector of pri tire emitters.
  std::vector<const sCDI_TIRE_EMITTER*> cdi_tire_emitters = GetTireEmitters();
  std::vector<PRI::cTIRE_EMITTER>& pri_tire_emitters = pri_parameters.GetTireEmitterList();
  pri_tire_emitters.resize(cdi_tire_emitters.size());
  ccDOTIMES(emitter_index, cdi_tire_emitters.size()) {
    //Create the geometry records and emitter geometry reference list:

    asINT32 first_geometry_reference = pri_geometry_reference_list.size();
    asINT32 last_geometry_reference = first_geometry_reference;
    asINT32 n_cylinders = cdi_tire_emitters.at(emitter_index)->m_cylGeometries.size();

    //For each cylinder, create a PRI geometry reference.    
    ccDOTIMES(cylinder_index, n_cylinders) { 
      const sCDI_CYL_ &cdi_cylinder = cdi_tire_emitters[emitter_index]->m_cylGeometries[cylinder_index];
      asINT32 entity_geometry_index = find_entity_geometry_by_name(cdi_cylinder.name, pri_entity_geometry_list); 
      PRI::cEMITTER_GEOMETRY_REFERENCE pri_geometry_reference; //Create a reference for this emitter's geom to the 
                                                               //entity geometry added above.
      pri_geometry_reference.SetGeometryType(PRI::GEOMETRY_TYPE_ENTITY);
      pri_geometry_reference.SetGeometryIndex(entity_geometry_index); 
      pri_geometry_reference_list.push_back(pri_geometry_reference);
      last_geometry_reference++;
    }

    //Convert the cdi emitter data into the pri_tire_emitters[emitter_index] object.
    cdi_tire_emitters[emitter_index]->ConvertToPRI(pri_tire_emitters[emitter_index]);

    //Remap the configuration index from the cdi convention to the pri convention (PR40128/PR40179)
    std::size_t cdi_config_index = pri_tire_emitters[emitter_index].GetEmitterConfigurationIndex();
    std::size_t pri_config_index = cdi_to_pri_emitter_configuration_map[cdi_config_index];
    pri_tire_emitters[emitter_index].SetEmitterConfigurationIndex(pri_config_index);
 
    //Set the gometry reference indices range in the newly created PRI emitter
    pri_tire_emitters[emitter_index].SetEmitterGeometryIndices(PRI::VECTOR2_INDEX(first_geometry_reference, 
                                                                                  last_geometry_reference));
  } 

  //Retrieve the vector of CDI rain emitters and write the data to the vector of pri rain emitters.
  std::vector<const sCDI_RAIN_EMITTER*> cdi_rain_emitters = GetRainEmitters();
  std::vector<PRI::cRAIN_EMITTER>& pri_rain_emitters = pri_parameters.GetRainEmitterList();
  pri_rain_emitters.resize(cdi_rain_emitters.size());
  ccDOTIMES(emitter_index, cdi_rain_emitters.size()) {
    //Create the geometry records and emitter geometry reference list:
    asINT32 n_cylinders = cdi_rain_emitters.at(emitter_index)->m_cylGeometries.size();
    asINT32 n_boxes = cdi_rain_emitters[emitter_index]->m_boxGeometries.size();

    asINT32 first_geometry_reference = pri_geometry_reference_list.size();
    asINT32 last_geometry_reference = first_geometry_reference;

    //For each cylinder, create a PRI geometry reference.    
    ccDOTIMES(cylinder_index, n_cylinders) { 
      const sCDI_CYL_ &cdi_cylinder = cdi_rain_emitters[emitter_index]->m_cylGeometries[cylinder_index];
      asINT32 entity_geometry_index = find_entity_geometry_by_name(cdi_cylinder.name, pri_entity_geometry_list); 
      PRI::cEMITTER_GEOMETRY_REFERENCE pri_geometry_reference; //Create a reference for this emitter's geom to the 
                                                               //entity geometry added above.
      pri_geometry_reference.SetGeometryType(PRI::GEOMETRY_TYPE_ENTITY);
      pri_geometry_reference.SetGeometryIndex(entity_geometry_index); 
      pri_geometry_reference_list.push_back(pri_geometry_reference);
      last_geometry_reference++;
    }
    
    //Repeat the above for boxes.
    ccDOTIMES(box_index, n_boxes) {
      const sCDI_BOX_ &cdi_box = cdi_rain_emitters[emitter_index]->m_boxGeometries[box_index];
      asINT32 region_index = cdi_box.region.regions[0];
      if((size_t) region_index >= region_names.size())
        msg_error("Region index of the box used for rain emitter \"%s\" is out of range.\n",
                  cdi_rain_emitters[emitter_index]->name.c_str());
      const std::string &box_name = region_names[region_index];

      asINT32 entity_geometry_index = find_entity_geometry_by_name(box_name, pri_entity_geometry_list); 
      PRI::cEMITTER_GEOMETRY_REFERENCE pri_geometry_reference; //Create a reference for this emitter's geom to the 
                                                               //entity geometry added above.
      pri_geometry_reference.SetGeometryType(PRI::GEOMETRY_TYPE_ENTITY);
      pri_geometry_reference.SetGeometryIndex(entity_geometry_index); 
      pri_geometry_reference_list.push_back(pri_geometry_reference);
      last_geometry_reference++;
    }

    //Convert the cdi emitter data into the pri_rain_emitters[emitter_index] object.
    cdi_rain_emitters[emitter_index]->ConvertToPRI(pri_rain_emitters[emitter_index]);
    
    //Remap the configuration index from the cdi convention to the pri convention (PR40128/PR40179)
    std::size_t cdi_config_index = pri_rain_emitters[emitter_index].GetEmitterConfigurationIndex();
    std::size_t pri_config_index = cdi_to_pri_emitter_configuration_map[cdi_config_index];
    pri_rain_emitters[emitter_index].SetEmitterConfigurationIndex(pri_config_index);

    //Set the gometry reference indices range in the newly created PRI emitter
    pri_rain_emitters[emitter_index].SetEmitterGeometryIndices(PRI::VECTOR2_INDEX(first_geometry_reference, 
                                                                                  last_geometry_reference));

  } 
 
}

void sCDI_PARTICLE_EMITTER_BASE::WriteToCDI(CDI_INFO cdi_info) const
{
  sCDI_NAME cdiName;
  cdiName.name = (char*)name.c_str();
  cdiName.n_char = name.size();
  cdi_write_name(cdi_info, &cdiName);

  cdi_write_parm(cdi_info, &emitter_configuration);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 7))
    cdi_write_parm(cdi_info, &particles_per_parcel);
  cdi_write_parm(cdi_info, &subject_to_dispersion_box);
  cdi_write_parm(cdi_info, &subject_to_gravity);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 3)) {
    cdi_write_enum(cdi_info, start_via);
    cdi_write_int_(cdi_info, monitors.size());
    ccDOTIMES(i, monitors.size())
      cdi_write_int_(cdi_info, monitors[i]);
    cdi_write_parm(cdi_info, &start);
    cdi_write_enum(cdi_info, end_via);
    cdi_write_parm(cdi_info, &end);
    cdi_write_parm(cdi_info, &duration);
  } else {
    cdi_write_parm(cdi_info, &start);
    cdi_write_parm(cdi_info, &end);
  }

  { // Even though 'frac_trajectory_recording' has been removed from sCDI_PARTICLE_EMITTER_BASE for 5.4b  (CDI v > 5.2), 
    // we keep file format the same, so that PowerVIZ 5.4a will have forward compatibility with files generated from 5.4b.
    sCDI_PARM fakeFracTrajRecord;
    fakeFracTrajRecord.value = 0.001;
    fakeFracTrajRecord.preferredUnit = "dimensionless";
    fakeFracTrajRecord.defaulted = true;
    cdi_write_parm(cdi_info, &fakeFracTrajRecord);
  }
  cdi_write_parm(cdi_info, &max_age);
  cdi_write_parm(cdi_info, &max_num_reflections);
  cdi_write_parm(cdi_info, &min_particle_velocity);
  cdi_write_parm(cdi_info, &visible);
}
void sCDI_PARTICLE_EMITTER_BASE::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  int& localIndexVarForMacro = *pIndex;    // :-(
  ccCDI_DO_INNER_CHUNK(localIndexVarForMacro, chunkName, cdi_info)
  {
    sCDI_NAME* pCDIName = cdi_read_name(cdi_info);
    name = pCDIName->name;
    cdi_destroy_name(pCDIName);
  }
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emitter_configuration);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 7))
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &particles_per_parcel);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &subject_to_dispersion_box);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &subject_to_gravity);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 3)) {
    cdi_inner_chunk_read_enum(*pIndex, "pemc", cdi_info, &(start_via));
    cdiINT32 numMonitors = 0;
    cdiINT32 monitorIndex = -1;
    cdi_inner_chunk_read_int_(*pIndex, "pemc", cdi_info, &numMonitors);
    ccDOTIMES(i, numMonitors) {
      cdi_inner_chunk_read_int_(*pIndex, "pemc", cdi_info, &monitorIndex);
      monitors.push_back(monitorIndex);
    }

    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &start);
    cdi_inner_chunk_read_enum(*pIndex, "pemc", cdi_info, &(end_via));
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &end);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &duration);
  } else {
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &start);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &end);
  }

  { // Even though 'frac_trajectory_recording' has been removed from sCDI_PARTICLE_EMITTER_BASE for 5.4b  (CDI v > 5.2), 
    // we keep file format the same, so that PowerVIZ 5.4a will have forward compatibility with files generated from 5.4b.
    // So read the parm chunk, but then throw it away
    sCDI_PARM fakeFracTrajRecord;
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &fakeFracTrajRecord);
  }
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &max_age);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &max_num_reflections);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &min_particle_velocity);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &visible);
}

  
void sCDI_EMITTER_NOZZLE_ORIENTATION::WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_write_parm(cdi_info, &spray_direction_csys);
  cdi_write_parm(cdi_info, &mean_spray_direction[0]);
  cdi_write_parm(cdi_info, &mean_spray_direction[1]);
  cdi_write_parm(cdi_info, &mean_spray_direction[2]);
  cdi_write_parm(cdi_info, &elliptical_nozzle);
  if (elliptical_nozzle.value == 1)
  {
    cdi_write_parm(cdi_info, &major_ellipse_direction[0]);
    cdi_write_parm(cdi_info, &major_ellipse_direction[1]);
    cdi_write_parm(cdi_info, &major_ellipse_direction[2]);
  }
}
void sCDI_EMITTER_NOZZLE_ORIENTATION::ReadFromCDI(CDI_INFO cdi_info, const char* chunkName, cdiINT32* pIndex)
{
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &spray_direction_csys);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &mean_spray_direction[0]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &mean_spray_direction[1]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &mean_spray_direction[2]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &elliptical_nozzle);
  if (elliptical_nozzle.value == 1)
  {
    major_ellipse_direction.resize(3);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &major_ellipse_direction[0]);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &major_ellipse_direction[1]);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &major_ellipse_direction[2]);
  }
}

sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS::
sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS() :
  // Green
  nozzle_cone_look("d:#00ff00 s:#003f00 e:#000000 g:128.000000 t:0.000000"),
  // Blue
  nozzle_body_color("#0000ff"),
  // Red
  nozzle_arrow_look("d:#ff0000 s:#3f0000 e:#000000 g:128.000000 t:0.000000")
{
}
void
sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS::
WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_write_parm(cdi_info, &nozzle_show);
  if ((nozzle_show.value == 1) || CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5)) {
    cdi_write_strg(cdi_info, nozzle_cone_look);
    cdi_write_strg(cdi_info, nozzle_body_color);
    cdi_write_strg(cdi_info, nozzle_arrow_look);
    cdi_write_parm(cdi_info, &nozzle_size);
  }
}
void
sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS::
ReadFromCDI(CDI_INFO cdi_info, const char* chunkName, cdiINT32* pIndex)
{
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &nozzle_show);
  if ((nozzle_show.value == 1) || CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5)) {
    cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &nozzle_cone_look);
    cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &nozzle_body_color);
    cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &nozzle_arrow_look);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &nozzle_size);
  }
}


sCDI_EMITTER_EMISSION_BOUNDARY_VISIBILITY_SETTINGS::
sCDI_EMITTER_EMISSION_BOUNDARY_VISIBILITY_SETTINGS() :
  // Off-white
  emission_boundary_look("d:#f5f5d3 s:#3d3d34 e:#000000 g:128.000000 t:0.000000")
{
}
void
sCDI_EMITTER_EMISSION_BOUNDARY_VISIBILITY_SETTINGS::
WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_write_parm(cdi_info, &emission_boundary_show);
  if ((emission_boundary_show.value == 1) || CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5)) {
    cdi_write_strg(cdi_info, emission_boundary_look);
    cdi_write_parm(cdi_info, &emission_boundary_size);
  }
}
void
sCDI_EMITTER_EMISSION_BOUNDARY_VISIBILITY_SETTINGS::
ReadFromCDI(CDI_INFO cdi_info, const char* chunkName, cdiINT32* pIndex)
{
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emission_boundary_show);
  if ((emission_boundary_show.value == 1) || CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5)) {
    cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &emission_boundary_look);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emission_boundary_size);
  }
}


sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS::
sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS() :
  // Blue
  emission_point_color("#0000ff")
{
}
void
sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS::
WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_write_bool(cdi_info, emission_point_show);
  if (emission_point_show || CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5)) {
    cdi_write_strg(cdi_info, emission_point_color);
    cdi_write_parm(cdi_info, &emission_point_size);
  }
};
void
sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS::
ReadFromCDI(CDI_INFO cdi_info, const char* chunkName, cdiINT32* pIndex)
{
  cdi_inner_chunk_read_bool(*pIndex, chunkName, cdi_info, &emission_point_show);
  if (emission_point_show || CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5)) {
    cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &emission_point_color);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &emission_point_size);
  }
}


void sCDI_CYL_::WriteToCDI(CDI_INFO cdi_info) const
{
  ASSERT_VALID_CDI_INFO(cdi_info);
  cdi_push(cdi_info, GetChunkType());

  sCDI_NAME cdiName;
  cdiName.name = (char*)name.c_str();
  cdiName.n_char = name.size();
  cdi_write_name(cdi_info, &cdiName);

  cdi_write_rgns(cdi_info, const_cast<std::vector<cdiINT32>&>(region.regions));

  cdi_write_parm(cdi_info, &start_point[0]);
  cdi_write_parm(cdi_info, &start_point[1]);
  cdi_write_parm(cdi_info, &start_point[2]);
  cdi_write_parm(cdi_info, &end_point[0]);
  cdi_write_parm(cdi_info, &end_point[1]);
  cdi_write_parm(cdi_info, &end_point[2]);
  cdi_write_parm(cdi_info, &start_radius);
  cdi_write_parm(cdi_info, &end_radius);
  cdi_write_parm(cdi_info, &num_sides);
  cdi_pop(cdi_info);
}
void sCDI_CYL_::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  int& localIndexVarForMacro = *pIndex;    // :-(
  ccCDI_DO_INNER_CHUNK(localIndexVarForMacro, chunkName, cdi_info)
  {
    sCDI_NAME* pCDIName = cdi_read_name(cdi_info);
    name = pCDIName->name;
    cdi_destroy_name(pCDIName);
  }

  cdi_inner_chunk_read_rgns(*pIndex, chunkName, cdi_info, region.regions);

  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &start_point[0]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &start_point[1]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &start_point[2]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &end_point[0]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &end_point[1]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &end_point[2]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &start_radius);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &end_radius);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &num_sides);
}


void sCDI_CYL_::ConvertToPRI(PRI::cCYLINDER_VIA_ENDPOINTS &pri_cylinder) const {
  pri_cylinder.SetEndPoint1(PRI::GetCompoundFloat3(start_point[0], start_point[1], start_point[2]));
  pri_cylinder.SetEndPoint2(PRI::GetCompoundFloat3(end_point[0], end_point[1], end_point[2]));
  pri_cylinder.SetRadius1(start_radius);
  pri_cylinder.SetRadius2(end_radius);
  pri_cylinder.SetNumberOfFaces((int)num_sides.value);
  pri_cylinder.SetCoordinateSystemIndex(0); //Cylinders are provided in the lattice csys (unlike sCDI_BOX objects).
}

void sCDI_BOX_::ConvertToPRI(PRI::cBOX_VIA_CORNERS &pri_box) const {

  sFLOAT corner1_coords[3];
  corner1_coords[0] = (sFLOAT)bbox.coord[0][0];
  corner1_coords[1] = (sFLOAT)bbox.coord[1][0];
  corner1_coords[2] = (sFLOAT)bbox.coord[2][0];
  
  sFLOAT corner2_coords[3];
  corner2_coords[0] = (sFLOAT)bbox.coord[0][1];
  corner2_coords[1] = (sFLOAT)bbox.coord[1][1];
  corner2_coords[2] = (sFLOAT)bbox.coord[2][1];
  
  std::string units[3];
  units[0] = "LatticeLength";
  units[1] = "LatticeLength";
  units[2] = "LatticeLength";
  
  std::string var_names[3];
  var_names[0] = "";
  var_names[1] = "";
  var_names[2] = "";
  
  PRI::cCOMPOUND_FLOAT3 corner1(std::vector<sFLOAT>(corner1_coords, corner1_coords + 3),
                                std::vector<std::string>(units, units + 3),
                                std::vector<std::string>(var_names, var_names + 3));

  PRI::cCOMPOUND_FLOAT3 corner2(std::vector<sFLOAT>(corner2_coords, corner2_coords + 3),
                                std::vector<std::string>(units, units + 3),
                                std::vector<std::string>(var_names, var_names + 3));

  pri_box.SetCorner1(corner1);
  pri_box.SetCorner2(corner2);
  pri_box.SetCoordinateSystemIndex(cdi_csys_to_pri_csys((size_t)csys.value));
}


static void WriteEmitterGeometriesToCDI(CDI_INFO cdi_info, const std::vector<sCDI_CYL_>* pCylGeoms, const std::vector<sCDI_BOX_>* pBoxGeoms)
{
  if (pCylGeoms)
  {
    const std::vector<sCDI_CYL_>& cylGeoms = *pCylGeoms;
      WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_CYLZ) {
        for (size_t ic = 0; ic < cylGeoms.size(); ic++)
          cylGeoms[ic].WriteToCDI(cdi_info);
      }
  }

  if (pBoxGeoms)
  {
    const std::vector<sCDI_BOX_>& boxGeoms = *pBoxGeoms;
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_BOXS) {
      for (size_t ib = 0; ib < boxGeoms.size(); ib++)
      {
        sCDI_BOX_* pBox = const_cast<sCDI_BOX_*>(&(boxGeoms[ib]));
        cdi_write_box_(cdi_info, pBox);
      }
    }
  }
}
static void ReadEmitterGeometriesFromCDI(CDI_INFO cdi_info, std::vector<sCDI_CYL_>* pCylGeoms, std::vector<sCDI_BOX_>* pBoxGeoms, int* pIndex)
{
  int& localIndexVarForMacro = *pIndex;    // :-(

  if (pCylGeoms)
  {
    char cylzChunkName[10];
    cio_type_to_string(CDI_CHUNK_TYPE_CYLZ, cylzChunkName);
    ccCDI_DO_INNER_CHUNK(localIndexVarForMacro, cylzChunkName, cdi_info) {
      cdiINT32 n_cyls = cio_get_count(cdi_info->cio_info);
      cdiINT32 index = 0;
      ccDOTIMES(ib, n_cyls) {
        sCDI_CYL_ cyl_;
        char chunkName[10];
        cio_type_to_string(cyl_.GetChunkType(), chunkName);
        ccCDI_DO_INNER_CHUNK(index, chunkName, cdi_info) {
          cyl_.ReadFromCDI(cdi_info, &index);
        }
        pCylGeoms->push_back(cyl_);
      }
    }
  }

  if (pBoxGeoms)
  {
    char boxsChunkName[10];
    cio_type_to_string(CDI_CHUNK_TYPE_BOXS, boxsChunkName);
    ccCDI_DO_INNER_CHUNK(localIndexVarForMacro, boxsChunkName, cdi_info) {
      cdiINT32 n_box_s = cio_get_count(cdi_info->cio_info);
      cdiINT32 index = 0;
      ccDOTIMES(ib, n_box_s) {
        sCDI_BOX_ box_;
        char chunkName[10];
        cio_type_to_string(CDI_CHUNK_TYPE_BOX_, chunkName);
        ccCDI_DO_INNER_CHUNK(index, chunkName, cdi_info) {
          cdi_read_box_(cdi_info, box_);
        }
        pBoxGeoms->push_back(box_);
      }
    }
  }
}


void sCDI_SURFACE_EMITTER::WriteToCDI(CDI_INFO cdi_info) const
{
  SUPER_EMITTER::WriteToCDI(cdi_info);

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(cdi_info))
    cdi_write_flst(cdi_info, geom_selection.m_selections.face_list);

  cdi_write_parm(cdi_info, &fixed_release_points);
  cdi_write_parm(cdi_info, &user_specified_nozzle_orientation);
  if (user_specified_nozzle_orientation.value == 1 || CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 16))
    nozzle_orientation.WriteToCDI(cdi_info);

  nozzle_visibility_settings.WriteToCDI(cdi_info);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
    emission_point_visibility_settings.WriteToCDI(cdi_info);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(cdi_info)) {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_GEOS) {
      geom_selection.WriteToCDI(cdi_info);
    }
  }
}
void sCDI_SURFACE_EMITTER::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  SUPER_EMITTER::ReadFromCDI(cdi_info, pIndex);

  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(cdi_info))
    cdi_inner_chunk_read_flst(*pIndex, chunkName, cdi_info, geom_selection.m_selections.face_list);

  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &fixed_release_points);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &user_specified_nozzle_orientation);
  if (user_specified_nozzle_orientation.value == 1 || CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 16))
    nozzle_orientation.ReadFromCDI(cdi_info, chunkName, pIndex);

  nozzle_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
    emission_point_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(cdi_info)) {
    CDI_WITH_INNER_CHUNK(cdi_info) {
      if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GEOS) {
        cCDI_GEOM_SELECTION_TREE* read_geos = cdi_read_geos(cdi_info);
        geom_selection = *read_geos;
        cdi_destroy_geos(read_geos);
      }
    }
  }
}

void sCDI_SURFACE_EMITTER::ConvertToPRI(PRI::cSURFACE_EMITTER& pri_emitter) const //Write data from this struct to a PRI::cSURFACE_EMITTER class
{
  set_pri_base_emitter_data(pri_emitter, *this); //Write the base class' data
  set_pri_directed_emitter_data(pri_emitter, *this); //set the fields specific to a PRI directed emitter.
  set_pri_geometry_emitter_data(pri_emitter, *this);    //Set the fields specific to a pri geometry emitter.
  set_pri_surface_emitter_data(pri_emitter, *this); //set the fields specific to a PRI surface emitter.
}

void sCDI_VOLUME_EMITTER::WriteToCDI(CDI_INFO cdi_info) const
{
  SUPER_EMITTER::WriteToCDI(cdi_info);

  // Yucky const_cast, but we're definitely NOT going to write to rgn_list here.
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(cdi_info))
    cdi_write_rgns(cdi_info, const_cast<std::vector<int>&>(geom_selection.m_selections.rgn_list));

  nozzle_orientation.WriteToCDI(cdi_info);

  cdi_write_parm(cdi_info, &fixed_release_points);
  if (fixed_release_points.value == 1)
  {
    cdi_write_parm(cdi_info, &release_spacing[0]);
    cdi_write_parm(cdi_info, &release_spacing[1]);
    cdi_write_parm(cdi_info, &release_spacing[2]);
  }

  WriteEmitterGeometriesToCDI(cdi_info, &m_cylGeometries, &m_boxGeometries);

  nozzle_visibility_settings.WriteToCDI(cdi_info);
  emission_boundary_visibility_settings.WriteToCDI(cdi_info);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
    emission_point_visibility_settings.WriteToCDI(cdi_info);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(cdi_info)) {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_GEOS) {
      geom_selection.WriteToCDI(cdi_info);
    }
  }
}
void sCDI_VOLUME_EMITTER::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  SUPER_EMITTER::ReadFromCDI(cdi_info, pIndex);

  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(cdi_info))
    cdi_inner_chunk_read_rgns(*pIndex, chunkName, cdi_info, geom_selection.m_selections.rgn_list);

  nozzle_orientation.ReadFromCDI(cdi_info, chunkName, pIndex);

  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &fixed_release_points);
  if (fixed_release_points.value == 1)
  {
    release_spacing.resize(3);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &release_spacing[0]);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &release_spacing[1]);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &release_spacing[2]);
  }

  ReadEmitterGeometriesFromCDI(cdi_info, &m_cylGeometries, &m_boxGeometries, pIndex);

  nozzle_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);
  emission_boundary_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
    emission_point_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(cdi_info)) {
    CDI_WITH_INNER_CHUNK(cdi_info) {
      if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GEOS) {
        cCDI_GEOM_SELECTION_TREE* read_geos = cdi_read_geos(cdi_info);
        geom_selection = *read_geos;
        cdi_destroy_geos(read_geos);
      }
    }
  }
}

void sCDI_VOLUME_EMITTER::ConvertToPRI(PRI::cVOLUME_EMITTER& pri_emitter) const //Write data from this struct to a PRI::cVOLUME_EMITTER class
{
  set_pri_base_emitter_data(pri_emitter, *this); //Write the base class' data.
  set_pri_directed_emitter_data(pri_emitter, *this); //Set the fields specific to a PRI directed emitter.
  set_pri_geometry_emitter_data(pri_emitter, *this);    //Set the fields specific to a pri geometry emitter.
  set_pri_volume_emitter_data(pri_emitter, *this); //Set the fields specific to a PRI volume emitter.
}
 

void sCDI_POINT_EMITTER::WriteToCDI(CDI_INFO cdi_info) const
{
  SUPER_EMITTER::WriteToCDI(cdi_info);

  points.WriteToCDI(cdi_info);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 22))
    cdi_write_int_(cdi_info, preferred_csys_index);
  nozzle_orientation.WriteToCDI(cdi_info);

  nozzle_visibility_settings.WriteToCDI(cdi_info);
}
void sCDI_POINT_EMITTER::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  SUPER_EMITTER::ReadFromCDI(cdi_info, pIndex);

  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  int& localIndexVarForMacro = *pIndex;    // :-(
  ccCDI_DO_INNER_CHUNK(localIndexVarForMacro, chunkName, cdi_info)
    points.ReadFromCDI(cdi_info);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 22))
    cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &preferred_csys_index);

  nozzle_orientation.ReadFromCDI(cdi_info, chunkName, pIndex);

  nozzle_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);
}

void sCDI_POINT_EMITTER::ConvertToPRI(PRI::cPOINT_EMITTER& pri_emitter) const//Write data from this struct to a PRI::cPOINT_EMITTER class
{
  set_pri_base_emitter_data(pri_emitter, *this); //Write the base class' data
  set_pri_directed_emitter_data(pri_emitter, *this); //set the fields specific to a PRI directed emitter.
  set_pri_point_emitter_data(pri_emitter, *this); //set the fields specific to a PRI point emitter.
}

void sCDI_TIRE_EMITTER::WriteToCDI(CDI_INFO cdi_info) const
{
  SUPER_EMITTER::WriteToCDI(cdi_info);

  // Yucky const_cast, but we're definitely NOT going to write to rgn_list here.
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(cdi_info))
    cdi_write_rgns(cdi_info, const_cast<std::vector<int>&>(geom_selection.m_selections.rgn_list));

  cdi_write_parm(cdi_info, &zero_angle_direction_csys);
  cdi_write_parm(cdi_info, &zero_angle_direction[0]);
  cdi_write_parm(cdi_info, &zero_angle_direction[1]);
  cdi_write_parm(cdi_info, &zero_angle_direction[2]);
  cdi_write_parm(cdi_info, &approx_rotation_axis_dir[0]);
  cdi_write_parm(cdi_info, &approx_rotation_axis_dir[1]);
  cdi_write_parm(cdi_info, &approx_rotation_axis_dir[2]);

  WriteEmitterGeometriesToCDI(cdi_info, &m_cylGeometries, NULL);

  nozzle_visibility_settings.WriteToCDI(cdi_info);
  emission_boundary_visibility_settings.WriteToCDI(cdi_info);

  cdi_write_parm(cdi_info, &tire_tread_show);
  if ((tire_tread_show.value == 1) || CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5))
    cdi_write_strg(cdi_info, tire_tread_look);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
    emission_point_visibility_settings.WriteToCDI(cdi_info);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(cdi_info)) {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_GEOS) {
      geom_selection.WriteToCDI(cdi_info);
    }
  }
}
void sCDI_TIRE_EMITTER::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  SUPER_EMITTER::ReadFromCDI(cdi_info, pIndex);

  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(cdi_info))
    cdi_inner_chunk_read_rgns(*pIndex, chunkName, cdi_info, geom_selection.m_selections.rgn_list);

  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &zero_angle_direction_csys);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &zero_angle_direction[0]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &zero_angle_direction[1]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &zero_angle_direction[2]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &approx_rotation_axis_dir[0]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &approx_rotation_axis_dir[1]);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &approx_rotation_axis_dir[2]);

  ReadEmitterGeometriesFromCDI(cdi_info, &m_cylGeometries, NULL, pIndex);

  nozzle_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);
  emission_boundary_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);

  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &tire_tread_show);
  if ((tire_tread_show.value == 1) || CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5))
    cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &tire_tread_look);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
    emission_point_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(cdi_info)) {
    CDI_WITH_INNER_CHUNK(cdi_info) {
      if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GEOS) {
        cCDI_GEOM_SELECTION_TREE* read_geos = cdi_read_geos(cdi_info);
        geom_selection = *read_geos;
        cdi_destroy_geos(read_geos);
      }
    }
  }
}

void sCDI_TIRE_EMITTER::ConvertToPRI(PRI::cTIRE_EMITTER& pri_emitter) const //Write data from this struct to a PRI::cTIRE_EMITTER class
{
  set_pri_base_emitter_data(pri_emitter, *this); //Write the base class' data
  set_pri_tire_emitter_data(pri_emitter, *this); //set the fields specific to a PRI tire emitter.
}

void sCDI_RAIN_EMITTER::WriteToCDI(CDI_INFO cdi_info) const
{
  SUPER_EMITTER::WriteToCDI(cdi_info);

  // Yucky const_cast, but we're definitely NOT going to write to rgn_list here.
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(cdi_info))
    cdi_write_rgns(cdi_info, const_cast<std::vector<int>&>(geom_selection.m_selections.rgn_list));

  WriteEmitterGeometriesToCDI(cdi_info, &m_cylGeometries, &m_boxGeometries);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 21))
    nozzle_visibility_settings.WriteToCDI(cdi_info);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
    emission_point_visibility_settings.WriteToCDI(cdi_info);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(cdi_info)) {
    WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_GEOS) {
      geom_selection.WriteToCDI(cdi_info);
    }
  }
}
void sCDI_RAIN_EMITTER::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  SUPER_EMITTER::ReadFromCDI(cdi_info, pIndex);

  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(cdi_info))
    cdi_inner_chunk_read_rgns(*pIndex, chunkName, cdi_info, geom_selection.m_selections.rgn_list);

  ReadEmitterGeometriesFromCDI(cdi_info, &m_cylGeometries, &m_boxGeometries, pIndex);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 21))
    nozzle_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
    emission_point_visibility_settings.ReadFromCDI(cdi_info, chunkName, pIndex);

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(cdi_info)) {
    CDI_WITH_INNER_CHUNK(cdi_info) {
      if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GEOS) {
        cCDI_GEOM_SELECTION_TREE* read_geos = cdi_read_geos(cdi_info);
        geom_selection = *read_geos;
        cdi_destroy_geos(read_geos);
      }
    }
  }
}

void sCDI_RAIN_EMITTER::ConvertToPRI(PRI::cRAIN_EMITTER& pri_emitter) const //Write data from this struct to a PRI::cRAIN_EMITTER class
{
  set_pri_base_emitter_data(pri_emitter, *this); //Write the base class' data
  set_pri_rain_emitter_data(pri_emitter, *this); //set the fields specific to a PRI rain emitter.
}

void cdi_read_srmi(CDI_INFO info, CDI_SRMI srmi)
{
  srmi->cccc = CDI_CHUNK_TYPE_SRMI;
  cdiINT32 index=0;
  const char *chunkName = "srmi";
  cdi_inner_chunk_read_parm(index, chunkName, info, &(srmi->particle_material));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(srmi->splash_model));
  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 2))
    cdi_inner_chunk_read_parm(index, chunkName, info, &(srmi->enable_reflection));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(srmi->reflect_min_momentum));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(srmi->reflect_min_normal_vel));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(srmi->reflect_min_angle));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(srmi->normal_rest_coeff));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(srmi->tang_restitution_coeff));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(srmi->scatter_angle_distribution));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(srmi->scatter_angle_range));
}

void cdi_write_srmi( CDI_INFO info, CDI_SRMI srmi)
{
  cdi_write_parm(info, &(srmi->particle_material));
  cdi_write_parm(info, &(srmi->splash_model));
  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 2))
    cdi_write_parm(info, &(srmi->enable_reflection));
  cdi_write_parm(info, &(srmi->reflect_min_momentum));
  cdi_write_parm(info, &(srmi->reflect_min_normal_vel));
  cdi_write_parm(info, &(srmi->reflect_min_angle));
  cdi_write_parm(info, &(srmi->normal_rest_coeff));
  cdi_write_parm(info, &(srmi->tang_restitution_coeff));
  cdi_write_parm(info, &(srmi->scatter_angle_distribution));
  cdi_write_parm(info, &(srmi->scatter_angle_range));
}

void cdi_read_scrn(CDI_INFO info, CDI_SCRN scrn)
{
  scrn->cccc = CDI_CHUNK_TYPE_SCRN;
  cdiINT32 index=0;
  const char *chunkName = "scrn";
  ccCDI_DO_INNER_CHUNK(index, chunkName, info)
  { 
    CDI_NAME name = cdi_read_name(info);    
    scrn->name = name->name;
    cdi_destroy_name(name);
  }
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(info)) {
    ccCDI_DO_INNER_CHUNK(index, chunkName, info)
    {
      CDI_FLST flst = cdi_read_flst(info);
      scrn->geom_selection.m_selections.face_list.assign(flst->face, flst->face + flst->n_face);
      cdi_destroy_flst(flst);
    }
  }
  cdi_inner_chunk_read_parm(index, chunkName, info, &(scrn->opening_size));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(scrn->pass_thru_fraction));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(scrn->surface_material));
  if (CDI_INFO_VERSION_AT_LEAST(info, 7, 26))
    cdi_inner_chunk_read_parm(index, chunkName, info, &(scrn->measured_fraction));//PR_42469
  else
    scrn->measured_fraction.value = 0; //for old cdi files

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(info)) {
    CDI_WITH_INNER_CHUNK(info) {
      if (cdi_get_type(info) == CDI_CHUNK_TYPE_GEOS) {
        cCDI_GEOM_SELECTION_TREE* read_geos = cdi_read_geos(info);
        scrn->geom_selection = *read_geos;
        cdi_destroy_geos(read_geos);
      }
    }
  }
}

void cdi_write_scrn( CDI_INFO info, CDI_SCRN scrn)
{
  sCDI_NAME name;
  name.name = (char*) scrn->name.c_str();
  name.n_char = scrn->name.size();
  //cdi is inconsistent about whether write functions do the push/pop 
  //themselves
  cdi_write_name(info, &name);
  if (cdi_version_is_not_at_least_or_is_parallel_dev_cdi<8,5>(info)) {
    sCDI_FLST flst;
    flst.cccc = CDI_CHUNK_TYPE_FLST;
    flst.face = &(scrn->geom_selection.m_selections.face_list[0]);
    flst.n_face = scrn->geom_selection.m_selections.face_list.size();
    //this funct does the push/pop of info
    cdi_write_flst(info, &flst);
  }

  cdi_write_parm(info, &(scrn->opening_size));
  cdi_write_parm(info, &(scrn->pass_thru_fraction));
  cdi_write_parm(info, &(scrn->surface_material));
  if (CDI_INFO_VERSION_AT_LEAST(info, 7, 26))
    cdi_write_parm(info, &(scrn->measured_fraction));//PR_42469

  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,5>(info)) {
    WITH_CDI_CHUNK(info, CDI_CHUNK_TYPE_GEOS) {
      scrn->geom_selection.WriteToCDI(info);
    }
  }
}

void sCDI_SCRN::ConvertToPRI(PRI::cPARAMETERS &pri_parameters, 
                             const std::vector<std::string> &face_names,
                             const cCDI_PARTITIONS& partitions) {
    
  // For each element in face_list, create a
  // cSCREEN_GEOMETRY_REFERENCE object and add it to
  // m_screenGeometryReferenceList of the pmr parameters.
  std::vector<PRI::cSCREEN_GEOMETRY_REFERENCE> &screen_geometry_references = 
    pri_parameters.GetScreenGeometryReferenceList();
  asINT32 first_screen_geometry_reference_index = screen_geometry_references.size();

  auto scrn_face_list = geom_selection.ExpandSelection(partitions);
  ccDOTIMES(face_list_index, scrn_face_list.size()) {
    asINT32 face_index = scrn_face_list[face_list_index];
    asINT32 entity_geometry_index = find_entity_geometry_by_name(face_names[face_index], 
                                                                 pri_parameters.GetEntityGeometryList());
    PRI::cSCREEN_GEOMETRY_REFERENCE screen_geometry_reference;
    screen_geometry_reference.SetGeometryType(PRI::GEOMETRY_TYPE_ENTITY);
    screen_geometry_reference.SetGeometryIndex(entity_geometry_index);
    screen_geometry_references.push_back(screen_geometry_reference);
  }
  asINT32 last_screen_geometry_reference_index = screen_geometry_references.size();
  if(first_screen_geometry_reference_index == last_screen_geometry_reference_index) 
    msg_error("Particle screen chunk for screen \"%s\" has no associated geometry.", name.c_str());

  //Create a PRI::cSCREEN object, set the range of screen geometry
  //references computed above, and add the object to m_screenList of
  //the pmr parameters.
  PRI::cSCREEN pri_screen;
  pri_screen.SetName(name);
  pri_screen.SetOpeningSize(opening_size);
  pri_screen.SetPassThroughFraction(pass_thru_fraction.value);
  pri_screen.SetSurfaceMaterialIndex(std::size_t(surface_material.value));
  pri_screen.SetGeometryIndices(PRI::VECTOR2_INDEX(first_screen_geometry_reference_index, 
                                                   last_screen_geometry_reference_index));
  pri_parameters.GetScreenList().push_back(pri_screen);

}

//depreciated:
void sCDI_SCRN::ConvertToPRI(PRI::cPARAMETERS &pri_parameters, 
                             const std::vector<std::string> &face_names) {
  //In PR52104, this function was found to be faulty. It is replaced by the above implementation.
  //This one is kept to not break the build of CDI users since the signature has changed.

#if 0
  // For each element in face_list, create a cSCREEN_GEOMETRY_REFERENCE object and add it to 
  // m_screenGeometryReferenceList of the pmr parameters.
  std::vector<PRI::cSCREEN_GEOMETRY_REFERENCE> &screen_geometry_references = 
    pri_parameters.GetScreenGeometryReferenceList();
  asINT32 first_screen_geometry_reference_index = screen_geometry_references.size();
  ccDOTIMES(face_list_index, geom_selection.m_selections.face_list.size()) {
    asINT32 face_index = geom_selection.m_selections.face_list[face_list_index];
    asINT32 entity_geometry_index = find_entity_geometry_by_name(face_names[face_index], 
                                                                 pri_parameters.GetEntityGeometryList());
    PRI::cSCREEN_GEOMETRY_REFERENCE screen_geometry_reference;
    screen_geometry_reference.SetGeometryType(PRI::GEOMETRY_TYPE_ENTITY);
    screen_geometry_reference.SetGeometryIndex(entity_geometry_index);
    screen_geometry_references.push_back(screen_geometry_reference);
  }
  asINT32 last_screen_geometry_reference_index = screen_geometry_references.size();
  if(first_screen_geometry_reference_index == last_screen_geometry_reference_index) 
    msg_error("Particle screen chunk for screen \"%s\" has no associated geometry.", name.c_str());

  //Create a PRI::cSCREEN object, set the range of screen geometry references computed above, and add the object to
  //m_screenList of the pmr parameters.
  PRI::cSCREEN pri_screen;
  pri_screen.SetName(name);
  pri_screen.SetOpeningSize(opening_size);
  pri_screen.SetPassThroughFraction(pass_thru_fraction.value);
  pri_screen.SetSurfaceMaterialIndex(std::size_t(surface_material.value));
  pri_screen.SetGeometryIndices(PRI::VECTOR2_INDEX(first_screen_geometry_reference_index, 
                                                   last_screen_geometry_reference_index));
  pri_parameters.GetScreenList().push_back(pri_screen);
#endif
}





void cdi_read_box_(CDI_INFO cdi_info, sCDI_BOX_ &box_)
{
  box_.cccc = CDI_CHUNK_TYPE_BOX_;
  cdiINT32 index=0;
  cdi_inner_chunk_read_rgns(index, "rgns", cdi_info, box_.region.regions);
  ccCDI_DO_INNER_CHUNK(index, "cvdp", cdi_info)
  { box_.csys = *cdi_read_cvdp(cdi_info);   }
  ccCDI_DO_INNER_CHUNK(index, "bbox", cdi_info)
  { box_.bbox = *cdi_read_bbox(cdi_info);   }
}

void cdi_write_box_(CDI_INFO cdi_info, CDI_BOX_ box_)
{
  box_->cccc = CDI_CHUNK_TYPE_BOX_;
  ASSERT_VALID_CDI_INFO(cdi_info);
  cdi_push(cdi_info, CDI_CHUNK_TYPE_BOX_);
  cdi_write_rgns(cdi_info, box_->region.regions);
  cdi_write_cvdp(cdi_info, &(box_->csys));
  cdi_write_bbox(cdi_info, &(box_->bbox));
  cdi_pop(cdi_info);
}

//========================================================================================
// Surface Material Interaction
//========================================================================================


void sCDI_SRMI::ConvertToPRI(asINT32 index, std::string name, PRI::cSURFACE_MATERIAL_INTERACTIONS &pri_surface_interaction, 
                             PRI::cPARTICLE_SURFACE_INTERACTION &pri_particle_surface_interaction) const {
  
  pri_surface_interaction.SetParticleMaterialIndex((size_t)particle_material.value);
  pri_surface_interaction.SetParticleSurfaceInteractionIndex(index);
  
  pri_particle_surface_interaction.SetName(name);
  pri_particle_surface_interaction.SetSplashModelEnabled(splash_model.value != 0.0);
  pri_particle_surface_interaction.SetReflectionEnabled(enable_reflection.value != 0.0);
  pri_particle_surface_interaction.SetMinimumReflectionMomentum(reflect_min_momentum);
  pri_particle_surface_interaction.SetMinimumNormalVelocity(reflect_min_normal_vel);
  pri_particle_surface_interaction.SetMinimumReflectionAngle(reflect_min_angle);
  pri_particle_surface_interaction.SetScatterAngleRangeStdDev(scatter_angle_range);
  pri_particle_surface_interaction.SetNormalRestitutionCoefficient(normal_rest_coeff.value);
  pri_particle_surface_interaction.SetScatterAngleDistribution(cdi_to_pri_distribution_type((CDI_DISTRIBUTION_TYPE)(int)scatter_angle_distribution.value));
  pri_particle_surface_interaction.SetTangentialRestitutionCoefficient(tang_restitution_coeff.value);
} 

// =======================================================================================
// Autostop
// =======================================================================================

// Vehicle ...............................................................................
void cdi_read_vhcl(CDI_INFO info, CDI_VHCL vhcl)
{
  cdiINT32 index=0;
  cdi_inner_chunk_read_int_(index, "vhcl", info, &(vhcl->csys_index));
  cdi_inner_chunk_read_enum(index, "vhcl", info, &(vhcl->moments_via));

  cdi_inner_chunk_read_enum(index, "vhcl", info, &(vhcl->centerline_direction));
  cdi_inner_chunk_read_enum(index, "vhcl", info, &(vhcl->up_direction));
  cdi_inner_chunk_read_enum(index, "vhcl", info, &(vhcl->side_force_direction));

  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 14))
    cdi_inner_chunk_read_bool(index, "vhcl", info, &(vhcl->is_front_axle_applicable));
  else 
    vhcl->is_front_axle_applicable = true;

  cdi_inner_chunk_read_int_(index, "vhcl", info, &(vhcl->front_axle_preferred_csys_index));
  cdi_inner_chunk_read_dbls(index, "vhcl", info, (idFLOAT*)vhcl->front_axle_origin);
  cdi_inner_chunk_read_dbls(index, "vhcl", info, (idFLOAT*)vhcl->front_axle_dir);
  
  if (CDI_INFO_VERSION_AT_LEAST(info, 4, 14))
    cdi_inner_chunk_read_bool(index, "vhcl", info, &(vhcl->is_rear_axle_applicable));
  else 
    vhcl->is_rear_axle_applicable = true;

  cdi_inner_chunk_read_int_(index, "vhcl", info, &(vhcl->rear_axle_preferred_csys_index));
  cdi_inner_chunk_read_dbls(index, "vhcl", info, (idFLOAT*)vhcl->rear_axle_origin);
  cdi_inner_chunk_read_dbls(index, "vhcl", info, (idFLOAT*)vhcl->rear_axle_dir);

  cdi_inner_chunk_read_dbls(index, "vhcl", info, (idFLOAT*)vhcl->moment_center);
  cdi_inner_chunk_read_int_(index, "vhcl", info, &(vhcl->floor_part_index));
  cdi_inner_chunk_read_dbls(index, "vhcl", info, (idFLOAT*)vhcl->floor_point);
  
  if (!CDI_INFO_VERSION_AT_LEAST(info, 4, 14)) {
    // VHCL chunk temporarily had a boolean 'user_defined_char_values' and parm 'char_area'. These
    // parameters won't be exactly preserved through a dump/undump cycle since they don't correspond
    // to anything in our object model now, but that's not a big deal because they're not meaningful
    // any more anyways.
    bool dummy_bool;
    cdi_inner_chunk_read_bool(index, "vhcl", info, &dummy_bool);
    sCDI_PARM dummy_parm;
    cdi_inner_chunk_read_parm(index, "vhcl", info, &dummy_parm);
  }
  cdi_inner_chunk_read_parm(index, "vhcl", info, &(vhcl->wheelbase));
}

void cdi_write_vhcl(CDI_INFO cdi_info, CDI_VHCL vhcl)
{
  cdi_write_int_(cdi_info, vhcl->csys_index);
  cdi_write_enum(cdi_info, vhcl->moments_via);

  cdi_write_enum(cdi_info, vhcl->centerline_direction);
  cdi_write_enum(cdi_info, vhcl->up_direction);
  cdi_write_enum(cdi_info, vhcl->side_force_direction);
  
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 14))
    cdi_write_bool(cdi_info, vhcl->is_front_axle_applicable);
  cdi_write_int_(cdi_info, vhcl->front_axle_preferred_csys_index);
  cdi_write_dbls(cdi_info, (idFLOAT*)vhcl->front_axle_origin, 3);
  cdi_write_dbls(cdi_info, (idFLOAT*)vhcl->front_axle_dir, 3);
  
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 14))
    cdi_write_bool(cdi_info, vhcl->is_rear_axle_applicable);
  cdi_write_int_(cdi_info, vhcl->rear_axle_preferred_csys_index);
  cdi_write_dbls(cdi_info, (idFLOAT*)vhcl->rear_axle_origin, 3);
  cdi_write_dbls(cdi_info, (idFLOAT*)vhcl->rear_axle_dir, 3);

  cdi_write_dbls(cdi_info, (idFLOAT*)vhcl->moment_center, 3);
  cdi_write_int_(cdi_info, vhcl->floor_part_index);
  cdi_write_dbls(cdi_info, (idFLOAT*)vhcl->floor_point, 3);
  
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 14)) {
    // VHCL chunk temporarily had a boolean 'user_defined_char_values' and parm 'char_area'
    cdi_write_bool(cdi_info, false);
    sCDI_PARM dummy_parm;
    cdi_write_parm(cdi_info, &dummy_parm);
  }
  cdi_write_parm(cdi_info, &(vhcl->wheelbase));
}

void cdi_destroy_vhcl(CDI_VHCL vhcl)
{
  exa_free(vhcl);
}

// Averaged Meas Window........................................................................
void cdi_read_amw_(CDI_INFO cdi_info, CDI_AMW_ amw_)
{
  cdiINT32 index=0;
  cdi_inner_chunk_read_strg(index, "amw_", cdi_info, &(amw_->name));
  cdi_inner_chunk_read_int_(index, "amw_", cdi_info, &(amw_->meas_window_to_average));
  cdi_inner_chunk_read_bool(index, "amw_", cdi_info, &(amw_->average_fnc));
  cdi_inner_chunk_read_bool(index, "amw_", cdi_info, &(amw_->average_pnc));
  cdi_inner_chunk_read_bool(index, "amw_", cdi_info, &(amw_->average_snc));
  cdi_inner_chunk_read_enum(index, "amw_", cdi_info, &(amw_->start_via));
  cdi_inner_chunk_read_parm(index, "amw_", cdi_info, &(amw_->start));

  cdiINT32 numMonitors = 0;
  cdiINT32 monitorIndex = -1;
  cdi_inner_chunk_read_int_(index, "amw_", cdi_info, &numMonitors);
  ccDOTIMES(i, numMonitors) {
    cdi_inner_chunk_read_int_(index, "amw_", cdi_info, &monitorIndex);
    amw_->monitors.push_back(monitorIndex);
  }

  cdi_inner_chunk_read_enum(index, "amw_", cdi_info, &(amw_->end_via));
  cdi_inner_chunk_read_parm(index, "amw_", cdi_info, &(amw_->end));
  cdi_inner_chunk_read_enum(index, "amw_", cdi_info, &(amw_->avg_interval_via));
  cdi_inner_chunk_read_parm(index, "amw_", cdi_info, &(amw_->avg_interval));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 30))
    cdi_inner_chunk_read_bool(index, "amw_", cdi_info, &(amw_->m_phaseAveraged));
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8, 10>(cdi_info)) {
    bool hasCpScreening(false);
    cdi_inner_chunk_read_bool(index, "amw_", cdi_info, &hasCpScreening);
    if (hasCpScreening)
      cdi_inner_chunk_read_mflt(index, "amw_", cdi_info, &(amw_->force_filter));
  }
  else if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8, 9>(cdi_info))
    amw_->force_filter = cdi_read_mflt(cdi_info);
}

void cdi_write_amw_(CDI_INFO cdi_info, CDI_AMW_ amw_)
{
  cdi_write_strg(cdi_info, amw_->name);
  cdi_write_int_(cdi_info, amw_->meas_window_to_average);
  cdi_write_bool(cdi_info, amw_->average_fnc);
  cdi_write_bool(cdi_info, amw_->average_pnc);
  cdi_write_bool(cdi_info, amw_->average_snc);
  cdi_write_enum(cdi_info, amw_->start_via);
  cdi_write_parm(cdi_info, &(amw_->start));

  cdi_write_int_(cdi_info, amw_->monitors.size());
  ccDOTIMES(i, amw_->monitors.size())
    cdi_write_int_(cdi_info, amw_->monitors[i]);

  cdi_write_enum(cdi_info, amw_->end_via);
  cdi_write_parm(cdi_info, &(amw_->end));
  cdi_write_enum(cdi_info, amw_->avg_interval_via);
  cdi_write_parm(cdi_info, &(amw_->avg_interval));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 30))
    cdi_write_bool(cdi_info, amw_->m_phaseAveraged);
  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8, 10>(cdi_info)) {
    bool hasCpScreening = amw_->force_filter;
    cdi_write_bool(cdi_info, hasCpScreening);
    if (hasCpScreening)
      cdi_write_mflt(cdi_info, amw_->force_filter);
  }
  else if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8, 9>(cdi_info))
    cdi_write_mflt(cdi_info, amw_->force_filter);
}


// Monitors ..............................................................................
void
sCDI_MNTR::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);

  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &monitor_type);

  cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &name);
  cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &preferred_time_unit);

  // Flow monitor-specific parameters
  cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &meas_window_index);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &variable_source);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &flow_variable);
  cdi_inner_chunk_read_bool(*pIndex, chunkName, cdi_info, &variable_is_vector_component);
  cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &reference_csys);
  cdi_inner_chunk_read_bool(*pIndex, chunkName, cdi_info, &variable_is_moment_component);
  cdi_inner_chunk_read_dbls(*pIndex, chunkName, cdi_info, reference_point);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 15))
    cdi_inner_chunk_read_bool(*pIndex, chunkName, cdi_info, &use_wheelbase);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 11)) {
    cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &region_filtering);
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,7>(cdi_info)) {
      CDI_WITH_INNER_CHUNK(cdi_info) {
        if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GEOS) {
          cCDI_GEOM_SELECTION_TREE* read_geos = cdi_read_geos(cdi_info);
          region_filtering_tree = *read_geos;
          cdi_destroy_geos(read_geos);
        }
      }
    }
    else {
      cdiINT32 region_filtering_partition_index = -1;
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 0))
        cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &region_filtering_partition_index);
      // Put the regions in the tree. Add 1 to the partition index because now there is a base assembly
      region_filtering_tree.PartitionIndex(region_filtering_partition_index + 1);
      cdi_inner_chunk_read_rgns(*pIndex, chunkName, cdi_info, region_filtering_tree.m_selections.rgn_list);
    }
    cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &face_filtering);
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,7>(cdi_info)) {
      CDI_WITH_INNER_CHUNK(cdi_info) {
        if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_GEOS) {
          cCDI_GEOM_SELECTION_TREE* read_geos = cdi_read_geos(cdi_info);
          face_filtering_tree = *read_geos;
          cdi_destroy_geos(read_geos);
        }
      }
    }
    else {
      cdiINT32 face_filtering_partition_index = -1;
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 0))
        cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &face_filtering_partition_index);
      // Put the faces in the tree. Add 1 to the partition index because now there is a base assembly
      face_filtering_tree.PartitionIndex(face_filtering_partition_index + 1);
      cdi_inner_chunk_read_flst(*pIndex, chunkName, cdi_info, face_filtering_tree.m_selections.face_list);
    }
  }

  // HX monitor-specific parameters
  cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &heat_exchanger_index);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &heat_exchanger_variable);

  // PowerTHERM monitor-specific parameters
  cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &powertherm_model_index);
  cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &coupled_powertherm_part);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &powertherm_part_side);
  cdi_inner_chunk_read_bool(*pIndex, chunkName, cdi_info, &coupled);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &powertherm_variable);

  // Signal Analysis parameters
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &signal_analysis);
  cdi_inner_chunk_read_bool(*pIndex, chunkName, cdi_info, &automatically_stop);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &analysis_scheme);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 18))
    cdi_inner_chunk_read_strg(*pIndex, chunkName, cdi_info, &classic_autostop_analysis_scheme);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &signal_conv_criteria);

  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &monitor_flow_pass_via);
  cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &coupling_phase);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &monitor_flow_pass);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &initial_transient_determined_via);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &minimum_initial_transient_via);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &minimum_initial_transient);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &end_of_initial_transient_via);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &end_of_initial_transient);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &initial_transient_variance_window_via);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &initial_transient_variance_window);

  // Accuracy/Confidence params
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &desired_accuracy_via);
  if (desired_accuracy_via == eCDI_MNTR_ACCY_VIA::PercentageOfMean) {
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,13>(cdi_info))
      cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &desired_accuracy_percentage);
    else
      cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &desired_accuracy);
  }
  else if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,13>(cdi_info) && (desired_accuracy_via == eCDI_MNTR_ACCY_VIA::LesserOfValueAndPercentageOfMean
      || desired_accuracy_via == eCDI_MNTR_ACCY_VIA::GreaterOfValueAndPercentageOfMean)) {
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &desired_accuracy);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &desired_accuracy_percentage);
  } else
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &desired_accuracy);

  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &confidence_level_via);
  cdi_inner_chunk_read_dbls(*pIndex, chunkName, cdi_info, (idFLOAT*)&custom_confidence_level);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &minimum_averaging_time_via);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &minimum_averaging_time);

  // Stabilization Window params
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &stabilization_window_via);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &stabilization_window);
  cdi_inner_chunk_read_bool(*pIndex, chunkName, cdi_info, &enable_subwindows);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &subwindow_via);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &subwindow);
  cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &subwindow_range_limit_via);
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &subwindow_range_limit);

  // Classic Autostop Support
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 17))
    cdi_inner_chunk_read_bool(*pIndex, chunkName, cdi_info, &classic_autostop_algorithm);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 13)) {
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &variance_gradient_limit);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &creep_limit);
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 18))
    cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &running_average_gradient_limit_via);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 13)) {
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &running_average_gradient_limit);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 20))
      cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &running_average_gradient_interval_via);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &running_average_gradient_interval);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 20))
      cdi_inner_chunk_read_enum(*pIndex, chunkName, cdi_info, &signal_processing_period_via);
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &signal_processing_period);
  }
}

void
sCDI_MNTR::WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_write_enum(cdi_info, monitor_type);

  cdi_write_strg(cdi_info, name);
  cdi_write_strg(cdi_info, preferred_time_unit);

  // Flow monitor-specific parameters
  cdi_write_int_(cdi_info, meas_window_index);
  cdi_write_enum(cdi_info, variable_source);
  cdi_write_enum(cdi_info, flow_variable);
  cdi_write_bool(cdi_info, variable_is_vector_component);
  cdi_write_int_(cdi_info, reference_csys);
  cdi_write_bool(cdi_info, variable_is_moment_component);
  cdi_write_dbls(cdi_info, (idFLOAT*)reference_point, 3);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 15))
    cdi_write_bool(cdi_info, use_wheelbase);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 11)) {
    // Yucky const_cast, but we're definitely NOT going to write to these here.
    cdi_write_enum(cdi_info, region_filtering);
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,7>(cdi_info)) {
      WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_GEOS) {
        region_filtering_tree.WriteToCDI(cdi_info);
      }
    }
    else {
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 0))
        cdi_write_int_(cdi_info, region_filtering_tree.PartitionIndex() - 1);
      cdi_write_rgns(cdi_info, const_cast<std::vector<int>&>(region_filtering_tree.m_selections.rgn_list));
    }
    cdi_write_enum(cdi_info, face_filtering);
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,7>(cdi_info)) {
      WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_GEOS) {
        face_filtering_tree.WriteToCDI(cdi_info);
      }
    }
    else {
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 0))
        cdi_write_int_(cdi_info, face_filtering_tree.PartitionIndex() - 1);
      cdi_write_flst(cdi_info, face_filtering_tree.m_selections.face_list);
    }
  }

  // HX monitor-specific parameters
  cdi_write_int_(cdi_info, heat_exchanger_index);
  cdi_write_enum(cdi_info, heat_exchanger_variable);

  // PowerTHERM monitor-specific parameters
  cdi_write_int_(cdi_info, powertherm_model_index);
  cdi_write_strg(cdi_info, coupled_powertherm_part);
  cdi_write_enum(cdi_info, powertherm_part_side);
  cdi_write_bool(cdi_info, coupled);
  cdi_write_enum(cdi_info, powertherm_variable);

  // Signal Analysis parameters
  cdi_write_enum(cdi_info, signal_analysis);
  cdi_write_bool(cdi_info, automatically_stop);
  cdi_write_enum(cdi_info, analysis_scheme);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 18))
    cdi_write_strg(cdi_info, classic_autostop_analysis_scheme);
  cdi_write_enum(cdi_info, signal_conv_criteria);

  cdi_write_enum(cdi_info, monitor_flow_pass_via);
  cdi_write_int_(cdi_info, coupling_phase);
  cdi_write_parm(cdi_info, &monitor_flow_pass);
  cdi_write_enum(cdi_info, initial_transient_determined_via);
  cdi_write_enum(cdi_info, minimum_initial_transient_via);
  cdi_write_parm(cdi_info, &minimum_initial_transient);
  cdi_write_enum(cdi_info, end_of_initial_transient_via);
  cdi_write_parm(cdi_info, &end_of_initial_transient);
  cdi_write_enum(cdi_info, initial_transient_variance_window_via);
  cdi_write_parm(cdi_info, &initial_transient_variance_window);

  // Accuracy/Confidence params
  cdi_write_enum(cdi_info, desired_accuracy_via);
  if (desired_accuracy_via == eCDI_MNTR_ACCY_VIA::PercentageOfMean) {
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,13>(cdi_info))
      cdi_write_parm(cdi_info, &desired_accuracy_percentage);
    else
      cdi_write_parm(cdi_info, &desired_accuracy);
  }
  else if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,13>(cdi_info) && (desired_accuracy_via == eCDI_MNTR_ACCY_VIA::LesserOfValueAndPercentageOfMean
      || desired_accuracy_via == eCDI_MNTR_ACCY_VIA::GreaterOfValueAndPercentageOfMean)) {
    cdi_write_parm(cdi_info, &desired_accuracy);
    cdi_write_parm(cdi_info, &desired_accuracy_percentage);
  } else
    cdi_write_parm(cdi_info, &desired_accuracy);

  cdi_write_enum(cdi_info, confidence_level_via);
  cdi_write_dbls(cdi_info, (idFLOAT*)&custom_confidence_level, 1);
  cdi_write_enum(cdi_info, minimum_averaging_time_via);
  cdi_write_parm(cdi_info, &minimum_averaging_time);

  // Stabilization Window params
  cdi_write_enum(cdi_info, stabilization_window_via);
  cdi_write_parm(cdi_info, &stabilization_window);
  cdi_write_bool(cdi_info, enable_subwindows);
  cdi_write_enum(cdi_info, subwindow_via);
  cdi_write_parm(cdi_info, &subwindow);
  cdi_write_enum(cdi_info, subwindow_range_limit_via);
  cdi_write_parm(cdi_info, &subwindow_range_limit);

  // Classic Autostop Support
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 17))
    cdi_write_bool(cdi_info, classic_autostop_algorithm);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 13)) {
    cdi_write_parm(cdi_info, &variance_gradient_limit);
    cdi_write_parm(cdi_info, &creep_limit);
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 18))
    cdi_write_enum(cdi_info, running_average_gradient_limit_via);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 13)) {
    cdi_write_parm(cdi_info, &running_average_gradient_limit);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 20))
      cdi_write_enum(cdi_info, running_average_gradient_interval_via);
    cdi_write_parm(cdi_info, &running_average_gradient_interval);
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 20))
      cdi_write_enum(cdi_info, signal_processing_period_via);
    cdi_write_parm(cdi_info, &signal_processing_period);
  }
}

void cCDI_MONITORS::WriteToCDI(CDI_INFO cdi_info) const
{
  for (size_t im = 0; im < m_monitors.size(); im++)
  {
    WITH_CDI_CHUNK(cdi_info, m_monitors[im].GetChunkType()) {
      m_monitors[im].WriteToCDI(cdi_info);
    }
  }
}

void cCDI_MONITORS::ReadFromCDI(CDI_INFO cdi_info)
{
  CIO_INFO cio = cdi_info->cio_info;

  asINT32 count = cio_get_count(cio);

  for (int i = 0; i < count; i++)
  {
    cio_descend(cio);

    cdiINT32 index = 0;
    sCDI_MNTR monitor;
    monitor.ReadFromCDI(cdi_info, &index);
    m_monitors.push_back(monitor);

    cio_ascend(cio);
  }
}


void cdi_write_aftd(CDI_INFO cdi_info, CDI_AFTD aftd)
{
  cdi_push(cdi_info, CDI_CHUNK_TYPE_AFTD);
  cdi_write_idFLOAT(cdi_info, &aftd->acceleration_factor, 1);
  cdi_pop(cdi_info);
}

CDI_AFTD cdi_read_aftd(CDI_INFO cdi_info)
{
  CDI_AFTD aftd = EXA_CALLOC_STRUCT(CDI_AFTD);
  aftd->cccc = CDI_CHUNK_TYPE_AFTD;
  cdi_read_idFLOAT(cdi_info, &aftd->acceleration_factor, 1);

  return aftd;
}
/****************************************************************\
|
| Function name:cdi_write_clbr
|
| Purpose:writes clbr (calibration parameters)
|
\****************************************************************/

// This is a copy of the dierckx_surface_coeff_array_size() function, defined
// in the dierckx component. We copy it here to prevent forcing all downstream
// users of CDI to link in the dierckx libraries.
int cdi_tire_surface_coeff_array_size(int ndim, int n_u_knots, int n_v_knots)
{
  return (n_u_knots - 4)*(n_v_knots - 4)*ndim;
}

void cdi_write_tire(CDI_INFO cdi_info, CDI_TIRE tire)
{
  auINT32 nu = tire->n_knots[0];
  auINT32 nv = tire->n_knots[1];
  auINT32 ncoeff = cdi_tire_surface_coeff_array_size(3,nu,nv);
  cdi_push(cdi_info, CDI_CHUNK_TYPE_TIRE);
  cdi_write_asINT32(cdi_info, &tire->csys_index, 1);
  cdi_write_asINT32(cdi_info, &tire->grooved_tire,1);
  cdi_write_auINT32(cdi_info, tire->n_knots, 2);
  cdi_write_idFLOAT(cdi_info, tire->u_knots.data(), nu);
  cdi_write_idFLOAT(cdi_info, tire->v_knots.data(), nv);
  cdi_write_idFLOAT(cdi_info, tire->coeff_carcass.data(), ncoeff);
  if (!tire->grooved_tire) {
    cdi_write_idFLOAT(cdi_info, tire->coeff_wrap.data(), ncoeff);
  }
  cdi_pop(cdi_info);
}

CDI_TIRE cdi_read_tire(CDI_INFO cdi_info)
{
  CDI_TIRE tire = new sCDI_TIRE;
  tire->cccc = CDI_CHUNK_TYPE_TIRE;
  cdi_read_asINT32(cdi_info, &tire->csys_index,1);
  cdi_read_asINT32(cdi_info, &tire->grooved_tire,1);
  cdi_read_auINT32(cdi_info, tire->n_knots, 2);
  auINT32 nu = tire->n_knots[0];
  auINT32 nv = tire->n_knots[1];
  auINT32 ncoeff = cdi_tire_surface_coeff_array_size(3,nu,nv);
  tire->u_knots.resize(nu);
  tire->v_knots.resize(nv);
  cdi_read_idFLOAT(cdi_info, tire->u_knots.data(), nu);
  cdi_read_idFLOAT(cdi_info, tire->v_knots.data(), nv);

  tire->coeff_carcass.resize(ncoeff);
  cdi_read_idFLOAT(cdi_info, tire->coeff_carcass.data(), ncoeff);

  if (!tire->grooved_tire) {
    tire->coeff_wrap.resize(ncoeff);
    cdi_read_idFLOAT(cdi_info, tire->coeff_wrap.data(), ncoeff);
  }

  return tire;
}

void cdi_destroy_tire(CDI_TIRE tire) {
  delete tire;
}

VOID
cdi_write_clbr(
               CDI_INFO cdi_info,
               CDI_CLBR clbr
               )
{
  clbr->cccc = CDI_CHUNK_TYPE_CLBR;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_CLBR);
  cdi_write_one_asINT32(cdi_info, clbr->face_index);
  cdi_write_one_asINT32(cdi_info, clbr->meas_window_index);
  cdi_write_one_asINT32(cdi_info, clbr->calibration_iterations);
  cdi_write_one_asINT32(cdi_info, clbr->cancel_pressure_fluctuations);
  cdi_write_one_asINT32(cdi_info, clbr->subtract_mean_velocity);
  cdi_write_one_asINT32(cdi_info, clbr->reset_initial_condition);

  cdi_pop(cdi_info);
}


/****************************************************************\
|
| Function name:cdi_read_clbr
|
| Purpose:reads clbr (calibration parameters)
|
\****************************************************************/
CDI_CLBR
cdi_read_clbr(
              CDI_INFO cdi_info
              )
{
  CDI_CLBR clbr = new sCDI_CLBR;
  clbr->cccc = CDI_CHUNK_TYPE_CLBR;
  clbr->face_index = cdi_read_one_asINT32(cdi_info);
  clbr->meas_window_index = cdi_read_one_asINT32(cdi_info);
  clbr->calibration_iterations = cdi_read_one_asINT32(cdi_info);
  clbr->cancel_pressure_fluctuations = (cdi_read_one_asINT32(cdi_info) != 0);
  clbr->subtract_mean_velocity = (cdi_read_one_asINT32(cdi_info) != 0);
  clbr->reset_initial_condition = (cdi_read_one_asINT32(cdi_info) != 0 );

  return(clbr);
}

/****************************************************************\
|
| Function name:cdi_destroy_clbr
|
| Purpose:destroys a clbr
|
\****************************************************************/
VOID
cdi_destroy_clbr(
                 CDI_CLBR clbr
                 )
{
  delete clbr;
}

/****************************************************************\
|
| User-Defined Scalar Transport functions
|
\****************************************************************/

void
sCDI_SCLR::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* index)
{
  if (!cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info))
    return;

  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);
  cdi_inner_chunk_read_strg(*index, chunkName, cdi_info, &uds_name);
  cdi_inner_chunk_read_strg(*index, chunkName, cdi_info, &unit_type);

  cdi_inner_chunk_read_strg(*index, chunkName, cdi_info, &unit_class_expression);
  
  cdi_inner_chunk_read_parm(*index, chunkName, cdi_info, &diffusion_coefficient);
  cdi_inner_chunk_read_parm(*index, chunkName, cdi_info, &scalar_turb_schmidt_number);
  cdi_inner_chunk_read_parm(*index, chunkName, cdi_info, &scalar_source_term);

  cdi_inner_chunk_read_bool(*index, chunkName, cdi_info, &allow_negative_values);
  cdi_inner_chunk_read_parm(*index, chunkName, cdi_info, &minimum_value);
  cdi_inner_chunk_read_parm(*index, chunkName, cdi_info, &maximum_value);
  cdi_inner_chunk_read_parm(*index, chunkName, cdi_info, &default_initial_condition);
  cdi_inner_chunk_read_parm(*index, chunkName, cdi_info, &molecular_weight);
}

void
sCDI_SCLR::WriteToCDI(CDI_INFO cdi_info) const
{

  if (!cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info))
    return;
  
  cdi_write_strg(cdi_info, uds_name);
  cdi_write_strg(cdi_info, unit_type);
  cdi_write_strg(cdi_info, unit_class_expression);
  cdi_write_parm(cdi_info, &diffusion_coefficient);
  cdi_write_parm(cdi_info, &scalar_turb_schmidt_number);
  cdi_write_parm(cdi_info, &scalar_source_term);
  cdi_write_bool(cdi_info, allow_negative_values);
  cdi_write_parm(cdi_info, &minimum_value);
  cdi_write_parm(cdi_info, &maximum_value);
  cdi_write_parm(cdi_info, &default_initial_condition);
  cdi_write_parm(cdi_info, &molecular_weight);
}

void cCDI_SCALARS::WriteToCDI(CDI_INFO cdi_info) const
{
  if (!cdi_version_is_at_least_and_not_parallel_dev_cdi<8,14>(cdi_info))
    return;

  for (size_t i = 0; i < m_scalars.size(); i++)
  {
    WITH_CDI_CHUNK(cdi_info, m_scalars[i].GetChunkType()) {
      m_scalars[i].WriteToCDI(cdi_info);
    }
  }
}

void cCDI_SCALARS::ReadFromCDI(CDI_INFO cdi_info)
{
  CIO_INFO cio = cdi_info->cio_info;

  asINT32 count = cio_get_count(cio);

  for (int i = 0; i < count; i++)
  {
    cio_descend(cio);

    cdiINT32 index = 0;
    sCDI_SCLR scalar;
    scalar.ReadFromCDI(cdi_info, &index);
    m_scalars.push_back(scalar);

    cio_ascend(cio);
  }
}

// =======================================================================================
// Solid Materials
// =======================================================================================

VOID
cdi_read_scmt(CDI_INFO info, CDI_SCMT scmt)
{
  scmt->cccc = CDI_CHUNK_TYPE_SCMT;
  
  cdiINT32 index = 0;
  const char *chunkName = "scmt";
  ccCDI_DO_INNER_CHUNK(index, chunkName, info)
  {
    CDI_NAME name = cdi_read_name(info);
    scmt->name = name->name;
    cdi_destroy_name(name);
  }
  cdi_inner_chunk_read_parm(index, chunkName, info, &(scmt->density));
  cdi_inner_chunk_read_parm(index, chunkName, info, &(scmt->specific_heat));
  cdi_inner_chunk_read_parm(index, chunkName, info, scmt->therm_conductivity);
  cdi_inner_chunk_read_parm(index, chunkName, info, scmt->therm_conductivity + 1);
  cdi_inner_chunk_read_parm(index, chunkName, info, scmt->therm_conductivity + 2);
  if (CDI_INFO_VERSION_AT_LEAST(info, 9, 8)) {
    cdi_inner_chunk_read_enum(index, chunkName, info, &(scmt->material_type));
    cdi_inner_chunk_read_enum(index, chunkName, info, &(scmt->anisotropy_type));
  }
  else {
    scmt->material_type = eCDI_SOLID_MATERIAL_TYPE::Enum::Isotropic;
    scmt->anisotropy_type = eCDI_SOLID_ANISOTROPY_TYPE::Enum::None;
  }
}

VOID
cdi_write_scmt(CDI_INFO info, CDI_SCMT scmt)
{
  scmt->cccc = CDI_CHUNK_TYPE_SCMT;

  cdi_push(info, CDI_CHUNK_TYPE_SCMT);

  sCDI_NAME name;
  name.name = (char*)scmt->name.c_str();
  name.n_char = scmt->name.size();
  cdi_write_name(info, &name);

  cdi_write_parm(info, &(scmt->density));
  cdi_write_parm(info, &(scmt->specific_heat));
  cdi_write_parm(info, scmt->therm_conductivity);
  cdi_write_parm(info, scmt->therm_conductivity + 1);
  cdi_write_parm(info, scmt->therm_conductivity + 2);
  if (CDI_INFO_VERSION_AT_LEAST(info, 9, 8)) {
    cdi_write_enum(info, scmt->material_type);
    cdi_write_enum(info, scmt->anisotropy_type);
  }

  cdi_pop(info);
}

// Part axes used to define material anisotropy

VOID
cdi_read_paxs(CDI_INFO info, CDI_PAXS paxs)
{
  paxs->cccc = CDI_CHUNK_TYPE_PAXS;

  cdiINT32 index = 0;
  const char *chunkName = "paxs";
  cdi_inner_chunk_read_int_(index, chunkName, info, &(paxs->part_index));
  cdi_inner_chunk_read_dbls(index, chunkName, info, (idFLOAT*)paxs->axis_dir);
}

VOID
cdi_write_paxs(CDI_INFO info, CDI_PAXS paxs)
{
  paxs->cccc = CDI_CHUNK_TYPE_PAXS;

  cdi_push(info, CDI_CHUNK_TYPE_PAXS);

  cdi_write_int_(info, paxs->part_index);
  cdi_write_dbls(info, (idFLOAT*)paxs->axis_dir, 3);

  cdi_pop(info);
}

// =======================================================================================
// Solid Material Assignments
// =======================================================================================

VOID
cdi_read_scma(CDI_INFO info, CDI_SCMA scma)
{
  scma->cccc = CDI_CHUNK_TYPE_SCMA;

  cdiINT32 index = 0;
  const char *chunkName = "scma";
  
  cdi_inner_chunk_read_rgns(index, chunkName, info, scma->regions);
  CDI_WITH_INNER_CHUNK(info) {
    if (cdi_get_type(info) == scma->geom_ref.GetChunkType()) {
      scma->geom_ref.ReadFromCDI(info);
    }
  }
  cdi_inner_chunk_read_int_(index, chunkName, info, &(scma->material));
}

VOID
cdi_write_scma(CDI_INFO info, CDI_SCMA scma)
{
  scma->cccc = CDI_CHUNK_TYPE_SCMA;

  cdi_push(info, CDI_CHUNK_TYPE_SCMA);

  cdi_write_rgns(info, scma->regions);
  WITH_CDI_CHUNK(info, scma->geom_ref.GetChunkType()) {
    scma->geom_ref.WriteToCDI(info);
  }
  cdi_write_int_(info, scma->material);

  cdi_pop(info);
}

VOID
cdi_destroy_scma(CDI_SCMA scma)
{
  delete scma;
}

cCDI_SOLID_MATERIAL_ASSIGNMENTS::cCDI_SOLID_MATERIAL_ASSIGNMENTS(std::vector<CDI_SCMA> materialAssigns)
{
  for (CDI_SCMA assignment : materialAssigns) {
    AppendMaterialAssignment(assignment);
  }
}

cCDI_SOLID_MATERIAL_ASSIGNMENTS::~cCDI_SOLID_MATERIAL_ASSIGNMENTS()
{
  for (CDI_SCMA assignment : mMaterialAssignments) {
    delete assignment;
  }
  mMaterialAssignments.clear();
}

void cCDI_SOLID_MATERIAL_ASSIGNMENTS::ReadFromCDI(CDI_INFO cdi_info)
{
  // This should be called when the "SCAS" chunk is hit
  asINT32 count = cio_get_count(cdi_info->cio_info);

  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);
    {
      CIO_CCCC type = cio_get_type(cdi_info->cio_info);
      if (type == CDI_CHUNK_TYPE_SCMA) {
        CDI_SCMA scma = new sCDI_SCMA();
        cdi_read_scma(cdi_info, scma);
        mMaterialAssignments.push_back(scma);
        UpdateMapForMaterialAssignment(scma);
      }
    }
    cio_ascend(cdi_info->cio_info);
  }
}

cdiINT32 cCDI_SOLID_MATERIAL_ASSIGNMENTS::GetMaterialIndexForPart(cdiINT32 partIndex) const
{
  cdiINT32 materialIndex = -1;
  auto it = mPartMaterialMap.find(partIndex);
  if (it != mPartMaterialMap.end()) {
    materialIndex = it->second;
  }
  return materialIndex;
}

void cCDI_SOLID_MATERIAL_ASSIGNMENTS::SetMaterialIndexForPart(cdiINT32 partIndex, cdiINT32 materialIndex)
{
  mPartMaterialMap[partIndex] = materialIndex;
}

void cCDI_SOLID_MATERIAL_ASSIGNMENTS::AppendMaterialAssignment(CDI_SCMA scma)
{
  mMaterialAssignments.push_back(scma);
  UpdateMapForMaterialAssignment(scma);
}

void cCDI_SOLID_MATERIAL_ASSIGNMENTS::UpdateMapForMaterialAssignment(CDI_SCMA scma)
{
  for (cdiINT32 partIndex : scma->regions) {
    SetMaterialIndexForPart(partIndex, scma->material);
  }
}

// =======================================================================================
// Radiation Surface Conditions
// =======================================================================================

VOID
cdi_read_rdsc(CDI_INFO info, CDI_RDSC rdsc)
{
  rdsc->cccc = CDI_CHUNK_TYPE_RDSC;

  cdiINT32 index = 0;
  const char *chunkName = "rdsc";
  ccCDI_DO_INNER_CHUNK(index, chunkName, info)
  {
    CDI_NAME name = cdi_read_name(info);
    rdsc->name = name->name;
    cdi_destroy_name(name);
  }
  cdi_inner_chunk_read_parm(index, chunkName, info, &(rdsc->emissivity));
}

VOID
cdi_write_rdsc(CDI_INFO info, CDI_RDSC rdsc)
{
  rdsc->cccc = CDI_CHUNK_TYPE_RDSC;

  cdi_push(info, CDI_CHUNK_TYPE_RDSC);

  sCDI_NAME name;
  name.name = (char*)rdsc->name.c_str();
  name.n_char = rdsc->name.size();
  cdi_write_name(info, &name);

  cdi_write_parm(info, &(rdsc->emissivity));

  cdi_pop(info);
}

VOID cdi_destroy_hcsh(CDI_HCSH hcsh)
{
  cdi_clear_hcsh(hcsh);
  exa_free(hcsh);
}

VOID cdi_clear_hcsh(CDI_HCSH hcsh)
{
  if (hcsh != NULL) {
    exa_free(hcsh->hcsl);
  }
}

VOID cdi_write_scpr(CDI_INFO cdi_info, CDI_SCPR scpr)
{
  scpr->cccc = CDI_CHUNK_TYPE_SCPR;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_SCPR);

  cdi_write_enum(cdi_info, scpr->criteria);
  cdi_write_enum(cdi_info, scpr->mesh_keep_option);
  if (scpr->criteria == eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Material) {
    cdi_write_enum(cdi_info, scpr->mesh_keep_property);
    cdi_write_int_(cdi_info, scpr->material_precedence_list_index);
  }

  cdi_pop(cdi_info);
}

VOID cdi_read_scpr(CDI_INFO cdi_info, CDI_SCPR scpr)
{
  scpr->cccc = CDI_CHUNK_TYPE_SCPR;

  cdiINT32 index = 0;
  const char *chunkName = "scpr";

  cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &(scpr->criteria));
  cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &(scpr->mesh_keep_option));
  if (scpr->criteria == eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Material) {
    cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &(scpr->mesh_keep_property));
    cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &(scpr->material_precedence_list_index));
  }
}

sCDI_SCPR cdi_convert_old_mesh_enum_to_scpr(eCDI_THERMAL_CONTACT_MESH::Enum oldMeshToKeep)
{
  sCDI_SCPR scpr;
  scpr.cccc = CDI_CHUNK_TYPE_SCPR;
  if (oldMeshToKeep == eCDI_THERMAL_CONTACT_MESH::Part1) {
    scpr.criteria = eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Part;
    scpr.mesh_keep_option = eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Part1;
  }
  else if (oldMeshToKeep == eCDI_THERMAL_CONTACT_MESH::Part2) {
    scpr.criteria = eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Part;
    scpr.mesh_keep_option = eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Part2;
  }
  else if (oldMeshToKeep == eCDI_THERMAL_CONTACT_MESH::MinArea) {
    scpr.criteria = eCDI_THERMAL_CONTACT_CRITERIA_TYPE::ContactArea;
    scpr.mesh_keep_option = eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Min;
  }
  else if (oldMeshToKeep == eCDI_THERMAL_CONTACT_MESH::MaxArea) {
    scpr.criteria = eCDI_THERMAL_CONTACT_CRITERIA_TYPE::ContactArea;
    scpr.mesh_keep_option = eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Max;
  }
  return scpr;
}

eCDI_THERMAL_CONTACT_MESH::Enum cdi_convert_scpr_to_old_mesh_enum(const sCDI_SCPR &scpr)
{
  eCDI_THERMAL_CONTACT_MESH::Enum meshToKeep = eCDI_THERMAL_CONTACT_MESH::SameAsParent;
  if (scpr.criteria == eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Part) {
    if (scpr.mesh_keep_option == eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Part1)
      meshToKeep = eCDI_THERMAL_CONTACT_MESH::Part1;
    else if (scpr.mesh_keep_option == eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Part2)
      meshToKeep = eCDI_THERMAL_CONTACT_MESH::Part2;
  }
  else if (scpr.criteria == eCDI_THERMAL_CONTACT_CRITERIA_TYPE::ContactArea) {
    if (scpr.mesh_keep_option == eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Min)
      meshToKeep = eCDI_THERMAL_CONTACT_MESH::MinArea;
    else if (scpr.mesh_keep_option == eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Max)
      meshToKeep = eCDI_THERMAL_CONTACT_MESH::MaxArea;
  }
  return meshToKeep;
}

VOID cdi_write_mtpr(CDI_INFO cdi_info, CDI_MTPR mtpr)
{
  mtpr->cccc = CDI_CHUNK_TYPE_MTPR;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_MTPR);

  cdi_write_strg(cdi_info, mtpr->name);

  cdiINT32 numMaterials = mtpr->materials.size();
  cdi_write_asINT32(cdi_info, &numMaterials, 1);
  if (numMaterials > 0) {
    cdiINT32* materialsArray = &(mtpr->materials[0]);
    cdi_write_asINT32(cdi_info, materialsArray, numMaterials);
  }

  cdi_pop(cdi_info);
}

VOID cdi_read_mtpr(CDI_INFO cdi_info, CDI_MTPR mtpr)
{
  mtpr->cccc = CDI_CHUNK_TYPE_MTPR;

  cdiINT32 index = 0;
  const char *chunkName = "mtpr";

  cdi_inner_chunk_read_strg(index, chunkName, cdi_info, &(mtpr->name));

  cdiINT32 numMaterials = 0;
  cdi_read_asINT32(cdi_info, &numMaterials, 1);
  mtpr->materials.resize(numMaterials);
  for (int im = 0; im < numMaterials; im++) {
    cdi_read_asINT32(cdi_info, &(mtpr->materials[im]), 1);
  }
}

cCDI_CUSTOM_MATERIAL_PRECEDENCES::~cCDI_CUSTOM_MATERIAL_PRECEDENCES()
{
  for (CDI_MTPR mtpr : mPrecedences) {
    delete mtpr;
  }
  mPrecedences.clear();
}

void cCDI_CUSTOM_MATERIAL_PRECEDENCES::ReadFromCDI(CDI_INFO cdi_info)
{
  // This should be called when the "MTPL" chunk is hit
  asINT32 count = cio_get_count(cdi_info->cio_info);

  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);
    {
      CIO_CCCC type = cio_get_type(cdi_info->cio_info);
      if (type == CDI_CHUNK_TYPE_MTPR) {
        CDI_MTPR mtpr = new sCDI_MTPR();
        cdi_read_mtpr(cdi_info, mtpr);
        mPrecedences.push_back(mtpr);
      }
    }
    cio_ascend(cdi_info->cio_info);
  }
}

std::pair<bool, bool> cCDI_CUSTOM_MATERIAL_PRECEDENCES::IsMaterialPreferred(CDI_SCMT material1, CDI_SCMT material2, cdiINT32 listIndex) const
{
  bool hasPreference = false;
  bool isMaterial1Preferred = false;
  int materialPriority1 = -1;
  int materialPriority2 = -1;
  if (!mMaterials.empty()) {
    if (listIndex >= 0 && listIndex < mPrecedences.size()) {
      CDI_MTPR mtpr = mPrecedences[listIndex];
      for (int i = 0; i < mtpr->materials.size(); ++i) {
        CDI_SCMT scmt = mMaterials[mtpr->materials[i]];
        if (scmt == material1)
          materialPriority1 = i;
        if (scmt == material2)
          materialPriority2 = i;
      }
    }
  }
  if (materialPriority1 >= 0) {
    hasPreference = true;
    isMaterial1Preferred = materialPriority2 >= 0 ? materialPriority1 <= materialPriority2 : true;
  }
  else if (materialPriority2 >= 0) {
    hasPreference = true;
    isMaterial1Preferred = false;
  }
  return std::make_pair(hasPreference, isMaterial1Preferred);
}

VOID cdi_write_stcp(CDI_INFO cdi_info, CDI_STCP stcp)
{
  stcp->cccc = CDI_CHUNK_TYPE_STCP;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_STCP);

  cdi_write_enum(cdi_info, stcp->enabled);
  cdi_write_enum(cdi_info, stcp->contact_extent);
  // Original files used to have contact resistance. Dummy value is fine.
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 2)) {
    sCDI_PARM resistance;
    cdi_write_parm(cdi_info, &resistance);
  }
  cdi_write_enum(cdi_info, stcp->heat_uniform);
  cdi_write_enum(cdi_info, stcp->join_parts);
  // For older CDIs, need to write the old mesh_to_keep value
  eCDI_THERMAL_CONTACT_MESH::Enum writeMeshToKeep = stcp->mesh_to_keep;
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 12)) {
    if (stcp->mesh_to_keep == eCDI_THERMAL_CONTACT_MESH::UserPrecedence && !stcp->mesh_precedence_list.empty()) {
      writeMeshToKeep = cdi_convert_scpr_to_old_mesh_enum(stcp->mesh_precedence_list[0]);
    }
  }
  cdi_write_enum(cdi_info, writeMeshToKeep);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 3)) {
    cdi_write_parm(cdi_info, &(stcp->max_separation));
    cdi_write_parm(cdi_info, &(stcp->max_angle));
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 7)) {
    cdi_write_bool(cdi_info, stcp->close_gaps);
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 12)) {
    if (stcp->mesh_to_keep == eCDI_THERMAL_CONTACT_MESH::UserPrecedence) {
      WITH_CDI_CHUNK(cdi_info, CDI_CHUNK_TYPE_SCPL) {
        for (sCDI_SCPR scpr : stcp->mesh_precedence_list)
          cdi_write_scpr(cdi_info, &scpr);
      }
    }
  }

  cdi_pop(cdi_info);
}

VOID cdi_read_stcp(CDI_INFO cdi_info, CDI_STCP stcp)
{
  stcp->cccc = CDI_CHUNK_TYPE_STCP;

  cdiINT32 index = 0;
  const char *chunkName = "stcp";

  cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &(stcp->enabled));
  cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &(stcp->contact_extent));
  // Original files used to have contact resistance
  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 2)) {
    sCDI_PARM resistance;
    cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &resistance);
  }
  cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &(stcp->heat_uniform));
  cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &(stcp->join_parts));
  cdi_inner_chunk_read_enum(index, chunkName, cdi_info, &(stcp->mesh_to_keep));
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 3)) {
    cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &(stcp->max_separation));
    cdi_inner_chunk_read_parm(index, chunkName, cdi_info, &(stcp->max_angle));
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 7)) {
    cdi_inner_chunk_read_bool(index, chunkName, cdi_info, &(stcp->close_gaps));
  }
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 12)) {
    // SCPL chunk
    if (stcp->mesh_to_keep == eCDI_THERMAL_CONTACT_MESH::UserPrecedence) {
      CDI_WITH_INNER_CHUNK(cdi_info) {
        if (cdi_get_type(cdi_info) == CDI_CHUNK_TYPE_SCPL) {
          asINT32 count = cio_get_count(cdi_info->cio_info);
          ccDOTIMES(i, count) {
            cio_descend(cdi_info->cio_info);
            {
              CIO_CCCC type = cio_get_type(cdi_info->cio_info);
              if (type == CDI_CHUNK_TYPE_SCPR) {
                sCDI_SCPR scpr;
                cdi_read_scpr(cdi_info, &scpr);
                stcp->mesh_precedence_list.push_back(scpr);
              }
            }
            cio_ascend(cdi_info->cio_info);
          }
        }
      }
    }
  }
  else {
    // For older CDIs, need to convert mesh_to_keep into a precedence list
    if (stcp->mesh_to_keep != eCDI_THERMAL_CONTACT_MESH::SameAsParent) {
      sCDI_SCPR scpr = cdi_convert_old_mesh_enum_to_scpr(stcp->mesh_to_keep);
      stcp->mesh_precedence_list.push_back(scpr);
      stcp->mesh_to_keep = eCDI_THERMAL_CONTACT_MESH::UserPrecedence;
    }
  }
}

VOID cdi_write_stcr(CDI_INFO cdi_info, CDI_STCR stcr)
{
  stcr->cccc = CDI_CHUNK_TYPE_STCR;

  cdi_push(cdi_info, CDI_CHUNK_TYPE_STCR);

  sCDI_NAME name;
  name.name = (char*)stcr->region_name.c_str();
  name.n_char = stcr->region_name.size();
  cdi_write_name(cdi_info, &name);

  cdi_write_int_(cdi_info, stcr->parent_index);

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 14)) {
    WITH_CDI_CHUNK(cdi_info, stcr->geometry_1.GetChunkType()) {
      stcr->geometry_1.WriteToCDI(cdi_info);
    }
    WITH_CDI_CHUNK(cdi_info, stcr->geometry_2.GetChunkType()) {
      stcr->geometry_2.WriteToCDI(cdi_info);
    }
  }
  else {
    cdiINT32 face1_index = -1;
    cdiINT32 face2_index = -1;
    if (!stcr->geometry_1.face_list.empty() && !stcr->geometry_2.face_list.empty()) {
      face1_index = stcr->geometry_1.face_list[0];
      face2_index = stcr->geometry_2.face_list[0];
    }

    cdi_write_int_(cdi_info, face1_index);
    cdi_write_int_(cdi_info, face2_index);

    cdi_write_rgns(cdi_info, const_cast<std::vector<int>&>(stcr->geometry_1.rgn_list));
    cdi_write_rgns(cdi_info, const_cast<std::vector<int>&>(stcr->geometry_2.rgn_list));
  }

  cdi_write_stcp(cdi_info, &(stcr->contact_parameters));

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 2))
    cdi_write_int_(cdi_info, stcr->surf_props_index);
  
  cdi_pop(cdi_info);
}

VOID
cdi_read_stcr(CDI_INFO cdi_info, CDI_STCR stcr)
{
  stcr->cccc = CDI_CHUNK_TYPE_STCR;

  cdiINT32 index = 0;
  const char *chunkName = "stcr";
  ccCDI_DO_INNER_CHUNK(index, chunkName, cdi_info)
  {
    CDI_NAME name = cdi_read_name(cdi_info);
    stcr->region_name = name->name;
    cdi_destroy_name(name);
  }

  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &(stcr->parent_index));

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 14)) {
    CDI_WITH_INNER_CHUNK(cdi_info) {
      if (cdi_get_type(cdi_info) == stcr->geometry_1.GetChunkType()) {
        stcr->geometry_1.ReadFromCDI(cdi_info);
      }
    }
    CDI_WITH_INNER_CHUNK(cdi_info) {
      if (cdi_get_type(cdi_info) == stcr->geometry_2.GetChunkType()) {
        stcr->geometry_2.ReadFromCDI(cdi_info);
      }
    }
  }
  else {
    cdiINT32 face1_index = -1;
    cdiINT32 face2_index = -1;
    cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &face1_index);
    cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &face2_index);
    if (face1_index >= 0 && face2_index >= 0) {
      stcr->geometry_1.GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
      stcr->geometry_2.GeometrySelectionType(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face);
      stcr->geometry_1.face_list.push_back(face1_index);
      stcr->geometry_2.face_list.push_back(face2_index);
    }

    cdi_inner_chunk_read_rgns(index, chunkName, cdi_info, stcr->geometry_1.rgn_list);
    cdi_inner_chunk_read_rgns(index, chunkName, cdi_info, stcr->geometry_2.rgn_list);
  }

  CDI_WITH_INNER_CHUNK(cdi_info) {
    cdi_read_stcp(cdi_info, &(stcr->contact_parameters));
  }

  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 2))
    cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &(stcr->surf_props_index));
}

VOID cdi_destroy_stcr(CDI_STCR stcr)
{
  exa_free(stcr);
}

cCDI_THERMAL_CONTACTS::~cCDI_THERMAL_CONTACTS()
{
  for (CDI_STCR contact : mThermalContacts) {
    delete contact;
  }
  mThermalContacts.clear();
}

void cCDI_THERMAL_CONTACTS::ReadFromCDI(CDI_INFO cdi_info)
{
  // This should be called when the "STCT" chunk is hit
  asINT32 count = cio_get_count(cdi_info->cio_info);

  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);
    {
      CIO_CCCC type = cio_get_type(cdi_info->cio_info);
      if (type == CDI_CHUNK_TYPE_STCR) {
        CDI_STCR stcr = new sCDI_STCR();
        cdi_read_stcr(cdi_info, stcr);
        mThermalContacts.push_back(stcr);
      }
    }
    cio_ascend(cdi_info->cio_info);
  }
}

void cCDI_THERMAL_CONTACTS::BuildContactTable(cCDI_PARTITIONS& partitions)
{
  // Go through the contact hierarchy in reverse so that any overloads are done first
  // Can skip the root (index = 0) because we do not include that in the map for efficiency
  std::set<cdiINT32> assignedFaces;
  for (size_t iContact = mThermalContacts.size() - 1; iContact > 0; --iContact) {
    CDI_STCR findContact = mThermalContacts[iContact];
    if (IsEntireFace(iContact)) {
      std::vector<cdiINT32> faces1 = findContact->geometry_1.ExpandSelection(partitions, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, true);
      std::vector<cdiINT32> faces2 = findContact->geometry_2.ExpandSelection(partitions, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, true);
      std::vector<cdiINT32> allowedFaces1, allowedFaces2;
      for (cdiINT32 face1 : faces1) {
        if (assignedFaces.find(face1) == assignedFaces.end()) {
          allowedFaces1.push_back(face1);
          assignedFaces.insert(face1);
        }
      }
      for (cdiINT32 face2 : faces2) {
        if (assignedFaces.find(face2) == assignedFaces.end()) {
          allowedFaces2.push_back(face2);
          assignedFaces.insert(face2);
        }
      }
      SetContactIndexForEntireFace(allowedFaces1, allowedFaces2, iContact);
    }
    else {
      // Create the combinations of parts
      std::vector<cdiINT32> parts1 = findContact->geometry_1.ExpandSelection(partitions, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part, true);
      std::vector<cdiINT32> parts2 = findContact->geometry_2.ExpandSelection(partitions, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part, true);
      for (cdiINT32 part1Index : parts1) {
        for (cdiINT32 part2Index : parts2) {
          if (!mContactPartPair.Exist(part1Index, part2Index)) {
            SetContactIndexForPartPair(part1Index, part2Index, iContact, true);
          }
        }
      }
    }
  }
}


bool cCDI_THERMAL_CONTACTS::sCONTACT_INDEX_MAP::Sort(cdiINT32 &i1, cdiINT32 &i2) const
{
  bool reversed = (i1 > i2);
  if (reversed) {
    cdiINT32 i1Original = i1;
    i1 = i2; 
    i2 = i1Original;
  }
  return reversed;
}

void cCDI_THERMAL_CONTACTS::sCONTACT_INDEX_MAP::Add(cdiINT32 i1, cdiINT32 i2, cdiINT32 iContact, bool is1Primary) 
{
  bool reversed = Sort(i1, i2);
  map[i1][i2] = std::make_pair(iContact, is1Primary!=reversed);
}

bool cCDI_THERMAL_CONTACTS::sCONTACT_INDEX_MAP::Exist(cdiINT32 i1, cdiINT32 i2) const
{
  Sort(i1, i2);
  auto it1 = map.find(i1);
  bool found = (it1 != map.end());
  if (it1 != map.end()) {
    auto& i1Map = it1->second;
    found = (i1Map.find(i2) != i1Map.end());
  }
  return found;
}

bool cCDI_THERMAL_CONTACTS::sCONTACT_INDEX_MAP::GetContactIndex(cdiINT32 i1, cdiINT32 i2, cdiINT32& iContact) const
{
  bool is1Primary = false;
  bool reversed = Sort(i1,i2);
  auto it1 = map.find(i1);
  if (it1 != map.end()) {
    auto& i1Map = it1->second;
    auto it2 = i1Map.find(i2);
    if (it2 != i1Map.end()) {
      iContact = it2->second.first;
      is1Primary = (it2->second.second != reversed);
    }
  }
  return is1Primary;
}

cdiINT32 cCDI_THERMAL_CONTACTS::GetContactIndexForPartPair(cdiINT32 partIndex1, cdiINT32 partIndex2) const
{
  cdiINT32 iContact = 0; // If not in the map, then it uses the "root" contact
  mContactPartPair.GetContactIndex(partIndex1, partIndex2, iContact);
  return iContact;
}
cdiINT32 cCDI_THERMAL_CONTACTS::GetContactIndexForPartPair(cdiINT32 partIndex1, cdiINT32 partIndex2, bool& isPart1Primary) const
{
  cdiINT32 iContact = 0; // If not in the map, then it uses the "root" contact
  bool primary1 = mContactPartPair.GetContactIndex(partIndex1, partIndex2, iContact);
  if ((iContact > 0) && ((size_t)iContact < mThermalContacts.size())) {
    isPart1Primary = primary1; 
  }
  return iContact;
}

cdiINT32 cCDI_THERMAL_CONTACTS::GetContactIndexForFacePair(cdiINT32 faceIndex1, cdiINT32 faceIndex2) const
{
  cdiINT32 iContact = -1; // If not in the map, then it uses the "root" contact
  mContactFacePair.GetContactIndex(faceIndex1, faceIndex2, iContact);
  return iContact;
}
cdiINT32 cCDI_THERMAL_CONTACTS::GetContactIndexForFacePair(cdiINT32 faceIndex1, cdiINT32 faceIndex2, bool& isFace1Primary) const
{
  cdiINT32 iContact = -1; // If not in the map, then there is no contact entry
  bool primary1 = mContactFacePair.GetContactIndex(faceIndex1, faceIndex2, iContact);
  if (ValidContactIndex(iContact)) {
    isFace1Primary = primary1;
  }
  return iContact;
}

void cCDI_THERMAL_CONTACTS::SetContactIndexForPartPair(cdiINT32 partIndex1, cdiINT32 partIndex2, cdiINT32 contactIndex, bool isPart1Primary)
{
  mContactPartPair.Add(partIndex1, partIndex2, contactIndex, isPart1Primary);
}

void cCDI_THERMAL_CONTACTS::SetContactIndexForFacePair(cdiINT32 faceIndex1, cdiINT32 faceIndex2, cdiINT32 contactIndex, bool isFace1Primary)
{
  mContactFacePair.Add(faceIndex1, faceIndex2, contactIndex, isFace1Primary);
}

void cCDI_THERMAL_CONTACTS::SetContactIndexForEntireFace(std::vector<cdiINT32> &faceIndices1, 
                                                         std::vector<cdiINT32> &faceIndices2, 
                                                         cdiINT32 contactIndex)
{
  mContactEntireFaceGeom[contactIndex] = std::make_pair(faceIndices1, faceIndices2);
  for (auto faceIndex1 : faceIndices1) {
    for (auto faceIndex2: faceIndices2) {
      SetContactIndexForFacePair(faceIndex1, faceIndex2, contactIndex, true);
    }
  }
}

void cCDI_THERMAL_CONTACTS::AppendContactRegion(CDI_STCR stcr)
{
  mThermalContacts.push_back(stcr);
}

CDI_STCR cCDI_THERMAL_CONTACTS::GetContactRegion(cdiINT32 contactIndex)
{
  return ValidContactIndex(contactIndex) ? mThermalContacts[contactIndex] : nullptr;
}

void cCDI_THERMAL_CONTACTS::GetContactRegionParents(cdiINT32 contactIndex,
                                                    std::vector<asINT32> &parentContactIndices)
{
  parentContactIndices.clear();
  CDI_STCR stcr = GetContactRegion(contactIndex);
  if (nullptr == stcr)
    return;
  asINT32 parent_index = stcr->parent_index;
  while (parent_index >= 0) {
    parentContactIndices.push_back(parent_index);
    stcr = GetContactRegion(parent_index);
    parent_index = (nullptr == stcr) ? -1 : stcr->parent_index;
  }
}

bool cCDI_THERMAL_CONTACTS::GetContactEntireFaceGeometryIndices(cdiINT32 contactIndex, std::vector<cdiINT32> &faceIndices1, std::vector<cdiINT32> &faceIndices2)
{
  faceIndices1.clear();
  faceIndices2.clear();
  if (!ValidContactIndex(contactIndex) || !IsEntireFace(contactIndex))
    return false;

  auto itMap = mContactEntireFaceGeom.find(contactIndex);
  if (itMap != mContactEntireFaceGeom.end()) {
    std::pair<std::vector<cdiINT32>, std::vector<cdiINT32>> faceIndices = itMap->second;
    faceIndices1 = faceIndices.first;
    faceIndices2 = faceIndices.second;
  }

  return true;
}

sCDI_STCP cCDI_THERMAL_CONTACTS::GetContactParameters(cdiINT32 contactIndex)
{
  CDI_STCR stcr = GetContactRegion(contactIndex);
  if (nullptr == stcr)
    return mThermalContacts[0]->contact_parameters;
  else {
    sCDI_STCP stcp = stcr->contact_parameters; // a copy to be resolved
    std::vector<asINT32> parents;
    GetContactRegionParents(contactIndex, parents);
    for (auto &ip : parents) {
      CDI_STCR parent_stcr = GetContactRegion(ip);
      if (parent_stcr != nullptr) {
        sCDI_STCP &parent_stcp = parent_stcr->contact_parameters;
        if (stcp.enabled == eCDI_THERMAL_CONTACT_BOOL::Enum::SameAsParent &&
            parent_stcp.enabled != eCDI_THERMAL_CONTACT_BOOL::Enum::SameAsParent)
          stcp.enabled = parent_stcp.enabled;
        if (stcp.contact_extent == eCDI_THERMAL_CONTACT_EXTENT::Enum::SameAsParent &&
            parent_stcp.contact_extent != eCDI_THERMAL_CONTACT_EXTENT::Enum::SameAsParent) {
          stcp.contact_extent = parent_stcp.contact_extent;
          if (stcp.contact_extent == eCDI_THERMAL_CONTACT_EXTENT::Enum::IntersectionAndGaps) {
            stcp.max_angle = parent_stcp.max_angle;
            stcp.max_separation = parent_stcp.max_separation;
            stcp.close_gaps = parent_stcp.close_gaps;
          }
        }
        if (stcp.join_parts == eCDI_THERMAL_CONTACT_BOOL::Enum::SameAsParent &&
            parent_stcp.join_parts != eCDI_THERMAL_CONTACT_BOOL::Enum::SameAsParent)
          stcp.join_parts = parent_stcp.join_parts;
        if (stcp.mesh_to_keep == eCDI_THERMAL_CONTACT_MESH::Enum::SameAsParent &&
            parent_stcp.mesh_to_keep != eCDI_THERMAL_CONTACT_MESH::Enum::SameAsParent) {
          stcp.mesh_to_keep = parent_stcp.mesh_to_keep;
          stcp.mesh_precedence_list = parent_stcp.mesh_precedence_list;
        }
      }
    }
    assert(stcp.enabled != eCDI_THERMAL_CONTACT_BOOL::Enum::SameAsParent);
    assert(stcp.contact_extent != eCDI_THERMAL_CONTACT_EXTENT::Enum::SameAsParent);
    assert(stcp.join_parts != eCDI_THERMAL_CONTACT_BOOL::Enum::SameAsParent);
    assert(stcp.mesh_to_keep != eCDI_THERMAL_CONTACT_MESH::Enum::SameAsParent);
    return stcp;
  }
}

bool cCDI_THERMAL_CONTACTS::IsEntireFace(cdiINT32 contactIndex)
{
  CDI_STCR stcr = GetContactRegion(contactIndex);
  if (nullptr == stcr) {
    return (mThermalContacts[0]->contact_parameters.contact_extent == eCDI_THERMAL_CONTACT_EXTENT::Enum::EntireFace);
  } else if (stcr->contact_parameters.contact_extent != eCDI_THERMAL_CONTACT_EXTENT::Enum::SameAsParent) {
    return (stcr->contact_parameters.contact_extent == eCDI_THERMAL_CONTACT_EXTENT::Enum::EntireFace);
  } else {
    //output value to be resolved checking the hierarchy
    std::vector<asINT32> parents;
    GetContactRegionParents(contactIndex, parents);
    for (auto &ip : parents) {
      CDI_STCR parent_stcr = GetContactRegion(ip);
      if (parent_stcr != nullptr && 
          parent_stcr->contact_parameters.contact_extent != eCDI_THERMAL_CONTACT_EXTENT::Enum::SameAsParent) {
        return (parent_stcr->contact_parameters.contact_extent == eCDI_THERMAL_CONTACT_EXTENT::Enum::EntireFace);
      }
    }
    //no valid parent found, returns root value
    return (mThermalContacts[0]->contact_parameters.contact_extent == eCDI_THERMAL_CONTACT_EXTENT::Enum::EntireFace);
  }
}

bool cCDI_THERMAL_CONTACTS::IsHeatUniform(cdiINT32 contactIndex)
{
  CDI_STCR stcr = GetContactRegion(contactIndex);
  if (nullptr == stcr) {
    return (mThermalContacts[0]->contact_parameters.heat_uniform == eCDI_THERMAL_CONTACT_BOOL::Enum::Yes);
  } else if (stcr->contact_parameters.heat_uniform != eCDI_THERMAL_CONTACT_BOOL::Enum::SameAsParent) {
    return (stcr->contact_parameters.heat_uniform == eCDI_THERMAL_CONTACT_BOOL::Enum::Yes);
  } else {
    //output value to be resolved checking the hierarchy
    std::vector<asINT32> parents;
    GetContactRegionParents(contactIndex, parents);
    for (auto &ip : parents) {
      CDI_STCR parent_stcr = GetContactRegion(ip);
      if (parent_stcr != nullptr && 
          parent_stcr->contact_parameters.heat_uniform != eCDI_THERMAL_CONTACT_BOOL::Enum::SameAsParent) {
        return (parent_stcr->contact_parameters.heat_uniform == eCDI_THERMAL_CONTACT_BOOL::Enum::Yes);
      }    
    }
    //no valid parent found, returns root value
    return (mThermalContacts[0]->contact_parameters.heat_uniform == eCDI_THERMAL_CONTACT_BOOL::Enum::Yes);
  }
}

bool cCDI_THERMAL_CONTACTS::GetMeshPrecedenceForPair(const sCDI_CONTACT_PRECEDENCE_PARAMETERS& part1,
                                                     const sCDI_CONTACT_PRECEDENCE_PARAMETERS& part2,
                                                     const sCDI_STCP& contactParameters,
                                                     bool isPart1Primary)
{
  bool part1Preferred = true;
  for (sCDI_SCPR precedence : contactParameters.mesh_precedence_list) {
    bool criteriaMet = false;
    auto ComparePropValues = [&criteriaMet, &part1Preferred](double part1Value, double part2Value, eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Enum keepOption)
    {
      if (keepOption == eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Min || keepOption == eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Max) {
        if (part1Value > part2Value) {
          part1Preferred = (keepOption == eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Max);
          criteriaMet = true;
        }
        else if (part1Value < part2Value) {
          part1Preferred = (keepOption == eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Min);
          criteriaMet = true;
        }
      }
    };
    if (precedence.criteria == eCDI_THERMAL_CONTACT_CRITERIA_TYPE::ContactArea) {
      ComparePropValues(part1.contact_area, part2.contact_area, precedence.mesh_keep_option);
    }
    else if (precedence.criteria == eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Part) {
      part1Preferred = ((precedence.mesh_keep_option == eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Part1) ?
                        isPart1Primary : !isPart1Primary);
      criteriaMet = true;
    }
    else if (precedence.criteria == eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Volume) {
      ComparePropValues(part1.volume, part2.volume, precedence.mesh_keep_option);
    }
    else if (precedence.criteria == eCDI_THERMAL_CONTACT_CRITERIA_TYPE::Material) {
      if (part1.material && part2.material) {
        if (precedence.mesh_keep_option == eCDI_THERMAL_CONTACT_MESH_KEEP_OPTION::Custom) {
          if (mCustomMaterialPrecedences) {
            std::tie(criteriaMet, part1Preferred) =
              mCustomMaterialPrecedences->IsMaterialPreferred(part1.material, part2.material, precedence.material_precedence_list_index);
          }
        }
        else {
          double part1PropValue = -1.0;
          double part2PropValue = -1.0;
          // The parameter values should be in lattice units
          // None should be equations but add the check
          auto GetParmLatticeValue = [](const sCDI_PARM& parm) -> double
          {
            return (parm.isEqVariable || parm.isDataCurve) ? -1.0 : parm.value;
          };
          
          if (precedence.mesh_keep_property == eCDI_THERMAL_CONTACT_PRECEDENCE_MATERIAL_PROP::Density) {
            part1PropValue = GetParmLatticeValue(part1.material->density);
            part2PropValue = GetParmLatticeValue(part2.material->density);
          }
          else if (precedence.mesh_keep_property == eCDI_THERMAL_CONTACT_PRECEDENCE_MATERIAL_PROP::SpecificHeat) {
            part1PropValue = GetParmLatticeValue(part1.material->specific_heat);
            part2PropValue = GetParmLatticeValue(part2.material->specific_heat);
          }
          else if (precedence.mesh_keep_property == eCDI_THERMAL_CONTACT_PRECEDENCE_MATERIAL_PROP::ThermalConductivity) {
            // Only need to compare conductivity in "principal" direction, which is entry 0
            part1PropValue = GetParmLatticeValue(part1.material->therm_conductivity[0]);
            part2PropValue = GetParmLatticeValue(part2.material->therm_conductivity[0]);
          }
          else if (precedence.mesh_keep_property == eCDI_THERMAL_CONTACT_PRECEDENCE_MATERIAL_PROP::ThermalDiffusivity) {
            // Only need to compare diffusivity in "principal" direction, which is entry 0
            if (GetParmLatticeValue(part1.material->density) > 0.0 &&
                GetParmLatticeValue(part1.material->specific_heat) > 0.0 &&
                GetParmLatticeValue(part1.material->therm_conductivity[0]) > 0.0)
              part1PropValue = GetParmLatticeValue(part1.material->therm_conductivity[0])
                / (GetParmLatticeValue(part1.material->density) * GetParmLatticeValue(part1.material->specific_heat));
            if (GetParmLatticeValue(part2.material->density) > 0.0 &&
                GetParmLatticeValue(part2.material->specific_heat) > 0.0 &&
                GetParmLatticeValue(part2.material->therm_conductivity[0]) > 0.0)
              part2PropValue = GetParmLatticeValue(part2.material->therm_conductivity[0])
                / (GetParmLatticeValue(part2.material->density) * GetParmLatticeValue(part2.material->specific_heat));
          }
          if (part1PropValue > 0.0 && part2PropValue > 0.0)
            ComparePropValues(part1PropValue, part2PropValue, precedence.mesh_keep_option);
        }
      }
    }
    if (criteriaMet)
      break;
  }
  return part1Preferred;
}

std::pair<bool, bool> cCDI_THERMAL_CONTACTS::GetMeshPrecedenceForPartPair(const sCDI_CONTACT_PRECEDENCE_PARAMETERS& part1,
                                                                          const sCDI_CONTACT_PRECEDENCE_PARAMETERS& part2)
{
  bool contactExists = false;
  bool part1Preferred = true;
  bool isPart1Primary = false;
  cdiINT32 contactIndex = GetContactIndexForPartPair(part1.geometry_index, part2.geometry_index, isPart1Primary);
  contactExists = (contactIndex >= 0);
  if (contactExists) {
    sCDI_STCP contactParameters = GetContactParameters(contactIndex);
    part1Preferred = GetMeshPrecedenceForPair(part1, part2, contactParameters, isPart1Primary);
  }
  return std::make_pair(contactExists, part1Preferred);
}

std::pair<bool, bool> cCDI_THERMAL_CONTACTS::GetMeshPrecedenceForFacePair(const sCDI_CONTACT_PRECEDENCE_PARAMETERS& face1,
                                                                          const sCDI_CONTACT_PRECEDENCE_PARAMETERS& face2)
{
  bool contactExists = false;
  bool face1Preferred = true;
  bool isFace1Primary = false;
  cdiINT32 contactIndex = GetContactIndexForFacePair(face1.geometry_index, face2.geometry_index, isFace1Primary);
  contactExists = (contactIndex >= 0);
  if (contactExists) {
    sCDI_STCP contactParameters = GetContactParameters(contactIndex);
    face1Preferred = GetMeshPrecedenceForPair(face1, face2, contactParameters, isFace1Primary);
  }
  return std::make_pair(contactExists, face1Preferred);
}

#ifndef MAX
#define MAX(a, b) (((a) > (b)) ? (a) : (b))
#endif

sCDI_CPNT cdi_round_cpnt (asINT32 coarsest_timestep, 
                          sCDI_CPNT unrounded)
{
  dFLOAT period = unrounded.freq; /* yes, it is the period, not the freq */
  dFLOAT start = unrounded.start;
  dFLOAT end = unrounded.end;

  /* round period up to multiple of coarsest_timestep */
  dFLOAT maybe_period = coarsest_timestep * floor(period / coarsest_timestep + 0.5);

  /* period must be at least coarsest_timestep */
  dFLOAT period_prime = MAX(coarsest_timestep, maybe_period);

  /* round start down to multiple of coarsest_timestep */
  dFLOAT maybe_start = coarsest_timestep * floor(start / coarsest_timestep);

  /* start must be at least coarsest_timestep */
  dFLOAT start_prime = MAX(coarsest_timestep, maybe_start);

  /* round end up to multiple of coarsest_timestep */
  dFLOAT maybe_end = coarsest_timestep * ceil(end / coarsest_timestep);

  /* end must be at least start_time */
  dFLOAT end_prime = MAX(maybe_end, start_prime);

  { sCDI_CPNT result;
    result.start = (asINT32) start_prime;
    result.end = (asINT32) end_prime;
    result.freq = (asINT32) period_prime;

    return result;
  }
}

VOID cdi_syntax_error_descend (cSTRING chunk_name)
{
  msg_error("Syntax error in CDI file: unable to descend into %s chunk.",
            chunk_name);
  return;
}

VOID cdi_syntax_error_ascend (cSTRING chunk_name, asINT32 index)
{
  asINT32 last_digit = index % 10;
  msg_error("Syntax error in CDI file: unable to ascend from %d%s child of %s chunk.",
            index, 
            (last_digit == 1 ?
             "st" :
             last_digit == 2 ?
             "nd" :
             last_digit == 3 ?
             "rd" :
             "th"),
            chunk_name);
  return;
}

// Returns a cSRI_MEAS_VARS object given a set of measurement options
cSRI_MEAS_VARS cdi_mstp_vars(cdiINT32 cdi_major_version,
                             cdiINT32 cdi_minor_version,
                             const sCDI_MSTP*  mstp,
                             CDI_MEAS_WINDOW_TYPE meas_window_type,
                             cdiINT32 stp_lattice_type,
                             SRI_HT_TYPE ht_type,
                             cdiINT32 boolean_is_ht_off_in_powercase,
                             cdiINT32 boolean_is_3d,
                             cdiINT32 boolean_is_std_meas_mme,
                             cdiINT32 is_density_constant,
                             cdiINT32 is_water_vapor_transport,
                             const sSRI_CUSTOM_VAR_ID_HELPER& vih,
                             cdiINT32 boolean_remove_var,
                             cdiINT32 boolean_is_turb,
			     cdiINT32 is_Eutectic_Fluid)
{
  cSRI_MEAS_VARS var_types; 
  // to keep the cdi_readwrite.h header free of PHYSTYPES, perform this cast
  // here for the supplied lattice type
  STP_LATTICE_TYPE lattice_type = STP_LATTICE_TYPE(stp_lattice_type);
  BOOLEAN is_3d = BOOLEAN(boolean_is_3d);
  BOOLEAN is_std_meas_mme = BOOLEAN(boolean_is_std_meas_mme);
  BOOLEAN is_5g_sim = (lattice_type == STP_LATTICE_5G);

  cdiINT32 mask = mstp->standard_mask;

  // Special treatment for avg mme ckpt window. 
  // NOTE: We use the exact same logic for determing the variables for avg mme ckpt file 
  //       from cp/mme_ckpt.cc:compute_avg_mme_ckpt_var_types(). If that function is 
  //       changed, the same change should be made here for consistency.
  if (meas_window_type == CDI_FLUID_WINDOW && (mask & CDI_MEAS_OPT_AVERAGE_MME)) {
    if (is_5g_sim) {      
      ccDOTIMES(i, vih.get_num_5g_fluid_components())
        var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_DENSITY, i));
      var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_XVEL));  
      var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_YVEL));  
      if (is_3d)
        var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_ZVEL));  
    } else {
      if (is_density_constant)
        var_types.add_var(SRI_VARIABLE_PRESSURE);
      else
        var_types.add_var(SRI_VARIABLE_DENSITY);
      var_types.add_var(SRI_VARIABLE_XVEL);
      var_types.add_var(SRI_VARIABLE_YVEL);
      if (is_3d)
        var_types.add_var(SRI_VARIABLE_ZVEL);

      var_types.add_var(SRI_VARIABLE_USTAR);
      var_types.add_var(SRI_VARIABLE_DENSITY_FROZEN);
      var_types.add_var(SRI_VARIABLE_XVEL_FROZEN);
      var_types.add_var(SRI_VARIABLE_YVEL_FROZEN);
      if (is_3d)
        var_types.add_var(SRI_VARIABLE_ZVEL_FROZEN);
    }
    if (boolean_is_turb) {
      var_types.add_var(SRI_VARIABLE_TURB_KINETIC_ENERGY);
      var_types.add_var(SRI_VARIABLE_TURB_DISSIPATION);
      var_types.add_var(SRI_VARIABLE_STRESS_TENSOR_MAG);
    }
    if (!boolean_is_ht_off_in_powercase && ht_type != SRI_HT_ISOTHERMAL)
      var_types.add_var(SRI_VARIABLE_TEMP);
    if (is_water_vapor_transport) {
      /*if (is_5g_sim)
	var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFRAC));
	else*/
        var_types.add_var(SRI_VARIABLE_WATER_VAPOR_MFRAC);
    }
    /*if(is_Eutectic_Fluid)//MMD_Eutectic
      var_types.add_var(SRI_VARIABLE_LIQUID_FRACTION);*/
    return var_types;
  }

  // sampling surface or standard fluid or standard porous or standard surface
  if (((meas_window_type == CDI_SURFACE_WINDOW)  && (mstp->meas_type == CDI_MEASTYPE_SAMPLED_FACE)
       && (mask & CDI_MEAS_OPT_STANDARD_FLUID))
      || ((mask & CDI_MEAS_OPT_STANDARD_FLUID)   && (meas_window_type == CDI_FLUID_WINDOW))
      || ((mask & CDI_MEAS_OPT_STANDARD_POROUS)  && (meas_window_type == CDI_POROUS_WINDOW))
      || ((mask & CDI_MEAS_OPT_STANDARD_SURFACE) && (meas_window_type == CDI_SURFACE_WINDOW))) {
    if (is_5g_sim) {
      var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_DENSITY));
      ccDOTIMES(i, vih.get_num_5g_fluid_components())
        var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_DENSITY, i));
    } else {
      var_types.add_var(SRI_VARIABLE_DENSITY);
    }

    BOOLEAN add_liquid_fraction = is_Eutectic_Fluid && ((meas_window_type == CDI_FLUID_WINDOW) || (meas_window_type == CDI_POROUS_WINDOW));

    if (is_std_meas_mme) {
      var_types.add_var(SRI_VARIABLE_XMOMENTUM);
      var_types.add_var(SRI_VARIABLE_YMOMENTUM);
      if (is_3d)
        var_types.add_var(SRI_VARIABLE_ZMOMENTUM);
      switch (ht_type) {
      case SRI_HT_ISOTHERMAL:
        if (lattice_type == STP_LATTICE_D34)
          var_types.add_var(SRI_VARIABLE_LB_ENERGY);
	/*else if (is_5g_sim && is_water_vapor_transport)
	  var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFRAC));*/
        break;
      case SRI_HT_PASSIVE_SCALAR:
        if (lattice_type == STP_LATTICE_D34)
          var_types.add_var(SRI_VARIABLE_LB_ENERGY);
        var_types.add_var(SRI_VARIABLE_TEMP);
        if(is_water_vapor_transport)
          var_types.add_var(SRI_VARIABLE_WATER_VAPOR_MFRAC);
        if (add_liquid_fraction)//MMD_Eutectic
		  var_types.add_var(SRI_VARIABLE_LIQUID_FRACTION);
        break;
      case SRI_HT_ACTIVE_SCALAR:
        //var_types.add_var(SRI_VARIABLE_ENERGY);
        var_types.add_var(SRI_VARIABLE_TEMP);
        if(is_water_vapor_transport)
          var_types.add_var(SRI_VARIABLE_WATER_VAPOR_MFRAC);
        if (add_liquid_fraction)//MMD_Eutectic
		  var_types.add_var(SRI_VARIABLE_LIQUID_FRACTION);
        break;
      default:
        break;
      }
    }
    else {
      if (is_5g_sim) {
	var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_XVEL));
	var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_YVEL));
	if (is_3d)
	  var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_ZVEL));
      } else {
	var_types.add_var(SRI_VARIABLE_XVEL);
	var_types.add_var(SRI_VARIABLE_YVEL);
	if (is_3d)
	  var_types.add_var(SRI_VARIABLE_ZVEL);
      }

      // Pressure should be a standard variable for 5g. Currently, 5g only supports isothermal cases.
      switch (ht_type) {
      case SRI_HT_ISOTHERMAL:
        if (lattice_type == STP_LATTICE_D34) 
          var_types.add_var(SRI_VARIABLE_PRESSURE);
	else if(is_5g_sim) {
	  var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_PRESSURE));
	  /*if(is_water_vapor_transport)
	    var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFRAC));*/
	}
        break;
      case SRI_HT_PASSIVE_SCALAR:
        if (lattice_type == STP_LATTICE_D34)
          var_types.add_var(SRI_VARIABLE_PRESSURE);
        var_types.add_var(SRI_VARIABLE_TEMP);
        if(is_water_vapor_transport)
          var_types.add_var(SRI_VARIABLE_WATER_VAPOR_MFRAC);
        if (add_liquid_fraction)//MMD_Eutectic
  		 var_types.add_var(SRI_VARIABLE_LIQUID_FRACTION);
        // No handling of is_5g_sim=TRUE because we don't yet allow 5g passive
        // scalar sims.
        break;
      case SRI_HT_ACTIVE_SCALAR:
        var_types.add_var(SRI_VARIABLE_PRESSURE);
        var_types.add_var(SRI_VARIABLE_TEMP);
        if(is_water_vapor_transport)
          var_types.add_var(SRI_VARIABLE_WATER_VAPOR_MFRAC);
        if (add_liquid_fraction)//MMD_Eutectic
		  var_types.add_var(SRI_VARIABLE_LIQUID_FRACTION);
        // No handling of is_5g_sim=TRUE because we don't yet allow 5g active
        // scalar sims.
        break;
      default:
        break;
      }
    }
  }

  // standard fluid particle vars: Particle Number, Mean Particle Velocity,
  //                               Mean Particle Diam, Mean Particle Surface Area,
  //                               Mean Particle Volume, Particle Mass Flux (for sampled face meas)
  if ((mask & CDI_MEAS_OPT_STANDARD_FLUID_PARTICLE) &&
      (meas_window_type == CDI_FLUID_WINDOW)) {
    var_types.add_var(SRI_VARIABLE_PRTCL_NUMBER);
    var_types.add_var(SRI_VARIABLE_PRTCL_MEAN_XVEL);
    var_types.add_var(SRI_VARIABLE_PRTCL_MEAN_YVEL);
    if (is_3d)
      var_types.add_var(SRI_VARIABLE_PRTCL_MEAN_ZVEL);
    var_types.add_var(SRI_VARIABLE_PRTCL_MEAN_DIAMETER);
    var_types.add_var(SRI_VARIABLE_PRTCL_MEAN_VOLUME);
  }
  // standard surface particle vars: Mean Particle Diam, Film Vel., Film Thickness
  if ((mask & CDI_MEAS_OPT_STANDARD_SURFACE_PARTICLE) &&
      (meas_window_type == CDI_SURFACE_WINDOW)) {
    var_types.add_var(SRI_VARIABLE_PRTCL_MASS_RATE_INBOUND);
    var_types.add_var(SRI_VARIABLE_PRTCL_MEAN_DIAM_INBOUND);
    var_types.add_var(SRI_VARIABLE_FILM_XVEL);
    var_types.add_var(SRI_VARIABLE_FILM_YVEL);
    if (is_3d)
      var_types.add_var(SRI_VARIABLE_FILM_ZVEL);
    var_types.add_var(SRI_VARIABLE_FILM_THICKNESS);
  }

  // sampled face measurement with standard fluid particle measurements
  if ((meas_window_type == CDI_SURFACE_WINDOW) && (mstp->meas_type == CDI_MEASTYPE_SAMPLED_FACE) &&
      (mask & CDI_MEAS_OPT_STANDARD_FLUID_PARTICLE)) {
    var_types.add_var(SRI_VARIABLE_PRTCL_MEAN_DENSITY);
    var_types.add_var(SRI_VARIABLE_PRTCL_MEAN_DIAMETER);
    var_types.add_var(SRI_VARIABLE_PRTCL_MEAN_VOLUME);
    var_types.add_var(SRI_VARIABLE_PRTCL_MASS_FLUX);
    var_types.add_var(SRI_VARIABLE_PRTCL_FLUX);
  }

  // standard surface
  if ((mask & CDI_MEAS_OPT_STANDARD_SURFACE) &&
      (meas_window_type == CDI_SURFACE_WINDOW)) {
    if (is_std_meas_mme
        && (lattice_type == STP_LATTICE_D34
            || ht_type == SRI_HT_ACTIVE_SCALAR)) {
      // For 19-state, kinetic force only yields different results for active scalar
      var_types.add_var(SRI_VARIABLE_KINETIC_XFORCE);
      var_types.add_var(SRI_VARIABLE_KINETIC_YFORCE);
      if (is_3d)
        var_types.add_var(SRI_VARIABLE_KINETIC_ZFORCE);
    }
    else {
      if (is_5g_sim) {
	var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_XFORCE));
	var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_YFORCE));
	if (is_3d)
	  var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_ZFORCE));
      } else {
	var_types.add_var(SRI_VARIABLE_XFORCE);
	var_types.add_var(SRI_VARIABLE_YFORCE);
	if (is_3d)
	  var_types.add_var(SRI_VARIABLE_ZFORCE);
      }
    }
    // For surface files in isothermal cases, near wall temp is recorded in TEMP 
    // (not NEAR_WALL_TEMP). This is terribly confusing, but history is sometimes ugly.
    if (!boolean_is_ht_off_in_powercase && 
        (ht_type == SRI_HT_ACTIVE_SCALAR || ht_type == SRI_HT_PASSIVE_SCALAR)) {
      var_types.add_var(SRI_VARIABLE_NEAR_WALL_TEMP);
      var_types.add_var(SRI_VARIABLE_HEAT_FLUX);
    }
    if(is_water_vapor_transport){
      /*if (is_5g_sim) {
        var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFRAC));
        var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFLUX));
	} else*/ {
        var_types.add_var(SRI_VARIABLE_WATER_VAPOR_MFRAC);
        var_types.add_var(SRI_VARIABLE_WATER_VAPOR_MFLUX);
        var_types.add_var(SRI_VARIABLE_WATER_FILM_THICKNESS);
      }
    }
    /*if (is_Eutectic_Fluid)//MMD_Eutectic
      var_types.add_var(SRI_VARIABLE_LIQUID_FRACTION);*/
  }

  // standard surface development
  if ((mstp->meas_type == CDI_MEASTYPE_DEVELOPMENT_FACE
       || mstp->meas_type == CDI_MEASTYPE_DEVELOPMENT_REGION) 
      && meas_window_type == CDI_SURFACE_WINDOW)
    var_types.add_var(SRI_VARIABLE_AREA);

  // standard porous
  if ((mask & CDI_MEAS_OPT_STANDARD_POROUS) &&
      (meas_window_type == CDI_POROUS_WINDOW)) {
    if (!boolean_is_ht_off_in_powercase &&
        (ht_type == SRI_HT_ACTIVE_SCALAR || ht_type == SRI_HT_PASSIVE_SCALAR))
      var_types.add_var(SRI_VARIABLE_HEAT_GENERATION);
    var_types.add_var(SRI_VARIABLE_FLUID_XFORCE);
    var_types.add_var(SRI_VARIABLE_FLUID_YFORCE);
    if (is_3d)
      var_types.add_var(SRI_VARIABLE_FLUID_ZFORCE);
  }

  // standard porous development
  if (mstp->meas_type == CDI_MEASTYPE_DEVELOPMENT_REGION
      && meas_window_type != CDI_SURFACE_WINDOW) {
    if (is_5g_sim) 
      /**Note: Global volume must be the first variable in 5G fluid development file to support ddp calculation for large-pore. **/
      var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_VOLUME));
    else
      var_types.add_var(SRI_VARIABLE_VOLUME);
  }

  if (is_5g_sim) {
    /**Note: Global volume must be the first variable in 5G fluid composite file to support ddp calculation for large-pore. **/ 
    if (mstp->meas_type == CDI_MEASTYPE_COMPOSITE_REGION
	&& meas_window_type != CDI_SURFACE_WINDOW)
      var_types.add_var(vih.get_5g_var_id(SRI_VARIABLE_VOLUME));
  }

  if ((mask & CDI_MEAS_OPT_HEAT_FLUX) && 
      (meas_window_type == CDI_SURFACE_WINDOW))
    var_types.add_var(SRI_VARIABLE_HEAT_FLUX);

  if ((mask & CDI_MEAS_OPT_TURB) &&
      (meas_window_type != CDI_SURFACE_WINDOW // do not add the following turbulence quantities for a surface measurement window
       || mstp->meas_type == CDI_MEASTYPE_SAMPLED_FACE // unless it is a sampled surface
       || mstp->meas_type == CDI_MEASTYPE_COMPOSITE_SAMPLED_FACE)) {
    var_types.add_var(SRI_VARIABLE_TURB_DISSIPATION);
    var_types.add_var(SRI_VARIABLE_TURB_KINETIC_ENERGY);
    var_types.add_var(SRI_VARIABLE_STRESS_TENSOR_MAG);
    var_types.add_var(SRI_VARIABLE_EDDY_VISCOSITY);
  }
  if ((mask & CDI_MEAS_OPT_TURB) && // do not add the following for any type of surface measurement (even sampled surface)
      meas_window_type != CDI_SURFACE_WINDOW) { // this is because sampling for this variable is not yet supported (as of PHYSICS/1116)
    if (ht_type == SRI_HT_ACTIVE_SCALAR || ht_type == SRI_HT_PASSIVE_SCALAR)
      var_types.add_var(SRI_VARIABLE_TURBULENT_THERMAL_DIFFUSIVITY); 
  }

  if ((mask & CDI_MEAS_OPT_HTC_CHAR_TEMP) &&
      (meas_window_type == CDI_SURFACE_WINDOW))
    var_types.add_var(SRI_VARIABLE_HTC_CHAR_TEMP);

  if ((mask & CDI_MEAS_OPT_HTC_NEAR_WALL_TEMP) &&
      (meas_window_type == CDI_SURFACE_WINDOW))
    var_types.add_var(SRI_VARIABLE_HTC_NEAR_WALL_TEMP);

  if ((mask & CDI_MEAS_OPT_DIV_U) && (meas_window_type != CDI_SURFACE_WINDOW))
    var_types.add_var(SRI_VARIABLE_DIV_U);

  if (mstp->extended_std_var_options.GetOption(cCDI_MEAS_STD_VAR_OPTIONS::STD_RADIATION_VARS) && meas_window_type == CDI_SURFACE_WINDOW) {
    var_types.add_var(SRI_VARIABLE_IRRADIATION);
    var_types.add_var(SRI_VARIABLE_RADIOSITY);
  }

  const cSRI_MEAS_VARS *custom_options = NULL;
  bool fluidOrSampledSurfaceWindow = 
    (meas_window_type == CDI_FLUID_WINDOW ||
     (meas_window_type == CDI_SURFACE_WINDOW && 
      (mstp->meas_type == CDI_MEASTYPE_SAMPLED_FACE || 
       mstp->meas_type == CDI_MEASTYPE_COMPOSITE_SAMPLED_FACE)));

  //
  // Compile the non-particle-tracking custom variables (if any)
  //
  if ((mask & CDI_MEAS_OPT_CUSTOM_FLUID) && 
      fluidOrSampledSurfaceWindow) {
    custom_options = &mstp->custom_fluid_options;
  }
  else if ((mask & CDI_MEAS_OPT_CUSTOM_SURFACE) && 
           (meas_window_type == CDI_SURFACE_WINDOW)) {
    custom_options = &mstp->custom_surface_options;
  }
  else if ((mask & CDI_MEAS_OPT_CUSTOM_POROUS) && 
           (meas_window_type == CDI_POROUS_WINDOW)) {
    custom_options = &mstp->custom_porous_options;
  }
  if (custom_options) {
    ccDOTIMES(i, custom_options->num_vars()) {
      sriINT var = custom_options->var(i);
      
      if (!CDI_VERSION_AT_LEAST(cdi_major_version, cdi_minor_version, 3, 16)) {
        // Previously when the user requested std_dev_pressure in PowerCASE, the simulator
        // computed pressure_sqrd and pressure so that std_dev_pressure could be calculated
        // in SRI via (pressure_sqrd - pressure^2). Now the simulator computes std_dev_pressure
        // directly.
        switch(var) {
          case SRI_VARIABLE_XVEL_SQRD:
            var = SRI_VARIABLE_STD_DEV_XVEL;
            break;
          case SRI_VARIABLE_YVEL_SQRD:
            var = SRI_VARIABLE_STD_DEV_YVEL;
            break;
          case SRI_VARIABLE_ZVEL_SQRD:
            var = SRI_VARIABLE_STD_DEV_ZVEL;
            break;
          case SRI_VARIABLE_VEL_MAG_SQRD:
            var = SRI_VARIABLE_STD_DEV_VEL_MAG;
            break;
          case SRI_VARIABLE_PRESSURE_SQRD:
            var = SRI_VARIABLE_STD_DEV_PRESSURE;
            break;
        }
      }

      var_types.add_var(var);
    }
  }
  
  // Compile the particle-tracking custom variables (if any)
  //
  custom_options = NULL;
  if ((mask & CDI_MEAS_OPT_CUSTOM_FLUID_PARTICLE) && 
           fluidOrSampledSurfaceWindow) {
    custom_options = &mstp->custom_fluid_particle_options;
  }
  else if ((mask & CDI_MEAS_OPT_CUSTOM_SURFACE_PARTICLE) && 
           (meas_window_type == CDI_SURFACE_WINDOW)) {
    custom_options = &mstp->custom_surface_particle_options;
  }
  if (custom_options) {
    ccDOTIMES(var, SRI_N_PREDEFINED_VARIABLE_TYPES) {
      if (custom_options->has_var(var))
        var_types.add_var(var);
    }
  }



  // store only density if both density and pressure custom measurements are
  // selected for 19-state non-active-scalar simulations
  if (STP_LATTICE_D34 != lattice_type && 
      STP_LATTICE_5G != lattice_type &&
      ht_type != SRI_HT_ACTIVE_SCALAR &&
      ((mask & CDI_MEAS_OPT_CUSTOM_FLUID) || 
       (mask & CDI_MEAS_OPT_CUSTOM_SURFACE) ||
       (mask & CDI_MEAS_OPT_CUSTOM_POROUS)) &&
      (var_types.has_var(SRI_VARIABLE_DENSITY) && 
       var_types.has_var(SRI_VARIABLE_PRESSURE)) &&
       boolean_remove_var) {
    var_types.remove_var(SRI_VARIABLE_PRESSURE);
  }

  // for 19-state isothermal simulations remove temperature from list of
  // variables, and for incompressible simulations, remove density from the
  // list of variables
  if (STP_LATTICE_D34 != lattice_type && ht_type == SRI_HT_ISOTHERMAL) {
    if (var_types.has_var(SRI_VARIABLE_TEMP) && boolean_remove_var) {
      var_types.remove_var(SRI_VARIABLE_TEMP);
    }
    // For surface files in isothermal cases, near wall temp is recorded in TEMP 
    // (not NEAR_WALL_TEMP). This is terribly confusing, but history is sometimes ugly.
    if (var_types.has_var(SRI_VARIABLE_NEAR_WALL_TEMP) && boolean_remove_var) {
      var_types.remove_var(SRI_VARIABLE_NEAR_WALL_TEMP);
    }
  }

  if (is_density_constant) 
    // explicitly compute pressure instead of deriving it from density and temperature
    var_types.replace_var_if_present(SRI_VARIABLE_DENSITY, SRI_VARIABLE_PRESSURE);

  // UDS vars for standard surface
  if ((mask & CDI_MEAS_OPT_STANDARD_SURFACE) && (meas_window_type == CDI_SURFACE_WINDOW)) {
    ccDOTIMES(i, vih.get_num_udscalars()) {
      var_types.add_var(vih.get_uds_var_id(SRI_VARIABLE_UDS_SCALAR_OFFSET, i));
      var_types.add_var(vih.get_uds_var_id(SRI_VARIABLE_UDS_SCALAR_FLUX_OFFSET, i));
    }
  }

  // UDS vars for standard fluid/porous
  if (((mask & CDI_MEAS_OPT_STANDARD_FLUID) && (meas_window_type == CDI_FLUID_WINDOW)) || 
    ((mask & CDI_MEAS_OPT_STANDARD_POROUS) && (meas_window_type == CDI_POROUS_WINDOW))
    ) {
    ccDOTIMES(i, vih.get_num_udscalars()) {
      var_types.add_var(vih.get_uds_var_id(SRI_VARIABLE_UDS_SCALAR_OFFSET, i));
      var_types.add_var(vih.get_uds_var_id(SRI_VARIABLE_UDS_SOURCE_TERM_OFFSET, i));
    }

    // sampled faces do not support measuring source term
    if (mstp->meas_type == CDI_MEASTYPE_SAMPLED_FACE) {
      ccDOTIMES(i, vih.get_num_udscalars()) {
        var_types.remove_var(vih.get_uds_var_id(SRI_VARIABLE_UDS_SOURCE_TERM_OFFSET, i));
      }
    }
  }

  return var_types;
}

//
// **************************************************************************
//                              cSRI_MEAS_VARS
// **************************************************************************
cSRI_MEAS_VARS::
cSRI_MEAS_VARS()
{
  ccDOTIMES(i, NUM_MASK_WORDS)
    m_bitmasksObsolete[i] = 0;
}

void
cSRI_MEAS_VARS::
add_var(sriINT var)
{
  if (this->has_var(var))
    return;
  m_vars.push_back(var);
}

void
cSRI_MEAS_VARS::
remove_var(sriINT var)
{
  for(std::vector<sriINT>::iterator it = m_vars.begin();
      it !=  m_vars.end(); it++) {
    if (*it == var) {
      m_vars.erase(it);
      break;
    }
  }
}

sriBOOL
cSRI_MEAS_VARS::
has_var(sriINT var) const
{
  return (std::find(m_vars.begin(), m_vars.end(), var) != m_vars.end());
}

void
cSRI_MEAS_VARS::
replace_var_if_present(sriINT old_var,
                       sriINT new_var)
{
  if (this->has_var(old_var)) {
    this->remove_var(old_var);
    this->add_var(new_var);
  }
}


sriINT
cSRI_MEAS_VARS::
num_vars() const
{
  return m_vars.size();
}
  
sriINT *
cSRI_MEAS_VARS::
get_vars(sriINT &n_vars) const
{
  n_vars = num_vars();

  sriINT *vars = sri_new_vector(sriINT, n_vars);
  ccDOTIMES(nth_var,n_vars) 
    vars[nth_var] = m_vars[nth_var];
  
  return vars;
}

// Converts meas vars stored in bitmasks to an STL vector
void 
cSRI_MEAS_VARS::convert_bit_mask_to_vars()
{
  int current_var = 0;
  ccDOTIMES(mask_index, NUM_MASK_WORDS) {
    ccDOTIMES(bit_index, NUM_BITS_PER_MASK_WORD) {
      if ((m_bitmasksObsolete[mask_index] & (1 << bit_index)) != 0) {
        this->add_var(current_var);
      }
      current_var++;
    }
  }
}

void
cSRI_MEAS_VARS::
convert_vars_to_bit_mask()
{
   ccDOTIMES(i, NUM_MASK_WORDS)
     m_bitmasksObsolete[i] = 0;
  ccDOTIMES(var_index, m_vars.size()) {
    const unsigned int mask_index = m_vars[var_index] / NUM_BITS_PER_MASK_WORD;
    const unsigned int bit_index = m_vars[var_index] % NUM_BITS_PER_MASK_WORD;
    if (mask_index < NUM_MASK_WORDS)
      m_bitmasksObsolete[mask_index] |= (1 << bit_index);
  }
}



sriINT
cSRI_MEAS_VARS::var(sriINT nth_var) const
{
  if (nth_var < 0 || nth_var > num_vars())
    return SRI_VARIABLE_INVALID;
  else
    return m_vars[nth_var];
}

void
cSRI_MEAS_VARS::
sort_vars()
{
  if (num_vars() > 0)
    std :: sort (m_vars.begin(), m_vars.end());
}

int cdi_meas_vars_num_bitmasks(cdiINT32 major_version,  cdiINT32 minor_version)
{
  if (CDI_VERSION_AT_LEAST(major_version, minor_version, 4, 1) ||
      (CDI_MAJOR_VERSION == 3 && CDI_MINOR_VERSION == 24))
    return 0;
  else if (CDI_VERSION_AT_LEAST(major_version, minor_version, 3, 9))
    return 4;
  else if (CDI_VERSION_AT_LEAST(major_version, minor_version, 3, 8))
    return 2;
  else
    return 0;
}

int cdi_meas_vars_num_bitmasks(CDI_INFO info)
{
  return cdi_meas_vars_num_bitmasks(info->major_version, info->minor_version);
}

namespace eCDI_MNTR_ANALYSIS_SCHEME
{
Enum GetOrdinal(const std::string& name)
{
    for (int i=0; i < NumAnalysisSchemes; i++) {
      if (GetName((Enum)i) == name) {
        return (Enum)i;
      }
    }
    
    // Handle old names for backwards compatibility
    
    if (name == "Auto Steady Drag") {
      return StdAeroDrag;
    }
    
    if (name == "Auto Steady Lift") {
      return StdAeroLift;
    }
    
    return Invalid;
}
};


//
// **************************************************************************
//                              cCDI_USER_DEFINED_VARS
// **************************************************************************

/*bool cCDI_USER_DEFINED_VARS::SetIndexOffset(int newOffset)
{
  // no negative offsets
  assert(newOffset >= 0);

  m_indexOffset = newOffset;
  
  // update startOffset to reflect the change-
  m_startOffset += m_indexOffset;
  return true;
}
*/

int cCDI_USER_DEFINED_VARS::GetScalarValueIndex(int indexOfScalar, int indexOfScalarValue) const
{
  assert(indexOfScalar >= 0 && indexOfScalar < GetNumberOfUserDefinedScalars());
  assert(indexOfScalarValue >= 0 && indexOfScalarValue < GetNumberOfVariables());

  return (GetFirstOffset() + (indexOfScalar * GetNumberOfVariables()) + indexOfScalarValue);
}

bool cCDI_USER_DEFINED_VARS::GetScalarTypeAndIndex(int scalarValueIndex, int* scalarIndex, CDI_VAR_ID_UDS_OFFSET* scalarValueType)
{
  int scalarValueTypeInt = ((scalarValueIndex - GetFirstOffset()) % (GetNumberOfVariables()));
  *scalarIndex = (scalarValueIndex - (GetFirstOffset() + scalarValueTypeInt)) / GetNumberOfVariables();
  *scalarValueType = ((CDI_VAR_ID_UDS_OFFSET)scalarValueTypeInt);
  if ((*scalarValueType) <= CDI_VAR_ID_UDS_INVALID || (*scalarValueType) >= CDI_VAR_ID_UDS_MAX_VARS || (*scalarIndex) < 0 || (*scalarIndex) >= GetNumberOfUserDefinedScalars()) {
    *scalarIndex = -1;
    *scalarValueType = CDI_VAR_ID_UDS_INVALID;
    return false;
  }

  return true;
}

/****************************************************************\//kEps_SuperCycle
|
| Function name:cdi_write_kesc
|
| Purpose:writes kesc (k-e super cycle)
|
\****************************************************************/
VOID
cdi_write_kesc(
  CDI_INFO cdi_info, /* the info structure */
   const sCDI_KESC& kesc /* the kesc structure */
)
{
  cdi_push(cdi_info, kesc.cccc);

  cdi_write_bool(cdi_info, kesc.bKesc_activated);
  
  if (kesc.bKesc_activated)
    cdi_write_asINT32(cdi_info, &kesc.super_cycling_factor, 1);

  cdi_pop(cdi_info);
}

/****************************************************************\//kEps_SuperCycle

|
| Function name:cdi_read_kesc
|
| Purpose:reads kesc
|
\****************************************************************/
sCDI_KESC cdi_read_kesc(CDI_INFO cdi_info)
{
  sCDI_KESC kesc;

  cdiINT32 index = 0;
  cdi_inner_chunk_read_bool(index, "kesc", cdi_info, &kesc.bKesc_activated);
  if(kesc.bKesc_activated)
    cdi_read_asINT32(cdi_info, &kesc.super_cycling_factor, 1);

  return kesc;
}

cCDI_RADIATION_PATCHES::~cCDI_RADIATION_PATCHES()
{
  for (cCDI_RADIATION_PATCH_BASE* patch : m_patches) {
    delete patch;
  }
  m_patches.clear();
}

void cCDI_RADIATION_PATCHES::WriteToCDI(CDI_INFO cdi_info) const
{
  for (cCDI_RADIATION_PATCH_BASE* patch : m_patches)
  {
    WITH_CDI_CHUNK(cdi_info, patch->GetPatchChunkType()) {
      patch->WriteToCDI(cdi_info);
    }
  }
}

void cCDI_RADIATION_PATCHES::ReadFromCDI(CDI_INFO cdi_info)
{
  // This should be called when the "RDPS" chunk is hit
  asINT32 count = cio_get_count(cdi_info->cio_info);

  ccDOTIMES(i, count) {
    cio_descend(cdi_info->cio_info);
    {
      cdiINT32 index = 0;
      CIO_CCCC type = cio_get_type(cdi_info->cio_info);
      if (type == cCDI_STD_RADIATION_PATCH::GetChunkType()) {
        cCDI_STD_RADIATION_PATCH* strp = new cCDI_STD_RADIATION_PATCH();
        strp->ReadFromCDI(cdi_info, &index);
        AddPatch(strp);
      }
      else if (type == cCDI_AXISYM_RADIATION_PATCH::GetChunkType()) {
        cCDI_AXISYM_RADIATION_PATCH* axrp = new cCDI_AXISYM_RADIATION_PATCH();
        axrp->ReadFromCDI(cdi_info, &index);
        AddPatch(axrp);
      }
    }
    cio_ascend(cdi_info->cio_info);
  }
}

void cCDI_RADIATION_PATCHES::BuildPatchTable(cCDI_PARTITIONS& partitions)
{
  // This should be called be clients after reading the CDI so that GetRadiationPatchForFace work

  // The patch list is (should be) written to the CDI in hierarchy order. Maybe we should sort to be safe?
  // Go through the patch list in reverse so that any overloads are done first
  // Can skip the root (index = 0) because we do not include that in the map for efficiency
  for (size_t iPatch = m_patches.size() - 1; iPatch > 0; --iPatch) {
    cCDI_RADIATION_PATCH_BASE* patch = m_patches[iPatch];

    // Get all the faces for this patch
    std::vector<cdiINT32> faces = patch->face_geom.ExpandSelection(partitions, cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Face, true);
    // Add each face to the map only if it isn't already there
    for (cdiINT32 faceIndex : faces) {
      auto itMap1 = m_patchFaceMap.find(faceIndex);
      if (itMap1 == m_patchFaceMap.end()) {
        m_patchFaceMap[faceIndex] = iPatch;
      }
    }
  }
}

cCDI_RADIATION_PATCH_BASE* cCDI_RADIATION_PATCHES::GetRadiationPatchForFace(cdiINT32 faceIndex) const
{
  cdiINT32 iPatch = 0; // If not in the map, then it uses the "root" patch
  auto itMap1 = m_patchFaceMap.find(faceIndex);
  if (itMap1 != m_patchFaceMap.end()) {
    iPatch = itMap1->second;
  }
  return (iPatch >= 0 && iPatch < m_patches.size()) ? m_patches[iPatch] : nullptr;
}

void cCDI_RADIATION_PATCH_BASE::WriteToCDI(CDI_INFO cdi_info) const
{
  WITH_CDI_CHUNK(cdi_info, face_geom.GetChunkType()) {
    face_geom.WriteToCDI(cdi_info);
  }
  cdi_write_parm(cdi_info, &angle_tolerance);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
    cdi_write_parm(cdi_info, &patch_size);
  }
}

void cCDI_RADIATION_PATCH_BASE::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  char chunkName[10];
  cio_type_to_string(GetPatchChunkType(), chunkName);

  CDI_WITH_INNER_CHUNK(cdi_info) {
    if (cdi_get_type(cdi_info) == face_geom.GetChunkType()) {
      face_geom.ReadFromCDI(cdi_info);
    }
  }
  cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &angle_tolerance);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &patch_size);
  }
}

void cCDI_STD_RADIATION_PATCH::WriteToCDI(CDI_INFO cdi_info) const
{
  cCDI_RADIATION_PATCH_BASE::WriteToCDI(cdi_info);

  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
    cdi_write_parm(cdi_info, &patch_size);
  }
}

void cCDI_STD_RADIATION_PATCH::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  char chunkName[10];
  cio_type_to_string(GetPatchChunkType(), chunkName);

  cCDI_RADIATION_PATCH_BASE::ReadFromCDI(cdi_info, pIndex);

  if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 9, 11)) {
    cdi_inner_chunk_read_parm(*pIndex, chunkName, cdi_info, &patch_size);
  }
}

void cCDI_AXISYM_RADIATION_PATCH::WriteToCDI(CDI_INFO cdi_info) const
{
  cCDI_RADIATION_PATCH_BASE::WriteToCDI(cdi_info);

  cdi_write_int_(cdi_info, axis_preferred_csys_index);
  cdi_write_dbls(cdi_info, (idFLOAT*)axis_origin, 3);
  cdi_write_dbls(cdi_info, (idFLOAT*)axis_dir, 3);
}

void cCDI_AXISYM_RADIATION_PATCH::ReadFromCDI(CDI_INFO cdi_info, cdiINT32* pIndex)
{
  char chunkName[10];
  cio_type_to_string(GetPatchChunkType(), chunkName);

  cCDI_RADIATION_PATCH_BASE::ReadFromCDI(cdi_info, pIndex);

  cdi_inner_chunk_read_int_(*pIndex, chunkName, cdi_info, &axis_preferred_csys_index);
  cdi_inner_chunk_read_dbls(*pIndex, chunkName, cdi_info, (idFLOAT*)axis_origin);
  cdi_inner_chunk_read_dbls(*pIndex, chunkName, cdi_info, (idFLOAT*)axis_dir);
}

void cCDI_UDDC::WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_write_strg(cdi_info, m_name);
  cdi_write_strg(cdi_info, m_unitsClass);
  cdi_write_strg(cdi_info, m_units);

  cdiINT32 size = m_data.size();
  cdi_write_int_(cdi_info, size);

  if (!m_data.empty())
  {
    cdi_write_dbls(cdi_info, const_cast<double*>(&m_data[0]), size);
  }
}

void cCDI_UDDC::ReadFromCDI(CDI_INFO cdi_info)
{
  cdiINT32 index = 0;
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);
  cdi_inner_chunk_read_strg(index, chunkName, cdi_info, &m_name);
  cdi_inner_chunk_read_strg(index, chunkName, cdi_info, &m_unitsClass);
  cdi_inner_chunk_read_strg(index, chunkName, cdi_info, &m_units);

  cdiINT32 size = 0;
  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &size);
  if (size > 0) {
    double* dataArr = EXA_MALLOC_ARRAY(double, size);
    cdi_inner_chunk_read_dbls(index, chunkName, cdi_info, dataArr);
    m_data.clear();
    for (int i = 0; i < size; i++) {
      m_data.push_back(dataArr[i]);
    }
    exa_free(dataArr);
  }
}

void cCDI_UDDC::SetData(const std::vector<double>* data)
{
  if (data) {
    m_data.clear();
    UNITS_STATUS convert_status;
    for (auto datum : *data) {
      m_data.push_back(datum);
    }
  }
}

void cCDI_UDDS::WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_write_strg(cdi_info, m_name);

  m_xData.WriteToCDI(cdi_info);

  cdiINT32 size = m_yData.size();
  cdi_write_int_(cdi_info, size);
  for (auto y : m_yData) {
    y.WriteToCDI(cdi_info);
  }
}

void cCDI_UDDS::ReadFromCDI(CDI_INFO cdi_info)
{
  cdiINT32 index = 0;
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);
  cdi_inner_chunk_read_strg(index, chunkName, cdi_info, &m_name);

  m_xData.ReadFromCDI(cdi_info);

  cdiINT32 size = 0;
  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &size);

  for (int i = 0; i < size; i++) {
    // Append each individual column
    cCDI_UDDC y;
    y.ReadFromCDI(cdi_info);
    m_yData.push_back(y);
  }
}

void cCDI_UDDS::SetYData(std::vector<cCDI_UDDC>* yData)
{
  if (yData) {
    m_yData.clear();
    for (auto datum : *yData) {
      m_yData.push_back(datum);
    }
  }
}

eCDI_UDD_STATUS cCDI_UDDS::GetXY(std::string name, cCDI_UDDC* xCol, cCDI_UDDC* yCol)
{
  for (auto datum : m_yData) {
    if (name == m_name + "." + datum.GetName() || name == datum.GetName()) {
      // Found it!
      *xCol = m_xData;
      *yCol = datum;
      return eCDI_UDD_STATUS::CURVE_STATUS_OK;
    }
  }

  return eCDI_UDD_STATUS::COLUMN_NOT_FOUND;
}

eCDI_UDD_STATUS cCDI_UDDS::GetColumn(std::string name, cCDI_UDDC* col)
{
  if (name == m_name + "." + m_xData.GetName() || name == m_xData.GetName()) {
    // They wanted the x column, which is their right
    *col = m_xData;
    return eCDI_UDD_STATUS::CURVE_STATUS_OK;
  }

  for (auto datum : m_yData) {
    if (name == m_name + "." + datum.GetName() || name == datum.GetName()) {
      // Found the y column
      *col = datum;
      return eCDI_UDD_STATUS::CURVE_STATUS_OK;
    }
  }

  return eCDI_UDD_STATUS::COLUMN_NOT_FOUND;
}

eCDI_UDD_STATUS cCDI_UDDS::GetYValueAt(std::string yCurveName, double xVal, double& yVal)
{
  for (auto y_col : m_yData) {
    if (yCurveName == m_name + "." + y_col.GetName() || yCurveName == y_col.GetName()) {
      // Found the y column
      yVal = cCDI_UDDS::GetYValueAt(xVal, &m_xData, &y_col);
      return eCDI_UDD_STATUS::CURVE_STATUS_OK;
    }
  }

  return eCDI_UDD_STATUS::COLUMN_NOT_FOUND;
}

eCDI_UDD_STATUS cCDI_UDDS::GetXCol(cCDI_UDDC* xCol)
{
  *xCol = m_xData;
  return eCDI_UDD_STATUS::CURVE_STATUS_OK;
}

eCDI_UDD_STATUS cCDI_UDDS::GetYCols(std::vector<cCDI_UDDC>* yCols)
{
  *yCols = m_yData;
  return eCDI_UDD_STATUS::CURVE_STATUS_OK;
}

double cCDI_UDDS::GetYValueAt(double xVal, const cCDI_UDDC* xCol, const cCDI_UDDC* yCol)
{
  return cCDI_UDDS::GetYValueAt(xVal, xCol->GetData(), yCol->GetData());
}

double cCDI_UDDS::GetYValueAt(double xVal, const std::vector<double> &xCol, const std::vector<double> &yCol)
{
  double yVal = 0;

  if (xCol.size() == yCol.size() && xCol.size() > 0) {
    // They should always be equal sizes - if not punt and call it 0

    if (xVal <= xCol[0]) {
      // X is below the lowest value, return the first Y
      yVal = yCol[0];
    }
    else if (xVal >= xCol[xCol.size() - 1]) {
      // X is greater than the greatest value, return the last Y
      yVal = yCol[yCol.size() - 1];
    }
    else {
      // If we're here, then interpolate the given x value
      double x_low = xCol[0], y_low = yCol[0];
      double x_high = xCol[xCol.size() - 1], y_high = yCol[yCol.size() - 1];

      for (int i = 0; i < xCol.size() - 1; i++) {
        if (xVal >= xCol[i] && xVal < xCol[i + 1]) {
          // Found it!
          x_low = xCol[i];
          x_high = xCol[i + 1];

          y_low = yCol[i];
          y_high = yCol[i + 1];
          break;
        }
      }

      if (x_high != x_low) {
        // Do a simple linear interpolation
        yVal = y_low + (xVal - x_low) * (y_high - y_low)/(x_high - x_low);
      }
      else {
        // If we get here the data in the arrays are dumb
        yVal = x_high;
      }
    }
  }

  return yVal;
}

void cCDI_UDDL::WriteToCDI(CDI_INFO cdi_info) const
{
  cdi_push(cdi_info, GetChunkType());
  cdiINT32 size = m_userDataCurves.size();
  cdi_write_int_(cdi_info, size);

  for (auto curve : m_userDataCurves) {
    curve.WriteToCDI(cdi_info);
  }

  cdi_pop(cdi_info);
}

void cCDI_UDDL::ReadFromCDI(CDI_INFO cdi_info)
{
  cdiINT32 size = 0;
  cdiINT32 index = 0;
  char chunkName[10];
  cio_type_to_string(GetChunkType(), chunkName);
  cdi_inner_chunk_read_int_(index, chunkName, cdi_info, &size);
  for (int i = 0; i < size; i++) {
    // Append each individual set of data curves
    cCDI_UDDS curve;
    m_userDataCurves.push_back(curve);
    m_userDataCurves[i].ReadFromCDI(cdi_info);
  }
}

eCDI_UDD_STATUS cCDI_UDDL::GetXY(std::string name, cCDI_UDDC* xCol, cCDI_UDDC* yCol)
{
  for (auto set : m_userDataCurves) {
    if (set.GetXY(name, xCol, yCol) == eCDI_UDD_STATUS::CURVE_STATUS_OK) {
      return eCDI_UDD_STATUS::CURVE_STATUS_OK;
    }
  }

  return eCDI_UDD_STATUS::CURVE_NOT_FOUND;
}

eCDI_UDD_STATUS cCDI_UDDL::GetAllXY(std::string name, cCDI_UDDC* xCol, std::vector<cCDI_UDDC>* yCols)
{
  for (auto set : m_userDataCurves) {
    if (set.GetName() == name) {
      set.GetXCol(xCol);
      set.GetYCols(yCols);
      return eCDI_UDD_STATUS::CURVE_STATUS_OK;
    }
  }

  return eCDI_UDD_STATUS::CURVE_NOT_FOUND;
}

eCDI_UDD_STATUS cCDI_UDDL::GetYValueAt(std::string yCurveName, double xVal, double& yVal)
{
  for (auto set : m_userDataCurves) {
    if (set.GetYValueAt(yCurveName, xVal, yVal) == eCDI_UDD_STATUS::CURVE_STATUS_OK) {
      return eCDI_UDD_STATUS::CURVE_STATUS_OK;
    }
  }

  return eCDI_UDD_STATUS::CURVE_NOT_FOUND;
}
