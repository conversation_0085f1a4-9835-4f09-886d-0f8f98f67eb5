/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#define _USE_MATH_DEFINES
#include <cmath> //included exclusively for the M_PI constant used in the 
                 //terminal velocity calculation at the bottom

#include "cdi_common.h"

/*** NOTE:  For the moment, we've got an ugly hack here in the handling of
**** initial conditions for fluid physics types (CDI_PHYS_TYPE_FV_FLUID,
**** CDI_PHYS_TYPE_VV_FLUID, CDI_PHYS_TYPE_LES_VV_FLUID) and the dynamical
**** parameters for CDI_PHYS_TYPE_SOURCE.  We want the user to specify these
**** conditions in terms of pressure, velocity, and temperature.  For the moment,
**** the CDI and LGI interfaces are in terms of mass, momentum, and energy.
**** We need the cdi_physics_table to be in terms of pvt, so that
**** .expr files can work in terms of these variables.
****
**** So we do the following.  When we read the CDI file, we read mme, and
**** enter those values into the region descriptors.  Afterwards, we change
**** the values into pvt form; it's as if we read pvt from the CDI file.
**** When we write the LGI file, we translate back into mme after querying
**** the cgdb/svbc modules for pvt.
*/

enum CDI_PHYSICS_VARS_INDEX
{
    CPVI_ACCEL_X,
    CPVI_ACCEL_Y,
    CPVI_ACCEL_Z,
    CPVI_ANG_VEL,
    CPVI_ANG_VEL_X,
    CPVI_ANG_VEL_Y,
    CPVI_ANG_VEL_Z,
    CPVI_BC_TYPE,
    CPVI_COMP0_DENSITY,
    CPVI_COMP0_FRICTION_FACTOR,
    CPVI_COMP0_MASS_FLUX,
    CPVI_COMP0_NU_ON_T,
    CPVI_COMP0_VOL_FRAC,
    CPVI_COMP0_WETTING_DENS,
    CPVI_COMP1_DENSITY,
    CPVI_COMP1_FRICTION_FACTOR,
    CPVI_COMP1_MASS_FLUX,
    CPVI_COMP1_NU_ON_T,
    CPVI_COMP1_VOL_FRAC,
    CPVI_COMP1_WETTING_DENS,
    CPVI_COMP_RATIO_TYPE,
    CPVI_COORD_SYSTEM,
    CPVI_CURVED_POROUS_MEDIUM,
    CPVI_CYL_BEGIN_X,
    CPVI_CYL_BEGIN_Y,
    CPVI_CYL_BEGIN_Z,
    CPVI_CYL_END_X,
    CPVI_CYL_END_Y,
    CPVI_CYL_END_Z,
    CPVI_DENSITY_T0,
    CPVI_DIST_TO_REFL_SURFACE,
    CPVI_DYN_HEAT_RATE_COEFF0,
    CPVI_DYN_HEAT_RATE_COEFF1,
    CPVI_DYN_HEAT_RATE_COEFF2,
    CPVI_ENERGY,
    CPVI_FAN_HUB_RADIUS,
    CPVI_FAN_LENGTH,
    CPVI_FAN_RADIUS,
    CPVI_GAXIS_X,
    CPVI_GAXIS_Y,
    CPVI_GAXIS_Z,
    CPVI_MASS,
    CPVI_MASS_FLUX_X,
    CPVI_MASS_FLUX_Y,
    CPVI_MASS_FLUX_Z,
    CPVI_MRF_TYPE,
    CPVI_NEW_MASS_FLUX_X,
    CPVI_NEW_MASS_FLUX_Y,
    CPVI_NEW_MASS_FLUX_Z,
    CPVI_NEW_PRESSURE,
    CPVI_NU_ON_T,
    CPVI_POINT_X,
    CPVI_POINT_Y,
    CPVI_POINT_Z,
    CPVI_POROUS_HEAT_COEF,
    CPVI_POROUS_Q0,
    CPVI_POROUS_REF_TEMP,
    CPVI_PRESSURE,
    CPVI_RAXIS_X,
    CPVI_RAXIS_Y,
    CPVI_RAXIS_Z,
    CPVI_REF_FRAME,
    CPVI_REF_TEMP,
    CPVI_RES0_B,
    CPVI_RES0_G,
    CPVI_RES0_R,
    CPVI_RES1_B,
    CPVI_RES1_G,
    CPVI_RES1_R,
    CPVI_RESISTANCE_REF_TEMP,
    CPVI_RESPONSE_TIME,
    CPVI_R_FORCE_COEFF0,
    CPVI_R_FORCE_COEFF1,
    CPVI_R_FORCE_COEFF2,
    CPVI_R_FORCE_COEFF3,
    CPVI_SLIP_FAC,
    CPVI_STATE_OF_MATTER,
    CPVI_SURFACE_CHAR_LEN,
    CPVI_SURFACE_ROUGHNESS,
    CPVI_TANG_VEL_COEFF0,
    CPVI_TANG_VEL_MODEL,
    CPVI_TANG_VEL_THRUST_COEFF,
    CPVI_TEMP,
    CPVI_THERMAL_EXP_COEFF,
    CPVI_TURB_DISSIPATION,
    CPVI_TURB_INTENSITY,
    CPVI_TURB_KE,
    CPVI_TURB_LENGTH_SCALE,
    CPVI_TURB_VIA,
    CPVI_UNIT_VEC_X,
    CPVI_UNIT_VEC_Y,
    CPVI_UNIT_VEC_Z,
    CPVI_USE_DYN_HEAT_RATE,
    CPVI_U_THETA_COEFF0,
    CPVI_U_THETA_COEFF1,
    CPVI_U_THETA_COEFF2,
    CPVI_U_THETA_COEFF3,
    CPVI_VEL_X,
    CPVI_VEL_X_T0,
    CPVI_VEL_Y,
    CPVI_VEL_Y_T0,
    CPVI_VEL_Z,
    CPVI_VEL_Z_T0,
    CPVI_WALL_HEAT_FLUX,
    CPVI_X_MOM,
    CPVI_Y_MOM,
    CPVI_Z_MOM,
    CPVI_WALL_THICKNESS,
    CPVI_WALL_CONDUCTIVITY,
    CPVI_EXT_HEAT_XFER_COEFF,
    CPVI_APM_DENSITY,    
    CPVI_APM_POROSITY,  
    CPVI_APM_THICKNESS, 
    CPVI_FLUID_DESC_INDEX,
    CPVI_SURFACE_MATERIAL,
    CPVI_CURVED_HX_TABLE_INDEX,
    CPVI_CURVED_HX_MEAS_WINDOW_INDEX,
    CPVI_VEL_SLIP_TYPE,
    CPVI_APM_TORTUOSITY,
    CPVI_BOUNDARY_LAYER_TYPE,
    CPVI_FAR_FIELD_BC,
    CPVI_CONDENSABLE_SURFACE,
    CPVI_FILM_THICKNESS,
    CPVI_WATER_VAPOR_SOURCE_TERM,
    CPVI_WATER_MASS_FRACTION,
    CPVI_RELATIVE_HUMIDITY,
    CPVI_DEFORMING_TIRE,
    CPVI_AVERAGE_MEA_ACROSS,
    CPVI_VELOCITY_VIA_TORQUE,
    CPVI_RESISTIVE_TORQUE,
    CPVI_EXTERNAL_TORQUE,
    CPVI_START_TIME,
    CPVI_END_TIME,
    CPVI_TIME_ACCURATE,
    CPVI_MOMENT_OF_INERTIA,
    CPVI_MEAS_WINDOW_INDEX,
    CPVI_INITIAL_ANG_VEL,
    CPVI_TURB_INTENSITY_X,
    CPVI_TURB_INTENSITY_Y,
    CPVI_TURB_INTENSITY_Z,
    CPVI_TURB_LEN_SCALE_X,
    CPVI_TURB_LEN_SCALE_Y,
    CPVI_TURB_LEN_SCALE_Z,
    CPVI_TURB_FREQ_MIN,
    CPVI_TURB_FREQ_MAX,
    CPVI_TURB_DELTA_T,
    CPVI_TURB_TABULAR_DATA_ID,
    CPVI_MASS_FLOW_RATE,
    CPVI_USE_REFL_DAMPING,
    CPVI_APM_USE_ADV_FLUID,
    CPVI_APM_VISCOUS_RES,
    CPVI_APM_INERTIAL_RES,
    CPVI_APM_REACTANCE_COEFF,
    CPVI_APM_SURFACE_POROSITY,
    CPVI_INLET_AREA,
    CPVI_MEAS_FRAME_NUM,
    CPVI_IS_TOTAL_TEMP,
    CPVI_IS_SUPERSONIC_INLET,
    CPVI_SS_INLET_PRESSURE,
    CPVI_REVERSE_MASS_FLOW,
    CPVI_ROTATION_VIA_REF_FRAME,
    CPVI_CONTAIN_TRANSONIC_FLOW,
    CPVI_VAR_ID_SURFACE_ACOUSTIC_ABSORB,
    CVPI_VEL_N,
    CVPI_IS_VEL_N,
    CPVI_MEAS_START_FRAME_NUM,
    CPVI_IMPORT_FLUCTUATIONS,
    CPVI_PM_LEAKAGE_RESOLUTION,
    CPVI_SOLID_MATERIAL,
    CPVI_SHELL_CONFIGURATION,
    CPVI_SOLID_IMPOSED_HEAT,
    CPVI_CONTACT_RESISTANCE,
    CPVI_HEAT_FLUX_1,
    CPVI_HEAT_FLUX_2,
    CPVI_SHELL_LAYER_THICKNESS,
    CPVI_SOLID_COMPUTE_RADIATION,
    CPVI_RADIATION_SURFACE_CONDITION,
    CPVI_ANISOTROPY_USE_PART_AXIS,
    CPVI_MIN_DESIGN_TEMP,
    CPVI_MAX_DESIGN_TEMP,
    CPVI_RADIATION_TEMP,
    CPVI_VISCOUS_DAMPING_FACTOR,
    CPVI_SPECIFY_HEAT_VIA_POWER_DENSITY,
    CPVI_SOLID_IMPOSED_HEAT_IN_POWER,
    CPVI_WALL_HEAT_FLOW_IN,
    CPVI_WALL_HEAT_FLOW_OUT,
    CPVI_SPECIAL_LAYER,
    CPVI_LAYER_CONTACT_AREA,
    CPVI_MAX
};

static const sCDI_PHYS_TYPE_DESCRIPTOR::sDP_VAR s_cdi_physics_vars[CPVI_MAX] =
{
    {CDI_VAR_ID_ACCEL_X,            "ref point accel X",                "Acceleration"},
    {CDI_VAR_ID_ACCEL_Y,            "ref point accel Y",                "Acceleration"},
    {CDI_VAR_ID_ACCEL_Z,            "ref point accel Z",                "Acceleration"},
    {CDI_VAR_ID_ANG_VEL,            "angular velocity",                 "AngularVelocity"},
    {CDI_VAR_ID_ANG_VEL_X,          "angular vel X",                    "AngularVelocity"},
    {CDI_VAR_ID_ANG_VEL_Y,          "angular vel Y",                    "AngularVelocity"},
    {CDI_VAR_ID_ANG_VEL_Z,          "angular vel Z",                    "AngularVelocity"},
    {CDI_VAR_ID_BC_TYPE,            "boundary condition type",          "Dimensionless"},
    {CDI_VAR_ID_COMP0_DENSITY,      "component 0 density",              "Density"},
    {CDI_VAR_ID_COMP0_FRICTION_FACTOR, "component 0 friction factor",   "Dimensionless"},
    {CDI_VAR_ID_COMP0_MASS_FLUX,    "component 0 mass flux",            "MassFlux"},
    {CDI_VAR_ID_COMP0_NU_ON_T,      "component 0 viscosity/temperature","Dimensionless"},
    {CDI_VAR_ID_COMP0_VOL_FRAC,     "volumetric component 0 percentage","Dimensionless"},
    {CDI_VAR_ID_COMP0_WETTING_DENS, "component 0 wetting density",      "Dimensionless"},
    {CDI_VAR_ID_COMP1_DENSITY,      "component 1 density",              "Density"},
    {CDI_VAR_ID_COMP1_FRICTION_FACTOR, "component 1 friction factor",   "Dimensionless"},
    {CDI_VAR_ID_COMP1_MASS_FLUX,    "component 1 mass flux",            "MassFlux"},
    {CDI_VAR_ID_COMP1_NU_ON_T,      "component 1 viscosity/temperature","Dimensionless"},
    {CDI_VAR_ID_COMP1_VOL_FRAC,     "volumetric component 1 percentage","Dimensionless"},
    {CDI_VAR_ID_COMP1_WETTING_DENS, "component 1 wetting density",      "Dimensionless"},
    {CDI_VAR_ID_COMP_RATIO_TYPE,    "component ratio type",             "Dimensionless"},
    {CDI_VAR_ID_COORD_SYSTEM,       "coord system",                     "Dimensionless"},
    {CDI_VAR_ID_CURVED_POROUS_MEDIUM, "is curved type",                 "Dimensionless"},
    {CDI_VAR_ID_CYL_BEGIN_X,        "axis tail X",                      "Length"},
    {CDI_VAR_ID_CYL_BEGIN_Y,        "axis tail Y",                      "Length"},
    {CDI_VAR_ID_CYL_BEGIN_Z,        "axis tail Z",                      "Length"},
    {CDI_VAR_ID_CYL_END_X,          "axis end X",                       "Length"},
    {CDI_VAR_ID_CYL_END_Y,          "axis end Y",                       "Length"},
    {CDI_VAR_ID_CYL_END_Z,          "axis end Z",                       "Length"},
    {CDI_VAR_ID_DENSITY_T0,         "density (t=0)",                    "Density"},
    {CDI_VAR_ID_DIST_TO_REFL_SURFACE, "distance to reflecting surface", "Length"},
    {CDI_VAR_ID_DYN_HEAT_RATE_COEFF0, "heating rate coefficient Kc",    "Dimensionless"},
    {CDI_VAR_ID_DYN_HEAT_RATE_COEFF1, "heating rate coefficient tdh",   "Dimensionless"},
    {CDI_VAR_ID_DYN_HEAT_RATE_COEFF2, "heating rate coefficient Dh",    "Dimensionless"},
    {CDI_VAR_ID_ENERGY,             "temperature",                      "Temperature"},
    {CDI_VAR_ID_FAN_HUB_RADIUS,     "hub radius",                       "Length"},
    {CDI_VAR_ID_FAN_LENGTH,         "fan length",                       "Length"},
    {CDI_VAR_ID_FAN_RADIUS,         "fan radius",                       "Length"},
    {CDI_VAR_ID_GAXIS_X,            "G axis X",                         "Dimensionless"},
    {CDI_VAR_ID_GAXIS_Y,            "G axis Y",                         "Dimensionless"},
    {CDI_VAR_ID_GAXIS_Z,            "G axis Z",                         "Dimensionless"},
    {CDI_VAR_ID_MASS,               "pressure",                         "StaticPressure"},
    {CDI_VAR_ID_MASS_FLUX_X,        "mass flux X",                      "MassFlux"},
    {CDI_VAR_ID_MASS_FLUX_Y,        "mass flux Y",                      "MassFlux"},
    {CDI_VAR_ID_MASS_FLUX_Z,        "mass flux Z",                      "MassFlux"},
    {CDI_VAR_ID_MRF_TYPE,           "type",                             "Dimensionless"},
    {CDI_VAR_ID_NEW_MASS_FLUX_X,    "mass flux X",                      "MassFlux"},
    {CDI_VAR_ID_NEW_MASS_FLUX_Y,    "mass flux Y",                      "MassFlux"},
    {CDI_VAR_ID_NEW_MASS_FLUX_Z,    "mass flux Z",                      "MassFlux"},
    {CDI_VAR_ID_NEW_PRESSURE,       "pressure",                         "StaticPressure"},
    {CDI_VAR_ID_NU_ON_T,            "viscosity/temperature",            "Dimensionless"},
    {CDI_VAR_ID_POINT_X,            "ref point X",                      "Length"},
    {CDI_VAR_ID_POINT_Y,            "ref point Y",                      "Length"},
    {CDI_VAR_ID_POINT_Z,            "ref point Z",                      "Length"},
    {CDI_VAR_ID_POROUS_HEAT_COEF,   "heating coefficient",              "HeatingCoeff"},
    {CDI_VAR_ID_POROUS_Q0,          "constant volumetric heat input",   "PowerDensity"},
    {CDI_VAR_ID_POROUS_REF_TEMP,    "reference temperature",            "Temperature"},
    {CDI_VAR_ID_PRESSURE,           "pressure",                         "StaticPressure"},
    {CDI_VAR_ID_RAXIS_X,            "R axis X",                         "Dimensionless"},
    {CDI_VAR_ID_RAXIS_Y,            "R axis Y",                         "Dimensionless"},
    {CDI_VAR_ID_RAXIS_Z,            "R axis Z",                         "Dimensionless"},
    {CDI_VAR_ID_REF_FRAME,          "reference frame",                  "Dimensionless"},
    {CDI_VAR_ID_REF_TEMP,           "reference temperature",            "Temperature"},
    {CDI_VAR_ID_RES0_B,             "b viscous resistance",             "InvTime"},
    {CDI_VAR_ID_RES0_G,             "g viscous resistance",             "InvTime"},
    {CDI_VAR_ID_RES0_R,             "r viscous resistance",             "InvTime"},
    {CDI_VAR_ID_RES1_B,             "b inertial resistance",            "InvLength"},
    {CDI_VAR_ID_RES1_G,             "g inertial resistance",            "InvLength"},
    {CDI_VAR_ID_RES1_R,             "r inertial resistance",            "InvLength"},
    {CDI_VAR_ID_RESISTANCE_REF_TEMP,"resistance reference temperature", "Temperature"},
    {CDI_VAR_ID_RESPONSE_TIME,      "response time",                    "Time"},
    {CDI_VAR_ID_R_FORCE_COEFF0,     "R force coefficient 0",            "Acceleration"},
    {CDI_VAR_ID_R_FORCE_COEFF1,     "R force coefficient 1",            "InvTime"},
    {CDI_VAR_ID_R_FORCE_COEFF2,     "R force coefficient 2",            "InvLength"},
    {CDI_VAR_ID_R_FORCE_COEFF3,     "R force coefficient 3",            "TimePerLengthSqrd"},
    {CDI_VAR_ID_SLIP_FAC,           "slip factor",                      "Dimensionless"},
    {CDI_VAR_ID_STATE_OF_MATTER,    "state of matter",                  "Dimensionless"},
    {CDI_VAR_ID_SURFACE_CHAR_LEN,   "wall model char_length",           "Length"},
    {CDI_VAR_ID_SURFACE_ROUGHNESS,  "surface roughness char height",    "Length"},
    {CDI_VAR_ID_TANG_VEL_COEFF0,    "tangential velocity 0th-order coeff","Velocity"},
    {CDI_VAR_ID_TANG_VEL_MODEL,     "tangential velocity model",        "Dimensionless"},
    {CDI_VAR_ID_TANG_VEL_THRUST_COEFF, "tangential velocity thrust coeff","InvMassFlow"},
    {CDI_VAR_ID_TEMP,               "temperature",                      "Temperature"},
    {CDI_VAR_ID_THERMAL_EXP_COEFF,  "thermal expansion coefficient",    "InvTemperature"},
    {CDI_VAR_ID_TURB_DISSIPATION,   "turb dissipation",                 "TurbulenceDissipation"},
    {CDI_VAR_ID_TURB_INTENSITY,     "turb intensity",                   "Dimensionless"},
    {CDI_VAR_ID_TURB_KE,            "turb kinetic energy",              "SpecificEnergy"},
    {CDI_VAR_ID_TURB_LENGTH_SCALE,  "turb length scale",                "Length"},
    {CDI_VAR_ID_TURB_VIA,           "turb profile via",                 "Dimensionless"},
    {CDI_VAR_ID_UNIT_VEC_X,         "unit vector X",                    "Dimensionless"},
    {CDI_VAR_ID_UNIT_VEC_Y,         "unit vector Y",                    "Dimensionless"},
    {CDI_VAR_ID_UNIT_VEC_Z,         "unit vector Z",                    "Dimensionless"},
    {CDI_VAR_ID_USE_DYN_HEAT_RATE,  "use dynamic heating rate",         "Dimensionless"},
    {CDI_VAR_ID_U_THETA_COEFF0,     "u theta coefficient 0",            "Velocity"},
    {CDI_VAR_ID_U_THETA_COEFF1,     "u theta coefficient 1",            "Dimensionless"},
    {CDI_VAR_ID_U_THETA_COEFF2,     "u theta coefficient 2",            "InvVelocity"},
    {CDI_VAR_ID_U_THETA_COEFF3,     "u theta coefficient 3",            "InvVelocitySqrd"},
    {CDI_VAR_ID_VEL_X,              "ref point vel X",                  "Velocity"},
    {CDI_VAR_ID_VEL_X_T0,           "ref point vel X (t=0)",            "Velocity"},
    {CDI_VAR_ID_VEL_Y,              "ref point vel Y",                  "Velocity"},
    {CDI_VAR_ID_VEL_Y_T0,           "ref point vel Y (t=0)",            "Velocity"},
    {CDI_VAR_ID_VEL_Z,              "ref point vel Z",                  "Velocity"},
    {CDI_VAR_ID_VEL_Z_T0,           "ref point vel Z (t=0)",            "Velocity"},
    {CDI_VAR_ID_WALL_HEAT_FLUX,     "wall heat flux",                   "WallHeatFlux"},
    {CDI_VAR_ID_X_MOM,              "X velocity",                       "Velocity"},
    {CDI_VAR_ID_Y_MOM,              "Y velocity",                       "Velocity"},
    {CDI_VAR_ID_Z_MOM,              "Z velocity",                       "Velocity"},
    {CDI_VAR_ID_WALL_THICKNESS,     "wall thickness",                   "Length"},
    {CDI_VAR_ID_WALL_CONDUCTIVITY,  "wall heat conductivity",           "ThermalConductivity"},
    {CDI_VAR_ID_EXT_HEAT_XFER_COEFF,"exterior HTC",                     "HeatTransferCoeff"},
    {CDI_VAR_ID_APM_DENSITY,        "density of Acoustic Porous Medium","Density"},
    {CDI_VAR_ID_APM_POROSITY,       "porosity of Acoustic Porous Medium","Dimensionless"},
    {CDI_VAR_ID_APM_THICKNESS,      "thickness of Acoustic Porous Medium","Length"},
    {CDI_VAR_ID_FLUID_DESC_INDEX,   "index of a fluid physics descriptor","Dimensionless"},
    {CDI_VAR_ID_SURFACE_MATERIAL,   "surface material",                 "Dimensionless"},
    {CDI_VAR_ID_CURVED_HX_TABLE_INDEX,   "curved hx table index",       "Dimensionless"},
    {CDI_VAR_ID_CURVED_HX_MEAS_WINDOW_INDEX,   "curved hx meas window index ", "Dimensionless"},
    {CDI_VAR_ID_VEL_SLIP_TYPE,       "slip velocity type",              "Dimensionless"},
    {CDI_VAR_ID_APM_TORTUOSITY,      "tortuosity of Acoustic Porous Medium","Dimensionless"},
    {CDI_VAR_ID_BOUNDARY_LAYER_TYPE, "type of boundary layer",          "Dimensionless"},
    {CDI_VAR_ID_FAR_FIELD_BC,        "far filed boundary condition",    "Dimensionless"},
    {CDI_VAR_ID_CONDENSABLE_SURFACE, "condensable surface",             "Dimensionless"},
    {CDI_VAR_ID_FILM_THICKNESS,      "initial film thickness",          "Length"},
    {CDI_VAR_ID_WATER_VAPOR_SOURCE_TERM, "water vapor source term",     "SourceTerm"},
    {CDI_VAR_ID_WATER_MASS_FRACTION, "water vapor mass fraction",       "Dimensionless"},
    {CDI_VAR_ID_RELATIVE_HUMIDITY,   "relative humidity",               "Dimensionless"},
    {CDI_VAR_ID_DEFORMING_TIRE,      "deforming tire",                  "Dimensionless"},
    {CDI_VAR_ID_AVERAGE_MEA_ACROSS,  "average measurements across deformed facets",   "Dimensionless"},
    {CDI_VAR_ID_VELOCITY_VIA_TORQUE, "angular velocity via torque", "Dimensionless"},
    {CDI_VAR_ID_RESISTIVE_TORQUE,    "resistive torque acting on the rotating body", "Torque"},
    {CDI_VAR_ID_EXTERNAL_TORQUE,     "external torque acting on the rotating body", "Torque"},
    {CDI_VAR_ID_START_TIME,          "start rotational dynamics", "Time"},
    {CDI_VAR_ID_END_TIME,            "end rotational dynamics", "Time"},
    {CDI_VAR_ID_TIME_ACCURATE,       "time accurate/steady-state", "Dimensionless"},
    {CDI_VAR_ID_MOMENT_OF_INERTIA,   "moment of inertia of the rotating body", "MomentOfInertia"},
    {CDI_VAR_ID_MEAS_WINDOW_INDEX,   "measurement window index", "Dimensionless"},
    {CDI_VAR_ID_INITIAL_ANG_VEL,     "initial angular velocity", "AngularVelocity"},
    {CDI_VAR_ID_TURB_INTENSITY_X,    "turbulent intensity along x",     "Dimensionless"},
    {CDI_VAR_ID_TURB_INTENSITY_Y,    "turbulent intensity along y",     "Dimensionless"},
    {CDI_VAR_ID_TURB_INTENSITY_Z,    "turbulent intensity along z",     "Dimensionless"},
    {CDI_VAR_ID_TURB_LEN_SCALE_X,    "turbulent length scale along x",  "Length"},
    {CDI_VAR_ID_TURB_LEN_SCALE_Y,    "turbulent length scale along y",  "Length"},
    {CDI_VAR_ID_TURB_LEN_SCALE_Z,    "turbulent length scale along z",  "Length"},
    {CDI_VAR_ID_TURB_FREQ_MIN,       "minimum turbulent frequency",     "Frequency"},
    {CDI_VAR_ID_TURB_FREQ_MAX,       "maximum turbulent frequency",     "Frequency"},
    {CDI_VAR_ID_TURB_DELTA_T,        "sampling time interval of turbulent velocities", "Time"},
    {CDI_VAR_ID_TABULAR_DATA_ID,     "ID of table containing turb. vel. history", "Dimensionless"},
    {CDI_VAR_ID_MASS_FLOW_RATE,      "true mass flow", "MassFlow"},
    {CDI_VAR_ID_USE_REFL_DAMPING,    "use reflection damping", "Dimensionless"},
    {CDI_VAR_ID_APM_USE_ADV_FLUID,   "use advanced fluid-apm interface modeling", "Dimensionless"},
    {CDI_VAR_ID_APM_VISCOUS_RES,     "interface viscous resistance", "Velocity"},
    {CDI_VAR_ID_APM_INERTIAL_RES,    "interface inertial resistance", "Dimensionless"},
    {CDI_VAR_ID_APM_REACTANCE_COEFF, "interface reactance coefficient", "Length"},
    {CDI_VAR_ID_APM_SURFACE_POROSITY,"surface porosity", "Dimensionless"},
    {CDI_VAR_ID_INLET_AREA,          "inlet area", "Area"},
    {CDI_VAR_ID_MEAS_FRAME_NUM,      "measurement frame number", "Dimensionless"},
    {CDI_VAR_ID_IS_TOTAL_TEMP,       "temp type","Dimensionless"},
    {CDI_VAR_ID_IS_SUPERSONIC_INLET, "supersonic inlet", "Dimensionless"},
    {CDI_VAR_ID_SS_INLET_PRESSURE,   "supersonic inlet pressure", "StaticPressure"},
    {CDI_VAR_ID_REVERSE_MASS_FLOW,   "reverse mass flow", "Dimensionless"},
    {CDI_VAR_ID_ROTATION_VIA_REF_FRAME, "rotation via reference frame", "Dimensionless"},
    {CDI_VAR_ID_CONTAIN_TRANSONIC_FLOW, "reference frame may contain transonic flow", "Dimensionless"},
    {CDI_VAR_ID_SURFACE_ACOUSTIC_ABSORB, "wall acoustic absorption", "Dimensionless"},
    {CDI_VAR_ID_VEL_N,              "ref point normal velocity",     "Velocity"},
    {CDI_VAR_ID_IS_VEL_N,           "ref point is normal velocity",  "Dimensionless"},
    {CDI_VAR_ID_MEAS_START_FRAME_NUM,"first measurement frame number", "Dimensionless"},
    {CDI_VAR_ID_IMPORT_FLUCTUATIONS, "import fluctuations from measurement file", "Dimensionless"},
    {CDI_VAR_ID_PM_LEAKAGE_RESOLUTION, "cooling air leakage model option", "Dimensionless"},
    {CDI_VAR_ID_SOLID_MATERIAL,      "ID of solid material", "Dimensionless" },
    {CDI_VAR_ID_SHELL_CONFIGURATION, "ID of shell configuration", "Dimensionless" },
    {CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT, "conductor volumetric heat input",   "PowerDensity" },
    {CDI_VAR_ID_CONTACT_RESISTANCE,  "contact resistance",   "ContactResistance" },
    {CDI_VAR_ID_HEAT_FLUX_1, "heat flux 1", "WallHeatFlux" },
    {CDI_VAR_ID_HEAT_FLUX_2, "heat flux 2", "WallHeatFlux" },
    {CDI_VAR_ID_SHELL_LAYER_THICKNESS, "shell layer thickness", "Length" },
    {CDI_VAR_ID_SOLID_COMPUTE_RADIATION, "compute radiation", "Dimensionless" },
    {CDI_VAR_ID_RADIATION_SURFACE_COND, "ID of radiation surface condition", "Dimensionless" },
    {CDI_VAR_ID_ANISOTROPY_USE_PART_AXIS, "use part axis for material", "Dimensionless" },
    {CDI_VAR_ID_MIN_DESIGN_TEMP, "min design temperature", "Temperature" },
    {CDI_VAR_ID_MAX_DESIGN_TEMP, "max design temperature", "Temperature" },
    {CDI_VAR_ID_RADIATION_TEMP, "radiation temperature", "Temperature"},
    {CDI_VAR_ID_VISCOUS_DAMPING_FACTOR, "viscous damping factor", "Dimensionless"},
    {CDI_VAR_ID_SPECIFY_HEAT_VIA_POWER_DENSITY, " specify heat via power density",   "Dimensionless" },
    {CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT_IN_POWER, "imposed heat in power units",   "Power" },
    {CDI_VAR_ID_CONDUCTOR_WALL_HEAT_FLOW_IN, "wall heat flow in", "Power" },
    {CDI_VAR_ID_CONDUCTOR_WALL_HEAT_FLOW_OUT, "wall heat flow out", "Power" },
    {CDI_VAR_ID_SPECIAL_LAYER, "Special Layer type", "Dimensionless" },
    {CDI_VAR_ID_LAYER_CONTACT_AREA, "Contact Area Fraction", "Dimensionless" }
};

//
// Macros defining the inheritance relations among physics variables.  All
// variables should be derived from the same root so that they can be static
// cast from the root safely.
//

#define WATER_VAPOR_SOURCE_TERM                         \
    s_cdi_physics_vars + CPVI_WATER_VAPOR_SOURCE_TERM

#define FLUID_VARIABLE_BASE                             \
    s_cdi_physics_vars + CPVI_NU_ON_T,                  \
    s_cdi_physics_vars + CPVI_COORD_SYSTEM,             \
    s_cdi_physics_vars + CPVI_REF_FRAME,                \
    s_cdi_physics_vars + CPVI_VISCOUS_DAMPING_FACTOR,   \
    WATER_VAPOR_SOURCE_TERM

#define FAN_FLUID_VARIABLE_BASE                         \
    s_cdi_physics_vars + CPVI_NU_ON_T,                  \
    s_cdi_physics_vars + CPVI_COORD_SYSTEM,             \
    s_cdi_physics_vars + CPVI_REF_FRAME,                \
    WATER_VAPOR_SOURCE_TERM

#define SURFEL_VARIABLE_BASE                            \
    s_cdi_physics_vars + CPVI_REF_FRAME

// PowerCASE no longer includes a variable of type CDI_VAR_ID_SHELL_CONFIGURATION in wall physics
// descriptors, but old CDI files contain such variables, so they live on.
//
// Function derive_solid_surface_phys_descs() in SIMENG relies on CPVI_SHELL_CONFIGURATION appearing
// at the end of the following two lists.
#define SLIP_VARIABLE_BASE                              \
    SURFEL_VARIABLE_BASE,                               \
    s_cdi_physics_vars + CPVI_SLIP_FAC,                 \
    s_cdi_physics_vars + CPVI_SURFACE_ROUGHNESS,        \
    s_cdi_physics_vars + CPVI_SURFACE_CHAR_LEN,         \
    s_cdi_physics_vars + CPVI_SURFACE_MATERIAL,         \
    s_cdi_physics_vars + CPVI_BOUNDARY_LAYER_TYPE,      \
    s_cdi_physics_vars + CPVI_CONDENSABLE_SURFACE,      \
    s_cdi_physics_vars + CPVI_FILM_THICKNESS,           \
    s_cdi_physics_vars + CPVI_VAR_ID_SURFACE_ACOUSTIC_ABSORB, \
    s_cdi_physics_vars + CPVI_RADIATION_SURFACE_CONDITION,    \
    s_cdi_physics_vars + CPVI_SHELL_CONFIGURATION       \

#define NO_SLIP_VARIABLE_BASE                           \
    SURFEL_VARIABLE_BASE,                               \
    s_cdi_physics_vars + CPVI_SURFACE_MATERIAL,         \
    s_cdi_physics_vars + CPVI_CONDENSABLE_SURFACE,      \
    s_cdi_physics_vars + CPVI_FILM_THICKNESS,           \
    s_cdi_physics_vars + CPVI_RADIATION_SURFACE_CONDITION, \
    s_cdi_physics_vars + CPVI_SHELL_CONFIGURATION

#define HUMIDITY_CONTENT_VARIABLES                      \
    s_cdi_physics_vars + CPVI_WATER_MASS_FRACTION,      \
    s_cdi_physics_vars + CPVI_RELATIVE_HUMIDITY

#define INLET_OUTLET_VARIABLE_BASE                      \
    SURFEL_VARIABLE_BASE,                               \
    s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,           \
    s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,     \
    s_cdi_physics_vars + CPVI_TEMP,                     \
    s_cdi_physics_vars + CPVI_RADIATION_TEMP,           \
    HUMIDITY_CONTENT_VARIABLES

#define INLET_OUTLET_VARIABLE_BASE_5G                   \
    SURFEL_VARIABLE_BASE,                               \
    s_cdi_physics_vars + CPVI_TEMP

#define NON_REFLECTIVE_IO_VARIABLE_BASE                 \
    INLET_OUTLET_VARIABLE_BASE,                         \
    s_cdi_physics_vars + CPVI_RESPONSE_TIME

#define NON_REFLECTIVE_IO_VARIABLE_BASE_5G              \
    INLET_OUTLET_VARIABLE_BASE_5G,                      \
    s_cdi_physics_vars + CPVI_RESPONSE_TIME

#define PRESSURE_VARIABLE_BASE                          \
    NON_REFLECTIVE_IO_VARIABLE_BASE,                    \
    s_cdi_physics_vars + CPVI_NEW_PRESSURE

#define MASS_FLUX_VARIABLE_BASE                         \
    NON_REFLECTIVE_IO_VARIABLE_BASE,                    \
    s_cdi_physics_vars + CPVI_NEW_MASS_FLUX_X,          \
    s_cdi_physics_vars + CPVI_NEW_MASS_FLUX_Y,          \
    s_cdi_physics_vars + CPVI_NEW_MASS_FLUX_Z,          \
    s_cdi_physics_vars + CPVI_COORD_SYSTEM,             \
    s_cdi_physics_vars + CPVI_USE_REFL_DAMPING

#define MASS_FLOW_VARIABLE_BASE                         \
    NON_REFLECTIVE_IO_VARIABLE_BASE,                    \
    s_cdi_physics_vars + CPVI_MASS_FLOW_RATE,           \
    s_cdi_physics_vars + CPVI_USE_REFL_DAMPING,         \
    s_cdi_physics_vars + CPVI_INLET_AREA,               \
    s_cdi_physics_vars + CPVI_REVERSE_MASS_FLOW

//
// additional variables to be appended to the bases.
//
#define ROTATION_VARIABLES                              \
    s_cdi_physics_vars + CPVI_ANG_VEL,                  \
    s_cdi_physics_vars + CPVI_UNIT_VEC_X,               \
    s_cdi_physics_vars + CPVI_UNIT_VEC_Y,               \
    s_cdi_physics_vars + CPVI_UNIT_VEC_Z,               \
    s_cdi_physics_vars + CPVI_POINT_X,                  \
    s_cdi_physics_vars + CPVI_POINT_Y,                  \
    s_cdi_physics_vars + CPVI_POINT_Z

#define VELOCITY_VARIABLES                              \
    s_cdi_physics_vars + CPVI_VEL_X,                    \
    s_cdi_physics_vars + CPVI_VEL_Y,                    \
    s_cdi_physics_vars + CPVI_VEL_Z,                    \
    s_cdi_physics_vars + CPVI_COORD_SYSTEM

#define NORMAL_VELOCITY_VARIABLES                       \
    s_cdi_physics_vars + CVPI_VEL_N,                    \
    s_cdi_physics_vars + CVPI_IS_VEL_N

#define DIRECTION_VARIABLES                             \
    s_cdi_physics_vars + CPVI_UNIT_VEC_X,               \
    s_cdi_physics_vars + CPVI_UNIT_VEC_Y,               \
    s_cdi_physics_vars + CPVI_UNIT_VEC_Z,               \
    s_cdi_physics_vars + CPVI_COORD_SYSTEM

#define TURBULENCE_VARIABLES                            \
    s_cdi_physics_vars + CPVI_TURB_VIA,                 \
    s_cdi_physics_vars + CPVI_TURB_INTENSITY,           \
    s_cdi_physics_vars + CPVI_TURB_LENGTH_SCALE,        \
    s_cdi_physics_vars + CPVI_TURB_KE,                  \
    s_cdi_physics_vars + CPVI_TURB_DISSIPATION

#define TURB_SYNTH_VARIABLES                            \
   s_cdi_physics_vars + CPVI_TURB_INTENSITY_X,          \
   s_cdi_physics_vars + CPVI_TURB_INTENSITY_Y,          \
   s_cdi_physics_vars + CPVI_TURB_INTENSITY_Z,          \
   s_cdi_physics_vars + CPVI_TURB_LEN_SCALE_X,          \
   s_cdi_physics_vars + CPVI_TURB_LEN_SCALE_Y,          \
   s_cdi_physics_vars + CPVI_TURB_LEN_SCALE_Z,          \
   s_cdi_physics_vars + CPVI_TURB_FREQ_MIN,             \
   s_cdi_physics_vars + CPVI_TURB_FREQ_MAX,             \
   s_cdi_physics_vars + CPVI_TURB_DELTA_T,              \
   s_cdi_physics_vars + CPVI_TURB_TABULAR_DATA_ID,      \
   s_cdi_physics_vars + CPVI_INLET_AREA

#define MME_VARIABLES                                   \
    s_cdi_physics_vars + CPVI_MASS,                     \
    s_cdi_physics_vars + CPVI_X_MOM,                    \
    s_cdi_physics_vars + CPVI_Y_MOM,                    \
    s_cdi_physics_vars + CPVI_Z_MOM,                    \
    s_cdi_physics_vars + CPVI_ENERGY

#define PVT_VARIABLES                                   \
    s_cdi_physics_vars + CPVI_NEW_PRESSURE,             \
    s_cdi_physics_vars + CPVI_VEL_X,                    \
    s_cdi_physics_vars + CPVI_VEL_Y,                    \
    s_cdi_physics_vars + CPVI_VEL_Z,                    \
    s_cdi_physics_vars + CPVI_TEMP

#define PM_LEAKAGE_RESOLUTION                           \
    s_cdi_physics_vars + CPVI_PM_LEAKAGE_RESOLUTION

#define POROUS_MEDIA_VARIABLES                          \
    s_cdi_physics_vars + CPVI_NU_ON_T,                  \
    s_cdi_physics_vars + CPVI_CURVED_POROUS_MEDIUM,     \
    s_cdi_physics_vars + CPVI_RAXIS_X,                  \
    s_cdi_physics_vars + CPVI_RAXIS_Y,                  \
    s_cdi_physics_vars + CPVI_RAXIS_Z,                  \
    s_cdi_physics_vars + CPVI_GAXIS_X,                  \
    s_cdi_physics_vars + CPVI_GAXIS_Y,                  \
    s_cdi_physics_vars + CPVI_GAXIS_Z,                  \
    s_cdi_physics_vars + CPVI_COORD_SYSTEM,             \
    s_cdi_physics_vars + CPVI_REF_FRAME,                \
    s_cdi_physics_vars + CPVI_RESISTANCE_REF_TEMP,      \
    s_cdi_physics_vars + CPVI_RES0_R,                   \
    s_cdi_physics_vars + CPVI_RES0_G,                   \
    s_cdi_physics_vars + CPVI_RES0_B,                   \
    s_cdi_physics_vars + CPVI_RES1_R,                   \
    s_cdi_physics_vars + CPVI_RES1_G,                   \
    s_cdi_physics_vars + CPVI_RES1_B,                   \
    WATER_VAPOR_SOURCE_TERM,				\
    PM_LEAKAGE_RESOLUTION

#define POROUS_MEDIA_THERMAL_VARIABLES                  \
    s_cdi_physics_vars + CPVI_POROUS_HEAT_COEF,         \
    s_cdi_physics_vars + CPVI_POROUS_REF_TEMP,          \
    s_cdi_physics_vars + CPVI_POROUS_Q0,                \
    s_cdi_physics_vars + CPVI_USE_DYN_HEAT_RATE,        \
    s_cdi_physics_vars + CPVI_DYN_HEAT_RATE_COEFF0,     \
    s_cdi_physics_vars + CPVI_DYN_HEAT_RATE_COEFF1,     \
    s_cdi_physics_vars + CPVI_DYN_HEAT_RATE_COEFF2

#define CURVED_HX_POROUS_MEDIA_VARIABLES                 \
    s_cdi_physics_vars + CPVI_CURVED_HX_TABLE_INDEX,     \
    s_cdi_physics_vars + CPVI_CURVED_HX_MEAS_WINDOW_INDEX

#define ACOUSTIC_POROUS_MEDIA_VARIABLES                 \
    s_cdi_physics_vars + CPVI_APM_POROSITY,             \
    s_cdi_physics_vars + CPVI_APM_TORTUOSITY,           \
    s_cdi_physics_vars + CPVI_APM_USE_ADV_FLUID,        \
    s_cdi_physics_vars + CPVI_APM_VISCOUS_RES,          \
    s_cdi_physics_vars + CPVI_APM_INERTIAL_RES,         \
    s_cdi_physics_vars + CPVI_APM_REACTANCE_COEFF,      \
    s_cdi_physics_vars + CPVI_APM_SURFACE_POROSITY

#define MME_VARIABLES_5G                                \
    s_cdi_physics_vars + CPVI_COMP_RATIO_TYPE,          \
    s_cdi_physics_vars + CPVI_COMP0_DENSITY,            \
    s_cdi_physics_vars + CPVI_COMP1_DENSITY,            \
    s_cdi_physics_vars + CPVI_NEW_PRESSURE,             \
    s_cdi_physics_vars + CPVI_DENSITY_T0,               \
    s_cdi_physics_vars + CPVI_COMP0_VOL_FRAC,           \
    s_cdi_physics_vars + CPVI_COMP1_VOL_FRAC,           \
    s_cdi_physics_vars + CPVI_STATE_OF_MATTER,          \
    s_cdi_physics_vars + CPVI_VEL_X,                    \
    s_cdi_physics_vars + CPVI_VEL_Y,                    \
    s_cdi_physics_vars + CPVI_VEL_Z,                    \
    s_cdi_physics_vars + CPVI_TEMP

#define STANDARD_WALL_5G                                \
    SURFEL_VARIABLE_BASE,                               \
    s_cdi_physics_vars + CPVI_COMP0_FRICTION_FACTOR,    \
    s_cdi_physics_vars + CPVI_COMP0_WETTING_DENS,       \
    s_cdi_physics_vars + CPVI_COMP1_FRICTION_FACTOR,    \
    s_cdi_physics_vars + CPVI_COMP1_WETTING_DENS

#define PRESSURE_VARIABLE_BASE_5G                       \
    NON_REFLECTIVE_IO_VARIABLE_BASE_5G,                 \
    s_cdi_physics_vars + CPVI_NEW_PRESSURE,             \
    s_cdi_physics_vars + CPVI_COMP0_VOL_FRAC,           \
    s_cdi_physics_vars + CPVI_COMP1_VOL_FRAC,           \
    s_cdi_physics_vars + CPVI_STATE_OF_MATTER,          \
    s_cdi_physics_vars + CPVI_BC_TYPE

#define CONDUCTION_FIXED_HTC_AMBIENT_TEMP_VARIABLES     \
    s_cdi_physics_vars + CPVI_TEMP,                     \
    s_cdi_physics_vars + CPVI_EXT_HEAT_XFER_COEFF

// Function derive_solid_surface_phys_descs() in SIMENG relies on the order of the 4 variables in
// the following list.
#define THERMAL_RESIST_VARIABLES                        \
    CONDUCTION_FIXED_HTC_AMBIENT_TEMP_VARIABLES,        \
    s_cdi_physics_vars + CPVI_WALL_THICKNESS,           \
    s_cdi_physics_vars + CPVI_WALL_CONDUCTIVITY

#define INLET_TEMP_VARIABLES                            \
    s_cdi_physics_vars + CPVI_IS_TOTAL_TEMP,            \
    s_cdi_physics_vars + CPVI_IS_SUPERSONIC_INLET,      \
    s_cdi_physics_vars + CPVI_SS_INLET_PRESSURE

#define CONDUCTION_BOUNDARY_VARIABLE_BASE               \
    s_cdi_physics_vars + CPVI_SHELL_CONFIGURATION,      \
    s_cdi_physics_vars + CPVI_RADIATION_SURFACE_CONDITION

static sCDI_PHYS_TYPE_DESCRIPTOR cdi_physics_table[] =
{
    {CDI_PHYS_TYPE_LOCAL_REF_FRAME, "Local Rotating Reference Frame", CDI_PHYSICS_IS_VOLUME, FALSE, 17, 0,
        {
            s_cdi_physics_vars + CPVI_ANG_VEL,
            s_cdi_physics_vars + CPVI_CYL_BEGIN_X,
            s_cdi_physics_vars + CPVI_CYL_BEGIN_Y,
            s_cdi_physics_vars + CPVI_CYL_BEGIN_Z,
            s_cdi_physics_vars + CPVI_CYL_END_X,
            s_cdi_physics_vars + CPVI_CYL_END_Y,
            s_cdi_physics_vars + CPVI_CYL_END_Z,
            s_cdi_physics_vars + CPVI_MRF_TYPE,
            // the following variables were added for rotational dynamics
            s_cdi_physics_vars + CPVI_RESISTIVE_TORQUE,
            s_cdi_physics_vars + CPVI_EXTERNAL_TORQUE,
            s_cdi_physics_vars + CPVI_VELOCITY_VIA_TORQUE,
            s_cdi_physics_vars + CPVI_INITIAL_ANG_VEL,
            s_cdi_physics_vars + CPVI_MOMENT_OF_INERTIA,
            s_cdi_physics_vars + CPVI_START_TIME,
            s_cdi_physics_vars + CPVI_END_TIME,
            s_cdi_physics_vars + CPVI_TIME_ACCURATE,
            s_cdi_physics_vars + CPVI_MEAS_WINDOW_INDEX
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NON_INERTIAL_FRAME, "Non-inertial Frame", CDI_PHYSICS_IS_VOLUME, FALSE, 16, 0,
        {
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_POINT_X,
            s_cdi_physics_vars + CPVI_POINT_Y,
            s_cdi_physics_vars + CPVI_POINT_Z,
            s_cdi_physics_vars + CPVI_VEL_X,
            s_cdi_physics_vars + CPVI_VEL_Y,
            s_cdi_physics_vars + CPVI_VEL_Z,
            s_cdi_physics_vars + CPVI_ACCEL_X,
            s_cdi_physics_vars + CPVI_ACCEL_Y,
            s_cdi_physics_vars + CPVI_ACCEL_Z,
            s_cdi_physics_vars + CPVI_VEL_X_T0,
            s_cdi_physics_vars + CPVI_VEL_Y_T0,
            s_cdi_physics_vars + CPVI_VEL_Z_T0,
            s_cdi_physics_vars + CPVI_ANG_VEL_X,
            s_cdi_physics_vars + CPVI_ANG_VEL_Y,
            s_cdi_physics_vars + CPVI_ANG_VEL_Z
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_GRAVITY_BUOYANCY, "Gravity and Buoyancy", CDI_PHYSICS_IS_VOLUME, FALSE, 6, 0,
        {
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_ACCEL_X,
            s_cdi_physics_vars + CPVI_ACCEL_Y,
            s_cdi_physics_vars + CPVI_ACCEL_Z,
            s_cdi_physics_vars + CPVI_REF_TEMP,
            s_cdi_physics_vars + CPVI_THERMAL_EXP_COEFF
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_BODY_FORCE, "Body Force", CDI_PHYSICS_IS_VOLUME, FALSE, 4, 0,
        {
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_ACCEL_X,
            s_cdi_physics_vars + CPVI_ACCEL_Y,
            s_cdi_physics_vars + CPVI_ACCEL_Z,
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_FV_FLUID, "FV Fluid", CDI_PHYSICS_IS_VOLUME, FALSE, 2, 5,
        {
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_REF_FRAME
        },
        {
            MME_VARIABLES
        },
    },

    {CDI_PHYS_TYPE_NON_SLIP, "EndoNoslip", CDI_PHYSICS_IS_ENDOSURFACE, TRUE, 1, 0,
        {
            SURFEL_VARIABLE_BASE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_SLIP, "Slip", CDI_PHYSICS_IS_EXOSURFACE, TRUE, 2, 0,
        {
            s_cdi_physics_vars + CPVI_SLIP_FAC,
            s_cdi_physics_vars + CPVI_REF_FRAME
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_SOURCE, "Source", CDI_PHYSICS_IS_ENDOSURFACE, FALSE, 5, 0,
        {
            MME_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_VV_FLUID, "VV Fluid", CDI_PHYSICS_IS_VOLUME, FALSE, 5, 5,
        {
            FLUID_VARIABLE_BASE
        },
        {
            MME_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_POROUS_VV_FLUID, "Porous VV Fluid", CDI_PHYSICS_IS_VOLUME, FALSE, 19, 7,
        {
	  POROUS_MEDIA_VARIABLES
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_LES_VV_FLUID, "Turb Fluid", CDI_PHYSICS_IS_VOLUME, FALSE, 5, 12,
        {
            FLUID_VARIABLE_BASE,
        },
        {
            MME_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_SOLID, "Solid", CDI_PHYSICS_IS_VOLUME, FALSE, 1, 0,
        {
          s_cdi_physics_vars + CPVI_SOLID_COMPUTE_RADIATION
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_SOLID_CONDUCTOR, "Conducting Solid", CDI_PHYSICS_IS_VOLUME, FALSE, 12, 1,
        {
          s_cdi_physics_vars + CPVI_SOLID_MATERIAL,
          s_cdi_physics_vars + CPVI_COORD_SYSTEM,
          s_cdi_physics_vars + CPVI_ANISOTROPY_USE_PART_AXIS,
          s_cdi_physics_vars + CPVI_GAXIS_X,
          s_cdi_physics_vars + CPVI_GAXIS_Y,
          s_cdi_physics_vars + CPVI_GAXIS_Z,
          s_cdi_physics_vars + CPVI_SOLID_IMPOSED_HEAT,
          s_cdi_physics_vars + CPVI_SOLID_COMPUTE_RADIATION,
          s_cdi_physics_vars + CPVI_MIN_DESIGN_TEMP,
          s_cdi_physics_vars + CPVI_MAX_DESIGN_TEMP,
          s_cdi_physics_vars + CPVI_SPECIFY_HEAT_VIA_POWER_DENSITY,
          s_cdi_physics_vars + CPVI_SOLID_IMPOSED_HEAT_IN_POWER,
        },
        {
          s_cdi_physics_vars + CPVI_TEMP
        }
    },
    {CDI_PHYS_TYPE_CONDUCTION_LAYER, "Conducting Shell layer", CDI_PHYSICS_IS_SHELL_CONDUCTION, FALSE, 14, 1,
        {
          s_cdi_physics_vars + CPVI_SPECIAL_LAYER,
          s_cdi_physics_vars + CPVI_SOLID_MATERIAL,
          s_cdi_physics_vars + CPVI_COORD_SYSTEM,
          s_cdi_physics_vars + CPVI_ANISOTROPY_USE_PART_AXIS,
          s_cdi_physics_vars + CPVI_GAXIS_X,
          s_cdi_physics_vars + CPVI_GAXIS_Y,
          s_cdi_physics_vars + CPVI_GAXIS_Z,
          s_cdi_physics_vars + CPVI_SOLID_IMPOSED_HEAT,
          s_cdi_physics_vars + CPVI_SHELL_LAYER_THICKNESS,
          s_cdi_physics_vars + CPVI_SPECIFY_HEAT_VIA_POWER_DENSITY,
          s_cdi_physics_vars + CPVI_SOLID_IMPOSED_HEAT_IN_POWER,
          s_cdi_physics_vars + CPVI_LAYER_CONTACT_AREA,
          s_cdi_physics_vars + CPVI_RADIATION_SURFACE_CONDITION,//front interface
          s_cdi_physics_vars + CPVI_RADIATION_SURFACE_CONDITION // back interface
        },
        {
          s_cdi_physics_vars + CPVI_TEMP
        }
    },

    { CDI_PHYS_TYPE_SOLID_INSULATOR, "Insulating Solid", CDI_PHYSICS_IS_VOLUME, FALSE, 1, 0,
        {
          s_cdi_physics_vars + CPVI_SOLID_COMPUTE_RADIATION
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_SOLID_HOLLOW, "Hollow Solid", CDI_PHYSICS_IS_VOLUME, FALSE, 1, 0,
      {
        s_cdi_physics_vars + CPVI_SOLID_COMPUTE_RADIATION
      },
      {NULL}
    },

    {CDI_PHYS_TYPE_5G_FLUID_PHASE_SOURCE_SINK, "Fluid Phase Source-Sink", CDI_PHYSICS_IS_VOLUME, FALSE, 3, 0,
        {
          s_cdi_physics_vars + CPVI_REVERSE_MASS_FLOW,
          s_cdi_physics_vars + CPVI_COMP0_VOL_FRAC,
          s_cdi_physics_vars + CPVI_COMP1_VOL_FRAC
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_ROTATING_TIRE, "Rotating Tire", CDI_PHYSICS_IS_VOLUME, FALSE, 10, 0,
        {
            s_cdi_physics_vars + CPVI_REF_FRAME,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE,
            s_cdi_physics_vars + CPVI_AVERAGE_MEA_ACROSS
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_SLIP95, "Slip95", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 11, 0,
        {
            SLIP_VARIABLE_BASE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_TRUE_NOSLIP, "NoSlip", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 6, 0,
        {
            NO_SLIP_VARIABLE_BASE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_STATIC_PRESSURE_FIXED_DIR, "CstatFixed", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 6, 0,
        {
            s_cdi_physics_vars + CPVI_PRESSURE,
            s_cdi_physics_vars + CPVI_TEMP,
            DIRECTION_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_STATIC_PRESSURE_FREE_DIR, "CstatFree", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 2, 0,
        {
            s_cdi_physics_vars + CPVI_PRESSURE,
            s_cdi_physics_vars + CPVI_TEMP
        },
        {NULL}
    },
    {CDI_PHYS_TYPE_STAG_PRESSURE_FIXED_DIR, "CstagFixed", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 6, 0,
        {
            s_cdi_physics_vars + CPVI_PRESSURE,
            s_cdi_physics_vars + CPVI_TEMP,
            DIRECTION_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_STAG_PRESSURE_FREE_DIR, "CstagFree", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 2, 0,
        {
            s_cdi_physics_vars + CPVI_PRESSURE,
            s_cdi_physics_vars + CPVI_TEMP
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LINEAR_SLIP, "LinearSlip", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 15, 0,
        {
            SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_VEL_SLIP, "VelocitySlip", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 22, 0,
        {
            SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_POINT_X,
            s_cdi_physics_vars + CPVI_POINT_Y,
            s_cdi_physics_vars + CPVI_POINT_Z,
            s_cdi_physics_vars + CPVI_ANG_VEL_X,
            s_cdi_physics_vars + CPVI_ANG_VEL_Y,
            s_cdi_physics_vars + CPVI_ANG_VEL_Z,
            s_cdi_physics_vars + CPVI_VEL_SLIP_TYPE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LINEAR_NOSLIP, "LinearNoSlip", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 10, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_ANGULAR_SLIP, "AngularSlip", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 20, 0,
        {
            SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_ANGULAR_NOSLIP, "AngularNoSlip", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 15, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_MASS_FLUX, "MassFlux", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 5, 0,
        {
            s_cdi_physics_vars + CPVI_MASS_FLUX_X,
            s_cdi_physics_vars + CPVI_MASS_FLUX_Y,
            s_cdi_physics_vars + CPVI_MASS_FLUX_Z,
            s_cdi_physics_vars + CPVI_TEMP,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_FIXED_VEL, "FixedVel", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 26, 0,
        {
            NON_REFLECTIVE_IO_VARIABLE_BASE,
            VELOCITY_VARIABLES,
            NORMAL_VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_IMPORT_FLUCTUATIONS,
            TURB_SYNTH_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_PASS_THRU, "PassThru", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 4, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            SURFEL_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_TEMP
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_SOURCE_SURFEL, "PressureVelocity", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 27, 0,
        {
            INLET_OUTLET_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            VELOCITY_VARIABLES,
            NORMAL_VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_FAR_FIELD_BC,
            s_cdi_physics_vars + CPVI_IMPORT_FLUCTUATIONS,
            TURB_SYNTH_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NEW_VV_FLUID, "VV Fluid", CDI_PHYSICS_IS_VOLUME, FALSE, 5, 7,
        {
            FLUID_VARIABLE_BASE,
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_NEW_SOURCE, "Source", CDI_PHYSICS_IS_ENDOSURFACE, FALSE, 7, 0,
        {
            PVT_VARIABLES,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_REF_FRAME
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NEW_LES_VV_FLUID, "Turb Fluid", CDI_PHYSICS_IS_VOLUME, FALSE, 5, 12,
        {
            FLUID_VARIABLE_BASE,
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_POROUS_LES_VV_FLUID, "Porous VV Fluid", CDI_PHYSICS_IS_VOLUME, FALSE, 19, 12,
        {
	  POROUS_MEDIA_VARIABLES
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR, "CstatFixed", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 13, 0,
        {
            PRESSURE_VARIABLE_BASE,
            DIRECTION_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR, "CstatFree", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 10, 0,
        {
            PRESSURE_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_DIST_TO_REFL_SURFACE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR, "CstagFixed", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 16, 0,
        {
            PRESSURE_VARIABLE_BASE,
            DIRECTION_VARIABLES,
            INLET_TEMP_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR, "CstagFree", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 9, 0,
        {
            PRESSURE_VARIABLE_BASE,
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NEW_MASS_FLUX, "MassFlux", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 13, 0,
        {
            MASS_FLUX_VARIABLE_BASE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_MASS_FLOW, "MassFlow", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 12, 0,
        {
            MASS_FLOW_VARIABLE_BASE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_SOURCE, "Source", CDI_PHYSICS_IS_ENDOSURFACE, FALSE, 12, 0,
        {
            PVT_VARIABLES,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR, "CstatFixed", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 18, 0,
        {
            PRESSURE_VARIABLE_BASE,
            DIRECTION_VARIABLES,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR, "CstatFree", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 15, 0,
        {
            PRESSURE_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_DIST_TO_REFL_SURFACE,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR, "CstagFixed", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 21, 0,
        {
            PRESSURE_VARIABLE_BASE,
            DIRECTION_VARIABLES,
            INLET_TEMP_VARIABLES,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR, "CstagFree", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 14, 0,
        {
            PRESSURE_VARIABLE_BASE,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_MASS_FLUX, "MassFlux", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 18, 0,
        {
            MASS_FLUX_VARIABLE_BASE,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_MASS_FLOW, "MassFlow", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 17, 0,
        {
            MASS_FLOW_VARIABLE_BASE,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_FIXED_VEL, "FixedVel", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 31, 0,
        {
            NON_REFLECTIVE_IO_VARIABLE_BASE,
            VELOCITY_VARIABLES,
            NORMAL_VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_IMPORT_FLUCTUATIONS,
            TURB_SYNTH_VARIABLES,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_SOURCE_SURFEL, "PressureVelocity", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 32, 0,
        {
            INLET_OUTLET_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            VELOCITY_VARIABLES,
            NORMAL_VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_FAR_FIELD_BC,
            s_cdi_physics_vars + CPVI_IMPORT_FLUCTUATIONS,
            TURB_SYNTH_VARIABLES,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

  /*------------------------BEGIN thermal stuff------------------------------------*/

    {CDI_PHYS_TYPE_POROUS_VV_FLUID_THERMAL, "Porous VV Fluid Thermal", CDI_PHYSICS_IS_VOLUME, FALSE, 26, 7,
        {
            POROUS_MEDIA_VARIABLES,
            POROUS_MEDIA_THERMAL_VARIABLES
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_THERMAL, "Porous VV Fluid Thermal", CDI_PHYSICS_IS_VOLUME, FALSE, 26, 12,
        {
            POROUS_MEDIA_VARIABLES,
            POROUS_MEDIA_THERMAL_VARIABLES
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_CURVED_HX_POROUS_VV_FLUID_THERMAL, "Curved HX Porous VV Fluid Thermal", CDI_PHYSICS_IS_VOLUME, FALSE, 28, 7,
        {
            POROUS_MEDIA_VARIABLES,
            POROUS_MEDIA_THERMAL_VARIABLES,
            CURVED_HX_POROUS_MEDIA_VARIABLES
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_CURVED_HX_POROUS_LES_VV_FLUID_THERMAL, "Curved HX Porous VV Fluid Thermal", CDI_PHYSICS_IS_VOLUME, FALSE, 28, 12,
        {
            POROUS_MEDIA_VARIABLES,
            POROUS_MEDIA_THERMAL_VARIABLES,
            CURVED_HX_POROUS_MEDIA_VARIABLES
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_POROUS_VV_FLUID_FIXED_TEMP, "Porous VV Fluid Fixed Temp", CDI_PHYSICS_IS_VOLUME, FALSE, 20, 7,
        {
            POROUS_MEDIA_VARIABLES,
            s_cdi_physics_vars + CPVI_POROUS_REF_TEMP
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_POROUS_LES_VV_FLUID_FIXED_TEMP, "Porous VV Fluid Fixed Temp", CDI_PHYSICS_IS_VOLUME, FALSE, 20, 12,
        {
            POROUS_MEDIA_VARIABLES,
            s_cdi_physics_vars + CPVI_POROUS_REF_TEMP
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_SLIP95_FIXED_TEMP, "Slip95 Fixed Temp", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 12, 0,
        {
            SLIP_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_TEMP
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_TEMP, "NoSlip Fixed Temp", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 7, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_TEMP
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_TEMP, "LinearSlip Fixed Temp", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 16, 0,
        {
            SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_TEMP
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_TEMP, "LinearNoSlip Fixed Temp", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 11, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_TEMP
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_TEMP, "AngularSlip Fixed Temp", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 21, 0,
        {
            SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE,
            s_cdi_physics_vars + CPVI_TEMP
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_TEMP, "AngularNoSlip Fixed Temp", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 16, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE,
            s_cdi_physics_vars + CPVI_TEMP
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_SLIP95_FIXED_HEAT_FLUX, "Slip95 Fixed Flux", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 12, 0,
        {
            SLIP_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_WALL_HEAT_FLUX
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_HEAT_FLUX, "NoSlip Fixed Flux", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 7, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_WALL_HEAT_FLUX
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_HEAT_FLUX, "LinearSlip Fixed Flux", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 16, 0,
        {
            SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_WALL_HEAT_FLUX
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_HEAT_FLUX, "LinearNoSlip Fixed Flux", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 11, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_WALL_HEAT_FLUX
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_HEAT_FLUX, "AngularSlip Fixed Flux", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 21, 0,
        {
            SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE,
            s_cdi_physics_vars + CPVI_WALL_HEAT_FLUX
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_HEAT_FLUX, "AngularNoSlip Fixed Flux", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 16, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE,
            s_cdi_physics_vars + CPVI_WALL_HEAT_FLUX
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_SLIP95_THERMAL_RESIST, "Slip95 Thermal Resist", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 15, 0,
        {
            SLIP_VARIABLE_BASE,
            THERMAL_RESIST_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_TRUE_NOSLIP_THERMAL_RESIST, "NoSlip Thermal Resist", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 10, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            THERMAL_RESIST_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LINEAR_SLIP_THERMAL_RESIST, "LinearSlip Thermal Resist", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 19, 0,
        {
            SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES,
            THERMAL_RESIST_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LINEAR_NOSLIP_THERMAL_RESIST, "LinearNoSlip Thermal Resist", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 14, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES,
            THERMAL_RESIST_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_ANGULAR_SLIP_THERMAL_RESIST, "AngularSlip Thermal Resist", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 24, 0,
        {
            SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE,
            THERMAL_RESIST_VARIABLES
        },
        {NULL}
    },


    {CDI_PHYS_TYPE_ANGULAR_NOSLIP_THERMAL_RESIST, "AngularNoSlip Thermal Resist", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 19, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE,
            THERMAL_RESIST_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR_TFLOAT, "CstatFixedTFloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 10, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            DIRECTION_VARIABLES,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR_TFLOAT, "CstatFreeTFloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 7, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME,
            s_cdi_physics_vars + CPVI_DIST_TO_REFL_SURFACE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR_TFLOAT, "CstagFixedTFloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 10, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            DIRECTION_VARIABLES,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME
        },
        {NULL}
    },
    //
    // TODO: the order of variables below is different from other similer PDs.
    //
    {CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR_TFLOAT, "CstagFreeTFloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 6, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME,
            s_cdi_physics_vars + CPVI_REF_FRAME
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_NEW_MASS_FLUX_TFLOAT, "MassFluxTFloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 10, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_MASS_FLUX_X,
            s_cdi_physics_vars + CPVI_NEW_MASS_FLUX_Y,
            s_cdi_physics_vars + CPVI_NEW_MASS_FLUX_Z,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_DENSITY_T0,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_MASS_FLOW_TFLOAT, "MassFlowTFloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 15, 0,
        {
            MASS_FLOW_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_DENSITY_T0,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_SOURCE_SURFEL_TFLOAT, "PressureVelocityTFloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 24, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            s_cdi_physics_vars + CPVI_VEL_X,
            s_cdi_physics_vars + CPVI_VEL_Y,
            s_cdi_physics_vars + CPVI_VEL_Z,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            NORMAL_VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_FAR_FIELD_BC,
            s_cdi_physics_vars + CPVI_IMPORT_FLUCTUATIONS,
            TURB_SYNTH_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_FIXED_VEL_TFLOAT, "FixedVelTFloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 23, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_VEL_X,
            s_cdi_physics_vars + CPVI_VEL_Y,
            s_cdi_physics_vars + CPVI_VEL_Z,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            NORMAL_VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME,
            s_cdi_physics_vars + CPVI_IMPORT_FLUCTUATIONS,
            TURB_SYNTH_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR_TFLOAT, "CstatFixedTfloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 15, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            DIRECTION_VARIABLES,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR_TFLOAT, "CstatFreeTfloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 11, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME,
            s_cdi_physics_vars + CPVI_DIST_TO_REFL_SURFACE,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR_TFLOAT, "CstagFixedTfloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 15, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            DIRECTION_VARIABLES,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },
    //
    // TODO: the third variable was ref_frame, bug?
    //
    {CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR_TFLOAT, "CstagFreeTfloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 11, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME,
            s_cdi_physics_vars + CPVI_DIST_TO_REFL_SURFACE,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_MASS_FLUX_TFLOAT, "MassFluxTfloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 15, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_MASS_FLUX_X,
            s_cdi_physics_vars + CPVI_NEW_MASS_FLUX_Y,
            s_cdi_physics_vars + CPVI_NEW_MASS_FLUX_Z,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_DENSITY_T0,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_MASS_FLOW_TFLOAT, "MassFlowTfloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 20, 0,
        {
            MASS_FLOW_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_DENSITY_T0,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_FIXED_VEL_TFLOAT, "FixedVelTfloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 28, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_VEL_X,
            s_cdi_physics_vars + CPVI_VEL_Y,
            s_cdi_physics_vars + CPVI_VEL_Z,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            NORMAL_VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_RESPONSE_TIME,
            s_cdi_physics_vars + CPVI_IMPORT_FLUCTUATIONS,
            TURB_SYNTH_VARIABLES,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_LES_SOURCE_SURFEL_TFLOAT, "PressureVelocityTfloat", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 29, 0,
        {
            s_cdi_physics_vars + CPVI_MEAS_FRAME_NUM,
            s_cdi_physics_vars + CPVI_MEAS_START_FRAME_NUM,
            s_cdi_physics_vars + CPVI_RADIATION_TEMP,
            s_cdi_physics_vars + CPVI_NEW_PRESSURE,
            s_cdi_physics_vars + CPVI_VEL_X,
            s_cdi_physics_vars + CPVI_VEL_Y,
            s_cdi_physics_vars + CPVI_VEL_Z,
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            NORMAL_VELOCITY_VARIABLES,
            s_cdi_physics_vars + CPVI_REF_FRAME,
            s_cdi_physics_vars + CPVI_FAR_FIELD_BC,
            s_cdi_physics_vars + CPVI_IMPORT_FLUCTUATIONS,
            TURB_SYNTH_VARIABLES,
            TURBULENCE_VARIABLES
        },
        {NULL}
    },

  /*------------------------END thermal stuff------------------------------------*/

  /*------------------------BEGIN fan model------------------------------------*/

    {CDI_PHYS_TYPE_INLINE_FAN, "Inline Fan Model", CDI_PHYSICS_IS_VOLUME, FALSE, 21, 7,
        {
            FAN_FLUID_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_R_FORCE_COEFF0,
            s_cdi_physics_vars + CPVI_R_FORCE_COEFF1,
            s_cdi_physics_vars + CPVI_R_FORCE_COEFF2,
            s_cdi_physics_vars + CPVI_R_FORCE_COEFF3,
            s_cdi_physics_vars + CPVI_U_THETA_COEFF0,
            s_cdi_physics_vars + CPVI_U_THETA_COEFF1,
            s_cdi_physics_vars + CPVI_U_THETA_COEFF2,
            s_cdi_physics_vars + CPVI_U_THETA_COEFF3,
            s_cdi_physics_vars + CPVI_POINT_X,
            s_cdi_physics_vars + CPVI_POINT_Y,
            s_cdi_physics_vars + CPVI_POINT_Z,
            s_cdi_physics_vars + CPVI_RAXIS_X,
            s_cdi_physics_vars + CPVI_RAXIS_Y,
            s_cdi_physics_vars + CPVI_RAXIS_Z,
            s_cdi_physics_vars + CPVI_GAXIS_X,
            s_cdi_physics_vars + CPVI_GAXIS_Y,
            s_cdi_physics_vars + CPVI_GAXIS_Z,
        },
        {
	  PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_LES_INLINE_FAN, "Inline Fan Model", CDI_PHYSICS_IS_VOLUME, FALSE, 21, 12,
        {
            FAN_FLUID_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_R_FORCE_COEFF0,
            s_cdi_physics_vars + CPVI_R_FORCE_COEFF1,
            s_cdi_physics_vars + CPVI_R_FORCE_COEFF2,
            s_cdi_physics_vars + CPVI_R_FORCE_COEFF3,
            s_cdi_physics_vars + CPVI_U_THETA_COEFF0,
            s_cdi_physics_vars + CPVI_U_THETA_COEFF1,
            s_cdi_physics_vars + CPVI_U_THETA_COEFF2,
            s_cdi_physics_vars + CPVI_U_THETA_COEFF3,
            s_cdi_physics_vars + CPVI_POINT_X,
            s_cdi_physics_vars + CPVI_POINT_Y,
            s_cdi_physics_vars + CPVI_POINT_Z,
            s_cdi_physics_vars + CPVI_RAXIS_X,
            s_cdi_physics_vars + CPVI_RAXIS_Y,
            s_cdi_physics_vars + CPVI_RAXIS_Z,
            s_cdi_physics_vars + CPVI_GAXIS_X,
            s_cdi_physics_vars + CPVI_GAXIS_Y,
            s_cdi_physics_vars + CPVI_GAXIS_Z,
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_INLINE_TABLE_FAN, "Inline Fan Model via Table", CDI_PHYSICS_IS_VOLUME, FALSE, 12, 7,
        {
            FAN_FLUID_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_DENSITY_T0,
            s_cdi_physics_vars + CPVI_FAN_LENGTH,
            s_cdi_physics_vars + CPVI_TANG_VEL_MODEL,
            s_cdi_physics_vars + CPVI_FAN_RADIUS,
            s_cdi_physics_vars + CPVI_FAN_HUB_RADIUS,
            s_cdi_physics_vars + CPVI_ANG_VEL,
            s_cdi_physics_vars + CPVI_TANG_VEL_COEFF0,
            s_cdi_physics_vars + CPVI_TANG_VEL_THRUST_COEFF,
        },
        {
	  PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_LES_INLINE_TABLE_FAN, "Inline Fan Model via Table", CDI_PHYSICS_IS_VOLUME, FALSE, 12, 12,
        {
            FAN_FLUID_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_DENSITY_T0,
            s_cdi_physics_vars + CPVI_FAN_LENGTH,
            s_cdi_physics_vars + CPVI_TANG_VEL_MODEL,
            s_cdi_physics_vars + CPVI_FAN_RADIUS,
            s_cdi_physics_vars + CPVI_FAN_HUB_RADIUS,
            s_cdi_physics_vars + CPVI_ANG_VEL,
            s_cdi_physics_vars + CPVI_TANG_VEL_COEFF0,
            s_cdi_physics_vars + CPVI_TANG_VEL_THRUST_COEFF,
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },

    /*------------------------END fan model------------------------------------*/

    /* ----------------------Acoustic Porous Media-----------------------------*/
    {CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID, "Acoustically Porous Adiabatic Fluid", CDI_PHYSICS_IS_VOLUME, FALSE, 26, 7,
        {
            POROUS_MEDIA_VARIABLES,
            ACOUSTIC_POROUS_MEDIA_VARIABLES
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID, "Acoustically Porous Adiabatic Fluid LES", CDI_PHYSICS_IS_VOLUME, FALSE, 26, 12,
        {
            POROUS_MEDIA_VARIABLES,
            ACOUSTIC_POROUS_MEDIA_VARIABLES
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },

#ifdef APM_WITH_HEAT_TRANSFER
    {CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID_FIXED_TEMP, "Acoustically Porous Fluid Fixed Temp", CDI_PHYSICS_IS_VOLUME, FALSE, 27, 7,
        {
            POROUS_MEDIA_VARIABLES,
            ACOUSTIC_POROUS_MEDIA_VARIABLES,
            s_cdi_physics_vars + CPVI_POROUS_REF_TEMP
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID_FIXED_TEMP, "Acoustically Porous Fluid LES Fixed Temp", CDI_PHYSICS_IS_VOLUME, FALSE, 27, 12,
        {
            POROUS_MEDIA_VARIABLES,
            ACOUSTIC_POROUS_MEDIA_VARIABLES,
            s_cdi_physics_vars + CPVI_POROUS_REF_TEMP
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID_THERMAL, "Acoustically Porous Fluid with Heat Transfer", CDI_PHYSICS_IS_VOLUME, FALSE, 33, 7,
        {
            POROUS_MEDIA_VARIABLES,
            ACOUSTIC_POROUS_MEDIA_VARIABLES,
            POROUS_MEDIA_THERMAL_VARIABLES
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES
        }
    },

    {CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID_THERMAL, "Acoustically Porous Fluid with Heat Transfer LES ", CDI_PHYSICS_IS_VOLUME, FALSE, 33, 12,
        {
            POROUS_MEDIA_VARIABLES,
            ACOUSTIC_POROUS_MEDIA_VARIABLES,
            POROUS_MEDIA_THERMAL_VARIABLES
        },
        {
            PVT_VARIABLES,
            HUMIDITY_CONTENT_VARIABLES,
            TURBULENCE_VARIABLES
        }
    },
#endif
#ifdef APM_OBSOLETE_IMPLEMENTATION
    {CDI_PHYS_TYPE_APM_FLUID_INTERFACE, "Acoustic Porous/Fluid Slip Interface", CDI_PHYSICS_IS_TRUE_SURFACE, FALSE, 1, 0, 
        {
            s_cdi_physics_vars + CPVI_FLUID_DESC_INDEX
        },
        {NULL}
    },
#endif

    {CDI_PHYS_TYPE_APM_SLIP95, "APM-Fluid Slip Interface", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 10, 0,
        {
            SLIP_VARIABLE_BASE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_APM_TRUE_NOSLIP, "APM-Fluid No Slip Interface", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 5, 0,
        {
            NO_SLIP_VARIABLE_BASE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_APM_LINEAR_SLIP, "APM-Fluid Linear Slip Interface", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 14, 0,
        {
            SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_APM_LINEAR_NOSLIP, "APM-Fluid Linear No Slip Interface", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 9, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_APM_ANGULAR_SLIP, "APM-Fluid Angular Slip Interface", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 19, 0,
        {
            SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE
        },
        {NULL}
    },

    {CDI_PHYS_TYPE_APM_ANGULAR_NOSLIP, "APM-Fluid Angular No Slip Interface", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 14, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE
        },
        {NULL}
    },

    /* ------------------End Acoustic Porous Media-----------------------------*/

    /* -------------------Conduction physics types-----------------------------*/
    { CDI_PHYS_TYPE_ADIABATIC, "Adiabatic boundary", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 2, 0,
        {
            CONDUCTION_BOUNDARY_VARIABLE_BASE
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_COUPLED_THERMAL, "Coupled thermal boundary", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 2, 0,
        {
            CONDUCTION_BOUNDARY_VARIABLE_BASE
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_FIXED_TEMP, "Fixed temp boundary", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 3, 0,
        {
            CONDUCTION_BOUNDARY_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_TEMP
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_FIXED_HEAT_FLUX, "Fixed heat flux boundary", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 4, 0,
        {
            CONDUCTION_BOUNDARY_VARIABLE_BASE,
            s_cdi_physics_vars + CPVI_WALL_HEAT_FLUX,  // Flow/solid interface: Heat Flux into Solid | Contact: Heat Flux into Geometry 1 
            s_cdi_physics_vars + CPVI_HEAT_FLUX_2     // Flow/solid interface: Heat Flux into Fluid | Contact: Heat Flux into Geometry 2 
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP, "Fixed htc ambient temperature boundary", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 4, 0,
        {
            CONDUCTION_BOUNDARY_VARIABLE_BASE,
            CONDUCTION_FIXED_HTC_AMBIENT_TEMP_VARIABLES
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_THERMAL_RESIST, "Thermal resistance boundary", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 6, 0,
      {
          CONDUCTION_BOUNDARY_VARIABLE_BASE,
          THERMAL_RESIST_VARIABLES
      },
      {NULL}
    },

    { CDI_PHYS_TYPE_CONTACT_RESISTANCE, "Contact resistance", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 1, 0,
        {
            // This type does not have a shell configuration
            s_cdi_physics_vars + CPVI_CONTACT_RESISTANCE
        },
        {NULL}
    },

    // All the types with a suffix of "_COUPLED" have the exact same set of parameters as the corresponding without "_COUPLED"
    { CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED, "NoSlip Coupled", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 6, 0,
        {
            NO_SLIP_VARIABLE_BASE
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_SLIP95_COUPLED, "Slip95 Coupled", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 11, 0,
        {
            SLIP_VARIABLE_BASE
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED, "LinearSlip Coupled", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 15, 0,
        {
            SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED, "LinearNoSlip Coupled", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 10, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            VELOCITY_VARIABLES
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED, "AngularSlip Coupled", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 20, 0,
        {
            SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED, "AngularNoSlip Coupled", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 15, 0,
        {
            NO_SLIP_VARIABLE_BASE,
            ROTATION_VARIABLES,
            s_cdi_physics_vars + CPVI_ROTATION_VIA_REF_FRAME,
            s_cdi_physics_vars + CPVI_DEFORMING_TIRE
        },
        {NULL}
    },

    { CDI_PHYS_TYPE_FIXED_HEAT_FLOW, "Fixed heat flow boundary", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 4, 0,
          {
              CONDUCTION_BOUNDARY_VARIABLE_BASE,
              s_cdi_physics_vars + CPVI_WALL_HEAT_FLOW_IN,
              s_cdi_physics_vars + CPVI_WALL_HEAT_FLOW_OUT
          },
          {NULL}
      },
    /* -----------------End Conduction physics types---------------------------*/

    /*------------------------5G physics types --------------------------------*/

    ///////////////////
    // 5G Fluid
    ///////////////////
    {CDI_PHYS_TYPE_5G_FLUID, "Fluid", CDI_PHYSICS_IS_VOLUME, FALSE, 2, 12,
        {
            s_cdi_physics_vars + CPVI_COORD_SYSTEM,
            s_cdi_physics_vars + CPVI_REF_FRAME
        },
        {
            MME_VARIABLES_5G
        }
    },

    ///////////////////
    // 5G Mass Flux Inlet
    ///////////////////
    {CDI_PHYS_TYPE_5G_MASS_FLUX_BC, "MassFlux", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 11, 0,
        {
            NON_REFLECTIVE_IO_VARIABLE_BASE_5G,
            DIRECTION_VARIABLES,
            s_cdi_physics_vars + CPVI_COMP0_MASS_FLUX,
            s_cdi_physics_vars + CPVI_COMP1_MASS_FLUX,
            s_cdi_physics_vars + CPVI_STATE_OF_MATTER,
            s_cdi_physics_vars + CPVI_BC_TYPE
        },
        {NULL}
    },

    ///////////////////
    // 5G Pressure Outlet
    ///////////////////
    {CDI_PHYS_TYPE_5G_PRESS_BC, "StaticPressure", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 8, 0,
        {
             PRESSURE_VARIABLE_BASE_5G
        },
        {NULL}
    },

    ///////////////////
    // 5G Pressure & Velocity Inlet
    ///////////////////
    {CDI_PHYS_TYPE_5G_PRESS_VEL_BC, "PressureVelocity", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 12, 0,
        {
            PRESSURE_VARIABLE_BASE_5G,
            VELOCITY_VARIABLES
        },
        {NULL}
    },

    ///////////////////
    // 5G Pressure Fixed Direction Outlet
    ///////////////////
    {CDI_PHYS_TYPE_5G_PRESS_FIXED_DIR_BC, "StaticPressure", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 12, 0,
        {
            PRESSURE_VARIABLE_BASE_5G,
            DIRECTION_VARIABLES
        },
        {NULL}
    },

    ///////////////////
    // 5G Standard Wall
    ///////////////////
    {CDI_PHYS_TYPE_5G_DNS_WALL, "StandardWall", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 5, 0,
        {
            STANDARD_WALL_5G
        },
        {NULL}
    },

    ///////////////////
    // 5G Sliding Wall
    ///////////////////
    {CDI_PHYS_TYPE_5G_DNS_LINEAR_WALL, "SlidingWall", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 9, 0,
        {
            STANDARD_WALL_5G,
            VELOCITY_VARIABLES
        },
        {NULL}
    },

    ///////////////////
    // 5G Rotating Wall
    ///////////////////
    {CDI_PHYS_TYPE_5G_DNS_ANGULAR_WALL, "RotatingWall", CDI_PHYSICS_IS_TRUE_SURFACE, TRUE, 12, 0,
        {
            STANDARD_WALL_5G,
            ROTATION_VARIABLES
        },
        {NULL}
    },

    /*------------------------END 5G physics types ----------------------------*/

    // The following entry must be last; it's used to indicate the end of the list.
    {CDI_PHYS_TYPE_NONE, "[none]", CDI_PHYSICS_IS_UNDEFINED, FALSE, 0, 0, {NULL}, {NULL}}
};

CDI_PHYS_TYPE_DESCRIPTOR cdi_lookup_physics(cdiINT32 cdi_physics_type)
{
  for (asINT32 i=0;
       cdi_physics_table[i].cdi_physics_type != CDI_PHYS_TYPE_NONE;
       i++) {
    if (cdi_physics_table[i].cdi_physics_type == cdi_physics_type)
      return cdi_physics_table + i;
  }
  
  return NULL;
}

CDI_PHYSICS_CLASS cdi_physics_class(cdiINT32 cdi_physics_type)
{
    CDI_PHYS_TYPE_DESCRIPTOR ptd = cdi_lookup_physics(cdi_physics_type);

    return ptd ? ptd->physics_class : CDI_PHYSICS_IS_UNDEFINED;
}

cdiBOOLEAN cdi_physics_requires_normal_p(cdiINT32 cdi_physics_type)
{
    CDI_PHYS_TYPE_DESCRIPTOR ptd = cdi_lookup_physics(cdi_physics_type);

    return ptd ? ptd->requires_normal_p : FALSE;
}


//---------------------------------------
//---- sCDI_UDS_PHYS_TYPE_DESCRIPTOR ----
//---------------------------------------
enum UDS_PHYSICS_VARS_INDEX
{
   UPVI_FLUID_SOURCE_TERM,
   UPVI_FLUID_INITIAL_CONDITION,
   UPVI_FLUID_DIFFUSIVITY,
   UPVI_BC_WALL_SCALAR_BOUNDARY_TYPE,
   UPVI_BC_INLET_WALL_VALUE,
   UPVI_BC_WALL_SCALAR_FLUX,
   UPVI_BC_INLET_CONTENT_VIA,
   UPVI_BC_INLET_NO_SCALAR_DIFFUSION,
   UPVI_BC_INLET_MEAN_VALUE,
   UPVI_BC_OUTLET_REVERSE_FLOW_COMPOSITION,
   UPVI_MAX
};

static const sCDI_UDS_PHYS_TYPE_DESCRIPTOR::sDP_VAR s_uds_physics_vars[CPVI_MAX] =
{
    {CDI_VAR_ID_UDS_FLUID_SOURCE_TERM, "fluid uds source term", "SourceTerm"},
    {CDI_VAR_ID_UDS_FLUID_INITIAL_CONDITION, "fluid uds initial value", "Dimensionless"},
    {CDI_VAR_ID_UDS_FLUID_DIFFUSIVITY, "fluid effective diffusivity", "KinematicViscosity"},
    {CDI_VAR_ID_UDS_BC_WALL_SCALAR_BOUNDARY_TYPE, "wall uds boundary type", "Dimensionless"},
    {CDI_VAR_ID_UDS_BC_INLET_WALL_VALUE, "surface uds value", "Dimensionless"},
    {CDI_VAR_ID_UDS_BC_WALL_SCALAR_FLUX, "surface uds flux", "MassFlux"},
    {CDI_VAR_ID_UDS_BC_INLET_CONTENT_VIA, "inlet via", "Dimensionless"},
    {CDI_VAR_ID_UDS_BC_INLET_NO_SCALAR_DIFFUSION, "inlet no uds diffusion", "Dimensionless"},
    {CDI_VAR_ID_UDS_BC_INLET_MEAN_VALUE, "inlet mean uds value", "Dimensionless"},
    {CDI_VAR_ID_UDS_BC_OUTLET_REVERSE_FLOW_COMPOSITION, "outlet reverse flow composition", "Dimensionless"}
};


static sCDI_UDS_PHYS_TYPE_DESCRIPTOR cdi_uds_physics_table[] =
{
 {CDI_UDS_PHYS_FLUID, "UDS basic fluid", 1, 1,
     {
        s_uds_physics_vars + UPVI_FLUID_SOURCE_TERM
     },   
     { 
        s_uds_physics_vars + UPVI_FLUID_INITIAL_CONDITION
     }  
  },

  {CDI_UDS_PHYS_SPECIAL_FLUID, "UDS special fluid", 2, 1,
     {
        s_uds_physics_vars + UPVI_FLUID_SOURCE_TERM,
	s_uds_physics_vars + UPVI_FLUID_DIFFUSIVITY
     },  
     { 
        s_uds_physics_vars + UPVI_FLUID_INITIAL_CONDITION
     }  
  },

  {CDI_UDS_PHYS_WALL, "UDS wall", 3, 0,
     {
        s_uds_physics_vars + UPVI_BC_WALL_SCALAR_BOUNDARY_TYPE,
        s_uds_physics_vars + UPVI_BC_INLET_WALL_VALUE,
        s_uds_physics_vars + UPVI_BC_WALL_SCALAR_FLUX
     },        
     {NULL}
     
  },
 
  {CDI_UDS_PHYS_INLET_OUTLET, "UDS inlet or outlet", 5, 0,
     {
        s_uds_physics_vars + UPVI_BC_INLET_CONTENT_VIA,
        s_uds_physics_vars + UPVI_BC_INLET_WALL_VALUE,
	s_uds_physics_vars + UPVI_BC_INLET_NO_SCALAR_DIFFUSION,
	s_uds_physics_vars + UPVI_BC_INLET_MEAN_VALUE,
        s_uds_physics_vars + UPVI_BC_OUTLET_REVERSE_FLOW_COMPOSITION
     },   
     {NULL}  
  },

  // The following entry must be last; it's used to indicate the end of the list.
  {CDI_UDS_PHYS_TYPE_NONE, "[none]", 0, 0, {NULL}, {NULL}}
};

CDI_UDS_PHYS_TYPE_DESCRIPTOR cdi_lookup_uds_physics(cdiINT32 uds_physics_type)
{
  for (asINT32 i=0;
       cdi_uds_physics_table[i].uds_physics_type != CDI_UDS_PHYS_TYPE_NONE;
       i++) {
    if (cdi_uds_physics_table[i].uds_physics_type == uds_physics_type)
      return cdi_uds_physics_table + i;
  }
  
  return NULL;
}
 


// The following implementations of sPARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE class methofs compute 
// the equilibrium drift velocity (terminal velocity) of a particle of a certain size and density 
// falling through air of a specified density. It exists here tentativly so the same calculation 
// can be used in PowerVIZ's rain emitters.


double sPARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE::Re2_Cd(double reynolds_number) { 
  double re_squared_cd = 24.0 * (reynolds_number + 0.15 * pow(reynolds_number, 1.687)) + 
    0.42 * reynolds_number * reynolds_number / (1.0 + 42500.0 * pow(reynolds_number, -1.16)); //Expression from Clift R, Grace JR, Weber ME. Bubbles, drops, and particles. New York: Academic Press 1978.
  return re_squared_cd;
}

double sPARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE::dRe2_Cddx(double reynolds_number) { 
  double intermediate_value = 1.0 + 42500.0 * pow(reynolds_number, -1.16);
  double derivative = 24.0*(1.0 + 0.15 * 1.687 * pow(reynolds_number, 0.687)) + 
    0.42 * 2.0 * reynolds_number / intermediate_value + 
    0.42 * reynolds_number * reynolds_number * 42500.0 * 1.16 * pow(reynolds_number, -2.16) / intermediate_value / intermediate_value;
  return(derivative);
}

VOID sPARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE::construct_equilibrium_reynolds_number_lookup_table() {
  double Re = min_reynolds_number;
  double K = Re2_Cd(Re);
  while(Re < max_reynolds_number)
    {
      reynolds_number_table.push_back(Re);
      k_value_table.push_back(K);
      double Re_top = Re, Re_bot = Re, slope;
      slope = dRe2_Cddx(Re);
      while(1)
        {
          if(fabs(slope * (Re_top - Re_bot) + K - Re2_Cd(Re_top)) > K_tolerance * K)
            break;
          Re_top *= 2.0;
        }
      double Re_mid = (Re_top + Re) / 2.0;
      while(Re_top - Re > reynolds_number_tolerance * Re_bot)
        {
          if(fabs(slope * (Re_mid - Re_bot) + K - Re2_Cd(Re_mid)) > K_tolerance * K)
            Re_top = Re_mid;
          else
            Re = Re_mid;
          Re_mid = (Re_top + Re) / 2.0;
        }
      Re = Re_mid;
      K = Re2_Cd(Re);
    }
  max_reynolds_number = Re;
  max_K = K;
}

double sPARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE::lookup_equilibrium_reynolds_number(double K) {
  //bisection search
  int bot = 0, top = reynolds_number_table.size() - 1;
  int mid = (bot + top) / 2;
  
  if(K <= k_value_table[0])
    return reynolds_number_table[0];
  
  if(K >= k_value_table[top])
    return reynolds_number_table[top];
  
  while(top - bot > 1)
    {
      if(K > k_value_table[mid]) bot = mid;
      else top = mid;
      mid = (bot + top) / 2;
    }
  double r = (K - k_value_table[bot]) / (k_value_table[top] - k_value_table[bot]);
  return reynolds_number_table[bot] * (1 - r) + reynolds_number_table[top] * r;
}

double sPARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE::compute_drag_force_magnitude(double diameter, 
                                                                              double rho_air,
                                                                              double relative_velocity_magnitude, 
                                                                              double kinematic_viscosity_of_air) {
  double reference_area = M_PI * diameter * diameter / 4.0;
  double dynamic_pressure = 0.5 * rho_air * relative_velocity_magnitude * relative_velocity_magnitude;
  double reynolds_number = relative_velocity_magnitude * diameter / kinematic_viscosity_of_air;
  double drag_force_magnitude = dynamic_pressure * reference_area * compute_drag_coefficient(reynolds_number);
  return(drag_force_magnitude);
}

double sPARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE::compute_terminal_velocity_magnitude(double gravity_magnitude,
                                                                                     double rho_air,
                                                                                     double kinematic_viscosity_of_air,
                                                                                     double rho_particle,
                                                                                     double particle_diameter) {

  double K = 4.0 / 3.0 * particle_diameter * particle_diameter * particle_diameter * gravity_magnitude / (kinematic_viscosity_of_air * kinematic_viscosity_of_air) * (rho_particle / rho_air - 1.0);
  //Compute the Reynolds number that corresponds to the terminal velocity
  double Re = lookup_equilibrium_reynolds_number(K);
  double terminal_velocity_magnitude = Re * kinematic_viscosity_of_air / particle_diameter; //Convert the Reynolds number back to a terminal velocity magnitude which is always parallel to gravity.
  return terminal_velocity_magnitude;
  
#if 0    
  //using Newton iteration directly instead of lookup
  if(Re < 1e-4) Re = 1.0 / VELOCITY_TO_MKS_SCALE_FACTOR * particle_diameter / kinematic_viscosity_of_air;
  const asINT32 max_allowed_newton_itterations = 20;
  for(asINT32 newton_itteration = 0; newton_itteration < max_allowed_newton_itterations; newton_itteration++)
    {
      double fRe2_Cd = Re2_Cd(Re);
      double Fnet = fRe2_Cd - K;
      if(fabs(Fnet) / K < 1e-4) break;
      Re -= Fnet / dRe2_Cddx(Re);
    }
  return Re * kinematic_viscosity_of_air / particle_diameter; //returns the magnitude of the terminal velocity (which is parallel to gravity)
#endif
  
}
