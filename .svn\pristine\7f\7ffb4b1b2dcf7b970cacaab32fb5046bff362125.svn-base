/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("cpc.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("cpc.copyright", "78") */ 

#ifndef CDI_PRESSURE_DROP_PARSER
#define CDI_PRESSURE_DROP_PARSER

#include SCALAR_H
#include UNITS_H

#include <string>
#include <vector>
#include <map>
#include <list>

#include "cdi_cLSR_QUADRATIC_SOLVER.h"

class cPRESSURE_DROP_PARSER
{

public:
  
  typedef std::pair<dFLOAT, std::string> cUVAL;
  typedef std::pair<dFLOAT, dFLOAT> cDATA_ROW;

  cPRESSURE_DROP_PARSER(const std::string& dataStr);
  ~cPRESSURE_DROP_PARSER() {}
  BOOLEAN ParseData();
  BOOLEAN ValidateData(UNITS_DB unitsDb);
  size_t DataTableSize() const { return m_dataTable.size(); }
  cDATA_ROW GetDataRow(size_t row) const { return m_dataTable.at(row); }
  cUVAL GetParam(const std::string& paramName) const;

  // The following data are only valid after ValidateData() is invoked.
  dFLOAT GetArea() const { return m_area; }
  dFLOAT GetDepth() const { return m_depth; }
  dFLOAT GetAirTemp() const { return m_airTemp; }
  dFLOAT GetAirDensity() const { return m_airDensity; }

  std::string GetError() const { return m_error; }
  size_t GetNumWarnings() const { return m_warnings.size(); }
  std::list<std::string> GetWarnings() const { return m_warnings; }
  std::string GetLastLineParsed() const { return m_lastLineParsed; }
  int GetNumLinesParsed() const { return m_numLinesParsed; }

  BOOLEAN PressureDropEstimate(
                               dFLOAT &inertialCoef,
                               dFLOAT &viscousCoef, 
                               UNITS_DB unitsDb = NULL,
                               UNITS_UNIT velUnit = NULL,
                               UNITS_UNIT accelUnit =NULL,
                               bool fitInertialCoefOnly = false);

  dFLOAT CalculateSolvedY(dFLOAT x) const; 
  dFLOAT CalculateRSquare() const;
  dFLOAT CalculateStandardDeviation() const;
  dFLOAT CalculateAverageDeviation() const;

#if 0
  VOID GetSolver(cLSR_QUADRATIC_SOLVER *s) { 
    s = &m_solver; 
  }  // Backdoor in case above API is not enough
#endif
private:

  typedef std::map<std::string, cUVAL> cPARAM_MAP;
  typedef std::vector<cDATA_ROW> cDATA_TABLE;

  cPRESSURE_DROP_PARSER();
  cPRESSURE_DROP_PARSER(const cPRESSURE_DROP_PARSER&);
  cPRESSURE_DROP_PARSER& operator=(const cPRESSURE_DROP_PARSER&);

  static std::string& StripComments(std::string& line, char commentChar = '#');
  BOOLEAN ParseParamLine(std::string line);
  BOOLEAN ParseDataKeywords(std::string line);
  BOOLEAN ParseDataUnits(std::string line);
  BOOLEAN ParseDataRow(std::string line);
  BOOLEAN ParamExists(const std::string& paramName) const;

  const std::string m_dataStr;
  std::string m_lastLineParsed;
  int m_numLinesParsed;
  cPARAM_MAP m_params;
  std::string m_leftColKeyword;
  std::string m_rightColKeyword;
  std::string m_leftColUnit;
  std::string m_rightColUnit;
  cDATA_TABLE m_dataTable;

  // The following data are only valid after ValidateData() is invoked.
  dFLOAT m_area;
  dFLOAT m_depth;
  dFLOAT m_airTemp;
  dFLOAT m_airDensity;

  cLSR_QUADRATIC_SOLVER m_solver;

  std::string m_error;
  std::list<std::string> m_warnings;

};

#endif
