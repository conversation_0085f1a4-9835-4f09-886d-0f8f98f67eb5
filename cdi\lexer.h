/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Unpublished Work Copyright (C) 1995, 1994 Exa Corporation.            ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129                                                      ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 
#ifndef CDI_LEXER_H_
#define CDI_LEXER_H_ 1

#include "cdi_common.h"


idFLOAT lexer_parse_dfloat (void);
asINT32 lexer_parse_int (void);
/**
 * Parse either an int or rbrace. Return true for int and false for rbrace
 */
BOOLEAN lexer_parse_int_or_rbrace(asINT32* value);
asINT64 lexer_parse_int64 (void);
STRING  lexer_parse_string (void);
std::string  lexer_parse_std_string (void);
VOID    lexer_parse_lbrace (void);
VOID    lexer_parse_rbrace (void);

// Returns true if an ID was found, false if an rbrace was found. Aborts (via
// msg_error) if neither was found.
BOOLEAN lexer_parse_n_char_id_or_rbrace (STRING result, asINT32 n_chars);

// Returns true if a string was found, false if an rbrace was found. Aborts (via
// msg_error) if neither was found.
BOOLEAN lexer_parse_string_or_rbrace (STRING* result);

// Returns true if a floating point number was found, false if an rbrace was found.
// Aborts (via msg_error) if neither was found.
BOOLEAN lexer_parse_dfloat_or_rbrace (idFLOAT &result);

VOID lexer_parse_specific_id (CIO_CCCC desired_id);
#define lexer_parse_hex lexer_parse_int


VOID lexer_set_lineno (asINT32 new_lineno);
asINT32 lexer_get_lineno (void);

VOID lexer_set_scan_string(STRING buffer);

typedef asINT32 LEXER_TOKEN_TYPE;

#define LEXER_TOKEN_IS_ID      1
#define LEXER_TOKEN_IS_NUMBER  2
#define LEXER_TOKEN_IS_LBRACE  3
#define LEXER_TOKEN_IS_RBRACE  4
#define LEXER_TOKEN_IS_STRING  5
#define LEXER_TOKEN_IS_BADCHAR 6


#endif
