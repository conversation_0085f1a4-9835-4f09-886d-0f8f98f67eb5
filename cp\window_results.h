/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Common definitions for the license process
 *--------------------------------------------------------------------------*/

#ifndef __CP_WRITE_SRI_WINDOW_H
#define __CP_WRITE_SRI_WINDOW_H

#include "common.h"

VOID initialize_sri_file_params_grf_info(SRI_FILE_PARAMS sri_file_params, BOOLEAN is_probe);
SRI_STATUS write_sri_meas_file_polyline_vertices(SRI_FILE sri_file);
SRI_STATUS write_sri_meas_file_grf_info(SRI_FILE sri_file, GRF_MEAS_FRAME_SP_TO_CP_MSG grf_info);

/* For 5G */
VOID get_5g_var_desc(sriINT id, 
		     SRI_CUSTOM_VAR_ID_HELPER cvid_helper,
		     SRI_VARIABLE_DESC desc);

/* For LB_UDS */
VOID get_uds_var_desc(sriINT id, 
		      SRI_CUSTOM_VAR_ID_HELPER cvid_helper,
		      SRI_VARIABLE_DESC desc,
		      BOOLEAN need_units = TRUE);


#endif
