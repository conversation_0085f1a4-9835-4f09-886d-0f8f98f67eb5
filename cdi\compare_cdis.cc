/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("fx1.copyright", "78") */
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */
#include "cdi_common.h"
#include "cdi_internal.h"
#include "cdi_encrypted_io.h"
#include <iostream>
#include <vector>
#include <stdio.h>
#include <set>

#include ARG_HELPER_H
#include CCUTILS_H
#include PLATFORM_H
#include CIPHER_H

static uINT16 uDUMP_CDI_DFLOAT_DIG = 0;

static STRING quotify_and_slashify_string(cSTRING src);
struct vertStruct {
	int vertIND;
	double x_cord;
	double y_cord;
	double z_cord;
};

struct facetStruct {
	int facetIND;
	int n_vertices;
	int *vertices;
};

struct shellStruct {
	int shellIND;
	int n_factes;
	int *facets;
};

struct bodyStruct {
	int bodyIND;
	int n_Shells;
	int *shells;
};

std::vector<vertStruct*> vertVec1, vertVec2;
std::vector <facetStruct*> facetVec1, facetVec2;
void print_ptge(CDI_INFO cdi_info);
void print_undb(CDI_INFO cdi_info);
void print_eqns(CDI_INFO cdi_info);
void print_tabl(CDI_INFO cdi_info);
void print_face(CDI_INFO cdi_info, int depth);
void print_mdlv(CDI_INFO cdi_info);
void print_rgnn(const sCDI_RGNN& rgnn, int depth);
void print_psmv(CDI_INFO cdi_info, int depth);
void print_psmv(CDI_INFO cdi_info, const sCDI_PSMV& psmv, int depth);
void print_bbox(CDI_INFO cdi_info);
void print_cmnt(CDI_INFO cdi_info);
void print_audt(CDI_INFO cdi_info);
void print_null(CDI_INFO cdi_info);
void print_name(CDI_INFO cdi_info);
void print_offs(CDI_INFO cdi_info);
void print_prgn(CDI_INFO cdi_info);
void print_rgpn(CDI_INFO cdi_info, int depth);
void print_vrtx(CDI_INFO cdi_info, int depth, std::vector<vertStruct*> &vertVec);
void print_indent(int depth);
CIO_ERRCODE dump_chunk(CDI_INFO cdi_info, int depth, std::vector<vertStruct*> &vertVec, std::vector<facetStruct*> &facetVec, std::string filtersString = "");
void print_rgdp(sCDI_RGDP& rgdp, CDI_INFO cdi_info, int depth);
void print_rgdp(CDI_INFO cdi_info, int depth);
void print_wpdt(CDI_INFO cdi_info, int depth);
void print_tire(CDI_INFO cdi_info, int depth);

static int Regn_number = 0;
static int Regn_Def_number = 0;
static int Phys_number = 0;
static int Fact_number = 0;
static int Vrtx_number = 0;
static int Edge_number = 0;

struct DumpCDIInputArgs {
	DumpCDIInputArgs()
		: roundoff_cdi(false), no_audt_mods(false),
		help_flag(false), cmd_name(NULL)
	{ }
	bool roundoff_cdi, no_audt_mods, help_flag;
	std::vector<std::string> filenames;
	std::string runDirString, runDirReplaceString, filtersString;
	STRING cmd_name;
} inArgs;

bool ParseCommandLineArgs(int argc, char *argv[],
	DumpCDIInputArgs& args) {
	if (!args.cmd_name || argc == 0 || !argv)
		return false;
	bool versions_only_flag = false;
	cARG_HELPER cmndLineArgHelpr;
	args.help_flag = false;
	args.roundoff_cdi = false;
	args.no_audt_mods = false;

	cmndLineArgHelpr.new_flag(0x00, "help", "print this help message", args.help_flag);
	cmndLineArgHelpr.new_flag('r', "roundoff", "Round Off CDI", args.roundoff_cdi);
	cmndLineArgHelpr.new_flag('n', "no_audt_mods", "No audt chunk mods", args.no_audt_mods);
	cmndLineArgHelpr.new_named_string('f', "filter_chunks", "\"chunks_to_filter_in_quotes_comma_separated\"",
		"Chunks to be filtered from the output. Ex: \"undb,cmnd\"",
		args.filtersString);
	cmndLineArgHelpr.new_named_string('d', "replace_rundir",
		"\"runDir,replaceDir\"",
		"Replace directory Dir1 with Dir2 in the output. Ex: \"Dir1,Dir2\"",
		args.runDirString);
	cmndLineArgHelpr.set_string_vector("files", "<CDI filename>", args.filenames, true);
	cmndLineArgHelpr.set_description("Print a given CDI file to console.");
	{
		std::string versionDesc = EXA_STR::Sprintf("print the version of %s", argv[0]);
		cmndLineArgHelpr.new_flag(0x00, "version", versionDesc.c_str(), versions_only_flag);
	}

	cmndLineArgHelpr.set_author("Exa Corp., http://www.exa.com");
	cmndLineArgHelpr.set_name(args.cmd_name);
	{
		std::string usageString("Usage: ");
		usageString += args.cmd_name;
		usageString += " [--no_audt_mods] [options] <cdi_filename>";
		cmndLineArgHelpr.set_usage(usageString.c_str());
	}

	{
		const char *distname = platform_exa_distname();
		// HD for executable in utest dist
		if (!distname || strlen(distname) == 0)
			distname = platform_getenv("EXA_UTEST_DIST");
		if (!distname || strlen(distname) == 0)
			return false;
		char *buf = EXA_MALLOC_ARRAY(char, strlen(CDI_VERSION) + (distname ? strlen(distname) : 0) + 4);
		sprintf(buf, "%s." CDI_VERSION, distname ? distname : "empty");
		cmndLineArgHelpr.set_version(buf);
	}

	//  cmndLineArgHelpr.set_build_date(EXA_BUILD_DATE);
	cmndLineArgHelpr.process(argc, argv);
	if (args.help_flag) {
		cmndLineArgHelpr.write_usage(stdout, 26);
		return false;
	}
	if (versions_only_flag) {
		cmndLineArgHelpr.write_version(stdout);
		return false;
	}

	return true;
}

BOOLEAN print_encoded64_chunk(CDI_INFO cdi_info, CIO_CCCC cccc, int depth = -1)
{
	BOOLEAN dump_in_clear = FALSE;

#ifdef ENCRYPT_OFF
	// Always dump in clear for do_not_release dump
	dump_in_clear = TRUE;
#endif

	if (dump_in_clear || !cdi_is_chunk_encrypted(cdi_info, cccc))
		return FALSE;

	// depth is -1 for leaf chunks (no children) such as CVDP
	bool onOneLine = (depth == -1);

	std::string buffer;
	cdi_read_chunk_and_encode64(cdi_info, buffer);
	printf(" { ");
	if (!onOneLine)
		printf("\n");

	printf("\"%s\"", buffer.c_str());

	if (!onOneLine) {
		printf("\n");
		print_indent(depth);
	}
	printf("}\n");

	return TRUE;
}
bool hasEqualGeomProp(int vertID1, int vertID2)
{
	bool bRet = false;
	if (vertID1 < 0 || vertID2 < 0 || vertID1 >= vertVec1.size() || vertID2 >= vertVec2.size())
		return bRet;
	//get the vertex from the corressponding vertex structures
	vertStruct *v1 = vertVec1[vertID1];
	vertStruct *v2 = vertVec2[vertID2];
	double xDiff = v1->x_cord - v2->x_cord;
	double yDiff = v1->y_cord - v2->y_cord;
	double zDiff = v1->z_cord - v2->z_cord;
	double distSquare = xDiff * xDiff + yDiff * yDiff + zDiff * zDiff;
	if (distSquare < 1.0e-16)
		bRet = true;
	return bRet;
}

bool bIsAlreadyVisitedvert(std::vector<int> &visitedVerts, int &vertID2)
{
	bool bRet = false;
	int size = visitedVerts.size();
	for (int i = 0; i < size; i++)
	{
		if (visitedVerts[i] == vertID2)
			return true;
	}
	return bRet;
}

bool bIsAlreadyVisitedfacet(std::vector<int> &visitedFacetId, int &facetIND)
{
	bool bRet = false;
	int size = visitedFacetId.size();
	for (int i = 0; i<size; i++)
	{
		if (visitedFacetId[i] == facetIND)
			return true;
	}
	return bRet;
}

int compareFacets(bool &bHasQuads)
{
	//facetVec1, facetVec2;
	//vertVec1, vertVec1;
	//first check number of the facets in both the case files
	std::vector<int> visitedFacetId;
	int numOffacet1 = 0, numOffacet2 = 0;
	numOffacet1 = facetVec1.size();
	numOffacet2 = facetVec2.size();

	int numOfVerts1 = vertVec1.size();
	int numOfVerts2 = vertVec2.size();

	if (numOfVerts1 != numOfVerts2 && (numOffacet1 == 0 || numOffacet2 == 0 || numOffacet1 != numOffacet2))
		return -1;
	int numOfFacetMatch = 0;
	for (int i = 0; i < numOffacet1; i++) {
		bool bFound = false;
		facetStruct* facet1 = facetVec1[i];
		for (int j = 0; j < numOffacet2; j++) {
			facetStruct* facet2 = facetVec2[j];
			int numOfFacetVerts1 = facet1->n_vertices;
			int numOfFacetVerts2 = facet2->n_vertices;
			if (bHasQuads == false && numOfFacetVerts1 != numOfFacetVerts2)//both the case files are not identical
			{
				bHasQuads = true;// continue;
				break;
			}
			if (numOfFacetVerts1 == numOfFacetVerts2)//facets have same shape
			{
			int numOfVerMatch = 0;
				for (int k1 = 0; k1 < numOfFacetVerts1; k1++) {
				int vertID1 = facet1->vertices[k1];
					for (int k2 = 0; k2 < numOfFacetVerts2; k2++) {
					int vertID2 = facet2->vertices[k2];
					//measure the distance between the two vertices
					if (hasEqualGeomProp(vertID1, vertID2)) {
						numOfVerMatch++;
					}
				}
			}
			if (numOfVerMatch == numOfVerts1) {
				numOfFacetMatch++;
				break;
			}
		}
	}
		if (bHasQuads == true)
			break;
	}
	if (bHasQuads == false && numOfFacetMatch != numOffacet1)
		return -1;
	if (bHasQuads)//facets have different shape
	{
		if (numOfVerts1 != numOfVerts2)
			return -1;
		int numOfVerMatch = 0;
		for (int k1 = 0; k1 < vertVec1.size(); k1++) {
		  int vertID1 = k1;
	      for (int k2 = 0; k2 < vertVec2.size(); k2++) {
			int vertID2 = k2;
			//measure the distance between the two vertices
			if (hasEqualGeomProp(vertID1, vertID2)) {
			  numOfVerMatch++;
			  break;
			}
		  }
		}
		if (numOfVerMatch == vertVec1.size())
		  return 0;
		else
		return -1;
	}
	//for (int i = 0 ; i < numOffacet1; i++) {
	//	facetStruct* facet1 = facetVec1[i];
	//	facetStruct* facet2 = facetVec2[i];
	//	int numOfVerts1 = facet1->n_vertices;
	//	int numOfVerts2 = facet2->n_vertices;
	//	if (numOfVerts1 != numOfVerts2)//both the case files are not identical
	//		return -1;
	//	for (int j = 0; j < numOfVerts1; j++) {
	//		int vertID1 = facet1->vertices[j];
	//		int vertID2 = facet2->vertices[j];
	//		//there is possibility that the vert IDs from both the case files might be different, so 
	//		//instead of the vert IDs, check distance between the two vertices from the two facets
	//		//In both the case files, the facet under consideration should have equal geom properties
	//		//measure the distance between the two vertices
	//		if (!hasEqualGeomProp(vertID1, vertID2))
	//			return -1;
	//	}
	//}
	return 0;
}

//#include <unistd.h> 

int main(int argc, char *argv[])
{
#if _MSC_VER >= 1400 && _MSC_VER < 1900
	_set_output_format(_TWO_DIGIT_EXPONENT);
#endif

	//usleep(10000000);

	// Handy pause for attaching from Visual Studio
	/*clock_t endWait = clock() + 20 * CLOCKS_PER_SEC;
	while (clock() < endWait) {}*/

	CDI_INFO cdi_info1 = NULL, cdi_info2 = NULL;
	asINT32 error_code;

	/* Standard debug preamble */
	platform_exa_debug_pfx(&argc, argv);

	if (argc < 3)//No proper arguments are provided, The num of arguments should be = 3
	{
		printf("Provide two cdi files for facet data comparison\n");
		exit(0);
	}

	if (!(inArgs.cmd_name = getenv("EXA_CURRENT_CMD"))) {
		inArgs.cmd_name = argv[0];
	};

	if (!ParseCommandLineArgs(argc, argv, inArgs))
		exit(0);

	std::string fileName1 = !inArgs.filenames.empty() ? inArgs.filenames[0] : "";
	std::string fileName2 = !inArgs.filenames.empty() ? inArgs.filenames[1] : "";

	//Parse run directory
	if (!inArgs.runDirString.empty()) {
		size_t delimPos = inArgs.runDirString.find(",");
		if (delimPos == std::string::npos || delimPos == 0) {
			msg_error("Invalid replace_rundir format. Required format -d \"Directory1,Directory2 \"");
			exit(1);
		}
		inArgs.runDirReplaceString = delimPos + 1 == inArgs.runDirString.size() ?
			"" : inArgs.runDirString.substr(delimPos + 1);
		inArgs.runDirString = inArgs.runDirString.substr(0, delimPos);
	}

	// parse the first filename and other arguments
	if (!fileName1.empty()) {
		if (inArgs.roundoff_cdi)
			uDUMP_CDI_DFLOAT_DIG = 9;
		else
			uDUMP_CDI_DFLOAT_DIG = DFLOAT_DIG;

		cdi_info1 = cdi_open_for_read(fileName1.c_str(), &error_code);

		if (cdi_info1 == NULL) {
			msg_error("Unable to open cdi-type file named %s, %s", fileName1.c_str(),
				cdi_get_error_state_string(error_code));
			msg_error("Usage: %s [--no_audt_mods] <cdi_filename>", inArgs.cmd_name);
			exit(1);
		}
	}
	else {
		msg_error("Usage: %s [--no_audt_mods] <filename>.cdi", inArgs.cmd_name);
		exit(1);
	}

	if (!fileName2.empty()) {
		if (inArgs.roundoff_cdi)
			uDUMP_CDI_DFLOAT_DIG = 9;
		else
			uDUMP_CDI_DFLOAT_DIG = DFLOAT_DIG;

		cdi_info2 = cdi_open_for_read(fileName2.c_str(), &error_code);

		if (cdi_info2 == NULL) {
			msg_error("Unable to open cdi-type file named %s, %s", fileName2.c_str(),
				cdi_get_error_state_string(error_code));
			msg_error("Usage: %s [--no_audt_mods] <cdi_filename>", inArgs.cmd_name);
			exit(1);
		}
	}
	else {
		msg_error("Usage: %s [--no_audt_mods] <filename>.cdi", inArgs.cmd_name);
		exit(1);
	}

	if (CDI_VERSION_NEWER(cdi_major_version(cdi_info1), cdi_minor_version(cdi_info1))) {
		msg_error("This CDI file uses version %d.%d of the CDI format. This version\n"
			"of dump_cdi only works with CDI file versions %d.%d and earlier.\n",
			cdi_major_version(cdi_info1), cdi_minor_version(cdi_info1),
			CDI_MAJOR_VERSION, CDI_MINOR_VERSION);
		exit(1);
	}

	//	printf("xcdi {\n  %5d %5d\n  case {\n",
	//		cdi_major_version(cdi_info1), cdi_minor_version(cdi_info1));

	if (CDI_VERSION_NEWER(cdi_major_version(cdi_info2), cdi_minor_version(cdi_info2))) {
		msg_error("This CDI file uses version %d.%d of the CDI format. This version\n"
			"of dump_cdi only works with CDI file versions %d.%d and earlier.\n",
			cdi_major_version(cdi_info2), cdi_minor_version(cdi_info2),
			CDI_MAJOR_VERSION, CDI_MINOR_VERSION);
		exit(1);
	}

	//	printf("xcdi {\n  %5d %5d\n  case {\n",
	//		cdi_major_version(cdi_info2), cdi_minor_version(cdi_info2));

		// Traversing can be done using for
	auto count1 = cio_get_count(cdi_info1->cio_info);
	{
		for (int i = 0; i < count1; ++i) {
			dump_chunk(cdi_info1, 2, vertVec1, facetVec1, inArgs.filtersString);
		}
	}
	Regn_number = 0;
	Vrtx_number = 0;
	Fact_number = 0;
	// Traversing can be done using for
	auto count2 = cio_get_count(cdi_info2->cio_info);
	{
		for (int i = 0; i < count2; ++i) {
			dump_chunk(cdi_info2, 2, vertVec2, facetVec2, inArgs.filtersString);
		}
	}

#if 0
	// Traversing could also be done using while
	int numChunks = 1;
	CIO_ERRCODE error = CIO_ERR_SUCCESS;
	do {
		error = dump_chunk(cdi_info, 2, vertVec1, facetVec1, inArgs.filtersString);
		if (error != CIO_ERR_FAIL) {
			error = dump_chunk(cdi_info, 2, vertVec2, facetVec2, inArgs.filtersString);
        }
	} while (error != CIO_ERR_FAIL);
#endif
	bool bHasQuads = false;
	int retVal = compareFacets(bHasQuads);
	switch (retVal)
	{
	case -1: 
	  if(bHasQuads) 
		printf("\n Quad facets found. Facets have different data : cdi files have different vertexdata \n"); 
	  else
		printf("\n Facets have different data : cdi files are DIFFERENT \n"); 
	  break;
	case 0 : 
	  if (bHasQuads)
		printf("\n Quad facets found. Quad and Triangular Facets have same vertex data : cdi files are SAME \n"); 
	  else
		printf("\n Facets have same data : cdi files are SAME \n");
	  break;
	default: break;
	}
	//release memory
	for (int i = 0; i < vertVec1.size(); i++){
		vertStruct *v = vertVec1[i];
		delete v;
	}
	for (int i = 0; i < vertVec2.size(); i++) {
		vertStruct *v = vertVec2[i];
		delete v;
	}
	vertVec1.clear();
	vertVec2.clear();

	for (int i = 0; i < facetVec1.size(); i++) {
		facetStruct *f = facetVec1[i];
		delete[] f->vertices;
		delete f;
	}
	for (int i = 0; i < facetVec2.size(); i++) {
		facetStruct *f = facetVec2[i];
		delete[] f->vertices;
		delete f;
	}
	facetVec1.clear();
	facetVec2.clear();

	// release the cdi system
	cdi_close(cdi_info1);
	cdi_close(cdi_info2);

//	printf("  }\n}\n");

	return(0);
}

std::string ReplaceRunDir(const char* inFilename) {
	std::string outFilename = (inFilename ? inFilename : "");
	if (!inArgs.runDirString.empty() && !outFilename.empty()) {
		size_t foundPos = outFilename.find(inArgs.runDirString);
		if (foundPos != std::string::npos)
			outFilename.replace(foundPos,
				inArgs.runDirString.length(), inArgs.runDirReplaceString);
	}
	return outFilename;
}

template <typename T>
void print_annotated_enum(T value, const::std::string& paramDescription, int depth);


/****************************************************************\
|
| Function name:print_scct
|
| Purpose:prints scct
|
\****************************************************************/
void
print_scct(CDI_INFO cdi_info)
{
	sCDI_SCCT scct;
	cdi_read_scct(cdi_info, &scct);

	printf(" { %d %d %d } (num models, num bcs, num walls)\n",
		scct.n_models,
		scct.n_bcs,
		scct.n_walls);
}

/****************************************************************\
|
| Function name:print_cmdl
|
| Purpose:prints cmdl
|
\****************************************************************/
void
print_cmdl(CDI_INFO cdi_info, int depth)
{
	sCDI_CMDL cmdl;
	cdi_read_cmdl(cdi_info, &cmdl);
	std::string slashified_model_name = quotify_and_slashify_string(cmdl.model_name.c_str());
	STRING slashified_model_type = quotify_and_slashify_string(cmdl.model_type);
	STRING slashified_model_filename = quotify_and_slashify_string(cmdl.model_filename);
	STRING slashified_absolute_model_filename = quotify_and_slashify_string((cmdl.absolute_model_filename) ?
		ReplaceRunDir(cmdl.absolute_model_filename).c_str() : cmdl.absolute_model_filename);
	STRING slashified_results_filename = quotify_and_slashify_string(cmdl.results_filename);
	STRING slashified_absolute_results_filename = quotify_and_slashify_string(cmdl.absolute_results_filename ?
		ReplaceRunDir(cmdl.absolute_results_filename).c_str() : cmdl.absolute_results_filename);
	STRING slashified_model_length_unit = quotify_and_slashify_string(cmdl.model_length_unit);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 2)) {
		printf(" {  %s   (model name)\n", slashified_model_name.c_str());
		print_indent(depth + 4);
		printf("%s   (model type)\n", slashified_model_type);
	}
	else {
		printf(" {  %s   (model type)\n", slashified_model_type);
	}
	print_indent(depth + 4);
	printf("%d   (coupling type)\n", cmdl.coupling_type);
	print_indent(depth + 4);
	printf("%s   (model filename)\n", slashified_model_filename);
	print_indent(depth + 4);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 5)) {
		printf("%s   (absolute model filename)\n", slashified_absolute_model_filename);
		print_indent(depth + 4);
	}
	printf("%s   (results filename)\n", slashified_results_filename);
	print_indent(depth + 4);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 5)) {
		printf("%s   (absolute results filename)\n", slashified_absolute_results_filename);
		print_indent(depth + 4);
	}
	printf("%s   (model length unit)\n", slashified_model_length_unit);
	print_indent(depth + 4);

	printf("%d   (num export variables)\n", cmdl.n_export_variables);
	print_indent(depth + 4);
	ccDOTIMES(i, cmdl.n_export_variables) {
		STRING slashified_unit = quotify_and_slashify_string(cmdl.export_variable_units[i]);
		printf("%d %s  (export variable %d, type & unit)\n",
			cmdl.export_variables[i], slashified_unit, i);
		print_indent(depth + 4);
		exa_free(slashified_unit);
	}

	printf("%d   (num import variables)\n", cmdl.n_import_variables);
	print_indent(depth + 4);
	ccDOTIMES(i, cmdl.n_import_variables) {
		STRING slashified_unit = quotify_and_slashify_string(cmdl.import_variable_units[i]);

		printf("%d %s  (import variable %d, type & unit)\n",
			cmdl.import_variables[i], slashified_unit, i);
		print_indent(depth + 4);
		exa_free(slashified_unit);
	}

	// Coupling phase table is always used for CDI version >= 4.3
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 3))
	{
		printf("%d  (num of coupling phases)\n", cmdl.n_coupling_phases);
		if (cmdl.n_coupling_phases > 0) // coupling phase table is used
		{
			ccDOTIMES(i, cmdl.n_coupling_phases)
			{
				print_indent(depth + 4);
        if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23)) {
          printf("%d  %d  %d  %d  %.*g %.*g %.*g %.*g %.*g %d (phase %d:  start  period  interval  delay  therm_time_ratio  stepsize  exact_start  exact_period  exact_interval adaptive_p)\n",
            cmdl.m_coupling_phase_descs[i].start,
            cmdl.m_coupling_phase_descs[i].period,
            cmdl.m_coupling_phase_descs[i].interval,
            cmdl.m_coupling_phase_descs[i].delay,
            uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].therm_time_ratio,
            uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].stepsize,
            uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].exact_start,
            uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].exact_period,
            uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].exact_interval,
            cmdl.m_coupling_phase_descs[i].adaptive_p,
            i+1);
       } else if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 2)) {
					printf("%d  %d  %d  %d  %.*g %.*g %.*g %.*g %.*g (phase %d:  start  period  interval  delay  therm_time_ratio  stepsize  exact_start  exact_period  exact_interval)\n",
						cmdl.m_coupling_phase_descs[i].start,
						cmdl.m_coupling_phase_descs[i].period,
						cmdl.m_coupling_phase_descs[i].interval,
						cmdl.m_coupling_phase_descs[i].delay,
						uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].therm_time_ratio,
						uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].stepsize,
						uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].exact_start,
						uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].exact_period,
						uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].exact_interval,
						i + 1);
        } else if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 6)) {
					printf("%d  %d  %d  %d  %.*g %.*g %.*g %.*g (phase %d:  start  period  interval  delay  stepsize  exact_start  exact_period  exact_interval)\n",
						cmdl.m_coupling_phase_descs[i].start,
						cmdl.m_coupling_phase_descs[i].period,
						cmdl.m_coupling_phase_descs[i].interval,
						cmdl.m_coupling_phase_descs[i].delay,
						uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].stepsize,
						uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].exact_start,
						uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].exact_period,
						uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].exact_interval,
						i + 1);
        } else if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 4)) {
					printf("%d  %d  %d  %d  %.*g (phase %d:  start  period  interval  delay  stepsize)\n",
						cmdl.m_coupling_phase_descs[i].start,
						cmdl.m_coupling_phase_descs[i].period,
						cmdl.m_coupling_phase_descs[i].interval,
						cmdl.m_coupling_phase_descs[i].delay,
						uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[i].stepsize,
						i + 1);
        } else {
					printf("%d  %d  %d  %d (phase %d:  start  period  interval  delay)\n",
						cmdl.m_coupling_phase_descs[i].start,
						cmdl.m_coupling_phase_descs[i].period,
						cmdl.m_coupling_phase_descs[i].interval,
						cmdl.m_coupling_phase_descs[i].delay,
						i + 1);
				}
			}
			print_indent(depth + 4);
			printf("%d  (use end time to stop coupling)\n", cmdl.use_end_time_for_coupling);
			print_indent(depth + 4);
			printf("%d  (num iterations)\n", cmdl.num_iterations);
			print_indent(depth + 4);
			printf("%d  (end time)\n", cmdl.end_time);
			if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 4)) {
				print_annotated_enum(eCDI_POWERTHERM_CALCULATION_TYPE::Enum(cmdl.m_calculation_type),
					"calculation type", depth + 4);
				// Before 6.2, there was only one Time Ratio value (i.e. not per phase)
				// So in those cases, just write time ratio from the first phase as *the* one value.
				if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 2)) {
					print_indent(depth + 4);
					printf("%.*g (powertherm powerflow time ratio)\n", uDUMP_CDI_DFLOAT_DIG, cmdl.m_coupling_phase_descs[0].therm_time_ratio);
				}
				print_indent(depth + 4);
				printf("%.*g (powertherm start time)\n", uDUMP_CDI_DFLOAT_DIG, cmdl.m_powertherm_start_time);
				print_indent(depth + 4);
				printf("%d (use default powertherm start time)\n", cmdl.m_use_default_powertherm_start_time);
			}
 
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23)) {
        print_indent(depth+4);
        printf("%.*g (adaptive up coeff)\n", uDUMP_CDI_DFLOAT_DIG, cmdl.adaptive_up_coeff);
        print_indent(depth+4);
        printf("%.*g (gradient low)\n", uDUMP_CDI_DFLOAT_DIG, cmdl.gradient_low);
        print_indent(depth+4);
        printf("%.*g (ratio max)\n", uDUMP_CDI_DFLOAT_DIG, cmdl.ratio_max);
        print_indent(depth+4);
        printf("%.*g (gradient percentage threshold)\n", uDUMP_CDI_DFLOAT_DIG, cmdl.gradient_percentage_threshold);
        print_indent(depth+4);
        printf("%d (fix pt time)\n", cmdl.fix_pt_time_p);
        print_indent(depth+4);
        printf("%.*g (total pt time)\n", uDUMP_CDI_DFLOAT_DIG, cmdl.total_pt_duration);
		}
	}
  } else {  // CDI version <= 4.2
		printf("%d   (start time)\n", cmdl.m_coupling_phase_descs[0].start);
		print_indent(depth + 4);
		printf("%d   (num iterations)\n", cmdl.num_iterations);
		print_indent(depth + 4);
		printf("%d   (period)\n", cmdl.m_coupling_phase_descs[0].period);
		print_indent(depth + 4);
		printf("%d   (average interval)\n", cmdl.m_coupling_phase_descs[0].interval);
		print_indent(depth + 4);
		printf("%d   (delay)\n", cmdl.m_coupling_phase_descs[0].delay);
	}

	printf("  %*.*g %*.*g %*.*g %*.*g (xform)\n",
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[0][0],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[0][1],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[0][2],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[0][3]);
	//print_indent(depth+4);
	printf("  %*.*g %*.*g %*.*g %*.*g\n",
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[1][0],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[1][1],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[1][2],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[1][3]);
	//print_indent(depth+4);
	printf("  %*.*g %*.*g %*.*g %*.*g\n",
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[2][0],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[2][1],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[2][2],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[2][3]);
	//print_indent(depth+4);
	printf("  %*.*g %*.*g %*.*g %*.*g\n",
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[3][0],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[3][1],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[3][2],
		DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, cmdl.l_to_g_xform[3][3]);

	print_indent(depth + 4);
	printf("%d   (# BCs defined on this model)\n", cmdl.num_coupling_model_bcs);
	print_indent(depth + 4);
	printf("%d   (# PowerFLOW BCs from this model)\n", cmdl.num_pf_bcs);
	print_indent(depth + 4);
	printf("(PowerFLOW faces with BCs from this model:)\n");
	print_indent(depth + 4);
	ccDOTIMES(i, cmdl.num_pf_bcs) {
		printf("%d ", cmdl.pf_faces_with_bcs[i]);
	}
	printf("\n");
	print_indent(depth);
	printf("}\n");

	exa_free(slashified_model_type);
	exa_free(slashified_model_filename);
	exa_free(slashified_absolute_model_filename);
	exa_free(slashified_results_filename);
	exa_free(slashified_absolute_results_filename);
	exa_free(slashified_model_length_unit);

	cdi_empty_cmdl(&cmdl);
}

/****************************************************************\
|
| Function name:print_scbc
|
| Purpose:prints scbc
|
\****************************************************************/
void
print_scbc(CDI_INFO cdi_info, int depth)
{
	sCDI_SCBC scbc;
	cdi_read_scbc(cdi_info, &scbc);
	STRING slashified_target_name = quotify_and_slashify_string(scbc.target_name);

	printf(" {  %d   (model index)\n", scbc.model_index);
	print_indent(depth + 4);
	printf("%d   (target type)\n", scbc.target_type);
	print_indent(depth + 4);
	printf("%s   (target name)\n", slashified_target_name);
	print_indent(depth + 4);
	printf("%.*g   (max match angle)\n", uDUMP_CDI_DFLOAT_DIG, scbc.max_match_angle);
	print_indent(depth + 4);
	printf("%.*g   (max match distance)\n", uDUMP_CDI_DFLOAT_DIG, scbc.max_match_distance);
	print_indent(depth + 4);
	printf("%d   (target side to match)\n", scbc.target_side_to_match);
	print_indent(depth + 4);

	printf("%d   (num source constraints)\n", scbc.n_source_constraints);
	print_indent(depth + 4);
	printf("(source constraint face indices:)\n");
	print_indent(depth + 4);
	ccDOTIMES(i, scbc.n_source_constraints) {
		printf("%d ", scbc.source_constraint_face_indices[i]);
	}
	printf("\n");
	print_indent(depth);
	printf("}\n");

	exa_free(slashified_target_name);

	cdi_empty_scbc(&scbc);
}

/****************************************************************\
|
| Function name:print_cplw
|
| Purpose:prints cplw
|
\****************************************************************/
void
print_cplw(CDI_INFO cdi_info, int depth)
{
	sCDI_CPLW cplw;
	cdi_read_cplw(cdi_info, &cplw);

	printf(" {  %d   (face index)\n", cplw.face_index);
	print_indent(depth + 4);
	printf("%d   (model index)\n", cplw.model_index);
	print_indent(depth + 4);
	printf("%.*g   (max match angle)\n", uDUMP_CDI_DFLOAT_DIG, cplw.max_match_angle);
	print_indent(depth + 4);
	printf("%.*g   (max match distance)\n", uDUMP_CDI_DFLOAT_DIG, cplw.max_match_distance);
	print_indent(depth + 4);
	printf("%d   (num source constraints)\n", cplw.n_source_constraints);
	ccDOTIMES(i, cplw.n_source_constraints) {
		print_indent(depth + 4);
		STRING slashified_constraint_name = quotify_and_slashify_string(cplw.source_constraint_names[i]);
		printf("%d %d %s  (source contraint %d; type, side & name)\n",
			cplw.source_constraint_types[i], cplw.source_constraint_sides[i],
			slashified_constraint_name, i);
		exa_free(slashified_constraint_name);
	}

	print_indent(depth + 4);
	printf("%d   (flags)\n", cplw.flags);

	print_indent(depth);
	printf("}\n");

	cdi_empty_cplw(&cplw);
}

/****************************************************************\
|
| Function name:print_fcmp
|
| Purpose:prints fcmp
|
\****************************************************************/
void
print_fcmp(CDI_INFO cdi_info, int depth)
{
	sCDI_FCMP fcmp;
	cdi_read_fcmp(cdi_info, &fcmp);

	STRING slashified_name = quotify_and_slashify_string(fcmp.name);
	printf(" {  %s   (name)\n", slashified_name);
	exa_free(slashified_name);
	print_indent(depth + 4);
	printf("%d   (eq of state)\n", fcmp.equation_of_state);
	print_indent(depth + 4);
	printf("%.*g   (viscosity)\n", uDUMP_CDI_DFLOAT_DIG, fcmp.viscosity);
	print_indent(depth + 4);
	printf("%.*g   (molecular weight)\n", uDUMP_CDI_DFLOAT_DIG, fcmp.molecular_weight);
	print_indent(depth);
	printf("}\n");

	cdi_empty_fcmp(&fcmp);
}

/****************************************************************\
|
| Function name:print_ptge
|
| Purpose:prints ptge
|
\****************************************************************/
void
print_ptge(CDI_INFO cdi_info)
{
	/* allocate and read a ptge */
	CDI_PTGE ptge = cdi_read_ptge(cdi_info);
	STRING slashified_powercase_version = quotify_and_slashify_string(ptge->powercase_version);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 0)) {
		printf(" { %" SCALAR_PRINTF_INT64 "d"
			" %" SCALAR_PRINTF_INT64 "d"
			" %" SCALAR_PRINTF_INT64 "d %s "
			" %" SCALAR_PRINTF_INT64 "d }\n",
			ptge->case_file_id,
			ptge->case_geometry_id,
			ptge->cdi_file_id,
			slashified_powercase_version,
			ptge->encryption_version_id);
	}
	else {
		printf(" { %" SCALAR_PRINTF_INT64 "d"
			" %" SCALAR_PRINTF_INT64 "d"
			" %" SCALAR_PRINTF_INT64 "d %s }\n",
			ptge->case_file_id,
			ptge->case_geometry_id,
			ptge->cdi_file_id,
			slashified_powercase_version);
	}

	/* free the ptge memory */
	cdi_destroy_ptge(ptge);
}

/****************************************************************\
|
| Function name:print_bbox
|
| Purpose:prints bbox
|
\****************************************************************/
void print_bbox(const sCDI_BBOX &bbox)
{
	printf(" {%*.*g %*.*g %*.*g %*.*g %*.*g %*.*g}\n",
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		bbox.coord[0][0],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		bbox.coord[1][0],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		bbox.coord[2][0],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		bbox.coord[0][1],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		bbox.coord[1][1],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		bbox.coord[2][1]);
}
void
print_bbox(CDI_INFO cdi_info)
{
	/* allocate and read a bbox */
	CDI_BBOX bbox = cdi_read_bbox(cdi_info);
	print_bbox(*bbox);
	/* free the bbox memory */
	cdi_destroy_bbox(bbox);
}

/****************************************************************\
|
| Function name:print_cmnt
|
| Purpose:prints cmnt
|
\****************************************************************/
void
print_cmnt(CDI_INFO cdi_info)
{
	/* allocate and read a cmnt */
	CDI_CMNT cmnt = cdi_read_cmnt(cdi_info);
	cmnt->title[cmnt->n_title_char] = '\0';
	cmnt->value[cmnt->n_value_char] = '\0';

	{
		STRING slashified_title = quotify_and_slashify_string(cmnt->title);
		STRING slashified_value = quotify_and_slashify_string(cmnt->value);

		printf(" { %s", slashified_title);
		printf(" %s }\n", slashified_value);

		exa_free(slashified_title);
		exa_free(slashified_value);
	}

	/* free the cmnt memory */
	cdi_destroy_cmnt(cmnt);
}

/****************************************************************\
|
| Function name:print_audt
|
| Purpose:prints audt
|
\****************************************************************/
void
print_audt(CDI_INFO cdi_info)
{
	/* allocate and read a audt */
	CDI_AUDT audt = cdi_read_audt(cdi_info);
	AUDIT_TRAIL trail = NULL;
	audt->audit_ur[audt->n_audit_ur_char] = '\0';

	trail = audit_make(audt->audit_ur);

	if (!inArgs.no_audt_mods) {
		audit_append_program_entry("dump_cdi", CDI_VERSION, NULL, NULL, (time_t)-1, trail);
		/* audit_append_misc_entry("dump", "undump_cdi readable ascii", trail); */
	}

	{
		STRING slashified_audit_ur = quotify_and_slashify_string(audit_universal_rep(trail));

		printf(" { %s }\n", slashified_audit_ur);

		exa_free(slashified_audit_ur);
	}

	/* free the audt memory */
	audit_free(trail);
	cdi_destroy_audt(audt);
}

/****************************************************************\
|
| Function name:print_undb
|
| Purpose:prints undb
|
\****************************************************************/
void
print_undb(CDI_INFO cdi_info)
{
	/* allocate and read a undb */
	CDI_UNDB undb = cdi_read_undb(cdi_info);
	undb->units_db_str[undb->units_db_str_length] = '\0';

	{
		STRING slashified_units_db_str = quotify_and_slashify_string(undb->units_db_str);

		printf(" { %s }\n", slashified_units_db_str);

		exa_free(slashified_units_db_str);
	}

	/* free the undb memory */
	cdi_destroy_undb(undb);
}

/****************************************************************\
|
| Function name:print_eqns
|
| Purpose:prints eqns
|
\****************************************************************/
void
print_eqns(CDI_INFO cdi_info)
{
	/* allocate and read a eqns */
	CDI_EQNS eqns = cdi_read_eqns(cdi_info);

  printf("{\n");
	STRING slashified_equations = quotify_and_slashify_string(eqns->equations);
  printf("%s", slashified_equations);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 23))
    printf("\n%d (PowerTHERM time available)\n", eqns->pthermTimeAvailable);
  printf("}\n");

	exa_free(slashified_equations);

	/* free the eqns memory */
	cdi_destroy_eqns(eqns);
}

/****************************************************************\
|
| Function name:print_gtbl
|
| Purpose:prints a grid-table
|
\****************************************************************/
void
print_gtbl(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a eqns */
	CDI_GTBL gtbl = cdi_read_gtbl(cdi_info);
	STRING slashified_name;
	STRING slashified_filename;
	STRING slashified_absolute_filename;
	STRING slashified_command;
	STRING slashified_table;

	slashified_name = quotify_and_slashify_string(gtbl->name);
	slashified_filename = quotify_and_slashify_string(gtbl->filename);
	slashified_absolute_filename = quotify_and_slashify_string(gtbl->absolute_filename ?
		ReplaceRunDir(gtbl->absolute_filename).c_str() : gtbl->absolute_filename);
	slashified_table = quotify_and_slashify_string(gtbl->table_string);
	slashified_command = quotify_and_slashify_string(gtbl->command_to_run_before_read);

	printf(" {  %s   (name)\n", slashified_name);
	print_indent(depth + 4);
	printf("%s   (filename)\n", slashified_filename);
	print_indent(depth + 4);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 4)) {
		printf("%s   (absolute filename)\n", slashified_absolute_filename);
		print_indent(depth + 4);
	}
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 1)) {
		printf("%d   (flags)\n", gtbl->flags);
		print_indent(depth + 4);
	}
	printf("%d   (csys index)\n", gtbl->coord_sys);
	print_indent(depth + 4);
	printf("%d   (read during sim)\n", gtbl->read_during_sim);
	print_indent(depth + 4);
	printf("%d   (read after meas)\n", gtbl->read_after_meas);
	print_indent(depth + 4);
	printf("%d   (meas window index)\n", gtbl->meas_window_index);
	print_indent(depth + 4);
	printf("%d   (1st meas frame to read after)\n", gtbl->first_meas_frame_to_read_after);
	print_indent(depth + 4);
	printf("%d   (num meas frames betw reads)\n", gtbl->num_meas_frames_between_reads);
	print_indent(depth + 4);
	printf("%s \n", slashified_command);
	print_indent(depth + 4);
	printf("%d   (first interval)\n", gtbl->first_interval);
	print_indent(depth + 4);
	printf("%d   (subsequent intervals)\n", gtbl->subsequent_intervals);
	print_indent(depth + 4);
	printf("%d   (end time)\n", gtbl->end_time);
	print_indent(depth + 4);
	printf("%s \n", slashified_table);

	exa_free(slashified_name);
	exa_free(slashified_filename);
	exa_free(slashified_absolute_filename);
	exa_free(slashified_command);
	exa_free(slashified_table);

	print_indent(depth);
	printf("}\n");

	/* free the tabl memory */
	cdi_destroy_gtbl(gtbl);
}


/****************************************************************\
|
| Function name:print_tabl
|
| Purpose:prints a table
|
\****************************************************************/
void
print_tabl(CDI_INFO cdi_info)
{
	/* allocate and read a eqns */
	cCDI_TABL tabl = cdi_read_tabl(cdi_info);
	STRING slashified_name;
	STRING slashified_table;

	printf(" { %d \n", tabl->num_tables);

	DOTIMES(i, tabl->num_tables, {

	  slashified_name = quotify_and_slashify_string(tabl->tables[i].name);
	  slashified_table = quotify_and_slashify_string(tabl->tables[i].table_string);

	  printf(" %s %s \n", slashified_name, slashified_table);

	  printf("%d \n", tabl->tables[i].coord_sys);

	  exa_free(slashified_name);
	  exa_free(slashified_table);
		});
	printf("}\n");

	/* free the tabl memory */
	cdi_destroy_tabl(tabl);
}


/****************************************************************\
|
| Function name:print_csys
|
| Purpose:prints coordinate systems
|
\****************************************************************/
void
print_csys(CDI_INFO cdi_info)
{
	/* allocate and read a eqns */
	cCDI_CSYS csys = cdi_read_csys(cdi_info);
	STRING slashified_name;

	printf(" { %d \n", csys->num_coord_systems);

	DOTIMES(i, csys->num_coord_systems, {

	  slashified_name = quotify_and_slashify_string(csys->coord_systems[i].name);

	  printf(" %s \n", slashified_name);

	  printf("   %*.*g %*.*g %*.*g %*.*g (xform)\n",
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[0][0],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[0][1],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[0][2],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[0][3]);
	  printf("   %*.*g %*.*g %*.*g %*.*g\n",
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[1][0],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[1][1],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[1][2],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[1][3]);
	  printf("   %*.*g %*.*g %*.*g %*.*g\n",
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[2][0],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[2][1],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[2][2],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[2][3]);
	  printf("   %*.*g %*.*g %*.*g %*.*g\n",
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[3][0],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[3][1],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[3][2],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].g_to_l_xform[3][3]);

	  printf("   %*.*g %*.*g %*.*g %*.*g (xform)\n",
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[0][0],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[0][1],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[0][2],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[0][3]);
	  printf("   %*.*g %*.*g %*.*g %*.*g\n",
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[1][0],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[1][1],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[1][2],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[1][3]);
	  printf("   %*.*g %*.*g %*.*g %*.*g\n",
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[2][0],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[2][1],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[2][2],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[2][3]);
	  printf("   %*.*g %*.*g %*.*g %*.*g\n",
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[3][0],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[3][1],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[3][2],
		 DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, csys->coord_systems[i].l_to_g_xform[3][3]);

	  exa_free(slashified_name);
		});
	printf("}\n");

	/* free the csys memory */
	cdi_destroy_csys(csys);
}


/****************************************************************\
|
| Function name:print_null
|
| Purpose:prints null
|
\****************************************************************/
void
print_null(CDI_INFO cdi_info)
{
	/* allocate and read a null */
	CDI_NULL null = cdi_read_null(cdi_info);
	int i;

	printf(" {%5d", null->n_null_char);
	for (i = 0; i < null->n_null_char; i++)
		printf(" %02x", null->null_char[i]);
	printf("}\n");

	/* free the null memory */
	cdi_destroy_null(null);
}


/****************************************************************\
|
| Functions for printing out bool chunks...
|
\****************************************************************/

// Prints out a single bool value, w/ optional parameter description if supplied
// by the caller.
void print_bool_helper(bool theValue,
	const std::string& comment = "")
{
	printf(" { %d }", theValue ? 1 : 0);
	if (!comment.empty())
		printf(" ( %s: %s )", comment.c_str(), theValue ? "Yes" : "No");
	printf("\n");
}

// Prints an *annotated* version of the bool value -- including indent, chunk
// type, value, and parameter description.
void print_annotated_bool(bool value,
	const::std::string& paramDescription,
	int depth)
{
	print_indent(depth);
	printf("bool");
	print_bool_helper(value, paramDescription);
}

// Reads a single bool from a CDI file. (assuming the cdi_info is queued up
// inside an bool chunk) and calls print_bool_helper() which does the actual
// formatted dump.
void print_bool(CDI_INFO cdi_info)
{
	cdiINT32 intValue = cdi_read_one_asINT32(cdi_info);
	bool value = (intValue != 0);
	print_bool_helper(value);
}

/****************************************************************\
|
| Functions for printing out int_ chunks...
|
\****************************************************************/

// Prints out a single int_ value, w/ optional parameter description if supplied
// by the caller.
void print_int_helper(cdiINT32 theValue,
	const std::string& comment = "")
{
	printf(" { %d }", theValue);
	if (!comment.empty())
		printf(" ( %s )", comment.c_str());
	printf("\n");
}

// Prints an *annotated* version of the int_ value -- including indent, chunk
// type, value, and parameter description. 
void print_annotated_int_(cdiINT32 value,
	const::std::string& paramDescription,
	int depth)
{
	print_indent(depth);
	printf("int_");
	print_int_helper(value, paramDescription);
}

// Reads a single int from a CDI file. (assuming the cdi_info is queued up
// inside an int_ chunk) and calls print_int_helper() which does the actual
// formatted dump.
void print_int_(CDI_INFO cdi_info)
{
	cdiINT32 theValue = cdi_read_one_asINT32(cdi_info);
	print_int_helper(theValue);
}
/****************************************************************\
|
| Functions for printing out enum chunks...
|
\****************************************************************/

// Prints out a single enum value, w/ optional parameter/value description if
// supplied by the caller.
void print_enum_helper(cdiINT32 theValue,
	const std::string& paramDescription = "",
	const std::string& valueDescription = "")
{
	printf(" { %d }", theValue);
	if (!paramDescription.empty()) {
		if (!valueDescription.empty())
			printf(" ( %s: %s )", paramDescription.c_str(), valueDescription.c_str());
		else
			printf(" ( %s )", paramDescription.c_str());
	}
	printf("\n");
}

// Prints an *annotated* version of the enum value -- including indent, chunk
// type, value, and parameter/value description. This function is templatized so
// that the compiler can can invoke the appropriate overload of
// GetCDIEnumValueDescription(). If no overload is found, the more general
// version (which takes an sINT32) is called, yielding an empty string.
template <typename T>
void print_annotated_enum(T value,
	const::std::string& paramDescription,
	int depth)
{
	print_indent(depth);
	printf("enum");
	print_enum_helper(value, paramDescription, GetCDIEnumValueDescription(value));
}


// Reads a single enum from a CDI file. (assuming the cdi_info is queued up
// inside an enum chunk) and calls print_enum_helper() which does the actual
// formatted dump.
void print_enum(CDI_INFO cdi_info)
{
	cdiINT32 theValue = cdi_read_one_asINT32(cdi_info);
	print_enum_helper(theValue);
}


/****************************************************************\
|
| Functions for printing out strg chunks...
|
\****************************************************************/

// Prints out a single std::string value, w/ optional parameter description if supplied
// by the caller.
void print_strg_helper(const std::string& theValue, const std::string& comment = "")
{
	STRING slashified_str = quotify_and_slashify_string(theValue.c_str());
	printf(" { %s }", slashified_str);
	exa_free(slashified_str);
	if (!comment.empty())
		printf(" ( %s )", comment.c_str());
	printf("\n");
}

// Prints an *annotated* version of the strg value -- including indent, chunk
// type, value, and parameter description. 
void print_annotated_strg(const::std::string& value
	, const::std::string& paramDescription
	, int depth)
{
	print_indent(depth);
	printf("strg");
	print_strg_helper(value, paramDescription);
}

// Reads a single std::string from a CDI file. (assuming the cdi_info is queued up
// inside an strg chunk) and calls print_strg_helper() which does the actual
// formatted dump.
void print_strg(CDI_INFO cdi_info)
{
	std::string str;
	cdi_read_stdstring(cdi_info, str);
	print_strg_helper(str);
}


/****************************************************************\
|
| Function name:print_name
|
| Purpose:prints name
|
\****************************************************************/
void
print_name(CDI_INFO cdi_info)
{
	/* allocate and read a name */
	CDI_NAME name = cdi_read_name(cdi_info);

	name->name[name->n_char] = '\0';

	{
		STRING slashified_name = quotify_and_slashify_string(name->name);

		printf(" { %s }\n", slashified_name);

		exa_free(slashified_name);
	}

	/* free the name memory */
	cdi_destroy_name(name);
}

/****************************************************************\
|
| Function name:print_offs
|
| Purpose:prints offs
|
\****************************************************************/
void
print_offs(CDI_INFO cdi_info)
{
	/* allocate and read an offs */
	CDI_OFFS offs = cdi_read_offs(cdi_info);

	printf(" { %d }\n", offs->offset);

	/* free the offs memory */
	cdi_destroy_offs(offs);
}

/****************************************************************\
|
| Function name:print_prgn
|
| Purpose:prints prgn
|
\****************************************************************/
void
print_prgn(CDI_INFO cdi_info)
{
	/* allocate and read a prgn */
	CDI_PRGN prgn = cdi_read_prgn(cdi_info);

	printf(" { %d }\n", prgn->tire_index);

	/* free the offs memory */
	cdi_destroy_prgn(prgn);
}

/****************************************************************\
|
| Function name:print_rgpn
|
| Purpose:prints rgpn
|
\****************************************************************/

void
print_rgpn(CDI_INFO cdi_info, int depth)
{

	if (print_encoded64_chunk(cdi_info, CDI_CHUNK_TYPE_RGPN, depth))
		return;

	/* allocate and read a rgpn */
	CDI_RGPN rgpn = cdi_read_rgpn(cdi_info);
	asINT32 num = rgpn->regionIds.size();
	printf(" { %d  (number of regions)\n", (int)num);
	//  print_indent(depth+1);
	for (size_t i = 0; i < rgpn->regionIds.size(); i++) {
		print_indent(depth + 1);
		printf("   %5d %d (protection)\n", rgpn->regionIds[i], rgpn->protectionIds[i]);
	}

	print_indent(depth);
	printf("}\n");

	cdi_destroy_rgpn(rgpn);
}


/****************************************************************\
|
| Function name:print_vrtx
|
| Purpose:prints vrtx
|
\****************************************************************/

void
print_vrtx(CDI_INFO cdi_info, int depth, std::vector<vertStruct*> &vertVec)
{
	if (print_encoded64_chunk(cdi_info, CDI_CHUNK_TYPE_VRTX, depth))
		return;
	/* allocate and read a vrtx */
	CDI_VRTX vrtx = cdi_read_vrtx(cdi_info);
	asINT32 num = vrtx->n_vertex;

	//printf(" { %5d      (number of verts)\n", (int)num);
	//print_indent(depth + 1);

	int i, imax = 3 * vrtx->n_vertex;
	int j;
	int old_num = Vrtx_number;
	for (j = 0, i = 0; j < imax; j += 3) {
		vertStruct *v = new vertStruct;
		v->vertIND = Vrtx_number++;
		v->x_cord = vrtx->coord[j];
		v->y_cord = vrtx->coord[j+1];
		v->z_cord = vrtx->coord[j+2];
		vertVec.push_back(v);
	}
	Vrtx_number = old_num;
	//printf("     ( x         y         z)\n");
	for (i = 0; i < imax; i += 3) {
		//print_indent(depth + 1);
		/*printf("%*.*g %*.*g %*.*g   (vrtx %d)\n",
			DFLOAT_DIG + 4,
			uDUMP_CDI_DFLOAT_DIG,
			vrtx->coord[i],
			DFLOAT_DIG + 4,
			uDUMP_CDI_DFLOAT_DIG,
			vrtx->coord[i + 1],
			DFLOAT_DIG + 4,
			uDUMP_CDI_DFLOAT_DIG,
			vrtx->coord[i + 2],
			Vrtx_number++);*/
	}
	//print_indent(depth);
	//printf("}\n");

	cdi_destroy_vrtx(vrtx);
}


/****************************************************************\
|
| Function name:print_edge
|
| Purpose:prints edge
|
\****************************************************************/
void
print_edge(CDI_INFO cio_info, int depth)
{
	/* allocate and read a edge */
	cCDI_EDGE edge = cdi_read_edge(cio_info);
	int i;

	printf(" { %5d     (number of edgehalves)\n", edge->n_edgehalf);
	print_indent(depth + 1);
	printf(" (conj  conj  head  tail)\n");
	print_indent(depth + 1);
	printf(" (facet edge  vrtx  vrtx)\n");
	for (i = 0; i < edge->n_edgehalf; i++)
	{
		print_indent(depth + 1);
		printf("%5d %5d %5d %5d    (edge %d)\n",
			edge->edgehalf[i].conj_facet,
			edge->edgehalf[i].conj_edgehalf,
			edge->edgehalf[i].head_vert,
			edge->edgehalf[i].tail_vert,
			Edge_number++);
	}
	print_indent(depth);
	printf("}\n");

	/* free the edge memory */
	cdi_destroy_edge(edge);
}

void print_old_face(CDI_INFO cdi_info,
	int depth)
{
	/* allocate and read a face */
	CDI_FACE_OLD face = cdi_read_face_old(cdi_info);
	int i;

	assert(face != NULL);

	printf(" {");

	if (cdi_get_major_version(cdi_info) >= 1) {
		printf(" %5d    (face index)\n", face->index);
		print_indent(depth + 1);
		{
			STRING name = NULL;
			if (face->name != NULL)
				name = quotify_and_slashify_string(face->name);
			else
				name = quotify_and_slashify_string("");
			printf("     %s    (face name)\n", name);
			exa_free(name);
		}
		print_indent(depth + 1);
		{
			STRING color = NULL;
			if (face->color != NULL)
				color = quotify_and_slashify_string(face->color);
			else
				color = quotify_and_slashify_string("");
			printf("     %s    (face color)\n", color);
			exa_free(color);
		}
		print_indent(depth + 1);
		printf("  ");
	}

	printf("   %5d    (surface physics index)\n", face->prop);

	print_indent(depth + 1);
	printf("     %5d    (number of facets)\n", face->n_facet);
	for (i = 0; i < face->n_facet; i++) {
		print_indent(depth + 1);
		printf("     %5d\n", face->facet[i]);
	}

	print_indent(depth);
	printf(" }\n");

	/* free the face memory */
	cdi_destroy_face_old(face);
}

/****************************************************************\
|
| Function name:print_face
|
| Purpose:prints face
|
\****************************************************************/
void print_face(CDI_INFO cdi_info,
	int depth)
{
	if (cdi_get_major_version(cdi_info) < 3) {
		print_old_face(cdi_info, depth);
		return;
	}
	else {
		sCDI_FACE face;
		cdi_read_face(cdi_info, &face);

		printf(" { %5d    (face index)\n", face.index);

		print_indent(depth + 1);
		printf("   %5d    (surface physics index)\n", face.prop);

		print_indent(depth + 1);
		{
			STRING name = NULL;
			if (face.name != NULL)
				name = quotify_and_slashify_string(face.name);
			else
				name = quotify_and_slashify_string("");
			printf("     %s    (face name)\n", name);
			exa_free(name);
		}

		if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
			print_indent(depth + 1);
			STRING color = NULL;
			if (face.psmv.mv_display_props[0].color != NULL)
				color = quotify_and_slashify_string(face.psmv.mv_display_props[0].color);
			else
				color = quotify_and_slashify_string("");
			printf("     %5s    (face color)\n", color);
			exa_free(color);
		}

		print_indent(depth + 1);
		printf("   %5d    (material index)\n", face.material_index);

		if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
			print_indent(depth + 1);
			printf("   %5d    (face display mode)\n", (asINT32)face.psmv.mv_display_props[0].display_mode);
		}

		print_indent(depth + 1);
		printf("   %5d    (number of facets - front)\n", face.n_front_facets);

		print_indent(depth + 1);
		printf("   %5d    (number of facets - back)\n", face.n_back_facets);

		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 13)) {
			print_indent(depth + 1);
			printf("   %5d    (number of partition segments)\n", face.psmv.n_partition_segments);
			ccDOTIMES(i, face.psmv.n_partition_segments) {
				print_indent(depth + 2);
				printf("   %5d   ", face.psmv.partition_segments[i].segment_index);

				if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
					STRING color = NULL;
					if (face.psmv.mv_display_props[i + 1].color != NULL)
						color = quotify_and_slashify_string(face.psmv.mv_display_props[i + 1].color);
					else
						color = quotify_and_slashify_string("");
					printf("     %5s   ", color);
					exa_free(color);
					printf("%5d   ", (asINT32)face.psmv.mv_display_props[i + 1].display_mode);
				}

				printf("%5d   ", face.psmv.partition_segments[i].effective_segment_index);
				if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0))
					printf("(segIndex, effective segIndex in partition %d)\n", i);
				else
					printf("(segIndex, color, dispMode, effective segIndex in partition %d)\n", i);
				// Replace line above with the following once tests are fully passing.
				// Keeping the above lines in the code to ease merging back to the mainline (where we'll want to do something similar)
				//printf("(segIndex, look, dispMode, effective segIndex in partition %d)\n", i);
			}

			if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 0)) {
				print_indent(depth + 1);
				printf("   %5d    (number of model views)\n", face.psmv.n_model_views);
				ccDOTIMES(i, face.psmv.n_model_views) {
					print_indent(depth + 2);
					{
						STRING color = quotify_and_slashify_string(face.psmv.mv_display_props[i].color);
						printf("     %10s   ", color);
						exa_free(color);
					}
					printf("%5d   ", (asINT32)face.psmv.mv_display_props[i].display_mode);

					printf("%*.*f   ", DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG,
						face.psmv.mv_display_props[i].transparency);
					printf("(color, dispMode, transparency)\n");
					// Replace line above with the following once tests are fully passing.
					// Keeping the above lines in the code to ease merging back to the mainline (where we'll want to do something similar)
					//printf("(look, dispMode, transparency)\n");
				}

				ccDOTIMES(i, face.psmv.n_model_views) {
					if (face.psmv.mv_display_props[i].realisticLook == NULL)
						break;  // Must be dealing with a file with no realistic look info

					print_indent(depth + 2);
					STRING realisticLook = quotify_and_slashify_string(face.psmv.mv_display_props[i].realisticLook);
					printf("     %s  (realistic look in model view %d)\n", realisticLook, i);
					exa_free(realisticLook);

				}
			}
		}
		print_indent(depth);
		printf(" }\n");

		cdi_empty_face(&face);
	}
}


/****************************************************************\
|
| Function name:print_mdlv
|
| Purpose:prints model view
|
\****************************************************************/
void print_mdlv(CDI_INFO cdi_info,
	int depth)
{
	sCDI_MDLV mdlv;
	cdi_read_model_view(cdi_info, &mdlv);

	printf(" {\n");

	print_indent(depth);
	{
		STRING name = quotify_and_slashify_string(mdlv.name);
		printf("     %s    (model view name)\n", name);
		exa_free(name);
	}

	print_indent(depth + 1);
	printf("%5d    (partition index)\n", mdlv.partition_index);
	print_indent(depth + 1);
	printf("%5d    (color reference model view index)\n", mdlv.color_ref_index);
	// Replace line above with the following once tests are fully passing.
	// Keeping the above lines in the code to ease merging back to the mainline (where we'll want to do something similar)
	//printf("%5d    (look reference model view index)\n", mdlv.color_ref_index);
	print_indent(depth + 1);
	printf("%5d    (display mode reference model view index)\n", mdlv.disp_mode_ref_index);
	print_indent(depth + 1);
	printf("%5d    (number of partition segments)\n", mdlv.n_partition_segments);

	ccDOTIMES(i, mdlv.n_partition_segments) {
		print_indent(depth + 2);
		{
			STRING color = quotify_and_slashify_string(mdlv.segment_display_props[i].color);
			printf("    %10s   ", color);
			exa_free(color);
			printf("    %5d   ", mdlv.segment_display_props[i].display_mode);

			printf("%*.*f   ", DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG,
				mdlv.segment_display_props[i].transparency);
			printf("(color, dispMode, transparency)\n");
			// Replace line above with the following once tests are fully passing.
			// Keeping the above lines in the code to ease merging back to the mainline (where we'll want to do something similar)
			//printf("(look, dispMode, transparency of segment %d)\n", i);
		}
	}

	ccDOTIMES(i, mdlv.n_partition_segments) {
		if (mdlv.segment_display_props[i].realisticLook == NULL) {
			break;  // Must be dealing with a file with no realistic look info
		}

		print_indent(depth + 2);
		STRING realisticLook = quotify_and_slashify_string(mdlv.segment_display_props[i].realisticLook);
		printf("    %s  (realistic look of segment %d)\n", realisticLook, i);
		exa_free(realisticLook);
	}

	print_indent(depth);
	printf("}\n");
}

/****************************************************************\
|
| Function name:print_psmv
|
| Purpose:prints segment/model view information
|
\****************************************************************/
void print_psmv(CDI_INFO cdi_info, const sCDI_PSMV& psmv,
	int depth)
{
	printf(" {\n");

	print_indent(depth + 1);
	printf("%5d    (number of partition segments)\n", psmv.n_partition_segments);

	ccDOTIMES(i, psmv.n_partition_segments) {
		print_indent(depth + 2);
		printf("    %5d   ", psmv.partition_segments[i].segment_index);
		printf("    %5d   ", psmv.partition_segments[i].effective_segment_index);
		printf("(segIndex, effective segIndex in partition %d)\n", i);
	}

	print_indent(depth + 1);
	printf("%5d    (number of model views)\n", psmv.n_model_views);

	ccDOTIMES(i, psmv.n_model_views) {
		print_indent(depth + 2);
		{
			STRING color = quotify_and_slashify_string(psmv.mv_display_props[i].color);
			printf("    %10s   ", color);
			exa_free(color);

			printf("    %5d   ", psmv.mv_display_props[i].display_mode);
			printf("%*.*f   ", DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG,
				psmv.mv_display_props[i].transparency);
			printf("(color, dispMode, transparency in model view %d)\n", i);
			// Replace line above with the following once tests are fully passing.
			// Keeping the above lines in the code to ease merging back to the mainline (where we'll want to do something similar)
			//printf("(look, dispMode, transparency in model view %d)\n", i);
		}
	}

	ccDOTIMES(i, psmv.n_model_views) {
		if (psmv.mv_display_props[i].realisticLook == NULL)
			break;  // Must be dealing with a file with no realistic look info

		print_indent(depth + 2);
		STRING realisticLook = quotify_and_slashify_string(psmv.mv_display_props[i].realisticLook);
		printf("    %s (realistic look in model view %d)\n", realisticLook, i);
		exa_free(realisticLook);
	}

	print_indent(depth);
	printf("  }\n");
}

void print_psmv(CDI_INFO cdi_info,
	int depth)
{
	sCDI_PSMV psmv;
	cdi_read_psmv(cdi_info, &psmv);
	print_psmv(cdi_info, psmv, depth);
}


/****************************************************************\
|
| Function name:print_fact
|
| Purpose:prints fact
|
\****************************************************************/
void print_old_fact(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a fact */
	cCDI_FACT fact = cdi_read_fact(cdi_info);
	int i;

	//printf(" { %5d     (number of facets)\n", fact->n_facet);
	//print_indent(depth + 1);
	//printf(" (face     edgehalves                    normal)\n");
	for (i = 0; i < fact->n_facet; i++)
	{
		//print_indent(depth + 1);

		// -r option.
		if (inArgs.roundoff_cdi) {
			if (fabs(fact->facet[i].normal[0]) < 1e-9)
				fact->facet[i].normal[0] = 0.0;
			if (fabs(fact->facet[i].normal[1]) < 1e-9)
				fact->facet[i].normal[1] = 0.0;
			if (fabs(fact->facet[i].normal[2]) < 1e-9)
				fact->facet[i].normal[2] = 0.0;

			/*printf("%5d %5d %5d %5d %*.*f %*.*f %*.*f (fact %d)\n",
				fact->facet[i].face,
				fact->facet[i].edgehalf[0],
				fact->facet[i].edgehalf[1],
				fact->facet[i].edgehalf[2],
				DFLOAT_DIG + 4,
				uDUMP_CDI_DFLOAT_DIG,
				fact->facet[i].normal[0],
				DFLOAT_DIG + 4,
				uDUMP_CDI_DFLOAT_DIG,
				fact->facet[i].normal[1],
				DFLOAT_DIG + 4,
				uDUMP_CDI_DFLOAT_DIG,
				fact->facet[i].normal[2],
				Fact_number++);*/
		}
		//else
			/*printf("%5d %5d %5d %5d %*.*g %*.*g %*.*g (fact %d)\n",
				fact->facet[i].face,
				fact->facet[i].edgehalf[0],
				fact->facet[i].edgehalf[1],
				fact->facet[i].edgehalf[2],
				DFLOAT_DIG + 4,
				uDUMP_CDI_DFLOAT_DIG,
				fact->facet[i].normal[0],
				DFLOAT_DIG + 4,
				uDUMP_CDI_DFLOAT_DIG,
				fact->facet[i].normal[1],
				DFLOAT_DIG + 4,
				uDUMP_CDI_DFLOAT_DIG,
				fact->facet[i].normal[2],
				Fact_number++);*/
	}
	//print_indent(depth);
	//printf("}\n");

	/* free the fact memory */
	cdi_destroy_fact(fact);
}

void print_fact(CDI_INFO cdi_info, int depth, std::vector<facetStruct*> &facetVec)
{
	if (cdi_get_major_version(cdi_info) < 3) {
		print_old_fact(cdi_info, depth);
		return;
	}
	else {
		sCDI_FACT_HEADER facet_header;
		cdi_read_facet_header(cdi_info, &facet_header);
		//printf(" { %5d     (number of facets)\n", facet_header.n_facets);

		//print_indent(depth + 1);
		//printf("   %5d    (number of vertex references)\n", facet_header.n_vertex_refs);

		//print_indent(depth + 1);
		//printf(" (front_face    back_face   n_vertices     vertices)\n");

		{
			int i, j;
			sCDI_FACT facet;
			for (i = 0; i < facet_header.n_facets; i++) {
				cdi_read_facet(cdi_info, &facet);
				facetStruct* f = new facetStruct();
				f->facetIND = i;
				f->n_vertices = facet.n_vertices;
				f->vertices = new int[facet.n_vertices];
				//print_indent(depth + 1);
				//printf("%5d %5d %5d ",
					//facet.front_face,
					//facet.back_face,
					//facet.n_vertices);
				for (j = 0; j < facet.n_vertices; j++) {
					//printf("%5d ", facet.vertices[j]);
					f->vertices[j] = facet.vertices[j];
				}
				//printf("(fact %d)\n", Fact_number++);
				facetVec.push_back(f);
			}
			/*print_indent(depth);
			printf("}\n");*/
		}
	}
}

/****************************************************************\
|
| Function name:print_mprm
|
| Purpose:prints mprm
|
\****************************************************************/
void
print_mprm(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a mprm */
	CDI_MPRM mprm = cdi_read_mprm(cdi_info);
	depth += 2;

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 12)) {
		printf(" { %d   (start time via: %s)\n", mprm->start_via, GetCDIEnumValueDescription(mprm->start_via).c_str());
		print_indent(depth);
		printf("   %d   (number of monitors)\n", (int)mprm->monitors.size());
		for (size_t im = 0; im < mprm->monitors.size(); im++) {
			print_indent(depth);
			printf("   %5d (monitor %d)\n", mprm->monitors[im], static_cast<int>(im));
		}
		print_indent(depth);
		printf("   %d   (start time)\n", mprm->start_time);
		print_indent(depth);
		printf("   %d   (end time via: %s)\n", mprm->end_via, GetCDIEnumValueDescription(mprm->end_via).c_str());
		print_indent(depth);
		printf("   %d   (duration)\n", mprm->duration);
		print_indent(depth);
		printf("   %d   (end time)\n", mprm->end_time);
		print_indent(depth);
		printf("   %d %d %d %d %d } (#frames, per, group, avg, space)\n",
			mprm->num_frames,
			mprm->period,
			mprm->period_sync_group_index,
			mprm->average_interval,
			mprm->spacing
		);
	}
	else if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 22))
		printf(" { %d %d %d %d %d %d %d } (start, end, #frames, per, group, avg, space)\n",
			mprm->start_time,
			mprm->end_time,
			mprm->num_frames,
			mprm->period,
			mprm->period_sync_group_index,
			mprm->average_interval,
			mprm->spacing
		);
	else if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 1))
		printf(" { %d %d %d %d %d %d } (start, end, #frames, per, avg, space)\n",
			mprm->start_time,
			mprm->end_time,
			mprm->num_frames,
			mprm->period,
			mprm->average_interval,
			mprm->spacing
		);
	else
		printf(" { %d %d %d %d %d } (start, end, per, avg, space)\n",
			mprm->start_time,
			mprm->end_time,
			mprm->period,
			mprm->average_interval,
			mprm->spacing
		);

	/* free the mprm memory */
	cdi_destroy_mprm(mprm);
}

/****************************************************************\
|
| Function name:print_mpsg
|
| Purpose:prints mpsg
|
\****************************************************************/
void
print_mpsg(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a mprm */
	sCDI_MPSG mpsg;
	cdi_read_mpsg(cdi_info, &mpsg);
	printf("{ \"%s\" } (name) \n", mpsg.name.c_str());
}

/****************************************************************\
|
| Function name:print_mflt
|
| Purpose:prints mflt
|
\****************************************************************/
void
print_mflt(CDI_INFO cdi_info)
{
	/* allocate and read a mflt */
	CDI_MFLT mflt = cdi_read_mflt(cdi_info);

	printf(" { %.*g %.*g } (min_pressure, max_pressure)\n",
		uDUMP_CDI_DFLOAT_DIG, mflt->min_pressure,
		uDUMP_CDI_DFLOAT_DIG, mflt->max_pressure);

	/* free the mflt memory */
	cdi_destroy_mflt(mflt);
}

/****************************************************************\
|
| Function name:print_mref
|
| Purpose:prints mref
|
\****************************************************************/
void
print_mref(CDI_INFO cdi_info)
{
	/* allocate and read a mref */
	CDI_MREF mref = cdi_read_mref(cdi_info);

	printf(" { %.*g %.*g %.*g } (reference point)\n",
		uDUMP_CDI_DFLOAT_DIG, mref->reference_point[0],
		uDUMP_CDI_DFLOAT_DIG, mref->reference_point[1],
		uDUMP_CDI_DFLOAT_DIG, mref->reference_point[2]);

	/* free the mref memory */
	cdi_destroy_mref(mref);
}


/****************************************************************\
|
| Function name:print_mdev
|
| Purpose:prints mdev
|
\****************************************************************/
void
print_mdev(CDI_INFO cdi_info, int depth)
{
	// allocate and read an mdev
	CDI_MDEV mdev = cdi_read_mdev(cdi_info);

	printf(" { %.*g %d %d }  (seg_size, coord_sys, lrf_index)\n",
		uDUMP_CDI_DFLOAT_DIG, mdev->seg_size, mdev->icsys, mdev->ilrf);

	cdi_destroy_mdev(mdev);
}



/****************************************************************\
|
| Function name:print_mfac
|
| Purpose:prints mfac
|
\****************************************************************/
#include <string>
void
print_mfac(CDI_INFO cdi_info)
{
	/* allocate and read a mfac */
	CDI_MFAC mfac = cdi_read_mfac(cdi_info);

	printf(" { %d %d } (grouping, merge_to_input)\n",
		(asINT32)mfac->grouping, mfac->merge_to_input);

	/* free the mfac memory */
	cdi_destroy_mfac(mfac);
}


std::string
get_std_options_list(cdiINT32 mask)
{
	std::string tempString;
	if (mask & CDI_MEAS_OPT_STANDARD_SURFACE)
		tempString += ", STD_SURF";
	if (mask & CDI_MEAS_OPT_STANDARD_FLUID)
		tempString += ", STD_FLUID";
	if (mask & CDI_MEAS_OPT_HEAT_FLUX)
		tempString += ", H_FLUX";
	if (mask & CDI_MEAS_OPT_MEAS_PER_VOXEL)
		tempString += ", MEAS_PER_VOXEL";
	if (mask & CDI_MEAS_OPT_TURB)
		tempString += ", TURB";
	if (mask & CDI_MEAS_OPT_STANDARD_POROUS)
		tempString += ", STD_POR";
	if (mask & CDI_MEAS_OPT_HTC_CHAR_TEMP)
		tempString += ", HTC_CHAR_TEMP";
	if (mask & CDI_MEAS_OPT_HTC_NEAR_WALL_TEMP)
		tempString += ", HTC_NEAR_WALL_TEMP";
	if (mask & CDI_MEAS_OPT_DIV_U)
		tempString += ", DIV_U";
	if (mask & CDI_MEAS_OPT_ROUND_PERIOD_DOWN)
		tempString += ", ROUND_DOWN";
	if (mask & CDI_MEAS_OPT_COMPOSITE_GEOMETRY)
		tempString += ", COMP_GEOM";
	if (mask & CDI_MEAS_OPT_FILE_PER_FRAME)
		tempString += ", FILE_PER";
	if (mask & CDI_MEAS_OPT_COMPRESS)
		tempString += ", COMPRESS_FILE";
	if (mask & CDI_MEAS_OPT_DONT_SHIFT_START_TIME)
		tempString += ", DONT_SHIFT";
	if (mask & CDI_MEAS_OPT_EXCLUDE_COARSE_LEVELS)
		tempString += ", EXCLUDE_COARSE";
	if (mask & CDI_MEAS_OPT_CUSTOM_FLUID)
		tempString += ", CUST_FLUID";
	if (mask & CDI_MEAS_OPT_CUSTOM_SURFACE)
		tempString += ", CUST_SURF";
	if (mask & CDI_MEAS_OPT_CUSTOM_POROUS)
		tempString += ", CUST_POR";
	if (mask & CDI_MEAS_OPT_LOCAL_CSYS)
		tempString += ", OUTPUT_IN_LOCAL_CSYS";
	if (mask & CDI_MEAS_OPT_DOUBLE_PRECISION)
		tempString += ", DBL_PRECISION";
	if (mask & CDI_MEAS_OPT_STANDARD_FLUID_PARTICLE)
		tempString += ", STD_FLUID_PRTCL";
	if (mask & CDI_MEAS_OPT_STANDARD_SURFACE_PARTICLE)
		tempString += ", STD_SURF_PRTCL";
	if (mask & CDI_MEAS_OPT_CUSTOM_FLUID_PARTICLE)
		tempString += ", CUST_FLUID_PRTCL";
	if (mask & CDI_MEAS_OPT_CUSTOM_SURFACE_PARTICLE)
		tempString += ", CUST_SURF_PRTCL";
	if (mask & CDI_MEAS_OPT_PARTICLE_VARS_PER_MATERIAL)
		tempString += ", SEP_PRTCL_VARS_PER_MATERIAL";
	if (mask & CDI_MEAS_OPT_PARTICLE_VARS_PER_EMITTER_PART)
		tempString += ", SEP_PRTCL_VARS_PER_EMITTER";
	if (mask & CDI_MEAS_OPT_CALC_HTC_FOR_ADB_WALLS)
		tempString += ", HTC_FOR_ADB_WALLS";
	if (mask & CDI_MEAS_OPT_NO_SURFEL_MERGER)
		tempString += ", NO_SURFEL_MERGER";
	if (mask & CDI_MEAS_OPT_RWNC_MEAS)
		tempString += ", REALISTIC_WIND_SEED_MEAS_FILE";
	return tempString.empty() ? "NONE" : tempString.substr(2);
}

/****************************************************************\
|
| Function name:print_mstp
|
| Purpose:prints mstp
|
\****************************************************************/
void
print_mstp(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a mstp */
	CDI_MSTP mstp = cdi_read_mstp(cdi_info);

	printf(" { %d (standard options) ", mstp->standard_mask);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 8)) {
		cSTRING measTypeString = "unknown";
		switch (mstp->meas_type) {
		case CDI_MEASTYPE_REGION:
			measTypeString = "standard region"; break;
		case CDI_MEASTYPE_FACE:
			measTypeString = "standard face"; break;
		case CDI_MEASTYPE_COMPOSITE_REGION:
			measTypeString = "composite region"; break;
		case CDI_MEASTYPE_COMPOSITE_FACE:
			measTypeString = "composite face"; break;
		case CDI_MEASTYPE_SAMPLED_FACE:
			measTypeString = "sampled face"; break;
		case CDI_MEASTYPE_PROBE:
			measTypeString = "probe"; break;
		case CDI_MEASTYPE_COMPOSITE_SAMPLED_FACE:
			measTypeString = "composite sampled face"; break;
		case CDI_MEASTYPE_DEVELOPMENT_REGION:
			measTypeString = "development region"; break;
		case CDI_MEASTYPE_DEVELOPMENT_FACE:
			measTypeString = "development face"; break;
		default:
			break;
		}
		printf("\n");
		print_indent(depth + 1);
		printf("     (opts = %s)\n", get_std_options_list(mstp->standard_mask).c_str());
		print_indent(depth + 1);
		printf("     %d (meas type = %s)\n", mstp->meas_type, measTypeString);

		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 1) ||
			(cdi_info->major_version == 3 && cdi_info->minor_version == 24)) {

			print_indent(depth + 1);
			printf("     ");
			printf("%d %d %d %d %d ", mstp->custom_fluid_options.num_vars(),
				mstp->custom_fluid_particle_options.num_vars(),
				mstp->custom_surface_options.num_vars(),
				mstp->custom_surface_particle_options.num_vars(),
				mstp->custom_porous_options.num_vars());
			printf(" (# custom vars for the following 5 categories)\n");

			print_indent(depth + 1);
			printf("     ");
			ccDOTIMES(ivar, mstp->custom_fluid_options.num_vars())
				printf("%d ", mstp->custom_fluid_options.var(ivar));
			printf("(custom fluid options)\n");

			print_indent(depth + 1);
			printf("     ");
			ccDOTIMES(ivar, mstp->custom_fluid_particle_options.num_vars())
				printf("%d ", mstp->custom_fluid_particle_options.var(ivar));
			printf("(custom fluid particle options)\n");

			print_indent(depth + 1);
			printf("     ");
			ccDOTIMES(ivar, mstp->custom_surface_options.num_vars())
				printf("%d ", mstp->custom_surface_options.var(ivar));
			printf("(custom surface options)\n");

			print_indent(depth + 1);
			printf("     ");
			ccDOTIMES(ivar, mstp->custom_surface_particle_options.num_vars())
				printf("%d ", mstp->custom_surface_particle_options.var(ivar));
			printf("(custom surface particle options)\n");

			print_indent(depth + 1);
			printf("     ");
			ccDOTIMES(ivar, mstp->custom_porous_options.num_vars())
				printf("%d ", mstp->custom_porous_options.var(ivar));
			printf("(custom porous options)\n");

			print_indent(depth);
		}
		else {

			int numBitmasks = cdi_meas_vars_num_bitmasks(cdi_info);

			print_indent(depth + 1);
			printf("     ");
			ccDOTIMES(i, numBitmasks)
				printf("%d ", mstp->custom_fluid_options.m_bitmasksObsolete[i]);
			printf("(custom fluid options)\n");
			if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 21)) {
				print_indent(depth + 1);
				printf("     ");
				ccDOTIMES(i, numBitmasks)
					printf("%d ", mstp->custom_fluid_particle_options.m_bitmasksObsolete[i]);
				printf("(custom particle fluid options)\n");
			}
			print_indent(depth + 1);
			printf("     ");
			ccDOTIMES(i, numBitmasks)
				printf("%d ", mstp->custom_surface_options.m_bitmasksObsolete[i]);
			printf("(custom surface options)\n");
			if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 21)) {
				print_indent(depth + 1);
				printf("     ");
				ccDOTIMES(i, numBitmasks)
					printf("%d ", mstp->custom_surface_particle_options.m_bitmasksObsolete[i]);
				printf("(custom particle surface options)\n");
			}
			print_indent(depth + 1);
			printf("     ");
			ccDOTIMES(i, numBitmasks)
				printf("%d ", mstp->custom_porous_options.m_bitmasksObsolete[i]);
			printf("(custom porous options)\n");

			print_indent(depth);
		}
	}

	printf("}\n");

	/* free the mstp memory */
	cdi_destroy_mstp(mstp);
}

/****************************************************************\
|
| Function name:print_ptyp
|
| Purpose:prints ptyp
|
\****************************************************************/
void
print_ptyp(CDI_INFO cdi_info)
{
	/* allocate and read a ptyp */
	CDI_PTYP ptyp = cdi_read_ptyp(cdi_info);

	printf(" { %d %d %d %d } (type, ints, conts, inits)\n",
		ptyp->type,
		ptyp->n_integer,
		ptyp->n_continuous,
		ptyp->n_initial
	);

	/* free the ptyp memory */
	cdi_destroy_ptyp(ptyp);
}

/****************************************************************\
|
| Function name:print_hxch
|
| Purpose:prints hxch
|
\****************************************************************/
void
print_hxch_base(CDI_INFO cdi_info, int depth, CDI_HXCH_BASE hxch)
{
	STRING slashified_name;

	slashified_name = quotify_and_slashify_string(hxch->name);

	printf(" {  %s   (name)\n", slashified_name);
	print_indent(depth + 4);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 7)) {
		STRING slashified_part_name = quotify_and_slashify_string(hxch->part_name);
		printf("%s   (part name)\n", slashified_part_name);
		print_indent(depth + 4);
	}
	printf("%d   (type)\n", hxch->type);
	print_indent(depth + 4);
	printf("%d   (tool)\n", hxch->tool);
	print_indent(depth + 4);
	printf("%d   (flags)\n", hxch->flags);
	print_indent(depth + 4);
	printf("%d   (table's csys index)\n", hxch->table_csys_index);
	print_indent(depth + 4);
	printf("%d   (porous medium's csys index)\n", hxch->medium_csys_index);
	print_indent(depth + 4);
	printf("%d   (inlet measurement face index)\n", hxch->inlet_face_index);
	print_indent(depth + 4);
	printf("%d   (outlet measurement face index)\n", hxch->outlet_face_index);
	print_indent(depth + 4);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 19)) {
		printf("%d   (top exchanger face index)\n", hxch->top_exchanger_face_index);
		print_indent(depth + 4);
		printf("%d   (coolant entry face index)\n", hxch->coolant_entry_face_index);
		print_indent(depth + 4);
	}

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 3)) {
		printf("%d   (inlet facet index offset)\n", hxch->inlet_facet_offset);
		print_indent(depth + 4);
		printf("%d   (outlet facet index offset)\n", hxch->outlet_facet_offset);
		print_indent(depth + 4);
	}
	printf("%d   (inlet measurement index)\n", hxch->inlet_meas_index);
	print_indent(depth + 4);
	printf("%d   (outlet measurement index)\n", hxch->outlet_meas_index);
	print_indent(depth + 4);
	if ((hxch->flags & CDI_HXCH_HAS_HEAT_GEN_MEAS) != 0) {
		printf("%d   (heat gen measurement index)\n", hxch->heat_gen_meas_index);
		print_indent(depth + 4);
	}
	printf("%d   (table index)\n", hxch->table_index);
	print_indent(depth + 4);
	printf("%d   (adiabatic medium index)\n", hxch->adiabatic_index);
	print_indent(depth + 4);
	printf("%d   (porous medium index)\n", hxch->medium_index);
	print_indent(depth + 4);
	printf("%d   (upstream exchanger index)\n", hxch->upstream_index);
	print_indent(depth + 4);
	printf("%d   (num stages)\n", hxch->n_stages);
	print_indent(depth + 4);
	printf("%d   (num coolant-direction divisions)\n", hxch->n_y_divisions);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 15)) {
		ccDOTIMES(i, hxch->n_stages) {
			print_indent(depth + 4);
			printf("%d   (num divisions for pass %d)\n", hxch->n_pass_divisions[i], i);
		}
	}
	else {
		cdiINT32 totalNumDivisions = hxch->n_pass_divisions[0];
		if (hxch->n_stages == 2)  // Pre version 3.15, only 1 or 2 stages were allowed
			totalNumDivisions += hxch->n_pass_divisions[1];

		print_indent(depth + 4);
		printf("%d   (num cross-coolant divisions)\n", totalNumDivisions);
	}

	print_indent(depth + 4);
	printf("%.*g   (x length)\n", uDUMP_CDI_DFLOAT_DIG, hxch->x_len);
	print_indent(depth + 4);
	printf("%.*g   (y length)\n", uDUMP_CDI_DFLOAT_DIG, hxch->y_len);
	print_indent(depth + 4);
	printf("%.*g   (z length)\n", uDUMP_CDI_DFLOAT_DIG, hxch->z_len);

	exa_free(slashified_name);
}

void
print_hxch(CDI_INFO cdi_info, int depth)
{
	if (print_encoded64_chunk(cdi_info, CDI_CHUNK_TYPE_HXCH, depth))
		return;

	/* allocate and read a hxch */
	CDI_HXCH hxch = cdi_read_hxch(cdi_info);
	STRING slashified_data_string;
	slashified_data_string = quotify_and_slashify_string(hxch->data_string);

	print_hxch_base(cdi_info, depth, hxch);

	print_indent(depth + 4);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 2)) {
		printf("%.*g   (HX experiment area)\n", uDUMP_CDI_DFLOAT_DIG, hxch->experiment_area);
		print_indent(depth + 4);
	}
	printf("%.*g   (percent upstream flow)\n", uDUMP_CDI_DFLOAT_DIG, hxch->percent_flow);
	print_indent(depth + 4);
	printf("%.*g   (mass flow rate)\n", uDUMP_CDI_DFLOAT_DIG, hxch->mass_flow_rate);
	print_indent(depth + 4);
	printf("%.*g   (specific heat)\n", uDUMP_CDI_DFLOAT_DIG, hxch->specific_heat);
	print_indent(depth + 4);
	printf("%.*g   (heat rejection)\n", uDUMP_CDI_DFLOAT_DIG, hxch->heat_rejection);
	print_indent(depth + 4);
	printf("%.*g   (entry temperature)\n", uDUMP_CDI_DFLOAT_DIG, hxch->entry_temp);
	print_indent(depth + 4);
	printf("%.*g   (experiment ref temp)\n", uDUMP_CDI_DFLOAT_DIG, hxch->experiment_ref_temp);
	print_indent(depth + 4);
	printf("%.*g   (min air flow)\n", uDUMP_CDI_DFLOAT_DIG, hxch->min_air_flow);
	print_indent(depth + 4);
	printf("%.*g   (max air flow)\n", uDUMP_CDI_DFLOAT_DIG, hxch->max_air_flow);
	print_indent(depth + 4);
	printf("%.*g   (Kc coefficient)\n", uDUMP_CDI_DFLOAT_DIG, hxch->kc_coeff);
	print_indent(depth + 4);
	printf("%.*g   (Kh coefficient)\n", uDUMP_CDI_DFLOAT_DIG, hxch->kh_coeff);
	print_indent(depth + 4);
	printf("%.*g   (alpha coefficient)\n", uDUMP_CDI_DFLOAT_DIG, hxch->alpha_coeff);
	print_indent(depth + 4);
	printf("%.*g   (beta coefficient)\n", uDUMP_CDI_DFLOAT_DIG, hxch->beta_coeff);
	print_indent(depth + 4);
	printf("%.*g   (D coefficient)\n", uDUMP_CDI_DFLOAT_DIG, hxch->d_coeff);
	print_indent(depth + 4);
	printf("%s   (data string)\n", slashified_data_string);

	exa_free(slashified_data_string);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 12)) {
		print_indent(depth + 4);
		printf("%.*g   (Exp coolant specific heat)\n", uDUMP_CDI_DFLOAT_DIG, hxch->exp_coolant_sp_heat);
		print_indent(depth + 4);
		printf("%.*g   (Exp coolant viscosity)\n", uDUMP_CDI_DFLOAT_DIG, hxch->exp_coolant_viscosity);
		print_indent(depth + 4);
		printf("%.*g   (Exp coolant thermal conductivity)\n", uDUMP_CDI_DFLOAT_DIG, hxch->exp_coolant_thermal_conductivity);
		print_indent(depth + 4);
		printf("%.*g   (Specified coolant viscosity)\n", uDUMP_CDI_DFLOAT_DIG, hxch->user_coolant_viscosity);
		print_indent(depth + 4);
		printf("%.*g   (Specified coolant thermal conductivity)\n", uDUMP_CDI_DFLOAT_DIG, hxch->user_coolant_thermal_conductivity);
		print_indent(depth + 4);
		printf("%.*g   (Experimental height)\n", uDUMP_CDI_DFLOAT_DIG, hxch->experimental_height);
		print_indent(depth + 4);
		printf("%.*g   (Experimental width)\n", uDUMP_CDI_DFLOAT_DIG, hxch->experimental_width);
		print_indent(depth + 4);
		printf("%.*g   (Experimental depth)\n", uDUMP_CDI_DFLOAT_DIG, hxch->experimental_depth);
	}
	print_indent(depth);
	printf("}\n");

	/* free the hxch memory */
	cdi_destroy_hxch(hxch);
}

/****************************************************************\
|
| Function name:print_amhx
|
| Purpose:prints amhx
|
\****************************************************************/
void
print_amhx(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a amhx */
	CDI_AMHX amhx = cdi_read_amhx(cdi_info);

	print_hxch_base(cdi_info, depth, amhx);

	STRING model_filename;
	model_filename = quotify_and_slashify_string(amhx->model_filename);
	print_indent(depth + 4);
	printf("%s   (model filename)\n", model_filename);
	exa_free(model_filename);

	STRING absolute_model_filename;

	absolute_model_filename = quotify_and_slashify_string(amhx->absolute_model_filename ?
		ReplaceRunDir(amhx->absolute_model_filename).c_str() : amhx->absolute_model_filename);
	print_indent(depth + 4);
	printf("%s   (absolute model filename)\n", absolute_model_filename);
	exa_free(absolute_model_filename);

	STRING amesim_hx_name;
	amesim_hx_name = quotify_and_slashify_string(amhx->amesim_hx_name);
	print_indent(depth + 4);
	printf("%s   (AMESim heat exchanger name)\n", amesim_hx_name);
	exa_free(amesim_hx_name);

	print_indent(depth);
	printf("}\n");

	/* free the amhx memory */
	cdi_destroy_amhx(amhx);
}
/****************************************************************\
|
| Function name:print_bsrg
|
| Purpose:prints bsrg
|
\****************************************************************/
void
print_bsrg(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a bsrg */
	CDI_BSRG bsrg = cdi_read_bsrg(cdi_info);

	STRING slashified_name;
	slashified_name = quotify_and_slashify_string(bsrg->mea_name);

	printf(" {  %s   (meas window)\n", slashified_name);
	print_indent(depth + 4);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 21)) {
		printf("%" SCALAR_PRINTF_INT64 "d   (cdi file id)\n", bsrg->cdi_file_id);
		print_indent(depth + 4);
	}
	printf("%d   (region index)\n", bsrg->region_index);
	print_indent(depth + 4);
	printf("%d   (facet index offset)\n", bsrg->facet_offset);
	print_indent(depth + 4);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 10)) {
		printf("%d   (spatial mapping)\n", bsrg->spatial_mapping);
		print_indent(depth + 4);
		printf("%d   (transient boundary seeding)\n", bsrg->transient_boundary_seeding);
		print_indent(depth + 4);
		printf("%d   (is merged to input)\n", bsrg->is_merged_to_input);
		print_indent(depth);
	}
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 1)) {
		printf("%d   (seed mks)\n", bsrg->seed_via_mks);
		print_indent(depth);
	}
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 13)) {
		printf("   %*.*g %*.*g %*.*g  (Region offset)\n",
			DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, bsrg->coord_offset[0],
			DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, bsrg->coord_offset[1],
			DFLOAT_DIG + 4, uDUMP_CDI_DFLOAT_DIG, bsrg->coord_offset[2]);
		print_indent(depth);
	}
	printf("}\n");

	exa_free(slashified_name);

	/* free the bsrg memory */
	cdi_destroy_bsrg(bsrg);
}
/****************************************************************\
|
| Function name:print_vpnt
|
| Purpose:prints vpnt
|
\****************************************************************/
void
print_vpnt(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a vpnt */
	CDI_VPNT vpnt = cdi_read_vpnt(cdi_info);

	STRING slashified_name;
	STRING slashified_camera_pos_unit;
	STRING slashified_orthographic_fov_unit;
	STRING slashified_spin_center_unit;

	slashified_name = quotify_and_slashify_string(vpnt->name);
	slashified_camera_pos_unit = quotify_and_slashify_string(vpnt->camera_pos_unit);
	slashified_orthographic_fov_unit = quotify_and_slashify_string(vpnt->orthographic_fov_unit);
	slashified_spin_center_unit = quotify_and_slashify_string(vpnt->spin_center_unit);

	printf(" {  %s   (name)\n", slashified_name);
	print_indent(depth + 4);
	printf("%d   (csys index)\n", vpnt->csys_index);
	print_indent(depth + 4);
	printf("%s   (camera position unit)\n", slashified_camera_pos_unit);
	print_indent(depth + 4);
	printf("%.*g   (x camera position)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->camera_pos[0]);
	print_indent(depth + 4);
	printf("%.*g   (y camera position)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->camera_pos[1]);
	print_indent(depth + 4);
	printf("%.*g   (z camera position)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->camera_pos[2]);
	print_indent(depth + 4);
	printf("%.*g   (x view direction)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->view_dir[0]);
	print_indent(depth + 4);
	printf("%.*g   (y view direction)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->view_dir[1]);
	print_indent(depth + 4);
	printf("%.*g   (z view direction)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->view_dir[2]);
	print_indent(depth + 4);
	printf("%.*g   (x up direction)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->up_dir[0]);
	print_indent(depth + 4);
	printf("%.*g   (y up direction)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->up_dir[1]);
	print_indent(depth + 4);
	printf("%.*g   (z up direction)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->up_dir[2]);
	print_indent(depth + 4);
	printf("%d   (projection type)\n", vpnt->projection_type);
	print_indent(depth + 4);
	printf("%s   (orthographic field of view unit)\n", slashified_camera_pos_unit);
	print_indent(depth + 4);
	printf("%.*g   (orthographic field of view)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->orthographic_fov);
	print_indent(depth + 4);
	printf("%.*g   (perspective field of view)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->perspective_fov);
	print_indent(depth + 4);
	printf("%s   (spin center unit)\n", slashified_spin_center_unit);
	print_indent(depth + 4);
	printf("%.*g   (x spin center)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->spin_center[0]);
	print_indent(depth + 4);
	printf("%.*g   (y spin center)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->spin_center[1]);
	print_indent(depth + 4);
	printf("%.*g   (z spin center)\n", uDUMP_CDI_DFLOAT_DIG, vpnt->spin_center[2]);
	print_indent(depth);
	printf("}\n");

	exa_free(slashified_name);
	exa_free(slashified_camera_pos_unit);
	exa_free(slashified_orthographic_fov_unit);
	exa_free(slashified_spin_center_unit);

	/* free the vpnt memory */
	cdi_destroy_vpnt(vpnt);
}

/****************************************************************\
|
| Function name:print_cdsr
|
| Purpose:prints cdsr
|
\****************************************************************/
void
print_cdsr(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a cdsr */
	sCDI_CDSR cdsr;
	cdi_read_condenser(cdi_info, &cdsr);

	STRING slashified_data_string;
	STRING slashified_name;

	slashified_data_string = quotify_and_slashify_string(cdsr.data_string);
	slashified_name = quotify_and_slashify_string(cdsr.name);

	printf(" {  %s   (name)\n", slashified_name);
	print_indent(depth + 4);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 7)) {
		STRING slashified_part_name = quotify_and_slashify_string(cdsr.part_name);
		printf("%s   (part name)\n", slashified_part_name);
		print_indent(depth + 4);
	}
	printf("%d   (type)\n", cdsr.type);
	print_indent(depth + 4);
	printf("%d   (tool)\n", cdsr.tool);
	print_indent(depth + 4);
	printf("%d   (flags)\n", cdsr.flags);
	print_indent(depth + 4);
	printf("%d   (table's csys index)\n", cdsr.table_csys_index);
	print_indent(depth + 4);
	printf("%d   (porous medium's csys index)\n", cdsr.medium_csys_index);
	print_indent(depth + 4);
	printf("%d   (inlet measurement face index)\n", cdsr.inlet_face_index);
	print_indent(depth + 4);
	printf("%d   (outlet measurement face index)\n", cdsr.outlet_face_index);
	print_indent(depth + 4);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 19)) {
		printf("%d   (top exchanger face index)\n", cdsr.top_exchanger_face_index);
		print_indent(depth + 4);
		printf("%d   (coolant entry face index)\n", cdsr.coolant_entry_face_index);
		print_indent(depth + 4);
	}

	printf("%d   (inlet facet index offset)\n", cdsr.inlet_facet_offset);
	print_indent(depth + 4);
	printf("%d   (outlet facet index offset)\n", cdsr.outlet_facet_offset);
	print_indent(depth + 4);
	printf("%d   (inlet measurement index)\n", cdsr.inlet_meas_index);
	print_indent(depth + 4);
	printf("%d   (outlet measurement index)\n", cdsr.outlet_meas_index);
	print_indent(depth + 4);
	if ((cdsr.flags & CDI_HXCH_HAS_HEAT_GEN_MEAS) != 0) {
		printf("%d   (heat gen measurement index)\n", cdsr.heat_gen_meas_index);
		print_indent(depth + 4);
	}
	printf("%d   (table index)\n", cdsr.table_index);
	print_indent(depth + 4);
	printf("%d   (adiabatic medium index)\n", cdsr.adiabatic_index);
	print_indent(depth + 4);
	printf("%d   (porous medium index)\n", cdsr.medium_index);
	print_indent(depth + 4);
	printf("%d   (num passes)\n", cdsr.n_passes);
	print_indent(depth + 4);
	ccDOTIMES(i, cdsr.n_passes) {
		printf("%d   (num tubes for pass %d)\n", cdsr.n_tubes[i], i);
		print_indent(depth + 4);
	}
	printf("%.*g   (x length)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.x_len);
	print_indent(depth + 4);
	printf("%.*g   (y length)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.y_len);
	print_indent(depth + 4);
	printf("%.*g   (z length)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.z_len);
	print_indent(depth + 4);
	printf("%.*g   (mass flow rate)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.mass_flow_rate);
	print_indent(depth + 4);
	printf("%.*g   (heat rejection)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.heat_rejection);
	print_indent(depth + 4);
	printf("%.*g   (entry temperature)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.entry_temp);
	print_indent(depth + 4);
	printf("%.*g   (entry pressure)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.entry_pressure);
	print_indent(depth + 4);
	printf("%.*g   (pressure drop)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.pressure_drop);
	print_indent(depth + 4);
	printf("%.*g   (experiment exit temp)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.experiment_exit_temp);
	print_indent(depth + 4);
	printf("%.*g   (min air flow)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.min_air_flow);
	print_indent(depth + 4);
	printf("%.*g   (max air flow)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.max_air_flow);
	print_indent(depth + 4);
	printf("%.*g   (Kc coefficient)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.kc_coeff);
	print_indent(depth + 4);
	printf("%.*g   (alpha coefficient)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.alpha_coeff);
	print_indent(depth + 4);
	printf("%.*g   (D coefficient)\n", uDUMP_CDI_DFLOAT_DIG, cdsr.d_coeff);
	print_indent(depth + 4);
	printf("%s   (data string)\n", slashified_data_string);

	exa_free(slashified_name);
	exa_free(slashified_data_string);

	print_indent(depth);
	printf("}\n");

	/* free the cdsr memory */
	cdi_empty_condenser(&cdsr);
}

/****************************************************************\
|
| Function name:print_ivdp
|
| Purpose:prints ivdp
|
\****************************************************************/
void
print_ivdp(CDI_INFO cdi_info)
{
	/* allocate and read a ivdp */
	CDI_IVDP ivdp = cdi_read_ivdp(cdi_info);

	printf(" { %d %d } (type, value)\n",
		ivdp->type,
		ivdp->value
	);

	/* free the ivdp memory */
	cdi_destroy_ivdp(ivdp);
}


/****************************************************************\
|
| Function name:print_cvdp
|
| Purpose:prints cvdp
|
\****************************************************************/
void print_cvdp(const sCDI_CVDP &cvdp)
{
	printf(" { %d %.*g } (type, value)\n",
		cvdp.type,
		uDUMP_CDI_DFLOAT_DIG,
		cvdp.value
	);
}

void
print_cvdp(CDI_INFO cdi_info)
{
	if (print_encoded64_chunk(cdi_info, CDI_CHUNK_TYPE_CVDP))
		return;

	/* allocate and read a cvdp */
	CDI_CVDP cvdp = cdi_read_cvdp(cdi_info);
	print_cvdp(*cvdp);
	/* free the cvdp memory */
	cdi_destroy_cvdp(cvdp);
}


/****************************************************************\
|
| Function name:print_bsdp
|
| Purpose:prints bsdp
|
\****************************************************************/
void
print_bsdp(CDI_INFO cdi_info)
{
	/* allocate and read a bsdp */
	CDI_BSDP bsdp = cdi_read_bsdp(cdi_info);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 10)) {
		printf(" { %d %.*g } (type, value)\n",
			bsdp->type,
			uDUMP_CDI_DFLOAT_DIG,
			bsdp->value
		);
	}
	else {
		printf(" { %d } (type)\n",
			bsdp->type
		);
	}
	/* free the bsdp memory */
	cdi_destroy_bsdp(bsdp);
}

/****************************************************************\
|
| Function name:print_pnts
|
| Purpose:prints pnts
|
\****************************************************************/
void
print_pnts(CDI_PNTS pnts, CDI_INFO info, int depth)
{
	if (CDI_INFO_VERSION_AT_LEAST(info, 3, 14)) {
		printf(" { %d  %d  %d (dimension, is_closed, num_pnts)\n",
			pnts->dim, pnts->is_closed, pnts->num_points);
	}
	else {
		printf(" { %d  %d  (dimension, num_pnts)\n", pnts->dim, pnts->num_points);
	}
	ccDOTIMES(i, pnts->num_points) {
		print_indent(depth + 3);
		ccDOTIMES(j, pnts->dim) {
			printf("%.*g ", uDUMP_CDI_DFLOAT_DIG, pnts->points[i*pnts->dim + j]);
		}
		printf(" (point %d)\n", i + 1);
	}
	print_indent(depth);
	printf("}\n");
}

void
print_pnts(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a pnts */
	CDI_PNTS pnts = cdi_read_pnts(cdi_info);

	print_pnts(pnts, cdi_info, depth);
	/* free the pnts memory */
	cdi_destroy_pnts(pnts);
}

static
void PrintVectorOfPoints(const sCDI_VECOF_PNTS& points, CDI_INFO info, int depth)
{
	cdiINT32 num_points = points.points.size() / points.dim;
	if (CDI_INFO_VERSION_AT_LEAST(info, 3, 14)) {
		printf(" { %d  %d  %d (dimension, is_closed, num_points)\n",
			points.dim, points.is_closed, num_points);
	}
	else {
		printf(" { %d  %d  (dimension, num_pnts)\n", points.dim, num_points);
	}
	ccDOTIMES(i, num_points) {
		print_indent(depth + 3);
		ccDOTIMES(j, points.dim) {
			printf("%.*g ", uDUMP_CDI_DFLOAT_DIG, points.points[i*points.dim + j]);
		}
		printf(" (point %d)\n", i + 1);
	}
	print_indent(depth);
	printf("}\n");
}

/****************************************************************\
|
| Function name:print_eqdp
|
| Purpose:prints eqdp
|
\****************************************************************/
void
print_eqdp(CDI_INFO cdi_info)
{
	/* allocate and read a eqdp */
	CDI_EQDP eqdp = cdi_read_eqdp(cdi_info);
	STRING slashified_var_name = quotify_and_slashify_string(eqdp->var_name);

	printf(" { %d %s } (type, var_name)\n",
		eqdp->type,
		slashified_var_name);

	/* free the eqdp memory */
	cdi_destroy_eqdp(eqdp);
}

/****************************************************************\
|
| Function name:print_rgns
|
| Purpose:prints rgns (regions list)
|
\****************************************************************/
void
print_rgns_helper(const sCDI_RGNS &rgns, int depth)
{
	printf(" { %5d     (number of regions)\n", rgns.n_region);
	for (int i = 0; i < rgns.n_region; i++)
	{
		print_indent(depth + 1);
		printf("     %5d (region %d)\n",
			rgns.region[i], i);
	}
	print_indent(depth);
	printf("}\n");
}

void
print_rgns(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a rgns */
	CDI_RGNS rgns = cdi_read_rgns(cdi_info);
	print_rgns_helper(*rgns, depth);
	/* free the rgns memory */
	cdi_destroy_rgns(rgns);
}

void
print_annotated_rgns(std::vector<cdiINT32>& rgnVec, int depth)
{
	sCDI_RGNS rgns;
	rgns.cccc = CDI_CHUNK_TYPE_RGNS;
	rgns.region = &rgnVec[0];
	rgns.n_region = rgnVec.size();
	print_indent(depth);
	printf("rgns");
	print_rgns_helper(rgns, depth);
}


/****************************************************************\
|
| Function name:print_facd
|
| Purpose:prints facd
|
\****************************************************************/
static void
print_facd(CDI_INFO cdi_info, CDI_FACD facd, int depth)
{
	print_indent(depth + 1);
	{
		STRING name = NULL;
		if (facd->face_name != NULL)
			name = quotify_and_slashify_string(facd->face_name);
		else
			name = quotify_and_slashify_string("");

		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 7)) { // separate front/back offsets, region index first introduced in 3.7
			if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 3, 18)) { // facet-side constraint introduced in 3.18
				printf("     %s  %.*g  %.*g  %d %d (face name, front_offset, back_offset, region_index, facet_side)\n",
					name,
					uDUMP_CDI_DFLOAT_DIG, facd->front_offset,
					uDUMP_CDI_DFLOAT_DIG, facd->back_offset,
					facd->region_index, facd->facet_side);
			}
			else
				printf("     %s  %.*g  %.*g  %d (face name, front_offset, back_offset, region_index)\n",
					name,
					uDUMP_CDI_DFLOAT_DIG, facd->front_offset,
					uDUMP_CDI_DFLOAT_DIG, facd->back_offset,
					facd->region_index);
		}
		else {
			printf("     %s  %.*g  (face name, offset)\n",
				name,
				uDUMP_CDI_DFLOAT_DIG, facd->front_offset);
		}
		exa_free(name);
	}
}

/****************************************************************\
|
| Function name:print_fdlt
|
| Purpose:prints fdlt ( list of faces with offset distances)
|
\****************************************************************/
void
print_fdlt(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a fdlt */
	CDI_FDLT fdlt = cdi_read_fdlt(cdi_info);
	int i;

	printf(" { %5d     (number of faces)\n", fdlt->n_facd);
	for (i = 0; i < fdlt->n_facd; i++)
	{
		print_facd(cdi_info, fdlt->facd + i, depth + 1);
	}
	print_indent(depth);
	printf("}\n");

	/* free the fdlt memory */
	cdi_destroy_fdlt(fdlt);
}

void print_annotated_parm(const sCDI_PARM* parm, const std::string& comment, int depth);
void print_annotated_dbls(const idFLOAT* values, const cdiINT32 count, const std::string& comment, int depth);


/****************************************************************\
|
| Function name:print_farg
|
| Purpose:prints farg
|
\****************************************************************/
static void
print_farg(CDI_INFO cdi_info, sCDI_FARG farg, int depth)
{
  char type_str[10];
  cio_type_to_string(CDI_CHUNK_TYPE_FARG, type_str);
  print_indent(depth);
  printf("%s {\n", type_str);
  depth++;
  print_annotated_enum(farg.type, "type", depth);
  std::string min_param, max_param;
  if (farg.type == 0) { // Curvature
    min_param = "min_curvature";
    max_param = "max_curvature";
  }
  else {
    min_param = "min_angle";
    max_param = "max_angle";
  }
  print_annotated_parm(&(farg.min_param), min_param, depth);
  print_annotated_parm(&(farg.max_param), max_param, depth);
  print_annotated_enum(farg.voxel_size_via, "voxel_size_via", depth);
  print_annotated_dbls((idFLOAT*)&farg.resolution, 1, "resolution", depth);
  print_annotated_int_(farg.scale, "scale", depth);
  print_annotated_int_(farg.thickness, "thickness", depth);
  depth--;
  print_indent(depth);
  printf("}\n");
}

/****************************************************************\
|
| Function name:print_gfar
|
| Purpose:prints gfar (array of angle-range records)
|
\****************************************************************/
void
print_gfar(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a gfar */
  sCDI_GFAR gfar=cdi_read_gfar(cdi_info);
	int i;
  printf(" {\n");
  print_indent(depth);
  printf("  %d     (number of angle-ranges)\n",gfar.n_farg);
  depth++;
  for(i=0;i<gfar.n_farg;i++) 
    print_farg(cdi_info, gfar.farg[i] , depth);
  depth--;
	print_indent(depth);
	printf("}\n");
}

/****************************************************************\
|
| Function name:print_gapd
|
| Purpose:prints gapd (gap VR parameters)
|
\****************************************************************/
void
print_gapd(CDI_INFO cdi_info, int depth)
{
  /* allocate and read a gapd */
  sCDI_GAPD gapd = cdi_read_gapd(cdi_info);
  int i;
  printf(" {\n");
  print_indent(depth);
  print_annotated_int_(gapd.n_voxels, "n_voxels", depth);

  print_indent(depth);
  print_annotated_parm(&(gapd.gap_size), "gap_size", depth);
  print_indent(depth);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 29)) {
    print_annotated_int_(gapd.scale, "scale", depth);  
    print_indent(depth);
  }
  printf("}\n");
}


/****************************************************************\
|
| Function name:print_gscc
|
| Purpose:prints gscc (grid scale value)
|
\****************************************************************/
void
print_gscc(CDI_INFO cdi_info)
{
	/* allocate and read a gscc */
	CDI_GSCC gscc = cdi_read_gscc(cdi_info);

	printf(" { %d } (value)\n",
		gscc->value
	);

	/* free the gscc memory */
	cdi_destroy_gscc(gscc);
}

/****************************************************************\
|
| Function name:print_gsep
|
| Purpose:prints gsep (grid scale separation factor)
|
\****************************************************************/
void print_gsep(CDI_INFO cdi_info)
{
	CDI_GSEP gsep = cdi_read_gsep(cdi_info);
	printf(" { %d %d } (scale separation_factor)\n",
		gsep->scale, gsep->separation_factor);
	cdi_destroy_gsep(gsep);
}

/****************************************************************\
|
| Function name:print_grdf
|
| Purpose:prints grdf (grid scale default parameters)
|
\****************************************************************/
void print_grdf(CDI_INFO cdi_info)
{
	CDI_GRDF grdf = cdi_read_grdf(cdi_info);
	printf(" { %d %d } (resolution_precedence separation_factor)\n",
		grdf->resolution_precedence, grdf->separation_factor);
	cdi_destroy_grdf(grdf);
}
/****************************************************************\
|
| Function name:print_prdf
|
| Purpose:prints prdf (case default region precedences)
|
\****************************************************************/
void print_prdf(CDI_INFO cdi_info)
{
	CDI_PRDF prdf = cdi_read_prdf(cdi_info);
	printf(" { %d %d } (default: scale_precedence physics_precedence)\n",
		prdf->default_scale_precedence,
		prdf->default_physics_precedence);
	cdi_destroy_prdf(prdf);
}
/****************************************************************\
|
| Function name:print_prec
|
| Purpose:prints prec (generalized precedence)
|
\****************************************************************/
void print_prec(CDI_INFO cdi_info)
{
	CDI_PREC prec = cdi_read_prec(cdi_info);
	printf(" { %d } (precedence)\n", prec->precedence);
	cdi_destroy_prec(prec);
}

/****************************************************************\
|
| Function name:print_simv
|
| Purpose:prints simv (simulation volume)
|
\****************************************************************/
void
print_simv(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a simv */
	CDI_SIMV simv = cdi_read_simv(cdi_info);

	printf(" { %*.*g %*.*g %*.*g          (max)\n",
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->max[0],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->max[1],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->max[2]);
	print_indent(depth + 1);
	printf("   %10d                                  (flags)\n",
		simv->flags);
	print_indent(depth + 1);
	printf("   %*.*g %*.*g %*.*g %*.*g (xform)\n",
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[0][0],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[0][1],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[0][2],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[0][3]);
	print_indent(depth + 1);
	printf("   %*.*g %*.*g %*.*g %*.*g\n",
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[1][0],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[1][1],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[1][2],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[1][3]);
	print_indent(depth + 1);
	printf("   %*.*g %*.*g %*.*g %*.*g\n",
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[2][0],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[2][1],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[2][2],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[2][3]);
	print_indent(depth + 1);
	printf("   %*.*g %*.*g %*.*g %*.*g\n",
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[3][0],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[3][1],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[3][2],
		DFLOAT_DIG + 4,
		uDUMP_CDI_DFLOAT_DIG,
		simv->xform[3][3]);
	print_indent(depth);
	printf("}\n");


	/* free the simv memory */
	cdi_destroy_simv(simv);
}

/****************************************************************\
|
| Function name:print_thma
|
| Purpose:prints thma (thermal acceleration)
|
\****************************************************************/
void
print_thma(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a thma */
	CDI_THMA thma = cdi_read_thma(cdi_info);

	printf(" { %5d    (start time)\n", thma->start_time);

	print_indent(depth + 2);
	printf("   %5d    (end time)\n", thma->end_time);

	print_indent(depth + 2);
	printf("   %5d    (period)\n", thma->period);

	print_indent(depth + 2);
	printf("   %5d    (accelerated interval)\n", thma->interval);

	print_indent(depth);
	printf("}\n");

	/* free the thma memory */
	cdi_destroy_thma(thma);
}

/****************************************************************\
|
| Function name: print_symp
|
| Purpose:prints symp (simulation volume)
|
\****************************************************************/
void print_symp(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a symp */
	CDI_SYMP symp = cdi_read_symp(cdi_info);

	printf(" { %10d   (axes)\n", symp->axes);
	print_indent(depth + 1);
	printf("   %10d   %10d   %10d   (min)\n",
		symp->min[0], symp->min[1], symp->min[2]);
	print_indent(depth + 1);
	printf("   %10d   %10d   %10d   (max)\n",
		symp->max[0], symp->max[1], symp->max[2]);
	print_indent(depth);
	printf("}\n");

	/* free the symp memory */
	cdi_destroy_symp(symp);
}

/****************************************************************\
|
| Function name:print_ghdr
|
| Purpose:prints ghdr
|
\****************************************************************/
void
print_ghdr(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a ghdr */
	CDI_GHDR ghdr = cdi_read_ghdr(cdi_info);
	depth += 2;

	// New information about simulation duration w.r.t. End of Initial Transient (Autostop).
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 11)) {
		printf(" { %d   (simulation duration via: %s)\n",
			ghdr->sim_duration_via, GetCDIEnumValueDescription(ghdr->sim_duration_via).c_str());
		print_indent(depth);
		printf("   %d     (number of monitors)\n", (int)ghdr->monitors.size());
		for (size_t im = 0; im < ghdr->monitors.size(); im++)
		{
			print_indent(depth + 1);
			printf("     %5d (monitor %d)\n", ghdr->monitors[im], static_cast<int>(im));
		}
		print_indent(depth);
		printf("   %d   (timesteps after initial transient)\n", ghdr->n_timesteps_after_init_trans);
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 1)) {
			print_indent(depth);
			printf("   %d   (has average mme window)\n", ghdr->has_average_mme_window);
			print_indent(depth);
			printf("   %d   (local velocity freeze)\n", ghdr->local_vel_freeze);
		}
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 27)) {
      print_indent(depth);
      printf("   %d   (Temperature Dependent Gas Specific Heat Ratio?)\n", ghdr->m_temperatureDependentGamma);
    }
    if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 28)) {
      print_indent(depth);
      printf("   %d   (Cooling air leakage model option : %s)\n",
        ghdr->m_coolingAirOpt,
        GetCDIEnumValueDescription(ghdr->m_coolingAirOpt).c_str());
    }

		print_indent(depth);
		printf("   ");
	}
	else {
		// Older (pre-Autostop) file - just print opening brace.
		printf(" { ");
	}

	if (!ghdr->include_second_block)
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 10)) {
			printf("%d %d %d} (header:timesteps, processors, liquid_material)\n",
				ghdr->n_timesteps,
				ghdr->n_processors,
				ghdr->liquidMaterialType);
		}
		else {
			printf("%d %d } (header:timesteps, processors)\n",
				ghdr->n_timesteps,
				ghdr->n_processors);
		}
	else if (!ghdr->include_third_block) {
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 10)) {
			printf("%d %d %d %d %lf } (max timesteps, processors, liquid_material, freeze_momentum_field, physical_time_scaling)\n",
				ghdr->n_timesteps,
				ghdr->n_processors,
				ghdr->liquidMaterialType,
				(int)ghdr->freeze_momentum_field,
				ghdr->physical_time_scaling);
		}
		else {
			printf("%d %d %d %lf } (max timesteps, processors, freeze_momentum_field, physical_time_scaling)\n",
				ghdr->n_timesteps,
				ghdr->n_processors,
				(int)ghdr->freeze_momentum_field,
				ghdr->physical_time_scaling);
		}
	}
	else {
		printf("%d   (max timesteps)\n", ghdr->n_timesteps);
		print_indent(depth);
		printf("   %d   (processors)\n", ghdr->n_processors);
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 10)) {
			print_indent(depth);
			printf("   %d   (liquid material)\n", ghdr->liquidMaterialType);
		}

		int freezeMomentumField = ghdr->freeze_momentum_field;
		print_indent(depth);
		printf("   %d   (freeze momentum field)\n", freezeMomentumField);
		print_indent(depth);
		printf("   %.*g   (phys time scaling)\n", uDUMP_CDI_DFLOAT_DIG, ghdr->physical_time_scaling);

		if (ghdr->include_third_block) {
			print_indent(depth);
			printf("   %d   (# prtcl emitters)\n", ghdr->numParticleEmitters);
			print_indent(depth);
			printf("   %d   (# prtcl materials)\n", ghdr->numParticleMaterials);
			print_indent(depth);
			printf("   %d   (# prtcl modeling vars)\n", ghdr->numParticleModelingVars);
			print_indent(depth);
			printf("   %d   (# UD scalars)\n", ghdr->numUDScalars);
			print_indent(depth);
			printf("   %d   (# UDS vars)\n", ghdr->numUDSVars);
			print_indent(depth);
			printf("   %d   (# MPF fluid components)\n", ghdr->num5gFluidComponents);
			print_indent(depth);
			printf("   %d   (# MPF vars)\n", ghdr->num5gVars);
			print_indent(depth);
			printf("}\n");
		}
	}

	/* free the ghdr memory */
	cdi_destroy_ghdr(ghdr);
}

/****************************************************************\
|
| Function name:print_cpnt
|
| Purpose:prints cpnt
|
\****************************************************************/
void
print_cpnt(CDI_INFO cdi_info)
{
	/* allocate and read a cpnt */
	CDI_CPNT cpnt = cdi_read_cpnt(cdi_info);

	printf(" { %d %d %d } (checkpoint: start,end,period)\n",
		cpnt->start,
		cpnt->end,
		cpnt->freq
	);

	/* free the cpnt memory */
	cdi_destroy_cpnt(cpnt);
}

/****************************************************************\
|
| Function name:print_unit
|
| Purpose:prints unit
|
\****************************************************************/
void
print_unit(CDI_INFO cdi_info)
{
	/* allocate and read a unit */
	CDI_UNIT unit = cdi_read_unit(cdi_info);

	printf(" { %.*g %.*g %.*g %.*g %d }\n\t\t\t\t (lattice units:m,l,t,T,n_user_units)\n",
		uDUMP_CDI_DFLOAT_DIG,
		unit->kilos_per_particle,
		uDUMP_CDI_DFLOAT_DIG,
		unit->meters_per_cell,
		uDUMP_CDI_DFLOAT_DIG,
		unit->seconds_per_timestep,
		uDUMP_CDI_DFLOAT_DIG,
		unit->kelvins_per_lattice_temp,
		unit->n_user_units
	);

	/* free the unit memory */
	cdi_destroy_unit(unit);
}

/****************************************************************\
|
| Function name:print_uunt
|
| Purpose:prints uunt
|
\****************************************************************/
void
print_uunt(CDI_INFO cdi_info)
{
	/* allocate and read a uunt */
	CDI_UUNT uunt = cdi_read_uunt(cdi_info);

	STRING slashified_unit_name = quotify_and_slashify_string(uunt->unit_name);

	printf(" { %d %d %d %d %d %.*g %.*g %s }\n\t\t\t (user units:type,m,l,t,T,scale,offset,name)\n",
		uunt->unit_type,
		uunt->mass_exponent,
		uunt->length_exponent,
		uunt->time_exponent,
		uunt->temp_exponent,
		uDUMP_CDI_DFLOAT_DIG,
		uunt->scale,
		uDUMP_CDI_DFLOAT_DIG,
		uunt->offset,
		slashified_unit_name
	);

	exa_free(slashified_unit_name);

	/* free the uunt memory */
	cdi_destroy_uunt(uunt);
}

/****************************************************************\
|
| Function name:print_cprp
|
| Purpose:prints cprp
|
\****************************************************************/
void
print_cprp(CDI_INFO cdi_info)
{
	/* allocate and read a cprp */
	CDI_CPRP cprp = cdi_read_cprp(cdi_info);

	STRING slashified_char_prop_name = quotify_and_slashify_string(cprp->char_prop_name);

	printf(" { %d %d %d %d %s %.*g }\n\t\t\t (char prop:m,l,t,T,name,value)\n",
		cprp->mass_exponent,
		cprp->length_exponent,
		cprp->time_exponent,
		cprp->temp_exponent,
		slashified_char_prop_name,
		uDUMP_CDI_DFLOAT_DIG,
		cprp->value
	);

	exa_free(slashified_char_prop_name);

	/* free the cprp memory */
	cdi_destroy_cprp(cprp);
}

/****************************************************************\
|
| Function name:print_ment
|
| Purpose:prints ment
|
\****************************************************************/
void
print_ment(CDI_INFO cdi_info) {
	CDI_MENT ment = cdi_read_ment(cdi_info);

	printf(" { %d }\n", ment->options);

	cdi_destroy_ment(ment);
}

/****************************************************************\
|
| Function name:print_prbe
|
| Purpose:prints prbe
|
\****************************************************************/
void
print_prbe(CDI_INFO cdi_info) {
	CDI_PRBE prbe = cdi_read_prbe(cdi_info);

	printf(" { %.*g %.*g %.*g %.*g }\n",
		uDUMP_CDI_DFLOAT_DIG, prbe->x,
		uDUMP_CDI_DFLOAT_DIG, prbe->y,
		uDUMP_CDI_DFLOAT_DIG, prbe->z,
		uDUMP_CDI_DFLOAT_DIG, prbe->diam);

	cdi_destroy_prbe(prbe);
}

/****************************************************************\
|
| Function name:print_parm
|
| Purpose:prints parm
|
\****************************************************************/
void print_parm_helper(const sCDI_PARM* parm, const std::string& comment)
{
	printf(" { %d %d \"%s\" %.*g \"%s\" }", parm->defaulted, parm->isEqVariable ? 1 : (parm->isDataCurve ? 2 : 0), parm->variableName.c_str(), uDUMP_CDI_DFLOAT_DIG, parm->value, parm->preferredUnit.c_str());
	if (!comment.empty())
		printf(" ( %s )", comment.c_str());

	printf("\n");
}

void print_parm(CDI_INFO cdi_info)
{
	sCDI_PARM parm;
	cdi_read_parm(cdi_info, &parm);
	print_parm_helper(&parm, "");
}

void print_annotated_parm(const sCDI_PARM* parm, const std::string& comment, int depth)
{
	print_indent(depth);
	printf("parm");
	print_parm_helper(parm, comment);
}

/****************************************************************\
|
| Function name:print_dbls
|
| Purpose:prints dbls
|
\****************************************************************/
void print_dbls_helper(const idFLOAT* values,
	const cdiINT32 count,
	const std::string& comment)
{
	// print values
	printf(" {");
	for (int i = 0; i < count; ++i)
		printf(" %.*g", uDUMP_CDI_DFLOAT_DIG, values[i]);
	//    printf(" %*.*g", DFLOAT_DIG+4, uDUMP_CDI_DFLOAT_DIG, values[i]);  
	printf(" }");

	// print comment
	if (!comment.empty())
		printf(" ( %s )", comment.c_str());

	printf("\n");
}

// print_dbls is used when size of one dbls is unknown and is
// calculated from the size of current chunk
void print_dbls(CDI_INFO cdi_info)
{
	cdiINT32 count = 0;
	idFLOAT* values = NULL;

	cdi_read_dbls(cdi_info, values, count);
	print_dbls_helper(values, count, "");

	cdi_destroy_dbls(values);
}

// like cdi_write_dbls, size of dbls is known when we print dbls
// in a known structure 
void print_annotated_dbls(const idFLOAT* values,
	const cdiINT32 count,
	const std::string& comment,
	int depth)
{
	print_indent(depth);
	printf("dbls");
	print_dbls_helper(values, count, comment);
}

/****************************************************************\
|
| Function name:print_prtj
|
| Purpose:prints prtj
|
\****************************************************************/
void print_prtj(CDI_INFO cdi_info, int depth)
{
	sCDI_PRTJ* prtj = cdi_read_prtj(cdi_info);
	printf(" { (particle trajectory measurement)\n");
	depth++;
	print_annotated_parm(&(prtj->trajectories), "Trajectories", depth);
	print_annotated_parm(&(prtj->recordVel), "Record velocities", depth);
	print_annotated_parm(&(prtj->dynTrajDecimation), "Dynamic Trajectory Decimation", depth);
	print_annotated_parm(&(prtj->decimErrortol), " Decimation Error Tolerance", depth);
	print_annotated_parm(&(prtj->hitPoints), "Hit_points type enum", depth);
	print_annotated_parm(&(prtj->adherParticles), "Adhering Particles", depth);
	print_annotated_parm(&(prtj->recordNormImpulse), "Record Normal Impulse", depth);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 3))
		print_annotated_parm(&(prtj->frac_trajectory_recording), "frac_trajectory_recording", depth);
	depth--;
	print_indent(depth);
	printf("}\n");
}


/****************************************************************\
|
| Function name:print_flst
|
| Purpose:prints flst
|
\****************************************************************/
void
print_flst(CDI_FLST flst, int depth)
{
	printf(" { %5d     (number of faces)\n", flst->n_face);

	DOTIMES(i, flst->n_face, {
	  print_indent(depth + 1);
	  printf("     %5d (face %d)\n",
			 flst->face[i],i);
		});

	print_indent(depth);
	printf("}\n");
}

void
print_flst(CDI_INFO cdi_info, int depth)
{
	CDI_FLST flst = cdi_read_flst(cdi_info);
	print_flst(flst, depth);
	cdi_destroy_flst(flst);
}

// print FLST stored as a std::vector<cdiINT32>
void
print_annotated_flst(std::vector<cdiINT32> &values, int depth)
{
	sCDI_FLST flst;
	flst.face = &(values[0]);
	flst.n_face = values.size();
	print_indent(depth);
	printf("flst");
	print_flst(&flst, depth);
}


/****************************************************************\
|
| Function name:print_wipr
|
| Purpose:prints wipr
|
\****************************************************************/
void
print_wipr(CDI_INFO info, int depth)
{
	sCDI_WIPR wipr;
	cdi_read_wipr(info, &wipr);

	printf(" {\n");
	depth++;

	// name
	print_indent(depth);
	printf("name { \"%s\" }\n", wipr.name.c_str());

	// wiper blade
	print_annotated_flst(wipr.wiper_blade, depth);

	// wiped surface
	print_annotated_flst(wipr.wiped_surface, depth);

	// wiper axis
	print_annotated_dbls((idFLOAT*)wipr.wiper_axis_origin, 3,
		"wiper axis origin", depth);
	print_annotated_dbls((idFLOAT*)wipr.wiper_axis_dir, 3,
		"wiper axis dir", depth);

	// end points
	print_annotated_dbls((idFLOAT*)wipr.inner_endpoint, 3,
		"plane inner end point", depth);
	print_annotated_dbls((idFLOAT*)wipr.outer_endpoint, 3,
		"plane outer end point", depth);

	// wiper type
	print_annotated_enum(wipr.wiper_type, "wiper type", depth);

	// angles
	print_annotated_parm(&(wipr.import_angle), "import angle", depth);
	print_annotated_parm(&(wipr.start_angle), "start angle", depth);
	print_annotated_parm(&(wipr.end_angle), "end angle", depth);

	// time
	print_annotated_parm(&(wipr.stroke_duration), "stroke duration", depth);
	print_annotated_parm(&(wipr.delay), "delay", depth);

  if (CDI_INFO_VERSION_AT_LEAST(info, 7, 22)) {
    print_annotated_enum(wipr.start_via, "start time via", depth);
    print_annotated_parm(&(wipr.initial_delay), "initial delay", depth+1);
    print_indent(depth+1);
    printf("%d   (number of monitors)\n", (int)wipr.monitors.size());
    for (size_t im = 0; im < wipr.monitors.size(); im++) {
      print_indent(depth);
      printf("   %5d (monitor %d)\n", wipr.monitors[im], static_cast<int>(im));
    }
    print_annotated_int_(wipr.emitter_index, "emitter index", depth+1);
    print_annotated_parm(&(wipr.emitter_delay), "emitter delay", depth+1);
    print_annotated_parm(&(wipr.num_strokes), "number strokes", depth);
  }
  else if (CDI_INFO_VERSION_AT_LEAST(info, 4, 15))
		print_annotated_parm(&(wipr.initial_delay), "initial delay", depth);

	depth--;
	print_indent(depth);
	printf("}\n");
}


/****************************************************************\
|
| Function name:print_rgct
|
| Purpose:prints rgct:  total region count
|
\****************************************************************/
void print_rgct(CDI_INFO cdi_info)
{
	sCDI_RGCT rgct;
	cdi_read_case_region_count(cdi_info, &rgct);
	printf(" { %5d }\n", rgct.n_regions);
}

/****************************************************************\
|
| Function name:print_rgnn
|
| Purpose:prints rgnn:  region numeration
|
\****************************************************************/
void print_rgnn(const sCDI_RGNN& rgnn, int depth)
{
	printf(" { %5d   (number of vertices) \n", rgnn.n_vertices);
	print_indent(depth + 1);
	printf(" %5d    (number of faces)\n", rgnn.n_faces);
	print_indent(depth + 1);
	printf(" %5d    (number of facets)\n", rgnn.n_facets);
	print_indent(depth + 1);
	printf(" %5d    (number of vertex references)\n", rgnn.n_vertex_refs);
	print_indent(depth);
	printf(" }\n");
}

void print_rgnn(CDI_INFO cdi_info, int depth)
{
	sCDI_RGNN rgnn;
	cdi_read_region_numeration(cdi_info, &rgnn);
	print_rgnn(rgnn, depth);
}

/****************************************************************\
|
| Function name:print_rgdp
|
| Purpose:prints rgdp:  region display properties
|
\****************************************************************/
void print_rgdp(sCDI_RGDP& rgdp, CDI_INFO cdi_info, int depth)
{
	STRING color = NULL;
	if (rgdp.color != NULL)
		color = quotify_and_slashify_string(rgdp.color);
	else
		color = quotify_and_slashify_string("");
	printf(" {  %s    (color)\n", color);
	exa_free(color);

	print_indent(depth + 1);
	printf("   %5d    (material index)\n", rgdp.material_index);
	print_indent(depth + 1);
	printf("   %5d    (display mode)\n", rgdp.display_mode);
	print_indent(depth);
	printf(" }\n");
}

void print_rgdp(CDI_INFO cdi_info, int depth)
{
	sCDI_RGDP rgdp;
	cdi_read_region_display_properties(cdi_info, &rgdp);
	print_rgdp(rgdp, cdi_info, depth);
}



/****************************************************************\
|
| Function name:print_prtt
|
| Purpose:prints prtt:  partition type
|
\****************************************************************/
void print_prtt(CDI_INFO cdi_info)
{
	sCDI_PRTT prtt;
	cdi_read_partition_type(cdi_info, &prtt);
	printf(" { %d }   (partition type)\n", (int)prtt.partition_type);
}


/****************************************************************\
|
| Function name:print_sgid
|
| Purpose:prints sgid:  segment index
|
\****************************************************************/
void print_sgid(CDI_INFO cdi_info)
{
	sCDI_SGID sgid;
	cdi_read_segment_index(cdi_info, &sgid);
	printf(" { %d }   (partition segment index)\n", (int)sgid.segment_index);
}

/****************************************************************\
|
| Function name:print_indent
|
| Purpose:prints indent
|
\****************************************************************/
void
print_indent(int depth)
{
	int i;

	for (i = 0; i < depth; i++)
		printf("  ");
}

//Print an accr chunk for ice accreation case information.
void print_accr(CDI_INFO cdi_info, int depth)
 {
   sCDI_ACCR accr;
   accr.cdi_read(cdi_info);

   printf(" { (accretion parameters)\n");
   depth++;
   print_annotated_parm(&(accr.m_total_accretion_duration), "Total accretion duration", depth);
   print_annotated_parm(&(accr.m_liquid_water_content), "Liquid water content", depth);
   print_annotated_int_(accr.m_material_index, "Particle material index", depth);
   print_annotated_parm(&(accr.m_accretion_material_density), "Accretion material density", depth);
   print_annotated_int_(accr.m_total_num_of_accretion_layers, "Total number of accretion layers", depth);
   print_annotated_parm(&(accr.m_collection_duration_per_layer), "Collection duration per layer", depth);
   print_annotated_parm(&(accr.m_accretion_acceleration_factor), "Accretion acceleration factor", depth);
   print_annotated_int_(accr.m_meas_window_index, "Measurement window index", depth);

   print_annotated_flst(const_cast<std::vector<int>&>(accr.m_surface_ids), depth);

   depth--;
   print_indent(depth);
   printf("}\n");
 }

void print_box_(const sCDI_BOX_ &box_, const std::string& comment, int depth)
{
	printf(" { (%s)\n", comment.c_str());
	print_annotated_rgns(const_cast<std::vector<cdiINT32>&>(box_.region.regions), depth + 1);
	print_indent(depth + 1);
	printf("cvdp");
	print_cvdp(box_.csys);
	print_indent(depth + 1);
	printf("bbox");
	print_bbox(box_.bbox);
	print_indent(depth);
	printf("}\n");
}

/****************************************************************\
 * |
 * | Function name:print_pglb
 * |
 * | Purpose:prints particle tracking globals
 * |
 * \****************************************************************/
void print_pglb(CDI_INFO cdi_info, int depth)
{
	sCDI_PGLB pglb;
	cdi_read_pglb(cdi_info, &pglb);
	printf(" { (particle-tracking globals)\n");
	depth++;
	print_indent(depth);
	printf("box_");
	print_box_(pglb.dispersion_box, "dispersion box", depth);
	print_annotated_parm(&(pglb.gravity_csys), "gravity csys", depth);
	print_annotated_parm(pglb.gravity, "gravity X", depth);
	print_annotated_parm(pglb.gravity + 1, "gravity Y", depth);
	print_annotated_parm(pglb.gravity + 2, "gravity Z", depth);
	print_annotated_parm(&(pglb.coupled_momentum_solver), "coupled particle/momentum solver", depth);
	print_annotated_parm(&(pglb.default_emitter_start), "default emitter start time", depth);
	print_annotated_parm(&(pglb.default_emitter_end), "default emitter end time", depth);
	print_annotated_parm(&(pglb.default_max_particle_age), "default max particle age", depth);
	print_annotated_parm(&(pglb.default_max_num_reflections), "default max num reflections", depth);
	print_annotated_parm(&(pglb.default_min_particle_velocity), "default min particle velocity", depth);
	depth--;
	print_indent(depth);
	printf("}\n");
}

static void PrintParticleGeneralParameterDistributionInfo(const cCDI_GENERAL_PARTICLE_PARAMETER_DISTRIBUTION& distInfo
	, int depth
	, const std::string parameterName)
{
	sCDI_PARM distributionType = distInfo.GetDistributionType();
	print_annotated_parm(&(distributionType), parameterName + " distribution", depth);
	switch (int(distributionType.value))
	{
	case DISTRIBUTION_NONE:
	{
		sCDI_PARM mean;
		distInfo.GetNoneDistribution(&mean);
		print_annotated_parm(&mean, "mean " + parameterName, depth);
		break;
	}
	case DISTRIBUTION_UNIFORM:
	{
		sCDI_PARM mean, range;
		distInfo.GetUniformDistribution(&mean, &range);
		print_annotated_parm(&mean, "mean " + parameterName, depth);
		print_annotated_parm(&range, parameterName + " range", depth);
		break;
	}
	case DISTRIBUTION_GAUSSIAN:
	{
		sCDI_PARM mean, stddev;
		distInfo.GetGaussianDistribution(&mean, &stddev);
		print_annotated_parm(&mean, "mean " + parameterName, depth);
		print_annotated_parm(&stddev, parameterName + " standard deviation", depth);
		break;
	}
	case DISTRIBUTION_GAMMA:
	{
		sCDI_PARM mean, shapeParam;
		distInfo.GetGammaDistribution(&mean, &shapeParam);
		print_annotated_parm(&mean, "mean " + parameterName, depth);
		// Ugh!
		if (parameterName.find("angle") != std::string::npos)
			print_annotated_parm(&shapeParam, parameterName + " standard deviation", depth);
		else
			print_annotated_parm(&shapeParam, parameterName + " shape parameter", depth);
		break;
	}
	default:
		break;
	}
}

void print_prmt(CDI_PRMT prmt, int depth)
{
	static int materialNumber = 1;
	printf(" { (particle-material %d)\n", materialNumber++);
	depth++;
	print_indent(depth);
	printf("name { \"%s\" }\n", prmt->name.c_str());
	print_annotated_parm(&(prmt->type), "type", depth);
	if (prmt->type.value == PARTICLE_MATERIAL_LIQUID) {
		print_annotated_parm(&(prmt->viscosity), "viscosity", depth);
		print_annotated_parm(&(prmt->surface_tension), "surface-tension", depth);
	}
	PrintParticleGeneralParameterDistributionInfo(prmt->density_info, depth, "density");
	if (prmt->type.value == PARTICLE_MATERIAL_LIQUID) {
		print_annotated_parm(&(prmt->breakup_model), "breakup-model", depth);
	}
	depth--;
	print_indent(depth);
	printf("}\n");
}

void print_prmt(CDI_INFO cdi_info, int depth)
{
	sCDI_PRMT prmt;
	cdi_read_prmt(cdi_info, &prmt);
	print_prmt(&prmt, depth);
}


// =======================================================================================
// Particle Model - Emitter Configurations
// =======================================================================================

static void PrintParticleDiameterDistributionInfo(const cCDI_PARTICLE_DIAMETER_DISTRIBUTION& diamInfo, int depth, CDI_INFO cdi_info)
{
	sCDI_PARM distributionType = diamInfo.GetDistributionType();
	print_annotated_parm(&(distributionType), "particle diameter distribution", depth);
	switch (int(distributionType.value))
	{
	case DISTRIBUTION_NONE:
	{
		sCDI_PARM mean;
		diamInfo.GetNoneDistribution(&mean);
		print_annotated_parm(&mean, "mean particle diameter", depth);
		break;
	}
	case DISTRIBUTION_UNIFORM:
	{
		sCDI_PARM mean, range;
		diamInfo.GetUniformDistribution(&mean, &range);
	  print_annotated_parm(&mean, "mean particle diameter", depth);
	  print_annotated_parm(&range, "particle diameter range", depth);
		break;
	}
	case DISTRIBUTION_GAUSSIAN:
	{
		sCDI_PARM mean, stddev, minDia, maxDia;
		diamInfo.GetGaussianDistribution(&mean, &stddev, &minDia, &maxDia);
		print_annotated_parm(&mean, "mean particle diameter", depth);
		print_annotated_parm(&stddev, "particle diameter standard deviation", depth);
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31)) {
		  print_annotated_parm(&minDia, "minimum diameter", depth);//PR_48032
		  print_annotated_parm(&maxDia, "maximum diameter", depth);
		}
		break;
	}
	case DISTRIBUTION_GAMMA:
	{
		sCDI_PARM mean, shapeParam, minDia, maxDia;
		diamInfo.GetGammaDistribution(&mean, &shapeParam, &minDia, &maxDia);
		print_annotated_parm(&mean, "mean particle diameter", depth);
		print_annotated_parm(&shapeParam, "particle diameter shape parameter", depth);
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31)) {//PR_48032
		  print_annotated_parm(&minDia, "minimum diameter", depth);
		  print_annotated_parm(&maxDia, "maximum diameter", depth);
		}
		break;
	}
	case DISTRIBUTION_ROSIN_RAMMLER:
	{
		sCDI_PARM characteristicValue, exponent, minDia, maxDia;
		diamInfo.GetRosinRammlerDistribution(&characteristicValue, &exponent, &minDia, &maxDia);
		print_annotated_parm(&characteristicValue, "particle characteristic diameter", depth);
		print_annotated_parm(&exponent, "particle diameter distribution exponent", depth);
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31)) {//PR_48032
		  print_annotated_parm(&minDia, "minimum diameter", depth);
		  print_annotated_parm(&maxDia, "maximum diameter", depth);
		}
		break;
	}
	case DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION: //PR_48032
	{
		sCDI_PARM characteristicValue, exponent, minDia, maxDia;
		diamInfo.GetRosinRammlerVolumeFractionDistribution(&characteristicValue, &exponent, &minDia, &maxDia);
		print_annotated_parm(&characteristicValue, "particle characteristic diameter", depth);
		print_annotated_parm(&exponent, "particle diameter distribution exponent", depth);
		print_annotated_parm(&minDia, "minimum diameter", depth);
		print_annotated_parm(&maxDia, "maximum diameter", depth);
		break;
	}
	case DISTRIBUTION_LOG_NORMAL:
	{
		sCDI_PARM mean, stddev, minDia, maxDia;;
		diamInfo.GetLogNormalDistribution(&mean, &stddev, &minDia, &maxDia);
		print_annotated_parm(&mean, "mean particle diameter, ", depth);
		print_annotated_parm(&stddev, "particle diameter standard deviation", depth);
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 31)) {//PR_48032
		  print_annotated_parm(&minDia, "minimum diameter", depth);
		  print_annotated_parm(&maxDia, "maximum diameter", depth);
		}
		break;
	}
	default:
		break;
	}
}



static void PrintEmitterConfigurations(CDI_INFO cdi_info, int depth)
{
	cCDI_EMITTER_CONFIGURATIONS configs;
	configs.ReadFromCDI(cdi_info);
	const std::vector<const sCDI_EMITTER_CONFIG_BASE*> emitterConfigPtrs = configs.GetAllEmittersConfigs();

	int emitterConfigCount = 0;

	printf(" {\n");      // Open outer Emitter Configurations bracing
	depth++;

	std::map<int, std::string> configTypeStrings;
	configTypeStrings[cCDI_EMITTER_CONFIGURATIONS::NOZZLE] = "nozzle";
	configTypeStrings[cCDI_EMITTER_CONFIGURATIONS::RAIN] = "rain";
	configTypeStrings[cCDI_EMITTER_CONFIGURATIONS::TIRE] = "tire";

	for (size_t iec = 0; iec < emitterConfigPtrs.size(); iec++)
	{
		char type_str[10];
		cio_type_to_string(emitterConfigPtrs[iec]->GetChunkType(), type_str);
		print_indent(depth);
		printf("%s", type_str);

		printf(" { (emitter config %d)\n", emitterConfigCount++);
		depth++;

		cCDI_EMITTER_CONFIGURATIONS::eEMITTER_CONFIGURATION_TYPE configurationType = emitterConfigPtrs[iec]->GetType();
		sCDI_PARM configurationTypeParm;
		configurationTypeParm.value = configurationType;
		print_annotated_parm(&configurationTypeParm, "configuration type - " + configTypeStrings[configurationType], depth);

		// sCDI_EMITTER_CONFIG_BASE Data
		print_indent(depth);
		printf("name { \"%s\" }\n", emitterConfigPtrs[iec]->name.c_str());
		print_annotated_parm(&(emitterConfigPtrs[iec]->material_index), "material index", depth);

		// OK, now type-specific data
		if (configurationType == cCDI_EMITTER_CONFIGURATIONS::NOZZLE) {

			const sCDI_NOZZLE_EMITTER_CONFIG& nozzleConfig = *(dynamic_cast<const sCDI_NOZZLE_EMITTER_CONFIG*>(emitterConfigPtrs[iec]));

			print_annotated_parm(&(nozzleConfig.nozzle_type), "nozzle type", depth);
			print_annotated_parm(&(nozzleConfig.angle_distribution), "angle distribution", depth);

			switch (cdiINT32(nozzleConfig.nozzle_type.value))
			{
			case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_FULL_CONE:
				print_annotated_parm(&(nozzleConfig.cone_half_angle), "cone half angle", depth);
				if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && nozzleConfig.angle_distribution.value == DISTRIBUTION_GAUSSIAN)
					print_annotated_parm(&(nozzleConfig.outer_half_angle_limit), "outer half angle limit", depth);
				break;
			case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_HOLLOW_CONE:
				print_annotated_parm(&(nozzleConfig.mean_angle), "mean half angle", depth);
				print_annotated_parm(&(nozzleConfig.angle_range), "angle range / std. dev.", depth);
				if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && nozzleConfig.angle_distribution.value == DISTRIBUTION_GAUSSIAN) {
					print_annotated_parm(&(nozzleConfig.inner_half_angle_limit), "inner half angle limit", depth);
					print_annotated_parm(&(nozzleConfig.outer_half_angle_limit), "outer half angle limit", depth);
				}
				break;
			case sCDI_NOZZLE_EMITTER_CONFIG::NOZZLETYPE_ELLIPTICAL_CONE:
				print_annotated_parm(&(nozzleConfig.major_half_angle), "major half angle", depth);
				if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && nozzleConfig.angle_distribution.value == DISTRIBUTION_GAUSSIAN)
					print_annotated_parm(&(nozzleConfig.major_outer_half_angle_limit), "major outer half angle limit", depth);
				print_annotated_parm(&(nozzleConfig.minor_half_angle), "minor half angle", depth);
				if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && nozzleConfig.angle_distribution.value == DISTRIBUTION_GAUSSIAN)
					print_annotated_parm(&(nozzleConfig.minor_outer_half_angle_limit), "minor outer half angle limit", depth);
				break;
			}

			PrintParticleGeneralParameterDistributionInfo(nozzleConfig.velocity_info, depth, "velocity");
			PrintParticleDiameterDistributionInfo(nozzleConfig.particle_diam_info, depth, cdi_info);
			print_annotated_parm(&(nozzleConfig.emission_rate_type), "emission rate type", depth);
			print_annotated_parm(&(nozzleConfig.emission_rate), "emission rate", depth);
		}

		else if (configurationType == cCDI_EMITTER_CONFIGURATIONS::RAIN) {

			const sCDI_RAIN_EMITTER_CONFIG& rainEmitterConfig = *(dynamic_cast<const sCDI_RAIN_EMITTER_CONFIG*>(emitterConfigPtrs[iec]));

			PrintParticleDiameterDistributionInfo(rainEmitterConfig.particle_diam_info, depth, cdi_info);
			print_annotated_parm(&(rainEmitterConfig.emission_rate_type), "emission rate type", depth);
			print_annotated_parm(&(rainEmitterConfig.emission_rate), "emission rate", depth);
			if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 11)) {
				print_annotated_parm(&(rainEmitterConfig.wind_x_velocity), "Wind X Velocity", depth);
				print_annotated_parm(&(rainEmitterConfig.wind_y_velocity), "Wind Y Velocity", depth);
				print_annotated_parm(&(rainEmitterConfig.wind_z_velocity), "Wind Z velocity", depth);
				print_annotated_int_((rainEmitterConfig.csysIndex), "Wind Velocity Coordinate System", depth);
				print_annotated_int_((rainEmitterConfig.refFrameIndex), "Reference frame index", depth);
				print_annotated_strg((rainEmitterConfig.refFrameName), "Reference frame name", depth);
			}
		}

		else if (configurationType == cCDI_EMITTER_CONFIGURATIONS::TIRE) {

			const sCDI_TIRE_EMITTER_CONFIG& tireEmitterConfig = *(dynamic_cast<const sCDI_TIRE_EMITTER_CONFIG*>(emitterConfigPtrs[iec]));

			print_annotated_parm(&(tireEmitterConfig.emission_rate_type), "emission rate type", depth);

			// In version 4.19, we changed the meaning of the emission rate to be per-tire-width.
			// We upgrade old tire emitter configurations in the read code, but can only do so when
			// the tire emitters have been read - which is *after* we dump the configurations.
			// So, we need to branch the code here to indicate exactly what is being dumped.
			if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19))
				print_annotated_parm(&(tireEmitterConfig.emission_rate), "emission rate per tire width", depth);
			else
				print_annotated_parm(&(tireEmitterConfig.emission_rate), "emission rate", depth);

			sCDI_PARM spatial_emission_distribution = tireEmitterConfig.GetSpatialEmissionDistributionType();
			print_annotated_parm(&spatial_emission_distribution, "spatial emission distribution", depth);
			if (spatial_emission_distribution.value == DISTRIBUTION_GAUSSIAN)
			{
				sCDI_PARM spatial_emission_standard_deviation = tireEmitterConfig.GetSpatialEmissionGaussianStandardDeviation();
				print_annotated_parm(&spatial_emission_standard_deviation, "spatial emission Gaussian standard deviation", depth);
			}
			else if (spatial_emission_distribution.value == DISTRIBUTION_1MX_POW_N)
			{
				sCDI_PARM spatial_emission_distribution_exponent = tireEmitterConfig.GetSpatialEmission1MXExponent();
				print_annotated_parm(&spatial_emission_distribution_exponent, "spatial emission [1-x]^n exponent", depth);
			}
			else if (spatial_emission_distribution.value == DISTRIBUTION_HALF_COSINE)
			{
				sCDI_PARM spatial_emission_distribution_exponent = tireEmitterConfig.GetSpatialEmissionHalfCosineExponent();
				print_annotated_parm(&spatial_emission_distribution_exponent, "spatial emission 1/2*[1+cos[PI*x^n]] exponent", depth);
			}
			print_annotated_parm(&(tireEmitterConfig.emission_rate_ratio), "emission rate ratio", depth);

			const std::vector<sCDI_TIRE_NOZZLE_PROPS>& tireNozzles = tireEmitterConfig.GetTireNozzles();

			if (!tireNozzles.empty())
			{
				char type_str[10];
				cio_type_to_string(tireNozzles[0].GetChunkType(), type_str);
				int tireNozzleNumber = 1;
				for (size_t inz = 0; inz < tireNozzles.size(); inz++)
				{
					print_indent(depth);
					printf("%s", type_str);
					printf(" { (tire nozzle %d)\n", tireNozzleNumber++);

					depth++;
					print_annotated_parm(&(tireNozzles[inz].tire_arc_position), "angular position", depth);
					PrintParticleGeneralParameterDistributionInfo(tireNozzles[inz].emission_offset_angle_info, depth, "emission offset angle");
					if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 19) && tireNozzles[inz].emission_offset_angle_info.GetDistributionType().value == DISTRIBUTION_GAUSSIAN)
						print_annotated_parm(&(tireNozzles[inz].outer_half_angle_limit), "outer half angle limit", depth);
					print_annotated_parm(&(tireNozzles[inz].transverse_stretch_factor), "transverse stretch factor", depth);
					PrintParticleGeneralParameterDistributionInfo(tireNozzles[inz].velocity_info, depth, "velocity");
					PrintParticleDiameterDistributionInfo(tireNozzles[inz].particle_diam_info, depth, cdi_info);

					depth--;
					print_indent(depth);
					printf("}\n");
				}
			}
		}

		depth--;
		print_indent(depth);
		printf("}\n");

	}  // End for

	depth--;

	print_indent(depth);
	printf("}\n");          // Close outer Emitter Configurations bracing
}



// =======================================================================================
// Particle Model - Emitters
// =======================================================================================

static void PrintBaseEmitter(const sCDI_PARTICLE_EMITTER_BASE& emitter, CDI_INFO cdi_info, int depth)
{
	print_indent(depth);
	printf("name { \"%s\" }\n", emitter.name.c_str());

	print_annotated_parm(&emitter.emitter_configuration, "emitter configuration", depth);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 7))
		print_annotated_parm(&emitter.particles_per_parcel, "particles per parcel", depth);
	print_annotated_parm(&emitter.subject_to_dispersion_box, "subject to dispersion box", depth);
	print_annotated_parm(&emitter.subject_to_gravity, "subject to gravity", depth);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 6, 3)) {
		print_indent(depth);
		printf("%d   (start time via: %s)\n", emitter.start_via, GetCDIEnumValueDescription(emitter.start_via).c_str());
		print_indent(depth);
		printf("%d   (number of monitors)\n", (int)emitter.monitors.size());
		for (size_t im = 0; im < emitter.monitors.size(); im++) {
			print_indent(depth);
			printf("   %5d (monitor %d)\n", emitter.monitors[im], static_cast<int>(im));
		}
		print_annotated_parm(&emitter.start, "start", depth);
		print_indent(depth);
		printf("%d   (end time via: %s)\n", emitter.end_via, GetCDIEnumValueDescription(emitter.end_via).c_str());
		print_annotated_parm(&emitter.end, "end", depth);
		print_annotated_parm(&emitter.duration, "duration", depth);
	}
	else {
		print_annotated_parm(&emitter.start, "start", depth);
		print_annotated_parm(&emitter.end, "end", depth);
	}

	{ // Even though 'frac_trajectory_recording' has been removed from sCDI_PARTICLE_EMITTER_BASE for 5.4b  (CDI v > 5.2), 
	  // we keep file format the same, so that undump_cdi 5.4a will have forward compatibility with files generated from 5.4b.
		sCDI_PARM fakeFracTrajRecord;
		fakeFracTrajRecord.value = 0.001;
		fakeFracTrajRecord.preferredUnit = "dimensionless";
		fakeFracTrajRecord.defaulted = true;
		print_annotated_parm(&fakeFracTrajRecord, "frac_trajectory_recording - for v5.3, deprecated here and moved to 'prtj' chunk", depth);
	}
	print_annotated_parm(&emitter.max_age, "max age", depth);
	print_annotated_parm(&emitter.max_num_reflections, "max num reflections", depth);
	print_annotated_parm(&emitter.min_particle_velocity, "min particle velocity", depth);
	print_annotated_parm(&emitter.visible, "visible", depth);
}


static void PrintNozzleOrientation(const sCDI_EMITTER_NOZZLE_ORIENTATION& orientation, int depth)
{
	print_annotated_parm(&orientation.spray_direction_csys, "spray direction csys", depth);
	print_annotated_parm(&orientation.mean_spray_direction[0], "mean spray direction X", depth);
	print_annotated_parm(&orientation.mean_spray_direction[1], "mean spray direction Y", depth);
	print_annotated_parm(&orientation.mean_spray_direction[2], "mean spray direction Z", depth);
	print_annotated_parm(&orientation.elliptical_nozzle, "elliptical nozzle", depth);
	if (orientation.elliptical_nozzle.value == 1)
	{
		print_annotated_parm(&orientation.major_ellipse_direction[0], "major ellipse direction X", depth);
		print_annotated_parm(&orientation.major_ellipse_direction[1], "major ellipse direction Y", depth);
		print_annotated_parm(&orientation.major_ellipse_direction[2], "major ellipse direction Z", depth);
	}
}


static void
PrintNozzleVisibilitySettings(const sCDI_EMITTER_NOZZLE_VISIBILITY_SETTINGS& visibilitySettings,
	CDI_INFO cdi_info, int depth)
{
	print_annotated_parm(&visibilitySettings.nozzle_show, "nozzle_show", depth);
	if ((visibilitySettings.nozzle_show.value == 1) ||
		CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5))
	{
		print_annotated_strg(visibilitySettings.nozzle_cone_look, "nozzle_cone_look", depth);
		print_annotated_strg(visibilitySettings.nozzle_body_color, "nozzle_body_color", depth);
		print_annotated_strg(visibilitySettings.nozzle_arrow_look, "nozzle_arrow_look", depth);
		print_annotated_parm(&visibilitySettings.nozzle_size, "nozzle_size", depth);
	}
}


static void
PrintEmissionBoundaryVisibilitySettings(const sCDI_EMITTER_EMISSION_BOUNDARY_VISIBILITY_SETTINGS& visibilitySettings,
	CDI_INFO cdi_info, int depth)
{
	print_annotated_parm(&visibilitySettings.emission_boundary_show, "emission_boundary_show", depth);
	if ((visibilitySettings.emission_boundary_show.value == 1) ||
		CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5))
	{
		print_annotated_strg(visibilitySettings.emission_boundary_look, "emission_boundary_look", depth);
		print_annotated_parm(&visibilitySettings.emission_boundary_size, "emission_boundary_size", depth);
	}
}


static void
PrintEmissionPointVisibilitySettings(const sCDI_EMITTER_EMISSION_POINT_VISIBILITY_SETTINGS& visibilitySettings,
	CDI_INFO cdi_info, int depth)
{
	print_annotated_bool(visibilitySettings.emission_point_show, "show emission points?", depth);
	if (visibilitySettings.emission_point_show || CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5))
	{
		print_annotated_strg(visibilitySettings.emission_point_color, "emission_point_color", depth);
		print_annotated_parm(&visibilitySettings.emission_point_size, "emission_point_size", depth);
	}
}


static void PrintCyl_Chunk(const sCDI_CYL_& cyl_, int depth)
{
	print_indent(depth);
	printf("name { \"%s\" }\n", cyl_.name.c_str());

	print_annotated_rgns(const_cast<std::vector<cdiINT32>&>(cyl_.region.regions), depth);

	print_annotated_parm(&(cyl_.start_point[0]), "start point X", depth);
	print_annotated_parm(&(cyl_.start_point[1]), "start point Y", depth);
	print_annotated_parm(&(cyl_.start_point[2]), "start point Z", depth);
	print_annotated_parm(&(cyl_.end_point[0]), "end point X", depth);
	print_annotated_parm(&(cyl_.end_point[1]), "end point Y", depth);
	print_annotated_parm(&(cyl_.end_point[2]), "end point Z", depth);
	print_annotated_parm(&(cyl_.start_radius), "start radius", depth);
	print_annotated_parm(&(cyl_.end_radius), "end radius", depth);
	print_annotated_parm(&(cyl_.num_sides), "num sides", depth);
}


static void PrintEmitterGeometries(const std::vector<sCDI_CYL_>* pCylGeoms, const std::vector<sCDI_BOX_>* pBoxGeoms, int depth)
{
	if (pCylGeoms)
	{
		char cyls_type_str[10];
		cio_type_to_string(CDI_CHUNK_TYPE_CYLS, cyls_type_str);
		print_indent(depth);
		printf("%s {\n", cyls_type_str);
		depth++;
		const std::vector<sCDI_CYL_>& cyl_s = *pCylGeoms;
		for (size_t ic = 0; ic < cyl_s.size(); ic++)
		{
			char cyl__type_str[10];
			cio_type_to_string(CDI_CHUNK_TYPE_CYL_, cyl__type_str);
			print_indent(depth);
			printf("%s {\n", cyl__type_str);
			PrintCyl_Chunk(cyl_s[ic], depth + 1);
			print_indent(depth);
			printf("}\n");
		}
		depth--;
		print_indent(depth);
		printf("}\n");
	}

	if (pBoxGeoms)
	{
		char boxs_type_str[10];
		cio_type_to_string(CDI_CHUNK_TYPE_BOXS, boxs_type_str);
		print_indent(depth);
		printf("%s", boxs_type_str);
		printf(" {\n");
		depth++;
		const std::vector<sCDI_BOX_>& box_s = *pBoxGeoms;
		for (size_t ib = 0; ib < box_s.size(); ib++)
		{
			char box__type_str[10];
			cio_type_to_string(CDI_CHUNK_TYPE_BOX_, box__type_str);
			print_indent(depth);
			printf(box__type_str);
			print_box_(box_s[ib], "emitter box geometry", depth);
		}
		depth--;
		print_indent(depth);
		printf("}\n");
	}
}


static void PrintSurfaceEmitter(const sCDI_SURFACE_EMITTER& emitter, CDI_INFO cdi_info, int depth)
{
	PrintBaseEmitter(emitter, cdi_info, depth);

	print_annotated_flst(const_cast<std::vector<int>&>(emitter.face_list), depth);

	print_annotated_parm(&emitter.fixed_release_points, "fixed release points", depth);
	print_annotated_parm(&emitter.user_specified_nozzle_orientation, "user specified nozzle orientation", depth);
	if (emitter.user_specified_nozzle_orientation.value == 1 || CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 16))
		PrintNozzleOrientation(emitter.nozzle_orientation, depth);

	PrintNozzleVisibilitySettings(emitter.nozzle_visibility_settings, cdi_info, depth);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
		PrintEmissionPointVisibilitySettings(emitter.emission_point_visibility_settings,
			cdi_info, depth);
}


static void PrintVolumeEmitter(const sCDI_VOLUME_EMITTER& emitter, CDI_INFO cdi_info, int depth)
{
	PrintBaseEmitter(emitter, cdi_info, depth);

	print_annotated_rgns(const_cast<std::vector<cdiINT32>&>(emitter.rgn_list), depth);

	PrintNozzleOrientation(emitter.nozzle_orientation, depth);

	print_annotated_parm(&emitter.fixed_release_points, "fixed release points", depth);
	if (emitter.fixed_release_points.value == 1)
	{
		print_annotated_parm(&(emitter.release_spacing[0]), "release_spacing X", depth);
		print_annotated_parm(&(emitter.release_spacing[1]), "release_spacing Y", depth);
		print_annotated_parm(&(emitter.release_spacing[2]), "release_spacing Z", depth);
	}

	PrintEmitterGeometries(&(emitter.m_cylGeometries), &(emitter.m_boxGeometries), depth);

	PrintNozzleVisibilitySettings(emitter.nozzle_visibility_settings, cdi_info, depth);
	PrintEmissionBoundaryVisibilitySettings(emitter.emission_boundary_visibility_settings,
		cdi_info, depth);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
		PrintEmissionPointVisibilitySettings(emitter.emission_point_visibility_settings,
			cdi_info, depth);
}


static void PrintPointEmitter(const sCDI_POINT_EMITTER& emitter, CDI_INFO cdi_info, int depth)
{
	PrintBaseEmitter(emitter, cdi_info, depth);

	print_indent(depth);
	printf("pnts");
	PrintVectorOfPoints(emitter.points, cdi_info, depth);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 22))
		print_annotated_int_(emitter.preferred_csys_index, "preferred coord sys index", depth);

	PrintNozzleOrientation(emitter.nozzle_orientation, depth);

	PrintNozzleVisibilitySettings(emitter.nozzle_visibility_settings, cdi_info, depth);
}


static void PrintTireEmitter(const sCDI_TIRE_EMITTER& emitter, CDI_INFO cdi_info, int depth)
{
	PrintBaseEmitter(emitter, cdi_info, depth);

	print_annotated_rgns(const_cast<std::vector<cdiINT32>&>(emitter.rgn_list), depth);

	print_annotated_parm(&(emitter.zero_angle_direction_csys), "zero angle direction csys", depth);
	print_annotated_parm(&(emitter.zero_angle_direction[0]), "zero angle direction X", depth);
	print_annotated_parm(&(emitter.zero_angle_direction[1]), "zero angle direction Y", depth);
	print_annotated_parm(&(emitter.zero_angle_direction[2]), "zero angle direction Z", depth);
	print_annotated_parm(&(emitter.approx_rotation_axis_dir[0]), "approx rotation axis dir X", depth);
	print_annotated_parm(&(emitter.approx_rotation_axis_dir[1]), "approx rotation axis dir Y", depth);
	print_annotated_parm(&(emitter.approx_rotation_axis_dir[2]), "approx rotation axis dir Z", depth);

	PrintEmitterGeometries(&(emitter.m_cylGeometries), NULL, depth);

	PrintNozzleVisibilitySettings(emitter.nozzle_visibility_settings, cdi_info, depth);
	PrintEmissionBoundaryVisibilitySettings(emitter.emission_boundary_visibility_settings,
		cdi_info, depth);

	print_annotated_parm(&emitter.tire_tread_show, "tire_tread_show", depth);
	if ((emitter.tire_tread_show.value == 1) || CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 5))
		print_annotated_strg(emitter.tire_tread_look, "tire_tread_look", depth);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
		PrintEmissionPointVisibilitySettings(emitter.emission_point_visibility_settings,
			cdi_info, depth);
}


static void PrintRainEmitter(const sCDI_RAIN_EMITTER& emitter, CDI_INFO cdi_info, int depth)
{
	PrintBaseEmitter(emitter, cdi_info, depth);

	print_annotated_rgns(const_cast<std::vector<cdiINT32>&>(emitter.rgn_list), depth);

	PrintEmitterGeometries(&(emitter.m_cylGeometries), &(emitter.m_boxGeometries), depth);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 21))
		PrintNozzleVisibilitySettings(emitter.nozzle_visibility_settings, cdi_info, depth);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 1))
		PrintEmissionPointVisibilitySettings(emitter.emission_point_visibility_settings,
			cdi_info, depth);
}


static void PrintEmitters(CDI_INFO cdi_info, int depth)
{
	cCDI_PARTICLE_EMITTERS emitters;
	emitters.ReadFromCDI(cdi_info);
	const std::vector<const sCDI_PARTICLE_EMITTER_BASE*>& emitterPtrs = emitters.GetAllEmitters();

	int emitterCount = 0;

	printf(" {\n");      // Open outer Emitters bracing
	depth++;


	std::map<int, std::string> emitterTypeStrings;
	emitterTypeStrings[cCDI_PARTICLE_EMITTERS::EMITTER_SURFACE] = "surface";
	emitterTypeStrings[cCDI_PARTICLE_EMITTERS::EMITTER_VOLUME] = "volume";
	emitterTypeStrings[cCDI_PARTICLE_EMITTERS::EMITTER_POINT] = "point";
	emitterTypeStrings[cCDI_PARTICLE_EMITTERS::EMITTER_TIRE] = "tire";
	emitterTypeStrings[cCDI_PARTICLE_EMITTERS::EMITTER_RAIN] = "rain";


	for (size_t ie = 0; ie < emitterPtrs.size(); ie++)
	{
		char type_str[10];
		cio_type_to_string(emitterPtrs[ie]->GetChunkType(), type_str);
		print_indent(depth);
		printf("%s", type_str);

		printf(" { (emitter %d)\n", emitterCount++);
		depth++;

		cCDI_PARTICLE_EMITTERS::eCDI_EMITTER_TYPE emitterType = emitterPtrs[ie]->GetType();
		sCDI_PARM emitterTypeParm;
		emitterTypeParm.value = emitterType;
		print_annotated_parm(&emitterTypeParm, "emitter type - " + emitterTypeStrings[emitterType], depth);

		switch (emitterType) {
		case cCDI_PARTICLE_EMITTERS::EMITTER_SURFACE:
			PrintSurfaceEmitter(*(dynamic_cast<const sCDI_SURFACE_EMITTER*>(emitterPtrs[ie])), cdi_info, depth);
			break;
		case cCDI_PARTICLE_EMITTERS::EMITTER_VOLUME:
			PrintVolumeEmitter(*(dynamic_cast<const sCDI_VOLUME_EMITTER*>(emitterPtrs[ie])), cdi_info, depth);
			break;
		case cCDI_PARTICLE_EMITTERS::EMITTER_POINT:
			PrintPointEmitter(*(dynamic_cast<const sCDI_POINT_EMITTER*>(emitterPtrs[ie])), cdi_info, depth);
			break;
		case cCDI_PARTICLE_EMITTERS::EMITTER_TIRE:
			PrintTireEmitter(*(dynamic_cast<const sCDI_TIRE_EMITTER*>(emitterPtrs[ie])), cdi_info, depth);
			break;
		case cCDI_PARTICLE_EMITTERS::EMITTER_RAIN:
			PrintRainEmitter(*(dynamic_cast<const sCDI_RAIN_EMITTER*>(emitterPtrs[ie])), cdi_info, depth);
			break;
		default:
			continue;
		}

		depth--;
		print_indent(depth);
		printf("}\n");
	}

	depth--;
	print_indent(depth);
	printf("}\n");          // Close outer Emitters bracing
}


void print_srmi(CDI_INFO& info, CDI_SRMI srmi, int depth)
{
	static int srmiNum = 1;
	printf(" { ( particle material interaction %d)\n", srmiNum++);
	depth++;
	print_annotated_parm(&(srmi->particle_material), "particle material", depth);
	print_annotated_parm(&(srmi->splash_model), "splash model", depth);
	if (CDI_INFO_VERSION_AT_LEAST(info, 4, 2)) {
		print_annotated_parm(&(srmi->enable_reflection), "enable reflection", depth);
	}
	print_annotated_parm(&(srmi->reflect_min_momentum), "reflect min momentum", depth);
	print_annotated_parm(&(srmi->reflect_min_normal_vel), "reflect min normal velocity", depth);
	print_annotated_parm(&(srmi->reflect_min_angle), "reflect min angle", depth);
	print_annotated_parm(&(srmi->normal_rest_coeff), "normal restitution coefficient", depth);
	print_annotated_parm(&(srmi->tang_restitution_coeff), "tangential restitution coefficient", depth);
	print_annotated_parm(&(srmi->scatter_angle_distribution), "scatter angle distribution", depth);
	print_annotated_parm(&(srmi->scatter_angle_range), "scatter angle range", depth);
	depth--;
	print_indent(depth);
	printf("}\n");

}

void print_srmi(CDI_INFO cdi_info, int depth)
{
	sCDI_SRMI srmi;
	cdi_read_srmi(cdi_info, &srmi);
	print_srmi(cdi_info, &srmi, depth);
}

void print_scrn(CDI_SCRN scrn, CDI_INFO cdi_info, int depth)
{
	static int screenNumber = 1;
	printf(" { (screen %d)\n", screenNumber++);
	depth++;
	print_indent(depth);
	printf("name { \"%s\" }\n", scrn->name.c_str());
	sCDI_FLST flst;
	flst.n_face = scrn->face_list.size();
	flst.face = &(scrn->face_list[0]);
	print_indent(depth);
	printf("flst");
	print_flst(&flst, depth);
	print_annotated_parm(&(scrn->opening_size), "opening size", depth);
	print_annotated_parm(&(scrn->pass_thru_fraction), "pass thru fraction", depth);
	print_annotated_parm(&(scrn->surface_material), "surface material", depth);
  if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 7, 26))
    print_annotated_parm(&(scrn->measured_fraction), "fraction of pass through for measurement", depth); //PR_42469
	depth--;
	print_indent(depth);
	printf("}\n");
}

void print_scrn(CDI_INFO cdi_info, int depth)
{
	sCDI_SCRN scrn;
	cdi_read_scrn(cdi_info, &scrn);
  print_scrn(&scrn, cdi_info, depth);
}


// =======================================================================================
// Autostop
// =======================================================================================


// Vehicle ...............................................................................
void print_vhcl(CDI_INFO cdi_info, int depth)
{
	sCDI_VHCL vhcl;
	cdi_read_vhcl(cdi_info, &vhcl);

	printf(" {\n");
	depth++;

	print_annotated_int_(vhcl.csys_index, "csys index", depth);
	print_annotated_enum(vhcl.moments_via, "moments via", depth);

	print_annotated_enum(vhcl.centerline_direction, "centerline dir", depth);
	print_annotated_enum(vhcl.up_direction, "up dir", depth);
	print_annotated_enum(vhcl.side_force_direction, "side force dir", depth);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 14))
		print_annotated_bool(vhcl.is_front_axle_applicable,
			"front axle applicable", depth);
	print_annotated_int_(vhcl.front_axle_preferred_csys_index,
		"front axle preferred csys index", depth);
	print_annotated_dbls((idFLOAT*)vhcl.front_axle_origin, 3,
		"front axle origin", depth);
	print_annotated_dbls((idFLOAT*)vhcl.front_axle_dir, 3,
		"front axle dir", depth);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 14))
		print_annotated_bool(vhcl.is_rear_axle_applicable,
			"rear axle applicable", depth);
	print_annotated_int_(vhcl.rear_axle_preferred_csys_index,
		"rear axle preferred csys index", depth);
	print_annotated_dbls((idFLOAT*)vhcl.rear_axle_origin, 3,
		"rear axle origin", depth);
	print_annotated_dbls((idFLOAT*)vhcl.rear_axle_dir, 3,
		"rear axle dir", depth);

	print_annotated_dbls((idFLOAT*)vhcl.moment_center, 3,
		"moment center", depth);
	print_annotated_int_(vhcl.floor_part_index,
		"floor part index", depth);
	print_annotated_dbls((idFLOAT*)vhcl.floor_point, 3,
		"floor point", depth);

	if (!CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 14)) {
		// VHCL chunk temporarily had a boolean 'user_defined_char_values' and parm 'char_area'
		print_annotated_bool(false, "obsolete user-defined char values", depth);
		sCDI_PARM dummy_parm;
		print_annotated_parm(&dummy_parm, "obsolete char area", depth);
	}
	print_annotated_parm(&(vhcl.wheelbase), "wheelbase", depth);

	depth--;
	print_indent(depth);
	printf("}\n");
}

// Averaged Meas Window  ...............................................................................
void print_amw_(CDI_INFO cdi_info, int depth)
{
	sCDI_AMW_ amw_;
	cdi_read_amw_(cdi_info, &amw_);

	printf(" {\n");
	depth++;

	print_annotated_strg(amw_.name, "name", depth);

	print_annotated_int_(amw_.meas_window_to_average, "meas window to average", depth);
	print_annotated_bool(amw_.average_fnc, "average fluid?", depth);
	print_annotated_bool(amw_.average_pnc, "average porous?", depth);
	print_annotated_bool(amw_.average_snc, "average surface?", depth);
	print_annotated_enum(amw_.start_via, "start via", depth);
	print_annotated_parm(&(amw_.start), "start", depth);
	print_annotated_int_(amw_.monitors.size(), "num monitors", depth);
	ccDOTIMES(i, amw_.monitors.size())
		print_annotated_int_(amw_.monitors[i], "monitor index", depth);
	print_annotated_enum(amw_.end_via, "end via", depth);
	print_annotated_parm(&(amw_.end), "end", depth);
	print_annotated_enum(amw_.avg_interval_via, "averaging interval via", depth);
	print_annotated_parm(&(amw_.avg_interval), "averaging interval", depth);

	depth--;
	print_indent(depth);
	printf("}\n");
}

// Monitors ..............................................................................
void sCDI_MNTR::Dump(CDI_INFO cdi_info, int depth) const
{
	print_annotated_enum(monitor_type, "monitor type", depth);

	print_annotated_strg(name, "monitor name", depth);
	print_annotated_strg(preferred_time_unit, "preferred time unit", depth);

	// Flow monitor-specific parameters
	print_annotated_int_(meas_window_index, "measurement window index", depth);
	print_annotated_enum(variable_source, "variable source", depth);
	print_annotated_enum(flow_variable, "flow variable", depth);
	print_annotated_bool(variable_is_vector_component, "vector component?", depth);
	print_annotated_int_(reference_csys, "reference coordinate system", depth);
	print_annotated_bool(variable_is_moment_component, "moment component?", depth);
	print_annotated_dbls((idFLOAT*)reference_point, 3, "reference point", depth);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 15))
		print_annotated_bool(use_wheelbase, "use wheelbase for dimless vehicle moments?", depth);

	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 11)) {
		// Yucky const_cast, but we're definitely NOT going to write to these here.
		print_annotated_enum(region_filtering, "region filter sense", depth);
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,7>(cdi_info)) {
      print_annotated_geos(region_filtering_tree, cdi_info, depth);
    }
    else {
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 0))
        print_annotated_int_(region_filtering_tree.PartitionIndex() - 1, "region filter partition", depth);
      print_annotated_rgns(const_cast<std::vector<cdiINT32>&>(region_filtering_tree.m_selections.rgn_list), depth);
    }
    print_annotated_enum(face_filtering, "face filter sense", depth);
    if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,7>(cdi_info)) {
      print_annotated_geos(face_filtering_tree, cdi_info, depth);
    }
    else {
      if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 5, 0))
        print_annotated_int_(face_filtering_tree.PartitionIndex() - 1, "face filter partition", depth);
      print_annotated_flst(const_cast<std::vector<int>&>(face_filtering_tree.m_selections.face_list), depth);
    }
	}

	// HX monitor-specific parameters
	print_annotated_int_(heat_exchanger_index, "heat exchanger index", depth);
	print_annotated_enum(heat_exchanger_variable, "heat exchanger variable", depth);

	// PowerTHERM monitor-specific parameters
	print_annotated_int_(powertherm_model_index, "powertherm model index", depth);
	print_annotated_strg(coupled_powertherm_part, "coupled powertherm part", depth);
	print_annotated_enum(powertherm_part_side, "powertherm part side", depth);
	print_annotated_bool(coupled, "coupled?", depth);
	print_annotated_enum(powertherm_variable, "powertherm variable", depth);

	// Signal Analysis parameters
	print_annotated_enum(signal_analysis, "signal analysis type", depth);
	print_annotated_bool(automatically_stop, "automatically stop", depth);
	if (classic_autostop_algorithm) {
		// Apparently, some users are getting upset that when classic autostop is ON, the 'analysis scheme'
		// dump doesn't match the 'classic autostop analysis scheme' dump, so we do this little hack for them.  (Argh)
		// We *could* write just one scheme type, depending on whether classic autostop is activated, but that is
		// written farther down in the file, so on read, we wouldn't yet know which scheme to read when we got to
		// this part of the file.  (Double Argh).
		// And the whole thing might not be an issue if the classic autostop scheme types had an enum associated with
		// because then the dump format would be more clear to the user.  (Final Argh)
		print_indent(depth);
		printf("enum");
		print_enum_helper(analysis_scheme, "analysis scheme", "[see classic autostop analysis scheme]");
	}
	else {
		if (GetCDIEnumValueDescription(analysis_scheme) == "Aero Drag (Best Practice)") {
			// Yuck: special hack for one of the Analysis Schemes: "Aero Drag (Best Practice)"
			// The lexer used during undump gets confused by the trailing ")" errors out.
			// So, we strip away the "(" and ")" here.
			// (We should probably add a check and assert in the dump routines to catch this error in the future)
			print_indent(depth);
			printf("enum");
			print_enum_helper(analysis_scheme, "analysis scheme", "Aero Drag - Best Practice");
		}
		else
			print_annotated_enum(analysis_scheme, "analysis scheme", depth);
	}
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 18))
		print_annotated_strg(classic_autostop_analysis_scheme, "classic autostop analysis scheme", depth);
	print_annotated_enum(signal_conv_criteria, "signal convergence criteria", depth);

	print_annotated_enum(monitor_flow_pass_via, "monitor flow pass via", depth);
	print_annotated_int_(coupling_phase, "coupling phase", depth);
	print_annotated_parm(&monitor_flow_pass, "monitor flow pass", depth);
	print_annotated_enum(initial_transient_determined_via, "initial transient determined via", depth);
	print_annotated_enum(minimum_initial_transient_via, "minimum initial transient via", depth);
	print_annotated_parm(&minimum_initial_transient, "minimum initial transient", depth);
	print_annotated_enum(end_of_initial_transient_via, "end of initial transient via", depth);
	print_annotated_parm(&end_of_initial_transient, "end of initial transient", depth);
	print_annotated_enum(initial_transient_variance_window_via, "initial transient variance window via", depth);
	print_annotated_parm(&initial_transient_variance_window, "initial transient variance window", depth);

	// Accuracy/Confidence params
	print_annotated_enum(desired_accuracy_via, "desired accuracy via", depth);
	if (desired_accuracy_via == eCDI_MNTR_ACCY_VIA::PercentageOfMean) {
	  if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,13>(cdi_info))
	    print_annotated_parm(&desired_accuracy_percentage, "desired accuracy percentage", depth);
	  else
	    print_annotated_parm(&desired_accuracy, "desired accuracy", depth);
	} else if (cdi_version_is_at_least_and_not_parallel_dev_cdi<8,13>(cdi_info) && (desired_accuracy_via == eCDI_MNTR_ACCY_VIA::LesserOfValueAndPercentageOfMean
	        || desired_accuracy_via == eCDI_MNTR_ACCY_VIA::GreaterOfValueAndPercentageOfMean)) {
	  print_annotated_parm(&desired_accuracy, "desired accuracy", depth);
	  print_annotated_parm(&desired_accuracy_percentage, "desired accuracy percentage", depth);
	} else
	  print_annotated_parm(&desired_accuracy, "desired accuracy", depth);
	print_annotated_enum(confidence_level_via, "confidence level via", depth);
	print_annotated_dbls((idFLOAT*)&custom_confidence_level, 1, "custom confidence level", depth);
	print_annotated_enum(minimum_averaging_time_via, "minumum averaging time via", depth);
	print_annotated_parm(&minimum_averaging_time, "minumum averaging time", depth);

	// Stabilization Window params
	print_annotated_enum(stabilization_window_via, "stabilization window via", depth);
	print_annotated_parm(&stabilization_window, "stabilization window", depth);
	print_annotated_bool(enable_subwindows, "enable subwindows", depth);
	print_annotated_enum(subwindow_via, "subwindow via", depth);
	print_annotated_parm(&subwindow, "subwindow", depth);
	print_annotated_enum(subwindow_range_limit_via, "subwindow range limit via", depth);
	print_annotated_parm(&subwindow_range_limit, "subwindow range limit", depth);

	// Classic Autostop Support
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 17))
		print_annotated_bool(classic_autostop_algorithm, "classic autostop algorithm", depth);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 13)) {
		print_annotated_parm(&variance_gradient_limit, "variance gradient limit", depth);
		print_annotated_parm(&creep_limit, "creep limit", depth);
	}
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 18))
		print_annotated_enum(running_average_gradient_limit_via, "running average gradient limit via", depth);
	if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 13)) {
		print_annotated_parm(&running_average_gradient_limit, "running average gradient limit", depth);
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 20))
			print_annotated_enum(running_average_gradient_interval_via, "running average gradient interval via", depth);
		print_annotated_parm(&running_average_gradient_interval, "running average gradient interval", depth);
		if (CDI_INFO_VERSION_AT_LEAST(cdi_info, 4, 20))
			print_annotated_enum(signal_processing_period_via, "signal processing period via", depth);
		print_annotated_parm(&signal_processing_period, "signal processing period", depth);
	}
}


static void print_mnts(CDI_INFO cdi_info, int depth)
{
	cCDI_MONITORS monitors;
	monitors.ReadFromCDI(cdi_info);

	printf(" {\n");      // Open outer Monitors bracing
	depth++;

	unsigned monitorCount = 0;
	for (size_t im = 0; im < monitors.m_monitors.size(); im++) {

		char type_str[10];
		cio_type_to_string(monitors.m_monitors[im].GetChunkType(), type_str);
		print_indent(depth);
		printf("%s", type_str);
		printf(" { (monitor %d)\n", monitorCount++);
		depth++;

		monitors.m_monitors[im].Dump(cdi_info, depth);

		depth--;
		print_indent(depth);
		printf("}\n");
	}

	depth--;
	print_indent(depth);
	printf("}\n");       // Close outer Monitors bracing
}



void print_mmbr(CDI_MMBR mmbr, const std::string& comment)
{
	printf(" { \"%s\" %.*g \"%s\" \"%s\"}", mmbr->name.c_str(), uDUMP_CDI_DFLOAT_DIG, mmbr->value, mmbr->unit.c_str(), mmbr->unitclass.c_str());
	if (!comment.empty())
		printf(" ( %s )", comment.c_str());

	printf("\n");
}

void print_mmbr(CDI_INFO cdi_info)
{
	sCDI_MMBR mmbr;
	cdi_read_mmbr(cdi_info, &mmbr);
	print_mmbr(&mmbr, "");
}

void print_aftd(CDI_INFO cdi_info, int depth)
{
	sCDI_AFTD* aftd = cdi_read_aftd(cdi_info);

	printf(" { %lf    (acceleration factor)", aftd->acceleration_factor);

	print_indent(depth);
	printf("}\n");

	exa_free(aftd);
}

void print_look(const sCDI_LOOK& look, int depth)
{
	printf(" {\n");
	print_indent(depth + 1);
	printf("\"%s\"   (name) \n", look.name.c_str());
	print_indent(depth + 1);
	printf("\"%s\"    (type)\n", look.type.c_str());
	print_indent(depth + 1);
	printf("\"%s\"    (origin)\n", look.origin.c_str());

	print_indent(depth + 1);
	std::string encSerializedLook = EXA_STR::Encode64((const char*)&look.serializedLook[0],
		look.serializedLook.size());
	printf("\"%s\"\n", encSerializedLook.c_str());

	print_indent(depth);
	printf("}\n");
}

void print_look(CDI_INFO cdi_info, int depth)
{
	sCDI_LOOK look;
	cdi_read_realistic_look(cdi_info, &look);
	print_look(look, depth);
}


/****************************************************************\
|
| Function name:print_wpdt
|
| Purpose:prints wpdt
|
\****************************************************************/
void
print_wpdt(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a wpdt */
	CDI_WPDT wpdt = cdi_read_wpdt(cdi_info);
	int i, imax = 3 * wpdt->n_timesteps;
	int timeStep = 0;

	printf(" { %d    (wind profile data table index)\n", wpdt->index);
	print_indent(depth + 1);
	printf("     \"%s\"  (wind profile data filename)\n", wpdt->filename);
	print_indent(depth + 1);
	printf("     %d    (number of timesteps)\n", wpdt->n_timesteps);
	print_indent(depth + 1);
	printf("     ( Vx              Vy             Vz)\n");
	for (i = 0; i < imax; i += 3) {
		print_indent(depth + 1);
		printf("%*.*g %*.*g %*.*g   (timestep %d)\n",
			DFLOAT_DIG + 4,
			uDUMP_CDI_DFLOAT_DIG,
			wpdt->velocity[i],
			DFLOAT_DIG + 4,
			uDUMP_CDI_DFLOAT_DIG,
			wpdt->velocity[i + 1],
			DFLOAT_DIG + 4,
			uDUMP_CDI_DFLOAT_DIG,
			wpdt->velocity[i + 2],
			timeStep++);
	}
	print_indent(depth);
	printf("}\n");

	/* free the wpdt memory */
	cdi_destroy_wpdt(wpdt);
}

void
print_tire(CDI_INFO cdi_info, int depth)
{
	const static int mod_count = 5;
	CDI_TIRE tire = cdi_read_tire(cdi_info);

	printf(" {\n");
	print_indent(depth + 1);
	printf("%d (csys index)\n", tire->csys_index);
	print_indent(depth + 1);
	printf("%d (grooved tire)\n", tire->grooved_tire);
	print_indent(depth + 1);
	printf("%d (number of u knots)\n", tire->n_knots[0]);
	print_indent(depth + 1);
	printf("%d (number of v knots)\n", tire->n_knots[1]);

	print_indent(depth + 1);
	printf("(u knots)");
	for (int i = 0; i < tire->n_knots[0]; i++) {
		if (i % mod_count == 0) {
			printf("\n");
			print_indent(depth + 1);
		}
		printf("%.*g ",
			uDUMP_CDI_DFLOAT_DIG,
			tire->u_knots[i]);
	}
	printf("\n");

	print_indent(depth + 1);
	printf("(v knots)");
	for (int i = 0; i < tire->n_knots[1]; i++) {
		if (i % mod_count == 0) {
			printf("\n");
			print_indent(depth + 1);
		}
		printf("%.*g ",
			uDUMP_CDI_DFLOAT_DIG,
			tire->v_knots[i]);
	}
	printf("\n");

	int n_coeff = cdi_tire_surface_coeff_array_size(3, tire->n_knots[0], tire->n_knots[1]);
	print_indent(depth + 1);
	printf("(carcass coefficients)");
	for (int i = 0; i < n_coeff; i++) {
		if (i % mod_count == 0) {
			printf("\n");
			print_indent(depth + 1);
		}
		printf("%.*g ",
			uDUMP_CDI_DFLOAT_DIG,
			tire->coeff_carcass[i]);
	}
	printf("\n");

	if (!tire->grooved_tire) {
		print_indent(depth + 1);
		printf("(wrap coefficients)");
		for (int i = 0; i < n_coeff; i++) {
			if (i % mod_count == 0) {
				printf("\n");
				print_indent(depth + 1);
			}
			printf("%.*g ",
				uDUMP_CDI_DFLOAT_DIG,
				tire->coeff_wrap[i]);
		}
		printf("\n");
	}

	print_indent(depth);
	printf("}\n");

	cdi_destroy_tire(tire);
}


/****************************************************************\
|
| Function name:print_wpdt
|
| Purpose:prints wpdt
|
\****************************************************************/

/****************************************************************\
|
| Function name:print_clbt
|
| Purpose:prints clbr
|
\****************************************************************/
void
print_clbr(CDI_INFO cdi_info, int depth)
{
	/* allocate and read a wpdt */
	CDI_CLBR clbr = cdi_read_clbr(cdi_info);

	printf(" { %d (inlet face index) \n", clbr->face_index);
	print_indent(depth + 1);
	printf(" %d (measurement window index) \n", clbr->meas_window_index);
	print_indent(depth + 1);
	printf(" %d (calibration iterations) \n", clbr->calibration_iterations);
	print_indent(depth + 1);
	printf(" %d (Remove pressure fluctuations) \n", clbr->cancel_pressure_fluctuations);
	print_indent(depth + 1);
	printf(" %d (Subtract mean velocity) \n", clbr->subtract_mean_velocity);
	print_indent(depth + 1);
	printf(" %d (Reset the initial condition to first iteration) \n", clbr->reset_initial_condition);
	print_indent(depth + 1);
	printf("}\n");

	/* free the clbr memory */
	cdi_destroy_clbr(clbr);
}

void PrintPowerByInfo(CDI_INFO cdi_info, int depth)
{
	sCDI_PWBY pbyInfo;
	pbyInfo.ReadFromCDI(cdi_info);

	printf(" {\n");
	depth++;
	print_annotated_bool(pbyInfo.m_isAPowerByFile, "Is From PowerBy?", depth);
	print_annotated_strg(pbyInfo.m_itemName, "item Name", depth);
	print_annotated_strg(pbyInfo.m_itemId, "item Id", depth);
	depth--;
	print_indent(depth);
	printf("}\n");
}

/****************************************************************\
|
| Function name:dump_chunk
|
| Purpose:dumps chunk
|
\****************************************************************/
CIO_ERRCODE dump_chunk(CDI_INFO cdi_info, int depth, std::vector<vertStruct*> &vertVec, std::vector<facetStruct*> &facetVec,  std::string filtersString)
{

	bool end_of_chunk = false;
	CIO_INFO cio = cdi_info->cio_info;
	CIO_CCCC type;
	char type_str[10];
	int i;
	static int srpt_already = FALSE;
	if (cio_descend(cio) == CIO_ERR_FAIL)
		return CIO_ERR_FAIL;

	type = cio_get_type(cio);
	cio_type_to_string(type, type_str);

	std::string tt(&type_str[0], 4);

	if (!filtersString.empty() &&
		filtersString.find(std::string(type_str)) != std::string::npos) {
		//Skip this chunk
		if (CDI_CHUNK_TYPE_REGN == type)
			++Regn_number;

		if (CDI_CHUNK_TYPE_RGND == type)
			++Regn_Def_number;

		if (CDI_CHUNK_TYPE_SRPT == type) {
			Phys_number = 0;
			srpt_already = TRUE;
		}

		if (CDI_CHUNK_TYPE_PHYS == type && srpt_already)
			++Phys_number;

		return cio_ascend(cio);
	}

	/*print_indent(depth);
	printf("%s", type_str);

	if (CDI_CHUNK_TYPE_REGN == type)
		printf(" (region number %d)", Regn_number++);

	if (CDI_CHUNK_TYPE_RGND == type)
		printf(" (region number %d)", Regn_Def_number++);

	if (CDI_CHUNK_TYPE_SRPT == type) {
		Phys_number = 0;
		srpt_already = TRUE;
	}

	if (CDI_CHUNK_TYPE_PHYS == type && srpt_already) {
		printf(" (surface physics %d)", Phys_number++);
	}*/

	switch (type) {
		/* look for children */
	case CDI_CHUNK_TYPE_XCDI:
	case CDI_CHUNK_TYPE_CASE:
	case CDI_CHUNK_TYPE_REGN:
	case CDI_CHUNK_TYPE_BODY:
	case CDI_CHUNK_TYPE_SHLL:
	case CDI_CHUNK_TYPE_MSTT:
	case CDI_CHUNK_TYPE_PLRF:
	case CDI_CHUNK_TYPE_IVAL:
	case CDI_CHUNK_TYPE_DPRM:
	case CDI_CHUNK_TYPE_PHYS:
	case CDI_CHUNK_TYPE_PHRG:
	case CDI_CHUNK_TYPE_GSCL:
	case CDI_CHUNK_TYPE_GFDS:
	case CDI_CHUNK_TYPE_GMCV:
  case CDI_CHUNK_TYPE_GGAP:
	case CDI_CHUNK_TYPE_MESR:
	case CDI_CHUNK_TYPE_FLUD:
	case CDI_CHUNK_TYPE_SOLD:
	case CDI_CHUNK_TYPE_MOVB:
	case CDI_CHUNK_TYPE_FPSS:
	case CDI_CHUNK_TYPE_ISLD:
	case CDI_CHUNK_TYPE_BODF:
	case CDI_CHUNK_TYPE_BFDF:
	case CDI_CHUNK_TYPE_BFPR:
	case CDI_CHUNK_TYPE_PCFG:
	case CDI_CHUNK_TYPE_GRID:
	case CDI_CHUNK_TYPE_MEAS:
	case CDI_CHUNK_TYPE_SRPT:
	case CDI_CHUNK_TYPE_VPTS:
	case CDI_CHUNK_TYPE_COUP:
	case CDI_CHUNK_TYPE_GLOB:
	case CDI_CHUNK_TYPE_FTAB:
	case CDI_CHUNK_TYPE_NIRF:
	case CDI_CHUNK_TYPE_FRAM:
	case CDI_CHUNK_TYPE_GRAV:
	case CDI_CHUNK_TYPE_GRVF:
	case CDI_CHUNK_TYPE_LRFS:
	case CDI_CHUNK_TYPE_SLRF:
	case CDI_CHUNK_TYPE_MLRF:
	case CDI_CHUNK_TYPE_CYLS:
	case CDI_CHUNK_TYPE_VREV:
	case CDI_CHUNK_TYPE_CYLR:
	case CDI_CHUNK_TYPE_BOXS:
	case CDI_CHUNK_TYPE_BOX_:
	case CDI_CHUNK_TYPE_TBLS:
	case CDI_CHUNK_TYPE_RGDF:
	case CDI_CHUNK_TYPE_RGND:
	case CDI_CHUNK_TYPE_SPRG:
	case CDI_CHUNK_TYPE_SCPL:
	case CDI_CHUNK_TYPE_INCM:
	case CDI_CHUNK_TYPE_INCR:
	case CDI_CHUNK_TYPE_SGDF:
	case CDI_CHUNK_TYPE_SGMT:
	case CDI_CHUNK_TYPE_PSDF:
	case CDI_CHUNK_TYPE_PRTN:
	case CDI_CHUNK_TYPE_PSGS:
	case CDI_CHUNK_TYPE_PSEG:
	case CDI_CHUNK_TYPE_MVDF:
	case CDI_CHUNK_TYPE_TRAC:
	case CDI_CHUNK_TYPE_PRMS:
	case CDI_CHUNK_TYPE_SRMS:
	case CDI_CHUNK_TYPE_SRFM:
	case CDI_CHUNK_TYPE_SCRS:
	case CDI_CHUNK_TYPE_WIPS:
	case CDI_CHUNK_TYPE_TIGR:
	case CDI_CHUNK_TYPE_VSTR:
	case CDI_CHUNK_TYPE_VSRS:
	case CDI_CHUNK_TYPE_CMPS:
	case CDI_CHUNK_TYPE_RLKS:
	case CDI_CHUNK_TYPE_WPTS:
	case CDI_CHUNK_TYPE_AMWS:
	case CDI_CHUNK_TYPE_BSRS: {
		auto count = cio_get_count(cio);
		//printf(" {\n");
		for (i = 0; i < count; i++) {
			CIO_ERRCODE error = dump_chunk(cdi_info, depth + 1, vertVec, facetVec);
			if (error == CIO_ERR_FAIL) {
				end_of_chunk = true;
				break;
			}
		}
		//print_indent(depth);
		//printf("}");
		break; }
	}

	if (end_of_chunk)
		return CIO_ERR_SUCCESS;

	switch (type) {
	case CDI_CHUNK_TYPE_SGID: {/*print_sgid(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_PRTT: {/*print_prtt(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_PTGE: {/*print_ptge(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_BBOX: {/*print_bbox(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_CMNT: {/*print_cmnt(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_AUDT: {/*print_audt(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_UNDB: {/*print_undb(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_EQNS: {/*print_eqns(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_GTBL: {/*print_gtbl(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_TABL: {/*print_tabl(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_CSYS: {/*print_csys(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_NULL: {/*print_null(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_NAME: {/*print_name(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_BOOL: {/*print_bool(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_INT_: {/*print_int_(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_ENUM: {/*print_enum(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_STRG: {/*print_strg(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_OFFS: {/*print_offs(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_PRGN: {/*print_prgn(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_RGPN: {/*print_rgpn(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_VRTX: {print_vrtx(cdi_info, depth, vertVec); break; }
	case CDI_CHUNK_TYPE_EDGE: {/*print_edge(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_FACE: {/*print_face(cdi_info, depth); */break; }
	case CDI_CHUNK_TYPE_MDLV: {/*print_mdlv(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_PSMV: {/*print_psmv(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_FACT: {print_fact(cdi_info, depth, facetVec); break; }
	case CDI_CHUNK_TYPE_MPRM: {/*print_mprm(cdi_info, depth)*/; break; }
	case CDI_CHUNK_TYPE_MFLT: {/*print_mflt(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_MREF: {/*print_mref(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_MDEV: {/*print_mdev(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_MSTP: {/*print_mstp(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_PTYP: {/*print_ptyp(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_HXCH: {/*print_hxch(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_AMHX: {/*print_amhx(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_BSRG: {/*print_bsrg(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_VPNT: {/*print_vpnt(cdi_info, depth); */break; }
	case CDI_CHUNK_TYPE_CDSR: {/*print_cdsr(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_IVDP: {/*print_ivdp(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_CVDP: {/*print_cvdp(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_BSDP: {/*print_bsdp(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_PNTS: {/*print_pnts(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_EQDP: {/*print_eqdp(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_RGNS: {/*print_rgns(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_FDLT: {/*print_fdlt(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_GFAR: {/*print_gfar(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_GSCC: {/*print_gscc(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_GSEP: {/*print_gsep(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_SIMV: {/*print_simv(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_THMA: {/*print_thma(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_SYMP: {/*print_symp(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_GHDR: {/*print_ghdr(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_UNIT: {/*print_unit(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_UUNT: {/*print_uunt(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_CPRP: {/*print_cprp(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_CPNT: {/*print_cpnt(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_MENT: {/*print_ment(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_PRBE: {/*print_prbe(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_PRTJ: {/*print_prtj(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_FLST: {/*print_flst(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_MFAC: {/*print_mfac(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_RGCT: {/*print_rgct(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_RGNN: {/*print_rgnn(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_RGDP: {/*print_rgdp(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_GRDF: {/*print_grdf(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_PRDF: {/*print_prdf(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_PREC: {/*print_prec(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_SCCT: {/*print_scct(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_CMDL: {/*print_cmdl(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_SCBC: {/*print_scbc(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_CPLW: {/*print_cplw(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_FCMP: {/*print_fcmp(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_PARM: {/*print_parm(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_DBLS: {/*print_dbls(cdi_info);*/ break; }
	case CDI_CHUNK_TYPE_PGLB: {/*print_pglb(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_WIPR: {/*print_wipr(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_PRMT: {/*print_prmt(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_ECGS: {/*PrintEmitterConfigurations(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_PEMC: {/*PrintEmitters(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_SRMI: {/*print_srmi(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_SCRN: {/*print_scrn(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_VHCL: {/*print_vhcl(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_AMW_: {/*print_amw_(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_MNTS: {/*print_mnts(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_MMBR: {/*print_mmbr(cdi_info); */break; }
	case CDI_CHUNK_TYPE_MPSG: {/*print_mpsg(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_AFTD: {/*print_aftd(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_LOOK: {/*print_look(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_WPDT: {/*print_wpdt(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_TIRE: {/*print_tire(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_CLBR: {/*print_clbr(cdi_info, depth);*/ break; }
	case CDI_CHUNK_TYPE_PWBY: {/*PrintPowerByInfo(cdi_info, depth);*/ break; }
	default: {/*printf("\n");*/ break; }
	}

	return cio_ascend(cio);
}


/*** Given a string s, returns a string which is s surrounded by double quotes, and
 *** with a backslash inserted before each double quote and backslash contained in s.
 *** This effectively converts s into a representation which can be unambiguously
 *** parsed later.
 */
static STRING quotify_and_slashify_string(cSTRING src)
{
	if (src == NULL)
		src = "";

	/** Worst case is we have to add two quotes plus a backslash for every character! */
	STRING dest = (STRING)exa_malloc(2 * strlen(src) + 3,
		"quotify_and_slashify_string",
		"a new string");

	dest[0] = '"';

	{
		CHARACTER *next_dest_char = dest + 1;
		const CHARACTER *next_src_char = src;

		/** For some unknown reason, the SGI compiler will complain unless the ugly
		 ** casts to (int) are present in the next two lines...
		 */
		while ((int)(*next_src_char) != (int)'\0') {
			if ((int)(*next_src_char) == (int)'"' || (int)(*next_src_char) == (int)'\\') {
				*next_dest_char = '\\';
				next_dest_char++;
			}
			*next_dest_char = *next_src_char;
			next_dest_char++;
			next_src_char++;
		}

		*next_dest_char = '"';
		next_dest_char++;
		*next_dest_char = '\0';
	}
	return(dest);
}

