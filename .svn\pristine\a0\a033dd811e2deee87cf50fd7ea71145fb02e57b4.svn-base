# Registry-Level VCC Options

# Activate a build via "True", deactivate w/"False"
# Only effective if registry-specified version of "DIST"
# provides for the corresponding build

# PowerFLOW Distribuion
BUILD_FLOW		True

# PowerSPECTRUM Distribuion
BUILD_SPECTRUM		False

# PowerACOUSTICS Distribuion
BUILD_ACOUSTICS		True

# PowerDELTA Distribuion
BUILD_DELTA		True

# Unit Test Distribuion
BUILD_UTEST		True
BUILD_UEXE              True

# SRI Library for Viz Team
BUILD_SRI		False

# RLM Tools Distribution
BUILD_RLMTOOLS          False

# ORM Distribution
BUILD_ORM               False

# PowerPRE Distribution (PowerCASE team)
BUILD_POWERPRE          False

# TAIAPI Distribution (PowerCASE team)
BUILD_TAIAPI		False

# DigitalROCK Distribution (mostly the same as PowerFLOW)
BUILD_DIGITALROCK       False

# Hemo Distribution (mostly the same as PowerFLOW)
BUILD_HEMO       	False

# Ice Distribution (mostly the same as PowerFLOW)
BUILD_ICE       	False

# Panel Load Export tool (dump_wni) for PowerACOUSTICS internal users
BUILD_PLE		False

# "include" Distribution (IDE transition prototype)
BUILD_INCLUDE		False


