/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Simulation engine definitions
 *
 * Jim Salem, Exa Corporation 
 * Created Fri Jun  3 1994
 *--------------------------------------------------------------------------*/

#ifndef __EXA_SIM_H
#define __EXA_SIM_H

#include "parse_args.h"
#include "common.h"

extern pid_t decomposer_child_pid;

#if SEPARATE_LICENSE_PROCESS
extern pid_t license_child_pid;
#endif
extern BOOLEAN initial_sp_status_requested;

/* Name of the default SP binary */
#define DEFAULT_SIMENG_HOST_SP_FILE SIMENG_DIR "/" HOST_SP_BIN_DIR "/pf_sim_sp"
#define SIMENG_HOST_SP_FILE_ENVVAR "EXA_SIMULATE_HOST_SP_FILE"

/* Poll timeout for the MPI version of the main loop - in milliseconds */
#define CP_POLL_TIMEOUT 1

/* Timer definitions */
#define CP_TOTAL_TIMER			0
#define CP_INITIALIZATION_TIMER		1
#define CP_TIMESTEP_TIMER		2
#define CP_SIM_TIMER			3
#define CP_SEED_TIMER			4
#define CP_CKPT_TIMER			5

#define CP_NUM_TIMERS			6

extern sTIMER cp_timers[CP_NUM_TIMERS];

#define cp_timer_clear(ID)	   timer_clear(cp_timers + (ID))
#define cp_timer_start(ID)	   timer_start(cp_timers + (ID))
#define cp_timer_stop(ID)	   timer_stop(cp_timers + (ID))
#define cp_timer_rename(ID, new)   timer_name((cp_timers + (ID)), new)
#define cp_timer_report(ID, units) timer_report(cp_timers + (ID), units)

/*--------------------------------------------------------------------------*
 * Routines shared between CP/SP and standalon versions
 *--------------------------------------------------------------------------*/

/* This parses all of the arguments and does the basic initialization up to
 * starting the SPs and reading the LGI file.
 * Returns the LGI filename and sets the passed in SIM_ARGS and STD_ARGS
 * structures. (If either is NULL, they are not set
 *
 * The LGI filename is checked to make sure it exists.
 * The following initializations are called:
 *    - std_arg_initialize
 *    - cp_initialize
 *    - cp_jobctl_initialize
 */

cSTRING exa_sim_initialize(int argc, char *argv_orig[], char *argv[], // argv is trashed
			   sSIM_ARGS *sim_args, sSTD_ARGS *std_args);
VOID start_seed_timer();
VOID report_seed_timer();

VOID clear_ckpt_timer();
VOID start_ckpt_timer();
VOID stop_ckpt_timer();
VOID report_ckpt_timer();
#endif /* __EXA_SIM_H */

