/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * The top-level simulation function
 *
 * Jim Salem, Exa Corporation 
 * Created Wed Jun  1 1994
 *--------------------------------------------------------------------------*/
#include <sys/wait.h>
#include <sched.h>
#include <time.h>
#include <pthread.h>
#include <sys/socket.h>

#include "common.h"
#include "exa_sim.h"
#include "errbuf.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "jobctl.h"
#include "window.h"
#include "table.h"
#include "heat_exchangers.h"
#include "license.h"
#include "timestep_subcycling.h"

#include "cp_stream_manager.h"

#if SURF_COUP
#include "coupling_model.h"
#endif // SURF_COUP

#include "particle_sim_cp.h"
#include "trajectory_results.h"
#include "transient_boundary_seeding.h"
#include "exa_sim_parse_cp.h"

static cSTRING argv0 = NULL;

static int license_equivalent_sps = 0; // effective number of SPs for license calculation (interesting for GPU)

#if ! SEPARATE_LICENSE_PROCESS

#include "license_guts.h" // includes a few fcn definitions

#else

asINT32 secs_between_license_heartbeats = MAX_SECS_BETWEEN_LICENSE_HEARTBEATS;
static asINT32 n_standard_licenses = 0;

#endif

#if CPU_TIME_CP_THREADS
asINT64 g_write_sri_results_thread_ns = 0;
#endif

static BOOLEAN debug_malloc_p = FALSE;

#if SURF_COUP
static cSTRING surface_coupling_model_type_to_connector_license_name(cSTRING model_type)
{
  bool rlm = !exalic_use_dsls_feature_names();
  if (strcmp(model_type, "PowerTHERM") == 0) {
    if (cp_info.force_radtherm_p)
      return rlm ? "ExaRadThermConnector" : "PWC";
    else
      return rlm ? "PowerTHERMConnector" : "PTI";
  } 
  else if (strcmp(model_type, "RadTherm") == 0) {
    if (cp_info.force_ptherm_p)
      return rlm ? "PowerTHERMConnector" : "PTI";
    else
      return rlm ? "ExaRadThermConnector" : "PWC";
  } 
  else {
    static char buf[128];
    sprintf(buf, "Exa%sConnector", model_type); // Not used, so no DSLS equivalent is defined
    return buf;
  }
}

typedef struct sSURFACE_COUPLING_MODEL_TYPE_COUNT {
  cSTRING model_type;
  sINT32  n_models_of_type;
} *SURFACE_COUPLING_MODEL_TYPE_COUNT;

static sSURFACE_COUPLING_MODEL_TYPE_COUNT *count_surface_coupling_models_per_type(asINT32 *n_model_types_return)
{
  asINT32 n_models = cp_info.n_surface_couplings;
  if (n_models <= 0) {
    *n_model_types_return = 0;
    return NULL;
  }

  sSURFACE_COUPLING_MODEL_TYPE_COUNT *model_type_counts = xnew sSURFACE_COUPLING_MODEL_TYPE_COUNT[ n_models];
  asINT32 n_model_types = 0;

  ccDOTIMES(model_ix, n_models) {
    cSTRING model_type = cp_info.surface_couplings[model_ix].cmdl.model_type;
    ccDOTIMES(m, n_model_types) {
      if (strcmp(model_type, model_type_counts[m].model_type) == 0) {
        model_type_counts[m].n_models_of_type++;
        goto next;
      }
    }
    model_type_counts[n_model_types].model_type = model_type;
    model_type_counts[n_model_types].n_models_of_type = 1;
    n_model_types++;
    next: ;
  }

  *n_model_types_return = n_model_types; 
  return model_type_counts;
}

#endif // SURF_COUP

#if ! SEPARATE_LICENSE_PROCESS

#define detect_proc_license(license_count) \
  detect_proc_license_guts(license_count, sim_args.use_opt_licenses, sim_args.opt_license_failover)

#define detect_proc_license_still_okay()	\
  detect_proc_license_still_okay_guts()

static INLINE VOID detect_aux_license(cSTRING feature_name, asINT32 license_count, cSTRING used_for)
{
  detect_aux_license_guts(feature_name, license_count, used_for);
}

#if SURF_COUP
// Name (init_xtr) chosen to obfuscate intended purpose. Otherwise would have been
// called detect_surface_coupling_model_connector_licenses. Cannot be static because
// it is referenced in cp_stream_manager.cc.
VOID init_xtr()
{
  asINT32 n_model_types;
  sSURFACE_COUPLING_MODEL_TYPE_COUNT *model_type_counts = count_surface_coupling_models_per_type(&n_model_types);
  ccDOTIMES(i, n_model_types) {
    detect_aux_license_guts(surface_coupling_model_type_to_connector_license_name(model_type_counts[i].model_type),
                            model_type_counts[i].n_models_of_type, NULL);
  }

  if (model_type_counts)
    delete[] model_type_counts;
}
#endif // SURF_COUP

#else	// SEPARATE_LICENSE_PROCESS

#include <libgen.h>

#define init_license_strings(alt_str)

#ifndef LICENSE_ENABLED

#define detect_proc_license(license_count)
#define detect_proc_license_still_okay()

#else

#include "license_aux.h"

pid_t license_child_pid = -1;
static int license_pipe_fd = -1;

static BOOLEAN write_lm_chars(cSTRING str, size_t n_bytes_to_write, BOOLEAN exit_on_failure = FALSE)
{
  size_t n_bytes = write_all(license_pipe_fd, (VOID *)str, n_bytes_to_write);

  if (n_bytes != n_bytes_to_write) {
    if (exit_on_failure) {
      msg_error("Lost contact with the license manager (%s).", time_str());
      exit(exit_failure_code());
    } else {
      return FALSE;
    }
  }
  return TRUE;
}

static BOOLEAN write_lm_int(LM_INT value, BOOLEAN exit_on_failure = FALSE)
{
  size_t nbytes = write_all(license_pipe_fd, &value, sizeof(value));

  if (nbytes != sizeof(value)) {
    if (exit_on_failure) {
      msg_error("Lost contact with the license manager (%s).", time_str());
      exit(exit_failure_code());
    } else {
      return FALSE;
    }
  }
  return TRUE;
}

static BOOLEAN encrypt_and_write_lm_int(LM_INT i, BOOLEAN exit_on_failure = FALSE)
{
  LM_INT j = lm_encrypt_int(i, license_child_pid);
  return write_lm_int(j, exit_on_failure);
}

static BOOLEAN read_lm_int(LM_INT *result, BOOLEAN exit_on_failure = FALSE)
{
  LM_INT value;
  asINT32 nbytes = read_all(license_pipe_fd, &value, sizeof(value));

  if (nbytes != sizeof(value)) {
    if (exit_on_failure) {
      msg_error("Lost contact with license manager (%s).", time_str());
      exit(exit_failure_code());
    } else {
      *result = 0;
      return FALSE;
    }
  }

  *result = value;
  return TRUE;
}

static inline BOOLEAN read_and_decrypt_lm_int(LM_INT *result, BOOLEAN exit_on_failure = FALSE)
{
  if (!read_lm_int(result, exit_on_failure))
    return FALSE;
  *result = lm_decrypt_int(*result, license_child_pid);
  return TRUE;
}

static VOID relaunch_pf_sim_comm();

#define LTRY(cmd)					\
  if (!(cmd)) {						\
    /* Kill and then restart the license process. */	\
    relaunch_pf_sim_comm();				\
    return;						\
  }

static VOID detect_proc_license_still_okay()
{
  LM_INT tag_heartbeat = lm_tag_heartbeat(license_child_pid);

  LTRY(write_lm_int(tag_heartbeat));

  LM_INT ack;
  LTRY(read_lm_int(&ack));

  if (ack != lm_tag_ok(license_child_pid))
    msg_error("License manager inconsistency.");
}

static VOID proc_license_release()
{
  LM_INT tag_release = lm_tag_release(license_child_pid);

  LTRY(write_lm_int(tag_release));

  LM_INT ack;
  LTRY(read_lm_int(&ack));

  if (ack != lm_tag_ok(license_child_pid))
    msg_error("License manager inconsistency.");
}

static VOID detect_aux_license(cSTRING feature_name, asINT32 license_count, cSTRING used_for)
{
  LM_INT tag_aux_license = lm_tag_aux_license(license_child_pid);
  LTRY(write_lm_int(tag_aux_license));
  LTRY(encrypt_and_write_lm_int(license_count));
  asINT32 len = strlen(feature_name) + 1;
  LTRY(encrypt_and_write_lm_int(len));
  LTRY(write_lm_chars(feature_name, len));

  if (!used_for) {
    asINT32 len = 0;
    LTRY(encrypt_and_write_lm_int(len));
  } else {
    asINT32 len = strlen(used_for) + 1;
    LTRY(encrypt_and_write_lm_int(len));
    LTRY(write_lm_chars(used_for, len));
  }

  LM_INT ack;
  read_lm_int(&ack);
  if (ack != lm_tag_ok(license_child_pid))
    msg_error("License manager inconsistency."); 
}

#if SURF_COUP
// Name (init_xtr) chosen to obfuscate intended purpose. Otherwise would have been
// called detect_surface_coupling_model_connector_licenses. Cannot be static because
// it is referenced in read_cp_lgi.cc.
VOID init_xtr()
{
  asINT32 n_model_types;
  sSURFACE_COUPLING_MODEL_TYPE_COUNT *model_type_counts = count_surface_coupling_models_per_type(&n_model_types);
  ccDOTIMES(i, n_model_types) {
    cSTRING feature_name = surface_coupling_model_type_to_connector_license_name(model_type_counts[i].model_type);
    detect_aux_license(feature_name, model_type_counts[i].n_models_of_type, NULL);
  }

  if (model_type_counts)
    delete[] model_type_counts;
}
#endif // SURF_COUP

static VOID detect_proc_license(asINT32 license_count)
{
  // On IA64, we are launching a separate process to establish
  // contact with the license manager.
  asINT32 not_timed_out = 30;
  pid_t child_pid = (pid_t) -1;
  int sv[2];

  // The error messages should not directly convey that we are launching
  // a separate process
  if (socketpair(AF_UNIX, SOCK_STREAM, 0, sv) != 0) {
    msg_error("Unable to contact license manager: %s",
        strerror(errno));
    exit(exit_failure_code());
  }

  do {
    child_pid = fork();
  } while ((child_pid == ((pid_t) -1)) && (errno == EAGAIN) && not_timed_out--);

  if (child_pid == (pid_t) -1) {
    msg_error("Unable to establish contact with license manager: %s",
        strerror(errno));
    exit(exit_failure_code());
  };

  if (child_pid == (pid_t) 0) {
    // Child process
    close(sv[0]);

    char socket_arg[64];
    sprintf(socket_arg, "%d", sv[1]);

    //std::string liaison = exalic_use_rlm_feature_names()
    //  // If using new feature names - that means we're using reprise (64 bit liaison)
    //  ? EXA_PATH::Join(EXA_PATH::DirName(argv0), "pf_sim_comm")

    //  // If not using new names, then we're stuck with the 32 bit liaison that goes w/flex
    //  : EXA_PATH::Join(EXA_PATH::DirName(EXA_PATH::DirName(argv0)), "pf_sim_comm");
    // With retirement of Flex, only the 64 bit liaison is possible
    std::string liaison = EXA_PATH::Join(EXA_PATH::DirName(argv0), "pf_sim_comm");

    STRING job_name = platform_get_file_base_name_wo_suffix(sim_args.cdi_filename, NULL);
    execl(liaison.c_str(), liaison.c_str(), socket_arg, job_name, NULL);

    // execl should never return
    msg_error("Unable to contact license manager daemon.");
    exit(exit_failure_code());
  }
  else {
    // Parent process
    license_child_pid = child_pid;
    license_pipe_fd = sv[0];
    close(sv[1]);

    // Tell child whether to check out opt licenses
    LM_INT opt_licence_flags = (sim_args.use_opt_licenses ? 1 : 0) | (sim_args.opt_license_failover ? 2 : 0);
    encrypt_and_write_lm_int(opt_licence_flags, TRUE);

    // Tell child how many licenses to check out
    encrypt_and_write_lm_int(license_count, TRUE);

    // Wait for child to check out license
    LM_INT n;
    read_and_decrypt_lm_int(&n, TRUE);
    n_standard_licenses = n;
  }
}

static VOID relaunch_pf_sim_comm()
{
  msg_warn("Lost contact with license manager (%s). Attempting to reestablish contact.", time_str());
  if (license_child_pid != ((pid_t) -1)) {
    /* Kill the license process. */	
    pid_t child_pid = license_child_pid;	
    license_child_pid = -1;
    kill(child_pid, SIGTERM);	
  }	

  if (license_pipe_fd >= 0) {
    close(license_pipe_fd);
    license_pipe_fd = -1;
  }
  /* Restart the license process. */	
  detect_proc_license(license_equivalent_sps);

  msg_print("Reestablished contact with license manager (%s). Continuing.", time_str());
}


#endif  // #ifndef LICENSE_ENABLED

#endif	// #if ! SEPARATE_LICENSE_PROCESS

// Name (init_rfc) chosen to obfuscate intended purpose. Otherwise would have been
// called detect_sim_type_licenses. Cannot be static because cp_cdi_reader.cc.
VOID init_rfc(asINT32 license_count)
{
}

static void cp_signal_workers_sim_finished()
{
  ccDOTIMES(rp,total_rps) {
    MPI_Send(NULL,0, MPI_INT, rp, eMPI_SIM_FINISHED_TAG, eMPI_rp_cp_comm);
  }

  // Wait for a response from the RPs. If we don't, we end up with a hang
  // as the CP will hit MPI_Finalize(), and the RPs get stuck trying to
  // MPI_Cancel() their sends/recvs
  ccDOTIMES(rp, total_rps) {
    MPI_Recv(NULL,0, MPI_INT, rp, eMPI_SIM_FINISHED_TAG, eMPI_rp_cp_comm, MPI_STATUS_IGNORE);
  }

  ccDOTIMES(sp,total_sps) {
    MPI_Send(NULL,0, MPI_INT, sp, eMPI_SIM_FINISHED_TAG, eMPI_sp_cp_comm);
  }

}

std::atomic<bool> merge_IO_thread = false;

static void *IO_results_thread(void *ignored)
{
#if CPU_TIME_CP_THREADS
  struct timespec write_sri_results_thread_start_timespec;
  clock_gettime(CLOCK_THREAD_CPUTIME_ID, &write_sri_results_thread_start_timespec); // Start the rotational dynamics-thread timer
#endif

  CP_MEAS_WINDOW_COLLECTION meas_windows = &cp_info.meas_windows;

  while(!merge_IO_thread) {
    // remove and process an entry from write sri results queue
    CP_MEAS_WINDOW window = cp_info.write_sri_results_queue->remove_entry();
    if (window) {
      cp_info.write_sri_results_queue->process(window, wallclock_time_secs());
    }

    if(has_trajectory_window() && !sim_args.disable_particle_modeling) 
      write_pri_result_buffers();  //Fix for PR43284 (PRI and SRI both use HDF5 so they must only be used from the same thread)  

    if (cp_info.write_sri_results_queue->is_empty())
      write_checkpoints();
    cp_sleep();
  }

  if(has_trajectory_window() && !sim_args.disable_particle_modeling) 
    write_pri_result_buffers();


#if CPU_TIME_CP_THREADS
  struct timespec write_sri_results_thread_stop_timespec;
  clock_gettime(CLOCK_THREAD_CPUTIME_ID, &write_sri_results_thread_stop_timespec); // Stop the rotational dynamics-thread timer
  asINT64 write_sri_results_thread_start_ns = write_sri_results_thread_start_timespec.tv_nsec + write_sri_results_thread_start_timespec.tv_sec * **********;
  asINT64 write_sri_results_thread_stop_ns = write_sri_results_thread_stop_timespec.tv_nsec + write_sri_results_thread_stop_timespec.tv_sec * **********;
  g_write_sri_results_thread_ns = write_sri_results_thread_stop_ns - write_sri_results_thread_start_ns;
#endif

  return NULL;
}

static pthread_t launch_IO_results_thread()
{
  pthread_attr_t attr;
  pthread_attr_init(&attr);
  pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_JOINABLE);
  pthread_attr_setscope(&attr, PTHREAD_SCOPE_PROCESS);

  pthread_t thread;
  int status = pthread_create(&thread, &attr, IO_results_thread, NULL);
  if (status != 0)
    msg_internal_error("Could not launch rotational dynamics thread: %s", strerror(status));

  pthread_attr_destroy(&attr);

  return thread;
}


/*--------------------------------------------------------------------------*
 * These routines exchange initialization info with the SPs
 *--------------------------------------------------------------------------*/

static VOID send_early_sim_run_info(SIM_ARGS sim_args)
{
  sCP_EARLY_SIM_RUN_INFO sim_info;

  /*** SP run-time error handling ***/

  // Use 2 * SIM_INFO_INITIAL_ERROR_COUNT because after receiving SIM_INFO_INITIAL_ERROR_COUNT
  // error msgs, the CP will send an ack msg to the SP enabling the next set of
  // SIM_INFO_INITIAL_ERROR_COUNT msgs. This reduces the likelihood that the SP will block
  // waiting for an ack msg.
  sim_info.sp_initial_error_count = 2 * SIM_INFO_INITIAL_ERROR_COUNT;
  sim_info.dns_nu_over_t_floor = sim_args->dns_nu_over_t_floor;
  sim_info.major_flow_direction = sim_args->major_flow_direction;

  /* Table comm - allocate and initialize */
  cp_info.table_comm_descs = new sCP_TABLE_COMM_DESC[total_sps];
  ccDOTIMES(sp, total_sps) {
    for (int i=2; i>=0; i--) {
      cp_info.table_comm_descs[sp].mpi_requests[i] = MPI_REQUEST_NULL;
    }
  }

  detect_proc_license_still_okay();

  /* Send to processors */
  ccDOTIMES(i, total_sps) {
    MPI_Datatype eMPI_cp_early_sim_run_info_type;
    eMPI_cp_early_sim_run_info_type_init(&eMPI_cp_early_sim_run_info_type);
    cp_init_mpi_send(&sim_info, 1, eMPI_cp_early_sim_run_info_type, i, eMPI_CP_SIM_RUN_INFO_TAG, eMPI_sp_cp_comm);
    MPI_Type_free(&eMPI_cp_early_sim_run_info_type);
  }
}

static VOID send_late_sim_run_info(SIM_ARGS sim_args)
{
  sCP_LATE_SIM_RUN_INFO sim_info;

  /* Run options */
  sim_info.run_options = sim_args->run_options;

  /* Initialization options */
  sim_info.num_sps = total_sps;
  sim_info.describe_p = FALSE;
  sim_info.describe_verbose_p = FALSE;
  sim_info.clear_all_p = sim_args->clear_all_p;
  sprintf(sim_info.root_filename, "%s", cp_info.root_filename);
  sim_info.ablm_cap = sim_args->ablm_cap;
  sim_info.mme_chkpnt_at_end = sim_args->mme_chkpnt_at_end;
  sim_info.full_chkpnt_at_end = sim_args->full_chkpnt_at_end;
  sim_info.parallel_io = sim_args->parallel_io;
  sim_info.parallel_io_max_buffer_size = sim_args->parallel_io_max_buffer_size;
  sim_info.allow_immediate_checkpoints = sim_args->allow_immediate_checkpoints;
  sim_info.barrier_instead_of_finalize = sim_args->barrier_instead_of_finalize;
  detect_proc_license_still_okay();

  /* Compute checkpoint times */
  if (cp_info.periodic_ckpts_p) {
    sim_info.initial_checkpoint = cp_info.ckpt_time_desc.start;
    sim_info.next_checkpoint = cp_info.ckpt_time_desc.period;
  } else {
    sim_info.initial_checkpoint = sim_info.next_checkpoint = TIMESTEP_INVALID;
  }

  /* Set the initial time in the CP info structure */
  cp_info.next_ckpt_time = cp_info.last_next_ckpt_time = TIMESTEP_NEVER;

  sim_info.gpu_repeatable = sim_args->gpu_repeatable;
  sim_info.gpu_lrf_max_isends = sim_args->gpu_lrf_max_isends;
  sim_info.gpu_max_buffer_size = sim_args->gpu_max_buffer_size;

  detect_proc_license_still_okay();

  /* Send to processors */
  ccDOTIMES(i, total_sps) {
    MPI_Datatype eMPI_cp_late_sim_run_info_type;
    eMPI_cp_late_sim_run_info_type_init(&eMPI_cp_late_sim_run_info_type);
    cp_init_mpi_send(&sim_info, 1, eMPI_cp_late_sim_run_info_type, i, eMPI_LATE_SIM_RUN_INFO_TAG, eMPI_sp_cp_comm);
    MPI_Type_free(&eMPI_cp_late_sim_run_info_type);
  }
}


/* Global info from SPs
 * All this does is receive addresses used in checkpointing from the SPs,
 * so in the MPI version we don't need to receive anything.
 * It also receives SP addresses of error buffer `ack' words.*/
static VOID receive_early_sim_run_info()
{
  /* Get the simeng version and precision from SP0 only */
  MPI_Status status;
//  CHARACTER early_info_from_SP0[MAX_VERSION_STRING+1];
  RECV_EXA_SIM_MSG<CHARACTER, MAX_VERSION_STRING + 1> early_info_from_SP0(eMPI_SIMENG_VERSION_TAG, 0);
  cp_init_mpi_recv<CHARACTER, MAX_VERSION_STRING + 1>(early_info_from_SP0.mpi_msg, 0, eMPI_SIMENG_VERSION_TAG, eMPI_sp_cp_comm, &status);

  strncpy(cp_info.simeng_version, early_info_from_SP0.return_buffer(), MAX_VERSION_STRING);

  BOOLEAN sps_are_dp = early_info_from_SP0.return_buffer()[MAX_VERSION_STRING];
  if (cp_info.is_sim_double_precision && !sps_are_dp)
    msg_internal_error("CP launched with -double_precision flag, but SPs are not double precision");
  if (!cp_info.is_sim_double_precision && sps_are_dp)
    msg_internal_error("CP launched without -double_precision flag, but SPs are double precision");

  detect_proc_license_still_okay();
}

static std::vector<sSP_GPU_INFO> recv_gpu_info()
{
  cSTRING s = total_sps > 1 ? "s" : "";
  msg_print("\nInitializing Nvidia GPU%s",s);

  std::vector<sSP_GPU_INFO> sp_gpu_info(total_sps);
  MPI_Status eMPI_status;

  // temporarily use this tag until after release
  ccDOTIMES(sp, total_sps) {
  RECV_EXA_SIM_MSG<sSP_GPU_INFO,1> recv_msg(eMPI_CP_SIM_RUN_INFO_TAG, sp);
    cp_init_mpi_recv(recv_msg.mpi_msg, sp, recv_msg.mpi_msg.tag(), eMPI_sp_cp_comm, &eMPI_status);
    sp_gpu_info[sp] = *recv_msg.mpi_msg.buffer();
  }
  return sp_gpu_info;
}

static int compute_gpu_equivalent_sps(const sSP_GPU_INFO& gpu_info) 
{
  bool is_A4000_or_A6000 = (strstr(gpu_info.name, "A4000") != nullptr) || (strstr(gpu_info.name, "A6000") != nullptr);
  dFLOAT scale_factor = 54.0;
  if (is_A4000_or_A6000) {
    scale_factor *= 2;
  }
  return (gpu_info.fp32_cores / scale_factor) + 0.5;
}


static int process_gpu_info(const std::vector<sSP_GPU_INFO>& gpu_info)
{
  size_t total_fp32_cores = 0;
  size_t total_global_mem = 0;
  int equivalent_sps = 0;
  ccDOTIMES(sp, total_sps) {
    int eq = compute_gpu_equivalent_sps(gpu_info[sp]);
    equivalent_sps += eq;
    msg_print("SP%d: %s\n"
              "     Global Mem: %.1f GB\n"
              "     FP32 Cores: %lu\n",
              //"     Equivalent CPU Cores: %d\n", We cannot show GPU/CPU equivalance info officially per DS policy
              sp,
              gpu_info[sp].name,
              gpu_info[sp].total_global_mem / 1.0e9f,
              gpu_info[sp].fp32_cores);
  }

  return equivalent_sps;
}

static VOID receive_late_sim_run_info()
{
  MPI_Status status;
  RECV_EXA_SIM_MSG<BOOLEAN, 1> is_some_bc_time_varying(eMPI_LATE_SIM_RUN_INFO_TAG, 0);
  cp_init_mpi_recv<BOOLEAN, 1>(is_some_bc_time_varying.mpi_msg, 0, eMPI_LATE_SIM_RUN_INFO_TAG, eMPI_sp_cp_comm, &status);

  cp_info.is_some_bc_time_varying = *is_some_bc_time_varying.return_buffer();

  if (cp_info.n_rotational_dynamics_descs > 0) {
    RECV_EXA_SIM_MSG<dFLOAT, 2> rotdyn_globals(eMPI_ROTDYN_GLOBALS_TAG, 0);
    cp_init_mpi_recv<dFLOAT, 2>(rotdyn_globals.mpi_msg, 0, eMPI_ROTDYN_GLOBALS_TAG, eMPI_sp_cp_comm, &status);
    g_timestep_per_lat_time_inc = rotdyn_globals.return_buffer()[0];
    g_user_max_vel = rotdyn_globals.return_buffer()[1];
  }
}

static VOID recv_ref_frame_info()
{
  if (cp_info.is_global_ref_frame) { // Global Ref Frame present
    asINT32 tag_grf = eMPI_GRF_SP_TO_CP_INIT_TAG;
    MPI_Datatype eMPI_GRF_SP_TO_CP_INIT_MSG;
    MPI_Status eMPI_status;
    RECV_EXA_SIM_MSG<sGRF_SP_TO_CP_INIT, 1> grf_info(tag_grf, MPI_ANY_SOURCE);
    cp_init_mpi_recv<sGRF_SP_TO_CP_INIT, 1>(grf_info.mpi_msg, MPI_ANY_SOURCE, tag_grf, eMPI_sp_cp_comm, &eMPI_status);

    cp_info.global_nirf_info = *grf_info.return_buffer();
    cp_info.is_global_ref_frame_time_varying = (!cp_info.global_nirf_info.is_angular_vel_constant
                                                || !cp_info.global_nirf_info.is_ref_point_vel_constant);
  }

  if (cp_info.n_lrfs() > 0) { // Local Ref Frame present
    asINT32 tag_lrf = eMPI_LRF_SP_TO_CP_INIT_TAG;
    MPI_Datatype eMPI_LRF_SP_TO_CP_INIT_MSG;
    
    asINT32 n_time_varying_lrfs = 0;
    ccDOTIMES(i, cp_info.n_lrfs()) {
      MPI_Status eMPI_status;
      RECV_EXA_SIM_MSG<sLRF_SP_TO_CP_INIT, 1> lrf_info(tag_lrf, MPI_ANY_SOURCE);
      cp_init_mpi_recv<sLRF_SP_TO_CP_INIT, 1>(lrf_info.mpi_msg, MPI_ANY_SOURCE, tag_lrf, eMPI_sp_cp_comm, &eMPI_status);

      SRI_LRF sri_lrf = &cp_info.sri_lrfs[i];

      vcopy(sri_lrf->axis_origin, lrf_info.return_buffer()->point);
      vcopy(sri_lrf->axis_direction, lrf_info.return_buffer()->axis);

      sri_lrf->has_constant_angular_vel = lrf_info.return_buffer()->is_angular_vel_constant;
      cp_info.sri_lrf_has_constant_angular_vel_flags[i] = lrf_info.return_buffer()->is_angular_vel_constant;

      if (!sri_lrf->has_constant_angular_vel)
        n_time_varying_lrfs++;

      sri_lrf->initial_angular_rotation = lrf_info.return_buffer()->angle_rotated;
      sri_lrf->initial_n_revolutions = lrf_info.return_buffer()->n_revolutions;
      sri_lrf->constant_angular_vel_mag = lrf_info.return_buffer()->angular_vel_mag;

      ccDOTIMES(j, cp_info.n_rotational_dynamics_descs) {
        if (cp_info.rotational_dynamics_descs[j].m_lrf_index == i) {
          sri_lrf->is_rotational_dynamics_on = TRUE;
          break;
        }
      }
    }

    cp_info.time_varying_lrf_indices.reserve(n_time_varying_lrfs);
    sSRI_LRF *sri_lrf = &cp_info.sri_lrfs[0];
    ccDOTIMES(l, cp_info.n_lrfs()) {
      if (!sri_lrf->has_constant_angular_vel)
        cp_info.time_varying_lrf_indices.push_back(l);
      sri_lrf++;
    }    
  }
}

static VOID recv_mbc_info()
{
  int tag = eMPI_MBC_INFO_TAG;

  MPI_Datatype eMPI_MBC_SP_TO_CP_INIT_MSG;


  MPI_Status eMPI_status;
  RECV_EXA_SIM_MSG<asINT32, 1> n_mbcs(tag, MPI_ANY_SOURCE);
  cp_init_mpi_recv<sINT32, 1>(n_mbcs.mpi_msg, MPI_ANY_SOURCE, tag, eMPI_sp_cp_comm, &eMPI_status);

  cp_info.sri_mbcs.reserve(*n_mbcs.return_buffer());

  sINT32 *face_mbc_indices = xnew sINT32[cp_info.n_sri_faces];
  RECV_EXA_SIM_MSG<sINT32> fmbc_indices(tag, cp_info.n_sri_faces, face_mbc_indices, MPI_ANY_SOURCE);
  cp_init_mpi_recv<sINT32>(fmbc_indices.mpi_msg, MPI_ANY_SOURCE, tag, eMPI_sp_cp_comm, &eMPI_status);
  ccDOTIMES(i, cp_info.n_sri_faces)
    cp_info.sri_faces[i].mbc_index = face_mbc_indices[i];
  delete[] face_mbc_indices;

  cp_info.sri_mbc_has_constant_vel_flags = *n_mbcs.return_buffer() > 0 ? xnew cBOOLEAN[*n_mbcs.return_buffer()] : NULL;
  cBOOLEAN *sri_mbc_has_constant_vel_flags = cp_info.sri_mbc_has_constant_vel_flags;

  asINT32 n_time_varying_mbcs = 0;
  ccDOTIMES(i, *n_mbcs.return_buffer()) {
    RECV_EXA_SIM_MSG<sMBC_SP_TO_CP_INIT, 1> mbc_info(tag, MPI_ANY_SOURCE);
    sSRI_MBC sri_mbc;
    MPI_Status eMPI_status;
    cp_init_mpi_recv<sMBC_SP_TO_CP_INIT, 1>(mbc_info.mpi_msg, MPI_ANY_SOURCE, tag, eMPI_sp_cp_comm, &eMPI_status);
    sri_mbc.type = (SRI_MBC_TYPE)mbc_info.return_buffer()->type;
    sri_mbc.name = xnew char [mbc_info.return_buffer()->name_length];
    sri_mbc.ref_frame_index = mbc_info.return_buffer()->ref_frame_index;
    vcopy(sri_mbc.axis_origin, mbc_info.return_buffer()->point);
    vcopy(sri_mbc.axis_direction, mbc_info.return_buffer()->axis);
    sri_mbc.has_constant_velocity = mbc_info.return_buffer()->is_vel_constant;
    sri_mbc.constant_angular_vel_mag = mbc_info.return_buffer()->angular_vel_mag;
    vcopy(sri_mbc.constant_linear_vel, mbc_info.return_buffer()->linear_vel);
    sri_mbc.has_space_varying_velocity = mbc_info.return_buffer()->is_vel_space_varying;

    *sri_mbc_has_constant_vel_flags++ = sri_mbc.has_constant_velocity;

    if (!sri_mbc.has_constant_velocity)
      n_time_varying_mbcs++;
    RECV_EXA_SIM_MSG<char> recv_name(tag, mbc_info.return_buffer()->name_length, sri_mbc.name, MPI_ANY_SOURCE);
    cp_init_mpi_recv<char>(recv_name.mpi_msg, MPI_ANY_SOURCE, tag, eMPI_sp_cp_comm, &eMPI_status);
    cp_info.sri_mbcs.push_back(sri_mbc);
  }     
    
  cp_info.time_varying_mbc_indices.reserve(n_time_varying_mbcs);
  SRI_MBC sri_mbc = &cp_info.sri_mbcs[0];
  ccDOTIMES(i, cp_info.n_mbcs()) {
    if (!sri_mbc->has_constant_velocity)
      cp_info.time_varying_mbc_indices.push_back(i);
    sri_mbc++;
  }
}

static VOID recv_movb_info()
{
  asINT32 tag = eMPI_MOVB_SP_TO_CP_INIT_TAG; 

  MPI_Status eMPI_status;

  RECV_EXA_SIM_MSG<asINT32,1> n_movb_xforms_msg(tag,MPI_ANY_SOURCE);

  cp_init_mpi_recv(n_movb_xforms_msg.mpi_msg, MPI_ANY_SOURCE, tag, eMPI_sp_cp_comm, &eMPI_status);
  auto n_movb_xforms = *n_movb_xforms_msg.return_buffer();
  if( n_movb_xforms != cp_info.sri_movb_xforms.size() ) {
    // sri_movb_xforms is currently allocated in sCP_CDI_READER::read_movb_chunk()
    msg_internal_error("Number of moving face xforms from sp %d does not match cp",eMPI_status.MPI_SOURCE);
  }

  cp_info.sri_movb_xform_has_constant_velocity_flags = n_movb_xforms > 0 ? xnew cBOOLEAN[n_movb_xforms] : NULL;
  cBOOLEAN *movb_constant_velocity_flag = cp_info.sri_movb_xform_has_constant_velocity_flags;

  SRI_MOVING_FACE_DESCRIPTOR movb = cp_info.sri_movb_xforms.data();
  ccDOTIMES(i, n_movb_xforms) {
    RECV_EXA_SIM_MSG<sMOVB_SP_TO_CP_INIT_MSG,1> l_msg(tag,MPI_ANY_SOURCE);
    const auto& l = *l_msg.return_buffer();

    cp_init_mpi_recv(l_msg.mpi_msg, MPI_ANY_SOURCE, tag, eMPI_sp_cp_comm, &eMPI_status);

    movb->has_constant_velocity = l.has_constant_velocity;
    movb->motion_type = (SRI_MOVING_FACE_MOTION_TYPE) l.motion_type;
    movb->constant_angular_vel_mag = l.constant_angular_vel_mag ;
    movb->initial_angle_rotated = l.initial_angle_rotated;
    movb->angle_rotated = l.angle_rotated;
    vcopy(movb->axis_origin,l.axis_origin);
    vcopy(movb->axis_direction,l.axis_direction);
    *movb_constant_velocity_flag = movb->has_constant_velocity;
    movb_constant_velocity_flag++;
    movb++;
  }     
}

/*--------------------------------------------------------------------------*
 * Main
 *--------------------------------------------------------------------------*/

BOOLEAN sim_timer_started = FALSE;
BOOLEAN sim_timer_reported = FALSE;
BOOLEAN init_timer_started = FALSE;
BOOLEAN init_timer_reported = FALSE;

inline VOID maybe_start_init_and_total_timers()
{
  if (sim_args.report_total_time_p) {
    cp_timer_clear(CP_TOTAL_TIMER);
    cp_timer_start(CP_TOTAL_TIMER);
    cp_timer_clear(CP_INITIALIZATION_TIMER);
    cp_timer_start(CP_INITIALIZATION_TIMER);
    init_timer_started = TRUE;
  }
}

inline VOID maybe_start_sim_timer()
{
  if (sim_args.report_total_time_p) {
    cp_timer_clear(CP_SIM_TIMER);
    cp_timer_start(CP_SIM_TIMER);
    sim_timer_started = TRUE;
  }
}

inline VOID maybe_report_init_timer()
{
  if (sim_args.report_total_time_p) {
    if (init_timer_started && !init_timer_reported) {
      cp_timer_stop(CP_INITIALIZATION_TIMER);
      cp_timer_report(CP_INITIALIZATION_TIMER, TIMER_UNITS_SECS);
      fflush(stdout);
      init_timer_reported = TRUE;
    }
  }
}

VOID start_seed_timer()
{
  cp_timer_clear(CP_SEED_TIMER);
  cp_timer_start(CP_SEED_TIMER);
}

VOID report_seed_timer()
{
  cp_timer_stop(CP_SEED_TIMER);
  cp_timer_report(CP_SEED_TIMER, TIMER_UNITS_SECS);
  fflush(stdout);
}

VOID clear_ckpt_timer()
{
  cp_timer_clear(CP_CKPT_TIMER);
}

VOID start_ckpt_timer()
{
  cp_timer_start(CP_CKPT_TIMER);
}

VOID stop_ckpt_timer()
{
  cp_timer_stop(CP_CKPT_TIMER);
}


VOID report_ckpt_timer()
{
  cp_timer_report(CP_CKPT_TIMER, TIMER_UNITS_SECS);
  fflush(stdout);
}

static VOID report_performance()
{
  TIMER sim_timer = cp_timers + CP_SIM_TIMER;
  int total_sps_report = (cp_info.is_conduction && cp_info.is_flow) ? total_sps/2 : total_sps;
  TIMESTEP timesteps = cp_info.time - cp_info.restart_time;
  TIMESTEP flow_timesteps = cp_info.flow_time - cp_info.restart_time_flow;
  TIMESTEP cond_timesteps = cp_info.cond_time - cp_info.restart_time_cond;
  dFLOAT secs = timer_wallclock_seconds(sim_timer);
  dFLOAT cpu_hours = secs * total_sps_report / 3600;
  dFLOAT gvot = (dFLOAT)cp_info.total_fluid_like_fevos * timesteps / 1e9;
  dFLOAT gsut = (dFLOAT)cp_info.total_regular_fesus * timesteps / 1e9;
  
  // Report coupling phase timers when flow and conduction solvers are
  // on and there is more than one coupled phase.
  if (cp_info.is_conduction && cp_info.is_flow && cp_info.time_coupling_phases.size() > 1)
    cp_info.coupling_phase_timers.print();
    
  if(cp_info.is_conduction && cp_info.is_flow) {
    msg_print("\nPerformance\n-----------");
    msg_print("Number of SPs        %9d",    (int)total_sps_report);
    msg_print("Number of RPs        %9d",    (int)total_rps);
    msg_print("CPU Hours            %9.4g",  cpu_hours);
    msg_print("Flow Timesteps       %9ld",   (long)flow_timesteps);
    msg_print("Conduction Timesteps %9ld",   (long)cond_timesteps);
    msg_print("GVoT                 %9.4g",  gvot);
    msg_print("GSuT                 %9.4g",  gsut);
    msg_print("GVoT/CPU-hour        %9.4g",  gvot / cpu_hours);
    msg_print("GSuT/CPU-hour        %9.4g",  gsut / cpu_hours);
    msg_print("timestep/sec         %9.4g",  timesteps / secs);
    msg_print("sec/timestep         %9.4g\n", secs / timesteps);
  } else {
    msg_print("\nPerformance\n-----------");
    msg_print("Number of SPs    %9d",    (int)total_sps_report);
    msg_print("Number of RPs    %9d",    (int)total_rps);
    msg_print("CPU Hours        %9.4g",  cpu_hours);
    msg_print("Timesteps        %9ld",   (long)timesteps);
    msg_print("GVoT             %9.4g",  gvot);
    msg_print("GSuT             %9.4g",  gsut);
    msg_print("GVoT/CPU-hour    %9.4g",  gvot / cpu_hours);
    msg_print("GSuT/CPU-hour    %9.4g",  gsut / cpu_hours);
    msg_print("timestep/sec     %9.4g",  timesteps / secs);
    msg_print("sec/timestep     %9.4g\n", secs / timesteps);
  }
} 

static VOID report_summary_time(asINT32 id, cSTRING after_colon)
{
  TIMER timer = cp_timers + id;
  dFLOAT secs = timer_wallclock_seconds(timer);
  dFLOAT hrs = secs / (60 * 60);

  msg_print("%s:%s %7g secs = %.4g hours (wallclock)", 
            timer->name, after_colon, secs, hrs);
}

inline VOID report_sim_and_total_timers()
{
  sim_timer_reported = TRUE;
  cp_timer_stop(CP_SIM_TIMER);
  cp_timer_stop(CP_TOTAL_TIMER);
  report_summary_time(CP_SIM_TIMER, "");
  report_summary_time(CP_CKPT_TIMER, "");
  report_summary_time(CP_TOTAL_TIMER, " ");
  fflush(stdout);
}


extern "C"
VOID maybe_report_init_and_sim_and_total_timers()
{
  maybe_report_init_timer();
  if (sim_args.report_total_time_p) {
    if (sim_timer_started && !sim_timer_reported) {
      report_sim_and_total_timers();
      if (sim_args.report_performance_p) {
        report_performance();
      }
    }
  }
  
  {
    extern int simulator_finish_status;

    TIMER sim_timer = cp_timers + CP_SIM_TIMER;
    int total_sps_report = (cp_info.is_conduction && cp_info.is_flow) ? total_sps/2 : total_sps;
    TIMER init_timer = cp_timers + CP_INITIALIZATION_TIMER;
    TIMESTEP timesteps = cp_info.time - cp_info.restart_time;
    dFLOAT init_secs = timer_wallclock_seconds(init_timer);
    dFLOAT secs = timer_wallclock_seconds(sim_timer);
    dFLOAT sim_secs = secs - init_secs;
    dFLOAT cpu_hours = secs * total_sps_report / 3600;
    dFLOAT gvot = (dFLOAT)cp_info.total_fluid_like_fevos * timesteps / 1e9;
    dFLOAT gsut = (dFLOAT)cp_info.total_regular_fesus * timesteps / 1e9;
    cJC_PACKAGE stat("Performance");
    cJC_PACKAGE estat(cJC_PACKAGE::FINISH);
    
    stat.DefineProperty("Number of SPs", total_sps_report);
    stat.DefineProperty("Number of RPs", total_rps);
    stat.DefineProperty("CPU Hours", cpu_hours, "Time", "hour");
    stat.DefineProperty("Timesteps", timesteps, "Time", "timestep");
    stat.DefineProperty("GVoT", gvot);
    stat.DefineProperty("GSuT", gsut);
    stat.DefineProperty("GVoT/CPU-hour", gvot / cpu_hours);
    stat.DefineProperty("GSuT/CPU-hour", gsut / cpu_hours);
    stat.DefineProperty("sec/timestep", secs / timesteps);
    stat.DefineProperty("Physical Time", cdi_data.seconds_per_timestep * timesteps, "Time", "second");
    stat.Emit();

    estat.DefineProperty("Total Wallclock", secs, "Time", "second");
    estat.DefineProperty("Init Wallclock", init_secs, "Time", "second");
    estat.DefineProperty("Sim Wallclock", sim_secs, "Time", "second");
    estat.DefineProperty("CPU", secs * total_sps_report, "Time", "second");
    estat.DefineProperty("Exit Status", simulator_finish_status ? "Error" : "Finished");
    estat.Emit();
  }
}

//----------------------------------------------------------------------------
// establish_error_handling
//----------------------------------------------------------------------------
static VOID establish_error_handling()
{
  // Establish error handling (needed by send_cp_sim_run_info())
  CHARACTER nameBuf[BUFSIZ];
  sprintf(nameBuf, "%s.simerr", cp_info.root_filename);
  cp_process_sp_errors_init(nameBuf);
}

//----------------------------------------------------------------------------
// add_initial_events_to_event_queue
//----------------------------------------------------------------------------
static VOID add_initial_events_to_event_queue()
{
  // Add an Exit Event onto the checkpoint queue 
  CKPT_QUEUE_ENTRY exit_entry   = new sCKPT_QUEUE_ENTRY;
  exit_entry->id                = EVENT_ID_EXIT;
  exit_entry->timestep          = cp_info.end_time;
  exit_entry->recur_rate        = 0;
  exit_entry->next_entry        = NULL;
  exit_entry->initial_condition = TRUE;
  exit_entry->precious          = FALSE;
  cp_info.async_ckpt_queue->add_entry(exit_entry);

  // If requested to terminate with an MME checkpoint...
  if (sim_args.mme_chkpnt_at_end) {
    CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
    new_queue_entry->id = EVENT_ID_MME_CKPT;
    new_queue_entry->timestep = TIMESTEP_LAST;
    new_queue_entry->recur_rate = 0;
    new_queue_entry->initial_condition = TRUE;
    new_queue_entry->precious = TRUE;
    cp_info.async_ckpt_queue->add_entry(new_queue_entry);
  }

  // If requested to terminate with a full checkpoint...
  if (sim_args.full_chkpnt_at_end) {
    CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
    new_queue_entry->id = EVENT_ID_FULL_CKPT;
    new_queue_entry->timestep = TIMESTEP_LAST;
    new_queue_entry->recur_rate = 0;
    new_queue_entry->initial_condition = TRUE;
    new_queue_entry->precious = (sim_args.no_final_checkpoint_retry? FALSE: TRUE);

    cp_info.async_ckpt_queue->add_entry(new_queue_entry);
  }

  // If requested to perform regular checkpoints
  if (cp_info.periodic_ckpts_p) {
    CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
    new_queue_entry->id = (sim_args.run_options & SIM_FULL_CKPTS) ? EVENT_ID_FULL_CKPT : EVENT_ID_MME_CKPT;
    new_queue_entry->timestep = cp_info.ckpt_time_desc.start;
    new_queue_entry->recur_rate = cp_info.ckpt_time_desc.period;
    new_queue_entry->initial_condition = TRUE;
    new_queue_entry->precious = FALSE;
    cp_info.async_ckpt_queue->add_entry(new_queue_entry);
  }
}

//----------------------------------------------------------------------------
// check_for_debugging_flags
//----------------------------------------------------------------------------
static VOID check_for_debugging_flags()
{
  cSTRING debug_malloc_mode = getenv("EXA_DEBUG_MALLOC_MODE");
  if(debug_malloc_mode != NULL) {
    debug_malloc_p = TRUE;
    if(strcmp(debug_malloc_mode, "CHECK_PTR") || (strcmp(debug_malloc_mode, "check_ptr")))
      exa_enable_malloc_debug(EXA_MALLOC_CHECK_PTR);
    else if(strcmp(debug_malloc_mode, "VERIFY_HEAP") || (strcmp(debug_malloc_mode, "verify_heap")))
      exa_enable_malloc_debug(EXA_MALLOC_VERIFY_HEAP);
    else
      msg_error("Setting of EXA_DEBUG_MALLOC_MODE (%s) is invalid", debug_malloc_mode);
  }

}

//----------------------------------------------------------------------------
// set_cp_stack_limit
//----------------------------------------------------------------------------
void set_cp_memory_limits()
{
  // The stack limit we set now in the CP does not affect the size of the CP's stack, but
  // it does determine the stack size for all children.
  cSTRING stack_limit = getenv("EXA_CP_CHILDREN_STACK_LIMIT");
  if (stack_limit != NULL) {
    msg_print("Stack limit for child processes set to %s.", stack_limit);
    raise_memory_limits(atol(stack_limit));
  } else {
    // Do not alter the stack limit
#if 0
    const asINT32 SIMENG_AND_INLINE_DECOMP_STACK_SIZE = 1 << 23;  // 8MB
    raise_memory_limits(SIMENG_AND_INLINE_DECOMP_STACK_SIZE);
#endif
    raise_memory_limits(0);
  }
}


//----------------------------------------------------------------------------
// setup_license_env_variables
//----------------------------------------------------------------------------
void setup_license_env_variables()
{
  // Place the values for lm_user and lm_host in the environment where they can be 
  // retrieved by the guts of detect_proc_license, even if it runs in a child process.
  static char lm_user[100];
  static char lm_host[100];
  if (sim_args.lm_user != NULL) {
    sprintf(lm_user, "EXA_LM_USER=%.80s", sim_args.lm_user);
    putenv(lm_user);
  }
  if (sim_args.lm_host != NULL) {
    sprintf(lm_host, "EXA_LM_HOST=%.80s", sim_args.lm_host);
    putenv(lm_host);
  }

  // blank the DISPLAY string for per-processor licenses
  char exa_lm_display[] = "EXA_LM_DISPLAY=init";
  putenv(exa_lm_display);

#if ! SEPARATE_LICENSE_PROCESS
  init_license_strings(sim_args.lm_alt);
#endif
  static char lm_alt[34];
  if (sim_args.lm_alt != NULL) {
    sprintf(lm_alt, "EXA_LM_ALT=%.20s", sim_args.lm_alt);
    putenv(lm_alt);
  }
}

static VOID setup_meas_compression() {
  // environment variables to force/disable compression for all files.
  BOOLEAN enable_compression = getenv("EXA_ENABLE_MEAS_COMPRESSION") != NULL;
  BOOLEAN disable_compression = getenv("EXA_DISABLE_MEAS_COMPRESSION") != NULL;
  if (enable_compression && disable_compression) {
    msg_print("Both EXA_ENABLE_MEAS_COMPRESSION and EXA_DISABLE_MEAS_COMPRESSION set; ignoring them");
    cp_info.meas_compression = DO_NOT_FORCE;
  } else if (enable_compression) {
    msg_print("EXA_ENABLE_MEAS_COMPRESSION set; enabling compression for all measurements");
    cp_info.meas_compression = ENABLE_COMPRESSION;
  } else if (disable_compression)  {
    msg_print("EXA_DISABLE_MEAS_COMPRESSION set; disabling compression for all measurements");
    cp_info.meas_compression = DISABLE_COMPRESSION;
  } else {
    cp_info.meas_compression = DO_NOT_FORCE;
  }
}

//----------------------------------------------------------------------------
// initialize_cp_sp_lib
//----------------------------------------------------------------------------
void initialize_cp_sp_lib(int argc, cSTRING argv[], asINT32 num_sps)
{
  cp_initialize(argc, (char **)argv); // calls MPI_Init, etc

  if(num_sps != total_sps) {
    msg_error("%d SPs were specified on the command line, but %d were indicated "
              "by the MPI control file.", num_sps, total_sps);
    exit(exit_failure_code());
  }
}

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
static VOID broadcast_vertices()
{
  DGF_VERTEX_INDEX n_vertices = cp_info.n_original_surfel_vertices;
  ccDO_FROM_BELOW_BY(i, 0, n_vertices, N_VERTICES_PER_BROADCAST) {
    DGF_VERTEX_INDEX n_to_write = MIN(N_VERTICES_PER_BROADCAST, n_vertices - i);
//#ifdef PARTICLE_MODELING_DOUBLE_PRECISION_VERTEX
    MPI_Bcast(&cp_info.vertex_array[i], 3 * n_to_write, eMPI_dFLOAT, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
//#else
//    MPI_Bcast(&cp_info.vertex_array[i], 3 * n_to_write, eMPI_sFLOAT, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
//#endif
  }
}
//#endif
void vel_exceed_warning()
{
  cBOOLEAN ve_warning = FALSE;
  cBOOLEAN dummy = FALSE;
  MPI_Reduce(&dummy, &ve_warning, 1, MPI_INT, MPI_BOR, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
  if(ve_warning)
    msg_warn("Thermal Acceleration factor in momentum freeze (%f) might be too big, that causes the seed velocity to exceed the velocity cap.\n", cp_info.thermal_timestep_ratio);
}

//----------------------------------------------------------------------------
// compute_boundary_surfel_percent_seeded
//----------------------------------------------------------------------------
void compute_boundary_surfel_percent_seeded()
{
  int bs[3] = { 0 };

  int dummy_int = 0;
  MPI_Reduce(&dummy_int, &bs[0], 1, MPI_INT, MPI_SUM,  eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 
  MPI_Reduce(&dummy_int, &bs[1], 1, MPI_INT, MPI_SUM,  eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 
  MPI_Reduce(&dummy_int, &bs[2], 1, MPI_INT, MPI_SUM,  eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
  

  int n_boundary_surfels = bs[0];
  int n_seeded_boundary_surfels = bs[1];
  int n_transient_seeded_boundary_surfels = bs[2];

  if (n_seeded_boundary_surfels > 0) {
    asINT32 percent_seeded = 0.5 + (100.0 * (dFLOAT)n_seeded_boundary_surfels / n_boundary_surfels);
    if ((percent_seeded == 100) && (n_seeded_boundary_surfels < n_boundary_surfels))
      percent_seeded = 99;
    if ((percent_seeded == 0) && (n_seeded_boundary_surfels > 0))
      percent_seeded = 1;
    msg_print("%d%% of inlet/outlet surfels (%d of %d) seeded from seed file.",
        percent_seeded,
        n_seeded_boundary_surfels,
        n_boundary_surfels);
  }      
  if (n_transient_seeded_boundary_surfels > 0) {
    asINT32 percent_seeded = 0.5 + (100.0 * (dFLOAT)n_transient_seeded_boundary_surfels / cp_info.m_n_surfels_on_transient_seeded_faces);
    if ((percent_seeded == 100) && (n_transient_seeded_boundary_surfels < cp_info.m_n_surfels_on_transient_seeded_faces))
      percent_seeded = 99;
    if ((percent_seeded == 0) && (n_transient_seeded_boundary_surfels > 0))
      percent_seeded = 1;
    msg_print("%d%% of inlet/outlet surfels (%d of %d) are transient seeded from surface measurement file",
        percent_seeded,
        n_transient_seeded_boundary_surfels,
        cp_info.m_n_surfels_on_transient_seeded_faces);
  }
}

static void reduce_fluid_voxels_count() {
  int voxel_count = 0;
  int dummy_int = 0;
  MPI_Reduce(&dummy_int, &voxel_count, 1, MPI_INT, MPI_SUM,  eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
  cp_info.total_fluid_like_voxels = voxel_count;
}

//----------------------------------------------------------------------------
// wait_for_all_sps
//----------------------------------------------------------------------------
#if 0
void wait_for_all_sps()
{
  // Wait for all SPs to get past initialization 
  /* This effectively replaces the MPI_Barrier that */
  /* was formerly here. */
  while (1) {
    cp_recompute_status();
    cp_process_sp_errors(wallclock_time_secs());
    if (cp_info.cp_status.status != SIM_STATUS_INIT) break;
    cp_sleep();
  }
}
#endif

static VOID determine_turb_synth_checkpointer() {

  if (total_sps > 1) {
    uINT32 checkpointer = total_sps + 1;
    uINT32 new_checkpointer;
    MPI_Allreduce(&checkpointer, &new_checkpointer, 1, MPI_INT, MPI_MIN, eMPI_sp_cp_comm);
    cp_info.m_turb_synth_ckpt_sp = new_checkpointer;
  }
}

//----------------------------------------------------------------------------
// print_debug_memory_report
//----------------------------------------------------------------------------
void print_debug_memory_report()
{
  if(debug_malloc_p) {
    msg_print("Memory report prior to `execution (CP)'");
    exa_malloc_debug_report(TRUE);
  }
}

//----------------------------------------------------------------------------
// complete_remaining_checkpoints
//----------------------------------------------------------------------------
void complete_remaining_checkpoints()
{
  // Finish any remaining checkpoints
  {
    int n_tries = 0;
    while (cp_info.async_ckpt_queue->entry_count > 0) {
      process_checkpoints();
      if (cp_info.async_ckpt_queue->entry_count <= 0)
        break;
      platform_sleep_seconds(1);
      ++n_tries;
      if (n_tries == 10)
        msg_print("Waiting for end-of-simulation checkpoint data from SPs"); 
    }
  }
}

//----------------------------------------------------------------------------
// report_time_for_last_step
//----------------------------------------------------------------------------
void report_time_for_last_step(SIM_STATUS& last_timestep_reported)
{
  /* Report time for last timestep */
  asINT32 time_every = sim_args.time_every;
  if (time_every && cp_info.cp_status.status >= (last_timestep_reported + time_every)) {
    static CHARACTER tbuffer[70];
    cp_timer_stop(CP_TIMESTEP_TIMER);
    sprintf(tbuffer, "Time for last %d timesteps (%d to %d)",
        cp_info.cp_status.status - last_timestep_reported,
        last_timestep_reported + 1,
        cp_info.cp_status.status);
    cp_timer_rename(CP_TIMESTEP_TIMER, tbuffer); 
    cp_timer_report(CP_TIMESTEP_TIMER, TIMER_UNITS_SECS);
    fflush(stdout);
    /* Get ready for next timer report */
    last_timestep_reported = cp_info.cp_status.status;
    cp_timer_clear(CP_TIMESTEP_TIMER);
    cp_timer_start(CP_TIMESTEP_TIMER);
  }
}

//----------------------------------------------------------------------------
// check_for_exa_signal_command
//----------------------------------------------------------------------------
void check_for_exa_signal_command(WALLCLOCK_TIME_SECS& time_of_last_signal_check, WALLCLOCK_TIME_SECS time_secs )
{
  // Check for a pending .exa_signal_cmd every 4 seconds 
  // Only after the current signals have been processed
  if (cp_info.n_signal_cmds_pending && !cp_info.async_msg_reply_pending_p) {
    asINT32 n_pending_cmds = cp_info.n_signal_cmds_pending;
    ccDOTIMES(nc, cp_info.n_signal_cmds_pending) {
      asINT32 first_pending_cmd = cp_info.n_signal_cmds_read -
                                  cp_info.n_signal_cmds_pending;
      // Creating a reference to the vector. This makes accessing the objects in
      // the vector easier.
      std::vector <sCMD_STRING> &pending_cmds = *cp_info.pending_signal_cmds;
      char *cmd = pending_cmds[first_pending_cmd + nc].cmd;
      cp_finish_signal_cmd(TRUE, cmd);
      n_pending_cmds--;
      // Still processing this command, no point in trying
      // other commands. Come back once this is processed.
      if (cp_info.async_msg_reply_pending_p)
        break;
    }
    cp_info.n_signal_cmds_pending = n_pending_cmds; 
  }
  if (WALLCLOCK_TIME_DIFF(time_secs, time_of_last_signal_check) > 4) {
    // Add signal commands to the queue
    if (jobctl_server_is_signal_cmd_pending()) {
      cp_handle_signal_cmds(FALSE);
    }
    time_of_last_signal_check = time_secs;
  }
}

//----------------------------------------------------------------------------
// check_status_of_licenses
//----------------------------------------------------------------------------
void check_status_of_licenses(WALLCLOCK_TIME_SECS& last_heartbeat_time, WALLCLOCK_TIME_SECS time_secs )
{
  if (WALLCLOCK_TIME_DIFF(time_secs, last_heartbeat_time) > secs_between_license_heartbeats) {
    detect_proc_license_still_okay();
    last_heartbeat_time = time_secs;
  }
}


//----------------------------------------------------------------------------
// print_final_status
//----------------------------------------------------------------------------
void print_final_status()
{
  if (cp_info.time > 0) {
    CHARACTER final_status[1024];
    if(cp_info.is_conduction && cp_info.is_flow)
      sprintf(final_status,
              cp_info.exit_on_schedule
              ? "Finished %d Flow Timesteps, %d Conduction Timesteps" : (cp_info.exit_event_id == EVENT_ID_HALT
                                          ? "Finished %d Flow Timesteps, %d Conduction Timesteps: Halt Request"
					  : "Finished %d Flow Timesteps, %d Conduction Timesteps: Exit Request"),
                          /* max_timestep */ cp_info.flow_time, cp_info.cond_time);

    else
      sprintf(final_status,
	    cp_info.exit_on_schedule
	    ? "Finished %d Timesteps" : (cp_info.exit_event_id == EVENT_ID_HALT
					 ? "Finished %d Timesteps: Halt Request"
					 : "Finished %d Timesteps: Exit Request"),
                                         /* max_timestep */ cp_info.time);
    msg_print("%s", final_status);
    cp_jobctl_output_status(final_status);

  } else {
    cp_jobctl_output_status("Finished");
  }
}

//----------------------------------------------------------------------------
// finalize
//----------------------------------------------------------------------------
#if defined(_EXA_HPMPI)
// Dummy MP_Irecv to work around hanging problem in HP-MPI - see PR 19489
static MPI_Request hpmpi_dummy_request;
#endif

static VOID rotational_dynamics_finalize()
{
  CP_ROTATIONAL_DYNAMICS_DESC rotdyn_desc = cp_info.rotational_dynamics_descs;
  ccDOTIMES(i, cp_info.n_rotational_dynamics_descs) {
    if (rotdyn_desc->m_is_resistive_torque_time_varying && rotdyn_desc->m_resistive_torque_mpi_request != MPI_REQUEST_NULL) {
      MPI_Cancel(&rotdyn_desc->m_resistive_torque_mpi_request);
    }
    if (rotdyn_desc->m_is_external_torque_time_varying && rotdyn_desc->m_external_torque_mpi_request != MPI_REQUEST_NULL) {
      MPI_Cancel(&rotdyn_desc->m_external_torque_mpi_request);
    }
    rotdyn_desc++;
  }
}

static VOID finalize()
{
#if defined(_EXA_HPMPI)
  // Complete dummy MPI_Irecv - see PR 19489
  asINT32 dummy_message = 0xFEED0FED;
  ccDOTIMES(sp, total_sps) {
    MPI_Send(&dummy_message, 1, eMPI_asINT32, sp, eMPI_DEBUG_DUMMY_TAG, eMPI_sp_cp_comm);
  }
  MPI_Status dummy_status;
  MPI_Wait(&hpmpi_dummy_request, &dummy_status);
#endif

  rotational_dynamics_finalize();
  windows_finalize();

  if(cp_info.dsm_reader) {
    cp_info.dsm_reader->cancel_pending_send_requests();
    delete cp_info.dsm_reader;
  }

    finalize_tbs_requests();
  // Canceling all the table pending send requests
  if (cp_info.table_queue && cp_info.table_comm_descs) {
      ccDOTIMES(sp, total_sps) {
        for (int i=2; i>=0; i--) {
          MPI_Request *send_request = &cp_info.table_comm_descs[sp].mpi_requests[i];
          int flag = 0;
          MPI_Status status;
          MPI_Test(send_request, &flag, &status);
          if (!flag)
            MPI_Cancel((send_request));
      }
    }
  }

#if !defined(_EXA_LAM_MPI)
  if(sim_args.barrier_instead_of_finalize) {
    sigset (SIGTERM,SIG_IGN); 
    MPI_Barrier(eMPI_sp_cp_comm);
  } else {
    MPI_Finalize();
  }
#endif
}

static void gpu_output_status()
{
  if (sim_args.gpu) {
    if (sim_args.gpu_repeatable) {
      msg_print("Simulation will run on GPU in repeatable mode");
    } else {
      msg_print("Simulation will run on GPU in non-repeatable mode");
    }
  }
}

static void parallel_io_status()
{
  if (sim_args.parallel_io) {
    const char* user_io_mode = getenv("INDEPENDENT_IO");
    if(user_io_mode != NULL) {
      msg_print("Simulation is using parallel independent mode for I/O");
    }
    else
    {
      msg_print("Simulation is using parallel mode for I/O");
    }
    if(sim_args.parallel_io_max_buffer_size == 0)// User did not defined a custom value
    {
      if(cp_info.parallel_io_one_write_buffer_size_mb != 0)
        sim_args.parallel_io_max_buffer_size = cp_info.parallel_io_one_write_buffer_size_mb; //taken from decomposer if sps>0
      else
        sim_args.parallel_io_max_buffer_size = 128;
    }
    msg_print("Parallel checkpoint buffer size: %u MB", 
                  sim_args.parallel_io_max_buffer_size);
    if (cp_info.parallel_io_one_write_buffer_size_mb !=0 && cp_info.parallel_io_one_write_buffer_size_mb != sim_args.parallel_io_max_buffer_size)
      msg_print("Recommended parallel checkpoint buffer size to maximize performance: %lu MB", 
                  cp_info.parallel_io_one_write_buffer_size_mb);
  }
}

//----------------------------------------------------------------------------
// main
//----------------------------------------------------------------------------
int main_internal(int argc, cSTRING argv[])
{
  // #ifndef COMMIT_ID
  // #define COMMIT_ID "unknown"
  // #endif

  // printf("[AAR7] PWF dev commit: %s\n", COMMIT_ID);

  argv0 = argv[0];
  platform_exa_debug_pfx(&argc, (char **) argv);
  cp_info.launch_time = platform_get_current_time();
  check_for_debugging_flags();

  set_cp_memory_limits();
  set_fp_underflow_flush_to_zero();

  // Parse arguments and do basic initialization. Copy argv because exa_sim_initialize trashes the vector.
  sSTD_ARGS std_args;
  STRING *argv_copy = xnew STRING [ argc];
  memcpy(argv_copy, argv, argc * sizeof(STRING));
  cSTRING lgi_filename = exa_sim_initialize(argc, (char **)argv, argv_copy, &sim_args, &std_args);

  sCP_STREAM_MANAGER cp_stream_manager;
  get_license_heartbeat_interval();

  read_turb_parm_init(); 

  int num_sps = 1;
  if (sim_args.n_processors != NULL) {
    num_sps = atoi(sim_args.n_processors);
  }

  cp_info_basic_init();

  if ((num_sps < 0) || (num_sps > STP_MAX_PROCS)) {
    msg_error("Invalid number of processors (%d), must be between 1 and %d",
              num_sps, STP_MAX_PROCS);
    exit(exit_failure_code());
    // Warning about running on 1 processor moved to SP
  } 

  // must happen before MPI_Initialize to reroute fork/system calls through a
  // ssytem command and coupling interface daemon if in daemon mode (RT14450)
  cSTRING message = cTHIRD_PARTY_INTERFACE::InitializeSystemDaemonInterface(argv[0]);
  if (message)
    msg_error("%s", message);

#if SURF_COUP
  cp_stream_manager.init_surface_coupling(&sim_args, argv[0]);
#endif

  initialize_cp_sp_lib(argc, argv, num_sps);

  cp_info.physical_node_sp_ranks = eMPI_cp_init_sp_node_comm();

  eMPI_wait_on_startup(argv[0], eMPI_PROCESS::CP);
  
  license_equivalent_sps = num_sps;
  
  if (sim_args.gpu) {
    std::vector<sSP_GPU_INFO> gpu_info = recv_gpu_info();
    license_equivalent_sps = process_gpu_info(gpu_info);
  }

  setup_license_env_variables();
  // change the license due to the number of RPs?
  detect_proc_license(license_equivalent_sps + total_rps); 

  delete[] argv_copy;

  cp_info.async_ckpt_queue = xnew sCKPT_QUEUE;

#if defined(_EXA_HPMPI)
  // Dummy MP_Irecv to work around hanging problem in HP-MPI - see PR 19489
  asINT32 dummy_recv_buffer;
  xMPI_Irecv(&dummy_recv_buffer, 1, eMPI_asINT32, 0, eMPI_DEBUG_DUMMY_TAG, eMPI_sp_cp_comm, &hpmpi_dummy_request);
#endif

  // Reserve some low-numbered file descriptors for later 
  FILE *dummy_fds[16];
  ccDOTIMES(i,16) dummy_fds[i] = fopen("/dev/null","a");

  maybe_start_init_and_total_timers();

  atexit(maybe_report_init_and_sim_and_total_timers);
  detect_proc_license_still_okay();

  initialize_status_tree();

  setup_meas_compression();

  ccDOTIMES(i,16) fclose(dummy_fds[i]);

  cp_register_simerr_handler(cp_process_init_time_simerrs_func);

  establish_error_handling();
  initialize_heat_exchanger_and_condenser_data();


  send_early_sim_run_info(&sim_args);
  receive_early_sim_run_info();


  sim_args.argc = argc;
  sim_args.argv = argv;

  {
    char tmpbuf[4096];
    strcpy(tmpbuf, sim_args.cdi_filename);

    int cdilen = strlen(tmpbuf);

    if (strcmp(tmpbuf + cdilen - 4, ".cdi") == 0) {
      tmpbuf[cdilen - 4] = (char) 0x00;
    }

    cJC_PACKAGE::SetJob(tmpbuf);
  }


  cp_info.request_to_exit_by_monitor = FALSE;
  cp_info.request_to_exit_by_monitor_convergence = FALSE;
  cp_info.request_to_exit_by_greatest_meas_window_end_time = FALSE;
  cp_info.exit_time_requested_by_monitor = TIMESTEP_INVALID;

  cp_info.request_to_exit_by_pt_time = FALSE;
  cp_info.exit_time_requested_by_pt_time = TIMESTEP_INVALID;

  cp_info.request_to_stop_avg_mme = FALSE;
  cp_info.request_to_reschedule_avg_mme = FALSE;
  cp_info.requested_time_to_stop_avg_mme = TIMESTEP_MAX;
  cp_info.requested_time_to_reschedule_avg_mme = TIMESTEP_MAX;

  cp_info.request_to_start_emitter = FALSE;
  cp_info.requested_time_to_start_emitter = TIMESTEP_MAX;

  cp_info.time_to_stop_avg_mme = TIMESTEP_MAX;
  cp_info.avg_mme_duration = TIMESTEP_INVALID;

  cp_info.average_mme_stopped = FALSE;

  cp_info.is_full_checkpoint_restore = sim_args.resume_filename && !g_seed_ctl.is_mme_checkpoint();


  cp_info.is_mme_checkpoint_restore  = g_seed_ctl.is_mme_checkpoint();

  cp_info.freeze_momentum_field = (g_seed_ctl.is_sim_seeded() || sim_args.suppress_seed_requirement) && sim_args.freeze_momentum_field;

  cp_info.solver_version = getenv("EXA_POWERFLOW_SOLVER_VERSION");
  // read LGI and CDI files
  // send record to streams

  cp_stream_manager.open_sp_data_streams();
  detect_proc_license_still_okay();
 
  maybe_terminate_simulation();

  gpu_output_status();
  die_if_gpu(cp_info.is_full_checkpoint_restore, "Full checkpoint restore", false);

  WALLCLOCK_TIME_SECS time_of_last_signal_check = wallclock_time_secs();
  cp_stream_manager.read_dgf_and_cdi_files();

  maybe_terminate_simulation();

  // Here's where we know the start/restart time
  {
    cJC_PACKAGE statistic(cJC_PACKAGE::START, cJC_PACKAGE::SIM);
    statistic.DefineProperty("Version", EXA_ENV::GetDistName());
    statistic.DefineProperty("Start Timestep", (long) cp_info.time);
    statistic.DefineProperty("Run Directory", EXA_ENV::GetCwd());
    statistic.Emit();
  }
  cp_stream_manager.close_sp_data_streams();
  maybe_terminate_simulation();

  windows_build_reduction_trees(); // also allocates the SP meas cells
  init_transient_boundary_seeding_comm();
  process_any_heat_exchangers_and_condensers(sim_args.resume_filename != NULL);

  parallel_io_status();
  insert_cmd_line_post_meas_cmds_in_queues();
  insert_pre_table_read_cmds_in_queues();

  add_initial_events_to_event_queue();

  check_for_missing_measurement_files();

#if SURF_COUP
  // This call relies on windows being properly initialized.
  initialize_coupling_model_queue();
#endif // SURF_COUP

  // Initialize the asynchronous message environment
  eMPI_async_event_msg_type_init(&cp_info.eMPI_ASYNC_MSG_TYPE);

  if (cp_jobctl_memory_status_requested())
    cp_jobctl_output_status("About to send late global info");
  send_late_sim_run_info(&sim_args);
  receive_late_sim_run_info();
  if (cp_jobctl_memory_status_requested())
    cp_jobctl_output_status("Global info exchanged with SPs");

  if (cp_jobctl_memory_status_requested())
    cp_jobctl_output_status("Late global info received");

  if (cp_jobctl_memory_status_requested())
    cp_jobctl_output_status("CP window info sent to SPs");

  recv_ref_frame_info();
  recv_mbc_info();
  recv_movb_info();
  send_transient_seed_data();
  if(cp_info.is_full_checkpoint_restore) {
    complete_tbs_send_req();
    send_transient_seed_data();
  }
  allocate_meas_window_lrf_mbc_and_movb_info(); // must follow recv_ref_frame_info and recv_mbc_info

  if (cp_info.meas_windows.some_surface_meas_window)
    g_meas_window_vertex_map.create();

  windows_post_initial_recvs();  // must be after recv_ref_frame_info and recv_mbc_info
  // sri_file is opened for writing in the following function in case of resumption
  // from a checkpoint
  verify_precision_of_meas_var_data();

  maybe_terminate_simulation();

#if SURF_COUP
  cp_stream_manager.exchange_cp_coupling_info();
#endif

  if (cp_jobctl_memory_status_requested())
    cp_jobctl_output_status("Received coupling surfel definition from SPs");

  detect_proc_license_still_okay();

#if 0
  // Wait for all SPs to get past initialization and report any errors
  // encountered by the SPs during initialization.  The corresponding 
  // recv_and_send_status code in SIMENG was causing a hang in some cases,
  // so we no longer need to wait for the SPs.
  wait_for_all_sps();
#endif

#define HACKED_BROADCASTING

#ifdef HACKED_BROADCASTING
  if (cp_info.is_shell_conduction_solver) {
    cp_particle_sim.broadcast_vertices();
  }

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  if ( cp_info.is_particle_solver) {
    cp_particle_sim.broadcast_vertices();
    if( cp_info.is_film_solver) {
      //This function also send vertices for stencil building in sp.
      cp_particle_sim.wait_for_sps_to_build_stencils();
    } else{
      //If not building stencil, the vertices only broadcast once.
      cp_particle_sim.broadcast_vertices();
    }
    cp_particle_sim.initialize();
  }
//#endif
#else
  if (cp_info.is_shell_conduction_solver || cp_info.is_particle_solver) {
    if (cp_info.is_film_solver)
      cp_particle_sim.wait_for_sps_to_build_stencils();
    else
      cp_particle_sim.broadcast_vertices();
  }

  if (cp_info.is_particle_solver) {
    cp_particle_sim.initialize();
  }
#endif

  wait_for_all_initialization_simerrs();

  maybe_terminate_simulation();

  determine_turb_synth_checkpointer();


  //vel_exceed_warning();
  compute_boundary_surfel_percent_seeded();
  // count number of fluid voxels needed for mme checkpoint
  reduce_fluid_voxels_count();
  // Because of the calls to MPI_Reduce in compute_boundary_surfel_percent_seeded, we
  // know that all SPs are past initialization. 
  force_cp_status_to_time_zero();

  
  msg_print("Finished case initialization");
  cp_jobctl_output_status("Initialization complete");

  // We used to think that the call to MPI_Reduce in compute_boundary_surfel_percent_seeded 
  // was synchronizing, but this is not the case. The barrier below is necessary for precise 
  // benchmarking. Without it the CP may not get around to starting the sim timer until the
  // SP's have progressed some number of timesteps.

  maybe_report_init_timer();
  MPI_Barrier(eMPI_sp_cp_comm);
  maybe_start_sim_timer();
  clear_ckpt_timer();

  if (sim_args.run_options & SIM_REPORT_CPU_AFFINITY) {
    std::string affinity = print_thread_affinity();
    msg_print("CP> CPU Affinity: %s", affinity.c_str());
  }

#if CPU_TIME_CP_THREADS
  struct timespec sp_start_timespec;
  clock_gettime(CLOCK_PROCESS_CPUTIME_ID, &sp_start_timespec);   // Start the both-threads timer

  struct timespec main_thread_start_timespec;
  clock_gettime(CLOCK_THREAD_CPUTIME_ID, &main_thread_start_timespec); // Start the main-thread timer
#endif
  
  detect_proc_license_still_okay();

  print_debug_memory_report();

  // If using DSLS credits, ideally we would register terminate_licenses as an atexit function so
  // that the final set of credits can be committed. Unfortunately, we're not confident this can
  // be implemented robustly. Also, this is not necessary here if there is a separate license process.
  // 
  // atexit(terminate_licenses);

  {
    cJC_PACKAGE stats("Case Size");

    stats.DefineProperty("Voxels", (long) cp_info.total_fluid_like_voxels);
    stats.DefineProperty("Fine Equivalent Voxels", cp_info.total_fluid_like_fevos);
    stats.DefineProperty("Surfels", cp_info.total_regular_surfels);
    stats.DefineProperty("Fine Equivalent Surfels", cp_info.total_regular_fesus);

    stats.Emit();
  }

  /* Setup for main loop */
  WALLCLOCK_TIME_SECS time_secs = wallclock_time_secs();
  check_for_exa_signal_command(time_of_last_signal_check, time_secs);

  pthread_t IO_results_thread = 0;
  {
    SIM_STATUS last_timestep_reported = -1;

    cp_timer_clear(CP_TIMESTEP_TIMER);
    cp_timer_start(CP_TIMESTEP_TIMER);

    detect_proc_license_still_okay();
    WALLCLOCK_TIME_SECS last_heartbeat_time = wallclock_time_secs();

    IO_results_thread = launch_IO_results_thread();
    while (1) { // Main loop
      // Compute current status
      cp_recompute_status();
      // End of simulation : Time to pack up
      if (cp_info.cp_status.status == SIM_STATUS_DONE) {
     
        process_exit_condition();

        complete_remaining_checkpoints();
        
        merge_IO_thread = true;
        
        if (IO_results_thread != 0) {
          pthread_join(IO_results_thread, 0);
        }
        
        if(has_trajectory_window() && !sim_args.disable_particle_modeling) {
          receive_all_remaining_trajectory_window_measurements();
          write_pri_result_buffers();
        }

        // Should be done twice, since the measurement writing thread has checked out
        // and may have added soemthing to the post recvs queue
        // and processing of write sri results queue may add something more to the
        // post recvs queue and these receives should be completed and written to disk.
        ccDOTIMES(iflush, 2) {
          // remove and process all entries from post recvs queue
          while (!cp_info.post_recvs_queue->is_empty()) {
            CP_MEAS_WINDOW window = cp_info.post_recvs_queue->remove_entry();
            if (window) {
              cp_info.post_recvs_queue->process(window, wallclock_time_secs());
            }
          }

          windows_output<ROTDYN_OUTPUT_QUEUE>(wallclock_time_secs(), TRUE);
          windows_output<FLOW_OUTPUT_QUEUE>(wallclock_time_secs(), TRUE);
          windows_output<COND_OUTPUT_QUEUE>(wallclock_time_secs(), TRUE);

          // remove and process all entries from write sri results queue
          while (!cp_info.write_sri_results_queue->is_empty()) {
            CP_MEAS_WINDOW window = cp_info.write_sri_results_queue->remove_entry();
            if (window) {
              cp_info.write_sri_results_queue->process(window, wallclock_time_secs());
            }
          }
        }

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
//        if(cp_info.is_particle_solver)
        if(has_trajectory_window() && !sim_args.disable_particle_modeling) {
          receive_all_remaining_trajectory_window_measurements();
          write_pri_result_buffers();
        }
//#endif

        process_exit_condition();
        complete_remaining_checkpoints();
        maybe_wait_for_tables_at_end();

#if SURF_COUP
        maybe_wait_for_surface_coupling_jobs();
        close_coupling_model_summary_files();
#endif
        break;
      }

      if (cp_info.is_radiation) {
        update_rp_status();
        update_sp_radiation_wait_time();
      }

      report_time_for_last_step(last_timestep_reported);

      // Check again for any pending checkpoint requests
      handle_pending_async_event_request();

      WALLCLOCK_TIME_SECS time_secs = wallclock_time_secs();
      check_for_exa_signal_command(time_of_last_signal_check, time_secs);
      if (cp_info.request_to_exit_by_monitor && cp_info.exit_time_requested_by_monitor != cp_info.end_time) {
        if (register_async_event_request(EVENT_ID_EXIT, 0, cp_info.exit_time_requested_by_monitor, FALSE, FALSE))
          cp_info.request_to_exit_by_monitor = FALSE;
      }
      if (cp_info.request_to_reschedule_avg_mme) {
#if DEBUG_AVG_MME
        msg_print("Request to reschedule avg mme for timestep %d with arg %d", cp_info.requested_time_to_reschedule_avg_mme, cp_info.avg_mme_duration);
#endif
        if (register_async_event_request(EVENT_ID_RESCHEDULE_AVG_MME_CKPT, 
                                         cp_info.avg_mme_ckpt_interval, 
                                         MAX(cp_info.requested_time_to_reschedule_avg_mme, cp_info.restart_time),
                                         FALSE, 
                                         TRUE))
          cp_info.request_to_reschedule_avg_mme = FALSE;
      }

      if (cp_info.request_to_stop_avg_mme) {
#if DEBUG_AVG_MME
        msg_print("At timestep %d request to stop average mme ckpt at timestep %d", cp_info.time, cp_info.requested_time_to_stop_avg_mme);
#endif
        if (register_async_event_request(EVENT_ID_STOP_AVG_MME_CKPT,
                                         0, 
                                         MAX(cp_info.requested_time_to_stop_avg_mme, cp_info.restart_time), 
                                         FALSE, 
                                         TRUE))
          cp_info.request_to_stop_avg_mme = FALSE;
      }
      if (cp_info.request_to_exit_by_greatest_meas_window_end_time && cp_info.greatest_meas_window_end_time != cp_info.end_time) {
        // If the greatest meas window is avg mme, then the stop avg mme event is scheduled some timestep aligned with the coarest scale.
        // SHould do the same for the exit event since otherwise exit could be scheduled earlier and cause the simulator to hang.
#if DEBUG_AVG_MME
        msg_print("At timestep %d request to exit at timestep %d due to greatest meas window.", cp_info.time, cp_info.greatest_meas_window_end_time);
#endif
        if (register_async_event_request(EVENT_ID_EXIT, 0, cp_info.greatest_meas_window_end_time, FALSE, cp_info.has_average_mme_window))
          cp_info.request_to_exit_by_greatest_meas_window_end_time = FALSE;
      }

      if (cp_info.request_to_exit_by_pt_time && cp_info.exit_time_requested_by_pt_time != cp_info.end_time) {
        if (register_async_event_request(EVENT_ID_EXIT, 0, cp_info.exit_time_requested_by_pt_time, FALSE, FALSE))
          cp_info.request_to_exit_by_pt_time = FALSE;
      }
      //Start emitters
      TIMESTEP requested_time_to_start_emitters = 0;
      asINT32 n_emitters_to_start = 0;
      if (!cp_particle_sim.emitters_requested_to_start.empty()) {
        requested_time_to_start_emitters = cp_particle_sim.emitters_requested_to_start.begin()->first;
        n_emitters_to_start = cp_particle_sim.emitters_requested_to_start.begin()->second.size();
      }
      if (cp_info.request_to_start_emitter && requested_time_to_start_emitters > cp_info.restart_time) {
#if DEBUG_START_EMITTERS
        msg_print("At timestep %d request to start %d emitters at timestep %d", cp_info.time, n_emitters_to_start, requested_time_to_start_emitters);
#endif
        if (register_async_event_request(EVENT_ID_START_EMITTER,
                                         n_emitters_to_start,
                                         requested_time_to_start_emitters, 
                                         FALSE, 
                                         FALSE))
          cp_info.request_to_start_emitter = FALSE;
      }

      //Start wipers
      TIMESTEP requested_time_to_start_wipers = cp_particle_sim.wipers_requested_to_start.begin()->first;
      asINT32 n_wipers_to_start = cp_particle_sim.wipers_requested_to_start.begin()->second.size();
      if (cp_info.request_to_start_wiper && requested_time_to_start_wipers > cp_info.restart_time) {
#if DEBUG_START_WIPERS
        msg_print("At timestep %d request to start %d wipers at timestep %d", cp_info.time, n_wipers_to_start, requested_time_to_start_wipers);
#endif
        if (register_async_event_request(EVENT_ID_START_WIPER,
                                         n_wipers_to_start,
                                         requested_time_to_start_wipers, 
                                         FALSE, 
                                         FALSE))
          cp_info.request_to_start_wiper = FALSE;
      }


      windows_output<ROTDYN_OUTPUT_QUEUE>(wallclock_time_secs(), FALSE);
      windows_output<FLOW_OUTPUT_QUEUE>(wallclock_time_secs(), FALSE);
      windows_output<COND_OUTPUT_QUEUE>(wallclock_time_secs(), FALSE);

      // remove and process an entry from post recvs queue
      CP_MEAS_WINDOW window = cp_info.post_recvs_queue->remove_entry();
      if (window) {
        cp_info.post_recvs_queue->process(window, wallclock_time_secs());
      }
      cp_process_sp_errors(time_secs);

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
//      if(cp_info.is_particle_solver)
      if(has_trajectory_window() && !sim_args.disable_particle_modeling)
        maybe_receive_sp_trajectory_data();
//#endif

#if SURF_COUP
      maybe_read_coupling_models();
#endif

      maybe_read_tables();
      send_transient_seed_data();
      check_status_of_licenses(last_heartbeat_time, time_secs);
      process_checkpoints();
      if(total_sps <= cp_info.nosleep_spcount)
        cp_sleep();
    } // End of main loop

    cp_process_sp_errors(wallclock_time_secs());

    // Process any pending checkpoint requests
    handle_pending_async_event_request();

    // Tell all the sps that the simulation is over
    cp_signal_workers_sim_finished();


    // The CP has to contribute something to the MPI_Reduce, by definition. For most purposes
    // this can be zero, but that breaks when the op is MIN. The constant below
    // is a year in nanoseconds, which probably will not be the minimum of any real simulation.
#define PRETTY_LONG_LONG (1000L * 1000L * 1000L * 3600L * 24L * 365L)

#if 1
    if(sim_args.run_options & (SIM_REPORT_THREADTIME | SIM_REPORT_THREADTIME_VERBOSE)) {
      asINT64 dummy = 0;
      //asINT64 dummy_min = PRETTY_LONG_LONG;
      asINT64 dummy_min = LONG_MAX;
      asINT64 comm_thread_sum;
      asINT64 main_thread_sum;
      asINT64 sp_sum;

      typedef struct sVAL_RANK {
        dFLOAT m_val;
        int m_rank;
      } *VAL_RANK;

      sVAL_RANK dummy_vrank;
 
      dummy_vrank.m_rank = eMPI_sp_cp_rank();
      sVAL_RANK  comm_thread_max_vrank;
      sVAL_RANK  comm_thread_min_vrank;
      sVAL_RANK  main_thread_max_vrank;
      sVAL_RANK  main_thread_min_vrank;
      sVAL_RANK  sp_max_vrank;
      sVAL_RANK  sp_min_vrank;


      MPI_Reduce(&dummy, &comm_thread_sum, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      dummy_vrank.m_val = 0; 
      MPI_Reduce(&dummy_vrank, &comm_thread_max_vrank, 1, MPI_DOUBLE_INT, MPI_MAXLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 
      dummy_vrank.m_val = PRETTY_LONG_LONG;
      MPI_Reduce(&dummy_vrank, &comm_thread_min_vrank, 1, MPI_DOUBLE_INT, MPI_MINLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 

      MPI_Reduce(&dummy, &main_thread_sum, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      dummy_vrank.m_val = 0; 
      MPI_Reduce(&dummy_vrank, &main_thread_max_vrank, 1, MPI_DOUBLE_INT, MPI_MAXLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 
      dummy_vrank.m_val = PRETTY_LONG_LONG;
      MPI_Reduce(&dummy_vrank, &main_thread_min_vrank, 1, MPI_DOUBLE_INT, MPI_MINLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 
 
      MPI_Reduce(&dummy, &sp_sum, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      dummy_vrank.m_val = 0; 
      MPI_Reduce(&dummy_vrank, &sp_max_vrank, 1, MPI_DOUBLE_INT, MPI_MAXLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 
      dummy_vrank.m_val = PRETTY_LONG_LONG;
      MPI_Reduce(&dummy_vrank, &sp_min_vrank, 1, MPI_DOUBLE_INT, MPI_MINLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 


#if 0
      MPI_Reduce(&dummy, &comm_thread_sum, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&dummy_min, &comm_thread_min, 1, MPI_LONG, MPI_MIN, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&dummy, &comm_thread_max, 1, MPI_LONG, MPI_MAX, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&dummy, &all_sim_threads_sum, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&dummy_min, &all_sim_threads_min, 1, MPI_LONG, MPI_MIN, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&dummy, &all_sim_threads_max, 1, MPI_LONG, MPI_MAX, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&dummy, &sp_sum, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&dummy_min, &sp_min, 1, MPI_LONG, MPI_MIN, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&dummy, &sp_max, 1, MPI_LONG, MPI_MAX, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
#endif

      dFLOAT comm_thread_sum_cputime = (dFLOAT)comm_thread_sum / (dFLOAT)**********;
      dFLOAT comm_thread_min_cputime = (dFLOAT)comm_thread_min_vrank.m_val / (dFLOAT)**********;
      dFLOAT comm_thread_max_cputime = (dFLOAT)comm_thread_max_vrank.m_val / (dFLOAT)**********;
      dFLOAT main_thread_sum_cputime = (dFLOAT)main_thread_sum / (dFLOAT)**********;
      dFLOAT main_thread_min_cputime = (dFLOAT)main_thread_min_vrank.m_val / (dFLOAT)**********;
      dFLOAT main_thread_max_cputime = (dFLOAT)main_thread_max_vrank.m_val / (dFLOAT)**********;
      dFLOAT sp_sum_cputime = (dFLOAT)sp_sum / (dFLOAT)**********;
      dFLOAT sp_min_cputime = (dFLOAT)sp_min_vrank.m_val / (dFLOAT)**********;
      dFLOAT sp_max_cputime = (dFLOAT)sp_max_vrank.m_val  / (dFLOAT)**********;      

      msg_print("SP thread statistics");
      msg_print("--------------------");
#if 0
      msg_print("");
      msg_print("Comm thread:");
      msg_print("  Min:  %g seconds SP%d",comm_thread_min_cputime,comm_thread_min_vrank.m_rank);
      msg_print("  Max:  %g seconds SP%d",comm_thread_max_cputime,comm_thread_max_vrank.m_rank);
      msg_print("  Mean:  %g seconds",comm_thread_sum_cputime / num_sps);
      msg_print("  Sum:  %g seconds",comm_thread_sum_cputime);
#endif      
      msg_print("");
      msg_print("All sim threads:");
      msg_print("  Min:  %g seconds SP%d",main_thread_min_cputime,main_thread_min_vrank.m_rank);
      msg_print("  Max:  %g seconds SP%d",main_thread_max_cputime,main_thread_max_vrank.m_rank);
      //msg_print("  Mean:  %g seconds",main_thread_sum_cputime / num_sps);
      //msg_print("  Sum:  %g seconds",main_thread_sum_cputime);

      msg_print("");
      msg_print("SP total:");
      msg_print("  Min:  %g seconds SP%d",sp_min_cputime,sp_min_vrank.m_rank);
      msg_print("  Max:  %g seconds SP%d",sp_max_cputime,sp_max_vrank.m_rank);
      msg_print("  Mean:  %g seconds",sp_sum_cputime / num_sps);
      msg_print("  Sum:  %g seconds",sp_sum_cputime);
    }
#endif

    if (sim_args.run_options & SIM_REPORT_TOTAL_MEMORY) 
      msg_print ("Total dynamically allocated bytes: %d", total_malloced_bytes ());

    if ( cp_info.is_particle_solver) {
      sPARTICLE_VAR zero = 0;
      sPARTICLE_VAR total_emitted_particle_mass = 0;
      sPARTICLE_VAR total_evaporated_particle_mass = 0;
      int total_evaporated_particles = 0;
      sPARTICLE_VAR total_lost_particle_mass = 0;
      MPI_Reduce(&zero, &total_emitted_particle_mass, 1, MPI_DOUBLE, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&zero, &total_lost_particle_mass, 1, MPI_DOUBLE, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      if(total_lost_particle_mass >= 0) { //Only print the lost mass data if the option was enabled on the SPs via an init file (the SPs sends negative values if not enabled).
        msg_print("\nTotal emitted particle mass is %g(kg).", total_emitted_particle_mass);
        msg_print("Total lost particle mass is %g(kg).", total_lost_particle_mass);
        msg_print("Fraction of lost mass is %g.\n", total_lost_particle_mass / total_emitted_particle_mass);
      }

      if(cp_info.is_thermal_particle_solver) {
        MPI_Reduce(&zero, &total_evaporated_particle_mass, 1, MPI_DOUBLE, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
        MPI_Reduce(&zero, &total_evaporated_particles, 1, MPI_INT, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
        msg_print("Total evaporated particle mass is %g(kg).", total_evaporated_particle_mass);
        msg_print("Total evaporated particles %d.", total_evaporated_particles);
      }

      // Total number of surfels
      long int total_stencil_bytes = 0;
      MPI_Reduce(&zero, &total_stencil_bytes, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      if (total_stencil_bytes >= 0) {
        msg_print("Total storage used for film solver stencils: %g (MBytes).", total_stencil_bytes / (double)(2<<19));
      }

    }
    // Done !

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    if ( cp_info.is_particle_solver) {
      cp_particle_sim.uninitialize();
    }
//#endif
    
    free_heat_exchanger_and_condenser_storage();
#if SURF_COUP
    close_coupling_model_summary_files();
#endif
    if (!sim_args.suppress_final_status_p) {
      print_final_status();
    }
  }

#if CPU_TIME_CP_THREADS
  struct timespec main_thread_stop_timespec;
  clock_gettime(CLOCK_THREAD_CPUTIME_ID, &main_thread_stop_timespec); // Stop the main-thread timer
  asINT64 main_thread_start_ns = main_thread_start_timespec.tv_nsec + main_thread_start_timespec.tv_sec * **********;
  asINT64 main_thread_stop_ns = main_thread_stop_timespec.tv_nsec + main_thread_stop_timespec.tv_sec * **********;
  asINT64 main_thread_ns =  main_thread_stop_ns -  main_thread_start_ns;
#endif


#if CPU_TIME_CP_THREADS
  struct timespec sp_stop_timespec;
  clock_gettime(CLOCK_PROCESS_CPUTIME_ID, &sp_stop_timespec); // Stop the both-threads timer
  asINT64 sp_start_ns = sp_start_timespec.tv_nsec + sp_start_timespec.tv_sec * **********;
  asINT64 sp_stop_ns = sp_stop_timespec.tv_nsec + sp_stop_timespec.tv_sec * **********;
  asINT64 sp_ns = sp_stop_ns - sp_start_ns;
  dFLOAT sp_cputime = (dFLOAT)sp_ns / ((dFLOAT)**********);
  dFLOAT main_thread_cputime = (dFLOAT)main_thread_ns / ((dFLOAT)**********);
  dFLOAT write_sri_results_thread_cputime = (dFLOAT)g_write_sri_results_thread_ns / ((dFLOAT)**********);
  msg_print("");
  msg_print("Write SRI results thread simulation time:  %g seconds",  write_sri_results_thread_cputime);
  msg_print("Main thread simulation time             :  %g seconds",  main_thread_cputime);
  msg_print("Both threads simulation time            :  %g seconds",  sp_cputime);
  msg_print("");
#endif

  close_all_meas_windows(); // close before MPI_Finalize, which has been known to hang
#if ! SEPARATE_LICENSE_PROCESS
  terminate_licenses();
#else
  // Positive request that license liaison release any held licenses
  proc_license_release();
#endif
  finalize();

  return(cp_info.exit_event_id == EVENT_ID_HALT ? exit_failure_code() : EXIT_SUCCESS);
}

int main(int argc, cSTRING argv[])
{
  exa_malloc_set_error_handler(xnew_malloc_error_handler);

  int result_error_code = EXIT_SUCCESS;
  try {
    result_error_code = main_internal(argc, argv);
  }
  catch (std::bad_alloc& ba) { // bad_alloc is a class
    fprintf(stderr, "Error: memory allocation failure: %s\n", ba.what());
    fflush(stderr);

    // Try to save the error_str into a hidden file, but don't worry if it fails. */
    FILE *hidden_file = fopen(".exa_error", "w");
    if (hidden_file != NULL) {
      fprintf(hidden_file, "Error: memory allocation failure: %s\n", ba.what());
      fprint_memory_limits(hidden_file);
      fflush(hidden_file);
      fclose(hidden_file);

      err_exit();
      return 1; // exit with error code (in case err_exit doesn't do its job)
    }
  }
  return result_error_code;
}
