SOURCE_ARCHIVABLE

GROUP_TARGET_USER exa_sim exa_sim_comm exa_sim_rlmcomm 
GROUP_SOURCE_SWDEV master.mak

RELEASE_TARGET_SAVE Makefile exa_sim_comm exa_answer mpipreexec exa_sim_mpich_gm_wrapper exa_sim* .exa_defines.h .vmake_status dplace_exa_sim cp_simsizes_lib.a

RELEASE_TARGET_REMOVE exa_sim*.*

RELEASE_SOURCE_REMOVE *~ .git TAGS .tags .tags_sorted_by_file .gitignore *.orig .*.swp

#ARCHIVE_SOURCE_REMOVE amd64_linux_crayxe6/* amd64_linux_impi/* amd64_linux_lam/*
#ARCHIVE_TARGET_SAVE *

# Tests live in this component
EXATEST_INFO Simulator smoketests
EXATEST_DISTRIBUTIONS powerflow
EXATEST_TESTCOMP physics_coll_tests/basicPhysics/smokeTests/2dUbend
