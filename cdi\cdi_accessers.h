/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */ 
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */ 

#ifndef _CDI_ACCESSERS_H
#define _CDI_ACCESSERS_H

CIO_CCCC	cdi_get_type(CDI_INFO cdi_info);
const char 	*cdi_get_filename(CDI_INFO cdi_info);
cdiINT32	cdi_get_major_version(CDI_INFO cdi_info);
cdiINT32	cdi_get_minor_version(CDI_INFO cdi_info);
const char 	*cdi_get_error_state_string(cdiINT32 errval);

#endif /* !_CDI_ACCESSERS_H */

