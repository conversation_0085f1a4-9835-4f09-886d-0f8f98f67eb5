/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 *
 * Write MME checkpoint files
 *
 *--------------------------------------------------------------------------*/

#include LGI_H
#include SRI_H

#include "common.h"
#include "mme_ckpt.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "window_results.h"
#include "checkpoint_control.h"
//// Globals used throughout this file

static SRI_VARIABLE_TYPE ckpt_var_types[DGF_MAX_TOTAL_MME_CKPT_VARS];
static SRI_VARIABLE_TYPE avg_ckpt_var_types[DGF_MAX_TOTAL_MME_CKPT_VARS];

static SRI_VARIABLE_TYPE cond_ckpt_var_types[DGF_MAX_MME_CKPT_VARS];
static SRI_VARIABLE_TYPE cond_avg_ckpt_var_types[DGF_MAX_MME_CKPT_VARS];

static asINT32           n_ckpt_vars = 0;
static asINT32           n_cond_ckpt_vars = 0;
static asINT32           n_avg_ckpt_vars = 0; // For avg mme ckpt
static asINT32           n_cond_avg_ckpt_vars = 0; // For avg mme ckpt

static SRI_FLUID_FILE sri_file;
static LGI_STREAM     *sp_streams;

//// Functions

static SRI_STATUS open_mme_ckpt_file(cSTRING filename, 
				     BOOLEAN try_until_success_p, BOOLEAN is_avg_mme,
                                     REALM realm)
{
  sSRI_FLUID_FILE_PARAMS sri_file_params;

  sri_file_params.are_special_fluid_volumes_present = FALSE;
  sri_file_params.n_nw_meas_cells = 0;
  sri_file_params.n_nw_norm_meas_cells = cp_info.total_nw_fluid_like_voxels_in_each_realm[realm];
  sri_file_params.n_nw_facet_sides = 0;
  sri_file_params.n_nw_neighbors = 0;

  sri_file_params.n_params = cp_info.sri_params.size();
  sri_file_params.params = &cp_info.sri_params[0];
  sri_file_params.n_translations = cp_info.n_sri_translations;
  sri_file_params.translations = cp_info.sri_translations;
  sri_file_params.n_vectors = 0;
  sri_file_params.vectors = NULL;
  sri_file_params.n_assemblies = cp_info.n_sri_assemblies;
  sri_file_params.assemblies = cp_info.sri_assemblies;
  sri_file_params.n_parts = cp_info.n_sri_parts;
  sri_file_params.parts = cp_info.sri_parts;
  sri_file_params.n_faces = cp_info.n_sri_faces;
  sri_file_params.faces = cp_info.sri_faces;
  sri_file_params.n_csys = cp_info.sri_n_csys;
  sri_file_params.all_csys = cp_info.sri_all_csys;
  sri_file_params.sim_ht_type = cp_info.sim_ht_type;
  sri_file_params.is_sampling_meas_type = FALSE; 
  sri_file_params.is_lattice_temperature_constant = cp_info.is_lattice_temperature_constant;
  if (sri_file_params.is_lattice_temperature_constant)
    sri_file_params.constant_lattice_temperature_value = cp_info.constant_lattice_temperature_value;
  else
    sri_file_params.constant_lattice_temperature_value = 0.0;

  sri_file_params.is_density_constant = cp_info.is_density_constant;
  if (sri_file_params.is_density_constant)
    sri_file_params.constant_density_value = cdi_data.char_density;
  else
    sri_file_params.constant_density_value = 0.0;

  sri_file_params.audit_trail = (STRING)audit_universal_rep(cp_info.audit_trail);
  sri_file_params.n_dims = cp_info.n_dims;
  sri_file_params.n_points = cp_info.total_fluid_like_voxels_in_each_realm[realm];
  
  // parentage record
  sri_file_params.ptge_rec.case_file_id = cp_info.ptge.case_file_id;
  sri_file_params.ptge_rec.case_geometry_id = cp_info.ptge.case_geometry_id;
  sri_file_params.ptge_rec.cdi_file_id = cp_info.ptge.cdi_file_id;
  sri_file_params.ptge_rec.lgi_file_id = cp_info.ptge.lgi_file_id;
  sri_file_params.ptge_rec.decomp_lgi_file_id = cp_info.ptge.decomp_lgi_file_id;
  sri_file_params.ptge_rec.decomp_nprocs = cp_info.ptge.decomp_n_procs;
  sri_file_params.ptge_rec.solver_version = strtol(cp_info.solver_version, NULL, 10);
  try {
    sri_file_params.ptge_rec.cp_version = std::stoi(CP_VERSION);
    if(sri_file_params.ptge_rec.cp_version == 0)
      throw std::out_of_range("Zero is not a valid CP version.");
  }
  catch (...) {
    msg_internal_error("Could not determine the CP version from string %s.",CP_VERSION);
  }

  sri_file_params.is_std_meas_mme = cp_info.is_std_meas_mme_p;

  if (is_avg_mme)
    sri_file_params.m_enable_compression = cp_info.is_avg_mme_ckpt_compressed;

  ccDOTIMES(i, 3) {
    sri_file_params.min_bound[i] = 0;
    sri_file_params.max_bound[i] = cp_info.simvol_size[i];
  }

  if (cp_info.is_5g_sim || cp_info.is_uds_transport)  
    sri_file_params.custom_var_id_helper = cp_info.cvid_helper;

  sri_file_params.n_scales = cp_info.num_scales;
  sri_file_params.meas_win_scale = sim_scale_to_sri_scale(cp_info.finest_scale());
  sri_file_params.single_voxel_meas_p = TRUE;
  if (realm == STP_FLOW_REALM){
    sri_file_params.n_variables = is_avg_mme ? n_avg_ckpt_vars : n_ckpt_vars;
    sri_file_params.is_solid_file = FALSE;
  } else if (realm == STP_COND_REALM){
    sri_file_params.n_variables = is_avg_mme ? n_cond_avg_ckpt_vars : n_cond_ckpt_vars;
    sri_file_params.is_solid_file = TRUE;
  }
  sri_file_params.variables = cnew sSRI_VARIABLE_DESC [sri_file_params.n_variables];
  SRI_VARIABLE_DESC var_desc = sri_file_params.variables;
  ccDOTIMES(var, sri_file_params.n_variables) {
    if (realm == STP_FLOW_REALM)
      var_desc->var_type = is_avg_mme ? avg_ckpt_var_types[var] : ckpt_var_types[var];
    else if (realm == STP_COND_REALM)
      var_desc->var_type = is_avg_mme ? cond_avg_ckpt_var_types[var] : cond_ckpt_var_types[var];
    if (var_desc->var_type < SRI_VARIABLE_FIRST_NON_PREDEFINED) {
      var_desc->tiny_name = sri_variable_type_to_tiny_name(var_desc->var_type);
      var_desc->short_name = sri_variable_type_to_short_name(var_desc->var_type);
      var_desc->long_name = sri_variable_type_to_long_name(var_desc->var_type);
      var_desc->unit_class_name = sri_variable_unit_class_name(var_desc->var_type);
      var_desc->lattice_unit_name = sri_variable_lattice_unit_name(var_desc->var_type);
    } else if (cp_info.is_5g_sim && cp_info.cvid_helper->is_5g_var_id(var_desc->var_type)) { //5G UDS use water_vapor_mfrac now
      get_5g_var_desc(var_desc->var_type,
		      cp_info.cvid_helper,
		      var_desc);
    } else if (cp_info.is_uds_transport && cp_info.cvid_helper->is_uds_var_id(var_desc->var_type)) {
      get_uds_var_desc(var_desc->var_type,
		       cp_info.cvid_helper,
		       var_desc);
    }

    var_desc++;
  }

  sri_file_params.n_fluid_components = cp_info.num_fluid_components;
  sri_file_params.fluid_components = cp_info.s_fluid_components_5g;

  if (cp_info.is_uds_transport && !cp_info.is_5g_sim) {  //5G UDS does not have PowerCASE support 
    sri_file_params.n_scalars = cp_info.n_scalars;    
    sri_file_params.scalars = cp_info.s_scalar_materials;
  }

  initialize_sri_file_params_grf_info(&sri_file_params, FALSE);

  sri_file_params.n_lrfs = cp_info.n_lrfs();
  sri_file_params.lrfs = &cp_info.sri_lrfs[0];

  ccDOTIMES(i, cp_info.n_lrfs())
    cp_info.sri_lrfs[i].n_points = cp_info.lrf_n_fluid_like_voxels[i];
  sri_file_params.is_output_in_local_csys = true;

  sri_file_params.n_moving_face_xforms = cp_info.n_movb_xforms();
  sri_file_params.n_moving_faces = cp_info.n_movbs();
  if (sri_file_params.n_moving_faces > 0) {
    sri_file_params.moving_faces = cp_info.sri_movbs.data();
    sri_file_params.moving_face_xforms = cp_info.sri_movb_xforms.data();
  }

  sri_file_params.encryption_struct = cp_info.encryption_struct.get_sri_encryption_struct();

  SRI_FILE file;
  SRI_STATUS status = sri_open_file_for_write(&sri_file_params, filename, 
                                              try_until_success_p, FALSE, SRI_FLUID_TYPE, &file);
  sri_file = (SRI_FLUID_FILE)file;

  if (status == SRI_SUCCESS)
    status = write_sri_meas_file_polyline_vertices(sri_file);

  return status;
}


/* Returns SRI_SUCCESS on success; otherwise an error status. */
static SRI_STATUS write_mme_ckpt_ref_frame_state(SRI_STATUS status, BOOLEAN try_until_success_p, BOOLEAN is_avg_mme, int sp_start)
{
  LGI_TAG tag;
  lgi_read_next_head(sp_streams[sp_start], tag);
  LGI_TAG_ID ref_frame_tag_id = is_avg_mme ? LGI_AVG_MME_CKPT_REF_FRAME_DATA_TAG : LGI_MME_CKPT_REF_FRAME_DATA_TAG;
  if (tag.id != ref_frame_tag_id)
    msg_internal_error("Expected LGI tag %d, but received LGI tag %d from SP %d"
                       " during checkpoint generation.",
                       ref_frame_tag_id, tag.id, sp_start);

  if (cp_info.is_global_ref_frame_time_varying) {
    sGRF_MEAS_FRAME_SP_TO_CP_MSG grf_info;
    lgi_read(sp_streams[sp_start], &grf_info, sizeof(grf_info));
    if (status == SRI_SUCCESS)
      status = write_sri_meas_file_grf_info(sri_file, &grf_info);
  }

  sSRI_LRF *sri_lrf = &cp_info.sri_lrfs[0];
  ccDOTIMES(i, cp_info.n_lrfs()) {
    if (!sri_lrf->has_constant_angular_vel) {
      sLRF_MEAS_FRAME_SP_TO_CP_MSG lrf_info;
      lgi_read(sp_streams[sp_start], &lrf_info, sizeof(lrf_info));
      if (status == SRI_SUCCESS)
        status = sri_file->write_ref_frame_angular_rotation(lrf_info.angle_rotated, i, try_until_success_p);
      if (status == SRI_SUCCESS)
        status = sri_file->write_ref_frame_n_revolutions(lrf_info.n_revolutions, i, try_until_success_p);
      if (status == SRI_SUCCESS)
        status = sri_file->write_lrf_angular_vel_mag(lrf_info.angular_vel_mag, i, try_until_success_p); 
    }
    sri_lrf++;
  }

  return status;
}

// update the moving face descriptor with the latest information and write to file
static SRI_STATUS write_mme_ckpt_movb_state(SRI_STATUS status, BOOLEAN try_until_success_p, BOOLEAN is_avg_mme, int sp_start)
{
  LGI_TAG tag;
  lgi_read_next_head(sp_streams[sp_start], tag);
  LGI_TAG_ID movb_data_tag_id = is_avg_mme ? LGI_AVG_MME_CKPT_MOVB_DATA_TAG : LGI_MME_CKPT_MOVB_DATA_TAG;
  if (tag.id != movb_data_tag_id)
    msg_internal_error("Expected LGI tag %d, but received LGI tag %d from SP %d"
                       " during checkpoint generation.",
                       movb_data_tag_id, tag.id, sp_start);

  ccDOTIMES(i, cp_info.n_movb_xforms()) {
    sMOVB_MEAS_FRAME_SP_TO_CP_MSG movb_info;
    lgi_read(sp_streams[sp_start], &movb_info, sizeof(movb_info));
    sSRI_XFORM sri_xform;
    sri_xform = movb_info.xform;

    if (status == SRI_SUCCESS)
      status = sri_file->write_moving_face_xform(&sri_xform, i, try_until_success_p);
  }

  return status;
}

// Iterates over the ublks that are being sent from the SPs
template<int _N_VOXELS> class tMME_CKPT_UBLKS;

// This specialization reads the ublks in ID order. This places data in the file
// in an optimal octree order, which improves memory usage during seeding from the
// resulting file
template<>
class tMME_CKPT_UBLKS<8>
{
public:

  tMME_CKPT_UBLKS(STP_SHOB_ID n_ublks, const STP_PROC * ublk_procs, STP_PROC num_sps) : m_n_ublks(n_ublks), m_ublk_procs(ublk_procs), m_num_sps(num_sps) {}

  class cITERATOR
  {
  public:
    // ublk_proc is allowed to be null, in the 1 SP case
    cITERATOR(STP_SHOB_ID ublk_id, const STP_PROC * ublk_proc, STP_PROC num_sps) : m_ublk_id(ublk_id), m_ublk_proc(ublk_proc), m_multiple_sps(num_sps > 1) {}

    bool operator == (const cITERATOR& other) {
      return m_ublk_id == other.m_ublk_id;
    }

    bool operator !=(const cITERATOR& other) {
      return !this->operator==(other);
    }

    cITERATOR& operator++ () {
      ++m_ublk_id;
      return *this;
    }

    LGI_STREAM operator * () {
      STP_PROC sp = m_multiple_sps ? m_ublk_proc[m_ublk_id] : 0;
      return sp_streams[sp];
    }

    STP_SHOB_ID n_ublks_processed() const { return m_ublk_id; }

  private:
    STP_SHOB_ID m_ublk_id;
    STP_PROC const * const m_ublk_proc;
    const bool m_multiple_sps;
  };

  cITERATOR begin() { return cITERATOR(0, m_ublk_procs, m_num_sps); }
  cITERATOR end() { return cITERATOR(m_n_ublks, m_ublk_procs, m_num_sps); }

  STP_SHOB_ID n_total_ublks() const { return m_n_ublks; }

private:

  const STP_SHOB_ID m_n_ublks;
  STP_PROC const * const m_ublk_procs;
  STP_PROC m_num_sps;

};

// This specialization reads the mblks from each SP.
// Since mblks are not necessarily in octree order, we just
// pull all the mblks from each SP in sequence. This results in a less optimal
// storage in the fnc file, but that has been deemed acceptable
template<>
class tMME_CKPT_UBLKS<64>
{
public:

  tMME_CKPT_UBLKS([[maybe_unused]] STP_SHOB_ID n_ublks, 
                  [[maybe_unused]] const STP_PROC * ublk_procs, 
                  STP_PROC num_sps) : m_total_mblks(0), m_num_mblks(num_sps)
  {
    ccDOTIMES(sp, num_sps) {
      m_num_mblks[sp].read(sp_streams[sp]);
      m_total_mblks += m_num_mblks[sp].num_ublks;
    }
  }

  class cITERATOR
  {
  public:
    cITERATOR(const std::vector<cDGF_MME_NUM_UBLKS>& num_mblks, STP_SHOB_ID mblk_id) : m_num_mblks(num_mblks), m_mblk_id(mblk_id), m_sp_mblk_id(0), m_sp(0) { }

    bool operator == (const cITERATOR& other) const 
    {
      return m_mblk_id == other.m_mblk_id;
    }

    bool operator != (const cITERATOR& other) const 
    {
      return !this->operator==(other);
    }

    cITERATOR& operator++ () noexcept 
    {
      ++m_mblk_id;
      ++m_sp_mblk_id;
      if ( m_sp_mblk_id == m_num_mblks[m_sp].num_ublks ) {
        m_sp_mblk_id = 0;
        ++m_sp;
      }
      return *this;
    }

    LGI_STREAM operator * () const 
    {
      return sp_streams[m_sp];
    }

    STP_SHOB_ID n_ublks_processed() const 
    {
      return m_mblk_id;
    }

  private:
    const std::vector<cDGF_MME_NUM_UBLKS>& m_num_mblks;
    STP_SHOB_ID m_mblk_id;
    STP_SHOB_ID m_sp_mblk_id;
    STP_PROC m_sp;
  };

  cITERATOR begin() const { return cITERATOR(m_num_mblks,0); }
  cITERATOR end() const { return cITERATOR(m_num_mblks, m_total_mblks); }

  STP_SHOB_ID n_total_ublks() const { return m_total_mblks; }

private:

  std::vector<cDGF_MME_NUM_UBLKS> m_num_mblks;
  STP_SHOB_ID m_total_mblks;

};

/* Returns SRI_SUCCESS on success; otherwise an error status. */
template<int _N_VOXELS>
static SRI_STATUS write_mme_ckpt_voxel_data(SRI_STATUS status, BOOLEAN try_until_success_p, cSTRING cp_status, BOOLEAN is_avg_mme, 
                                            REALM realm, sriPOINT *n_voxels_written, sriPOINT *n_nw_voxels_written,
                                            int sp_start, int total_sps_per_solver)
{
  for (int sp = sp_start; sp < sp_start + total_sps_per_solver; sp++ ) {
    LGI_TAG tag;
    lgi_read_next_head(sp_streams[sp], tag);
    LGI_TAG_ID voxel_data_tag_id = is_avg_mme ? LGI_AVG_MME_CKPT_VOXEL_DATA_TAG : LGI_MME_CKPT_VOXEL_DATA_TAG;
    if (tag.id != voxel_data_tag_id)
      msg_internal_error("MME checkpoint: expected LGI tag %d, but received LGI tag %d from SP %d",
                         voxel_data_tag_id, tag.id, sp); //COND_MME_CHECK: Change SP to CSP or FSP 
  }

  static constexpr int N_UBLKS = _N_VOXELS/8;
  using cDGF_MME_CKPT_UBLK_VOXEL_MASK = tDGF_MME_CKPT_UBLK_VOXEL_MASK<_N_VOXELS>;
  using cDGF_MME_CKPT_UBLK_HEADER = tDGF_MME_CKPT_UBLK_HEADER<_N_VOXELS>;
  using cDGF_MME_CKPT_SURFACE_NORMALS = tDGF_MME_CKPT_SURFACE_NORMALS<_N_VOXELS>;
  using cDGF_MME_CKPT_MEAS_VAR = tDGF_MME_CKPT_MEAS_VAR<_N_VOXELS>;
  using cMME_CKPT_UBLKS = tMME_CKPT_UBLKS<_N_VOXELS>;

  const int BUF_SIZE = 1000 * 1000;
  auto voxel_scales     = std::make_unique<sriBYTE[]>(BUF_SIZE);
  auto voxel_volumes    = std::make_unique<sriFLOAT[]>(BUF_SIZE);
  auto voxel_ref_frames = std::make_unique<sriLRF_INDEX[]>(BUF_SIZE);
  auto voxel_coords     = std::make_unique<sINT32[]>(3*BUF_SIZE);

  std::array<std::unique_ptr<sriFLOAT[]>, DGF_MAX_TOTAL_MME_CKPT_VARS> voxel_meas_variables;
  int var_count = (cp_info.is_uds_transport && !cp_info.is_5g_sim) ? DGF_MAX_TOTAL_MME_CKPT_VARS : DGF_MAX_MME_CKPT_VARS;  //why not using n_vars?
  for(int i=0;i<var_count; ++i) {
    voxel_meas_variables[i] = std::make_unique<sriFLOAT[]>(BUF_SIZE);
  }

  //sriPOINT     n_voxels_written[STP_N_REALMS];
  //sriPOINT     n_nw_voxels_written[STP_N_REALMS];
    
  sriPOINT     n_voxels = 0;
  n_voxels_written[realm] = 0;
  sriPOINT     coord_index = 0;

  //near wall voxels
  auto nw_voxel_indices   = std::make_unique<sriINT[]>(BUF_SIZE);
  auto nw_surface_normals = std::make_unique<sriFLOAT[]>(3*BUF_SIZE);

  sriPOINT     n_nw_voxels = 0;
  n_nw_voxels_written[realm] = 0;
  sriPOINT     nw_normal_index = 0;

  STP_SHOB_ID n_ublks = cp_info.num_ublks[realm];
  STP_PROC *ublk_procs = &cp_info.ublk_procs[realm][0];
  BOOLEAN multiple_sps = total_sps > 1;
  BOOLEAN some_lrfs = cp_info.n_lrfs() > 0;
  BOOLEAN is_2d = cp_info.n_dims == 2;
  asINT32 n_vars;
  if (realm == STP_FLOW_REALM)
    n_vars = is_avg_mme ? n_avg_ckpt_vars : n_ckpt_vars;
  else if (realm == STP_COND_REALM)
    n_vars = is_avg_mme ? n_cond_avg_ckpt_vars : n_cond_ckpt_vars;
  
  cMME_CKPT_UBLKS mme_ckpt_ublks(n_ublks, ublk_procs, total_sps);
  const auto end = mme_ckpt_ublks.end();
  dFLOAT one_over_n_ublks = 1.0 / mme_ckpt_ublks.n_total_ublks();
  asINT32 percent_ublks_processed = 0;
 
  for (auto it = mme_ckpt_ublks.begin(); it != end; ++it) {
    LGI_STREAM sp_stream = *it;
    cDGF_MME_CKPT_UBLK_VOXEL_MASK mask;
    lgi_read(sp_stream, mask);

    if (mask.fluid_like_voxel_mask == 0) {
      continue;
    }

    cDGF_MME_CKPT_UBLK_HEADER header;
    lgi_read(sp_stream, header);

    uINT8 voxel_scale = sim_scale_to_sri_scale(header.ublk_scale);

    sriPOINT n_voxels_before_ublk = n_voxels;
    sriPOINT n_nw_voxels_before_ublk = n_nw_voxels;

    ccDOTIMES(voxel, _N_VOXELS) {
      if (is_voxel_in_mask(voxel, mask.fluid_like_voxel_mask)) {
        voxel_scales[n_voxels] = voxel_scale;

        if (some_lrfs)
          voxel_ref_frames[n_voxels] = header.ref_frame_index;

        sINT8 child_ublk = voxel / 8;
        sINT8 ublk_voxel = voxel % 8;

        voxel_coords[coord_index++] = header.ublk_coords[0][child_ublk] + voxel_index_to_x_finest_offset(ublk_voxel, voxel_scale);
        voxel_coords[coord_index++] = header.ublk_coords[1][child_ublk] + voxel_index_to_y_finest_offset(ublk_voxel, voxel_scale);
        if (!is_2d) {
          voxel_coords[coord_index++] = header.ublk_coords[2][child_ublk] + voxel_index_to_z_finest_offset(ublk_voxel, voxel_scale);
        }
        n_voxels++;
      }
    }

    if (header.is_near_surface) {
      asINT32 voxel_index_before_ublk = n_voxels_before_ublk + n_voxels_written[realm];
      asINT32 nw_voxel_index_before_ublk = n_nw_voxels_before_ublk + n_nw_voxels_written[realm];
      cDGF_MME_CKPT_SURFACE_NORMALS surface;
      lgi_read(sp_stream, surface);

      ccDOTIMES(voxel, _N_VOXELS) {
        if (is_voxel_in_mask(voxel, mask.fluid_like_voxel_mask)) {

          asINT32 voxel_index_at = voxel_index_before_ublk++;
          asINT32 nw_voxel_index_at = nw_voxel_index_before_ublk++;

          if (surface.normals[0][voxel] != DGF_MME_CKPT_INVALID_NORMAL) {
            nw_voxel_indices[n_nw_voxels] = voxel_index_at;
            nw_surface_normals[nw_normal_index++] = surface.normals[0][voxel];
            nw_surface_normals[nw_normal_index++] = surface.normals[1][voxel];
            if (!is_2d) {
              nw_surface_normals[nw_normal_index++] = surface.normals[2][voxel];
            }
            n_nw_voxels++;
          }
        }
      }
    }

    ccDOTIMES(i, n_vars) {
      cDGF_MME_CKPT_MEAS_VAR meas;
      lgi_read(sp_stream, meas);
      auto index = n_voxels_before_ublk;
      ccDOTIMES(voxel, _N_VOXELS) {
        if (is_voxel_in_mask(voxel, mask.fluid_like_voxel_mask)) {
          voxel_meas_variables[i][index++] = meas.var[voxel];
        }
      }
    }

    if (n_voxels >= BUF_SIZE - _N_VOXELS) {
      if (status == SRI_SUCCESS)
        status = sri_file->write_voxel_scales(voxel_scales.get(), n_voxels, n_voxels_written[realm], try_until_success_p);
      if (status == SRI_SUCCESS)
        status = sri_file->write_fluid_volumes(voxel_volumes.get(), n_voxels, n_voxels_written[realm], try_until_success_p);
      if (status == SRI_SUCCESS && some_lrfs)
        status = sri_file->write_ref_frame_indices(voxel_ref_frames.get(), n_voxels, n_voxels_written[realm], try_until_success_p);
      if (status == SRI_SUCCESS)
        status = sri_file->write_meas_cell_coords(voxel_coords.get(), n_voxels, n_voxels_written[realm], try_until_success_p);
      if (status == SRI_SUCCESS) {
        ccDOTIMES(i, n_vars) {
          status = sri_file->write_variable(i, voxel_meas_variables[i].get(), n_voxels, n_voxels_written[realm], try_until_success_p);
          if (status != SRI_SUCCESS)
            break;
        }
      }

      n_voxels_written[realm] += n_voxels;
      n_voxels = 0;
      coord_index = 0;
    }

    if (n_nw_voxels >= BUF_SIZE - _N_VOXELS) {
      if (status == SRI_SUCCESS)
        status = sri_file->write_nw_norm_meas_cell_indices(nw_voxel_indices.get(), n_nw_voxels, n_nw_voxels_written[realm], try_until_success_p);
      if (status == SRI_SUCCESS)
        status = sri_file->write_nw_surface_normals(nw_surface_normals.get(), n_nw_voxels, n_nw_voxels_written[realm], try_until_success_p);
      n_nw_voxels_written[realm] += n_nw_voxels;
      n_nw_voxels = 0;
      nw_normal_index = 0;
    }

    asINT32 percent_ublks = (it.n_ublks_processed()+1) * (100.0 * one_over_n_ublks);
    if (percent_ublks > percent_ublks_processed) {
      char status_msg[256];
      percent_ublks_processed = percent_ublks;

      sprintf(status_msg, "%s (Fluid checkpoint: %d%% complete)",
              cp_status, percent_ublks_processed);
      cp_jobctl_output_status(status_msg);
    }
  } // end ublk loop


  // flush anything remaining in the buffer
  if (n_voxels > 0) {
    if (status == SRI_SUCCESS)
      status = sri_file->write_voxel_scales(voxel_scales.get(), n_voxels, n_voxels_written[realm], try_until_success_p);
    if (status == SRI_SUCCESS)
      status = sri_file->write_fluid_volumes(voxel_volumes.get(), n_voxels, n_voxels_written[realm], try_until_success_p);
    if (status == SRI_SUCCESS && some_lrfs)
      status = sri_file->write_ref_frame_indices(voxel_ref_frames.get(), n_voxels, n_voxels_written[realm], try_until_success_p);
    if (status == SRI_SUCCESS)
      status = sri_file->write_meas_cell_coords(voxel_coords.get(), n_voxels, n_voxels_written[realm], try_until_success_p);
    if (status == SRI_SUCCESS) {
      asINT32 n_vars;
      if (realm == STP_FLOW_REALM)
        n_vars = is_avg_mme ? n_avg_ckpt_vars : n_ckpt_vars;
      else if (realm == STP_COND_REALM)
        n_vars = is_avg_mme ? n_cond_avg_ckpt_vars : n_cond_ckpt_vars;
      ccDOTIMES(i, n_vars) {
        status = sri_file->write_variable(i, voxel_meas_variables[i].get(), n_voxels, n_voxels_written[realm], try_until_success_p);
        if (status != SRI_SUCCESS)
          break;
      }
    }

    n_voxels_written[realm] += n_voxels;
  }

  if (n_nw_voxels > 0) {
    if (status == SRI_SUCCESS)
      status = sri_file->write_nw_norm_meas_cell_indices(nw_voxel_indices.get(), n_nw_voxels, n_nw_voxels_written[realm], try_until_success_p);
    if (status == SRI_SUCCESS)
      status = sri_file->write_nw_surface_normals(nw_surface_normals.get(), n_nw_voxels, n_nw_voxels_written[realm], try_until_success_p);

    n_nw_voxels_written[realm] += n_nw_voxels;
  }

  return status;
}

static VOID read_sp_eof(asINT32 sp)
{
  LGI_EOF_REC eof_record;
  lgi_read_next_head(sp_streams[sp], &eof_record, sizeof(eof_record));

  if (eof_record.tag.id != LGI_EOF_TAG) {
    msg_internal_error("While writing checkpoint file, failed to receive EOF"
		       " from SP %d, received tag %d instead",
		       sp, eof_record.tag.id);
  }
}


//NOTE: The CDI function cdi_mstp_vars() duplicates the logic here for finding avg mme ckpt variables. 
//      If we change the code here, the same change should be made in CDI for consistency. 
static VOID compute_mme_ckpt_var_types()
{
  if (n_ckpt_vars > 0) // already computed for prior ckpt
    return;

  BOOLEAN store_frozen_vars = FALSE;
  
  store_frozen_vars = cp_info.local_vel_freeze && !cp_info.is_5g_sim && !cp_info.is_transonic_mach_regime;

  if (cp_info.is_5g_sim) {
    for (asINT32 i=0; i < cp_info.num_fluid_components; i++)
      ckpt_var_types[n_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_DENSITY, i);

    ckpt_var_types[n_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_XVEL);
    ckpt_var_types[n_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_YVEL);
    if (cp_info.n_dims == 3)
      ckpt_var_types[n_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_ZVEL);
  } else {
    if (cp_info.is_density_constant)
      ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_PRESSURE;
    else
      ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_DENSITY;

    ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_XVEL;
    ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_YVEL;
    if (cp_info.n_dims == 3)
      ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_ZVEL;
    if (store_frozen_vars) {
      ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_USTAR;
      ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_DENSITY_FROZEN;
      ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_XVEL_FROZEN;
      ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_YVEL_FROZEN;
      if (cp_info.n_dims == 3)
        ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_ZVEL_FROZEN;
    }
    if (cp_info.is_checkpoint_body_force) {
      ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_XPRESSURE_GRADIENT;
      ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_YPRESSURE_GRADIENT;
      if (cp_info.n_dims == 3)
	ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_ZPRESSURE_GRADIENT;
    }
  }

  if (cp_info.is_turb) {
    ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_TURB_KINETIC_ENERGY;
    ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_TURB_DISSIPATION;
    ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_STRESS_TENSOR_MAG;
  } 

  if (cp_info.is_heat_transfer) {    
    ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_TEMP;
  }   

  if (cp_info.is_uds_transport) {
    if (cp_info.is_5g_sim) {
      ckpt_var_types[n_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFRAC);
    } else {
      for (int nth_uds = 0; nth_uds < cp_info.n_scalars; nth_uds++)
	ckpt_var_types[n_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_uds_var_id(SRI_VARIABLE_UDS_SCALAR_OFFSET, nth_uds);
    }
  } else if (cp_info.is_water_vapor_transport ) {
    ckpt_var_types[n_ckpt_vars++] = SRI_VARIABLE_WATER_VAPOR_MFRAC;  //ckpt only one uds
  }

  if (cp_info.is_uds_transport && !cp_info.is_5g_sim) {
    if (n_ckpt_vars > DGF_MAX_TOTAL_MME_CKPT_VARS)
	msg_internal_error("Max Fluid checkpoint variable types exceeded");
  } else {
    if (n_ckpt_vars > DGF_MAX_MME_CKPT_VARS)
      msg_internal_error("Max Fluid checkpoint variable types exceeded");
  }
}

static VOID compute_cond_mme_ckpt_var_types()
{
  if (n_cond_ckpt_vars > 0) // already computed for prior ckpt
    return;

  if (cp_info.is_conduction) {
    cond_ckpt_var_types[n_cond_ckpt_vars++] = SRI_VARIABLE_TEMP;
  } 

  if (n_cond_ckpt_vars > DGF_MAX_MME_CKPT_VARS) //COND_MME_CHECK conduction max should be 1
    msg_internal_error("Max conduction checkpoint variable types exceeded");
}


static VOID compute_avg_mme_ckpt_var_types()
{
  if (n_avg_ckpt_vars > 0) // already computed for prior ckpt
    return;

  BOOLEAN store_frozen_vars = FALSE;
  
  store_frozen_vars = cp_info.has_average_mme_window && !cp_info.is_5g_sim && !cp_info.is_transonic_mach_regime;

  if (cp_info.is_5g_sim) {
    for (asINT32 i=0; i < cp_info.num_fluid_components; i++)
      avg_ckpt_var_types[n_avg_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_DENSITY, i);

    avg_ckpt_var_types[n_avg_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_XVEL);
    avg_ckpt_var_types[n_avg_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_YVEL);
    if (cp_info.n_dims == 3)
      avg_ckpt_var_types[n_avg_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_ZVEL);
  } else {
    if (cp_info.is_density_constant)
      avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_PRESSURE;
    else
      avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_DENSITY;

    avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_XVEL;
    avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_YVEL;
    if (cp_info.n_dims == 3)
      avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_ZVEL;
    if (store_frozen_vars) {
      avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_USTAR;
      avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_DENSITY_FROZEN;
      avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_XVEL_FROZEN;
      avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_YVEL_FROZEN;
      if (cp_info.n_dims == 3)
        avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_ZVEL_FROZEN;
    }
  }

  if (cp_info.is_turb) {
    avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_TURB_KINETIC_ENERGY;
    avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_TURB_DISSIPATION;
    avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_STRESS_TENSOR_MAG;
  } 

  if (cp_info.is_heat_transfer) {
    avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_TEMP;
  } 

  if (cp_info.is_uds_transport) {
    if (cp_info.is_5g_sim) {
      avg_ckpt_var_types[n_avg_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_5g_var_id(SRI_VARIABLE_WATER_VAPOR_MFRAC);
    } else {
      for (int nth_uds = 0; nth_uds < cp_info.n_scalars; nth_uds++)
	avg_ckpt_var_types[n_avg_ckpt_vars++] = (SRI_VARIABLE_TYPE)cp_info.cvid_helper->get_uds_var_id(SRI_VARIABLE_UDS_SCALAR_OFFSET, nth_uds);
    }
  } else if (cp_info.is_water_vapor_transport ) {
    avg_ckpt_var_types[n_avg_ckpt_vars++] = SRI_VARIABLE_WATER_VAPOR_MFRAC;
  }
  
  if (cp_info.is_uds_transport && !cp_info.is_5g_sim) {
    if (n_avg_ckpt_vars > DGF_MAX_TOTAL_MME_CKPT_VARS)
      msg_internal_error("Max Fluid checkpoint variable types exceeded");
  } else {
    if (n_avg_ckpt_vars > DGF_MAX_MME_CKPT_VARS)
      msg_internal_error("Max Fluid checkpoint variable types exceeded");
  }
}

static VOID compute_cond_avg_mme_ckpt_var_types()
{
  if (n_cond_avg_ckpt_vars > 0) // already computed for prior ckpt
    return;

  if (cp_info.is_conduction) {
    cond_avg_ckpt_var_types[n_cond_avg_ckpt_vars++] = SRI_VARIABLE_TEMP;
  } 

  if (n_cond_avg_ckpt_vars > DGF_MAX_MME_CKPT_VARS)
    msg_internal_error("Max Fluid checkpoint variable types exceeded");
}

static void write_classic_autostop_monitor_mme_ckpt(TIMESTEP ckpt_timestep)
{
  ccDOTIMES(i, cp_info.monitors.size())
  {
    MONITOR monitor = cp_info.monitors[i];
    if (monitor->m_msap.m_useClassicAlgorithm) {
      
      CHARACTER monitor_mme_ckpt_filename[PLATFORM_MAXPATHLEN];
      sprintf(monitor_mme_ckpt_filename, "%s/%s.dat.ckpt", monitors_dir_name, monitor->m_name);
      FILE* monitor_mme_ckpt_file;
      if ((monitor_mme_ckpt_file = fopen(monitor_mme_ckpt_filename, "w")) == NULL) {
        msg_warn("Unable to write fluid ckpt data file for classic autostop monitor \"%s\"", monitor->m_name);
        continue;
      }

      // If the previous run is ended by monitors, those monitors will not end the simulation when resuming
      // from the mme ckpt file.
      fprintf(monitor_mme_ckpt_file, "%d\n", monitor->m_converged_and_ended_sim_before_ckpt);
      // Write the signal ckpt file up to ckpt_timestep
      ccDOTIMES(j, monitor->m_timesteps.size()) {
        if (monitor->m_timesteps[j] > ckpt_timestep)
          break;
        fprintf(monitor_mme_ckpt_file, "%d   %12.9g\n", monitor->m_timesteps[j], monitor->m_msa.Signal(j)); 
      }
      fclose(monitor_mme_ckpt_file);
    }
  }
}




BOOLEAN write_mme_ckpt(cSTRING filename, TIMESTEP ckpt_timestep,
                       BOOLEAN try_until_success_p, cSTRING cp_status, BOOLEAN is_avg_mme, REALM realm, sriPOINT *n_voxels_written, sriPOINT *n_nw_voxels_written)
{
  if (realm == STP_FLOW_REALM){
    if (is_avg_mme)
      compute_avg_mme_ckpt_var_types();
    else
      compute_mme_ckpt_var_types();
  } else if (realm == STP_COND_REALM) {
    if (is_avg_mme)
      compute_cond_avg_mme_ckpt_var_types();
    else
      compute_cond_mme_ckpt_var_types();
  }

  SRI_STATUS status = open_mme_ckpt_file(filename, try_until_success_p, is_avg_mme, realm);

  // If the checkpoint write fails for some reason (say for instance, the disk is
  // full), we must consume all the data from the SPs and proceed.

  // Make sure to open the SP streams before using the CP's notion of time.
  // This almost ensures that the SPs and the CP are synchronized.
  sp_streams = return_sp_stream();

  if (sri_file == NULL) {
    msg_warn("Error while attempting to open checkpoint file \"%s\""
	     " for checkpoint after timestep %ld."
	     " No checkpoint will be written for this timestep.\n"
	     "System error message was: %s",
	     filename,
	     (long)sim_status_timestep(cp_info.cp_status.status) - 1,
	     sri_error_string(status));
  }

  if (status == SRI_SUCCESS) {
    sri_file->set_meas_frame(0);
    TIMESTEP start_timestep = ckpt_timestep - 1;
    if (is_avg_mme)
      start_timestep = ckpt_timestep - cp_info.avg_mme_ckpt_interval;
    status = sri_file->write_meas_frame_time(start_timestep, ckpt_timestep, try_until_success_p);
  }
  
  int total_sps_per_solver;
  int sp_start;
  BOOLEAN close_lgi_stream_now;
  if (realm == STP_COND_REALM){
    if (cp_info.is_flow){
      total_sps_per_solver = total_sps/2;
      sp_start = total_sps_per_solver;
      close_lgi_stream_now = TRUE;
    } else {
      total_sps_per_solver = total_sps;
      sp_start = 0;
      close_lgi_stream_now = TRUE;
    }
  } else if (realm == STP_FLOW_REALM) {
    sp_start = 0;
    if (cp_info.is_conduction){
      total_sps_per_solver = total_sps/2;
      close_lgi_stream_now = FALSE; //both realm active and is currently processing flow realm
    } else {
      total_sps_per_solver = total_sps;
      close_lgi_stream_now = TRUE;
    }
  }

  status = write_mme_ckpt_ref_frame_state(status, try_until_success_p, is_avg_mme, sp_start);

  status = write_mme_ckpt_movb_state(status, try_until_success_p, is_avg_mme, sp_start);

  if (sim_args.gpu) {
    status = write_mme_ckpt_voxel_data<64>(status, try_until_success_p, cp_status, is_avg_mme, realm, n_voxels_written, n_nw_voxels_written, sp_start, total_sps_per_solver);
  } else {
    status = write_mme_ckpt_voxel_data<8>(status, try_until_success_p, cp_status, is_avg_mme, realm, n_voxels_written, n_nw_voxels_written, sp_start, total_sps_per_solver);
  }
  
  char status_msg[256];
  if (realm == STP_FLOW_REALM)
    sprintf(status_msg, "%s (Finishing fluid checkpoint)", 
          cp_status);
  else
    sprintf(status_msg, "%s (Finishing conduction checkpoint)", 
          cp_status);
  cp_jobctl_output_status(status_msg);

  for(int i = sp_start; i < sp_start + total_sps_per_solver; i++)
    read_sp_eof(i);
  
  if (close_lgi_stream_now) //close stream until all realms processed
    close_lgi_streams();

  if (sri_file != NULL) {
    int close_status = sri_file->close_file(FALSE);
    if (status == SRI_SUCCESS)
      status = close_status;
  }

  if ((sri_file != NULL) && (status != SRI_SUCCESS)) {
    msg_warn("Error while attempting to write checkpoint file \"%s\""
             " for checkpoint after timestep %ld."
             " No checkpoint will be written for this timestep.\n"
             "System error message was: %s",
             filename,
             (long)sim_status_timestep(cp_info.cp_status.status) - 1,
             sri_error_string(status));
    remove(filename);
  }    

  // For each classic autostop monitor, write the signal ckpt file which contains the 
  // signals up to ckpt_timestep
  write_classic_autostop_monitor_mme_ckpt(ckpt_timestep);

  return (status == SRI_SUCCESS);
}
