/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Communication tree
 *
 * Samuel Watson, Exa Corporation
 * Created June 2012
 *--------------------------------------------------------------------------*/

#ifndef __SIM_TREE_H
#define __SIM_TREE_H

#include "common.h"

#define DEBUG_SIMTREE FALSE

#define NO_PARENT -9

typedef struct sTREE_NODE {
  STP_PROC    node;
  STP_PROC    parent_node;
  STP_PROC    n_descendants;  // Unused but nice to look at
  STP_PROC    n_children;
  STP_PROC    children[MAX_TREE_DEGREE];
} *TREE_NODE;


VOID make_subtree(asINT32 node, asINT32 size, asINT32 degree, sTREE_NODE *tree);

VOID print_tree_array(asINT32 nodes, sTREE_NODE *tree);

VOID print_subtree(asINT32 node, asINT32 indent,  sTREE_NODE *tree);


#endif /* __SIM_TREE_H */
