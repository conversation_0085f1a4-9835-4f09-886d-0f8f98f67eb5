/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#include MSGERR_H
#include "write_pm_lgi_records.h"

#include "cp_info.h" //needed for cp_info.unitd_db
#include "particle_sim_cp.h" //needed for lattice time correrction factor

#include LGI_H
//#include "cp_stream_manager.h"

extern LGI_STREAM* g_sp_streams; //CP streams
extern STP_PROC total_sps;

#if 1
template<class OBJECT_TYPE>
VOID write_to_all_sps(OBJECT_TYPE* object)
{
  ccDOTIMES(i,total_sps)
    lgi_write(g_sp_streams[i], *object);
}

VOID write_to_all_sps(const char* string, size_t n_bytes)
{
  ccDOTIMES(i,total_sps)
    lgi_write(g_sp_streams[i],(VOID*) string, n_bytes);
}

VOID write_to_all_sps(VOID* string, size_t n_bytes)
{
  ccDOTIMES(i,total_sps)
    lgi_write(g_sp_streams[i],(VOID*) string, n_bytes);
}
#endif

LGI::eLGI_DISTRIBUTION_TYPE cdi_to_lgi_distribution(CDI_DISTRIBUTION_TYPE cdi_type) {
  switch(cdi_type) {
  case DISTRIBUTION_UNIFORM:
    return(LGI::DISTRIBUTION_UNIFORM);
    break;
  case DISTRIBUTION_GAUSSIAN:
    return(LGI::DISTRIBUTION_GAUSSIAN);
    break;
  case DISTRIBUTION_GAMMA:
    return(LGI::DISTRIBUTION_GAMMA);
    break;
  case DISTRIBUTION_NONE:
    return(LGI::DISTRIBUTION_NONE);
    break;
  case DISTRIBUTION_ROSIN_RAMMLER:
    return(LGI::DISTRIBUTION_ROSIN_RAMMLER);
    break;
  case DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION:
    return(LGI::DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION);
    break;
  case DISTRIBUTION_LOG_NORMAL:
    return(LGI::DISTRIBUTION_LOG_NORMAL);
    break;
  case DISTRIBUTION_LINEAR:
    return(LGI::DISTRIBUTION_LINEAR);
    break;
  case DISTRIBUTION_1MX_POW_N:
    return(LGI::DISTRIBUTION_1MX_POW_N);
    break;
  case DISTRIBUTION_HALF_COSINE:
    return(LGI::DISTRIBUTION_HALF_COSINE);
    break;
  default:
    msg_error("Unrecognized distribution type specified in CDI file.\n");
  }
  return(LGI::NUM_PDF_TYPES);
}


LGI::eLGI_DISTRIBUTION_TYPE cdi_to_lgi_distribution(sCDI_PARM cdi_parm) {
  CDI_DISTRIBUTION_TYPE cdi_type = (CDI_DISTRIBUTION_TYPE)(int)cdi_parm.value;
  return(cdi_to_lgi_distribution(cdi_type));
}

// Define constructors for LGI emitter configurations that initialize from CDI emitter configurations structs:
sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE::sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE(
                                                                                                 sCDI_DATA& cdi_data,
                                                                                                 sCDI_EMITTER_CONFIG_BASE* cdi_emitter_config)
{
  m_cdi_major_version = cdi_data.major_version;
  m_cdi_minor_version = cdi_data.minor_version;
  m_num_total_string_bytes = 0;
  name_length = cdi_emitter_config->name.length();
  configuration_type = (eLGI_EMITTER_CONFIGURATION_TYPE)cdi_emitter_config->GetType();
  material_index = cdi_emitter_config->material_index.value;
  m_num_total_string_bytes += name_length;
}
sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER::sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER(
                                                                                               sCDI_DATA& cdi_data,
                                                                                               sCDI_NOZZLE_EMITTER_CONFIG* cdi_nozzle_config) :
  sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE(
                                                  cdi_data,
                                                  (sCDI_EMITTER_CONFIG_BASE*)cdi_nozzle_config)
{
  emission_rate_type = (LGI::eLGI_EMISSION_RATE_TYPE)cdi_nozzle_config->emission_rate_type.value;
  emission_rate_constant = cdi_nozzle_config->emission_rate.value;

  if (emission_rate_type != LGI::EMISSION_RATE_PARTICLE) //Then apply the lattice time inc to lattice time correction
    emission_rate_constant *= cp_particle_sim.lattice_time_correction();

  emission_rate_variable_name_length = cdi_nozzle_config->emission_rate.variableName.length();
  num_nozzle_configs = 1;
  m_num_total_string_bytes += emission_rate_variable_name_length;
}

sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER::sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER(
                                                                                               sCDI_DATA& cdi_data,
                                                                                               sCDI_RAIN_EMITTER_CONFIG* cdi_rain_config) :
  sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE(
                                                  cdi_data,
                                                  (sCDI_EMITTER_CONFIG_BASE*)cdi_rain_config)
{
  emission_rate_type = (LGI::eLGI_EMISSION_RATE_TYPE)cdi_rain_config->emission_rate_type.value;
  emission_rate_constant = cdi_rain_config->emission_rate.value;

  if (emission_rate_type != LGI::EMISSION_RATE_PARTICLE) //Then apply the lattice time inc to lattice time correction
    emission_rate_constant *= cp_particle_sim.lattice_time_correction();

  emission_rate_variable_name_length = cdi_rain_config->emission_rate.variableName.length();
  m_num_total_string_bytes += emission_rate_variable_name_length;

  if(m_cdi_major_version < 7 ||
     (m_cdi_major_version == 7 && m_cdi_minor_version < 11)) {
    //CDI files before version 7.11 do not specify a wind velocity,
    //csys, or reference frame.  It was assumed the wind velocity was
    //v_char in the lattice x direction in the body fixed reference
    //frame.

    specialized_parameters.rain_params.wind_x_name_length = 0;
    specialized_parameters.rain_params.wind_x_constant = cdi_data.char_vel;

    specialized_parameters.rain_params.wind_y_name_length = 0;
    specialized_parameters.rain_params.wind_y_constant = 0;

    specialized_parameters.rain_params.wind_z_name_length = 0;
    specialized_parameters.rain_params.wind_z_constant = 0;

    specialized_parameters.rain_params.wind_velocity_csys_index = 0;
    specialized_parameters.rain_params.wind_velocity_reference_frame_index = 0; //CDI_GLOBAL_REF_FRAME_INDEX (not SRI);

    //Also modify the CDI data struct passed by the caller which is used later for poulating PRI file parameters.
    cdi_rain_config->wind_x_velocity.isEqVariable = false;
    cdi_rain_config->wind_x_velocity.variableName.clear();
    cdi_rain_config->wind_x_velocity.value = cdi_data.char_vel;
    cdi_rain_config->wind_x_velocity.preferredUnit = "m/s";

    cdi_rain_config->wind_y_velocity.isEqVariable = false;
    cdi_rain_config->wind_y_velocity.variableName.clear();
    cdi_rain_config->wind_y_velocity.value = 0;
    cdi_rain_config->wind_y_velocity.preferredUnit = "m/s";

    cdi_rain_config->wind_z_velocity.isEqVariable = false;
    cdi_rain_config->wind_z_velocity.variableName.clear();
    cdi_rain_config->wind_z_velocity.value = 0;
    cdi_rain_config->wind_z_velocity.preferredUnit = "m/s";

    cdi_rain_config->csysIndex = 0;
    cdi_rain_config->refFrameIndex = 0;
    cdi_rain_config->refFrameName = "Global Body-Fixed";

  } else {
    
    specialized_parameters.rain_params.wind_x_name_length = cdi_rain_config->wind_x_velocity.variableName.length();
    specialized_parameters.rain_params.wind_x_constant =  cdi_rain_config->wind_x_velocity.value;
    m_num_total_string_bytes += specialized_parameters.rain_params.wind_x_name_length;

    specialized_parameters.rain_params.wind_y_name_length = cdi_rain_config->wind_y_velocity.variableName.length();
    specialized_parameters.rain_params.wind_y_constant =  cdi_rain_config->wind_y_velocity.value;
    m_num_total_string_bytes += specialized_parameters.rain_params.wind_y_name_length;

    specialized_parameters.rain_params.wind_z_name_length = cdi_rain_config->wind_z_velocity.variableName.length();
    specialized_parameters.rain_params.wind_z_constant =  cdi_rain_config->wind_z_velocity.value;
    m_num_total_string_bytes += specialized_parameters.rain_params.wind_z_name_length;

    specialized_parameters.rain_params.wind_velocity_csys_index = cdi_rain_config->csysIndex;
    specialized_parameters.rain_params.wind_velocity_reference_frame_index = cdi_rain_config->refFrameIndex;
  }

  num_nozzle_configs = 1;
}
sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER::sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER(
                                                                                               sCDI_DATA& cdi_data,
                                                                                               sCDI_TIRE_EMITTER_CONFIG* cdi_tire_config) :
  sLGI_PARTICLE_EMITTER_CONFIGURATION_WRITER_BASE(
                                                  cdi_data,
                                                  (sCDI_EMITTER_CONFIG_BASE*)cdi_tire_config)
{
  emission_rate_type = (LGI::eLGI_EMISSION_RATE_TYPE)cdi_tire_config->emission_rate_type.value;
  emission_rate_constant = cdi_tire_config->emission_rate.value;

  if (emission_rate_type != LGI::EMISSION_RATE_PARTICLE) //Then apply the lattice time inc to lattice time correction
    emission_rate_constant *= cp_particle_sim.lattice_time_correction();

  emission_rate_variable_name_length = cdi_tire_config->emission_rate.variableName.length();
  m_num_total_string_bytes += emission_rate_variable_name_length;

  specialized_parameters.tire_params.angular_emission_distribution_type = cdi_to_lgi_distribution(cdi_tire_config->GetSpatialEmissionDistributionType());

  specialized_parameters.tire_params.angular_emission_distribution_parameter_constant = cdi_tire_config->GetSpatialEmissionParameter().value;
  specialized_parameters.tire_params.angular_emission_distribution_parameter_variable_name_length = cdi_tire_config->GetSpatialEmissionParameter().variableName.length();
  m_num_total_string_bytes += specialized_parameters.tire_params.angular_emission_distribution_parameter_variable_name_length;

  specialized_parameters.tire_params.angular_emission_distribution_ratio_constant = cdi_tire_config->emission_rate_ratio.value;
  specialized_parameters.tire_params.angular_emission_distribution_ratio_variable_name_length = cdi_tire_config->emission_rate_ratio.variableName.length();
  m_num_total_string_bytes += specialized_parameters.tire_params.angular_emission_distribution_ratio_variable_name_length;

  num_nozzle_configs = cdi_tire_config->GetTireNozzles().size();
}

//Methods for writing an LGI emitter configuration to the LGI stream:
VOID sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER::write_base(sCDI_EMITTER_CONFIG_BASE* config){
  //write this
  //write the name
  //write emission rate variable name if one is used.

  write_to_all_sps((LGI_PARTICLE_EMITTER_CONFIGURATION_REC)this);
  write_to_all_sps(config->name.c_str(), sizeof(char) * name_length);
}

VOID  sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER::write(sCDI_NOZZLE_EMITTER_CONFIG* config) {
  write_base((sCDI_EMITTER_CONFIG_BASE*)config);
  //  For nozzle emitters confugrations, write the emission rate variable name.
  write_to_all_sps(config->emission_rate.variableName.c_str(), sizeof(char) * emission_rate_variable_name_length);
}

VOID sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER::write(sCDI_RAIN_EMITTER_CONFIG* config)  {
  write_base((sCDI_EMITTER_CONFIG_BASE*)config);
  //  For rain emitters configurations, write the emission rate variable name.
  write_to_all_sps(config->emission_rate.variableName.c_str(), sizeof(char) * emission_rate_variable_name_length);

  write_to_all_sps(config->wind_x_velocity.variableName.c_str(), 
                   sizeof(char) * specialized_parameters.rain_params.wind_x_name_length); 
  write_to_all_sps(config->wind_y_velocity.variableName.c_str(), 
                   sizeof(char) * specialized_parameters.rain_params.wind_y_name_length); 
  write_to_all_sps(config->wind_z_velocity.variableName.c_str(), 
                   sizeof(char) * specialized_parameters.rain_params.wind_z_name_length); 
}



VOID sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_WRITER::write(sCDI_TIRE_EMITTER_CONFIG* config) {

  write_base((sCDI_EMITTER_CONFIG_BASE*)config);

  //If a tire emitter config, also write these:
  //  write emission rate variable name.
  //  write the angular_emission_std_variable name.
  //  write the angular_emission_exponent_variable_name.
  //  write the angular_emission_rate_ratio_variable_name.

  write_to_all_sps(config->emission_rate.variableName.c_str(),
                   sizeof(char) * emission_rate_variable_name_length);

  write_to_all_sps(config->GetSpatialEmissionParameter().variableName.c_str(),
                   sizeof(char) * specialized_parameters.tire_params.angular_emission_distribution_parameter_variable_name_length);

  write_to_all_sps(config->emission_rate_ratio.variableName.c_str(),
                   sizeof(char) * specialized_parameters.tire_params.angular_emission_distribution_ratio_variable_name_length);
}


//====================================================
//Some nozzle data is sent down the LGI stream separately from the above emitter config data.
//This was done since tire emitter configurations can have an arbitrary number of tire nozzle configirations.
//Some methods for LGI nozzle data records are defined below.


//Define some constructors for LGI nozzle configuration data records:

//Construct based on data common to all types of nozzle data
sLGI_NOZZLE_CONFIGURATION_WRITER_BASE::sLGI_NOZZLE_CONFIGURATION_WRITER_BASE(sCDI_EMITTER_CONFIG_BASE* cdi_config)
{
  //Nothing really needs to be done here.
  nozzle_type = UNDEFINED_NOZZLE;
  m_num_total_string_bytes = 0;
}

//Construct an LGI record from a sCDI_TIRE_NOZZLE_PROPS record
sLGI_NOZZLE_CONFIGURATION_REC_WRITER::sLGI_NOZZLE_CONFIGURATION_REC_WRITER( sCDI_TIRE_NOZZLE_PROPS *cdi_tire_nozzle_props)
{
  nozzle_type = TIRE_NOZZLE;
  m_num_total_string_bytes = 0;

  sCDI_PARM param1, param2, param3, param4;
  CDI_DISTRIBUTION_TYPE dist;

  dist = (CDI_DISTRIBUTION_TYPE)(int)cdi_tire_nozzle_props->particle_diam_info.GetDistributionType().value;
  cdi_tire_nozzle_props->particle_diam_info.GetParameterValues(&param1, &param2, &param3, &param4);
  diameter_distribution = cdi_to_lgi_distribution(dist);
  diameter_distribution_param1_constant = param1.value;
  diameter_distribution_param1_variable_name_length = param1.variableName.length();
  diameter_min = param3.value;
  diameter_max = param4.value;
  m_num_total_string_bytes += diameter_distribution_param1_variable_name_length;
  diameter_distribution_param2_constant = param2.value;
  diameter_distribution_param2_variable_name_length = param2.variableName.length();
  m_num_total_string_bytes += diameter_distribution_param2_variable_name_length;

  dist = (CDI_DISTRIBUTION_TYPE)(int)cdi_tire_nozzle_props->velocity_info.GetDistributionType().value;
  cdi_tire_nozzle_props->velocity_info.GetParameterValues( &param1, &param2);
  specialized_parameters.tire_params.velocity_distribution = cdi_to_lgi_distribution(dist);
  specialized_parameters.tire_params.velocity_magnitude_distribution_param1_constant = param1.value;
  specialized_parameters.tire_params.velocity_magnitude_distribution_param1_variable_name_length = param1.variableName.length();
  m_num_total_string_bytes += specialized_parameters.tire_params.velocity_magnitude_distribution_param1_variable_name_length;
  specialized_parameters.tire_params.velocity_magnitude_distribution_param2_constant = param2.value;
  specialized_parameters.tire_params.velocity_magnitude_distribution_param2_variable_name_length = param2.variableName.length();
  m_num_total_string_bytes += specialized_parameters.tire_params.velocity_magnitude_distribution_param2_variable_name_length;

  //Apply the correction needed due to the LatticeTime vs LatticeTimeInc issues.
  switch(dist) {
  case DISTRIBUTION_UNIFORM:
  case DISTRIBUTION_GAUSSIAN:
    specialized_parameters.tire_params.velocity_magnitude_distribution_param1_constant *= cp_particle_sim.lattice_time_correction();
    specialized_parameters.tire_params.velocity_magnitude_distribution_param2_constant *= cp_particle_sim.lattice_time_correction();
    break;
  case DISTRIBUTION_NONE:
    specialized_parameters.tire_params.velocity_magnitude_distribution_param1_constant *= cp_particle_sim.lattice_time_correction();
    break;
  default:
    msg_error("Unsupported PDF type specified for tire emitter velocity magnitude.\n");
  }

  specialized_parameters.tire_params.tire_arc_position_constant = cdi_tire_nozzle_props->tire_arc_position.value;
  specialized_parameters.tire_params.tire_arc_position_variable_name_length = cdi_tire_nozzle_props->tire_arc_position.variableName.length();
  m_num_total_string_bytes += cdi_tire_nozzle_props->tire_arc_position.variableName.length();
  dist = (CDI_DISTRIBUTION_TYPE)(int)cdi_tire_nozzle_props->emission_offset_angle_info.GetDistributionType().value;
  cdi_tire_nozzle_props->emission_offset_angle_info.GetParameterValues(&param1, &param2);
  specialized_parameters.tire_params.offset_angle_distribution = cdi_to_lgi_distribution(dist);
  specialized_parameters.tire_params.emission_offset_angle_constant = param1.value;
  specialized_parameters.tire_params.emission_offset_angle_variable_name_length = param1.variableName.length();
  m_num_total_string_bytes += specialized_parameters.tire_params.emission_offset_angle_variable_name_length;
  specialized_parameters.tire_params.cone_half_angle_constant = param2.value;
  specialized_parameters.tire_params.cone_half_angle_variable_name_length = param2.variableName.length();
  m_num_total_string_bytes += specialized_parameters.tire_params.cone_half_angle_variable_name_length;

  specialized_parameters.tire_params.stretch_factor_constant = cdi_tire_nozzle_props->transverse_stretch_factor.value;
  specialized_parameters.tire_params.stretch_factor_variable_name_length = cdi_tire_nozzle_props->transverse_stretch_factor.variableName.length();
  m_num_total_string_bytes += specialized_parameters.tire_params.stretch_factor_variable_name_length;
}

//Construct an LGI record from a sCDI_RAIN_EMITTER_CONFIG
sLGI_NOZZLE_CONFIGURATION_REC_WRITER::sLGI_NOZZLE_CONFIGURATION_REC_WRITER( sCDI_RAIN_EMITTER_CONFIG *cdi_rain_config)
  : sLGI_NOZZLE_CONFIGURATION_WRITER_BASE( (sCDI_EMITTER_CONFIG_BASE*) cdi_rain_config)
{
  nozzle_type = RAIN_DATA;

  sCDI_PARM param1, param2, param3, param4;
  CDI_DISTRIBUTION_TYPE dist;
  dist = (CDI_DISTRIBUTION_TYPE)(int)cdi_rain_config->particle_diam_info.GetDistributionType().value;
  cdi_rain_config->particle_diam_info.GetParameterValues(&param1, &param2, &param3, &param4);
  diameter_distribution = cdi_to_lgi_distribution(dist);

  diameter_distribution_param1_constant = param1.value;
  diameter_distribution_param1_variable_name_length = param1.variableName.length();
  m_num_total_string_bytes += diameter_distribution_param1_variable_name_length;

  diameter_distribution_param2_constant = param2.value;
  diameter_distribution_param2_variable_name_length = param2.variableName.length();
  m_num_total_string_bytes += diameter_distribution_param2_variable_name_length;

  diameter_min = param3.value;
  diameter_max = param4.value;
}


//Consturct an LGI nozzle data record from a CDI nozzle emitter configuration.
sLGI_NOZZLE_CONFIGURATION_REC_WRITER::sLGI_NOZZLE_CONFIGURATION_REC_WRITER(sCDI_NOZZLE_EMITTER_CONFIG* cdi_nozzle_config)
  : sLGI_NOZZLE_CONFIGURATION_WRITER_BASE((sCDI_EMITTER_CONFIG_BASE*) cdi_nozzle_config)
{
  nozzle_type =  (LGI_EMITTER_NOZZLE_PARAM_TYPE)cdi_nozzle_config->nozzle_type.value;


  sCDI_PARM param1, param2, param3, param4;
  CDI_DISTRIBUTION_TYPE dist;
  dist = (CDI_DISTRIBUTION_TYPE)(int)cdi_nozzle_config->particle_diam_info.GetDistributionType().value;
  cdi_nozzle_config->particle_diam_info.GetParameterValues(&param1, &param2, &param3, &param4);
  diameter_distribution = cdi_to_lgi_distribution(dist);

  diameter_distribution_param1_constant = param1.value;
  diameter_distribution_param1_variable_name_length = param1.variableName.length();
  m_num_total_string_bytes += diameter_distribution_param1_variable_name_length;

  diameter_distribution_param2_constant = param2.value;
  diameter_distribution_param2_variable_name_length = param2.variableName.length();
  m_num_total_string_bytes += diameter_distribution_param2_variable_name_length;

  diameter_min = param3.value;
  diameter_max = param4.value;

  switch(nozzle_type) {
  case FULL_CONE_NOZZLE:

    specialized_parameters.full_cone_params.cone_half_angle_distribution = cdi_to_lgi_distribution(cdi_nozzle_config->angle_distribution);
    specialized_parameters.full_cone_params.cone_half_angle_constant = cdi_nozzle_config->cone_half_angle.value;
    specialized_parameters.full_cone_params.cone_half_angle_variable_name_length = cdi_nozzle_config->cone_half_angle.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->cone_half_angle.variableName.length();

    //added starting in cdi/513 (10/26/16)
    specialized_parameters.full_cone_params.outer_half_angle_limit_constant = cdi_nozzle_config->outer_half_angle_limit.value;
    specialized_parameters.full_cone_params.outer_half_angle_limit_variable_name_length = cdi_nozzle_config->outer_half_angle_limit.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->outer_half_angle_limit.variableName.length();

    dist = (CDI_DISTRIBUTION_TYPE)(int)cdi_nozzle_config->velocity_info.GetDistributionType().value;
    cdi_nozzle_config->velocity_info.GetParameterValues(&param1, &param2);
    specialized_parameters.full_cone_params.velocity_distribution = cdi_to_lgi_distribution(dist);
    specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_constant = param1.value;
    specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_variable_name_length = param1.variableName.length();
    m_num_total_string_bytes += specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_variable_name_length;

    specialized_parameters.full_cone_params.velocity_magnitude_distribution_param2_constant = param2.value;
    specialized_parameters.full_cone_params.velocity_magnitude_distribution_param2_variable_name_length = param2.variableName.length();
    m_num_total_string_bytes += specialized_parameters.full_cone_params.velocity_magnitude_distribution_param2_variable_name_length;

    switch(dist) {
    case DISTRIBUTION_UNIFORM:
    case DISTRIBUTION_GAUSSIAN:
      specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_constant *= cp_particle_sim.lattice_time_correction();
      specialized_parameters.full_cone_params.velocity_magnitude_distribution_param2_constant *= cp_particle_sim.lattice_time_correction();
      break;
    case DISTRIBUTION_NONE:
      specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_constant *= cp_particle_sim.lattice_time_correction();
      break;
    default:
      msg_error("Unsupported PDF type specified for full cone nozzle emitter configuration velocity magnitude.\n");
    }

    break;
  case HOLLOW_CONE_NOZZLE:
    specialized_parameters.hollow_cone_params.angle_distribution_param1_constant = cdi_nozzle_config->mean_angle.value;
    specialized_parameters.hollow_cone_params.angle_distribution_param1_variable_name_length = cdi_nozzle_config->mean_angle.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->mean_angle.variableName.length();

    specialized_parameters.hollow_cone_params.angle_distribution =  cdi_to_lgi_distribution(cdi_nozzle_config->angle_distribution);
    specialized_parameters.hollow_cone_params.angle_distribution_param2_constant = cdi_nozzle_config->angle_range.value;
    specialized_parameters.hollow_cone_params.angle_distribution_param2_variable_name_length = cdi_nozzle_config->angle_range.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->angle_range.variableName.length();

    //added starting in cdi/513 (10/26/16)
    specialized_parameters.hollow_cone_params.outer_half_angle_limit_constant = cdi_nozzle_config->outer_half_angle_limit.value;
    specialized_parameters.hollow_cone_params.outer_half_angle_limit_variable_name_length = cdi_nozzle_config->outer_half_angle_limit.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->outer_half_angle_limit.variableName.length();
    specialized_parameters.hollow_cone_params.inner_half_angle_limit_constant = cdi_nozzle_config->inner_half_angle_limit.value;
    specialized_parameters.hollow_cone_params.inner_half_angle_limit_variable_name_length = cdi_nozzle_config->inner_half_angle_limit.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->inner_half_angle_limit.variableName.length();

    dist = (CDI_DISTRIBUTION_TYPE)(int)cdi_nozzle_config->velocity_info.GetDistributionType().value;
    cdi_nozzle_config->velocity_info.GetParameterValues(&param1, &param2);
    specialized_parameters.hollow_cone_params.velocity_distribution = cdi_to_lgi_distribution(dist);

    specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_constant = param1.value;
    specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_variable_name_length = param1.variableName.length();
    m_num_total_string_bytes += specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_variable_name_length;

    specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param2_constant = param2.value;
    specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param2_variable_name_length = param2.variableName.length();
    m_num_total_string_bytes += specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param2_variable_name_length;

    switch(dist) {
    case DISTRIBUTION_UNIFORM:
    case DISTRIBUTION_GAUSSIAN:
      specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_constant *= cp_particle_sim.lattice_time_correction();
      specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param2_constant *= cp_particle_sim.lattice_time_correction();
      break;
    case DISTRIBUTION_NONE:
      specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_constant *= cp_particle_sim.lattice_time_correction();
      break;
    default:
      msg_error("Unsupported PDF type specified for hollow cone nozzle emitter configuration velocity magnitude.\n");
    }

    break;
  case ELLIPTICAL_CONE_NOZZLE:
    specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param1_constant = cdi_nozzle_config->major_half_angle.value;
    specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param1_variable_name_length = cdi_nozzle_config->major_half_angle.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->major_half_angle.variableName.length();
    specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution = cdi_to_lgi_distribution(cdi_nozzle_config->angle_distribution);

    specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param2_constant = cdi_nozzle_config->angle_range.value;
    specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param2_variable_name_length = cdi_nozzle_config->angle_range.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->angle_range.variableName.length();

    specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param1_constant = cdi_nozzle_config->minor_half_angle.value;
    specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param1_variable_name_length = cdi_nozzle_config->minor_half_angle.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->minor_half_angle.variableName.length();
    specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution = cdi_to_lgi_distribution(cdi_nozzle_config->angle_distribution);

    specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param2_constant = cdi_nozzle_config->angle_range.value;
    specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param2_variable_name_length = cdi_nozzle_config->angle_range.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->angle_range.variableName.length();

    //added starting in cdi/513 (10/26/16)
    specialized_parameters.elliptical_cone_params.minor_outer_half_angle_limit_constant = cdi_nozzle_config->minor_outer_half_angle_limit.value;
    specialized_parameters.elliptical_cone_params.minor_outer_half_angle_limit_variable_name_length = cdi_nozzle_config->minor_outer_half_angle_limit.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->minor_outer_half_angle_limit.variableName.length();
    specialized_parameters.elliptical_cone_params.major_outer_half_angle_limit_constant = cdi_nozzle_config->major_outer_half_angle_limit.value;
    specialized_parameters.elliptical_cone_params.major_outer_half_angle_limit_variable_name_length = cdi_nozzle_config->major_outer_half_angle_limit.variableName.length();
    m_num_total_string_bytes += cdi_nozzle_config->major_outer_half_angle_limit.variableName.length();

    dist = (CDI_DISTRIBUTION_TYPE)(int)cdi_nozzle_config->velocity_info.GetDistributionType().value;
    cdi_nozzle_config->velocity_info.GetParameterValues(&param1, &param2);
    specialized_parameters.hollow_cone_params.velocity_distribution = cdi_to_lgi_distribution(dist);
    specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_constant = param1.value;
    specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_variable_name_length = param1.variableName.length();
    m_num_total_string_bytes += specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_variable_name_length;

    specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param2_constant = param2.value;
    specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param2_variable_name_length = param2.variableName.length();
    m_num_total_string_bytes += specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param2_variable_name_length;


    switch(dist) {
    case DISTRIBUTION_UNIFORM:
    case DISTRIBUTION_GAUSSIAN:
      specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_constant *= cp_particle_sim.lattice_time_correction();
      specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param2_constant *= cp_particle_sim.lattice_time_correction();
      break;
    case DISTRIBUTION_NONE:
      specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_constant *= cp_particle_sim.lattice_time_correction();
      break;
    default:
      msg_error("Unsupported PDF type specified for elliptical cone nozzle emitter configuration velocity magnitude.\n");
    }

    break;
  default:
    msg_error("Unimplemented nozzle type found.");
  };
}

//Define some methods that write nozzle data to the LGI stream along with the required strings.


//Write nozzle data for a rain emitter configuration along with the necessecary strings
VOID sLGI_NOZZLE_CONFIGURATION_REC_WRITER::write(sCDI_RAIN_EMITTER_CONFIG* config) {
  //write this
  //write diameter_distribution_param1_variable_name
  //write diameter_distribution_param2_variable_name
  write_to_all_sps((LGI_NOZZLE_CONFIGURATION_REC)this);
  sCDI_PARM param1, param2;
  CDI_DISTRIBUTION_TYPE dist;
  dist = (CDI_DISTRIBUTION_TYPE)(int)config->particle_diam_info.GetDistributionType().value;
  config->particle_diam_info.GetParameterValues(&param1, &param2);

  write_to_all_sps(param1.variableName.c_str(), sizeof(char) * diameter_distribution_param1_variable_name_length);
  write_to_all_sps(param2.variableName.c_str(), sizeof(char) * diameter_distribution_param2_variable_name_length);
}


//Write tire emitter configuration nozzle data to the LGI stream along with the associated strings.
VOID sLGI_NOZZLE_CONFIGURATION_REC_WRITER::write(sCDI_TIRE_EMITTER_CONFIG* config, sCDI_TIRE_NOZZLE_PROPS* nozzle_config) {
  //write this
  //write diameter_distribution_param1_variable_name
  //write diameter_distribution_param2_variable_name

  write_to_all_sps((LGI_NOZZLE_CONFIGURATION_REC)this);

  sCDI_PARM param1, param2;
  CDI_DISTRIBUTION_TYPE dist;
  dist = (CDI_DISTRIBUTION_TYPE)(int)nozzle_config->particle_diam_info.GetDistributionType().value;
  nozzle_config->particle_diam_info.GetParameterValues(&param1, &param2); //This is just to get the variable names.
  write_to_all_sps(param1.variableName.c_str(), sizeof(char) * diameter_distribution_param1_variable_name_length);
  write_to_all_sps(param2.variableName.c_str(), sizeof(char) * diameter_distribution_param2_variable_name_length);

  //write velocity distribution parameters 1 variable name
  //write the velocity distribution parameter 2 variable name
  //write tire arc position variable name
  //write the emission offset angle variable name
  //write the cone half angle variable name
  //write the stretch factor variable name
  dist = (CDI_DISTRIBUTION_TYPE)(int)nozzle_config->velocity_info.GetDistributionType().value;
  nozzle_config->velocity_info.GetParameterValues(&param1, &param2);

  write_to_all_sps(param1.variableName.c_str(), sizeof(char) *
                   specialized_parameters.tire_params.velocity_magnitude_distribution_param1_variable_name_length);
  write_to_all_sps(param2.variableName.c_str(), sizeof(char) *
                   specialized_parameters.tire_params.velocity_magnitude_distribution_param2_variable_name_length);

  write_to_all_sps(nozzle_config->tire_arc_position.variableName.c_str(), sizeof(char) *
                   specialized_parameters.tire_params.tire_arc_position_variable_name_length);


  dist = (CDI_DISTRIBUTION_TYPE)(int)nozzle_config->emission_offset_angle_info.GetDistributionType().value;
  nozzle_config->emission_offset_angle_info.GetParameterValues(&param1, &param2);
  write_to_all_sps(param1.variableName.c_str(), sizeof(char) *
                   specialized_parameters.tire_params.emission_offset_angle_variable_name_length);
  write_to_all_sps(param2.variableName.c_str(), sizeof(char) *
                   specialized_parameters.tire_params.cone_half_angle_variable_name_length);

  write_to_all_sps(nozzle_config->transverse_stretch_factor.variableName.c_str(), sizeof(char) *
                   specialized_parameters.tire_params.stretch_factor_variable_name_length);

}

//Write nozzle data for a nozzle emitter configuration
VOID sLGI_NOZZLE_CONFIGURATION_REC_WRITER::write(sCDI_NOZZLE_EMITTER_CONFIG* nozzle_config) {
  //write this
  //write diameter_distribution_param1_variable_name
  //write diameter_distribution_param2_variable_name
  write_to_all_sps((LGI_NOZZLE_CONFIGURATION_REC)this);

  sCDI_PARM param1, param2;
  CDI_DISTRIBUTION_TYPE dist;
  dist = (CDI_DISTRIBUTION_TYPE)(int)nozzle_config->particle_diam_info.GetDistributionType().value;
  nozzle_config->particle_diam_info.GetParameterValues(&param1, &param2);
  write_to_all_sps(param1.variableName.c_str(), sizeof(char) * diameter_distribution_param1_variable_name_length);
  write_to_all_sps(param2.variableName.c_str(), sizeof(char) * diameter_distribution_param2_variable_name_length);

  switch(nozzle_type) {
  case FULL_CONE_NOZZLE:
    //write mean velocity variable name
    //write velocity range vraiable name
    //write cone half angle variable name
    dist = (CDI_DISTRIBUTION_TYPE)(int)nozzle_config->velocity_info.GetDistributionType().value;
    nozzle_config->velocity_info.GetParameterValues(&param1, &param2);

    write_to_all_sps(param1.variableName.c_str(), sizeof(char) *
                     specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_variable_name_length);
    write_to_all_sps(param2.variableName.c_str(), sizeof(char) *
                     specialized_parameters.full_cone_params.velocity_magnitude_distribution_param2_variable_name_length);
    write_to_all_sps(nozzle_config->cone_half_angle.variableName.c_str(), sizeof(char) *
                     specialized_parameters.full_cone_params.cone_half_angle_variable_name_length);
    write_to_all_sps(nozzle_config->outer_half_angle_limit.variableName.c_str(), sizeof(char) *       //added for CDI/513 (10/26/16)
                     specialized_parameters.full_cone_params.outer_half_angle_limit_variable_name_length);


    break;
  case HOLLOW_CONE_NOZZLE:
    //write mean velocity variable name
    //write mean velocity range name
    //write mean angle variable name
    //write angle stddev variable name
    dist = (CDI_DISTRIBUTION_TYPE)(int)nozzle_config->velocity_info.GetDistributionType().value;
    nozzle_config->velocity_info.GetParameterValues(&param1, &param2);

    write_to_all_sps(param1.variableName.c_str(), sizeof(char) *
                     specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_variable_name_length);
    write_to_all_sps(param2.variableName.c_str(), sizeof(char) *
                     specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param2_variable_name_length);
    write_to_all_sps(nozzle_config->mean_angle.variableName.c_str(), sizeof(char) *
                     specialized_parameters.hollow_cone_params.angle_distribution_param1_variable_name_length);
    write_to_all_sps(nozzle_config->angle_range.variableName.c_str(), sizeof(char) *
                     specialized_parameters.hollow_cone_params.angle_distribution_param2_variable_name_length);
    write_to_all_sps(nozzle_config->outer_half_angle_limit.variableName.c_str(), sizeof(char) *       //added for CDI/513 (10/26/16)
                     specialized_parameters.hollow_cone_params.outer_half_angle_limit_variable_name_length);
    write_to_all_sps(nozzle_config->inner_half_angle_limit.variableName.c_str(), sizeof(char) *
                     specialized_parameters.hollow_cone_params.inner_half_angle_limit_variable_name_length);

    break;
  case ELLIPTICAL_CONE_NOZZLE:
    //write mean velcity variable name
    //write velocity range variable name
    //write major cone half angle variable name
    //write major cone angle stddev range variable name
    //write minor cone half angle variable name
    //write minor cone half angle stddev variable name
    dist = (CDI_DISTRIBUTION_TYPE)(int)nozzle_config->velocity_info.GetDistributionType().value;
    nozzle_config->velocity_info.GetParameterValues(&param1, &param2);
    write_to_all_sps(param1.variableName.c_str(), sizeof(char) *
                     specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_variable_name_length);
    write_to_all_sps(param2.variableName.c_str(), sizeof(char) *
                     specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param2_variable_name_length);

    write_to_all_sps(nozzle_config->major_half_angle.variableName.c_str(), sizeof(char) *
                     specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param1_variable_name_length);
    write_to_all_sps(nozzle_config->angle_range.variableName.c_str(), sizeof(char) *
                     specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param2_variable_name_length);
    write_to_all_sps(nozzle_config->minor_half_angle.variableName.c_str(), sizeof(char) *
                     specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param1_variable_name_length);
    write_to_all_sps(nozzle_config->angle_range.variableName.c_str(), sizeof(char) *
                     specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param2_variable_name_length);
    write_to_all_sps(nozzle_config->minor_outer_half_angle_limit.variableName.c_str(), sizeof(char) *       //added for CDI/513 (10/26/16)
                     specialized_parameters.elliptical_cone_params.minor_outer_half_angle_limit_variable_name_length);
    write_to_all_sps(nozzle_config->major_outer_half_angle_limit.variableName.c_str(), sizeof(char) *
                     specialized_parameters.elliptical_cone_params.major_outer_half_angle_limit_variable_name_length);



    break;
  default: break;
  }
}


//=========Particle Emitters==========

//sLGI_EMITTER_WRITER_BASE::sLGI_EMITTER_WRITER_BASE(const sCDI_PARTICLE_EMITTER_BASE* cdi_emitter) {
VOID init_emitter_base_memebers(LGI_EMITTER_BASE emitter_base, const sCDI_PARTICLE_EMITTER_BASE* cdi_emitter) {
  emitter_base->emitter_type = (eLGI_EMITTER_TYPE)cdi_emitter->GetType();
  emitter_base->name_length = cdi_emitter->name.length();
  emitter_base->emitter_configuration_index = cdi_emitter->emitter_configuration.value;
  emitter_base->particles_per_parcel = cdi_emitter->particles_per_parcel.value;
  emitter_base->subject_to_dispersion_box = cdi_emitter->subject_to_dispersion_box.value != 0 ? TRUE : FALSE;
  emitter_base->subject_to_gravity = cdi_emitter->subject_to_gravity.value != 0 ? TRUE : FALSE;
  emitter_base->start_time = cdi_emitter->start.value;
  emitter_base->end_time = cdi_emitter->end.value;
  emitter_base->duration = cdi_emitter->duration.value;  // No need to write to file
  //emitter_base->fraction_eligible_for_measurement = cdi_emitter->frac_trajectory_recording.value; //Moved to trajectory windows to support cdi version 5.3 (2/28/17 - wanderer))
  emitter_base->max_age = cdi_emitter->max_age.value;
  emitter_base->max_num_reflections = cdi_emitter->max_num_reflections.value;
  emitter_base->min_particle_velocity = cdi_emitter->min_particle_velocity.value;
  emitter_base->show_decorations = cdi_emitter->visible.value;
}


//Define some constructors that take CDI emitters as arguments
//---------LGI Surface Emitters:
//Consturuct an LGI surface emitter record from a CDI surface emitter record

sLGI_SURFACE_EMITTER_REC_WRITER::sLGI_SURFACE_EMITTER_REC_WRITER(const sCDI_SURFACE_EMITTER* cdi_emitter) : sLGI_EMITTER_WRITER_BASE()
{
  init_emitter_base_memebers((LGI_EMITTER_BASE) this,(const sCDI_PARTICLE_EMITTER_BASE*)cdi_emitter);
  m_num_total_dynamic_bytes += name_length;
  auto face_list = ((sCDI_SURFACE_EMITTER*) cdi_emitter)->geom_selection.ExpandSelection(cp_info.partitions());
  n_faces = face_list.size();

  m_num_total_dynamic_bytes += n_faces * sizeof(int);
  fixed_release_points = cdi_emitter->fixed_release_points.value != 0 ? TRUE : FALSE;
  emitt_along_surface_normal = cdi_emitter->user_specified_nozzle_orientation.value == 0 ? TRUE : FALSE;

  spray_direction_csys_id = cdi_emitter->nozzle_orientation.spray_direction_csys.value;
  ccDOTIMES(axis, 3) {
    spray_direction_constants[axis] = cdi_emitter->nozzle_orientation.mean_spray_direction[axis].value;
    spray_direction_variable_name_lengths[axis] = cdi_emitter->nozzle_orientation.mean_spray_direction[axis].variableName.length();
    m_num_total_dynamic_bytes += cdi_emitter->nozzle_orientation.mean_spray_direction[axis].variableName.length();
  }

  elliptical_nozzle = cdi_emitter->nozzle_orientation.elliptical_nozzle.value == 1 ? TRUE : FALSE;
  if(elliptical_nozzle)
    {
      ccDOTIMES(axis, 3) {
        major_ellipse_direction[axis] = cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].value;
        major_ellipse_direction_name_lengths[axis] = cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].variableName.length();
        m_num_total_dynamic_bytes += cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].variableName.length();
      }
    }
  else
    {
      ccDOTIMES(axis, 3) {
        major_ellipse_direction[axis] = 0.0;
        major_ellipse_direction_name_lengths[axis] = 0;
      }
    }

}

//Method to write a surface emitter to the lgi stream along with the required strings
VOID sLGI_SURFACE_EMITTER_REC_WRITER::write(const sCDI_SURFACE_EMITTER* cdi_emitter) {
  write_to_all_sps((sLGI_SURFACE_EMITTER_REC*)this);
  write_to_all_sps(cdi_emitter->name.c_str(), sizeof(char) * name_length);
  auto face_list = ((sCDI_SURFACE_EMITTER*) cdi_emitter)->geom_selection.ExpandSelection(cp_info.partitions());
  write_to_all_sps((const char*) face_list.data(), sizeof(int) * n_faces);

  ccDOTIMES(axis,3) {
    write_to_all_sps(cdi_emitter->nozzle_orientation.mean_spray_direction[axis].variableName.c_str(), sizeof(char) * spray_direction_variable_name_lengths[axis]);
  }

  if(elliptical_nozzle)
    {
      ccDOTIMES(axis,3) {
        write_to_all_sps(cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].variableName.c_str(), sizeof(char) * major_ellipse_direction_name_lengths[axis]);
      }
    }

}



//------- LGI volume emitters:


sLGI_VOLUME_EMITTER_REC_WRITER::sLGI_VOLUME_EMITTER_REC_WRITER(const sCDI_VOLUME_EMITTER* cdi_emitter) : sLGI_EMITTER_WRITER_BASE()
{
  init_emitter_base_memebers((LGI_EMITTER_BASE) this, (const sCDI_PARTICLE_EMITTER_BASE*)cdi_emitter);
  m_num_total_dynamic_bytes += name_length;
  auto rgn_list = ((sCDI_SURFACE_EMITTER*) cdi_emitter)->geom_selection.ExpandSelection(cp_info.partitions());
  num_regions = rgn_list.size();

  spray_direction_csys_id = cdi_emitter->nozzle_orientation.spray_direction_csys.value;
  ccDOTIMES(axis, 3) {
    spray_direction_constants[axis] = cdi_emitter->nozzle_orientation.mean_spray_direction[axis].value;
    spray_direction_variable_name_lengths[axis] = cdi_emitter->nozzle_orientation.mean_spray_direction[axis].variableName.length();
    m_num_total_dynamic_bytes += cdi_emitter->nozzle_orientation.mean_spray_direction[axis].variableName.length();
  }

  elliptical_nozzle = cdi_emitter->nozzle_orientation.elliptical_nozzle.value == 1 ? TRUE : FALSE;
  if(elliptical_nozzle)
    {
      ccDOTIMES(axis, 3) {
        major_ellipse_direction[axis] = cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].value;
        major_ellipse_direction_name_lengths[axis] = cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].variableName.length();
        m_num_total_dynamic_bytes += cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].variableName.length();
      }
    }
  else
    {
      ccDOTIMES(axis, 3) {
        major_ellipse_direction[axis] = 0.0;
        major_ellipse_direction_name_lengths[axis] = 0;
      }
    }

  if(cdi_emitter->release_spacing.size() == 3)
    ccDOTIMES(axis, 3) {
      release_spacing[axis] = cdi_emitter->release_spacing[axis].value;
    }

  fixed_release_points = cdi_emitter->fixed_release_points.value != 0 ? TRUE : FALSE;
  num_cylinders = cdi_emitter->m_cylGeometries.size();
  num_boxes = cdi_emitter->m_boxGeometries.size();
  //count the size of the geometry regions.
  ccDOTIMES(cylinder_index, num_cylinders){
    m_num_total_dynamic_bytes += cdi_emitter->m_cylGeometries[cylinder_index].name.length();
    m_num_total_dynamic_bytes += sizeof(sLGI_CYLINDER_REC);
  }
  ccDOTIMES(box_index, num_boxes) {
    m_num_total_dynamic_bytes += strlen("unnamed");//cdi_emitter->mboxGeometries[box_index].name_length;
    m_num_total_dynamic_bytes += sizeof(sLGI_BOX_REC);
  }


}

VOID sLGI_VOLUME_EMITTER_REC_WRITER::write(const sCDI_VOLUME_EMITTER* cdi_emitter) {
  write_to_all_sps((sLGI_VOLUME_EMITTER_REC*)this);
  write_to_all_sps(cdi_emitter->name.c_str(), sizeof(char) * name_length);
  ccDOTIMES(axis,3) {
    write_to_all_sps(cdi_emitter->nozzle_orientation.mean_spray_direction[axis].variableName.c_str(), sizeof(char) * spray_direction_variable_name_lengths[axis]);
  }

  if(elliptical_nozzle)
    {
      ccDOTIMES(axis,3) {
        write_to_all_sps(cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].variableName.c_str(), sizeof(char) * major_ellipse_direction_name_lengths[axis]);
      }
    }

  ccDOTIMES(cylinder_index, num_cylinders) {
    sLGI_CYLINDER_REC_WRITER lgi_cylinder_rec(&(cdi_emitter->m_cylGeometries[cylinder_index]));
    lgi_cylinder_rec.write(&(cdi_emitter->m_cylGeometries[cylinder_index]));
  }

  ccDOTIMES(box_index, num_boxes) {
    sLGI_BOX_REC_WRITER lgi_box_rec(&(cdi_emitter->m_boxGeometries[box_index]));
    lgi_box_rec.write(&(cdi_emitter->m_boxGeometries[box_index]));
  }
}

// ----------LGI Point emitters:

sLGI_POINT_EMITTER_REC_WRITER::sLGI_POINT_EMITTER_REC_WRITER(const sCDI_POINT_EMITTER* cdi_emitter) : sLGI_EMITTER_WRITER_BASE()
{
  init_emitter_base_memebers((LGI_EMITTER_BASE) this, (const sCDI_PARTICLE_EMITTER_BASE*)cdi_emitter);
  m_num_total_dynamic_bytes += name_length;

  spray_direction_csys_id = cdi_emitter->nozzle_orientation.spray_direction_csys.value;
  ccDOTIMES(axis, 3) {
    spray_direction_constants[axis] = cdi_emitter->nozzle_orientation.mean_spray_direction[axis].value;
    spray_direction_variable_name_lengths[axis] = cdi_emitter->nozzle_orientation.mean_spray_direction[axis].variableName.length();
    m_num_total_dynamic_bytes += cdi_emitter->nozzle_orientation.mean_spray_direction[axis].variableName.length();
  }

  elliptical_nozzle = cdi_emitter->nozzle_orientation.elliptical_nozzle.value == 1 ? TRUE : FALSE;
  if(elliptical_nozzle)
    {
      ccDOTIMES(axis, 3) {
        major_ellipse_direction[axis] = cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].value;
        major_ellipse_direction_name_lengths[axis] = cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].variableName.length();
        m_num_total_dynamic_bytes += cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].variableName.length();
      }
    }
  else
    {
      ccDOTIMES(axis, 3) {
        major_ellipse_direction[axis] = 0.0;
        major_ellipse_direction_name_lengths[axis] = 0;
      }
    }


  num_points = cdi_emitter->points.points.size() / cdi_emitter->points.dim;
  m_num_total_dynamic_bytes += sizeof(dFLOAT) * num_points * 3;
}

VOID sLGI_POINT_EMITTER_REC_WRITER::write(const sCDI_POINT_EMITTER * cdi_emitter)
{
  write_to_all_sps((sLGI_POINT_EMITTER_REC*)this);
  write_to_all_sps(cdi_emitter->name.c_str(), sizeof(char) * name_length);
  ccDOTIMES(axis,3) {
    write_to_all_sps(cdi_emitter->nozzle_orientation.mean_spray_direction[axis].variableName.c_str(), sizeof(char) * spray_direction_variable_name_lengths[axis]);
  }

  if(elliptical_nozzle)
    {
      ccDOTIMES(axis,3) {
        write_to_all_sps(cdi_emitter->nozzle_orientation.major_ellipse_direction[axis].variableName.c_str(), sizeof(char) * major_ellipse_direction_name_lengths[axis]);
      }
    }

  write_to_all_sps((const char*)cdi_emitter->points.points.data(), sizeof(dFLOAT) * num_points * 3);
}

// -----------LGI Tire emitters:

sLGI_TIRE_EMITTER_REC_WRITER::sLGI_TIRE_EMITTER_REC_WRITER(const sCDI_TIRE_EMITTER* cdi_emitter) : sLGI_EMITTER_WRITER_BASE()
{
  init_emitter_base_memebers((LGI_EMITTER_BASE) this, (const sCDI_PARTICLE_EMITTER_BASE*)cdi_emitter);
  m_num_total_dynamic_bytes += name_length;
  auto rgn_list = ((sCDI_SURFACE_EMITTER*) cdi_emitter)->geom_selection.ExpandSelection(cp_info.partitions());  
  num_regions = rgn_list.size();
  zero_angle_csys_index = cdi_emitter->zero_angle_direction_csys.value;
  ccDOTIMES(axis, 3) {
    zero_angle_direction[axis] = cdi_emitter->zero_angle_direction[axis].value;
    rotation_axis[axis] = cdi_emitter->approx_rotation_axis_dir[axis].value;
  }

  num_cylinders = cdi_emitter->m_cylGeometries.size();
  //count the size of the geometry regions.
  ccDOTIMES(cylinder_index, num_cylinders){
    m_num_total_dynamic_bytes += cdi_emitter->m_cylGeometries[cylinder_index].name.length();
    m_num_total_dynamic_bytes += sizeof(sLGI_CYLINDER_REC);
  }
}


VOID sLGI_TIRE_EMITTER_REC_WRITER::write(const sCDI_TIRE_EMITTER* cdi_emitter) {
  write_to_all_sps((sLGI_TIRE_EMITTER_REC*)this);
  write_to_all_sps(cdi_emitter->name.c_str(), sizeof(char) * name_length);
  ccDOTIMES(cylinder_index, num_cylinders) {
    sLGI_CYLINDER_REC_WRITER lgi_cylinder_rec(&(cdi_emitter->m_cylGeometries[cylinder_index]));
    lgi_cylinder_rec.write(&(cdi_emitter->m_cylGeometries[cylinder_index]));
  }
}



// -------------LGI Rain emitters:

sLGI_RAIN_EMITTER_REC_WRITER::sLGI_RAIN_EMITTER_REC_WRITER(const sCDI_RAIN_EMITTER* cdi_emitter) : sLGI_EMITTER_WRITER_BASE()
{
  init_emitter_base_memebers((LGI_EMITTER_BASE) this, (const sCDI_PARTICLE_EMITTER_BASE*)cdi_emitter);
  m_num_total_dynamic_bytes += name_length;

  num_cylinders = cdi_emitter->m_cylGeometries.size();
  num_boxes = cdi_emitter->m_boxGeometries.size();

  //count the size of the geometry regions.
  ccDOTIMES(cylinder_index, num_cylinders){
    m_num_total_dynamic_bytes += cdi_emitter->m_cylGeometries[cylinder_index].name.length();
    m_num_total_dynamic_bytes += sizeof(sLGI_CYLINDER_REC);
  }
  ccDOTIMES(box_index, num_boxes) {
    m_num_total_dynamic_bytes += strlen("unnamed");//cdi_emitter->mboxGeometries[box_index].name_length;
    m_num_total_dynamic_bytes += sizeof(sLGI_BOX_REC);
  }
}

VOID sLGI_RAIN_EMITTER_REC_WRITER::write(const sCDI_RAIN_EMITTER* cdi_emitter) {
  write_to_all_sps((sLGI_RAIN_EMITTER_REC*)this);
  write_to_all_sps(cdi_emitter->name.c_str(), sizeof(char) * name_length);

  ccDOTIMES(cylinder_index, num_cylinders) {
    sLGI_CYLINDER_REC_WRITER lgi_cylinder_rec(&(cdi_emitter->m_cylGeometries[cylinder_index]));
    lgi_cylinder_rec.write(&(cdi_emitter->m_cylGeometries[cylinder_index]));
  }

  ccDOTIMES(box_index, num_boxes) {
    sLGI_BOX_REC_WRITER lgi_box_rec(&(cdi_emitter->m_boxGeometries[box_index]));
    lgi_box_rec.write(&(cdi_emitter->m_boxGeometries[box_index]));
  }
}


#include "cp_info.h"
extern sCDI_DATA cdi_data;

//write functions for boxes and cylinders
sLGI_CYLINDER_REC_WRITER::sLGI_CYLINDER_REC_WRITER(const sCDI_CYL_* cdi_cylinder) {
  name_length = cdi_cylinder->name.length();
  region_index = cdi_cylinder->region.regions[0];
  ccDOTIMES(axis, 3) {
    end_points[0][axis] = cdi_cylinder->start_point[axis].value;
    end_points[1][axis] = cdi_cylinder->end_point[axis].value;
  }

  //The CDI file actually stores cylinder face positions in the lattice csys even
  //though it indicates a csys id for the default. The coordinates are corrected
  //here to be in the default csys.

  ccDOTIMES(axis, 3) {
    end_points[0][axis] -= cdi_data.origin[axis];
    end_points[1][axis] -= cdi_data.origin[axis];
  }

  radii[0] = cdi_cylinder->start_radius.value;
  radii[1] = cdi_cylinder->end_radius.value;
}


VOID sLGI_CYLINDER_REC_WRITER::write(const sCDI_CYL_* cdi_cylinder) {
  write_to_all_sps((sLGI_CYLINDER_REC*)this);
  write_to_all_sps(cdi_cylinder->name.c_str(), sizeof(char) * name_length);
}

sLGI_BOX_REC_WRITER::sLGI_BOX_REC_WRITER(const sCDI_BOX_ * cdi_box) {
  name_length = strlen("unnamed");//cdi_box->name.length();
  region_index = cdi_box->region.regions[0];
  csys_index = cdi_box->csys.value;
  ccDOTIMES(axis, 3) {
    
    //PR40873: the SPs rely on the assumption that corners[0][dim] < corners[1][dim] for all dimensions.
    if(cdi_box->bbox.coord[axis][0] < cdi_box->bbox.coord[axis][1]) {  
      corners[0][axis] = cdi_box->bbox.coord[axis][0];
      corners[1][axis] = cdi_box->bbox.coord[axis][1];
    } else {
      corners[1][axis] = cdi_box->bbox.coord[axis][0];
      corners[0][axis] = cdi_box->bbox.coord[axis][1];
    }
  }
}


VOID sLGI_BOX_REC_WRITER::write(const sCDI_BOX_* cdi_box) {
  write_to_all_sps((sLGI_BOX_REC*)this);
  //write_to_all_sps(cdi_box->name.c_str(), sizeof(char) * name_length);
  write_to_all_sps("unnamed", sizeof(char) * name_length);
}
