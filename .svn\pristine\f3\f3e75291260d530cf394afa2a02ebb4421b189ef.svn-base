#!/bin/sh
#
# Flex's default skeleton file has support for interactive parsers, for which
# the generated code must look at the file stream and determine if it is
# an interactive terminal.  It uses isatty() for this, which needs the
# unistd.h header.  Needless to say, this isn't available on Windows.  So we
# tell Flex that we will always be non-interactive and then sed out the
# damaging #include.

if [ $# != 1 ] ; then
  echo "Usage: $0 filename"
  exit 1
fi

filename="$1"
tmpfile=hack_unistd_tmp$$

sed "s/^# *include *<unistd\.h>.*$//" $filename > $tmpfile

mv $tmpfile $filename
