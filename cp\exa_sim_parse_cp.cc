/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/* This file is copied from simeng to provide a mechanism the CP can
   read a turb init file. One modification is that if a varname is not
   recognized, it is ignored silently */

#include "common.h"

int g_measure_conduction_passthrough = 0;
int g_shell_tangential_measurements = 0;

int g_do_accretion_filtering_with_film_stencil = 0;
int g_is_erosion_simulation = 0;
int g_use_particle_evaporation_model = 0;


/* This routine allows the user to skip over the rest of the line, after
 * reading a number. The file pointer moves beyond the next newline
 * character. */
VOID skip_2_nl (FILE *dfp) {
  char ch;
  while ( (ch=fgetc(dfp)) != '\n') {
    ;
  }
  return;
}

typedef enum {
  PARM_INT,
  PARM_FLOAT //flaoting point types assumed to be stored in doubles.
} PHYSICS_PARM_TYPE;

typedef struct sPHYSICS_PARM {
  cSTRING name;
  PHYSICS_PARM_TYPE type;
  void *addr;
  asINT32 n_elems;
} *PHYSICS_PARM;

#define set_parm(name, addr, type) { #name, PARM_ ## type, (void *)&addr, 1}
#define set_parm_v(name, addr, type, n_elems) { #name, PARM_ ## type, (void *)&addr, n_elems}


static sPHYSICS_PARM physics_parms[] = {
  set_parm(measure_conduction_passthrough, g_measure_conduction_passthrough, INT),
  set_parm(shell_tangential_measurements, g_shell_tangential_measurements, INT),
  set_parm(do_accretion_filtering_with_film_stencil, g_do_accretion_filtering_with_film_stencil, INT),
  set_parm(is_erosion_simulation, g_is_erosion_simulation, INT),
  set_parm(use_particle_evaporation_model, g_use_particle_evaporation_model, INT)
};

#define N_PHYSICS_PARMS (sizeof(physics_parms) / sizeof(sPHYSICS_PARM))
#define PARM_TYPE_INT 0
#define PARM_TYPE_FLOAT 1

/* This routine reads the optional input turbulence parameters. */
static VOID read_turb_parm_init_internal (const char * input_file)
{
  const asINT32 LINE_BUFFER_SIZE = 160;
  char in_line[LINE_BUFFER_SIZE];
  const char *delim = " ,\t\r\n";

  FILE *dfp = fopen(input_file, "r");

  if (dfp != NULL) {
#if BUILD_5G_LATTICE
    msg_warn ("Initializing user input parameters from file %s. \n", input_file);
#else
    msg_warn ("Initializing turbulence parameters from file %s. \n", input_file);
#endif

    while (fgets(in_line, LINE_BUFFER_SIZE-1, dfp) != NULL) {
      if (in_line[0] == '#' ) {
        continue;
      } else if (in_line[0] == '$') {
        printf("A $ sign is found, closing the %s file.\n", input_file);
        fclose (dfp);
        return;
      } else {
        //sscanf(in_line, "%s %s %s", parm_type, parm_name, parm_value);
        char *parm_type = strtok(in_line, delim);
        if (parm_type == NULL) //empty line
          continue;

        char *parm_name = strtok(NULL, delim);

        PHYSICS_PARM physics_parm = (PHYSICS_PARM)&physics_parms;
        BOOLEAN get_string;
        for (asINT32 nth_parm = 0; nth_parm < N_PHYSICS_PARMS; nth_parm++) {
          if (strcmp(parm_name, physics_parm->name) != 0) {
            get_string = FALSE;
            physics_parm ++;
          } else {
            get_string = TRUE;
            break;
          }
        }
        if (get_string) {
          if (!((strcmp(parm_type, "int") == 0 || strcmp(parm_type, "INT") == 0) && (physics_parm->type == PARM_INT)) && !((strcmp(parm_type, "float") == 0 || strcmp(parm_type, "FLOAT") == 0) && (physics_parm->type == PARM_FLOAT))) {
            msg_error ("Parameter type mismatch %s %s\n", parm_name, parm_type);
          }

          char *parm_value;
          double *fvalue = (double*)physics_parm->addr;
          int     *ivalue = (int*)physics_parm->addr;
          asINT32 n_elems = physics_parm->n_elems;

          for (asINT32 i=0; (parm_value = strtok(NULL, delim)) != NULL; i++) {
            if (i == n_elems){
              msg_warn ("Excessive values of variable %s ignored", parm_name);
              break;
            }

            switch (physics_parm->type) {
            case PARM_FLOAT:
              fvalue[i] = atof(parm_value);
              printf("%s = %f \n", parm_name, fvalue[i]);
              break;
            case PARM_INT:
              ivalue[i] = atol(parm_value);
              printf("%s = %d \n", parm_name, ivalue[i]);
              break;
            }
          }
        }
#if 0
        else {
          printf("Unknown string and value is found:-\n%s\n", parm_name);
          fclose (dfp);
          printf("Terminated during initialization.\n");
          exit(1);
        }
#endif

      }
    }
    printf("End of file reached, closing the %s file.\n",  input_file);
    fclose (dfp);
  }

  return;

}


#if BUILD_5G_LATTICE
static VOID expand_user_input_internal ()
{
  switch (g_mp_multiphase_type) {
  case IDEAL_IDEAL:
    //mp_num_components = 2;
    //mp_eos[0]         = 6;
    //mp_eos[1]         = 6;
    g_mp_G[0][0] = 0.;
    g_mp_G[0][1] = -10.;
    g_mp_G[1][0] = -10.;
    g_mp_G[1][1] = 0.;
    break;
  case PR_PR:
    //mp_num_components = 2;
    //mp_eos[0]         = 3;
    //mp_eos[1]         = 3;
    g_mp_G[0][0] = 1.;
    g_mp_G[0][1] = -0.;
    g_mp_G[1][0] = -0.;
    g_mp_G[1][1] = 1.;
    break;
  case IDEAL:
    //mp_num_components = 1;
    //mp_eos[0]         = 6;
    g_mp_G[0][0] = 0.;
    break;
  case PR:
    //mp_num_components = 1;
    //mp_eos[0]         = 3;
    g_mp_G[0][0] = 1.;
    break;
  case IDEAL_PR:
    //mp_num_components = 2;
    //mp_eos[0]         = 6;
    //mp_eos[1]         = 3;
    g_mp_G[0][0] = 0.;
    g_mp_G[0][1] = -10.;
    g_mp_G[1][0] = -10.;
    g_mp_G[1][1] = 1.;
    break;
  default:
    msg_error("Unknown mp_multiphase_type \n ");
  }
  return;
}

static VOID expand_turb_parm_internal ()
{
  switch (g_mp_multiphase_type) {
  case IDEAL_IDEAL:
    //mp_num_components = 2;
    /*mp_eos[0]         = 6;
      mp_eos[1]         = 6;
      mp_G[0][0] = 0.;
      mp_G[0][1] = -10.;
      mp_G[1][0] = -10.;
      mp_G[1][1] = 0.;*/
    break;
  case PR_PR:
    //sim.num_components  = 2;
    /*mp_eos[0]         = 3;
      mp_eos[1]         = 3;
      mp_G[0][0] = 1.;
      mp_G[0][1] = -0.;
      mp_G[1][0] = -0.;
      mp_G[1][1] = 1.;*/
    break;
  case IDEAL:
    //sim.num_components  = 1;
    /*mp_eos[0]         = 6;
      mp_G[0][0] = 0.;*/
    break;
  case PR:
    //sim.num_components  = 1;
    /*mp_eos[0]         = 3;
      mp_G[0][0] = 1.;*/
    break;
  case IDEAL_PR:
    //sim.num_components  = 2;
    /*mp_eos[0]         = 6;
      mp_eos[1]         = 3;
      mp_G[0][0] = 0.;
      mp_G[0][1] = -10.;
      mp_G[1][0] = -10.;
      mp_G[1][1] = 1.;*/
    break;
  default:
    msg_error("Unknown mp_multiphase_type \n ");
  }

  if (g_mp_solver_type == STOKES_SOLVER_INDEX) {
    g_mp_protection_Ma = g_mp_protection_Ma_stokes;
    printf("Stokes solver parameter mp_protection_Ma_stokes = %f \n", g_mp_protection_Ma_stokes);
  }
  return;
}

extern VOID read_time_depended_input (const char * input_file)
{
  read_turb_parm_init_internal (input_file);
  // time depended input file should not change components and G
  //expand_user_input_internal ();
  if (g_mc_types != NULL) {
    for (asINT32 c = 0; c < sim.num_components; ++c) {
      g_mc_types[c].update (c);
    }
  }

  return;
}
#endif

VOID read_turb_parm_init()
{


#if BUILD_5G_LATTICE
  read_turb_parm_init_internal ("user_input.init");
  expand_user_input_internal ();

  read_turb_parm_init_internal ("exa_turb_5g.init");
  expand_turb_parm_internal ();
#elif BUILD_D19_LATTICE
  read_turb_parm_init_internal ("exa_turb_d19.init");
#elif BUILD_D39_LATTICE
  read_turb_parm_init_internal ("exa_turb_d39.init");
#elif  BUILD_D34_LATTICE
  read_turb_parm_init_internal ("exa_turb_d34.init");
#endif

  return;
}

