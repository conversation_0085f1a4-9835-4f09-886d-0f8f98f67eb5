/*--------------------------------------------------------------------------*
 * Common definitions for the license process
 *
 *--------------------------------------------------------------------------*/

#ifndef __CP_LICENSE_H
#define __CP_LICENSE_H

#include SCALAR_H
#include MSGERR_H

/*--------------------------------------------------------------------------*
 * Utilities needed for separate process talking to license system
 *--------------------------------------------------------------------------*/

typedef sINT32 LM_INT; // Need agreement across platforms on size

#define LM_KEY 0x49da2765

#define lm_encrypt_int(x, salt) ((x) ^ ((salt) ^ LM_KEY))
#define lm_decrypt_int(x, salt) lm_encrypt_int(x, salt)

#define lm_tag_ok(child_pid) 		lm_encrypt_int(0, child_pid)
#define lm_tag_heartbeat(child_pid) 	lm_encrypt_int(1, child_pid)
#define lm_tag_usage(child_pid) 	lm_encrypt_int(2, child_pid)
#define lm_tag_error(child_pid) 	lm_encrypt_int(3, child_pid)
#define lm_tag_voxels(child_pid) 	lm_encrypt_int(4, child_pid)
#define lm_tag_aux_license(child_pid) 	lm_encrypt_int(5, child_pid)
#define lm_tag_release(child_pid) 	lm_encrypt_int(6, child_pid)

#define MAX_SECS_BETWEEN_LICENSE_HEARTBEATS (5 * 60)	/* 5 minutes */
extern asINT32 secs_between_license_heartbeats;

//----------------------------------------------------------------------------
// get_license_heartbeat_interval
//----------------------------------------------------------------------------
inline void get_license_heartbeat_interval()
{
  cSTRING license_heartbeat_interval_string = getenv("EXA_LICENSE_HEARTBEAT_INTERVAL");
  secs_between_license_heartbeats = MAX_SECS_BETWEEN_LICENSE_HEARTBEATS; // prevent hacking
  if (license_heartbeat_interval_string != NULL) {
    asINT32 maybe_new_interval = atoi(license_heartbeat_interval_string);
    if (maybe_new_interval <= MAX_SECS_BETWEEN_LICENSE_HEARTBEATS) {
      secs_between_license_heartbeats = maybe_new_interval;
      msg_print("License heartbeat interval reduced to %d seconds.", secs_between_license_heartbeats);
    } else {
      msg_warn("License heartbeat interval cannot be set above %d seconds.", secs_between_license_heartbeats);
    }
  }
}

#endif
