/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#include "common.h"
#include "table.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "jobctl.h"

#include "heat_exchangers.h"

BOOLEAN does_table_filename_include_digit(cSTRING name)
{
  while (*name != '\0') {
    if (isdigit(*name++)) {
      return TRUE;
    }
  }

  return FALSE;
}

// Increments the last number in the name. If the last number begins
// with a zero, the new number has a minimum field width the
// same as the old number and uses 0 as the pad character.
//
// Assumes there is enough space in name for an additional digit if
// necessary.
//
// Also fills index_return (if not NULL) with the last number found
// in the new name, including leading zeros if present.
VOID increment_table_filename(STRING name, asINT32 inc, STRING index_return)
{
  asINT32 len = strlen(name);

  asINT32 last_digit = -1;
  for (asINT32 i = len-1; i>=0; i--) {
    if (isdigit(name[i])) {
      last_digit = i;
      break;
    }
  }

  if (last_digit < 0) {
    if (index_return)
      index_return[0] = '\0';
    return;
  }

  asINT32 j;
  for (j = last_digit - 1; j >= 0; j--) {
    if (!isdigit(name[j]))
      break;
  }
  asINT32 first_digit = j + 1;

  int number;
  sscanf(name + first_digit, "%d", &number);
  number += inc;

  char suffix[1024];
  
  strcpy(suffix, name + last_digit + 1);
  
  if (name[first_digit] == '0') {
    asINT32 n_digits = last_digit - first_digit + 1;
    sprintf(name + first_digit, "%0*d", n_digits, number);
  } else {
    sprintf(name + first_digit, "%d", number);
  }
  if (index_return != NULL)
    strcpy(index_return, name + first_digit);
   
  strcpy(name + strlen(name), suffix);
}

static VOID insert_table_queue_entry(TABLE_QUEUE_ENTRY entry)
{
  TABLE_QUEUE_ENTRY queue = cp_info.table_queue;
  if ((queue == NULL)
      || (entry->read_time < queue->read_time)) {
    cp_info.table_queue = entry;
    entry->next = queue;
  } else {
    TABLE_QUEUE_ENTRY before = queue;
    TABLE_QUEUE_ENTRY after = before->next;
    while (after != NULL) {
      if (after->read_time > entry->read_time)
	break;
      before = after;
      after = before->next;
    }
    before->next = entry;
    entry->next = after;
  }
}

// vtable_header houses the serialized header of a vtable for transfer via MPI.
// Once the table has been transferred, the header is freed.
static char *vtable_header = NULL; 
static size_t vtable_header_offset = 0;
static VOID vtable_stream_write_header_buffer(const VOID *buf, size_t n_chars)
{
  memcpy(vtable_header + vtable_header_offset, buf, n_chars);
  vtable_header_offset += n_chars;
}

static TABLE_LOCATION locate_table_file(
    cSTRING table_filename, 
    cSTRING abs_table_filename, 
    char fullname[PLATFORM_MAXPATHLEN])
{
  // order of searching for the model file is 
  // a) search path specified on the exaqsub command line
  // b) relative path specified in the CDI file 
  // c) absolute path specified in the CDI file 
  // d) run directory 
  cSTRING search_path;
  CHARACTER base_name[PLATFORM_MAXPATHLEN];
  CHARACTER run_dir[PLATFORM_MAXPATHLEN];
  // if abs_table_filename is NULL, do not consider absolute paths, 
  // abs_table_filename should not be NULL only when reading 
  // in the initial table (t=0). For checkpoint restart, if the 
  // number of tables read prior to the checkpoint is greater 
  // than 0, absolute path should be NULL

  memset(base_name,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
  memset(run_dir,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
  if (!platform_get_cwd(run_dir,PLATFORM_MAXPATHLEN-1)) {
    return TABLE_LOCATION_INVALID;
  }

  if (!fullname) {
    msg_internal_error("Cannot specify NULL string for the table filename to be copied into");
  }

  search_path = cp_info.table_search_path;
  // a) first check the search path if provided
  if (search_path) {
    // get the basename of the coupling file
    if (!platform_get_file_base_name(table_filename,base_name)) {
      return TABLE_LOCATION_INVALID;
    }
    asINT32 basename_len = strlen(base_name);
    while (1) {
      asINT32 count = strcspn(search_path, PLATFORM_SRCH_PATHSEP);
      if ((count > 0) && ((count + basename_len) < FILENAME_MAX)) {
	strncpy(fullname, search_path, count);
	strncpy(fullname + count, PLATFORM_PATHSEP, 1);
	strcpy(fullname + count + 1, base_name);

	if (platform_file_present(fullname)) {
	  return TABLE_LOCATION_SEARCH_PATH;
	}
      }
      if (search_path[count] == '\0')
	break;
      search_path += count + 1;
    }
  }

  // b) Check supplied relative path in CDI next
  // relative path could also be an absolute path 
  if (platform_file_present(table_filename) && table_filename[0] != '/') {
    strcpy(fullname,table_filename);
    return TABLE_LOCATION_CDI_RELATIVE;
  }

  // c) Check supplied absolute path in CDI next
  if (abs_table_filename && platform_file_present(abs_table_filename)) {
    strcpy(fullname,abs_table_filename);
    return TABLE_LOCATION_CDI_ABSOLUTE;
  }

  // d) Check the run directory next
  if (platform_get_file_base_name(table_filename,base_name) &&
      platform_file_present(base_name)) {
      strcpy(fullname,base_name);
      return TABLE_LOCATION_RUNDIR;
  }

  return TABLE_LOCATION_INVALID;
}

BOOLEAN is_table_file_present(cSTRING table_filename, 
    			      cSTRING abs_table_filename, 
			      char fullname[PLATFORM_MAXPATHLEN])
{
  return (TABLE_LOCATION_INVALID != locate_table_file(table_filename, abs_table_filename, fullname));
}
  
FILE *open_table_file(cSTRING table_filename, cSTRING abs_table_filename, 
    		      char table_location[PLATFORM_MAXPATHLEN])
{
  FILE *fp;

  if (TABLE_LOCATION_INVALID == 
      locate_table_file(table_filename, abs_table_filename, table_location))
    return NULL;
  return fopen(table_location,"r");
}


  
static VOID maybe_read_table_internal(TABLE_QUEUE_ENTRY entry)
{
  TABLE_DESC table_desc = entry->table_desc;
  const WALLCLOCK_TIME_SECS    SECS_BETWEEN_TABLE_FILE_CHECKS = 1;
  WALLCLOCK_TIME_SECS time_secs;

  // If the table is associated with a CDI meas window and has a before-table-read 
  // command, we don't look for the table until the relevant frame of the associated 
  // meas files has been written. This ensures that the environment variables
  // set prior to issuing the command (EXA_TABLE_FILENAME and EXA_TABLE_INDEX) are
  // consistent with user expectations. If we found the table file early, these 2
  // variables would be incremented, potentially confusing the user's command. This
  // is particularly an issue after a checkpoint restart when a table generated
  // in the original run may be left around and thus available before the relevant 
  // frame is written.

  if (((table_desc->cdi_meas_window == NULL) || (table_desc->cmd_string == NULL) || table_desc->has_cmd_been_issued)
      && (WALLCLOCK_TIME_DIFF(time_secs = wallclock_time_secs(), 
			      table_desc->last_file_check_time) > SECS_BETWEEN_TABLE_FILE_CHECKS)) {
    char table_location[PLATFORM_MAXPATHLEN];
    table_desc->last_file_check_time = time_secs;

    // We don't want to update a table file on the last timestep, but this has 
    // been coded to behave properly if the user extends the end time via exasignal.
    FILE *fp = open_table_file(table_desc->filename, table_desc->abs_filename, table_location);
    STRING filename = table_location;
    if (fp == NULL) {
      if ((cp_info.time >= entry->read_time) && !entry->waiting_msg_issued) {
        entry->waiting_msg_issued = TRUE;
        msg_print("Waiting for table file \"%s\" (table \"%s\") at timestep %d", 
                  table_desc->filename, table_desc->name, entry->read_time);
      }
    } else if (entry->read_time < cp_info.end_time) {
      entry->waiting_msg_issued = FALSE;
      
      char job_status[512];
      sprintf(job_status, "%s (Reading table file \"%s\")", 
	      cp_jobctl_status_string(),
	      filename);
      cp_jobctl_output_status(job_status);

      if (entry->read_time == cp_info.time) {
        if ((table_desc->cdi_flags & CDI_GTBL_COUPLE_TO_POWERCOOL) == CDI_GTBL_COUPLE_TO_POWERCOOL) {
          msg_print("Reading coupling table file \"%s\" (table \"%s\") generated by PowerCOOL at timestep %d", 
                    filename, table_desc->name, entry->read_time);
        } else if ((table_desc->cdi_flags & CDI_GTBL_COUPLE_TO_AMESIM) == CDI_GTBL_COUPLE_TO_AMESIM) {
          msg_print("Reading coupling table file \"%s\" (table \"%s\") generated by AMESim at timestep %d", 
                    filename, table_desc->name, entry->read_time);
        } else {
          msg_print("Reading table file \"%s\" (table \"%s\") at timestep %d", 
                    filename, table_desc->name, entry->read_time);
        }
      } else {
        msg_print("Read table file \"%s\" (table \"%s\") at timestep %d"
                  " to be used beginning at timestep %d", 
                  filename, table_desc->name, cp_info.time, entry->read_time);
      }

      EXPRLANG_VALUE vtable;
      STRING msg = NULL;
      BOOLEAN status = TRUE;
      vtable = exprlang_v_table_read_from_file(fp,
					       cp_info.units_db,
					       DEFAULT_VTABLE_ITERATIONS,
					       &status, &msg);
      fclose(fp);

      BOOLEAN n_vars_invalid = FALSE;
      BOOLEAN var_units_invalid = FALSE;
      if (status) {
        n_vars_invalid = exprlang_v_table_num_measurements(vtable) != table_desc->n_variables;
        ccDOTIMES(v, table_desc->n_variables) {
          if (!units_equal_p(cp_info.units_db, exprlang_v_table_get_measurement_unit(vtable, v), 
                             table_desc->variable_units[v])) {
            var_units_invalid = TRUE;
            break;
          }
        }
      }

      if (!status || n_vars_invalid || var_units_invalid) {
        char new_filename[1024];
        sprintf(new_filename, "%s.%s", filename, "error");
        free(msg); msg = NULL; // cleanup the error msg from exprlang

        // if rename fails, we will simply repeat this error sequence over and over
        if (n_vars_invalid)
          msg = EXA_STRDUP("Number of variables differs from initial table");
        else if (var_units_invalid)
          msg = EXA_STRDUP("Variable units differ from initial table");

        if (rename(filename, new_filename) != 0) {
          msg_warn("Error while reading table from file \"%s\": %s\n\n"
                   "Failed to rename \"%s\" to \"%s\". The simulation will"
                   " continue, but will likely fail again reading the same file.",
                   filename, msg, filename, new_filename);
        } else {
          msg_warn("Error while reading table from file \"%s\": %s\n\n"
                   "File has been renamed to \"%s\"."
                   " Waiting for new version of \"%s\".",
                   filename, msg, new_filename, filename);
        }
        free(msg); msg = NULL;
        return;
      }

      // At this point, we know we have a good table.
      table_desc->n_tables_read++;
      increment_table_filename(table_desc->filename, 1, table_desc->index);
      increment_table_filename(table_desc->abs_filename, 1, NULL);
      
      table_desc->has_cmd_been_issued = FALSE; // reset flag that is set when post meas cmd is issued

      CP_TABLE_COMM_DESC table_comm_descs = cp_info.table_comm_descs;

      // Quadrics requires that all objects sent be allocated on the heap
      static VTABLE_XCHG_SIZE *table_sizes = NULL;
      if (table_sizes == NULL)
        table_sizes = xnew VTABLE_XCHG_SIZE[ 2];

      // Send the table size to all SPs. The tables are dynamic, and can change
      // in size each timestep, so we notify all sps about the current table size.
      size_t table_header_size = exprlang_v_table_header_serial_length(vtable);
      size_t table_data_size = exprlang_v_table_data_serial_length(vtable);
      table_sizes[0] = table_header_size;
      table_sizes[1] = table_data_size;
      int tag = make_mpi_tag<eMPI_MSG::VTABLE>(table_desc->table_index);

      // heat exchanger tables are only to be consumed by the flow sps
      STP_PROC sps = (table_desc->hx_index == -1)? total_sps : total_fsps;
      ccDOTIMES(sp, sps) {
        MPI_Issend(table_sizes, 2, eMPI_VTABLE_XCHG_SIZE, sp, 
                   tag,
                   eMPI_sp_cp_comm, &table_comm_descs[sp].mpi_requests[0]);
      }

      // Use synchronous sends (MPI_Issend) below to prevent the CP from racing
      // ahead reading tables that get buffered internal to MPI. This is
      // particularly an issue if eager mode message sends come into play.
      vtable_header = xnew char[table_header_size];
      exprlang_serialize_v_table_header_to_stream(vtable, vtable_stream_write_header_buffer);
      char *vtable_data = exprlang_v_table_data_buffer(vtable);

      std::map<std::string,std::vector<int> >::iterator node_it = cp_info.physical_node_sp_ranks.begin();
      std::map<std::string,std::vector<int> >::iterator node_end = cp_info.physical_node_sp_ranks.end();

      // There is 1 process on each node that is the "owner" of a given table
      // These are assigned in a round-robin fashion. We only send the actual
      // table data to the owner for each node.
      for( ; node_it != node_end; ++node_it) {
        int n_processes_on_node = node_it->second.size();
        int owning_sp_index = table_desc->table_index % n_processes_on_node;
        int vtable_send_to_sp = node_it->second[owning_sp_index];

        // Serialize the table header into a buffer and send to SPs
        vtable_header_offset = 0;

        MPI_Issend(vtable_header, table_header_size, eMPI_CHARACTER, vtable_send_to_sp, 
                   tag,
                   eMPI_sp_cp_comm, &table_comm_descs[vtable_send_to_sp].mpi_requests[1]);

        // Send the table data to the SPs
        MPI_Issend(vtable_data, table_data_size, eMPI_CHARACTER, vtable_send_to_sp, 
                   tag,
                   eMPI_sp_cp_comm, &table_comm_descs[vtable_send_to_sp].mpi_requests[2]);
      }

      entry->vtable = vtable;
      entry->next_sp_to_test = 0;
      // Restore status message to a standard timestep report
      cp_jobctl_output_current_status();

      // For the HX associated with this table, update the monitors it contributes to
      if ((table_desc->cdi_flags & CDI_GTBL_COUPLE_TO_POWERCOOL) == CDI_GTBL_COUPLE_TO_POWERCOOL
          || (table_desc->cdi_flags & CDI_GTBL_COUPLE_TO_AMESIM) == CDI_GTBL_COUPLE_TO_AMESIM) {
#if DEBUG_MONITOR        
        msg_print("Table %s is coupled to hx %d", table_desc->name, table_desc->hx_index);
#endif
        //TODO: should be modified if variable PowerCOOL coupling is used
        process_hx_monitors(table_desc->hx_index, entry->read_time, 
                            entry->read_time + table_desc->period);
       
      }
    }
  }
}

VOID maybe_wait_for_tables_at_end()
{
  const WALLCLOCK_TIME_SECS SECS_BETWEEN_TABLE_FILE_CHECKS = 1;
  TABLE_QUEUE_ENTRY entry;
  TABLE_DESC table_desc;
  char table_location[PLATFORM_MAXPATHLEN];

  for (entry = cp_info.table_queue; entry != NULL; entry = entry->next) {
    if (entry->read_time > cp_info.end_time) break;

    table_desc = entry->table_desc;
    if (((table_desc->cdi_meas_window == NULL) || (table_desc->cmd_string == NULL) 
	  || table_desc->has_cmd_been_issued)) {
      BOOLEAN print_message = TRUE;
      // check the search path, not just in the run directory
      while (!is_table_file_present(table_desc->filename, table_desc->abs_filename, table_location)) {
        if (print_message) {
          msg_print("Waiting for table file \"%s\" (table \"%s\") at timestep %d", 
                    table_desc->filename, table_desc->name, entry->read_time);
          print_message = FALSE;
        }
        platform_sleep_seconds(SECS_BETWEEN_TABLE_FILE_CHECKS);
      }
      msg_print("Found table file \"%s\" (table \"%s\") at timestep %d", 
                table_location, table_desc->name, entry->read_time);
    }
  }
}

VOID maybe_read_tables()
{
  TABLE_QUEUE_ENTRY entry = cp_info.table_queue;

  if (entry == NULL)
    return;

  if (!entry->vtable) {
    maybe_read_table_internal(entry);
  } else {
    CP_TABLE_COMM_DESC table_comm_descs = cp_info.table_comm_descs;

    // heat exchanger tables are only to be consumed by the flow sps
    STP_PROC sps = (entry->table_desc->hx_index == -1)? total_sps : total_fsps;

    // Count the number of isends of the table data that have completed.
    ccDO_FROM_BELOW(sp,entry->next_sp_to_test,sps) {
      asINT32 n_chunks_sent = 0;
      for (asINT32 i=sCP_TABLE_COMM_DESC::N_REQUESTS-1; i>=0; i--) {
        int flag = 0;
        MPI_Status status;
        MPI_Test(&table_comm_descs[sp].mpi_requests[i], &flag, &status);
        if (flag) {
          n_chunks_sent++;
        } else {
          break;
        }
      }
      if (n_chunks_sent == sCP_TABLE_COMM_DESC::N_REQUESTS) {
        entry->next_sp_to_test = sp+1;
      } else {
        break;
      }
    } 
    if (entry->next_sp_to_test == sps) {
      // Done transferring this table to all SPs
      exprlang_v_table_free(entry->vtable);
      delete[] vtable_header;
      vtable_header = NULL;

      entry->vtable = NULL;
      cp_info.table_queue = entry->next;
      
      if (entry->read_time == entry->table_desc->next_periodic_read) {
        // This was a periodic read - reinsert in queue
        TIMESTEP next_read_time;
        if (entry->table_desc->hx_index == -1) {
          next_read_time = entry->read_time + entry->table_desc->period;
        }
        else {
          auto realm_fn = &cp_info.realm_phase_time_info[STP_FLOW_REALM];
          next_read_time = realm_fn->realm_to_global_timestep(realm_fn->global_to_realm_timestep(entry->read_time) + entry->table_desc->period);
        }

        if (next_read_time <= entry->table_desc->end_time) {
          entry->read_time = next_read_time;
          entry->table_desc->next_periodic_read = next_read_time;
          insert_table_queue_entry(entry);
        }
      }
    } // sps
      
  }
}

VOID initialize_table_queue()
{
  // Insert table descs with a known read time in the queue
  TABLE_DESC table_descs = cp_info.table_descs;
  asINT32 n_tables = cp_info.n_tables;
  TABLE_QUEUE_ENTRY entries = cnew sTABLE_QUEUE_ENTRY[ n_tables];

  TABLE_DESC table_desc = table_descs;
  TABLE_QUEUE_ENTRY entry = entries;

  for (asINT32 i=0; i<n_tables; i++, table_desc++, entry++) {
    if (table_desc->read_during_sim
	&& (table_desc->next_periodic_read > cp_info.restart_time)
	&& (table_desc->next_periodic_read <= table_desc->end_time)) {
      table_desc->last_file_check_time = 0;

      entry->table_desc = table_desc;
      entry->read_time = table_desc->next_periodic_read;

      insert_table_queue_entry(entry);
    }
  }
}
