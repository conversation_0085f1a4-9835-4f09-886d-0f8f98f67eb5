#!/bin/csh

# simple wrapper on procreate_cdi

# usage: procreate_here run_dir pro_cmd pro_args 

if ($#argv < 2) then
  echo "usage: ${0} run_dir pro_cmd pro_args"
  exit 1
endif

set run_dir=$1

shift

set assy=`ls | grep .asm | tail -1`

set assy=$assy:r

set case=`ls | grep .exac | tail -1`

set case=$case:r

echo "procreate_cdi . ${assy} ${case} ${run_dir} $*"
procreate_cdi . ${assy} ${case} ${run_dir} $*
