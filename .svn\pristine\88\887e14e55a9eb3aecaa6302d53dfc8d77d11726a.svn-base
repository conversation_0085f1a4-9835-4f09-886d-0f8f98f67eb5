/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */

#include <locale>
#include <utility>

#include "cdi_common.h"

#include GEOM_COMMON_H


cCDI_GEOMETRY_GENERATOR::cCDI_GEOMETRY_GENERATOR(const cCDI_PARTITIONS& cdiPartitions,
                                                 std::unordered_set<cdiINT32> regionsMeasured,
                                                 std::unordered_set<cdiINT32> facesMeasured,
                                                 std::unordered_set<cdiINT32> solidRegions,
                                                 std::unordered_set<cdiINT32> fluidRegions,
                                                 CDI_ENTITY_FILTER_FUNCTION regionFilterFunc,
                                                 CDI_ENTITY_FILTER_FUNCTION faceFilterFunc,
                                                 cGEOM_COMMON_ENTITY_LIST& entities /*appended to*/)
    : m_cdiPartitions(cdiPartitions)
    , m_regionsMeasured(std::move(regionsMeasured))
    , m_facesMeasured(std::move(facesMeasured))
    , m_solidRegions(std::move(solidRegions))
    , m_fluidRegions(std::move(fluidRegions))
    , m_regionFilterFunc(regionFilterFunc)
    , m_faceFilterFunc(faceFilterFunc)
    , m_entities(entities)
{
}

// Does a case-insensitive lexicographical comparison of two strings, and returns true
// if the first one should appear before the second.
/* static */
bool cCDI_GEOMETRY_GENERATOR::LexicographicalLess(const std::string& strA, const std::string& strB) const
{
  // NOTE: we could also return QString::compare(strA, strB, Qt::CaseInsensitive) < 0
  // which is what CASE uses to sort them.  But we don't want to introduce a Qt dependency.
  return lexicographical_compare(begin(strA), end(strA),
                                 begin(strB), end(strB),
                                 [](const unsigned char& charA, const unsigned char& charB) {
                                   return tolower(charA) < tolower(charB);
                                 });
}

// Visit this segment and its children, appending to m_entities in the order desired
// by geometry selection UI.
void cCDI_GEOMETRY_GENERATOR::VisitSegments(const cCDI_SEGMENT& segment, const std::string& segmentPath)
{
  const cCDI_PARTITION& partition = segment.GetPartition();
  const cdiINT32 partitionIndex = m_cdiPartitions.GetPartitionIndex(partition);

  std::string segmentDisplayName;
  if (segment.IsRoot()) {
    segmentDisplayName = segment.GetPartition().GetName() + "/";
  } else {
    segmentDisplayName = segment.GetName(/*includePath=*/false);
  }

  // Segments are never "interesting" since they are never referenced in a "mesr" chunk,
  // and they don't have indices that can be used for comparing across partitions.
  cGEOM_COMMON_ENTITY* entity = new cGEOM_COMMON_ENTITY(partitionIndex,
                                                        eGEOM_COMMON_ITEM_TYPE::Segment,
                                                        segmentPath,
                                                        segmentDisplayName,
                                                        /*index=*/-1,
                                                        /*interesting=*/false);
  m_entities.push_back(entity);

  // Get the part and partial part index lists, combine them, and sort them case-insensitively
  // by the corresponding names.
  auto regionIndices = segment.GetRegionIndices(/*recursive=*/false);
  for (auto* partialPart : segment.GetPartialParts()) {
    regionIndices.push_back(partialPart->GetPartIndex());
  }
  std::sort(regionIndices.begin(), regionIndices.end(),
            [this](cdiINT32 indexA, cdiINT32 indexB) {
              return LexicographicalLess(m_cdiPartitions.GetShortPartName(indexA),
                                         m_cdiPartitions.GetShortPartName(indexB));
            });

  // Now walk the parts/partial parts and their faces in sorted order.
  for (auto regionIndex : regionIndices) {
    std::vector<cdiINT32> faceIndices;
    eGEOM_COMMON_ITEM_TYPE itemType = eGEOM_COMMON_ITEM_TYPE::Other;
    if (cCDI_PARTIAL_PART* partialPart = segment.FindPartialPart(regionIndex)) {
      itemType = eGEOM_COMMON_ITEM_TYPE::Partial_Part;
      faceIndices = partialPart->GetFaceIndices();
    }
    else {
      itemType = eGEOM_COMMON_ITEM_TYPE::Part;
      faceIndices = m_cdiPartitions.GetPartEntity(regionIndex).GetFaceList();
    }

    // See if the region is a solid region or a fluid region.
    CDI_ENTITY_FILTER_CLASS regionFilterClass = CDI_TREAT_AS_UNKNOWN;
    if (m_solidRegions.count(regionIndex) != 0) {
      regionFilterClass = CDI_TREAT_AS_SOLID;
    }
    else if (m_fluidRegions.count(regionIndex) != 0) {
      regionFilterClass = CDI_TREAT_AS_FLUID;
    }
    // Determine if the region is interesting.
    // A region is considered interesting if it passes the region filter function.
    const bool regionMeasured = m_regionsMeasured.count(regionIndex) != 0;
    const bool regionInteresting = (*m_regionFilterFunc)(regionMeasured, regionFilterClass);

    std::string partFullPath;
    segment.GetQualifiedPartName(regionIndex, /*includePath=*/true, &partFullPath);
    const std::string partShortName = m_cdiPartitions.GetShortPartName(regionIndex);
    std::string partDisplayName = partShortName;
    // If the part is not unique in its parent segment, then append the path in the
    // base assembly to the short name for disambiguation.
    if (cGEOM_COMMON_ENTITY::FullPathIsForNonUniqueEntity(partFullPath)) {
      const std::string pathInBaseAssembly = m_cdiPartitions.GetPartName(regionIndex);
      partDisplayName += " (" + pathInBaseAssembly + ")";
    }
    entity = new cGEOM_COMMON_ENTITY(partitionIndex,
                                     itemType,
                                     partFullPath,
                                     partDisplayName,
                                     regionIndex,
                                     regionInteresting);
    m_entities.push_back(entity);

    // Sort the face indices by the corresponding names, case-insensitively.
    std::sort(faceIndices.begin(), faceIndices.end(),
              [this](cdiINT32 indexA, cdiINT32 indexB) {
                return LexicographicalLess(m_cdiPartitions.GetShortFaceName(indexA),
                                           m_cdiPartitions.GetShortFaceName(indexB));
              });

    // Now walk them in sorted order.
    for (auto faceIndex : faceIndices) {
      // If the face's region is interesting, we treat the face as measured because
      // we want the face to be interesting too -- unless it's not solid.
      const bool faceMeasured = regionInteresting || m_facesMeasured.count(faceIndex) != 0;
      CDI_ENTITY_FILTER_CLASS faceFilterClass = CDI_TREAT_AS_UNKNOWN;
      if (regionFilterClass == CDI_TREAT_AS_SOLID ||
          (m_cdiPartitions.GetFaceEntity(faceIndex).GetSurfacePropertyIndex() != CDI_PHYS_TYPE_NONE)) {
        faceFilterClass = CDI_TREAT_AS_SOLID;
      }
      const bool faceInteresting = (*m_faceFilterFunc)(faceMeasured, faceFilterClass);

      std::string faceFullPath;
      segment.GetQualifiedFaceName(faceIndex, /*includePath=*/true, &faceFullPath);
      std::string faceShortName = m_cdiPartitions.GetShortFaceName(faceIndex);
      if (cGEOM_COMMON_ENTITY::FullPathIsForNonUniqueEntity(faceFullPath)) {
        const std::string pathInBaseAssembly = m_cdiPartitions.GetFaceName(faceIndex);
        faceShortName += " (" + pathInBaseAssembly + ")";
      }
      entity = new cGEOM_COMMON_ENTITY(partitionIndex,
                                       eGEOM_COMMON_ITEM_TYPE::Face,
                                       faceFullPath,
                                       faceShortName,
                                       faceIndex,
                                       faceInteresting);
      m_entities.push_back(entity);
    }
  }

  // Now visit child segments.  This results in the DFS ordering of segments,
  // as desired by the tree widget.
  for (auto* childSegment : segment.GetChildSegments()) {
    VisitSegments(*childSegment, segmentPath + childSegment->GetName() + "/");
  }
}

void cCDI_GEOMETRY_GENERATOR::Finalize()
{
  // Visit every segment in DFS order within each partition.  Visit those segments'
  // parts and faces as well.  Causes the m_entities list to be written.
  for (auto* partition : m_cdiPartitions.GetPartitions()) {
    VisitSegments(partition->GetRoot(), partition->GetName() + "/");
  }
}
