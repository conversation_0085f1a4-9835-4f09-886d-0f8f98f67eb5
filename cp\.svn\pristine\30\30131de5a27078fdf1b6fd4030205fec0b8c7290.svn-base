/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#ifndef _PARTICLE_SIM_CP_H_
#define _PARTICLE_SIM_CP_H_

#include CP_H   
#include CDI_H

#include "window.h"

typedef class sMONITOR *MONITOR;

typedef class sWIPER {
  asINT32 m_sim_wiper_id;
  std::string m_name;
  BOOLEAN m_start_time_via_monitors_p;
  std::vector<asINT32> m_master_monitors;
  asINT32 m_n_master_monitors_to_check;
  BOOLEAN m_requested_to_start_p;
  TIMESTEP m_requested_start_time;
  TIMESTEP m_start_time;
 public:
  static TIMESTEP start_delay;
 sWIPER(asINT32 id,
        std::string name,
        BOOLEAN start_time_via_monitors_p,
        std::vector<asINT32> monitors,
        asINT32 n_monitors_to_check,
        TIMESTEP start) :
  m_sim_wiper_id(id),
    m_name(name),
    m_start_time_via_monitors_p(start_time_via_monitors_p),
    m_master_monitors(monitors),
    m_n_master_monitors_to_check(n_monitors_to_check),
    m_start_time(start)
    {
      m_requested_to_start_p = FALSE;
      m_requested_start_time = TIMESTEP_MAX;
    }
  ~sWIPER(VOID);

  asINT32 id() { return m_sim_wiper_id; }
  BOOLEAN start_via_monitors_p() { return m_start_time_via_monitors_p; }
  VOID unset_start_via_monitors_p() { m_start_time_via_monitors_p = FALSE; }
  std::vector<asINT32>& monitors() { return m_master_monitors; }
  asINT32 monitors_to_check() { return m_n_master_monitors_to_check; }
  BOOLEAN check_if_should_start(asINT32 monitor_to_remove) {return FALSE;};
  BOOLEAN is_requested_to_start() { return m_requested_to_start_p; }
  BOOLEAN is_scheduled_to_start() { return m_start_time < TIMESTEP_MAX; }
  TIMESTEP get_requested_start_time() { return m_requested_start_time; }
  TIMESTEP get_start_time() { return m_start_time; }
  void set_start_time(TIMESTEP start);
  void remove_dummy_monitors(sCDI_WIPR& cdi_wiper, std::map<asINT32, asINT32>& cdi_monitor_index_to_sim_index); 
  void remove_control_monitor(MONITOR monitor);
  void request_to_start(TIMESTEP start);  // Request the simulator (cp and sps) to start emitter at start time
} *WIPER;




typedef class sEMITTER
{
  asINT32 m_sim_emitter_id;
  std::string m_name;
  BOOLEAN m_start_time_via_monitors_p;
  std::vector<asINT32> m_master_monitors;
  asINT32 m_n_master_monitors_to_check;
  BOOLEAN m_requested_to_start_p;
  TIMESTEP m_requested_start_time;
  TIMESTEP m_start_time;
  BOOLEAN m_end_time_via_duration_p;
  TIMESTEP m_duration;
  TIMESTEP m_end_time;
  
public:
  static TIMESTEP start_delay;
  sEMITTER(asINT32 id,
           std::string name,
           BOOLEAN start_time_via_monitors_p,
           std::vector<asINT32> monitors,
           asINT32 n_monitors_to_check,
           TIMESTEP start,
           BOOLEAN end_time_via_duration_p,
           TIMESTEP duration,
           TIMESTEP end) :
           m_sim_emitter_id(id),
           m_name(name),
           m_start_time_via_monitors_p(start_time_via_monitors_p),
           m_master_monitors(monitors),
           m_n_master_monitors_to_check(n_monitors_to_check),
           m_start_time(start),
           m_end_time_via_duration_p(end_time_via_duration_p),
           m_duration(duration),
           m_end_time(end)
  {
    if (!m_end_time_via_duration_p)
      m_duration = 0;
    m_requested_to_start_p = FALSE;
    m_requested_start_time = TIMESTEP_MAX;
  }

  ~sEMITTER(VOID);
  asINT32 id() { return m_sim_emitter_id; }
  BOOLEAN start_via_monitors_p() { return m_start_time_via_monitors_p; }
  VOID unset_start_via_monitors_p() { m_start_time_via_monitors_p = FALSE; }

  BOOLEAN end_via_duration_p() { return m_end_time_via_duration_p; }
  std::vector<asINT32>& monitors() { return m_master_monitors; }
  asINT32 monitors_to_check() { return m_n_master_monitors_to_check; }
  BOOLEAN check_if_should_start(asINT32 monitor_to_remove) {return FALSE;};
  BOOLEAN is_requested_to_start() { return m_requested_to_start_p; }
  BOOLEAN is_scheduled_to_start() { return m_start_time < TIMESTEP_MAX; }
  TIMESTEP get_requested_start_time() { return m_requested_start_time; }
  TIMESTEP get_start_time() { return m_start_time; }
  void set_start_time(TIMESTEP start);
  TIMESTEP get_end_time() { return m_end_time; }

  void remove_dummy_monitors(sCDI_PARTICLE_EMITTER_BASE &cdi_emitter, std::map<asINT32, asINT32>& cdi_monitor_index_to_sim_index); 
  void remove_control_monitor(MONITOR monitor);
  
  void request_to_start(TIMESTEP start);  // Request the simulator (cp and sps) to start emitter at start time
} *EMITTER;

typedef class sCP_PARTICLE_SIM {
 private:

  //Conversion factor from LatticeTimeInc to LatticeTime needed to correct CDI parameters and measurements.
  sPARTICLE_VAR m_lattice_time_correction; 
  sPARTICLE_VAR m_one_over_lattice_time_correction; 
  
  //For virtual wipers:
  std::vector<sCDI_WIPR> m_cdi_simple_wiper_models;

  //CDI data stored incase the data needs to be written to a PMR file:
  sCDI_PGLB m_cdi_particle_global_parameters;
  cCDI_EMITTER_CONFIGURATIONS m_cdi_emitter_configurations;
  cCDI_PARTICLE_EMITTERS m_cdi_particle_emitters;
  sCDI_ACCR *m_ice_accretion_parameters;
  std::vector<sCDI_PRMT> m_cdi_particle_materials;
  std::vector<sCDI_SRMI> m_cdi_surface_interaction_parameters;
  std::vector<std::string> m_cdi_surface_interaction_names;
  std::vector<sCDI_SCRN> m_cdi_particle_screens;
  std::vector<asINT32> m_face_index_to_screen_index_map;
  std::vector<sCDI_SCMT> m_cdi_conduction_solid_materials;
  friend struct sCP_CDI_READER;

 public:

  VOID compute_lattice_time_correction_factor(UNITS_DB units_db);
  sPARTICLE_VAR lattice_time_correction() {return m_lattice_time_correction;}
  sPARTICLE_VAR one_over_lattice_time_correction() {return m_one_over_lattice_time_correction;}
  CDI_PGLB cdi_particle_global_parameters() {return &m_cdi_particle_global_parameters;}
  cCDI_EMITTER_CONFIGURATIONS* cdi_emitter_configurations() {return &m_cdi_emitter_configurations;}
  cCDI_PARTICLE_EMITTERS* cdi_particle_emitters() {return &m_cdi_particle_emitters;}

  std::vector<sCDI_WIPR>* cdi_wiper_models() {return &m_cdi_simple_wiper_models;}
  std::vector<sCDI_PRMT>* cdi_particle_materials() {return &m_cdi_particle_materials;}
  std::vector<sCDI_SCMT>* cdi_conduction_solid_materials() {return &m_cdi_conduction_solid_materials;}
  std::vector<sCDI_SRMI>* cdi_surface_interaction_parameters() {return &m_cdi_surface_interaction_parameters;}
  std::vector<std::string>* cdi_surface_interaction_names() {return &m_cdi_surface_interaction_names;}
  std::vector<sCDI_SCRN>* cdi_particle_screens() {return &m_cdi_particle_screens;}
  std::vector<asINT32>* face_index_to_screen_index_map() {return &m_face_index_to_screen_index_map;}
  sCP_PARTICLE_SIM(VOID);
  ~sCP_PARTICLE_SIM(VOID);

  VOID initialize(VOID);
  VOID uninitialize(VOID);
  VOID wait_for_sps_to_build_stencils();
  VOID maybe_append_experimental_particle_var_types();
  VOID maybe_append_rime_ice_var_types();
  VOID maybe_append_erosion_var_types();
  VOID maybe_append_thermal_particle_var_types();
  VOID set_experimental_particle_var_descriptors(SRI_VARIABLE_DESC descriptor);
 
  VOID expand_particle_measurements();
  VOID maybe_append_particle_population_size_measurement_var_types();
  VOID strip_particle_modeling_meas_variables();
  VOID broadcast_vertices();

public:

  std::vector<WIPER> wipers;
  std::map< TIMESTEP, std::vector<asINT32> > wipers_requested_to_start;

  std::vector<EMITTER> emitters;
  std::map< TIMESTEP, std::vector<asINT32> > emitters_requested_to_start;

  void dump_emitters_to_start(); // for debug
  void dump_wipers_to_start();
} *CP_PARTICLE_SIM;

extern sCP_PARTICLE_SIM cp_particle_sim;

#endif
