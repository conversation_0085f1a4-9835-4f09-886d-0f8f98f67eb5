/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
#include <algorithm>
#include "jobctl.h"
#include "cp_info.h"
#include "window.h"
#include <dirent.h>
#include LGI_H
#include CDI_H

#include AME_MODEL_H
#include CIPHER_H

#define ucmd(cmd)						\
{								\
  if ((units_status = (cmd)) != UNITS_STATUS_OK) {              \
    msg_error("UNITS failure code: %d\n",units_status);         \
    return;                                                     \
  }                                                             \
}

static BOOLEAN create_directory(const char * dirname)
{
  if (platform_file_present(dirname)) {
    BOOLEAN success;
    // Deleting a directory has been known to fail somewhat arbitrarily - hence try multiple times
    int n_tries = 20;
    do {
      success = platform_remove_dir(dirname);
    } while (!success && (n_tries-- > 0));
    if (!success) { 
      msg_error("Unable to remove %s subdirectory. Please check permissions in the run directory.", dirname);
    } 
  }
  // Creating a directory has been known to fail somewhat arbitrarily - hence try multiple times
  int success;
  int n_tries = 20;
  do {
    success = platform_createdir(dirname);
  } while (!success && (n_tries-- > 0));

  if (!success) {
    msg_error("Unable to create %s subdirectory, please check permissions in the run directory", dirname);
    return FALSE;
  }

  return TRUE;
}

VOID remove_future_lines_from_powercool_results_file(cSTRING results_filename)
{
  FILE *results_file = fopen(results_filename, "r+");
  if (!results_file) {
    msg_warn("Unable to open PowerCOOL output file \"%s\": %s", results_filename, strerror(errno));
    return;
  }

  const int MAX_LINE_LENGTH = 512;  // Maximal length of a line in PowerCOOL results file
  char line[MAX_LINE_LENGTH];

  TIMESTEP restart_time = cp_info.restart_time;
  long truncate_file_offset = 0;
  
  // Advance file pointer to where we should truncate the file
  while (fgets(line, MAX_LINE_LENGTH, results_file)) {
    // Skip lines starting with "#"
    if (line[0] == '#')
      continue;
    
    int timestep, iteration;
    asINT32 n_matched = sscanf(line, "%d %d", &iteration, &timestep);

    if (n_matched < 2)
      // We encountered on a non-comment line that did not have the frame and timestep entries
      continue;
    if (timestep > restart_time) {
      msg_print("Creating a backup copy of \"%s\" in \"%s.bak\" before deleting lines written after"
                " the time the checkpoint file was written (%d timesteps).",
                results_filename, results_filename, cp_info.restart_time);
      char cmd[2 * PLATFORM_MAXPATHLEN + 30];
      sprintf(cmd, "cp \"%s\" \"%s.bak\"", results_filename, results_filename);
      system(cmd);
      ftruncate(fileno(results_file), truncate_file_offset);
      break;
    }
    truncate_file_offset = ftellf(results_file);
  }

  fclose(results_file);
}

VOID convert_hxch_name_slash_to_underscore(STRING old_name, STRING new_name)
{
  while (*old_name != '\0') {
    if (*old_name == '/')
      *new_name = '_';
    else
      *new_name = *old_name;
    new_name++;
    old_name++;
  }
  *new_name = '\0';
}

/*@fcn convert_hxch_part_name_to_base_assembly_name
 *In the hierarchical partitions world, users can assign heat exchanger parts
 *via any partition and segment. However in TOPTANK, during coupling, we attempt 
 *to match the HXCH part name against that in the measurement file. The part names
 *in the measurement file are always written with respect to the base assembly.
 *Therefore to be consistent with the measurement files for a successful coupling,
 *we convert the heat exchanger part name to the base assembly path here.*/
VOID convert_hxch_part_name_to_base_assembly_name(char*& old_name)
{
  if (!cp_info.m_partitions_info.is_pre_heirarchical_partitions_cdi()) {

    cCDI_GEOMETRY_REF geomRef(cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
    std::vector<cdiINT32> part_indices;

    if (cdi_get_geometry_by_path(cp_info.partitions(), old_name, &geomRef)){
      part_indices = geomRef.ExpandSelection(cp_info.partitions(),
                                             cCDI_GEOMETRY_REF::eGEOMETRY_TYPE::Part);
    }

    if (part_indices.empty() || (part_indices.size() > 1)) {
      msg_internal_error("Unable to match heat exchanger part name %s, against a unique part in the Base Assembly",
                         old_name);
    } else {
      auto part_index = part_indices[0];
      auto base_assembly_part_name = cp_info.partitions().GetPartName(part_index);
      size_t old_string_len = strlen(old_name);
      size_t new_string_len = base_assembly_part_name.length();
      //If the new string length is shorter, just overwrite existing mem location
      //If not reallocate
      if (new_string_len > old_string_len) {
        exa_free(old_name);
        old_name = new char[new_string_len + 1]; //plus one for null char
      }
      strncpy(old_name, base_assembly_part_name.c_str(), new_string_len);
      old_name[new_string_len] = '\0';
    }
  }
}

static VOID write_condenser_coupling(BOOLEAN is_from_ckpt)
{
  // assuming by this point the units database is loaded in and the cdsr
  // cpinfo vector is filled.
  FILE *cdsr_inp;
  char input_filename[PLATFORM_MAXPATHLEN];
  char condenser_filename[PLATFORM_MAXPATHLEN];
  char Pflow_table_file[PLATFORM_MAXPATHLEN];
  char app_name[PLATFORM_MAXPATHLEN];
  char redirect_file[PLATFORM_MAXPATHLEN];
  int rval;

  UNITS_UNIT mks_length_unit,mks_T_unit,mks_power_unit,mks_massflow_unit,mks_area_unit,mks_pressure_unit;
  UNITS_UNIT lattice_length_unit,lattice_T_unit,lattice_power_unit,lattice_massflow_unit,lattice_area_unit,lattice_pressure_unit;
  UNITS_STATUS units_status;
  double L_slope,L_offset,T_slope,T_offset,Q_slope,Q_offset,M_slope,M_offset,area_slope,area_offset,pressure_slope,pressure_offset;
  if (cp_info.n_cdsrs == 0) 
    return;

  cp_info.cdsr_cmd_strings = cnew char* [cp_info.n_cdsrs];
  if (!cp_info.units_db) {
    msg_error("Units database not available for launching heat exchanger simulation");
    return;
  } else {
    ucmd(units_parse_unit(cp_info.units_db, "m", &mks_length_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticeLength", &lattice_length_unit));
    ucmd(units_parse_unit(cp_info.units_db, "degC", &mks_T_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticeTemperature", &lattice_T_unit));
    ucmd(units_parse_unit(cp_info.units_db, "kwatt", &mks_power_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticePower", &lattice_power_unit));
    ucmd(units_parse_unit(cp_info.units_db, "bar", &mks_pressure_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticeStaticPressure", &lattice_pressure_unit));
    ucmd(units_parse_unit(cp_info.units_db, "kg/s", &mks_massflow_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticeMassFlow", &lattice_massflow_unit));
    ucmd(units_parse_unit(cp_info.units_db, "m^2", &mks_area_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticeArea", &lattice_area_unit));
  }

  // convert from lattice units to degC mks units 
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_T_unit,mks_T_unit,&T_slope,&T_offset));
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_length_unit,mks_length_unit,&L_slope,&L_offset));
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_power_unit,mks_power_unit,&Q_slope,&Q_offset));
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_pressure_unit,mks_pressure_unit,&pressure_slope,&pressure_offset));
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_massflow_unit,mks_massflow_unit,&M_slope,&M_offset));
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_area_unit,mks_area_unit,&area_slope,&area_offset));

  CDI_CDSR cdsr;
  ccDOTIMES (i,cp_info.n_cdsrs) {
    cp_info.cdsr_cmd_strings[i] = cnew char [PLATFORM_MAXPATHLEN];
    cdsr = cp_info.cdi_cdsrs[i];
    if (i == 0) {
      // should work on most UNIX systems
      if (cdsr->tool == 0) {
        strcpy(app_name,"powercool");
        if (!is_from_ckpt && !cp_info.powercool_dir_created_p) {
          // heat exchangers, if present would have already created a
          // powercool directory if applicable for this simulation. 
          cp_info.powercool_dir_created_p = create_directory("powercool");
        }
      } else {
        msg_error("Unknown condenser solver, only PowerCOOL supported currently");
      }
    }
    // if the condenser name is very long, cannot create filenames, could truncate,
    // but that could conflict with some other long named hx, so err out
    if (strlen(cdsr->name) > PLATFORM_MAXPATHLEN - 64) {
      msg_error("Name of condenser too long to accommodate in path \"%s\"", cdsr->name);
    }

    char clean_hx_name[PLATFORM_MAXPATHLEN];
    convert_hxch_name_slash_to_underscore ( cdsr->name, clean_hx_name );
    rval = sprintf(input_filename,"%s/%s.inp", app_name, clean_hx_name);
    if (rval == -1 || rval >= PLATFORM_MAXPATHLEN) {
      msg_error("condenser input filename too long or corrupted %s",input_filename);
    }
    // create powercool input file
    if ((cdsr_inp = fopen(input_filename,"w")) == NULL) {
      msg_error("Unable to create condenser input file %s, please check permissions in the run directory",input_filename);
    }
    // launch the command as powercool_cp powercool/<hxname>.inp
    // the powercool_cp script calls the generic powercool script
    // need the wrapper in case a -tcfile argument is specified

    if (!getenv("EXA_DIST")) {
      msg_error("EXA_DIST environment variable unavailable to launch hx coupling tool");
    }
    // -env_table: pick up table filename from cp supplied env
    rval =  sprintf(cp_info.cdsr_cmd_strings[i],
		    "%s/dist/generic/scripts/%s_cp %d \"%s\" -condenser -env_table -name \"%s\" ",
		    getenv("EXA_DIST"),app_name,getpid(),input_filename,cdsr->name);
    if (rval == -1 || rval >= PLATFORM_MAXPATHLEN) {
      msg_error("condenser command string too long or corrupted %s",cp_info.cdsr_cmd_strings[i]);
    }

    // start writing the input file for powercool
    fprintf(cdsr_inp,"%s\t\t%d\t  #%s\n","NPasses",cdsr->n_passes,"n_passages: number of subsequent sections in the core of the device");
    fprintf(cdsr_inp,"%s\t\t","Ntubes");
    asINT32 n_z_divisions = 0, n_y_divisions = 0;
    ccDOTIMES(j,cdsr->n_passes) {
     fprintf(cdsr_inp," %d",cdsr->n_tubes[j]);
     n_z_divisions += cdsr->n_tubes[j];
    }
    n_y_divisions = MAX(50,n_z_divisions);
    fprintf(cdsr_inp,"\t  #%s\n","number of tubes rows in each passage, starting from inlet of hot vapor");

    fprintf(cdsr_inp,"%s\t\t%d\t  #%s\n","Maxi",n_y_divisions,"maxi: length, segments, of the active row of matrix, i.e. 1 to 20, elements 0 and 21 used as BC");
    fprintf(cdsr_inp,"%s\t\t%d\t  #%s\n","Maxk",n_z_divisions,"maxk: number of rows in the matrix = length of column");
    fprintf(cdsr_inp,"%s\t\t%lf\t  #%s\n","Heat_Rejected",Q_offset+Q_slope*cdsr->heat_rejection,"Q_air, kW: time-average for heat rejected by the core of the condenser to air");
    fprintf(cdsr_inp,"%s\t\t%lf\t  #%s\n","Entry_Temp",(cdsr->entry_temp > 0.0)? T_offset+T_slope*cdsr->entry_temp : -1.0,"T of compressed refrigerant vapor (+ oil) injected into the core from compressor");

    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","Length",L_offset+L_slope*cdsr->y_len,"condenser_length, m: measured along the floor, normal to the car axis");
    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","Height",L_offset+L_slope*cdsr->z_len,"condenser_height, m: measured upward normally to the floor");
    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","Depth",L_offset+L_slope*cdsr->x_len,"condenser_depth (thickness of the finned core), m: measured along the car axis");
    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","Flow_Rate",(cdsr->mass_flow_rate > 0.0)? M_offset+M_slope*cdsr->mass_flow_rate : -1.0,"refrigerant flow rate, kg/s");

    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","Kc",cdsr->kc_coeff,"Kc: best-fit-to-experiment constant in sandwich formula");
    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","Alpha",cdsr->alpha_coeff,"Alpha: turbulent constant in sandwich formula");
    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","D",cdsr->d_coeff,"D: best-fit-to-experiment constant in sandwich formula");
    fprintf(cdsr_inp,"%s\t\t%d  #%s\n","RefType",cdsr->type,"Refrigerant type (0 = R134a, 1 = Freon, 2 = Ammonia");
    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","Cp_air",cdi_data.specific_heat,"Cp_air, J/kg/degK: mean heat capacity of the external air passing through the CAC");
    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","RefPinlet",(cdsr->entry_pressure > 0.0)? pressure_offset+pressure_slope*cdsr->entry_pressure : -1.0,"bar, inlet pressure in the refrigerant vapor in the tubes of the entry passage");
    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","RefPdrop",(cdsr->pressure_drop > 0.0)? pressure_offset+pressure_slope*cdsr->pressure_drop : -1.0,"bar, pressure drop in the refrigerant from inlet to the outlet");
    // TSubcool is actually a temperature difference. Thus we only use T_slope in the conversion 
    // below and not T_offset. T_slope will be the same for degC and degK.
    fprintf(cdsr_inp,"%s\t\t%lf  #%s\n","TSubcool",(cdsr->experiment_exit_temp > 0.0)? T_slope*cdsr->experiment_exit_temp : -1.0," deg C, subcooling temperature at exit, relative to condensation: mean value at lab testing");

    fprintf(cdsr_inp,"%s\t%d\t  #%s\n","Pflow_inp",1,"Read data from PowerFLOW measurement files");

    fprintf(cdsr_inp,"%s\t%lf  #%s\n","Air_Mdot_min",(cdsr->min_air_flow > 0.0)? M_offset+M_slope*cdsr->min_air_flow : -1.0,"Minimum air mass flow in experimental HTC data");
    fprintf(cdsr_inp,"%s\t%lf  #%s\n","Air_Mdot_max",(cdsr->max_air_flow > 0.0)? M_offset+M_slope*cdsr->max_air_flow : -1.0,"Maximum air mass flow in experimental HTC data");

    if (cdsr->medium_csys_index >= 0) {
      fprintf(cdsr_inp,"%s\t%lf %lf %lf  #%s\n","A_csys_r",
              cp_info.all_csys->coord_systems[cdsr->medium_csys_index].g_to_l_xform[0][0],
              cp_info.all_csys->coord_systems[cdsr->medium_csys_index].g_to_l_xform[0][1],
              cp_info.all_csys->coord_systems[cdsr->medium_csys_index].g_to_l_xform[0][2],
              "Air velocity direction specified by the user");
    }

    fprintf(cdsr_inp,"%s\t%d\t  #%s\n","FI_offset",cdsr->inlet_facet_offset,"CDI facet offset for inlet measurement window");
    fprintf(cdsr_inp,"%s\t%d\t  #%s\n","FO_offset",cdsr->outlet_facet_offset,"CDI facet offset for outlet measurement window");
    // only support .snc type of sampling surface measurement files for hx
    fprintf(cdsr_inp,"imeas_file\t %s.snc  #%s\n",(cp_info.cdi_meas_windows[cdsr->inlet_meas_index]).name,"Input inlet measurement file");
    fprintf(cdsr_inp,"omeas_file\t %s.snc  #%s\n",(cp_info.cdi_meas_windows[cdsr->outlet_meas_index]).name,"Input outlet measurement file");
    fprintf(cdsr_inp,"companion_file\t %s/%s.ncc  #%s\n",app_name,(cp_info.cdi_meas_windows[cdsr->inlet_meas_index]).name,"Inlet measurement companion file to write data to");
    fprintf(cdsr_inp,"debug_file\t %s/%s_debug_fields.txt  #%s\n",app_name,clean_hx_name,"Debug data output file");
    fprintf(cdsr_inp,"results_file\t %s/%s.out  #%s\n",app_name,clean_hx_name,"Tabular results output file");
    fprintf(cdsr_inp,"imeas_dump_file %s/%s_meas.dat  #%s\n",app_name,clean_hx_name,"Data dump file for measurement values");
    if (cdsr->heat_gen_meas_index >= 0)
      fprintf(cdsr_inp,"hmeas_file\t %s.cpnc  #%s\n",(cp_info.cdi_meas_windows[cdsr->heat_gen_meas_index]).name,"Composite measurement file with heat generation");
    fprintf(cdsr_inp,"part_name %s\t  #%s\n",cdsr->part_name,"Condenser part name (usually same as condenser name)");

    fclose(cdsr_inp);

    // print out the header information to the PowerCOOL output file at the 
    // beginning of the simulation, results will be appended for each timestep 
    // by PowerCOOL, everytime it is run

    // Find the hxch in all_hx_info
    sINT32 hx_index = -1;
    ccDOTIMES(hx, cp_info.all_hx_info.size()) {
      if (cp_info.all_hx_info[hx].type == CDI_CHUNK_TYPE_CDSR && cp_info.all_hx_info[hx].index == i) {
        hx_index = hx;
        break;
      }
    }
    if (hx_index < 0) {
      msg_internal_error("Cannot find the condenser \"%s\" in all_hx_info", cdsr->name);
    } else {
      cp_info.all_hx_info[hx_index].old_pos = 0;
    }
    cSTRING results_filename = cp_info.all_hx_info[hx_index].results_filename;

    if (!is_from_ckpt) {
      FILE *fout = fopen(results_filename, "w");
      if (!fout)
        msg_error("Unable to create condenser output file \"%s\", please check permissions",results_filename);
      fprintf(fout,"# Results for PowerCOOL simulation of Condenser %s \n",cdsr->name);
      fprintf(fout,"# Iteration Timestep T_refg_entry(C) T_refg_exit(C) Refg_Mass_Flow(kg/s) Heat_rejected(kW) T_air_entry_mean(C) T_air_inlet_mean(C) T_air_outlet_mean(C) Air_Mass_Flow(kg/s) Num_PCOOL_iter PCOOL_Residual Air_flow_in(CFM) Air_flow_out(CFM)\n");
      fclose(fout);
    } 

    // dump the stdout from powercool into a powercool/<hxname>.log file
    rval = sprintf(redirect_file,"\"%s/%s.log\"",app_name,clean_hx_name);
    if (rval == -1 || rval >= PLATFORM_MAXPATHLEN) {
      msg_error("Condenser output filename too long or corrupted %s",redirect_file);
    }

#if 0
    if (!is_from_ckpt) {
      if (platform_remove_file(redirect_file)) {
        msg_error("Cannot remove condenser log file %s",redirect_file);
      }
    }
#endif

    if (strlen(cp_info.cdsr_cmd_strings[i])+strlen(redirect_file) > PLATFORM_MAXPATHLEN-4) {
      msg_error("Condenser command string too long");
    } else {
      strcat(strcat(cp_info.cdsr_cmd_strings[i]," >> "),redirect_file);
    }
    // replace the table command string with the one newly constructed
    cp_info.table_descs[cdsr->table_index].cmd_string = cp_info.cdsr_cmd_strings[i];
    cp_info.cdi_meas_windows[cdsr->inlet_meas_index].flush_every_frame = TRUE;
    cp_info.cdi_meas_windows[cdsr->outlet_meas_index].flush_every_frame = TRUE;
    if (cdsr->heat_gen_meas_index >= 0)
      cp_info.cdi_meas_windows[cdsr->heat_gen_meas_index].flush_every_frame = TRUE;
  }
  return;
}

static VOID write_powercool_heat_exchanger_coupling(BOOLEAN is_from_ckpt)
{
  FILE *hxch_inp;
  char input_filename[PLATFORM_MAXPATHLEN];
  char htc_filename[PLATFORM_MAXPATHLEN];
  char Pflow_table_file[PLATFORM_MAXPATHLEN];
  char tc_filename[PLATFORM_MAXPATHLEN];
  char app_name[PLATFORM_MAXPATHLEN];
  char redirect_file[PLATFORM_MAXPATHLEN];
  int rval;

  UNITS_UNIT mks_length_unit,mks_T_unit,mks_power_unit,mks_massflow_unit,mks_area_unit;
  UNITS_UNIT lattice_length_unit,lattice_T_unit,lattice_power_unit,lattice_massflow_unit,lattice_area_unit;
  UNITS_STATUS units_status;
  double L_slope,L_offset,T_slope,T_offset,P_slope,P_offset,M_slope,M_offset,area_slope,area_offset;
  if (cp_info.n_hxchs == 0) 
    return;
  
  if (!cp_info.units_db) {
    msg_error("Units database not available for launching heat exchanger simulation");
    return;
  } else {
    ucmd(units_parse_unit(cp_info.units_db, "m", &mks_length_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticeLength", &lattice_length_unit));
    ucmd(units_parse_unit(cp_info.units_db, "degC", &mks_T_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticeTemperature", &lattice_T_unit));
    ucmd(units_parse_unit(cp_info.units_db, "kwatt", &mks_power_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticePower", &lattice_power_unit));
    ucmd(units_parse_unit(cp_info.units_db, "kg/s", &mks_massflow_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticeMassFlow", &lattice_massflow_unit));
    ucmd(units_parse_unit(cp_info.units_db, "m^2", &mks_area_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticeArea", &lattice_area_unit));
  }

  // convert from lattice units to degC mks units 
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_T_unit,mks_T_unit,&T_slope,&T_offset));
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_length_unit,mks_length_unit,&L_slope,&L_offset));
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_power_unit,mks_power_unit,&P_slope,&P_offset));
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_massflow_unit,mks_massflow_unit,&M_slope,&M_offset));
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_area_unit,mks_area_unit,&area_slope,&area_offset));

  strcpy(app_name,"powercool");

  CDI_HXCH hxch;
  ccDOTIMES (i,cp_info.n_hxchs) {

    hxch = cp_info.cdi_hxchs[i];
    if (hxch->tool != 0)
      msg_internal_error("Unknown heat exchanger solver, only PowerCOOL and AMESim supported currently");

    cp_info.hxch_cmd_strings[i] = cnew char [PLATFORM_MAXPATHLEN];
    
    // should work on most UNIX systems
    if (!is_from_ckpt && !cp_info.powercool_dir_created_p) {
      cp_info.powercool_dir_created_p = create_directory("powercool");
    }
    
    // if the hx name is very long, cannot create filenames, could truncate,
    // but that could conflict with some other long named hx, so err out
    if (strlen(hxch->name) > PLATFORM_MAXPATHLEN - 64) {
      msg_error("Name of heat exchanger too long to accommodate in path \"%s\"", hxch->name);
    }
    char clean_hx_name[PLATFORM_MAXPATHLEN];
    convert_hxch_name_slash_to_underscore ( hxch->name, clean_hx_name );
    rval = sprintf(input_filename,"%s/%s.inp", app_name, clean_hx_name);
    if (rval == -1 || rval >= PLATFORM_MAXPATHLEN) {
      msg_error("heat exchanger input filename too long or corrupted %s",input_filename);
    }
    // create powercool input file
    if ((hxch_inp = fopen(input_filename,"w")) == NULL) {
      msg_error("Unable to create heat exchanger input file %s, please check permissions in the run directory",input_filename);
    }
    // launch the command as powercool_cp powercool/<hxname>.inp
    // the powercool_cp script calls the generic powercool script
    // need the wrapper in case a -tcfile argument is specified

    if (!getenv("EXA_DIST")) {
      msg_error("EXA_DIST environment variable unavailable to launch hx coupling tool");
    }
    // -env_table: pick up table filename from cp supplied env

    rval =  sprintf(cp_info.hxch_cmd_strings[i],
                    "%s/dist/generic/scripts/%s_cp %d \"%s\" -env_table -name \"%s\" ",
                    getenv("EXA_DIST"),app_name,getpid(),input_filename,hxch->name);
    
    if (rval == -1 || rval >= PLATFORM_MAXPATHLEN) {
      msg_error("heat exchanger command string too long or corrupted %s",cp_info.hxch_cmd_strings[i]);
    }

    // start writing the input file for powercool
    fprintf(hxch_inp,"%s\t\t%d\t  #%s\n","Itype",hxch->n_stages-1,"Num passes in radiator or CAC: 0==I-type; 1==U-type; 2==3-pass, etc.");
    //fprintf(hxch_inp,"%s\t\t%d\t  #%s\n","NPasses",hxch->n_stages,"n_passages: number of subsequent sections in the core of the device");

    fprintf(hxch_inp,"%s\t\t","Ntubes");
    asINT32 n_z_divisions = 0;
    ccDOTIMES(j,hxch->n_stages) {
     fprintf(hxch_inp," %d",hxch->n_pass_divisions[j]);
     n_z_divisions += hxch->n_pass_divisions[j];
    }
    fprintf(hxch_inp,"\t  #%s\n","number of rows in each pass, starting from entry point of inner fluid");

    fprintf(hxch_inp,"%s\t\t%d\t  #%s\n","Maxi",hxch->n_y_divisions,"maxi: length, segments, of the active row of matrix, i.e. 1 to 20, elements 0 and 21 used as BC");
    fprintf(hxch_inp,"%s\t\t%d\t  #%s\n","Maxk",n_z_divisions,"maxk: number of rows in the matrix = length of column");
    fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Mdotc",M_offset+M_slope*hxch->mass_flow_rate,"inner_flow_rate, kg/s: liquid coolant in the radiator, or compressed air in CAC");
    if (hxch->experiment_ref_temp < 0.0) {
      fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Tlab",25.0,"degC, mean T of the coolant in hx in the lab of manufacturer at testing (usually, 80-90C)");
    } else {
      fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Tlab",T_offset+T_slope*hxch->experiment_ref_temp,"deg C, mean T of liquid in tubes in the lab characterization of the HX");
    }
    fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Cpc",hxch->specific_heat,"Cpc, J/kg/degK: heat capacity of the coolant in the heat exchanger");
    fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Cp_air",cdi_data.specific_heat,"Cp_air, J/kg/degK: mean heat capacity of the external air passing through the CAC");

    fprintf(hxch_inp,"%s\t%lf  #%s\n","Exp_Area",area_offset+area_slope*hxch->experiment_area,"m^2: area used for experimental HTC table");
    fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Width",L_offset+L_slope*hxch->y_len,"radiator_length, m: measured along the floor, normal to the car axis");
    fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Depth",L_offset+L_slope*hxch->x_len,"radiator_depth (thickness of active, finned, part), m: measured along the car axis");
    fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Height",L_offset+L_slope*hxch->z_len,"radiator_height, m: measured upward normally to the floor");
    fprintf(hxch_inp,"%s\t%lf  #%s\n","Lower_Height",L_offset+L_slope*hxch->z_len*double(hxch->n_pass_divisions[0])/double(n_z_divisions)," m: size of the section of the heat exchanger connected to inner fluid entry point");
    if (hxch->experimental_width > 0) {
      fprintf(hxch_inp,"%s\t%lf  #%s\n","Tested_width",hxch->experimental_width," m: width of lab-tested heat exchanger, size in direction of internal fluid flow");
    }
    if (hxch->experimental_height > 0) {
      fprintf(hxch_inp,"%s\t%lf  #%s\n","Tested_height",hxch->experimental_height," m: height of lab-tested heat exchanger, size in direction orthogonal to internal fluid flow and external cooling air flow");
    }
    if (hxch->experimental_depth > 0) {
      fprintf(hxch_inp,"%s\t%lf  #%s\n","Tested_depth",hxch->experimental_depth," m: depth of lab-tested heat exchanger, measured in direction external cooling air flow");
    }
    if (hxch->exp_coolant_viscosity > 0) {
      fprintf(hxch_inp,"%s\t%lf  #%s\n","Lab_inner_dynamic_viscosity",hxch->exp_coolant_viscosity," Dynamic viscosity of coolant used in lab testing Pa*sec");
    }
    if (hxch->exp_coolant_thermal_conductivity > 0) {
      fprintf(hxch_inp,"%s\t%lf  #%s\n","Lab_inner_thermal_conductivity",hxch->exp_coolant_thermal_conductivity," Thermal conductivity of coolant used in lab testing W/(m*degK)");
    }
    if (hxch->exp_coolant_sp_heat > 0) {
      fprintf(hxch_inp,"%s\t%lf  #%s\n","Cpc_lab",hxch->exp_coolant_sp_heat,"  specific heat of coolant used in lab testing J/(kg*degK)");
    }

    if (hxch->upstream_index == -1) { // otherwise picks up temp from upstream output file
      if (hxch->flags & CDI_HXCH_USE_HEAT_REJECTION) {
	if ((P_offset+P_slope*hxch->heat_rejection) <= 0.0 ) {
	  msg_error("Must specify positive heat rejection for \"%s\"",hxch->name);
	}
	fprintf(hxch_inp,"%s\t%lf  #%s\n","P_engine",P_offset+P_slope*hxch->heat_rejection,"P_engine, kW: heat rejected by the engine's core (cylinders, bearings)");
      } else {
	if ((T_offset+T_slope*hxch->entry_temp) <= 0.0 ) {
	  msg_error("Must specify coolant entry temperature greater than 0C for \"%s\"",hxch->name);
	}
	fprintf(hxch_inp,"%s\t\t%lf  #%s\n","T_cac",T_offset+T_slope*hxch->entry_temp,"degC: Coolant entry temp or compressed air entry temp for CAC");
      }
    } else {
      char additional_cmd_args[PLATFORM_MAXPATHLEN];
      char upstream_clean_hx_name[PLATFORM_MAXPATHLEN];
      convert_hxch_name_slash_to_underscore ( cp_info.cdi_hxchs[hxch->upstream_index]->name, upstream_clean_hx_name );
      rval = sprintf(additional_cmd_args," -tcfile \"%s/.%s.tc\"", app_name, upstream_clean_hx_name);
      if (rval == -1 || rval >= PLATFORM_MAXPATHLEN || 
	  strlen(additional_cmd_args)+
	  strlen(cp_info.hxch_cmd_strings[i]) >= PLATFORM_MAXPATHLEN) {
        msg_error("heat exchanger command arguments too long or corrupted: \"%s\"", cp_info.hxch_cmd_strings[i]);
      } else {
	strcat(cp_info.hxch_cmd_strings[i],additional_cmd_args);
      }
    }
    if (hxch->flags & CDI_HXCH_USE_COEFFICIENTS) {
      if (((hxch->flags & CDI_HXCH_USE_COEFFICIENTS) == 0) // use Sandwich coefficients or bilinear interpolation ?
        || hxch->data_string == NULL 
        || hxch->data_string[0] == '\0') {

        fprintf(hxch_inp,"%s\t%d\t  #%s\n","Interp_type",0,"1: use bilinear interpolation for HTC; 0: use \"sandwich\" formula");
        fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Kc",hxch->kc_coeff,"Interp_type 0 Kc: best-fit-to-experiment constant in sandwich formula");
        fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Alpha",hxch->alpha_coeff,"Interp_type 0 Alpha: turbulent constant in sandwich formula");
        fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Kh",hxch->kh_coeff,"Interp_type 0 Kh: best-fit-to-experiment constant in sandwich formula");
        fprintf(hxch_inp,"%s\t\t%lf  #%s\n","D",hxch->d_coeff,"Interp_type 0 D: best-fit-to-experiment constant in sandwich formula");
        fprintf(hxch_inp,"%s\t\t%lf  #%s\n","Beta",hxch->beta_coeff,"Interp_type 0 Beta: best-fit-to-experiment constant in sandwich formula");
      } else {  // For denso project, use encrypted numbers
        fprintf(hxch_inp,"%s\t%d\t  #%s\n","Interp_type", -1, "1: use bilinear interpolation for HTC; 0: use \"sandwich\" formula");
        sCIPHER_DOUBLE kc(hxch->kc_coeff, 0), alpha(hxch->alpha_coeff, 1), kh(hxch->kh_coeff, 2), d(hxch->d_coeff, 3), beta(hxch->beta_coeff, 4);
        fprintf(hxch_inp,"%s\t\t%lu  #%s\n","Kc", kc.getInt(), "Interp_type 0 Kc: best-fit-to-experiment constant in sandwich formula");
        fprintf(hxch_inp,"%s\t\t%lu  #%s\n","Alpha", alpha.getInt(), "Interp_type 0 Alpha: turbulent constant in sandwich formula");
        fprintf(hxch_inp,"%s\t\t%lu  #%s\n","Kh", kh.getInt(), "Interp_type 0 Kh: best-fit-to-experiment constant in sandwich formula");
        fprintf(hxch_inp,"%s\t\t%lu  #%s\n","D", d.getInt(), "Interp_type 0 D: best-fit-to-experiment constant in sandwich formula");
        fprintf(hxch_inp,"%s\t\t%lu  #%s\n","Beta", beta.getInt(), "Interp_type 0 Beta: best-fit-to-experiment constant in sandwich formula");
      }
    } else {
      FILE *htc_file;
      fprintf(hxch_inp,"%s\t%d\t  #%s\n","Interp_type",1,"1: use bilinear interpolation for HTC; 0: use \"sandwich\" formula");
      rval = sprintf(htc_filename,"%s/%s_htc.dat", app_name, clean_hx_name);
      if (rval == -1 || rval >= PLATFORM_MAXPATHLEN) {
	msg_error("heat exchanger HTC filename too long or corrupted");
      }
      if (NULL == (htc_file = fopen(htc_filename,"w"))) {
	msg_error("Cannot open file for writing HTC data for heat exchanger \"%s\"", hxch->name);
      }
      fprintf(htc_file,"%s\n",hxch->data_string);
      fclose(htc_file);
      fprintf(hxch_inp,"HTC_file\t%s\t  #%s\n",htc_filename,"htc file for Interp_type 1: used for computing HTC if bilinear formula");
    }
    if (hxch->flags & CDI_HXCH_USE_SUTHERLAND) {
      fprintf(hxch_inp,"%s\t\t%d\t  #%s\n","UTDC",1,"1: Use temperature-dependent corrections for HTC; 0: no corrections");
    } else {
      fprintf(hxch_inp,"%s\t\t%d\t  #%s\n","UTDC",0,"1: Use temperature-dependent corrections for HTC; 0: no corrections"); 
    }
    fprintf(hxch_inp,"%s\t%d\t  #%s\n","Pflow_inp",1,"Read data from PowerFLOW measurement files");
    if (hxch->flags & CDI_HXCH_LATTICE_AIR_MASS_FLOW) {
      fprintf(hxch_inp,"%s\t%lf  #%s\n","Air_Mdot_min",M_offset+M_slope*hxch->min_air_flow,"Minimum air mass flow in experimental HTC data");
      fprintf(hxch_inp,"%s\t%lf  #%s\n","Air_Mdot_max",M_offset+M_slope*hxch->max_air_flow,"Maximum air mass flow in experimental HTC data");
    } else {
      fprintf(hxch_inp,"%s\t%lf  #%s\n","Air_Mdot_min",hxch->min_air_flow,"Minimum air mass flow in experimental HTC data");
      fprintf(hxch_inp,"%s\t%lf  #%s\n","Air_Mdot_max",hxch->max_air_flow,"Maximum air mass flow in experimental HTC data");
    }
    if (hxch->medium_csys_index >= 0) {
      fprintf(hxch_inp,"%s\t%lf %lf %lf  #%s\n","A_csys_r",
              cp_info.all_csys->coord_systems[hxch->medium_csys_index].g_to_l_xform[0][0],
              cp_info.all_csys->coord_systems[hxch->medium_csys_index].g_to_l_xform[0][1],
              cp_info.all_csys->coord_systems[hxch->medium_csys_index].g_to_l_xform[0][2],
              "Air velocity direction specified by the user");
    }

    fprintf(hxch_inp,"%s\t%d\t  #%s\n","FI_offset",hxch->inlet_facet_offset,"CDI facet offset for inlet measurement window");
    fprintf(hxch_inp,"%s\t%d\t  #%s\n","FO_offset",hxch->outlet_facet_offset,"CDI facet offset for outlet measurement window");
    // only support .snc type of sampling surface measurement files for hx
    fprintf(hxch_inp,"imeas_file\t %s.snc  #%s\n",(cp_info.cdi_meas_windows[hxch->inlet_meas_index]).name,"Input inlet measurement file");
    fprintf(hxch_inp,"omeas_file\t %s.snc  #%s\n",(cp_info.cdi_meas_windows[hxch->outlet_meas_index]).name,"Input outlet measurement file");
    fprintf(hxch_inp,"companion_file\t %s/%s.ncc  #%s\n",app_name,(cp_info.cdi_meas_windows[hxch->inlet_meas_index]).name,"Inlet measurement companion file to write data to");
    fprintf(hxch_inp,"debug_file\t %s/%s_debug_fields.txt  #%s\n",app_name,clean_hx_name,"Debug data output file");
    fprintf(hxch_inp,"estimate_file\t %s/%s.est  #%s\n",app_name,clean_hx_name,"HX estimate output file generated for radiator simulation");
    fprintf(hxch_inp,"results_file\t %s/%s.out  #%s\n",app_name,clean_hx_name,"Tabular results output file");
    fprintf(hxch_inp,"tc_outfile\t %s/.%s.tc  #%s\n",app_name,clean_hx_name,"degC: Coolant exit temperature output file");
    if (hxch->heat_gen_meas_index >= 0)
      fprintf(hxch_inp,"hmeas_file\t %s.cpnc  #%s\n",(cp_info.cdi_meas_windows[hxch->heat_gen_meas_index]).name,"Composite measurement file with heat generation");
    fprintf(hxch_inp,"imeas_dump_file %s/%s_meas.dat  #%s\n",app_name,clean_hx_name,"Data dump file for measurement values");
    fprintf(hxch_inp,"part_name %s\t  #%s\n",hxch->part_name,"Heat exchanger part name (usually same as heat exchanger name)");

    fclose(hxch_inp);

    // print out the header information to the PowerCOOL output file at the 
    // beginning of the simulation, results will be appended for each timestep 
    // by PowerCOOL, everytime it is run
      
    // Find the hxch in all_hx_info
    sINT32 hx_index = -1;
    ccDOTIMES(hx, cp_info.all_hx_info.size()) {
      if (cp_info.all_hx_info[hx].type == CDI_CHUNK_TYPE_HXCH && cp_info.all_hx_info[hx].index == i) {
        hx_index = hx;
        break;
      }
    }
    if (hx_index < 0) {
      msg_internal_error("Cannot find the heat exchanger \"%s\" in all_hx_info", hxch->name);
    } else {
      cp_info.all_hx_info[hx_index].old_pos = 0;
    }
    cSTRING results_filename = cp_info.all_hx_info[hx_index].results_filename;

    if (!is_from_ckpt) {
      FILE *fout = fopen(results_filename,"w");
      if (!fout)
        msg_error("Unable to create heat exchanger output file %s, please check permissions in the run directory",results_filename);
      fprintf(fout,"# Results for PowerCOOL simulation of Heat Exchanger %s \n",hxch->name);
      fprintf(fout,"# Iteration Timestep T_coolant_entry(C) T_coolant_exit(C) Coolant_Mass_Flow(kg/s) Heat_rejected(kW) T_air_entry_mean(C) T_air_inlet_mean(C) T_air_outlet_mean(C) Air_Mass_Flow(kg/s) Num_PCOOL_iter PCOOL_Residual Air_flow_in(CFM) Air_flow_out(CFM)\n");
      fclose(fout);
    } 

    // dump the stdout from powercool into a powercool/<hxname>.log file
    rval = sprintf(redirect_file,"\"%s/%s.log\"",app_name,clean_hx_name);
    if (rval == -1 || rval >= PLATFORM_MAXPATHLEN) {
      msg_error("Heat exchanger output filename too long or corrupted %s",redirect_file);
    }
#if 0
    if (!is_from_ckpt) {
      if (platform_remove_file(redirect_file)) {
	msg_error("Cannot remove heat exchanger log file %s",redirect_file);
      }
    }
#endif

    if (strlen(cp_info.hxch_cmd_strings[i])+strlen(redirect_file) > PLATFORM_MAXPATHLEN-4) {
      msg_error("Heat exchanger command string too long");
    } else {
      strcat(strcat(cp_info.hxch_cmd_strings[i]," >> "),redirect_file);
    }

    // replace the table command string with the one newly constructed
    cp_info.table_descs[hxch->table_index].cmd_string = cp_info.hxch_cmd_strings[i];
    cp_info.cdi_meas_windows[hxch->inlet_meas_index].flush_every_frame = TRUE;
    cp_info.cdi_meas_windows[hxch->outlet_meas_index].flush_every_frame = TRUE;
    if (hxch->heat_gen_meas_index >= 0)
      cp_info.cdi_meas_windows[hxch->heat_gen_meas_index].flush_every_frame = TRUE;
  } 
  return;
}

static void check_amesim_model(cSTRING model_fullpath, cSTRING model_name_ext, cSTRING model_name_no_ext,
                               const CDI_AMHX amhx, double L_offset, double L_slope,
                               cAMESIM_SUBMODEL_INFO &hx, char *model_version)
{
  char init_dir[PLATFORM_MAXPATHLEN];
  sprintf(init_dir, "amesim/%s_init", model_name_no_ext);
  create_directory(init_dir);

  if (chdir(init_dir) != 0)
    msg_internal_error("Failed to cd into \"%s\": %s", init_dir, strerror(errno));

  char model_filename_above[PLATFORM_MAXPATHLEN];
  if (model_fullpath[0] == '/')
    sprintf(model_filename_above, "%s", model_fullpath);
  else
    sprintf(model_filename_above, "../../%s", model_fullpath);

  if (-1 == symlink(model_filename_above, model_name_ext)) {
    msg_warn("Could not create a symbolic link from \"%s\" to \"%s\": %s",
             model_name_ext, model_filename_above, strerror(errno));
  }

  cAME_MODEL_INFO ame_model(model_name_ext);
  if (!ame_model.GetAMESimModelInfo(true)) {
    chdir("../.."); // get out of init directory so it can be removed
    platform_remove_dir(init_dir);
    msg_error("Could not extract information about model \"%s\": %s",
              model_fullpath, ame_model.GetErrorMessage());
  }
  char clean_hx_name[PLATFORM_MAXPATHLEN];
  convert_hxch_name_slash_to_underscore ( amhx->name, clean_hx_name );
  // Move the log file generated by the AME API into a permanent place in <run_dir>/amesim
  char api_log_file[PLATFORM_MAXPATHLEN];
  sprintf(api_log_file, "../%s_ame_api.log", clean_hx_name);
  rename("ame_api.log", api_log_file);

  if (chdir("../..") != 0) {
    msg_internal_error("Failed to cd back to the run directory: %s", strerror(errno));
  }

  platform_remove_dir(init_dir);

  asINT32 model_maj_version = ame_model.CircuitMajorVersionNum();
  asINT32 model_med_version = ame_model.CircuitMediumVersionNum();
  asINT32 api_maj_version = ame_model.CApiMajorVersionNum();
  asINT32 api_med_version = ame_model.CApiMediumVersionNum();
  asINT32 ame_at_exa_maj_version = ame_model.AMESimAtExaMajorVersionNum();
  asINT32 ame_at_exa_med_version = ame_model.AMESimAtExaMediumVersionNum();
  std::string ame_at_exa_version = cAME_MODEL_INFO::GetAMESimVersionName(ame_at_exa_maj_version, ame_at_exa_med_version);

  strcpy(model_version, cAME_MODEL_INFO::GetAMESimVersionName(model_maj_version, model_med_version).c_str());

  if ((model_maj_version == 16 && model_med_version < 1) || model_maj_version < 16 )
    msg_error("%s: AMESim model \"%s\" version (%d) is not supported. Only Amesim 16.1 and above are supported",
              amhx->name, model_fullpath, model_maj_version);

  if ((model_maj_version == ame_at_exa_maj_version && model_med_version > ame_at_exa_med_version) || (model_maj_version > ame_at_exa_maj_version))
    msg_warn("%s: AMESim model \"%s\" version is %s. This version of PowerFLOW is validated with AMESim versions 16.1 to %s",
             amhx->name, model_fullpath,
             model_version, 
             ame_at_exa_version.c_str());

  if ((model_maj_version == api_maj_version && model_med_version > api_med_version) || (model_maj_version > api_maj_version))
    msg_warn("%s: AMESim model \"%s\" version %s is newer than the AMESim installation version (%s)",
             amhx->name, model_fullpath, 
             model_version, 
             ame_at_exa_version.c_str());

  for (int i = 0; i < ame_model.NumRads(); ++i)
    if (strcmp(amhx->amesim_hx_name, ame_model.Radiator(i).Alias()) == 0)
      hx = ame_model.Radiator(i);

  for (int i = 0; i < ame_model.NumCacs(); ++i)
    if (strcmp(amhx->amesim_hx_name, ame_model.Cac(i).Alias()) == 0)
      hx = ame_model.Cac(i);

  for (int i = 0; i < ame_model.NumConds(); ++i)
    if (strcmp(amhx->amesim_hx_name, ame_model.Condenser(i).Alias()) == 0)
      hx = ame_model.Condenser(i);

  // check if grid size match
  bool gridMismatch = false;
  if (amhx->n_y_divisions != hx.WidthDivisions()) {
    msg_warn("%s: Number of divisions along coolant direction (%d) does not match the corresponding AMESim heat exchanger \"%s\" (%d)",
              amhx->name, amhx->n_y_divisions, hx.Alias(), hx.WidthDivisions());
    gridMismatch = true;
  }
  if (amhx->n_pass_divisions[0] != hx.HeightDivisions()) {
    msg_warn("%s: Number of divisions across coolant direction (%d) does not match the corresponding AMESim heat exchanger \"%s\" (%d)",
              amhx->name, amhx->n_pass_divisions[0], hx.Alias(), hx.HeightDivisions());
    gridMismatch = true;
  }
  if (gridMismatch)
    msg_warn("%s: AMESim heat exchanger \"%s\" input and output grids are different. Companion file will not be generated.",
             amhx->name, hx.Alias());

  if (hx.IsVelocityConstant())
    msg_error("%s: AMESim heat exchanger \"%s\" is configured with a constant velocity,"
              " not a velocity map file",
              amhx->name, hx.Alias());

  if (hx.IsTemperatureConstant())
    msg_error("%s: AMESim heat exchanger \"%s\" is configured with a constant temperature,"
              " not a temperature map file",
              amhx->name, hx.Alias());

  if (!hx.IsCoupledToEXA())
    msg_error("%s: Parameter \"writexa\" is not set to true for AMESim heat exchanger \"%s\"."
              " AMESim will not generate output files for PowerFLOW",
              amhx->name, hx.Alias());

  // amhx->y_len = length of HX in coolant flow direction in PowerCASE
  // hx.Width()  = width of HX in AMESim
  //             = length in coolant flow direction since AMESim only allows the coolant flow dir to be horizontal
  // amhx->z_len = length of other dimension of HX in PowerCASE
  // amhx->x_len = depth of HX in PowerCASE
  double x_len = L_offset + L_slope * amhx->x_len;
  double y_len = L_offset + L_slope * amhx->y_len;
  double z_len = L_offset + L_slope * amhx->z_len;
  if (fabs(hx.Width() - y_len) > (0.001 * y_len)
      || fabs(hx.Height() - z_len) > (0.001 * z_len)
      || fabs(hx.Thickness() - x_len) > (0.001 * x_len)) {
    msg_warn("%s: heat exchanger dimensions (%gm, %gm, %gm) do not match the corresponding AMESim heat exchanger \"%s\" (%gm, %gm, %gm)",
             amhx->name, y_len, z_len, x_len,
             hx.Alias(), hx.Width(), hx.Height(), hx.Thickness());
  }
}

static void get_amesim_model_info(const cAMESIM_SUBMODEL_INFO &hx,
                                  int &coolant_up_or_down, int &coolant_left_or_right,
                                  char *pf_to_ame_temp_air_filename,
                                  char *pf_to_ame_vel_air_filename,
                                  char *ame_to_pf_temp_coolant_filename,
                                  char *ame_to_pf_heat_flux_filename)
{
  if (hx.IsCoolantEntryDown())
    coolant_up_or_down = 2;
  else 
    coolant_up_or_down = 1;

  if (hx.IsCoolantEntryLeft())
    coolant_left_or_right = 1;
  else
    coolant_left_or_right = 2;

  strcpy(pf_to_ame_temp_air_filename, hx.AirTempFilename());
  strcpy(pf_to_ame_vel_air_filename, hx.AirVelocityFilename());
  strcpy(ame_to_pf_temp_coolant_filename, hx.CoolantTempFilename());
  strcpy(ame_to_pf_heat_flux_filename, hx.HeatFluxFilename());
}

static void ensure_one_pre_table_read_cmd_per_amesim_model()
{
  // Make sure that the command string is only set once for each AMESim model file.
  CDI_AMHX *hxs = cp_info.cdi_amhxs;
  ccDOTIMES(i, cp_info.n_amhxs) {
    ccDOTIMES(j, i) {
      // Two heat exchangers are only guaranteed to reside in the same AMESim model file
      // if they have the same the relative and absolute model paths.
      if (0 == strcmp(hxs[i]->model_filename, hxs[j]->model_filename)
          && 0 == strcmp(hxs[i]->absolute_model_filename, hxs[j]->absolute_model_filename)) {
        cp_info.table_descs[hxs[i]->table_index].cmd_string = NULL;
        break;
      }
    }
  }
}

static VOID write_amesim_heat_exchanger_coupling(BOOLEAN is_from_ckpt)
{
  if (cp_info.n_amhxs == 0) 
    return;

  FILE *amhx_inp;
  char input_filename[PLATFORM_MAXPATHLEN];
  const char *model_subdir = "amesim";
  int rval;

  char version[64];
  int coolant_up_or_down, coolant_left_or_right;
  char pf_to_ame_temp_air_filename[PLATFORM_MAXPATHLEN];
  char pf_to_ame_vel_air_filename[PLATFORM_MAXPATHLEN];
  char ame_to_pf_temp_coolant_filename[PLATFORM_MAXPATHLEN];
  char ame_to_pf_heat_flux_filename[PLATFORM_MAXPATHLEN];
  
  UNITS_UNIT mks_length_unit;
  UNITS_UNIT lattice_length_unit;
  UNITS_STATUS units_status;
  double L_slope,L_offset;

  if (!cp_info.units_db) {
    msg_error("Units database not available for launching heat exchanger simulation");
    return;
  } else {
    ucmd(units_parse_unit(cp_info.units_db, "m", &mks_length_unit));
    ucmd(units_parse_unit(cp_info.units_db, "LatticeLength", &lattice_length_unit));
  }

  const char *ame = getenv("AME");
  if (ame == NULL) {
    msg_error("\"AME\" environment variable not set");
  }
  msg_print("AMESim installation found via AME environment variable at \"%s\".", ame);

  // convert from lattice units to degC mks units 
  ucmd(units_conversion_coefficients(cp_info.units_db,lattice_length_unit,mks_length_unit,&L_slope,&L_offset));

  // should work on most UNIX systems
  if (cp_info.n_amhxs > 0 && !is_from_ckpt && !cp_info.amesim_dir_created_p) {
    cp_info.amesim_dir_created_p = create_directory("amesim");
  }
    
  if (!getenv("EXA_DIST")) {
    msg_internal_error("\"EXA_DIST\" environment variable not set. Cannot launch amesim_cp");
  }

  char full_app_name[PLATFORM_MAXPATHLEN];

  sprintf(full_app_name, "%s/dist/generic/scripts/amesim_cp", getenv("EXA_DIST"));
  if (!platform_file_present(full_app_name)) 
    msg_internal_error("\"%s\" not found", full_app_name);

  if (!is_cmd_executable_via_system(full_app_name))
    msg_internal_error("\"%s\" is not executable", full_app_name);

  char amepilot[PLATFORM_MAXPATHLEN];
  sprintf(amepilot, "%s/AMEPilot", ame);
  if (!platform_file_present(amepilot)) 
    msg_error("\"%s\" not found", amepilot);

  if (!is_cmd_executable_via_system(amepilot))
    msg_error("\"%s\" is not executable", amepilot);
    
  ccDOTIMES (i,cp_info.n_amhxs) {
    CDI_AMHX amhx = cp_info.cdi_amhxs[i];
    char clean_hx_name[PLATFORM_MAXPATHLEN];
    convert_hxch_name_slash_to_underscore ( amhx->name, clean_hx_name );

    if (amhx->tool != 0)
      msg_internal_error("Unknown heat exchanger solver, only AMESim and PowerCOOL currently supported");

    if (amhx->model_filename == NULL) {
      msg_internal_error("%s: AMESim model filename not provided in CDI file", amhx->name);
    }
    if (amhx->absolute_model_filename == NULL) {
      msg_internal_error("%s: AMESim absolute model filename not provided in CDI file", amhx->name);
    }

    char *model_name = amhx->model_filename;
    char *absolute_model_name = amhx->absolute_model_filename;
    char full_model_name[PLATFORM_MAXPATHLEN];

    // Order of searching for the model file is 
    // a) search path specified on the exaqsub command line
    // b) relative path specified in the CDI file 
    // c) absolute path specified in the CDI file 
    // d) run directory 
    // e) amesim subdirectory 
    // f) EXA_AMESIM_SEARCH_PATH env variable
    eMODEL_LOCATION model_location = locate_coupling_file(model_name, absolute_model_name, 
                                                          model_subdir, cp_info.amesim_model_path, 
                                                          full_model_name);

    if (MODEL_LOCATION_INVALID == model_location)
      msg_error("Cannot find AMESim model file \"%s\".", model_name);
    
    std::string model_ext = amhx->model_filename;
    std::string model_no_ext = model_ext.substr(0, model_ext.length()-4); // remove ".ame"
    rval = sprintf(input_filename,"%s/%s.inp",model_subdir,model_no_ext.c_str());

    if (rval == -1 || rval >= PLATFORM_MAXPATHLEN) {
      msg_error("%s: heat exchanger input filename \"%s\" too long or corrupted",amhx->name,input_filename);
    }

    cAMESIM_SUBMODEL_INFO ame_hx;
    check_amesim_model(full_model_name, model_ext.c_str(), model_no_ext.c_str(), amhx, L_offset, L_slope, ame_hx, version);
    get_amesim_model_info(ame_hx, coolant_up_or_down, coolant_left_or_right,
                          pf_to_ame_temp_air_filename, pf_to_ame_vel_air_filename,
                          ame_to_pf_temp_coolant_filename, ame_to_pf_heat_flux_filename);

    cp_info.amhx_cmd_strings[i] = cnew char [PLATFORM_MAXPATHLEN];
    
    // if the hx name is very long, cannot create filenames, could truncate,
    // but that could conflict with some other long named hx, so err out
    if (strlen(amhx->name) > PLATFORM_MAXPATHLEN - 64) {
      msg_error("%s: Name of heat exchanger too long to accommodate in path",amhx->name);
    }
    if (strlen(model_name) > PLATFORM_MAXPATHLEN - 64) {
      msg_error("%s: AMESim model filename too long to accommodate in path: \"%s\"",amhx->name, model_name);
    }

    // create amesim input file
    if ((amhx_inp = fopen(input_filename,"a")) == NULL) {
      msg_error("%s: Unable to create heat exchanger input file \"%s\", please check permissions in the run directory",amhx->name,input_filename);
    }
    // launch the command as amesim_cp amesim/<model_name>.inp
    // the amesim_cp script calls the generic AMESim script

    rval = sprintf(cp_info.amhx_cmd_strings[i],
                   "%s %d -nummodels 1 -paramfile \"%s\" -model %s -version \"%s\" -timeout %d",
                   full_app_name, getpid(), input_filename, full_model_name, version, cp_info.amesim_timeout);

    if (rval == -1 || rval >= PLATFORM_MAXPATHLEN) {
      msg_error("%s: heat exchanger command string too long or corrupted %s",
                amhx->name,cp_info.amhx_cmd_strings[i]);
    }

    // start writing the input file for amesim
    fprintf(amhx_inp,"%s\t\t%s\t  #%s\n","HeatExchanger", amhx->name, "Name of heat exchanger");
    fprintf(amhx_inp,"%s\t\t%s\t  #%s\n","PF_Cpl_Filename_T", pf_to_ame_temp_air_filename, "T Filename from powerflow to coupling tool");
    fprintf(amhx_inp,"%s\t\t%s\t  #%s\n","PF_Cpl_Filename_V", pf_to_ame_vel_air_filename, "V Filename from powerflow to coupling tool");
    fprintf(amhx_inp,"%s\t\t%s\t  #%s\n","Cpl_PF_Filename_T", ame_to_pf_temp_coolant_filename, "T Filename from coupling tool to powerflow");
    fprintf(amhx_inp,"%s\t\t%s\t  #%s\n","Cpl_PF_Filename_Q", ame_to_pf_heat_flux_filename, "Q Filename from coupling tool to powerflow");
    fprintf(amhx_inp,"%s\t\t%d\t  #%s\n","Coolant_Up_Or_Down", coolant_up_or_down, "Coolant entry up or down: up(1), down(2)");
    fprintf(amhx_inp,"%s\t\t%d\t  #%s\n","Coolant_Left_Or_Right", coolant_left_or_right, "Coolant entry left or right: left(1), right(2)");
    fprintf(amhx_inp,"%s\t\t%s\t  #%s\n","PF_Table_Name", cp_info.table_descs[amhx->table_index].name, "Powerflow Table name");

    fprintf(amhx_inp,"%s\t\t%d\t  #%s\n","Maxi",amhx->n_y_divisions,"maxi: length, segments, of the active row of matrix, i.e. 1 to 20, elements 0 and 21 used as BC");
    fprintf(amhx_inp,"%s\t\t%d\t  #%s\n","Maxk",amhx->n_pass_divisions[0],"maxk: number of rows in the matrix = length of column");
    fprintf(amhx_inp,"%s\t\t%.12g  #%s\n","Length",L_offset+L_slope*amhx->y_len,"radiator_length, m: measured along the floor, normal to the car axis");
    fprintf(amhx_inp,"%s\t\t%.12g  #%s\n","Depth",L_offset+L_slope*amhx->x_len,"radiator_depth (thickness of active, finned, part), m: measured along the car axis");
    fprintf(amhx_inp,"%s\t\t%.12g  #%s\n","Height",L_offset+L_slope*amhx->z_len,"radiator_height, m: measured upward normally to the floor");    
    fprintf(amhx_inp,"%s\t%.12g  #%s\n","Height_Ratio", (L_offset+L_slope*amhx->z_len) / ame_hx.Height(), "powercase_height / amesim_height");        
    fprintf(amhx_inp,"%s\t%g %g %g  #%s\n","A_csys_r",
	cp_info.all_csys->coord_systems[amhx->medium_csys_index].g_to_l_xform[0][0],
	cp_info.all_csys->coord_systems[amhx->medium_csys_index].g_to_l_xform[0][1],
	cp_info.all_csys->coord_systems[amhx->medium_csys_index].g_to_l_xform[0][2],
	"Air velocity direction specified by the user");

    fprintf(amhx_inp,"%s\t%d\t  #%s\n","FI_offset",amhx->inlet_facet_offset,"CDI facet offset for inlet measurement window");
    fprintf(amhx_inp,"%s\t%d\t  #%s\n","FO_offset",amhx->outlet_facet_offset,"CDI facet offset for outlet measurement window");
    // only support .snc type of sampling surface measurement files for hx
    fprintf(amhx_inp,"imeas_file\t %s.snc  #%s\n",(cp_info.cdi_meas_windows[amhx->inlet_meas_index]).name,"Input inlet measurement file");
    fprintf(amhx_inp,"omeas_file\t %s.snc  #%s\n",(cp_info.cdi_meas_windows[amhx->outlet_meas_index]).name,"Input outlet measurement file");
    fprintf(amhx_inp,"companion_file\t %s/%s.ncc  #%s\n",model_subdir,(cp_info.cdi_meas_windows[amhx->inlet_meas_index]).name,"Inlet measurement companion file to write data to");
    //fprintf(amhx_inp,"results_file\t %s/%s.out  #%s\n",model_subdir,clean_hx_name,"Tabular results output file");
    fprintf(amhx_inp,"imeas_dump_file %s/%s_meas.dat  #%s\n",model_subdir,clean_hx_name,"Data dump file for measurement values");
    fprintf(amhx_inp,"part_name %s\t  #%s\n",amhx->part_name,"Heat exchanger part name (usually same as heat exchanger name)");

    fclose(amhx_inp);

    // Note that for AMESim heat exchangers, the results file name was designated by toptank.
    // Here we assume that both are using the same name convention: "model_subdir"/"amhx->name".out
    sINT32 hx_index = -1;
    ccDOTIMES(hx, cp_info.all_hx_info.size()) {
      if (cp_info.all_hx_info[hx].type == CDI_CHUNK_TYPE_AMHX && cp_info.all_hx_info[hx].index == i) {
        hx_index = hx;
        break;
      }
    }
    // Assign the results_filename to all_hx_info
    if (hx_index < 0) {
      msg_internal_error("Cannot find the AMESim heat exchanger %d in all_hx_info", i);
    } else {
      char results_filename[PLATFORM_MAXPATHLEN];
      rval = sprintf(results_filename,"%s/%s.out",model_subdir,clean_hx_name);
        if (rval == -1 || rval >= PLATFORM_MAXPATHLEN) {
        msg_error("AMESim heat exchanger results filename too long or corrupted");
      }
      cp_info.all_hx_info[hx_index].results_filename = strsave(results_filename);
      cp_info.all_hx_info[hx_index].old_pos = 0;
    }

    // replace the table command string with the one newly constructed
    cp_info.table_descs[amhx->table_index].cmd_string = cp_info.amhx_cmd_strings[i];
    cp_info.cdi_meas_windows[amhx->inlet_meas_index].flush_every_frame = TRUE;
    cp_info.cdi_meas_windows[amhx->outlet_meas_index].flush_every_frame = TRUE;
    if (amhx->heat_gen_meas_index >= 0)
      cp_info.cdi_meas_windows[amhx->heat_gen_meas_index].flush_every_frame = TRUE;
  } 

  ensure_one_pre_table_read_cmd_per_amesim_model();

  return;
}

static VOID write_heat_exchanger_coupling(BOOLEAN is_from_ckpt)
{
  // assuming by this point the units database is loaded in and the amhx
  // cpinfo vector is filled.
  if (cp_info.n_hxchs == 0 && cp_info.n_amhxs == 0) 
    return;

  cp_info.hxch_cmd_strings = cnew char* [cp_info.n_hxchs];
  cp_info.amhx_cmd_strings = cnew char* [cp_info.n_amhxs];
  write_powercool_heat_exchanger_coupling(is_from_ckpt);
  write_amesim_heat_exchanger_coupling(is_from_ckpt);

  return;
}

typedef struct sHX_SERIES {
  int child_index;
  int parent_index;
  int coarse_meas_win_index;
  int coarse_meas_win_scale;
} *HX_SERIES;

static void find_hx_coarsest_scale(int direction,HX_SERIES all_hx_series, 
                                   int hx_index, int *scale, int *win_index) 
{ 
  if (!all_hx_series) return;
  int hx_heirarchy_index = ((direction > 0)
                            ? (all_hx_series + hx_index)->parent_index
                            : (all_hx_series + hx_index)->child_index);
  if ( hx_heirarchy_index == -1) 
    return;
  if (sim_is_scale_coarser((all_hx_series + hx_heirarchy_index)->coarse_meas_win_scale,*scale)) {
    *scale = (all_hx_series + hx_heirarchy_index)->coarse_meas_win_scale;
    *win_index = (all_hx_series + hx_heirarchy_index)->coarse_meas_win_index;
  }
  find_hx_coarsest_scale(direction,all_hx_series,hx_heirarchy_index,scale,win_index);
}

VOID sync_series_heat_exchanger_meas_windows()
{
  if (cp_info.n_hxchs <= 0) 
    return;

  HX_SERIES all_hx_series = cnew sHX_SERIES [cp_info.n_hxchs];
  // initialize the lookup table
  ccDOTIMES (i, cp_info.n_hxchs) {
    (all_hx_series+i)->coarse_meas_win_scale = -1;
    (all_hx_series+i)->coarse_meas_win_index = -1;
    (all_hx_series+i)->child_index = -1;
    (all_hx_series+i)->parent_index = -1;
  }

  // fill up the lookup table for all heat exchangers present
  ccDOTIMES (i, cp_info.n_hxchs) {
    CDI_HXCH hxch = cp_info.cdi_hxchs[i];
    if (!hxch) continue;

    CDI_MEAS_WINDOW inlet_meas_win  = cp_info.cdi_meas_windows + hxch->inlet_meas_index;
    CDI_MEAS_WINDOW outlet_meas_win = cp_info.cdi_meas_windows + hxch->outlet_meas_index;
    CDI_MEAS_WINDOW heat_gen_meas_win = hxch->heat_gen_meas_index >= 0? 
                                        cp_info.cdi_meas_windows + hxch->heat_gen_meas_index :
                                        NULL;
    (all_hx_series+i)->coarse_meas_win_scale = outlet_meas_win->coarsest_fluid_shob_scale;
    (all_hx_series+i)->coarse_meas_win_index = hxch->outlet_meas_index;

    if (sim_is_scale_coarser(inlet_meas_win->coarsest_fluid_shob_scale,
                             (all_hx_series+i)->coarse_meas_win_scale)) {
      (all_hx_series+i)->coarse_meas_win_scale = inlet_meas_win->coarsest_fluid_shob_scale;
      (all_hx_series+i)->coarse_meas_win_index = hxch->inlet_meas_index;
    }

    if (heat_gen_meas_win && sim_is_scale_coarser(heat_gen_meas_win->coarsest_fluid_shob_scale,
                                                  (all_hx_series+i)->coarse_meas_win_scale)) {
      (all_hx_series+i)->coarse_meas_win_scale = heat_gen_meas_win->coarsest_fluid_shob_scale;
      (all_hx_series+i)->coarse_meas_win_index = hxch->heat_gen_meas_index;
    }

    if (hxch->upstream_index >= 0) {
      if (hxch->upstream_index == i)
        msg_internal_error("For heat exchanger \"%s\", upstream heat exchanger is self.",
                           hxch->name); 
      (all_hx_series+i)->parent_index = hxch->upstream_index;
      (all_hx_series+(hxch->upstream_index))->child_index = i;
    }
  }

  // obtain the coarsest meas win scale for all children and ancestors
  ccDOTIMES (i, cp_info.n_hxchs) {
    CDI_HXCH hxch = cp_info.cdi_hxchs[i];
    if (!hxch) continue;

    int c_win_index = (all_hx_series+i)->coarse_meas_win_index;
    int c_win_scale = (all_hx_series+i)->coarse_meas_win_scale;
    CDI_MEAS_WINDOW inlet_meas_win  = cp_info.cdi_meas_windows + hxch->inlet_meas_index;
    CDI_MEAS_WINDOW outlet_meas_win = cp_info.cdi_meas_windows + hxch->outlet_meas_index;
    CDI_MEAS_WINDOW heat_gen_meas_win = hxch->heat_gen_meas_index >= 0? 
                                        cp_info.cdi_meas_windows + hxch->heat_gen_meas_index :
                                        NULL;

    find_hx_coarsest_scale(-1,all_hx_series,i,&c_win_scale,&c_win_index);
    find_hx_coarsest_scale(+1,all_hx_series,i,&c_win_scale,&c_win_index);

    inlet_meas_win->fluid_time_desc = (cp_info.cdi_meas_windows+c_win_index)->fluid_time_desc; 
    outlet_meas_win->fluid_time_desc = (cp_info.cdi_meas_windows+c_win_index)->fluid_time_desc;
    if (heat_gen_meas_win)
      heat_gen_meas_win->fluid_time_desc = (cp_info.cdi_meas_windows+c_win_index)->fluid_time_desc;
  }

  delete[] all_hx_series;
}

// condensers do not appear in series, but ensure the inlet and outlet
// measurement windows are synced to the same timestep if they appear in
// different VR levels

VOID sync_condenser_meas_windows()
{
  if (cp_info.n_cdsrs <= 0) 
    return;

  ccDOTIMES (i, cp_info.n_cdsrs) {
    CDI_CDSR cdsr = cp_info.cdi_cdsrs[i];
    if (!cdsr) continue;

    CDI_MEAS_WINDOW inlet_meas_win  = cp_info.cdi_meas_windows + cdsr->inlet_meas_index;
    CDI_MEAS_WINDOW outlet_meas_win = cp_info.cdi_meas_windows + cdsr->outlet_meas_index;
    CDI_MEAS_WINDOW heat_gen_meas_win = cdsr->heat_gen_meas_index >= 0? 
                                        cp_info.cdi_meas_windows + cdsr->heat_gen_meas_index :
                                        NULL;

    int coarsest_scale = outlet_meas_win->coarsest_fluid_shob_scale;
    int coarsest_meas_index = cdsr->outlet_meas_index;

    if (sim_is_scale_coarser(inlet_meas_win->coarsest_fluid_shob_scale, coarsest_scale)) {
      coarsest_meas_index = cdsr->inlet_meas_index;
      coarsest_scale = inlet_meas_win->coarsest_fluid_shob_scale;
    }
    if (heat_gen_meas_win && sim_is_scale_coarser(heat_gen_meas_win->coarsest_fluid_shob_scale, coarsest_scale)) {
      coarsest_meas_index = cdsr->heat_gen_meas_index;
      coarsest_scale = heat_gen_meas_win->coarsest_fluid_shob_scale;
    }

    CDI_MEAS_WINDOW coarsest_win = cp_info.cdi_meas_windows + coarsest_meas_index;
    inlet_meas_win->fluid_time_desc = coarsest_win->fluid_time_desc;
    outlet_meas_win->fluid_time_desc = coarsest_win->fluid_time_desc;
    if (heat_gen_meas_win)
      heat_gen_meas_win->fluid_time_desc = coarsest_win->fluid_time_desc;

  }
}


VOID initialize_heat_exchanger_and_condenser_data()
{
  cp_info.n_hxchs = 0;
  cp_info.hxch_cmd_strings = NULL;
  cp_info.cdi_hxchs = NULL;

  cp_info.n_cdsrs = 0;
  cp_info.cdsr_cmd_strings = NULL;
  cp_info.cdi_cdsrs = NULL;

  cp_info.n_amhxs = 0;
  cp_info.amhx_cmd_strings = NULL;
  cp_info.cdi_amhxs = NULL;
}

VOID free_heat_exchanger_and_condenser_storage()
{
  if (cp_info.n_hxchs <= 0 && cp_info.n_cdsrs <= 0 && cp_info.n_amhxs <= 0) 
    return;

  ccDOTIMES(i,cp_info.n_hxchs) {
    if (cp_info.cdi_hxchs[i]) {
      cdi_destroy_hxch(cp_info.cdi_hxchs[i]);
    }
    if (cp_info.hxch_cmd_strings[i]) {
      delete[] cp_info.hxch_cmd_strings[i];
    }
  }
  delete[] cp_info.cdi_hxchs;
  cp_info.cdi_hxchs = NULL;
  if (cp_info.hxch_cmd_strings) {
    delete[] cp_info.hxch_cmd_strings;
    cp_info.hxch_cmd_strings = NULL;
  }

  ccDOTIMES(i,cp_info.n_cdsrs) {
    if (cp_info.cdi_cdsrs[i]) {
      cdi_empty_condenser(cp_info.cdi_cdsrs[i]);
    }
    if (cp_info.cdsr_cmd_strings[i]) {
      delete[] cp_info.cdsr_cmd_strings[i];
    }
  }
  delete[] cp_info.cdi_cdsrs;
  cp_info.cdi_cdsrs = NULL;
  if (cp_info.cdsr_cmd_strings) {
    delete[] cp_info.cdsr_cmd_strings;
    cp_info.cdsr_cmd_strings = NULL;
  }

  ccDOTIMES(i,cp_info.n_amhxs) {
    if (cp_info.cdi_amhxs[i]) {
      cdi_destroy_amhx(cp_info.cdi_amhxs[i]);
    }
    if (cp_info.amhx_cmd_strings[i]) {
      delete[] cp_info.amhx_cmd_strings[i];
    }
  }
  delete[] cp_info.cdi_amhxs;
  cp_info.cdi_amhxs = NULL;
  if (cp_info.amhx_cmd_strings) {
    delete[] cp_info.amhx_cmd_strings;
    cp_info.amhx_cmd_strings = NULL;
  }
}

VOID process_any_heat_exchangers_and_condensers(BOOLEAN is_from_ckpt)
{

  cp_info.powercool_dir_created_p = FALSE;
  cp_info.amesim_dir_created_p = FALSE;
  write_heat_exchanger_coupling(is_from_ckpt);
  write_condenser_coupling(is_from_ckpt);
}

static char *fgets_try_several_times(char *s, int size, FILE *stream)
{
  int n_tries = 0;
  while (!fgets(s, size, stream)) {
    // wait for 1 sec and retry. If more than 5 secs, issue a warning message and go on
    platform_sleep_seconds(1);
    n_tries++;
    if (n_tries == 5)
      return NULL;
  }
  return s;
} 

// Update monitors which are associated with the heat exchanger.
VOID process_hx_monitors(asINT32 hx_index, TIMESTEP signal_time, TIMESTEP next_signal_time)
{
  // If this hx does not contribute to any monitors, we do not need to read the output file
  sHX_INFO &hx_info = cp_info.all_hx_info[hx_index];
  if (hx_info.monitors.empty())
    return; 

  const int MAX_LINE_LENGTH = 512;  // Maximal length of a line in PowerCOOL result file

  FILE* results_file;
  if ((results_file = fopen(hx_info.results_filename, "r")) == NULL) {
    msg_warn("Cannot open file \"%s\" to read heat exchanger data at timestep %d.", 
             hx_info.results_filename, signal_time);
    return;
  }

  char line[MAX_LINE_LENGTH];

  // Move to the previous position
  fseek(results_file, hx_info.old_pos, SEEK_SET);

  if (!fgets_try_several_times(line, MAX_LINE_LENGTH, results_file)) {
    msg_warn("Failed to read heat exchanger results from file \"%s\" at timestep %d "
             "for use with monitors. These results will be read the next time that "
             "the simulation couples to PowerCOOL.", hx_info.results_filename, signal_time);
    return;
  }

  // On checkpoint resume, skip past all pre-resume lines in <heat_exchanger>.out file
  if (hx_info.old_pos == 0) {
    asINT32 monitor_index = hx_info.monitors[0];
    HX_MONITOR hx_monitor = cp_info.hx_monitors[monitor_index];
    asINT32 n_before_ckpt_couplings = hx_monitor->m_timesteps.size();
    int last_timestep = -1;

    while (n_before_ckpt_couplings > 0) {
      if (line[0] != '#') {  // Skip lines starting with "#"
        int timestep, iteration;
        asINT32 n_matched = sscanf(line, "%d %d", &iteration, &timestep);
        if (n_matched == 2) {
          if (timestep > last_timestep) {
            last_timestep = timestep;
            n_before_ckpt_couplings--;
          }
          if (timestep > cp_info.restart_time) {
            break;
          }
        }
      }
      if (!fgets_try_several_times(line, MAX_LINE_LENGTH, results_file)) {
        msg_warn("Failed to read heat exchanger results from file \"%s\" at timestep %d "
                 "for use with monitors.  These results will be read the next time that "
                 "the simulation couples to PowerCOOL.", hx_info.results_filename, signal_time);
        return;
      }
    }
  }

  // It is possible that some previous lines were not read (e.g. due to delay 
  // in the filesystem), so we should read in as many lines as possible here.
  int timestep = -1;
  do {
    // Update the position
    hx_info.old_pos = ftell(results_file);
  
    // Skip lines starting with "#"
    if (line[0] == '#')
      continue;

    int frame;
    double coolant_entry_T, coolant_exit_T, coolant_mass_flow, heat_rejected;
    double air_entry_mean_T, air_inlet_mean_T, air_outlet_mean_T, air_mass_flow;
    int num_iter;
    double residual;
    sscanf(line, "%d %d %lf %lf %lf %lf %lf %lf %lf %lf %d %lg", 
           &frame, &timestep, &coolant_entry_T, &coolant_exit_T, &coolant_mass_flow, &heat_rejected,
           &air_entry_mean_T, &air_inlet_mean_T, &air_outlet_mean_T, &air_mass_flow, &num_iter, &residual);
    // For this hxch, update all the monitors it contributes to
    ccDOTIMES(i, hx_info.monitors.size()) {
      asINT32 monitor_index = hx_info.monitors[i];
      HX_MONITOR hx_monitor = cp_info.hx_monitors[monitor_index];
  
      double data;
      switch(hx_monitor->m_hx_var_type) {
      case eCDI_MNTR_HX_VAR::CoolantEntryTemp:
        data = coolant_entry_T;
        break;
      case eCDI_MNTR_HX_VAR::HeatRejected:
        data = heat_rejected;
        break;
      case eCDI_MNTR_HX_VAR::CoolantExitTemp:
        data = coolant_exit_T;
        break;
      case eCDI_MNTR_HX_VAR::CoolantMassFlowRate:
        data = coolant_mass_flow;
        break;
      case eCDI_MNTR_HX_VAR::InletAirMeanTemp:
        data = air_inlet_mean_T;
        break;
      case eCDI_MNTR_HX_VAR::OutletAirMeanTemp:
        data = air_outlet_mean_T;
        break;
      case eCDI_MNTR_HX_VAR::EntryAirMeanTemp:
        data = air_entry_mean_T;
        break;
      case eCDI_MNTR_HX_VAR::AirMassFlowRate:
        data = air_mass_flow;
        break;
      default:
        msg_internal_error("Variable is not supported for heat exchanger monitor %s", hx_monitor->m_name);
        break;
      }
   
      // If reading a previously missed data, next_signal_time will be meaningless here
      hx_monitor->append_and_analyze_signal(data, timestep, next_signal_time);
    }
  } while (timestep < signal_time  // If resuming from checkpoint, do not read future lines
                                   // written after the checkpoint was written
           && fgets(line, MAX_LINE_LENGTH, results_file));
  
  fclose(results_file);
}
