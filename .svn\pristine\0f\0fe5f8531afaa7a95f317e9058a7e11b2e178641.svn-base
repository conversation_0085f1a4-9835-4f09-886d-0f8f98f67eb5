/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("fx1.copyright", "78") */
/*****************************************************************************
 *** Copyright (C) 2018 Exa Corporation, a Dassault Systemes company.      ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("fx1.copyright", "78") */

#ifndef CDI_GEOMETRY_GENERATOR_H__INCLUDED
#define CDI_GEOMETRY_GENERATOR_H__INCLUDED

#include <unordered_set>

#include "cdi_export.h"


class cCDI_GEOMETRY_GENERATOR {
 public:
  cCDI_GEOMETRY_GENERATOR(const cCDI_PARTITIONS& cdiPartitions,           // normally filled in by cCDI_PARTITIONS::Read*FromCDI
                          std::unordered_set<cdiINT32> regionsMeasured,   // normally filled in from effective selections in mesr chunk
                          std::unordered_set<cdiINT32> facesMeasured,     // "
                          std::unordered_set<cdiINT32> solidRegions,      // normally filled in by cCDI_GEOMETRY_READER
                          std::unordered_set<cdiINT32> fluidRegions,      // "
                          CDI_ENTITY_FILTER_FUNCTION regionFilterFunc,    // pass cdi_match_any to disable filtering
                          CDI_ENTITY_FILTER_FUNCTION faceFilterFunc,      // pass cdi_match_any to disable filtering
                          cGEOM_COMMON_ENTITY_LIST& entities);            // out parameter (set by the call)

  cCDI_GEOMETRY_GENERATOR() = delete;

  cCDI_GEOMETRY_GENERATOR(const cCDI_GEOMETRY_GENERATOR& other) = delete;
  cCDI_GEOMETRY_GENERATOR& operator=(const cCDI_GEOMETRY_GENERATOR& other) = delete;

  cCDI_GEOMETRY_GENERATOR(cCDI_GEOMETRY_GENERATOR&& other) = delete;
  cCDI_GEOMETRY_GENERATOR& operator=(cCDI_GEOMETRY_GENERATOR&& other) = delete;

  ~cCDI_GEOMETRY_GENERATOR() = default;

  // Does a case-insensitive lexicographical comparison of two strings, and returns true
  // if the first one should appear before the second.
  /* static */
  bool LexicographicalLess(const std::string& strA, const std::string& strB) const;

  // Visit this segment and its children, appending to m_entities in the order desired
  // by geometry selection UI.
  void VisitSegments(const cCDI_SEGMENT& segment, const std::string& segmentPath);

  // Visit every segment in DFS order within each partition.  Visit those segments'
  // parts and faces as well.  Causes the m_entities list to be written in the proper
  // order for passing to a cQ_GEOM_SELECTION_WIDGET.
  void Finalize();

  // Make this class usable as a functor, for convenience.
  void operator()() { Finalize(); }

 private:
  const cCDI_PARTITIONS& m_cdiPartitions;         // object with partition/seg/part/face info
  std::unordered_set<cdiINT32> m_regionsMeasured; // set of parts included in measurements
  std::unordered_set<cdiINT32> m_facesMeasured;   // set of faces included in measurements
  std::unordered_set<cdiINT32> m_solidRegions;    // set of regions classified as solid
  std::unordered_set<cdiINT32> m_fluidRegions;    // set of regions classified as fluid
  CDI_ENTITY_FILTER_FUNCTION m_regionFilterFunc;  // filter function to use for regions
  CDI_ENTITY_FILTER_FUNCTION m_faceFilterFunc;    // filter function to use for faces
  cGEOM_COMMON_ENTITY_LIST& m_entities;           // hierarchy returned
};

#endif  // CDI_GEOMETRY_GENERATOR_H__INCLUDED
