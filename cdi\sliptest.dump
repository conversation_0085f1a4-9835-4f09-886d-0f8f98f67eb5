xcdi {
      0     8
  case {
    cmnt { "title" "SLIPTEST" }
    audt { "program:ExaCASE 061 seven-sisters kt 04/18/1996 14:44:24 GMT
file:assembly 09/19/1994 20:56:15 GMT /fs/d1/kt/uss/slip/sliptest.asm.1
file:part 09/19/1994 20:56:15 GMT /fs/d1/kt/uss/slip/sliptest_obj.prt.1
file:part 09/19/1994 20:56:15 GMT /fs/d1/kt/uss/slip/sliptest_sv.prt.1
file:case 10/26/1995 19:48:46 GMT /fs/d1/kt/uss/slip/sliptest.exac.3
file:propset 10/26/1995 19:48:45 GMT /fs/d1/kt/uss/slip/sliptest_slip.exap.3
file:propset 10/26/1995 19:48:45 GMT /fs/d1/kt/uss/slip/sliptest_meas.exap.3
file:propset 10/26/1995 19:48:46 GMT /fs/d1/kt/uss/slip/sliptest_sv.exap.3
file:propset 10/26/1995 19:48:46 GMT /fs/d1/kt/uss/slip/sliptest_obj.exap.3
program:dump_cdi 129-jrm-01 hercules jrm 01/26/2003 20:33:17 GMT
" }
    vrtx {    16      (number of verts)
           ( x         y         z)
                        0                  50                  10   (vrtx 0)
                        0                  50                   0   (vrtx 1)
                        0                   0                   0   (vrtx 2)
                       50                   0                   0   (vrtx 3)
                       50                  50                   0   (vrtx 4)
                       50                   0                  10   (vrtx 5)
                       50                  50                  10   (vrtx 6)
                        0                   0                  10   (vrtx 7)
                       15                19.5                  10   (vrtx 8)
                       15                29.5                   0   (vrtx 9)
                       15                19.5                   0   (vrtx 10)
                       35                19.5                   0   (vrtx 11)
                       35                30.5                   0   (vrtx 12)
                       35                30.5                  10   (vrtx 13)
                       15                29.5                  10   (vrtx 14)
                       35                19.5                  10   (vrtx 15)
    }
    regn (region number 0) {
      name { "SLIPTEST_SV" }
      bbox {                  0                   0                   0                  50                  50                  10}
      body {
        shll {
          ftab {
            face {      -1    (surface physics index)
                      12    (number of facets)
                       0
                       1
                       2
                       3
                       4
                       5
                       6
                       7
                       8
                       9
                      10
                      11
             }
          }
          fact {    12     (number of facets)
             (face     edgehalves                    normal)
                0     0     1     2                  -1                  -0                  -0 (fact 0)
                0     3     4     5                   0                   0                  -1 (fact 1)
                0     6     7     8                  -0                  -0                  -1 (fact 2)
                0     9    10    11                   1                  -0                   0 (fact 3)
                0    12    13    14                   1                  -0                   0 (fact 4)
                0    15    16    17                   0                   1                   0 (fact 5)
                0    18    19    20                   0                   1                  -0 (fact 6)
                0    21    22    23                   0                  -0                   1 (fact 7)
                0    24    25    26                  -1                   0                  -0 (fact 8)
                0    27    28    29                   0                  -1                   0 (fact 9)
                0    30    31    32                   0                  -1                   0 (fact 10)
                0    33    34    35                   0                   0                   1 (fact 11)
          }
          edge {    36     (number of edgehalves)
             (conj  conj  head  tail)
             (facet edge  vrtx  vrtx)
                6    19     1     0    (edge 0)
                1     4     2     1    (edge 1)
                8    25     0     2    (edge 2)
                9    28     2     3    (edge 3)
                0     1     1     2    (edge 4)
                2     7     3     1    (edge 5)
                3    10     3     4    (edge 6)
                1     5     1     3    (edge 7)
                5    16     4     1    (edge 8)
               10    31     3     5    (edge 9)
                2     6     4     3    (edge 10)
                4    12     5     4    (edge 11)
                3    11     4     5    (edge 12)
                5    15     6     4    (edge 13)
               11    34     5     6    (edge 14)
                4    13     4     6    (edge 15)
                2     8     1     4    (edge 16)
                6    18     6     1    (edge 17)
                5    17     1     6    (edge 18)
                0     0     0     1    (edge 19)
                7    22     6     0    (edge 20)
               11    35     6     7    (edge 21)
                6    20     0     6    (edge 22)
                8    24     7     0    (edge 23)
                7    23     0     7    (edge 24)
                0     2     2     0    (edge 25)
                9    27     7     2    (edge 26)
                8    26     2     7    (edge 27)
                1     3     3     2    (edge 28)
               10    30     7     3    (edge 29)
                9    29     3     7    (edge 30)
                3     9     5     3    (edge 31)
               11    33     7     5    (edge 32)
               10    32     5     7    (edge 33)
                4    14     6     5    (edge 34)
                7    21     7     6    (edge 35)
          }
        }
      }
    }
    regn (region number 1) {
      name { "SLIPTEST_OBJ" }
      bbox {                 15                19.5                   0                  35                30.5                  10}
      body {
        shll {
          ftab {
            face {       0    (surface physics index)
                      12    (number of facets)
                       0
                       1
                       2
                       3
                       4
                       5
                       6
                       7
                       8
                       9
                      10
                      11
             }
          }
          fact {    12     (number of facets)
             (face     edgehalves                    normal)
                0     0     1     2                  -1                   0                   0 (fact 12)
                0     3     4     5                   0                  -1                   0 (fact 13)
                0     6     7     8                   0                   0                  -1 (fact 14)
                0     9    10    11                   0                   0                  -1 (fact 15)
                0    12    13    14 -0.0499376169438922   0.998752338877845                   0 (fact 16)
                0    15    16    17 -0.0499376169438922   0.998752338877845                   0 (fact 17)
                0    18    19    20                  -1                   0                   0 (fact 18)
                0    21    22    23                   0                   0                   1 (fact 19)
                0    24    25    26                   0                  -1                   0 (fact 20)
                0    27    28    29                   1                   0                   0 (fact 21)
                0    30    31    32                   1                   0                   0 (fact 22)
                0    33    34    35                   0                   0                   1 (fact 23)
          }
          edge {    36     (number of edgehalves)
             (conj  conj  head  tail)
             (facet edge  vrtx  vrtx)
                6    19     9     8    (edge 36)
                3    11    10     9    (edge 37)
                1     3     8    10    (edge 38)
                0     2    10     8    (edge 39)
                2     7    11    10    (edge 40)
                8    25     8    11    (edge 41)
                9    28    11    12    (edge 42)
                1     4    10    11    (edge 43)
                3    10    12    10    (edge 44)
                4    13    12     9    (edge 45)
                2     8    10    12    (edge 46)
                0     1     9    10    (edge 47)
               10    31    12    13    (edge 48)
                3     9     9    12    (edge 49)
                5    15    13     9    (edge 50)
                4    14     9    13    (edge 51)
                6    18    14     9    (edge 52)
               11    34    13    14    (edge 53)
                5    16     9    14    (edge 54)
                0     0     8     9    (edge 55)
                7    22    14     8    (edge 56)
               11    35    14    15    (edge 57)
                6    20     8    14    (edge 58)
                8    24    15     8    (edge 59)
                7    23     8    15    (edge 60)
                1     5    11     8    (edge 61)
                9    27    15    11    (edge 62)
                8    26    11    15    (edge 63)
                2     6    12    11    (edge 64)
               10    30    15    12    (edge 65)
                9    29    12    15    (edge 66)
                4    12    13    12    (edge 67)
               11    33    15    13    (edge 68)
               10    32    13    15    (edge 69)
                5    17    14    13    (edge 70)
                7    21    15    14    (edge 71)
          }
        }
      }
    }
    simv {                  50                  50                  10          (max)
                  1                                  (flags)
                           1                   0                   0                   0 (xform)
                           0                   1                   0                   0
                           0                   0                   1                   0
                           0                   0                   0                   1
    }
    grid {
      gscl {
        name { "SLIPTEST_SV" }
        rgns {     1     (number of regions)
                   0 (region 0)
        }
        gscc { 0 } (value)
      }
    }
    pcfg {
      flud {
        phrg {
          rgns {     1     (number of regions)
                     0 (region 0)
          }
          phys {
            name { "SLIPTEST_SV" }
            ptyp { 6 0 1 5 } (type, ints, conts, inits)
            dprm {
              cvdp { 10 0.05 } (type, value)
            }
            ival {
              cvdp { 15 2550 } (type, value)
              cvdp { 16 0 } (type, value)
              cvdp { 17 0 } (type, value)
              cvdp { 18 0 } (type, value)
              cvdp { 19 2142 } (type, value)
            }
          }
        }
      }
      sold {
        phrg {
          rgns {     1     (number of regions)
                     1 (region 0)
          }
          phys {
            name { "SLIPTEST_SLIP" }
            ptyp { 7 0 0 0 } (type, ints, conts, inits)
          }
        }
      }
    }
    meas {
      mesr {
        name { "sliptest_meas.msr" }
        rgns {     1     (number of regions)
                   0 (region 0)
        }
        mstt {
          mprm { 0 1000000 1 1 2 } (start, end, per, avg, space)
          mstp { 3 } (options)
          name { "SLIPTEST_MEAS" }
        }
      }
    }
    srpt {
      phys (surface physics 0) {
        name { "SLIPTEST_SLIP" }
        ptyp { 8 0 1 0 } (type, ints, conts, inits)
        dprm {
          cvdp { 13 0.99 } (type, value)
        }
      }
    }
    glob {
      ghdr { 100 1 } (header:timesteps, processors)
      unit { 0 0 0 0 16 }
				 (lattice units:m,l,t,T,n_user_units)
      null {    0}
      uunt { 0 0 1 0 0 1 -0 "cells" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 1 1 0 0 0 1 -0 "particles" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 2 0 0 1 0 1 -0 "timesteps" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 3 0 0 0 1 1 -0 "c^2/ts^2" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 4 1 1 -2 0 1 -0 "p-c/ts^2" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 5 1 2 -2 0 1 -0 "p-c^2/ts^2" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 6 1 2 -2 0 1 -0 "p-c^2/ts^2" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 7 0 2 0 0 1 -0 "cells^2" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 8 0 1 -1 0 1 -0 "c/ts" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 9 1 -1 -1 0 1 -0 "p/c-ts" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 10 0 2 -1 0 1 -0 "c^2/ts" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 11 0 2 -1 0 1 -0 "c^2/ts" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 12 1 -3 0 0 1 -0 "p/c^3" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 13 0 1 -2 0 1 -0 "c/ts^2" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 14 1 0 -3 0 1 -0 "p/ts^3" }
			 (user units:type,m,l,t,T,scale,offset,name)
      uunt { 15 1 -1 -2 0 1 -0 "p/c-ts^2" }
			 (user units:type,m,l,t,T,scale,offset,name)
      cprp { 0 1 0 0 "$CASE_CHAR_LENGTH" 1 }
			 (char prop:m,l,t,T,name,value)
      cprp { 0 1 -1 0 "$CASE_CHAR_SPEED" 0.2 }
			 (char prop:m,l,t,T,name,value)
      cprp { 1 -3 0 0 "$CASE_CHAR_DENSITY" 2550 }
			 (char prop:m,l,t,T,name,value)
      cprp { 0 0 0 1 "$CASE_CHAR_TEMPERATURE" 0.42 }
			 (char prop:m,l,t,T,name,value)
      cprp { 0 0 0 0 "$CASE_REYNOLDS_NUMBER" 10000 }
			 (char prop:m,l,t,T,name,value)
      cprp { 0 0 0 0 "$CASE_FLUID_PRANDTL_NUMBER" 0.9 }
			 (char prop:m,l,t,T,name,value)
      cprp { 0 0 0 0 "$CASE_TURB_PRANDTL_NUMBER" 0.9 }
			 (char prop:m,l,t,T,name,value)
    }
  }
}
