/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Measurement window support
 *
 * Jim Salem, Exa Corporation 
 * Created Mon Jun  6 1994
 *--------------------------------------------------------------------------*/

#include <algorithm>

#include "common.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "window.h"
#include "window_results.h"
#include "trajectory_results.h"
#include "timestep_subcycling.h"
#include "sim_tree.h"

#if SURF_COUP
#include "coupling_model.h"
#endif

#include "jobctl.h"

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
#include "trajectory_window.h"
//#endif

/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/

sINT32 sCP_MEAS_WINDOW::n_remaining_sp_meas_cell_ref_list_elts = 0;
sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF_LIST_ELT * sCP_MEAS_WINDOW::sp_meas_cell_ref_list_elt_pool = NULL;

sMEAS_WINDOW_VERTEX_MAP g_meas_window_vertex_map;

VOID sMEAS_WINDOW_VERTEX_MAP::create()  
{ 
  m_map = xnew sriINT [ cp_info.vertex_array.size() ]; 
}

// clear() sets all entries in the "map" to -1
VOID sMEAS_WINDOW_VERTEX_MAP::clear()   
{ 
  memset(m_map, -1, sizeof(sriINT) * cp_info.vertex_array.size()); 
}

sGRF_MEAS_FRAME_SP_TO_CP_MSG *sCP_MEAS_WINDOW::grf_return_buffer() {
  return (sGRF_MEAS_FRAME_SP_TO_CP_MSG*)(global_nirf_info_msg.buffer());
}

sLRF_MEAS_FRAME_SP_TO_CP_MSG *sCP_MEAS_WINDOW::lrf_return_buffer(int N) {
  return (sLRF_MEAS_FRAME_SP_TO_CP_MSG*)(lrf_info_msg[N].buffer());
}

sMBC_MEAS_FRAME_SP_TO_CP_MSG *sCP_MEAS_WINDOW::mbc_return_buffer(int N) {
  return (sMBC_MEAS_FRAME_SP_TO_CP_MSG*)(mbc_info_msg[N].buffer());
}

sMOVB_MEAS_FRAME_SP_TO_CP_MSG *sCP_MEAS_WINDOW::movb_return_buffer(int N) {
  return (sMOVB_MEAS_FRAME_SP_TO_CP_MSG*)(movb_info_msg[N].buffer());
}

STRING sCP_MEAS_WINDOW::compose_output_pathname() 
{
  if (output_filename[0] == '/') // output_filename is already an abolute path
    return output_filename;
  STRING p = xnew char[strlen(cp_info.run_dir) + strlen(output_filename) + 2];
  sprintf(p, "%s/%s", cp_info.run_dir, output_filename);
  return p;
}

STRING sCP_MEAS_WINDOW::compose_output_filename()
{
  cSTRING cdi_meas_window_name = cdi_meas_window->name;

  if (cdi_meas_window->is_average_mme) {
    cp_info.avg_mme_ckpt_preit_filename = cdi_meas_window_name + std::string(".pre_IT.avg.ckpt.fnc");
    cp_info.avg_mme_ckpt_filename = cdi_meas_window_name + std::string(".avg.ckpt.fnc");
    cp_info.avg_mme_ckpt_tmp_filename = cdi_meas_window_name + std::string(".avg.ctmp.fnc");
    cp_info.avg_mme_ckpt_preit_filename_cond = cdi_meas_window_name + std::string(".pre_IT.avg.ckpt.vnc");
    cp_info.avg_mme_ckpt_filename_cond = cdi_meas_window_name + std::string(".avg.ckpt.vnc");
    cp_info.avg_mme_ckpt_tmp_filename_cond = cdi_meas_window_name + std::string(".avg.ctmp.vnc");
    return NULL;
  }

  cSTRING window_extension;
  if (is_composite) {
    switch (meas_window_type) {
    case LGI_FLUID_WINDOW:
      window_extension = "cfnc";
      break;
    case LGI_VOLUME_WINDOW:
      window_extension = "cvnc";
      break;
    case LGI_POROUS_WINDOW:
      window_extension = "cpnc";
      break;
    case LGI_SAMPLING_SURFACE_WINDOW:
    case LGI_SURFACE_WINDOW:
      window_extension = "csnc";
      break;
    case LGI_SAMPLING_SHELL_WINDOW:
    case LGI_SHELL_WINDOW:
      window_extension = "chnc";
      break;
    default:
      msg_internal_error("Unknown measurement file type");
      break;
    }
  } else if (is_development) {
    switch (meas_window_type) {
    case LGI_FLUID_WINDOW:
      window_extension = "dfnc";
      break;
    case LGI_VOLUME_WINDOW:
      window_extension = "dvnc";
      break;
    case LGI_POROUS_WINDOW:
      window_extension = "dpnc";
      break;
    case LGI_SAMPLING_SURFACE_WINDOW:
    case LGI_SURFACE_WINDOW:
      window_extension = "dsnc";
      break;
    case LGI_SAMPLING_SHELL_WINDOW:
    case LGI_SHELL_WINDOW:
      window_extension = "dhnc";
      break;
    default:
      msg_internal_error("Unknown measurement file type");
      break;
    }
  }else {
    switch (meas_window_type) {
    case LGI_FLUID_WINDOW:
      window_extension = is_probe ? "pfnc" : "fnc";
      break;
    case LGI_VOLUME_WINDOW:
      window_extension = is_probe ? "pvnc" : "vnc";
      break;
    case LGI_POROUS_WINDOW:
      window_extension = is_probe ? "ppnc" : "pnc";
      break;
    case LGI_SAMPLING_SURFACE_WINDOW:
    case LGI_SURFACE_WINDOW:
      window_extension = is_probe ? "psnc" : "snc";
      break;
    case LGI_TRAJECTORY_WINDOW:
      window_extension = "pmr";
      break;
    case LGI_SAMPLING_SHELL_WINDOW:
    case LGI_SHELL_WINDOW:
      window_extension = is_probe ? "phnc" : "hnc";
      break;
    default:
      msg_internal_error("Unknown measurement file type");
      break;
    }
  }

  CHARACTER meas_root_name[PLATFORM_MAXPATHLEN];
  if (file_per_frame_p) {
    sprintf(meas_root_name, "%s.hdr", cdi_meas_window_name);
  } else {
    sprintf(meas_root_name, "%s", cdi_meas_window_name);
  }

  // Make the space big enough to accomodate ".tmp" in the end for temporary meas 
  // files used by meas windows started via monitors
  char *filename = new char[6 + strlen(meas_root_name) + strlen(window_extension)];
 
  if (cdi_meas_window->start_time_via_monitors_p && !cdi_meas_window->meas_started_p)
    sprintf(filename, "%s.%s.tmp", meas_root_name, window_extension);
  else 
    sprintf(filename, "%s.%s", meas_root_name, window_extension);
  
  return filename;
}


VOID sCP_MEAS_WINDOW::rename_tmp_meas_file()
{
  // If using file_per_frame_p, the header file will be renamed 
  STRING new_output_filename = strsave(output_filename);
  // It is possible that ".tmp" has already been removed from the filename, which could happen when resuming from ckpt (original run passed
  // the end of initial transient while the ckpt timestep is before that). In that case do not change the filename
  if (strcmp(output_filename + strlen(output_filename) - 4, ".tmp") == 0) {
    new_output_filename[strlen(new_output_filename) - 4] = '\0'; // Remove ".tmp" in the end
    if (platform_file_present(output_filename)) {
      if (0 != rename(output_filename, new_output_filename)) {
        msg_warn("Could not rename \"%s\" to \"%s\": %s",
                  output_filename, new_output_filename, strerror((errno)));
      }
    }
    sprintf(output_filename, "%s", new_output_filename);
    delete[] new_output_filename;
  }
}

// For meas windows started via monitors
VOID sCP_MEAS_WINDOW::start_meas_window(TIMESTEP end_init_transient_detected_time,
                                        TIMESTEP end_init_transient)
{
#if DEBUG_MONITOR
  msg_print("meas window with file %s should start now at timestep %d EITDT: %d EIT: %d", 
            output_filename, cp_info.time, 
            end_init_transient_detected_time, end_init_transient); 
  msg_print("cp_meas_window output time: %d", m_output_timestep);
#endif
  TIME_DESC time_desc = (this->meas_window_type == LGI_VOLUME_WINDOW || this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW) ? &cdi_meas_window->cond_time_desc :
                          &cdi_meas_window->fluid_time_desc;
  dFLOAT start_time = time_desc->start;
  dFLOAT period = time_desc->period;
  dFLOAT end_time = cdi_meas_window->end_time;

  // It is possible that the user specifed meas window end time is earlier
  asINT32 last_set_before_user_end_time = (end_time - start_time)/period;
  asINT32 last_set_before_eitdt = (end_init_transient_detected_time - 1 - start_time)/period - 1;
#if DEBUG_MONITOR
  msg_print("last set before eitdt %d last_set_before_user_end_time %d end_init_transient_detected_time %d start time %f end_time %f period %f",
            last_set_before_eitdt, 
            last_set_before_user_end_time, 
            end_init_transient_detected_time, 
            start_time, 
            end_time,
            period);
#endif

  asINT32 last_set_to_keep = MIN(last_set_before_user_end_time, last_set_before_eitdt);
  
  asINT32 first_set_after_eit = (end_init_transient - start_time + period - 1)/period;
 
  //if (last_set_before_eitdt < first_set_after_eit) // meas window not started yet
  //  return;

  if (last_set_before_user_end_time < first_set_after_eit)
    msg_warn("User specified end time %d is too early to allow at least one frame"
             " after the end of initial transient, thus no meas file is written.", 
             (int)end_time);  

  
  // If the tmp meas file is deleted, then base_frame is not zero and it should be subtracted from the current frame number to
  // get the real frame number in the file.
  first_set_after_eit = MAX(first_set_after_eit - base_frame, 0);

  if (last_set_before_eitdt == (first_set_after_eit - 2) // EIT and EITDT are in the same frame of the measurement window started
    || last_set_before_eitdt == (first_set_after_eit - 1)) {// EIT and EITDT are in two adjacent frames of the measurement window started   
    first_set_to_write_in_file = first_set_after_eit;
    return;
  }

  // When resuming from full ckpt, it is possible that sri_file is still null since this meas window has not started to write
  // any data yet (thus open_sri_file_for_resume() has not been called). Should open the sri file for resume here if that is the case. See PR48004
  if (sri_file == nullptr) {
    open_sri_file_for_resume();
  }

  // Start the measurement window, rename .fnc(snc).tmp measurement file to .fnc(snc)
  sri_file->turn_off_cyclic_mode(TRUE, first_set_after_eit, last_set_to_keep - base_frame, last_set_before_eitdt - base_frame); // stop cyclic mode and set the first retained frame in buffer
  
  rename_tmp_meas_file(); 
 
  // If this cp meas window has one or more monitors attached to it, then should start those monitors by 
  // pushing the signals to msa. 
  ccDOTIMES(i, m_flow_monitors.size()) {
    MONITOR monitor = cp_info.monitors[m_flow_monitors[i]];
#if DEBUG_MONITOR
    msg_print("Monitor %s should start at timestep %d since the cp meas window is started!", monitor->m_name, end_init_transient);
#endif
    asINT32 first_signal_index = 1;   // At least starts from frame 1 (2nd frame) for a meas window controlled by monitors
    ccDOTIMES(j, monitor->m_signal.size()) {
      if (end_init_transient <= monitor->m_timesteps[j])
        break;
      first_signal_index++;
    }
    // Extract the signals which we should keep. 
    // TODO: Note that we should keep the same number of signals as the number of 
    //       frames kept in the meas file. However that is not guarannteed here and it should be fixed. 

    std::vector<sFLOAT> temp_signal(monitor->m_signal.begin()+first_signal_index, monitor->m_signal.end());
    std::vector<TIMESTEP> temp_timesteps(monitor->m_timesteps.begin()+first_signal_index, monitor->m_timesteps.end());
    std::vector<TIMESTEP> temp_next_timesteps(monitor->m_next_timesteps.begin()+first_signal_index, monitor->m_next_timesteps.end());
 
    monitor->m_signal.clear();
    monitor->m_timesteps.clear();
    monitor->m_next_timesteps.clear();

    ccDOTIMES(index, temp_signal.size()) {
#if DEBUG_MONITOR
      msg_print("Push signal %f at timestep %d to msa for monitor %s", temp_signal[index], temp_timesteps[index], monitor->m_name);
#endif
      monitor->append_and_analyze_signal(temp_signal[index], temp_timesteps[index], temp_next_timesteps[index], TRUE, FALSE); // Do not need to convert units again. 
    }
  }
  ccDOTIMES(i, m_solid_monitors.size()) {
    MONITOR monitor = cp_info.monitors[m_solid_monitors[i]];
#if DEBUG_MONITOR
    msg_print("Monitor %s should start at timestep %d since the cp meas window is started!", monitor->m_name, end_init_transient);
#endif
    asINT32 first_signal_index = 1;   // At least starts from frame 1 (2nd frame) for a meas window controlled by monitors
    ccDOTIMES(j, monitor->m_signal.size()) {
      if (end_init_transient <= monitor->m_timesteps[j])
        break;
      first_signal_index++;
    }
    // Extract the signals which we should keep. 
    // TODO: Note that we should keep the same number of signals as the number of 
    //       frames kept in the meas file. However that is not guarannteed here and it should be fixed. 

    std::vector<sFLOAT> temp_signal(monitor->m_signal.begin()+first_signal_index, monitor->m_signal.end());
    std::vector<TIMESTEP> temp_timesteps(monitor->m_timesteps.begin()+first_signal_index, monitor->m_timesteps.end());
    std::vector<TIMESTEP> temp_next_timesteps(monitor->m_next_timesteps.begin()+first_signal_index, monitor->m_next_timesteps.end());
 
    monitor->m_signal.clear();
    monitor->m_timesteps.clear();
    monitor->m_next_timesteps.clear();

    ccDOTIMES(index, temp_signal.size()) {
#if DEBUG_MONITOR
      msg_print("Push signal %f at timestep %d to msa for monitor %s", temp_signal[index], temp_timesteps[index], monitor->m_name);
#endif
      monitor->append_and_analyze_signal(temp_signal[index], temp_timesteps[index], temp_next_timesteps[index], TRUE, FALSE); // Do not need to convert units again. 
    }
  }
}

static sriINT find_frame_before(SRI_FILE sri_file, TIMESTEP time)
{
  // Binary search might make sense here, but the frame of interest is likely to be near the end of the file.
  for (sriINT i = sri_file->n_frames() - 1; i >= 0; i--) {
    sriINT start_time, end_time;
    sri_file->set_meas_frame(i);
    if (sri_file->read_meas_frame_time(&start_time, &end_time) != SRI_SUCCESS)
      return -1;
    if (end_time <= time)
      return i;
  }
  return -1;
}

static sriINT estimate_prev_nsets_written(TIME_DESC time_desc, STP_REALM realm)
{
  // If not resuming from ckpt, prev_nsets_written is 0. If resuming from full ckpt, prev_nsets_written 
  // is extracted from the ckpt file. If resuming from MME ckpt, prev_nsets_written is extracted from
  // the meas files, but if the meas files are missing, we need to fall back on the calculation shown
  // here. This calculation is flawed if the period of a meas window has been changed previously via
  // the --meas_timing option.
  TIMESTEP clear_time;
  TIMESTEP output_time;
  asINT32  n_prior_frames;

  TIMESTEP start_base_time = cp_info.start_time * cp_info.n_user_base_steps;  //in base steps - always 0

  // Set the initial time
  output_time = compute_next_time(start_base_time, cp_info.restart_base_time,
                                  time_desc, &clear_time, &n_prior_frames);   /* in base_steps */

  return n_prior_frames;
}

VOID sCP_MEAS_WINDOW::warn_about_meas_window_timing_changes_and_maybe_set_prev_nsets_written()
{
  STP_REALM realm = lgi_meas_window_type_realm(this->meas_window_type);
  sriINT prior_frame = -1;
  if (cp_info.is_mme_checkpoint_restore) {
    BOOLEAN done = FALSE;
    if (platform_file_present(output_filename)) {
      if (sri_file == NULL)
        open_sri_file_for_resume();
      if (sri_file != NULL) {
        prior_frame = find_frame_before(sri_file, cp_info.restart_time);
        prev_nsets_written = prior_frame + 1 + base_frame;      
        done = TRUE;
      }
    }
    if (!done)
      prev_nsets_written = estimate_prev_nsets_written(cdi_meas_window->is_time_desc_superseded
                                                       ? &cdi_meas_window->superseded_time_desc
                                                       : &cdi_meas_window->fluid_time_desc,
                                                       realm);
  } else {
    // prev_nsets_written and base_frame either 0 or set by read_meas_window_ckpt_data
    prior_frame = prev_nsets_written - 1 - base_frame;
  }

  initial_prev_nsets_written = prev_nsets_written;

  // Issue appropriate warnings about changes relative to the original time desc. The original may be either
  // based on the content of the CDI file, or based on a prior run that was checkpointed and is now being
  // restarted.
  if (!cdi_meas_window->is_time_desc_superseded 
      || prior_frame < 0 
      || cdi_meas_window->superseded_time_desc_warning_issued)
    return;

  TIME_DESC old_time_desc = &cdi_meas_window->superseded_time_desc;
  TIME_DESC new_time_desc = (this->meas_window_type == LGI_VOLUME_WINDOW || this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW) ? &cdi_meas_window->cond_time_desc :
                          &cdi_meas_window->fluid_time_desc;


  if (new_time_desc->repeat <= 0                  // No warning about timing changes if no frames to be written
      || !platform_file_present(output_filename)) // No warning about timing changes if file is missing
    return;

  if (sri_file == NULL)
    open_sri_file_for_resume();
  if (sri_file == NULL)
    return; // should never happen

  sri_file->set_meas_frame(prior_frame);
  sriINT start_time, end_time;
  if (sri_file->read_meas_frame_time(&start_time, &end_time) != SRI_SUCCESS)
    return;

  cdi_meas_window->superseded_time_desc_warning_issued = TRUE;

  TIMESTEP prior_old_midpoint = start_time + (end_time - start_time) / 2;
  
  TIMESTEP clear_time;
  asINT32  n_prior_frames;
  TIMESTEP output_time = compute_next_time(0, cp_info.restart_base_time,
                                           new_time_desc, &clear_time, &n_prior_frames);
  TIMESTEP new_midpoint = (clear_time + output_time + 1) / 2;

#if 0 // Alternative way to compute prior_old_midpoint that breaks down when restoring from MME checkpoint
  output_time = compute_next_time(0, cp_info.restart_base_time, 1, 
                                  old_time_desc, &clear_time, &n_prior_frames);
  TIMESTEP old_midpoint = (clear_time + output_time + 1) / 2;
  TIMESTEP prior_old_midpoint = old_midpoint - old_time_desc->period;
#endif

  TIMESTEP frame_delta = new_midpoint - prior_old_midpoint;

  if (prior_old_midpoint > 0) { // at least one frame exists in the meas files
    if (new_time_desc->period != old_time_desc->period) {
      msg_warn("For measurement window \"%s\", the period between frames"
               " has been changed from %d to %d timesteps. The last frame"
               " was written at %d timesteps and the next frame will be written at %d timesteps.",
               cdi_meas_window->name, old_time_desc->period / cp_info.n_user_base_steps, 
               new_time_desc->period / cp_info.n_user_base_steps, 
               prior_old_midpoint / cp_info.n_user_base_steps, new_midpoint / cp_info.n_user_base_steps);
    }
    else {
      if (frame_delta != new_time_desc->period) {   
        msg_warn("For measurement window \"%s\", the period is %d timesteps, but the last frame"
                 " was written at %d timesteps and the next frame will be written at %d timesteps.",
                 cdi_meas_window->name, new_time_desc->period / cp_info.n_user_base_steps,
                 prior_old_midpoint / cp_info.n_user_base_steps, new_midpoint / cp_info.n_user_base_steps);
#if 0
        msg_warn("For measurement window \"%s\", the distance between the next frame written"
                 " to the measurement files and the prior frame will be %d timesteps,"
                 " not the measurement window period (%d timesteps)",
                 cdi_meas_win->name, frame_delta / cp_info.n_user_base_steps,
                 new_time_desc->period / cp_info.n_user_base_steps);
#endif
      }
    }
  }
}

// This cannot be called until the CDI meas window time descs are updated, which cannot 
// occur until after ALL the DGF meas windows are read.
VOID sCP_MEAS_WINDOW::init_clear_and_output_times()
{
  // This clause allows for windows where all vars are eliminated due to --disable_particle_modeling
  // If resuming from full ckpt while the average mme is already stopped, then avg mme window should be actually inactive
  if ((!is_average_mme && n_variables <= 0) || (is_average_mme && cp_info.average_mme_stopped)) {
    m_clear_timestep  = TIMESTEP_NEVER;
    m_output_timestep = TIMESTEP_NEVER;
    m_next_update_time.clear_time  = TIMESTEP_NEVER;
    m_next_update_time.output_time = TIMESTEP_NEVER;
    
    if (this->coupling_window_p) {
      SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_index;
      surface_coupling->unrounded_output_time      = TIMESTEP_NEVER;
      surface_coupling->unrounded_next_output_time = TIMESTEP_NEVER;
    }

    is_valid = FALSE;
    return;
  }

  TIMESTEP clear_time;
  TIMESTEP output_time;
  dFLOAT unrounded_output_time = 0; // unrounded PowerTHERM launch time corresponding to the meas window output time. See PR41598
  asINT32  n_prior_frames;

  TIMESTEP start_time = cp_info.start_time * cp_info.n_user_base_steps;  //in base steps - always 0
  TIMESTEP restart_base_time = cp_info.restart_base_time;
  //if(meas_window_type == 6 || meas_window_type == 7)
  //  restart_base_time = cp_info.n_conduction_base_steps - 1;
  STP_REALM realm = lgi_meas_window_type_realm(this->meas_window_type);
  TIME_DESC time_desc = (realm == STP_COND_REALM) ? &cdi_meas_window->cond_time_desc : &cdi_meas_window->fluid_time_desc;

  // Set the initial time
  output_time = compute_next_time(start_time, restart_base_time,
                                  time_desc, &clear_time, &n_prior_frames);
  if (this->coupling_window_p) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_index;
#if DEBUG_VARIABLE_POWERTHERM_COUPLING
    msg_print("compute next unrounded time n_prior_frames %d", n_prior_frames);
#endif
    unrounded_output_time = compute_next_unrounded_time(n_prior_frames,
                                                        &surface_coupling->unrounded_time_desc);   /* in base_steps */
  }

  if (is_average_mme && !cp_info.average_mme_stopped && !cdi_meas_window->disabled_by_meas_include_exclude) {
    // TODO: change to some member variable?
    CDI_MEAS_WINDOW cdi_meas_win = cdi_meas_window;
    cp_info.avg_mme_ckpt_interval = time_desc->interval;
#if DEBUG_AVG_MME
    msg_print("Avg mme window rounded time desc: start %d period %d repeat %d interval %d",
              cdi_meas_win->fluid_time_desc.start,
              cdi_meas_win->fluid_time_desc.period,
              cdi_meas_win->fluid_time_desc.repeat,
              cdi_meas_win->fluid_time_desc.interval
              );
#endif

    // If started via time, figure out the time to reset, time to end, etc.
    if (!cdi_meas_win->start_time_via_monitors_p) {
      TIMESTEP start = time_desc->start + time_desc->interval; // First avg mme ckpt output time
      TIMESTEP period = time_desc->period;
      TIMESTEP end = start + period * (time_desc->repeat - 1);
      // Figure out the first start time after ckpt resume
      while (start < cp_info.restart_time)
        start += period;

      if (start <= end) {
#if DEBUG_AVG_MME
        msg_print("Insert periodic ckpt events starting from time %d in the queue", start);
#endif
        CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
        new_queue_entry->id = EVENT_ID_AVG_MME_CKPT;
        new_queue_entry->timestep = start;
        new_queue_entry->recur_rate = period;
        new_queue_entry->initial_condition = TRUE;
        new_queue_entry->precious = FALSE;
        cp_info.async_ckpt_queue->add_entry(new_queue_entry);
      }

      //cp_info.request_to_stop_avg_mme = TRUE;
      cp_info.time_to_stop_avg_mme = end;
#if DEBUG_AVG_MME
      msg_print("Stop mme ckpt at timestep %d", end);
#endif
     } else { // If started via monitors, start to write average mme ckpt from timestep 0
      time_desc->start = 0;
      time_desc->repeat = -1; // Tell SP to repeat forever
#if DEBUG_AVG_MME
      msg_print("Insert periodic ckpt events starting from 0 in the queue");
#endif
      CKPT_QUEUE_ENTRY new_queue_entry = xnew sCKPT_QUEUE_ENTRY;
      new_queue_entry->id = EVENT_ID_AVG_MME_CKPT;
      new_queue_entry->timestep = cdi_meas_win->period;
      new_queue_entry->recur_rate = cdi_meas_win->period;
      new_queue_entry->initial_condition = TRUE;
      new_queue_entry->precious = FALSE;
      cp_info.async_ckpt_queue->add_entry(new_queue_entry);
    }
  }

  if (output_time == TIMESTEP_NEVER)
    is_valid = FALSE;

  if (!cp_info.is_full_checkpoint_restore) {
    // Restart from MME checkpoint or start from t=0

    // For a restart from an MME checkpoint, the clear time must be set to the
    // restart time, because no prior meas window data exists.
      
    //clear_time = MAX(clear_time, cp_info.time);
    clear_time = MAX(clear_time, restart_base_time);  // in base steps
  }

  m_clear_timestep  = clear_time;   // in base steps
  m_output_timestep = output_time;  // in base steps

#if DEBUG_AVG_MME
  if (is_average_mme)
    msg_print("clear time %d output time %d", m_clear_timestep, m_output_timestep);
#endif
  if (this->coupling_window_p) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_index;
#if DEBUG_VARIABLE_POWERTHERM_COUPLING
    msg_print("SET surface coupling unrounded output time to %f", unrounded_output_time);
#endif
    surface_coupling->unrounded_output_time = unrounded_output_time;
  }
  // if it is a coupling measurement window and the first phase is ignored, then we should update the cdi_meas_window->time_desc here
  // to get the correct next output_time
  if (this->coupling_window_p)
  {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_index;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    if ((cmdl->n_coupling_phases >= 2) &&
        (cmdl->m_coupling_phase_descs[1].start == cmdl->m_coupling_phase_descs[0].start)) {
      // start should be a fictitious start so that compute_next_time() gets the correct value
      time_desc->start = cmdl->m_coupling_phase_descs[1].start - cmdl->m_coupling_phase_descs[1].interval;   // in user's steps
      time_desc->start *= cp_info.n_user_base_steps;   // in base steps
      time_desc->period = cmdl->m_coupling_phase_descs[1].period * cp_info.n_user_base_steps; // in base steps
      time_desc->interval = cmdl->m_coupling_phase_descs[1].interval * cp_info.n_user_base_steps; // in base steps

      surface_coupling->unrounded_time_desc.start = cmdl->m_coupling_phase_descs[1].exact_start - cmdl->m_coupling_phase_descs[1].exact_interval;   // in user's steps
      surface_coupling->unrounded_time_desc.start *= cp_info.n_user_base_steps;   // in base steps
      surface_coupling->unrounded_time_desc.period = cmdl->m_coupling_phase_descs[1].exact_period * cp_info.n_user_base_steps; // in base steps
      surface_coupling->unrounded_time_desc.interval = cmdl->m_coupling_phase_descs[1].exact_interval * cp_info.n_user_base_steps; // in base steps
    }
  }

  // Set the next time
  output_time = compute_next_time(output_time, output_time,
                                  time_desc, &clear_time, &n_prior_frames);

  m_next_update_time.clear_time  = clear_time;
  m_next_update_time.output_time = output_time;

  if (this->coupling_window_p) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + coupling_model_index;
    unrounded_output_time = compute_next_unrounded_time(n_prior_frames,
                                                        &surface_coupling->unrounded_time_desc);
    surface_coupling->unrounded_next_output_time = unrounded_output_time;
  }
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::allocate_sp_meas_cells()
{
  m_sp_meas_cells = xnew MEAS_FLOAT_TYPE *[total_sps];

  sINT64 total_sp_meas_cells = 0;
  asINT32 n_sps = 0;
  ccDOTIMES(i, total_sps) {
    if (m_sp_n_meas_cells[i] > 0) {
      total_sp_meas_cells += m_sp_n_meas_cells[i];
      n_sps++;
    }
  }

  MEAS_FLOAT_TYPE *sp_meas_cells = xnew MEAS_FLOAT_TYPE[total_sp_meas_cells * n_variables];
  m_participating_sps     = xnew STP_PROC[n_sps];

  receive_requests = xnew MPI_Request[n_sps];
  ccDOTIMES(i, n_sps) {
    receive_requests[i] = MPI_REQUEST_NULL;
  }

  moving_meas_count_receive_requests = xnew MPI_Request[total_sps];
  moving_meas_data_receive_requests = xnew MPI_Request[total_sps];
  ccDOTIMES(sp, total_sps) {
    moving_meas_count_receive_requests[sp] = MPI_REQUEST_NULL;
    moving_meas_data_receive_requests[sp] = MPI_REQUEST_NULL;
  }

  asINT32 nth_participating_sp = 0;
  ccDOTIMES(i, total_sps) {
    m_sp_meas_cells[i] = sp_meas_cells;
    if (m_sp_n_meas_cells[i] > 0) {
      sp_meas_cells += (sINT64)m_sp_n_meas_cells[i] * n_variables; 
      m_participating_sps[nth_participating_sp++] = i;
    }
  }

  resolve_sp_meas_cell_ptrs();  // convert SP meas cell references into pointers

  // Allocate a flat array of floats/doubles for the moving measurement cells.
  // They will be composited in place from multiple SPs
  m_moving_meas_cells = xnew MEAS_FLOAT_TYPE[n_moving_meas_cells * n_variables];
  m_moving_data_recv_buffer = xnew std::vector<MEAS_FLOAT_TYPE>[total_sps];
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::allocate_sp_meas_cells()
{
  MPI_Status mpi_status;
  int tag = make_mpi_tag<eMPI_MSG::MWIN>(index);

  // recv vector of meas cell indices that each child will send
  ccDOTIMES(i, m_n_child_sps) {
    MPI_Probe(m_child_sps[i], tag, eMPI_sp_cp_comm, &mpi_status);
    int n_cells;
    MPI_Get_count(&mpi_status, eMPI_MEAS_CELL_INDEX, &n_cells);
    m_n_child_meas_cells[i] = n_cells;
    m_child_global_meas_cell_indices[i] = xnew STP_MEAS_CELL_INDEX[ n_cells ];
    RECV_EXA_SIM_MSG<STP_MEAS_CELL_INDEX> recv_child_global_meas_cell_indices(tag, n_cells, m_child_global_meas_cell_indices[i], m_child_sps[i]);
    g_exa_sp_cp_comm.recv(recv_child_global_meas_cell_indices.mpi_msg);

    m_child_meas_cells[i] = xnew REDUCTION_MEAS_CELL_VAR[n_cells * n_variables];
  }

  // Make sure all meas cells are referenced by some ublk or surfel
  STP_MEAS_CELL_INDEX *meas_cell_indices0 = m_child_global_meas_cell_indices[0];
  STP_MEAS_CELL_INDEX *meas_cell_indices1 = m_child_global_meas_cell_indices[1];
  STP_MEAS_CELL_INDEX *end_meas_cell_indices0 = meas_cell_indices0 + m_n_child_meas_cells[0];
  STP_MEAS_CELL_INDEX *end_meas_cell_indices1 = meas_cell_indices1 + m_n_child_meas_cells[1];

  asINT32 dev_meas_cells_empty = 0;

  ccDOTIMES(i, n_stationary_meas_cells) {
    BOOLEAN found = FALSE;
    if (meas_cell_indices0 < end_meas_cell_indices0
        && *meas_cell_indices0 == i) {
      found = TRUE;
      meas_cell_indices0++;
    }
    if (meas_cell_indices1 < end_meas_cell_indices1
        && *meas_cell_indices1 == i) {
      found = TRUE;
      meas_cell_indices1++;
    }


    if (!found) {
      // Development meas windows are allowed to have empty measurment cells corresponding to segments which receive no surfel contribution.
      // Count the number of empty meas cells (don't send them either) and then issue a warning outside of the meas_cells loop
      if (is_development) 
        dev_meas_cells_empty++;
      else 
        msg_internal_error("Meas %s index %d of %s meas window \"%s\"  index %d is not referenced by any %s %s",
                           is_lgi_meas_window_type_surface(meas_window_type) ? "surfel" : "cell",
                           get_meas_surfel_index(i), type_name(),
                           cdi_meas_window->name, this->index, this->meas_window_type == LGI_VOLUME_WINDOW || this->meas_window_type == LGI_SHELL_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW ? "conduction" : "flow",
                           is_lgi_meas_window_type_surface(meas_window_type) ? "surfels" : "ublks");
    }
  }

  if (is_development && dev_meas_cells_empty > 0) {
    dFLOAT per_dev_meas_cells_empty = (dFLOAT) dev_meas_cells_empty / (dFLOAT) n_stationary_meas_cells;
    if (per_dev_meas_cells_empty > 0.50)
      msg_warn("For development meas window \"%s\", %d of %lu meas cells include no %s."
               " Perhaps a larger segment size would be more appropriate.",
               cdi_meas_window->name, dev_meas_cells_empty, n_stationary_meas_cells, sri_file_type == SRI_SURFACE_DEV_TYPE ? "surfels" : "voxels");
  } 

  // Allocate a flat array of floats/doubles for the moving measurement cells.
  // They will be composited in place from multiple SPs
  if (n_moving_meas_cells > 0) {
    m_moving_meas_cells = xnew MEAS_FLOAT_TYPE[n_moving_meas_cells * n_variables];
    m_moving_data_recv_buffer = xnew std::vector<REDUCTION_MEAS_CELL_VAR>[total_sps];

    moving_meas_count_receive_requests = xnew MPI_Request[total_sps];
    moving_meas_data_receive_requests = xnew MPI_Request[total_sps];
    ccDOTIMES(sp, total_sps) {
      moving_meas_count_receive_requests[sp] = MPI_REQUEST_NULL;
      moving_meas_data_receive_requests[sp] = MPI_REQUEST_NULL;
    }
  }

}

template <typename MEAS_FLOAT_TYPE>
MEAS_FLOAT_TYPE tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::scale_variable(const MEAS_FLOAT_TYPE var,
									   sriFLOAT& spatial_avg_factor, // the value will be updated for 5g in this function
									   const sriFLOAT time_avg_factor,
									   const SRI_VARIABLE_TYPE var_type,
									   const sriFLOAT scale_factor_for_porosity)
{

  if(var_type > SRI_VARIABLE_FIRST_USER_VARIABLE) {
    asINT32 i = 42;
  }

  switch (var_type) {
  case SRI_VARIABLE_STD_DEV_XVEL:
  case SRI_VARIABLE_STD_DEV_YVEL:
  case SRI_VARIABLE_STD_DEV_ZVEL:
  case SRI_VARIABLE_STD_DEV_VEL_MAG:
  case SRI_VARIABLE_STD_DEV_PRESSURE:
  case SRI_VARIABLE_STD_DEV_TEMP:
  case SRI_VARIABLE_STD_DEV_DENSITY: {
    sriFLOAT scale_factor = spatial_avg_factor * time_avg_factor;
    /* std dev variables from the SPs are scaled by cell volume/area squared */
    return (std::sqrt( var * spatial_avg_factor * scale_factor));
  }
  case SRI_VARIABLE_N_SCREENED_SURFELS:
  case SRI_VARIABLE_SCREENED_AREA:
  case SRI_VARIABLE_AREA:
  case SRI_VARIABLE_N_SCREENED_VOXELS:
  case SRI_VARIABLE_SCREENED_VOLUME:
  case SRI_VARIABLE_VOLUME:
    return (var * time_avg_factor);
  case SRI_VARIABLE_DEFROST_TIME:
    return var; // Do not scale defrost time
  default: {
      sriFLOAT scale_factor = spatial_avg_factor * time_avg_factor;
      if (cp_info.is_5g_sim) {
	/**NOTE: Global volume must be the first var in 5G fluid composite or development file, because its inverse is used as "spatial_avg_factor" to scale other ddp vars. **/
	// Per phase pressure is not multiplied by spatial_avg_factor = 1/Sum(V_i) (1/Sum(V_i phi_i) for large pore) here.
        // Instead it is scaled by Sum(H_i V_i) in exa_meas_composite_totals during post-processing
        // For global volume, the meas variable is Sum(V_i), and it is not multiplied by spatial_avg_factor either.

        if ((cp_info.cvid_helper->is_per_phase_5g_var_id(var_type) &&
             (cp_info.cvid_helper->get_5g_var_type(var_type)==SRI_VARIABLE_PRESSURE))
            || (cp_info.cvid_helper->get_5g_var_type(var_type)==SRI_VARIABLE_VOLUME)
	    || (cp_info.cvid_helper->get_5g_var_type(var_type)==SRI_VARIABLE_PORE_VOLUME)
	    || (cp_info.cvid_helper->get_5g_var_type(var_type)==SRI_VARIABLE_PM_VOLUME)) {
	  if (cp_info.cvid_helper->is_global_5g_var_id(var_type) && cp_info.cvid_helper->get_5g_var_type(var_type)==SRI_VARIABLE_VOLUME) {
            // For global volume, use the measured volume (Sum(V_i phi_i)) to update scale_factor
            //msg_print("Should update global volume from %f to %f", scale_factor, 1.0/var); 
            spatial_avg_factor = 1.0/var;
          }
          return (var * time_avg_factor);
	} else if (cp_info.cvid_helper->get_5g_var_type(var_type)==SRI_VARIABLE_POROSITY) {
          return (var * time_avg_factor * scale_factor_for_porosity);
	} else
	  return (var * scale_factor);
      } else
        return (var * scale_factor);
    }
  }
}
  
VOID sCP_MEAS_WINDOW::allocate_ref_frame_indices ()
{
  if (cp_info.n_lrfs() > 0) {
    asINT32 n_points = (sri_file_type == SRI_SURFACE_TYPE) ? (CP_SURFACE_MEAS_WINDOW(this))->m_n_surfels :  n_meas_cells;
    m_ref_frame_indices.resize(n_points, SRI_INVALID_REF_FRAME_INDEX);
  }
}

sCP_MEAS_WINDOW::sCP_MEAS_WINDOW(cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index,
                                 cBOOLEAN is_meas_vars_output_double_precision)
{
  is_valid           = TRUE;
  sri_file_type      = SRI_UNDEFINED_FILE_TYPE;
  sri_file           = NULL;
  prev_nsets_written = 0;
  initial_prev_nsets_written = 0;
  base_frame         = 0;
  first_set_to_write_in_file = -1;
  cdi_window_index = dgf_window->cdi_meas_window_index;

  first_frame_after_ckpt = -1;

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  m_is_particle_trajectory_window = FALSE;   //If it is a trajectory window sTRAJECTORY_WINDOW's consturctor will set this true.
//#endif

  if (dgf_window->cdi_meas_window_index >= cp_info.n_cdi_meas_windows)
    msg_internal_error("CDI meas window index %d from LGI file exceeds number"
                       " of meas windows present in CDI file %d.", dgf_window->cdi_meas_window_index, cp_info.n_cdi_meas_windows);

  cdi_meas_window      = &cp_info.cdi_meas_windows[ dgf_window->cdi_meas_window_index ];
  cdi_meas_window->n_meas_files++;
  index                = window_index;
  cdi_meas_window->one_window = index;
    
  last_meas_cell_index = -1;
  last_sp              = -1;

  coupling_window_p    = FALSE;
  coupling_model_index = -1;
  rotational_dynamics_index = -1;
  stationary_scale_factors_precomputed_p = FALSE;
  moving_scale_factors_precomputed_p = FALSE;
  m_some_meas_cell_spans_procs = FALSE;
  n_surfels.resize(cp_info.n_sri_faces, 0);
  first_surfel.resize(cp_info.n_sri_faces, 0);
  first_point.resize(cp_info.n_sri_faces, 0);
  n_surfels_with_two_meas.resize(cp_info.n_sri_faces, 0);
  m_sp_n_meas_cells    = cnew STP_MEAS_CELL_INDEX[total_sps]; // zero
  m_n_moving_meas_cells_per_sp = cnew STP_MEAS_CELL_INDEX[total_sps]; 

  meas_window_type     = dgf_window->meas_type;
  is_average_mme       = meas_window_type == LGI_AVERAGE_MME_WINDOW;
  meas_cell_scale      = dgf_window->meas_cell_scale;
  is_composite         = (dgf_window->meas_window_flags & DGF_MEAS_WINDOW_IS_COMPOSITE) != 0;
  is_development       = (dgf_window->meas_window_flags & DGF_MEAS_WINDOW_IS_DEVELOPMENT) != 0;
  is_per_voxel         = (!is_composite && !is_development) && 
                         ((dgf_window->meas_window_flags & DGF_MEAS_WINDOW_PER_VOXEL) != 0);
  // is_probe          = (dgf_window->meas_window_flags & DGF_MEAS_WINDOW_IS_PROBE) != 0;
  is_probe             = cdi_meas_window->mstp.meas_type == CDI_MEASTYPE_PROBE;
  n_meas_cells         = (is_development) ? dgf_window->num_meas_cells : dgf_window->num_measurements;
  n_moving_meas_cells  = dgf_window->num_moving_meas_cells;
  n_stationary_meas_cells = n_meas_cells - n_moving_meas_cells;
  file_per_frame_p     = (cdi_meas_window->mstp.standard_mask & CDI_MEAS_OPT_FILE_PER_FRAME) != 0;
  
  is_output_in_local_csys = (cdi_meas_window->mstp.standard_mask & 
                             CDI_MEAS_OPT_LOCAL_CSYS) != 0;

  is_meas_vars_output_dp  = is_meas_vars_output_double_precision;

  calc_htc_for_adb_walls = (cdi_meas_window->mstp.standard_mask & CDI_MEAS_OPT_CALC_HTC_FOR_ADB_WALLS) != 0;

//  global_nirf_info_ = MPI_REQUEST_NULL;

  asINT32 n_dims = cp_info.n_dims;
  sriINT max_sri_int = ~((sriINT)1 << (8 * sizeof(sriINT) - 1));
  ccDOTIMES(i, n_dims) {
    min_bound[i] = max_sri_int;
    max_bound[i] = -max_sri_int;
  }
  if (n_dims == 2) {
    min_bound[2] = 0;
    max_bound[2] = 0;
  }

  CDI_MEAS_WINDOW_VAR_SET var_set;

  switch (meas_window_type) {
  case LGI_AVERAGE_MME_WINDOW:
    break;
  case LGI_FLUID_WINDOW:
    var_set       = &cdi_meas_window->fluid_var_set;
    sri_file_type = (is_composite ? SRI_COMPOSITE_FLUID_TYPE
                     : (is_development ? SRI_FLUID_DEV_TYPE : SRI_FLUID_TYPE));
    break;
  case LGI_POROUS_WINDOW:
    var_set       = &cdi_meas_window->porous_var_set;
    sri_file_type = (is_composite ? SRI_COMPOSITE_FLUID_TYPE
                     : (is_development ? SRI_FLUID_DEV_TYPE : SRI_FLUID_TYPE));
    break;
  case LGI_SURFACE_WINDOW:
    var_set       = &cdi_meas_window->surface_var_set;
    sri_file_type = (is_composite ? SRI_COMPOSITE_SURFACE_TYPE
                     : (is_development ? SRI_SURFACE_DEV_TYPE : SRI_SURFACE_TYPE));
    break;
  case LGI_SAMPLING_SURFACE_WINDOW:
    var_set       = &cdi_meas_window->fluid_var_set;
    sri_file_type = (is_composite ? SRI_COMPOSITE_SURFACE_TYPE
                     : (is_development ? SRI_SURFACE_DEV_TYPE : SRI_SURFACE_TYPE));
    break;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  case LGI_TRAJECTORY_WINDOW:
    var_set       = &cdi_meas_window->fluid_var_set;
    sri_file_type = SRI_TRAJECTORY_TYPE;
    break;
  case LGI_VOLUME_WINDOW:
    var_set       = &cdi_meas_window->conduction_solid_var_set;
    sri_file_type = (is_composite ? SRI_COMPOSITE_FLUID_TYPE
                     : (is_development ? SRI_FLUID_DEV_TYPE : SRI_FLUID_TYPE));
    break;
  case LGI_SHELL_WINDOW:
    var_set       = &cdi_meas_window->shell_var_set;
    sri_file_type = (is_composite ? SRI_COMPOSITE_SURFACE_TYPE
                     : (is_development ? SRI_SURFACE_DEV_TYPE : SRI_SURFACE_TYPE));
    break;
  case LGI_SAMPLING_SHELL_WINDOW:
    var_set       = &cdi_meas_window->conduction_solid_var_set;
    sri_file_type = (is_composite ? SRI_COMPOSITE_SURFACE_TYPE
                     : (is_development ? SRI_SURFACE_DEV_TYPE : SRI_SURFACE_TYPE));
    break;
//#endif
  default:
    msg_internal_error("Unrecognized meas window type");
  } 

  // Average mme window does not write any sri file, instead it writes avg mme ckpt file which is handled by the ckpt routine.
  if (meas_window_type != LGI_AVERAGE_MME_WINDOW) {
    n_variables = var_set->n_vars;
    if (meas_window_type != LGI_TRAJECTORY_WINDOW) //Trajectory windows can have n_variables = 0.
    if (n_variables == 0)
      msg_internal_error("Number of measurement variables for %s window \"%s\" is zero.",
                         lgi_meas_window_type_to_name(meas_window_type), cdi_meas_window->name);
    var_types = var_set->var_types;
    m_sp_meas_cell_refs = NULL;

    // In the future, we can find a way to limit this to only when there is a
    // deforming tire in the meas file. We have to include the areas if there
    // because deforming tires area can change over time.
    if ( n_moving_meas_cells > 0 ) {
      if ( is_development ) {
        msg_internal_error("Development windows cannot have moving measurements");
      }
      var_types = new SRI_VARIABLE_TYPE[n_variables+1];

      var_types[0] = SRI_VARIABLE_AREA;

      for(int i=0; i < n_variables; i++) {
        var_types[i+1] = var_set->var_types[i];
      }
      n_variables++;
    }

    // TODO: get rid of this when PowerCASE supports radiation variables
    if (cp_info.is_radiation && std::string(cdi_meas_window->name) == "__radiation__") {
      msg_warn("Adding radiation variables to meas window");
      var_types = new SRI_VARIABLE_TYPE[n_variables+2];
      var_types[0] = SRI_VARIABLE_IRRADIATION;
      var_types[1] = SRI_VARIABLE_RADIOSITY;

      for (int i=0; i < n_variables; i++) {
        var_types[i+2] = var_set->var_types[i];
      }
      n_variables += 2;
    }

    population_var_indices = xnew sINT16[n_variables];
    m_var_component_ids = xnew sINT16[n_variables];
    ccDOTIMES(var_index, n_variables) {
      population_var_indices[var_index] = -1;
      m_var_component_ids[var_index] = -1;
    }

    asINT32 dgf_coarsest_shob_scale = dgf_window->coarsest_shob_scale;

    if (is_scale_coarser(dgf_coarsest_shob_scale, COARSEST_SCALE))
      msg_internal_error("Meas window contains an invalid coarsest shob scale (%d)",
                         dgf_coarsest_shob_scale);

    if (dgf_window->meas_type == LGI_VOLUME_WINDOW || dgf_window->meas_type == LGI_SHELL_WINDOW || dgf_window->meas_type == LGI_SAMPLING_SHELL_WINDOW) {
      if (is_scale_coarser(dgf_coarsest_shob_scale, cdi_meas_window->coarsest_cond_shob_scale)) {
        cdi_meas_window->coarsest_cond_shob_scale = dgf_coarsest_shob_scale;
      }
    } else {
      if (is_scale_coarser(dgf_coarsest_shob_scale, cdi_meas_window->coarsest_fluid_shob_scale)) {
        cdi_meas_window->coarsest_fluid_shob_scale = dgf_coarsest_shob_scale;
      }
    }
    compute_bytes_per_frame();
  
    n_bytes_since_last_sync = 0;
  }
  output_filename = compose_output_filename();
}

template <typename MEAS_FLOAT_TYPE> 
tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE> :: tCP_NO_REDUCTION_MEAS_WINDOW
              (cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index, cBOOLEAN is_meas_vars_output_dp)
          : sCP_MEAS_WINDOW(dgf_window, window_index, is_meas_vars_output_dp)
{ 
  m_sp_meas_cells     = NULL; // allocated later by allocate_sp_meas_cells()

  m_next_sp_index_to_test = 0;
  m_participating_sps = NULL;

  m_moving_data_recv_buffer = NULL;
  m_moving_meas_cells = NULL;
  m_next_sp_moving_index_to_test_for_count = 0;
  m_next_sp_moving_index_to_test_for_data = 0;
}

template <typename MEAS_FLOAT_TYPE> tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::
          tCP_REDUCTION_MEAS_WINDOW
          (cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index, cBOOLEAN is_meas_vars_output_dp)
  : sCP_MEAS_WINDOW(dgf_window, window_index, is_meas_vars_output_dp)
{ 
  m_n_child_sps = 0;
  m_n_child_sps_ready = 0;
  m_child_sps[0] = m_child_sps[1] = 0;
  m_child_global_meas_cell_indices[0] = m_child_global_meas_cell_indices[1] = NULL;
  m_n_child_meas_cells[0] = m_n_child_meas_cells[1] = 0;
  m_child_meas_cells[0] = m_child_meas_cells[1] = NULL;
  m_child_receive_requests[0] = m_child_receive_requests[1] = MPI_REQUEST_NULL;

  // This was moved to the finish_init method because n_variables may be changed, for example, by
  // maybe_append_particle_population_size_measurement_var_types.
  // m_meas_variable_sums = cnew MEAS_FLOAT_TYPE[n_variables];

  m_moving_data_recv_buffer = NULL;
  m_moving_meas_cells = NULL;
  m_next_sp_moving_index_to_test_for_count = 0;
  m_next_sp_moving_index_to_test_for_data = 0;
}

template <typename MEAS_FLOAT_TYPE>
tCP_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>:: tCP_FLUID_MEAS_WINDOW
        (cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index, cBOOLEAN is_meas_vars_output_dp)
  : tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>(dgf_window, window_index, is_meas_vars_output_dp)
{

  m_current_meas_cube_location[0] = -1;
  m_current_meas_cube_location[1] = -1;
  m_current_meas_cube_location[2] = -1;

  m_current_meas_cube_first_meas_cell_index = -1;
  m_current_meas_cube_first_meas_cell_is_partial = FALSE;

  m_n_nw_facet_sides          = 0;
  m_n_nw_neighbors            = 0;

#if LGI_COMPLETE_NEAR_WALL_MEAS_CELL_DATA // de-activated near wall meas cell info
  m_nw_face_neighbors_masks   = NULL;
  m_nw_facet_sides            = NULL;      
  m_nw_first_facet_sides      = NULL;  
  m_nw_neighbors              = NULL;          
  m_nw_neighbor_faces         = NULL;     
  m_nw_first_neighbors        = NULL;
#endif 

  m_scales          = NULL;
  m_coords          = NULL;
  m_volumes         = NULL; // allocated later when d_volumes copied here
  m_d_volumes       = NULL; 
  m_part_indices    = NULL; 

}    

template <typename MEAS_FLOAT_TYPE>
VOID tCP_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>::allocate_window_data_for_sri() 
{
  m_scales          = xnew sriBYTE [this->n_meas_cells];
  m_coords          = xnew sriINT  [cp_info.n_dims * this->n_meas_cells];
  m_d_volumes       = (dFLOAT *)cp_vmem_reserve(this->n_meas_cells * sizeof(dFLOAT));
  m_part_indices    = xnew sriPART_INDEX [this->n_meas_cells];
  memset(m_d_volumes, 0, this->n_meas_cells * sizeof(dFLOAT)); // zero so we can accumulate into

  sriBYTE sri_coarsest_scale = sim_scale_to_sri_scale(COARSEST_SCALE);
  memset(m_scales, sri_coarsest_scale, this->n_meas_cells);

  sCP_MEAS_WINDOW::allocate_meas_cell_refs();

}

template <typename MEAS_FLOAT_TYPE>
tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>:: tCP_SURFACE_MEAS_WINDOW
        (cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index, cBOOLEAN is_meas_vars_output_dp)
  : tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>(dgf_window, window_index, is_meas_vars_output_dp)
{
  m_n_vertices        = 0;
  m_n_vertex_refs     = 0;
  m_first_vertex_refs = NULL;
  m_vertex_refs       = NULL; // allocated later when we finally determine the number of vertex refs
  m_faces             = NULL;
  m_facet_ids         = NULL;
  m_surfel_areas      = NULL; // allocated later when d_surfel_areas copied here
  m_d_surfel_areas    = NULL;
  m_surfel_normals    = NULL;
  m_inv_phys_norm     = NULL;
  m_scales            = NULL;
  m_mate_window_read  = FALSE;
  m_n_surfels         = dgf_window->num_meas_cells;
}
//
// Allocation of data for surface meas window was split into three pieces
// allocate_prelim_data_for_sri
// allocate_surface_normals
// allocate_window_data_for_sri
// Combined allocate_prelim_data_for_sri and allocate_surface_normals, since
// both are done before thermal coupling maps are built
//
template <typename MEAS_FLOAT_TYPE>
VOID tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::allocate_prelim_data_for_sri()
{
  m_d_surfel_areas    = (dFLOAT *)cp_vmem_reserve(this->m_n_surfels * sizeof(dFLOAT));
  memset(m_d_surfel_areas, 0, this->m_n_surfels * sizeof(dFLOAT)); // zero so we can accumulate into

  m_scales            = xnew sriBYTE       [this->m_n_surfels];
  sriBYTE sri_coarsest_scale = sim_scale_to_sri_scale(COARSEST_SCALE);
  sri_coarsest_scale |= 0x80; // setting the bit for one measurement per meas surfel.
  memset(m_scales, sri_coarsest_scale, this->m_n_surfels * sizeof(sriBYTE));
  // allocate only for non coupling windows, since it is only needed in DEBUG
  // mode for coupling windows and is done in allocate_coupling_sri_data
  if (!this->coupling_window_p) {
    m_surfel_normals    = xnew sriFLOAT [cp_info.n_dims * this->m_n_surfels];
    m_inv_phys_norm     = xnew sriBOOL  [this->m_n_surfels];
  }
  sCP_MEAS_WINDOW::allocate_meas_cell_refs();
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::allocate_window_data_for_sri()
{
  m_faces             = xnew sriFACE_INDEX [this->n_meas_cells];
  m_first_vertex_refs = xnew sriINT        [this->n_meas_cells];
  if (this->meas_window_type == LGI_SAMPLING_SURFACE_WINDOW || this->meas_window_type == LGI_SAMPLING_SHELL_WINDOW) {
    // facet IDs are needed for PowerCOOL
    m_facet_ids       = xnew sriFACET_ID   [this->n_meas_cells];
  }
}

template<typename T> struct float_type_to_int_type;

template<> 
struct float_type_to_int_type<float>
{
  typedef uINT32 type;
};

template<> 
struct float_type_to_int_type<double>
{
  typedef uINT64 type;
};

template <typename MEAS_FLOAT_TYPE>
VOID tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::composite_moving_meas_cells()
{
  typedef typename float_type_to_int_type<MEAS_FLOAT_TYPE>::type MEAS_INT_TYPE;

  if (this->n_moving_meas_cells == 0)
    return;

  size_t n_stationary_surfels = this->m_n_surfels - this->n_moving_meas_cells;
  for(int i=n_stationary_surfels; i<this->m_n_surfels; i++) {
    this->m_surfel_areas[i] = 0;
  }

  // clear before compositing in place
  memset(this->m_moving_meas_cells, 0, this->n_moving_meas_cells * this->n_variables * sizeof(MEAS_FLOAT_TYPE));

  // The first variable in each meas cell received from the SPs contains the global meas cell index
  ccDOTIMES(sp, total_sps) {
    sINT64 recv_buffer_index = 0;
    ccDOTIMES(meas_cell, this->m_n_moving_meas_cells_per_sp[sp]) {
      // convert first variable to integer meas cell index
      STP_MEAS_CELL_INDEX global_meas_cell_index;
      global_meas_cell_index = reinterpret_cast<MEAS_INT_TYPE&>(this->m_moving_data_recv_buffer[sp][recv_buffer_index++]);

      if ((global_meas_cell_index < 0) || (global_meas_cell_index >= this->n_meas_cells))
        msg_error("Received invalid moving measurement cell index %d from SP %d for window \"%s\"", global_meas_cell_index, sp, this->output_filename);

      // The first variable is area, so pull it out.  We do not increment
      // recv_buffer_index because area also needs to end up in the meas file
      MEAS_FLOAT_TYPE area = this->m_moving_data_recv_buffer[sp][recv_buffer_index];
      this->m_surfel_areas[global_meas_cell_index] += area;

      ccDOTIMES(n, this->n_variables) {
        STP_MEAS_CELL_INDEX moving_meas_array_index = (global_meas_cell_index - n_stationary_surfels) + (n * this->n_moving_meas_cells);
        MEAS_FLOAT_TYPE m = this->m_moving_data_recv_buffer[sp][recv_buffer_index++]; 
        this->m_moving_meas_cells[moving_meas_array_index] += m;
      }
    }
  }
  TIMESTEP lcm_solver_steps = (this->is_cond_window()) ? this->cdi_meas_window->lcm_cond_tsteps : this->cdi_meas_window->lcm_flow_tsteps;
  MEAS_FLOAT_TYPE time_scaling = static_cast<MEAS_FLOAT_TYPE>(lcm_solver_steps)/static_cast<MEAS_FLOAT_TYPE>(this->m_output_timestep - this->m_clear_timestep);

  for(size_t i = n_stationary_surfels; i < this->m_n_surfels; i++) {
    this->m_surfel_areas[i] *= time_scaling;
  }

  this->moving_scale_factors_precomputed_p = FALSE;

  ccDOTIMES(sp,total_sps) {
    this->m_moving_data_recv_buffer[sp].clear();
  }
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_COMPOSITE_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::composite_moving_meas_cells()
{
  typedef typename float_type_to_int_type<REDUCTION_MEAS_CELL_VAR>::type REDUCTION_MEAS_INT_TYPE;
  if (this->n_moving_meas_cells == 0)
    return;

  // clear before compositing in place
  memset(this->m_moving_meas_cells, 0, this->n_moving_meas_cells * this->n_variables * sizeof(MEAS_FLOAT_TYPE));

  // clear the moving meas cells areas
  for(int i=this->n_stationary_meas_cells; i<this->n_meas_cells; i++) {
    this->m_face_areas[i] = 0;
  }

  // The first variable in each meas cell contains the global meas cell index and a ref count
  ccDOTIMES(sp, total_sps) {
    sINT64 recv_buffer_index = 0;
    ccDOTIMES(meas_cell, this->m_n_moving_meas_cells_per_sp[sp]) {
      // convert first variable to integer meas cell index
      STP_MEAS_CELL_INDEX global_meas_cell_index;
      global_meas_cell_index = reinterpret_cast<REDUCTION_MEAS_INT_TYPE&>(this->m_moving_data_recv_buffer[sp][recv_buffer_index++]);

      if ((global_meas_cell_index < 0) || (global_meas_cell_index >= this->n_meas_cells))
        msg_error("Received invalid moving measurement cell index %d from SP %d for window \"%s\"", global_meas_cell_index, sp, this->output_filename);

      MEAS_FLOAT_TYPE area = this->m_moving_data_recv_buffer[sp][recv_buffer_index];
      this->m_face_areas[global_meas_cell_index] += area;

      ccDOTIMES(n, this->n_variables) {
        STP_MEAS_CELL_INDEX moving_meas_array_index = (global_meas_cell_index - this->n_stationary_meas_cells) + (n * this->n_moving_meas_cells);
        this->m_moving_meas_cells[moving_meas_array_index] += this->m_moving_data_recv_buffer[sp][recv_buffer_index++]; 
      }
    }

  }
  TIMESTEP lcm_solver_steps = (this->is_cond_window()) ? this->cdi_meas_window->lcm_cond_tsteps : this->cdi_meas_window->lcm_flow_tsteps;
  MEAS_FLOAT_TYPE time_scaling = static_cast<MEAS_FLOAT_TYPE>(lcm_solver_steps)/static_cast<MEAS_FLOAT_TYPE>(this->m_output_timestep - this->m_clear_timestep);

  for(size_t i = this->n_stationary_meas_cells; i < this->n_meas_cells; i++) {
    this->m_face_areas[i] *= time_scaling;
  }
  this->moving_scale_factors_precomputed_p = FALSE;
}

#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
template <typename MEAS_FLOAT_TYPE>
VOID tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::allocate_coupling_sri_data()
{
  m_faces             = xnew sriFACE_INDEX [this->n_meas_cells];
  m_first_vertex_refs = xnew sriINT        [this->n_meas_cells];
  m_surfel_normals    = xnew sriFLOAT      [cp_info.n_dims * this->n_meas_cells];
  m_inv_phys_norm     = xnew sriBOOL       [this->n_meas_cells];
}
#endif

template <typename MEAS_FLOAT_TYPE>
tCP_COMPOSITE_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>::
                tCP_COMPOSITE_FLUID_MEAS_WINDOW
                (cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index, cBOOLEAN is_meas_vars_output_dp)
  : tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>(dgf_window, window_index, is_meas_vars_output_dp)
{
  m_part_indices = NULL;
  m_part_volumes = NULL;
  m_scale_factor_5g = 1.0;
  sCP_MEAS_WINDOW::allocate_meas_cell_refs();
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_COMPOSITE_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>::allocate_window_data_for_sri()
{
  m_part_indices = xnew sriINT   [this->n_meas_cells];
  m_part_volumes = cnew sriFLOAT [this->n_meas_cells];
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_FLUID_DEV_MEAS_WINDOW<MEAS_FLOAT_TYPE>::allocate_window_data_for_sri()
{
  if (this->n_meas_cells > 0) {
    m_volumes.assign(this->n_meas_cells, 0.0);
  }
}

template <typename MEAS_FLOAT_TYPE>
tCP_COMPOSITE_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::
                tCP_COMPOSITE_SURFACE_MEAS_WINDOW
                (cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index, cBOOLEAN is_meas_vars_output_dp)
  : tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>(dgf_window, window_index, is_meas_vars_output_dp)
{
  m_faces             = NULL;
  m_projected_areas   = NULL;
  m_face_areas        = NULL;
  sCP_MEAS_WINDOW::allocate_meas_cell_refs();
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_COMPOSITE_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::allocate_window_data_for_sri()
{
  m_faces             = xnew sriFACE_INDEX [this->n_meas_cells];
  m_projected_areas   = xnew sriFLOAT      [cp_info.n_dims * this->n_meas_cells];
  m_face_areas        = xnew sriFLOAT      [this->n_meas_cells];

}

template <typename MEAS_FLOAT_TYPE>
tCP_SURFACE_DEV_MEAS_WINDOW<MEAS_FLOAT_TYPE>::
                tCP_SURFACE_DEV_MEAS_WINDOW
                (cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index, cBOOLEAN is_meas_vars_output_dp)
  : tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>(dgf_window, window_index, is_meas_vars_output_dp)
{
  // This data is referenced immediately in sCP_DGF_READER:: read_meas_windows_header, and thus
  // is allocated in the constructor rather than being deferred until allocate_window_data_for_sri() 
  sriINT *face_first_meas_cell = cnew sriINT[3*cp_info.n_sri_faces];
  sriINT *face_n_segments =  cnew sriINT[3*cp_info.n_sri_faces];
  sriINT *face_first_segment = cnew sriINT[3*cp_info.n_sri_faces];
  m_face_is_movb.resize(cp_info.n_sri_faces, FALSE);
  ccDOTIMES(axis,3) {
    m_face_first_meas_cell[axis] = face_first_meas_cell + cp_info.n_sri_faces*axis;
    m_face_n_segments[axis] = face_n_segments + cp_info.n_sri_faces*axis;
    m_face_first_segment[axis] = face_first_segment + cp_info.n_sri_faces*axis;
  }
  sCP_MEAS_WINDOW::allocate_meas_cell_refs();
}


template <typename MEAS_FLOAT_TYPE>
tCP_FLUID_DEV_MEAS_WINDOW<MEAS_FLOAT_TYPE>::
                tCP_FLUID_DEV_MEAS_WINDOW
                (cDGF_MEAS_WINDOW *dgf_window, asINT32 window_index, cBOOLEAN is_meas_vars_output_dp)
  : tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>(dgf_window, window_index, is_meas_vars_output_dp)
{

  // This data is referenced immediately in sCP_DGF_READER:: read_meas_windows_header, and thus
  // is allocated in the constructor rather than being deferred until allocate_window_data_for_sri() 
  sriINT *part_first_meas_cell = cnew sriINT[3*cp_info.n_sri_parts];
  sriINT *part_n_segments =  cnew sriINT[3*cp_info.n_sri_parts];
  sriINT *part_first_segment = cnew sriINT[3*cp_info.n_sri_parts];
  ccDOTIMES(axis,3) {
    m_part_first_meas_cell[axis] = part_first_meas_cell + cp_info.n_sri_parts*axis;
    m_part_n_segments[axis] = part_n_segments + cp_info.n_sri_parts*axis;
    m_part_first_segment[axis] = part_first_segment + cp_info.n_sri_parts*axis;
  }
  sCP_MEAS_WINDOW::allocate_meas_cell_refs();

  m_scale_factor_5g = 1.0;
}

extern "C" VOID close_all_meas_windows()
{
  static cBOOLEAN all_closed = FALSE;
  if (all_closed)
    return;

  all_closed = TRUE;
  DO_CP_MEAS_WINDOWS(window) {
    if (window->is_average_mme) {
      continue;
    }
    if (window->sri_file != NULL) {
      window->sri_file->close_file(FALSE);
      window->sri_file = NULL;
    }

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    if (window->m_is_particle_trajectory_window)
      ((CP_TRAJECTORY_WINDOW)window)->close_pmr_file();
//#endif
  }
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>::finish_init()
{
  // Convert volumes from double to single precision
  m_volumes = xnew sriFLOAT [this->n_meas_cells];
  ccDOTIMES(i, this->n_meas_cells)
    m_volumes[i] = m_d_volumes[i];
  vmem_free(m_d_volumes);
  m_d_volumes = NULL;
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::finish_init()
{
  // Convert surfel areas from double to single precision
  m_surfel_areas = xnew sriFLOAT [this->m_n_surfels];
  ccDOTIMES(i, this->m_n_surfels)
    m_surfel_areas[i] = m_d_surfel_areas[i];
  vmem_free(m_d_surfel_areas);
  m_d_surfel_areas = NULL;
}

VOID finish_init_of_meas_windows()
{
  atexit(close_all_meas_windows);
    
  // compute the lattice translations to be used for all windows
  cp_info_set_sri_lattice_translations();

  WALLCLOCK_TIME_SECS time_secs = wallclock_time_secs();

  DO_CP_MEAS_WINDOWS(window) {
    ccDOTIMES(axis, cp_info.n_dims) 
      window->max_bound[axis] = MIN(window->max_bound[axis], cp_info.simvol_size[axis]);
  }

  // Free temporary storage for all windows before we allocate the SP meas cells.
  DO_CP_MEAS_WINDOWS(window) {
    if (!window->is_average_mme)
      window->finish_init();
    window->m_time_of_last_sync = time_secs;
    window->init_clear_and_output_times();
    if (!window->is_average_mme) {
      if (window->n_meas_cells > 0) {
        if (window->rotational_dynamics_index >= 0) {
          window->insert_in_queue(ROTDYN_OUTPUT_QUEUE); // must follow init_clear_and_output_times()
        } else {
          if (!window->is_average_mme) {
            if(window->is_cond_window())
              window->insert_in_queue(COND_OUTPUT_QUEUE);
            else
              window->insert_in_queue(FLOW_OUTPUT_QUEUE);
          }
        }
      }
    }
  }
}

VOID windows_finalize()
{
  DO_CP_MEAS_WINDOWS(window) {
    if (window->is_average_mme)
      continue;
    if (cp_info.is_global_ref_frame_time_varying && window->global_nirf_info_msg.m_request != MPI_REQUEST_NULL)
      MPI_Cancel(&window->global_nirf_info_msg.m_request);

    ccDOTIMES(l, cp_info.n_time_varying_lrfs()) {
      if(window->lrf_info_msg[l].m_request != MPI_REQUEST_NULL)
        MPI_Cancel(&window->lrf_info_msg[l].m_request);
    }

    ccDOTIMES(i, cp_info.n_time_varying_mbcs()) {
      if(window->mbc_info_msg[i].m_request != MPI_REQUEST_NULL)
        MPI_Cancel(&window->mbc_info_msg[i].m_request);
    }

    ccDOTIMES(i, cp_info.n_movb_xforms()) {
      if(window->movb_info_msg[i].m_request != MPI_REQUEST_NULL)
        MPI_Cancel(&window->movb_info_msg[i].m_request);
    }


    if(window->use_tree_reduction()) {
      CP_REDUCTION_MEAS_WINDOW win = (CP_REDUCTION_MEAS_WINDOW)window;
      ccDOTIMES(i, win->m_n_child_sps) {
        if(win->m_child_receive_requests[i] != MPI_REQUEST_NULL)
          MPI_Cancel(&win->m_child_receive_requests[i]);
      }
    } else {
      CP_NO_REDUCTION_MEAS_WINDOW win = (CP_NO_REDUCTION_MEAS_WINDOW)window;
      if (win->n_stationary_meas_cells > 0) {
        ccDOTIMES(i, win->m_n_participating_sps) {
          if(win->receive_requests[i]  != MPI_REQUEST_NULL)
            MPI_Cancel(&win->receive_requests[i]);
        }
      }
    }
    if (window->n_moving_meas_cells > 0) {
      ccDOTIMES(sp, total_sps) {
        if (window->moving_meas_count_receive_requests[sp]  != MPI_REQUEST_NULL)
          MPI_Cancel(&window->moving_meas_count_receive_requests[sp]);
        if (window->moving_meas_data_receive_requests[sp]  != MPI_REQUEST_NULL)
          MPI_Cancel(&window->moving_meas_data_receive_requests[sp]);
      }
    }      
  }
}

VOID allocate_meas_window_lrf_mbc_and_movb_info()
{
  DO_CP_MEAS_WINDOWS(window) {
    window->lrf_info_msg = cp_info.n_time_varying_lrfs() > 0 ?
        cnew cExaMsg<sLRF_MEAS_FRAME_SP_TO_CP_MSG, 1>[cp_info.n_time_varying_lrfs()] : NULL;

    window->mbc_info_msg = (cp_info.n_time_varying_mbcs() > 0
                        ? cnew cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1>[cp_info.n_time_varying_mbcs()]
                        : NULL);
   window->movb_info_msg = cp_info.n_movb_xforms() > 0
        ? cnew cExaMsg<sMOVB_MEAS_FRAME_SP_TO_CP_MSG, 1>[cp_info.n_movb_xforms()] : NULL;
  }
}


/*--------------------------------------------------------------------------*
 * Window parameter functions
 *--------------------------------------------------------------------------*/

static CDI_MEAS_WINDOW find_cdi_meas_window_by_name(cSTRING name)
{
  ccDOTIMES(i, cp_info.n_cdi_meas_windows) {
    CDI_MEAS_WINDOW cdi_meas_win = &cp_info.cdi_meas_windows[i];
    if (strcmp(cdi_meas_win->name, name) == 0)
      return cdi_meas_win;
  }
  return NULL;
}

VOID insert_cmd_line_post_meas_cmds_in_queues()
{
  POST_MEAS_CMD cmd = cp_info.cmd_line_post_meas_cmds;
  
  while (cmd != NULL) {
    CDI_MEAS_WINDOW cdi_meas_win = find_cdi_meas_window_by_name(cmd->meas_window_name);

    if (cdi_meas_win == NULL) {
      msg_error("Invalid measurement window name \"%s\" included in -after_meas option.", cmd->meas_window_name);
      continue;
    }

    // Advance issue_frame as necessary after checkpoint restore
    if (cdi_meas_win->one_window >= 0) {
      CP_MEAS_WINDOW window = cp_info.meas_windows[cdi_meas_win->one_window];
      while (cmd->issue_frame < window->prev_nsets_written)
        cmd->issue_frame += cmd->period;
    }

    // Grab next cmd in list since cmd->next will be altered by insert_post_meas_cmd_in_queue
    POST_MEAS_CMD next_cmd = cmd->next;
    cdi_meas_win->insert_post_meas_cmd_in_queue(cmd);
    cmd = next_cmd;
  }

  cp_info.cmd_line_post_meas_cmds = NULL;
}

static TABLE_DESC find_table_desc_by_name(cSTRING name)
{
  ccDOTIMES(i, cp_info.n_tables) {
    TABLE_DESC table_desc = &cp_info.table_descs[i];
    if (strcmp(table_desc->name, name) == 0)
      return table_desc;
  }
  return NULL;
}
      
VOID insert_pre_table_read_cmds_in_queues()
{
  // First process the cmds provided on the simulator command line since they
  // override commands found in the CDI file.
  PRE_TABLE_CMD cmd = cp_info.cmd_line_pre_table_cmds;
  
  while (cmd != NULL) {
    TABLE_DESC table_desc = find_table_desc_by_name(cmd->table_name);
    if (table_desc == NULL) {
      msg_error("Invalid table name \"%s\" included in -before_table option.", cmd->table_name);
      continue;
    }

    CDI_MEAS_WINDOW cdi_meas_win = table_desc->cdi_meas_window;
    if (cdi_meas_win == NULL) {
      msg_error("Only tables linked to measurement windows may use the -before_table option."
		" Table \"%s\" is not linked to a measurement window.", cmd->table_name);
      continue;
    }

    // Discard the cmd string that came in via the CDI file (overridden by cmd line option)
    table_desc->cmd_string = cmd->cmd_string;
    cmd = cmd->next;
  }

  cp_info.cmd_line_pre_table_cmds = NULL;

  // Then process the commands that came in via the CDI file
  ccDOTIMES(i, cp_info.n_tables) {
    TABLE_DESC table_desc = &cp_info.table_descs[i];

    CDI_MEAS_WINDOW cdi_meas_win = table_desc->cdi_meas_window;

    // some measurement windows may not be generated by the disc and hence may
    // be empty (PR22412), so check if cdi_meas_window->one_window is valid
    if (cdi_meas_win != NULL && cdi_meas_win->one_window >= 0) {
      cSTRING cmd_string = table_desc->cmd_string;
      if (cmd_string != NULL) {
        POST_MEAS_CMD post_meas_cmd = xnew sPOST_MEAS_CMD;
        post_meas_cmd->meas_window_name = cdi_meas_win->name;
        post_meas_cmd->issue_frame = table_desc->first_frame;
        post_meas_cmd->period = table_desc->num_meas_frames_between_reads;
        post_meas_cmd->cmd_string = cmd_string;
        post_meas_cmd->table_desc = table_desc;

        // Advance issue_frame as necessary after checkpoint restore
        CP_MEAS_WINDOW window = cp_info.meas_windows[cdi_meas_win->one_window];
        while (post_meas_cmd->issue_frame < window->prev_nsets_written)
          post_meas_cmd->issue_frame += post_meas_cmd->period;    

        cdi_meas_win->insert_post_meas_cmd_in_queue(post_meas_cmd);
      }
    }
  }
}


#if 0 // Old version - may come in handy
VOID sCP_FLUID_MEAS_WINDOW::_precompute_cell_scale_factors()
{
  asINT32 n_dims = cp_info.n_dims;

  // 1/8 is necessary because we assume 8 voxels contributing to a meas cell (i.e., the
  // common case, "ublk measurements"). For single voxel measurements where only 1 voxel
  // contributes to a meas cell, the physics code scales the variables by 8.
  sriFLOAT base_mass_scale  = 1.0/8.0;
  sriINT   meas_cell_scale  = window->meas_cell_scale;
  ccDOTIMES (i, window->n_meas_cells) {
    asINT32  sri_voxel_scale = window->scales[i]; // scale of finest voxel in meas cell
    asINT32  voxel_scale     = sri_scale_to_sim_scale(sri_voxel_scale);
    sriFLOAT volume          = window->volumes[i];
    sriFLOAT mass_scale      = base_mass_scale * (1 << sri_voxel_scale);
    if (is_scale_finer(voxel_scale, meas_cell_scale)) {
      sriINT diff = voxel_scale - meas_cell_scale;
      mass_scale *= 1.0 / (volume * (1 << (n_dims * diff)));
    } else {
      mass_scale /= volume;
    }
    window->volumes[i] = mass_scale;
  }
}
#endif

template <typename MEAS_FLOAT_TYPE>
VOID tCP_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>::_precompute_stationary_cell_scale_factors()
{
  // Fluid meas windows do not have moving meas cells, so do all of them
  ccDOTIMES (i, this->n_meas_cells) {
    m_volumes[i] = 1.0 / m_volumes[i];
  }
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::_precompute_stationary_cell_scale_factors()
{
  // The areas in surfel_areas are in finest voxel units (not local units).
  // Thus we expect surfel measurements accumulated in the dynamics routines
  // to be scaled appropriately.
  asINT32 n_stationary_surfels = this->m_n_surfels - this->n_moving_meas_cells;
  ccDOTIMES (i, n_stationary_surfels) {
    this->m_surfel_areas[i] = 1.0 / m_surfel_areas[i];
  }
} 

template <typename MEAS_FLOAT_TYPE>
VOID tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::_precompute_moving_cell_scale_factors()
{
  size_t n_stationary_surfels = this->m_n_surfels - this->n_moving_meas_cells;
  for(size_t i = n_stationary_surfels; i < this->m_n_surfels; i++) {
    if ( this->m_surfel_areas[i] != 0 ) {
      this->m_surfel_areas[i] = 1.0 / m_surfel_areas[i];
    }
  }
} 

template <typename MEAS_FLOAT_TYPE>
VOID tCP_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::convert_for_surface_coupling(sFLOAT** meas_variables)
{
  // use a dFLOAT for accumulating values from multiple SPs
  static dFLOAT *var_vals = NULL;
  asINT32 n_vars = this->cdi_meas_window->surface_var_set.n_vars;
  asINT32 n_dims = cp_info.n_dims;

  sriFLOAT *meas_cell_scale_factors = cell_scale_factors();

  if (NULL == var_vals) {
    var_vals = new dFLOAT[n_vars];
  }

  sCP_MEAS_WINDOW::uSP_MEAS_CELL_REF_OR_LIST *sp_mcell_refs = this->sp_meas_cell_refs();
  sriFLOAT time_scale = 1.0 / (this->m_output_timestep - this->m_clear_timestep) *
                        this->cdi_meas_window->lcm_flow_tsteps;
  ccDOTIMES(meas_cell_index, this->n_stationary_meas_cells) {
    // the code for the scale factor is borrowed from _precompute_scale_factors
    // see comment there for a more detailed explanation of the computation
    sCP_MEAS_WINDOW::uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &sp_mcell_refs[meas_cell_index];
    if (ref_or_list->is_list()) {
      // This clause should not really be executed since surface coupling
      // measurement surfels are currently the same as surfels, which implies
      // that they are not shared by SPs. But leave it here in case that
      // changes in the future.

      sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF_LIST_ELT *list               = ref_or_list->list();
      sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF          first_ref           = list->m_ref;
      MEAS_FLOAT_TYPE *first_sp_meas_cell = first_ref.sp_meas_cell(this->all_sp_meas_cells());
      ccDOTIMES(k, n_vars)
        var_vals[k] = first_sp_meas_cell[k];

      list = list->m_next; // jump to second ref
      while (list != NULL) {
        sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref           = list->m_ref;
        MEAS_FLOAT_TYPE *sp_meas_cell = ref.sp_meas_cell(this->all_sp_meas_cells());
        ccDOTIMES(k, n_vars)
          var_vals[k] += sp_meas_cell[k];
        list = list->m_next;
      }
      ccDOTIMES(k, n_vars) {
        meas_variables[k][meas_cell_index] = (sriFLOAT)var_vals[k] * (meas_cell_scale_factors[meas_cell_index] * time_scale);
      }
    }
    else {
      sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref           = ref_or_list->ref();
      MEAS_FLOAT_TYPE *sp_meas_cell = ref.sp_meas_cell(this->all_sp_meas_cells());

      ccDOTIMES(k, n_vars)
        meas_variables[k][meas_cell_index] = sp_meas_cell[k] * (meas_cell_scale_factors[meas_cell_index] * time_scale);
    }
  } // for n_stationary_meas_cells

#if 0 
  // Coupling measurement window cannot include any moving meas surfels
  ccDOTIMES(meas_cell_index, this->n_moving_meas_cells) {
    // the code for the scale factor is borrowed from _precompute_scale_factors
    // see comment there for a more detailed explanation of the computation
    {
      MEAS_FLOAT_TYPE *sp_meas_cell = this->all_moving_meas_cells();

      ccDOTIMES(k, n_vars)
        meas_variables[k][meas_cell_index] = sp_meas_cell[k] * (meas_cell_scale_factors[meas_cell_index + n_stationary_meas_cells] * time_scale);
    }
  } // for n_moving_meas_cells
#endif


}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_COMPOSITE_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::_precompute_stationary_cell_scale_factors()
{
  ccDOTIMES (i, this->n_stationary_meas_cells) {
    m_face_areas[i] = 1.0 / m_face_areas[i];
  }
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_COMPOSITE_SURFACE_MEAS_WINDOW<MEAS_FLOAT_TYPE>::_precompute_moving_cell_scale_factors()
{
  for(size_t i = this->n_stationary_meas_cells; i < this->n_meas_cells; i++) {
    if ( this->m_face_areas[i] != 0 ) {
      this->m_face_areas[i] = 1.0 / this->m_face_areas[i];
    }
  }
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_COMPOSITE_FLUID_MEAS_WINDOW<MEAS_FLOAT_TYPE>::_precompute_stationary_cell_scale_factors()
{
  ccDOTIMES (i, this->n_meas_cells) {
    m_part_volumes[i] = 1.0 / m_part_volumes[i];
  }

  m_scale_factor_5g = 1.0;
  if (cp_info.is_5g_sim) {
    sriFLOAT z_dim_factor = 1.0;
    if (cp_info.n_dims == 3) {
      sriFLOAT z_dim = this->compute_z_dimension();
      z_dim_factor =  1.0 / z_dim;
    }
    m_scale_factor_5g = z_dim_factor;
  }
}

template <typename MEAS_FLOAT_TYPE>
VOID tCP_FLUID_DEV_MEAS_WINDOW<MEAS_FLOAT_TYPE>::_precompute_stationary_cell_scale_factors()
{
  if (cp_info.is_5g_sim) {
    ccDOTIMES (i, this->n_meas_cells) {
      m_volumes[i] = 1.0 / m_volumes[i];
    }
  } else {
    ccDOTIMES (i, this->n_meas_cells) {
      m_volumes[i] = 1.0;
    }
  }
  m_scale_factor_5g = 1.0;
  if (cp_info.is_5g_sim) {
    sriFLOAT z_dim_factor = 1.0;
    if (cp_info.n_dims == 3) {
      sriFLOAT z_dim = this->compute_z_slice_dimension();
      if (z_dim > std::numeric_limits<MEAS_FLOAT_TYPE>::epsilon())
        z_dim_factor =  1.0 / z_dim;
    }
    m_scale_factor_5g = z_dim_factor;
  }
}

#if SURF_COUP
dFLOAT run_surface_coupling_job(CP_MEAS_WINDOW window, asINT32 model_index, TIMESTEP timestep, dFLOAT unrounded_timestep, BOOLEAN dry_run)
{
  const int SECONDS_BETWEEN_LICENSE_CHECKS = 10;
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs + model_index;
  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + model_index;
  cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;
  CDI_CMDL cmdl = surface_coupling->get_cmdl();

  
  dFLOAT powertherm_phase_start_time = 0; // PT start time of the coupling phase for the current coupling, relative to the first coupling
  dFLOAT unrounded_relative_timestep_in_phase = unrounded_timestep; // unrounded relative timestep within the current coupling phase

  asINT32 phase = 0;
  asINT32 n_phases = cmdl->m_coupling_phase_descs.size();
  ccDO_FROM_TO(i, 0, (n_phases - 2)) {
    asINT32 n_records = coupling_model_desc->m_coupling_phase_ratios[i].size();
#if DEBUG_ADAPTIVE_COUPLING
    if (n_records > 0) {
      msg_print("-------------------------------------");
      msg_print("Phase %d has %d ratio records", phase, n_records);
      ccDOTIMES(j, n_records)
        msg_print("Record: timestep %d ratio scale factor %f", coupling_model_desc->m_coupling_phase_ratios[phase][j].timestep,
                coupling_model_desc->m_coupling_phase_ratios[phase][j].ratio_scale_factor);
    }
#endif

    if (timestep < cmdl->m_coupling_phase_descs[i+1].start) {
      break;
    }
    phase++;
    sFLOAT ratio_scale_factor = 1.0;
    TIMESTEP prev_exact_start_time_for_ratio_record = cmdl->m_coupling_phase_descs[i].exact_start;
    ccDOTIMES(record_index, n_records) {
      sPT_PF_RATIO_RECORD ratio_record = coupling_model_desc->m_coupling_phase_ratios[i][record_index];
      TIMESTEP unrounded_duration = (ratio_record.timestep - prev_exact_start_time_for_ratio_record);
      powertherm_phase_start_time += unrounded_duration * 
                                    cmdl->m_coupling_phase_descs[i].therm_time_ratio * ratio_scale_factor;
      prev_exact_start_time_for_ratio_record = ratio_record.timestep;
      ratio_scale_factor = ratio_record.ratio_scale_factor;
    }
    powertherm_phase_start_time += (cmdl->m_coupling_phase_descs[i+1].exact_start - prev_exact_start_time_for_ratio_record) * 
                                    cmdl->m_coupling_phase_descs[i].therm_time_ratio * ratio_scale_factor;
  }
  // Calculate the unrounded relative time in phase using the ratio history
  asINT32 n_records = coupling_model_desc->m_coupling_phase_ratios[phase].size();
  sFLOAT ratio_scale_factor = 1.0;
  sFLOAT new_ratio_scale_factor = 1.0;
  unrounded_relative_timestep_in_phase = 0.0;
#if DEBUG_ADAPTIVE_COUPLING
  if (n_records > 0) {
    msg_print("Phase %d has %d ratio records", phase, n_records);
    ccDOTIMES(j, n_records)
      msg_print("Record: timestep %d ratio scale factor %f", coupling_model_desc->m_coupling_phase_ratios[phase][j].timestep,
                coupling_model_desc->m_coupling_phase_ratios[phase][j].ratio_scale_factor);
    msg_print("-------------------------------------");
  }
#endif
  TIMESTEP prev_start_time_for_ratio_record = cmdl->m_coupling_phase_descs[phase].start;
  ccDOTIMES(record_index, n_records) {
    sPT_PF_RATIO_RECORD ratio_record = coupling_model_desc->m_coupling_phase_ratios[phase][record_index];
    if (timestep <= ratio_record.timestep) {
      if (timestep == ratio_record.timestep)
        new_ratio_scale_factor = ratio_record.ratio_scale_factor;
      break;
    }
    new_ratio_scale_factor = ratio_record.ratio_scale_factor;
    TIMESTEP rounded_duration = (ratio_record.timestep - prev_start_time_for_ratio_record);
    TIMESTEP unrounded_duration = rounded_duration * cmdl->m_coupling_phase_descs[phase].exact_period/cmdl->m_coupling_phase_descs[phase].period;
    unrounded_relative_timestep_in_phase +=  unrounded_duration * ratio_scale_factor;
    prev_start_time_for_ratio_record = ratio_record.timestep;
    ratio_scale_factor = new_ratio_scale_factor;
  }
  unrounded_relative_timestep_in_phase += (unrounded_timestep - prev_start_time_for_ratio_record) * 
                                    ratio_scale_factor;

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
  msg_print("unrounded relative timestep in phase = %f unrounded_timestep %f phase_start %f",
            unrounded_relative_timestep_in_phase, unrounded_timestep, cmdl->m_coupling_phase_descs[phase].exact_start);
#endif

  sFLOAT ratio = cmdl->m_coupling_phase_descs[phase].therm_time_ratio;
  if (ratio <= 0)
    ratio = 1.0;  // For steady state and infer from model, set the ratio to 1.0
  if (cmdl->m_use_default_powertherm_start_time) {
    //msg_print("Reset cmdl->m_powertherm_start_time to %f",  cmdl->m_coupling_phase_descs[phase].exact_start);
    cmdl->m_powertherm_start_time = cmdl->m_coupling_phase_descs[0].exact_start;
  }

  UNITS_UNIT lattice_unit;
  UNITS_UNIT mks_unit; 
  units_get_default_for_class(cp_info.units_db, "lattice", "Time", &lattice_unit);
  units_get_default_for_class(cp_info.units_db, "mks", "Time", &mks_unit);

  dFLOAT powertherm_start_time_s, powertherm_step_s, phase_period_s, coupling_time_s, unrounded_coupling_time_s, first_coupling_time_s;  // In seconds
  dFLOAT powertherm_phase_start_time_s, unrounded_relative_time_in_phase_s; // In seconds
  units_convert(cp_info.units_db, cmdl->m_powertherm_start_time, lattice_unit, mks_unit, &powertherm_start_time_s);             //unrounded
  units_convert(cp_info.units_db, cmdl->m_coupling_phase_descs[phase].stepsize, lattice_unit, mks_unit, &powertherm_step_s);    //unrounded
  units_convert(cp_info.units_db, cmdl->m_coupling_phase_descs[phase].exact_period, lattice_unit, mks_unit, &phase_period_s);   //unrounded
  units_convert(cp_info.units_db, timestep, lattice_unit, mks_unit, &coupling_time_s);                                          //rounded      
  units_convert(cp_info.units_db, unrounded_timestep, lattice_unit, mks_unit, &unrounded_coupling_time_s);                      //unrounded    
  units_convert(cp_info.units_db, cmdl->m_coupling_phase_descs[0].exact_start, lattice_unit, mks_unit, &first_coupling_time_s); //unrounded
  units_convert(cp_info.units_db, powertherm_phase_start_time, lattice_unit, mks_unit, &powertherm_phase_start_time_s);         //rounded
  units_convert(cp_info.units_db, unrounded_relative_timestep_in_phase, lattice_unit, mks_unit, &unrounded_relative_time_in_phase_s); //unrounded
  dFLOAT powertherm_time_s = powertherm_phase_start_time_s + ratio * unrounded_relative_time_in_phase_s;

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
  msg_print("PowerTHERM will run with pt_start_time = %f s, ratio = %f, pt_step = %f s, period = %f s, "
            "(PF)coupling time = %f s, (PT) unrounded_coupling_time_s = %f s first coupling time = %f s powertherm_time_s = %f", 
            powertherm_start_time_s, ratio * new_ratio_scale_factor, powertherm_step_s * new_ratio_scale_factor, phase_period_s * ratio * new_ratio_scale_factor, 
            coupling_time_s, unrounded_coupling_time_s, first_coupling_time_s, powertherm_time_s);
#endif

  dFLOAT powertherm_real_start_time = unrounded_coupling_time_s;
  dFLOAT powertherm_real_end_time = unrounded_coupling_time_s + phase_period_s;
  dFLOAT powertherm_real_step = powertherm_step_s;

  // For cdi version 7.26 and above, the powertherm start time is the abolute start time
  // when weather file is being used. For cdi verion > 7.26, we need to check if weather
  // file is being used, and if so we should append the weather file start time to the
  // PowerTHERM start time in cdi.
  bool is_cdi_ver_at_least_7_26 = cdi_data.major_version > 7 || (cdi_data.major_version == 7 && cdi_data.minor_version >= 26);
  if (is_cdi_ver_at_least_7_26) {
    // Check if the PowerTHERM start time is within range of weather time data
    bool is_weather_file_used;
    std::string weather_file_name;
    dFLOAT weather_start_time;
    eTPI_STATUS status = coupling_interface->GetWeatherFileInfo(is_weather_file_used, weather_file_name, weather_start_time);
    if (status != TPI_SUCCESS) {
      msg_error("Could not read coupling model file \"%s\".", cmdl->model_filename);
    }
    
    if (is_weather_file_used) {
      dFLOAT first_powertherm_start_time = powertherm_start_time_s + powertherm_time_s; // This is true for transient calculation type
      if (cmdl->m_calculation_type != eCDI_POWERTHERM_CALCULATION_TYPE::Transient)  // For Steady State or Infer From Model, get the real PowerTHERM start time from tdf file
        coupling_interface->GetTransientSimStartTime(first_powertherm_start_time);
#if DEBUG_WEATHER_FILE
        if (cmdl->m_calculation_type == eCDI_POWERTHERM_CALCULATION_TYPE::Transient)
          msg_print("PowerTHERM first start time = powertherm_start_time_s %f + powertherm_time_s %f", 
                  powertherm_start_time_s + powertherm_time_s, 
                  powertherm_start_time_s, powertherm_time_s);

        msg_print("Weather file %s, weather start time %f seconds, Powertherm first start time %f seconds.", 
                  weather_file_name.c_str(), weather_start_time, first_powertherm_start_time);
#endif

      if (first_powertherm_start_time < (weather_start_time - 0.001)) { // 0.001 used to deal with precision lost
        msg_error("PowerTHERM start time is earlier than the start time in weather file \"%s\". Please check the model setup.", 
                  weather_file_name.c_str());
      }
    }
  }

  // Not really running the job, just return powertherm end time (used for adaptive coupling)
  if (dry_run) {
    cp_info.m_adaptive_params.first_pt_start_time = powertherm_start_time_s;
    // For tdf using weather file, should add weather start time in addition
    if (!is_cdi_ver_at_least_7_26)  // Old cdi format where the powertherm start time is the relative time to the weather file start time
      coupling_interface->AddWeatherStartTime(cp_info.m_adaptive_params.first_pt_start_time);

    if (cmdl->m_calculation_type == eCDI_POWERTHERM_CALCULATION_TYPE::InferFromModel) {
      coupling_interface->GetTransientSimStartTime(cp_info.m_adaptive_params.first_pt_start_time);
    } 

    powertherm_real_end_time = powertherm_time_s + ratio * phase_period_s;
    return powertherm_real_end_time;
  }

  if (coupling_model_desc && coupling_model_desc->n_runs_launched == 0)
    backup_init_log_file(coupling_model_desc);

  // if this is a one-way coupling (PF->app), then wait for a previous
  // instance of the application to complete and backup the log files, model
  // files etc
  if (!coupling_model_desc->couple_during_sim_p) {
    const WALLCLOCK_TIME_SECS SECS_BETWEEN_COUPLING_MODEL_DATA_CHECKS = 1;
    while (TPI_JOB_IN_PROGRESS == coupling_interface->GetJobStatus()) {
      if (coupling_interface->NumMessages() > 0) {
	CDI_CMDL cmdl = surface_coupling->get_cmdl();
	dump_tpi_messages(coupling_interface, cmdl->model_filename);
      }
      platform_sleep_seconds(SECS_BETWEEN_COUPLING_MODEL_DATA_CHECKS);
    }
    if (TPI_JOB_COMPLETED_SUCCESS == coupling_interface->GetJobStatus()) {
      coupling_interface->ResetJobStatus();
      backup_log_file(coupling_model_desc);
      backup_current_coupling_link(coupling_model_desc);
    }
    else if (TPI_JOB_COMPLETED_FAILURE == coupling_interface->GetJobStatus()) {
      dump_tpi_messages(coupling_interface, cmdl->model_filename);
      msg_error("%s job (model \"%s\") failed. Check the log file for details.", 
                cmdl->model_type,cmdl->model_filename);
    }
    coupling_model_desc->n_coupling_models_read++;
  }

  if (coupling_model_desc->n_runs_launched > 0
      && (!cp_info.ptherm_model_changed_p
	  || coupling_model_desc->n_runs_launched > coupling_model_desc->n_runs_before_ckpt_restore)) {
    coupling_interface->SetRunMode(TPI_RESTART_RUN);
  }

  // create a numbered link to the current model file
  make_current_coupling_links(coupling_model_desc);
  coupling_model_desc->last_run_read_time = coupling_model_desc->next_periodic_read;
  coupling_model_desc->n_runs_launched++;

  char jobctl_status[4096];
  sprintf(jobctl_status, "Timestep %d (Launching %s job (model \"%s\"))",
          timestep,cmdl->model_type, coupling_model_desc->model_filename);
  cp_jobctl_output_status(jobctl_status);
  msg_print("%s", jobctl_status);

  eTPI_STATUS status = coupling_interface->SaveModel();
  if (coupling_interface->NumMessages() > 0)
    dump_tpi_messages(coupling_interface, cmdl->model_filename);
  if (status == TPI_FAILURE)
    // dump_tpi_messages above should call msg_error so this should never execute
    msg_error("Failed to save \"%s\" with updated boundary conditions.", coupling_model_desc->model_filename);

  if (TPI_LICENSE_UNAVAILABLE == coupling_interface->GetLicenseStatus()) {
    msg_warn("No licenses available for %s",cmdl->model_type);
  }

  BOOLEAN waiting_for_license_msg_issued = FALSE;
  while (TPI_LICENSE_IN_USE == coupling_interface->GetLicenseStatus()) {
    if (!waiting_for_license_msg_issued) {
      waiting_for_license_msg_issued = TRUE;
      msg_warn("Waiting for license for %s", cmdl->model_type);
    }
    platform_sleep_seconds(SECONDS_BETWEEN_LICENSE_CHECKS);
  }
  if (waiting_for_license_msg_issued) {
    msg_print("%s license found. Launching job.", cmdl->model_type);
  }
 
 CHARACTER tdf_file_name[PLATFORM_MAXPATHLEN];
 CHARACTER model_number[PLATFORM_MAXPATHLEN];
 memset(tdf_file_name,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
 memset(model_number,'\0',PLATFORM_MAXPATHLEN*sizeof(CHARACTER));
 
 sprintf(model_number, "%03d", coupling_model_desc->n_coupling_models_read);

 // find occurrence of _cur, and replace with number 001 etc 
 strcpy(tdf_file_name, coupling_model_desc->model_filename);
 replace_coupling_file_current_suffix(tdf_file_name, model_number, tdf_file_name);

  // XDU: write the coupling table file
  platform_get_file_base_name(tdf_file_name, tdf_file_name); // remove the directory name

  eTPI_JOB_STATUS job_status = coupling_interface->RunJob(powertherm_start_time_s,
                                                          powertherm_step_s * new_ratio_scale_factor,
                                                          phase_period_s * ratio * new_ratio_scale_factor,
                                                          (eTPI_CALCULATION_TYPE)cmdl->m_calculation_type,
                                                          powertherm_time_s,
                                                          powertherm_real_start_time,
                                                          powertherm_real_end_time,
                                                          powertherm_real_step,
                                                          is_cdi_ver_at_least_7_26
                                                         );

  // For steady state, there is no time information in tdf file. Use the PowerFLOW time as the PT time in .tdx file instead
  if ( TPI_JOB_SUBMIT_SUCCESS == job_status) {
    if (cmdl->m_calculation_type == eCDI_POWERTHERM_CALCULATION_TYPE::SteadyState) {
      powertherm_real_start_time  = coupling_time_s;
      powertherm_real_end_time    = coupling_time_s + phase_period_s;
      powertherm_real_step        = phase_period_s;
    } else if (cmdl->m_calculation_type == eCDI_POWERTHERM_CALCULATION_TYPE::InferFromModel) {
      // If the original model is steady state, then we use the PowerFLOW time as the PT time in .tdx file
      if (powertherm_real_start_time == powertherm_real_end_time) { // duration 0 tells this is steady state
        powertherm_real_start_time  = coupling_time_s;
        // For CDI version <= 4.2, exact_start, exact_period and exact_interval are not set properly in cmdl.
        // Need to set them to the inexact versions here explicitly. Should be removed once the CDI is fixed.
        dFLOAT alt_phase_period_s = 0.0;   
        units_convert(cp_info.units_db, cmdl->m_coupling_phase_descs[phase].period, lattice_unit, mks_unit, &alt_phase_period_s); 
        powertherm_real_end_time    = coupling_time_s + alt_phase_period_s;
        powertherm_real_step        = alt_phase_period_s;
      } else {
        // Calculate the real ratio for infer from model and transient tdf file
        ratio = (powertherm_real_end_time - powertherm_real_start_time) / phase_period_s;
        cmdl->m_coupling_phase_descs[phase].therm_time_ratio = ratio;
      }
    }
#if DEBUG_POWERTHERM_COUPLING_TABLE
    msg_print("%s %.10f %.10f %.10f %.10f %d %d %d",
              tdf_file_name,
              powertherm_real_start_time,
              powertherm_real_end_time,
              powertherm_real_step,
              ratio,
              phase,
              timestep,
              timestep + cmdl->m_coupling_phase_descs[phase].period);
#endif
    fprintf(coupling_model_desc->coupling_time_fp, 
            //"%s %17.16f %17.16f %17.16f %17.16f %d %d %d\n",
            "%s %.10f %.10f %.10f %.10f %d %d %d\n",
            tdf_file_name,
            powertherm_real_start_time,
            powertherm_real_end_time,
            powertherm_real_step,
            ratio * new_ratio_scale_factor,
            phase,
            timestep,
            timestep + cmdl->m_coupling_phase_descs[phase].period);
    fflush(coupling_model_desc->coupling_time_fp);
  } else {
    if (coupling_interface->NumMessages() > 0)
      dump_tpi_messages(coupling_interface, cmdl->model_filename);
    if (TPI_JOB_CONNECTION_DROPPED == job_status) {
      // first try to kill the previous job
      if (coupling_interface->TerminateJob() != TPI_JOB_TERMINATE_SUCCESS) {
        msg_warn("Unable to terminate external application for model \"%s\"",
                 coupling_model_desc->model_filename);
      }
      // retry launching the client, and running the job again
      // also check for license availability
      eTPI_STATUS client_status = coupling_interface->LaunchClient();
      const int SECONDS_BETWEEN_LICENSE_CHECKS = 10;
      BOOLEAN license_warning_issued = FALSE;

      while (coupling_interface->GetJobStatus(true) == TPI_JOB_COMPLETED_NOLICENSE) {
        if (!license_warning_issued) { 
          msg_warn("Cannot obtain a license for the %s job for model \"%s\". Trying again...",
                   cmdl->model_type, cmdl->model_filename);
          license_warning_issued = TRUE;
        }
        platform_sleep_seconds(SECONDS_BETWEEN_LICENSE_CHECKS);
        client_status = coupling_interface->LaunchClient();
      }
      if (client_status != TPI_SUCCESS) {
        dump_tpi_messages(coupling_interface, cmdl->model_filename);
        msg_error("Failed to launch %s job (model \"%s\"). Check the log file for details.", 
                  cmdl->model_type,cmdl->model_filename);
      }
      if (license_warning_issued) {
        msg_print("Obtained a license for the %s job for model \"%s\".",
                  cmdl->model_type, cmdl->model_filename);
      }

    }
    else {
      msg_error("Failed to launch %s job (model \"%s\"). Check the log file for details.", 
                cmdl->model_type, coupling_model_desc->model_filename);
    }
  }
  
  // If this is initial coupling at ts=0, do not try to stop the simulation
  if (timestep > 0) {
    if (cp_info.adaptive_coupling_p) {
      if (cp_info.m_adaptive_params.fix_pt_time_p) {
        if ((powertherm_real_end_time - cp_info.m_adaptive_params.first_pt_start_time) >= cp_info.m_adaptive_params.total_pt_duration) {
          msg_print("Request to exit since PowerTHERM total duration %f >= %f", (powertherm_real_end_time - cp_info.m_adaptive_params.first_pt_start_time), cp_info.m_adaptive_params.total_pt_duration);
          cp_info.request_to_exit_by_pt_time = TRUE;
          cp_info.exit_time_requested_by_pt_time = cp_info.time;
        }
      }
    }
  }
  // Get the next powertherm start time and send to SPs for evaluation of thermal time
  cp_info.next_powertherm_start_time = powertherm_real_end_time;

  return 0.0;
}

VOID launch_surface_coupling_app(CP_SURFACE_MEAS_WINDOW window, TIMESTEP timestep, BOOLEAN run_only_p, dFLOAT unrounded_timestep)
{
  static sFLOAT **meas_variables = NULL;
  static asINT32 n_meas_ptrs_allocated = 0;

  asINT32 model_index = window->coupling_model_index;
  if (model_index >= cp_info.n_surface_couplings) {
    msg_internal_error("Surface coupling model index %d is greater than the number of models",model_index);
    return;
  }

  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs+model_index;
  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+model_index;
  CDI_CMDL cmdl = surface_coupling->get_cmdl();
  sINT32 n_vars = surface_coupling->n_export_vars();
  CDI_SCBC scbcs = surface_coupling->get_scbcs();
  cSTRING model_filename = cmdl->model_filename;
  asINT32 nwt_index, htc_index;

  TIMESTEP n_user_base_steps = cp_info.n_user_base_steps;
  TIMESTEP restart_time = cp_info.restart_time;  /* in user's timestep */
  TIMESTEP window_output_timestep = window->m_output_timestep / n_user_base_steps;  //in user's timestep
  TIMESTEP window_clear_timestep = window->m_clear_timestep / n_user_base_steps;    //in user's timestep

  // is this a restart run, and should the first coupling period be skipped?
  if (skip_first_coupling(coupling_model_desc,model_index,
			  window_output_timestep,restart_time)) {
    return;
  }

  cTHIRD_PARTY_INTERFACE *coupling_interface = surface_coupling->tpi;

  // only run without collecting measurements, can be used to handle the
  // special case of coupling start time == 0
  if (run_only_p) {
    //msg_print("run_only_P surface coupling output timestep %d", unrounded_timestep);
    run_surface_coupling_job(window, model_index, timestep, unrounded_timestep);
    return;
  }

  if (NULL == meas_variables || n_meas_ptrs_allocated != n_vars) {
    // if there are multiple coupling models, the number of coupling variables
    // could, in general, be different
    if (meas_variables) {
      delete [] meas_variables;
    }
    meas_variables = new sFLOAT*[n_vars];
    n_meas_ptrs_allocated = n_vars;
  }

  ccDOTIMES(var, n_vars) {
    meas_variables[var] = cp_info.coupling_export_var_array + var*window->n_meas_cells;
  }

  ccDOTIMES(var, n_vars) {
    eTPI_VARIABLE_TYPE var_type = 
      eTPI_VARIABLE_TYPE((surface_coupling->export_vars())[var]);
    switch (var_type) {
      case TPI_VAR_TYPE_HEAT_TRANSFER_COEFFICIENT:
        htc_index = var;
        break;
      case TPI_VAR_TYPE_NEAR_WALL_TEMPERATURE:
        nwt_index = var;
        break;
      default:
        msg_internal_error("Unsupported coupling export variable type %d", var_type);
        break;
    }
  }

  window->precompute_cell_scale_factors();
  window->convert_for_surface_coupling(meas_variables);

  // convert to user specified units from lattice units
  ccDOTIMES(var,n_vars) {
    sFLOAT u_slope = surface_coupling->export_var_unit_conversion_slopes[var];
    sFLOAT u_offset = surface_coupling->export_var_unit_conversion_offsets[var];

    sFLOAT *source_data = cp_info.coupling_export_var_array + var*window->n_meas_cells;
    ccDOTIMES(i,window->n_meas_cells) {
      source_data[i] = u_offset + u_slope * source_data[i];
    }
  }

  dFLOAT total_source_area = 0.0;
  // The surfel areas have already been scaled, so unscale them for
  // calculating the statistics below
  ccDOTIMES(i,window->n_meas_cells) {
    sFLOAT unscaled_area = ((sINT64) 1 << (cp_info.n_dims * window->m_scales[i])) / window->m_surfel_areas[i]; 
    total_source_area += unscaled_area;
  }
  // map the data for each foreign model target face
  eTPI_STATUS status = coupling_interface->InitializeConvectionRecord();
  if (coupling_interface->NumMessages() > 0)
    dump_tpi_messages(coupling_interface, cmdl->model_filename);
  if (status == TPI_FAILURE)
    // dump_tpi_messages above should call msg_error so this should never execute
    msg_error("Failed to initialize convection BC record for \"%s\".",
              cmdl->model_filename);

  ccDOTIMES(coupling_model_bc,cmdl->num_coupling_model_bcs) {
    // skip invalid targets for which bc eligibility failed
    // The 10.1.015 Intel compiler screws up this if statement when compiled -O3
    if (-1 == surface_coupling->foreign_target_indices[coupling_model_bc]) {
      continue;
    }

    ccDOTIMES(var, n_vars) {
      SIM_SURFACE_DATA foreign_data = &(surface_coupling->foreign_target_data[coupling_model_bc])[var];
      sFLOAT *source_data = cp_info.coupling_export_var_array + var*window->n_meas_cells;
      dFLOAT source_av_value = 0.0;
      asINT32 nvalues = foreign_data->GetSurface()->NumFacetFaces();
      if (0 == nvalues) { // skip this target if it has zero elements
        continue; 
      }
      ccDOTIMES(i,window->n_meas_cells) {
        sFLOAT unscaled_area = ((sINT64) 1 << (cp_info.n_dims * window->m_scales[i])) / window->m_surfel_areas[i]; 
        source_av_value += source_data[i] * unscaled_area;
      }
      source_av_value /= total_source_area;

      sFLOAT *values_array = foreign_data->GetScalarArray();
      surface_coupling->native_to_foreign_map->MapSourceToTarget(source_data,
                                                                 values_array, surface_coupling->foreign_target_indices[coupling_model_bc],
                                                                 sFLOAT(source_av_value),TRUE,TRUE);
      sFLOAT min_var_value = SFLOAT_MAX, max_var_value = -SFLOAT_MAX;
      dFLOAT av_var_value = 0.0;
      ccDOTIMES (i, nvalues) {
        // compute export statistics for each target face
        min_var_value = MIN(min_var_value,values_array[i]);
        max_var_value = MAX(max_var_value,values_array[i]);
        av_var_value += values_array[i];
      }
      av_var_value /= nvalues;
      std::string varname = coupling_interface->GetVarName(eTPI_VARIABLE_TYPE(surface_coupling->export_vars()[var]));
      std::replace(varname.begin(), varname.end(), ' ', '_');
      fprintf(coupling_model_desc->summary_fp," %3d %6d %11g %11g %11g  %s(%s)\t%s\t%s\n",
              coupling_model_desc->n_coupling_models_read + 1, window_output_timestep,
              min_var_value, max_var_value, av_var_value,
              varname.c_str(),
              surface_coupling->export_var_units()[var],
              surface_coupling->cmdl.model_type,
              scbcs[coupling_model_bc].target_name);
      fflush(coupling_model_desc->summary_fp);

      // Check PowerTHERM monitors. The current timestep is the windows 
      // output timestep (PT is launched).
      double data;

      char *target_name = scbcs[coupling_model_bc].target_name;
      // Here target_name is the full name containing the assembly name, 
      // e.g. it is in the form of ":assembly_name:part_name".

      if (!strcmp(varname.c_str(), "HTC")) { 
         ccDOTIMES(i, cp_info.pt_monitors.size()) {
          POWERTHERM_MONITOR pt_monitor = cp_info.pt_monitors[i];
#if DEBUG_MONITOR
          //msg_print("Part name = %s.  pt_monitor->m_powertherm_part = %s", 
          //           target_name, pt_monitor->m_powertherm_part.c_str());
#endif
          if ((pt_monitor->m_powertherm_model_index == model_index) && 
              !strcmp(target_name, pt_monitor->m_powertherm_part.c_str())) {
            // Only deal with HTC here. Surface temperature is written when 
            // reading the PowerTHERM data, i.e. at timestep 
            // read_in_timestep = (current_output_timestep + delay), and it 
            // is handled in coupling_model.cc.

            // Check if the current data contributes to Surface HTC monitors.
            if (is_powertherm_var_type_surface_htc(pt_monitor->m_powertherm_var_type)) {
              switch (pt_monitor->m_powertherm_var_type) {
              case eCDI_MNTR_PT_VAR::MeanHTC:
                data = av_var_value;
#if DEBUG_MONITOR
                msg_print("Part %s: Mean Surface HTC = %f", target_name, data);
#endif
                break;
              case eCDI_MNTR_PT_VAR::MaxHTC:
                data = max_var_value;
#if DEBUG_MONITOR
                msg_print("Part %s: Max Surface HTC = %f", target_name, data);
#endif
              case eCDI_MNTR_PT_VAR::MinHTC:
                data = min_var_value;
#if DEBUG_MONITOR
                msg_print("Part %s: Min Surface HTC = %f", target_name, data);
#endif
                break;
              default:
                msg_internal_error("PowerTHERM monitor var type %d is not supported", 
                                   pt_monitor->m_powertherm_var_type);
                break;
              }

              TIMESTEP signal_timestep;       // timestep when the signal will be processed (current timestep + delay)
              TIMESTEP next_signal_timestep;  // next signal appending timestep (next output timestep + delay)
              // Find the location of current timestep in the coupling phase table
              asINT32 phase = 0;
              asINT32 n_phases = coupling_model_desc->m_coupling_phase_descs.size();
              ccDO_FROM_TO(phase_index, 1, n_phases-1)
              {              
                if (window_output_timestep >= cmdl->m_coupling_phase_descs[phase_index].start)
                  phase++;
                else
                  break;
              }
              signal_timestep = window_output_timestep;
              next_signal_timestep = signal_timestep + cmdl->m_coupling_phase_descs[phase].period;

              pt_monitor->append_and_analyze_signal(data, signal_timestep, next_signal_timestep); 
            }
          }
        }
      }
    }
    // Write the convection boundary condition to the PowerTHERM/RadTherm database
    status = coupling_interface->WriteConvectionBC(
	                   surface_coupling->foreign_target_data[coupling_model_bc][nwt_index],
	                   surface_coupling->foreign_target_data[coupling_model_bc][htc_index]);
    if (coupling_interface->NumMessages() > 0)
      dump_tpi_messages(coupling_interface, cmdl->model_filename);
    if (status == TPI_FAILURE)
      // dump_tpi_messages above should call msg_error so this should never execute
      msg_error("Failed to write convection BCs for \"%s\".",
		cmdl->model_filename);
  }
  status = coupling_interface->UpdateConvectionRecord();
  if (coupling_interface->NumMessages() > 0)
    dump_tpi_messages(coupling_interface, cmdl->model_filename);
  if (status == TPI_FAILURE)
    // dump_tpi_messages above should call msg_error so this should never execute
    msg_error("Failed to update convection BC record for \"%s\".",
              cmdl->model_filename);

  // now actually run the job
  //msg_print("surface coupling output timestep %f", unrounded_timestep );
  run_surface_coupling_job(window, model_index, timestep, unrounded_timestep);

}

static VOID maybe_launch_surface_coupling_jobs()
{
  TIMESTEP n_user_base_steps = cp_info.n_user_base_steps;

  ccDOTIMES(model_index, cp_info.n_surface_couplings) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings+model_index;
    SIM_ABSTRACT_SURFACE source_surf = surface_coupling->native_resulting_source_surface;
    if (source_surf && source_surf->NumFacetFaces() > 0) {
      continue; // handled by the regular window launching infrastructure 
    }
    // for one-way coupling windows (Foreign->PF), launch the application at cdi meas win time
    asINT32 win_index = cp_info.n_cdi_meas_windows - cp_info.n_surface_couplings + model_index;
    CDI_MEAS_WINDOW cdi_meas_win = cp_info.cdi_meas_windows + win_index;
    // launch directly without collecting window measurements
    if ( (cp_info.time >= surface_coupling->output_timestep) && 
         (cp_info.time < surface_coupling->next_update_time.output_time) ) {  //in user's timestep
#if DEBUG_VARIABLE_POWERTHERM_COUPLING
      msg_print("surface_coupling output timestep %d", surface_coupling->output_timestep);
#endif
      run_surface_coupling_job(NULL, model_index, surface_coupling->output_timestep);
      surface_coupling->output_timestep = surface_coupling->next_update_time.output_time;

      TIMESTEP base_output_timestep = surface_coupling->output_timestep * n_user_base_steps;  //in base steps
      TIMESTEP next_base_clear_time;
//      surface coupling is only done with a fluid meas windows. So no need to get conduction meas window timing parameters
      TIMESTEP next_base_output_timestep = compute_next_time (base_output_timestep, base_output_timestep,
                                                              &cdi_meas_win->fluid_time_desc, 
                                                              (TIMESTEP *) &(next_base_clear_time), NULL);   //in base steps
      surface_coupling->next_update_time.output_time = next_base_output_timestep / n_user_base_steps;  //in user's time
      surface_coupling->next_update_time.clear_time = next_base_clear_time / n_user_base_steps;  //in user's time  //does this clear_time really make sense here???
 
    }
  } // for model_index
}

#endif

VOID verify_precision_of_meas_var_data()
{
  // For an mme checkpoint restore, compare the precision of measusrement
  // variables in the file to the current setting and if they do not agree
  // stop the simulation
  if (!cp_info.is_mme_checkpoint_restore)
    return;

  DO_CP_MEAS_WINDOWS(window) {
    if (!window->coupling_window_p && (window->prev_nsets_written > 0)) {
      // deleted sri files will be opened when it is time to write the first frame
      if ((window->sri_file == NULL) &&
          (platform_file_present(window->output_filename))) {
        if (window->sri_file == NULL)
          window->open_sri_file_for_resume();

        BOOLEAN is_any_meas_var_dp = window->sri_file->is_any_meas_var_dp();
        if (is_any_meas_var_dp != window->is_meas_vars_output_dp) {
          msg_error("Precision of measurement variables in measurement file \"%s\" "
                    "does not match the precision requested for the resumed simulation.",
                    window->output_filename);
        }
      }
    }
  }
}

#if COPY_TMP_MEAS_FILES_AT_CKPT
static VOID maybe_restore_tmp_meas_file(CP_MEAS_WINDOW window)
{
  // If restarted from a checkpoint, possibly restore the .tmp meas file from the .tmp.ckpt file
  CHARACTER ckpt_filename[PLATFORM_MAXPATHLEN];
  sprintf(ckpt_filename, "%s.ckpt", window->output_filename);
  if (platform_file_present(ckpt_filename)) {
    msg_print("Restore from a ckpt tmp meas file");
    if (!platform_copy_file(ckpt_filename, window->output_filename))
      msg_warn("Cannot ocpy file \"%s\" to \"%s\"", ckpt_filename, window->output_filename); 
    // If the regular meas file exists, remove it
    STRING new_output_filename = strsave(window->output_filename);
    new_output_filename[strlen(new_output_filename) - 4] = '\0'; // Remove ".tmp" in the end
    platform_remove_file(new_output_filename);
    delete[] new_output_filename;
  }
}
#endif

VOID check_for_missing_measurement_files()
{
  // Only check if this is a checkpoint-restore
  if (cp_info.restart_base_time == 0)
    return;

  DO_CP_MEAS_WINDOWS(window) {
    if (window->is_average_mme)
      continue;

#if COPY_TMP_MEAS_FILES_AT_CKPT
    maybe_restore_tmp_meas_file(window);
#endif
    if ( (!window->coupling_window_p && window->prev_nsets_written > 0) ||
         window->m_is_particle_trajectory_window ) {
      // Measurement file should already exist from previous run. 
 
      // For meas window started via monitors, it is possible to find regular meas file 
      // when resuming from a ckpt before EIT (thus expecting a .tmp meas file). We need 
      // to modify window->output_filename if that happens.
      STRING file_ext_name = &window->output_filename[strlen(window->output_filename) - 4];
      BOOLEAN is_tmp_meas_file = (strcmp(file_ext_name, ".tmp") == 0); // Expects .tmp meas file

      if (window->cdi_meas_window->start_time_via_monitors_p && is_tmp_meas_file) {
        STRING nontemp_output_filename = strsave(window->output_filename);
        nontemp_output_filename[strlen(window->output_filename) - 4] = '\0';

        if (platform_file_present(window->output_filename)) {
          if (platform_file_present(nontemp_output_filename)) {
            msg_warn("Find both tmp and non-tmp meas files in current directory. Will use the "
                     "tmp file %s for resuming from ckpt.", window->output_filename);    
          } 
        } else { // .tmp meas file does not exist
          if (platform_file_present(nontemp_output_filename)) {
            msg_warn("Found non-tmp meas file %s while expecting tmp meas file %s. This happens probably because "
                     "the previous run proceeded after ckpt time and passed the end of initial transient.", 
                     nontemp_output_filename, window->output_filename);
            window->output_filename[strlen(window->output_filename) - 4] = '\0';
          } else {
            msg_warn("No measurement file found. A new measurement file will be created.");
          }
        }
        delete[] nontemp_output_filename;
      }

      if (!platform_file_present(window->output_filename)) {

       if (sim_args.allow_missing_meas_files == TRUE && !window->m_is_particle_trajectory_window) {
          // It is acceptable to make a new measurement file and continue
          msg_warn("File \"%s\" is missing. A new measurement file will be created "
                   "for the remaining results.", window->output_filename);
        }
        else {
          // -abort_if_missing_meas_files option specified. Abort simulation.
          msg_error("Unable to find measurement file \"%s\".", window->output_filename);
        }
      }
    } 
  }
}

VOID sCP_MEAS_WINDOW::post_recvs()
{
  static BOOLEAN mpi_types_initialized_p = FALSE;
  static MPI_Datatype eMPI_GRF_MEAS_FRAME_SP_TO_CP_MSG;
  static MPI_Datatype eMPI_LRF_MEAS_FRAME_SP_TO_CP_MSG;
  static MPI_Datatype eMPI_MOVB_MEAS_FRAME_SP_TO_CP_MSG;
  static MPI_Datatype eMPI_MBC_MEAS_FRAME_SP_TO_CP_MSG;
  if (!mpi_types_initialized_p) {
    sGRF_MEAS_FRAME_SP_TO_CP_MSG::mpi_type_init(&eMPI_GRF_MEAS_FRAME_SP_TO_CP_MSG);
    sLRF_MEAS_FRAME_SP_TO_CP_MSG::mpi_type_init(&eMPI_LRF_MEAS_FRAME_SP_TO_CP_MSG);
    sMOVB_MEAS_FRAME_SP_TO_CP_MSG::mpi_type_init(&eMPI_MOVB_MEAS_FRAME_SP_TO_CP_MSG);
    sMBC_MEAS_FRAME_SP_TO_CP_MSG::mpi_type_init(&eMPI_MBC_MEAS_FRAME_SP_TO_CP_MSG);
    mpi_types_initialized_p = TRUE;
  }

  // Don't write time-varying ref frame info to probe files
  if (!is_probe) {
    int tag = make_mpi_tag<eMPI_MSG::MRFRAME>(index);
    // The ref frame related messages are sent by only 1 SP (perhaps a different SP for each window)
    if (cp_info.is_global_ref_frame_time_varying) {
      global_nirf_info_msg.init(this->m_master_sp, my_proc_id);
      global_nirf_info_msg.set_nelems(1);
      global_nirf_info_msg.settag(tag);
      g_exa_sp_cp_comm.irecv(global_nirf_info_msg);
   }

    ccDOTIMES(l, cp_info.n_time_varying_lrfs()) {
      lrf_info_msg[l].init(this->m_master_sp, my_proc_id);
      lrf_info_msg[l].set_nelems(1);
      lrf_info_msg[l].settag(tag);
      g_exa_sp_cp_comm.irecv(lrf_info_msg[l]);
   }
 
    ccDOTIMES(i, cp_info.n_time_varying_mbcs()) {
      mbc_info_msg[i].init(this->m_master_sp, my_proc_id);
      mbc_info_msg[i].set_nelems(1);
      mbc_info_msg[i].settag(tag);
      g_exa_sp_cp_comm.irecv(mbc_info_msg[i]);
   }

    ccDOTIMES(l, cp_info.n_movb_xforms()) {
      movb_info_msg[l].init(this->m_master_sp, my_proc_id);
      movb_info_msg[l].set_nelems(1);
      movb_info_msg[l].settag(tag);
      g_exa_sp_cp_comm.irecv(movb_info_msg[l]);
   }

  }

  post_var_data_recvs();
}

// We always receive double precision data for reduction windows
template <typename MEAS_FLOAT_TYPE>
VOID  tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::post_var_data_recvs() 
{
  if (n_moving_meas_cells > 0) {
    ccDOTIMES(sp, total_sps) {
      int moving_tag = make_mpi_tag<eMPI_MSG::MOVMWIN>(index);
      MPI_Irecv(&m_n_moving_meas_cells_per_sp[sp], 1, eMPI_uINT32,
                 sp, moving_tag, eMPI_sp_cp_comm, &moving_meas_count_receive_requests[sp]);
    }
  }

  {
    int tag = make_mpi_tag<eMPI_MSG::MWIN>(index);
    ccDOTIMES(i, m_n_child_sps) {
      if (m_n_child_meas_cells[i] > 0) 
        MPI_Irecv(m_child_meas_cells[i], (sINT64)n_variables * m_n_child_meas_cells[i], eMPI_REDUCTION_FLOAT,
                  m_child_sps[i], tag, eMPI_sp_cp_comm, &m_child_receive_requests[i]);
    }
  }
}

template<typename FLOAT_TYPE> struct sFLOAT_TO_MPI_TYPE;

template<> struct sFLOAT_TO_MPI_TYPE<sFLOAT>
{
  static MPI_Datatype mpi_type() { return MPI_FLOAT; }
};

template<> struct sFLOAT_TO_MPI_TYPE<dFLOAT>
{
  static MPI_Datatype mpi_type() { return MPI_DOUBLE; }
};

template <typename MEAS_FLOAT_TYPE>
VOID tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::post_var_data_recvs()
{
  if (n_moving_meas_cells > 0) {
    ccDOTIMES(sp, total_sps) {
      int moving_tag = make_mpi_tag<eMPI_MSG::MOVMWIN>(index);
      MPI_Irecv(&m_n_moving_meas_cells_per_sp[sp], 1, eMPI_uINT32,
                 sp, moving_tag, eMPI_sp_cp_comm, &moving_meas_count_receive_requests[sp]);
    }
  }

  if (n_stationary_meas_cells > 0) {
    ccDOTIMES(i, m_n_participating_sps) {
      asINT32 sp = m_participating_sps[i];
      /* The following assumes the components are all sdFLOATS */
      int n_sdfloats_to_recv_from_sp = m_sp_n_meas_cells[sp] * n_variables;
      int tag = make_mpi_tag<eMPI_MSG::MWIN>(index);

      MPI_Irecv(sp_meas_cells(sp), n_sdfloats_to_recv_from_sp, sFLOAT_TO_MPI_TYPE<MEAS_FLOAT_TYPE>::mpi_type(),
                 sp, tag, eMPI_sp_cp_comm, &receive_requests[i]);
    }
  }
}  


VOID windows_post_initial_recvs()
{
  TIMESTEP n_user_base_steps = cp_info.n_user_base_steps;
  TIMESTEP exit_timestep =  n_user_base_steps * cp_info.end_time;

  DO_CP_MEAS_WINDOWS(window) {
    if (!window->is_average_mme)
      window->post_recvs();
  }
}


VOID windows_build_reduction_trees()
{
  MPI_Datatype mpi_type;
  sCP_TO_TREE_NODE_INIT_MSG::mpi_type_init(&mpi_type);

  STP_PROC total_ranks = total_sps + 1;
  STP_PROC *rank_map = xnew STP_PROC[total_ranks]; // Indexed by node index. Only n_participating_sps entries are valid.
  sTREE_NODE *tree = xnew sTREE_NODE[total_ranks]; // Also indexed by node index. Only n_participating_sps entries are valid.

  DO_CP_MEAS_WINDOWS(window) {
    if (window->is_average_mme)
      continue;
    // Make the rank map
    STP_PROC sp_node_count = 0;
    ccDOTIMES(sp, total_sps) {
      if(window->m_sp_n_meas_cells[sp] > 0) {
        rank_map[sp_node_count] = sp;
        sp_node_count++;
      }
    }
    window->m_n_participating_sps = sp_node_count;
    STP_PROC cp_node = sp_node_count;
    rank_map[cp_node] = total_sps;

    tree[cp_node].parent_node = NO_PARENT;

    // Composite window meas data
    if(window->use_tree_reduction()) {
      make_subtree(cp_node, sp_node_count + 1, WINDOW_MEAS_TREE_DEGREE, tree);

#if DEBUG_SIMTREE
      msg_print("=========> Reduction tree for window %d", window->index);
      print_tree_array(sp_node_count + 1, tree);
      print_subtree(cp_node, 2,  tree);
#endif
      
      CP_REDUCTION_MEAS_WINDOW win = (CP_REDUCTION_MEAS_WINDOW)window;
      win->m_n_child_sps = tree[cp_node].n_children;
      ccDOTIMES(child, win->m_n_child_sps) {
        win->m_child_sps[child]       = rank_map[tree[cp_node].children[child]];
        tree[cp_node].children[child] = rank_map[tree[cp_node].children[child]];
      }

      ccDOTIMES(sp_node, sp_node_count) {
        sCP_TO_TREE_NODE_INIT_MSG msg;
        int sp_rank = rank_map[sp_node];
        int tag = make_mpi_tag<eMPI_MSG::MWIN>(window->index);
        msg.parent_rank = rank_map[tree[sp_node].parent_node];
        msg.n_children = tree[sp_node].n_children;      
        ccDOTIMES(child, msg.n_children) {
          msg.child_rank[child] = rank_map[tree[sp_node].children[child]];
        }
        cp_init_mpi_send(&msg,1,mpi_type,sp_rank,tag,eMPI_sp_cp_comm);
      }
    }
    window->allocate_sp_meas_cells(); // also sets up reduction trees
  }
  delete[] rank_map;
  delete[] tree; 
  MPI_Type_free(&mpi_type);
}

// The order of meas windows in each SP's output queue must match the order of windows
// in the CP's output queue.
VOID sCP_MEAS_WINDOW::insert_in_queue(eQUEUE_TYPE queue_type)
{
  CP_MEAS_WINDOW window = this;
  TIMESTEP output_time = window->m_output_timestep;
  TIMESTEP next_output_time = window->m_next_update_time.output_time;
  if (output_time >= 0) {
    CP_MEAS_WINDOW_COLLECTION meas_windows = &cp_info.meas_windows;
    CP_MEAS_WINDOW queue = meas_windows->m_queues[queue_type];
    if (queue == NULL
        || (output_time < queue->m_output_timestep)
        || ((output_time == queue->m_output_timestep) && (index < queue->index))) {
      window->m_next_in_queue[queue_type] = queue;
      meas_windows->m_queues[queue_type] = window;      
    } else {
      CP_MEAS_WINDOW last;
      while (1) {
        last = queue;
        queue = queue->m_next_in_queue[queue_type];
        if (queue == NULL 
            || (output_time < queue->m_output_timestep)
            || ((output_time == queue->m_output_timestep) && (index < queue->index)))
          break;
      }
      window->m_next_in_queue[queue_type] = queue;
      last->m_next_in_queue[queue_type] = window;      
    }
  }
}

BOOLEAN sCP_MEAS_WINDOW::has_all_sp_data_arrived()
{
  int receive_flag;
  MPI_Status receive_status;

  // has SP ref frame and MBC data arrived
  if (!is_probe) {
    if (cp_info.is_global_ref_frame_time_varying) {
      MPI_Test(&global_nirf_info_msg.m_request, &receive_flag, &receive_status);
      if (!receive_flag)
        return FALSE;
    }

    ccDOTIMES(l, cp_info.n_time_varying_lrfs()) {
      MPI_Test(&lrf_info_msg[l].m_request, &receive_flag, &receive_status);
      if (!receive_flag)
        return FALSE;
    }

    ccDOTIMES(i, cp_info.n_time_varying_mbcs()) {
      MPI_Test(&mbc_info_msg[i].m_request, &receive_flag, &receive_status);
      if(!receive_flag)
        return FALSE;
    }

    ccDOTIMES(i, cp_info.n_movb_xforms()) {
      MPI_Test(&movb_info_msg[i].m_request, &receive_flag, &receive_status);
      if(!receive_flag) {
        return FALSE;
      }
    }
  }

  return has_sp_var_data_arrived();
}

template <typename MEAS_FLOAT_TYPE>
bool tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::has_moving_meas_cell_data_arrived()
{

  int receive_flag;
  if (n_moving_meas_cells == 0) {
    return true;
  }

  ccDO_FROM_BELOW(sp, m_next_sp_moving_index_to_test_for_count, total_sps) {
    MPI_Test(&moving_meas_count_receive_requests[sp], &receive_flag, MPI_STATUS_IGNORE);
    if(!receive_flag) {
      m_next_sp_moving_index_to_test_for_count = sp;
      return false;
    }
    else if (m_n_moving_meas_cells_per_sp[sp] > 0) {
      int moving_tag = make_mpi_tag<eMPI_MSG::MOVMWIN>(index);
      // post the request for the global meas cell indices and associated moving meas data
      // in that order (1 + n_variables) below
      int n_reduction_floats_to_recv_from_sp = m_n_moving_meas_cells_per_sp[sp] * (1 + n_variables);
      m_moving_data_recv_buffer[sp].resize(n_reduction_floats_to_recv_from_sp);
      MPI_Irecv(&m_moving_data_recv_buffer[sp][0], n_reduction_floats_to_recv_from_sp, sFLOAT_TO_MPI_TYPE<MEAS_FLOAT_TYPE>::mpi_type(),
                 sp, moving_tag, eMPI_sp_cp_comm, &moving_meas_data_receive_requests[sp]);
    }
    else {
      m_moving_data_recv_buffer[sp].resize(0);
    }
  }
  m_next_sp_moving_index_to_test_for_count = total_sps;

  ccDO_FROM_BELOW(sp, m_next_sp_moving_index_to_test_for_data, total_sps) {
    MPI_Test(&moving_meas_data_receive_requests[sp], &receive_flag, MPI_STATUS_IGNORE);
    if(!receive_flag) {
      m_next_sp_moving_index_to_test_for_data = sp;
      return false;
    }
  }
  m_next_sp_moving_index_to_test_for_data = total_sps;

  return true;

}

template <typename MEAS_FLOAT_TYPE>
bool tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::has_stationary_meas_cell_data_arrived()
{
  if (n_stationary_meas_cells == 0 ) {
    return true;
  }

  int receive_flag;

  // receive meas cells from all child SPs
    ccDO_FROM_BELOW(k, m_next_sp_index_to_test, m_n_participating_sps) {
      asINT32 sp = m_participating_sps[k];
      MPI_Test(&receive_requests[k], &receive_flag, MPI_STATUS_IGNORE);
      if(!receive_flag) {
        m_next_sp_index_to_test = k;
        return FALSE;
      }
    }

  return true;
}

template <typename MEAS_FLOAT_TYPE>
BOOLEAN tCP_NO_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::has_sp_var_data_arrived()
{
  bool stationary_ready = has_stationary_meas_cell_data_arrived();
  bool moving_ready = has_moving_meas_cell_data_arrived();

  if ( stationary_ready && moving_ready ) {
    m_next_sp_index_to_test = 0;
    m_next_sp_moving_index_to_test_for_count = 0;
    m_next_sp_moving_index_to_test_for_data = 0;
    return true;
  }

  return false;
}

// This function is divided up into 3 sections First, we test for
// moving_meas_cell counts from all the SPs. As a performance optimization, we
// test the SPs sequentially. If we haven't received a count from the currently
// tested SP, we bail. We only move onto the next section when all counts are
// true.  Second, we test for the actual data being received, in a similar
// manner to the first section.  Third, all counts and data have been received,
// so reset the state counters.

template <typename MEAS_FLOAT_TYPE>
bool tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::has_moving_meas_cell_data_arrived()
{

  int receive_flag;
  if (n_moving_meas_cells == 0) {
    return true;
  }

  ccDO_FROM_BELOW(sp, m_next_sp_moving_index_to_test_for_count, total_sps) {
    MPI_Test(&moving_meas_count_receive_requests[sp], &receive_flag, MPI_STATUS_IGNORE);
    if(!receive_flag) {
      m_next_sp_moving_index_to_test_for_count = sp;
      return false;
    }
    else if (m_n_moving_meas_cells_per_sp[sp] > 0) {
      int moving_tag = make_mpi_tag<eMPI_MSG::MOVMWIN>(index);
      // post the request for the global meas cell indices and associated moving meas data
      // in that order (1 + n_variables) below
      int n_reduction_floats_to_recv_from_sp = m_n_moving_meas_cells_per_sp[sp] * (1 + n_variables);
      m_moving_data_recv_buffer[sp].resize(n_reduction_floats_to_recv_from_sp);
      MPI_Irecv(&m_moving_data_recv_buffer[sp][0], n_reduction_floats_to_recv_from_sp, eMPI_REDUCTION_FLOAT,
                sp, moving_tag, eMPI_sp_cp_comm, &moving_meas_data_receive_requests[sp]);
    }
    else {
      m_moving_data_recv_buffer[sp].resize(0);
    }
  }
  m_next_sp_moving_index_to_test_for_count = total_sps;

  ccDO_FROM_BELOW(sp, m_next_sp_moving_index_to_test_for_data, total_sps) {
    MPI_Test(&moving_meas_data_receive_requests[sp], &receive_flag, MPI_STATUS_IGNORE);
    if(!receive_flag) {
      m_next_sp_moving_index_to_test_for_data = sp;
      return false;
    }
  }
  m_next_sp_moving_index_to_test_for_data = total_sps;

  return true;

}

template <typename MEAS_FLOAT_TYPE>
bool tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::has_stationary_meas_cell_data_arrived()
{
  int receive_flag;

  // receive meas cells from all child SPs
  while (m_n_child_sps_ready < m_n_child_sps) {
    int flag;
    MPI_Test(&m_child_receive_requests[m_n_child_sps_ready], &flag, MPI_STATUS_IGNORE);
    if (!flag) {
      return false;
    }
    m_n_child_sps_ready++;
  }

  return true;
}


template <typename MEAS_FLOAT_TYPE>
BOOLEAN tCP_REDUCTION_MEAS_WINDOW<MEAS_FLOAT_TYPE>::has_sp_var_data_arrived()
{

  bool moving_ready = has_moving_meas_cell_data_arrived();
  bool stationary_ready = has_stationary_meas_cell_data_arrived();

  // Don't put the above function calls on the same line, because then they
  // would short-circuit. We are relying on the side-effects of the
  // has_{moving,stationary}_meas_cell_data_arrived() functions

  if ( moving_ready && stationary_ready ) {
    m_n_child_sps_ready = 0;
    m_next_sp_moving_index_to_test_for_count = 0;
    m_next_sp_moving_index_to_test_for_data = 0;
    return true;
  }

  return false;
}

// Check the phase of variable PowerTHERM coupling windows
static VOID maybe_update_window_coupling_phases(CP_MEAS_WINDOW window)
{
  TIMESTEP n_user_base_steps = cp_info.n_user_base_steps;
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs + window->coupling_model_index;
  SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + window->coupling_model_index;
  CDI_CMDL cmdl = surface_coupling->get_cmdl();
  asINT32 n_phases = coupling_model_desc->m_coupling_phase_descs.size();

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
  msg_print("m_clear_timestep = %d, m_output_timestep = %d. They are "
            "window->m_next_update_time: clear %d output %d",
             window->m_clear_timestep, window->m_output_timestep, window->m_next_update_time.clear_time, 
             window->m_next_update_time.output_time);
#endif

  ccDO_FROM_TO(phase_index, 1, n_phases-1)
  {
    sCDI_COUPLING_PHASE_DESC coupling_phase = cmdl->m_coupling_phase_descs[phase_index]; 
    if (window->m_output_timestep == coupling_phase.start)
    {
      CDI_MEAS_WINDOW cdi_meas_win = coupling_model_desc->cdi_meas_window;
//      No need for conduction timing parameters. As coupling is done only with fluid meas windows.
      cdi_meas_win->fluid_time_desc.period = coupling_phase.period * n_user_base_steps; // in base steps
      cdi_meas_win->fluid_time_desc.interval = coupling_phase.interval * n_user_base_steps; // in base steps
      // time_desc.start should be the start of the averaging interval
      cdi_meas_win->fluid_time_desc.start = (coupling_phase.start)* n_user_base_steps
                                      + cdi_meas_win->fluid_time_desc.period 
                                      - cdi_meas_win->fluid_time_desc.interval;  // in base steps
      window->m_next_update_time.clear_time = cdi_meas_win->fluid_time_desc.start;
      window->m_next_update_time.output_time = window->m_next_update_time.clear_time 
                                               + cdi_meas_win->fluid_time_desc.interval;

      if (window->coupling_window_p) {
        surface_coupling->unrounded_time_desc.period = coupling_phase.exact_period * n_user_base_steps;
        surface_coupling->unrounded_time_desc.interval = coupling_phase.interval * n_user_base_steps;
        surface_coupling->unrounded_time_desc.start = coupling_phase.exact_start * n_user_base_steps
                                                  + surface_coupling->unrounded_time_desc.period
                                                  - surface_coupling->unrounded_time_desc.interval;
        surface_coupling->unrounded_next_output_time = surface_coupling->unrounded_time_desc.start + surface_coupling->unrounded_time_desc.interval;
      }

      // TODO: save time_desc.repeat when rounding the coupling times so that 
      // we can avoid the repeative calculation here
      if (phase_index == (n_phases - 1)) {
        if (cmdl->use_end_time_for_coupling) {
          cdi_meas_win->fluid_time_desc.repeat = (cmdl->end_time - coupling_phase.delay 
                                            - coupling_phase.start)/cdi_meas_win->fluid_time_desc.period;
        }
        else
          cdi_meas_win->fluid_time_desc.repeat = cmdl->num_iterations; // cmdl->num_iterations now stores the 
                                                                 // number of iterations for the last phase 
      } else {
        cdi_meas_win->fluid_time_desc.repeat = (cmdl->m_coupling_phase_descs[phase_index+1].start 
                                          - coupling_phase.start)/coupling_phase.period;
      }

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
      msg_print("\nCP: At timestep %d update cdi_meas_win->time_desc to: "
                "start: %d period: %d interval: %d repeat: %d\n", cp_info.time,
                 cdi_meas_win->fluid_time_desc.start, cdi_meas_win->fluid_time_desc.period, 
                 cdi_meas_win->fluid_time_desc.interval, cdi_meas_win->fluid_time_desc.repeat);
#endif
    }
  }  

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
  msg_print("m_clear_timestep = %d, m_output_timestep = %d. They are changed to "
            "window->m_next_update_time: clear %d output %d",
             window->m_clear_timestep, window->m_output_timestep, window->m_next_update_time.clear_time, 
             window->m_next_update_time.output_time);
#endif

}

/* Outputs all windows until timestep last_output_timestep*/

VOID output_windows_up_to_timestep(TIMESTEP last_output_timestep)
{
  TIMESTEP last_output_flow_timestep = last_output_timestep;
  TIMESTEP last_output_cond_timestep = last_output_timestep;
  if(cp_info.is_flow && cp_info.is_conduction) {
    last_output_flow_timestep = cp_info.convert_to_ts_flow(last_output_timestep);
    last_output_cond_timestep = cp_info.convert_to_ts_cond(last_output_timestep);
  }
  ccDOTIMES(iflush, 2) {
    // remove and process all entries from post recvs queue
    while (!cp_info.post_recvs_queue->is_empty()) {
      CP_MEAS_WINDOW window = cp_info.post_recvs_queue->remove_entry();
      if (window) {
        cp_info.post_recvs_queue->process(window, wallclock_time_secs());
      }
    }

    CP_MEAS_WINDOW_COLLECTION meas_windows = &cp_info.meas_windows;
    for (CP_MEAS_WINDOW window = meas_windows->m_queues[ROTDYN_OUTPUT_QUEUE];
        window != NULL;
        window = meas_windows->m_queues[ROTDYN_OUTPUT_QUEUE]) {
      if (window->m_output_timestep > last_output_flow_timestep)
        break;
      windows_output<ROTDYN_OUTPUT_QUEUE>(wallclock_time_secs(), FALSE);
    }

    for (CP_MEAS_WINDOW window = meas_windows->m_queues[FLOW_OUTPUT_QUEUE];
        window != NULL;
        window = meas_windows->m_queues[FLOW_OUTPUT_QUEUE]) {
      if (window->m_output_timestep > last_output_flow_timestep)
        break;
      windows_output<FLOW_OUTPUT_QUEUE>(wallclock_time_secs(), FALSE);
    }


    for (CP_MEAS_WINDOW window = meas_windows->m_queues[COND_OUTPUT_QUEUE];
        window != NULL;
        window = meas_windows->m_queues[COND_OUTPUT_QUEUE]) {
      if (window->m_output_timestep > last_output_cond_timestep)
        break;
      windows_output<COND_OUTPUT_QUEUE>(wallclock_time_secs(), FALSE);
    }

    // remove and process all entries from write sri results queue
    while (!cp_info.write_sri_results_queue->is_empty()) {
      CP_MEAS_WINDOW window = cp_info.write_sri_results_queue->remove_entry();
      if (window) {
        cp_info.write_sri_results_queue->process(window, wallclock_time_secs());
      }
    }
  }
}

static BOOLEAN exit_windows_queue_loop(CP_MEAS_WINDOW window, TIMESTEP exit_timestep, BOOLEAN &some_remaining_window)
{
  // if coupling phase table is used, check the end time
  if (window->coupling_window_p)
  {
    COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs + window->coupling_model_index;
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + window->coupling_model_index;
    CDI_CMDL cmdl = surface_coupling->get_cmdl();
    asINT32 n_phases = coupling_model_desc->m_coupling_phase_descs.size();

    if ((n_phases > 0) && (window->m_output_timestep > cmdl->end_time * cp_info.n_user_base_steps))
      return TRUE;
  }

  if (window->m_output_timestep > exit_timestep) {
    return TRUE;
  } else if (!window->has_all_sp_data_arrived()) {
    some_remaining_window = TRUE;
    return TRUE;
  }
  return FALSE;
}

template <eQUEUE_TYPE queue_type>
static void dequeue_window(CP_MEAS_WINDOW window)
{
#if DEBUG_VARIABLE_POWERTHERM_COUPLING
  if (window->coupling_window_p && (queue_type == FLOW_OUTPUT_QUEUE || queue_type == COND_OUTPUT_QUEUE))
    msg_print("CP: at timestep %d remove meas window %d-%d", cp_info.time, window->m_clear_timestep, window->m_output_timestep);
#endif

  // remove window from head of output queue
  cp_info.meas_windows.m_queues[queue_type] = window->m_next_in_queue[queue_type];
}

static void launch_coupling_app_or_composite_moving_cells(CP_MEAS_WINDOW window)
{
  // Write results file or launch coupling application
  if (window->coupling_window_p) {
#if SURF_COUP
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + window->coupling_model_index;
    launch_surface_coupling_app(static_cast<CP_SURFACE_MEAS_WINDOW>(window), 
                                window->m_output_timestep/cp_info.n_user_base_steps, 
                                FALSE,
                                surface_coupling->unrounded_output_time/cp_info.n_user_base_steps);
    // check if the output timestep is the end of the current phase.
    maybe_update_window_coupling_phases(window);
#endif // SURF_COUP
  } else {
    if (window->n_moving_meas_cells > 0) {
      window->composite_moving_meas_cells();
    }
  }
}

template <eQUEUE_TYPE queue_type>
static void queue_window(CP_MEAS_WINDOW window)
{
#if DEBUG_VARIABLE_POWERTHERM_COUPLING
  if (window->coupling_window_p &&(queue_type == FLOW_OUTPUT_QUEUE || queue_type == COND_OUTPUT_QUEUE))
    msg_print("CP: meas window %d-%d is inserted in the queue", window->m_clear_timestep, window->m_output_timestep);
#endif
  window->insert_in_queue(queue_type);  
}

static BOOLEAN compute_next_output_time(CP_MEAS_WINDOW window)
{
  // compute next output and clear times (in base steps)
  window->m_clear_timestep   = window->m_next_update_time.clear_time;
  window->m_output_timestep  = window->m_next_update_time.output_time;

  if (window->coupling_window_p) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + window->coupling_model_index;
#if DEBUG_VARIABLE_POWERTHERM_COUPLING
    msg_print("compute next output time SET unrounded output time to %f", surface_coupling->unrounded_next_output_time);
#endif
    surface_coupling->unrounded_output_time = surface_coupling->unrounded_next_output_time;
  }

  if (window->m_output_timestep == TIMESTEP_NEVER) {       
    window->is_valid = FALSE;
    if (window->sri_file) { // could be a surface coupling window
      window->sri_file->close_file(FALSE);
      window->sri_file = NULL; // indicates window is no longer open
    }
    return FALSE;
  }
 
  asINT32 n_prior_frames;
  STP_REALM realm = lgi_meas_window_type_realm(window->meas_window_type);
  TIME_DESC time_desc = 
      (realm == STP_COND_REALM) ? &window->cdi_meas_window->cond_time_desc : &window->cdi_meas_window->fluid_time_desc;
  window->m_next_update_time.output_time = compute_next_time(window->m_output_timestep, window->m_output_timestep,
                                                             time_desc,
                                                             &window->m_next_update_time.clear_time, &n_prior_frames);
  if (window->coupling_window_p) {
    SURFACE_COUPLING surface_coupling = cp_info.surface_couplings + window->coupling_model_index;
    surface_coupling->unrounded_next_output_time = compute_next_unrounded_time(n_prior_frames,
                                                                               &surface_coupling->unrounded_time_desc);
  }
  return TRUE;
}

static void sync_window(CP_MEAS_WINDOW window, WALLCLOCK_TIME_SECS time_secs)
{
  const asINT32 N_SECS_BEFORE_SYNC = 5 * 60;  // 5 minutes

  if ((window->n_bytes_since_last_sync > 0)
      && WALLCLOCK_TIME_DIFF(time_secs, window->m_time_of_last_sync) > N_SECS_BEFORE_SYNC) {
    if (!window->coupling_window_p) {
      window->sync(time_secs);
    }
  }
}

VOID sWINDOW_QUEUE_MT::process(CP_MEAS_WINDOW window, WALLCLOCK_TIME_SECS time_secs) {

  TIMESTEP n_user_base_steps = cp_info.n_user_base_steps;
  TIMESTEP exit_timestep =  n_user_base_steps * cp_info.end_time;
  if(cp_info.is_flow && cp_info.is_conduction) {
    if(window->is_cond_window())
      exit_timestep = cp_info.convert_to_ts_cond(cp_info.end_time);
    else
      exit_timestep = cp_info.convert_to_ts_flow(cp_info.end_time);
  }
  if (m_queue_type == WRITE_SRI_RESULTS_QUEUE) {
    window->write_sri_results(time_secs);
    // If the coupling meas window is in the output queue, the next output time is
    // already updated in windows_output()
    if (!window->coupling_window_p) {
      if (compute_next_output_time(window)) {
        if (window->m_output_timestep <= exit_timestep) {
          cp_info.post_recvs_queue->add_entry(window);
        }
        sync_window(window, time_secs);
      }
    }
  } else if (m_queue_type == POST_RECVS_QUEUE) {
    window->post_recvs();
    if(window->is_cond_window())
      queue_window<COND_OUTPUT_QUEUE>(window);
    else
      queue_window<FLOW_OUTPUT_QUEUE>(window);
  } else {
    msg_internal_error("This queue type %d is not supported", m_queue_type);
  }
}

/* Outputs all of the windows that have output pending */
template <eQUEUE_TYPE queue_type>
VOID windows_output(WALLCLOCK_TIME_SECS time_secs, BOOLEAN output_all_remaining_windows)
{
#if SURF_COUP
  if (queue_type == ROTDYN_OUTPUT_QUEUE || queue_type == FLOW_OUTPUT_QUEUE || queue_type == COND_OUTPUT_QUEUE)
    maybe_launch_surface_coupling_jobs();
#endif
  TIMESTEP n_user_base_steps = cp_info.n_user_base_steps;
  TIMESTEP exit_timestep =  n_user_base_steps * cp_info.end_time;
  if(cp_info.is_flow && cp_info.is_conduction) {
    if(queue_type == FLOW_OUTPUT_QUEUE)
      exit_timestep = cp_info.convert_to_ts_flow(cp_info.end_time);
    else if(queue_type == COND_OUTPUT_QUEUE)
      exit_timestep = cp_info.convert_to_ts_cond(cp_info.end_time);
  }

  CP_MEAS_WINDOW_COLLECTION meas_windows = &cp_info.meas_windows;
  BOOLEAN some_remaining_window;
  BOOLEAN exit_now;
  do { // loop while (some_remaining_window && output_all_remaining_windows)
    some_remaining_window = FALSE;
    exit_now = FALSE;
    for (CP_MEAS_WINDOW window = meas_windows->m_queues[queue_type]; 
         window != NULL && !exit_now;
         window = meas_windows->m_queues[queue_type]) {

      switch(queue_type) {
        case FLOW_OUTPUT_QUEUE:
        case COND_OUTPUT_QUEUE:
        case ROTDYN_OUTPUT_QUEUE:
          if ((exit_now = exit_windows_queue_loop(window, exit_timestep, some_remaining_window)))
            break;
          dequeue_window<queue_type>(window);
          launch_coupling_app_or_composite_moving_cells(window);
          if (!window->coupling_window_p) {
            if (queue_type == ROTDYN_OUTPUT_QUEUE) {
              window->write_sri_results(time_secs);
            } else {
              cp_info.write_sri_results_queue->add_entry(window);
            }
          }
#if DEBUG_SURFACE_COUPLING_MEAS_WINDOW
          if (window->coupling_window_p) {
            window->write_sri_results(time_secs);
          }
#endif
          if (window->coupling_window_p || queue_type == ROTDYN_OUTPUT_QUEUE) {
            if (compute_next_output_time(window)) {
              queue_window<queue_type>(window);
              if (window->m_output_timestep <= exit_timestep)
                window->post_recvs();
              sync_window(window, time_secs);
            }
          }
          break;

        default :
          msg_internal_error("This queue type %d is not supported", queue_type);
          break;

      }
    }
  } while (some_remaining_window && output_all_remaining_windows);
  
  if (queue_type == FLOW_OUTPUT_QUEUE || queue_type == COND_OUTPUT_QUEUE ) {
    //Check if any trajectory windows need to be flushed to disk (these operate outside the output queue).
    ccDOTIMES(trajectory_window_index, g_trajectory_windows.size()) {
      g_trajectory_windows[trajectory_window_index]->sync(time_secs);
    }
  }
}

template void windows_output<ROTDYN_OUTPUT_QUEUE>(WALLCLOCK_TIME_SECS time_secs, BOOLEAN output_all_remaining_windows);
template void windows_output<FLOW_OUTPUT_QUEUE>(WALLCLOCK_TIME_SECS time_secs, BOOLEAN output_all_remaining_windows);
template void windows_output<COND_OUTPUT_QUEUE>(WALLCLOCK_TIME_SECS time_secs, BOOLEAN output_all_remaining_windows);

#if COPY_TMP_MEAS_FILES_AT_CKPT
// Checkpoint the tmp meas files if the meas window is started via monitors
// and it has not been started at the ckpt time (tmp meas file exists)
VOID ckpt_tmp_meas_windows()
{
  DO_CP_MEAS_WINDOWS(window) {
    CDI_MEAS_WINDOW cdi_win = window->cdi_meas_window;  

    CHARACTER tmpckpt_filename[PLATFORM_MAXPATHLEN];
    CHARACTER ckpt_filename[PLATFORM_MAXPATHLEN];
    sprintf(tmpckpt_filename, "%s.ckpt.ctmp", window->output_filename);
    sprintf(ckpt_filename, "%s.ckpt", window->output_filename);

    // Use window->sri_file to check if the meas file is present. 
    // platform_file_present() might not work since the meas file may be
    // from previous simulations.
    if (cdi_win->start_time_via_monitors_p && window->sri_file) {
      // Need to sync the file since the on-disk file may not be up to date
      window->sri_file->sync_file();
      if (platform_copy_file(window->output_filename, tmpckpt_filename)) {
        platform_rename_file(tmpckpt_filename, ckpt_filename);
#if DEBUG_MONITOR    
        msg_print("cp meas window not started at ckpt, copy the tmp meas file %s to ckpt file %s",
                  window->output_filename, ckpt_filename);
#endif      
      }
    } else {
      // Remove ckpt tmp file if the meas window alreasy started
      platform_remove_file(ckpt_filename);
    }
  }
}
#endif

/* Determine a solver_mask according to a set of SRI_VARIABLE_TPREs.
 * This new fuction is for measurement support for subcycling. The SRI_VARIABLE_TPREs
 * listed bellow are not a complete set, they are only the variables really for output 
 * in the simulator (with definitions). Hence, if it is decided to add new SRI_VARIABLE_TPREs
 * in measurement output lately, we need to add them into this fuction.
 */

STP_ACTIVE_SOLVER_MASK window_set_solver_mask_from_sri_vars(asINT32 n_vars, 
							    SRI_VARIABLE_TYPE *var_types, 
							    BOOLEAN is_ht, 
							    BOOLEAN is_turb,
							    BOOLEAN is_active_ht)
{
  
  STP_ACTIVE_SOLVER_MASK solver_mask = 0;
    
  for(asINT32 i = 0; i < n_vars; i++) {
    SRI_VARIABLE_TYPE var_type = var_types[i];
    switch (var_type) {
    case SRI_VARIABLE_DENSITY:
    case SRI_VARIABLE_STD_DEV_DENSITY:
    case SRI_VARIABLE_XVEL:
    case SRI_VARIABLE_YVEL:
    case SRI_VARIABLE_ZVEL:
    case SRI_VARIABLE_VEL_MAG:
    case SRI_VARIABLE_KINETIC_ENERGY:
    case SRI_VARIABLE_DIV_U:
    case SRI_VARIABLE_XMOMENTUM:
    case SRI_VARIABLE_YMOMENTUM:
    case SRI_VARIABLE_ZMOMENTUM:
    case SRI_VARIABLE_MOMENTUM_MAG:
    case SRI_VARIABLE_STD_DEV_XVEL:
    case SRI_VARIABLE_STD_DEV_YVEL:
    case SRI_VARIABLE_STD_DEV_ZVEL:
    case SRI_VARIABLE_STD_DEV_VEL_MAG:
    case SRI_VARIABLE_LB_ENERGY: 
    case SRI_VARIABLE_FLUID_XFORCE:
    case SRI_VARIABLE_FLUID_YFORCE:
    case SRI_VARIABLE_FLUID_ZFORCE:
    case SRI_VARIABLE_FLUID_FORCE_MAG:
    case SRI_VARIABLE_FLUID_XTORQUE:
    case SRI_VARIABLE_FLUID_YTORQUE:
    case SRI_VARIABLE_FLUID_ZTORQUE:
    case SRI_VARIABLE_FLUID_TORQUE_MAG:
    case SRI_VARIABLE_XFORCE:
    case SRI_VARIABLE_YFORCE:
    case SRI_VARIABLE_ZFORCE:
    case SRI_VARIABLE_KINETIC_XFORCE:
    case SRI_VARIABLE_KINETIC_YFORCE:
    case SRI_VARIABLE_KINETIC_ZFORCE:
    case SRI_VARIABLE_TFORCE_MAG:
    case SRI_VARIABLE_XTORQUE:
    case SRI_VARIABLE_YTORQUE:
    case SRI_VARIABLE_ZTORQUE:
    case SRI_VARIABLE_XPRESSURE_GRADIENT:
    case SRI_VARIABLE_YPRESSURE_GRADIENT:
    case SRI_VARIABLE_ZPRESSURE_GRADIENT:
    case SRI_VARIABLE_PRESSURE_GRADIENT_MAG:
    case SRI_VARIABLE_FORCE_MAG:
    case SRI_VARIABLE_USTAR:
    case SRI_VARIABLE_YPLUS:
    case SRI_VARIABLE_THERMAL_YPLUS:
    case SRI_VARIABLE_KINETIC_FORCE_MAG:
    case SRI_VARIABLE_TORQUE_MAG:
    case SRI_VARIABLE_MASS_FLUX:
    case SRI_VARIABLE_VISCOSITY:
      solver_mask |= LB_ACTIVE;
      break;

    case SRI_VARIABLE_TEMP:
    case SRI_VARIABLE_TOTAL_TEMPERATURE:
    case SRI_VARIABLE_STD_DEV_TEMP:
    case SRI_VARIABLE_NEAR_WALL_TEMP:
    case SRI_VARIABLE_HEAT_FLUX:
    case SRI_VARIABLE_HTC_CHAR_TEMP:
    case SRI_VARIABLE_LIQUID_FRACTION:
      if(is_ht)
	solver_mask |= T_PDE_ACTIVE;
      else
	solver_mask |= LB_ACTIVE;
      break;

    case SRI_VARIABLE_ENERGY:
    case SRI_VARIABLE_INTERNAL_ENERGY:
    case SRI_VARIABLE_ENTHALPY:
    case SRI_VARIABLE_HEAT_GENERATION: 
      solver_mask |= (LB_ACTIVE | T_PDE_ACTIVE);
      break;

    case SRI_VARIABLE_HTC_NEAR_WALL_TEMP:
      if(is_ht)
	solver_mask |= T_PDE_ACTIVE;
      else
	solver_mask |= LB_ACTIVE;
      break;

    case SRI_VARIABLE_TURB_KINETIC_ENERGY:
    case SRI_VARIABLE_TURB_DISSIPATION:
    case SRI_VARIABLE_STRESS_TENSOR_MAG:
    case SRI_VARIABLE_EDDY_VISCOSITY:
    case SRI_VARIABLE_XVORTICITY: 
    case SRI_VARIABLE_YVORTICITY: 
    case SRI_VARIABLE_ZVORTICITY:   
    case SRI_VARIABLE_SWIRL:
    case SRI_VARIABLE_VORTICITY_MAG:   
    case SRI_VARIABLE_LAMBDA2:   
      solver_mask |= KE_PDE_ACTIVE;
      break;

    case SRI_VARIABLE_TURBULENT_THERMAL_CONDUCTIVITY:
      solver_mask |= (KE_PDE_ACTIVE | T_PDE_ACTIVE);
      break;
      
    case SRI_VARIABLE_PRESSURE:          
    case SRI_VARIABLE_STD_DEV_PRESSURE:
      if(is_active_ht)
	solver_mask |= (LB_ACTIVE | T_PDE_ACTIVE);
      else
	solver_mask |= LB_ACTIVE;
      break;
	    
    case SRI_VARIABLE_N_SCREENED_VOXELS:
    case SRI_VARIABLE_SCREENED_VOLUME:
    case SRI_VARIABLE_VOLUME:
    case SRI_VARIABLE_N_SCREENED_SURFELS:
    case SRI_VARIABLE_SCREENED_AREA:
    case SRI_VARIABLE_AREA:
      solver_mask |= LB_ACTIVE; //in powercase, time setting is based on LB timer
      break;
    case SRI_VARIABLE_WATER_VAPOR_MFRAC:
    case SRI_VARIABLE_WATER_VAPOR_MFLUX:
    case SRI_VARIABLE_WATER_FILM_THICKNESS:
    case SRI_VARIABLE_DEFROST_TIME:
      if (cp_info.is_water_vapor_transport)
        solver_mask |=  UDS_PDE_ACTIVE;
      break;
    case SRI_VARIABLE_IRRADIATION:
    case SRI_VARIABLE_RADIOSITY:
      if (cp_info.is_radiation) {
        solver_mask |= (LB_ACTIVE | T_PDE_ACTIVE);
      }
      break;
    default:
      if (cp_info.is_5g_sim && cp_info.cvid_helper->is_5g_var_id(var_type)) {	
	if (cp_info.is_uds_transport) {
	  SRI_VARIABLE_TYPE offset = cp_info.cvid_helper->get_5g_var_type(var_type);
	  if (offset == SRI_VARIABLE_WATER_VAPOR_MFRAC ||offset== SRI_VARIABLE_WATER_VAPOR_MFLUX)  //no Case support for 5G UDS yet
	    solver_mask |=  UDS_PDE_ACTIVE;
	  else
	    solver_mask |= LB_ACTIVE;
	} else
	  solver_mask |= LB_ACTIVE;
	
      }
      else {
        if (sri_is_particle_tracking_vartype(var_type)) {
          // without this bodge, the cdi_meas_window time_desc structure won't be populated
          // if a window has only particle meas variables in it
          solver_mask |= LB_ACTIVE | PARTICLE_ACTIVE;
        } else if (cp_info.is_uds_transport && cp_info.cvid_helper->is_uds_var_id(var_type)) {
	  eSRI_VARIABLE_UDS_OFFSET offset = cp_info.cvid_helper->get_uds_var_type(var_type);
	  if (offset == SRI_VARIABLE_UDS_SCALAR_OFFSET ||offset== SRI_VARIABLE_UDS_SOURCE_TERM_OFFSET || offset== SRI_VARIABLE_UDS_SCALAR_FLUX_OFFSET)
	    solver_mask |=  UDS_PDE_ACTIVE;
	  else
	    msg_internal_error("Unhandled uds measurement variable offset type %d",  (int)offset);
	} else
	  msg_internal_error("Unhandled measurement variable type %d",
			     var_type);
      }
      break;
    }
  }

  if(!is_ht)
    solver_mask &= (~T_PDE_ACTIVE);
  
  if (!is_turb)
    solver_mask &= (~KE_PDE_ACTIVE);

  return solver_mask;
}

// align_group_flow_meas_window_time_parameters
//
// Here's the logic. Note that this description pre-dates the round_period_down flag,
// the no_shift_start_time flag, sub-cycling, and the concept of a period sync group. 
//
// The rounded averaging interval is the integer multiple of the coarsest timestep 
// which is closest to the given interval (that's the first term of the MAX). But, we
// don't accept anything less than the coarsest timestep (that's the second term of 
// the MAX).
//
// half_avg is half of avg -- the distance from the center of a bar to the end.
//
// The rounded period is the nearest integer multiple of the coarsest_timestep,
// but in any event no less than the rounded averaging interval.
//
// The rounded start time is a time no later than the given start time, possibly rounded
// down to ensure that the measurement bar starts and ends at an integer multiple of
// the coarsest timestep (that's the first term of the MAX).  But, we make sure that
// the start of the first bar is no earlier than zero; in other words, the minimum
// rounded start time is half the rounded averaging interval (that's the second term of
// the MAX).  The math of the first term works like this:  s+half_avg is the user's
// given time for the end of the first bar, which is then rounded *down* to the nearest
// integer multiple of the coarest timestep, then we subtract half the averaging interval
// to arrive at the middle of the first bar.  
//
// The rounded end time is the middle of the latest bar which starts earlier than the
// given end time -- ie. we include any bar that's even partially within the user's
// desired end time.  That means we want to count any bar which lies partially to the
// left of the given end time.  So the number of bars is the ceiling of the given end
// time minus the start of the first bar (ts_prime - ha_prime), divided by the period.
// The time from the center of the first bar to the center of the last bar is the number
// of bars, minus one (classic fencepost), times p_prime.  This plus ts_prime gives
// te_prime.

// Returns min_period_offset for the windows in the period sync group, which is then passed 
// to a second call to this function that occurs when timing parameters of some window in the 
// group have been changed via a --meas_timing command line option. min_period_offset is used 
// to align the frames of the windows of the period sync group. On the second call, if some
// windows in the group should retain their original alignment, prior_min_period_offset
// is used to align the altered windows according to the original alignment.

static TIMESTEP align_group_flow_meas_window_time_parameters(std::vector< CDI_MEAS_WINDOW > &cdi_meas_window_group,
                                                             TIMESTEP prior_min_period_offset) // -1 if to be ignored
{
  TIMESTEP max_n_lb_base_steps = 1;
  TIMESTEP max_n_t_base_steps  = 1;
  TIMESTEP max_n_ke_base_steps = 1;
  TIMESTEP max_n_uds_base_steps = 1;
  TIMESTEP max_n_particle_base_steps = 1;
  
  dFLOAT  min_period            = TIMESTEP_MAX;
  dFLOAT  min_round_down_period = TIMESTEP_MAX;
  asINT32 coarsest_shob_scale   = FINEST_SCALE;
  dFLOAT  n_user_base_steps     = cp_info.n_user_base_steps;
  // the least common multiple (lcm) of timesteps of solvers included in all window's solver_mask 
  TIMESTEP lcm_n_flow_steps;

  // The averaging intervals of the windows in the group are independent, and thus calculated separately.
  // We also initially calculate the periods of the windows independently so that the round_period_down
  // flag can be handled independently. To see why, consider a pair of windows where the period must
  // be a multiple of 8 for each, one with user period 33 and round_period_down set, the other with user
  // period 31 and round_period_down unset. The desired period is 32.
  static std::vector<TIMESTEP> avg_intervals; // static so we don't have to allocate repeatedly
  avg_intervals.reserve(cdi_meas_window_group.size());
  avg_intervals.resize(0);
  ccDOTIMES(i, cdi_meas_window_group.size()) {
    CDI_MEAS_WINDOW cdi_win = cdi_meas_window_group[i];
    TIME_DESC time_desc = &cdi_win->fluid_time_desc;

    TIMESTEP n_lb_base_steps = 1;
    TIMESTEP n_t_base_steps  = 1;
    TIMESTEP n_ke_base_steps = 1;
    TIMESTEP n_uds_base_steps = 1;
    TIMESTEP n_particle_base_steps = 1;
    TIMESTEP n_min_base_steps = TIMESTEP_MAX;
    if (cdi_win->solver_mask & LB_ACTIVE) {
      n_lb_base_steps = max_n_lb_base_steps = cp_info.n_lb_base_steps;
      if (n_min_base_steps > n_lb_base_steps) n_min_base_steps = n_lb_base_steps;
    }
    if (cdi_win->solver_mask & T_PDE_ACTIVE) {
      n_t_base_steps  = max_n_t_base_steps  = cp_info.n_t_base_steps;
      if (n_min_base_steps > n_t_base_steps) n_min_base_steps = n_t_base_steps;
    }
    if (cdi_win->solver_mask & KE_PDE_ACTIVE) {
      n_ke_base_steps = max_n_ke_base_steps = cp_info.n_ke_base_steps;
      if (n_min_base_steps > n_ke_base_steps) n_min_base_steps = n_ke_base_steps;
    }
    if (cdi_win->solver_mask & UDS_PDE_ACTIVE) {
      n_uds_base_steps = max_n_uds_base_steps = cp_info.n_uds_base_steps;
      if (n_min_base_steps > n_uds_base_steps) n_min_base_steps = n_uds_base_steps;
    }
    if ((n_lb_base_steps + n_t_base_steps + n_ke_base_steps + n_uds_base_steps) > 4) {
      lcm_n_flow_steps = lcm(n_lb_base_steps,
                             n_t_base_steps,
                             n_ke_base_steps,
                             n_uds_base_steps,
                             n_particle_base_steps) / n_min_base_steps;
    } else {
      lcm_n_flow_steps = 1;
    }
    cdi_win->lcm_flow_tsteps = (TIMESTEP)lcm_n_flow_steps;

    dFLOAT ct  = lcm_n_flow_steps * scale_to_delta_t(cdi_win->coarsest_fluid_shob_scale); // coarsest timestep
    dFLOAT a   = n_user_base_steps * cdi_win->average_interval;
    dFLOAT p   = n_user_base_steps * cdi_win->period;
    dFLOAT avg = 0;
    if (a >= 0) {
      avg = MAX(ct * floor(a / ct + 0.5), ct);
      // ensure avg is even - may be odd in subcycling if ct is odd
      if (((int)avg & 1) && ct != 1) {
        if (avg >= a && avg != ct)
          avg -= ct;
        else
          avg += ct;
      }
    }
    avg_intervals.push_back(avg);
    BOOLEAN round_period_down = cdi_win->mstp.standard_mask & CDI_MEAS_OPT_ROUND_PERIOD_DOWN;
    dFLOAT period;
    if (round_period_down) {
      period = MAX(ct * floor(p / ct), ct);
      min_round_down_period = MIN(period, min_round_down_period);
    } else {
      // traditionally we have rounded up period to avg, but perhaps this is not what the user expects...
      if (a >= 0)
        period = MAX(ct * floor(p / ct + 0.5), avg);
      else
        period = MAX(ct * floor(p / ct + 0.5), ct);
    }
    // ensure period is even - may be odd in subcycling if ct is odd
    if (((int)period & 1) && ct != 1) {
      if (period >= p && period != ct)
        period -= ct;
      else
        period += ct;
    }
    min_period = MIN(period, min_period);
    coarsest_shob_scale = coarsest_scale_of_pair(cdi_win->coarsest_fluid_shob_scale, coarsest_shob_scale);
  }

  dFLOAT period;
  dFLOAT ct;     // coarsest timestep of group
  if (cdi_meas_window_group.size() > 1) {
    if ((max_n_lb_base_steps + max_n_t_base_steps + max_n_ke_base_steps + max_n_uds_base_steps) > 4) {
      TIMESTEP n_min_base_steps = std::min({max_n_lb_base_steps, max_n_t_base_steps, max_n_ke_base_steps,
                                            max_n_uds_base_steps, max_n_particle_base_steps});
      lcm_n_flow_steps = lcm(max_n_lb_base_steps,
                               max_n_t_base_steps,
                               max_n_ke_base_steps,
                               max_n_uds_base_steps,
                               max_n_particle_base_steps) / n_min_base_steps;
    } else {
      lcm_n_flow_steps = 1;
    }
    ct = lcm_n_flow_steps * scale_to_delta_t(coarsest_shob_scale);
    period = MAX(ct * floor(min_period / ct + 0.5), ct);
    if (period > min_round_down_period)
      period = MAX(ct * floor(min_period / ct), ct);
    // ensure period is even - may be odd in subcycling if ct is odd
    if (((int)period & 1) && ct != 1) {
      if (period >= min_period && period != ct)
        period -= ct;
      else
        period += ct;
    }
  } else {
    ct = lcm_n_flow_steps * scale_to_delta_t(coarsest_shob_scale);
    period = min_period;
  }

  // If the period of the group changed, then we restart all windows at cp_info.restart_time
  if (prior_min_period_offset >= 0
      && cdi_meas_window_group[0]->fluid_time_desc.period != (TIMESTEP)period) {
    ccDOTIMES(i, cdi_meas_window_group.size()) {
      CDI_MEAS_WINDOW cdi_win = cdi_meas_window_group[i];
      if (cdi_win->start_time < cp_info.restart_time)
        cdi_win->start_time = cp_info.restart_time;
      cdi_win->min_start_time = cp_info.restart_time;
    }
  }

  BOOLEAN some_win_clear_time_before_restart_time = FALSE;
  if (prior_min_period_offset >= 0 && cdi_meas_window_group.size() > 1) {
    ccDOTIMES(i, cdi_meas_window_group.size()) {
      CDI_MEAS_WINDOW cdi_win = cdi_meas_window_group[i];
      TIMESTEP prior_clear_time;
      asINT32 n_prior_frames;
      compute_next_time(0, cp_info.restart_base_time,
                        &cdi_win->fluid_time_desc, &prior_clear_time, &n_prior_frames);
      if (prior_clear_time < cp_info.restart_base_time) {
        CP_MEAS_WINDOW cp_meas_win = cp_info.meas_windows[cdi_win->one_window];
        if (cp_meas_win) {
          STRING filename = cp_meas_win->compose_output_pathname();
          if (platform_file_present(filename)) {
            some_win_clear_time_before_restart_time = TRUE;
            break;
          }
        }
      }
    }
  }

  dFLOAT   half_ct = MAX(ct/2, 1.0); // integer
  TIMESTEP min_period_offset;
  BOOLEAN  use_prior_min_period_offset;
  // If the period of the group changed, prior_min_period_offset is irrelevant.
  // If all clear times are after cp_info.restart_time, prior_min_period_offset is irrelevant.
  if (prior_min_period_offset >= 0
      // Note that the periods of all windows in the group are identical
      && cdi_meas_window_group[0]->fluid_time_desc.period == (TIMESTEP)period
      && some_win_clear_time_before_restart_time) {
    use_prior_min_period_offset = TRUE;
    min_period_offset = prior_min_period_offset;
  } else {
    use_prior_min_period_offset = FALSE;
    min_period_offset = TIMESTEP_MAX;
  }

  ccDOTIMES(i, cdi_meas_window_group.size()) {
    CDI_MEAS_WINDOW cdi_win = cdi_meas_window_group[i];
    TIME_DESC time_desc = &cdi_win->fluid_time_desc;
    if (cdi_win->average_interval < 0 // user intended period and avg to be sync'ed
        || avg_intervals[i] > period) {
      avg_intervals[i] = period;
    }
    // If the averaging interval of this window changed, then we restart it at cp_info.restart_time
    if (prior_min_period_offset >= 0
        && avg_intervals[i] != time_desc->interval) {
      if (cdi_win->start_time < cp_info.restart_time)
        cdi_win->start_time = cp_info.restart_time;
      cdi_win->min_start_time = cp_info.restart_time;
    }
    time_desc->interval = avg_intervals[i];
    time_desc->period = period;
    BOOLEAN round_period_down = cdi_win->mstp.standard_mask & CDI_MEAS_OPT_ROUND_PERIOD_DOWN;
    if (round_period_down) {
      dFLOAT p = n_user_base_steps * cdi_win->period;
      if (period > p) {
        dFLOAT p2 = period / n_user_base_steps;
        msg_warn("Unable to round period of measurement window \"%s\" down. Period set to"
                 " %g timesteps (%g seconds).", 
                 cdi_win->name, p2, p2 * cdi_data.seconds_per_timestep);
      }    
    }
    if (cdi_meas_window_group.size() > 1) {
      // align the frame centers for all meas windows in group
      dFLOAT s = n_user_base_steps * cdi_win->start_time;
      dFLOAT s2; // center of frame
      BOOLEAN no_shift_start_time = cdi_win->mstp.standard_mask & CDI_MEAS_OPT_DONT_SHIFT_START_TIME;
      dFLOAT half_avg = (dFLOAT)time_desc->interval / 2.0;     // either integer or 0.5
      if (no_shift_start_time) {
        // s is interpreted as the end of the first frame
        s2 = ct * floor(s / ct) - half_avg;     
      } else {
        // s is interpreted as the middle of the first frame
        s2 = ct * floor((s + half_avg) / ct) - half_avg; // either integer or X.5
      }
      dFLOAT min_start = n_user_base_steps * cdi_win->min_start_time;
      min_start = half_ct * ceil(min_start / half_ct); // round up to multiple of ct/2
      //if (s2 < half_avg)
      //  s2 = half_avg;
      if (s2 - half_avg < min_start)
        s2 = min_start + half_avg;
      // period_offset is a multiple of ct/2
      TIMESTEP period_offset = (TIMESTEP)s2 % (TIMESTEP)period;
      time_desc->repeat = period_offset; // stash period_offset in repeat slot
      if (!use_prior_min_period_offset)
        min_period_offset = MIN(min_period_offset, period_offset);
    }
  }

  ccDOTIMES(i, cdi_meas_window_group.size()) {
    CDI_MEAS_WINDOW cdi_win = cdi_meas_window_group[i];
    TIME_DESC time_desc = &cdi_win->fluid_time_desc;

    dFLOAT s = n_user_base_steps * cdi_win->start_time;
    dFLOAT e = MIN(n_user_base_steps * cdi_win->end_time, TIMESTEP_MAX);
    dFLOAT period_delta = 0;
    if (cdi_meas_window_group.size() > 1) {
      TIMESTEP period_offset = time_desc->repeat; // stashed above in repeat slot
      period_delta = period_offset - min_period_offset;
      if (period_delta < 0)
        period_delta += time_desc->period;
    }
    dFLOAT start;
    dFLOAT s2; // center of frame
    BOOLEAN no_shift_start_time = cdi_win->mstp.standard_mask & CDI_MEAS_OPT_DONT_SHIFT_START_TIME;
    dFLOAT half_avg = (dFLOAT)time_desc->interval / 2.0; // either integer or 0.5
    if (no_shift_start_time) {
      // s is interpreted as the end of the first frame
      s2 = ct * floor(s / ct) - half_avg;
    } else {
      // s is interpreted as the middle of the first frame
      s2 = ct * floor((s + half_avg) / ct) - half_avg;
    }
    dFLOAT min_start = n_user_base_steps * cdi_win->min_start_time;
    min_start = half_ct * ceil(min_start / half_ct); // round up to multiple of ct/2
    //if (s2 < half_avg)
    //  s2 = half_avg;
    if (s2 - half_avg < min_start)
      s2 = min_start + half_avg;
    s2 -= period_delta;
    if (s2 - half_avg < min_start)
      s2 += time_desc->period;
    start = s2 - half_avg; // start will be integral

    // if it is not the 1st phase in the coupling timestep table, do not change the start time
    if (cdi_win->is_not_first_coupling_phase) {
      time_desc->start = s;
      start = s; // need to use s for computing time_desc->repeat
    }
    else
      time_desc->start = start;

    if (cdi_win->num_frames > 0)
      time_desc->repeat = MIN(cdi_win->num_frames, TIMESTEP_MAX);
    else {
      e = MAX(e, start);
      dFLOAT end = period * floor((e - start) / period) + start;
      // The extra comparison in the following line is commented out to account for
      // the fact that the simulation end time might be extended. When the decomposer
      // and discretizer compute the number of frames, however, they include such a
      // comparison.
      if (end < e /* && (end + time_desc->interval) <= cp_info.end_time */ )
        end += period;
      
      dFLOAT repeat = ((end - start) / period);
      time_desc->repeat = MIN(repeat, TIMESTEP_MAX);
    }
  }

  return min_period_offset;
}


static TIMESTEP align_group_cond_meas_window_time_parameters(std::vector< CDI_MEAS_WINDOW > &cdi_meas_window_group,
                                                             TIMESTEP prior_min_period_offset) // -1 if to be ignored
{
  TIMESTEP max_n_conduction_base_steps = 1;

  dFLOAT  min_period            = TIMESTEP_MAX;
  dFLOAT  min_round_down_period = TIMESTEP_MAX;
  asINT32 coarsest_shob_scale   = FINEST_SCALE;
  dFLOAT  n_user_base_steps     = cp_info.n_user_base_steps;
  dFLOAT  n_solver_base_steps   = (dFLOAT)n_user_base_steps / (dFLOAT)cp_info.n_conduction_base_steps;
  // The averaging intervals of the windows in the group are independent, and thus calculated separately.
  // We also initially calculate the periods of the windows independently so that the round_period_down
  // flag can be handled independently. To see why, consider a pair of windows where the period must
  // be a multiple of 8 for each, one with user period 33 and round_period_down set, the other with user
  // period 31 and round_period_down unset. The desired period is 32.
  static std::vector<TIMESTEP> avg_intervals; // static so we don't have to allocate repeatedly
  avg_intervals.reserve(cdi_meas_window_group.size());
  avg_intervals.resize(0);
  ccDOTIMES(i, cdi_meas_window_group.size()) {
    CDI_MEAS_WINDOW cdi_win = cdi_meas_window_group[i];
    TIME_DESC time_desc = &cdi_win->cond_time_desc;
    TIMESTEP n_conduction_base_steps = 1;
    if (cdi_win->solver_mask & CONDUCTION_PDE_ACTIVE) n_conduction_base_steps = max_n_conduction_base_steps = cp_info.n_conduction_base_steps;
    //conduction only has one solver involved, so we already know that lcm_n_cond_tsteps = 1
    cdi_win->lcm_cond_tsteps = 1;
    dFLOAT solver_time_scale = (n_conduction_base_steps==1) ? n_user_base_steps : n_solver_base_steps;
    dFLOAT ct  = scale_to_delta_t(cdi_win->coarsest_cond_shob_scale); // coarsest timestep
    dFLOAT a   = solver_time_scale * cdi_win->average_interval;
    dFLOAT p   = solver_time_scale * cdi_win->period;
    dFLOAT avg = 0;
    if (a >= 0) {
      avg = MAX(ct * floor(a / ct + 0.5), ct);
      // ensure avg is even - may be odd in subcycling if ct is odd
      if (((int)avg & 1) && (ct != 1)) {
        if (avg >= a && avg != ct)
          avg -= ct;
        else
          avg += ct;
      }
    }
    avg_intervals.push_back(avg);
    BOOLEAN round_period_down = cdi_win->mstp.standard_mask & CDI_MEAS_OPT_ROUND_PERIOD_DOWN;
    dFLOAT period;
    if (round_period_down) {
      period = MAX(ct * floor(p / ct), ct);
      min_round_down_period = MIN(period, min_round_down_period);
    } else {
      // traditionally we have rounded up period to avg, but perhaps this is not what the user expects...
      if (a >= 0)
        period = MAX(ct * floor(p / ct + 0.5), avg);
      else
        period = MAX(ct * floor(p / ct + 0.5), ct);
    }
    // ensure period is even - may be odd in subcycling if ct is odd
    if (((int)period & 1) && ct != 1) {
      if (period >= p && period != ct)
        period -= ct;
      else
        period += ct;
    }
    min_period = MIN(period, min_period);
    coarsest_shob_scale = coarsest_scale_of_pair(cdi_win->coarsest_cond_shob_scale, coarsest_shob_scale);
  }

  dFLOAT period;
  dFLOAT ct;     // coarsest timestep of group
  if (cdi_meas_window_group.size() > 1) {
    ct = scale_to_delta_t(coarsest_shob_scale);
    period = MAX(ct * floor(min_period / ct + 0.5), ct);
    ct = scale_to_delta_t(coarsest_shob_scale);
    if (period > min_round_down_period)
      period = MAX(ct * floor(min_period / ct), ct);
    // ensure period is even - may be odd in subcycling if ct is odd
    if (((int)period & 1) && ct != 1) {
      if (period >= min_period && period != ct)
        period -= ct;
      else
        period += ct;
    }
  } else {
    ct = scale_to_delta_t(coarsest_shob_scale);
    period = min_period;
  }

  // If the period of the group changed, then we restart all windows at cp_info.restart_time
  if (prior_min_period_offset >= 0
      && cdi_meas_window_group[0]->cond_time_desc.period != (TIMESTEP)period) {
    ccDOTIMES(i, cdi_meas_window_group.size()) {
      CDI_MEAS_WINDOW cdi_win = cdi_meas_window_group[i];
      if (cdi_win->start_time < cp_info.restart_time)
        cdi_win->start_time = cp_info.restart_time;
      cdi_win->min_start_time = cp_info.restart_time;
    }
  }

  BOOLEAN some_win_clear_time_before_restart_time = FALSE;
  if (prior_min_period_offset >= 0 && cdi_meas_window_group.size() > 1) {
    ccDOTIMES(i, cdi_meas_window_group.size()) {
      CDI_MEAS_WINDOW cdi_win = cdi_meas_window_group[i];
      TIMESTEP prior_clear_time;
      asINT32 n_prior_frames;
      compute_next_time(0, cp_info.restart_base_time,
                        &cdi_win->cond_time_desc, &prior_clear_time, &n_prior_frames);
      if (prior_clear_time < cp_info.restart_base_time) {
        CP_MEAS_WINDOW cp_meas_win = cp_info.meas_windows[cdi_win->one_window];
        if (cp_meas_win) {
          STRING filename = cp_meas_win->compose_output_pathname();
          if (platform_file_present(filename)) {
            some_win_clear_time_before_restart_time = TRUE;
            break;
          }
        }
      }
    }
  }

  dFLOAT   half_ct = MAX(ct/2, 1.0); // integer
  TIMESTEP min_period_offset;
  BOOLEAN  use_prior_min_period_offset;
  // If the period of the group changed, prior_min_period_offset is irrelevant.
  // If all clear times are after cp_info.restart_time, prior_min_period_offset is irrelevant.
  if (prior_min_period_offset >= 0
      // Note that the periods of all windows in the group are identical
      && cdi_meas_window_group[0]->cond_time_desc.period == (TIMESTEP)period
      && some_win_clear_time_before_restart_time) {
    use_prior_min_period_offset = TRUE;
    min_period_offset = prior_min_period_offset;
  } else {
    use_prior_min_period_offset = FALSE;
    min_period_offset = TIMESTEP_MAX;
  }

  ccDOTIMES(i, cdi_meas_window_group.size()) {
    CDI_MEAS_WINDOW cdi_win = cdi_meas_window_group[i];
    TIME_DESC time_desc = &cdi_win->cond_time_desc;
    if (cdi_win->average_interval < 0 // user intended period and avg to be sync'ed
        || avg_intervals[i] > period) {
      avg_intervals[i] = period;
    }
    // If the averaging interval of this window changed, then we restart it at cp_info.restart_time
    if (prior_min_period_offset >= 0
        && avg_intervals[i] != time_desc->interval) {
      if (cdi_win->start_time < cp_info.restart_time)
        cdi_win->start_time = cp_info.restart_time;
      cdi_win->min_start_time = cp_info.restart_time;
    }
    time_desc->interval = avg_intervals[i];
    time_desc->period = period;
    BOOLEAN round_period_down = cdi_win->mstp.standard_mask & CDI_MEAS_OPT_ROUND_PERIOD_DOWN;
    if (round_period_down) {
      dFLOAT p = n_solver_base_steps * cdi_win->period;
      if (period > p) {
        dFLOAT p2 = period / n_solver_base_steps;
        msg_warn("Unable to round period of measurement window \"%s\" down. Period set to"
                 " %g timesteps (%g seconds).",
                 cdi_win->name, p2, p2 * cdi_data.seconds_per_timestep);
      }
    }
    if (cdi_meas_window_group.size() > 1) {
      // align the frame centers for all meas windows in group
      dFLOAT s = n_solver_base_steps * cdi_win->start_time;
      dFLOAT s2; // center of frame
      BOOLEAN no_shift_start_time = cdi_win->mstp.standard_mask & CDI_MEAS_OPT_DONT_SHIFT_START_TIME;
      dFLOAT half_avg = (dFLOAT)time_desc->interval / 2.0;     // either integer or 0.5
      if (no_shift_start_time) {
        // s is interpreted as the end of the first frame
        s2 = ct * floor(s / ct) - half_avg;
      } else {
        // s is interpreted as the middle of the first frame
        s2 = ct * floor((s + half_avg) / ct) - half_avg; // either integer or X.5
      }
      dFLOAT min_start = n_solver_base_steps * cdi_win->min_start_time;
      min_start = half_ct * ceil(min_start / half_ct); // round up to multiple of ct/2
      //if (s2 < half_avg)
      //  s2 = half_avg;
      if (s2 - half_avg < min_start)
        s2 = min_start + half_avg;
      // period_offset is a multiple of ct/2
      TIMESTEP period_offset = (TIMESTEP)s2 % (TIMESTEP)period;
      time_desc->repeat = period_offset; // stash period_offset in repeat slot
      if (!use_prior_min_period_offset)
        min_period_offset = MIN(min_period_offset, period_offset);
    }
  }

  ccDOTIMES(i, cdi_meas_window_group.size()) {
    CDI_MEAS_WINDOW cdi_win = cdi_meas_window_group[i];
    TIME_DESC time_desc = &cdi_win->cond_time_desc;

    dFLOAT s = n_solver_base_steps * cdi_win->start_time;
    dFLOAT e = MIN(n_solver_base_steps * cdi_win->end_time, TIMESTEP_MAX);
    dFLOAT period_delta = 0;
    if (cdi_meas_window_group.size() > 1) {
      TIMESTEP period_offset = time_desc->repeat; // stashed above in repeat slot
      period_delta = period_offset - min_period_offset;
      if (period_delta < 0)
        period_delta += time_desc->period;
    }
    dFLOAT start;
    dFLOAT s2; // center of frame
    BOOLEAN no_shift_start_time = cdi_win->mstp.standard_mask & CDI_MEAS_OPT_DONT_SHIFT_START_TIME;
    dFLOAT half_avg = (dFLOAT)time_desc->interval / 2.0; // either integer or 0.5
    if (no_shift_start_time) {
      // s is interpreted as the end of the first frame
      s2 = ct * floor(s / ct) - half_avg;
    } else {
      // s is interpreted as the middle of the first frame
      s2 = ct * floor((s + half_avg) / ct) - half_avg;
    }
    dFLOAT min_start = n_solver_base_steps * cdi_win->min_start_time;
    min_start = half_ct * ceil(min_start / half_ct); // round up to multiple of ct/2
    //if (s2 < half_avg)
    //  s2 = half_avg;
    if (s2 - half_avg < min_start)
      s2 = min_start + half_avg;
    s2 -= period_delta;
    if (s2 - half_avg < min_start)
      s2 += time_desc->period;
    start = s2 - half_avg; // start will be integral

    // if it is not the 1st phase in the coupling timestep table, do not change the start time
    if (cdi_win->is_not_first_coupling_phase) {
      time_desc->start = s;
      start = s; // need to use s for computing time_desc->repeat
    }

    if (cdi_win->num_frames > 0)
      time_desc->repeat = MIN(cdi_win->num_frames, TIMESTEP_MAX);
    else {
      e = MAX(e, start);
      dFLOAT end = period * floor((e - start) / period) + start;
      // The extra comparison in the following line is commented out to account for
      // the fact that the simulation end time might be extended. When the decomposer
      // and discretizer compute the number of frames, however, they include such a
      // comparison.
      if (end < e /* && (end + time_desc->interval) <= cp_info.end_time */ )
        end += period;

      dFLOAT repeat = ((end - start) / period);
      time_desc->repeat = MIN(repeat, TIMESTEP_MAX);
    }
  }

  return min_period_offset;
}

static BOOLEAN maybe_override_cdi_meas_win_timing_params(CDI_MEAS_WINDOW cdi_meas_win,
                                                         BOOLEAN end_time_only,
                                                         BOOLEAN &start_time_changed)
{
  BOOLEAN something_changed = FALSE;
  start_time_changed = FALSE;

  MEAS_TIMING_OPTION meas_timing_option = cp_info.cmdline_meas_timing_options;
  while (meas_timing_option != NULL) {
    if (strcmp(meas_timing_option->meas_window_name, cdi_meas_win->name) == 0) {
      // end_time has no impact on sequence of frames
      if (end_time_only) {
        if (meas_timing_option->end_time >= 0
            && meas_timing_option->end_time != cdi_meas_win->end_time) {
          something_changed = TRUE;
          cdi_meas_win->overridden_by_meas_timing = TRUE;
          cdi_meas_win->end_time = meas_timing_option->end_time;
        }
      } else {
        if (meas_timing_option->start_time >= 0
            && meas_timing_option->start_time != cdi_meas_win->start_time) {
          start_time_changed = TRUE;
          something_changed = TRUE;
          cdi_meas_win->overridden_by_meas_timing = TRUE;
          cdi_meas_win->start_time = meas_timing_option->start_time;
          cdi_meas_win->min_start_time = cp_info.restart_time;
        }
        if (meas_timing_option->end_time >= 0
            && meas_timing_option->end_time != cdi_meas_win->end_time) {
          something_changed = TRUE;
          cdi_meas_win->overridden_by_meas_timing = TRUE;
          cdi_meas_win->end_time = meas_timing_option->end_time;
        }
        if (meas_timing_option->period > 0
            && meas_timing_option->period != cdi_meas_win->period) {
          something_changed = TRUE;
          cdi_meas_win->overridden_by_meas_timing = TRUE;
          cdi_meas_win->period = meas_timing_option->period;
        }
        if (meas_timing_option->average_interval > 0
            && meas_timing_option->average_interval != cdi_meas_win->average_interval) {
          something_changed = TRUE;
          cdi_meas_win->overridden_by_meas_timing = TRUE;
          cdi_meas_win->average_interval = meas_timing_option->average_interval;
        }
      }
      break; // last option on command line is first in list so break is required
    }
    meas_timing_option = meas_timing_option->next;
  }
  
  return something_changed;
}

VOID align_meas_window_time_parameters()
{
  // If a fresh simulation from t=0, override all timing parameters as dictated by --meas_timing 
  // command line options. If restarting from checkpoint, defer overriding start_time, period, 
  // and averaging interval so that (a) we can issue appropriate warnings about changes relative to
  // existing meas files, and (b) we can try not to shift (in time) the frames of the windows of a 
  // period sync group (when the period of the group is not changed).

  if (cp_info.cmdline_meas_timing_options) {
    // end_time and average_interval have no impact on sequence of frames
    BOOLEAN end_time_only = cp_info.restart_time > 0;
    BOOLEAN start_time_changed;
    DO_CDI_MEAS_WINDOWS(cdi_meas_win) {
      if (!cdi_meas_win->is_average_mme)
        maybe_override_cdi_meas_win_timing_params(cdi_meas_win, end_time_only, start_time_changed);
    }
  }

  // This is a vector of vectors of CDI meas windows
  std::vector< std::vector< CDI_MEAS_WINDOW > > period_sync_groups;
  // Build the period sync groups
  DO_CDI_MEAS_WINDOWS(cdi_meas_window) {
    if (cdi_meas_window->period_sync_group_index >= 0
        && cdi_meas_window->solver_mask != 0
        // start_time set to TIMESTEP_MAX if window excluded via --meas_exclude or --meas_include
        && cdi_meas_window->start_time != TIMESTEP_MAX) { 
      if (cdi_meas_window->period_sync_group_index >= period_sync_groups.size())        
        period_sync_groups.resize(cdi_meas_window->period_sync_group_index + 1);
      period_sync_groups[cdi_meas_window->period_sync_group_index].push_back(cdi_meas_window);
    }
  }

  // Compute the timing parameters for the windows in each sync group
  ccDOTIMES(i, period_sync_groups.size()) {
    std::vector< CDI_MEAS_WINDOW > &period_sync_group = period_sync_groups[i];
    TIMESTEP min_period_offset = align_group_flow_meas_window_time_parameters(period_sync_group, -1);
    if (cp_info.restart_time > 0 && cp_info.cmdline_meas_timing_options) {
      BOOLEAN all_start_times_changed = TRUE;
      BOOLEAN some_win_changed = FALSE;
      ccDOTIMES(w, period_sync_group.size()) {
        BOOLEAN start_time_changed;
        BOOLEAN something_changed = maybe_override_cdi_meas_win_timing_params(period_sync_group[w], 
                                                                              FALSE, start_time_changed);
        if (!start_time_changed)
          all_start_times_changed = FALSE;
        if (something_changed)
          some_win_changed = TRUE;
      }
      if (some_win_changed) {
        if (all_start_times_changed)
          min_period_offset = -1; // we are free to start from scratch for all windows
        // Make a copy of each window's time_desc to use in warnings later
        ccDOTIMES(w, period_sync_group.size()) {
          period_sync_group[w]->superseded_time_desc = period_sync_group[w]->fluid_time_desc;
          period_sync_group[w]->is_time_desc_superseded = TRUE;
        }

        // Recompute the window's timing parameters
        align_group_flow_meas_window_time_parameters(period_sync_group, min_period_offset);
        align_group_cond_meas_window_time_parameters(period_sync_group, min_period_offset);
      }
    }
  }

  // Compute the timing parameters for windows not in a sync group
  std::vector< CDI_MEAS_WINDOW > cdi_meas_windows(1);
  DO_CDI_MEAS_WINDOWS(cdi_meas_window) {

    // Average mme window time parameters are treated separately
    if (cdi_meas_window->is_average_mme) { 

      if (cdi_meas_window->disabled_by_meas_include_exclude) {
        cdi_meas_window->fluid_time_desc.start = TIMESTEP_MAX;
        cdi_meas_window->fluid_time_desc.period = cdi_meas_window->period;
        cdi_meas_window->fluid_time_desc.repeat = -1;
      } else { 
        align_avg_mme_ckpt_time_parameters(&cdi_meas_window->fluid_time_desc, 
                                           cdi_meas_window->start_time, 
                                           cdi_meas_window->end_time, 
                                           cdi_meas_window->average_interval, 
                                           cdi_meas_window->period);
        if (cdi_meas_window->start_time_via_monitors_p) {
          cdi_meas_window->fluid_time_desc.start = 0;
          cdi_meas_window->fluid_time_desc.repeat = -1;
          cdi_meas_window->start_time = 0;
          cdi_meas_window->end_time = cdi_meas_window->duration;
        }
      }
      continue;
    }

    if ((cdi_meas_window->period_sync_group_index < 0
         // start_time set to TIMESTEP_MAX if window excluded via --meas_exclude or --meas_include
         || cdi_meas_window->start_time == TIMESTEP_MAX)
        && cdi_meas_window->solver_mask != 0) {  
      cdi_meas_windows[0] = cdi_meas_window;
      align_group_flow_meas_window_time_parameters(cdi_meas_windows, -1);
      align_group_cond_meas_window_time_parameters(cdi_meas_windows, -1);
      if (cp_info.restart_time > 0 && cp_info.cmdline_meas_timing_options) {
        BOOLEAN start_time_changed;
        BOOLEAN something_changed = maybe_override_cdi_meas_win_timing_params(cdi_meas_window, 
                                                                              FALSE, start_time_changed);
        if (something_changed) {
          cdi_meas_window->superseded_time_desc = cdi_meas_window->fluid_time_desc;
          cdi_meas_window->is_time_desc_superseded = TRUE;
          // For a one window group, the min_period_offset arg of 0 is just a flag to indicate that
          // this is the second call to this fcn.
          align_group_flow_meas_window_time_parameters(cdi_meas_windows, 0);
          align_group_cond_meas_window_time_parameters(cdi_meas_windows, 0);
        }
      }
    }
  }
}

VOID sCDI_MEAS_WINDOW::find_meas_window_end_time_via_monitors(TIMESTEP end_init_transient)
{
  if (meas_end_time_via == eCDI_MEAS_END_TIME_VIA::EndTime) {
    if (is_average_mme) {
      TIMESTEP time_to_stop = MAX(end_time, end_init_transient + period);
      if (time_to_stop != TIMESTEP_MAX) {
        cp_info.request_to_stop_avg_mme = TRUE;
        cp_info.requested_time_to_stop_avg_mme = MAX(end_time, end_init_transient + period);
      }
    }
    return;
  }
  end_time = (dFLOAT)end_init_transient + (dFLOAT)duration; // Avoid overflow!
  // end time is dFLOAT
#if DEBUG_MONITOR
  msg_print("Change cdi meas window end time to %f", end_time);
#endif    

  // If the end time of average mme window is found, should request to stop avg mme at the end time.
  if (is_average_mme) {
    TIMESTEP time_to_stop = MIN(end_time, TIMESTEP_MAX);
    if (time_to_stop != TIMESTEP_MAX) {
      cp_info.request_to_stop_avg_mme = TRUE;
      cp_info.requested_time_to_stop_avg_mme = MIN(end_time, TIMESTEP_MAX);
    }
  }

  // Possibly stop the simulation at greatest meas window end time
  if (cp_info.sim_duration_via == eCDI_SIM_DURATION_VIA::GreatestMeasurementEndTime) {
    cp_info.greatest_meas_window_end_time = MAX(cp_info.greatest_meas_window_end_time,
                                                end_time);
    if (--cp_info.n_meas_windows_to_check_end_time == 0) {
      msg_print("Find greatest meas window end time %d", cp_info.greatest_meas_window_end_time);
      if ((TIMESTEP)cp_info.end_time == TIMESTEP_MAX) { // If current end time is set by user (either from exaqsub or from exasignal) 
                                            // do not request to stop. See PR41559.
        cp_info.request_to_exit_by_greatest_meas_window_end_time = TRUE;
      }
    }
  }
}

BOOLEAN sCDI_MEAS_WINDOW::mated_flow_conduction_surface_files() {
	return m_mated_flow_conduction_surface_files;
}

// Rename meas files by removing the .tmp, e.g. changing "test.fnc.tmp" to "test.fnc".
VOID sCDI_MEAS_WINDOW::rename_tmp_meas_files()
{
  // Already started, no need to proceed. This may happen if restarting from a ckpt
  if (meas_started_p || disabled_by_meas_include_exclude)
    return;

  meas_started_p = TRUE;

  if (fluid_meas_window_index >= 0)
    cp_info.meas_windows[fluid_meas_window_index]->rename_tmp_meas_file();
  if (surface_meas_window_index >= 0)
    cp_info.meas_windows[surface_meas_window_index]->rename_tmp_meas_file();
  if (porous_meas_window_index >= 0)
    cp_info.meas_windows[porous_meas_window_index]->rename_tmp_meas_file();

}

VOID sCDI_MEAS_WINDOW::start_meas_windows(TIMESTEP end_init_transient_detected_time,
                                          TIMESTEP end_init_transient)
{ 
  // Already started, no need to proceed. This may happen if restarting from a ckpt
  if (meas_started_p || disabled_by_meas_include_exclude)
    return;
 
  meas_started_p = TRUE;

  if (is_average_mme) {
    cp_info.avg_mme_ckpt_after_eit_p = TRUE;

    // If avg mme meas window is requested to start or scheduled to start at ckpt time, 
    // do nothing here since these are handled when reading in ckpt data.
    if (cp_info.is_full_checkpoint_restore && cp_info.time == cp_info.restart_time) {
      if (cp_info.request_to_stop_avg_mme || cp_info.time_to_stop_avg_mme < TIMESTEP_MAX || cp_info.average_mme_stopped)
        return;
    }

    // If some monitors have user specified EIT which is later, then should use that
    // time as the real end_init_transient
    if (end_init_transient < fluid_time_desc.start) {
      msg_print("some monitors have user specified EIT %d which is later, so use that instead", fluid_time_desc.start);
      end_init_transient = fluid_time_desc.start;
    }

    msg_print("Start avg mme meas window with EIT %d", end_init_transient);

    // Figure out the current average mme ckpt interval for the ckpt file on disk
    // Find the real end time
    asINT32  current_avg_mme_ckpt_file_index = floor(cp_info.time / period) - 1; 
    TIMESTEP current_avg_mme_ckpt_file_start = current_avg_mme_ckpt_file_index * period;
    TIMESTEP current_avg_mme_ckpt_file_end   = current_avg_mme_ckpt_file_start + period;
#if DEBUG_AVG_MME
    msg_print("current avg mme ckpt file: index %d start %d output %d", current_avg_mme_ckpt_file_index, current_avg_mme_ckpt_file_start,
              current_avg_mme_ckpt_file_end);
#endif

    //First ckpt file after the end of init transient
    asINT32  first_avg_mme_ckpt_index = end_init_transient / period;
    TIMESTEP first_avg_mme_ckpt_start = first_avg_mme_ckpt_index * period;
    TIMESTEP first_avg_mme_ckpt_end   = first_avg_mme_ckpt_start + period;

    if (current_avg_mme_ckpt_file_index < 0 || end_init_transient > current_avg_mme_ckpt_file_start) {
      // current avg mme ckpt file not usable
      if (end_init_transient < current_avg_mme_ckpt_file_end) { // The next avg mme ckpt file to be written is usable
        TIMESTEP real_avg_mme_ckpt_end = current_avg_mme_ckpt_file_end + duration;
#if DEBUG_AVG_MME
        msg_print("Next ckpt file to be written is OK with clear time %d.", current_avg_mme_ckpt_file_end);
#endif
        find_meas_window_end_time_via_monitors(current_avg_mme_ckpt_file_end);
      } else { // Need to reschedule avg mme ckpt start and period
#if DEBUG_AVG_MME
        msg_print("Reschedule avg mme ckpt start to earliest time possible with duration %d", duration);
#endif
        cp_info.request_to_reschedule_avg_mme = TRUE;
        cp_info.requested_time_to_reschedule_avg_mme = cp_info.time;
        cp_info.avg_mme_duration = duration;
        // Actual avg mme start and end time unknown until CP finishes negotiating with SPs.
      }    
    } else { // Can use the current file 
#if DEBUG_AVG_MME
      msg_print("Current ckpt file is OK.");
#endif
      // Rename the avg mme ckpt filename to regular name
      platform_rename_file(cp_info.avg_mme_ckpt_preit_filename.c_str(), cp_info.avg_mme_ckpt_filename.c_str());

      find_meas_window_end_time_via_monitors(first_avg_mme_ckpt_start);
    }
  } 
  else {
    // It is possible that the user specified end of initial transient of some other 
    // controlling monitors is later, so the meas window should not really start now.
    // Since all controlling monitors have reached the end of initial transient, this 
    // meas window now looks like a normal meas window.
    
    // end_init_transient is calculated by monitors with end of initial transient set 
    // to "Automatic". It may not be the real end init transient time of the meas window 
    // since those monitors with end init transient set to "User Specified" may reach 
    // the end of init transient later
    if (end_init_transient_detected_time < fluid_time_desc.start) {
      // In this case time_desc->start is the real meas window end init transient;
      find_meas_window_end_time_via_monitors(fluid_time_desc.start);
      fluid_time_desc.repeat = (meas_end_time_via == eCDI_MEAS_END_TIME_VIA::Duration) ? 
                          (duration - 1)/fluid_time_desc.period + 1 : 
                          (end_time - fluid_time_desc.start - 1)/fluid_time_desc.period + 1;
      start_time_via_monitors_p = FALSE;
      // meas window will start later, just renames output files since start time no longer depends on monitors
      if (fluid_meas_window_index >= 0)
        cp_info.meas_windows[fluid_meas_window_index]->rename_tmp_meas_file();
      if (surface_meas_window_index >= 0)
        cp_info.meas_windows[surface_meas_window_index]->rename_tmp_meas_file();
      if (porous_meas_window_index >= 0)
        cp_info.meas_windows[porous_meas_window_index]->rename_tmp_meas_file();
    } 
    else {
      find_meas_window_end_time_via_monitors(end_init_transient);
      if (fluid_meas_window_index >= 0)
        cp_info.meas_windows[fluid_meas_window_index]->start_meas_window(end_init_transient_detected_time,
                                                                         end_init_transient);
      if (surface_meas_window_index >= 0)
        cp_info.meas_windows[surface_meas_window_index]->start_meas_window(end_init_transient_detected_time,
                                                                           end_init_transient);
      if (porous_meas_window_index >= 0)
        cp_info.meas_windows[porous_meas_window_index]->start_meas_window(end_init_transient_detected_time,
                                                                          end_init_transient);
    }  
  }
}

VOID sCDI_MEAS_WINDOW::compute_rounded_time_desc() // only called for coupling windows
{
  std::vector< CDI_MEAS_WINDOW > cdi_meas_windows(1);
  cdi_meas_windows[0] = this;
  align_group_flow_meas_window_time_parameters(cdi_meas_windows, -1);
  align_group_cond_meas_window_time_parameters(cdi_meas_windows, -1);
}

dFLOAT sCDI_MEAS_WINDOW::start_time_in_cond_ts() {
  dFLOAT start_time = this->start_time;
  if(this->start_time_defined_by == eCDI_COUPLED_SOLVER::FlowSolver)
    start_time = this->start_time/cp_info.n_conduction_base_steps;
  return start_time;
}

dFLOAT sCDI_MEAS_WINDOW::end_time_in_cond_ts() {
  dFLOAT end_time = this->end_time;
  if(this->start_time_defined_by == eCDI_COUPLED_SOLVER::FlowSolver)
    end_time = this->end_time/cp_info.n_conduction_base_steps;
  return end_time;
}

dFLOAT sCDI_MEAS_WINDOW::avg_int_in_cond_ts() {
  dFLOAT average_interval = this->average_interval;
  if(this->start_time_defined_by == eCDI_COUPLED_SOLVER::FlowSolver)
    average_interval = this->average_interval/cp_info.n_conduction_base_steps;
  return average_interval;
}

dFLOAT sCDI_MEAS_WINDOW::period_in_cond_ts() {
  dFLOAT period = this->period;
  if(this->start_time_defined_by == eCDI_COUPLED_SOLVER::FlowSolver)
    period = this->period/cp_info.n_conduction_base_steps;
  return period;
}

dFLOAT sCDI_MEAS_WINDOW::start_time_in_flow_ts() {
  dFLOAT start_time = this->start_time;
  if(this->start_time_defined_by == eCDI_COUPLED_SOLVER::ConductionSolver)
    start_time = this->start_time * cp_info.n_conduction_base_steps/cp_info.n_lb_base_steps;
  return start_time;
}

dFLOAT sCDI_MEAS_WINDOW::end_time_in_flow_ts() {
  dFLOAT end_time = this->end_time;
  if(this->start_time_defined_by == eCDI_COUPLED_SOLVER::ConductionSolver)
    end_time = this->end_time * cp_info.n_conduction_base_steps/cp_info.n_lb_base_steps;
  return end_time;
}

dFLOAT sCDI_MEAS_WINDOW::avg_int_in_flow_ts() {
  dFLOAT average_interval = this->average_interval;
  if(this->start_time_defined_by == eCDI_COUPLED_SOLVER::ConductionSolver)
    average_interval = this->average_interval * cp_info.n_conduction_base_steps/cp_info.n_lb_base_steps;
  return average_interval;
}

dFLOAT sCDI_MEAS_WINDOW::period_in_flow_ts() {
  dFLOAT period = this->period;
  if(this->start_time_defined_by == eCDI_COUPLED_SOLVER::ConductionSolver)
    period = this->period * cp_info.n_conduction_base_steps/cp_info.n_lb_base_steps;
  return period;
}


VOID align_avg_mme_ckpt_time_parameters(TIME_DESC time_desc, TIMESTEP _start, TIMESTEP _end, TIMESTEP _interval, TIMESTEP _period)
{
  dFLOAT    n_user_base_steps  = cp_info.n_user_base_steps;
  TIMESTEP  lcm_n_solver_steps = lcm(cp_info.n_lb_base_steps,
                                     cp_info.n_t_base_steps,
                                     cp_info.n_ke_base_steps,
                                     cp_info.n_uds_base_steps,
                                     cp_info.n_conduction_base_steps);
  dFLOAT    ct                 = lcm_n_solver_steps * scale_to_delta_t(COARSEST_SCALE); // coarsest timestep
  
  dFLOAT p = n_user_base_steps * _period;
  dFLOAT it = n_user_base_steps * _interval;

  // round period and interval to nearest multiple of ct
  dFLOAT period = MAX(ct * floor(p / ct + 0.5), ct);
  dFLOAT interval = MAX(ct * floor(it / ct + 0.5), ct);
  // ensure period and interval are even - may be odd in subcycling if ct is odd
  if (((int)period & 1) && ct != 1) {
    if (period >= p && period != ct)
      period -= ct;
    else
      period += ct;
  }
  if (((int)interval & 1) && ct != 1) {
    if (interval >= it && interval != ct)
      interval -= ct;
    else
      interval += ct;
  }


  dFLOAT s  = n_user_base_steps * _start;
  dFLOAT e  = MIN(n_user_base_steps * _end, TIMESTEP_MAX);
  dFLOAT s2 = ct * ceil(s / ct); // round up
  // s2 is the first clear time, the first output time should be s2+interval
  dFLOAT s3 = s2 + interval;
  dFLOAT e2 = period * ceil((e - s3) / period) + s3;
  dFLOAT start = MAX(s3 - interval, 0);   // start is the start of the first interval (clear time)
  e2 = MAX(e2, s3);
  e2 = MIN(e2, TIMESTEP_MAX);
  dFLOAT end = (TIMESTEP)(e2 + 0.6); // round up
  dFLOAT repeat = ((end - start - interval) / period) + 1;
  time_desc->repeat = MIN(repeat, TIMESTEP_MAX);

  // ckpt time is in user's timestep, ckpt will be made at synchronous timesteps
  time_desc->start    = start / n_user_base_steps;
  time_desc->period   = period / n_user_base_steps;
  time_desc->interval = interval / n_user_base_steps;
}

VOID align_ckpt_time_parameters(TIMESTEP _start, TIMESTEP _end, TIMESTEP _period)
{
  dFLOAT    n_user_base_steps  = cp_info.n_user_base_steps;
  TIMESTEP  lcm_n_solver_steps = lcm(cp_info.n_lb_base_steps,
                                     cp_info.n_t_base_steps,
                                     cp_info.n_ke_base_steps,
                                     cp_info.n_uds_base_steps,
                                     cp_info.n_conduction_base_steps);
  dFLOAT    ct                 = lcm_n_solver_steps * scale_to_delta_t(COARSEST_SCALE); // coarsest timestep
  TIME_DESC time_desc          = &cp_info.ckpt_time_desc;
  
  dFLOAT p = n_user_base_steps * _period;

  // round period to nearest multiple of ct
  dFLOAT period = MAX(ct * floor(p / ct + 0.5), ct);
  // ensure period is even - may be odd in subcycling if ct is odd
  if (((int)period & 1) && ct != 1) {
    if (period >= p && period != ct)
      period -= ct;
    else
      period += ct;
  }

  dFLOAT s  = n_user_base_steps * _start;
  dFLOAT e  = MIN(n_user_base_steps * _end, TIMESTEP_MAX);
  dFLOAT s2 = ct * floor(s / ct);
  dFLOAT e2 = period * ceil((e - s2) / period) + s2;
  dFLOAT start = MAX(s2 - period, 0);
  e2 = MAX(e2, s2);
  e2 = MIN(e2, TIMESTEP_MAX);
  dFLOAT end = (TIMESTEP)(e2 + 0.6); // round up
  dFLOAT repeat = ((end - start) / period) + 1;
  time_desc->repeat = MIN(repeat, TIMESTEP_MAX);

  // ckpt time is in user's timestep, ckpt will be made at synchronous timesteps
  time_desc->start    = start / n_user_base_steps;
  time_desc->period   = period / n_user_base_steps;
  time_desc->interval = time_desc->period;
}

CP_MEAS_WINDOW sCP_MEAS_WINDOW_COLLECTION::add_meas_window(cDGF_MEAS_WINDOW *dgf_window, BOOLEAN is_coupling)
{
    sCP_MEAS_WINDOW *window;
    BOOLEAN is_composite = dgf_window->meas_window_flags & DGF_MEAS_WINDOW_IS_COMPOSITE;
    BOOLEAN is_development = dgf_window->meas_window_flags & DGF_MEAS_WINDOW_IS_DEVELOPMENT;

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    BOOLEAN is_trajectory = cp_info.cdi_meas_windows[dgf_window->cdi_meas_window_index].m_is_particle_trajectory_window;
//#endif

    if (dgf_window->cdi_meas_window_index >= cp_info.n_cdi_meas_windows)
      msg_internal_error("CDI meas window index %d from LGI file exceeds number"
                         " of meas windows present in CDI file %d.", dgf_window->cdi_meas_window_index, cp_info.n_cdi_meas_windows);

    CDI_MEAS_WINDOW cdi_meas_window    = &cp_info.cdi_meas_windows[ dgf_window->cdi_meas_window_index ];
    BOOLEAN is_meas_vars_output_dp = (cdi_meas_window->mstp.standard_mask &
                                          CDI_MEAS_OPT_DOUBLE_PRECISION) !=0;

    // -double_precision_meas option filled in cp_info.is_sim_double_precision
    if (cp_info.is_sim_double_precision && cp_info.is_double_precision_meas_forced && !is_coupling) {
      is_meas_vars_output_dp = TRUE;
    } else {
      // Output should be single precision for a single precision simulation
      if (!cp_info.is_sim_double_precision && is_meas_vars_output_dp) {
        static bool issued_warning = false;
        if (!issued_warning) {
          msg_warn("All measurement file variables will be single precision irrespective of"
                   " the precision specified in PowerCASE.");
          issued_warning = true;
        }
        is_meas_vars_output_dp = FALSE;
      }
    }

    if (dgf_window->num_meas_cells == 0) {
      window = NULL;
    }
    else {
      switch (dgf_window->meas_type) {
      case LGI_AVERAGE_MME_WINDOW:
        window = xnew sCP_FLUID_MEAS_WINDOW_SFLOAT(dgf_window,
                                                   m_meas_windows.size(),
                                                   is_meas_vars_output_dp);
        break;
      case LGI_FLUID_WINDOW:
      case LGI_VOLUME_WINDOW:
      case LGI_POROUS_WINDOW:
        if (cp_info.dsm_reader && cp_info.dsm_reader->is_meas_window_set() &&
            (strcmp(cdi_meas_window->name, cp_info.dsm_reader->get_meas_window_name()) == 0)) {
          // window name is mapped into an index
          cp_info.dsm_reader->set_meas_window_index( m_meas_windows.size() );
          if (is_composite || is_development) {
            msg_internal_error("Meas window DSM calculation cannot be composite or development");
          }
        }
        if (is_meas_vars_output_dp) {
          if (is_composite) {
            window = xnew sCP_COMPOSITE_FLUID_MEAS_WINDOW_DFLOAT(dgf_window,
                                                                 m_meas_windows.size(),
                                                                 is_meas_vars_output_dp);
          }
          else if(is_development) {
            window = xnew sCP_FLUID_DEV_MEAS_WINDOW_DFLOAT(dgf_window,
                                                           m_meas_windows.size(),
                                                           is_meas_vars_output_dp);
          }
          else {
            window = xnew sCP_FLUID_MEAS_WINDOW_DFLOAT(dgf_window,
                                                       m_meas_windows.size(),
                                                       is_meas_vars_output_dp);
          }
        } else {
          if (is_composite) {
            window = xnew sCP_COMPOSITE_FLUID_MEAS_WINDOW_SFLOAT(dgf_window,
                                                                 m_meas_windows.size(),
                                                                 is_meas_vars_output_dp);
          }
          else if(is_development) {
            window = xnew sCP_FLUID_DEV_MEAS_WINDOW_SFLOAT(dgf_window,
                                                           m_meas_windows.size(),
                                                           is_meas_vars_output_dp);
          }
          else {
            window = xnew sCP_FLUID_MEAS_WINDOW_SFLOAT(dgf_window,
                                                       m_meas_windows.size(),
                                                       is_meas_vars_output_dp);
          }
        }
        break;
      case LGI_SAMPLING_SURFACE_WINDOW:
      case LGI_SURFACE_WINDOW:
        if (is_meas_vars_output_dp) {
          if (is_composite) {
            window = xnew sCP_COMPOSITE_SURFACE_MEAS_WINDOW_DFLOAT(dgf_window, m_meas_windows.size(),
                                                                   is_meas_vars_output_dp);
          }
          else if (is_development) {
            window = xnew sCP_SURFACE_DEV_MEAS_WINDOW_DFLOAT(dgf_window, m_meas_windows.size(),
                                                             is_meas_vars_output_dp);
          }
          else {
            window = xnew sCP_SURFACE_MEAS_WINDOW_DFLOAT(dgf_window, m_meas_windows.size(),
                                                         is_meas_vars_output_dp);
            some_surface_meas_window = TRUE;
          }
        } else {
          if (is_composite) {
            window = xnew sCP_COMPOSITE_SURFACE_MEAS_WINDOW_SFLOAT(dgf_window, m_meas_windows.size(),
                                                                   is_meas_vars_output_dp);
          }
          else if (is_development) {
            window = xnew sCP_SURFACE_DEV_MEAS_WINDOW_SFLOAT(dgf_window, m_meas_windows.size(),
                                                             is_meas_vars_output_dp);
          }
          else {
            window = xnew sCP_SURFACE_MEAS_WINDOW_SFLOAT(dgf_window, m_meas_windows.size(),
                                                         is_meas_vars_output_dp);
            some_surface_meas_window = TRUE;
          }
        }
        break;
      case LGI_SAMPLING_SHELL_WINDOW:
      case LGI_SHELL_WINDOW:
        if (is_meas_vars_output_dp) {
          if (is_composite) {
            window = xnew sCP_COMPOSITE_SURFACE_MEAS_WINDOW_DFLOAT(dgf_window, m_meas_windows.size(),
                                                                   is_meas_vars_output_dp);
          }
          else if (is_development) {
            window = xnew sCP_SURFACE_DEV_MEAS_WINDOW_DFLOAT(dgf_window, m_meas_windows.size(),
                                                             is_meas_vars_output_dp);
          }
          else {
            window = xnew sCP_SURFACE_MEAS_WINDOW_DFLOAT(dgf_window, m_meas_windows.size(),
                                                         is_meas_vars_output_dp);
            some_surface_meas_window = TRUE;
          }
        } else {
          if (is_composite) {
            window = xnew sCP_COMPOSITE_SURFACE_MEAS_WINDOW_SFLOAT(dgf_window, m_meas_windows.size(),
                                                                   is_meas_vars_output_dp);
          }
          else if (is_development) {
            window = xnew sCP_SURFACE_DEV_MEAS_WINDOW_SFLOAT(dgf_window, m_meas_windows.size(),
                                                             is_meas_vars_output_dp);
          }
          else {
            window = xnew sCP_SURFACE_MEAS_WINDOW_SFLOAT(dgf_window, m_meas_windows.size(),
                                                         is_meas_vars_output_dp);
            some_surface_meas_window = TRUE;
          }
        }
        break;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    case LGI_TRAJECTORY_WINDOW:
      if (is_meas_vars_output_dp) {
        window = xnew sCP_TRAJECTORY_WINDOW_DFLOAT(dgf_window, m_meas_windows.size(), is_meas_vars_output_dp,
                                                   cp_info.cdi_meas_windows[dgf_window->cdi_meas_window_index].name);
      } else {
        window = xnew sCP_TRAJECTORY_WINDOW_SFLOAT(dgf_window, m_meas_windows.size(), is_meas_vars_output_dp,
                                                   cp_info.cdi_meas_windows[dgf_window->cdi_meas_window_index].name);
      }
      break;
      default:
        msg_internal_error("Unrecognized DGF meas window type");
      }
    }

    m_meas_windows.push_back(window);
    return m_meas_windows.back();
}

VOID sCP_MEAS_WINDOW::append_var_type(SRI_VARIABLE_TYPE var_type, sINT16 population_var_index, sINT16 var_component_id) {
  //A pedantic way of appending a new variable type to the set of
  //measurement variables of a CDI_MEAS_WINDOW which maintains the
  //extra stuff used for particle modeling if it exists.
  
  //Allocate the set of data needed for the new set of variables
  SRI_VARIABLE_TYPE *new_var_types = new SRI_VARIABLE_TYPE[n_variables + 1];
  sINT16 *new_population_var_indices = new sINT16[n_variables + 1];
  sINT16 *new_var_component_ids = new sINT16[n_variables + 1];
  
  //Copy everything needed from the old set of variables.
  ccDOTIMES(i, n_variables) {
    new_var_types[i] = this->var_types[i];

    if (population_var_indices != NULL) 
      new_population_var_indices[i] = this->population_var_indices[i];
    else
      new_population_var_indices[i] = -1;

    if(m_var_component_ids != NULL)
      new_var_component_ids[i] = this->m_var_component_ids[i];
    else
      new_var_component_ids[i] = -1;
  }

  //Put the data for the new varaible at the end.
  new_var_types[n_variables] = var_type;
  new_population_var_indices[n_variables] = population_var_index;
  new_var_component_ids[n_variables] = var_component_id;
 
  //Delete the data from the obsolete set of variables.
  delete [] var_types;
  if(population_var_indices != NULL)
    delete [] population_var_indices;
  if(m_var_component_ids != NULL)
    delete [] m_var_component_ids;
  
  //Update this window with the data for the new set of variables.
  this->n_variables++;
  this->var_types = new_var_types;
  this->population_var_indices = new_population_var_indices;
  this->m_var_component_ids = new_var_component_ids;

  //Recompute the number of bytes per frame.
  this->compute_bytes_per_frame();
}



template struct tCP_REDUCTION_MEAS_WINDOW<sriFLOAT>;
template struct tCP_REDUCTION_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_NO_REDUCTION_MEAS_WINDOW<sriFLOAT>;
template struct tCP_NO_REDUCTION_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_FLUID_MEAS_WINDOW<sriFLOAT>;
template struct tCP_FLUID_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_SURFACE_MEAS_WINDOW<sriFLOAT>;
template struct tCP_SURFACE_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_COMPOSITE_FLUID_MEAS_WINDOW<sriFLOAT>;
template struct tCP_COMPOSITE_FLUID_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_COMPOSITE_SURFACE_MEAS_WINDOW<sriFLOAT>;
template struct tCP_COMPOSITE_SURFACE_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_FLUID_DEV_MEAS_WINDOW<sriFLOAT>;
template struct tCP_SURFACE_DEV_MEAS_WINDOW<sriDOUBLE>;
template struct tCP_SURFACE_DEV_MEAS_WINDOW<sriFLOAT>;
template struct tCP_FLUID_DEV_MEAS_WINDOW<sriDOUBLE>;
