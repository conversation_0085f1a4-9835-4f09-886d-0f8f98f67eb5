/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 *
 * Write checkpoint files
 *
 *--------------------------------------------------------------------------*/

#include "common.h"
#include LGI_H
#include "window.h"
#include "full_ckpt.h"
#include "cp_lattice.h"
#include "cp_info.h"
#include "trajectory_results.h"
#include "checkpoint_control.h"

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
#include "particle_sim_cp.h"
//extern sCP_PARTICLE_SIM cp_particle_sim;
//#endif

#ifndef _EXA_HPMPI
#include "MPI_mapping.h"
#include "hdf5_writer.h"
#include "timer.h"
#include "utilities.h"

struct MeasWinEssentialDataInMaster
{
  TIMESTEP num_clear_steps;
  asINT32 nframes_sent;
  sINT16 n_sp_variables;
  sINT16 header_len;
};

std::vector<MeasWinEssentialDataInMaster> essential_data;
std::vector<MPI_Request> essential_data_request;
#endif

/*--------------------------------------------------------------------------*
 * Utilities
 *--------------------------------------------------------------------------*/

/*-----------------------------------------------*
 * Aborting checkpointing.
 *-----------------------------------------------*/
static BOOLEAN checkpoint_aborting_p = FALSE;
static int checkpoint_errno = 0;

static LGI_STREAM cp_stream;
static LGI_STREAM *sp_streams;

#define try_write(STMT)                         \
  if (!checkpoint_aborting_p) {                 \
    BOOLEAN successp = (STMT);                  \
    if (!successp) {                            \
      checkpoint_errno = errno;                 \
      checkpoint_aborting_p = TRUE;             \
    }                                           \
  }

static VOID write_lgi_head(VOID *buffer, size_t buflen)
{
  if (!checkpoint_aborting_p) {
    BOOLEAN successp = lgi_write_next_head(cp_stream, buffer, buflen);
    if (!successp) {
      checkpoint_errno = errno;
      checkpoint_aborting_p = TRUE;
    }
  }
}

template < class OBJECT_TYPE >
static inline VOID write_lgi_head(OBJECT_TYPE &object)
{
  write_lgi_head(&object, sizeof(object));
}

static VOID write_lgi_variable_head(VOID *buffer, size_t buflen)
{
  if (!checkpoint_aborting_p) {
    BOOLEAN successp = lgi_write_next_variable_head(cp_stream, buffer, buflen);
    if (!successp) {
      checkpoint_errno = errno;
      checkpoint_aborting_p = TRUE;
    }
  }
}

template < class OBJECT_TYPE >
static inline VOID write_lgi_variable_head(OBJECT_TYPE &object)
{
  write_lgi_variable_head(&object, sizeof(object));
}

static VOID write_lgi(VOID *buffer, size_t buflen)
{
  if (!checkpoint_aborting_p) {
    BOOLEAN successp = lgi_write(cp_stream, buffer, buflen);
    if (!successp) {
      checkpoint_errno = errno;
      checkpoint_aborting_p = TRUE;
    }
  }
}

template < class OBJECT_TYPE >
static inline VOID write_lgi(OBJECT_TYPE &object)
{
  write_lgi(&object, sizeof(object));
}

static VOID close_lgi_file()
{       
  if (cp_stream) {
    /** Unlike the above functions, we want to try the LGI operation even if we
    *** are in the middle of aborting.  If we're not aborting, but the close fails,
    *** it could mean that the checkpoint wasn't written properly, so we set
    *** the aborting flag so that we'll pick up the fact that the checkpoint is bad.
    *** (For an NFS file under Solaris, for example, an out of space error might
    *** not be reported by the O/S until the call to close().).
    */
    BOOLEAN successp = lgi_close_stream(cp_stream);

    if (!successp) {
      if (!checkpoint_aborting_p) {
        checkpoint_errno = errno;
        checkpoint_aborting_p = TRUE;
      }
    }
  }
}

/* TOC support */

sLGI_POS g_output_ckpt_file_pos[N_CKPT_FILE_POS];
sLGI_POS g_input_ckpt_file_pos[N_CKPT_FILE_POS];

static VOID cp_get_output_file_pos(LGI_STREAM output_ckpt_stream, const CP_CKPT_FILE_POS ckpt_pos) {
  lgi_getpos(output_ckpt_stream, &g_output_ckpt_file_pos[ckpt_pos]);
}

static VOID cp_set_output_file_pos(LGI_STREAM output_ckpt_stream, const CP_CKPT_FILE_POS ckpt_pos) {
  lgi_setpos(output_ckpt_stream, &g_output_ckpt_file_pos[ckpt_pos]);
}

static VOID init_output_file_pos() {
  ccDOTIMES(i, N_CKPT_FILE_POS) {
    g_output_ckpt_file_pos[i] = -1;
  }
}
            
static VOID output_toc_start()
{
  cDGF_CKPT_TOC table_of_contents;

  memset(&table_of_contents, 0x0, sizeof(table_of_contents));

  lgi_write_init_tag(&table_of_contents, DGF_CKPT_TOC_TAG, sizeof(table_of_contents));

  cp_get_output_file_pos(cp_stream, CKPT_TOC_POS);

  lgi_write_next_head(cp_stream, table_of_contents);
}

static VOID output_toc_end()
{
  cDGF_CKPT_TOC table_of_contents;

  lgi_write_init_tag(&table_of_contents, DGF_CKPT_TOC_TAG, sizeof(table_of_contents));

  table_of_contents.ckpt_info_pos         = g_output_ckpt_file_pos[CKPT_CKPT_INFO_POS];
  table_of_contents.audit_trail_pos       = g_output_ckpt_file_pos[CKPT_AUDIT_TRAIL_POS];
  table_of_contents.file_parentage_pos    = g_output_ckpt_file_pos[CKPT_FILE_PARENTAGE_POS];
  table_of_contents.random_seed_pos       = g_output_ckpt_file_pos[CKPT_RANDOM_SEED_POS];
  table_of_contents.eqn_random_seed_pos   = g_output_ckpt_file_pos[CKPT_EQN_RANDOM_SEED_POS];
  table_of_contents.turb_synth_pos        = g_output_ckpt_file_pos[CKPT_TURB_SYNTH_POS];
  table_of_contents.radiation_tm_pos      = g_output_ckpt_file_pos[CKPT_RADIATION_TM_POS];
  table_of_contents.cdi_meas_window_pos   = g_output_ckpt_file_pos[CKPT_CDI_MEAS_WINDOW_POS];
  table_of_contents.ublk_shob_state_pos   = g_output_ckpt_file_pos[CKPT_UBLK_SHOB_STATE_POS];
  table_of_contents.surfel_shob_state_pos = g_output_ckpt_file_pos[CKPT_SURFEL_SHOB_STATE_POS];  
  table_of_contents.meas_window_pos       = g_output_ckpt_file_pos[CKPT_MEAS_WINDOW_POS];
  table_of_contents.nirf_pos              = g_output_ckpt_file_pos[CKPT_NIRF_POS];
  table_of_contents.lrf_pos               = g_output_ckpt_file_pos[CKPT_LRF_POS];
  table_of_contents.movb_pos              = g_output_ckpt_file_pos[CKPT_MOVB_POS];
  table_of_contents.emitter_pos           = g_output_ckpt_file_pos[CKPT_EMITTER_POS];
  table_of_contents.thermal_accel_pos     = g_output_ckpt_file_pos[CKPT_THERMAL_ACCEL_POS];
  table_of_contents.fan_pos               = g_output_ckpt_file_pos[CKPT_FAN_POS];
  table_of_contents.averaged_contact_pos  = g_output_ckpt_file_pos[CKPT_AVERAGED_CONTACT_POS];
  table_of_contents.rotdyn_pos            = g_output_ckpt_file_pos[CKPT_ROTDYN_POS];
  table_of_contents.monitors_pos          = g_output_ckpt_file_pos[CKPT_MONITORS_POS];
  table_of_contents.pt_pf_ratio_pos       = g_output_ckpt_file_pos[CKPT_PT_PF_RATIO_POS];

  // Get the current position so that it can be reset after writing TOC
  sLGI_POS curr_pos;
  lgi_getpos(cp_stream, &curr_pos);

  cp_set_output_file_pos(cp_stream, CKPT_TOC_POS);
  lgi_write_next_head(cp_stream, table_of_contents);

  lgi_setpos(cp_stream, &curr_pos);
}


/*--------------------------------------------------------------------------*
 * Header and records from the SPs
 *--------------------------------------------------------------------------*/

static BOOLEAN write_header() 
{
  auINT32 sp_version = lgi_read_header(sp_streams[0], FALSE); // must be native endianness
  ccDO_FROM_BELOW(i, 1, total_sps) {
    auINT32 version = lgi_read_header(sp_streams[i], FALSE); // must be native endianness
    if (version != sp_version) {
      msg_internal_error("While writing a full checkpoint file, some of the SPs used"
			 " different LGI versions: %ld vs. %ld.", 
			 (long) sp_version, (long) version);
    }
  }

  if (sp_version != LGI_CHECKPOINT_VERSION)
    msg_internal_error("While writing a full checkpoint file, the SP checkpoint version (%ld)"
		       " disagreed with the CP version (%ld).",
		       (long) sp_version, (long) LGI_CHECKPOINT_VERSION);

  return cp_stream && lgi_write_header(cp_stream, LGI_CHECKPOINT_VERSION);
}

#ifndef _EXA_HPMPI
size_t get_audit_trail_len()
{
  size_t len = sizeof(size_t);
  len += strlen(audit_universal_rep(cp_info.audit_trail))*sizeof(char);
  return len;
}

static VOID write_audit_trail( sCKPT_BUFFER& buff)
{
  size_t len = get_audit_trail_len();
  buff.allocate(len);
  buff.write(&len);
  cSTRING audit_trail_ur = audit_universal_rep(cp_info.audit_trail);
  buff.write(audit_trail_ur, strlen(audit_trail_ur));
}
#endif

static VOID write_audit_trail()
{
  cSTRING audit_trail_ur = audit_universal_rep(cp_info.audit_trail);
  cp_get_output_file_pos(cp_stream,CKPT_AUDIT_TRAIL_POS);

  LGI_AUDIT_TRAIL_REC audit_trail_rec;
  audit_trail_rec.tag.id   = LGI_AUDIT_TRAIL_TAG;

  audit_trail_rec.length = strlen(audit_trail_ur);

  audit_trail_rec.tag.length = lgi_pad_and_encode_record_length(audit_trail_rec.length
                                                                + sizeof(audit_trail_rec));

  write_lgi_head(audit_trail_rec);
  write_lgi((VOID *)audit_trail_ur, audit_trail_rec.length);
}

static VOID write_file_parentage()
{
  cp_get_output_file_pos(cp_stream,CKPT_FILE_PARENTAGE_POS);
  write_lgi_head(cp_info.ptge);
  write_lgi(cp_info.disc_platform,
            cp_info.ptge.disc_platform_n_chars);
  write_lgi(cp_info.decomp_target,
            cp_info.ptge.decomp_target_n_chars);
  write_lgi(cp_info.powercase_version,
            cp_info.ptge.powercase_version_n_chars);
  write_lgi(cp_info.disc_version,
            cp_info.ptge.disc_version_n_chars);  
  write_lgi(cp_info.decomp_version,
            cp_info.ptge.decomp_version_n_chars);
  write_lgi(cp_info.loadfactors_version,
            cp_info.ptge.loadfactors_version_n_chars);
}

#ifndef _EXA_HPMPI
size_t get_file_parentage_len()
{
  size_t len = sizeof(size_t);
  len += sizeof(LGI_PARENTAGE_REC);
  size_t num_chars = cp_info.ptge.disc_platform_n_chars+
            cp_info.ptge.decomp_target_n_chars+
            cp_info.ptge.powercase_version_n_chars+
            cp_info.ptge.disc_version_n_chars+
            cp_info.ptge.decomp_version_n_chars+
            cp_info.ptge.loadfactors_version_n_chars;
  len += num_chars*sizeof(char);
  return len;
}
static VOID write_file_parentage( sCKPT_BUFFER& buff)
{
  size_t len = get_file_parentage_len();
  buff.allocate(len);
  buff.write(&len);
  buff.write(&cp_info.ptge);
  buff.write(cp_info.disc_platform,
            cp_info.ptge.disc_platform_n_chars);
  buff.write(cp_info.decomp_target,
            cp_info.ptge.decomp_target_n_chars);
  buff.write(cp_info.powercase_version,
            cp_info.ptge.powercase_version_n_chars);
  buff.write(cp_info.disc_version,
            cp_info.ptge.disc_version_n_chars);  
  buff.write(cp_info.decomp_version,
            cp_info.ptge.decomp_version_n_chars);
  buff.write(cp_info.loadfactors_version,
            cp_info.ptge.loadfactors_version_n_chars);
}
#endif

static inline VOID copy_record_body_from_sp(LGI_TAG &tag, STP_PROC sp)
{
  const asINT32 COPY_BUFSIZE = 16 * 1024;
  char buf[COPY_BUFSIZE];
  FILE_OFFSET n_bytes_left = (FILE_OFFSET)tag.length * LGI_LEN_MULTIPLE;

  n_bytes_left -= sizeof(LGI_TAG);

  while (n_bytes_left > 0) {
    size_t n = MIN(n_bytes_left, sizeof(buf));
    lgi_read(sp_streams[sp], buf, n);
    write_lgi(buf, n); 
    n_bytes_left -= n;
  }
}

static VOID copy_record_from_sp(LGI_TAG_ID tag_id, STP_PROC sp)
{
  LGI_TAG tag;
  lgi_read_next_head(sp_streams[sp], tag);
  if (tag.id != tag_id)
    msg_internal_error("Expected LGI record %s (tag %d) from SP %d but found %s (tag %d)",
                       lgi_tag_namestring(tag_id), tag_id, sp,
                       lgi_tag_namestring(tag.id), tag.id);

  write_lgi_head(tag);

  copy_record_body_from_sp(tag, sp);
}

static VOID write_random_seed()
{

  cp_get_output_file_pos(cp_stream,CKPT_RANDOM_SEED_POS);
  ccDOTIMES(sp, total_sps)
    copy_record_from_sp(LGI_CKPT_RANDOM_SEED_TAG, sp);
}

static VOID write_eqn_random_seed()
{
  cp_get_output_file_pos(cp_stream,CKPT_EQN_RANDOM_SEED_POS);
  ccDOTIMES(sp, total_sps)
    copy_record_from_sp(LGI_CKPT_EQN_RANDOM_SEED_TAG, sp);
}

static VOID write_fan_descs()
{
  LGI_TAG tag = lgi_peek_tag(sp_streams[0]);
  if (tag.id != LGI_CKPT_FAN_TAG)
    return;
  cp_get_output_file_pos(cp_stream, CKPT_FAN_POS); 
  ccDOTIMES(sp, total_sps)
    copy_record_from_sp(LGI_CKPT_FAN_TAG, sp);
}

static VOID write_averaged_contacts()
{
  LGI_TAG tag = lgi_peek_tag(sp_streams[0]);
  if (tag.id != LGI_CKPT_AVERAGED_CONTACTS_TAG)
    return;
  cp_get_output_file_pos(cp_stream, CKPT_AVERAGED_CONTACT_POS);
  
  //Collects info from all SPs to fille the complete set of average heat fluxes
  std::vector<BOOLEAN> is_recv(cp_info.num_averaged_contacts, FALSE);
  std::vector<cLGI_CKPT_AVERAGED_CONTACT_ITEM> avg_contact_items(cp_info.num_averaged_contacts);
  cLGI_CKPT_AVERAGED_CONTACT_HEADER header;
  cLGI_CKPT_AVERAGED_CONTACT_ITEM item;
  ccDOTIMES(sp, total_sps) {
    header.read(sp_streams[sp]);
    ccDOTIMES(i, header.num_averaged_contacts) {
      item.read(sp_streams[sp]);
      uINT32 idx = item.global_averaged_contact_index;
      if (!is_recv.at(idx)) {
        avg_contact_items.at(idx) = item;
        is_recv.at(idx) = TRUE;
      }
    }
  }
  //Writes info if all all averaged contacts have been received. Otherwise, throws an error
  if (std::all_of(is_recv.begin(), is_recv.end(), [](bool v) { return v; })) {
    //Writes top level record with the total number of 
    header.num_averaged_contacts = cp_info.num_averaged_contacts;
    header.write(cp_stream, FALSE);
    //Writes each of the averaged contact info
    ccDOTIMES(i, cp_info.num_averaged_contacts) {
      avg_contact_items[i].write(cp_stream);
    }
  } else {
    msg_internal_error("Missing averaged contacts for checkpoint");
  }
}

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS

VOID write_random_particle_property_state() {
  LGI_TAG tag = lgi_peek_tag(sp_streams[0]);
  if(tag.id != LGI_CKPT_PARTICLE_RANDOM_PROPERTY_TAG)
    return;
  ccDOTIMES(sp, total_sps) {
    copy_record_from_sp(LGI_CKPT_PARTICLE_RANDOM_PROPERTY_TAG, sp);

  }
}
#ifndef _EXA_HPMPI
size_t get_cp_emitter_state_len() {
  return sizeof(size_t) + sizeof(asINT32) + 
          cp_particle_sim.emitters.size() * sizeof(LGI_CKPT_PARTICLE_EMITTERS_STATE_REC);
}

VOID write_cp_emitter_state( sCKPT_BUFFER& buff) {
  
  size_t len = get_cp_emitter_state_len();
  buff.allocate(len);
  buff.write(&len);
  asINT32 n_emitters = cp_particle_sim.emitters.size(); // Only emitters started via monitors
  buff.write(&n_emitters);

  ccDOTIMES(i, n_emitters) {
    LGI_CKPT_PARTICLE_EMITTERS_STATE_REC ckpt_emitter_desc;
    EMITTER emitter = cp_particle_sim.emitters[i];
    ckpt_emitter_desc.sim_emitter_id        = emitter->id();
    ckpt_emitter_desc.is_requested_to_start = emitter->is_requested_to_start();
    ckpt_emitter_desc.is_scheduled_to_start = emitter->is_scheduled_to_start();
    ckpt_emitter_desc.requested_start_time  = emitter->get_requested_start_time();
    ckpt_emitter_desc.start_time            = emitter->get_start_time();
    ckpt_emitter_desc.end_time              = emitter->get_end_time(); 
    buff.write(&ckpt_emitter_desc);
  }
}
#endif

VOID write_emitter_state() {
  cp_get_output_file_pos(cp_stream, CKPT_EMITTER_POS);
  // Write cp emitter state for emitters started via monitors
  LGI_TAG tag;
  tag.id = LGI_CKPT_PARTICLE_EMITTERS_STATE_TAG;
  tag.length = 0;
  write_lgi_variable_head(tag);
  asINT32 n_emitters = cp_particle_sim.emitters.size(); // Only emitters started via monitors
  write_lgi(n_emitters);

  ccDOTIMES(i, n_emitters) {
    LGI_CKPT_PARTICLE_EMITTERS_STATE_REC ckpt_emitter_desc;
    EMITTER emitter = cp_particle_sim.emitters[i];
    ckpt_emitter_desc.sim_emitter_id        = emitter->id();
    ckpt_emitter_desc.is_requested_to_start = emitter->is_requested_to_start();
    ckpt_emitter_desc.is_scheduled_to_start = emitter->is_scheduled_to_start();
    ckpt_emitter_desc.requested_start_time  = emitter->get_requested_start_time();
    ckpt_emitter_desc.start_time            = emitter->get_start_time();
    ckpt_emitter_desc.end_time              = emitter->get_end_time(); 
    write_lgi(ckpt_emitter_desc);
  }

  tag = lgi_peek_tag(sp_streams[0]);
  if (tag.id != LGI_CKPT_PARTICLE_EMITTERS_TAG)
    return;

  ccDOTIMES(sp, total_sps) {
    copy_record_from_sp(LGI_CKPT_PARTICLE_EMITTERS_TAG, sp);
  }
}

VOID sCP_TRAJECTORY_ID_MAP::write_ckpt()
{
  LGI_TAG tag;
  tag.id = LGI_TRAJECTORY_ID_MAP_TAG;
  tag.length = 0;

  write_lgi_variable_head(tag);

  write_lgi(m_base_global_ids.data(), sizeof(TRAJECTORY_ID) * m_base_global_ids.size());
  write_lgi(m_next_available_global_index.data(), sizeof(TRAJECTORY_ID) * m_next_available_global_index.size());
}
//#endif



static VOID write_global_nirf_state()
{
  if (cp_info.is_global_ref_frame) {
    cp_get_output_file_pos(cp_stream,CKPT_NIRF_POS );
    copy_record_from_sp(LGI_GLOBAL_NIRF_STATE_TAG, 0); // only provided by SP 0
  }
}

static VOID write_lrf_state()
{
  if (cp_info.n_lrfs() > 0) {
    cp_get_output_file_pos(cp_stream,CKPT_LRF_POS);
    LGI_TAG tag = lgi_peek_tag(sp_streams[0]);
    if (tag.id != LGI_LRF_STATE_TAG)
      return;

    copy_record_from_sp(LGI_LRF_STATE_TAG, 0); // only provided by SP 0
  }
}

static VOID write_cp_rotdyn_state()
{
  cp_get_output_file_pos(cp_stream,CKPT_ROTDYN_POS);
  LGI_TAG tag;
  tag.id = DGF_CKPT_ROTDYN_DESC_TAG;
  tag.length = 0;

  write_lgi_variable_head(tag);
  write_lgi(cp_info.n_rotational_dynamics_descs);

  ccDOTIMES(i, cp_info.n_rotational_dynamics_descs) {
    CP_ROTATIONAL_DYNAMICS_DESC rotdyn_desc = cp_info.rotational_dynamics_descs + i;
    DGF_CKPT_ROTDYN_DESC ckpt_rotdyn_desc;
    ckpt_rotdyn_desc.moment_of_inertia = rotdyn_desc->m_moment_of_inertia;
    ckpt_rotdyn_desc.angular_acceleration = rotdyn_desc->m_angular_acceleration;
    ckpt_rotdyn_desc.angular_acceleration_m1 = rotdyn_desc->m_angular_acceleration_m1;
    ckpt_rotdyn_desc.tau = rotdyn_desc->m_tau;
    ckpt_rotdyn_desc.tau_m1 = rotdyn_desc->m_tau_m1;
    ckpt_rotdyn_desc.tau_abs = rotdyn_desc->m_tau_abs;
    ckpt_rotdyn_desc.tau_abs_m1 = rotdyn_desc->m_tau_abs_m1;
    ckpt_rotdyn_desc.char_MI = rotdyn_desc->m_char_MI;
    ckpt_rotdyn_desc.omega_max = rotdyn_desc->m_omega_max;
    ckpt_rotdyn_desc.max_radius_lrf = rotdyn_desc->m_max_radius_lrf;
    ckpt_rotdyn_desc.lrf_height = rotdyn_desc->m_lrf_height;
    ckpt_rotdyn_desc.count = rotdyn_desc->m_count;
    ckpt_rotdyn_desc.omega_next = rotdyn_desc->m_omega_next;
    ckpt_rotdyn_desc.cur_alpha_weight = rotdyn_desc->m_cur_alpha_weight;
    ckpt_rotdyn_desc.mi_decrease_factor = rotdyn_desc->m_mi_decrease_factor;
    ckpt_rotdyn_desc.initial_mi_coeff = rotdyn_desc->m_initial_mi_coeff;
    ckpt_rotdyn_desc.min_mi_ratio = rotdyn_desc->m_min_mi_ratio;
    write_lgi(ckpt_rotdyn_desc);
  }
}

#ifndef _EXA_HPMPI
static size_t get_cp_rotdyn_len()
{
  return sizeof(size_t) + sizeof(asINT32) + cp_info.n_rotational_dynamics_descs * sizeof(DGF_CKPT_ROTDYN_DESC);
}

static VOID write_cp_rotdyn( sCKPT_BUFFER& buff)
{
  size_t len = get_cp_rotdyn_len();
  buff.allocate(len);
  buff.write(&len);
  buff.write(&cp_info.n_rotational_dynamics_descs);

  ccDOTIMES(i, cp_info.n_rotational_dynamics_descs) {
    CP_ROTATIONAL_DYNAMICS_DESC rotdyn_desc = cp_info.rotational_dynamics_descs + i;
    DGF_CKPT_ROTDYN_DESC ckpt_rotdyn_desc;
    ckpt_rotdyn_desc.moment_of_inertia = rotdyn_desc->m_moment_of_inertia;
    ckpt_rotdyn_desc.angular_acceleration = rotdyn_desc->m_angular_acceleration;
    ckpt_rotdyn_desc.angular_acceleration_m1 = rotdyn_desc->m_angular_acceleration_m1;
    ckpt_rotdyn_desc.tau = rotdyn_desc->m_tau;
    ckpt_rotdyn_desc.tau_m1 = rotdyn_desc->m_tau_m1;
    ckpt_rotdyn_desc.tau_abs = rotdyn_desc->m_tau_abs;
    ckpt_rotdyn_desc.tau_abs_m1 = rotdyn_desc->m_tau_abs_m1;
    ckpt_rotdyn_desc.char_MI = rotdyn_desc->m_char_MI;
    ckpt_rotdyn_desc.omega_max = rotdyn_desc->m_omega_max;
    ckpt_rotdyn_desc.max_radius_lrf = rotdyn_desc->m_max_radius_lrf;
    ckpt_rotdyn_desc.lrf_height = rotdyn_desc->m_lrf_height;
    ckpt_rotdyn_desc.count = rotdyn_desc->m_count;
    ckpt_rotdyn_desc.omega_next = rotdyn_desc->m_omega_next;
    ckpt_rotdyn_desc.cur_alpha_weight = rotdyn_desc->m_cur_alpha_weight;
    ckpt_rotdyn_desc.mi_decrease_factor = rotdyn_desc->m_mi_decrease_factor;
    ckpt_rotdyn_desc.initial_mi_coeff = rotdyn_desc->m_initial_mi_coeff;
    ckpt_rotdyn_desc.min_mi_ratio = rotdyn_desc->m_min_mi_ratio;
    buff.write(&ckpt_rotdyn_desc);
  }
}
#endif

static VOID write_turb_synth_info_state()
{
  if (cp_info.num_turb_tables > 0) {
    cp_get_output_file_pos(cp_stream, CKPT_TURB_SYNTH_POS);
    STP_PROC ckpt_sp = cp_info.m_turb_synth_ckpt_sp;
    LGI_TAG tag = lgi_peek_tag(sp_streams[ckpt_sp]);
    if (tag.id != LGI_TURB_SYNTH_INFO_STATE_TAG)
      return;

    copy_record_from_sp(LGI_TURB_SYNTH_INFO_STATE_TAG, ckpt_sp); // only provided by SP 0
  }
}

static VOID write_radiation_tm_info()
{
  if (cp_info.is_radiation) {
    cp_get_output_file_pos(cp_stream, CKPT_RADIATION_TM_POS);
    STP_PROC procs_to_check[STP_N_REALMS] = {STP_PROC_INVALID,STP_PROC_INVALID};

    if (cp_info.is_flow) {
      procs_to_check[STP_FLOW_REALM] = 0;
    }

    if (cp_info.is_conduction) {
      if (procs_to_check[STP_FLOW_REALM] != STP_PROC_INVALID) {
        procs_to_check[STP_COND_REALM] = total_sps/2;
      } else {
        procs_to_check[STP_COND_REALM] = 0;
      }
    }

    for (REALM realm = 0; realm < STP_N_REALMS; realm++) {
      auto sp = procs_to_check[realm];
      if (sp == STP_PROC_INVALID) {
        continue;
      }

      copy_record_from_sp(LGI_CKPT_RADIATION_TM_TAG, sp);
    }
  }
}

static VOID write_movb_state()
{
  if (cp_info.n_movb_xforms() > 0) {
    cp_get_output_file_pos(cp_stream, CKPT_MOVB_POS);
    LGI_TAG tag = lgi_peek_tag(sp_streams[0]);
    if (tag.id != LGI_MOVB_STATE_TAG)
      return;

    copy_record_from_sp(LGI_MOVB_STATE_TAG, 0); // only provided by SP 0
  }
}

static VOID write_thermal_accel_state()
{
  if (cp_info.is_heat_transfer) {
    cp_get_output_file_pos(cp_stream, CKPT_THERMAL_ACCEL_POS);
    copy_record_from_sp(LGI_THERMAL_ACCEL_STATE_TAG, 0); // only provided by SP 0
  }
}

static VOID write_transient_seed_descs()
{
  LGI_TAG tag;
  tag.id = DGF_CKPT_TBS_DESC_TAG;
  tag.length = 0;
  write_lgi_variable_head(tag);
  write_lgi(cp_info.n_transient_seed_from_meas_descs);
  ccDOTIMES(i, cp_info.n_seed_from_meas_descs) {
    if(!cp_info.seed_from_meas_descs[i]->m_transient_boundary_seeding)
      continue;
    DGF_CKPT_TBS_DESC tbs_desc;
    TRANSIENT_BOUNDARY_SEEDING tbs_meas_desc =  static_cast<TRANSIENT_BOUNDARY_SEEDING>(cp_info.seed_from_meas_descs[i]);
    tbs_desc.n_frames_sent = tbs_meas_desc->m_n_frames_sent;
    asINT32 frames = (cp_info.time - tbs_meas_desc->m_frame_start_time) / tbs_meas_desc->m_frame_period;
    int nth_send = frames / NUM_FRAMES - 1;
    int start_frame_index = 0;
    for(int i = 0; i < nth_send ; i++) {
      for(int j = 0; j < NUM_FRAMES; j++) {
        start_frame_index ++;
        if(start_frame_index % tbs_meas_desc->m_total_meas_frames == 0)
          start_frame_index = 0;
      }
    }
    tbs_desc.n_total_frames_sent = start_frame_index;
    write_lgi(tbs_desc);
  }
}

#ifndef _EXA_HPMPI
static size_t get_transient_seeds_descs_len()
{
  size_t len = sizeof(size_t) + sizeof(asINT32);
  ccDOTIMES(i, cp_info.n_seed_from_meas_descs) {
    if(!cp_info.seed_from_meas_descs[i]->m_transient_boundary_seeding)
      continue;
    len += sizeof(DGF_CKPT_TBS_DESC);
  }
  return len;
}
static VOID write_transient_seed_descs( sCKPT_BUFFER& buff)
{
  size_t len = get_transient_seeds_descs_len();
  buff.allocate(len);
  buff.write(&len);
  buff.write(&cp_info.n_transient_seed_from_meas_descs);
  ccDOTIMES(i, cp_info.n_seed_from_meas_descs) {
    if(!cp_info.seed_from_meas_descs[i]->m_transient_boundary_seeding)
      continue;
    DGF_CKPT_TBS_DESC tbs_desc;
    TRANSIENT_BOUNDARY_SEEDING tbs_meas_desc =  static_cast<TRANSIENT_BOUNDARY_SEEDING>(cp_info.seed_from_meas_descs[i]);
    tbs_desc.n_frames_sent = tbs_meas_desc->m_n_frames_sent;
    asINT32 frames = (cp_info.time - tbs_meas_desc->m_frame_start_time) / tbs_meas_desc->m_frame_period;
    int nth_send = frames / NUM_FRAMES - 1;
    int start_frame_index = 0;
    for(int i = 0; i < nth_send ; i++) {
      for(int j = 0; j < NUM_FRAMES; j++) {
        start_frame_index ++;
        if(start_frame_index % tbs_meas_desc->m_total_meas_frames == 0)
          start_frame_index = 0;
      }
    }
    tbs_desc.n_total_frames_sent = start_frame_index;
    buff.write(&tbs_desc);
  }
}


typedef struct {		/* Fixed Part */
  TIMESTEP	time;		/* Time of the checkpoint */
  sINT16        n_sps;	        /* Number of processors */
  sINT16	version;	/* Set to LGI_CHECKPOINT_VERSION */
  sINT8		is_big_endian;
  sINT8         is_double_precision;
  sINT8         avg_mme_ckpt;
  STP_LATTICE_TYPE_ID	lattice_type;
  TIMESTEP          n_lb_base_steps;
  TIMESTEP          n_t_base_steps;
  TIMESTEP          n_ke_base_steps;
  TIMESTEP          n_scalar_base_steps;
  TIMESTEP          n_particle_base_steps;
  TIMESTEP          acous_start_time;
  sINT8          freeze_momentum_field;
  dFLOAT         thermal_timestep_ratio;
  sINT32         momentum_freeze_start_time;
  sINT32         calibration_iterations;
  sINT8          local_vel_freeze;
  sINT8          is_large_pore;  /* 5G */
  } sCKPT_INFO; //necessary bit from LGI_CHECKPOINT_INFO_REC

constexpr size_t get_checkpoint_info_len()
{
  size_t len = sizeof(size_t);
  len += sizeof(LGI_CHECKPOINT_INFO_REC);
  return len;
}

static VOID write_checkpoint_info( sCKPT_BUFFER& buff, TIMESTEP ckpt_timestep)
{
  LGI_CHECKPOINT_INFO_REC record;

  LGI_WRITE_INIT_TAG (record, LGI_CHECKPOINT_INFO);

  record.time = ckpt_timestep;
  record.n_sps = total_sps;
  record.version = LGI_CHECKPOINT_VERSION;
  record.lattice_type = cp_info.lattice_type;
  record.is_big_endian = SCALAR_BIG_ENDIAN;
  record.is_double_precision = cp_info.is_sim_double_precision;
  record.n_lb_base_steps = cp_info.n_lb_base_steps;
  record.n_t_base_steps = cp_info.n_t_base_steps;
  record.n_ke_base_steps = cp_info.n_ke_base_steps;
  record.n_uds_base_steps = cp_info.n_uds_base_steps;
  record.n_conduction_base_steps = cp_info.n_conduction_base_steps;
  record.n_radiation_base_steps = cp_info.n_radiation_base_steps;
  record.n_particle_base_steps = cp_info.n_particle_base_steps;
  record.acous_start_time = cp_info.acous_start_time;
  record.freeze_momentum_field = cp_info.freeze_momentum_field;
  record.thermal_timestep_ratio = cp_info.thermal_timestep_ratio;
  record.momentum_freeze_start_time = cp_info.momentum_freeze_start_time;
  record.avg_mme_ckpt = cp_info.has_average_mme_window;
  record.local_vel_freeze = cp_info.local_vel_freeze;
  record.calibration_iterations = cp_info.calibration_params.iterations;
  record.is_large_pore = cp_info.is_large_pore_sim;

  buff.write(&record);
}
#endif

static VOID write_checkpoint_info(TIMESTEP ckpt_timestep)
{
  cp_get_output_file_pos(cp_stream, CKPT_CKPT_INFO_POS);

  LGI_CHECKPOINT_INFO_REC record;

  LGI_WRITE_INIT_TAG (record, LGI_CHECKPOINT_INFO);

  record.time = ckpt_timestep;
  record.n_sps = total_sps;
  record.version = LGI_CHECKPOINT_VERSION;
  record.lattice_type = cp_info.lattice_type;
  record.is_big_endian = SCALAR_BIG_ENDIAN;
  record.is_double_precision = cp_info.is_sim_double_precision;
  record.n_lb_base_steps = cp_info.n_lb_base_steps;
  record.n_t_base_steps = cp_info.n_t_base_steps;
  record.n_ke_base_steps = cp_info.n_ke_base_steps;
  record.n_uds_base_steps = cp_info.n_uds_base_steps;
  record.n_conduction_base_steps = cp_info.n_conduction_base_steps;
  record.n_radiation_base_steps = cp_info.n_radiation_base_steps;
  record.n_particle_base_steps = cp_info.n_particle_base_steps;
  record.acous_start_time = cp_info.acous_start_time;
  record.freeze_momentum_field = cp_info.freeze_momentum_field;
  record.thermal_timestep_ratio = cp_info.thermal_timestep_ratio;
  record.momentum_freeze_start_time = cp_info.momentum_freeze_start_time;
  record.avg_mme_ckpt = cp_info.has_average_mme_window;
  record.local_vel_freeze = cp_info.local_vel_freeze;
  record.calibration_iterations = cp_info.calibration_params.iterations;
  record.is_large_pore = cp_info.is_large_pore_sim;

  write_lgi_head(record);
}

/*--------------------------------------------------------------------------*
 * Verifying EOF
 *--------------------------------------------------------------------------*/
static VOID verify_sp_eof()
{
  ccDOTIMES(i, total_sps) {
    LGI_TAG tag = lgi_peek_tag(sp_streams[i]);
    if (tag.id != LGI_EOF_TAG)
      msg_internal_error("While writing full checkpoint file, the SP streams "
                         "still had data left after the file was complete.  "
                         "Data left began with tag %d(%s).", 
                         (int) tag.id, lgi_tag_namestring(tag.id));
  }
}

static VOID write_shob_data(STP_SHOB_ID n_shobs, cSTRING cp_status,
                            cSTRING shob_flavor, STP_PROC* shob_procs) {

  // msg_print("Writing %d %s shobs",n_shobs,shob_flavor);
#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  std::vector<char> ckpt_buf; //Changed to remove restriction on size of ckpt record incase a
                              //large number of parcels were checkpointed along with the given shob.
#else
  char buf[LGI_MAX_SHOB_CKPT_LEN];
#endif

  char status[256];

  BOOLEAN multiple_sps = total_sps > 1;
  dFLOAT one_over_n_shobs = 1.0 / n_shobs;
  asINT32 percent_shobs_processed = 0;
  for (STP_SHOB_ID i = 0; i < n_shobs; i++) {
    STP_PROC sp = multiple_sps ? shob_procs[i] : 0;
    cDGF_CKPT_SHOB_HEADER shob_header;
    shob_header.read(sp_streams[sp]);
    // msg_print("%s shob %d, len %d", shob_flavor, shob_header.shob_id, shob_header.len);
    if (shob_header.shob_id != i) {
      msg_internal_error("Full checkpoint: %s sent from SP to CP not in expected order. Expect %d but received %d",
                         shob_flavor, i, shob_header.shob_id);
    }

    try_write(shob_header.write(cp_stream));
    if (ckpt_buf.size() < shob_header.len) {
      ckpt_buf.resize(2 * shob_header.len);
    }
    lgi_read(sp_streams[sp], &ckpt_buf[0], shob_header.len);
    write_lgi(&ckpt_buf[0], shob_header.len);
    asINT32 percent_shobs = (i + 1) * (100.0 * one_over_n_shobs);
    if (percent_shobs > percent_shobs_processed) {
      percent_shobs_processed = percent_shobs;
      sprintf(status, "%s (Full checkpoint: %s: %d%% complete)", cp_status,
              shob_flavor, percent_shobs_processed);
      cp_jobctl_output_status(status);
    }
    //msg_print("write full ckpt for %s %d", shob_flavor, i);
  }

}

static VOID write_surfels(cSTRING cp_status) {

  ccDOTIMES(sp, total_sps) {
    LGI_TAG sp_tag;
    lgi_read_next_head(sp_streams[sp], sp_tag);
    if (sp_tag.id != LGI_SHOB_STATE_TAG)
      msg_internal_error("Full checkpoint: surfel data not sent from SP %d", sp);
  }
  cSTRING shob_flavor[2] = {"flow surfels", "cond_surfels"};
  LGI_TAG tag;
  tag.id = LGI_SHOB_STATE_TAG;
  tag.length = 0;
  cp_get_output_file_pos(cp_stream, CKPT_SURFEL_SHOB_STATE_POS);
  write_lgi_variable_head(tag);
  DO_REALMS(realm) {
    STP_SHOB_ID n_shobs = cp_info.num_surfels[realm];
    STP_PROC *shob_procs =  &cp_info.surfel_procs[realm][0];
    write_shob_data(n_shobs, cp_status, shob_flavor[realm], shob_procs);
  }
}

static VOID write_ublks(cSTRING cp_status) {

  ccDOTIMES(sp, total_sps) {
    LGI_TAG sp_tag;
    lgi_read_next_head(sp_streams[sp], sp_tag);
    if (sp_tag.id != LGI_SHOB_STATE_TAG)
      msg_internal_error("Full checkpoint: ublk data not sent from SP %d", sp);
  }
  cSTRING shob_flavor = "voxels";
  LGI_TAG tag;
  tag.id = LGI_SHOB_STATE_TAG;
  tag.length = 0;
  cp_get_output_file_pos(cp_stream, CKPT_UBLK_SHOB_STATE_POS);
  write_lgi_variable_head(tag);
  DO_REALMS(realm) {
    STP_SHOB_ID n_shobs = cp_info.num_ublks[realm];
    STP_PROC *shob_procs = &cp_info.ublk_procs[realm][0];
    //msg_print("Writing shob data for %d ublks realm %d",n_shobs,realm);
    write_shob_data(n_shobs, cp_status, shob_flavor, shob_procs);
  }
}

static VOID write_cdi_meas_windows()
{
  cp_get_output_file_pos(cp_stream,CKPT_CDI_MEAS_WINDOW_POS); 
  LGI_TAG tag;
  tag.id = DGF_CKPT_CDI_MEAS_WINDOW_TAG;
  tag.length = 0;

  write_lgi_variable_head(tag);
  sINT32 n_cdi_meas_windows = cp_info.n_non_surf_coupling_cdi_meas_windows;
  write_lgi(n_cdi_meas_windows);

  ccDOTIMES(i, n_cdi_meas_windows) {
    CDI_MEAS_WINDOW cdi_win = cp_info.cdi_meas_windows + i;
    DGF_CKPT_CDI_MEAS_WINDOW ckpt_cdi_win;
    ckpt_cdi_win.overridden_by_meas_timing = cdi_win->overridden_by_meas_timing;
    ckpt_cdi_win.disabled_by_meas_include_exclude = cdi_win->disabled_by_meas_include_exclude;
    ckpt_cdi_win.start_time                = cdi_win->start_time;
    ckpt_cdi_win.end_time                  = cdi_win->end_time;
    ckpt_cdi_win.average_interval          = cdi_win->average_interval;
    ckpt_cdi_win.period                    = cdi_win->period;
    ckpt_cdi_win.min_start_time            = cdi_win->min_start_time;
    ckpt_cdi_win.is_meas_started           = cdi_win->meas_started_p;
    if (cdi_win->is_average_mme) {
      ckpt_cdi_win.is_requested_to_stop    = cp_info.request_to_stop_avg_mme;
      ckpt_cdi_win.requested_stop_time     = cp_info.requested_time_to_stop_avg_mme;
      ckpt_cdi_win.is_scheduled_to_stop    = cp_info.time_to_stop_avg_mme < TIMESTEP_MAX;
      ckpt_cdi_win.scheduled_stop_time     = cp_info.time_to_stop_avg_mme;
      ckpt_cdi_win.is_meas_stopped         = cp_info.average_mme_stopped;
#if DEBUG_AVG_MME
      msg_print("Write avg mme meas window ckpt: is_requested_to_stop %d requested_stop_time %f is_scheduled_to_stop %d scheduled_stop_time %f is_meas_stopped %d",
                ckpt_cdi_win.is_requested_to_stop,
                ckpt_cdi_win.requested_stop_time,
                ckpt_cdi_win.is_scheduled_to_stop,
                ckpt_cdi_win.scheduled_stop_time,
                ckpt_cdi_win.is_meas_stopped
                );
#endif
    }
    write_lgi(ckpt_cdi_win);
  }
}

#ifndef _EXA_HPMPI
size_t get_cdi_meas_windows_len()
{
  size_t len = sizeof(size_t);
  len += sizeof(sINT32);
  sINT32 n_cdi_meas_windows = cp_info.n_non_surf_coupling_cdi_meas_windows;
  len += n_cdi_meas_windows*sizeof(DGF_CKPT_CDI_MEAS_WINDOW);
  return len;
}

static VOID write_cdi_meas_windows( sCKPT_BUFFER& buff)
{
  size_t len = get_cdi_meas_windows_len();
  buff.allocate(len);
  buff.write(&len);
  sINT32 n_cdi_meas_windows = cp_info.n_non_surf_coupling_cdi_meas_windows;
  buff.write(&n_cdi_meas_windows);

  ccDOTIMES(i, n_cdi_meas_windows) {
    CDI_MEAS_WINDOW cdi_win = cp_info.cdi_meas_windows + i;
    DGF_CKPT_CDI_MEAS_WINDOW ckpt_cdi_win;
    ckpt_cdi_win.overridden_by_meas_timing = cdi_win->overridden_by_meas_timing;
    ckpt_cdi_win.disabled_by_meas_include_exclude = cdi_win->disabled_by_meas_include_exclude;
    ckpt_cdi_win.start_time                = cdi_win->start_time;
    ckpt_cdi_win.end_time                  = cdi_win->end_time;
    ckpt_cdi_win.average_interval          = cdi_win->average_interval;
    ckpt_cdi_win.period                    = cdi_win->period;
    ckpt_cdi_win.min_start_time            = cdi_win->min_start_time;
    ckpt_cdi_win.is_meas_started           = cdi_win->meas_started_p;
    if (cdi_win->is_average_mme) {
      ckpt_cdi_win.is_requested_to_stop    = cp_info.request_to_stop_avg_mme;
      ckpt_cdi_win.requested_stop_time     = cp_info.requested_time_to_stop_avg_mme;
      ckpt_cdi_win.is_scheduled_to_stop    = cp_info.time_to_stop_avg_mme < TIMESTEP_MAX;
      ckpt_cdi_win.scheduled_stop_time     = cp_info.time_to_stop_avg_mme;
      ckpt_cdi_win.is_meas_stopped         = cp_info.average_mme_stopped;
#if DEBUG_AVG_MME
      msg_print("Write avg mme meas window ckpt: is_requested_to_stop %d requested_stop_time %f is_scheduled_to_stop %d scheduled_stop_time %f is_meas_stopped %d",
                ckpt_cdi_win.is_requested_to_stop,
                ckpt_cdi_win.requested_stop_time,
                ckpt_cdi_win.is_scheduled_to_stop,
                ckpt_cdi_win.scheduled_stop_time,
                ckpt_cdi_win.is_meas_stopped
                );
#endif
    }
    buff.write(&ckpt_cdi_win);
  }
}

static VOID recieve_meas_win_essential_data()
{
  // Size of the windows_clearsteps. Resizing it.
  int num_clear_steps = 0;
  essential_data_request.clear();//Needed if more than one checkpoint per simulation. i.e. checkpoint_interval option
  for (size_t id = 0; id < cp_info.n_meas_windows(); id++)
  {
    CP_MEAS_WINDOW window = cp_info.meas_windows[id];
    // ToDo: remaining conditions
    num_clear_steps += window && !window->is_average_mme && !window->m_is_particle_trajectory_window;
  }  
  essential_data.resize(num_clear_steps);

  // 
  int win_idx = 0;
  for (size_t id = 0; id < cp_info.n_meas_windows(); id++)
  {
    CP_MEAS_WINDOW window = cp_info.meas_windows[id];
    // ToDo: Remaining conditions
    if(window && !window->is_average_mme)
    {
      if (!window->m_is_particle_trajectory_window) {
        essential_data_request.push_back(MPI_REQUEST_NULL);
        MPI_Irecv(&essential_data[win_idx++], sizeof(MeasWinEssentialDataInMaster),
                MPI_BYTE, window->m_master_sp, eMPI_MEAS_WIN_SP_TO_CP_ESS_TAG,
                eMPI_sp_cp_comm, &essential_data_request.back());
        // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(), "\033[0;35m MPI_Irecv_eMPI_MEAS_WIN_SP_TO_CP_ESS_TAG: %i from sp%d\n\033[0m", id, window->m_master_sp);

      }
    }
  }
}
#endif
// write_meas_windows assumes that the SPs send meas cells to the CP in global index order.
static VOID write_meas_windows(TIMESTEP ckpt_timestep, cSTRING cp_status)
{
  ccDOTIMES(sp, total_sps) {
    LGI_TAG tag;
    lgi_read_next_head(sp_streams[sp], tag);
    if (tag.id != DGF_CKPT_MEAS_WINDOW_TAG)
      msg_internal_error("Full checkpoint: meas window data not sent from SP %d",
                         sp);
  }

  cp_get_output_file_pos(cp_stream, CKPT_MEAS_WINDOW_POS);

  LGI_TAG tag;
  tag.id = DGF_CKPT_MEAS_WINDOW_TAG;
  tag.length = 0;
  write_lgi_variable_head(tag);

  STP_MEAS_WINDOW_INDEX n_ckpt_meas_windows = cp_info.n_meas_windows();
  write_lgi(n_ckpt_meas_windows);

  const int N_VARS_BUFFER = 32;
  std::vector<SP_MEAS_CELL_VAR> sp_meas_cell_buffer(N_VARS_BUFFER); // space for 32 variables should be ample
  SP_MEAS_CELL_VAR *sp_meas_cell = &sp_meas_cell_buffer[0];

  TIMESTEP base_ckpt_timestep = ckpt_timestep * cp_info.n_user_base_steps;
  TIMESTEP solver_timestep = base_ckpt_timestep;


  char status[256];

  DO_CP_MEAS_WINDOWS(window) {

    if (window->is_average_mme) {
      continue;
    }
    if(cp_info.is_conduction && cp_info.is_flow) {
      if(window->is_cond_window())
        solver_timestep = cp_info.convert_to_ts_cond(base_ckpt_timestep);
      else
        solver_timestep = cp_info.convert_to_ts_flow(base_ckpt_timestep);

    }
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    if (!window->m_is_particle_trajectory_window) {
//#endif
      // No need to checkpoint this window if the data will simply be cleared after the checkpoint-restore,
      // or if we have passed the window's end time. We must read clear_timestep from an SP because
      // window->m_clear_timestep may not be the same as the SP's clear_timestep because the CP may be
      // servicing the checkpoint before a meas window frame.
      TIMESTEP clear_timestep;

      lgi_read(sp_streams[window->m_master_sp], clear_timestep);
      write_lgi(clear_timestep);
      // See PR 26615 as to why the following computation on CP is unreliable,
      // and we read it from the SP instead
      // TIMESTEP prev_nsets_written_minus_base_frame = window->prev_nsets_written - window->base_frame;
      TIMESTEP n_frames_written; 
      lgi_read(sp_streams[window->m_master_sp], n_frames_written);
      TIMESTEP prev_nsets_written = n_frames_written + window->initial_prev_nsets_written;
      write_lgi(prev_nsets_written);
      TIMESTEP base_frame = window->base_frame;
      write_lgi(base_frame);

      // Write m_n_valid_sets for meas windows started via monitors
      TIMESTEP n_empty_cyclic_buffer_slots = window->sri_file? window->sri_file->n_empty_cyclic_buffer_slots() : -1; // -1 if window is not started yet
      write_lgi(n_empty_cyclic_buffer_slots);
      TIMESTEP first_set_to_write_in_file = window->first_set_to_write_in_file;
      write_lgi(first_set_to_write_in_file);

      if (clear_timestep >= 0 && clear_timestep < solver_timestep) {
        sINT8 some_meas_cell_spans_procs = window->m_some_meas_cell_spans_procs;
        write_lgi(some_meas_cell_spans_procs);

        sINT16 n_sp_variables; // the SPs may collect extra variables (e.g. for std-dev measurements)
        lgi_read(sp_streams[window->m_master_sp], n_sp_variables);
        write_lgi(n_sp_variables);
        sINT16 n_ckpt_variables = window->n_variables;
        write_lgi(n_ckpt_variables);

        if (n_sp_variables > N_VARS_BUFFER) {
          sp_meas_cell_buffer.resize(n_sp_variables);
          sp_meas_cell = &sp_meas_cell_buffer[0];
        }
        asINT32 sp_meas_cell_size = n_sp_variables * sizeof(SP_MEAS_CELL_VAR);

        sINT16 header_len;
        lgi_read(sp_streams[window->m_master_sp], header_len);
        write_lgi(header_len);

        const asINT32 BUFLEN = 1024;
        char buf[BUFLEN];
        while (header_len > 0) {        
          asINT32 n = MIN(header_len, BUFLEN);
          lgi_read(sp_streams[window->m_master_sp], buf, n);
          write_lgi(buf, n);
          header_len -= n;
        }

        STP_MEAS_CELL_INDEX n_ckpt_meas_cells = window->n_meas_cells;
        write_lgi(n_ckpt_meas_cells);

        sprintf(status, "%s (Full checkpoint: %s)",
                cp_status, window->output_filename);
        cp_jobctl_output_status(status);

        dFLOAT one_over_n_cells = 1.0 / n_ckpt_meas_cells;
        asINT32 percent_cells_processed = 0;
        const asINT32 N_CELLS_PER_STATUS = 32 * 1024;
        asINT32 next_cell_status = N_CELLS_PER_STATUS;
        
        if (!some_meas_cell_spans_procs) {
          // number of SP meas cells per meas cell not written to ckpt file (always equal to 1)
          for (STP_MEAS_CELL_INDEX i = 0; i < window->n_stationary_meas_cells; i++) {
            sCP_MEAS_WINDOW::uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &window->m_sp_meas_cell_refs[i];
            if (ref_or_list->is_list())
              msg_internal_error("Full checkpoint: meas window \"%s\" is has no meas cells that span processor boundaries"
                                 " yet it has a list of SP meas cell refs for meas cell %d.",
                                 window->output_filename, i);
          
            sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = ref_or_list->ref();
            STP_PROC                           sp  = ref.sp();
            if (sp >= 0)
              lgi_read(sp_streams[sp], sp_meas_cell, sp_meas_cell_size);
            write_lgi(sp_meas_cell, sp_meas_cell_size);

            asINT32 percent_cells = (i+1) * (100.0 * one_over_n_cells);
            if (percent_cells > percent_cells_processed
                && i >= next_cell_status) {
              percent_cells_processed = percent_cells;
              next_cell_status = i + N_CELLS_PER_STATUS;
              sprintf(status, "%s (Full checkpoint: %s: %d%% complete)",
                      cp_status, window->output_filename, percent_cells_processed);
              cp_jobctl_output_status(status);
            }
          }
        } else {
          // number of SP meas cells per meas cell written to ckpt file
          for (STP_MEAS_CELL_INDEX i = 0; i < window->n_stationary_meas_cells; i++) {
            sCP_MEAS_WINDOW::uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &window->m_sp_meas_cell_refs[i];

            if (ref_or_list->is_list()) {
              sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF_LIST_ELT *list           = ref_or_list->list();
              STP_PROC                                    n_sp_meas_cells = 0;
              do {
                n_sp_meas_cells++;
                list = list->m_next;
              } while (list != NULL);

              write_lgi(n_sp_meas_cells);

              list = ref_or_list->list(); // reset to beginning of list
              do {
                sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = list->m_ref;
                STP_PROC                           sp  = ref.sp();
                if (sp >= 0)
                  lgi_read(sp_streams[sp], sp_meas_cell, sp_meas_cell_size);
                write_lgi(sp_meas_cell, sp_meas_cell_size);
                list = list->m_next;
              } while (list != NULL);
            } else {
              sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = ref_or_list->ref();
              STP_PROC                           sp  = ref.sp();
              STP_PROC                           n_sp_meas_cells = 1;
              if (sp >= 0)
                lgi_read(sp_streams[sp], sp_meas_cell, sp_meas_cell_size);
              write_lgi(n_sp_meas_cells);
              write_lgi(sp_meas_cell, sp_meas_cell_size);
            }

            asINT32 percent_cells = (i+1) * (100.0 * one_over_n_cells);
            if (percent_cells > percent_cells_processed
                && i >= next_cell_status) {
              percent_cells_processed = percent_cells;
              next_cell_status = i + N_CELLS_PER_STATUS;
              sprintf(status, "%s (Full checkpoint: %s: %d%% complete)",
                      cp_status, window->output_filename, percent_cells_processed);
              cp_jobctl_output_status(status);
            }
          }
        }
        // now checkpoint the moving meas cell data
        if (window->n_moving_meas_cells > 0) {
          sp_meas_cell_buffer.resize(n_sp_variables+1); // the extra +1 is for the meas_index/ref_count
          sp_meas_cell = &sp_meas_cell_buffer[0];
          asINT32 sp_meas_cell_size = (n_sp_variables+1) * sizeof(SP_MEAS_CELL_VAR);
          ccDOTIMES(sp, total_sps) {
            STP_MEAS_CELL_INDEX n_meas_cells_per_sp;
            lgi_read(sp_streams[sp], &n_meas_cells_per_sp, sizeof(STP_MEAS_CELL_INDEX));
            STP_PROC ckpt_sp = sp;
            write_lgi(ckpt_sp);
            write_lgi(n_meas_cells_per_sp);
            if (n_meas_cells_per_sp == 0)
              continue;
            lgi_read(sp_streams[sp], &sp_meas_cell_size, sizeof(asINT32));
            if (sp_meas_cell_size > sp_meas_cell_buffer.size() * sizeof(SP_MEAS_CELL_VAR)) {
              sp_meas_cell_buffer.resize(sp_meas_cell_size/sizeof(SP_MEAS_CELL_VAR) + 1);
              sp_meas_cell = &sp_meas_cell_buffer[0];
            }
            ccDOTIMES(n, n_meas_cells_per_sp) {
              lgi_read(sp_streams[sp], sp_meas_cell, sp_meas_cell_size); 
              write_lgi(sp_meas_cell, sp_meas_cell_size);
            }
          }
        }
      }
    } else {
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
      //For checkpointing of trajectory windows, a checkpoint table entry needs to be added to the meas file which
      //contains the information needed to rewind the PMR file once the simulation is resumed. Since there may be
      //multiple entries in the PMR table, the index to the relevant record is stored in the checkpoint file here.
      CP_TRAJECTORY_WINDOW trajectory_window = (CP_TRAJECTORY_WINDOW)window;
      //The table entry in the PMR file is created later after synchronize_trajectory_window_data()
      //has been called. Assuming that occurs successfully, set what will be the new index will be here.
      sINT32 checkpoint_table_index = trajectory_window->pmr_file().GetNumberOfCheckPoints();
      write_lgi(checkpoint_table_index);
//#endif
    }
  }
}

#ifndef _EXA_HPMPI
size_t cp_meas_win_data_len (TIMESTEP ckpt_timestep)
{
  size_t sz = sizeof(size_t);          // Length of the own buffer
  sz += sizeof(STP_MEAS_WINDOW_INDEX); // n_ckpt_meas_windows

  size_t active_win_idx = 0;
  DO_CP_MEAS_WINDOWS(window) {

    if (window->is_average_mme) {
      continue;
    }

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    if (!window->m_is_particle_trajectory_window) {
//#endif
      // No need to checkpoint this window if the data will simply be cleared after the checkpoint-restore,
      // or if we have passed the window's end time. We must read clear_timestep from an SP because
      // window->m_clear_timestep may not be the same as the SP's clear_timestep because the CP may be
      // servicing the checkpoint before a meas window frame.
      sz += sizeof(TIMESTEP);//CP: prev_nsets_written
      sz += sizeof(TIMESTEP);//CP: base_frame
      sz += sizeof(TIMESTEP);//CP: n_empty_cyclic_buffer_slots
      sz += sizeof(TIMESTEP);//CP: first_set_to_write_in_file
      sz += sizeof(TIMESTEP);//CP: clear_step

      TIMESTEP base_ckpt_timestep = ckpt_timestep * cp_info.n_user_base_steps;
      TIMESTEP solver_timestep = base_ckpt_timestep;
      TIMESTEP clear_step = essential_data[active_win_idx].num_clear_steps;
      if(cp_info.is_conduction && cp_info.is_flow) {
      if(window->is_cond_window())
        solver_timestep = cp_info.convert_to_ts_cond(base_ckpt_timestep);
      else
        solver_timestep = cp_info.convert_to_ts_flow(base_ckpt_timestep);

    }
      if (clear_step >= 0 && clear_step < solver_timestep) {
        sINT8 some_meas_cell_spans_procs = window->m_some_meas_cell_spans_procs;
        sz += sizeof(sINT8); //CP: some_meas_cell_spans_procs
        sz += sizeof(sINT16);//CP: n_sp_variables
        sz += sizeof(sINT16);//CP: n_ckpt_variables
        sz += sizeof(sINT16);//CP: header_len
        sz += sizeof(STP_MEAS_CELL_INDEX);//CP: n_ckpt_meas_cells

        if (some_meas_cell_spans_procs)
        {
          sz += sizeof(STP_PROC) * window->n_stationary_meas_cells; // CP: n_sp_meas_cells
          for (STP_MEAS_CELL_INDEX i = 0; i < window->n_stationary_meas_cells; i++)
          {
            sCP_MEAS_WINDOW::uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &window->m_sp_meas_cell_refs[i];
            if (ref_or_list->is_list())
            {
              sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF_LIST_ELT *list = ref_or_list->list();
              do
              {
                sz += sizeof(STP_PROC); // CP: SP Id
                list = list->m_next;
              } while (list != NULL);
            }
            else
              sz += sizeof(STP_PROC);  // CP: SP id
          }
        }
        else
          sz += sizeof(STP_PROC) * window->n_stationary_meas_cells; // CP: SP id
      }
      active_win_idx++;
    } else {
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
      sz += sizeof(sINT32); //CP: checkpoint_table_index
//#endif
    }
  }
  return sz;
}


// write_meas_windows assumes that the SPs send meas cells to the CP in global index order.
template <bool SAVE_CP>
static VOID write_meas_windows_map_generator(TIMESTEP ckpt_timestep, cSTRING cp_status, sCKPT_BUFFER& buff)
{
  TIMESTEP base_ckpt_timestep = ckpt_timestep * cp_info.n_user_base_steps;
  TIMESTEP solver_timestep = base_ckpt_timestep;
  STP_MEAS_WINDOW_INDEX n_ckpt_meas_windows = cp_info.n_meas_windows();
  char status[256];

  if constexpr (SAVE_CP)
  {
    size_t len = cp_meas_win_data_len(ckpt_timestep);
    buff.allocate(len);
    buff.write(&len);
    buff.write(&n_ckpt_meas_windows);
  }

  size_t active_win_idx = 0;
  DO_CP_MEAS_WINDOWS(window) {

    if (window->is_average_mme) {
      continue;
    }

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    if (!window->m_is_particle_trajectory_window) {
//#endif
      TIMESTEP prev_nsets_written = essential_data[active_win_idx].nframes_sent + window->initial_prev_nsets_written;
      TIMESTEP base_frame = window->base_frame;
      TIMESTEP n_empty_cyclic_buffer_slots = window->sri_file? window->sri_file->n_empty_cyclic_buffer_slots() : -1; // -1 if window is not started yet
      TIMESTEP first_set_to_write_in_file = window->first_set_to_write_in_file;

      // Fron now in advance, some specific info coming from the master SP of the window will be used,
      // so blocking until receive info from master SP.
      MPI_Wait(&essential_data_request[active_win_idx], MPI_STATUS_IGNORE);
      TIMESTEP clear_step = essential_data[active_win_idx].num_clear_steps;
      
      if constexpr (SAVE_CP)
      {
        // No need to checkpoint this window if the data will simply be cleared after the checkpoint-restore,
        // or if we have passed the window's end time. We must read clear_timestep from an SP because
        // window->m_clear_timestep may not be the same as the SP's clear_timestep because the CP may be
        // servicing the checkpoint before a meas window frames
        buff.write(&clear_step);
        buff.write(&prev_nsets_written); // CP + SP (send)
        buff.write(&base_frame);//CP
        buff.write(&n_empty_cyclic_buffer_slots);//CP
        buff.write(&first_set_to_write_in_file);//CP
      }
      if(cp_info.is_conduction && cp_info.is_flow) {
        if(window->is_cond_window())
          solver_timestep = cp_info.convert_to_ts_cond(base_ckpt_timestep);
        else
          solver_timestep = cp_info.convert_to_ts_flow(base_ckpt_timestep);

      }
      // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(), "DEBUG write_meas_windows_map_generator computeWin=%d \n",clear_step >= 0 && clear_step < solver_timestep);

      if (clear_step >= 0 && clear_step < solver_timestep) {
        
        sprintf(status, "%s (Full checkpoint: %s)",
                cp_status, window->output_filename);
        cp_jobctl_output_status(status);

        // Initializing maps for measurement windows
        if constexpr (!SAVE_CP)
          window->cp_sp_comm.allocate_maps();

        sINT8 some_meas_cell_spans_procs = window->m_some_meas_cell_spans_procs;
        sINT16 n_ckpt_variables = window->n_variables;
        sINT16 n_sp_variables = essential_data[active_win_idx].n_sp_variables;
        STP_MEAS_CELL_INDEX n_ckpt_meas_cells = window->n_meas_cells;
        sINT16 header_len = essential_data[active_win_idx].header_len;
        asINT32 sp_meas_cell_size = n_sp_variables * sizeof(SP_MEAS_CELL_VAR);

        if constexpr (SAVE_CP)
        {
          buff.write(&some_meas_cell_spans_procs);//CP
          buff.write(&n_sp_variables); // CP
          buff.write(&n_ckpt_variables);//CP maybe unused
          buff.write(&header_len); // CP
          buff.write(&n_ckpt_meas_cells);//CP
        }
        else
        {
          // Create the first element to the master, by the moment, empty
          window->cp_sp_comm.append_to_map(window->m_master_sp, header_len);
        }

        dFLOAT one_over_n_cells = 1.0 / n_ckpt_meas_cells;
        asINT32 percent_cells_processed = 0;
        const asINT32 N_CELLS_PER_STATUS = 32 * 1024;
        asINT32 next_cell_status = N_CELLS_PER_STATUS;
        
        if (!some_meas_cell_spans_procs) {
          // number of SP meas cells per meas cell not written to ckpt file (always equal to 1)
          for (STP_MEAS_CELL_INDEX i = 0; i < window->n_stationary_meas_cells; i++) {
            sCP_MEAS_WINDOW::uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &window->m_sp_meas_cell_refs[i];
            if (ref_or_list->is_list())
              msg_internal_error("Full checkpoint: meas window \"%s\" is has no meas cells that span processor boundaries"
                                 " yet it has a list of SP meas cell refs for meas cell %d.",
                                 window->output_filename, i);
          
            sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF ref = ref_or_list->ref();
            STP_PROC                           sp  = ref.sp();
            if constexpr (SAVE_CP)
              buff.write(&sp);
            else
              if (sp >= 0)
                window->cp_sp_comm.append_to_map(sp, sp_meas_cell_size);
          }
        } else {
          // number of SP meas cells per meas cell written to ckpt file
          for (STP_MEAS_CELL_INDEX i = 0; i < window->n_stationary_meas_cells; i++) {
            sCP_MEAS_WINDOW::uSP_MEAS_CELL_REF_OR_LIST *ref_or_list = &window->m_sp_meas_cell_refs[i];
            if (ref_or_list->is_list())
            {
              sCP_MEAS_WINDOW::sSP_MEAS_CELL_REF_LIST_ELT *list = ref_or_list->list();
              if constexpr (SAVE_CP)
              {
                STP_PROC n_sp_meas_cells = 0;
                do {
                  n_sp_meas_cells++;
                  list = list->m_next;
                } while (list != NULL);
                // Save number of SPs into the HDF5 file
                buff.write(&n_sp_meas_cells); // CP
                // Saving SPs
                list = ref_or_list->list();
                do {
                  STP_PROC sp = list->m_ref.sp();
                  buff.write(&sp);
                  list = list->m_next;
                } while (list != NULL);
              }
              else
              {
                do {
                  STP_PROC sp = list->m_ref.sp();
                  if (sp >= 0)
                    window->cp_sp_comm.append_to_map(sp, sp_meas_cell_size);
                  list = list->m_next;
                } while (list != NULL);
              }
            }
            else
            {
              STP_PROC sp = ref_or_list->ref().sp();
              // Save number of SPs into the file or extend the map
              if constexpr (SAVE_CP)
              {
                STP_PROC n_sp_meas_cells = 1;
                buff.write(&n_sp_meas_cells); // CP
                buff.write(&sp);
              }
              else
                if (sp >= 0)
                  window->cp_sp_comm.append_to_map(sp, sp_meas_cell_size);
            }
          }
        }
        // MOVING MEAS CELLS APPENDED IN SPs
      
        // Openning communications (non-blocking send of maps)
        if constexpr (!SAVE_CP)
        {
          for (STP_PROC sp = 0; sp < total_sps; ++sp)
          { 
            bool sp_has_meas_cells = window->m_sp_n_meas_cells[sp]+ window->n_moving_meas_cells > 0;
            // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"sproc=%d cells %d moving %d\n", sp, window->m_sp_n_meas_cells[sp], 
            //                                         window->n_moving_meas_cells);
            window->cp_sp_comm.open_comm(sp, sp_has_meas_cells);
          }
        }
      }
      
      // Update counter of active windows
      active_win_idx++;
      
    } else {
      if constexpr (SAVE_CP)
      {
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
        //For checkpointing of trajectory windows, a checkpoint table entry needs to be added to the meas file which
        //contains the information needed to rewind the PMR file once the simulation is resumed. Since there may be
        //multiple entries in the PMR table, the index to the relevant record is stored in the checkpoint file here.
        CP_TRAJECTORY_WINDOW trajectory_window = (CP_TRAJECTORY_WINDOW)window;
        //The table entry in the PMR file is created later after synchronize_trajectory_window_data()
        //has been called. Assuming that occurs successfully, set what will be the new index will be here.
        sINT32 checkpoint_table_index = trajectory_window->pmr_file().GetNumberOfCheckPoints();
        buff.write(&checkpoint_table_index);
//#endif
      }
    }
  }

  // Waiting for communications of the maps and free memory once the data has been successfully sent.
  if constexpr (!SAVE_CP)
  {
    DO_CP_MEAS_WINDOWS(window)
      window->cp_sp_comm.close_comm();
  }

}

static size_t get_coupling_model_pt_pf_ratio_history_len()
{
  size_t len  = sizeof(size_t);
  len += sizeof(sINT16);
  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
 
  ccDOTIMES(i,cp_info.n_surface_couplings) {
    asINT32 n_phases = coupling_model_desc->m_coupling_phase_ratios.size();
    len += sizeof(asINT32);
    ccDOTIMES(phase_index, n_phases) {
      asINT32 n_records = coupling_model_desc->m_coupling_phase_ratios[phase_index].size();
      len += sizeof(asINT32);
      ccDOTIMES(record_index, n_records) {
        len += sizeof(sPT_PF_RATIO_RECORD);
      }
    }
  }
  return len;
}
static VOID write_coupling_model_pt_pf_ratio_history( sCKPT_BUFFER& buff)
{
  size_t len = get_coupling_model_pt_pf_ratio_history_len();
  buff.allocate(len);
  buff.write(&len);
  sINT16  n_models = cp_info.n_surface_couplings;
  buff.write(&n_models);

  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
 
  ccDOTIMES(i,cp_info.n_surface_couplings) {
    // maybe dump vectors directly?
    asINT32 n_phases = coupling_model_desc->m_coupling_phase_ratios.size();
    buff.write(&n_phases);
    ccDOTIMES(phase_index, n_phases) {
      asINT32 n_records = coupling_model_desc->m_coupling_phase_ratios[phase_index].size();
      buff.write(&n_records);
      ccDOTIMES(record_index, n_records) {
        sPT_PF_RATIO_RECORD ratio_record = coupling_model_desc->m_coupling_phase_ratios[phase_index][record_index];
        buff.write(&ratio_record);
      }
    }
  }
}
#endif

VOID write_coupling_model_pt_pf_ratio_history()
{
  cp_get_output_file_pos(cp_stream, CKPT_PT_PF_RATIO_POS);
  LGI_TAG pt_pf_ratio_tag;
  pt_pf_ratio_tag.id   = DGF_PT_PF_RATIO_TAG;
  pt_pf_ratio_tag.length = 0;

  write_lgi_variable_head(pt_pf_ratio_tag);
  sINT16  n_models = cp_info.n_surface_couplings;
  write_lgi(n_models);

  COUPLING_MODEL_DESC coupling_model_desc = cp_info.coupling_model_descs;
 
  ccDOTIMES(i,cp_info.n_surface_couplings) {
    //if (!coupling_model_desc->couple_during_sim_p)
    //  continue;
    asINT32 n_phases = coupling_model_desc->m_coupling_phase_ratios.size();
    write_lgi(n_phases);
    ccDOTIMES(phase_index, n_phases) {
      asINT32 n_records = coupling_model_desc->m_coupling_phase_ratios[phase_index].size();
      write_lgi(n_records);
      ccDOTIMES(record_index, n_records) {
        sPT_PF_RATIO_RECORD ratio_record = coupling_model_desc->m_coupling_phase_ratios[phase_index][record_index];
        write_lgi(ratio_record.timestep);
        write_lgi(ratio_record.ratio_scale_factor);
      }
    }
  }
}

#ifndef _EXA_HPMPI
static size_t get_monitors_len()
{
  size_t len =  sizeof(size_t)  + sizeof(sINT16);
  sINT16  n_monitors = cp_info.monitors.size();
  // Write monitor data
  ccDOTIMES(i, n_monitors) {
    MONITOR monitor = cp_info.monitors[i];
    len += sizeof(sINT8);
    len += 2* sizeof(cBOOLEAN);
    TIMESTEP n_msa_signals = monitor->m_msa.SignalSize();
    TIMESTEP n_monitor_signals = monitor->m_timesteps.size();
    // n_msa_signals could be different from n_monitor_signals for monitors
    // associated with meas windows started by other monitors.
    // If that is the case, then we should store monitor->m_signal
    if (n_msa_signals == n_monitor_signals) { // regular monitor, signal is stored in msa
      len += sizeof(TIMESTEP);
      ccDOTIMES(j, n_msa_signals) {
        len += sizeof(sFLOAT);
        len += sizeof(TIMESTEP);
      }
    } else { // monitor which is not started yet, signal is collected in m_signal[]
      len += sizeof(TIMESTEP);
      ccDOTIMES(j, n_monitor_signals) {
        len += sizeof(sFLOAT);
        len += sizeof(TIMESTEP);
      }
    }
  }
  return len;
}

static VOID write_monitors( sCKPT_BUFFER& buff)
{
  size_t len = get_monitors_len();
  buff.allocate(len);
  buff.write(&len);
  sINT16  n_monitors = cp_info.monitors.size();
  buff.write(&n_monitors);
  // Write monitor data
  ccDOTIMES(i, n_monitors) {
    MONITOR monitor = cp_info.monitors[i];
    sINT8 converged_and_ended_sim_before_ckpt = monitor->m_converged_and_ended_sim_before_ckpt;
    buff.write(&converged_and_ended_sim_before_ckpt);

    // If the monitor autostop is enabled or disabled by exaqsub option, the flag should be checkpointed for resume
    cBOOLEAN disabled_autostop_by_on_off = monitor->m_disabled_autostop_by_on_off;
    buff.write(&disabled_autostop_by_on_off);
    cBOOLEAN enabled_autostop_by_on_off = monitor->m_enabled_autostop_by_on_off;
    buff.write(&enabled_autostop_by_on_off);

    TIMESTEP n_msa_signals = monitor->m_msa.SignalSize();
    TIMESTEP n_monitor_signals = monitor->m_timesteps.size();
    // n_msa_signals could be different from n_monitor_signals for monitors
    // associated with meas windows started by other monitors.
    // If that is the case, then we should store monitor->m_signal
    if (n_msa_signals == n_monitor_signals) { // regular monitor, signal is stored in msa
      buff.write(&n_msa_signals);
#if DEBUG_MONITOR_CKPT
      msg_print("Write %d signals for monitor %s", n_msa_signals, monitor->m_name);
#endif
      ccDOTIMES(j, n_msa_signals) {
        sFLOAT signal = monitor->m_msa.Signal(j);
        buff.write(&signal);
        buff.write(&monitor->m_timesteps[j]);
#if DEBUG_MONITOR_CKPT      
        msg_print("time %d signal %f", monitor->m_timesteps[j], signal);
#endif
      }
    } else { // monitor which is not started yet, signal is collected in m_signal[]
      buff.write(&n_monitor_signals);
#if DEBUG_MONITOR_CKPT
      msg_print("Write %d signals for monitor %s", n_monitor_signals, monitor->m_name);
#endif
      ccDOTIMES(j, n_monitor_signals) {
        sFLOAT signal = monitor->m_signal[j];
        buff.write(&signal);
        buff.write(&monitor->m_timesteps[j]);
#if DEBUG_MONITOR_CKPT      
        msg_print("time %d signal %f", monitor->m_timesteps[j], signal);
#endif
      }
    }
  }
}
#endif

static VOID write_monitors()
{
  cp_get_output_file_pos(cp_stream, CKPT_MONITORS_POS);
  LGI_TAG monitor_tag;
  monitor_tag.id   = DGF_MONITORS_TAG;
  monitor_tag.length = 0;

  write_lgi_variable_head(monitor_tag);
  sINT16  n_monitors = cp_info.monitors.size();
  write_lgi(n_monitors);
  // Write monitor data
  ccDOTIMES(i, n_monitors) {
    MONITOR monitor = cp_info.monitors[i];
    sINT8 converged_and_ended_sim_before_ckpt = monitor->m_converged_and_ended_sim_before_ckpt;
    write_lgi(converged_and_ended_sim_before_ckpt);

    // If the monitor autostop is enabled or disabled by exaqsub option, the flag should be checkpointed for resume
    cBOOLEAN disabled_autostop_by_on_off = monitor->m_disabled_autostop_by_on_off;
    write_lgi(disabled_autostop_by_on_off);
    cBOOLEAN enabled_autostop_by_on_off = monitor->m_enabled_autostop_by_on_off;
    write_lgi(enabled_autostop_by_on_off);

    TIMESTEP n_msa_signals = monitor->m_msa.SignalSize();
    TIMESTEP n_monitor_signals = monitor->m_timesteps.size();
    // n_msa_signals could be different from n_monitor_signals for monitors
    // associated with meas windows started by other monitors.
    // If that is the case, then we should store monitor->m_signal
    if (n_msa_signals == n_monitor_signals) { // regular monitor, signal is stored in msa
      write_lgi(n_msa_signals);
#if DEBUG_MONITOR_CKPT
      msg_print("Write %d signals for monitor %s", n_msa_signals, monitor->m_name);
#endif
      ccDOTIMES(j, n_msa_signals) {
        sFLOAT signal = monitor->m_msa.Signal(j);
        write_lgi(&signal, sizeof(signal));
        write_lgi(monitor->m_timesteps[j]);
#if DEBUG_MONITOR_CKPT      
        msg_print("time %d signal %f", monitor->m_timesteps[j], signal);
#endif
      }
    } else { // monitor which is not started yet, signal is collected in m_signal[]
      write_lgi(n_monitor_signals);
#if DEBUG_MONITOR_CKPT
      msg_print("Write %d signals for monitor %s", n_monitor_signals, monitor->m_name);
#endif
      ccDOTIMES(j, n_monitor_signals) {
        sFLOAT signal = monitor->m_signal[j];
        write_lgi(&signal, sizeof(signal));
        write_lgi(monitor->m_timesteps[j]);
#if DEBUG_MONITOR_CKPT      
        msg_print("time %d signal %f", monitor->m_timesteps[j], signal);
#endif
      }
    }
  }
}


/*--------------------------------------------------------------------------*
 * Main functions
 *--------------------------------------------------------------------------*/

/* OUTPUT_STREAM may be NULL if there was an error opening it. If
 * so, we must consume all the data from the SPs anyway.
 */
static VOID write_full_ckpt_internal(TIMESTEP ckpt_timestep, cSTRING cp_status)
{
  init_output_file_pos();
  write_header();
  output_toc_start();

  write_checkpoint_info(ckpt_timestep);
  
  write_audit_trail();

  write_file_parentage();

  write_random_seed();
  write_eqn_random_seed();
  
  // Write synthetic turbulent velocity information
  write_turb_synth_info_state();

  write_radiation_tm_info();

  write_cdi_meas_windows();

  write_ublks(cp_status);
  write_surfels(cp_status);

  write_meas_windows(ckpt_timestep, cp_status);

  char status[256];
  sprintf(status, "%s (Full checkpoint : Writing lrf state)", cp_status);
  cp_jobctl_output_status(status);
  write_global_nirf_state();
  write_lrf_state();
  write_movb_state();

  if(cp_info.lattice_type == STP_LATTICE_D19) {
    write_thermal_accel_state();
  }

  write_fan_descs();

  if (cp_info.num_averaged_contacts > 0) {
    write_averaged_contacts();
  }

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  sprintf(status, "%s (Full checkpoint : Writing particle property)", cp_status);
  cp_jobctl_output_status(status);
  write_emitter_state();
  write_random_particle_property_state();
//#endif
  sprintf(status, "%s (Finishing full checkpoint)", cp_status);
  cp_jobctl_output_status(status);

  // Check that we are at EOF on all the SP streams
  verify_sp_eof();
}

BOOLEAN write_full_ckpt_to_lgi(cSTRING filename, TIMESTEP ckpt_timestep, BOOLEAN precious, cSTRING cp_status)
{
  BOOLEAN retry_writes_until_success = precious;
  cp_stream = lgi_open_checkpoint_stream(LGI_FILE_NO_ERROR_TYPE, filename, retry_writes_until_success);
    
  if (!cp_stream) {
    msg_warn("Error while attempting to open checkpoint file \"%s\""
	     " for checkpoint at timestep %ld."
	     " No checkpoint will be written for this timestep.\n"
	     "System error message was: %s",
	     filename,
	     (long)ckpt_timestep,
	     strerror(errno));
    checkpoint_aborting_p = TRUE;
    checkpoint_errno = errno;
  } 
  else {
    checkpoint_aborting_p = FALSE;
    checkpoint_errno = 0;
  }

#if 0
  if (!precious)
    lgi_set_retry_writes_mode(cp_stream, FALSE);
#endif

  sp_streams = return_sp_stream();

  /* Must consume the SP data whether we can write it to disk or not */
  write_full_ckpt_internal(ckpt_timestep, cp_status);

  // Close the SP streams first, then write monitors to ckpt file
  close_lgi_streams();

  // hpc_io::utils::Timer not_writing_lgi_timer;
  if (has_trajectory_window() && !sim_args.disable_particle_modeling) {
    synchronize_trajectory_window_data(ckpt_timestep);
    write_pri_result_buffers();
    g_trajectory_id_map.write_ckpt();

    DO_CP_MEAS_WINDOWS(window) {
      if(window->m_is_particle_trajectory_window) {    
        CP_TRAJECTORY_WINDOW trajectory_window = (CP_TRAJECTORY_WINDOW)window;
        [[maybe_unused]] sINT32 checkpoint_table_index = trajectory_window->create_checkpoint_table_entry(ckpt_timestep);
      }
    }
    
  }

  // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(), "baseline spent %f not writing ckpt-lgi file. loop.\n", not_writing_lgi_timer.getTime());
  // not_writing_lgi_timer.restart();
  // Write all meas windows with output timestep up to ckpt_timestep
  output_windows_up_to_timestep(ckpt_timestep);
  //hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"baseline spent %f not writing ckpt-lgi file. output_windows_up_to_timestep.\n", not_writing_lgi_timer.getTime());
  char status[256];
  sprintf(status, "%s (Full checkpoint : Writing adaptive coupling data)", cp_status); 
  write_coupling_model_pt_pf_ratio_history();
  sprintf(status, "%s (Full checkpoint : Writing monitors data)", cp_status); 
  write_monitors();
  sprintf(status, "%s (Full checkpoint : Writing rotational dynamics data)", cp_status); 
  write_cp_rotdyn_state();
  sprintf(status, "%s (Full checkpoint : Writing transient seeding descs)", cp_status); 
  write_transient_seed_descs();
  sprintf(status, "%s (Closing full checkpoint file)", cp_status);
  cp_jobctl_output_status(status);

  output_toc_end();

  /* Close all the streams */
  close_lgi_file();


  exa_free(sp_streams);
  sp_streams = NULL;
		 
  if (checkpoint_aborting_p) {
    if (cp_stream) {
      /** Note that the current timestep is t+1 when we're checkpointing
      *** the state of things after completing timestep t.  Hence the
      *** minus one in the msg_warn statement below.
      */
      CHARACTER exceptional_condition_msg[1024];

      sprintf(exceptional_condition_msg, 
	      "Error while writing full checkpoint after timestep %ld to file \"%s\"."
	      " The old checkpoint (if any) will not be updated.\n"
	      "System error message was: %s",
	      (long)sim_status_timestep(cp_info.cp_status.status) - 1,
	      filename,
	      strerror(checkpoint_errno));
      jobctl_server_report_exceptional_condition(exceptional_condition_msg);
      remove(filename);
    }
  }
  cp_stream = NULL;

  return !checkpoint_aborting_p;
}

#ifndef _EXA_HPMPI
void log_shob_write_progress(const STP_SHOB_ID total_shobs, cSTRING shob_flavor, cSTRING cp_status)
{
  char status[256];
  MPI_Request written_shobs_request = MPI_REQUEST_NULL;
  uint64_t written_shobs = 0;
  constexpr uint64_t zero = 0;
  dFLOAT hundred_over_n_shobs = 100.0 / total_shobs;

  while (written_shobs != total_shobs) {
    // we cannot mix MPI_IReduce and MPI_Reduce, so we emulate it by just waiting for the result
    MPI_Ireduce(&zero, &written_shobs, 1, MPI_UINT64_T, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm, 
                  &written_shobs_request);
    MPI_Wait(&written_shobs_request, MPI_STATUS_IGNORE);
    asINT32 percent_shobs = written_shobs * hundred_over_n_shobs;

    sprintf(status, "%s (Full checkpoint: %s: %d%% complete)", cp_status,
              shob_flavor, percent_shobs);
    cp_jobctl_output_status(status);
  }
}
BOOLEAN write_full_ckpt_to_hdf5(cSTRING filename, TIMESTEP ckpt_timestep, BOOLEAN precious, cSTRING cp_status)
{
  MPI_Bcast((void*)(filename), FILENAME_MAXLEN, MPI_CHAR, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
  hpc_io::utils::Timer hdf5_timer;
  hpc_io::utils::Timer timer; 
  double extra_stuff_time=0.;
//do other things while waiting for SPs to close hdf5 file
  if (has_trajectory_window() && !sim_args.disable_particle_modeling) {
    synchronize_trajectory_window_data(ckpt_timestep);
    // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"synchronize_trajectory_window_data needed %f seconds\n", timer.getTime());
    extra_stuff_time += timer.getTime();
    timer.restart();
    write_pri_result_buffers();
    // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"write_pri_result_buffers needed %f seconds\n", timer.getTime());
    extra_stuff_time += timer.getTime();
    timer.restart();
  }
  // Open communication with SPs to receive essential data required to save the meas win
  recieve_meas_win_essential_data();
  // Initialize windows and create the maps. Sending them to each SP
  {
    sCKPT_BUFFER dummy_buffer;
    write_meas_windows_map_generator<false>(ckpt_timestep, cp_status, dummy_buffer);
  }
  // Write all meas windows with output timestep up to ckpt_timestep
  output_windows_up_to_timestep(ckpt_timestep);
  // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"output_windows_up_to_timestep needed %f seconds\n", timer.getTime());
  extra_stuff_time += timer.getTime();
  timer.restart();
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Extra stuff done before needed %f seconds\n", extra_stuff_time);

  if (cp_info.is_conduction) {
    msg_error("Cannot checkpoint conduction cases in parallel yet!");
  }

  if(cp_info.num_ublks[STP_FLOW_REALM] > 0)
    log_shob_write_progress(cp_info.num_ublks[STP_FLOW_REALM], "ublks", cp_status);
  // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"log_shob_write_progress ublks needed %f seconds\n", timer.getTime());
  timer.restart();
  if(cp_info.num_surfels[STP_FLOW_REALM] > 0)
    log_shob_write_progress(cp_info.num_surfels[STP_FLOW_REALM], "surfels", cp_status);
  // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"log_shob_write_progress surfels needed %f seconds\n", timer.getTime());
  timer.restart();


  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Barrier waiting for SPs to close writers\n");
  MPI_Barrier(eMPI_sp_cp_comm);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Barrier needed %f seconds\n", timer.getTime());
  timer.restart();
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Starting write\n");
  MPI_Comm dummy = MPI_COMM_NULL;
    
  // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"filename %s\n", filename);
  std::unique_ptr<hpc_io::HDF5_writer> writer = std::make_unique<hpc_io::HDF5_writer>(dummy, 
                                                                                      sim_args.parallel_io_max_buffer_size);
  writer->initialize();
  //CP writer opens ckpt file previously created by SPs once they've closed it.
  writer->open_file(filename);
  // hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Writer intialized and open file done\n");
  timer.restart();
  sCKPT_BUFFER buffer;
  write_checkpoint_info(buffer, ckpt_timestep);
  assert(buffer.m_capacity == buffer.m_offset && " not copied all data");
  writer->create_ckpt_info_group();
  writer->serial_write(buffer.m_buffer, buffer.m_offset);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Checkpoint_info written [%s] in %f seconds \n",
          hpc_io::utils::bytesToHuman(buffer.m_offset).c_str(), timer.getTime());

  timer.restart();
  write_audit_trail(buffer);
    assert(buffer.m_capacity == buffer.m_offset && " not copied all data");
  writer->create_audit_trail_group();
  writer->serial_write(buffer.m_buffer, buffer.m_offset);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Audit_trail written [%s] in %f seconds \n",
      hpc_io::utils::bytesToHuman(buffer.m_offset).c_str(), timer.getTime());

  timer.restart();
  write_file_parentage(buffer);
    assert(buffer.m_capacity == buffer.m_offset && " not copied all data");
  writer->create_file_parentage_group();
  writer->serial_write(buffer.m_buffer, buffer.m_offset);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"File parentage written [%s] in %f seconds \n",
      hpc_io::utils::bytesToHuman(buffer.m_offset).c_str(), timer.getTime());

  writer->create_cdi_meas_windows_group();
  write_cdi_meas_windows(buffer);
    assert(buffer.m_capacity == buffer.m_offset && " not copied all data");
  writer->serial_write(buffer.m_buffer, buffer.m_offset);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"CDI meas windows data written [%s] in %f seconds \n",
      hpc_io::utils::bytesToHuman(buffer.m_offset).c_str(), timer.getTime());

  char status[256];
  timer.restart();
  writer->create_cp_meas_windows_group();
  write_meas_windows_map_generator<true>(ckpt_timestep, cp_status, buffer);
    assert(buffer.m_capacity == buffer.m_offset && " not copied all data");
  writer->serial_write(buffer.m_buffer, buffer.m_offset);
  sprintf(status, "%s (Full checkpoint: all windows completed)", cp_status);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Meas window data written [%s] in %f seconds \n",
          hpc_io::utils::bytesToHuman(buffer.m_offset).c_str(), timer.getTime());

//This can't be done before or checkpoint_table_index is incremented before writing it.
  if (has_trajectory_window() && !sim_args.disable_particle_modeling) {
    DO_CP_MEAS_WINDOWS(window) {
      if(window->m_is_particle_trajectory_window) {    
        CP_TRAJECTORY_WINDOW trajectory_window = (CP_TRAJECTORY_WINDOW)window;
        [[maybe_unused]] sINT32 checkpoint_table_index = trajectory_window->create_checkpoint_table_entry(ckpt_timestep);
      }
    }
    hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"DO_CP_MEAS_WINDOWS(window) %f seconds\n", timer.getTime());
  }


  sprintf(status, "%s (Full checkpoint : Writing particle property)", cp_status);
  writer->create_cp_emitter_state_group();
  write_cp_emitter_state(buffer);
    assert(buffer.m_capacity == buffer.m_offset && " not copied all data");
  writer->serial_write(buffer.m_buffer, buffer.m_offset);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Emitter_state written [%s] in %f seconds \n",
          hpc_io::utils::bytesToHuman(buffer.m_offset).c_str(), timer.getTime());

  if (has_trajectory_window() && !sim_args.disable_particle_modeling) {
    // synchronize_trajectory_window_data(ckpt_timestep);
    // write_pri_result_buffers();
    writer->create_trajectory_windows_group();
    g_trajectory_id_map.write_ckpt(buffer);
    writer->serial_write(buffer.m_buffer, buffer.m_offset);

    // DO_CP_MEAS_WINDOWS(window) {
    //   if(window->m_is_particle_trajectory_window) {    
    //     CP_TRAJECTORY_WINDOW trajectory_window = (CP_TRAJECTORY_WINDOW)window;
    //     sINT32 checkpoint_table_index = trajectory_window->create_checkpoint_table_entry(ckpt_timestep);
    //   }
    // }
  }

  // // Write all meas windows with output timestep up to ckpt_timestep
  // output_windows_up_to_timestep(ckpt_timestep);

  sprintf(status, "%s (Full checkpoint : Writing adaptive coupling data)", cp_status); 
  timer.restart();
  writer->create_coupling_model_pt_pf_ratio_history_group();
  write_coupling_model_pt_pf_ratio_history(buffer);
    assert(buffer.m_capacity == buffer.m_offset && " not copied all data");
  writer->serial_write(buffer.m_buffer, buffer.m_offset);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Coupling_model_pt_pf_ratio_history data written [%s] in %f seconds \n",
      hpc_io::utils::bytesToHuman(buffer.m_offset).c_str(), timer.getTime());

  sprintf(status, "%s (Full checkpoint : Writing monitors data)", cp_status); 
  timer.restart();
  writer->create_monitors_group();
  write_monitors(buffer);
    assert(buffer.m_capacity == buffer.m_offset && " not copied all data");
  writer->serial_write(buffer.m_buffer, buffer.m_offset);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Monitors data written [%s] in %f seconds \n",
      hpc_io::utils::bytesToHuman(buffer.m_offset).c_str(), timer.getTime());

  sprintf(status, "%s (Full checkpoint : Writing rotational dynamics data)", cp_status); 
  timer.restart();
  writer->create_cp_rotdyn_group();
  write_cp_rotdyn(buffer);
    assert(buffer.m_capacity == buffer.m_offset && " not copied all data");
  writer->serial_write(buffer.m_buffer, buffer.m_offset);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Rotdyn_state data written [%s] in %f seconds \n",
      hpc_io::utils::bytesToHuman(buffer.m_offset).c_str(), timer.getTime());

  sprintf(status, "%s (Full checkpoint : Writing transient seeding descs)", cp_status); 
  timer.restart();
  writer->create_transient_seed_descs_group();
  write_transient_seed_descs(buffer);
    assert(buffer.m_capacity == buffer.m_offset && " not copied all data");
  writer->serial_write(buffer.m_buffer, buffer.m_offset);
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Transient_seed_descs data written [%s] in %f seconds \n",
      hpc_io::utils::bytesToHuman(buffer.m_offset).c_str(), timer.getTime());

  sprintf(status, "%s (Closing full checkpoint file)", cp_status);
  cp_jobctl_output_status(status);

  buffer.deallocate();
  writer->close();
  hpc_io::utils::print<hpc_io::utils::CP>(eMPI_sp_cp_rank(),"Finished write_full_ckpt_to_hdf5 CP at %f\n",  hdf5_timer.getTime());

  // re-work this to be useful
  checkpoint_aborting_p = FALSE;
  checkpoint_errno = 0;
  return !checkpoint_aborting_p;
}
#endif
BOOLEAN write_full_ckpt(cSTRING filename, TIMESTEP ckpt_timestep, BOOLEAN precious, cSTRING cp_status)
{
  if (!sim_args.parallel_io)
  {
    return write_full_ckpt_to_lgi(filename, ckpt_timestep, precious, cp_status);
  }
  else
  {
    #ifndef _EXA_HPMPI
      return write_full_ckpt_to_hdf5(filename, ckpt_timestep, precious, cp_status);
    #else
      msg_warn("Error HPMPI does not support parallel checkpointing"
	     " No checkpoint will be written for this timestep.\n");
      checkpoint_aborting_p = TRUE;
      return !checkpoint_aborting_p;
    #endif
  }
}

