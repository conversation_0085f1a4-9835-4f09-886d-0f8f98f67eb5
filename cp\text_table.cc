#include "text_table.h"


#include <assert.h>

cTEXT_TABLE::cTEXT_TABLE(std::string table_name, std::vector<std::string> column_labels, std::vector<eVAR_TYPE> var_types) :
  m_table_name(table_name),
  m_column_labels(column_labels),
  m_var_types(var_types),
  m_num_records_printed(0),
  m_indent_level(4) {
}

cTEXT_TABLE::~cTEXT_TABLE() {
  printf("\n");
}

void cTEXT_TABLE::add_column(std::string column_label, eVAR_TYPE var_type) {
  m_column_labels.push_back(column_label);
  m_var_types.push_back(var_type);
}

void  cTEXT_TABLE::print_header() {

#if 0
  int title_sep_length = (table_width() - m_table_name.length()) / 2 - 1;
  std::string title_sep(title_sep_length, '-');
  printf("\n%s %s %s\n", title_sep.c_str(),  m_table_name.c_str(), title_sep.c_str());
#else
  int title_sep_length = m_table_name.length();
  std::string title_sep(title_sep_length, '-');
  printf("\n%s\n%s\n", m_table_name.c_str(), title_sep.c_str());
#endif
  
  printf("%s", std::string(m_indent_level,' ').c_str());
  for(const auto &column_label : m_column_labels){
    printf("%s ", column_label.c_str());
  }
  printf("\n");
  printf("%s", std::string(m_indent_level,' ').c_str());
  for(const auto &column_label : m_column_labels){
    std::string sep(column_label.length(),'-');
    printf("%s ", sep.c_str());
  }
  printf("\n");
}

void cTEXT_TABLE::print_record(int count, ...) {
  assert(count == m_var_types.size());
  if(!m_num_records_printed)
    print_header();
  std::string format(m_indent_level,' ');
  for(int i = 0; i < m_var_types.size(); i++) {
    int field_width = m_column_labels[i].length();
    switch(m_var_types[i]) {
    case eVAR_TYPE::INT:
      format += "%" + std::to_string(field_width) + "d ";
      break;
    case eVAR_TYPE::FLOAT:
      format += "%" + std::to_string(field_width) + "f ";
      break;
    case eVAR_TYPE::STRING:
      format += "%" + std::to_string(field_width) + "s ";
      break;
    default:
      break;
    }
  }
  format += "\n";
  va_list fields;
  va_start(fields, count);
  vprintf(format.c_str(), fields);
  va_end(fields);
  m_num_records_printed++;
}
