/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2025, 1994-2024 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

#ifndef CP_SEED_FROM_MEAS_H
#define CP_SEED_FROM_MEAS_H

#include <unordered_map>

#include "common.h"
#include "seed.h"
#include "cp_nero.h"

static const ssize_t NUM_FRAMES = 10;

typedef struct sTBS_SURFEL_INFO {
  STP_PROC sp;
  auINT32 var_index;
  auINT32 var_mask;
  sFLOAT overlap_area_ratio;
  sTBS_SURFEL_INFO(STP_PROC sp1, auINT32 var_index1, auINT32 var_mask1, sFLOAT area_ratio) {
    sp = sp1;
    var_index = var_index1;
    var_mask = var_mask1;
    overlap_area_ratio = area_ratio;
  }
} *TBS_SURFEL_INFO;

static const ssize_t SIMPLE_MAP_BUFFER_SIZE = 256;
static const ssize_t N_SIMPLE_MAP_BUFFERS = 4;

template<int N = N_SIMPLE_MAP_BUFFERS, int BUFFER_SIZE = SIMPLE_MAP_BUFFER_SIZE>
class cSEED_CACHE {
  sriFLOAT m_meas_cell_buffer[N][BUFFER_SIZE][3];
public:
  sriFLOAT *meas_cell_from_buffer(asINT32 n, auINT32 mci = 0);
  VOID reset_buffer();
};


typedef class cSIMPLE_MAP {
  auINT32 m_min_mci_in_buffer[4];
  int m_buffer_index;
  int buffer_size;
  cSEED_CACHE<> m_buffer;
public:
  cSIMPLE_MAP() {
    m_buffer_index = 0;
    buffer_size = 0;
  };
  auINT32 nearest_meas_cell(SRI_SURFACE_FILE face_meas_file, std::vector<auINT32> &meas_cell_indices, sFLOAT centroid[3]);
  VOID fill_meas_cell_buffer(auINT32 meas_cell_index);
  VOID init_buffer(SRI_SURFACE_FILE face_meas_file);
  VOID reset_buffer(SRI_SURFACE_FILE face_meas_file, auINT32 mci);
  VOID increment_buffer_index();
} *SIMPLE_MAP;

const int MEAS_CELLS_BUFFER_SIZE = 128*1024;

typedef struct sSEED_FROM_MEAS_DESC {
  
  SRI_FILE                            m_sampled_face_meas_file;
  std::string                         m_sampled_face_meas_filename;
  sriFACET_ID                         m_facet_id_offset;
  std::map< sriFACET_ID, asINT32 >    m_meas_cell_index_from_facet_id;
  sriINT                              m_meas_frame_num;
  sriINT                              m_meas_start_frame_num;
  sriINT                              m_total_meas_frames;
  auINT32                             m_masks_union;
  sriBOOL                             m_transient_boundary_seeding;
  sriBOOL                             m_is_octree_map;
  sriBOOL                             m_is_mean_vel_added;
  dFLOAT                              m_cdi_to_meas_file_length_scale_factor;
  sSMART_SEED_PARAMS                  m_params;
  static const auINT32                m_vel_mask;

  sriPOINT  m_n_pts_total;
  sriPOINT  m_meas_start_index[DGF_N_BOUNDARY_SEED_VARS];
  sriBOOL   m_is_var_double[DGF_N_BOUNDARY_SEED_VARS];
  sriFLOAT  *m_var_values[DGF_N_BOUNDARY_SEED_VARS];
  sriDOUBLE *m_var_values_double[DGF_N_BOUNDARY_SEED_VARS];

  MSI_TABLE                                             m_msi_table;
  cSIMPLE_MAP                                            m_simple_map;
  std::unordered_map<sriFACET_ID, std::vector<auINT32>> m_facet_id_to_meas_cells_map;
  // meas file and cdi file can have different length in meters per lattice.
  // Surfel centroids are scaled to lattice units of meas file to fetch nearest meas cell.
  asINT32                                               m_frame_period;
  asINT32                                               m_frame_start_time;
  auINT32                                               m_n_total_frames_sent;

  sriFLOAT *m_scale_factors;
  sriFLOAT *m_surfel_areas;
  cdiINT64 m_cdi_file_id;

  static const SRI_VARIABLE_TYPE m_sri_var_type_from_dgf_bseed_var_type[DGF_N_BOUNDARY_SEED_VARS];

  VOID setup();
  VOID fill_seed_params(cDGF_SMART_SEED_CONTROL &seed_from_meas_control);

  sSEED_FROM_MEAS_DESC()
  {
    m_sampled_face_meas_file = NULL;
    m_facet_id_offset = 0;
    m_meas_frame_num  = -1;
    m_meas_start_frame_num  = 0;
    m_total_meas_frames = -1;
    m_transient_boundary_seeding = FALSE;
    m_is_mean_vel_added = FALSE;
    m_masks_union     = 0;
    m_is_octree_map      = 0;
    m_cdi_to_meas_file_length_scale_factor = 1;
    m_n_total_frames_sent = 0;
    ccDOTIMES(i, DGF_N_BOUNDARY_SEED_VARS) {
      m_meas_start_index[i]  = 0;
      m_is_var_double[i]     = FALSE;
      m_var_values[i]        = NULL;
      m_var_values_double[i] = NULL;
    }

    m_n_pts_total   = 0;
    m_scale_factors = NULL;
    m_surfel_areas  = NULL;
    m_cdi_file_id   = -1;
  }

  VOID fill_seed_from_meas_data(cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data, cDGF_SURFEL_DESC &surfel_desc, asINT32 desc_index,asINT32 frame_index=-1);
  VOID fill_transient_boundary_seeding_data(sFLOAT &surfel_seed_data, asINT32 meas_cell_index, asINT32 frame_index, asINT32 i,
                                            auINT32 var_mask, cBOOLEAN reset_meas_file_buffer);
  VOID compute_scale_factors();
  VOID write_scale_factors(LGI_STREAM stream);
  VOID free_memory();
  VOID create_mci_centroid_map();
  auINT32 nearest_meas_cell_to_surfel(std::vector<auINT32> &meas_cells, sFLOAT centroid[3]);
  VOID fill_facet_id_to_meas_cell_map(SRI_FILE meas_file);
  VOID set_cdi_to_meas_file_length_scale_factor();
  VOID set_frame_period_and_start_time();
} *SEED_FROM_MEAS_DESC;

#endif
